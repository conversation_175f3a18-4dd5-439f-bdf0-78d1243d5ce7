<template>
  <div class="container">
    正在退出登录...
  </div>
</template>

<script>
import SSO from 'sso/sso.js'

export default {
  name: 'user-logout',
  data () {
    return {

    }
  },
  created () {
    this.logout()
  },
  methods: {
    logout() {
      let jump = this.$utils.toPage('/', 'aiui', 'none')
      if (this.$route.query.pageFrom) {
        jump = this.$route.query.pageFrom
      }
      SSO.logout(function () {
        location.href = jump
      })
    }
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="scss" scoped>

</style>
