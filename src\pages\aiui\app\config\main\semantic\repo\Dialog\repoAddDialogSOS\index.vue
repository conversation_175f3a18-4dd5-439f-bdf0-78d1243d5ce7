<template>
  <el-dialog
    title="添加问答库"
    :visible.sync="dialog.show"
    width="760px"
    top="5vh"
    :show-close="false"
    @closed="closeSkillDialog"
  >
    <div class="skill-header">
      <el-input
        size="small"
        class="search-area"
        placeholder="搜索"
        v-model.trim="searchVal"
        @keyup.enter.native="searchAppConfig"
        style="width: 258px"
      >
        <i
          @click.stop.prevent="searchAppConfig"
          slot="suffix"
          class="el-input__icon el-icon-search search-area-btn"
        />
      </el-input>
    </div>

    <div class="config-content">
      <div class="skill-container" v-loading="loading" v-if="dialog.show">
        <div class="check-config">
          <doc-repo
            :data="ragData"
            :dataCopy="ragDataCopy"
            @selectchange="onSelectchange"
          ></doc-repo>
        </div>
      </div>
    </div>

    <span slot="footer" class="dialog-footer">
      <el-button size="small" @click="dialog.show = false">取消</el-button>
      <el-button
        size="small"
        type="primary"
        @click="saveChangeData"
        :loading="saveLoading"
        :disabled="!switchHasChange"
      >
        保存配置
      </el-button>
    </span>
  </el-dialog>
</template>
<script>
import docRepo from './docRepo.vue'

export default {
  props: {
    dialog: Object,
    appId: '',
    currentScene: Object,
  },
  data() {
    return {
      saveLoading: false,

      clickSearchVal: '',
      searchVal: '',

      selectKey: '1',
      ragData: [],
      ragDataCopy: [],
      originData: [],
      loading: false,

      switchHasChange: false,
      channel: '',
    }
  },
  methods: {
    onSelectchange(code, val) {
      this.switchHasChange = true
      this.ragData = this.ragData.map((item) => {
        if (item.id === code) {
          return {
            ...item,
            selected: val,
          }
        } else {
          return { ...item }
        }
      })
      this.ragDataCopy = this.ragDataCopy.map((item) => {
        if (item.id === code) {
          return {
            ...item,
            selected: val,
          }
        } else {
          return { ...item }
        }
      })
    },

    saveChangeData() {
      let that = this
      const addRepos = []
      const delRepos = []
      const updateRepos = []

      // 创建一个原始数据的映射，方便查找
      const originMap = {}
      this.originData.forEach((item) => {
        originMap[item.id] = item
      })

      // 遍历当前表格数据，找出变更
      this.ragData.forEach((currentItem) => {
        const originItem = originMap[currentItem.id]

        if (!originItem) {
          // 如果是新增的数据（如果有这种情况）
          addRepos.push(currentItem)
          return
        }

        // 检查selected字段的变化
        if (currentItem.selected !== originItem.selected) {
          if (currentItem.selected) {
            // 从未选中变为选中 -> 添加到addRepos
            addRepos.push(currentItem)
          } else {
            // 从选中变为未选中 -> 添加到delRepos
            delRepos.push(currentItem)
          }
        } else {
          // selected没有变化，检查isTop是否有变化
          if (currentItem.threshold !== originItem.threshold) {
            // 只有isTop变化 -> 添加到updateRepos
            updateRepos.push(currentItem)
          }
        }
      })

      const params = {
        botId: this.currentScene.botBoxId,
        channel: this.channel,
        addRepos,
        updateRepos,
        delRepos,
      }
      that.saveLoading = true

      this.$utils.httpPost(
        this.$config.api.AIUI_BOT_CONFIG_SAVE_RAGREOPCONFIG,
        JSON.stringify(params),
        {
          config: {
            headers: {
              'Content-Type': 'application/json',
            },
          },
          success: (res) => {
            that.saveLoading = false
            that.switchHasChange = false
            that.$emit('saveSuccess')
            that.$message.success('保存成功')
            that.dialog.show = false
          },
          error: (err) => {
            console.log('err', err)
            // reject(err.desc)
            that.saveLoading = false
            that.$message.error(err)
          },
        }
      )
    },

    // 关闭弹窗
    closeSkillDialog() {
      // 搜索框清空
      this.clickSearchVal = ''
      this.searchVal = ''
    },
    searchAppConfig() {
      this.clickSearchVal = this.searchVal
      // this.$emit('search', this.clickSearchVal)
      this.ragData = this.ragDataCopy.filter((it) =>
        it.name.includes(this.clickSearchVal)
      )
    },

    getAppRagConfig() {
      let that = this
      this.loading = true
      this.$utils.httpGet(
        this.$config.api.AIUI_BOT_CONFIG_GET_BOTRAGREPOS,
        {
          botId: this.currentScene.botBoxId,
          pageIndex: 1,
          pageSize: 1000,
        },
        {
          success: (res) => {
            console.log(res, '这个是知识库的res')
            that.loading = false
            let newRepos = (res.data.repos || []).map(({ repoid, ...rest }) => {
              return {
                ...rest,
                repoId: repoid,
              }
            })
            that.originData = JSON.parse(JSON.stringify(newRepos))
            that.ragDataCopy = JSON.parse(JSON.stringify(newRepos))
            that.channel = res.data.channel
            that.ragData = newRepos || []
          },
          error: (res) => {},
        }
      )
    },
  },
  watch: {
    'dialog.show'(val) {
      if (val) {
        this.saveLoading = false
        this.getAppRagConfig()
      }
    },
  },
  components: { docRepo },
}
</script>
<style lang="scss" scoped>
.skill-header {
  position: absolute;
  top: 12px;
  right: 20px;
}
.tab-container {
  display: flex;
  position: relative;
  &::before {
    position: absolute;
    content: ' ';
    width: 100%;
    height: 1px;
    background: #e7e9ed;
    bottom: 0;
  }
}

.skill-type {
  margin-top: 1%;
  margin-bottom: 1%;
}
.add-skill-tab {
  a {
    display: inline-block;
    width: 108px;
    height: 40px;
    line-height: 40px;
    cursor: pointer;
    color: unset;
    text-align: center;
  }
  .active {
    position: relative;
    color: $primary;
    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      display: inline-block;
      width: 88px;
      height: 2px;
      background-color: #1f90fe;
      border-radius: 2px;
      transform: translateX(-50%);
    }
  }
}

.el-tabs {
  margin-left: 20px;
}
</style>
