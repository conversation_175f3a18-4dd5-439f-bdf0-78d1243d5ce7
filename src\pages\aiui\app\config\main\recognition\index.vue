<template>
  <div>
    <recognitionSOS v-if="currentScene && currentScene.sos === true" />
    <recognition
      v-if="
        currentScene && currentScene.sceneBoxId && currentScene.sos !== true
      "
    />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import recognition from './recognition'
import recognitionSOS from './recognitionSOS'

export default {
  data() {
    return {}
  },
  computed: {
    ...mapGetters({
      currentScene: 'aiuiApp/currentScene',
      appInfo: 'aiuiApp/app',
      subAccountEditable: 'aiuiApp/subAccountEditable',
    }),
    appId() {
      return this.$route.params.appId
    },
  },
  components: {
    recognition,
    recognitionSOS,
  },
}
</script>
<style lang="scss" scoped></style>
