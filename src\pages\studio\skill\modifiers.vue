<template>
  <os-page :options="pageOptions">
    <studio-skill-header-right slot="btn" />
    <div class="os-scroll">
      <div class="mgt32 mgb24" style="font-size: 0">
        <el-button
          class="mgr16"
          icon="ic-r-plus"
          type="primary"
          size="small"
          :disabled="!canCreate"
          @click="add"
        >
          创建修饰语
        </el-button>
        <div class="fr" @keyup.enter="getListByPage(1)" style="width: 480px">
          <el-input
            class="search-area"
            placeholder="搜索修饰语"
            size="small"
            v-model="searchVal"
          >
            <i
              slot="suffix"
              class="el-input__icon el-icon-search search-area-btn"
              @click="getListByPage(1)"
            />
          </el-input>
        </div>
      </div>
      <os-table
        :tableData="tableData"
        style="margin-bottom: 56px"
        class="modifiers-table"
        @change="getListByPage"
        @edit="toEdit"
        @del="toDel"
      >
        <el-table-column type="index" width="80"></el-table-column>
        <el-table-column prop="name" width="160" label="修饰语名称">
          <div
            slot-scope="scope"
            class="cp text-blod name"
            @click="toEdit(scope.row)"
          >
            {{ scope.row.name }}
            <el-tooltip
              v-if="scope.row.source"
              effect="dark"
              content="源技能的修饰语"
              placement="top"
            >
              <div class="intent-tag ib" style="vertical-align: text-bottom">
                源
              </div>
            </el-tooltip>
          </div>
        </el-table-column>
        <el-table-column
          prop="fragments"
          label="修饰语内容"
          min-width="300"
          @click.native="toEdit(scope.row)"
        >
          <div
            slot-scope="scope"
            class="cp fragments-wrap"
            @click="toEdit(scope.row)"
          >
            <el-popover
              v-if="scope.row.fragments && scope.row.fragments.length"
              width="400"
              placement="bottom-start"
              trigger="hover"
            >
              <el-scrollbar style="padding-right: 12px; height: 232px">
                <p
                  class="modifier-popover-line"
                  v-for="(item, index) of scope.row.fragments"
                  :key="index"
                >
                  {{ item }}
                </p>
              </el-scrollbar>
              <template slot="reference">
                <span class="ib ellipsis fragment-content">{{
                  scope.row.fragments[0]
                }}</span
                ><span class="intent-tag ib">{{
                  scope.row.fragments.length
                }}</span>
              </template>
            </el-popover>
            <span v-else>-</span>
          </div>
        </el-table-column>
        <el-table-column prop="intents" width="268" label="关联意图">
          <template slot-scope="scope">
            <span
              class="mgr8"
              v-if="scope.row.intents && !scope.row.intents.length"
              >-</span
            >
            <el-popover
              v-else
              width="240"
              placement="bottom-start"
              trigger="hover"
            >
              <el-scrollbar style="padding-right: 12px; height: 232px">
                <p
                  class="modifier-popover-line"
                  v-for="(item, index) of scope.row.intents"
                  :key="index"
                >
                  {{ item }}
                </p>
              </el-scrollbar>
              <template slot="reference">
                <span class="ellipsis">{{ scope.row.intentNames }}</span>
              </template>
            </el-popover>
          </template>
        </el-table-column>
      </os-table>
    </div>
    <modifier-dialog
      v-if="dialog.show"
      :dialog="dialog"
      @change="getListByPage"
    ></modifier-dialog>
  </os-page>
</template>

<script>
import ModifierDialog from './modifierDialog/modifierDialog'
export default {
  name: 'modifiers-list-page',
  props: {
    subAccount: Boolean,
    subAccountEditable: Boolean,
    limitCount: {
      required: true,
      default: () => ({}),
    },
  },
  data() {
    return {
      pageOptions: {
        title: '自定义修饰语',
        loading: false,
      },
      searchVal: '',
      tableData: {
        loading: false,
        total: 0,
        page: 1,
        size: 10,
        handles: ['edit', 'del'],
        handleColumnText: '操作',
        list: [],
      },
      dialog: {
        show: false,
      },
    }
  },
  computed: {
    businessId() {
      return this.$store.state.studioSkill.id
    },
    canCreate() {
      if (!this.subAccountEditable) return false
      let limitCount = this.limitCount.skill_modifier_count || 3
      let filterRes = this.tableData.list.filter((item) => !item.source) || []
      if (filterRes.length >= limitCount) return false
      return true
    },
  },
  created() {
    this.getListByPage()
    this.subInit()
  },
  methods: {
    subInit() {
      if (!this.subAccountEditable) {
        return (this.tableData.handles = ['edit'])
      }
      this.tableData.handles = ['edit', 'del']
    },
    add() {
      this.dialog.show = true
      this.dialog.modifierId = ''
    },
    getListByPage(page) {
      this.tableData.loading = true
      this.$utils.httpPost(
        this.$config.api.STUDIO_MODIFIER_GET_LIST,
        {
          businessId: this.businessId,
          search: this.searchVal,
          pageIndex: page || this.tableData.page,
          pageSize: this.tableData.size,
        },
        {
          success: (res) => {
            this.tableData.loading = false
            this.tableData.total = res.data.count
            this.tableData.list = res.data.list
            let len = this.tableData.list.length,
              tmp = ''
            for (let i = 0; i < len; i++) {
              if (
                this.tableData.list[i].intents &&
                this.tableData.list[i].intents.length
              ) {
                tmp = this.tableData.list[i].intents.join('|')
              }
              if (this.tableData.list[i].source) {
                // 源技能的修饰语不可编辑/删除
                this.tableData.list[i].noEdit = true
                this.tableData.list[i].noDel = true
              }
              this.tableData.list[i].intentNames = tmp
            }
          },
          error: (err) => {
            this.tableData.loading = false
          },
        }
      )
    },
    toEdit(row) {
      if (!this.subAccountEditable) return
      if (row.source) {
        return this.$message.warning('源技能的修饰语不可编辑')
      }
      this.dialog.show = true
      this.dialog.modifierId = row.id
    },
    toDel(row) {
      let self = this
      if (
        (row.intents && row.intents.length) ||
        (row.names && row.names.length)
      ) {
        return this.$message.warning(
          '自定义修饰语正被引用，为保证技能效果，请取消引用后再删除'
        )
      }
      let desc = '修饰语删除后不可恢复，请谨慎操作。'
      let confirmTitle =
        row.name && row.name.length > 9
          ? `${row.name.substring(0, 9)}...`
          : row.name
      this.$confirm(desc, `确定删除修饰语 - ${confirmTitle}`, {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        confirmButtonClass: 'el-button--danger',
        type: 'warning',
        showClose: false,
      })
        .then(() => {
          self.delModifier(row.id)
        })
        .catch(() => {})
    },
    delModifier(id) {
      this.$utils.httpPost(
        this.$config.api.STUDIO_MODIFIER_DEL,
        {
          businessId: this.businessId,
          modifierId: id,
        },
        {
          success: (res) => {
            if (res.flag) {
              this.$message.success('删除成功')
              this.getListByPage(1)
            }
          },
          error: (err) => {},
        }
      )
    },
  },
  components: {
    ModifierDialog,
  },
}
</script>

<style lang="scss">
.modifiers-table {
  :deep(.el-table--enable-row-hover .el-table__body tr:hover > td) {
    .name,
    .fragments-wrap {
      color: $primary;
    }
  }
  .el-table .ic-r-edit {
    color: $primary;
  }
  .fragment-content {
    vertical-align: middle;
    margin-right: 4px;
    max-width: calc(100% - 40px);
  }
}

.el-scrollbar
  .el-scrollbar__wrap.el-scrollbar__wrap--hidden-default
  .el-scrollbar__view
  .modifier-popover-line {
  padding: 9px 12px;
  line-height: 22px;
  border-bottom: 1px solid $grey2;
}
</style>
