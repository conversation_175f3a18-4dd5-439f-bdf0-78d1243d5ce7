<template lang="html">
  <div class="os-header">
    <div class="container">
      <div class="container-logo">
        <a @click="$utils.toPage('/')" v-if="!subAccountInfo">
          <img src="../assets/svg/logo/logo-white-full.svg" />
        </a>
        <img v-else src="../assets/svg/logo/logo-white-full.svg" />
      </div>
      <div class="container-navs" v-show="!subAccountInfo">
        <a @click="$utils.toPage('/')">首页</a>
        <a @click="$utils.toPage('/device')">设备接入</a>
        <a @click="$utils.toPage('/service')">服务接入</a>
        <a @click="$utils.toPage('/index-studio')">技能工作室</a>
        <a @click="$utils.toPage('/news/')">最新动态</a>
        <a @click="$utils.toPage('/resources')">资源中心</a>
      </div>
      <div v-if="!hasUserInfo" class="container-collapse">
        <div class="container-collapse-login" @click="$utils.toPage('/user/login')">登录</div>
        <div class="container-collapse-register" @click="$utils.toPage('/user/register')">注册</div>
      </div>
      <div v-else class="container-collapse">
        <a style="margin-right: 16px;">
          <div class="container-collapse-in" v-show="!subAccountInfo">
            控制台
            <ul class="container-collapse-in-dropdown">
              <li @click="$utils.toPage('/products')">设备接入控制台</li>
              <li @click="$utils.toPage('/services')">服务接入控制台</li>
              <li @click="$utils.toPage('/skills', 'studio')">技能开发控制台</li>
            </ul>
          </div>
        </a>
        <a>
          <div class="container-collapse-user">
            <div class="container-collapse-user-name">
              <span v-if="subAccountInfo">{{subAccountInfo.login}}</span>
              <span v-else>{{userInfo.email || userInfo.mobile}}</span>
            </div>
            <ul class="container-collapse-user-dropdown" v-if="!subAccountInfo">
              <li @click="$utils.toPage('/user-info')">基本资料</li>
              <li v-if="limitCount && limitCount.hasOwnProperty('sub_account') && limitCount['sub_account'] != '0'" @click="toCooperation">
                协同操作
              </li>
              <li @click="$utils.toPage('/user/logout', 'studio')">退出</li>
            </ul>
            <ul class="container-collapse-user-dropdown" v-else>
              <li @click="toUserInfo">
                基本资料
              <li @click="tologs">
                协同操作
              </li>
              <li @click="toOrders">
                我的订单
              </li>
              <li @click="logout">退出</li>
            </ul>
          </div>
        </a>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'OsHeader',
  data() {
    return {

    }
  },
  watch: {

  },
  computed: {
    ...mapGetters({
      userInfo: 'user/userInfo',
      subAccountInfo: 'user/subAccountInfo',
      limitCount: 'aiuiApp/limitCount'
    }),
    hasUserInfo() {
      return this.userInfo || this.subAccountInfo
    }
  },
  mounted() {
    let self = this
  },
  methods: {
    toLogin () {
      let self = this
      window.location.href = "/studio/login"
    },
    logout () {
      let self = this
      let data = {
        subSessionId: this.$utils.getCookie('subSessionId'),
        sub_account_id: this.$utils.getCookie('sub_account_id')
      }
      this.$utils.httpPost(this.$config.api.COOP_SUB_ACCOUNT_LOGOUT, data, {
        success: res => {
          if(res.flag) {
            this.$message.success('退出成功')
            this.$router.push({name: 'sub-login'})
            localStorage.removeItem("firstEnterSkill", "1");
            localStorage.removeItem("firstEnterEntity", "1");
            localStorage.removeItem("firstEnterAuxiliary", "1");
          }
        },
        error: err => {

        }
      })
    },
    toCooperation() {
      let routeData = this.$router.resolve({path: '/cooperation'})
      window.open(routeData.href, '_blank')
    },
    toUserInfo () {
      let routeData = this.$router.resolve({path: '/sub/user-info'})
      window.open(routeData.href, '_blank')
    },
    tologs () {
      let routeData = this.$router.resolve({path: '/sub/cooperation/logs'})
      window.open(routeData.href, '_blank')
    },
    toOrders(){
      let routeData = this.$router.resolve({path: '/orders'})
      window.open(routeData.href, '_blank')
    },
    tolink (url) {
      window.location = url
    }
  }
}
</script>

<style lang="scss" scoped>
.os-header {
  width: 100%;
  height: 60px;
  top: 0;
  box-shadow: 0 0 0 1px rgba(255, 255, 255, .08);
  z-index: 9999;
  transition: all .6s linear;
  background-color: #000;
  .container {
    width: 100%;
    height: 100%;
    padding: 0 16px 0 24px;
    margin: auto;
    display: flex;
    display: -ms-flexbox;
    align-items: center;
    justify-content: space-between;
    &-logo {
      width: 300px;
      img {
        width: 108px;
        vertical-align: middle;
      }
    }

    &-navs {
      height: 100%;
      display: flex;
      display: -ms-flexbox;
      a {
        min-width: 76px;
        text-align: center;
        line-height: 60px;
        color: #fff;
        margin: 0 16px;
        opacity: .85;
        font-weight: 500;
        &:hover {
          opacity: 1;
        }
      }
      &-active {
        font-weight: 600;
        opacity: 1 !important;
      }
    }

    &-collapse {
      width: 300px;
      display: flex;
      display: -ms-flexbox;
      align-items: center;
      justify-content: flex-end;
      &-login {
        color: #fff;
        opacity: .85;
        cursor: pointer;
        width: 96px;
        line-height: 60px;
        font-weight: 500;
        text-align: center;
        &:hover {
          opacity: 1;
        }
      }
      &-register {
        width: 96px;
        padding: 8px 0;
        color: #fff;
        background-color: $primary;
        border-color: $primary;
        border-radius: 1px;
        text-align: center;
        cursor: pointer;
        &:hover {
          background-color: mix(#fff, $primary, 10%);
        }
      }
      &-in {
        color: #fff;
        opacity: .85;
        cursor: pointer;
        width: 45px;
        line-height: 60px;
        font-weight: 500;
        text-align: left;
        &:hover {
          opacity: 1;
          .container-collapse-in-dropdown {
            display: block;
          }
        }
      }
      &-user {
        color: #fff;
        opacity: .85;
        cursor: pointer;
        width: auto;
        line-height: 60px;
        font-weight: 500;
        text-align: left;
        &:hover {
          opacity: 1;
          .container-collapse-user-dropdown {
            display: block;
          }
        }
        &-name {
          max-width: 130px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
      &-in, &-user {
        position: relative;
        margin-right: 16px;
        &:after {
          position: absolute;
          content: " ";
          bottom: 0;
          top: 0;
          right: -16px;
          margin: auto;
          width: 0;
          height: 0;
          border-width: 6px 5px 0px;
          border-style: solid;
          border-color: #f4f7fa transparent transparent;
        }
      }
    }
  }
  .container-collapse-in-dropdown {
    position: absolute;
    right: -16px;
    top: 60px;
    background-color: #fff;
    border: 1px solid #ebeef5;
    border-radius: 1px;
    box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
    padding: 8px 0;
    display: none;
    &:after {
      position: absolute;
      content: " ";
      top: -6px;
      right: 30px;
      margin: auto;
      width: 0;
      height: 0;
      border-width: 0 6px 6px;
      border-style: solid;
      border-color: transparent transparent #f4f7fa;
    }
    & li {
      list-style: none;
      line-height: 36px;
      padding: 0 20px;
      margin: 0;
      font-size: 14px;
      color: #606266;
      cursor: pointer;
      outline: none;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      &:hover {
        color: $primary;
        background-color: mix(#fff, $primary, 80%);
      }
    }
  }
  .container-collapse-user-dropdown {
    position: absolute;
    right: -16px;
    top: 60px;
    background-color: #fff;
    border: 1px solid #ebeef5;
    border-radius: 1px;
    box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
    padding: 8px 0;
    display: none;
    &:after {
      position: absolute;
      content: " ";
      top: -6px;
      right: 24px;
      margin: auto;
      width: 0;
      height: 0;
      border-width: 0 6px 6px;
      border-style: solid;
      border-color: transparent transparent #f4f7fa;
    }
    & li {
      list-style: none;
      line-height: 36px;
      padding: 0 20px;
      margin: 0;
      font-size: 14px;
      color: #606266;
      cursor: pointer;
      outline: none;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      &:hover {
        color: $primary;
        background-color: mix(#fff, $primary, 80%);
      }
    }
  }
}
</style>
