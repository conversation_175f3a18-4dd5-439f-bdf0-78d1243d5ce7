<template>
  <div :class="{ 'check-box': true, active: value }" @click="toggleChecked">
    <div class="check-box-indicator"></div>
    <div class="check-box-text">
      {{ type === '0' ? '开启搜索' : '情感陪伴' }}
    </div>
    <div
      :class="{
        'check-box-icon': true,
        'icon-search': type === '0',
        'icon-star': type === '1',
      }"
    ></div>
  </div>
</template>
<script>
export default {
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    type: {
      type: String,
      default: '',
    },
  },
  data() {
    return {}
  },
  methods: {
    toggleChecked() {
      this.$emit('input', !this.value)
    },
  },
}
</script>
<style lang="scss" scoped>
// .control-sprite{background:url(~@A/images/model-exeperience/control-sprite.png)  no-repeat;}
// .star{height:38px;width:40px;background-position:0 0;}
// .star-active{height:38px;width:40px;background-position:0 -38px;}
// .search{height:46px;width:46px;background-position:0 -76px;}
// .search-active{height:46px;width:46px;background-position:0 -122px;}

.check-box {
  display: flex;
  align-items: center;
  height: 27px;
  background: #ffffff;
  border-radius: 14px;
  box-shadow: -5px 0px 4.75px 0.25px rgba(201, 221, 255, 0.3);
  padding: 5px 0px 5px 10px;
  cursor: pointer;
  .check-box-indicator {
    width: 9px;
    height: 9px;
    border: 2px solid #dbdbdb;
    border-radius: 100%;
  }
  .check-box-text {
    font-size: 14px;
    color: #424343;
    margin: 0 -6px 0 5px;
  }
  .check-box-icon {
    background: url(~@A/images/model-exeperience/control-sprite.png) no-repeat;
    transform: scale(0.5);
  }
  .icon-search {
    height: 46px;
    width: 46px;
    background-position: 0 -76px;
  }
  .icon-star {
    height: 38px;
    width: 40px;
    background-position: 0 0;
  }
  &.active {
    background: #cadefe;
    .check-box-indicator {
      border: 2px solid #639dff;
      background: #639dff;
    }
    .icon-search {
      height: 46px;
      width: 46px;
      background-position: 0 -122px;
    }
    .icon-star {
      height: 38px;
      width: 40px;
      background-position: 0 -38px;
    }
  }
}
.check-box + .check-box {
  margin-left: 20px;
}
</style>
