include:
  - project: 'RS_AIUI/OPS/BuildScript'
    ref: master
    file: '/web-template.yml'

src-build:
  image: node:18.14.2-buster-slim
  variables:
    SASS_BINARY_SITE: https://depend.iflytek.com/artifactory/api/npm/npm-repo/node-sass
  script:
    - npm cache clean --force
    - npm config set proxy false
    - npm config set maxsockets=5
    - npm config set fetch-timeout 600000
    - npm config set fetch-retries 5
    - npm config set registry https://depend.iflytek.com/artifactory/api/npm/npm-repo
    - npm i
    - npm run build
