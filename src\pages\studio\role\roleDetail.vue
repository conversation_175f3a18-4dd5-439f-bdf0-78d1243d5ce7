<template>
  <div style="height: 100%">
    <div class="detail-header">
      <div class="detail-header-left">
        <div
          class="back_icon_wrapper"
          @click="$router.push({ name: 'studio-role' })"
        >
          <back-icon></back-icon>
        </div>
        <span class="text">编辑角色</span>
        <img :src="roleInfo.image" class="role-icon" @click="openPhoto" />
        <el-dropdown @command="handleRoleCommand" placement="bottom">
          <span class="dropdown-role">
            {{ roleInfo.name }}<i class="el-icon-arrow-down el-icon--right"></i>
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              v-for="role in roleList"
              :key="role.id"
              :command="role.id"
              :disabled="role.id == roleInfo.id"
              >{{ role.name }}</el-dropdown-item
            >
          </el-dropdown-menu>
        </el-dropdown>
        <el-tag v-if="roleInfo.status === 0" size="small">待发布</el-tag>
        <el-tag type="success" v-else size="small">已发布</el-tag>
      </div>
      <div class="header-button">
        <!-- <el-button>导入</el-button>
        <el-button>另存为</el-button> -->
        <el-button type="primary" @click="publish">构建发布</el-button>
      </div>
    </div>
    <div class="role-detail-content">
      <div class="role-form-content" ref="scrollContainer">
        <el-form
          :model="roleBaseForm"
          ref="roleBaseForm"
          class="role-form"
          label-position="left"
          label-width="120px"
          label-suffix="："
          @submit.native.prevent
        >
          <h3>人设信息</h3>

          <!-- 基础信息 -->
          <h4 id="base">基础信息</h4>
          <el-form-item
            v-if="propertyObject['base'] && propertyObject['base'].length > 0"
            v-for="(bItem, index) in propertyObject['base']"
            :key="`base_${index}`"
            :error="bItem.name === '' && bItem.value ? '字段名称不可为空' : ''"
          >
            <div slot="label">
              <el-input
                v-if="bItem.isNew"
                v-model="bItem.name"
                placeholder="自定义字段"
                @blur="(e) => changeMethod('base', bItem)"
                @keyup.enter.native="() => changeMethod('base', bItem)"
              />
              <span v-else class="el-form-item__label">
                <span style="color: #f53f3f" v-show="bItem.required"
                  >*&nbsp;</span
                >{{ bItem.name }}：
              </span>
            </div>
            <el-input
              v-if="bItem.name === '姓名'"
              class="base-name"
              v-model="bItem.value"
              :placeholder="`支持中文/英文/数字/下划线格式，不超过32个字符`"
              @blur="(e) => changeMethod('name', e.target.value)"
              @keyup.enter.native="() => changeMethod('name', bItem.value)"
            />
            <el-input
              v-else
              type="textarea"
              v-model="bItem.value"
              autosize
              :placeholder="bItem.description || `请输入${bItem.name}`"
              @blur="(e) => changeMethod('base', bItem)"
            />
            <i
              class="el-icon-delete del"
              v-show="bItem.name != '姓名'"
              @click="delMethod('base', index)"
            />
          </el-form-item>
          <el-button @click="addMethod('property', 'base')" class="add-btn">
            +&nbsp;添加自定义字段
          </el-button>

          <!-- 角色任务 -->
          <h4 id="task">角色任务</h4>
          <el-form-item
            v-for="(tItem, index) in propertyObject['task']"
            :label="tItem.name"
            :key="`task_${index}`"
          >
            <el-input
              type="textarea"
              v-model="tItem.value"
              autosize
              :placeholder="tItem.description || `请输入${tItem.name}`"
              @blur="(e) => changeMethod('task', tItem)"
            />
          </el-form-item>

          <!-- 性格特征 -->
          <h4 id="character">个性特征</h4>
          <el-form-item
            v-for="(cItem, index) in propertyObject['character']"
            :label="cItem.name"
            :key="`character_${index}`"
          >
            <el-input
              type="textarea"
              v-model="cItem.value"
              autosize
              :placeholder="cItem.description || `请输入${cItem.name}`"
              @blur="(e) => changeMethod('character', cItem)"
            />
          </el-form-item>

          <!-- 语言特征 -->
          <h4 id="language">语言风格</h4>
          <el-form-item
            v-for="(lItem, index) in propertyObject['language']"
            :label="lItem.name"
            :key="`language_${index}`"
          >
            <el-button
              v-if="lItem.name === '对话示例' && hasConv(lItem) === 0"
              @click="addMethod('chat-example')"
              class="add-btn"
            >
              +&nbsp;添加对话示例
            </el-button>
            <p v-if="lItem.name === '对话示例' && hasConv(lItem) > 0">
              已添加对话&nbsp;
              <span
                style="color: #009bff; cursor: pointer"
                @click="addMethod('chat-example')"
              >
                {{ hasConv(lItem) }}轮&nbsp;
                <i class="el-icon-edit" />
              </span>
            </p>
            <el-input
              v-if="lItem.name === '风格描述'"
              type="textarea"
              v-model="lItem.value"
              autosize
              :placeholder="lItem.description || `请输入${lItem.name}`"
              @blur="(e) => changeMethod('language', lItem)"
            />
          </el-form-item>

          <!-- 社会关系 -->
          <h4 id="social">社会关系</h4>
          <el-form-item
            v-for="(sItem, index) in propertyObject['social']"
            :key="`social_${index}`"
            :error="sItem.name === '' && sItem.value ? '字段名称不可为空' : ''"
          >
            <div slot="label">
              <el-input
                v-if="sItem.isNew"
                v-model="sItem.name"
                placeholder="请输入字段"
                @blur="(e) => changeMethod('social', sItem)"
                @keyup.enter.native="() => changeMethod('social', sItem)"
              />
              <span v-else class="el-form-item__label">{{ sItem.name }}：</span>
            </div>
            <el-input
              type="textarea"
              v-model="sItem.value"
              autosize
              :placeholder="`请输入${sItem.name}的信息`"
              @blur="(e) => changeMethod('social', sItem)"
            />
            <i class="el-icon-delete del" @click="delMethod('social', index)" />
          </el-form-item>
          <el-button @click="addMethod('property', 'social')" class="add-btn">
            +&nbsp;添加社会关系
          </el-button>

          <h3>高级配置</h3>
          <h4 id="voice">声音</h4>
          <el-form-item label="音色">
            <div class="tone-select">
              <div class="tone-card">
                <img :src="roleTtsData.url" v-show="roleTtsData.url" />
                <span class="text" :title="roleTtsData.name">
                  {{ roleTtsData.name || '暂未选择音色' }}
                </span>
                <a @click="changeMethod('tone')">更换</a>
              </div>
              <el-button @click="addMethod('tone')" style="position: relative">
                声音复刻
              </el-button>
            </div>
          </el-form-item>
          <el-form-item label="音量">
            <el-slider
              class="slider"
              v-model="roleTtsData.volume"
              @change="(val) => changeMethod('volume', val)"
              show-input
            ></el-slider>
          </el-form-item>
          <el-form-item label="语速">
            <el-slider
              class="slider"
              v-model="roleTtsData.speed"
              @change="(val) => changeMethod('speed', val)"
              show-input
            ></el-slider>
          </el-form-item>
          <el-form-item label="试听文本">
            <el-input
              type="textarea"
              v-model="roleTtsData.text"
              :autosize="{ minRows: 2 }"
              :placeholder="`请输入试听文本`"
              :maxlength="300"
              show-word-limit
              style="margin-bottom: 8px"
            />
            <el-button
              v-if="testStatus === 2"
              type="primary"
              size="small"
              @click="stop"
              >停止试听</el-button
            >
            <el-button
              v-else
              type="primary"
              size="small"
              :loading="testStatus === 1"
              @click="synthetic"
              >试听</el-button
            >
          </el-form-item>
        </el-form>

        <!-- 右侧菜单部分 -->
        <div class="form-menu">
          <!-- <p class="hd1">人设信息</p> -->
          <ul>
            <li
              v-for="(item, index) in characterList"
              :key="item.name"
              class="hd2"
              :class="{
                'hd2-active': activeMenu === item.name,
              }"
              @click="jumpTo(item.name)"
            >
              {{ item.value }}
            </li>
          </ul>
          <!-- <p class="hd1">高级配置</p> -->
          <ul>
            <li
              v-for="(item, index) in advancedList"
              :key="item.value"
              class="hd2"
              :class="{
                'hd2-active': activeMenu === item.value,
              }"
              @click="jumpTo(item.value)"
            >
              {{ item.name }}
            </li>
          </ul>
        </div>
      </div>
      <!-- <div
        class="os-side-right"
        :class="{ 'os-side-right-open': rightTestOpen }"
      ></div> -->
    </div>
    <conv-sample
      :roleInfo="roleInfo"
      :languageData="propertyObject['language']"
      :dialog="convDialog"
      @change="changeMethod('language')"
    />
    <tone-select
      :dialog="ttsDialog"
      :roleTtsData="roleTtsData"
      @change="(val) => changeMethod('tts', val)"
      @refreshTts="getRoleTts"
    />
    <voice-replicate
      :dialog="replicateDialog"
      @change="(val) => changeMethod('replicate', val)"
    />
    <select-icon
      ref="SelectIconDialog"
      :roleInfo="roleInfo"
      @change="(val) => changeMethod('icon', val)"
    />
    <publish-remind
      :dialog="publishDialog"
      :roleInfo="roleInfo"
      :roleApps="roleAppList"
      @publish="publishRole"
    />
  </div>
</template>
<script>
import { mapGetters } from 'vuex'
import { fetchEventSource } from '@microsoft/fetch-event-source'
import TTSRecorder from './tts/TTSRecorder'
import convSample from './dialog/convSample.vue'
import toneSelect from './dialog/toneSelect.vue'
import voiceReplicate from './dialog/voiceReplicate.vue'
import selectIcon from './dialog/selectIconDialog.vue'
import publishRemind from './dialog/publishRemind.vue'

const abortController = new AbortController()
export default {
  name: 'role-detail',
  components: {
    convSample,
    toneSelect,
    voiceReplicate,
    selectIcon,
    publishRemind,
  },
  data() {
    return {
      roleList: [],
      roleInfo: {
        id: '',
        name: '测试角色名称',
        status: 1,
        taskDsp: '',
        disposition: '',
        preference: '',
        styleDsp: '',
      },
      characterList: [
        {
          name: 'base',
          value: '基础信息',
        },
        {
          name: 'task',
          value: '角色任务',
        },
        {
          name: 'character',
          value: '性格特征',
        },
        {
          name: 'language',
          value: '语言特征',
        },
        {
          name: 'social',
          value: '社会关系',
        },
      ],
      advancedList: [
        //仅在高级配置的菜单中使用
        {
          name: '声音',
          value: 'voice',
        },
      ],
      propertyObject: {
        base: [],
        character: [],
        task: [],
        language: [],
        social: [],
      },
      roleBaseForm: {
        name: '',
      },
      convDialog: {
        show: false,
      },
      ttsDialog: {
        show: false,
      },
      replicateDialog: {
        show: false,
      },
      publishDialog: {
        show: false,
      },
      roleTtsData: {},
      testStatus: 0, //试听状态 0：初始状态，1：正在合成，2：播放中
      ttsRecorder: null,
      ttsRecorderStatus: 'init',
      uid: this.$utils.experienceUid(),
      roleAppList: [],
      activeMenu: 'base', // 当前激活的菜单
      domHeightList: [],
    }
  },
  computed: {
    ...mapGetters({
      rightTestOpen: 'studioSkill/rightTestOpen',
    }),
  },
  methods: {
    jumpTo(module) {
      let el = document.getElementById(module)
      // console.log('el', el, module)
      if (el) {
        el.scrollIntoView({
          behavior: 'smooth',
          block: 'start',
          inline: 'nearest',
        })
      }
      // this.$nextTick(() => {
      //   this.activeMenu = module
      // })
    },
    hasConv(item) {
      if (item.name != '对话示例') return 0
      let value = item.value
      if (!value || value === '') return 0
      try {
        let list = JSON.parse(value)
        return list.length
      } catch (e) {
        console.log('解析对话错误', e)
        return 0
      }
    },
    getRoleInfo() {
      return new Promise((resolve, reject) => {
        this.$utils.httpGet(
          this.$config.api.AIUI_ROLE_DETAIL,
          {
            roleId: this.$route.params.roleId,
          },
          {
            success: (res) => {
              this.roleInfo = res.data
              this.roleBaseForm.name = res.data.name
              resolve()
            },
            error: (err) => {},
          }
        )
      })
    },
    getRoleCategory() {
      this.$utils.httpGet(
        this.$config.api.AIUI_ROLE_CATEGORY_LIST,
        {},
        {
          success: (res) => {
            if (res.code == '0') {
              this.characterList = res.data.list || []
            }
          },
          error: (err) => {},
        }
      )
      this.$utils.httpGet(
        this.$config.api.AIUI_ROLE_PROPERTY_DETAIL,
        {
          roleId: this.$route.params.roleId,
        },
        {
          success: (d_res) => {
            if (d_res.code == '0') {
              let roleData = d_res.data
              this.propertyObject = JSON.parse(JSON.stringify(roleData))
              this.$nextTick(() => {
                this.getSecMenuTop()
              })
            }
          },
          error: (err) => {},
        }
      )
    },
    getRoleTts() {
      this.$utils.httpGet(
        this.$config.api.AIUI_ROLE_TTS,
        {
          roleId: this.$route.params.roleId,
        },
        {
          success: async (res) => {
            if (res.data) {
              this.roleTtsData = {
                ...res.data,
                text:
                  res.data.text ||
                  '贝加尔湖是世界上最古老、最深的淡水湖泊，位于俄罗斯西伯利亚地区， 湖水极其清澈透明，是世界上最纯净的湖泊之一。',
              }
            } else {
              // 没有配置发音人
              this.roleTtsData = {}
            }
          },
          error: (err) => {
            this.roleTtsData = {}
            this.$message.error('获取发音人配置失败')
          },
        }
      )
    },
    getAllRole() {
      this.$utils.httpGet(
        this.$config.api.AIUI_ROLE_LIST,
        {},
        {
          success: (res) => {
            this.roleList = res.data.list
          },
          error: (err) => {},
        }
      )
    },
    getTtsText() {
      return new Promise((resolve, reject) => {
        this.$utils.httpGet(
          this.$config.api.AIUI_BOT_ROLE_TTS_TEXT,
          {},
          {
            success: (res) => {
              if (res.data) {
                return resolve(res.data.text)
              }
              return resolve('')
            },
            error: (err) => {
              return reject('')
            },
          }
        )
      })
    },
    handleRoleCommand(command) {
      this.$router.replace({
        name: 'role',
        params: {
          roleId: command,
        },
      })
    },
    checkRoleApp() {
      return new Promise((resolve, reject) => {
        this.$utils.httpGet(
          this.$config.api.AIUI_ROLE_USED_APP_LIST,
          {
            roleId: this.roleInfo.id,
          },
          {
            success: (res) => {
              if (res.code === '0') {
                return resolve(res.data?.apps || [])
              } else {
                return reject(res.message || '获取角色绑定应用列表失败')
              }
            },
            error: (err) => {},
          }
        )
      })
    },
    async publish() {
      this.roleAppList = await this.checkRoleApp()
      if (this.roleAppList.length == 0) {
        let data = {
          roleId: this.roleInfo.id,
        }
        this.publishRole(data)
      } else {
        this.publishDialog.show = true
      }
    },
    publishRole(data, usedInApp = false) {
      this.$utils.httpPost(
        this.$config.api.AIUI_ROLE_PUBLISH,
        JSON.stringify(data),
        {
          config: {
            headers: {
              'Content-Type': 'application/json;charset=UTF-8',
            },
          },
          success: (res) => {
            this.$message.success(
              usedInApp
                ? '角色构建成功！如需在现网生效，请到我的应用构建发布～'
                : '角色构建成功'
            )
            this.getRoleInfo()
          },
          error: (err) => {
            this.getRoleInfo()
          },
        }
      )
    },
    addMethod(type, val) {
      switch (type) {
        case 'property':
          // 添加属性
          let pList = this.propertyObject[val]
          if (!pList) return
          pList.push({
            name: '',
            value: '',
            isNew: true,
          })
          break
        case 'chat-example':
          // 添加对话示例
          this.convDialog.show = true
          break
        case 'tone':
          // 声音复刻
          this.replicateDialog.show = true
          break
        default:
          break
      }
    },
    delMethod(property, index) {
      switch (property) {
        case 'base':
        case 'social':
          let pList = this.propertyObject[property]
          if (!pList) return
          let pItem = { ...pList[index] }
          if (pItem.isNew) {
            // 新增属性直接删除
            pList.splice(index, 1)
          } else {
            this.$confirm(
              `确定删除属性 - ${this.propertyObject[property][index].name}?`,
              `删除确认`,
              {
                confirmButtonText: '确定删除',
                cancelButtonText: '取消',
                confirmButtonClass: 'el-button--danger',
                type: 'warning',
                showClose: false,
              }
            )
              .then(() => {
                pList.splice(index, 1)
                this.changeMethod(property, pItem)
              })
              .catch(() => {})
          }
          break
        case 'chat-example':
          break
        default:
          break
      }
    },
    validateVariable(type, variable) {
      if (type === 'name') {
        // 检查长度不超过32个字符
        if (variable.length > 32) {
          return false
        }
        // 正则表达式：匹配中文、英文、数字、下划线
        const regex = /^[\u4e00-\u9fa5a-zA-Z0-9_]+$/
        return regex.test(variable)
      }
    },
    changeMethod(type, val) {
      // debugger
      switch (type) {
        case 'icon':
          let iconData = {
            roleId: this.roleInfo.id,
            image: val,
          }
          this.$utils.httpPost(
            this.$config.api.AIUI_ROLE_EDIT,
            JSON.stringify(iconData),
            {
              config: {
                headers: {
                  'Content-Type': 'application/json;charset=UTF-8',
                },
              },
              success: (res) => {
                this.getRoleInfo()
              },
              error: (err) => {
                this.getRoleInfo()
              },
            }
          )
          break
        case 'name':
          if (!this.validateVariable('name', val)) {
            this.$message.warning(
              '角色支持中文/英文/数字/下划线格式，不超过32个字符'
            )
            this.getRoleCategory()
            return
          }
          this.changeMethod('base', {
            name: 'name',
            value: val,
          })
          break
        // let data = {
        //   roleId: this.roleInfo.id,
        //   name: this.roleBaseForm.name,
        // }
        // this.$utils.httpPost(
        //   this.$config.api.AIUI_ROLE_EDIT,
        //   JSON.stringify(data),
        //   {
        //     config: {
        //       headers: {
        //         'Content-Type': 'application/json;charset=UTF-8',
        //       },
        //     },
        //     success: (res) => {
        //       this.getRoleInfo()
        //     },
        //     error: (err) => {
        //       this.getRoleInfo()
        //     },
        //   }
        // )
        // break
        case 'base':
        case 'task':
        case 'character':
        case 'language':
        case 'social':
          if (val && (!val.name || val.name.trim() === '')) {
            return
          }
          let refreshFlag = false
          let listRes = this.propertyObject[type]
          let p_data = {
            roleId: this.roleInfo.id,
            properties: [
              {
                categoryName: type,
                list: listRes.map((item) => {
                  const obj = {
                    name: item.name,
                    value: item.value,
                  }
                  if ('id' in item) {
                    obj.id = item.id
                  }
                  if (item.isNew) {
                    refreshFlag = true
                  }
                  return obj
                }),
              },
            ],
          }
          if (type === 'language') refreshFlag = true
          this.$utils.httpPost(
            this.$config.api.AIUI_ROLE_PROPERTY_EDIT,
            JSON.stringify(p_data),
            {
              config: {
                headers: {
                  'Content-Type': 'application/json;charset=UTF-8',
                },
              },
              success: (res) => {
                if (refreshFlag) {
                  this.getRoleCategory()
                }
              },
              error: (err) => {
                this.getRoleCategory()
              },
            }
          )
          break
        case 'tts':
          // 切换发音人后停止声音试听
          this.ttsRecorder && this.ttsRecorder.resetAudio()
          let tts_data = {
            roleId: this.roleInfo.id,
            volume: 50,
            speed: 50,
          }
          if (val.vcn) {
            //官方音色
            tts_data['vcn'] = val.vcn
          } else if (val.id) {
            // 声音复刻
            tts_data['vcn'] = 'x5_clone'
            tts_data['resId'] = val.resId
          }
          this.$utils.httpPost(
            this.$config.api.AIUI_ROLE_TTS_SAVE,
            JSON.stringify(tts_data),
            {
              config: {
                headers: {
                  'Content-Type': 'application/json;charset=UTF-8',
                },
              },
              success: (res) => {
                this.getRoleTts()
              },
              error: (err) => {},
            }
          )
          break
        case 'tone':
          this.ttsDialog.show = true
          break
        case 'volume':
        case 'speed':
          let tts_num_data = {
            roleId: this.roleInfo.id,
            vcn: this.roleTtsData.vcn,
            volume: this.roleTtsData.volume,
            speed: this.roleTtsData.speed,
          }
          if (this.roleTtsData.vcn === 'x5_clone') {
            tts_num_data['resId'] = this.roleTtsData.resId
          }
          this.$utils.httpPost(
            this.$config.api.AIUI_ROLE_TTS_SAVE,
            JSON.stringify(tts_num_data),
            {
              config: {
                headers: {
                  'Content-Type': 'application/json;charset=UTF-8',
                },
              },
              success: (res) => {},
              error: (err) => {
                this.getRoleTts()
              },
            }
          )
          break
        default:
          break
      }
    },
    synthetic() {
      if (this.roleTtsData.vcn === '' || !this.roleTtsData.vcn) {
        this.$message.error('请先选择发音人')
        return
      }
      if (this.roleTtsData.text === '') {
        this.$message.error('试听文本不能为空')
        return
      }
      setTimeout(() => {
        this.testStatus = 1
      })
      let that = this
      this.ttsRecorder && this.ttsRecorder.resetAudio()
      const baseUrl = '/aiui/web/user/chat'
      let formData = new FormData()
      formData.append('version', 'vtts')
      formData.append('expUid', this.uid)
      formData.append('query', this.roleTtsData.text)
      formData.append('speed', this.roleTtsData.speed)
      formData.append('volume', this.roleTtsData.volume)
      formData.append('vcn', this.roleTtsData.vcn)
      if (this.roleTtsData.vcn === 'x5_clone') {
        formData.append('resId', this.roleTtsData.resId)
        formData.append('ttsType', 3)
      } else {
        formData.append('ttsType', this.roleTtsData.ttsType || 3)
      }
      try {
        fetchEventSource(baseUrl, {
          method: 'POST',
          openWhenHidden: true,
          body: formData,
          signal: abortController.signal,
          async onopen(response) {
            if (response.ok) {
              console.log('连接了')
              setTimeout(() => {
                that.testStatus = 2
              }, 1000)
            } else {
            }
          },

          onmessage(event) {
            try {
              const result = JSON.parse(event.data || '{}')
              // console.log('result----------------', result)
              // 处理每一条信息
              if (result.code == '300001') {
                that.$message.error('请先登录')
                setTimeout(() => {
                  window.location.href = '/user/login'
                }, 2000)
              } else if (result.code == '0') {
                const data = result.data
                that.handleMessage(data)
              } else {
                that.$message.error(result.desc || '未知错误')
                that.stop()
              }
            } catch (e) {
              console.log(e)
            }
          },
          onclose() {
            console.info('断开了')
          },
          onerror(err) {
            console.log('报错了', err)
            that.stop()
            throw new Error(err)
          },
        })
      } catch (e) {
        console.log('fetchEventSource e', e)
      }
    },
    handleMessage(data) {
      if (data.type === 'tts') {
        if (data.audio) {
          this.ttsRecorder.result(data.audio)
        }
      }
    },
    stop() {
      setTimeout(() => {
        this.ttsRecorder && this.ttsRecorder.audioStop()
        // this.ttsRecorder && this.ttsRecorder.resetAudio()
      }, 0)
      abortController.abort()
      this.testStatus = 0
    },
    ttsRecorderStatusCb(status) {
      this.ttsRecorderStatus = status
    },
    openPhoto() {
      this.$refs.SelectIconDialog.show()
    },
    getDomToTop(el) {
      if (el.parentElement) {
        return this.getDomToTop(el.parentElement) + el.offsetTop
      }
      return el.offsetTop
    },
    getSecMenuTop() {
      let secDomList = []
      let menuList = [
        'base',
        'task',
        'character',
        'language',
        'social',
        'voice',
      ]
      let heightTop = this.getDomToTop(this.$refs.scrollContainer)
      menuList.map((item) => {
        let dom = document.getElementById(item)
        if (dom) {
          let top = this.getDomToTop(dom)
          let secMenuItem = {
            menu: item,
            top: top - heightTop, // 减去容器的偏移高度
          }
          secDomList.push(secMenuItem)
        }
      })
      console.log(secDomList, 'secDomList')
      this.domHeightList = secDomList
    },
  },
  created() {
    this.getRoleInfo().then(() => {
      this.getRoleCategory()
    })
    this.getAllRole()
    this.getRoleTts()
    this.ttsRecorder = new TTSRecorder(this.ttsRecorderStatusCb, true)
  },
  mounted() {
    this.getSecMenuTop()
    // console.log("this.domHeightList", this.domHeightList)

    this.$refs.scrollContainer.addEventListener('scroll', () => {
      let scrollTop = this.$refs.scrollContainer.scrollTop
      const scrollBottom = scrollTop + this.$refs.scrollContainer.clientHeight
      const contentHeight = this.$refs.scrollContainer.scrollHeight
      // 如果已经接近底部，强制选中最后一个菜单
      // if (contentHeight - scrollBottom <= 5) {
      //   this.activeMenu = this.domHeightList[this.domHeightList.length - 1].menu
      //   return
      // }
      for (let i = this.domHeightList.length - 1; i >= 0; i--) {
        if (scrollTop >= this.domHeightList[i].top) {
          this.activeMenu = this.domHeightList[i].menu
          break
        }
      }
    })
  },
  beforeDestroy() {
    // console.log('beforeDestroy', this.ttsRecorder, this.ttsRecorder.audioStop)
    this.ttsRecorder && this.ttsRecorder.audioStop()
  },
  watch: {
    ttsRecorderStatus(val) {
      // console.log('watch ttsRecorderStatus', val)
      if (val === 'endPlay') {
        // console.log('声音播放结束')
        this.testStatus = 0
      }
    },
    $route(to, from) {
      if (to.params.roleId !== from.params.roleId) {
        this.getRoleInfo().then(() => {
          this.getRoleCategory()
        })
        this.getAllRole()
        this.getRoleTts()
        this.ttsRecorder = new TTSRecorder(this.ttsRecorderStatusCb, true)
        abortController.abort()
      }
    },
  },
}
</script>
<style lang="scss" scoped>
.detail-header {
  height: 63px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 14px;
  border-bottom: 1px solid #e7e7e7;
  &-left {
    display: flex;
    align-items: center;
  }
  .back_icon_wrapper {
    display: flex;
    margin-right: 10px;
  }
  .text {
    font-size: 24px;
    font-family: PingFang SC, PingFang SC-500;
    font-weight: 500;
    color: #000000;
  }
  .role-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    margin-left: 12px;
    cursor: pointer;
  }
  .dropdown-role {
    font-size: 14px;
    font-family: PingFang SC, PingFang SC-400;
    font-weight: 400;
    color: #000000;
    cursor: pointer;
    padding: 4px 12px;
    border: 1px solid #efefef;
    border-radius: 28px;
    margin: 0 15px;
  }
}

.role-detail-content {
  display: flex;
  height: calc(100vh - 63px);
  background: $secondary-bgc;
  padding: 14px;
}

.role-form-content {
  padding: 0 35px;
  width: 100%;
  position: relative;
  height: 100%;
  overflow: auto;
  display: flex;
  background: #fff;
  border-radius: 14px;
}
.role-form {
  width: calc(100% - 360px);
  padding-bottom: 24px;

  h3 {
    font-size: 20px;
    font-family: PingFang SC, PingFang SC-500;
    font-weight: 500;
    color: #000000;
    margin: 22px 0;
  }
  h4 {
    font-size: 16px;
    color: #000000;
    margin: 22px 0;
  }
  .add-btn {
    width: calc(100% - 50px);
    border-radius: 10px;
  }
  :deep(.el-form-item) {
    margin-bottom: 0;
    .del {
      display: none;
    }
    &:hover {
      .del {
        display: inline-block;
      }
    }
  }
  :deep(.el-form-item__content) {
    margin-bottom: 24px;
    .el-input.base-name {
      width: calc(100% - 50px);
      position: relative;
    }
    .el-textarea {
      width: calc(100% - 50px);
      position: relative;
    }
    .del {
      margin-left: 12px;
      color: $primary;
      cursor: pointer;
    }
  }
  :deep(.el-textarea) {
    .el-textarea__inner {
      padding: 10px 16px;
      min-height: 44px !important;
      line-height: 24px;
    }
    .el-input__count {
      color: #909399;
      position: absolute;
      font-size: 12px;
      bottom: -32px;
      right: 0px;
    }
  }
  .tone-select {
    display: flex;
    align-items: center;
    gap: 8px;
  }
  .tone-card {
    width: 210px;
    height: 60px;
    padding: 6px 7px;
    display: inline-flex;
    align-items: center;
    border: 1px solid #ededed;
    border-radius: 6px;
    background: #ffffff;
    > img {
      width: 48px;
      height: 48px;
      border-radius: 6px;
    }
    > a {
      margin-left: auto;
    }
    .text {
      margin-left: 8px;
      display: inline-block;
      max-width: 105px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  .slider {
    width: 360px;
  }
}
.form-menu {
  position: sticky;
  margin-left: 120px;
  height: min-content;
  top: 24px;
  width: 174px;
  background: #ffffff;
  border-radius: 10px;
  padding: 7px 16px;
  box-shadow: 0px 0px 20.2px 0px rgba(194, 194, 194, 0.25);
  .hd1 {
    font-size: 14px;
    font-family: PingFang SC, PingFang SC-500;
    font-weight: 500;
    line-height: 22px;
    margin: 8px 0;
  }
  .hd2 {
    padding: 9px 0 9px 13px;
    font-size: 13px;
    font-family: PingFang SC, PingFang SC-400;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.6);
    margin: 8px 0;
    cursor: pointer;
    border-radius: 3px;
    &:hover {
      background: #f2f7ff;
    }
  }
  .hd2-active {
    font-weight: 500;
    color: #009bff;
    background: #f2f7ff;
    position: relative;
    &:before {
      background-color: #009bff;
      content: ' ';
      height: 40px;
      left: -15px;
      position: absolute;
      top: -2px;
      width: 3px;
      z-index: 1;
    }
  }
}

:deep(.el-slider) {
  .el-slider__bar {
    background-color: #009bff;
  }
  .el-slider__button {
    width: 12px;
    height: 12px;
    background: #ffffff;
    border: 2px solid #009bff;
  }
}
</style>
