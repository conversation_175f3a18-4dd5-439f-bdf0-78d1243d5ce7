<template>
  <CommonDialogBody
    abbBtnText="创建问答库"
    tagText="文档问答"
    jumpUrl="/studio/qaBank"
    :scrollHeight="362"
    switchKey="selected"
    :ragData="ragData"
    :ragDataCopy="ragDataCopy"
    :loading="loading"
    @selectchange="onSelectchange"
    @toDocCard="toDocCard"
  >
    <template #search>
      <el-input
        size="medium"
        class="search-area"
        placeholder="搜索"
        v-model.trim="searchVal"
        @keyup.enter.native="searchAppConfig"
        style="width: 267px"
      >
        <i
          @click.stop.prevent="searchAppConfig"
          slot="suffix"
          class="el-input__icon el-icon-search search-area-btn"
        />
      </el-input>
    </template>
  </CommonDialogBody>
</template>

<script>
import CommonDialogBody from '../../commonDialogBody/index.vue'

export default {
  name: 'KeywordQaPane',
  components: { CommonDialogBody },
  props: {
    appId: '',
    currentScene: Object,
  },
  data() {
    return {
      searchVal: '',
      ragData: [],
      ragDataCopy: [],
      originData: [],
      loading: false,
    }
  },
  created() {
    this.getAppRagConfig()
    this.debouncedSave = this.$utils.debounce(() => this.saveChangeData(), 500)
  },
  methods: {
    toDocCard(item) {
      console.log('toDocCard', item)
      window.open(`/studio/ragqa/${item.id}/localDoc`, '_blank')
    },

    saveChangeData() {
      let that = this
      let param = {
        botId: this.currentScene.botBoxId,

        addRepos: [],
        updateRepos: [],
        delRepos: [],
      }

      this.$utils.httpPost(
        this.$config.api.AIUI_BOT_CONFIG_SAVE_RAGREOPCONFIG,

        JSON.stringify(param),
        {
          config: {
            headers: {
              'Content-Type': 'application/json',
            },
          },
          success: (res) => {
            that.$message.success('保存成功')
          },
          error: (err) => {
            that.$message.error(err.desc)
          },
        }
      )
    },

    getAppRagConfig() {
      let that = this
      this.loading = true

      this.$utils.httpGet(
        this.$config.api.AIUI_BOT_CONFIG_GET_BOTRAGREPOS,
        {
          botId: this.currentScene.botBoxId,
          pageIndex: 1,
          pageSize: 1000,
        },
        {
          success: (res) => {
            console.log(res, '这个是知识库的res')
            that.loading = false
            let newRepos = (res.data.repos || []).map(({ repoid, ...rest }) => {
              return {
                ...rest,
                repoId: repoid,
              }
            })

            const colors = this.skillIconBgColors
            newRepos.forEach((item, index) => {
              item.color = colors[index % colors.length]
            })

            that.originData = JSON.parse(JSON.stringify(newRepos))
            that.ragDataCopy = JSON.parse(JSON.stringify(newRepos))

            that.ragData = newRepos || []
          },
          error: (res) => {},
        }
      )
    },
    searchAppConfig() {
      this.ragData = this.ragDataCopy.filter((it) =>
        it.name.includes(this.searchVal)
      )
    },
    saveSwitchData() {
      let that = this
      const addRepos = []
      const delRepos = []
      const updateRepos = []

      // 创建一个原始数据的映射，方便查找
      const originMap = {}
      this.originData.forEach((item) => {
        originMap[item.id] = item
      })

      // 遍历当前表格数据，找出变更
      this.ragData.forEach((currentItem) => {
        const originItem = originMap[currentItem.id]

        if (!originItem) {
          // 如果是新增的数据（如果有这种情况）
          addRepos.push(currentItem)
          return
        }

        // 检查selected字段的变化
        if (currentItem.selected !== originItem.selected) {
          if (currentItem.selected) {
            // 从未选中变为选中 -> 添加到addRepos
            addRepos.push(currentItem)
          } else {
            // 从选中变为未选中 -> 添加到delRepos
            delRepos.push(currentItem)
          }
        } else {
          // selected没有变化，检查isTop是否有变化
          if (currentItem.threshold !== originItem.threshold) {
            // 只有isTop变化 -> 添加到updateRepos
            updateRepos.push(currentItem)
          }
        }
      })

      const params = {
        botId: this.currentScene.botBoxId,
        addRepos,
        updateRepos,
        delRepos,
      }

      this.$utils.httpPost(
        this.$config.api.AIUI_BOT_CONFIG_SAVE_RAGREOPCONFIG,
        JSON.stringify(params),
        {
          config: {
            headers: {
              'Content-Type': 'application/json',
            },
          },
          success: (res) => {
            this.getAppRagConfig()
            that.$emit('saveSuccess')
            that.$message.success('保存成功')
          },
          error: (err) => {
            console.log('err', err)
            // reject(err.desc)
            that.$message.error(err)
          },
        }
      )
    },

    onSelectchange(item, isSelected) {
      const id = item.id
      this.ragData = this.ragData.map((item) => {
        if (item.id === id) {
          return {
            ...item,
            selected: isSelected,
          }
        } else {
          return { ...item }
        }
      })
      this.ragDataCopy = this.ragDataCopy.map((item) => {
        if (item.id === id) {
          return {
            ...item,
            selected: isSelected,
          }
        } else {
          return { ...item }
        }
      })
      this.saveSwitchData()
    },
  },
}
</script>

<style lang="scss" scoped></style>
