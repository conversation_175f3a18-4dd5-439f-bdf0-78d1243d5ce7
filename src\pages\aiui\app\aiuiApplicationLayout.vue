<template>
  <div class="app-container">
    <aiui-menu />
    <div class="app-content">
      <div class="app-content-container">
        <!-- <div class="app-menu-wrapper">
          <div class="app-menu">
            <div
              :class="{
                'app-menu-title': true,
                active: activeRoute === 'config',
              }"
            >
              配置
            </div>
            <div class="app-menu-content">
              <div>
                <router-link
                  :to="{ name: 'app-config' }"
                  active-class="active"
                  ><div class="app-menu-sub-title">语音识别</div></router-link
                >

                <recogSimple />
              </div>
              <div>
                <router-link
                  :to="{ name: 'app-semantic' }"
                  active-class="active"
                  ><div class="app-menu-sub-title">语义模型</div></router-link
                >
                <semanticModelSimple />
              </div>
              <div>
                <router-link
                  :to="{ name: 'app-character' }"
                  active-class="active"
                >
                  <div class="app-menu-sub-title">回复角色</div></router-link
                >

                <charactorSimple />
              </div>
            </div>
          </div>
          <div class="app-menu">
            <router-link :to="{ name: 'app-tool' }">
              <div
                :class="{
                  'app-menu-title': true,
                  active: activeRoute === 'download',
                }"
              >
                资源下载
              </div></router-link
            >
          </div>
          <div class="app-menu">
            <div
              :class="{
                'app-menu-title': true,
                active: activeRoute === 'statistic',
              }"
            >
              用量统计
            </div>
            <div class="app-menu-content" style="padding-top: 0px">
              <div>
                <router-link
                  :to="{ name: 'app-statistic-service-index' }"
                  active-class="active"
                  ><div class="app-menu-sub-title">调用量统计</div></router-link
                >
              </div>
              <div>
                <router-link :to="{ name: 'app-users' }" active-class="active"
                  ><div class="app-menu-sub-title">设备量统计</div></router-link
                >
              </div>
              <div>
                <router-link
                  :to="{ name: 'app-online-device' }"
                  active-class="active"
                  ><div class="app-menu-sub-title">设备量明细</div></router-link
                >
              </div>
            </div>
          </div>
        </div> -->
        <app-header />
        <div class="os-main">
          <router-view
            :contentReviewConfig="contentReviewConfig"
            :subAccount="subAccount"
            :rightTestOpen="rightTestOpen"
            :subAccountEditable="subAccountEditable"
            :subAccountHasCreateAuth="subAccountHasCreateAuth"
          ></router-view>
        </div>
      </div>
      <div
        class="os-side-right"
        :class="{ 'os-side-right-open': rightTestOpen }"
        v-if="$route.name === 'app-config' || $route.name === 'sub-app-config'"
      >
        <right-test-close
          v-if="!rightTestOpen"
          :rightTestOpen="rightTestOpen"
          debugType="app"
        ></right-test-close>

        <!-- 新sos应用 -->
        <template v-if="currentScene && currentScene.sos === true">
          <app-debug45 v-show="rightTestOpen"></app-debug45>
        </template>
        <!-- 老的aiui大模型应用 -->
        <template v-if="showAIUILLMDebug">
          <app-debug45 v-show="rightTestOpen"></app-debug45>
        </template>

        <template v-if="showAIUIGeneralDebug">
          <skill-debug v-show="rightTestOpen" debugType="app" />
        </template>
        <feedBackHover :rightTestOpen="rightTestOpen" />
      </div>
    </div>
  </div>
</template>

<script>
import RightTestClose from '@C/rightTestClose'
import feedBackHover from '@C/feedBackHover'
import { mapGetters } from 'vuex'
import { bus } from '@U/bus'
import appIcon from '@A/images/aiui5/app/<EMAIL>'
import appActiveIcon from '@A/images/aiui5/app/<EMAIL>'

import devIcon from '@A/images/aiui5/app/<EMAIL>'
import devActiveIcon from '@A/images/aiui5/app/<EMAIL>'

import statisticsIcon from '@A/images/aiui5/app/<EMAIL>'
import statisticsActiveIcon from '@A/images/aiui5/app/<EMAIL>'
import aiuiMenu from '@C/aiuiMenu'
import appHeader from './appHeader'

// import recogSimple from './config/sider/recog/index'
// import semanticModelSimple from './config/sider/semanticModelSimple'
// import charactorSimple from './config/sider/charactorSimple'
import RECOGNITION_LLM_SEMANTIC_State from '@U/AIUIState/RECOGNITION_LLM_SEMANTIC_State'
import RECOGNITION_LLM_SEMANTIC_SYNTHESIS_State from '@U/AIUIState/RECOGNITION_LLM_SEMANTIC_SYNTHESIS_State'

export default {
  data() {
    return {
      paidSources: [],
      contentReviewConfig: {},
      menus: [
        {
          name: '应用',
          icon: appIcon,
          activeIcon: appActiveIcon,
          menus: [
            {
              key: 'app-info',
              value: '应用信息',
              icon: 'ic-mn-basic-info',
              index: 0,
            },
            {
              key: 'app-config',
              value: '应用配置',
              icon: 'ic-mn-device-skill',
              index: 1,
            },
          ],
        },
      ],
      publishInfo: {
        name: '上线',
        menus: [
          {
            key: 'app-publish',
            value: '更新发布',
            icon: 'ic-mn-launch',
            index: 5,
          },
          {
            key: 'app-version',
            value: '版本管理',
            icon: 'ai-mn-ip-list',
            index: 6,
          },
        ],
      },
      statistic: {
        name: '应用数据统计',
        icon: statisticsIcon,
        activeIcon: statisticsActiveIcon,
        menus: [
          {
            key: 'app-statistic-service-index',
            value: '服务统计',
            icon: 'ai-mn-data',
            index: '7',
          },
        ],
      },
      appMenus: [
        {
          key: 'app-users',
          value: '设备统计',
          icon: 'ai-mn-data-user',
          index: '8',
        },
      ],
      sourceMenu: [
        {
          key: 'app-sources',
          value: '信源授权统计',
          icon: 'ai-mn-dict',
          index: '9',
        },
      ],
      updateMenu: {
        name: '升级',
        menus: [
          {
            key: 'app-ota-update',
            value: '固件升级',
            icon: 'ic-mn-update',
            index: '10',
          },
        ],
      },
      originModelType: '',
    }
  },
  computed: {
    ...mapGetters({
      rightTestOpen: 'studioSkill/rightTestOpen',
      subAccount: 'user/subAccount',
      subAccountAppAuths: 'aiuiApp/subAccountAppAuths',
      currentScene: 'aiuiApp/currentScene',
      context: 'aiuiApp/context',
    }),
    appName() {
      return this.$store.state.aiuiApp.app.appName
    },
    appPlatformNum() {
      return this.$store.state.aiuiApp.app.platformNum
    },
    subAccountEditable() {
      const val =
        this.subAccountAppAuths[this.$route.params.appId] == 2 ? false : true
      this.$store.dispatch('aiuiApp/setSubAccountEditable', val)
      return val
    },
    subAccountHasCreateAuth() {
      return this.$store.state.aiuiApp.subAccountHasCreateAuth
    },

    showAIUILLMDebug() {
      if (this.currentScene && this.currentScene.point && this.context) {
        const point = this.currentScene.point
        const context = this.context
        if (
          this.currentScene.sos !== true &&
          (context.isCurrentState(RECOGNITION_LLM_SEMANTIC_State) ||
            context.isCurrentState(RECOGNITION_LLM_SEMANTIC_SYNTHESIS_State))
        ) {
          return true
        }
      }
      return false
    },
    showAIUIGeneralDebug() {
      if (this.currentScene && this.currentScene.point && this.context) {
        const point = this.currentScene.point
        const context = this.context
        if (
          this.currentScene.sos !== true &&
          !context.isCurrentState(RECOGNITION_LLM_SEMANTIC_State) &&
          !context.isCurrentState(RECOGNITION_LLM_SEMANTIC_SYNTHESIS_State)
        ) {
          return true
        }
      }
      return false
    },
  },

  // watch: {
  //   subAccountEditable(val) {
  //     debugger
  //     this.$store.dispatch('aiuiApp/setSubAccountEditable', val)
  //   },
  // },

  created() {
    this.$store.dispatch('aiuiApp/setLimitCount')
    this.$store.dispatch('aiuiApp/setId', this.$route.params.appId)

    this.$store.dispatch('aiuiApp/setQcAuth')
    // this.subAccountSKilInit()
    console.log('this.$route', this.$route)
  },

  methods: {},
  components: {
    RightTestClose,
    feedBackHover,
    aiuiMenu,
    appHeader,

    // recogSimple,
    // semanticModelSimple,
    // charactorSimple,
  },
}
</script>

<style lang="scss" scoped>
.os_side_right {
  margin-top: 63.5px;
  height: calc(100vh - 128px);
  overflow: hidden;
  .collapse-btn-wrap {
    border-top: 1px solid $grey007;
  }
  .debug-wrap {
    border-top: 1px solid $grey007;
  }
}
.app-menu-wrapper {
  width: 330px;
  min-width: 330px;
  max-height: calc(100vh - 70px);
  overflow: auto;
  background: #f0f3f6;
  .app-menu {
    border-bottom: 1px solid #e1e1e1;
    .app-menu-title {
      width: 100%;
      height: 52px;
      line-height: 52px;
      padding-left: 20px;
      color: #17171e;
      font-weight: 500;
      font-size: 16px;
      &.active {
        background: #e2e9f4;
        border-left: 6px solid $primary;
      }
    }
    .app-menu-content {
      padding: 10px 16px 28px 30px;
      .app-menu-sub-title {
        height: 46px;
        line-height: 46px;
        cursor: pointer;
        color: #555454;
        &:hover {
          color: $primary;
        }
      }
      .active {
        .app-menu-sub-title {
          color: $primary;
        }
      }
    }
  }
}

// @media screen and (max-width: 1601px) {
//   .os-aside {
//     width: 180px;
//   }
// }

.app-content {
  flex: 1;
  overflow: hidden;
  display: flex;
}

.app-content-container {
  // display: flex;
  // flex: 1;
  // box-sizing: border-box;
  // min-width: 0;
  // overflow: hidden;
  flex: 1;
}
</style>
