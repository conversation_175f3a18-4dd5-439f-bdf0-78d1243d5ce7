<template>
  <div style="height: 100%">
    <div style="padding: 0 60px" v-if="showCategorySearch">
      <el-tabs v-model="businessType" @tab-click="handleCategoryClick">
        <el-tab-pane
          v-for="(item, index) in categoryTypes"
          :label="item.label"
          :name="item.value"
          :key="index"
        ></el-tab-pane>
      </el-tabs>
    </div>
    <div class="os-scroll" style="height: calc(100% - 40px)">
      <div class="store-skills-row" v-loading="skillsData.loading">
        <div
          class="store-skills-col ib"
          v-for="(skill, index) in skillsData.list"
          :key="index"
        >
          <div class="os-store-skill" @click="toSkillDetail(skill)">
            <div class="os-store-skill-thumb">
              <img v-if="skill.url" :src="skill.url" />
              <span v-else>{{
                skill.zhName && skill.zhName.substr(0, 1)
              }}</span>
            </div>
            <div class="os-store-skill-content">
              <p class="os-store-skill-name" :title="skill.zhName">
                {{ skill.zhName }}
              </p>
              <p class="os-store-skill-provider">
                {{
                  skill.provider !== 'undefined' ? skill.provider || '-' : '-'
                }}
              </p>
              <p class="os-store-skill-desc" :title="skill.briefIntroduction">
                {{ skill.briefIntroduction }}
              </p>
            </div>
            <div class="os-store-skill-tag-area">
              <span v-if="skill.count" class="os-store-skill-tag">
                {{ skill.count }}个信源
              </span>
              <span v-if="skill.dialectinfo" class="os-store-skill-dialect-tag">
                {{ skill.dialectinfo }}
              </span>
            </div>
          </div>
        </div>
      </div>
      <div class="store-skill-page">
        <el-pagination
          v-if="showPagination"
          ref="pagination"
          class="txt-al-c"
          @current-change="getSkills"
          :current-page="skillsData.page"
          :page-size="skillsData.size"
          :total="skillsData.total"
          :layout="pageLayout"
        >
        </el-pagination>
      </div>
      <div class="empty-skill-tip" v-if="skillsData.total === 0">
        <p>没有相应的技能</p>
        <p>
          这里的技能还是不能满足您的需求？您还可以
          <router-link :to="{ path: '/studio/skill' }"
            >自定义您的技能</router-link
          >
        </p>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'store-skills',
  props: ['search'],
  data() {
    return {
      sortType: 0,
      businessType: '',
      icon: {
        efficiencyTool: 10100,
        lifeService: 10200,
        vedioRead: 10300,
        education: 10400,
        healthy: 10500,
        entertainment: 10600,
        childrenEducation: 10700,
        finance: 10800,
        smartHome: 10900,
        qa: 20100,
      },
      provider: '',
      skillsData: {
        loading: true,
        total: -1,
        page: 1,
        size: 24,
        list: [],
      },
      needSearch: false,
      categoryTypes: [],
      showCategorySearch: true,
    }
  },
  watch: {
    $route: function (val, oldVal) {
      this.handleSkillType(val.params.skillType)
    },
  },
  computed: {
    pageLayout() {
      if (this.skillsData.total / this.skillsData.size > 7) {
        return 'prev, pager, next, jumper'
      }
      return 'prev, pager, next'
    },
    showPagination() {
      return this.skillsData.total > this.skillsData.size
    },
  },
  created() {
    this.handleSkillType(this.$route.params.skillType)
    // 获取各种二级分类的类型
    this.getSecondCategoryTypes()
  },
  methods: {
    handleSkillType(type) {
      console.log(type)
      switch (type) {
        case 'all':
          this.sortType = 0
          this.businessType = '0'
          this.provider = ''
          this.needSearch = true
          this.showCategorySearch = true
          break
        case 'new':
          this.sortType = 1
          this.businessType = '0'
          this.provider = ''
          this.needSearch = false
          this.showCategorySearch = true
          break
        case 'hot':
          this.sortType = 2
          this.businessType = '0'
          this.provider = ''
          this.needSearch = false
          this.showCategorySearch = true
          break
        // 车载，属于行业技能，不设二级分类
        case 'vehicle':
          this.sortType = 0
          this.businessType = '0'
          this.provider = 3
          this.needSearch = false
          this.showCategorySearch = false
          break
        case 'official':
          this.sortType = 0
          this.businessType = '0'
          this.provider = 1
          this.needSearch = false
          this.showCategorySearch = true
          break
        case 'other':
          this.sortType = 0
          this.businessType = '0'
          this.provider = 2
          this.needSearch = false
          this.showCategorySearch = true
          break
        case 'dialect':
          this.sortType = 0
          this.businessType = '0'
          this.provider = 4
          this.needSearch = false
          this.showCategorySearch = true
          break
        case 'open':
          this.sortType = 0
          this.businessType = '0'
          this.provider = 5
          this.needSearch = false
          this.showCategorySearch = true
          break
        default:
          this.sortType = 0
          this.businessType = this.icon[type]
          this.provider = ''
          this.needSearch = false
          this.showCategorySearch = true
          break
      }

      this.getSkills(1)
    },
    getSkills(page) {
      console.log('in getSkills, this.businessType', this.businessType)
      this.skillsData.loading = true
      this.$utils.httpGet(
        this.$config.api.AIUI_STORE_SKILLS,
        {
          sortType: this.sortType,
          businessType: this.businessType === '0' ? '' : this.businessType,
          search: this.needSearch ? this.search : '',
          provider: this.provider,
          pageIndex: page || this.skillsData.page,
          pageSize: this.skillsData.size,
        },
        {
          success: (res) => {
            this.skillsData.list = res.data.skills
            this.skillsData.total = res.data.count
            this.skillsData.page = res.data.pageIndex
            this.skillsData.size = res.data.pageSize
            this.skillsData.loading = false
          },
          error: (err) => {
            console.log('page=>>')
            console.log(err)
          },
        }
      )
    },
    toSkillDetail(skill) {
      this.$router.push({
        name: 'store-skill',
        params: { skillId: skill.id },
        query: { type: skill.type },
      })
    },
    getSecondCategoryTypes() {
      let self = this
      this.$utils.httpGet(
        this.$config.api.AIUI_STORE_SKILL_TYPES,
        {},
        {
          success: (res) => {
            // res.data[20100] = '开放问答'
            let categoryTypes = Array.prototype.map.call(
              Object.keys(res.data),
              (item, index) => {
                return {
                  value: item,
                  label: res.data[item],
                }
              }
            )
            categoryTypes = [
              { label: '全部', value: '0' },
              ...categoryTypes,
              { label: '开放问答', value: '20100' },
            ]
            this.categoryTypes = categoryTypes
          },
          error: (err) => {},
        }
      )
    },
    handleCategoryClick() {
      this.getSkills(1)
    },
  },
}
</script>

<style lang="scss" scoped>
.store-skills-row {
  width: 100%;
  padding-left: 48px;
  padding-bottom: 20px;
  margin-left: -24px;
  overflow: auto;
}
.store-skills-col {
  width: 29%;
  margin: 32px 24px 0;
}
@media screen and (max-width: 1601px) {
  .store-skills-col {
    width: 45%;
  }
}
@media screen and (max-width: 1060px) {
  .store-skills-col {
    width: inherit;
  }
}
.store-skill-page {
  height: 130px;
  margin-bottom: 40px;
  display: block;
  align-items: center;
  flex: auto;
}
.os-store-skill {
  min-width: 330px;
  background-color: #fff;
  height: 102px;
  border-radius: 2px;
  position: relative;
  padding: 16px;
  cursor: pointer;
  overflow: hidden;
  box-sizing: border-box;
  &:hover {
    box-shadow: 0px 8px 21.5px 3.5px rgba(157, 202, 243, 0.24);
  }
  &-thumb {
    border-radius: 6px;
    width: 70px;
    height: 70px;
    float: left;
    // line-height: 70px;
    overflow: hidden;
    text-align: center;
    font-size: 20px;
    border: 1px solid $grey2;
    display: flex;
    align-items: center;
    justify-content: center;

    img {
      width: 100%;
      height: 100%;
    }
  }
  &-content {
    float: left;
    margin-left: 16px;
  }
  &-name {
    font-size: 16px;
    color: $semi-black;
    font-weight: 500;
    margin-bottom: 10px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 200px;
  }
  &-provider {
    font-size: 12px;
    margin-bottom: 2px;
    color: $grey5;
  }
  &-desc {
    font-size: 12px;
    color: $grey5;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 200px;
  }

  &-tag-area {
    position: absolute;
    top: 16px;
    right: 16px;
  }
  &-tag {
    font-size: 12px;
    color: $grey5;
    background-color: $grey4-15;
    border-radius: 12px;
    padding: 0 10px;
    height: 20px;
    line-height: 20px;
  }

  &-dialect-tag {
    font-size: 12px;
    color: #fff;
    background-color: $success;
    border-radius: 12px;
    padding: 0 10px;
    height: 20px;
    line-height: 20px;
  }
}
.empty-skill-tip {
  text-align: center;
  line-height: 50px;
}
</style>
