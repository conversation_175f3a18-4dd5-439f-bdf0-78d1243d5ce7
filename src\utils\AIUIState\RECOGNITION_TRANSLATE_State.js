import State from './State'
import RECOGNITION_TRANSLATE_SYNTHESIS_State from './RECOGNITION_TRANSLATE_SYNTHESIS_State'

class RECOGNITION_TRANSLATE_State extends State {
  constructor() {
    super('1,4')
  }
  //     RECOGNITION: '1',
  //     RECOGNITION_SEMANTIC: '1,2',
  //     RECOGNITION_SEMANTIC_SYNTHESIS: '1,2,8',
  //     RECOGNITION_TRANSLATE: '1,4',
  //     RECOGNITION_TRANSLATE_SYNTHESIS: '1,4,8',
  //     RECOGNITION_LLM_SEMANTIC: '1,13',
  //     RECOGNITION_LLM_SEMANTIC_SYNTHESIS: '1,13,14',
  //     RECOGNITION_POSTPROCESS: '1,3',
  //     RECOGNITION_SEMANTIC_POSTPROCESS: '1,2,3',
  //     RECOGNITION_SEMANTIC_POSTPROCESS_SYNTHESIS: '1,2,3,8',
  //     RECOGNITION_POSTPROCESS_SYNTHESIS: '1,3,8',
  //     RECOGNITION_SYNTHESIS: '1,8'
  handle(context, action) {
    switch (action) {
      case 'to_RECOGNITION_TRANSLATE_SYNTHESIS':
        context.setState(new RECOGNITION_TRANSLATE_SYNTHESIS_State())
        break

      default:
        super.handle(context, action)
    }
  }
}
export default RECOGNITION_TRANSLATE_State
