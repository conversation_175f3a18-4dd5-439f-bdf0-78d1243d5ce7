.skill-type {
  margin-top: 1%;
  margin-bottom: 1%;
  > a {
    position: relative;
    margin-right: 1%;
    &:nth-last-child(1) {
      margin-right: 0;
    }
  }
}

.item-title {
  font-size: 16px;
  font-weight: 500;
  padding-left: 10px;
  margin-bottom: 8px;
  border-left: 2px solid $primary;
}
.item-desc {
  color: #a5a5b8;
  margin-bottom: 18px;
}
.skill-header {
  height: 40px;
}

.add-skill-tab {
  a {
    display: inline-block;
    width: 108px;
    height: 40px;
    line-height: 40px;
    cursor: pointer;
    color: unset;
    text-align: center;
    &:hover {
      color: $primary;
    }
    &:visited {
      color: $primary;
    }
    &:active {
      color: $warning;
    }
  }
  .active {
    position: relative;
    color: $primary;
    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      display: inline-block;
      width: 88px;
      height: 2px;
      background-color: #1f90fe;
      border-radius: 2px;
      transform: translateX(-50%);
    }
  }
}

.skill-tab {
  // width: 465px;
  height: 40px;
  border-bottom: 1px solid $grey2;

  li {
    display: inline-block;
    height: 40px;
    line-height: 40px;
    cursor: pointer;

    &:hover {
      color: $primary;
    }
  }
  .active {
    color: $primary;
    border-bottom: 1px solid $primary;
  }
  span {
    margin: 0 8px;
    color: $grey4;
  }
}
.has-public-repo {
  width: auto;
}
.search-area {
  width: 200px;
  margin-top: 6px;
}
.skill-wrap {
  // overflow: hidden;
  // min-height: 200px;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  max-height: calc(85vh - 160px);
  padding-bottom: 20px;
  overflow-y: auto;
  .empty-card {
    width: 280px;
  }
  .skill-type-title {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    margin: 25px 0 5px;
    width: 100%;
    .title {
      padding-left: 12px;
      font-weight: bold;
      color: #333333;
      font-size: 16px;
    }
  }
  .store-skill {
    padding-left: 0;
    padding-right: 0;
  }
}
.skill {
  position: relative;
  // float: left;
  // min-width: 221px;
  // min-width: 200px;
  height: 94px;
  // margin: 18px 0 0 10px;
  padding: 20px 10px 10px 10px;
  border-radius: 5px;
  // border: 1px solid $grey2;
  // box-shadow: 0px 5px 20px 0px rgba(165, 165, 165, 0.3);
  .label-wrap {
    border-top: 1px solid #afbac4;
  }

  transition: 0.1s;
  cursor: pointer;
  .ic-r-edit {
    display: none;
  }
  &:hover {
    .ic-r-delete {
      display: inline-block;
    }
    .ic-r-edit {
      display: inline-block;
    }
  }
}
.skill-active {
  // border: 1px solid #1f90fe;
  // box-shadow: 0px 5px 20px 0px rgba(31, 144, 254, 0.3);
  .label-wrap {
    border-top: 1px solid #8bb6ff;
  }
}

// activeTab: 0
.activeTab0 {
  > a {
    display: inline-flex;
  }
  .device-skill-thumb {
    width: 50px;
    text-align: center;
    align-items: center;
    align-self: center;
    align-content: center;
    margin-right: 15px;
    img {
      width: 30px;
      height: 30px;
      border-radius: 15px;
    }
    .skill-icon {
      //line-height: 32px;
    }
  }

  .device-skill-content {
    //margin-left: 10px;
    font-size: 14px;
    color: #262626;
    font-weight: 500;
    line-height: 50px;
    width: 95px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

// character-card
.skill.charater {
  min-width: 262px !important;
  height: 94px;
  border-radius: 6px;
  position: relative;
  padding: 27px 20px;
  overflow: hidden;
  box-sizing: border-box;
  // border: 1px solid #8bc1f4;
  cursor: pointer;
  box-sizing: border-box;
  // &:hover {
  //   border: 1px solid rgba(23, 132, 233, 1);
  //   box-shadow: 0 0 0 2px #e3f0fc;
  //   & .ic {
  //     visibility: visible;
  //   }
  // }
  .device-skill-thumb {
    float: left;
    width: 32px;
    height: 30px;
    img {
      width: 30px;
      height: 30px;
      border-radius: 15px;
    }
    .skill-icon {
      border-radius: 10px;
      font-size: 24px;
      text-align: center;
      line-height: 32px;
      width: 32px;
      height: 32px;
    }
  }
  .device-skill-content {
    float: left;
    margin-left: 10px;
    font-size: 14px;
    color: $semi-black;
    font-weight: 500;
    line-height: 32px;
    width: 95px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.ic {
  position: absolute;
  // right: 10px;
  font-size: 16px;
  color: $grey4;
  &.el-icon-success {
    top: 8px;
    right: 8px;
    color: rgba(23, 132, 233, 1);
    display: none;

    &.active {
      display: block;
    }
  }
}

.hover-area {
  display: none;
  opacity: 0;
  //   transition:  0.3s ease-in-out;
  transition: opacity 0.5s, transform 0.5s, -webkit-transform 0.5s;
  background-color: #fff;
  text-align: center;
  height: 88px;
  border-radius: 6px;
  position: relative;
  padding: 30px 20px 0 20px;
  overflow: hidden;
  box-sizing: border-box;
  //   width: 98%;
  cursor: pointer;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  //   border: 1px solid #8bc1f4;
  &.active {
    display: block;
    opacity: 1;
  }
  .ic {
    position: absolute;
    font-size: 16px;
    color: $grey4;
  }
  .ic-r-edit {
    bottom: 12px;
    right: 12px;
    // visibility: visible;
    cursor: pointer;
  }
  .el-icon-success {
    top: 5px;
    right: 5px;
    color: rgba(23, 132, 233, 1);
    display: none;

    &.active {
      display: block;
    }
  }
  :deep(.el-button--mini) {
    display: inline-block;
    min-width: 40%;
    span {
      display: inline-block;
      width: 100%;
      height: 100%;
    }
  }
  .check-btn {
    border-color: #f5f5f6;
    background: #f5f5f6;
    color: rgba(38, 38, 38, 1);
    margin-left: 0px;
  }
}
.device-skill.add-btn {
  cursor: pointer;

  .icon {
    display: block;
    width: 24px;
    height: 24px;
    box-sizing: border-box;
    font-size: 24px;
    color: #b8babf;
    margin: 0 auto;
  }
  p {
    text-align: center;
    margin-top: 3px;
    width: 100%;
    height: 22px;
    font-size: 14px;
    font-weight: 400;
    color: rgba(184, 186, 191, 1);
    line-height: 22px;
  }
}
//end
.skill-container .skill {
  width: 100%;
}

.skill-header-shrink {
  position: relative;
  width: 100%;
  display: block;
  transform: translateX(0%);
  transition: all 0.4s cubic-bezier(0.42, 0, 1, 1) 0s;
  > ul {
    max-width: 80%;
    :deep(> span) {
      margin: 0 auto !important;
      position: relative;
    }
  }
  > .search-area {
    max-width: 25%;
  }
}

.skill-container .skill {
  // width: 230px;
}

.add-skill-wrap {
  .skill {
    min-width: 262px;
  }

  .skill-info {
    width: 160px;
  }
  .skill-title {
    max-width: 65%;
  }
}
.skill-icon {
  float: left;
  width: 44px;
  height: 44px;
  line-height: 42px;
  color: $semi-black;
  text-align: center;
  margin-right: 10px;
  font-style: normal;
  font-size: 24px;
  font-weight: 500;
  color: rgba(23, 132, 233, 1);
  border: 1px solid #f3f3f3;
  border-radius: 8px 8px 0 8px;

  border: 1px solid #f1f1f1;
  border-radius: 4px;
  box-shadow: 1px 1px 2.91px 0px rgba(189, 201, 222, 0.3);
}
.skill-info {
  // float: left;
  // width: 65%;
}
.skill-title {
  max-width: 110px;
  color: $semi-black;
  font-size: 14px;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  vertical-align: middle;
  font-weight: 600;
}
.private-skill,
.private-repo {
  .skill-title {
    margin-top: 8px;
  }
  .skill-source {
    margin-top: 10px;
    vertical-align: middle;
  }
  .skill-update {
    margin-top: 15px;
    margin-bottom: 5px;
    vertical-align: middle;
    font-size: 12px;
  }
}
.skill-source {
  font-size: 12px;
}
.skill-disabled {
  color: #ccc;
  cursor: not-allowed;
}
.skill-desc {
  font-size: 13px;
  color: $grey5;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 180px;
  padding-top: 10px;
}
.skill-config {
  position: absolute;
  bottom: 10px;
  right: 10px;
  color: $grey4;
  font-size: 18px;
  cursor: pointer;
}
.ic-r-delete {
  display: none;
  top: auto;
  bottom: 10px;
  right: 50px;
}

.empty-skill-tips {
  color: $grey5;
  margin-top: 30px;
  width: 100%;
  text-align: center;
}
.skill-footer {
  position: relative;
}
.add-skill {
  margin-top: 8px;
}
.pagination {
  position: absolute;
  top: 0;
  right: 0;
}

.add-skill-desc {
  color: $grey4;
  margin-top: 0px;
  margin-bottom: 32px;
}
.other-skill-source-tips {
  color: $warning;
  margin-top: -27px;
  margin-bottom: 32px;
}
.add-skill-pagination {
  text-align: center;
}
.skill-used {
  position: relative;
  overflow: hidden;
  border: 1px solid $primary;

  &:hover {
    border: 1px solid $primary;
  }

  // &::after {
  //   content: "";
  //   position: absolute;
  //   right: -5px;
  //   bottom: -5px;
  //   width: 10px;
  //   height: 10px;
  //   background: $primary;
  //   transform: rotate(45deg);
  // }
  .ic-r-tick-thin {
    position: absolute;
    right: 5px;
    top: 5px;
    color: #e4e7ed;
    font-size: 12px;
    color: rgba(23, 132, 233, 1);
  }
}

.threshold-label {
  display: inline-block;
  width: 55px;
  font-weight: 600;
  color: $grey5;
}
.threshold-slider {
  display: inline-block;
  vertical-align: middle;
  width: 450px;
}
.threshold-desc {
  color: $grey5;
  margin: 25px 0;
}
.source-list {
  border-top: 1px solid $grey2;
  margin-bottom: 30px;
}
.source-item {
  height: 72px;
  padding: 12px;
  cursor: move;
  border-bottom: 1px solid $grey2;
}
.ic-r-menu {
  color: $grey4;
  font-size: 18px;
  line-height: 48px;
  margin-right: 12px;
}
.source-logo {
  max-width: 48px;
  max-height: 48px;
  border-radius: 50%;
  margin-right: 8px;
}
.source-title {
  color: $semi-black;
  font-weight: 500;
  margin-top: 3px;
}
.source-desc {
  color: $grey5;
  font-size: 12px;
  margin-top: 4px;
}
.source-used {
  margin-top: 12px;
}
.private-skill-number {
  font-size: 12px;
  color: $grey5;
}
.add-threshold-desc {
  color: $grey4;
  margin-bottom: 32px;
}
.add-skill-title {
  line-height: 24px;
  font-size: 18px;
  color: #262626;
  font-weight: 500;
}
.add-qa-threashold {
  color: $grey4;
  margin-bottom: 32px;
  margin-top: 0px;
}
.skill-version-title {
  padding-top: 20px;
  border-top: 1px solid #ccc;
  margin-bottom: 10px;
}
.add-skill-search {
  &.charater {
    width: 75%;
    float: right;
    :deep(.el-input__inner) {
      height: 36px;
      line-height: 36px;
    }
  }
}
.table-area {
  height: 328px;
  overflow: hidden;
  :deep(.el-tabs__content) {
    height: 328px;
    overflow-y: scroll;
  }
}
.must-answer-wrap {
  margin: 0 0 16px;
  color: $grey5;
  font-weight: 600;
  :deep(.el-tooltip) {
    color: $grey3;
  }
  :deep(.el-switch) {
    margin-left: 18px;
  }
}
.cursor-default {
  cursor: default !important;
}

.label-wrap {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 12px auto 0;
  padding: 0 5px;
  text-align: center;
  border-top: 1px solid #e4e7ed;
}
.update-state-label {
  position: absolute;
  padding: 2px 4px;
  top: 47px;
  color: #fff;
  font-size: 11px;
  border-radius: 0px 0px 5px 5px;
  width: 42px;
  white-space: nowrap;
}
.title-btm-btn-group {
  display: flex;
  color: #8eb8fe;
  a,
  p {
    margin: 0;
    font-size: 13px;
  }
  span {
    display: inline-block;
    padding-left: 2px;
  }
}
.skill-config-new {
  color: $primary;
  font-size: 25px;
  cursor: pointer;
  padding-top: 7px;
}

.ability-tag {
  padding: 3px 12px;
  color: #fff;
  background: rgba(33, 116, 255, 0.5);
  font-size: 14px;
  border-radius: 4px;
}

.switch-wrap {
  position: absolute;
  top: 20px;
  right: 10px;
}
