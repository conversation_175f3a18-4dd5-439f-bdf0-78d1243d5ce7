<template>
  <el-date-picker
    class="date-picker-wrap"
    v-model="value"
    type="date"
    :editable="false"
    :clearable="false"
    size="medium"
    placeholder="选择日期"
    :picker-options="pickerOptions"></el-date-picker>
</template>
<script>
  const yesterday = (new Date()).getTime() - 3600 * 1000 * 24
  export default {
    name: 'date-picker',
    data() {
      return {
        value: '',
        pickerOptions: {
          disabledDate(time) {
            return time.getTime() > yesterday
          },
          shortcuts: [{
            text: '昨日',
            onClick(picker) {
              picker.$emit('pick', yesterday)
            }
          }, {
            text: '前一周',
            onClick(picker) {
              const date = new Date()
              date.setTime(date.getTime() - 3600 * 1000 * 24 * 8)
              picker.$emit('pick', date)
            }
          }, {
            text: '一月前',
            onClick(picker) {
              const date = new Date()
              date.setTime(date.getTime() - 3600 * 1000 * 24 * 31);
              picker.$emit('pick', date)
            }
          }]
        },
      }
    },
    watch: {
      value(val) {
        this.value = this.$utils.dateFormat(val, 'yyyy-MM-dd')
        this.$emit('setTime', this.value)
      }
    },
    created() {
      this.initTime()
    },
    methods: {
      initTime() {
        let date = new Date()
        date.setDate(date.getDate() - 1)
        date.setMonth(date.getMonth())
        this.value = this.$utils.dateFormat(date, 'yyyy-MM-dd')
        this.$emit('setTime', this.value)
      }
    }
  }
</script>
<style lang="scss">
  .date-picker-wrap.el-date-editor--daterange {
    padding: 0;
    width: 262px;
    .el-icon-date {
      position: absolute;
      right: 10px;
    }
    .el-range-input {
      width: 112px;
      color: $semi-black;
    }
  }
</style>
