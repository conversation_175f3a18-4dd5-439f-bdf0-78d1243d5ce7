<template lang="html">
  <div class="aiui">
    <div class="banner-wrap">
			<div class="content-wrap">
				<a class="buy-btn" data-href="/user/login" v-if="!userinfo">立即购买</a>
				<a class="buy-btn" :href="buyUrl" v-else>立即购买</a>
				<a class="docs-btn" href="https://aiui-file.cn-bj.ufileos.com/AIUI%20%E8%AF%84%E4%BC%B0%E6%9D%BF%E7%A1%AC%E4%BB%B6%E8%A7%84%E6%A0%BC.zip">硬件规格</a>
				<!-- <ul class="tab">
					<li><a href="/solutions/morfei">讯飞魔飞智能麦克风 MORFEI 2.0</a></li>
					<li><a href="javascript:void(0);" class="active">AIUI 评估板</a></li>
				</ul> -->
			</div>
		</div>
		<div class="product-form">
			<div class="content-wrap">
				<div class="content-item">
					<div class="content-left">
						<p class="product-title">产品形态</p>
						<p class="product-desc">
							AIUI面向机器人、智能家居、智能音箱<br/>
							等领域的客户，提供软硬一体化的解决<br/>
							方案，具体包括评估板、模块、软核三<br/>
							种产品形态</p>
					</div>
					<div :class="['content-right', 'content-right-' + activeIndex]">
						<div class="form-wrap">
							<div
								v-for="(aiui, index) in aiuiMessage"
								:class="['form', {'active': index == activeIndex}]"
								@mouseover="changeIndex(index)">
								<p class="product-title">{{aiui.title}}</p>
								<div class="form-desc-warp">
									<p class="form-desc">{{aiui.desc1}}</p>
									<p class="form-desc">{{aiui.desc2}}</p>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="features-wrap">
			<div class="content-wrap">
				<div class="feature-wrap">
					<div class="mask"></div>
					<div class="feature feature1">
						<i class="feature-icon"></i>

						<div class="feature-content">
							<p class="feature-title">远场精准识别</p>

							<div class="feature-desc">
								<p>麦克风阵列硬件</p>
								<p>远场识别引擎</p>
								<p>语义上下文自修正</p>
							</div>
						</div>
					</div>
				</div>
				<div class="feature-wrap">
					<div class="mask"></div>
					<div class="feature feature2">
						<i class="feature-icon"></i>

						<div class="feature-content">
							<p class="feature-title">全双工交互</p>

							<div class="feature-desc">
								<p>持续录音，连续识别</p>
								<p>拒识 - 过滤无效语音</p>
								<p>基于语义的智能断句</p>
								<p>随时可打断</p>
								<p>支持主动式交互</p>
							</div>
						</div>
					</div>
				</div>
				<div class="feature-wrap">
					<div class="mask"></div>
					<div class="feature feature3">
						<i class="feature-icon"></i>

						<div class="feature-content">
							<p class="feature-title">上下文对话</p>

							<div class="feature-desc">
								<p>上下文理解</p>
								<p>基于内容提问</p>
								<p>多对话场景管理</p>
								<p>跨场景信息共享</p>
								<p>长时记忆</p>
							</div>
						</div>
					</div>
				</div>

				<div class="feature-wrap">
					<div class="mask"></div>
					<div class="feature feature4">
						<i class="feature-icon"></i>

						<div class="feature-content">
							<p class="feature-title">个性化可拓展</p>

							<div class="feature-desc">
								<p>产品特性定制</p>
								<p>用户个性化支持</p>
								<p>交互模式可扩展</p>
							</div>
						</div>
					</div>
				</div>
				<div class="feature-wrap">
					<div class="mask"></div>
					<div class="feature feature5">
						<i class="feature-icon"></i>

						<div class="feature-content">
							<p class="feature-title">快速集成</p>

							<div class="feature-desc">
								<p>软硬一体化</p>
								<p>云端配置一体化</p>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="application-scenarios">
			<div class="content-wrap">
				<div class="scenarios-wrap scenarios-robot">
					<div class="scenarios">
						<i class="scenarios-icon"></i>
						<p class="scenarios-title">机器人解决方案</p>
					</div>
					<div class="scenarios-hover">
						<p class="scenarios-title">机器人解决方案</p>
						<div class="scenarios-desc">
							可适用于餐饮、酒店等行业机器人，也可以用于家庭机器人，提供丰富的场景、闲聊问答和私有语义。
						</div>
					</div>
				</div>
				<div class="scenarios-wrap scenarios-home">
					<div class="scenarios">
						<i class="scenarios-icon"></i>
						<p class="scenarios-title">智能家居解决方案</p>
					</div>
					<div class="scenarios-hover">
						<p class="scenarios-title">智能家居解决方案</p>
						<div class="scenarios-desc">
							包含39种智能家居设备的语义场景。强大的语义理解能力将满足用户个性化的交互需求。
						</div>
					</div>
				</div>
				<div class="scenarios-wrap scenarios-voice-box">
					<div class="scenarios">
						<i class="scenarios-icon"></i>
						<p class="scenarios-title">智能音箱解决方案</p>
					</div>
					<div class="scenarios-hover">
						<p class="scenarios-title">智能音箱解决方案</p>
						<div class="scenarios-desc">
							具备完善的音乐及相关扩展场景。精准唤醒、远场识别、随时打断、个性化交互等能力构成了成熟的智能音箱方案。
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="video-demo">
			<div class="content-wrap">
				<div class="video-wrap">
					<div
						v-for="item in messageList"
						:class="item.class"
						@click="playVideo(item.data)">
						<i class="video-img"></i>
						<p class="video-title">{{item.title}}</p>
					</div>
				</div>
			</div>
		</div>
  	<div class="qa">
			<ul class="qa_content">
				<li class="li_q" >购买事宜</li>
				<li class="li_a li_sort">
					<ol>
						<li>点击购买按钮后，即可直接购买评估板，评估板最多支持100套的购买。当需要购买超过100套的评估板，或者购买模块时，请联系商务。</li>
						<li>商务合作电话：19955108393、19955108395。</li>
						<li>付款成功后会在五个工作日内发货（节假日顺延）。</li>
					</ol>
				</li>
				<li class="li_q">评估板与模块的区别</li>
				<li class="li_a li_sort" style="border-bottom: none;">
					<ol>
						<li>组成区别<br>(1)&nbsp;模块为包含AIUI功能的核心硬件。<br>(2)&nbsp;评估板除了包含模块以外，还包含了EVB板以及其他用于评估功能的配件。</li>
						<li>功能区别<br>(1)&nbsp;模块是为了安装在<b>量产产品</b>上，实现AIUI功能的模块，只有在确定量产之后，才需要购买模块。<br>(2)&nbsp;评估板是为了实现效果的<b>快速体验</b>。在经过评估后，确定采用此方案能否满足实际产品需求。<b style="color:#22a4e7; ">评估板属于电子产品，除非质量问题，不能退换货。</b></li>
						<li>服务区别<br>(1)&nbsp;针对评估板客户，我们提供周全的服务，包括完善的售前售后支持、完备的包装配件等。<br>(2)&nbsp;针对模块客户，我们支持300块量产起购，提供完备的开发支持和效果评估。</li>
						<li>价格区别<br>评估板4999元/片，包含模块、配件以及相应的支持服务费用。模块价格低于评估板，价格和购买事宜请联系商务。</li>
					</ol>
				</li>
			</ul>
		</div>
		<video-player
      v-if="showPlayer"
			:width="800"
			:height="600"
      :videoSrc="videoSrc"
      :videoStyle="videoStyle"
      @close="showPlayer=false"/>
  </div>
</template>

<script>
import VideoPlayer from "~/components/videoPlayer"
import utils from '~/assets/lib/utils.js'

export default {
  layout: 'aiuiHome',
  head () {
    return {
      title: 'AIUI 硬件解决方案',
      meta: [
        { name: 'keywords', content: 'AIUI，科大讯飞，AIUI 开放平台，AIUI 开发者，AIUI解决方案，语音唤醒，语义识别，语音合成，全双工交互' },
        { name: 'description', content: 'AIUI 评估板（量产版）是 AIUI 软硬一体解决方案，讯飞魔飞智能麦克风是成品级解决方案。相比 AIUI SDK 具有远场拾音、回声消除，全双工交互的特点。' }
      ]
    }
  },
  data () {
    return {
			userinfo: null,
			buyUrl: '',
			messageList: [
					{
						data: 'https://aiui-file.cn-bj.ufileos.com/song.mp4',
						class: 'video',
						title: 'AIUI-歌曲篇'
					},
					{
						data: 'https://aiui-file.cn-bj.ufileos.com/poem.mp4',
						class: 'video video2',
						title: 'AIUI-古诗词篇'
					},
					{
						data: 'https://aiui-file.cn-bj.ufileos.com/AIUI-wide-screen.mp4',
						class: 'video video3',
						title: 'AIUI评估板开箱视频'
					},
					{
						data: 'https://aiui-file.cn-bj.ufileos.com/play.mp4',
						class: 'video video4',
						title: '让我们玩起来'
					},
					{
						data: 'https://aiui-file.cn-bj.ufileos.com/home.mp4',
						class: 'video video5',
						title: 'AIUI-家居中控篇'
					}
				],
				aiuiMessage: [
					{
						title: '评估板',
						desc1: '供开发者快速体验评估',
						desc2: '支持四、六麦标准方案'
					},
					{
						title: '模块',
						desc1: '供开发者量产开发使用',
						desc2: '模块上不带两侧的引脚排针'
					},
					{
						title: '软核',
						desc1: '自行实现硬件设计',
						desc2: '需进行声学结构评估'
					}
				],
				videoSrc: '',
				showPlayer: false,
				activeIndex: 0,
				videoStyle: {
					width: '800px',
					height: '600px',
					'box-sizing': 'border-box',
					'margin-left': '-400px',
					'margin-top': '-300px'
				},
    }
	},
	components: {
    VideoPlayer
  },
	mounted() {
    let self = this
    this.selectTab = this.$route.name
    utils.httpRequest('/api/graphql', 'post', {
      data: {
        query: `
          {
            userDetailInfo {
              certificatedName
              email
              isCertificated
              mobile
              type
            }
          }
        `
      },
      success: (res) => {
        self.userinfo = res.data.userDetailInfo
      }
    })
		this.buyUrl = `https://www.xfyun.cn/aiui/index/buyevb?ssoSessionId=${utils.getCookie('ssoSessionId')}&account_id=${utils.getCookie('account_id')}`
  },
  methods: {
    coreClick(index) {
      this.coreIndex = index
      this.coreSwiper.slideTo(index, 1000, false)
		},
		canBuy() {
			if(this.$store.state.auth.loggedIn) {
				window.location.href = BASE_XFYUN + 'aiui/index/buyevb?ssoSessionId='+ getCookie('ssoSessionId') + '&account_id=' + getCookie('account_id')
			} else {
				this.$router.push({
					name: 'login.index',
					query: {
						jump: Base64.encode(BASE_XFYUN + 'aiui/index/buyevb')
					}
				})
			}
		},
		playVideo(params) {
			this.videoSrc = params
			this.showPlayer = true
		},
		closePlayer(params) {
			this.showPlayer = params
		},
		changeIndex(params) {
			this.activeIndex = params
		}
  }
}
</script>
<style lang="scss" scoped>
	.content-wrap {
		width: 1200px;
		margin: 0 auto;
		color: #999;
		font-size: 14px;
		position: relative;
	}

	.banner-wrap {
		height: 470px;
		padding-top: 60px;
		min-width: 1200px;
		box-sizing: border-box;
		background: url("~assets/images/solutions/aiui/banner.png") #1a294d no-repeat center bottom;

		.content-wrap {
			height: 410px;
		}
	}

	.buy-btn {
		width: 150px;
		height: 40px;
		color: #fff;
		line-height: 40px;
		text-align: center;
		display: inline-block;
		border: 1px solid #fff;
		border-radius: 5px;
		position: absolute;
		top: 220px;
		left: 15px;
	}
	.docs-btn {
		width: 150px;
		height: 40px;
		color: #fff;
		line-height: 40px;
		text-align: center;
		display: inline-block;
		border: 1px solid #fff;
		border-radius: 5px;
		position: absolute;
		top: 220px;
		left: 195px;
	}
	.docs-btn {
		width: 150px;
		height: 40px;
		color: #fff;
		line-height: 40px;
		text-align: center;
		display: inline-block;
		border: 1px solid #fff;
		border-radius: 5px;
		position: absolute;
		top: 220px;
		left: 195px;
	}

	.tab {
		width: 1200px;
		position: absolute;
		bottom: 0;

		a {
			width: 600px;
			height: 48px;
			float: left;
			text-align: center;
			line-height: 44px;
			color: #fff;
			font-size: 16px;
			background: #304681;
			border-color: #304681;
			-webkit-box-sizing: border-box;
			-moz-box-sizing: border-box;
			box-sizing: border-box;
		}

		.active {
			color: #4752b3;
			background: #fff;
			border-top: 4px solid #79b2ff;
		}
	}

	.product-form {
		min-width: 1200px;

		.content-wrap {
			height: 440px;
			box-sizing: border-box;
			padding: 70px 45px 45px 45px;
		}

		.product-title {
			margin-top: 10px;
			color: #3482ec;
			font-size: 20px;
		}

		.product-desc {
			font-size: 16px;
			line-height: 30px;
			margin-top: 30px;
		}

		.content-left {
			width: 315px;
			float: left;
		}

		.content-right {
			width: 795px;
			float: left;
			text-align: right;
		}

		.content-right-0 {
			background: url("~assets/images/solutions/aiui/form-board.png") no-repeat;
		}
		.content-right-1 {
			background: url("~assets/images/solutions/aiui/form-module.png") no-repeat;
		}
		.content-right-2 {
			background: url("~assets/images/solutions/aiui/form-soft-core.png") no-repeat;
		}

		.form-wrap {
			width: 250px;
			float: right;

			.form {
				margin-bottom: 35px;
			}

			.product-title {
				color: #333;
			}

			.form-desc-warp {
				margin-top: 10px;
			}

			.form-desc {
				font-size: 16px;
				line-height: 25px;
			}

			.active {
				color: #5799f3;

				.product-title {
					color: #3482ec;
				}
			}
		}
	}

	.features-wrap {
		min-width: 1200px;
		height: 360px;
		background: #363946;
		overflow: hidden;


		.feature-wrap {
			width: 240px;
			height: 360px;
			float: left;
			position: relative;
			transition: all .4s cubic-bezier(.4,0,.2,1);

			&:hover {
				.mask {
					background: #3482ec;
					opacity: 0.8;
					filter: alpha(opacity=80);
				}

				.feature-icon {
					height: 0;
					opacity: 0;
					margin-bottom: 0;
					filter: alpha(opacity=0);
				}

				.feature-desc {
					opacity: 1;
					filter: alpha(opacity=100);
				}
			}
		}

		.mask {
			width: 240px;
			height: 360px;
			position: absolute;
			top: 0;
			left: 0;
			opacity: 0;
			filter: alpha(opacity=0);
			transition: all .4s cubic-bezier(.4,0,.2,1);
		}

		.feature {
			width: 240px;
			height: 360px;
			color: #fff;
			font-size: 14px;
			text-align: center;
			padding-top: 90px;
			background: url("~assets/images/solutions/aiui/feature1.png") no-repeat;
			position: absolute;
			top: 0;
			left: 0;

			.feature-icon {
				height: 50px;
				display: block;
				margin-bottom: 25px;
				transition: all .4s cubic-bezier(.4,0,.2,1);
				background: url("~assets/images/solutions/aiui/feature-icon1.png") no-repeat center center;
			}

			.feature-title {
				color: #fff;
				font-size: 18px;
				padding: 5px 0;
			}

			.feature-desc {
				margin-top: 25px;
				line-height: 30px;
				opacity: 0;
				filter: alpha(opacity=0);
				p {
					color: #fff;
				}
			}
		}

		.feature2 {
			background: url("~assets/images/solutions/aiui/feature2.png") no-repeat;

			.feature-icon {
				background: url("~assets/images/solutions/aiui/feature-icon2.png") no-repeat center center;
			}
		}

		.feature3 {
			background: url("~assets/images/solutions/aiui/feature3.png") no-repeat;

			.feature-icon {
				background: url("~assets/images/solutions/aiui/feature-icon3.png") no-repeat center center;
			}
		}

		.feature4 {
			background: url("~assets/images/solutions/aiui/feature4.png") no-repeat;

			.feature-icon {
				background: url("~assets/images/solutions/aiui/feature-icon4.png") no-repeat center center;
			}
		}

		.feature5 {
			background: url("~assets/images/solutions/aiui/feature5.png") no-repeat;

			.feature-icon {
				background: url("~assets/images/solutions/aiui/feature-icon5.png") no-repeat center center;
			}
		}
	}

	.application-scenarios {
		min-width: 1200px;
		height: 360px;
		padding: 50px 0;
		box-sizing: border-box;
		background: #f6f7fa;


		.scenarios-wrap {
			width: 335px;
			height: 260px;
			float: left;
			position: relative;
			padding: 50px 0 20px 0;
			text-align: center;
			border: 1px solid #dddee1;
			box-sizing: border-box;
			box-shadow: 0 0 15px rgba(221,222,225,.7);

			&:hover {
				border: 1px solid #3482ec;
				box-shadow: 0 0 15px rgba(52,130,236,.5);


				.scenarios {
					display: none;
				}

				.scenarios-hover {
					display: block;
				}
			}
		}

		.scenarios-title {
			color: #3482ec;
			margin-top: 20px;
			font-size: 18px;
		}

		.scenarios-icon {
			height: 140px;
			display: block;
			background: url("~assets/images/solutions/aiui/scenarios-robot.png") no-repeat center top;
		}

		.scenarios-home {
			margin: 0 95px;

			.scenarios-icon {
				background: url("~assets/images/solutions/aiui/scenarios-home.png") no-repeat center top;
			}
		}

		.scenarios-voice-box {
			.scenarios-icon {
				background: url("~assets/images/solutions/aiui/scenarios-voice-box.png") no-repeat center top;
			}
		}

		.scenarios-hover {
			display: none;
		}

		.scenarios-desc {
			color: #666;
			line-height: 30px;
			width: 210px;
			text-align: justify;
			margin: 30px auto 0 auto;
		}
	}

	.video-demo {
		min-width: 1200px;
		height: 365px;
		padding: 95px 0;
		box-sizing: border-box;

		.video-wrap {
			text-align: center;
			margin-left: -50px;
		}

		.video {
			width: 199px;
			margin-left: 50px;
			cursor: pointer;
			float: left;
		}

		.video-img {
			height: 116px;
			display: block;
			background: url("~assets/images/solutions/aiui/video1.png") no-repeat center;
		}

		.video-title {
			color: #666;
			font-size: 16px;
			margin-top: 40px;
		}

		.video2 .video-img {
			background: url("~assets/images/solutions/aiui/video2.png") no-repeat center;
		}

		.video3 .video-img {
			background: url("~assets/images/solutions/aiui/video3.png") no-repeat center;
		}

		.video4 .video-img {
			background: url("~assets/images/solutions/aiui/video4.png") no-repeat center;
		}

		.video5 .video-img {
			background: url("~assets/images/solutions/aiui/video5.png") no-repeat center;
		}
	}

	.doc-wrap {
		min-width: 1200px;
		height: 135px;
		color: #999;
		font-size: 12px;
		background: #f6f7fa;
		padding: 45px 0;
		box-sizing: border-box;

		.doc {
			width: 300px;
			float: left;
			text-align: center;
			border-right: 1px solid #c7c8c9;
			box-sizing: border-box;

			&:last-child {
				border: none;
			}
		}

		.doc-title {
			color: #333;
			font-size: 14px;
			display: block;
			margin-bottom: 10px;

			&:hover {
				color: #3482ec;
			}
		}
	}

	.qa {
		min-width: 1200px;
		background: #f6f7fa;
		padding: 1px 0;
		box-sizing: border-box;

		.qa_content {
			width: 1200px;
			margin: 0 auto;
		}
		.qa_content .li_q {
			margin-top: 30px;
			padding-left: 60px;
			line-height: 45px;
			color: #333;
			font-size: 16px;
			text-align: left;
		}
		.qa_content .li_sort {
			padding-left: 78px;
			border-bottom: 1px dashed #dddddd;
		}

		.qa_content p{
			color: #999;
			padding: 0 63px;
		}

		.qa_content .li_a {
			padding: 10px 81px 30px;
			font-size: 14px;
			line-height: 24px;
			color: #666;
		}
		.qa_content ol li {
			list-style-type: decimal;
			list-style-position: outside;
			text-align: left;
		}

		b {
			font-weight: bold;
		}
	}
</style>
