<template>
  <div class="section-tech wow fadeInUp">
    <div class="title">
      <span>核心技术</span> <span class="sub-title">全链路人机交互能力</span>
    </div>
    <div class="card-wrap">
      <div class="card__left">
        <div
          :class="['card-tab', { 'card-tab__active': activeIndex === index }]"
          v-for="(item, index) in list"
          :key="index"
          @mouseenter="activeIndex = index"
        >
          <div class="icon-wrap">
            <img
              class="icon icon-default"
              v-lazy="require(`@A/images/home/<USER>/${item.icon}.png`)"
            />
          </div>
          {{ item.title }}
        </div>
      </div>
      <div class="card__middle">
        <div
          v-for="(item, index) in list"
          :class="['card-info', { 'card-info__active': activeIndex === index }]"
          :key="index"
        >
          <div class="card-info-desc">
            <span>{{ item.desc }}</span
            ><audio-play v-if="item.desc === '语音合成'"></audio-play>
          </div>
          <div class="list-wrap">
            <div
              class="mt-item"
              v-for="(it, i) in item.item"
              :key="`${index}_${i}`"
            >
              <img
                style="
                  margin-right: 6px;
                  margin-top: 4px;
                  width: 16px;
                  height: 16px;
                "
                v-lazy="require(`@A/images/home/<USER>/switch.png`)"
              />
              <div class="mt-item-title">{{ it }}</div>
            </div>
          </div>
        </div>
      </div>
      <div class="card__right">
        <img
          v-for="(item, index) in list"
          :class="['img', { 'img-show': activeIndex === index }]"
          v-lazy="require(`@A/images/home/<USER>/${item.picture}.png`)"
          :key="index"
        />
      </div>
    </div>
  </div>
</template>
<script>
import audioPlay from './audioPlay.vue'
export default {
  name: 'section-tech',
  data() {
    return {
      activeIndex: 0,
      list: [
        {
          title: '大模型',
          icon: 'icon1',
          desc: '大模型',
          item: [
            '内置讯飞星火大模型',
            '供100+官方开发的技能',
            '支持接入第三方内容信源',
            '提供可视化编辑界面，帮助开发者自定义语音交互技能',
          ],
          picture: 'card1',
        },
        {
          title: '麦克风阵列',
          icon: 'icon2',
          desc: '麦克风阵列',
          item: [
            // '提供多模态降噪算法，支撑高噪场景自由交互',
            '提供线性2/4/6麦和环形4/6麦阵列和对应的前端声学算法',
            '解决远距离收音时波束形成、回声消除、去除混响、噪音抑制、声源定位等问题',
            '提高远场识别的准确率',
          ],
          picture: 'card2',
        },
        {
          title: '多模态交互',
          icon: 'icon8',
          desc: '多模态交互',
          item: [
            '人脸唤醒、主动问候，交互更贴心',
            '唇动拾音、无需唤醒词，交互更简单',
            '多模态语音增强，解决嘈杂人声场景交互难题',
          ],
          picture: 'card8',
        },
        {
          title: '语音唤醒',
          icon: 'icon3',
          desc: '语音唤醒',
          item: [
            '高唤醒率',
            '低配置要求，超低功耗',
            '支持200个自定义唤醒词',
            '支持用户自定义',
            '智能评估唤醒词质量',
          ],
          picture: 'card3',
        },
        {
          title: '语音识别',
          icon: 'icon4',
          desc: '语音识别',
          item: [
            '内置讯飞星火大模型',
            '支持离线识别',
            '支持流式识别，实时出字',
            '识别结果响应时间低于200ms',
          ],
          picture: 'card4',
        },
        {
          title: '语音合成',
          icon: 'icon5',
          desc: '语音合成',
          item: ['超拟人口语化合成', '提供众多特色发音人', '支持离线语音合成'],
          picture: 'card5',
        },
        {
          title: '虚拟人驱动',
          icon: 'icon6',
          desc: '虚拟人驱动',
          item: [
            '支持2D、3D虚拟人',
            '支持驱动第三方虚拟人 ',
            '口唇表情及动作表达逼真',
          ],
          picture: 'card6',
        },
        {
          title: '语音翻译',
          icon: 'icon7',
          desc: '语音翻译',
          item: [
            '支持在线、 离线语音翻译',
            '支持中文与40多个外文的互译',
            '中英翻译通过了国家六级水平考试',
          ],
          picture: 'card7',
        },
      ],
    }
  },
  components: {
    audioPlay,
  },
}
</script>
<style lang="scss" scoped>
.section-tech {
  width: 100%;
  height: 744px;
  background: url('../../../assets/images/home/<USER>/banner.png') center/cover
    no-repeat;

  .title {
    width: 1200px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    padding: 64px 0 56px;

    span {
      font-size: 30px;
      font-family: PingFangSC, PingFangSC-Medium;
      font-weight: 500;
      text-align: justify;
      color: #000000;
    }

    .sub-title {
      font-size: 16px;
      font-family: PingFangSC, PingFangSC-Regular;
      font-weight: 400;
      color: #676a99;
      margin-left: 24px;
    }
  }

  .card-wrap {
    width: 1200px;
    height: 516px;
    box-sizing: border-box;
    margin: 0 auto;
    background: #f1f7ff;
    box-shadow: 0px 4px 24px 0px rgba(62, 117, 251, 0.3);
    display: flex;
    justify-content: space-between;

    .card__left {
      width: 238px;
      background: #f1f7ff;
      box-shadow: 0px 4px 24px 0px rgba(62, 117, 251, 0.3);

      .card-tab {
        width: 238px;
        height: 64px;
        box-sizing: border-box;
        padding-left: 26px;
        cursor: pointer;
        display: flex;
        align-items: center;
        font-size: 16px;
        color: #000000;
        position: relative;
        font-family: PingFangSC, PingFangSC-Regular;
        font-weight: 400;

        &:last-child {
          margin-bottom: 0;
        }

        .icon-wrap {
          width: 40px;
          height: 40px;
          background: #fff;
          border-radius: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          margin-right: 26px;

          .icon {
            max-width: 100%;
            max-height: 100%;
          }

          .icon-hover {
            display: none;
          }
        }

        &__active {
          color: $primary;
          background: linear-gradient(180deg, #eff6ff, #cbe1ff);
          border: 1px solid #ffffff;
          box-shadow: 0px 4px 4px 0px #d7e8ff inset;
          font-family: PingFangSC, PingFangSC-Medium;
          font-weight: 500;

          &::after {
            content: '';
            position: absolute;
            right: 0;
            top: 0;
            width: 8px;
            height: 64px;
            background: $primary;
          }
        }
      }
    }

    .card__middle {
      flex: 1;
      padding-left: 72px;
      margin-top: 88px;

      @keyframes cardAni {
        from {
          transform: translateX(30px);
          opacity: 0;
        }

        to {
          transform: translateX(0);
          opacity: 1;
        }
      }

      .card-info {
        display: none;

        &__active {
          display: block;
        }

        .card-info-desc {
          width: 346px;
          font-size: 24px;
          font-family: PingFangSC, PingFangSC-Medium;
          font-weight: 500;
          text-align: justify;
          color: #000000;
          line-height: 33px;
          margin-bottom: 32px;
          animation: cardAni 0.5s ease forwards;
          display: flex;
          align-items: center;
        }

        .list-wrap {
          width: 346px;
          display: flex;
          flex-direction: column;
          animation: cardAni 0.5s ease 0.1s forwards;

          .mt-item {
            display: flex;
            margin-bottom: 24px;

            &:last-child {
              margin-bottom: 0;
            }

            .mt-item-title {
              font-size: 16px;
              font-family: PingFangSC, PingFangSC-Regular;
              font-weight: 400;
              text-align: justify;
              color: #262b4f;
            }
          }
        }
      }
    }

    .card__right {
      width: 454px;
      height: 312px;
      border-radius: 8px;
      overflow: hidden;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 80px;
      margin-right: 40px;

      .img {
        display: none;
        max-width: 100%;
        max-height: 100%;
        animation: imgAni 0.5s ease forwards;

        &-show {
          display: block;
        }
      }
    }
  }
}
</style>
