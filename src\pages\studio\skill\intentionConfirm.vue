<template>
  <os-collapse
    :default="intentConfirmCollapse"
    size="large"
    v-if="skill && skill.type != 8"
  >
    <template slot="title">
      <div v-on:click.stop class="os-collapse-switch">
        <el-switch
          v-model="intention.isConfirm"
          class="mgr16"
          :active-value="1"
          :inactive-value="0"
          :disabled="!subAccountEditable"
          @change="saveConfirm"
        />
      </div>
      意图确认
      <el-tooltip
        effect="dark"
        content="槽值填满后，系统对该意图进行一次性确认"
        placement="right"
      >
        <i class="el-icon-question"></i>
      </el-tooltip>
    </template>
    <label-selector
      @select="onLabelSelect"
      title="意图确认"
      v-if="intention.isConfirm"
    ></label-selector>

    <intention-confirm-text-adder
      v-if="intention.isConfirm"
      :slotNames="slotNames"
      :data="intention.confirmSentence || []"
      @add="addIntentionConfirm"
      @del="delIntentionConfirm"
      @edit="editIntentionConfirm"
      @change="onInputChange"
      :disabled="!subAccountEditable"
      :reg="/^[\u4e00-\u9fffa-zA-Z0-9 {}_?？°]{1,50}$/"
      warning="仅支持汉字/字母/数字/空格/{}/_/?/°，且每条不超过50字"
      editPlaceholder="使用“{”符号引用必选槽位，每条不超过50字，回车或者点击框外空白处保存"
      placeholder="使用“{”符号引用必选槽位，最多添加5条，每条不超过50字，回车新增。例：你是不是要问{city}的天气"
      ref="intentionConfirmRef"
    >
    </intention-confirm-text-adder>
  </os-collapse>
</template>

<script>
import intentionConfirmTextAdder from './intentionConfirmTextAdder.vue'
import labelSelector from './labelSelector.vue'

import { mapGetters } from 'vuex'

export default {
  name: 'intention-confirm',
  props: {
    intentionObj: {
      type: Object,
      default: {},
    },
    intentId: {
      type: String | Number,
      default: '',
    },
    subAccountEditable: Boolean,
    slotNames: Array,
  },
  data() {
    return {
      intention: { confirmSentence: [] },
      loading: false,
    }
  },
  watch: {
    intentionObj(val) {
      if (val) {
        let newVal = { ...val, confirmSentence: [] }
        if (!val.confirmSentence) {
          this.intention = newVal
        } else {
          this.intention = {
            ...val,
            confirmSentence: (val.confirmSentence || []).map((item) => {
              return {
                ...item,
                changed: false,
              }
            }),
          }
        }
      }
    },
  },
  computed: {
    ...mapGetters({
      businessId: 'studioSkill/id',
      skill: 'studioSkill/skill',
    }),
    intentConfirmCollapse() {
      return this.intention.isConfirm === 1
    },
  },
  methods: {
    onInputChange(index) {
      this.$set(
        this.intention,
        'confirmSentence',
        (this.intention.confirmSentence || []).map((item, i) => {
          if (index === i) {
            return {
              ...item,
              changed: true,
            }
          } else {
            return {
              ...item,
            }
          }
        })
      )
    },
    onLabelSelect(label) {
      this.$refs.intentionConfirmRef.$refs.intelInput.insertLabel(label)
    },
    // 保存意图确认文本
    addIntentionConfirm(text) {
      if (this.loading) {
        return
      }
      let confirmSentence = (this.intention.confirmSentence || []).slice()
      confirmSentence.push(text)
      this.saveIntentionConfirm(confirmSentence, text, 'add')
    },
    delIntentionConfirm(text) {
      let confirmSentence = Array.prototype.filter.call(
        this.intention.confirmSentence,
        function (item, index) {
          return item != text
        }
      )
      this.saveIntentionConfirm(confirmSentence, text, 'sub')
    },
    editIntentionConfirm(text, index) {
      const otherItems = (this.intention.confirmSentence || []).filter(
        (_, idx) => idx !== index
      )
      const find = otherItems.findIndex((itm) => itm.answer === text.answer)
      if (find !== -1) {
        return this.$message.error('不得与其他条目重复')
      }
      let confirmSentence = (this.intention.confirmSentence || []).map(
        (item, i) => {
          if (index === i) {
            return {
              ...item,
              ...text,
            }
          } else {
            return { ...item }
          }
        }
      )
      this.saveIntentionConfirm(confirmSentence, '', 'modify', index)
    },
    saveIntentionConfirm(list, text, mode, index) {
      let self = this
      this.loading = true
      this.$utils.httpPost(
        this.$config.api.STUDIO_INTENT_SAVE_CONFIRM_SENTENCE,
        {
          businessId: this.businessId,
          intentId: this.intentId,
          confirmSentence: JSON.stringify(
            list.map((item) => {
              return {
                answer: item.answer,
                labels: (item.labels || []).map(({ picture, ...rest }) => {
                  return { ...rest }
                }),
              }
            })
          ),
        },
        {
          success: (res) => {
            this.loading = false
            let textInfo = mode === 'sub' ? '删除成功' : '保存成功'
            self.$message.success(textInfo)
            // self.$set(self.intention, 'confirmSentence', list)
            if (mode === 'add') {
              self.$refs.intentionConfirmRef.resetInitialStatus()
              self.intention.confirmSentence.push(text)
            } else if (mode === 'sub') {
              const delIndex = self.intention.confirmSentence.findIndex(
                (im) => im === text
              )
              self.intention.confirmSentence.splice(delIndex, 1)
            } else if (mode === 'modify') {
              self.intention.confirmSentence = (list || []).map((it, i) => {
                if (index === i) {
                  return {
                    ...it,
                    changed: false,
                  }
                } else {
                  return { ...it }
                }
              })
            }
          },
          error: (err) => {
            // self.editSaveLoading = false
            this.$message.error('请求失败，请稍后重试')
            this.loading = false
            this.$emit('setEditSaveLoading', false)
          },
        }
      )
    },
    // 保存意图确认switch
    saveConfirm() {
      let self = this
      this.$utils.httpPost(
        this.$config.api.STUDIO_INTENT_SAVE_CONFIRM,
        {
          businessId: this.businessId,
          intentId: this.intentId,
          isConfirm: this.intention.isConfirm,
        },
        {
          success: (res) => {
            self.$message.success('保存成功')
          },
          error: (err) => {
            // self.editSaveLoading = false
            this.$emit('setEditSaveLoading', false)
          },
        }
      )
    },
  },
  components: { intentionConfirmTextAdder, labelSelector },
}
</script>

<style lang="scss">
#app {
  height: 100%;
}
</style>
