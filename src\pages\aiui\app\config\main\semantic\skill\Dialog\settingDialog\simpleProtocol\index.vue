<template>
  <div>
    <p class="item-title">
      使用精简协议
      <el-switch
        :value="simpleprotocolOpen"
        @change="onSimpleprotocolChange"
        :disabled="!subAccountEditable"
      ></el-switch>
    </p>
    <p class="config-desc config-desc-bt-clo" style="color: #ff9b00">
      将多个技能精简成一种输出协议，大大降低技能适配工作。适用于无屏设备。
    </p>
    <p class="item-desc">
      精简后的技能类型和协议说明
      <a :href="`${$config.docs}doc-18/`" target="_blank">查看文档</a>
    </p>
  </div>
</template>
<script>
import { mapGetters } from 'vuex'

export default {
  props: {
    show: Boolean,
  },
  data() {
    return {
      simpleprotocolOpen: false,
    }
  },
  computed: {
    ...mapGetters({
      currentScene: 'aiuiApp/currentScene',
      appInfo: 'aiuiApp/app',
      subAccountEditable: 'aiuiApp/subAccountEditable',
    }),
    appId() {
      return this.$route.params.appId
    },
  },

  watch: {
    show(val) {
      if (val) {
        this.getSimpleprotocol() // 获取配置列表
      }
    },
  },
  methods: {
    getSimpleprotocol() {
      this.$utils.httpPost(
        this.$config.api.AIUI_DIST_SIMPLEPROTOCOL,
        {
          appid: this.appId,
          sceneName: this.currentScene.sceneBoxName,
        },
        {
          success: (res) => {
            if (res.flag) {
              this.simpleprotocolOpen = res.data.simpleProtocol == 1
            } else {
              this.$message.error(res.desc)
            }
          },
        }
      )
    },
    onSimpleprotocolChange(val) {
      this.$confirm(
        `<p style="color: #FF5A5A;">精简协议与普通协议不相兼容，如该情景模式已有用户在使用，请确保端侧可同步升级。否则建议启用新的情景模式来使用${
          val ? '精简' : '普通'
        }协议。</p>`,
        '提示',
        {
          confirmButtonText: '取消',
          cancelButtonText: '确定',
          type: 'warning',
          dangerouslyUseHTMLString: true,
        }
      )
        .then(() => {})
        .catch(() => {
          this.simpleprotocolOpen = val
          this.saveSimpleprotocol()
        })
    },
    saveSimpleprotocol() {
      this.$utils.httpPost(
        this.$config.api.AIUI_DIST_SAVE_SIMPLEPROTOCOL,
        {
          appid: this.appId,
          sceneName: this.currentScene.sceneBoxName,
          simpleProtocol: this.simpleprotocolOpen ? 1 : 0,
        },
        {
          success: (res) => {
            // self.simpleprotocolChanged = false
          },
          error: (err) => {
            // this.$emit('saveFail')
          },
        }
      )
    },
  },
}
</script>
<style lang="scss" scoped>
@import '../../common.scss';
</style>
