<template>
  <div class="header-right">
    <div v-if="character && character.operator">
      <p class="header-save-time">
        最近由<span class="text-blod" style="color: #262626">{{
          character.operator
        }}</span>
      </p>
      <p
        v-if="character.updateTime"
        class="header-save-time"
        style="text-align: right"
      >
        保存于{{ character.updateTime | time }}
      </p>
    </div>
    <template v-else>
      <span class="header-save-time" v-if="character.updateTime"
        >最近保存 {{ character.updateTime | time }}</span
      >
    </template>
    <span class="header-qa">
      <el-tooltip
        class="item"
        effect="dark"
        content="设备人设修改后需要重新构建"
        placement="bottom"
      >
        <i class="el-icon-question" />
      </el-tooltip>
    </span>
    <el-button
      size="small"
      type="primary"
      @mousedown.native.prevent="beforeSturcture"
      :loading="structureLoading"
    >
      {{ structureLoading ? '构建中...' : '构建设备人设' }}
    </el-button>
    <!-- <el-dialog title="构建设备人设" :visible.sync="dialogShow" width="480px">
      <el-form
        :model="form"
        ref="form"
        label-width="50px"
        class="demo-ruleForm"
        @submit.native.prevent
      >
        <el-form-item
          label="备注"
          prop="content"
          :rules="[
            { required: true, message: '备注不能为空' },
            {
              min: 1,
              max: 200,
              message: '长度在 1 到 200 个字符',
              trigger: 'blur',
            },
          ]"
        >
          <el-input
            type="textarea"
            v-model="form.content"
            autocomplete="off"
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            size="medium"
            style="margin-bottom: 32px; min-width: 104px"
            @click="submitForm"
            >提交</el-button
          >
          <el-button size="medium" style="min-width: 104px" @click="cancel"
            >取消</el-button
          >
        </el-form-item>
      </el-form>
    </el-dialog> -->
    <el-dialog title="提示" :visible.sync="structResDialogShow" width="480px">
      <p v-for="(item, index) of structRes" :key="index">{{ item }}</p>
      <span slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          style="margin-top: 8px; min-width: 104px"
          @click="structResDialogShow = false"
          >知道了</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'studioCharacterHeaderRight',
  data() {
    return {
      mohu: true,
      structureLoading: false,
      checkCount: 0,
      character: {},
      dialogShow: false,
      form: {
        content: '',
      },
      structResDialogShow: false,
      structRes: [],
    }
  },
  computed: {
    ...mapGetters({
      originalCharacter: 'studioCharacter/character',
      subAccount: 'user/subAccount',
    }),
  },
  watch: {
    originalCharacter: function (val, oldVal) {
      this.character = this.$deepClone(val)
    },
  },
  created() {
    if (this.$store.state.studioCharacter.character.id) {
      this.character = this.$deepClone(
        this.$store.state.studioCharacter.character
      )
    }
  },
  methods: {
    beforeSturcture() {
      this.structure()
    },
    submitForm() {
      let self = this
      this.$refs.form.validate((valid) => {
        if (valid) {
          self.structure(self.form.content)
        }
      })
    },
    structure(content) {
      let self = this
      let data = {
        businessId: this.character.id,
      }
      if (content) {
        data.content = content
      }
      this.structureLoading = true
      this.$utils.httpPost(this.$config.api.STUDIO_SKILL_STRUCTURE, data, {
        success: (res) => {
          self.$message.success('提交成功，正在构建...')
          self.checkStatus()
        },
        error: (err) => {
          self.structureLoading = false
          if (err.data && err.data instanceof Array && err.data.length) {
            this.structRes = err.data
            this.structResDialogShow = true
          }
        },
      })
    },
    checkStatus() {
      let self = this
      this.checkCount += 1
      this.$utils.httpGet(
        this.$config.api.STUDIO_SKILL_PUBLISH_CHECK,
        {
          businessId: this.character.id,
        },
        {
          success: (res) => {
            if (res.data === '1' || res.data === '4') {
              if (self.structureLoading) {
                self.$message.error('构建失败')
              }
              self.structureLoading = false
              self.checkCount = 0
            } else if (res.data === '2') {
              if (self.structureLoading) {
                self.$message.success('构建成功')
                // 测试说不用修改，否则要动巡检脚本了
                // self.update()
              }
              self.structureLoading = false
              self.checkCount = 0
            } else {
              if (self.checkCount < 300) {
                setTimeout(function () {
                  self.checkStatus()
                }, 2000)
              } else {
                if (self.structureLoading) {
                  self.$message.error('构建失败')
                }
                self.structureLoading = false
                self.checkCount = 0
              }
            }
          },
          error: (err) => {
            console.log('page=>>')
            console.log(err)
          },
        }
      )
    },
    cancel() {
      this.$refs.form.resetFields()
      this.dialogShow = false
    },
    update() {
      this.$utils.httpGet(
        this.$config.api.STUDIO_SKILL_DETAIL,
        {
          skillId: this.$route.params.skillId,
        },
        {
          success: (res) => {
            this.character.updateTime = res.data.updateTime
          },
        }
      )
    },
  },
  components: {},
}
</script>

<style lang="scss">
.header-right {
  display: flex;
  align-items: center;
}
.header-save-time {
  font-size: 12px;
  color: $grey5;
  margin-right: 24px;
}
// .header-more {
//   font-size: 16px;
//   color: $grey5;
//   margin-right: 24px;
//   cursor: pointer;
// }
.header-qa {
  font-size: 16px;
  color: $grey4;
  margin-right: 8px;
  cursor: pointer;
}
</style>
