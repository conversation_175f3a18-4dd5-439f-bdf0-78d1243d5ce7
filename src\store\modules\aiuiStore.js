// @服务信息
import { utils } from '@U'
import { api } from '@/config'

export default {
  namespaced: true,
  state: {
    skillDetail: {},
    searchVal: '',
    collapse: false,
    hideAiuiMenu: false,
  },

  actions: {
    setSkillDetail({ state, commit, rootState }, skill) {
      commit('setSkillDetail', skill)
    },
    setSearchVal({ state, commit, rootState }, data) {
      commit('setSearchVal', data)
    },
    setCollapse({ state, commit, rootState }, data) {
      commit('setCollapse', data)
    },
    setHideAiuiMenu({ state, commit, rootState }, data) {
      commit('setHideAiuiMenu', data)
    },
  },

  mutations: {
    setSkillDetail(state, skill) {
      state.skillDetail = skill
    },
    setSearchVal(state, skill) {
      state.searchVal = skill
    },
    setCollapse(state, isCollapse) {
      state.collapse = isCollapse
    },
    setHideAiuiMenu(state, isHide) {
      state.hideAiuiMenu = isHide
    },
  },

  getters: {
    skillDetail(state, getters, rootState) {
      return state.skillDetail
    },
    searchVal(state, getters, rootState) {
      return state.searchVal
    },
    collapse(state, getters, rootState) {
      return state.collapse
    },
    hideAiuiMenu(state, getters, rootState) {
      return state.hideAiuiMenu
    },
  },
}
