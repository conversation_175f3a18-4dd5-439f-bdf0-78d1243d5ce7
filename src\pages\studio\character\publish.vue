<template>
  <os-page :options="pageOptions">
    <studio-character-header-right slot="btn" />
    <div class="skill-publish-page">
      <template>
        <os-collapse :default="true" size="large" title="基本信息">
          <el-form
            ref="skillForm"
            :rules="rules"
            :model="character"
            label-width="118px"
            label-position="left"
            :disabled="character.isCheck"
          >
            <el-form-item
              :label="'设备人设名称'"
              prop="name"
              :placeholder="'请输入设备人设名称'"
            >
              <el-input v-model="character.name"></el-input>
            </el-form-item>
          </el-form>
        </os-collapse>
        <os-divider />
        <os-collapse :default="true" size="large">
          <template slot="title"> 发布信息 </template>
          <el-form
            ref="releaseForm"
            :model="character"
            label-width="98px"
            :rules="rules"
            label-position="left"
            :disabled="character.isCheck"
          >
            <el-form-item
              label="版本号"
              class="is-required"
              prop="latestVersionArr"
              v-if="character.latestVersionArr"
            >
              <el-input
                class="release-form-version-input"
                v-model.number="character.latestVersionArr[0]"
                type="number"
              ></el-input>
              <span>.</span>
              <el-input
                class="release-form-version-input"
                v-model.number="character.latestVersionArr[1]"
                type="number"
              ></el-input>
              <span>.</span>
              <el-input
                class="release-form-version-input mgr16"
                v-model.number="character.latestVersionArr[2]"
                type="number"
              ></el-input>
              <span
                v-if="
                  character.onlineVersion && character.onlineVersion != '0.0.0'
                "
                >最近发布的版本{{ character.onlineVersion }}</span
              >
            </el-form-item>
            <el-form-item label="更新说明">
              <el-input
                type="textarea"
                resize="none"
                style="font-family: Arial"
                :autosize="{ minRows: 4, maxRows: 4 }"
                placeholder="请简要描述你的设备人设功能，若为版本更新请说明更新点。"
                v-model="character.updateLog"
              >
              </el-input>
            </el-form-item>
          </el-form>
        </os-collapse>

        <div class="mgb56" style="margin-top: 40px">
          <el-button
            type="primary"
            @click="onSubmit(1)"
            :loading="publishing"
            :disabled="!canPulish"
          >
            {{ publishing ? '发布中...' : '发布上线' }}
          </el-button>
          <el-button
            @click="onSubmit(2)"
            :loading="saving"
            :disabled="!changed"
          >
            {{ saving ? '保存中...' : '仅保存' }}
          </el-button>
          <os-give-up-save :edited="changed" @noSave="noSave" />
        </div>
      </template>
    </div>
    <page-leave-tips
      :dialog="leaveDialog"
      @save="onSubmit(2)"
      @noSave="noSave"
      @noJump="noJump"
    />
  </os-page>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'character-publish',
  data() {
    return {
      pageOptions: {
        title: '发布',
        loading: false,
      },
      rules: {
        name: this.$rules.skillZhName(),

        latestVersionArr: [{ validator: this.checkVersion, trigger: ['blur'] }],
      },
      character: {},
      publishing: false,
      saving: false,
      leaveDialog: {
        show: false,
      },
      routeTo: {},
      versionUnValid: false,

      initDataChanged: false, //记录computed检测不到的数据是否有改变(包括：别名、示例说法、iconUrl)
    }
  },
  beforeRouteLeave: function (to, from, next) {
    if (this.changed) {
      this.leaveDialog.show = true
      this.routeTo = to
      next(false)
    } else {
      next()
    }
  },
  computed: {
    ...mapGetters({
      originalCharacter: 'studioCharacter/character',
      userInfo: 'user/userInfo',
      subAccount: 'user/subAccount',
    }),
    edited() {
      let self = this
      return (
        self.character.name !== self.originalCharacter.name ||
        self.character.updateLog !== self.originalCharacter.updateLog
      )
    },
    changed() {
      return this.edited || this.initDataChanged
    },

    canPulish() {
      return !this.character.isCheck
    },
  },
  watch: {
    originalCharacter: function (val, oldVal) {
      this.character = this.$deepClone(val)

      if (this.routeTo.name) {
        this.$router.push({
          name: this.routeTo.name,
          params: this.routeTo.params,
        })
      }
    },
  },
  created() {
    if (this.$store.state.studioCharacter.character.id) {
      let character = this.$deepClone(
        this.$store.state.studioCharacter.character
      )
      this.character = {
        ...character,
      }
    }
  },

  methods: {
    checkVersion(rule, value, callback) {
      let self = this
      let first = parseInt(value[0]),
        second = parseInt(value[1]),
        third = parseInt(value[2])
      if (
        !Number.isInteger(first) ||
        !Number.isInteger(second) ||
        !Number.isInteger(third)
      ) {
        callback(new Error('版本号必须为数字值'))
      }
      if (
        value.join('.') == self.character.onlineVersion ||
        first < self.character.onlineVersion.split('.')[0]
      ) {
        callback(new Error('当前版本号需大于最近发布的版本号'))
      }
      if (first == self.character.onlineVersion.split('.')[0]) {
        if (second < self.character.onlineVersion.split('.')[1]) {
          callback(new Error('当前版本号需大于最近发布的版本号'))
        }
        if (
          second == self.character.onlineVersion.split('.')[1] &&
          third <= self.character.onlineVersion.split('.')[2] - 1
        ) {
          callback(new Error('当前版本号需大于最近发布的版本号'))
        }
      }
      callback()
    },

    checkInitDataStatus() {
      let self = this
      if (self.character.name != self.originalCharacter.name) {
        self.initDataChanged = true
      } else {
        self.initDataChanged = false
      }
    },

    checkStoreSkill() {
      return true
    },
    onSubmit(isPublish) {
      let self = this
      if (this.saving || this.publishing) {
        return
      }

      let canSubmit = true
      this.$refs.releaseForm.validate((valid) => {
        if (!valid) {
          canSubmit = false
        }
      })

      if (!canSubmit) return
      let data = {
        isPublish: isPublish,
        updateLog: this.character.updateLog || '',
      }
      data.latestVersion = this.character.latestVersionArr.join('.')
      if (data.latestVersion === '..') {
        data.latestVersion = ''
      }
      self.pageOptions.loading = true
      let api = ''

      if (isPublish === 1) {
        this.publishing = true
      } else {
        this.saving = true
      }
      this.$utils.httpPost(api, data, {
        success: (res) => {
          self.pageOptions.loading = false
          if (isPublish === 1) {
            this.publishing = false
            self.$message.success('发布成功')
          } else {
            this.saving = false
            self.$message.success('保存成功')
          }
          self.$store.dispatch('studioCharacter/setSkill', this.character.id)
          self.initDataChanged = false
        },
        error: (err) => {
          self.pageOptions.loading = false
          if (isPublish === 1) {
            this.publishing = false
          } else {
            this.saving = false
          }
          console.log('page=>>')
          console.log(err)
        },
      })
    },

    noSave() {
      this.$refs.skillForm.clearValidate()
      this.initDataChanged = false
      this.character = this.$deepClone(this.originalCharacter)

      if (this.routeTo.name) {
        this.$router.push({
          name: this.routeTo.name,
          params: this.routeTo.params,
        })
      }
    },
    noJump() {
      this.routeTo = {}
    },
  },
  components: {},
}
</script>

<style lang="scss" scoped>
.skill-publish-page {
  padding-bottom: 70px;
}
.release-form-price-select {
  width: 92px;
}
.release-form-price-input {
  .el-input__inner {
    width: 160px;
  }
}
.release-form-version-input {
  width: 80px;
}
.release-form-phrase-label {
  color: $grey5;
  padding-right: 24px;
}
.item-skill-privacy {
  font-size: 0;
}
.skill-privacy {
  display: inline-block;
  margin-right: 16px;
  width: 52px;
  height: 24px;
  line-height: 24px;
  text-align: center;
  color: $success;
  border-radius: 12px;
  background-color: $success-light-12;
}
.contact-form {
  .el-form-item {
    width: 43%;
    display: inline-block;
  }
  .el-form-item:nth-child(2n + 1) {
    margin-right: 13%;
  }
}
//实名认证
.certificate-wrap {
  padding: 40px 48px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 1);
  border: 1px solid rgba(242, 245, 247, 1);
  box-shadow: 0px 6px 18px 0px rgba(0, 0, 0, 0.05);
  font-size: 16px;
  .title {
    margin-bottom: 12px;
    line-height: 1.23;
    font-size: 20px;
    font-weight: 500;
  }
  .ic-r-exclamation {
    margin-right: 16px;
    vertical-align: -1px;
    color: $warning;
  }
}
.welcom-words-tip {
  padding-left: 16px;
  height: 40px;
  color: $grey5;
  background: $grey4-15;
  border-radius: 2px;
}

.param-container {
  margin: 0 auto;
  // width: 80%;

  li,
  .protocol-container {
    display: flex;
    //  justify-content: space-between;
    list-style-type: none;
    > div {
      border-left: 1px solid #eee;
      border-top: 1px solid #eee;
      border-bottom: 1px solid #eee;
      text-align: center;
      padding: 10px;
    }
    > div:last-child {
      border-right: 1px solid #eee;
    }
    > div:nth-child(1) {
      width: 20%;
    }
    > div:nth-child(2) {
      width: 20%;
    }
    > div:nth-child(3) {
      width: 20%;
    }
    > div:nth-child(4) {
      width: 20%;
    }
    > div:nth-child(5) {
      width: 20%;
    }
    ::v-deep .el-form-item__content {
      margin-left: 0 !important;
    }
  }

  .protocol-container {
    width: 100%;
  }
}
</style>
<style lang="scss">
.skill-publish-page {
  .os-collapse-title {
    margin: 28px 0;
    .ic-r-angle-d {
      color: $grey5;
    }
    a {
      font-size: 14px;
    }
  }
}
</style>
