<template>
  <el-dialog title="创建动作" :visible.sync="dialog.show" width="480px">
    <el-form :model="form" :rules="rules" ref="labelForm" label-position="top">
      <el-form-item label="中文名称" prop="zhName">
        <el-input
          v-model.trim="form.zhName"
          placeholder="支持中文/英文/数字/下划线格式，不超过32个字符"
          ref="valueInput"
          @keyup.enter.native="toName"
        />
      </el-form-item>
      <el-form-item label="英文标识" prop="value">
        <el-input
          ref="nameInput"
          v-model.trim="form.value"
          placeholder="支持英文/数字/下划线格式，不超过16个字符"
          @keyup.enter.native="save"
        />
      </el-form-item>
      <div class="upload-container">
        <el-form-item label="图片" prop="picture">
          <p class="text-tip">
            (支持gif/jpg/png/jpeg文件，宽高须相等，大小不超过500k)
          </p>
          <el-upload
            v-loading="loading"
            :action="`${$config.server}${this.$store.state.user.baseUrl}/skill/avatar/upload`"
            :show-file-list="false"
            :on-success="uploadSuccess"
            :before-upload="beforeUpload"
          >
            <img v-if="form.picture" :src="form.picture" class="avatar" />
            <i v-else class="ic-r-plus avatar-uploader-icon"></i>
          </el-upload>
        </el-form-item>
      </div>
      <el-form-item label="描述" prop="description">
        <el-input
          ref="descriptionInput"
          v-model.trim="form.description"
          placeholder="支持中文/标点符号，不超过50个字符"
          @keyup.enter.native="save"
        />
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button
        class="dialog-btn"
        type="primary"
        style="min-width: 104px"
        @click="save"
        :loading="saving"
      >
        {{ saving ? '创建中...' : '创建' }}
      </el-button>
    </span>
  </el-dialog>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  props: {
    dialog: {
      type: Object,
      default: {},
    },
  },
  data() {
    return {
      saving: false,
      form: {
        value: '',
        name: '',
        picture: '',
        description: '',
      },
      rules: {
        value: [
          this.$rules.required('动作英文标识不能为空'),
          this.$rules.lengthLimit(1, 16, '动作英文标识长度不能超过16个字符'),
          this.$rules.englishRegLimit(),
        ],
        zhName: [
          this.$rules.required('动作中文名不能为空'),
          this.$rules.lengthLimit(1, 32, '动作中文名长度不能超过32个字符'),
          this.$rules.baseRegLimit(),
        ],
        description: [
          this.$rules.lengthLimit(1, 50, '动作描述长度不能超过50个字符'),
          {
            pattern:
              /^[\u4e00-\u9fa5\s\·\~\！\@\#\￥\%\……\&\*\（\）\——\-\+\=\【\】\{\}\、\|\；\‘\’\：\“\”\《\》\？\，\。\、\`\~\!\#\$\%\^\&\*\(\)\_\[\]{\}\\\|\;\'\'\:\"\"\,\.\/\<\>\?]{0,}$/,
            message: '只支持汉字/标点符号',
            trigger: 'blur',
          },
        ],
        // picture: [this.$rules.required('动作图片不能为空')],
      },
      limitSize: 0.5, // 单位/M
      loading: false,
      imgBeforeUpload: '',
      // image: {
      //   url: '',
      // },
    }
  },
  computed: {
    ...mapGetters({
      subAccount: 'user/subAccount',
    }),
  },
  watch: {
    'dialog.show': function (val, oldVal) {
      let self = this
      if (val) {
        this.form = {
          value: '',
          zhName: '',
          picture: '',
          description: '',
        }
        // this.image.url = ''
        this.$refs.labelForm && this.$refs.labelForm.resetFields()
        this.$nextTick(function () {
          self.$refs.valueInput && self.$refs.valueInput.focus()
        })
      } else {
      }
    },
  },
  methods: {
    toName() {
      this.$refs.nameInput && this.$refs.nameInput.focus()
    },

    beforeUpload(file) {
      let isPNGOrJPG =
        file.type === 'image/gif' ||
        file.type === 'image/png' ||
        file.type === 'image/jpg' ||
        file.type === 'image/jpeg'
      let unExceed = file.size < 1024 * 1024 * 0.5
      if (!isPNGOrJPG) {
        this.$message.error('仅支持.png .jpg .jpeg格式文件')
        return false
      }
      if (!unExceed) {
        this.$message.error(`文件不能超过500k`)
        return false
      }
      let self = this
      self.loading = true
      return new Promise(function (resolve, reject) {
        let reader = new FileReader()
        reader.readAsDataURL(file)
        reader.onload = function (theFile) {
          let img = new Image()
          img.src = theFile.target.result
          img.onload = function () {
            if (img.height !== img.width) {
              self.$message.error(`图片宽高尺寸应相同`)
              self.loading = false
              reject('图片尺寸不对')
            } else {
              self.imgBeforeUpload = img.src
              resolve(file)
            }
          }
        }
      })
    },
    uploadSuccess(res) {
      if (res.data) {
        this.loading = false
        this.form.picture = res.data.url
        this.$message.success('上传成功')
        this.$refs.labelForm.validateField('picture')
        this.$emit('setIconInfo', res.data)
      } else {
        this.loading = false
        this.$message.error(res.desc)
        this.$emit('setIconInfo', '')
      }
    },
    save() {
      let self = this
      if (this.saving) {
        return
      }
      if (this.loading) {
        this.$message.warning('图片上传中，请稍候')
        return
      }

      this.$refs.labelForm.validate((valid) => {
        if (valid) {
          // if (!self.imgBeforeUpload) {
          //   return this.$message.warning('请先上传动作图片')
          // }
          this.saving = true
          let data = {
            value: this.form.value,
            zhName: this.form.zhName,
            picture: this.form.picture,
            description: this.form.description,
          }
          let api = this.$config.api.STUDIO_LABEL_CREATE
          this.$utils.httpPost(api, data, {
            success: (res) => {
              this.saving = false
              self.$message.success('创建成功')
              self.dialog.show = false
              self.$emit('change', 1)
              // 确认创建成功的跳转
              // self.subAccount
              //   ? self.$router.push({
              //       name: 'sub-auxiliary',
              //       params: { entityId: res.data.id },
              //     })
              //   : self.$router.push({
              //       name: 'auxiliary',
              //       params: { entityId: res.data.id },
              //     })
            },
            error: (err) => {
              this.saving = false
              console.log('page=>>')
              console.log(err)
            },
          })
        }
      })
    },
  },
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="scss" scoped>
.avatar-uploader-icon {
  width: 80px;
  height: 80px;
  line-height: 80px;
}
.ic-r-plus {
  font-size: 32px;
  color: $grey3;
}
.upload-tip {
  padding-top: 7px;
  padding-left: 16px;
  line-height: 22px;
}
.text-tip {
  font-size: 12px;
  color: #666;
  margin-bottom: 0;
  position: absolute;
  z-index: 1;
  top: -38px;
  left: 40px;
}

.upload-container {
  position: relative;
}

:deep(.el-upload) {
  width: 80px;
  height: 80px;
  border-radius: 2px;
  border: 1px dashed #d9d9d9;
  text-align: left;
  .avatar {
    width: 100%;
    height: 100%;
  }
  i {
    padding-left: 23px;
  }
}
</style>
