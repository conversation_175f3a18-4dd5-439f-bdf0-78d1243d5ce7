<template>
  <div class="os-scroll">
    <handle-platform-top v-if="subAccount"></handle-platform-top>
    <div
      class="handle-platform-content"
      v-loading="exportLoading"
      element-loading-text="正在导出中，请稍候"
      :style="{ padding: subAccount ? '10px' : 0 }"
    >
      <div class="mgb24 handle_platform_content_top">
        <div @keyup.enter="getAuxiliaries(1)">
          <el-input
            class="search-area"
            placeholder="通过名称、英文标识或辅助词词条搜索辅助词"
            size="medium"
            v-model="searchVal"
          >
            <i
              slot="suffix"
              class="el-input__icon el-icon-search search-area-btn"
              @click="getAuxiliaries(1)"
            />
          </el-input>
        </div>
        <div class="if-alc">
          <el-button
            icon="ic-r-plus"
            type="primary"
            size="medium"
            @click="openCreateauxiliary"
          >
            创建辅助词
          </el-button>
          <el-button
            type="text"
            size="small"
            style="margin-left: 24px"
            @click="linkOfficialJAuxiliary"
          >
            查看官方辅助词
          </el-button>
          <el-button
            type="text"
            size="small"
            style="margin-left: 24px"
            @click="exportAll"
          >
            一键导出辅助词
          </el-button>
        </div>
      </div>
      <os-table
        :tableData="tableData"
        class="auxiliar-table gutter-table-style transparent-bgc"
        :height="'calc(100vh - 230px)'"
        style="margin-bottom: 15px"
        @change="getAuxiliaries"
        @edit="toEdit"
        @del="del"
        @row-click="toEdit"
        v-if="hasItem"
      >
        <el-table-column prop="value" width="210" label="辅助词名称">
          <template slot-scope="scope">
            <div
              class="text-blod cp auxiliaries-page-entity-zh-name"
              :title="scope.row.value || '-'"
              @click.stop.prevent="toEdit(scope.row)"
            >
              {{ scope.row.value || '-' }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="name" width="130" label="英文标识">
          <template slot-scope="scope">
            <div class="auxiliarie-name" :title="scope.row.name">
              {{ scope.row.name }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="number" width="80" label="词条数">
          <template slot-scope="scope">
            <div>{{ scope.row.number }}条</div>
          </template>
        </el-table-column>
        <el-table-column prop="updateTime" width="200" label="更新时间">
          <template slot-scope="scope">
            <div>{{ scope.row.updateTime | date('yyyy-MM-dd hh:mm:ss') }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="count" width="120" label="被引用数">
          <template slot-scope="scope">
            <div
              v-if="scope.row.count"
              class="text-primary"
              style="cursor: pointer; height: 40px; line-height: 40px"
              @click="openCountDialog(scope.row)"
            >
              {{ scope.row.count }}
            </div>
            <span v-else>{{ scope.row.count }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" width="90" label="状态">
          <template slot-scope="scope">
            <div
              class="ib entity-status"
              :class="'entity-status-' + scope.row.status"
            />
            <span :class="{ 'entity-status-txt': scope.row.status !== 1 }">{{
              scope.row.status === 1 ? '已构建' : '未构建'
            }}</span>
          </template>
        </el-table-column>
      </os-table>
      <div class="create-guide" v-else>
        <div class="icon"></div>
        <p class="title">
          你还没有创建任何辅助词，
          <a @click="openCreateauxiliary"> 点击创建 </a>
        </p>
      </div>
    </div>
    <create-auxiliary-dialog :dialog="dialog" @change="getAuxiliaries" />
    <skill-quote-dialog :dialog="countDialog" />
  </div>
</template>

<script>
import HandlePlatformTop from './top.vue'
import CreateAuxiliaryDialog from './dialog/createAuxiliary.vue'
import SkillQuoteDialog from './dialog/skillQuote.vue'
import { mapGetters } from 'vuex'

export default {
  name: 'studio-handle-platform-auxiliaries',
  data() {
    return {
      nav: 'auxiliaries',
      searchVal: '',
      tableData: {
        loading: true,
        total: 0,
        page: 1,
        size: 10,
        handles: ['edit', 'del'],
        handleColumnText: '操作',
        list: [],
      },
      dialog: {
        show: false,
      },
      countDialog: {
        show: false,
        entityId: '',
      },
      hasItem: true,
      exportLoading: false,
    }
  },
  computed: {
    ...mapGetters({
      subAccount: 'user/subAccount',
    }),
  },
  created() {
    this.getAuxiliaries(1)
    if (localStorage.getItem('pageHandle') === 'createAux') {
      this.dialog.show = true
      localStorage.setItem('pageHandle', null)
    }
  },
  methods: {
    getAuxiliaries(page) {
      let self = this
      this.tableData.loading = true
      this.$utils.httpGet(
        this.$config.api.STUDIO_AUXILIARY_LIST,
        {
          pageIndex: page || this.tableData.page,
          pageSize: this.tableData.size,
          search: this.searchVal,
        },
        {
          success: (res) => {
            if (res.data.count <= 0 && !self.searchVal) {
              self.hasItem = false
            } else {
              self.hasItem = true
            }
            self.tableData.list = res.data.results
            self.tableData.total = res.data.count
            self.tableData.page = res.data.pageIndex
            self.tableData.size = res.data.pageSize
            self.tableData.loading = false
          },
          error: (err) => {},
        }
      )
    },
    openCreateauxiliary() {
      this.dialog.show = true
    },
    // 查看官方辅助词
    linkOfficialJAuxiliary() {
      let routeData = this.$router.resolve({
        name: 'studio-official-auxiliaries',
      })
      window.open(routeData.href, '_blank')
    },
    // 打开引用技能的dialog
    openCountDialog(entity) {
      this.countDialog.show = true
      this.countDialog.entityId = entity.id
    },
    toEdit(data) {
      let routeData
      if (this.subAccount) {
        routeData = this.$router.resolve({
          name: 'sub-auxiliary',
          params: { entityId: data.id },
        })
      } else {
        routeData = this.$router.resolve({
          name: 'auxiliary',
          params: { entityId: data.id },
        })
      }
      window.open(routeData.href, '_blank')
      // this.subAccount
      //   ? this.$router.push({
      //       name: 'sub-auxiliary',
      //       params: { entityId: data.id },
      //     })
      //   : this.$router.push({
      //       name: 'auxiliary',
      //       params: { entityId: data.id },
      //     })
    },
    del(data) {
      let self = this
      if (data.count) {
        this.$message.warning('该辅助词被技能引用，请先取消引用后再删除')
        return
      }
      this.$confirm(
        '辅助词删除后不可恢复，请谨慎操作。',
        `确定删除辅助词 - ${data.value}`,
        {
          confirmButtonText: '确定删除',
          cancelButtonText: '取消',
          confirmButtonClass: 'el-button--danger',
          type: 'warning',
          showClose: false,
        }
      )
        .then(() => {
          self.delEntity(data)
        })
        .catch(() => {})
    },
    delEntity(data) {
      let self = this
      this.tableData.loading = true
      this.$utils.httpPost(
        this.$config.api.STUDIO_ENTITY_DEL,
        {
          entityId: data.id,
        },
        {
          success: (res) => {
            self.$message.success('删除成功')
            if (self.tableData.list.length === 1 && self.tableData.page > 1) {
              self.tableData.page -= 1
            }
            self.getAuxiliaries()
          },
          error: (err) => {},
        }
      )
    },
    exportAll() {
      this.exportLoading = true
      this.$utils
        .postopen(this.$config.api.STUDIO_ENTITYS_EXCEL, {
          fileName: '辅助词压缩包',
          entityIds: '',
          isAuxiliary: '1',
        })
        .then(() => {
          this.exportLoading = false
        })
    },
  },
  components: {
    HandlePlatformTop,
    CreateAuxiliaryDialog,
    SkillQuoteDialog,
  },
}
</script>

<style lang="scss" scoped>
.handle-platform-content {
  // max-width: 1200px;
  width: 100%;
  margin: auto;
  .handle_platform_content_top {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
.search-area {
  width: 480px;
}

.entity-status {
  width: 8px;
  height: 8px;
  border: 2px solid;
  border-radius: 8px;
  margin-right: 6px;
  &-2 {
    border-color: $grey4;
  }
  &-1 {
    border-color: $success;
  }
  &-txt {
    color: $grey5;
  }
  &-count {
    color: $primary;
  }
}
.text-blod,
.auxiliarie-name {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.create-guide {
  margin: 76px 0;
  text-align: center;
  font-size: 16px;
  color: $grey5;
  .icon {
    margin: 0 auto 24px;
    width: 120px;
    height: 120px;
    background: url(../../../assets/images/app/create-app.png) center no-repeat;
    background-size: 100%;
  }
  .title {
    font-size: 16px;
    font-weight: 600;
    a {
      font-weight: 600;
    }
  }
  .desc {
    margin: 24px auto;
    width: 480px;
    font-size: 14px;
    line-height: 22px;
  }
}
</style>
<style lang="scss">
.el-table--enable-row-hover .el-table__body tr:hover > td {
  .auxiliaries-page-entity-zh-name {
    color: $primary;
  }
}
.el-table .ic-r-edit {
  color: $primary;
}
</style>
<style lang="scss">
.auxiliar-table {
  tr {
    cursor: pointer;
  }
}
</style>
