<template>
  <div class="variable-editor">
    <div ref="editorRef" class="editor-container"></div>
    <el-popover
      v-if="variables && variables.length > 0"
      ref="popover"
      placement="left-start"
      :value="popoverOpen"
      :visible-arrow="false"
      trigger="manual"
      @after-enter="handleAfterOpen"
    >
      <div class="tree-wrap my-variable-popover" tabindex="-1" ref="treeRef">
        <span>系统变量</span>
        <div
          v-for="item in variables"
          :key="item.index"
          class="info-item"
          @click="handleVariableInsert(item)"
        >
          {{ item.name }}
        </div>
        <!-- <el-divider></el-divider>
        <span style="margin-top: 20px">信源结果变量</span>
        <el-tree
          :data="formattedSourceList"
          :props="defaultProps"
          default-expand-all
          :highlight-current="true"
          :expand-on-click-node="false"
          :current-node-key="selectedKeys[0]"
          @current-change="handleCurrentChange"
          @node-click="handleNodeClick"
          ref="tree"
          node-key="id"
        >
          <div
            slot-scope="{ node, data }"
            class="flex-row-center"
            :class="{ 'disabled-node': data.name === '[Array Item]' }"
          >
            <svg-icon
              :iconClass="typeIcons[data.type] || typeIcons.string"
              :customStyle="{ width: '16px', height: '16px' }"
            />
            <span class="ml-1">{{ node.label }}</span>
          </div>
        </el-tree> -->
        <!-- <div
          v-for="item in popoverSouceList"
          :key="item.index"
          @click="handleVariableInsert(item)"
          class="info-item"
        >
          {{ item.name }}
        </div> -->
      </div>
      <span slot="reference" ref="anchorRef" class="anchor-point"></span>
    </el-popover>
  </div>
</template>

<script>
import {
  EditorView,
  ViewPlugin,
  placeholder,
  Decoration,
  keymap,
} from '@codemirror/view'
import { EditorState, RangeSetBuilder, StateEffect } from '@codemirror/state'
import { defaultKeymap, insertNewlineAndIndent } from '@codemirror/commands'

// 扁平化树结构
const flattenTree = (nodes, result = []) => {
  for (const node of nodes) {
    result.push({ key: node.id, title: node.name })
    if (node.children) {
      flattenTree(node.children, result)
    }
  }
  return result
}

export default {
  name: 'VariableEditor',
  props: {
    value: {
      type: String,
      default: '',
    },
    variables: {
      type: Array,
      default: () => [
        {
          id: 'user',
          name: 'user',
          type: 'object',
          children: [
            { id: 'user.name', name: 'name', type: 'string' },
            { id: 'user.age', name: 'age', type: 'number' },
          ],
        },
        {
          id: 'items',
          name: 'items',
          type: 'array<object>',
          children: [
            { id: 'items.title', name: 'title', type: 'string' },
            { id: 'items.price', name: 'price', type: 'number' },
          ],
        },
      ],
    },
    popoverSouceList: {
      type: Array,
      default: [],
    },
    placeholder: {
      type: String,
      default: '请输入内容...',
    },
  },
  data() {
    return {
      popoverOpen: false,
      selectedKeys: [],
      editorView: null,
      lastCursorPos: null,
      flattenedTree: [],
      defaultProps: {
        children: 'children',
        label: 'name',
      },
      // 类型图标映射
      typeIcons: {
        string: 'string',
        number: 'number',
        boolean: 'boolean',
        object: 'object',
        'array<object>': 'array-object',
        'array<string>': 'array-string',
        'array<number>': 'array-number',
        'array<boolean>': 'array-boolean',
      },
      formattedSourceList: [],
    }
  },
  computed: {
    currentIndex() {
      return this.flattenedTree.findIndex(
        (node) => node.key === this.selectedKeys[0]
      )
    },
  },
  mounted() {
    this.flattenedTree = flattenTree(this.variables)
    this.initEditor()
  },
  beforeDestroy() {
    if (this.editorView) {
      this.editorView.destroy()
    }
  },
  watch: {
    variables: {
      handler(newVal) {
        this.flattenedTree = flattenTree(newVal)
        if (this.editorView) {
          // 重新配置编辑器以更新插件
          this.editorView.dispatch({
            effects: StateEffect.reconfigure.of(this.createExtensions()),
          })
        }
      },
      deep: true,
    },
    popoverSouceList: {
      handler() {
        this.formatPopoverSourceList()
      },
      deep: true,
    },
    value(newVal) {
      if (this.editorView && newVal !== this.editorView.state.doc.toString()) {
        this.editorView.dispatch({
          changes: {
            from: 0,
            to: this.editorView.state.doc.length,
            insert: newVal,
          },
        })
      }
    },
    popoverOpen(val) {
      if (val && this.flattenedTree.length > 0) {
        this.selectedKeys = [this.flattenedTree[0].key]
        this.$nextTick(() => {
          if (this.$refs.tree) {
            this.$refs.tree.setCurrentKey(this.selectedKeys[0])
          }
        })
      }
    },
  },
  methods: {
    formatPopoverSourceList() {
      const formattedList = JSON.parse(JSON.stringify(this.popoverSouceList))

      // 递归处理数组项
      const formatArrayType = (items) => {
        if (!items || !Array.isArray(items)) return

        items.forEach((item) => {
          // 处理当前项
          if (
            item.type === 'array' &&
            item.children &&
            item.children.length > 0
          ) {
            const firstChild = item.children[0]
            if (firstChild) {
              // 根据第一个子元素的type更新当前项的type
              if (firstChild.type === 'object') {
                item.type = 'array<object>'
              } else if (firstChild.type === 'string') {
                item.type = 'array<string>'
              } else if (firstChild.type === 'number') {
                item.type = 'array<number>'
              } else if (firstChild.type === 'boolean') {
                item.type = 'array<boolean>'
              }
            }
          }

          // 递归处理子项
          if (item.children && item.children.length > 0) {
            formatArrayType(item.children)
          }
        })
      }

      // 开始递归处理
      formatArrayType(formattedList)

      this.formattedSourceList = formattedList
    },

    initEditor() {
      if (!this.$refs.editorRef) return

      this.editorView = new EditorView({
        doc: this.value,
        parent: this.$refs.editorRef,
        extensions: this.createExtensions(),
      })

      // 添加失焦事件
      this.$refs.editorRef.addEventListener('blur', this.onEditorBlur)
    },
    createExtensions() {
      return [
        placeholder(this.placeholder || '请输入内容...'),
        EditorView.editable.of(true),
        EditorView.lineWrapping,
        keymap.of([
          ...defaultKeymap,
          { key: 'Enter', run: insertNewlineAndIndent },
        ]),
        EditorState.languageData.of(() => {
          return [{ autocomplete: () => [] }]
        }),
        this.createUpdateListener(),
        this.createVariablePlugin(),
        this.createInterpolationPlugin(this.variables),
      ]
    },
    createUpdateListener() {
      return EditorView.updateListener.of((update) => {
        if (update.docChanged) {
          // const content = update.state.doc.toString();
          // 不要在每次更改时都触发，而是在失焦时触发

          const content = update.state.doc.toString()
          this.$emit('input', content)
          this.$emit('change', content)
        }
      })
    },
    createVariablePlugin() {
      const self = this
      return ViewPlugin.fromClass(
        class {
          constructor(view) {
            this.view = view
          }

          update(update) {
            if (update.docChanged || update.selectionSet) {
              const pos = update.state.selection.main.head
              const doc = update.state.doc.toString()

              // 只有当光标位置真正变化时才更新
              if (self.lastCursorPos !== pos) {
                self.lastCursorPos = pos
                // 延迟更新 Popover 位置
                setTimeout(() => {
                  self.$refs.popover &&
                    self.$refs.popover.$el &&
                    self.$refs.popover.updatePopper()
                }, 10)
              }

              // 1. 正则查找所有的 {xxx}
              const regex = /\{(.*?)\}/g
              let match
              let inInterpolation = false

              while ((match = regex.exec(doc)) !== null) {
                const start = match.index
                const end = start + match[0].length

                if (pos > start && pos < end) {
                  // 光标在插值表达式内
                  inInterpolation = true
                  setTimeout(() => {
                    const coords = this.view.coordsAtPos(pos)
                    const editorRect = this.view.dom.getBoundingClientRect()

                    if (coords) {
                      self.$refs.anchorRef.style.position = 'absolute'
                      self.$refs.anchorRef.style.left = `${
                        coords.left - editorRect.left - 10
                      }px`
                      self.$refs.anchorRef.style.top = `${
                        coords.top - editorRect.top
                      }px`
                      self.$refs.anchorRef.dataset.start = start
                      self.$refs.anchorRef.dataset.end = end
                      self.popoverOpen = true
                    }
                  }, 0)

                  break
                }
              }

              if (!inInterpolation) {
                // 检测输入 { 的情况
                const prev = update.state.sliceDoc(pos - 1, pos)
                if (prev === '{') {
                  setTimeout(() => {
                    const coords = this.view.coordsAtPos(pos)
                    const editorRect = this.view.dom.getBoundingClientRect()
                    if (coords) {
                      self.$refs.anchorRef.style.position = 'absolute'
                      self.$refs.anchorRef.style.left = `${
                        coords.left - editorRect.left - 10
                      }px`
                      self.$refs.anchorRef.style.top = `${
                        coords.top - editorRect.top
                      }px`
                      self.$refs.anchorRef.dataset.start = pos
                      self.$refs.anchorRef.dataset.end = pos
                      self.popoverOpen = true
                    }
                  }, 0)
                } else {
                  self.popoverOpen = false
                }
              }
            }
          }
        }
      )
    },
    createInterpolationPlugin(variables) {
      const self = this
      return ViewPlugin.fromClass(
        class {
          constructor(view) {
            this.decorations = this.buildDecorations(view)
          }

          update(update) {
            if (update.docChanged || update.viewportChanged) {
              this.decorations = this.buildDecorations(update.view)
            }
          }

          buildDecorations(view) {
            const builder = new RangeSetBuilder()
            const doc = view.state.doc
            const text = doc.toString()
            const regex = /\{(.*?)\}/g
            let match

            while ((match = regex.exec(text)) !== null) {
              const [full, expr] = match
              const start = match.index
              const end = start + full.length

              const isValid = self.validatePath(variables, expr.trim())

              const deco = Decoration.mark({
                class: isValid
                  ? 'cm-decoration-interpolation-valid'
                  : 'cm-decoration-interpolation-invalid',
              })

              builder.add(start, end, deco)
            }

            return builder.finish()
          }
        },
        {
          decorations: (v) => v.decorations,
        }
      )
    },
    validatePath(schema, rawPath) {
      if (!rawPath || !schema || schema.length === 0) {
        return false
      }

      // 处理原始路径，去除可能的括号部分
      const cleanPath = rawPath.replace(/（.*?）/g, '').trim()
      const segments = cleanPath.replace(/\[(\d+)\]/g, '[$1]').split('.')

      // 对于简单变量（没有点号的情况），直接在schema中查找匹配项
      if (segments.length === 1 && segments[0]) {
        const currentSegment = segments[0].trim()

        // 遍历所有变量，检查是否有匹配项
        for (const variable of schema) {
          if (!variable || !variable.name) continue

          // 提取变量名中括号前的部分
          let variableName = variable.name
          const nameMatch = variableName.match(/([^（]+)/)
          if (nameMatch && nameMatch[1]) {
            variableName = nameMatch[1].trim()
          }

          // 如果找到匹配的变量名，返回true
          if (variableName === currentSegment) {
            return true
          }
        }

        return false
      }

      // 递归匹配复杂路径（包含点号的情况）
      function match(nodes, index) {
        if (!nodes || nodes.length === 0) return false
        if (index >= segments.length) return true

        const currentKey = segments[index].trim()
        if (!currentKey) return false

        for (const node of nodes) {
          if (!node) continue

          // 获取节点标题，去除可能的括号部分
          let nodeTitle = node.name || node.label || ''
          const titleMatch = nodeTitle.match(/([^（]+)/)
          if (titleMatch && titleMatch[1]) {
            nodeTitle = titleMatch[1].trim()
          }

          const type = node.type || ''
          const children = node.children || []

          // 匹配数组字段，如 abc[0]
          if (/\[\d+\]$/.test(currentKey)) {
            const name = currentKey.replace(/\[\d+\]$/, '').trim()
            if (
              nodeTitle === name &&
              type.includes('array') &&
              children.length > 0
            ) {
              return match(children, index + 1)
            }
          }

          // 匹配普通字段
          if (nodeTitle === currentKey) {
            if (
              (type.includes('object') || type.includes('array')) &&
              children.length > 0
            ) {
              return match(children, index + 1)
            }
            // 如果不是object类型，且已经是最后一个字段
            return index === segments.length - 1
          }
        }

        return false
      }

      return match(schema, 0)
    },
    handleAfterOpen() {
      if (this.$refs.treeRef) {
        this.$refs.treeRef.focus()
      }
    },
    handleCurrentChange(data) {
      if (data) {
        this.selectedKeys = [data.id]
      }
    },
    handleVariableInsert(data) {
      // 提取变量名，去除括号中的中文解释
      let key = data.name
      // 提取变量名中括号前的部分
      const nameMatch = key.match(/([^（]+)/)
      if (nameMatch && nameMatch[1]) {
        key = nameMatch[1]
      }

      this.selectedKeys = [data.id]

      const view = this.editorView
      if (!view) return

      const state = view.state
      const pos = state.selection.main.head
      const doc = state.doc.toString()

      let insertText = `{${key}}`
      let targetFrom = pos
      let targetTo = pos
      let foundInBraces = false

      // 检查光标是否在 {...} 内部
      const regex = /\{[^}]*\}/g
      let match
      while ((match = regex.exec(doc)) !== null) {
        const [full] = match
        const start = match.index
        const end = start + full.length
        if (pos > start && pos < end) {
          targetFrom = start
          targetTo = end
          foundInBraces = true
          break
        }
      }

      // 如果不在 {...} 中，但光标前是 `{`，只插入 `${key}}`，不要加多一个 `{`
      if (!foundInBraces && doc[pos - 1] === '{') {
        targetFrom = pos
        insertText = `${key}}` // 前面已经有 {，只补后半段
      }

      const transaction = state.update({
        changes: {
          from: targetFrom,
          to: targetTo,
          insert: insertText,
        },
        selection: { anchor: targetFrom + insertText.length },
      })

      view.dispatch(transaction)
      view.focus()
      this.popoverOpen = false
    },
    onEditorBlur(e) {
      const related = e.relatedTarget

      // 如果焦点转移到了 Popover 内部，则不处理 blur
      if (related && related.closest('.my-variable-popover')) {
        return
      }

      // const view = this.editorView
      // if (view) {
      //   this.$emit('input', view.state.doc.toString())
      //   this.$emit('blur')
      // }
      this.$emit('blur')
    },
    handleKeyDownCapture(e) {
      if (!['ArrowUp', 'ArrowDown', 'Enter'].includes(e.key)) {
        e.stopPropagation()
      }
    },
    handleKeyDown(e) {
      if (!['ArrowUp', 'ArrowDown', 'Enter'].includes(e.key)) return

      if (e.key === 'ArrowDown') {
        let nextKey
        if (this.currentIndex < this.flattenedTree.length - 1) {
          nextKey = this.flattenedTree[this.currentIndex + 1].key
        } else {
          nextKey = this.flattenedTree[0].key
        }
        this.selectedKeys = [nextKey]
        this.$refs.tree.setCurrentKey(nextKey)
      } else if (e.key === 'ArrowUp') {
        let prevKey
        if (this.currentIndex > 0) {
          prevKey = this.flattenedTree[this.currentIndex - 1].key
        } else {
          prevKey = this.flattenedTree[this.flattenedTree.length - 1].key
        }
        this.selectedKeys = [prevKey]
        this.$refs.tree.setCurrentKey(prevKey)
      } else if (e.key === 'Enter' && this.selectedKeys[0]) {
        // 获取当前选中的树节点
        const currentNode = this.$refs.tree.getNode(this.selectedKeys[0])

        if (currentNode && currentNode.data) {
          // 如果是[Array Item]节点，不执行插入操作
          if (currentNode.data.name === '[Array Item]') {
            return
          }

          // 使用递归构建完整路径
          const fullPath = this.buildPathRecursively(currentNode)
          this.handleVariableInsertWithPath(currentNode.data, fullPath)
        }
      }
    },
    handleNodeClick(data, node) {
      if (data.name === '[Array Item]') {
        // 如果点击的是 [Array Item]，不执行插入操作
        return
      }

      // 使用递归构建完整路径
      const fullPath = this.buildPathRecursively(node)

      // 插入带完整路径的变量
      this.handleVariableInsertWithPath(data, fullPath)
    },

    // 递归构建完整路径
    buildPathRecursively(node) {
      const data = node.data

      // 如果是[Array Item]节点，应该被跳过，不作为路径的一部分
      if (data.name === '[Array Item]') {
        // 如果是[Array Item]节点，直接返回父节点路径
        return this.buildPathRecursively(node.parent)
      }

      // 处理节点名称，去除括号中的中文解释
      let nodeName = data.name
      // 提取变量名中括号前的部分
      const nameMatch = nodeName.match(/([^（]+)/)
      if (nameMatch && nameMatch[1]) {
        nodeName = nameMatch[1]
      }

      // 如果是根节点或顶级节点，直接返回节点名称
      if (!node.parent || node.parent.level === 0) {
        return data.name // 保留完整名称，因为在handleVariableInsertWithPath中会处理
      }

      // 获取父节点路径
      const parentPath = this.buildPathRecursively(node.parent)

      // 判断父节点类型
      const parentData = node.parent.data
      const isParentArray =
        parentData.type === 'array' ||
        parentData.type === 'array<string>' ||
        parentData.type === 'array<number>' ||
        parentData.type === 'array<boolean>' ||
        parentData.type === 'array<object>'

      if (parentData.name === '[Array Item]') {
        // 如果父节点是[Array Item]，我们需要找到祖父节点的路径
        // 由于[Array Item]被跳过，parentPath已经是祖父节点的路径了
        if (node.parent.parent && node.parent.parent.data) {
          const grandParentData = node.parent.parent.data
          const isGrandParentArray =
            grandParentData.type === 'array' ||
            grandParentData.type === 'array<string>' ||
            grandParentData.type === 'array<number>' ||
            grandParentData.type === 'array<boolean>' ||
            grandParentData.type === 'array<object>'

          if (isGrandParentArray) {
            // 祖父节点是数组，所以我们需要添加[0]
            return `${parentPath}[0].${nodeName}`
          }
        }

        // 默认情况：如果祖父节点不是数组或者没有祖父节点
        return `${parentPath}.${nodeName}`
      } else if (isParentArray) {
        // 如果父节点是数组，添加[0]
        return `${parentPath}[0].${nodeName}`
      } else {
        // 父节点是对象，使用点连接
        return `${parentPath}.${nodeName}`
      }
    },
    // 使用完整路径插入变量
    handleVariableInsertWithPath(data, fullPath) {
      this.selectedKeys = [data.id]

      const view = this.editorView
      if (!view) return

      const state = view.state
      const pos = state.selection.main.head
      const doc = state.doc.toString()

      // 处理路径，去除括号中的中文解释
      let processedPath = fullPath
      // 提取变量名中括号前的部分
      const nameMatch = processedPath.match(/([^（]+)/)
      if (nameMatch && nameMatch[1]) {
        processedPath = nameMatch[1]
      }

      let insertText = `{${processedPath}}`
      let targetFrom = pos
      let targetTo = pos
      let foundInBraces = false

      // 检查光标是否在 {...} 内部
      const regex = /\{[^}]*\}/g
      let match
      while ((match = regex.exec(doc)) !== null) {
        const [full] = match
        const start = match.index
        const end = start + full.length
        if (pos > start && pos < end) {
          targetFrom = start
          targetTo = end
          foundInBraces = true
          break
        }
      }

      // 如果不在 {...} 中，但光标前是 `{`，只插入 `${key}}`，不要加多一个 `{`
      if (!foundInBraces && doc[pos - 1] === '{') {
        targetFrom = pos
        insertText = `${processedPath}}` // 前面已经有 {，只补后半段
      }

      const transaction = state.update({
        changes: {
          from: targetFrom,
          to: targetTo,
          insert: insertText,
        },
        selection: { anchor: targetFrom + insertText.length },
      })

      view.dispatch(transaction)
      view.focus()
      this.popoverOpen = false
    },
  },
}
</script>

<style scoped lang="scss">
.variable-editor {
  position: relative;
  width: 100%;

  .editor-container {
    width: 100%;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    min-height: 150px;
    overflow: hidden;
    transition: border-color 0.2s, box-shadow 0.2s;

    &:focus-within {
      border-color: #409eff !important;
    }
  }
}

.anchor-point {
  position: absolute;
  z-index: 10;
}

.tree-wrap {
  min-width: 200px;

  // 添加树节点的样式
  :deep(.el-tree-node__content) {
    cursor: pointer;
    display: flex;
    align-items: center;

    &:hover {
      background-color: #f5f7fa;
    }
  }

  // 修复展开图标样式
  :deep(.el-tree-node__expand-icon) {
    padding: 6px;

    &.expanded {
      transform: rotate(90deg); // 确保展开时图标旋转90度
    }

    &.is-leaf {
      color: transparent; // 叶子节点的图标透明
    }
  }

  // 当前选中节点的样式
  :deep(.el-tree-node.is-current > .el-tree-node__content) {
    background-color: #f0f7ff;
    color: #409eff;
  }

  // 禁用节点的样式
  .disabled-node {
    cursor: not-allowed !important;
    opacity: 0.6;
    color: #999;
  }
}

.flex-row-center {
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
}

.ml-1 {
  margin-left: 4px;
}

/* CodeMirror 6 编辑器样式 */
:global(.cm-editor) {
  height: 150px !important;
  min-height: 150px !important;
  overflow-y: auto;
  padding: 3px;
  &.cm-focused {
    outline: none;
  }
}

/* 修复El-tree展开图标旋转问题 */
:global(.el-tree-node__expand-icon.el-icon-caret-right) {
  transition: transform 0.3s ease;
}
/* 插值表达式样式 */
:global(.cm-decoration-interpolation-valid) {
  color: #409eff !important;
  background-color: rgba(64, 158, 255, 0.1);
  border-radius: 2px;
}

:global(.cm-decoration-interpolation-invalid) {
  color: #f56c6c !important;
  background-color: rgba(245, 108, 108, 0.1);
  text-decoration: wavy underline #f56c6c;
  border-radius: 2px;
}

.info-item {
  cursor: pointer;
  width: 200px;
  padding: 3px;
}
.info-item:hover {
  background-color: #f0f7fd;
  border-radius: 6px;
}
</style>
