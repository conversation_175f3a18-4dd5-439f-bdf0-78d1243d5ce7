<template>
  <div>
    <os-collapse :default="true" size="large">
      <template slot="title">
        语料示例
        <el-tooltip
          class="item"
          effect="dark"
          content="系统提供的用户常用的表述，自定义添加表述，请至拓展语料模块进行添加。"
          placement="right"
        >
          <i class="el-icon-question" />
        </el-tooltip>
      </template>
      <div class="utterance-collapse" v-loading="utterancesData.loading">
        <div
          v-for="(utterance, index) in oldUtterances"
          class="utterance-collapse-item"
          :key="index"
        >
          <div
            class="utterance-collapse-item-title"
            :class="{
              'utterance-collapse-item-title-show':
                collapseActiveExample === utterance.id,
            }"
            @click="handleCollapseExample(utterance, $event)"
          >
            <os-utterance
              :defaultUtterance="utterance"
              :defaultIndex="index"
              :subAccountEditable="false"
              :referenceMode="true"
            />
          </div>
          <div
            class="utterance-collapse-item-content"
            :class="{
              'utterance-collapse-item-content-show':
                collapseActiveExample === utterance.id,
            }"
          >
            <utterance-slot-table
              :utterance="utterance"
              :subAccountEditable="false"
            />
          </div>
        </div>
      </div>
      <div>
        <el-pagination
          ref="pagination"
          v-if="showPagination"
          class="txt-al-c"
          @current-change="getUtterances"
          :current-page="utterancesData.page"
          :page-size="utterancesData.size"
          :total="utterancesData.total"
          :layout="pageLayout"
        >
        </el-pagination>
      </div>
    </os-collapse>
    <os-divider class="mgt28" />
  </div>
</template>
<script>
import UtteranceSlotTable from '../utteranceSlotTable.vue'

export default {
  data() {
    return {
      utterancesData: {
        loading: true,
        total: 0,
        page: 1,
        size: 1000,
        list: [],
      },
      oldUtterances: [],
      collapseActiveExample: false,
    }
  },
  created() {
    this.getUtterances()
  },
  computed: {
    pageLayout() {
      if (this.utterancesData.total / this.utterancesData.size > 7) {
        return 'prev, pager, next, jumper'
      }
      return 'prev, pager, next'
    },
    showPagination() {
      return this.utterancesData.total > this.utterancesData.size
    },
  },
  methods: {
    // 获取语料列表
    getUtterances(page) {
      let self = this
      this.utterancesData.loading = true
      this.$utils.httpGet(
        this.$config.api.STUDIO_INTENT_SYSTEM_UTTERANCES,
        {
          quoteId: this.$route.params.quoteId,
          pageIndex: page || this.utterancesData.page,
          pageSize: this.utterancesData.size,
          utterance: this.utteranceSearchName,
        },
        {
          success: (res) => {
            // 根据产品需求，只截3条
            let result = res.data.utterances || []
            if (result.length > 3) {
              result = result.slice(0, 3)
            }
            self.utterancesData.list = result
            self.oldUtterances = []
            self.utterancesData.total = res.data.count
            self.utterancesData.page = res.data.pageIndex
            self.utterancesData.size = res.data.pageSize
            self.$nextTick(function () {
              self.oldUtterances = JSON.parse(JSON.stringify(result))
            })
            self.utterancesData.loading = false
            // self.getCurrentUtterFzy(self.utterancesData.list)
          },
          error: (err) => {
            self.utterancesData.loading = false
          },
        }
      )
    },

    // 示例语料
    handleCollapseExample(item, event) {
      if (
        (this.collapseActiveExample === item.id &&
          event.target.className !==
            'utterance-area utterance-area-disabled') ||
        event.target.className === 'ic-r-delete' ||
        item.template !== 2
      ) {
        return
      }
      this.collapseActiveExample =
        this.collapseActiveExample === item.id ? '' : item.id
    },
  },
  components: {
    UtteranceSlotTable,
  },
}
</script>
<style lang="scss" scoped>
.utterance-collapse-item {
  border: 1px solid $grey2;
  border-bottom: 0;
  transition: all 0.2s;
  &-show {
    border-bottom: 2px solid $primary;
  }
}

.utterance-collapse-item-title {
  position: relative;
  &-del {
    position: absolute;
    right: 16px;
    top: 0;
    bottom: 0;
    margin: auto;
    line-height: 44px;
    cursor: pointer;
    display: none;
  }
  &:hover {
    .utterance-collapse-item-title-del {
      display: block;
    }
  }
}

.utterance-collapse-item:last-child {
  border-bottom: 1px solid $grey2;
}

.utterance-collapse-item-content {
  max-height: 0;
  overflow: hidden;
  // transition: max-height 0.2s ease-in;
  &-show {
    transition: max-height 0.2s ease-in;
    max-height: 900px;
    overflow: auto;
  }
}
</style>
