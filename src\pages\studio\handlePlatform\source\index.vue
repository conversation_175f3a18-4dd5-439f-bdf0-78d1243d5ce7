<template>
  <div class="container">
    <div class="source-header">
      <div class="source_header_title">信源库</div>
      <span>智能体支持接入外部信源</span>
    </div>

    <div class="source-content">
      <div class="operate-area">
        <el-button type="primary" @click="addInfoSource">创建信源</el-button>
        <el-input
          class="search-area"
          v-model="searchName"
          @keydown.enter.native="getList(1)"
          clearable
          style="width: 300px"
          placeholder="搜索信源名称"
          size="medium"
        >
          <i
            slot="suffix"
            class="el-input__icon el-icon-search search-area-btn"
            @click="getList(1)"
          />
        </el-input>
      </div>

      <os-table
        :tableData="tableData"
        @del="toDel"
        @edit="toEdit"
        @change="getList"
        @row-click="toDetail"
        class="source-table gutter-table-style transparent-bgc"
        :height="'calc(100vh - 212px)'"
      >
        <el-table-column prop="sourceName" label="信源名称">
          <template slot-scope="scope">
            <os-skill-simple-item
              class="cp skills-page-skill-zh-name"
              :url="scope.row.url"
              :name="scope.row.sourceName || '-'"
            />
          </template>
        </el-table-column>
        <el-table-column prop="description" label="描述"></el-table-column>
        <el-table-column prop="apiCount" label="接口数量"></el-table-column>
        <el-table-column
          prop="useCount"
          label="被引用数(智能体数量)"
        ></el-table-column>
        <el-table-column label="更新日期" prop="updateTime"></el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-tooltip
              class="item"
              effect="dark"
              content="编辑"
              placement="top"
            >
              <i
                class="sub-account cell-handle-ic ic-r-edit"
                @click.stop.prevent="toEdit(scope.row)"
              />
            </el-tooltip>

            <i
              class="sub-account cell-handle-hovershow cell-handle-ic ic-r-delete"
              @click.prevent.stop="toDel(scope.row)"
            />
          </template>
        </el-table-column>
      </os-table>
    </div>

    <el-dialog
      :visible="modalVisible"
      :title="title"
      @close="cancel"
      :close-on-click-modal="false"
      width="480px"
    >
      <el-form
        :model="form"
        ref="form"
        :rules="rules"
        label-width="80px"
        label-position="top"
      >
        <el-form-item label="信源名称:" prop="sourceName">
          <el-input v-model="form.sourceName"></el-input>
        </el-form-item>

        <el-form-item label="信源描述:" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="3"
          ></el-input>
        </el-form-item>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button @click="cancel">取消</el-button>
        <el-button
          class="dialog-btn"
          type="primary"
          @click="submit"
          :loading="saving"
        >
          {{ saving ? '创建中...' : '确定' }}
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'studio-platForm-source',
  data() {
    return {
      searchName: '',
      tableData: {
        loading: false,
        total: 0,
        page: 1,
        size: 10,
        list: [],
      },

      modalVisible: false,
      title: '新增信源',
      saving: false,
      form: {
        sourceName: null,
        description: null,
      },
      rules: {
        sourceName: [
          { required: true, message: '请输入信源名称', trigger: 'blur' },
          {
            pattern: /^[\u4e00-\u9fa5a-zA-Z0-9._-]{0,32}$/,
            message:
              '输入内容只能包含中文、英文字母、数字、小数点、短横线和下划线，且长度不超过32个字符',
            trigger: 'blur',
          },
        ],
        description: [
          { required: true, message: '请输入信源描述', trigger: 'blur' },
          { max: 250, message: '输入内容请在250个字符以内', trigger: 'blur' },
        ],
      },
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList(page) {
      const params = {
        pageSize: this.tableData.size,
        pageIndex: page || this.tableData.page,
        sourceName: this.searchName,
      }
      this.tableData.loading = true
      this.$utils.httpPost(
        '/aiui-agent/openPlatform/source/page',
        JSON.stringify(params),
        {
          config: {
            headers: {
              'Content-Type': 'application/json;charset=UTF-8',
            },
          },
          success: (res) => {
            if (res.code === '0') {
              this.tableData.list = res.data.records
              this.tableData.total = res.data.total
              this.tableData.loading = false
            }
          },
          error: (err) => {
            this.tableData.loading = false
          },
        }
      )
    },
    addInfoSource() {
      this.modalVisible = true
    },

    toDel(item) {
      console.log(item, '删除的item')
      this.$confirm('将删除此信源, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          this.$utils.httpPost(
            `/aiui-agent/openPlatform/source/delete?sourceId=${item.sourceId}`,

            {},
            {
              config: {
                headers: {
                  'Content-Type': 'application/json;charset=UTF-8',
                },
              },
              success: (res) => {
                if (res.code === '0') {
                  this.$message.success('操作成功')
                  this.getList()
                }
              },
              error: (err) => {
                this.$message.error(err.desc)
              },
            }
          )
        })
        .catch(() => {})
    },

    toDetail(item) {
      this.$router.push({
        name: 'studio-source-detail',
        params: {
          sourceId: item.sourceId,
        },
      })
    },
    toEdit(item) {
      this.modalVisible = true
      this.title = '编辑信源'
      this.sourceId = item.sourceId
      this.form = {
        sourceName: item.sourceName,
        description: item.description,
      }
    },
    submit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          const params = this.sourceId
            ? {
                sourceName: this.form.sourceName,
                description: this.form.description,
                sourceId: this.sourceId,
              }
            : {
                sourceName: this.form.sourceName,
                description: this.form.description,
              }
          this.saving = true

          let SourceApi = this.sourceId
            ? '/aiui-agent/openPlatform/source/edit'
            : '/aiui-agent/openPlatform/source/add'
          this.$utils.httpPost(SourceApi, JSON.stringify(params), {
            config: {
              headers: {
                'Content-Type': 'application/json;charset=UTF-8',
              },
            },
            success: (res) => {
              if (res.code === '0') {
                this.saving = false
                this.$message.success('操作成功')
                this.cancel()
                this.getList()
              }
            },
            error: (err) => {
              this.saving = false
              this.$message.error(err.desc)
            },
          })
        }
      })
    },
    cancel() {
      this.modalVisible = false
      this.form = {
        sourceName: null,
        description: null,
      }
      this.sourceId = null
      this.title = '新增信源'
      this.$refs.form.resetFields()
    },
  },
}
</script>

<style lang="scss" scoped>
.container {
  padding-top: 0px;
  background-color: $white-grey;
  height: 100%;
  .source-header {
    display: flex;
    height: 63px;
    align-items: center;
    padding: 0px 24px;
    h2 {
      margin-right: 10px;
      font-size: 20px;
      font-weight: 600;
    }
    span {
      font-size: 16px;
      color: #666666;
    }
    .source_header_title {
      font-size: 20px;
      font-weight: 600;
      color: #000;
      margin-right: 10px;
    }
  }
  .source-content {
    padding: 10px 20px;

    .operate-area {
      display: flex;
      justify-content: space-between;
      align-content: center;
      flex-direction: row-reverse;
      margin-bottom: 20px;
    }
  }
}

.source-table {
  .el-table td {
    padding: 0;
  }
  .el-table tr {
    cursor: pointer;
  }
}
</style>
