<template>
  <div class="content-container">
    <!-- <div class="top-area">
      <el-tabs v-model="businessType" @tab-click="handleCategoryClick">
        <el-tab-pane
          v-for="(item, index) in categoryTypes"
          :label="item.label"
          :name="item.value"
          :key="index"
        ></el-tab-pane>
      </el-tabs>
     
    </div> -->
    <div class="os-scroll-bottom" v-loading.body="skillsData.loading">
      <!-- <p
        class="store-desc"
        :style="{ visibility: skillsData.loading ? 'hidden' : 'visible' }"
      >
        AIUI平台最新上架了五个方言技能，方言技能里融合了普通话、粤语、四川话语义，同方言识别与方言合成组合起来可以达到方言交互的全链路效果。<br />
        AIUI平台最新上架了十个封闭技能，进入封闭技能后退出封闭技能前，交互只会在封闭技能内进行，不会进入其他的技能。对于一些依靠上下文来进行交互的游戏效果有着显著提升。
      </p> -->
      <template v-if="skillsData.total > 0">
        <div class="store-skills-row">
          <div
            class="store-skills-col ib"
            v-for="(skill, index) in skillsData.list"
            :key="index"
          >
            <div class="os-store-skill" @click="toSkillDetail(skill)">
              <div class="os-store-skill-thumb">
                <img v-if="skill.url" :src="skill.url" />
                <span v-else>{{
                  skill.zhName && skill.zhName.substr(0, 1)
                }}</span>
              </div>
              <div class="os-store-skill-content">
                <p class="os-store-skill-name" :title="skill.zhName">
                  {{ skill.zhName }}
                </p>
                <p class="os-store-skill-provider">
                  {{
                    skill.provider !== 'undefined' ? skill.provider || '-' : '-'
                  }}
                </p>
                <p class="os-store-skill-desc" :title="skill.briefIntroduction">
                  {{ skill.briefIntroduction }}
                </p>
              </div>
              <div class="os-store-skill-tag-area">
                <span class="os-store-skill-tag blue">
                  {{
                    skill.provider.includes('科大讯飞')
                      ? '官方技能'
                      : '第三方技能'
                  }}
                </span>
              </div>
              <div class="os-store-skill-tag-area tag-area-bottom">
                <span v-if="skill.count" class="os-store-skill-tag">
                  {{ skill.count }}个信源
                </span>
                <!-- <span v-if="skill.dialectinfo" class="os-store-skill-dialect-tag">
                {{ skill.dialectinfo }}
              </span> -->
              </div>
            </div>
          </div>
        </div>
        <div class="store-skill-page">
          <el-pagination
            v-if="showPagination"
            ref="pagination"
            class="txt-al-c"
            @current-change="getSkills"
            :current-page="skillsData.page"
            :page-size="skillsData.size"
            :total="skillsData.total"
            :layout="pageLayout"
          >
          </el-pagination>
        </div>
      </template>

      <!-- <div class="empty-skill-tip" v-if="skillsData.total === 0 && !skillsData.loading">
        <p>没有相应的技能</p>
        <p>
          这里的技能还是不能满足您的需求？您还可以
          <router-link :to="{ path: '/studio/skill' }"
            >自定义您的技能</router-link
          >
        </p>
      </div> -->
      <skills-empty v-if="skillsData.total === 0 && !skillsData.loading" />
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import skillsEmpty from './skillsEmpty.vue'

export default {
  name: 'store-skill-all',
  props: ['search', 'fixed', 'scrollTop', 'scrollTo'],
  data() {
    return {
      sortType: 0,
      businessType: '',
      icon: {
        efficiencyTool: 10100,
        lifeService: 10200,
        vedioRead: 10300,
        education: 10400,
        healthy: 10500,
        entertainment: 10600,
        childrenEducation: 10700,
        finance: 10800,
        smartHome: 10900,
        qa: 20100,
      },
      provider: '',
      skillsData: {
        loading: true,
        total: -1,
        page: 1,
        size: 6,
        list: [],
      },
      needSearch: false,
      categoryTypes: [],
      showCategorySearch: true,
      searchVal: '',
    }
  },
  watch: {
    $route: function (val, oldVal) {
      // this.handleSkillType(val.params.skillType)
    },
  },
  computed: {
    pageLayout() {
      if (this.skillsData.total / this.skillsData.size > 7) {
        return 'prev, pager, next, jumper'
      }
      return 'prev, pager, next'
    },
    showPagination() {
      return this.skillsData.total > this.skillsData.size
    },
  },
  created() {
    // 根据屏幕尺寸获取分页参数
    this.getAdaptPageSize()
    this.handleSkillType(this.$route.params.skillType)
    // 获取各种二级分类的类型
    this.getSecondCategoryTypes()
  },
  methods: {
    getAdaptPageSize() {
      // if (window.innerHeight < 600) {
      //   this.skillsData.size = 9
      // } else {
      //   this.skillsData.size = 12
      // }
      this.skillsData.size = 18
    },
    searchSkill() {
      this.sortType = 1
      this.businessType = '0'
      this.provider = ''
      this.needSearch = true
      this.showCategorySearch = true

      this.getSkills(1)
    },
    handleSkillType(type) {
      console.log(type)
      this.sortType = 1
      this.businessType = '0'
      this.provider = ''
      this.needSearch = true
      this.showCategorySearch = true
      const infoObj = JSON.parse(
        sessionStorage.getItem('GLOBAL_SKILL_STORE_NEW') || '{}'
      )
      if (infoObj.businessType) {
        this.businessType = infoObj.businessType
      }
      if (infoObj.page) {
        this.skillsData.page = infoObj.page
        sessionStorage.removeItem('GLOBAL_SKILL_STORE_NEW')
      }
      this.getSkills()
    },
    getSkills(page) {
      console.log('in getSkills, this.businessType', this.businessType)
      this.skillsData.loading = true
      this.$utils.httpGet(
        this.$config.api.AIUI_STORE_SKILLS,
        {
          sortType: this.sortType,
          businessType: this.businessType === '0' ? '' : this.businessType,
          search: this.needSearch ? this.searchVal : '',
          provider: this.provider,
          pageIndex: page || this.skillsData.page,
          pageSize: this.skillsData.size,
        },
        {
          success: (res) => {
            this.skillsData.list = res.data.skills
            this.skillsData.total = res.data.count
            this.skillsData.page = res.data.pageIndex
            // this.skillsData.size = res.data.pageSize
            this.skillsData.loading = false
            this.$nextTick(() => {
              this.restoreScrollTop()
            })
          },
          error: (err) => {
            console.log('page=>>')
            console.log(err)
          },
        }
      )
    },
    restoreScrollTop() {
      let top = sessionStorage.getItem('GLOBAL_SKILL_STORE_NEW_SCROLLTOP') || 0
      if (top) {
        this.scrollTo(Number(top))
        sessionStorage.removeItem('GLOBAL_SKILL_STORE_NEW_SCROLLTOP')
      }
    },
    toSkillDetail(skill) {
      // 保存当前页码等信息，便于返回时重新获取
      const infoObj = {
        businessType: this.businessType,
        page: this.skillsData.page,
      }
      sessionStorage.setItem('GLOBAL_SKILL_STORE_NEW', JSON.stringify(infoObj))
      sessionStorage.setItem('GLOBAL_SKILL_STORE_NEW_SCROLLTOP', this.scrollTop)
      this.$router.push({
        name: 'store-skill',
        params: { skillId: skill.id },
        query: { type: skill.type },
      })
    },
    getSecondCategoryTypes() {
      const categoryTypes = [
        { label: '通用', value: '0' },
        { label: '机器人', value: '20100' },
        { label: '回答', value: '20100' },
        { label: '国产', value: '20100' },
      ]
      this.categoryTypes = categoryTypes
    },
    handleCategoryClick() {
      this.getSkills(1)
    },
  },
  components: {
    skillsEmpty,
  },
}
</script>

<style lang="scss" scoped>
@import '@A/scss/skills.scss';
.store-skills-row {
  padding-top: 15px;
}

.store-desc {
  font-size: 14px;
  padding: 30px 30px 0px 30px;
}
</style>
