<template>
  <div class="os-divider"></div>
</template>

<script>
export default {
  name: '<PERSON>sDivider',
  props: {
    text: {
      type: String,
      default: ""
    }
  },
  data () {
    return {

    }
  },
  computed: {

  },
  mounted() {

  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="scss" scoped>
.os-divider {
  font-size: 14px;
  line-height: 1.5;
  color: rgba(0,0,0,.65);
  box-sizing: border-box;
  padding: 0;
  list-style: none;
  background: #ebeef5;
  vertical-align: middle;
  position: relative;
  top: -.06em;
  display: block;
  height: 1px;
  width: 100%;
  clear: both;
  &-with-text {
    display: table;
    white-space: nowrap;
    text-align: center;
    background: transparent;
    color: $grey5;
    font-size: 14px;
    &:after, &:before {
      content: "";
      display: table-cell;
      position: relative;
      top: 50%;
      width: 50%;
      border-top: 1px solid #ebeef5;
      transform: translateY(50%);
    }
  }
  &-inner-text {
    display: inline-block;
    padding: 0 24px;
  }
}
</style>
