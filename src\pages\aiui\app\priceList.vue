<template>
  <el-dialog
    :class="['price-list-wrap', {'turing-price-list-wrap': dialog.goodType == 'turing'}]"
    :visible.sync="dialog.show">
    <template slot="title">
      <p class="dialog-title">套餐购买</p>
      <span class="tip" v-if="dialog.goodType == 'webApi'">超出每日免费交互次数的调用，将从流量包中扣除。</span>
      <span class="tip" v-if="dialog.goodType == 'turing'">
        AIUI 为每个 APPID 每天提供 500 次图灵科技信源免费测试额度，您可以根据使用情况购买图灵科技流量包，有效期一年。</span>
    </template>
    <div class="dialog-body">
      <!-- webAPI -->
      <div class="item-wrap" v-if="dialog.goodType == 'webApi'" v-for="(item, index) in webApi" :key="index">
        <p class="item-type">{{ item.type }}</p>
        <p class="item-unit-price">
          {{ item.unitPrice }}<span class="unit">/万次</span>
        </p>
        <p class="item-count">{{ item.count }}次</p>
        <p class="item-annual-price">{{ item.annualPrice }}/年</p>
        <el-button type="primary" size="small" @click="buyWebApi(item.packageId)">立即购买</el-button>
        <p class="item-payment-standard">按年付费</p>
      </div>

      <!-- 图灵 -->
      <div class="item-wrap" v-if="dialog.goodType == 'turing'" v-for="(item, index) in turing" :key="index">
        <p class="item-type">{{ item.type }}</p>
        <p class="item-unit-price">
          {{ item.unitPrice }}<span class="unit">/万次</span>
        </p>
        <p class="item-count">{{ item.count }}次</p>
        <p class="item-annual-price">{{ item.annualPrice }}/年</p>
        <el-button type="primary" size="small" @click="buyTuring(item.packageId)">立即购买</el-button>
        <p class="item-payment-standard">按年付费</p>
      </div>
    </div>
    <p class="tip" v-if="dialog.goodType == 'webApi'">流量包仅包含语音识别、语音语义、文本语义的接口调用费用，不包含收费信源。有效期一年。</p>
    <span slot="footer" class="dialog-footer">
      <!-- <el-button class="btn-cancel" @click="dialog.show = false">以后再买</el-button> -->
      <span class="btn-cancel" @click="dialog.show = false">以后再买</span>
    </span>
  </el-dialog>
</template>
<script>
  export default {
    name: 'price-tag-list',
    props: {
      dialog: {
        type: Object,
        default: {}
      }
    },
    data() {
      return {
        webApi: [
          {
            type: '初级包',
            unitPrice: '36.0元' ,
            count: '2000万',
            annualPrice: '7.2万',
            packageId: '1005002',
          },
          {
            type: '中级包',
            unitPrice: '33.6元' ,
            count: '5000万',
            annualPrice: '16.8万',
            packageId: '1005003',
          },
          {
            type: '高级包',
            unitPrice: '30.0元' ,
            count: '1亿',
            annualPrice: '30.0万',
            packageId: '1005004',
          }
        ],
        turing: [
          {
            type: '体验包',
            unitPrice: '30.0元' ,
            count: '1万',
            annualPrice: '30元',
            packageId: '1007004',
          },
          {
            type: '初级包',
            unitPrice: '27.5元' ,
            count: '20万',
            annualPrice: '550元',
            packageId: '1007001',
          },
          {
            type: '中级包',
            unitPrice: '25.0元' ,
            count: '200万',
            annualPrice: '5000元',
            packageId: '1007002',
          },
          {
            type: '高级包',
            unitPrice: '24.0元' ,
            count: '2000万',
            annualPrice: '4.8万',
            packageId: '1007003',
          }
        ]
      }
    },
    computed: {
      appId(){
        return this.$store.state.aiuiApp.app.appid
      },
      appName(){
        return this.$store.state.aiuiApp.app.appName
      },
    },
    methods: {
      buyTuring(packageId) {
				window.open(this.$config.xfyunConsole + "sale/buy?packageId="+packageId+"&wareId=1007&appId="+this.appId+
					"&appName="+this.appName+"&serviceName=图灵科技信源")
      },
      buyWebApi(packageId) {
        window.open(this.$config.xfyunConsole + "sale/buy?packageId="+packageId+"&wareId=1005&appId="+this.appId+
          "&appName="+this.appName+"&serviceName=AIUI_WebAPI")
      },
    }
  }
</script>
<style lang="scss" scoped>
  .dialog-title {
    margin-bottom: 8px;
    font-size: 24px;
    color: $semi-black;
  }
  .tip {
    color: $grey6;
  }
  .dialog-body {
    display: flex;
    margin-bottom: 12px;
    height: 380px;
  }
  .item-wrap {
    flex: 1;
    padding: 24px 28px;
    text-align: center;
    color: $semi-black;
    min-width: 216px;
    max-width: 216px;
    height: 340px;
    border: 1px solid $grey3;
    border-right-color: transparent;
    &:last-child {
      border-right-color: $grey3;
    }
    &:hover {
      transform: scale(1.1, 1.1);
      transition: all 0.5s;
      border: 1px solid $primary;
      background-color: $white;
    }
  }
  .item-type {
    margin-bottom: 16px;
    font-size: 18px;
    font-weight: 500;
  }
  .item-unit-price {
    margin-bottom: 30.5px;
    padding-bottom: 25px;
    font-size: 32px;
    font-weight: 500;
    letter-spacing: 1px;
    border-bottom: 1px dashed $grey3;
  }
  .unit {
    font-size: 18px;
  }
  .item-count {
    margin-bottom: 16px;
    font-size: 12px;
  }
  .item-annual-price {
    margin-bottom: 32.5px;
    font-size: 12px;
  }
  .item-payment-standard {
    margin-top: 16px;
    font-size: 12px;
    color: $grey6;
  }
  .btn-cancel {
    display: inline-block;
    margin-right: 12px;
    width: 48px;
    height: 24px;
    line-height: 24px;
    font-size: 12px;
    color: $grey6;
    cursor: pointer;
  }
</style>
<style lang="scss">
  .price-list-wrap {
    .el-dialog {
      width: 712px;
    }
    .el-dialog__footer {
      padding-top: 16px;
    }
  }
  .turing-price-list-wrap {
    .el-dialog {
      width: 928px;
    }
  }
</style>
