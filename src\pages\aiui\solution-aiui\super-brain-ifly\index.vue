<template>
  <div class="main_content">
    <section class="main_content_banner">
      <div class="banner_text_wrapper">
        <div class="banner_text">
          <div class="banner_text_title">讯飞超脑板</div>
          <div class="info">专门面向人工智能及机器人领域的嵌入式终端主板</div>
        </div>
        <div class="banner_text_button_wrap">
          <div class="button primary_button" @click="toConsole">合作咨询</div>
          <!-- <div class="button text_button" plain @click="toBuy">立即购买</div> -->
        </div>
      </div>
    </section>

    <div class="section section1">
      <div class="title section_title">应用场景</div>
      <div class="section_content">
        <div
          class="content_item"
          v-for="(item, index) in applicationScenarios"
          :key="index"
        >
          <img :src="item.imgSrc" :alt="item.title" />
          <div class="item_title">{{ item.title }}</div>
        </div>
      </div>
    </div>

    <div class="product_features_wrap">
      <div class="product_features">
        <h2 class="section_title">产品特点</h2>

        <div class="card_wrapper">
          <div class="model_card">
            <h3>超大内存 响应速度快</h3>
            <ul>
              <li>
                <img
                  src="@/assets/images/solution/super-brain-core/product-features-icon.png"
                  alt=""
                />
                <span>8GB LPDDR4内存</span>
              </li>
              <li>
                <img
                  src="@/assets/images/solution/super-brain-core/product-features-icon.png"
                  alt=""
                />
                <span>64GB EMMC存储</span>
              </li>
            </ul>
            <img
              src="@/assets/images/solution/super-brain-ifly/product-features-01.png"
              alt=""
            />
          </div>

          <div class="model_card">
            <h3>丰富接口 可扩展强</h3>
            <ul>
              <li>
                <img
                  src="@/assets/images/solution/super-brain-core/product-features-icon.png"
                  alt=""
                />
                <span>可接入多种外设资源</span>
              </li>
              <li>
                <img
                  src="@/assets/images/solution/super-brain-core/product-features-icon.png"
                  alt=""
                />
                <span>支持二次开发</span>
              </li>
            </ul>
            <img
              src="@/assets/images/solution/super-brain-ifly/product-features-02.png"
              alt=""
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 硬件接口展示 -->
    <div class="hardware_interface_wrap">
      <h2 class="section_title">接口说明</h2>
      <div class="interface_cards">
        <div class="interface_card">
          <div class="card_label">正面接口</div>
          <img
            style="width: 892px; height: 797px"
            src="@/assets/images/solution/super-brain-ifly/front-interface.png"
            alt="正面接口布局"
          />
        </div>
        <div class="interface_card">
          <div class="card_label">背面接口</div>
          <img
            style="width: 675px; height: 629px"
            src="@/assets/images/solution/super-brain-ifly/back-interface.png"
            alt="背面接口布局"
          />
        </div>
      </div>
    </div>

    <!-- 接入模式 -->
    <div class="access_mode_wrap">
      <div class="access_mode">
        <div class="access_mode_card">
          <h2>接入模式</h2>
          <div class="table_container">
            <el-table
              :data="accessModeList"
              style="width: 100%"
              :span-method="tableSpanMethod"
            >
              <el-table-column
                prop="type"
                label="类型"
                width="163"
              ></el-table-column>
              <el-table-column prop="parameter" label="参数"></el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </div>

    <!-- 合作咨询 -->
    <div class="cooperation_consulting_wrapper">
      <div class="banner-text">
        <h2>立即联系您的专属顾问</h2>
        <p class="banner_text_content">
          免费咨询专属顾问 为您量身定制产品推荐方案<br />
        </p>
        <div class="banner-text-button" @click="toConsole">合作咨询</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AiuiWebChildToys',

  data() {
    return {
      applicationScenarios: [
        {
          title: '智慧屏',
          imgSrc: require('@/assets/images/solution/super-brain-ifly/application-scenarios-01.png'),
        },
        {
          title: '自助终端机',
          imgSrc: require('@/assets/images/solution/super-brain-ifly/application-scenarios-02.png'),
        },
        {
          title: '机器人',
          imgSrc: require('@/assets/images/solution/super-brain-ifly/application-scenarios-03.png'),
        },
      ],

      accessModeList: [
        {
          type: '板卡尺寸',
          parameter: '150mm x 110mm',
        },
        {
          type: 'SOC',
          parameter: 'RockChip RK3588S',
        },
        {
          type: 'CPU',
          parameter:
            '八核 64 位（4 × Cortex-A76 + 4 × Cortex-A55），主频高达2.4GHz',
        },
        {
          type: 'GPU',
          parameter: '支持 OpenGL ES3.2 / OpenCL 2.2 / Vulkan1.1, 450 GFLOPS',
        },
        {
          type: 'GPU',
          parameter: 'ARM Mali-G610 MP4 四核 GPU',
        },
        {
          type: 'NPU',
          parameter: 'NPU 算力高达 6 TOPS，支持 INT4/INT8/INT16 混合运算',
        },
        {
          type: 'NPU',
          parameter:
            '可实现基于 TensorFlow / MXNet / PyTorch / Caﬀe 等系列框架的网络模型转换',
        },
        {
          type: 'ISP',
          parameter:
            '集成最大 48MP ISP，集成HDR、3A、LSC、3DNR、2DNR、锐化、去噪、鱼眼校正、伽马校正等算法',
        },
        {
          type: '操作系统',
          parameter: 'Linux：Ubuntu Desktop、Debian11',
        },
        {
          type: '内存/存储',
          parameter: '8GB LPDDR4/ 64GB eMMC',
        },
        {
          type: '视频输出',
          parameter:
            '1 × HDMI2.0（7680 x 4320@60Hz或2160p@60Hz 或 1080p@120Hz）',
        },
        {
          type: '视频输出',
          parameter: '1 × DP1.4（8192 x 4320@30Hz）（USB3.1 Gen1接口）',
        },
        {
          type: '视频输出',
          parameter: '1 × LVDS（单/双通道，最大背光不超过12V@1A）',
        },
        {
          type: '视频输出',
          parameter: '1 × eDP（4k@60Hz）',
        },
        {
          type: '视频输出',
          parameter: '最高可以实现双屏异显',
        },
        {
          type: '视频输出',
          parameter: 'LVDS、eDP支持显示触摸（VCC-3.3V、I2C、INT、RST）',
        },
        {
          type: 'MIPI摄像头',
          parameter: '2 × 2 lane MIPI-CSI 输入',
        },
        {
          type: '视频解码',
          parameter: '8K@60fps H.265/VP9/AVS2',
        },
        {
          type: '视频解码',
          parameter: '8K@30fps H.264 AVC/MVC',
        },
        {
          type: '视频解码',
          parameter: '4K@60fps AV1',
        },
        {
          type: '视频解码',
          parameter: '1080P@60fps MPEG-2/-1/VC-1/VP8',
        },
        {
          type: '视频编码',
          parameter: '8K@30fps 编码，支持 H.265/H.264',
        },
        {
          type: '视频编码',
          parameter: '最高可实现32路1080P@30fps解码和 16 路1080P',
        },

        {
          type: '音频输入',
          parameter: '1 × UAC输入（USB接口）',
        },
        {
          type: '音频输出',
          parameter: '1 × HDMI2.0音频输出',
        },
        {
          type: '音频输出',
          parameter: '1 × DP1.4音频输出（USB3.1 Gen1接口）',
        },
        {
          type: '音频输出',
          parameter:
            '1 × 双通道扬声器输出（4P-2.0mm，支持8Ω喇叭，单喇叭功率≥10w）',
        },
        {
          type: '音频输出',
          parameter: '1 × 音频回采输出（4P-1.25mm）',
        },
        {
          type: 'USB接口',
          parameter: '4 × USB3.0（限流 1A）',
        },
        {
          type: 'USB接口',
          parameter: '1 × USB3.1 Gen1接口（USB3.0 OTG/DP1.4）（限流 2A）',
        },
      ],
      hardwareFeatures: [
        {
          parameter: '资源接口',
          classification: '核心板引出全部外设资源',
          specification:
            'I2S*2，PCIE2.0*2，USB2.0 HOST*2，USB3.0*1，SDIO*1，SPI*2，RGMII*1，I2C*4，UART*7，MIPI*6',
        },
        {
          parameter: '系统参数',
          classification: '编解码',
          specification:
            '视频解码：8K@60fps H.265/VP9/AVS28K@30fps H.264 AVC/MVC4K@60fps AV11080P@60fps MPEG-2/-1/VC-1/VP8 视频编码：8K@30fps编码，支持H.265 / H.264*最高可实现32路',
        },
      ],
      headerStyle: {
        background: 'linear-gradient(85deg,#569cfe 0%, #227eff 100%)',
        color: '#fff',
        height: '60px',
        fontSize: '16px',
        fontWeight: 'bold',
      },
    }
  },

  mounted() {},

  methods: {
    toConsole() {
      // 判断是否有百度SEM营销参数，有的话，提交表单页需要带上
      const search = window.location.search
      if (search) {
        window.open(`/solution/apply/49${search}`)
      } else {
        window.open('/solution/apply/49')
      }
    },

    toBuy() {
      window.open('https://www.aifuwus.com/onstage/cmddetail?id=4434')
    },
    tableSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        const type = row.type
        const data = this.accessModeList
        // 找到当前行是第几个 type 的第一个
        let firstRowIndex = rowIndex
        while (firstRowIndex > 0 && data[firstRowIndex - 1].type === type) {
          firstRowIndex--
        }
        // 只在第一个 type 行返回 rowspan
        if (firstRowIndex === rowIndex) {
          // 统计有多少个连续的 type
          let rowspan = 1
          for (let i = rowIndex + 1; i < data.length; i++) {
            if (data[i].type === type) {
              rowspan++
            } else {
              break
            }
          }
          return { rowspan, colspan: 1 }
        } else {
          return { rowspan: 0, colspan: 0 }
        }
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.main_content {
  background-color: #fff;
  .main_content_banner {
    background: url(~@A/images/solution/super-brain-ifly/banner.jpg) center
      no-repeat;
    background-size: cover;
    height: 501px;
    overflow: hidden;
    width: 100%;
  }

  .banner_text_wrapper {
    max-width: 1200px;
    color: #fff;
    height: 100%;
    margin: auto;

    .banner_text {
      width: 500px;
      margin: 97px 0 65px 0;
      color: #000000;
      .banner_text_title {
        font-size: 44px;
        letter-spacing: 1px;
        font-weight: 600;
      }
      .info {
        font-size: 18px;
        width: 552px;
        height: 100px;
        line-height: 100px;
        transform: translateX(-66px);
        padding-left: 66px;
        backdrop-filter: blur(1px);
        border-radius: 50px;
      }
    }
    .banner_text_button_wrap {
      display: flex;
      gap: 30px;
      .button {
        font-size: 18px;
        text-align: center;
        font-weight: 400;
        width: 183px;
        height: 60px;
        line-height: 60px;
        border-radius: 8px;

        cursor: pointer;
        letter-spacing: 1px;
        &.primary_button {
          background: #1d69ff;
          color: #fff;
        }
        &.text_button {
          border: 1.5px solid #1d69ff;
          color: #1d69ff;
          font-weight: 600;
        }
      }
    }
  }

  .section_title {
    font-size: 34px;
    color: #000000;
  }
  .section {
    padding: 60px 0 90px;
    text-align: center;
    .title {
      margin-bottom: 50px;
    }
    .section_content {
      display: flex;
      justify-content: center;
      gap: 30px;
      flex-wrap: wrap;
    }
    .content_item {
      position: relative;
      img {
        width: 380px;
        height: 421x;
        object-fit: cover;
        display: block;
      }

      .item_title {
        position: absolute;
        top: 10%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 100%;
        font-size: 22px;
        color: #000000;
      }
    }
  }

  .product_features_wrap {
    text-align: center;
    background-color: #e6edf6;

    .product_features {
      background: url(~@A/images/solution/super-brain-core/product-features-bg.png)
        center no-repeat;
      background-size: cover;
      padding-bottom: 92.5px;
      h2 {
        padding: 50px 0 75px 0;
      }
      .card_wrapper {
        display: flex;
        justify-content: center;
        gap: 20px;
        .model_card {
          background: linear-gradient(180deg, #e7ecf2, #ffffff 100%);
          border: 2px solid #ffffff;
          border-radius: 20px;
          box-shadow: 0px 50px 100px 0px rgba(16, 38, 93, 0.2);
          padding: 50px 43px 55px 43px;
          width: 590px;

          h3 {
            font-weight: 600;
            margin-bottom: 30px;
            font-size: 28px;
            font-family: PingFang SC, PingFang SC-600;
            color: #1d1d1d;
          }

          ul {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin: 30px 0 60px 0;
            li {
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 20px;
              font-family: Alibaba PuHuiTi, Alibaba PuHuiTi-400;
              color: #2a2a2a;
              line-height: 22px;
              img {
                width: 20px;
                height: 21px;
                margin-right: 8px;
              }
            }
          }
          img {
            width: 494px;
            height: 255px;
          }
        }
      }
    }
  }

  .hardware_interface_wrap {
    text-align: center;
    padding-bottom: 140px;
    h2 {
      padding: 130px 0 87px 0;
    }

    .interface_cards {
      display: flex;
      flex-direction: column;
      gap: 70px;
      align-items: center;
    }

    .interface_card {
      position: relative;
      width: 1200px;
      padding: 30px;
      background: linear-gradient(180deg, #eceef2, #ffffff 100%);
      border: 2px solid #ffffff;
      border-radius: 20px;
      box-shadow: 0px 50px 100px 0px rgba(107, 151, 187, 0.25);

      .card_label {
        position: absolute;
        top: -1px;
        left: -1px;
        width: 152px;
        height: 50px;
        background: linear-gradient(261deg, #9fdff2 0%, #0c9afa 100%);
        border-radius: 20px 0px 20px 0px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 18px;
      }

      img {
        display: block;
        margin: 0 auto;
      }
    }
  }

  .access_mode_wrap {
    text-align: center;
    background-color: #e6edf6;
    .access_mode {
      padding-bottom: 128px;
      background: url(~@A/images/solution/super-brain-ifly/access-mode.png)
        center no-repeat;
      background-size: cover;

      .access_mode_card {
        width: 1200px;
        margin: auto;
        border-radius: 20px;

        h2 {
          text-align: center;
          padding: 50px 0 50px 0;
          font-size: 52px;
          font-family: Alibaba PuHuiTi, Alibaba PuHuiTi-500;
          font-weight: 500;
          color: #1e1e1e;
        }

        .table_container {
          :deep(.el-table) {
            overflow: hidden;
            border-radius: 20px;
            border: 1px solid #d7dfee;

            .el-table__header-wrapper {
              height: 60px !important;
              table {
                thead {
                  tr {
                    background: linear-gradient(0deg, #569cfe 0%, #227eff 100%);
                    th {
                      background: none;
                      padding: 19px 0;
                      .cell {
                        font-size: 20px;
                        color: #ffffff;
                        padding: 0 40px;
                      }
                      &:first-child {
                        border-right: 1px solid #d7dfee;
                      }
                    }
                  }
                }
              }
            }

            .el-table__body-wrapper {
              .el-table__body {
                tbody {
                  tr {
                    pointer-events: none !important;
                    td {
                      pointer-events: none !important;
                      border-right: 1px solid #d7dfee;
                      border-color: #d7dfee;
                      .cell {
                        font-size: 18px;
                        color: #36363b;
                        padding: 0 30px;
                        line-height: 30px;
                      }
                      &:first-child:not(.el-table_1_column_2) {
                        background-color: #f5f9ff; // 只有第一列背景色
                      }
                      &:last-child {
                        border-right: none;
                        .cell {
                          padding: 0 20px;
                        }
                      }
                    }
                    &:last-child {
                      td {
                        border-bottom: none;
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

.cooperation_consulting_wrapper {
  background: url(~@A/images/solution/child-education/img_guwenBG.png) center
    no-repeat;
  background-size: cover;
  height: 300px;
  overflow: hidden;
  width: 100%;
  .banner-text {
    max-width: 1200px;
    color: #fff;
    height: 100%;
    margin: auto;
    &-button {
      font-size: 16px;
      text-align: center;
      font-weight: 400;
      width: 140px;
      height: 40px;
      line-height: 40px;
      background: #1d69ff;
      border-radius: 4px;
      color: #fff;
      cursor: pointer;
    }
    h2 {
      color: #181818;
      padding-top: 50px;
      margin-bottom: 20px;
      font-size: 36px;
      font-weight: 400;
      line-height: 48px;
    }
    p {
      font-size: 18px;
      margin-bottom: 50px;
    }

    .banner_text_content {
      width: 570px;
      font-size: 16px;
      font-family: SourceHanSansSC-Regular, SourceHanSansSC;
      font-weight: 400;
      color: #444444;
      line-height: 30px;
    }
  }
}
</style>
