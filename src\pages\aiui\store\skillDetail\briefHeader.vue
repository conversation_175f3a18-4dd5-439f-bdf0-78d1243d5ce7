<template>
  <div class="store-skill-detail">
    <i class="iconfont icon-fanhui icon-back" @click="back" />
    <div class="store-skill-detail-thumb">
      <img v-if="skill.detail.url" :src="skill.detail.url" />
      <span v-else>{{
        skill.detail.zhName && skill.detail.zhName.substr(0, 1)
      }}</span>
    </div>
    <div class="store-skill-detail-content">
      <p class="store-skill-detail-name">
        <span>{{ skill.detail.zhName }}</span>
        <span>{{ skill.detail.name }}</span>
        <!-- <a
          v-if="type == 7"
          target="_blank"
          style="margin-left: 10px"
          :href="`${$config.docs}aiui/2_app_setting/access_app.html#兜底`"
        >
          <el-tooltip placement="bottom">
            <div slot="content">
              当前技能为兜底技能，相比于普通技能优先级较低，<br />点击阅读文档介绍。
            </div>
            <el-button size="small">兜底技能</el-button>
          </el-tooltip>
        </a> -->
      </p>
      <div class="store-skill-detail-tags">
        <!-- <div
          class="store-skill-detail-tag"
          v-if="skill.openSkill && skill.aliasName instanceof Array"
        >
          <i class="iconfont icon-bieming"></i
          ><span>{{ skill.aliasName.join(',') }}</span>
        </div> -->
        <div class="store-skill-detail-tag" v-if="!!skill.detail.mc">
          <!-- <img :src="require('@A/svg/store/category.svg')" /><span>{{
            skill.detail.mc
          }}</span> -->
          <i class="iconfont icon-fenlei head-icon"></i
          ><span>{{ skill.detail.mc }}</span>
        </div>
        <div class="store-skill-detail-tag">
          <i class="iconfont icon-yonghu head-icon"></i
          ><span>{{ skill.detail.provider || '科大讯飞' }}</span>
        </div>

        <div class="store-skill-detail-tag" v-if="type != 7">
          <i class="iconfont icon-yingyong head-icon"></i
          ><span>{{ skill.detail.count }}个应用使用</span>
        </div>
        <div class="store-skill-detail-tag" v-if="skill.openSkill">
          <i class="iconfont icon-fengbijineng head-icon"></i
          ><span>封闭技能</span>
        </div>
        <div class="store-skill-detail-tag" v-if="type == 3">
          <i class="iconfont icon-fangyanjineng head-icon"></i
          ><span>{{ skill.dialect && skill.dialect.dialectinfo }}</span>
        </div>
        <div class="store-skill-detail-tag" v-if="type == 7">
          <i class="iconfont icon-doudijineng head-icon"></i
          ><span>兜底技能</span>
        </div>
      </div>
      <div>
        <el-button type="primary" @click="addToApp">使用到应用中</el-button>
      </div>
    </div>
    <add-to-app-dialog :dialog="dialog" />
  </div>
</template>
<script>
import AddToAppDialog from '../dialog/addToApp'
import isLogin from '@U/isLogin'
import { utils } from '@U/index'

export default {
  components: { AddToAppDialog },
  data() {
    return {
      dialog: {
        show: false,
        data: {},
      },
    }
  },
  props: {
    skill: {
      type: Object,
      default: {
        detail: {},
      },
    },
    id: String | Number,
    type: String | Number,
  },
  methods: {
    back() {
      this.$router.back()
    },
    addToApp() {
      const login = isLogin()
      if (login) {
        if (this.type == 7) {
          this.$alert(
            '该技能为兜底技能，如需使用，请至应用管理页面添加',
            '提示',
            {
              type: 'info',
              confirmButtonText: '确定',
            }
          )

          return
        }
        this.dialog.show = true
        this.dialog.data = {
          skillId: this.id,
          name: this.skill.detail.name,
          operation: 'open',
        }
      } else {
        let fromUrl = encodeURIComponent(window.location.href)
        let loginPath = `${location.origin}/user/login?pageFrom=${fromUrl}`
        utils.toPage(loginPath)
      }
    },
  },
}
</script>
<style lang="scss" scoped>
.icon-back {
  font-size: 28px;
  margin-right: 30px;
  cursor: pointer;
  color: #999;
}
.store-skill-detail-tag {
  .icon-ico_fangyan {
    font-size: 20px !important;
    margin-right: 0 !important;
    color: #1b9adc;
  }
  .icon-yonghu {
    color: #a29eef;
  }
  .icon-yingyong {
    color: #fab67d;
  }
  .icon-fengbijineng {
    color: #9ae1d1;
  }
  .icon-fangyanjineng {
    color: #89bbf3;
  }
  .icon-doudijineng {
    color: #9ae1d1;
  }
  .icon-fenlei {
    color: #83d97c;
  }
  .head-icon {
    font-size: 20px;
  }
}
</style>
