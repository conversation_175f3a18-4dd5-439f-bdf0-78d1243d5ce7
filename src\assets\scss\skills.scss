p {
  margin-bottom: 0;
}
.content-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}
.store-skills-row {
  width: 100%;
  height: 100%;
  padding-left: 48px;
  padding-bottom: 20px;
  // overflow: auto;
}
.store-skills-col {
  width: 29%;
  margin: 30px 15px 0;
}
.store-skill-page {
  display: block;
  align-items: center;
  flex: auto;
  // position: fixed;
  // bottom: 2px;
  // left: 50%;
  // transform: translateX(-50%);
}
.os-store-skill {
  min-width: 330px;
  background-color: #fff;
  height: 102px;
  border-radius: 2px;
  position: relative;
  padding: 16px;
  cursor: pointer;
  overflow: hidden;
  box-sizing: border-box;
  box-shadow: 0px 5px 20px 0px rgba(165, 165, 165, 0.15);
  &:hover {
    box-shadow: 0px 5px 20px 0px rgba(165, 165, 165, 0.3);
  }
  &-thumb {
    border-radius: 6px;
    width: 70px;
    height: 70px;
    float: left;
    // line-height: 70px;
    overflow: hidden;
    text-align: center;
    font-size: 20px;
    border: 1px solid $grey2;
    display: flex;
    align-items: center;
    justify-content: center;

    img {
      width: 100%;
      height: 100%;
    }
  }
  &-content {
    float: left;
    margin-left: 16px;
  }
  &-name {
    font-size: 18px;
    color: $semi-black;
    font-weight: 500;
    margin-bottom: 2px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 200px;
  }
  &-provider {
    font-size: 14px;
    margin-bottom: 2px;
    color: $grey5;
  }
  &-desc {
    font-size: 14px;
    color: $grey5;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 200px;
  }

  &-tag-area {
    position: absolute;
    top: 8px;
    right: 16px;
  }
  .tag-area-bottom {
    top: 36px;
  }
  &-tag {
    font-size: 12px;
    color: $grey5;
    background-color: $grey4-15;
    border-radius: 12px;
    // width: 66px;
    padding: 0 8px;
    text-align: center;
    height: 20px;
    line-height: 20px;
    display: inline-block;
    &.blue {
      background: #89bbf3;
      color: #fff;
    }
  }

  &-dialect-tag {
    font-size: 12px;
    color: #fff;
    background-color: $success;
    border-radius: 12px;
    padding: 0 10px;
    height: 20px;
    line-height: 20px;
  }
}
.empty-skill-tip {
  text-align: center;
  line-height: 50px;
}

.top-area {
  height: 80px;
  padding: 0 35px;
  border-bottom: 1px solid #eff1f1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  :deep(.el-tabs__header) {
    margin: 0;
  }
  :deep(.el-tabs__item) {
    padding: 0 50px;
    font-size: 18px;
    height: 80px;
    line-height: 80px;
    color: $grey001;
    &:hover,
    &.is-active {
      color: $primary;
    }
  }
  :deep(.el-tabs__nav-wrap::after) {
    display: none;
  }
}

.store-desc {
  font-size: 16px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #999999;
  padding: 30px 0 0 30px;
}
.os-scroll-bottom {
  // padding-bottom: 50px;
  height: 100%;
}
.empty-skill-tip {
  padding-top: 50px;
}

// 技能商店适配不同屏幕
@media screen and (max-height: 801px) {
  .store-skills-row {
    padding-left: 24px;
    padding-bottom: 2px;
  }
  .store-skills-col {
    margin: 12px 10px 0;
  }

  .os-store-skill {
    height: 80px;
    border-radius: 2px;
    position: relative;
    padding: 14px;

    box-shadow: 0px 4px 10px 0px rgba(165, 165, 165, 0.15);
    &:hover {
      box-shadow: 0px 4px 10px 0px rgba(165, 165, 165, 0.3);
    }

    &-thumb {
      border-radius: 6px;
      width: 54px;
      height: 54px;
    }

    &-name {
      font-size: 14px;
      margin-bottom: 2px;
      max-width: 160px;
    }
    &-provider {
      font-size: 12px;
    }
    &-desc {
      font-size: 12px;
    }

    &-content {
      margin-top: -4px;
    }
  }

  .top-area {
    height: 54px;
    padding: 0 23px;
    :deep(.el-tabs__item) {
      padding: 0 35px;
      font-size: 12px;
      height: 54px;
      line-height: 54px;
    }
  }

  .store-desc {
    font-size: 16px;
    font-family: Microsoft YaHei;
    font-weight: 400;
    color: #999999;
    padding: 30px 0 0 30px;
  }
}

@media screen and (max-width: 1401px) {
  .os-store-skill {
    &-name {
      max-width: 120px;
    }
  }
}

.top-area-fixed {
  position: fixed;
  top: 60px;
  z-index: 10;
  left: 234px;
  right: 0;
  background: #fff;
}

// @media screen and (max-width: 1601px) {
//   .top-area-fixed {
//     top: 40px;
//   }
// }
@media screen and (max-height: 801px) {
  .top-area-fixed {
    left: 160px;
  }
}
