import Vue from 'vue'
import Vuex from 'vuex'

import metaModule from './modules/metaModule.js'
import user from './modules/user.js'
import studioSkill from './modules/studioSkill.js'
import studioCharacter from './modules/studioCharacter.js'
import studioQa from './modules/studioQa'
import aiuiApp from './modules/aiuiApp.js'
import aiuiStore from './modules/aiuiStore.js'
import commonStore from './modules/commonStore.js'
import pluginKnowledge from './modules/pluginKnowledge'
Vue.use(Vuex)

export default new Vuex.Store({
  // rootState
  state: {},
  mutations: {},
  modules: {
    metaModule,
    user,
    studioSkill,
    studioCharacter,
    studioQa,
    aiuiApp,
    aiuiStore,
    commonStore,
    pluginKnowledge,
  },
  // 严格模式
  strict: true,
  // 插件
  // plugins: debug ? [createLogger()] : []
})
