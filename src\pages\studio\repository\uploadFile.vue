<template>
  <el-upload
    class="repo__upload-file"
    :action="action"
    :show-file-list="false"
    :before-upload="beforeUpload"
    :on-success="success"
    :on-error="uploadError">
    <el-button size="small">
      {{options.text}}</el-button>
  </el-upload>
</template>
<script>
import { mapGetters } from 'vuex'
export default {
  props: {
    repoId:'',
    themeId:  [String, Number],
    api: String,
    options: {
      isCover: {
        type: Number,
        default: 1
      },
      text: String,
    }
  },
  data() {
    return {
      baseUrl: this.$config.server
    }
  },
  computed: {
    ...mapGetters({
      limitCount: 'aiuiApp/limitCount'
    }),
    action() {
      let tmp = `${this.baseUrl}/aiui/web/kbqa/excel/${this.api}?repositoryId=${this.repoId}&operation=${this.options.isCover}`
      if(this.themeId) {
       return `${tmp}&themeId=${this.themeId}`
      } else {
        return tmp
      }
    }
  },
  computed: {
    ...mapGetters({
      limitCount: 'aiuiApp/limitCount'
    }),
    action() {
      let tmp = `${this.baseUrl}/aiui/web/kbqa/excel/${this.api}?repositoryId=${this.repoId}&operation=${this.options.isCover}`
      if(this.themeId) {
       return `${tmp}&themeId=${this.themeId}`
      } else {
        return tmp
      }
    }
  },
  methods: {
    beforeUpload(file){
      let reg =/\.xls(x)?$/i
      let type = reg.test(file.name)
      let unExceed = file.size <  1024 * 1024 * this.limitCount['qa_file_size']
      if(!type){
        this.$message.error('仅支持xls或xlsx文件')
      }
      if(!unExceed){
        this.$message.error(`文件不能超过${this.limitCount['qa_file_size']}M`)
      }
      this.$emit('setLoad', type && unExceed)
      return type && unExceed
    },
    success(data){
      if(data.flag){
        this.$emit('setLoad', false)
        this.$emit('getData')
        this.$message.success('上传成功')
      } else {
        let failedTip
        if(data.data && data.data.length) {
          failedTip = JSON.stringify(data.data)
          this.$emit('setLoad', false, failedTip)
        } else {
          this.$message.error(data.desc || '上传失败')
        }
        this.$emit('setLoad', false, failedTip)
      }
    },
    uploadError(){
      this.$emit('setLoad', false)
    }
  },
  components: {  }
}
</script>
<style lang="scss">
  .repo__upload-file {
    .el-button {
      padding: 0;
      min-width: unset;
      text-align: left;
      color: $grey6;
      border: none;
      background: transparent;
      &:hover {
        background-color: #e8f3fd;
        color: #459ded;
      }
    }
    .el-upload {
      text-align: left;
    }
  }
</style>