<template>
  <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
    <el-tab-pane label="本地文档" name="first"
      ><local-document></local-document
    ></el-tab-pane>
    <el-tab-pane label="在线文档" name="second">在线文档</el-tab-pane>
  </el-tabs>
</template>
<script>
import localDocument from './localDocument.vue'
export default {
  data() {
    return {
      activeName: 'first',
    }
  },
  methods: {
    handleClick() {},
  },
  components: { localDocument },
}
</script>
<style lang="scss" scoped></style>
