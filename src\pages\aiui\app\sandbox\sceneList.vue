<template>
  <div class="scene-list-wrap">
    <span class="scene-label">情景模式：</span>
    <el-select
      style="width: 200px;"
      size="medium"
      v-model="currentSceneName.name"
      placeholder="情景模式"
    >
      <el-option
        v-for="(item, index) in sceneList"
        :key="index"
        :label="item.sceneName"
        :value="item.sceneBoxName"
        @click.native="changeScene(item)"
      >
      </el-option>
    </el-select>
    <el-switch
      v-model="showDiff.show"
      active-text="只看不同"
      style="margin-left: 30px;"
      @change="setDisplay"
    ></el-switch>
  </div>
</template>
<script>
export default {
  name: 'app-sandbox-secen-list',
  props: {
    sceneList: {
      type: Array,
      default: () => ([])
    },
    showDiff: {
      default: () => ({show:false})
    },
    currentSceneName: {
      default: () => ({name:'main_box'})
    },
  },
  data() {
    return {
    }
  },
  methods: {
    changeScene(val) {
      this.$emit('setScene', val)
    },
    setDisplay() {
      this.$emit('setDisplay', this.showDiff)
    }
  }
}
</script>
<style lang="scss" scoped>
.scene-label {
  color: $grey6;
}
</style>
