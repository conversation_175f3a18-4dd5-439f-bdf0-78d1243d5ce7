<template>
  <div class="main-content">
    <section class="main-content-banner"></section>
    <section class="section section-1">
      <div class="content-text">
        <p class="pc-show">
          完成<a
            href="https://console.xfyun.cn/user/realnameauth"
            target="_blank"
            >实名认证</a
          >实名认证的新用户即可立即领取免费礼包。<a
            @click="showDialog"
            target="_blank"
          >
            详细规则>>></a
          >
        </p>
      </div>
    </section>
    <section class="section section-2">
      <ul>
        <li :class="{ 'gift-box-shadow': (boxShadow != null) & !boxShadow }">
          <div class="gift-enterprise">
            <h2>企业认证礼包</h2>
            <div class="gift-enterprise-div">
              <img
                class="gift-enterprise-div-img"
                src="../../../assets/images/aiui/novice/enterprise-gift.png"
              />
            </div>
            <div class="inter-account">
              <p class="account in-line">50万次</p>
              <p class="account-text in-line">交互量</p>
            </div>
            <p class="period">免费使用有效期1年</p>
            <div class="banner-text-button">
              <el-button
                class="btn-gft-collect"
                type="primary"
                @click="giftCollect($event)"
                >立即领取</el-button
              >
            </div>
          </div>
        </li>
        <li :class="{ 'gift-box-shadow': boxShadow != null && boxShadow }">
          <div class="gift-enterprise">
            <h2>个人认证礼包</h2>
            <div class="gift-enterprise-div">
              <img
                style="min-height: 172.323px"
                class="gift-enterprise-div-img"
                src="../../../assets/images/aiui/novice/personal-gift.png"
              />
            </div>
            <div class="inter-account">
              <p class="account in-line">5万次</p>
              <p class="account-text in-line">交互量</p>
            </div>
            <p class="period">免费使用有效期1年</p>
            <div class="banner-text-button">
              <el-button
                class="btn-gft-collect"
                type="primary"
                @click="giftCollect"
                >立即领取</el-button
              >
            </div>
          </div>
        </li>
      </ul>
    </section>
    <giftDetailRuleDialog :dialog="dialog"></giftDetailRuleDialog>
    <giftCollectDialog :dialog="collectDialog"></giftCollectDialog>
    <giftCheckDialog :dialog="checkDialog"></giftCheckDialog>
  </div>
</template>

<script>
import giftCollectDialog from '../dialog/giftCollectDialog'
import giftDetailRuleDialog from '../dialog/giftDetailRuleDialog'
import giftCheckDialog from '../dialog/giftCheckDialog'
import { mapGetters } from 'vuex'

export default {
  name: 'giftCollect',
  components: {
    giftCollectDialog,
    giftDetailRuleDialog,
    giftCheckDialog,
  },
  data: function () {
    return {
      dialog: {
        show: false,
      },
      collectDialog: {
        show: false,
      },
      checkDialog: {
        show: false,
        type: 2,
      },
    }
  },
  computed: {
    ...mapGetters({
      userInfo: 'user/userInfo',
    }),
    boxShadow() {
      if (this.userInfo && this.userInfo.isCertificated) {
        if (this.userInfo.type == 1) {
          // 个人
          return true
        } else {
          // 企业
          return false
        }
      }
    },
  },
  methods: {
    giftCollect(e) {
      let thiz = this
      this.checkCertificated().then((result) => {
        if (!result) {
          this.checkDialog.type = 1
          this.checkDialog.show = true
        } else {
          thiz.checkGiftCollectCondition().then(
            (resolve) => {
              if (resolve.flag) {
                if (resolve.data.check) {
                  this.collectDialog.show = true
                } else {
                  this.checkDialog.show = true
                  //this.$message.error("你不满足新用户免费套餐领取条件。")
                }
              } else {
                this.$message.error(resolve.desc)
              }
            },
            (reject) => {}
          )
        }
      })
    },
    showDialog() {
      this.dialog.show = true
    },
    checkCertificated() {
      return new Promise((resolve, reject) => {
        resolve(this.userInfo && this.userInfo.isCertificated)
      })
    },
    checkGiftCollectCondition() {
      return new Promise((resolve, reject) => {
        this.$utils.httpGet(
          this.$config.api.GIFT_COLLECT_CONDITION,
          {},
          {
            success: (res) => {
              resolve(res)
            },
            error: (err) => {
              this.$message.error(err)
            },
          }
        )
      })
    },
  },
}
</script>
<!--<style>
  .os-main{
    overflow: auto !important;
    overflow-x: hidden !important;
  }
</style>-->
<style scoped lang="scss">
.main-content-banner {
  background: url('../../../assets/images/aiui/novice/banner.jpg') center
    no-repeat;
  background-size: cover;
}

.main-content {
  width: 100%;
  height: auto;
  overflow: hidden;
  &-banner {
    height: 458px;
    overflow: hidden;
    width: 100%;
    background-size: cover;
  }

  .section {
    > ul {
      display: flex;
      width: 52.08%;
      margin: 0 auto;
      li:nth-last-of-type(2) {
        margin-right: 2.81%;
      }
      li {
        float: left;
        width: 24.69%;
        height: 40.33%;
        margin-top: 4.67%;
        margin-bottom: 20.4%;
        background: #ffffff;
        &.gift-box-shadow {
          box-shadow: 0px 0px 14px 0px rgba(51, 51, 51, 0.25);
        }
        border: 1px solid #eeeeee;
        align-items: center;
        flex: 1;
        img {
          width: 100%;
        }
        &:hover .app-text {
          display: flex;
        }
      }
    }
    .gift-enterprise {
      margin: 0 auto;
      height: 86.22%;
      width: 64.9%;
      position: relative;
      h2 {
        margin-top: 8.65%;
        width: 100%;
        text-align: center;
      }
      &-div {
        margin-top: 2.24%;
        margin-bottom: 3.84%;
        &-img {
          //background: url("../../../assets/images/aiui/novice/enterprise-gift.png") no-repeat;
          width: 100%;
          position: relative;
          vertical-align: middle;
        }
      }
      .inter-account {
        display: flex;
        justify-content: center;
        .in-line {
          float: left;
          text-align: center;
        }
      }
      .account {
        font-size: 22px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #ff0000;
        line-height: 21px;
      }
      .period {
        text-align: center;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #ff7e00;
        line-height: 21px;
      }
      .banner-text-button {
        font-size: 16px;
        width: 100%;
        display: block;
        left: revert;
        margin: 0 auto;
        margin-bottom: 5.13%;
        display: flex;
        justify-content: center;
        .btn-gft-collect {
          width: 64.06%;
          height: 9.97%;
          max-width: 64.06% !important;
          min-width: 9.97% !important;
          background: #1784e9;
          box-shadow: 0px 2px 4px 0px rgba(23, 132, 233, 0.2);
          border-radius: 34px;
        }
      }
    }
    .content-text {
      max-width: 1200px;
      color: #fff;
      height: 100%;
      margin: auto;
      padding-left: 10px;
      text-align: center;
      .pc-show {
        color: #333333;
        position: relative;
        margin-top: 5.34%;
      }
      h2 {
        padding-top: 40px;
        font-size: 28px;
        font-weight: 500;
        margin-bottom: 10px;
      }

      p {
        font-size: 14px;
        padding: 0 15px;
      }

      p:nth-of-type(4) {
        margin-bottom: 30px;
      }
    }
  }
}
</style>
