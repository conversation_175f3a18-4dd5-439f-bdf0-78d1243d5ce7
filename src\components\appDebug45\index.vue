<template>
  <div class="debug-wrap">
    <div class="debug-header">
      <div class="collapse-btn" @click="closeRightTest">
        <i class="iconfont icon-zhankai"></i>
        <span>{{ debugType === 'app' ? '模拟测试' : '知识体验' }}</span>
      </div>
    </div>
    <!-- <div class="clear" @click="cleanHistory">
      <i class="iconfont icon-shanchu"></i>
    </div> -->
    <div class="clear">
      <div @click="cleanHistory"><svg-icon iconClass="delete" /></div>
      <div
        class="divider"
        style="margin: 0 11px"
        v-if="showDeviceStatusEditWrap"
      ></div>
      <div
        class="simulate-params-btn"
        @click="deviceStatusSettingDialog.show = true"
        v-if="showDeviceStatusEditWrap"
      >
        <svg-icon iconClass="setting" />
        <span>模拟端侧传参</span>
      </div>
    </div>

    <div class="debug-dialog" ref="list">
      <div
        :class="['dialog dialog-' + item.type]"
        v-for="(item, index) in dialogList"
        :key="index"
      >
        <template v-if="item.type === 'answer'">
          <div class="msg-answer-item">
            <i class="robot-avatar"></i>
            <div class="ib message">
              <template v-if="item.data.answer">
                <div style="padding: 5px 15px">
                  <vue-markdown
                    :source="item.data[item.type]"
                    @rendered="onRendered"
                    :html="false"
                    ref="markdownRef"
                  ></vue-markdown>
                </div>

                <div class="view-button-group">
                  <a
                    class="view-button"
                    v-if="item.data.showJson"
                    @click="openJsonDialog(item.data.json)"
                    ><i class="iconfont icon-chakan"></i
                    ><span style="white-space: nowrap">&nbsp;查看JSON</span></a
                  >
                  <a
                    class="view-button"
                    v-if="item.data.showLink"
                    @click="openLinkDialog(item.data.linkJson)"
                    ><i class="icon-link"></i
                    ><span style="white-space: nowrap">&nbsp;溯源引用</span></a
                  >
                </div>
              </template>
              <template v-if="item.data.loading">
                <div style="padding: 5px 15px">
                  <span>正在处理</span>&nbsp;<inputing></inputing>
                </div>
              </template>
            </div>
          </div>
        </template>
        <div class="ib message" v-else>
          {{ item.data[item.type] }}
        </div>
      </div>
    </div>

    <div class="send-wrap">
      <el-input
        class="debug-input"
        :maxlength="120"
        v-model="question"
        size="medium"
        @keyup.enter.native="experience"
        @keyup.up.native="preQuestion"
        @keyup.down.native="nextQuestion"
        placeholder="输入文本，回车体验"
      ></el-input>
      <span
        :class="['debug-send', { 'debug-send-active': question }]"
        @click="experience"
      >
        <svg-icon iconClass="send" />&nbsp;发送</span
      >
    </div>

    <el-dialog
      class="debug-json-dialog"
      title="JSON"
      :visible.sync="showJson"
      width="50%"
    >
      <div class="request-json">
        <template>
          <i class="ic-r-copy" title="复制代码" @click="copyJson(resJson)"></i>
          <json-view class="json-wrap" :data="resJson"></json-view>
        </template>
      </div>
      <div class="dialog-bottom"></div>
    </el-dialog>

    <!-- 查看知识库溯源引用 -->
    <appDebug45LinkDialog :dialog="showLinkDialog" :json="resLinkJson" />

    <!-- sos应用体验时传递端状态引用 -->
    <deviceStatusSettingDialog
      :dialog="deviceStatusSettingDialog"
      :fields="formFields"
      :userId="currentUserId"
      :appId="currentAppId"
      :scene="currentSceneName"
      @updateFormFields="updateFormFields"
    />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import VueMarkdown from 'vue-markdown'
import '@/assets/lib/prism/prism.js'
import '@/assets/lib/prism/prism.css'
import Inputing from '@P/aiui/model-experience/Inputing.vue'
import appDebug45LinkDialog from './appDebug45LinkDialog.vue'
import deviceStatusSettingDialog from './deviceStatusSettingDialog/index.vue'
import { getDeviceStatus } from '@/utils/deviceStatusStorage'

export default {
  name: 'app-debug45',
  props: {
    debugType: {
      default: 'app',
      type: String,
    },
  },
  data() {
    return {
      submitting: false,
      rc4Answer: [
        '啊哦~~这个问题太难了，换个问题吧！',
        '不好意思，我好像没听懂。。。',
      ],
      rc4Index: 0,
      question: '',
      questionList: [],
      questionIndex: 0, //记录按上下键问题开始位置
      uid: this.$utils.experienceUid(),
      dialogList: [
        {
          type: 'answer',
          data: {
            logId: -1,
            answer:
              this.debugType === 'app'
                ? '你好，我是智能的小飞～'
                : '你好，我是智能的小飞～你可以点击构建，然后测试知识问答效果(^_-)～',
          },
        },
        // {
        //   type: 'question',
        //   data: {
        //     question:
        //       '合肥的天气',
        //   },
        // },
      ],
      //  chatList: [
      //   {
      //     people: 'ai',
      //     con: '你好，我是智能的小飞~',
      //     type: 'text',
      //     preset: true,
      //   },
      // ],
      showJson: false,
      resJson: {},

      showLink: false,
      showLinkDialog: {
        show: false,
      },
      resLinkJson: [],

      // 端状态
      deviceStatusSettingDialog: {
        show: false,
      },

      formFields: [
        {
          name: 'device_status',
          type: 'object',
          value: '',
          children: [
            {
              name: 'volume',
              type: 'string',
              value: '50',
              children: [],
              items: [],
              itemType: 'string',
            },
            {
              name: 'brightness',
              type: 'string',
              value: '80',
              children: [],
              items: [],
              itemType: 'string',
            },
            {
              name: 'playing_status',
              type: 'string',
              value: '暂停播放',
              children: [],
              items: [],
              itemType: 'string',
            },
          ],
          items: [],
          itemType: 'string',
        },
      ],
    }
  },

  watch: {
    rightTestOpen: {
      handler(nVal, oVal) {
        //nval改变后的新数据，oval改变前的旧数据
        if (this.debugType !== 'app') {
          if (nVal) {
            this.getDocInfo()
          } else {
            this.dialogList = this.dialogList.filter(
              (item) => !item.data.errorTip
            )
          }
        }
      },
      deep: true, // 深度监听
      immediate: true, //立即执行
    },

    submitting(val, oldVal) {
      if (!val && oldVal) {
        this.$nextTick(() => {
          Array.from(
            document.getElementsByClassName('inputing-cursor')
          ).forEach((item) => {
            item.remove()
          })
        })
      }
    },

    // 监听场景名称变化，重新加载设备状态数据
    '$route.query.sceneName'() {
      this.loadDeviceStatusFromStorage()
    },
  },

  mounted() {
    // 组件挂载后从本地存储加载设备状态数据
    this.loadDeviceStatusFromStorage()
  },

  updated() {
    if (this.$refs.list) {
      this.$refs.list.scrollTop = 100000
    }
  },
  computed: {
    ...mapGetters({
      rightTestOpen: 'studioSkill/rightTestOpen',
      knowledgeId: 'pluginKnowledge/knowledgeId',
      currentScene: 'aiuiApp/currentScene',
    }),

    showDeviceStatusEditWrap() {
      return this.currentScene.sos === true
    },

    // 获取当前用户ID
    currentUserId() {
      return this.$store.state.user.userInfo?.uid
    },

    // 获取当前应用ID
    currentAppId() {
      return this.$route.params.appId
    },

    // 获取当前场景名称
    currentSceneName() {
      return this.$route.query.sceneName
    },
  },
  methods: {
    // 知识点----注意！ 知识体验才有的东西，后面待与其他体验分开
    getDocInfo() {
      let data = {
        id: this.knowledgeId,
      }
      let self = this
      this.$utils.httpGet(this.$config.api.KNOWLEDGE_DOC_INFO, data, {
        success: (res) => {
          console.log('=======res', res)
          if (res.data.buildStatus == 2) {
            let ansRes = {
              answer:
                '您当前有知识点正在构建发布，请等待构建发布成功后再体验。',
              errorTip: true,
            }
            self.addDialog('answer', ansRes)
          } else if (res.data.buildStatus == 3) {
            let ansRes = {
              answer:
                '您当前存在构建失败的知识点及问题干预，可影响您的体验，请重新构建发布。',
              errorTip: true,
            }
            self.addDialog('answer', ansRes)
          } else if (res.data.buildStatus == 0 || res.data.status == 0) {
            let ansRes = {
              answer: '您当前有更新内容未构建发布，请先构建发布成功后再体验。',
              errorTip: true,
            }
            self.addDialog('answer', ansRes)
          }
        },
        error: (err) => {},
      })
    },

    onRendered() {
      this.$nextTick(() => {
        window.Prism && window.Prism.highlightAll()
      })
    },
    preQuestion() {
      if (this.questionIndex > 0) {
        this.questionIndex--
        this.question = this.questionList[this.questionIndex]
      } else {
        this.question = ''
        this.questionIndex = -1
      }
    },
    nextQuestion() {
      if (this.questionIndex < this.questionList.length) {
        this.questionIndex++
        this.question = this.questionList[this.questionIndex]
      } else {
        this.question = ''
        this.questionIndex = this.questionList.length
      }
    },
    addDialog(type, data) {
      this.dialogList.push({
        type: type,
        data: data,
        httpCode: null,
      })
    },
    // 关闭EventSource连接的方法
    closeEventSource() {
      if (this.eventSource) {
        this.eventSource.close()
        this.eventSource = null
      }
    },
    removeLoading() {
      this.dialogList = this.dialogList.filter(
        (item) => !(item.type === 'answer' && item.data.loading)
      )
    },
    experience() {
      if (this.submitting) {
        return this.$message.warning('请等机器人回复完再进行提问')
      }
      if (!this.question) return

      let self = this

      let finalResponse = ''
      let hasSemantic = false
      if (!('EventSource' in window)) {
        throw new Error('浏览器不支持sse')
      }

      if (this.question && this.question.length > 500) {
        return this.$message.warning('输入文本不能超过500字符')
      }

      this.addDialog('question', {
        question: this.question.trim(),
      })
      this.closeEventSource()

      let question = encodeURIComponent(this.question)
      let sceneName = this.$route.query.sceneName
        ? `${this.$route.query.sceneName}_box`
        : `main_box`
      const base = this.$store?.state?.user?.baseUrl || '/aiui/web'
      let eventSourceUrl =
        this.debugType === 'app'
          ? `${base}/user/chat?version=v50&expUid=${this.uid}&query=${question}&appid=${this.$route.params.appId}&sceneName=${sceneName}`
          : `${base}/user/chat?version=v50&expUid=${this.uid}&query=${question}&repoId=${this.$route.params.repoId}&studio=true`
      if (this.currentScene.sos === true && this.debugType === 'app') {
        let json = this.convertToJson(this.formFields)
        eventSourceUrl = `${eventSourceUrl}&cbmAgent=${encodeURIComponent(
          JSON.stringify(json)
        )}`
      }
      this.submitting = true
      this.question = ''

      // 正在思考中
      this.addDialog('answer', {
        loading: true,
      })

      this.eventSource = new EventSource(eventSourceUrl, {
        withCredentials: true,
      })
      this.eventSource.onopen = (event) => {
        console.log('event onopen')
      }

      this.eventSource.onerror = (event) => {
        console.log('onerror', event)
        this.submitting = false
        if (event.data) {
          let data = JSON.parse(event.data)
          if (data.desc) {
            this.$message.error(data.desc)
            this.removeLoading()
          }
        }
        this.eventSource.close()
      }

      this.eventSource.addEventListener(
        'result',
        (event) => {
          const result = JSON.parse(event.data || '{}')
          console.log(result)
          if (result.data.knowledge) {
            console.log(JSON.parse(result.data.knowledge))
          }
          // 处理每一条信息
          const data = result.data
          this.handleMessage(data)
        },
        false
      )
    },

    handleMessage(data) {
      // 根据finish 改变所有 showOpt状态
      // 过来了一条推送数据，先过滤loading,再判断往chatList数组里加回复数据,还是改已有的回复数据
      // 可以根据logId判断回复是否已经被插往 chatList中
      this.removeLoading()
      if (data.type === 'nlp') {
        const index = this.dialogList.findIndex(
          (item) => item.data.logId === data.logId
        )
        if (index === -1) {
          // 没找到第一次添加回复数据

          this.addDialog('answer', {
            answer: data.text,
            showJson: data.semantic,
            json: data.semantic ? JSON.parse(data.semantic) : {},
            // rag文档问答库引用
            showLink:
              !!data.knowledge &&
              data.knowledge !== '[]' &&
              data.knowledge !== 'null',
            linkJson: data.knowledge ? JSON.parse(data.knowledge) : {},
            logId: data.logId,
          })
        } else {
          this.dialogList = this.dialogList.map((item) => {
            if (item.type === 'answer') {
              return {
                ...item,
                data: {
                  ...item.data,
                  answer:
                    item.data.logId === data.logId
                      ? item.data.answer + data.text
                      : item.data.answer,
                  showJson:
                    item.data.logId === data.logId
                      ? !!data.semantic
                      : item.data.showJson,
                  json:
                    item.data.logId === data.logId
                      ? !!data.semantic
                        ? JSON.parse(data.semantic)
                        : {}
                      : item.data.json,
                  showLink:
                    item.data.logId === data.logId
                      ? !!data.knowledge &&
                        data.knowledge !== '[]' &&
                        data.knowledge !== 'null'
                      : item.data.showLink,
                  linkJson:
                    item.data.logId === data.logId
                      ? !!data.knowledge
                        ? JSON.parse(data.knowledge)
                        : {}
                      : item.data.linkJson,
                },
              }
            } else {
              return item
            }
          })
        }

        this.$nextTick(() => {
          let i = this.dialogList
            .filter((item) => item.type === 'answer')
            .findIndex((item) => item.data.logId === data.logId)
          this.insertCursor(i)
        })
      }
      if (data.finish) {
        this.submitting = false
      }
    },

    insertCursor(index) {
      // 获取展示内容的容器
      const parent = this.$refs.markdownRef[index]?.$el
      // console.log('markdownRef', parent)
      if (!parent) return
      // 获取最后一个子元素节点
      let lastChild = parent.lastElementChild || parent
      // 如果是pre标签，就在pre标签中找到class为hljs的元素
      if (lastChild.tagName === 'PRE') {
        lastChild = lastChild.getElementsByClassName('hljs')[0] || lastChild
      }
      // 兼容是ul标签的情况，找到OL标签内部的最后一个元素
      if (lastChild.tagName === 'OL') {
        lastChild = this.findLastElement(lastChild)
      }
      // 向最后一个子元素中插入span标签实现光标
      lastChild?.insertAdjacentHTML(
        'beforeend',
        '<span class="inputing-cursor"></span>'
      )
    },
    // 递归找到DOM下最后一个元素节点
    findLastElement(element) {
      // 如果该DOM没有子元素，则返回自身
      if (!element.children.length) {
        return element
      }
      const lastChild = element.children[element.children.length - 1]
      // 如果最后一个子元素是元素节点，则递归查找
      if (lastChild.nodeType === Node.ELEMENT_NODE) {
        return this.findLastElement(lastChild)
      }
      return element
    },

    copyJson(data) {
      this.$utils.copyClipboard(JSON.stringify(data, null, '    '))
    },
    openJsonDialog(datas) {
      this.showJson = !this.showJson
      // if (datas.hasOwnProperty('res') && datas.res) {
      //   this.resJson = datas.res
      // }
      this.resJson = datas
    },
    openLinkDialog(json) {
      this.showLinkDialog.show = !this.showLinkDialog.show
      this.resLinkJson = json
    },
    closeRightTest() {
      this.$store.dispatch('studioSkill/setRightTestOpen', false)
    },
    cleanHistory() {
      // 关闭EventSource连接
      this.closeEventSource()
      this.dialogList = [
        {
          type: 'answer',
          data: {
            answer: '你好，我是智能的小飞~',
          },
        },
      ]
      this.submitting = false
      this.uid = this.$utils.experienceUid()
      this.$message.success('清除会话历史成功')
    },

    updateFormFields(formFields) {
      console.log('updateFormFields exe, update this.formFields', formFields)
      this.formFields = formFields
    },

    // 从本地存储加载设备状态数据
    loadDeviceStatusFromStorage() {
      const storedFields = getDeviceStatus(
        this.currentUserId,
        this.currentAppId,
        this.currentSceneName
      )
      if (storedFields && storedFields.length > 0) {
        this.formFields = storedFields
      } else {
        // 如果本地存储没有数据，重置为默认数据
        this.resetToDefaultFormFields()
      }
    },

    // 重置为默认的表单字段数据
    resetToDefaultFormFields() {
      this.formFields = [
        {
          name: 'device_status',
          type: 'object',
          value: '',
          children: [
            {
              name: 'volume',
              type: 'string',
              value: '50',
              children: [],
              items: [],
              itemType: 'string',
            },
            {
              name: 'brightness',
              type: 'string',
              value: '80',
              children: [],
              items: [],
              itemType: 'string',
            },
            {
              name: 'playing_status',
              type: 'string',
              value: '暂停播放',
              children: [],
              items: [],
              itemType: 'string',
            },
          ],
          items: [],
          itemType: 'string',
        },
      ]
    },

    /**
     * 将表单编辑器数据结构转换为标准JSON格式
     * @param {Array} formFields 表单编辑器数据结构
     * @returns {Object} 标准JSON对象
     */
    convertToJson(formFields) {
      const result = {}

      formFields.forEach((field) => {
        if (!field.name) return // 跳过没有名称的字段

        switch (field.type) {
          case 'object':
            // 递归处理对象类型的子字段
            result[field.name] = field.children
              ? this.convertToJson(field.children)
              : {}
            break

          case 'array':
            // 处理数组类型
            result[field.name] = field.items
              ? field.items.map((item) => {
                  if (item.type === 'object') {
                    return item.children
                      ? this.convertToJson(item.children)
                      : {}
                  }
                  return item.value !== undefined ? item.value : null
                })
              : []
            break

          default:
            // 处理基本类型（string, number, boolean）
            result[field.name] = field.value !== undefined ? field.value : null
        }
      })

      return result
    },
  },
  components: {
    VueMarkdown,
    Inputing,
    appDebug45LinkDialog,
    deviceStatusSettingDialog,
  },
}
</script>

<style lang="scss" scoped>
@import '@C/debug.scss';

.simulate-params-btn {
  display: flex;
  align-items: center;
  color: #31323b;
  cursor: pointer;

  span {
    margin-left: 4px;
    font-size: 14px;
    color: #31323b;
  }

  &:hover {
    opacity: 0.8;
  }
}
</style>
<style lang="scss">
.inputing-cursor {
  position: relative;
}
.inputing-cursor::after {
  content: ' ';
  position: absolute;
  top: 0;
  right: 0;
  height: 100%;
  width: 2px;
  background-color: black;
  animation-name: blink;
  animation-duration: 1s;
  animation-iteration-count: infinite;
}

@keyframes blink {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
</style>
