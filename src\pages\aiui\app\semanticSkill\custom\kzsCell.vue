<template>
  <!-- <div v-else-if="item.skillListType === 'grepositoryList'">
                <div style="cursor: default" :key="index">
                  <i class="skill-icon">{{
                    item.name && item.name.substr(0, 1)
                  }}</i>
                  <div class="skill-info">
                    <p class="skill-title" :title="item.name">
                      {{ item.name }}
                    </p>
                  </div>
                  <i
                    v-if="subAccountEditable"
                    @click="configThreshold(item)"
                    class="skill-config ic-r-setting"
                  ></i>
                  <i
                    v-if="subAccountEditable"
                    @click="deleteRepo(item, index, 'grepositoryList')"
                    class="skill-config ic-r-delete"
                  ></i>
                </div>
              </div> -->

  <div :class="['skill', { 'skill-active': item.used }]">
    <div :class="['content-wrap', { 'cursor-default': subAccount }]">
      <i class="skill-icon">{{ item.name && item.name.substr(0, 1) }}</i>
      <div class="skill-info">
        <p class="skill-title" :title="item.name">
          {{ item.name }}
        </p>

        <!-- 标题下按钮 -->
        <div class="title-btm-btn-group">
          <p class="ability-tag">开放问答</p>
        </div>
      </div>
    </div>

    <div @click.stop class="switch-wrap">
      <el-switch
        :disabled="!subAccountEditable"
        size="small"
        v-model="item.used"
        @change="(val) => onSwitchChange(val, item)"
      >
      </el-switch>
    </div>
    <!-- 下面的label area -->
    <div v-if="subAccountEditable && item.used" class="label-wrap">
      <p class="skill-desc" :title="item.name"></p>
      <i
        @click.prevent.stop="showThresholdInfo(item)"
        class="skill-config-new AIUI-myapp-iconfont ai-myapp-setting2"
        v-if="subAccountEditable && item.used"
      ></i>
    </div>
    <threshold-info
      :thresholdVisible="thresholdVisible"
      :isRepository="true"
      :configSkillItem="configSkillItem"
      :appId="appId"
      :currentScene="currentScene"
      :skillThresholdConfig="skillThresholdConfig"
      :qaThresholdConfig="qaThresholdConfig"
      :skillConfig="skillConfig"
      :globalThresholdChange="globalThresholdChange"
      @thresholdVisibleChange="onThresholdVisibleChange"
      @change="$emit('change')"
    ></threshold-info>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import thresholdInfo from '../thresholdInfo.vue'

export default {
  components: { thresholdInfo },
  data() {
    return {
      thresholdVisible: false,
      configSkillItem: {},
    }
  },
  props: {
    item: Object,
    qaConfig: Object,
    ubotQaConfig: Object,

    currentScene: Object,
    appId: String,
    skillThresholdConfig: Object,
    qaThresholdConfig: Object,
    skillConfig: Object,
    globalThresholdChange: Boolean,
  },
  computed: {
    ...mapGetters({
      subAccount: 'user/subAccount',
      subAccountAppAuths: 'aiuiApp/subAccountAppAuths',
      subAccountSkillAuths: 'aiuiApp/subAccountSkillAuths',
    }),
    subAccountEditable() {
      return this.subAccountAppAuths[this.$route.params.appId] == 2
        ? false
        : true
    },
  },
  methods: {
    onThresholdVisibleChange(val) {
      this.thresholdVisible = val
    },
    showThresholdInfo(item) {
      this.configSkillItem = item
      this.thresholdVisible = true
    },

    doRealChange(val, item) {
      console.log('onSwitchChange', val, item)
      this.$emit('change')
      let data
      let operation = val ? 'open' : 'close'
      data = {
        repositoryId: item.id,
        type: '1',
        operation,
      }
      this.qaConfig[item.id] = data
    },

    onSwitchChange(val, item) {
      let that = this
      if (!val) {
        const tip =
          '「开放问答」现在已经迁移至「兜底配置-讯飞闲聊-精选问答」，「开放问答」删除后将无法再次添加，如需使用闲聊功能，请在「兜底配置」中进行配置。'

        this.$confirm(tip, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(() => {
            that.doRealChange(val, item)
          })
          .catch(() => {
            item.used = true
          })
      } else {
        that.doRealChange(val, item)
      }
    },
  },
}
</script>
<style lang="scss" scoped>
@import '../style.scss';
// .skill {
//   height: 117px;
// }

.content-wrap {
  display: flex;
  // height: 70px;
}
</style>
