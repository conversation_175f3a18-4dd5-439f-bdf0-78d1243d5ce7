<template>
  <el-dialog :visible.sync="isShow" :title="title" @close="cancel">
    <div v-if="type === 1" class="desc">
      配置默认鉴权，信源相关的API接口将默认使用该鉴权方式
    </div>

    <div v-if="type === 2" class="desc">
      正在编辑 {{ toolName }} 的鉴权配置，可点击
      <span @click="reset" style="margin: 0 5px; color: red; cursor: pointer"
        >重置</span
      >
      恢复默认鉴权配置
    </div>

    <el-form
      :model="form"
      ref="form"
      :rules="rules"
      label-width="125px"
      label-position="left"
    >
      <!-- <el-form-item label="授权方式:" prop="authType">
        <el-radio-group v-model="form.authType" class="authType">
          <el-radio :label="-1" border>无需授权，无需权限即可使用API</el-radio>
          <el-radio :label="2" border
            >需要在请求头 (header) 或查询参数 (query) 中携带授权信息</el-radio
          >
        </el-radio-group>
      </el-form-item> -->

      <el-form-item label="授权方式:" prop="authType">
        <div class="auth-radio-group">
          <div class="card" :class="{ active: form.authType === -1 }">
            <div class="card-header">
              <el-radio :label="-1" v-model="form.authType"
                >不需要授权</el-radio
              >
            </div>
            <div class="card-content">无需授权，无需权限即可使用API</div>
          </div>

          <div class="card" :class="{ active: form.authType === 2 }">
            <div class="card-header">
              <el-radio :label="2" v-model="form.authType">Service</el-radio>
            </div>
            <div class="card-content">
              需要在请求头 (header) 或查询参数 (query) 中携带授权信息
            </div>
          </div>
        </div>
      </el-form-item>

      <el-form-item label="位置：" v-if="form.authType === 2" prop="location">
        <el-select v-model="form.location" class="form-item">
          <el-option value="header" label="Header"></el-option>
          <el-option value="query" label="Query"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item
        label="ParameterName："
        prop="parameterName"
        v-if="form.authType === 2"
      >
        <el-input
          v-model="form.parameterName"
          placeholder="密钥的参数，您需要传递Service Token的参数名"
          class="form-item"
        ></el-input>
      </el-form-item>

      <el-form-item
        label="Service token / APl key："
        prop="serviceToken"
        v-if="form.authType === 2"
        class="serviceTokenLabel"
      >
        <el-input
          v-model="form.serviceToken"
          placeholder="密钥的参数，代表您的身份或给定的服务权限"
          class="form-item"
        ></el-input>
      </el-form-item>
    </el-form>

    <span slot="footer" class="dialog-footer">
      <el-button @click="cancel">取 消</el-button>
      <el-button type="primary" @click="handleSubmit">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: 'autoInfoModal',
  components: {},
  props: {
    defaultAuthConfig: Object,
  },
  data() {
    return {
      type: 1, //  1默认鉴权  2自定义API的鉴权

      isShow: false,
      toolId: null, // 自定义APi的toolId
      toolName: null,
      title: '配置默认鉴权',
      form: {
        authType: -1,
        location: null,
        parameterName: null,
        serviceToken: null,
      },
      rules: {
        authType: [
          { required: true, message: '请选择授权方式', trigger: 'blur' },
        ],
        location: [{ required: true, message: '请选择位置', trigger: 'blur' }],

        parameterName: [
          { required: true, message: '请填写ParameterName', trigger: 'blur' },
        ],
        serviceToken: [
          { required: true, message: '请填写serviceToken', trigger: 'blur' },
        ],
      },
    }
  },
  mounted() {},
  methods: {
    show(type, toolId, AuthInfo, toolName) {
      this.isShow = true
      this.type = type // 1 默认鉴权  2 自定义API鉴权
      this.toolId = toolId
      console.log(type, toolId, AuthInfo, toolName, 'show的参数')
      this.title = this.type === 1 ? '默认鉴权' : '自定义鉴权'
      if (this.type === 1) {
        //  默认鉴权的 有参数
        if (AuthInfo?.location) {
          this.form.authType = 2
          this.form.location = AuthInfo?.location
          this.form.parameterName = AuthInfo?.parameterName
          this.form.serviceToken = AuthInfo?.serviceToken
        } else {
          this.form.authType = -1
        }
      } else {
        //  自定义鉴权的时候， AuthInfo 是JSON，还需要解析下才能拿到 location  和 parameterName这些
        let customInfo = AuthInfo
        if (AuthInfo) {
          if (customInfo.authType === 2) {
            this.form.authType = customInfo.authType
            this.form.location = customInfo?.authInfo?.location
            this.form.parameterName = customInfo?.authInfo.parameterName
            this.form.serviceToken = customInfo?.authInfo.serviceToken
          } else {
            this.form.authType = -1
            this.form.authInfo = ''
          }
        } else {
          this.form.authType = -1
          this.form.authType = ''
        }

        console.log(this.defaultAuthConfig, '在自定义鉴权里面打印默认鉴权')
      }

      if (toolName) {
        this.toolName = toolName
      }
    },

    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          const params = {
            location: this.form.location,
            parameterName: this.form.parameterName,
            serviceToken: this.form.serviceToken,
          }
          if (this.type === 1) {
            //  默认鉴权 调接口
            const editApiData = {
              sourceId: this.$route.params.sourceId,
              authInfo: this.form.authType === 2 ? JSON.stringify(params) : '',
              authType: this.form.authType,
            }
            this.$utils.httpPost(
              `/aiui-agent/openPlatform/source/edit`,
              JSON.stringify(editApiData),
              {
                config: {
                  headers: {
                    'Content-Type': 'application/json;charset=UTF-8',
                  },
                },
                success: (res) => {
                  if (res.code === '0') {
                    this.$message.success('默认鉴权配置已保存')
                    this.$emit('refresh')
                  }
                },
                error: (err) => {},
              }
            )
          } else {
            // 自定义保存本地的 存信息，不能调接口
            const customInfo = {
              authType: this.form.authType,
              authInfo: this.form.authType === 2 ? params : '',
            }
            this.$emit('saveCustomInfo', customInfo)
            // this.$message.success('自定义鉴权配置已保存')

            if (this.toolId) {
              //  自定义api调接口保存
              const editApiData = {
                toolId: this.toolId,
                authType: this.form.authType,
                authInfo:
                  this.form.authType === 2 ? JSON.stringify(params) : '',
              }
              this.$utils.httpPost(
                `/aiui-agent/openPlatform/source/apiSave`,
                JSON.stringify(editApiData),
                {
                  config: {
                    headers: {
                      'Content-Type': 'application/json;charset=UTF-8',
                    },
                  },
                  success: (res) => {
                    if (res.code === '0') {
                      this.$message.success('自定义鉴权配置已保存')
                      this.$emit('refresh')
                    }
                  },
                  error: (err) => {},
                }
              )
            }
          }
          this.cancel()
        }
      })
    },
    reset() {
      //  自定义重置功能

      if (!this.defaultAuthConfig.location) {
        this.$message.warning('该信源暂未配置默认鉴权')
      } else {
        this.form.authType = 2
        this.form.location = this.defaultAuthConfig?.location
        this.form.parameterName = this.defaultAuthConfig?.parameterName
        this.form.serviceToken = this.defaultAuthConfig?.serviceToken
      }
    },
    cancel() {
      this.isShow = false
      this.form = {
        authType: -1,
        location: null,
        parameterName: null,
        serviceToken: null,
      }
      this.toolId = null
      this.toolName = null
      this.type = 1
    },
  },
}
</script>

<style lang="scss" scoped>
.desc {
  margin: 0 auto;
  margin-bottom: 10px;
}
.auth-radio-group {
  display: flex;
  align-content: center;
  gap: 20px;
  .card {
    width: 260px;
    border: 1px solid #f0f0f0;
    border-radius: 5px;
    pointer-events: auto;
    .card-header {
      background-color: #eff3f9;
      padding: 0px 10px;
    }
    .card-content {
      padding: 10px;
      color: #666;
      font-size: 14px;
      line-height: 1.4;
    }
    &.active {
      border-color: #409eff;
    }
  }

  ::v-deep .el-radio__original {
    display: none !important;
  }

  ::v-deep
    .el-radio:focus:not(.is-focus):not(:active):not(.is-disabled)
    .el-radio__inner {
    box-shadow: none !important;
  }
}

.serviceTokenLabel {
  :deep(.el-form-item__label) {
    width: 150px; /* 固定宽度 */
    display: -webkit-box;
    // -webkit-line-clamp: 2; /* 限制为1行 */
    -webkit-box-orient: vertical;
    overflow: hidden;
    line-height: 1.5;
  }
}
</style>
