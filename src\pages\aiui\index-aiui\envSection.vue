<template>
  <div
    class="section-env wow fadeInUp"
    v-lazy:background="require(`@A/images/home/<USER>/banner.png`)"
  >
    <div class="env-logo">
      <img v-lazy="require('@A/images/home/<USER>/logo.png')" alt="" />
    </div>
    <div class="env-bgs">
      <img v-lazy="require('@A/images/home/<USER>/brand.png')" alt="" />
    </div>
  </div>
</template>
<script>
export default {
  name: 'section-env',
  data() {
    return {}
  },
}
</script>
<style lang="scss" scoped>
@import './common.scss';

.section-env {
  width: auto;
  height: 680px;
  // background: url('../../../assets/images/home/<USER>/banner.png');

  .env-logo {
    width: 1200px;
    padding: 64px 0;
    margin: 0 auto;
    display: flex;
    justify-content: center;

    img {
      width: 365px;
      height: 144px;
    }
  }

  .env-bgs {
    width: 1200px;
    margin: 0 auto;
    margin: 0 auto;
    display: flex;
    justify-content: center;

    img {
      width: 1104px;
      height: 304px;
    }
  }
}
</style>
