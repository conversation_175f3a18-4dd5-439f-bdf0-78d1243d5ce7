<template>
  <div>
    <os-collapse :default="true" size="large" title="技能回复">
      <inteligient-input @onAdd="onAdd"></inteligient-input>
      <inteligient-reply></inteligient-reply>
    </os-collapse>
    <os-divider class="mgt28" />
  </div>
</template>
<script>
import InteligientInput from './referSlots/inteligientInput.vue'
import InteligientReply from './referSlots/inteligientReply.vue'
export default {
  data() {
    return {}
  },
  methods: {
    onAdd(itemAddName) {
      console.log(itemAddName)
      this.$message.error(itemAddName)
    },
  },
  components: {
    InteligientInput,
    InteligientReply,
  },
}
</script>
<style lang="scss"></style>
