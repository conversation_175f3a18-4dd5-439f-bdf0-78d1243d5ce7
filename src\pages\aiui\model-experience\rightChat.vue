<template>
  <div class="content-right-wrap">
    <div class="content-right">
      <div class="title-wapper">
        <div class="title"></div>
      </div>

      <div
        class="rightChatboxContainer"
        id="rightChatbox"
        :style="{
          height:
            current === 'ability' ? 'calc(100% - 210px)' : 'calc(100% - 230px)',
        }"
      >
        <div class="padding-wrap">
          <div class="title" v-if="chatList.length <= 1">
            <div class="title-right">
              <div class="title-text">您好，我是AIUI。</div>
              <div class="title-desc">
                结合大模型强大的泛化理解与回复生成能力，解决了传统语义交互方式多轮效果弱、闲聊能力差等问题，同时耦合了传统的语义系统，保留了传统语义系统的信源内容等优势。广泛适用于机器人、虚拟人、电视、儿童教育硬件等智能硬件的交互能力升级，带来更类人自然的交互体验。
              </div>
            </div>
          </div>
          <chain-selector
            v-if="current === 'chain'"
            :current="current"
            :currentChain="currentChain"
            @selected="setCurrentChain"
          ></chain-selector>
          <ability-selector
            v-if="current === 'ability'"
            :currentAbility="currentAbility"
            @selected="setCurrentAbility"
          ></ability-selector>
          <product-selector
            v-if="current === 'product'"
            :currentProduct="currentProduct"
            @selected="setCurrentProduct"
          ></product-selector>
        </div>
        <div class="rightChatbox" ref="rightChatbox">
          <div
            v-for="(item, index) in chatList"
            :key="index"
            :class="item.people === 'me' ? 'me' : 'ai'"
            class="chatbox"
          >
            <div
              class="con"
              style="white-space: pre-wrap; position: relative"
              v-if="item.type === 'loading'"
            >
              <div
                style="
                  white-space: pre-wrap;
                  max-width: 100%;
                  padding-top: 12px;
                "
              >
                <span>正在处理</span>&nbsp;<inputing></inputing>
              </div>

              <div class="icon-ai" v-if="item.people === 'ai'"></div>
            </div>
            <div
              class="con"
              style="position: relative"
              v-if="item.type === 'text'"
            >
              <div v-if="item.people === 'me'" style="max-width: 100%">
                {{ item.con }}
              </div>
              <div v-else style="max-width: 100%; padding-top: 12px">
                <vue-markdown
                  :source="item.con"
                  @rendered="onRendered"
                  :html="false"
                  ref="markdownRef"
                ></vue-markdown>
              </div>
              <div
                class="icon-opt"
                v-if="
                  item.people === 'ai' &&
                  item.replyType === 'realAnswer' &&
                  item.showOpt
                "
              >
                <el-tooltip
                  class="item"
                  effect="dark"
                  content="点赞"
                  placement="top"
                >
                  <div
                    class="dianzan"
                    :class="{ active: item.sign == 1 }"
                    @click="dianzan(item)"
                  ></div>
                </el-tooltip>
                <div class="divider-line"></div>
                <el-tooltip
                  class="item"
                  effect="dark"
                  content="点踩"
                  placement="top"
                >
                  <div
                    class="diancai"
                    :class="{ active: item.sign == 2 }"
                    @click="diancai(item)"
                  ></div>
                </el-tooltip>
              </div>
              <div
                class="icon-view"
                v-if="item.people === 'ai' && item.replyType === 'realAnswer'"
              >
                <a
                  class="view-button"
                  style="margin-right: 8px"
                  @click="showJsonView(item)"
                  v-if="item.showJson"
                  ><i class="iconfont icon-chakan"></i
                  ><span>&nbsp;结构化信息</span></a
                >
                <json-resource-player
                  v-if="item.showJsonPlayer"
                  :resources="(item.jsonResources || []).slice(0, 5)"
                >
                </json-resource-player>
              </div>

              <div class="icon-me" v-if="item.people === 'me'"></div>
              <div class="icon-ai" v-if="item.people === 'ai'"></div>
            </div>
          </div>
          <music-player
            v-if="showMusicPlayer"
            :resources="resources"
            @close="showMusicPlayer = false"
          ></music-player>
        </div>
      </div>
      <div class="bottom-wrapper">
        <div class="ability-selector">
          <skill-selector
            v-if="showSkillSelector"
            @selected="setSelectedSkill"
          ></skill-selector>
          <advance-config
            v-if="showAbility"
            :vcnConfig="vcnConfig"
            :replyStyleConfig="replyStyleConfig"
            :personaConfig="personaConfig"
            :speakers="speakers"
            :currentChain="currentChain"
            :current="current"
            :currentProduct="currentProduct"
            @setReplyStyleConfig="setReplyStyle"
            @setVcnConfig="setVcnConfig"
            @setPersonaConfig="setPersonaConfig"
          ></advance-config>
        </div>
        <div class="control-bar-wrap">
          <div style="position: relative" class="control-bar-input">
            <div class="sendWrapper">
              <div class="control-block-wrapper">
                <div class="control-block">
                  <voice-control @end="onVoiceEnd"></voice-control>
                </div>
                <div
                  class="control-block send"
                  @click="() => sendTextMessage()"
                >
                  <img :src="require('@A/images/model-exeperience/send.png')" />
                </div>
              </div>

              <textarea
                v-model="inputText"
                @input="valueChange"
                @keydown.enter="onKeyEnter"
                placeholder="请输入您想了解的内容"
                style="resize: none"
              />
            </div>
          </div>
        </div>
        <a class="opt-clean" @click="cleanHistory(true)">清除会话历史</a>
      </div>

      <el-dialog
        class="debug-json-dialog"
        title="JSON"
        :visible.sync="showJson"
        width="50%"
      >
        <div class="request-json">
          <template>
            <i
              class="ic-r-copy"
              title="复制代码"
              @click="copyJson(resJson)"
            ></i>
            <json-view class="json-wrap" :data="resJson"></json-view>
          </template>
        </div>
        <div class="dialog-bottom"></div>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import Inputing from './Inputing.vue'
import VueMarkdown from 'vue-markdown'
import MusicPlayer from './musicPlayer.vue'
import JsonResourcePlayer from './jsonResourcePlayer'
import CustomCheckbox from './customCheckbox.vue'
import '@/assets/lib/prism/prism.js'
import '@/assets/lib/prism/prism.css'

import ChainSelector from './chainSelector.vue'
import AbilitySelector from './abilitySelector.vue'
import ProductSelector from './productSelector.vue'
import SkillSelector from './skillSelector.vue'
import AdvanceConfig from './advanceConfig.vue'

import VoiceControl from './voiceControl.vue'
import { fetchEventSource } from '@microsoft/fetch-event-source'
import TTSRecorder from './tts/TTSRecorder'
import TypeWriter from './typeWriter'
import { bus } from '@U/bus'

export default {
  name: 'rightChat',
  components: {
    Inputing,
    VueMarkdown,
    MusicPlayer,
    JsonResourcePlayer,
    CustomCheckbox,
    ChainSelector,
    AbilitySelector,
    ProductSelector,
    SkillSelector,
    AdvanceConfig,
    VoiceControl,
  },

  props: {
    current: {
      type: String,
    },
  },

  data() {
    const chainId = localStorage.getItem('AIUI_CHAIN_CONFIG') || 'chinese'
    return {
      welcomeWords: '你好，我是智能的小飞~',
      // 输入框
      inputText: '',
      websocket: null,
      // 快捷输入
      chatList: [
        {
          people: 'ai',
          con: '你好，我是智能的小飞~',
          type: 'text',
          preset: true,
        },
      ],
      canSend: true,
      showReconnect: false,
      // 控制状态
      flag: false,
      showSend: true,
      // 文本内容是否正在回复
      isReplying: false,
      isActive: true,

      eventSource: null,
      doc: '0',
      uid: this.$utils.experienceUid(),

      shortCut: '0',
      resJson: {},
      showJson: false,

      showMusicPlayer: false,
      resources: [],
      resourceType: '',
      jsonResources: [],
      currentChain: chainId,

      currentAbility: {},

      currentProduct: {},

      category: '',

      // 高级配置-回复风格
      replyStyleConfig: null,
      // 高级配置-发音人设置
      vcnConfig: null,
      personaConfig: null,

      ttsRecorder: null,
      ttsRecorderStatus: 'init',
      abortController: new AbortController(),
      speakers: [],

      // 查看JSON 以logId为key值，主要考虑的是多意图的情形
      semantic: {},

      typeWriter: null,
    }
  },

  watch: {
    ttsRecorderStatus(val) {
      console.log('watch ttsRecorderStatus', val)
      if (val === 'endPlay') {
        if (this.resources.length > 0 && this.resourceType === 'url') {
          this.showMusicPlayer = true
          this.$nextTick(() => {
            this.rightChatbox()
          })
        }
      }
    },
    current(val, oldVal) {
      if (
        (val === 'chain' && oldVal === 'product') ||
        (val === 'product' && oldVal === 'chain')
      ) {
        bus.$emit('REMOVE_VCN_CONFIG')
      }
      this.cleanHistory()
    },
    currentChain(val) {
      // 选择的链路发生了变化 1、将链路本地存储保存起来，2、清除流式合成保存
      localStorage.setItem('AIUI_CHAIN_CONFIG', val)
      //
      bus.$emit('REMOVE_VCN_CONFIG')
      this.cleanHistory()
    },

    'currentProduct.productId'(val) {
      this.cleanHistory()
    },

    isReplying(val, oldVal) {
      if (!val && oldVal) {
        // console.log(
        //   '-----------------isReplying 由true变为false---------------'
        // )
        this.$nextTick(() => {
          Array.from(
            document.getElementsByClassName('inputing-cursor')
          ).forEach((item) => {
            // console.log('cursor element', item)
            item.remove()
          })
        })
      }
    },
  },

  mounted() {
    this.ttsRecorder = new TTSRecorder(this.ttsRecorderStatusCb)
    this.typeWriter = new TypeWriter(this.onConsume, this.onFinish)
    this.getInfosTTSExperience()
  },

  computed: {
    showSkillSelector() {
      if (
        this.current === 'chain' ||
        (this.current === 'product' &&
          Number(this.currentProduct.productId) === 2)
      ) {
        return true
      } else {
        return false
      }
    },

    showAbility() {
      if (this.current === 'chain' || this.current === 'product') {
        return true
      } else {
        return false
      }
    },
  },
  methods: {
    getInfosTTSExperience() {
      let that = this
      this.$utils.httpGet(
        this.$config.api.APP_TTS_GET_INFOS_FOR_EXPERIENCE,
        {},
        {
          success: (res) => {
            const chineseSpeakers = (res.data.chinese || []).map((item) => {
              return {
                ...item,
                lang: 'chinese',
              }
            })
            const cantoneseSpeakers = (res.data.cantonese || []).map((item) => {
              return {
                ...item,
                lang: 'cantonese',
              }
            })
            const englishSpeakers = (res.data.english || []).map((item) => {
              return {
                ...item,
                lang: 'english',
              }
            })
            that.speakers = [
              ...chineseSpeakers,
              ...cantoneseSpeakers,
              ...englishSpeakers,
            ]
          },
          error: (err) => {
            console.log('page=>>')
            console.log(err)
          },
        }
      )
    },
    stopResponseSSE() {
      this.abortController.abort()
      this.abortController = new AbortController()
    },
    ttsRecorderStatusCb(status) {
      // console.log('ttsRecorederStaus', status)
      this.ttsRecorderStatus = status
    },

    rightChatbox() {
      let container = document.getElementById('rightChatbox')
      if (container) {
        container.scrollTop = 1000000
      }
    },
    valueChange(e) {
      this.inputText = e.target.value
    },
    onKeyEnter(e) {
      if ((e.ctrlKey || e.metaKey) && e.keyCode == 13) {
        //用户点击了ctrl+enter触发
        if (this.shortCut === '1') {
          this.sendTextMessage()
        }
      } else {
        // e && e.preventDefault()
        //用户点击了enter触发
        if (this.shortCut === '0') {
          e && e.preventDefault()
          this.sendTextMessage()
        }
      }
    },

    handleMessage(data) {
      // 根据finish 改变所有 showOpt状态
      // 过来了一条推送数据，先过滤loading,再判断往chatList数组里加回复数据,还是改已有的回复数据
      // 可以根据logId判断回复是否已经被插往 chatList中
      this.removeLoading()

      if (this.current === 'ability') {
        if (data.finish || data.nlpFinish) {
          // console.error('test')
          this.isReplying = false
        }
      }

      // 如果是语音识别的结果，用作用户展示
      if (data.type === 'iat') {
        this.addToChatList({
          people: 'me',
          con: data.iat,
          type: 'text',
        })
      }
      if (data.type === 'tts') {
        if (this.vcnConfig && this.vcnConfig.isOn === '1') {
          // 开启了发音人
          if (data.audio) {
            this.ttsRecorder.result(data.audio)
          }
        }
      }

      if (data.type === 'nlp') {
        if (data.semantic) {
          this.semantic[data.logId] = data.semantic
        }
        if (!data.subScene) {
          // 不是单原子能力
          if (data && data.text) {
            this.typeWriter.add(data)
            this.typeWriter.start()
          }

          if (data.finish || data.nlpFinish) {
            this.typeWriter.end()
          }

          if (data.resources) {
            this.resourceType = data.resourceType
            if (data.resourceType === 'url') {
              this.resources = data.resources
            }
            if (data.resourceType === 'json') {
              this.jsonResources = data.resources
            }
          }
        } else {
          // 原子能力体验
          const transformText = this.transformDataText(data)
          if (transformText) {
            this.addToChatList({
              people: 'ai',
              con: transformText,
              replyType: 'realAnswer',
              sign: -1,
              type: 'text',
              showOpt: true,
              showJson: this.semantic[data.logId],
              json: this.semantic[data.logId]
                ? JSON.parse(this.semantic[data.logId])
                : {},
              logId: data.logId,
            })
          }
        }
      }
    },

    onConsume(data) {
      const index = this.chatList.findIndex((item) => item.logId === data.logId)
      if (index === -1) {
        // 没找到第一次添加回复数据
        this.addToChatList({
          people: 'ai',
          con: data.text,
          replyType: 'realAnswer',
          sign: -1,
          type: 'text',
          showOpt: true,
          showJson: this.semantic[data.logId],
          json: this.semantic[data.logId]
            ? JSON.parse(this.semantic[data.logId])
            : {},
          // 泛屏品类体验显示的播放列表
          showJsonPlayer: this.resourceType === 'json',
          jsonResources: this.resourceType === 'json' ? this.jsonResources : [],

          logId: data.logId,
        })
      } else {
        this.chatList = this.chatList.map((item) => {
          if (item.people === 'me') {
            return item
          } else {
            return {
              ...item,
              con: item.logId === data.logId ? item.con + data.text : item.con,
              showJson:
                item.logId === data.logId
                  ? this.semantic[data.logId]
                  : item.showJson,
              json:
                item.logId === data.logId
                  ? this.semantic[data.logId]
                    ? JSON.parse(this.semantic[data.logId])
                    : {}
                  : item.json,
              showJsonPlayer:
                item.logId === data.logId
                  ? this.resourceType === 'json'
                  : item.showJsonPlayer,
              jsonResources:
                item.logId === data.logId
                  ? this.resourceType === 'json'
                    ? this.jsonResources
                    : []
                  : item.jsonResources,
            }
          }
        })
      }
      this.$nextTick(() => {
        let i = this.chatList
          .filter((item) => item.people === 'ai')
          .findIndex((item) => item.logId === data.logId)
        this.insertCursor(i)
      })
    },

    onFinish() {
      // console.log('------------出字finish------')
      this.isReplying = false
      // 如果没有开启流式合成，这时应该直接展示出播放器
      if (this.resourceType === 'url') {
        if (!(this.vcnConfig && this.vcnConfig.isOn === '1')) {
          this.showMusicPlayer = true
          this.$nextTick(() => {
            this.rightChatbox()
          })
        }
      } else if (this.resourceType === 'json') {
      }
    },

    transformDataText(data) {
      let text = data.text
      if (data.subScene === 'cbm_tidy') {
        try {
          let textObj = JSON.parse(data.text)
          let intent = textObj.intent || []
          if (intent.length === 1) {
            text =
              '规整结果：\n' +
              intent.map((it) => `意图：${it.value}`).join('\n')
          } else {
            text =
              '规整结果：\n' +
              intent.map((it) => `意图${it.index + 1}：${it.value}`).join('\n')
          }
        } catch (e) {}
      } else if (data.subScene === 'cbm_intent_domain') {
        if (data.intentDomain) {
          try {
            let textObj = JSON.parse(data.intentDomain)
            let domain = textObj.domain || []
            if (domain.length === 1) {
              text = domain.map((it) => `分类：${it.skill || ''}`).join('\n')
            } else {
              text = domain
                .map((it) => `分类${it.index + 1}：${it.skill || ''}`)
                .join('\n')
            }
          } catch (e) {}
        } else {
          return data.finish ? '暂无意图落域结果' : ''
        }
      } else if (data.subScene === 'cbm_plugin_cmd') {
        if (data.pluginCmd) {
          try {
            let textObj = JSON.parse(data.pluginCmd)
            text = `指令函数：${textObj.func_name}\n意图：${
              textObj.args && textObj.args.intent
            }\n关键信息槽位：${JSON.stringify(
              (textObj.args && textObj.args.parameters) || {}
            )}`
          } catch (e) {}
        } else {
          return data.finish ? '暂无指令抽取结果' : ''
        }
      } else if (data.subScene === 'cbm_denial') {
        try {
          let textObj = JSON.parse(data.text)
          if (String(textObj.type) === '0') {
            text = '无关信息，拒识'
          } else {
            text = '正常交互，不拒识'
          }
        } catch (e) {}
      }
      return text
    },

    cleanHistory(pop) {
      this.typeWriter.done()
      // 清除会话历史
      this.uid = this.$utils.experienceUid()
      this.chatList = this.chatList.filter((item) => item.preset)
      if (this.showMusicPlayer) {
        this.showMusicPlayer = false
      }

      this.semantic = {}
      this.resources = []
      this.resourceType = ''
      this.jsonResources = []
      if (pop) {
        this.$message.success('清除会话历史成功')
      }
      this.stopResponseSSE()

      // console.log('stopResponseSSE 执行')

      // 宏任务，在stopstopResponseSSE的回调之后执行
      setTimeout(() => {
        this.isReplying = false
        this.ttsRecorder.resetAudio()
        // console.log('清空了audio')
      }, 0)
    },

    // 当前面会话nlpFinish为true 已结束，但是finish仍然为false，正在推送音频，这是新会话，需要断掉之前会话，
    cleanSession(cb) {
      if (this.showMusicPlayer) {
        this.showMusicPlayer = false
      }

      this.semantic = {}
      this.resources = []
      this.resourceType = ''
      this.jsonResources = []
      this.stopResponseSSE()

      // console.log('cleanSession stopResponseSSE 执行')

      // 宏任务，在stopstopResponseSSE的回调之后执行
      setTimeout(() => {
        this.isReplying = false
        this.ttsRecorder.resetAudio()
        // console.log('清空了audio')
        cb && cb()
      }, 0)
    },

    //修改聊天内容
    addToChatList(val) {
      this.chatList.push(val)
      this.scrollMessage()
    },

    // filter loading
    removeLoading() {
      this.chatList = this.chatList.filter((item) => item.type !== 'loading')
    },

    scrollMessage() {
      this.$nextTick(() => {
        setTimeout(() => {
          this.rightChatbox()
        }, 0)
      })
    },

    onRendered() {
      this.$nextTick(() => {
        window.Prism && window.Prism.highlightAll()
      })
    },
    insertCursor(index) {
      // 获取展示内容的容器
      const parent = this.$refs.markdownRef[index]?.$el
      // console.log('markdownRef', parent)
      if (!parent) return
      // 获取最后一个子元素节点
      let lastChild = parent.lastElementChild || parent
      // 如果是pre标签，就在pre标签中找到class为hljs的元素
      if (lastChild.tagName === 'PRE') {
        lastChild = lastChild.getElementsByClassName('hljs')[0] || lastChild
      }
      // 兼容是ul标签的情况，找到OL标签内部的最后一个元素
      if (lastChild.tagName === 'OL') {
        lastChild = this.findLastElement(lastChild)
      }
      // 向最后一个子元素中插入span标签实现光标
      lastChild?.insertAdjacentHTML(
        'beforeend',
        '<span class="inputing-cursor"></span>'
      )
    },
    // 递归找到DOM下最后一个元素节点
    findLastElement(element) {
      // 如果该DOM没有子元素，则返回自身
      if (!element.children.length) {
        return element
      }
      const lastChild = element.children[element.children.length - 1]
      // 如果最后一个子元素是元素节点，则递归查找
      if (lastChild.nodeType === Node.ELEMENT_NODE) {
        return this.findLastElement(lastChild)
      }
      return element
    },

    showJsonView(item) {
      console.log('showJSONview')
      this.resJson = item.json
      this.showJson = true
    },
    copyJson(data) {
      this.$utils.copyClipboard(JSON.stringify(data, null, '    '))
    },
    dianzan(item) {
      this.$utils.httpGet(
        this.$config.api.CHAT_SIGN,
        {
          logId: item.logId,
          sign: 1,
        },
        {
          success: (res) => {
            item.sign = 1
            this.$message.success('点赞成功')
          },
          error: (err) => {},
        }
      )
    },
    diancai(item) {
      this.$utils.httpGet(
        this.$config.api.CHAT_SIGN,
        {
          logId: item.logId,
          sign: 2,
        },
        {
          success: (res) => {
            item.sign = 2
            this.$message.success('点踩成功')
          },
          error: (err) => {},
        }
      )
    },

    setCurrentChain(chain) {
      this.currentChain = chain
    },
    setCurrentAbility(a) {
      this.currentAbility = a
    },

    setCurrentProduct(p) {
      this.currentProduct = p
    },

    setSelectedSkill(val) {
      this.category = val
    },

    onVoiceEnd(data) {
      this.sendAudioMessage(data)
    },
    sendTextMessage() {
      let inputMessage = ''

      if (!this.inputText.trim()) {
        this.$message.error('输入文本不能为空')
        this.inputText = ''
        return
      }
      if (this.inputText && this.inputText.length > 500) {
        return this.$message.warning('输入文本不能超过500字符')
      }
      if (this.isReplying) {
        this.$message.warning('请让机器人回答完问题再进行回复')
        return
      }
      inputMessage = this.inputText

      this.addToChatList({
        people: 'me',
        con: inputMessage,
        type: 'text',
      })
      this.cleanSession(() => {
        this.sendMessage(inputMessage, 'text')
        this.inputText = ''
      })
    },
    sendAudioMessage(audioData) {
      if (this.isReplying) {
        this.$message.warning('请让机器人回答完问题再进行回复')
        return
      }
      const blob = new Blob([audioData], { type: 'audio/pcm' })
      this.cleanSession(() => {
        this.sendMessage(blob, 'audio')
      })
    },
    sendMessage(val, type) {
      let that = this

      // 清除下tts播放
      this.ttsRecorder.resetAudio()
      this.showMusicPlayer = false
      this.resources = []
      this.resourceType = ''
      this.jsonResources = []
      this.semantic = {}

      let baseUrl = '/aiui/web/user/chat'
      let formData = new FormData()
      // formData.append('sparkcons', true)
      formData.append('version', 'v50')
      formData.append('expUid', this.uid)

      if (!type || type === 'text') {
        // 文本
        formData.append('query', val)
      } else {
        // 音频
        formData.append('audio', val)
        formData.append('dataType', 'audio')
      }
      let obj = {}
      if (this.current === 'chain') {
        obj = {
          chainId: 'cbm_v45',
          // 体验技能
          category: this.category,
        }
        // 判断体验的语种类型
        if (this.currentChain === 'cantonese') {
          obj.language = 'zh_cn'
          obj.accent = 'cn_cantonese'
        } else if (this.currentChain === 'english') {
          obj.language = 'en_us'
          obj.accent = 'mandarin'
        }
        // 判断是否有发音人
        if (this.vcnConfig && this.vcnConfig.isOn === '1') {
          obj.vcn = this.vcnConfig.vcn
          obj.ttsType = (
            this.speakers.find((s) => s.vcn === this.vcnConfig.vcn) || {}
          ).ttsType
        } else {
          delete obj.vcn
        }
        // 回复风格
        if (this.replyStyleConfig && this.replyStyleConfig.isOn === '1') {
          obj.guideType = this.replyStyleConfig.guideType
          obj.guideValues = JSON.stringify([this.replyStyleConfig.guideValues])
        } else {
          delete obj.guideType
          delete obj.guideValues
        }

        if (this.personaConfig && this.personaConfig.isOn === '1') {
          obj.personaName = this.personaConfig.personaName
          obj.personaFather = this.personaConfig.personaFather
        } else {
          delete obj.personaName
          delete obj.personaFather
        }
      } else if (this.current === 'ability') {
        obj = {
          chainId: 'cbm_ability',
          subScene: this.currentAbility.abilityId,
        }
        if (this.currentAbility.expChainId) {
          obj.expChainId = this.currentAbility.expChainId
        }
      } else if (this.current === 'product') {
        obj = {
          chainId: 'cbm_v45',
          productType: this.currentProduct.productId,
        }

        // 判断是否有发音人
        if (this.vcnConfig && this.vcnConfig.isOn === '1') {
          obj.vcn = this.vcnConfig.vcn
          obj.ttsType = (
            this.speakers.find((s) => s.vcn === this.vcnConfig.vcn) || {}
          ).ttsType
        } else {
          delete obj.vcn
        }
        // 回复风格
        if (
          this.replyStyleConfig &&
          this.replyStyleConfig.isOn === '1' &&
          Number(this.currentProduct.productId) !== 2
        ) {
          obj.guideType = this.replyStyleConfig.guideType
          obj.guideValues = JSON.stringify([this.replyStyleConfig.guideValues])
        } else {
          delete obj.guideType
          delete obj.guideValues
        }

        if (this.personaConfig && this.personaConfig.isOn === '1') {
          obj.personaName = this.personaConfig.personaName
          obj.personaFather = this.personaConfig.personaFather
        } else {
          delete obj.personaName
          delete obj.personaFather
        }

        if (Number(this.currentProduct.productId) === 2) {
          obj.category = this.category
        } else {
          delete obj.category
        }
      }

      Object.keys(obj).forEach((k) => {
        if (obj[k]) {
          // delete obj[k]
          formData.append(k, obj[k])
        }
      })

      // 正在思考中
      this.addToChatList({
        people: 'ai',
        type: 'loading',
      })
      try {
        fetchEventSource(baseUrl, {
          method: 'POST',
          openWhenHidden: true,
          body: formData,
          async onopen(response) {
            if (response.ok) {
              console.log('连接了')
              that.isReplying = true
            } else {
            }
          },

          onmessage(event) {
            try {
              const result = JSON.parse(event.data || '{}')
              console.log(result)
              // 处理每一条信息
              if (result.code == '300001') {
                // handleLoginExpire()
                that.$message.error('请先登录再使用')
                that.isReplying = false
                setTimeout(() => {
                  window.location.href = '/user/login'
                }, 2000)
              } else if (result.code == '0') {
                const data = result.data
                that.handleMessage(data)
              } else {
                that.$message.error(result.desc || '未知错误')
                that.removeLoading()
                that.isReplying = false
              }
            } catch (e) {
              console.log(e)
              that.isReplying = false
            }
          },
          onclose() {
            console.info('断开了')

            // that.isReplying = false
          },
          onerror(err) {
            console.log('报错了', err)
            that.isReplying = false
            throw new Error(err)
          },
          signal: this.abortController.signal,
        })
      } catch (e) {
        console.log('fetchEventSource e', e)
      }
    },

    setPersonaConfig(cfg) {
      this.personaConfig = cfg
    },

    setReplyStyle(cfg) {
      this.replyStyleConfig = cfg
    },
    setVcnConfig(cfg) {
      this.vcnConfig = cfg
    },
  },
}
</script>
<style lang="scss">
.inputing-cursor {
  position: relative;
}
.inputing-cursor::after {
  content: ' ';
  position: absolute;
  top: 0;
  right: 0;
  height: 100%;
  width: 2px;
  background-color: black;
  animation-name: blink;
  animation-duration: 1s;
  animation-iteration-count: infinite;
}

@keyframes blink {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
</style>
<style lang="scss" scoped>
:deep(p) {
  margin-bottom: 0;
}

.content-right-wrap {
  flex: 1;
  background: linear-gradient(180deg, #f1f6ff, #e8eeff);
}

.content-right {
  width: 100%;
  max-width: 1148px;
  height: 100%;
  // width: 1148px;
  font-size: 16px;
  margin: 0 auto;
  position: relative;
  .title-wapper {
    padding: 18px 0 18px 0;
    .title {
      margin: 0 auto;
      width: 119px;
      height: 20px;
      background: url(~@A/images/model-exeperience/v2/<EMAIL>)
        center/contain no-repeat;
    }
  }

  .foot {
    width: 100%;
    font-size: 14px;
    font-family: PingFang SC, PingFang SC-Regular;
    font-weight: 400;
    color: #959595;
    padding: 12px 0 24px 30px;
  }

  .rightChatboxContainer {
    width: 100%;
    height: calc(100% - 230px);
    position: relative;
    overflow-y: auto;
    overflow-x: hidden;
  }

  .rightChatbox {
    width: 100%;
    // height: 100%;
    // overflow: auto;
    box-sizing: border-box;
    // padding-top: 57px;
    // padding-bottom: 150px;
    padding: 30px 88px;

    // &::-webkit-scrollbar-track {
    //   background-color: rgba(255, 255, 255, 0.3);
    // }

    // &::-webkit-scrollbar-thumb {
    //   background: #bacff2;
    // }
  }

  .control-bar-wrap {
    // height: 140px; // abc
    padding: 0 30px 0;
    display: flex;
  }

  .shortcut-setting {
    position: absolute;
    bottom: -30px;
    right: 10px;
    z-index: 1;
  }

  .shortcut-setting-new {
    position: absolute;
    right: 10px;
    z-index: 1;
    top: -30px;
  }

  .control-bar {
    display: flex;
    justify-content: center;
    align-items: center;
    // width: 1200px;
    margin: 0 auto;
    opacity: 1;
  }

  .chatbox {
    margin-bottom: 52px;
  }

  .me {
    display: flex;
    justify-content: flex-start;
    flex-direction: row-reverse;

    .icon-me {
      width: 40px;
      height: 40px;
      position: absolute;
      z-index: 1;
      top: 0;
      right: -58px;
      background: url(~@A/images/model-exeperience/icon-me2.png) center/contain
        no-repeat;
    }

    .con {
      display: flex;
      align-items: center;
      max-width: 90%;
      min-height: 40px;
      padding: 0px 16px;
      background: $primary;
      min-width: 200px;
      border-radius: 12px;
      padding-right: 16px;
      font-size: 14px;
      line-height: 20px;
      // word-break: break-all;
      word-break: break-word;
      box-shadow: 0px 2px 8px 0px rgba(24, 78, 155, 0.15);
      color: #fff;

      &::after {
        content: '';
        border: 8px solid #ffffff00;
        border-left: 8px solid $primary;
        position: absolute;
        top: 12px;
        right: -15px;
      }
    }

    .con2 {
      width: 200px;
      height: 100px;
    }
  }

  .ai {
    .icon-ai {
      width: 40px;
      height: 40px;
      position: absolute;
      z-index: 1;
      top: 0px;
      left: -58px;
      background: url(~@A/images/model-exeperience/icon-ai.png) center/contain
        no-repeat;
    }

    .con {
      color: #fff;
      box-sizing: border-box;

      width: fit-content;
      max-width: 600px;
      min-width: 350px;
      min-height: 40px;
      padding: 0px 16px 12px 16px;
      background: #fff;
      border-radius: 12px;
      border: 1px solid transparent;
      box-shadow: 0px 2px 8px 0px rgba(24, 78, 155, 0.15);
      color: #282c33;
      font-size: 14px;
      line-height: 20px;
      word-break: break-all;

      &::after {
        content: '';
        border: 8px solid #ffffff00;
        border-right: 8px solid #fff;
        position: absolute;
        top: 12px;
        left: -15px;
      }

      :deep(pre) {
        overflow: auto;
      }
    }
  }
}

.control-bar-input {
  position: relative;
  //   flex: 1;
  width: 100%;
  // height: 130px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.sendWrapper {
  //   display: flex;
  align-items: center;
  position: relative;
  flex: 1;

  .control-block-wrapper {
    display: flex;
    align-items: center;

    position: absolute;
    right: 10px;
    bottom: 14px;

    .control-block + .control-block {
      margin-left: 16px;
    }
  }

  .send {
    width: 32px;
    height: 32px;
    background-color: #1676ff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;

    cursor: pointer;

    img {
      margin-left: -2px;
    }
  }

  .icon-footer {
    position: absolute;
    // top: 50%;
    // transform: translateY(-50%);
    top: 0;
  }

  .icon-divider {
    width: 1px;
    height: 35px;
    border: 1px solid #d5e0eb;
    right: 60px;
  }

  textarea {
    width: 100%;
    // height: 47px;
    height: 90px;
    background: #ffffff;
    border: 1px solid #e6edf4;
    border-radius: 4px;
    box-shadow: 0px 2px 9px 0px rgba(112, 137, 158, 0.15);
    // margin-top: 30px;
    font-size: 14px;
    font-weight: 400;
    color: #666;
    line-height: 22px;
    padding: 14px 100px 0 24px;
    outline: none;
  }
}

li {
  margin: 0;
}

.icon-opt {
  position: absolute;
  z-index: 1;
  right: -70px;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
}

.icon-view {
  position: absolute;
  z-index: 1;
  bottom: -40px;
  left: 0;
  display: flex;
  align-items: flex-start;

  :deep(.el-input--small .el-input__inner) {
    border-radius: 32px;
    width: 120px;
  }
}

.dianzan {
  cursor: pointer;
  font-size: 12px;
  width: 17px;
  height: 16px;
  margin-right: 10px;
  background: url(~@A/images/model-exeperience/up.png) center/contain no-repeat;

  &:hover {
    background: url(~@A/images/model-exeperience/up-hover.png) center/contain
      no-repeat;
  }

  &.active {
    background: url(~@A/images/model-exeperience/up-solid.png) center/contain
      no-repeat;
  }
}

.diancai {
  cursor: pointer;
  width: 17px;
  height: 16px;
  background: url(~@A/images/model-exeperience/down.png) center/contain
    no-repeat;

  &:hover {
    background: url(~@A/images/model-exeperience/down-hover.png) center/contain
      no-repeat;
  }

  &.active {
    background: url(~@A/images/model-exeperience/down-solid.png) center/contain
      no-repeat;
  }
}

.divider-line {
  width: 1px;
  height: 16px;
  background: #bfc7d6;
  margin-right: 10px;
}

.divider {
  width: 1px;
  height: 15px;
  border: 1px solid #f2f2f2;
  margin: 0 10px;
}

.view-button {
  width: 120px;
  height: 24px;
  border-radius: 19px;
  text-align: center;
  line-height: 24px;
  background: linear-gradient(90deg, #e6eeff, #f9faff);
  border: 1px solid #b6c9ff;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.opt-clean {
  display: inline-block;
  min-width: 100px;
  position: absolute;
  right: 30px;
  top: -30px;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 11px;
  padding: 2px 4px;
  font-size: 14px;
  text-align: center;
  color: #1878e2;
}

.control-btns {
  position: absolute;
  left: 0px;
  top: -5px;
  display: flex;
}

.json-wrap {
  min-height: 250px;
  max-height: 500px;
  padding: 25px;
  border-radius: 12px;
  margin-bottom: 20px;
  // border: 1px solid $grey4;
  overflow-x: hidden;
  overflow-y: auto;
  word-break: break-word;
  word-wrap: break-word;
  background-color: $grey1-30 !important;
}

.dialog-bottom {
  height: 20px;
}

.ic-r-copy {
  position: absolute;
  right: 24px;
  top: 24px;
  z-index: 8;
  font-size: 16px;
  color: $grey4;
  cursor: pointer;

  &::after {
    content: '';
    display: inline-block;
    position: absolute;
    right: -8px;
    top: -8px;
    z-index: -5;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: $white;
    box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.1);
  }
}

.padding-wrap {
  padding: 0 30px;
  .title {
    width: 100%;
    height: 113px;
    display: flex;
    align-items: center;
    background: url(~@A/images/model-exeperience/v2/bg.png) left/cover no-repeat;
    overflow: hidden;
    background-origin: border-box;
    border: 3px solid #ffffff;
    border-radius: 8px;
    box-shadow: 0px 2px 8px 0px rgba(124, 159, 245, 0.5);
    // 纯占位

    .title-right {
      padding: 0 32px 0 140px;
      .title-text {
        font-size: 18px;
        font-weight: 500;
        color: #333;
        line-height: 25px;
      }
      .title-desc {
        margin-top: 8px;
        font-size: 14px;
        font-weight: 400;
        color: #262b4f;
        line-height: 20px;
      }
    }
  }
}

.bottom-wrapper {
  position: fixed;
  bottom: 10px;
  z-index: 10;
  width: 100%;
  max-width: 1148px; /*  */
}

.ability-selector {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30px;
  margin-bottom: 8px;
}

@media screen and (max-width: 1441px) {
  .content-right {
    width: 100%;
    min-width: 1040px;
    max-width: 1040px;
  }
  .bottom-wrapper {
    max-width: 1040px;
    min-width: 1040px;
  }
}

@media screen and (max-width: 1601px) {
  .content-right {
    // .rightChatboxContainer {
    //   height: calc(100% - 247px);
    // }

    // .rightChatbox {
    //   padding: 40px 66px;
    // }

    // .control-bar-wrap {
    //   padding: 0 20px;
    //   height: 120px;
    // }
  }
}
</style>
