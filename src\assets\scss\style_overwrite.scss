.el-button {
  border-radius: 4px;
}
.el-button--primary {
  color: #fff;
  background-color: $primary;
  border-color: $primary;
}
.el-button--primary:hover,
.el-button--primary:focus {
  background: #61b0fd;
  border-color: #61b0fd;
  color: #fff;
  -webkit-box-shadow: 0 4px 8px 0 #a2cef6;
  box-shadow: 0 4px 8px 0 #a2cef6;
}
.el-button--primary:active {
  background: #1577d2;
  border-color: #1577d2;
  color: #fff;
  outline: none;
}
.el-button--primary.is-active {
  background: #1577d2;
  border-color: #1577d2;
  color: #fff;
}
.el-button--primary.is-disabled,
.el-button--primary.is-disabled:hover,
.el-button--primary.is-disabled:focus,
.el-button--primary.is-disabled:active {
  color: white;
  background-color: #8bc2f4;
  border-color: #8bc2f4;
  -webkit-box-shadow: inherit;
  box-shadow: inherit;
}
.el-button--primary.is-plain {
  color: $primary;
  background: #e8f3fd;
  border-color: #a2cef6;
}
.el-button--primary.is-plain:hover,
.el-button--primary.is-plain:focus {
  background: $primary;
  border-color: $primary;
  color: #fff;
}
.el-button--primary.is-plain:active {
  background: #1577d2;
  border-color: #1577d2;
  color: #fff;
  outline: none;
}
.el-button--primary.is-plain.is-disabled,
.el-button--primary.is-plain.is-disabled:hover,
.el-button--primary.is-plain.is-disabled:focus,
.el-button--primary.is-plain.is-disabled:active {
  color: #74b5f2;
  background-color: #e8f3fd;
  border-color: #d1e6fb;
}

// 定义一种自定义类型按钮
.el-button--normal {
  color: #555454;
  background-color: #fff;
  border-color: #e8e8e8;
  padding: 11px 16px !important;
  min-width: 90px;
}
.el-button--normal:hover,
.el-button--normal:focus {
  background: #f1f7ff;
  border-color: #e8e8e8;
  color: #555454;
}
.el-button--normal:active {
  background: #f1f7ff;
  border-color: #e8e8e8;
  color: #555454;
  outline: none;
}
.el-button--normal.is-active {
  background: #f1f7ff;
  border-color: #e8e8e8;
  color: #555454;
}
.el-button--normal.is-disabled,
.el-button--normal.is-disabled:hover,
.el-button--normal.is-disabled:focus,
.el-button--normal.is-disabled:active {
  color: white;
  background-color: #c8c9cc;
  border-color: #c8c9cc;
  -webkit-box-shadow: inherit;
  box-shadow: inherit;
}
.el-button--normal.is-plain {
  color: #909399;
  background: #f4f4f5;
  border-color: #d3d4d6;
}
.el-button--normal.is-plain:hover,
.el-button--normal.is-plain:focus {
  background: #909399;
  border-color: #909399;
  color: #fff;
}
.el-button--normal.is-plain:active {
  background: #82848a;
  border-color: #82848a;
  color: #fff;
  outline: none;
}
.el-button--normal.is-plain.is-disabled,
.el-button--normal.is-plain.is-disabled:hover,
.el-button--normal.is-plain.is-disabled:focus,
.el-button--normal.is-plain.is-disabled:active {
  color: #bcbec2;
  background-color: #f4f4f5;
  border-color: #e9e9eb;
}

//
.el-tabs__item {
  padding: 0 20px;
  height: 40px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  line-height: 40px;
  display: inline-block;
  list-style: none;
  font-size: 14px;
  font-weight: 500;
  color: $grey002;
  position: relative;
}

.el-tabs__item.is-active {
  color: $primary;
}
.el-tabs__item:hover {
  color: $primary;
  cursor: pointer;
}
.el-tabs__item.is-disabled {
  color: #d5d8de;
  cursor: default;
}
