<template>
  <el-dialog
    title="信源信息"
    :visible="sourceVisible"
    width="570px"
    @close="onClose"
    :modal="false"
  >
    <p class="add-skill-desc">
      使用该技能需要信源支持，排序靠上的信源将优先被调用匹配结果。
    </p>
    <p class="other-skill-source-tips" v-if="hasOtherSource">
      该技能存在第三方信源，稳定性由提供商提供服务保障。
    </p>

    <draggable
      class="source-list"
      v-model="sourceList"
      @end="sourceChange = true"
    >
      <li
        class="source-item"
        v-for="(item, index) in sourceList"
        :key="item.code"
      >
        <i class="ic-r-menu fl"></i>
        <img class="source-logo fl" :src="item.logo" />
        <div class="source-info fl">
          <p class="source-title">{{ item.name }}</p>
          <p class="source-desc">提供商：{{ item.name }}</p>
        </div>
        <!-- <el-switch
          class="source-used fr"
          v-model="item.hasAuthorized"
          @change="sourceChange = true"
          :active-value="1"
          :inactive-value="0"
        ></el-switch> -->
        <el-checkbox
          class="source-used fr"
          v-model="item.hasAuthorized"
          @change="sourceChange = true"
          :true-label="1"
          :false-label="0"
        ></el-checkbox>
      </li>
    </draggable>

    <span slot="footer" class="dialog-footer">
      <el-button @click="toggleSourceVisible(false)">取消</el-button>
      <el-button type="primary" @click="sourceConfirm">确定</el-button>
    </span>
  </el-dialog>
</template>
<script>
import draggable from 'vuedraggable'
export default {
  components: { draggable },
  props: {
    sourceVisible: Boolean,
    skillItem: Object,
    currentScene: Object,
    sourceConfig: Object,
    appId: String,
    isExtendSkill: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      sourceList: [],
      hasOtherSource: false,
      sourceChange: false,
      sourceChangeList: {}, // 存储修改过的信源数据
    }
  },
  watch: {
    sourceVisible(val) {
      if (val) {
        this.configSource(this.skillItem, this.isExtendSkill)
      }
    },
  },
  methods: {
    onClose() {
      this.toggleSourceVisible(false)
    },
    configSource(item, isExtendSkill) {
      let self = this
      this.configSkillItem = item
      this.sourceChange = false
      this.hasOtherSource = false
      this.sourceList = []

      let data = {
        appid: this.appId,
        sceneName: this.currentScene.sceneBoxName,
        business: item['name'],
        businessId: item.id,
      }
      if (isExtendSkill) {
        data.id = item.id
      }

      // if (data.business.indexOf('.')) {
      //   let nameArr = data.business.split('.')
      //   data.business = nameArr[nameArr.length - 1]
      // }
      // business字段
      // 1)以IFYLTEK.开头,取.后面值；如 IFLYTEK.train 传train
      // 2)非IFYLTEK.开头，传整体值：如 JLCHEN.test 传 JLCHEN.test
      if (data.business.startsWith('IFLYTEK.')) {
        let nameArr = data.business.split('.')
        data.business = nameArr[nameArr.length - 1]
      }

      // 如果技能修改过信源，则显示修改过的配置
      if (this.sourceConfig[this.configSkillItem.id]) {
        self.sourceList = this.sourceChangeList[this.configSkillItem.id]
        self.checkHasOtherSource()
        return
      }

      this.$utils.httpGet(
        this.$config.api.AIUI_APP_BUSINESS_SOURCE_CONFIG,
        data,
        {
          success: (res) => {
            if (res.flag) {
              self.sourceList = res.data
              self.checkHasOtherSource()
            }
          },
        }
      )
    },
    checkHasOtherSource() {
      this.hasOtherSource = this.sourceList.some(
        (item) => item.code !== 'iflytek'
      )
    },
    sourceConfirm() {
      if (!this.sourceChange) {
        this.toggleSourceVisible(false)
        return
      }

      this.sourceChangeList[this.configSkillItem.id] = this.sourceList
      let provider = []
      this.sourceList.forEach((item) => {
        if (item['hasAuthorized']) {
          provider.push(item.code)
        }
      })
      let skillName = this.configSkillItem['name']
      if (skillName.startsWith('IFLYTEK.')) {
        let nameArr = skillName.split('.')
        skillName = nameArr[nameArr.length - 1]
      }

      // let arr = this.configSkillItem['name'].split('.')
      let data = {
        skillName,
        type: 'open',
        business: this.sourceList[0]['businessCode'],
      }
      if (provider.length) {
        data.providers = provider.join(',')
      } else {
        data.type = 'close'
        data.providers = this.sourceList[0].code
      }

      this.sourceConfig[this.configSkillItem.id] = data
      this.$emit('change')
      this.toggleSourceVisible(false)
    },
    toggleSourceVisible(val) {
      this.$emit('sourceVisibleChange', val)
    },
  },
}
</script>
<style lang="scss" scoped>
@import './style.scss';
</style>
