<template>
  <el-dialog
    class="add-category-dialog"
    :visible.sync="dialog.show"
    :close-on-click-modal="true"
    width="470px"
  >
    <a-tabs
      :activeKey="activeKey"
      :tab-position="mode"
      type="line"
      :style="{ height: '500px' }"
      @prevClick="callback"
      @nextClick="callback"
      @change="tabClick"
    >
      <a-tab-pane
        class="a-tab-pane"
        :forceRender="true"
        v-if="
          dialog !== undefined &&
          dialog.list !== undefined &&
          dialog.list.children !== undefined
        "
        v-for="(item, index) in dialog.list.children"
        :key="index"
        :tab="`${item.contentName}`"
      >
        <!--Content of tab {{ item.contentName }} -->
        <div class="div-tab-catg-out">
          <div
            class="div-tab-catg"
            :class="{ 'div-tab-catg-inline': category.type === -1 }"
            :id="Math.random()"
            v-if="categoryList !== undefined"
            v-for="(category, index_i) in categoryList"
            :key="index_i"
          >
            <ul v-if="category.type === -1">
              <li>
                <img :src="category.cover" />
              </li>
              <li :title="category.albumName">
                <p>{{ category.albumName }}</p>
              </li>
            </ul>
            <ul v-else class="ul-catg">
              <li>
                <p>{{ category.contentName }}</p>
              </li>
              <li>
                <ul
                  v-if="
                    category !== undefined && category.children !== undefined
                  "
                  v-for="(album, index_a) in category.children"
                  :key="index_a"
                >
                  <li>
                    <img :src="album.cover" />
                  </li>
                  <li :title="album.albumName">
                    <p>{{ album.albumName }}</p>
                  </li>
                </ul>
              </li>
              <li></li>
            </ul>
          </div>
        </div>
      </a-tab-pane>
    </a-tabs>
  </el-dialog>
</template>

<script>
import Tabs from 'ant-design-vue/lib/tabs'
import 'ant-design-vue/lib/tabs/style/css'
export default {
  name: 'previewDialog',
  props: {
    dialog: {
      type: Object,
      default: {
        show: false,
        list: {
          children: [],
        },
      },
    },
  },
  data() {
    return {
      mode: 'top',
      n: 0,
      count: 0,
      activeKey: 0,
      tmpData: [],
      categoryListTmp: [],
      categoryList: [],
      firstCategory: {},
      sceneList: [],
    }
  },
  watch: {
    'dialog.show': function () {
      if (this.dialog.show === true) {
        /* this.$emit('getSceneList', (sceneList) => {
              this.sceneList = sceneList
              this.getAlbumOrSongById(this.sceneList[0])
            })*/
        this.firstCategory = this.dialog.list.children[0]
        this.getAlbumOrSongById(this.firstCategory)
      }
      this.activeKey = 0
      ;(this.n = 0),
        (this.count = 0),
        (this.tmpData = []),
        (this.categoryListTmp = []),
        (this.categoryList = []),
        (this.sceneList = []),
        (this.firstCategory = {})
    },
  },
  computed: {
    appId() {
      return this.$route.params.appId
    },
  },
  methods: {
    tabClick(e) {
      this.activeKey = e
      ;(this.n = 0),
        (this.count = 0),
        (this.tmpData = []),
        (this.categoryListTmp = []),
        (this.categoryList = []),
        (this.firstCategory = {})
      this.firstCategory = this.dialog.list.children[e]
      this.getAlbumOrSongById(this.firstCategory)
    },
    callback(val) {},
    getAlbumOrSongById(category, index) {
      let thiz = this
      //thiz.count ++
      this.$utils.httpGet(
        this.$config.api.RECOMMEND_CONTENT_GET_ALBUM,
        {
          appid: thiz.appId,
          contentTypeId: category.id,
          type: category.type, // -1专辑，-2单曲 1一级分类，2二级分类 参考2.4
          page: 1,
          size: 99999,
        },
        {
          success: (res) => {
            if (res.flag) {
              if (thiz.count === 0) {
                thiz.tmpData = res.data.list
                thiz.categoryListTmp = res.data.list
              }
              if (category.type === 2 && index !== undefined) {
                thiz.categoryListTmp[index].children = res.data.list
                thiz.n++
              }
              /*if(thiz.tmpData.splice(0, 1).type === 2) {
                  thiz.getAlbumOrSongById(item, index)
                }*/
              if (thiz.count === 0) {
                thiz.tmpData.forEach((item, index) => {
                  if (item.type === 2 && item.childrenNum > 0) {
                    thiz.count++
                  }
                })
                thiz.tmpData.forEach((item, index) => {
                  if (item.type === 2 && item.childrenNum > 0) {
                    thiz.getAlbumOrSongById(item, index)
                  }
                })
              }
              if (thiz.count === thiz.n) {
                this.categoryList = this.categoryListTmp
                thiz.$forceUpdate()
              }
              //thiz.categoryList = res.data.list
            } else {
              this.$message.error(res.desc)
            }
          },
          error: (err) => {},
        }
      )
    },
  },
  components: {
    [Tabs.name]: Tabs,
  },
}
</script>
<style>
.ant-tabs-content {
  overflow-y: auto !important;
  overflow-x: hidden !important;
  max-height: 82%;
  line-height: 1em;
  position: relative !important;
  margin-left: unset !important;
  display: block !important;
}
.ant-tabs-content::-webkit-scrollbar {
  -webkit-appearance: none;
}

.ant-tabs-content::-webkit-scrollbar:vertical {
  width: 9px;
}

.ant-tabs-content::-webkit-scrollbar:horizontal {
  height: 9px;
}

.ant-tabs-content::-webkit-scrollbar-thumb {
  border-radius: 8px;
  border: 2px solid white; /* should match background, can't be transparent */
  background-color: rgba(0, 0, 0, 0.5);
}
</style>

<style scoped lang="scss">
.div-tab-catg-out {
}
.div-tab-catg {
  position: relative;
  width: 100%;
  line-height: 2em;
  &-inline {
    display: inline-flex;
    float: left;
    width: 30%;
    > ul {
      > li:nth-child(2) {
        top: -1em;
      }
    }
  }
  > ul {
    > li {
      width: 100%;
      text-align: center;
      > img {
        width: 80%;
        border-radius: 2%;
      }
    }
    > li:nth-child(2) {
      display: inline-block;
      position: relative;
      width: 100% !important;
      > p {
        width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        position: absolute;
        margin: 0 auto;
        text-align: center;
        align-items: center;
        align-self: center;
        justify-content: center;
        align-content: center;
      }
    }
  }
  .ul-catg {
    > li:nth-of-type(1) {
      text-align: left;
      line-height: normal;
      font-size: 15px;
      > p {
        font-weight: 700;
      }
    }
    > li {
      text-align: initial;
      > ul {
        width: 30%;
        display: inline-block;
        text-align: initial;
        > li {
          width: 100%;
          text-align: center;
          > img {
            width: 80%;
            border-radius: 2%;
          }
          > p {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            margin-bottom: unset;
            line-height: initial;
            margin-top: 1em;
          }
        }
      }
    }
  }
}
</style>
