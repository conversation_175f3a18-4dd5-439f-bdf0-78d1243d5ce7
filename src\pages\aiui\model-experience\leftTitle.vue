<template>
  <div class="card card-top">
    <div class="box">
      <div class="title">星火交互认知大模型</div>
      星火交互认知大模型是传统语义交互方式的升级，结合大模型强大的泛化理解与回复生成能力，解决了传统语义交互方式多轮效果弱、闲聊能力差等问题，同时耦合了传统的语义系统，保留了传统语义系统的信源内容等优势。广泛适用于电视、投影仪、儿童教育硬件等智能硬件的交互能力升级，带来更类人自然的交互体验。
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {}
  },
}
</script>
<style scoped lang="scss">
.card {
  position: relative;
}

.card-top {
  height: auto;
  padding: 10px 30px;
  box-sizing: border-box;
}

.box {
  width: 100%;
  // min-height: 180px;
  font-size: 14px;
  font-family: PingFang SC, PingFang SC-Medium;
  font-weight: 500;
  color: #262626;
  line-height: 30px;
  padding: 20px 14px 8px 14px;
  background: #f9fcff;
  border: 1px solid #ffffff;
  border-radius: 4px;
  box-shadow: 0px 0px 23.75px 0px #d3d9e3;
  margin-top: 22px;
  position: relative;
  background: url(~@A/images/model-exeperience/title-bak.png) right bottom /
    contain no-repeat;

  .title {
    position: absolute;
    left: -10px;
    top: -20px;
    width: 214px;
    height: 44px;
    line-height: 34px;
    font-size: 18px;
    font-family: PingFang SC, PingFang SC-Heavy;
    font-weight: 800;
    text-align: center;
    color: #262626;
    background: url(~@A/images/model-exeperience/title1.png) center no-repeat;
    background-size: cover;
  }
}

// @media screen and (max-width: 1601px) {

//   .card-top {
//     height: calc(50% - 5px);
//   }
// }
@media screen and (max-height: 800px) {
  .box {
    font-size: 13px;
    line-height: 24px;
  }
}
</style>
