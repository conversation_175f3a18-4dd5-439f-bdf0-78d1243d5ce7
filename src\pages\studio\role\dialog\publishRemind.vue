<template>
  <el-dialog 
    :title="`温馨提醒`" 
    :visible.sync="dialog.show" 
    width="680px"
  >
    <div class="note-msg">
      <span>"{{ roleInfo.name }}"</span>已被以下应用的情景模式使用。
    </div>
    <div class="tips">
      <i class="ic-r-exclamation"></i>配置将同步至情景模式的沙盒环境~ 如需生产环境同步更改，请确认同步后到“我的应用”点击构建发布哦！
    </div>
    <el-table
      ref="multipleTable"
      :data="roleApps"
      style="width: 100%"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        prop="appName"
        label="应用名称"
      >
      </el-table-column>
      <el-table-column
        prop="appid"
        label="appid"
      >
      </el-table-column>
      <el-table-column
        prop="sceneName"
        label="情景模式">
        <template slot-scope="scope">
          <span>{{ scope.row.sceneName.slice(0,-4) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        type="selection"
        width="55">
      </el-table-column>
    </el-table>
    <span slot="footer" class="dialog-footer">
      <el-button @click="publishWithNoApp"> 不了，我自己去应用修改发音人 </el-button>
      <el-button class="dialog-btn" type="primary" @click="save">
        确定
      </el-button>
    </span>
  </el-dialog>
</template>
<script>
export default {
  name:'',
  data() {
    return {
      selectApps:[]
    }
  },
  props:{
    dialog: {
      type: Object,
      default: () => ({
        show: false,
      }),
    },
    roleInfo:{
      type: Object,
      default: () => ({}),
    },
    roleApps: {
      type: Array,
      default: () => [],
    },
  },
  methods:{
    handleSelectionChange(selection) {
      console.log('selectionChange', selection);
      this.selectApps = selection;
    },
    publishWithNoApp(){
      let publishData = {
        roleId: this.roleInfo.id,
      }
      this.$emit('publish', publishData, true);
      this.dialog.show = false;
    },
    save(){
      let publishData = {
        roleId: this.roleInfo.id,
        configs: this.selectApps.map(item => {
          return {
            appid: item.appid,
            sceneName: item.sceneName,
          }
        })
      }
      this.$emit('publish', publishData, true);
      this.dialog.show = false;
    }
  },
}
</script>
<style lang="scss" scoped>
.note-msg{
  font-size: 13px;
  color: #323232;
}
.tips{
  padding: 8px;
  background: #fff2ea;
  font-size: 12px;
  margin: 15px 0;
  border-radius: 6px;
  padding: 5px 10px;
  .ic-r-exclamation{
    font-size: 16px;
    margin-right: 4px;
    color: #FF7D00;
  }
}
</style>
