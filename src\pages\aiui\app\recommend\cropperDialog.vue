<template>
    <div>
      <el-dialog title="图片剪裁" :visible.sync="dialog.show" :append-to-body="true">
        <div class="cropper-content">
          <div class="cropper" style="text-align:center">
            <vueCropper
              ref="cropper"
              :img="option.img"
              :outputSize="option.outputSize"
              :outputType="option.outputType"
              :info="option.info"
              :canScale="option.canScale"
              :autoCrop="option.autoCrop"
              :autoCropWidth="option.autoCropWidth"
              :autoCropHeight="option.autoCropHeight"
              :fixed="option.fixed"
              :fixedBox="option.fixedBox"
              :fixedNumber="option.fixedNumber"
            ></vueCropper>
          </div>
        </div>
        <div slot="footer" class="dialog-footer">
          <el-button @click="doCancel">取 消</el-button>
          <el-button type="primary" @click="finish" :loading="loading">确认</el-button>
        </div>
      </el-dialog>
    </div>
</template>

<script>
  import {VueCropper} from 'vue-cropper'
    export default {
        name: "cropperDialog",
      components: {
        VueCropper
      },
      props: {
        dialog: {
          type: Object,
          default: {
            show: false
          }
        },
      },
      data() {
          return {
            option: {
              img: '', // 裁剪图片的地址
              info: true, // 裁剪框的大小信息
              outputSize: 0.8, // 裁剪生成图片的质量
              outputType: 'png', // 裁剪生成图片的格式
              canScale: true, // 图片是否允许滚轮缩放
              autoCrop: true, // 是否默认生成截图框
              autoCropWidth: 200, // 默认生成截图框宽度
              autoCropHeight: 200, // 默认生成截图框高度
              fixedBox: true, // 固定截图框大小 不允许改变
              fixed: true, // 是否开启截图框宽高固定比例
              fixedNumber: [1, 1], // 截图框的宽高比例
              full: true, // 是否输出原图比例的截图
              canMoveBox: false, // 截图框能否拖动
              original: false, // 上传图片按照原始比例渲染
              centerBox: false, // 截图框是否被限制在图片里面
              infoTrue: true, // true 为展示真实输出图片宽高 false 展示看到的截图框宽高
              canMove:true,
            },
            loading: false,
            uploadFile: File,
            imageUrl: null
          }
      },
      watch: {
        'dialog.show': function () {
          this.option.img = this.dialog.img
        }
      },
      computed: {
        appId() {
          return this.$route.params.appId
        }
      },
      methods: {
        // base-64格式转换方法
        getBase64 (file) {
          return new Promise(function (resolve, reject) {
            let reader = new FileReader()
            let imgResult = ''
            reader.readAsDataURL(file)
            reader.onload = function () {
              imgResult = reader.result
            }
            reader.onerror = function (error) {
              reject(error)
            }
            reader.onloadend = function () {
              resolve(imgResult)
            }
          })
        },
        doCancel() {
          this.loading = false
          this.dialog.show = false
        },
        finish() {
          let thiz = this
          this.$refs.cropper.getCropBlob((data) => {
            let aTime = new Date().getTime() // 取时间戳，给文件命名
            let fileName = aTime + '.' + data.type.substr(6) // 给文件名加后缀
            let file = new window.File([data], fileName, { type: data.type }) // blob转file
            thiz.uploadFile = file;
            thiz.loading = true
            thiz.customRequest();
          })
        },
        customRequest () {
          let formData = new FormData(), thiz = this;
          formData.append("file", this.uploadFile);
          this.$utils.httpPost(this.$config.api.RECOMMEND_CONTENT_ALBUMUPLOAD + '?appid=' + this.appId, formData,{
            config: {
              headers: {
              },
              //responseType: "blob"
            },
            success: res => {
              if (res.flag) {
                thiz.loading = false
                thiz.dialog.show = false
                thiz.imageUrl = res.data.url
                thiz.$emit('updateEdit', thiz.imageUrl)
              } else {
                this.$message.error(res.data)
              }
            },
            error: err => {

            }
          })

        },
      }
    }
</script>

<style>
  .ant-upload-select-picture-card i {
    font-size: 32px;
    color: #999;
  }

  .ant-upload-select-picture-card .ant-upload-text {
    margin-top: 8px;
    color: #666;
  }

  .avatar-uploader{
    background:red!important;
    width:150px;height:150px;
    text-align: center;
    line-height: 150px;
  }
  .el-icon-plus{
    width:150px;height:150px;font-size:30px;
  }
  .cropper-content{
    width:500px;height:500px;background: pink;
  }
  .cropper{
    width:500px;
    height:500px;
    background: yellow;
  }
</style>
<style scoped>

</style>
