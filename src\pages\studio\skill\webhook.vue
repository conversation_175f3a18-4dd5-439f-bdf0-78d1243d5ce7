<template>
  <el-form
    size="medium"
    label-width="90px"
    label-position="left"
    :model="form"
    :rules="rules"
    ref="form">
    <el-form-item
      label="回调地址"
      prop="url">
      <el-input
        v-model="form.url"
        :disabled="!subAccountEditable"
        placeholder="请填写回调地址的URL，仅支持HTTPS">
        <template slot="prepend">https://</template>
      </el-input>
    </el-form-item>
    <el-form-item
      label="公私钥对"
      prop="publicKey">
      <p class="public-key">publicKey</p>
      <div class="key-wrap">
        <p>{{ form.publicKey.substr(0, 26) }}</p>
        <p class="mgr16">{{ form.publicKey.substr(27, 22) }} ...... {{form.publicKey.substr(-48, 23) }}</p>
        <p>{{ form.publicKey.substr(-24) }}</p>
        <p class="btn-copy" @click="copy(form.publicKey)">复制</p>
      </div>
      <p class="public-key">secretKey</p>
      <div class="key-wrap">
        <p>{{ form.privateKey.substr(0, 31) }}</p>
        <p class="mgr16">{{ form.privateKey.substr(32, 22) }} ...... {{form.privateKey.substr(-52, 22) }}</p>
        <p>{{ form.privateKey.substr(-29) }}</p>
        <p class="btn-copy" @click="copy(form.privateKey)">复制</p>
      </div>
      <el-button size="small" style="width: 90px; min-width:unset;" @click="generateKey"
        :disabled="!subAccountEditable">重新生成</el-button>
    </el-form-item>
    <el-form-item
      label="加密算法"
      prop="encryptType">
      {{ form.encryptType || 'RSA1024' }}
    </el-form-item>
    <el-form-item v-if="subAccountEditable">
      <el-button
        type="primary"
        style="margin-right: 40px;"
        size="small"
        @click="submit('form')">保存</el-button><a @click="cancel">放弃修改</a>
    </el-form-item>
  </el-form>
</template>
<script>
export default {
  props: {
    businessId: {
      required: true
    },
    subAccountEditable: Boolean
  },
  data() {
    return {
      form: {
        id: '',
        url: '',
        publicKey: '',
        privateKey: '',
        encryptType: ''
      },
      oldForm: {
        id: '',
        url: '',
        publicKey: '',
        privateKey: '',
        encryptType: ''
      },
      rules: {
        'url': [{required: true, message: '回调地址不能为空', trigger: 'blur'},
          { pattern: /^(([A-Za-z0-9-~]+).)+([A-Za-z0-9-~\/])+$/, message: 'URL格式不正确', trigger: 'blur'}],
      }
    }
  },
  created() {
    this.getInfo()
  },
  methods: {
    getInfo() {
      let self = this
      let data = {
        businessId: self.businessId
      }
      self.$utils.httpGet(self.$config.api.STUDIO_PROCESS_WEBHOOK_INFO, data, {
        success: res => {
          if(res.data.url) {
            res.data.url = res.data.url.substr(8)
          }
          self.form = res.data
          self.oldForm = JSON.parse(JSON.stringify(res.data))
        },
        error: err => {
        }
      })
    },
    copy(value) {
      this.$utils.copyClipboard(value)
    },
    generateKey() {
      let self = this
      this.$confirm('重新生成保存后，旧的公私钥将失效。', '你是否确定重新生成公私钥对？', {
        confirmButtonText: '重新生成',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        self.$utils.httpPost(self.$config.api.STUDIO_PROCESS_WEBHOOK_GENERATE_KEY, {
          businessId: self.businessId
        }, {
          success: res => {
            self.form.publicKey = res.data.RSAPublicKey
            self.form.privateKey = res.data.RSAPrivateKey
          }
        })
      }).catch(() => {
      })
    },
    submit(formName) {
      let self = this
      let data = {
        businessId: self.businessId,
        id: self.form.id || '',
        url: 'https://' + self.form.url,
        publicKey: self.form.publicKey,
        privateKey: self.form.privateKey,
        encryptType: self.form.encryptType || 'RSA1024',
      }
      self.$refs[formName].validate(valid => {
        if (!valid) {
          return
        }
        self.$utils.httpPost(self.$config.api.STUDIO_PROCESS_WEBHOOK_SAVE, data, {
          success: res => {
            self.$message.success('保存成功')
            self.getInfo()
          },
          error: err => {
          }
        })
      })
    },
    cancel() {
      this.getInfo()
    }
  }
}
</script>
<style lang="scss" scoped>
.public-key {
  color: $grey5;
  font-weight: 600px;
  margin-right: 25px;
  line-height: 22px;
}
.key-wrap {
  p {
    line-height: 22px;
  }
  .btn-copy {
    margin-bottom: 16px;
    color: $primary;
    cursor: pointer;
  }
}
</style>
