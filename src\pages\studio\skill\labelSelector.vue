<template>
  <div v-if="false">
    <p
      class="intro-words"
      :title="`${title}支持动作啦，点击下面的名称即可插入，快来试试吧！`"
    >
      {{ title }}支持动作啦，点击下面的名称即可插入，快来试试吧！
    </p>
    <div class="selector-container">
      <div
        ref="containerRef"
        :class="['container-left', { collapse: collapse, expand: !collapse }]"
      >
        <ul ref="labelListRef">
          <li
            v-for="(item, index) in labels"
            :key="index"
            @mousedown.prevent="onSelect(item)"
          >
            <span :title="item.zhName">{{ item.zhName }}</span>
            <img
              :src="
                item.picture ||
                'https://aiui-file.cn-bj.ufileos.com/avatar/default.png'
              "
            />
          </li>
        </ul>
      </div>
      <div class="container-right" v-if="showCollapse">
        <i
          :class="[
            'icon-toggle',
            { 'el-icon-arrow-down': collapse, 'el-icon-arrow-up': !collapse },
          ]"
          @mousedown.prevent="toggleCollapse"
        ></i>
      </div>
    </div>
  </div>
</template>
<script>
import { mapGetters } from 'vuex'
import { debounce } from 'lodash-es'

export default {
  props: {
    title: String,
  },
  data() {
    return {
      collapse: true,
      labels: [],
      showCollapse: false,
    }
  },
  created() {
    this.getCanuseLabels()
  },
  mounted() {
    window.addEventListener('resize', debounce(this.onResize, 100))
  },
  watch: {
    rightTestOpen(val) {
      setTimeout(() => {
        this.judgeShowCollapseOrNot()
      }, 300)
    },
  },
  beforeDestroyed() {
    window.removeEventListener('resize', debounce(this.onResize, 100))
  },
  computed: {
    ...mapGetters({
      rightTestOpen: 'studioSkill/rightTestOpen',
    }),
  },
  methods: {
    getCanuseLabels() {
      this.$utils.httpGet(
        this.$config.api.STUDIO_CANUSE_LABELS,
        {
          // pageIndex: page || this.tableData.page,
          pageIndex: 1,
          pageSize: 1000,
        },
        {
          success: (res) => {
            if (res.flag) {
              this.labels = res.data.labels || []
              this.$nextTick(() => {
                this.judgeShowCollapseOrNot()
              })
            }
          },
          error: (err) => {
            // this.tableData.loading = false
            console.log('page=>>')
            console.log(err)
          },
        }
      )
    },
    judgeShowCollapseOrNot() {
      try {
        const rect = this.$refs.labelListRef.getBoundingClientRect()
        if (rect.height > 37) {
          this.showCollapse = true
        } else {
          this.showCollapse = false
        }
      } catch (e) {
        console.log('judgeShowCollapseOrNot exception catched', e)
      }
    },
    toggleCollapse() {
      this.collapse = !this.collapse
      if (this.collapse) {
        this.$refs.containerRef.scrollTop = 0
      }
    },
    onSelect(selector) {
      this.$emit('select', selector)
    },
    onResize() {
      this.judgeShowCollapseOrNot()
    },
  },
}
</script>
<style lang="scss" scoped>
.intro-words {
  color: #8c8c8c;
  font-size: 14px;
  margin-bottom: 5px;
  height: 40px;
  line-height: 40px;
  background: #f4f5f5;
  padding-left: 16px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.selector-container {
  display: flex;

  .container-left {
    width: calc(100% - 50px);
    ul {
      display: flex;
      flex-wrap: wrap;
      li {
        cursor: pointer;
        margin-bottom: 5px;
        // width: 80px;
        padding: 0 6px;
        height: 32px;
        border: 1px solid #ddd;
        border-radius: 2px;
        display: flex;
        align-items: center;
        justify-content: center;
        &:hover {
          border: 1px solid $primary;
          background: #edf5ff;
        }
        img {
          width: 20px;
          height: 20px;
          margin-left: 5px;
        }
        span {
          display: inline-block;
          max-width: 68px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          color: #666;
        }
      }
      li:first-child {
        margin-right: 5px;
      }
      li + li {
        margin-right: 5px;
      }
    }
  }
  .collapse {
    max-height: 37px;
    overflow: hidden;
  }
  .expand {
    max-height: 120px;
    overflow: auto;
  }
  .container-right {
    width: 50px;
    padding-top: 10px;
    .icon-toggle {
      font-size: 18px;
      cursor: pointer;
    }
  }
}
</style>
