<template>
  <div class="application-header header2">
    <div class="application-header__left">
      <scene-import
        :appId="appId"
        :appInfo="app"
        :currentScene="currentScene"
      ></scene-import>
    </div>
    <div>
      <template v-if="app.check">
        <a
          @click="versionDialog.show = true"
          style="margin-left: 10px; font-size: 14px"
          >版本管理</a
        >
        <el-button
          type="primary"
          size="small"
          @click="publishDialog.show = true"
          style="margin-left: 10px"
          >发布</el-button
        >
      </template>
      <template v-else>
        <el-button
          type="primary"
          size="small"
          @click="auditDialog.show = true"
          style="margin-left: 10px"
          >审核上线</el-button
        >
      </template>
    </div>
    <publish-dialog
      :dialog="publishDialog"
      :subAccountEditable="subAccountEditable"
      :subAccount="subAccount"
    />
    <version-dialog
      :dialog="versionDialog"
      :subAccountEditable="subAccountEditable"
    />
    <audit-dialog
      :dialog="auditDialog"
      :subAccountEditable="subAccountEditable"
      :subAccount="subAccount"
    />
  </div>
</template>
<script>
import { mapGetters } from 'vuex'

import sceneImport from '../../sceneImport'

import PublishDialog from '../../../sandbox/publish.vue'
import VersionDialog from '../../../sandbox/version.vue'
import AuditDialog from '../../../audit.vue'
export default {
  data() {
    return {
      publishDialog: {
        show: false,
      },
      versionDialog: {
        show: false,
      },
      auditDialog: {
        show: false,
      },
      sceneList: [],
    }
  },
  computed: {
    ...mapGetters({
      currentScene: 'aiuiApp/currentScene',
      app: 'aiuiApp/app',
      subAccount: 'user/subAccount',
      subAccountEditable: 'aiuiApp/subAccountEditable',
    }),
    appId() {
      return this.$route.params.appId
    },
  },
  methods: {},
  components: {
    sceneImport,
    PublishDialog,
    VersionDialog,
    AuditDialog,
  },
}
</script>
<style lang="scss" scoped>
.application-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 35px;
  padding: 0 20px;
  &__left {
    display: flex;
    align-items: center;
  }
  &__right {
    display: flex;
    align-items: center;
  }
  .back-icon {
    margin-right: 14px;
  }

  .config-title {
    font-size: 20px;
    font-weight: 500;
    color: #000000;
    line-height: 20px;
    margin-right: 14px;
  }

  .appid {
    font-size: 14px;
    font-weight: 400;
    color: #8d8d99;
    line-height: 20px;
  }

  .appkey {
    font-size: 14px;
    font-weight: 400;
    color: $primary;
    line-height: 20px;
    margin-left: 12px;
    cursor: pointer;
  }

  .appname {
    font-size: 14px;
    font-weight: 600;
    color: #17171e;
    line-height: 20px;
    margin: 0 12px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    max-width: 120px;
  }
}

.header2 {
  // border-top: 1px solid #f4f4f4;
  height: 64px;
  border-bottom: 1px solid $grey007;
}
</style>
