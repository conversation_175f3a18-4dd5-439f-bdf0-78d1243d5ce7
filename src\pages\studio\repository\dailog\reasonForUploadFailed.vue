<template>
  <el-dialog
    title="错误提示"
    :visible.sync="dialog.show"
    :close-on-click-modal="false"
    width="760px"
  >
    <p v-for="(item, index) in uploadFailedTip" :key="index" class="mgb12">
      {{item.desc}}
    </p>
    <span slot="footer" class="dialog-footer">
      <el-button class="dialog-btn"
        type="primary"
        style="min-width: 104px;" @click="dialog.show = false">知道了
      </el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: 'reason-for-upload-failed',
  props: {
    dialog: {
      type: Object,
      default: {}
    },
    uploadFailedTip: {
      type: Array,
      default: []
    }
  }
}
</script>