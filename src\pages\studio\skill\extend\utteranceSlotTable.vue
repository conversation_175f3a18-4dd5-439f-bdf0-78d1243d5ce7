<template>
  <os-table
    ref="utteranceSlotTable"
    class="utterance-slot-table"
    :border="true"
    :tableData="tableData"
    @del="delSlot"
  >
    <el-table-column label="槽位类型" width="120">
      <template slot-scope="scope">
        <el-button
          size="mini"
          style="min-width: 72px"
          :disabled="!subAccountEditable"
          @click="changeSlotType(scope.row)"
          >{{ scope.row.slotType === 0 ? '实体' : '辅助词'
          }}<i class="ic-r-exchange el-icon--right"></i
        ></el-button>
      </template>
    </el-table-column>
    <el-table-column label="对应短语" width="90">
      <template slot-scope="scope">
        {{ scope.row.text || '-' }}
      </template>
    </el-table-column>
    <el-table-column label="对应实体/辅助词" width="160">
      <template slot-scope="scope">
        <span
          v-if="scope.row.entityName"
          :style="utteranceColor[scope.row.slotName]"
          @click.stop="openSelectEntity(scope.row, $event)"
        >
          {{ scope.row.slotType === 0 ? '@' : '#' }}{{ scope.row.entityName }}
        </span>
        <a v-else @click.stop="openSelectEntity(scope.row, $event)">{{
          scope.row.slotType === 0 ? '设置对应实体' : '设置对应辅助词'
        }}</a>
      </template>
    </el-table-column>
    <el-table-column label="槽位标识" width="120">
      <template slot-scope="scope">
        <div
          class="utterance-slot-table-slotname"
          @keyup.enter="changeSlotNameBlur"
        >
          <span v-if="subAccountEditable">{{ scope.row.slotName }}</span>
          <el-input
            v-else
            class="search-area"
            size="medium"
            placeholder="请输入槽位标识"
            v-model="scope.row.slotName"
            @blur="changeSlotName(scope.row, scope.$index)"
          >
          </el-input>
        </div>
      </template>
    </el-table-column>
    <el-table-column width="96">
      <template slot="header" slot-scope="scope">
        <os-table-qahead
          label="表述必须"
          class="tabel-qahead"
          tip="表述必须指的是当该槽位存在时，用户的语句才是一句完整通顺的表述。例如「请问今天合肥几点日出」中，「请问」和「今天」为非表述必须，因为当其缺失时，「合肥几点日出」依旧是完整通顺的一句表述。"
        />
      </template>
      <template slot-scope="scope">
        <el-checkbox
          v-model="scope.row.optional"
          :true-label="1"
          :false-label="0"
          :disabled="!scope.row.entityName || !subAccountEditable"
          :change="necessaryChange(scope.row, scope.$index)"
        />
      </template>
    </el-table-column>
  </os-table>
</template>

<script>
export default {
  props: {
    utterance: {
      type: Object,
      default: {},
    },
    currentUtterId: [String, Number],
    subAccountEditable: Boolean,
  },
  data() {
    return {
      tableData: {
        loading: true,
        total: 0,
        page: 1,
        size: 99,
        handles: ['del'],
        list: [],
      },
      oldSlotList: [],
      currentUtterFzyValue: '',
    }
  },
  computed: {
    utteranceColor() {
      return this.$store.state.studioSkill.utteranceColor
    },
    intention() {
      return this.$store.state.studioSkill.intention
    },
  },
  watch: {
    utterance: function (val, oldVal) {
      this.initTableData()
    },
    currentUtterId() {
      this.getCurrentUtterFzy()
    },
  },
  created() {
    this.initTableData()
  },
  methods: {
    getCurrentUtterFzy() {
      if (this.utterance.id !== this.currentUtterId) return
      if (!this.utterance.hasOwnProperty('slotData')) return
      let kv = this.utterance.slotData && JSON.parse(this.utterance.slotData)
      if (!kv.hasOwnProperty('Fzy')) return
      this.currentUtterFzyValue = kv['Fzy'][0].value
    },
    getSameSlotNameNum(val) {
      //检查当前语料中相同槽位标识的个数
      let tmp = []
      this.oldSlotList.forEach((item) => {
        if (item.slotName) {
          tmp.push(item.slotName)
        }
      })
      let num = 0
      num = tmp.reduce((a, v) => (v === val ? a + 1 : a), 0)
      return num
    },
    initTableData() {
      let self = this
      this.tableData.loading = false
      this.tableData.list = Array.prototype.filter.call(
        this.utterance.mark,
        function (item, index) {
          if (item.slotName) {
            return item
          }
        }
      )
      this.oldSlotList = JSON.parse(JSON.stringify(this.tableData.list))
    },
    openSelectEntity(item, event) {
      if (!this.subAccountEditable) return
      let data = {
        utterance: this.utterance,
        selectedText: item,
      }
      let rect = event.target.getBoundingClientRect()
      this.$store.dispatch('studioSkill/setUtteranceEntityPopover', {
        show: true,
        data: data,
        rect: {
          top: rect.top,
          left: rect.left,
          width: rect.width,
          y: rect.y,
        },
      })
    },
    necessaryChange(item, index) {
      if (this.oldSlotList[index].optional != item.optional) {
        let data = {
          markId: item.markId,
          businessId: this.intention.businessId,
          intentId: this.intention.id,
          slotName: item.slotName,
          slotType: item.slotType, // 0是实体，1是辅助词
          entityId: item.entityId,
          optional: item.optional,
        }
        this.editMark(data)
      }
    },
    delSlot(slot, index) {
      let self = this
      if (!self.subAccountEditable) return
      let marks = []
      let sort = 0
      let mainIndex = slot.sort
      let oldSlotName = this.oldSlotList[index].slotName
      this.getCurrentUtterFzy()
      let sameSlotNameNum = this.getSameSlotNameNum(oldSlotName)
      for (let i = 0; i < this.utterance.mark.length; i++) {
        if (i === mainIndex) {
          if (
            oldSlotName == self.currentUtterFzyValue &&
            sameSlotNameNum == 1
          ) {
            self.$message({
              type: 'warning',
              message: '请先删除语料中附加的Fzy类型KV',
            })
            return
          }
          if (mainIndex > 0 && !self.utterance.mark[mainIndex - 1].slotName) {
            marks[marks.length - 1] = {
              text:
                self.utterance.mark[mainIndex - 1].text +
                this.utterance.mark[i].text,
              sort: self.utterance.mark[mainIndex - 1].sort,
            }
          } else {
            marks.push({
              text: this.utterance.mark[i].text,
              sort: this.utterance.mark[i].sort,
            })
            sort++
          }
        } else if (i === mainIndex + 1 && !this.utterance.mark[i].slotName) {
          marks[marks.length - 1].text += this.utterance.mark[i].text
        } else {
          this.utterance.mark[i].sort = sort++
          marks.push(this.utterance.mark[i])
        }
      }
      let data = {
        template: this.utterance.template,
        id: this.utterance.id,
        utterance: this.utterance.utterance,
        businessId: this.intention.businessId,
        intentId: this.intention.id,
        mark: JSON.stringify(marks),
      }

      this.$utils.httpPost(
        this.$config.api.STUDIO_INTENT_UTTERANCE_MARKS_UPDATE,
        data,
        {
          success: (res) => {
            self.$message.success('删除成功')
            self.initSkillQuote()
            self.$emit('change')
          },
          error: (err) => {
            console.log('page=>>')
            console.log(err)
          },
        }
      )
    },
    changeSlotType(item) {
      let data = {
        markId: item.markId,
        businessId: this.intention.businessId,
        intentId: this.intention.id,
        slotName: item.slotName,
        slotType: item.slotType === 0 ? 1 : 0, // 0是实体，1是辅助词
        // entityId: item.id,
        // optional: this.selectedText.optional
      }
      this.editMark(data)
    },
    changeSlotNameBlur(event) {
      event.target.blur()
    },
    changeSlotName(item, index) {
      if (item.slotName === this.oldSlotList[index].slotName) {
        return
      }
      this.getCurrentUtterFzy()
      let sameSlotNameNum = this.getSameSlotNameNum(
        this.oldSlotList[index].slotName
      )
      if (
        this.oldSlotList[index].slotName == this.currentUtterFzyValue &&
        sameSlotNameNum == 1
      ) {
        this.$message({
          type: 'warning',
          message: '请先删除语料中附加的Fzy类型KV',
        })
        return
      }
      let data = {
        markId: item.markId,
        businessId: this.intention.businessId,
        intentId: this.intention.id,
        slotName: item.slotName,
        slotType: item.slotType, // 0是实体，1是辅助词
        entityId: item.entityId,
        optional: item.optional,
      }
      this.editMark(data)
    },
    editMark(data) {
      let self = this
      if (!data.slotName) {
        return self.$message.warning('槽位标识不能为空')
      }
      this.$utils.httpPost(
        this.$config.api.STUDIO_INTENT_UTTERANCE_MARK_UPDATE,
        data,
        {
          success: (res) => {
            self.$message.success('修改成功')
            self.initSkillQuote()
            self.$emit('change')
          },
          error: (err) => {
            self.$emit('change')
            // console.log('page=>>');
            // console.log(err);
          },
        }
      )
    },
    // 重置是否有 引用实体 和 引用辅助词
    initSkillQuote() {
      // this.$store.dispatch('studioSkill/initHasSkillQuote', 0)
      // this.$store.dispatch('studioSkill/initHasSkillQuote', 1)
    },
  },
  components: {},
}
</script>

<style lang="scss">
.utterance-slot-table {
  .el-table__body tr:hover > td {
    background-color: #fff;
  }
  td {
    padding: 0;
    height: 56px;
    line-height: 56px;
    transition: none !important;
  }
  &-slotname input {
    border: 0 !important;
    padding: 0;
  }
}
.el-tooltip__popper {
  max-width: 500px;
  line-height: 180%;
}
</style>
