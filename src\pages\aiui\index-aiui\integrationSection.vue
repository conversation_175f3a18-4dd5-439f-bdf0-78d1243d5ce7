<template>
  <section
    class="aiui-section section-integration"
    v-lazy:background-image="
      require('@A/images/aiui/main-page/img_process_bg.png')
    "
  >
    <div class="aiui-section-wrap">
      <div class="aiui-section-title">
        <span>接入流程</span>
      </div>
      <div class="aiui-section-desc">
        低代码快速集成体验&nbsp;&nbsp; |
        &nbsp;&nbsp;语音技能内容无缝对接&nbsp;&nbsp; |
        &nbsp;&nbsp;设备数据在线查看&nbsp;&nbsp; |
        &nbsp;&nbsp;云端效果优化即时生效
      </div>
      <div class="process-list">
        <ul class="step-list">
          <li>
            <div
              class="step-block"
              v-lazy:background-image="
                require('@A/images/aiui/main-page/img_outline_border.png')
              "
            >
              <span>1</span>
            </div>
            <p>创建应用</p>
          </li>
          <li>
            <div
              class="step-block"
              v-lazy:background-image="
                require('@A/images/aiui/main-page/img_outline_border.png')
              "
            >
              <span>2</span>
            </div>
            <p>集成AIUI</p>
          </li>
          <li>
            <div
              class="step-block"
              v-lazy:background-image="
                require('@A/images/aiui/main-page/img_outline_border.png')
              "
            >
              <span>3</span>
            </div>
            <p>体验测试</p>
          </li>
          <li>
            <div
              class="step-block"
              v-lazy:background-image="
                require('@A/images/aiui/main-page/img_outline_border.png')
              "
            >
              <span>4</span>
            </div>
            <p>效果优化</p>
          </li>
          <div class="divider"></div>
        </ul>
        <div class="dotted-line"></div>
      </div>
    </div>
  </section>
</template>
<script>
function getWindowHeight() {
  return 'innerHeight' in window
    ? window.innerHeight
    : document.documentElement.offsetHeight
}
export default {
  data() {
    return {}
  },
  mounted() {
    // this.adjustContent()
  },
  methods: {
    adjustContent() {
      // 设置高度
      let height = Math.max(getWindowHeight(), 500)

      Array.from(document.getElementsByClassName('section-app')).forEach(
        (item, index) => {
          item.style.height = `${height}px`
        }
      )
    },
  },
}
</script>
<style lang="scss" scoped>
@import './common.scss';

.section-integration {
  min-height: 587px;
  background: #fff center/cover no-repeat;
}
.process-list {
  .step-list {
    display: flex;
    justify-content: center;
    // margin-top: 216px;
    margin-top: 100px;
    position: relative;
    .divider {
      position: absolute;
      content: ' ';
      display: inline-block;

      z-index: 9;
      bottom: -26px;
      left: 50%;
      transform: translateX(-50%);

      height: 4px;
      width: 100%;
      max-width: 1200px;
      background-image: repeating-linear-gradient(
        90deg,
        #fff,
        #fff 4px,
        #999 0,
        #999 12px
      );
    }
    li {
      .step-block {
        width: 138px;
        text-align: center;
        height: 149px;
        background: center/contain no-repeat;
        font-size: 44px;
        font-weight: bold;
        color: #ffffff;
        line-height: 149px;
        span {
          font-family: Source Han Sans CN;
          margin-left: -4px;
        }
      }
      p {
        text-align: center;
        width: 80px;
        font-size: 18px;
        font-weight: 400;
        color: #333;
        margin: 20px auto 0;
      }
      position: relative;

      &::after {
        position: absolute;
        content: ' ';
        display: inline-block;
        width: 20px;
        height: 20px;
        border: 6px solid #333;
        border-radius: 100%;
        z-index: 10;
        bottom: -34px;
        left: 50%;
        transform: translateX(-50%);
        background: #fff;
      }
    }
    li + li {
      margin-left: 156px;
    }
  }

  .dotted-line {
    // width: 1327px;
    // height: 16px;
    // background: url(~@A/images/aiui/main-page/img_dotted_line.png);
  }
}
@media screen and (min-width: 1500px) {
  .process-list {
    margin-top: 216px;
  }
}
</style>
