<template>
  <div class="aiui_card">
    <h2 class="aiui_title">{{ pluginInfo.pluginName }}</h2>

    <div class="tags_container">
      <el-tag type="info" class="aiui_tag" size="mini">{{
        pluginInfo.pluginType == 21 ? 'AIUI智能体' : '三方智能体'
      }}</el-tag>
      <el-tag
        v-if="pluginInfo.templateTag"
        type="info"
        class="aiui_tag"
        size="mini"
        >{{ pluginInfo.templateTag }}</el-tag
      >
    </div>

    <el-tooltip
      :disabled="!isOverflow"
      :content="pluginInfo.pluginDesc"
      placement="top"
      popper-class="card_tooltip"
    >
      <div class="description" ref="pluginDescRef" @mouseenter="checkOverflow">
        {{ pluginInfo.pluginDesc }}
      </div>
    </el-tooltip>

    <div class="creation_date">创建于: {{ formattedDate }}</div>
  </div>
</template>

<script>
export default {
  name: 'AgentPopover',
  data() {
    return {
      isOverflow: false,
    }
  },
  props: {
    pluginInfo: {
      type: Object,
      default: () => ({}),
    },
  },
  computed: {
    formattedDate() {
      return this.$utils.dateFormat(
        this.pluginInfo.createTime,
        'yyyy年MM月dd日'
      )
    },
  },
  methods: {
    checkOverflow() {
      this.$nextTick(() => {
        const el = this.$refs.pluginDescRef
        if (el) {
          this.isOverflow = el.scrollHeight > el.clientHeight
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.aiui_card {
  // margin: 0 auto;
  .aiui_title {
    font-size: 16px;
    color: #000;
  }

  .tags_container {
    display: flex;
    gap: 10px;
    margin: 10px 0;
    .aiui_tag {
      font-size: 12px;
    }
  }
  .description {
    font-size: 12px;
    line-height: 1.6;
    margin-bottom: 10px;
    display: -webkit-box;
    -webkit-line-clamp: 2; /* 限制显示行数 */
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .creation_date {
    font-size: 12px;
    color: #999;
  }
}
</style>
