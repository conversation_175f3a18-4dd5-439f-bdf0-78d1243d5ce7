<template>
  <div class="app-container">
    <aiui-menu />
    <div class="os-container" style="flex-direction: column">
      <!-- <aiui-header></aiui-header> -->
      <div class="os-container">
        <div class="os-aside">
          <studio-qaBank-menu-head :type="3" />
          <div class="os-scroll os_scroll" style="height: calc(100% - 63px)">
            <el-popover placement="bottom" width="264" trigger="click">
              <select-qa :qaType="0" />
              <div slot="reference" class="studio-skill-menu-head-skill-name">
                <span :title="qa.name || '-'">{{ qa.name || '-' }}</span>
              </div>
            </el-popover>

            <el-menu class="os-menu" :default-openeds="openeds">
              <template v-for="menu in menus" :index="menu">
                <el-submenu
                  class="skill-menu"
                  v-if="menu.sub"
                  :key="menu.key"
                  :index="menu.key"
                >
                  <template slot="title">
                    <!-- <i :class="menu.icon"></i> -->
                    <!-- <svg-icon
                      :iconClass="menu.icon"
                      class="menu_icon"
                      :customStyle="{ width: menu.width, height: menu.height }"
                    /> -->
                    <span>{{ menu.value }}</span>
                  </template>
                  <el-menu-item-group>
                    <template v-for="submenu in menu.sub">
                      <el-menu-item
                        v-if="!submenu.disabled"
                        :key="submenu.key"
                        :index="submenu.key"
                        :class="[
                          'skill-sub-menu',
                          { 'os-menu-active': routeName === submenu.key },
                        ]"
                        @click="selectMenu(submenu)"
                      >
                        <template>
                          {{ submenu.value }}
                        </template>
                      </el-menu-item>
                    </template>
                  </el-menu-item-group>
                </el-submenu>
                <el-menu-item
                  v-else
                  :key="menu.index"
                  :index="menu.key"
                  :class="{ 'os-menu-active': routeName === menu.key }"
                  @click="selectMenu(menu)"
                >
                  <!-- <i :class="menu.icon"></i> -->
                  <!-- <svg-icon
                    :iconClass="menu.icon"
                    class="menu_icon"
                    :customStyle="{ width: menu.width, height: menu.height }"
                  /> -->
                  <span slot="title">{{ menu.value }}</span>
                </el-menu-item>
              </template>
            </el-menu>
            <!-- <a
              class="studio-skill-layout-to-docs"
              :href="DocsInUrl"
              target="_blank"
              >文档中心<i class="ic-r-link skill-left-bar"></i
            ></a> -->
          </div>
        </div>
        <div class="os-main">
          <template>
            <router-view
              :subAccount="subAccount"
              :limitCount="limitCount"
            ></router-view>
          </template>
        </div>
        <div
          class="os-side-right"
          :class="{ 'os-side-right-open': rightTestOpen }"
        >
          <right-test-close
            v-if="!rightTestOpen"
            :rightTestOpen="rightTestOpen"
            debugType="qa"
          ></right-test-close>
          <qa-debug v-show="rightTestOpen" debugType="keyqa" />
          <feedBackHover :rightTestOpen="rightTestOpen" />
        </div>
        <!-- 子账号登录后首次进入提示 -->
        <!-- <cooperate-warn-dialog
        :dialog="cooperateDialog"
        type="firstEnterSkill"
      ></cooperate-warn-dialog> -->
      </div>
    </div>
  </div>
</template>

<script>
import Vue from 'vue'
import SSO from 'sso/sso.js'
import dicts from '@M/dicts'
import { mapGetters } from 'vuex'
import Axios from 'axios'
import RightTestClose from '@C/rightTestClose'
import feedBackHover from '../components/feedBackHover'
import CooperateWarnDialog from '@C/cooperatWarnDialog'
import AiuiHeader from '../components/aiuiHeader'
import OsHeader from '../components/header'
import aiuiMenu from '../components/aiuiMenu'
import SelectQa from '@P/studio/qabank/selectQa.vue'

export default {
  data() {
    return {
      menus: [
        {
          key: 'keyqa-info',
          value: '基本信息',
          icon: 'basic-information',
          index: 0,
          width: '18px',
          height: '18px',
        },

        {
          key: 'keyQABank',
          value: '问答编辑',
          icon: 'qa-editor',
          index: 1,
          width: '18px',
          height: '16px',
        },
        {
          key: 'keyqa-publish',
          value: '发布',
          icon: 'release',
          index: 3,
          width: '17px',
          height: '17px',
        },
        {
          key: 'keyqa-version',
          value: '版本管理',
          icon: 'version-management',
          index: 5,
          width: '18px',
          height: '16px',
        },
      ],
      openeds: [],
      DocsInUrl: `${this.$config.docs}doc-65/`,
      cooperateDialog: {
        show: false,
      },
    }
  },
  computed: {
    ...mapGetters({
      qa: 'studioQa/qa',
      subAccount: 'user/subAccount',
      rightTestOpen: 'studioSkill/rightTestOpen',
      limitCount: 'aiuiApp/limitCount',
    }),
  },
  created() {
    let self = this
    if (self.$router.match(location.pathname)) {
      self.routeName = self.$router.match(location.pathname).name
    }

    // 判断是语句问答还是关键词问答
    // 关键词问答
    this.$store.dispatch('studioQa/setKeyQa', this.$route.params.repoId)

    this.$store.dispatch('studioSkill/setRightTestOpen', false)
    this.$store.dispatch('aiuiApp/setLimitCount')
    self.init()
  },
  mounted() {
    let self = this
    if (window.userDetailInfo && window.userDetailInfo.subAccount) {
      window.logout = function () {
        let data = {
          subSessionId: self.$utils.getCookie('subSessionId'),
          sub_account_id: self.$utils.getCookie('sub_account_id'),
        }
        self.$utils.httpPost(self.$config.api.COOP_SUB_ACCOUNT_LOGOUT, data, {
          success: (res) => {
            if (res.flag) {
              self.$message.success('退出成功')
              self.$router.push({ name: 'sub-login' })
              localStorage.removeItem('firstEnterSkill', '1')
              localStorage.removeItem('firstEnterEntity', '1')
              localStorage.removeItem('firstEnterAuxiliary', '1')
            }
          },
          error: (err) => {},
        })
      }
    } else {
      window.logout = function () {
        SSO.logout(function () {
          self.$utils.clearCookie()
          self.$store.dispatch('user/setUserInfo', null)
          self.$message.success('已退出登录')
          setTimeout(function () {
            window.location.href = '/index-studio'
          }, 1000)
        })
      }
    }
  },
  watch: {
    $route: function (to, from) {
      let self = this
      self.routeName = to.name
    },
    limitCount: function (val) {
      window.limitCount = val
      if (window.header && !window.header.limitCount) {
        window.header.limitCount = val
      }
    },
  },
  methods: {
    init() {},
    addIntent() {},
    selectMenu(menu) {
      let routeData = {}
      routeData.name = menu.key
      if (menu.params) {
        routeData.params = menu.params
      }
      this.$router.push(routeData)
    },

    //协同操作--start

    //协同操作--end
  },
  components: {
    OsHeader,
    AiuiHeader,
    RightTestClose,
    CooperateWarnDialog,
    feedBackHover,
    aiuiMenu,
    SelectQa,
  },
}
</script>

<style lang="scss">
.os-menu-add.skill-layout {
  position: absolute;
  right: 24px;
  top: 0;
  width: 50px;
  font-size: 14px !important;
  display: flex;
  color: $primary;
  i {
    font-size: 14px !important;
    color: $primary !important;
    flex: 1;
    margin: 0 !important;
  }
}
.skill-menu {
  .el-submenu__title {
    padding-left: 30px !important;
  }
  .skill-sub-menu.el-menu-item {
    padding-left: 78px !important;
    font-size: 14px;
  }
}

.studio-skill-layout-to-docs {
  display: block;
  margin: 24px;
  padding-top: 32px;
  border-top: 1px solid $grey2;
}
.ic-r-link.skill-left-bar {
  padding-left: 4px;
  vertical-align: -1px;
}
</style>
