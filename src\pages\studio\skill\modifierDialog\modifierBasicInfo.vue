<template>
  <el-form
    class="mgb20"
    :model="infoForm"
    ref="infoForm"
    label-width="100px"
    :rules="rules"
    label-position="left"
  >
    <el-form-item label="修饰语名称" prop="name">
      <template v-if="!edit && infoForm.name">
        <span class="modifier-name" :title="infoForm.name">{{
          infoForm.name
        }}</span>
        <div class="ib modifier-edit-btn" @click="toEdit">
          <i class="ic-r-edit" />
          <span>编辑</span>
        </div>
      </template>
      <div v-else @keyup.enter="editHandleBlur">
        <el-input
          v-model="infoForm.name"
          ref="nameInput"
          class="name-input"
          size="small"
          style="width: 400px"
          placeholder="只支持英文、数字、小数点格式，长度不超过20个字符"
          @blur="editHandle"
        />
        <input type="text" style="display: none" />
        <i class="btn-save el-icon-check" @click="$refs.infoForm.validate()" />
        <i
          class="btn-cancel el-icon-close"
          data-action="del"
          @mousedown="cancelEdit"
        />
      </div>
    </el-form-item>
    <el-form-item label="关联意图" class="form-item-intents">
      <span v-if="!infoForm.intentNames">-</span>
      <p class="mgr16" v-else>{{ infoForm.intentNames }}</p>
    </el-form-item>
  </el-form>
</template>

<script>
export default {
  name: 'modifier-basic-info',
  props: {
    businessId: String,
    dialog: {
      type: Object,
      default: () => ({
        show: false,
        modifierId: '',
      }),
    },
  },
  data() {
    return {
      edit: false,
      oldName: '',
      infoForm: {
        name: '',
        intentNames: '',
      },
      rules: {
        name: [
          this.$rules.required('修饰词名称不能为空'),
          this.$rules.lengthLimit(1, 20, '修饰词名称长度不能超过20个字符'),
          this.$rules.englishRegLimitV2(),
        ],
      },
    }
  },
  created() {
    this.infoForm = {
      name: '',
      intentNames: '',
    }
    if (!this.dialog.modifierId) {
      this.toEdit()
    }
    this.getDetail()
  },
  methods: {
    getDetail() {
      if (!this.dialog.modifierId) return
      this.infoForm.intentNames = ''
      this.$utils.httpPost(
        this.$config.api.STUDIO_MODIFIER_DETAIL,
        {
          businessId: this.businessId,
          modifierId: this.dialog.modifierId,
        },
        {
          success: (res) => {
            this.infoForm.name = res.data.name
            this.oldName = res.data.name
            let len = res.data.intents && res.data.intents.length
            if (!len) return
            let tmp = []
            for (let i = 0; i < len; i++) {
              tmp.push(res.data.intents[i].intentName)
            }
            this.infoForm.intentNames = tmp.join('|')
          },
          error: (err) => {},
        }
      )
    },
    cancelEdit() {
      this.edit = false
      this.infoForm.name = this.oldName
      this.$refs.infoForm && this.$refs.infoForm.clearValidate()
    },
    toEdit() {
      this.edit = true
      this.$nextTick(function () {
        this.$refs['nameInput'] && this.$refs['nameInput'].focus()
      })
    },
    editHandle() {
      if (this.infoForm.name === this.oldName) {
        this.edit = false
        return
      }
      if (!this.edit) {
        return
      }
      this.$refs.infoForm.validate((valid) => {
        if (valid) {
          this.$utils.httpPost(
            this.$config.api.STUDIO_MODIFIER_ADD_OR_EDIT,
            {
              businessId: this.businessId,
              name: this.infoForm.name,
              id: this.dialog.modifierId || '',
            },
            {
              success: (res) => {
                this.oldName = this.infoForm.name
                this.edit = false
                if (!this.dialog.modifierId) {
                  this.$emit('setNewModifierId', res.data.id)
                }
                this.dialog.modifierId = res.data.id
              },
              error: (err) => {},
            }
          )
        }
      })
    },
    editHandleBlur(e) {
      e.target.blur()
    },
  },
}
</script>

<style lang="scss" scoped>
.modifier-name {
  display: inline-block;
  vertical-align: top;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.modifier-edit-btn {
  vertical-align: top;
  margin-left: 9px;
  color: $primary;
  cursor: pointer;
}
.name-input {
  width: 188px;
}
.btn-save,
.btn-cancel {
  margin-left: 16px;
  cursor: pointer;
  &:hover {
    color: $primary;
  }
}
.form-item-intents {
  word-break: break-all;
  :deep(label),
  :deep(.el-form-item__content) {
    line-height: 24px;
  }
}
</style>
