<template>
  <div class="content-container">
    <el-row class="online-device-header">
      <el-col :span="12" class="online-device-header-item">
        <div class="grid-content">
          <span>查询设备：</span>
          <el-input
            class="search-input"
            v-model="search.search"
            placeholder="请输入设备码查询"
            style="width: calc(100% - 80px)"
            size="medium"
            @keyup.native.enter="getSearchResult"
          >
            <i
              slot="suffix"
              class="el-input__icon el-icon-search search-area-btn"
              @click="getSearchResult"
            />
          </el-input>
        </div>
      </el-col>
      <el-col :span="12" class="online-device-header-item">
        <div class="grid-content">
          <span>时间：</span>
          <date-range @setTime="setTime"></date-range>
        </div>
      </el-col>
    </el-row>
    <el-row class="online-device-header">
      <el-col :span="12" class="online-device-header-item">
        <div class="grid-content">
          <span>设备类型：</span>
          <el-select placeholder="请选择" size="medium" v-model="search.os">
            <el-option label="全部" value="" key="all"> </el-option>
            <el-option label="Android SDK" value="1" key="1"> </el-option>
            <el-option label="Linux SDK" value="4" key="4"> </el-option>
            <el-option label="Windows SDK" value="3" key="3"> </el-option>
            <el-option label="iOS SDK" value="2" key="2"> </el-option>
          </el-select>
        </div>
      </el-col>
      <el-col :span="12" class="online-device-header-item">
        <div class="grid-content">
          <el-button type="primary" size="small" @click="getSearchResult"
            >查询</el-button
          >
          <el-button type="primary" size="small" @click="exportSnList"
            >导出</el-button
          >
        </div>
      </el-col>
    </el-row>
    <div style="color: #ff6300">共搜索到 {{ tableData.sum }} 个设备信息</div>
    <os-table
      class="app-list-table"
      :tableData="tableData"
      style="margin-bottom: 56px"
      @change="getAppList"
    >
      <el-table-column prop="deviceId" label="设备码"> </el-table-column>
      <el-table-column label="注册时间">
        <template slot-scope="scope">
          {{ $utils.dateFormat(scope.row.createTime) }}
        </template>
      </el-table-column>
    </os-table>
  </div>
</template>

<script>
import dateRange from './dateRange'
/**
 * 激活设备明细
 */
export default {
  name: 'online-device',
  components: { dateRange },
  data() {
    return {
      pageOptions: {
        title: '激活设备明细',
        loading: false,
        returnBtn: false,
      },
      tableData: {
        loading: true,
        total: 0,
        page: 1,
        size: 80,
        list: [],
        sum: 1,
      },
      search: {
        search: '',
        os: '',
        startDate: '',
        endDate: '',
        timeChanged: false,
      },
    }
  },
  watch: {
    'search.timeChanged'(val) {
      if (val) {
        this.getSnList()
      }
    },
  },
  methods: {
    getSearchResult() {
      this.getAppList(1)
    },
    getAppList(page) {
      this.tableData.page = page
      this.getSnList()
    },
    exportSnList() {
      let param = this.getSearchParam()
      this.$utils.postopen(this.$config.api.APP_SN_EXPORT, param)
    },
    setTime(start, end) {
      this.search.startDate = start
      this.search.endDate = end
      this.search.timeChanged = true
    },
    getSearchParam() {
      let param = {
        appid: this.$route.params.appId,
        pageIndex: this.tableData.page,
        pageSize: this.tableData.size,
        search: this.search.search.trim(),
        os: this.search.os,
        startDate: this.search.startDate,
        endDate: this.search.endDate,
      }
      // if (this.search.search.trim()) {
      //   param = {
      //     appid: this.$route.params.appId,
      //     pageIndex: this.tableData.page,
      //     pageSize: this.tableData.size,
      //     search: this.search.search.trim(),
      //     os: this.search.os,
      //     startDate: this.search.startDate,
      //     endDate: this.search.endDate,

      //   }
      // } else {
      //   param = {
      //     appid: this.$route.params.appId,
      //     pageIndex: this.tableData.page,
      //     pageSize: this.tableData.size,
      //     search: this.search.search.trim(),
      //     os: this.search.os,
      //     startDate: this.search.startDate,
      //     endDate: this.search.endDate,
      //   }
      // }
      return param
    },
    getSnList() {
      this.tableData.loading = true
      // 搜索规则：当用户搜索了设备码的时候，无视时间和设备类型，直接展示全部时间段+全部设备类型的内容。
      // 当用户没有输入设备码，按用户选择时间和设备类型来搜索。
      let param = this.getSearchParam()
      this.$utils.httpGet(this.$config.api.APP_SN_LIST, param, {
        success: (res) => {
          this.tableData.loading = false
          this.tableData.total = res.data.max * 16
          this.tableData.list = res.data.list
          this.tableData.sum = res.data.count
        },
        error: (err) => {
          this.tableData.loading = false
        },
      })
    },
  },
}
</script>

<style scoped lang="scss">
@import './config/main/common.scss';

.online-device-header {
  display: flex;
  margin: 20px 0;
  .online-device-header-item + .online-device-header-item {
    margin-left: 15px;
  }
  :deep(.el-date-editor .el-range-separator) {
    padding: 0 !important;
  }
}

.content-container {
  padding: 14px 16px;
  max-height: calc(100vh - 134px);
  overflow: auto;
  background: $secondary-bgc;
}
</style>
