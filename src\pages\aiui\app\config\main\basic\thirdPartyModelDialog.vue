<template>
  <el-dialog
    title="三方大模型配置"
    :visible.sync="visible"
    width="550px"
    :before-close="handleCancel"
    class="third_party_dialog"
  >
    <div class="dialog_content">
      <!-- <el-button plain size="small" class="debug_btn" @click="handleTest"
        >调试</el-button
      > -->
      <p class="dialog_tip">
        配置三方大模型的接口信息，注意需在语义模型中选择三方大模型才生效
      </p>

      <el-form
        ref="thirdPartyForm"
        :model="formData"
        :rules="formRules"
        label-width="70px"
        label-position="left"
        class="third_party_form"
      >
        <el-form-item label="接口地址：" prop="url">
          <el-input
            v-model="formData.url"
            placeholder="请输入文字"
            size="small"
          ></el-input>
        </el-form-item>

        <el-form-item label="鉴权信息：" prop="token">
          <el-input
            v-model="formData.token"
            placeholder="请输入文字"
            size="small"
          ></el-input>
        </el-form-item>

        <el-form-item label="模型标识：" prop="modelName">
          <el-input
            v-model="formData.modelName"
            placeholder="请输入文字"
            size="small"
          ></el-input>
        </el-form-item>
      </el-form>
    </div>

    <div slot="footer" class="dialog_footer">
      <el-button @click="handleCancel" size="small">取消</el-button>
      <el-button type="primary" @click="handleConfirm" size="small"
        >确定</el-button
      >
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'thirdPartyModelDialog',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    modelInfo: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      formData: {
        url: '',
        token: '',
        modelName: '',
      },
      formRules: {
        url: [
          { required: true, message: '请输入接口地址', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              if (!value) {
                callback()
                return
              }
              // 检查URL是否符合HTTP协议标准
              const urlPattern =
                /^https?:\/\/[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b(?:[-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/
              if (!urlPattern.test(value)) {
                callback(new Error('不符合HTTP协议标准'))
                return
              }
              callback()
            },
            trigger: 'blur',
          },
        ],
        token: [{ required: true, message: '请输入鉴权信息', trigger: 'blur' }],
        modelName: [
          { required: true, message: '请输入模型标识', trigger: 'blur' },
        ],
      },
    }
  },

  created() {
    this.initFormData()
  },
  methods: {
    initFormData() {
      // 如果modelInfo中有配置信息，则填充表单
      if (this.modelInfo) {
        const { url, token, modelName } = this.modelInfo
        this.formData = {
          url: url || '',
          token: token || '',
          modelName: modelName || '',
        }
      } else {
        // 重置表单
        this.formData = {
          url: '',
          token: '',
          modelName: '',
        }
      }
      // 重置表单验证
      this.$nextTick(() => {
        this.$refs.thirdPartyForm.clearValidate()
      })
    },

    // 取消按钮
    handleCancel() {
      this.$emit('update:visible', false)
    },

    handleConfirm() {
      this.$refs.thirdPartyForm.validate((valid) => {
        if (valid) {
          this.$emit('confirm', this.formData)
        } else {
          return false
        }
      })
    },

    handleTest() {
      this.$refs.thirdPartyForm.validate((valid) => {
        if (valid) {
          this.$message.success('开始测试连接...')
          setTimeout(() => {
            this.$message.success('连接测试成功')
          }, 1000)
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.third_party_dialog {
  .dialog_content {
    .dialog_tip {
      margin: 20px 0;
      margin-top: 0;
      color: #b8bcbd;
    }
    .el-form {
      .el-form-item {
        :deep(.el-form-item__label) {
          padding-right: 0px;
        }
      }
    }
  }
}
</style>
