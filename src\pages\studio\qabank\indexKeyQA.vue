<template>
  <div class="os-scroll os_scroll" ref="scrollDiv">
    <div class="qabank-page" v-loading="tableData.loading || publishing">
      <div class="qabank-page-head">
        <div class="qabank-page-head-title">
          <!-- <span style="max-width: 250px" class="txt-ellipsis-nowrap">{{
            '问答编辑'
          }}</span> -->
        </div>

        <div class="header-right">
          <span class="header-save-time" v-if="baseQaInfo.updateTime"
            >最近保存 {{ baseQaInfo.updateTime | time }}</span
          >
          <span class="header-qa">
            <el-tooltip
              class="item"
              effect="dark"
              content="问答库修改后需要重新构建"
              placement="bottom"
            >
              <i class="el-icon-question" />
            </el-tooltip>
          </span>
          <el-button
            size="small"
            type="primary"
            @mousedown.native.prevent="structure"
            :loading="structureLoading"
            style="padding: 11px 0px"
          >
            {{ structureLoading ? '构建中...' : '构建问答库' }}</el-button
          >
        </div>
      </div>

      <div v-loading="structureLoading" style="padding: 26px 30px 16px 30px">
        <div class="qabank-page-handle-bar">
          <div>
            <el-button
              class="mgr16"
              icon="ic-r-plus"
              type="primary"
              size="small"
              style="min-width: 112px"
              @click="openCreateTheme"
            >
              创建主题
            </el-button>

            <el-dropdown
              trigger="click"
              @command="handleCommand"
              placement="bottom-start"
            >
              <el-button size="small">
                批量操作
                <i class="ic-r-triangle-down el-icon--right" />
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="cover">
                  <upload
                    :qaId="qaId"
                    :options="cover"
                    :limitCount="limitCount"
                    @setLoad="setLoad"
                    @getQaPair="getQaPair(1)"
                  ></upload>
                </el-dropdown-item>
                <el-dropdown-item command="questioning">
                  <upload
                    :qaId="qaId"
                    :options="addOnly"
                    :limitCount="limitCount"
                    @setLoad="setLoad"
                    @getQaPair="getQaPair(1)"
                  ></upload>
                </el-dropdown-item>
                <el-dropdown-item command="export">导出问答</el-dropdown-item>
                <el-dropdown-item command="down">下载模版</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>

            <!-- <el-button
              size="small"
              style="height: 38px; margin-left: 5px"
              @click="publishQaPackFn"
              :loading="publishing"
            >
              {{ publishing ? '立即发布中' : '立即发布' }}
            </el-button> -->

            <!-- <el-switch
              v-model="avatar"
              active-text="虚拟人驱动"
              inactive-text=""
              @change="onAvatarSwitchChange"
              style="margin-left: 20px"
            >
            </el-switch> -->
          </div>

          <div class="fr" style="font-size: 0" @keyup.enter="getQaPair(1)">
            <el-input
              placeholder="搜索主题、问题、答案"
              v-model="searchVal"
              size="medium"
              class="search-area"
            >
              <el-select
                v-model="type"
                slot="prepend"
                style="width: 100px"
                placeholder="请选择"
              >
                <el-option label="全部" value=""></el-option>
                <el-option label="主题" value="topicName"></el-option>
                <el-option label="问题" value="question"></el-option>
                <el-option label="答案" value="answer"></el-option>
              </el-select>
              <i
                slot="suffix"
                class="el-input__icon el-icon-search search-area-btn"
                @click="getQaPair(1)"
              />
            </el-input>
            <el-button size="small" @click="resetSearch"> 重置 </el-button>
          </div>
        </div>

        <div class="qabank-page-theme-list">
          <div
            :class="{
              'qabank-page-theme': true,
              'theme-editting': editingTheme.topicId === theme.topicId,
            }"
            v-for="(theme, index) in tableData.list"
            :key="theme.topicId || index"
          >
            <!-- <i
              class="qabank-page-theme-angle ic-r-angle-d"
              :class="{ 'qabank-page-theme-angle-active': theme.more }"
              @click="showMore(theme, index)"
            /> -->
            <!-- <i
              v-if="theme.topicId"
              class="qabank-page-theme-del ic-r-delete"
              @click="delTheme(theme)"
            /> -->
            <template v-if="editingTheme.topicId !== theme.topicId">
              <div class="qabank-page-theme-head">
                <div class="theme-head-wrap">
                  <span
                    class="qabank-page-theme-head-name txt-ellipsis-nowrap"
                    style="cursor: pointer"
                    @click="toEdit(theme)"
                    :title="theme.topicName"
                    v-html="
                      theme.topicName.replace(
                        new RegExp(searchVal.trim(), 'im'),
                        '<span class=\'qabank-page-hight-light\'>' +
                          searchVal.trim() +
                          '</span>'
                      )
                    "
                  ></span>
                  <span
                    class="status-tag status-warning"
                    v-if="theme.status === 1"
                    >待构建</span
                  >
                  <span
                    class="status-tag status-publishing"
                    v-else-if="theme.status === 2"
                    >待发布</span
                  >
                  <span
                    class="status-tag status-success"
                    v-else-if="theme.status === 3"
                    >已发布</span
                  >
                </div>
                <ul class="theme-edit-btns">
                  <li v-if="!theme.more" @click="showMore(theme, index)">
                    <i class="el-icon-arrow-down"></i><span>展开</span>
                  </li>
                  <li v-else @click="showMore(theme, index)">
                    <i class="el-icon-arrow-up"></i><span>收起</span>
                  </li>
                  <li @click="toEdit(theme)">
                    <i class="el-icon-edit-outline"></i><span>编辑</span>
                  </li>
                  <li v-if="theme.topicId">
                    <el-popconfirm
                      title="确定删除该主题吗？"
                      @confirm="delTheme(theme)"
                    >
                      <span slot="reference"
                        ><i class="el-icon-delete"></i><span>删除</span></span
                      >
                    </el-popconfirm>
                  </li>
                </ul>
              </div>
              <div class="qabank-page-theme-content">
                <ul class="qabank-page-theme-content-ul">
                  <label>问题</label>
                  <li class="question-wrap">
                    <div
                      v-for="(question, quesIndex) in theme.questions"
                      :key="quesIndex"
                      v-if="theme.more || quesIndex === 0"
                    >
                      <div
                        class="ib serial-number"
                        style="
                          width: 24px;
                          min-width: 24px;
                          align-self: flex-start;
                          margin-top: 12px;
                        "
                      >
                        {{ quesIndex + 1 }}
                      </div>
                      <span
                        v-html="
                          question.question.replace(
                            new RegExp(searchVal.trim(), 'im'),
                            '<span class=\'qabank-page-hight-light\'>' +
                              searchVal.trim() +
                              '</span>'
                          )
                        "
                      ></span>
                    </div>
                  </li>
                </ul>
                <ul class="qabank-page-theme-content-ul">
                  <label>答案</label>
                  <li class="answer-outer-wrap">
                    <div
                      v-for="(answer, ansIndex) in theme.answers"
                      :key="ansIndex"
                      v-if="theme.more || ansIndex === 0"
                      class="answer-wrap"
                    >
                      <div class="ib serial-number" style="width: 24px">
                        {{ ansIndex + 1 }}
                      </div>
                      <!-- <span
                      v-html="
                        answer.answer.replace(
                          new RegExp(searchVal.trim(), 'im'),
                          '<span class=\'qabank-page-hight-light\'>' +
                            searchVal.trim() +
                            '</span>'
                        )
                      "
                    ></span> -->
                      <inteligient-rich-input
                        :key="`${theme.topic}_${ansIndex}_${
                          answer.answer
                        }_${JSON.stringify(answer.labels || [])}`"
                        placeholder=""
                        :value="{
                          text: answer.answer,
                          labels: answer.labels || [],
                          changed: false,
                        }"
                        :showAdd="false"
                        :showSwitch="false"
                        :disabled="true"
                        :edit="true"
                        :editIndex="index"
                        :hasSlot="false"
                        :renderMark="avatar"
                        :searchVal="searchVal"
                      >
                      </inteligient-rich-input>
                    </div>
                  </li>
                </ul>
              </div>
            </template>
            <div v-else>
              <div
                class="qabank-page-theme-head"
                style="margin-top: 4px; margin-bottom: 24px"
              >
                <el-input
                  :ref="'qaValueInput' + index"
                  size="small"
                  class="qabank-page-theme-head-input"
                  v-model="theme.topicName"
                  placeholder="请输入主题名称"
                  maxlength="128"
                />
              </div>
              <div class="qabank-page-theme-content">
                <ul class="qabank-page-theme-content-ul">
                  <div>
                    <label style="padding-right: 28px">添加问题</label>

                    <el-tooltip effect="dark" placement="top">
                      <span class="key-qa-tip">
                        <svg-icon iconClass="info"
                      /></span>

                      <div slot="content">
                        建议使用问法中的关键词来写问法规<br />则，“|”表示或者，“&”表示并<br />且，“()”用于连接一组规则。例<br />如：(外出|出差)&报销，需要命中<br />“外出+报销”或“出差+报销”才会<br />给出答案。
                      </div>
                    </el-tooltip>
                  </div>

                  <li
                    class="edit-question-wrap"
                    v-loading="aiLoading"
                    element-loading-text="正在扩写中，请稍候"
                  >
                    <aiui-text-adder
                      :data="theme.questions"
                      :max="40"
                      dataKey="question"
                      @add="addQues"
                      @edit="editQues"
                      @del="delQues"
                      :reg="quesTextReg"
                      warning="问题不能超过128个字符"
                      placeholder="回车新增一行， |表示或者，&表示并且，()用于连接一组规则"
                    />
                    <el-tooltip
                      content="调用AI扩写，生成更多问题语料！"
                      placement="top"
                    >
                      <div
                        :class="{
                          'magic-icon': true,
                          active: theme.questions.length > 0,
                        }"
                        v-if="
                          theme.questions.filter((it) => !it.ai).length < 40
                        "
                        @click="onAiMagicClick(theme)"
                      >
                        <svg-icon iconClass="magic" />
                      </div>
                    </el-tooltip>
                  </li>
                </ul>
                <ul class="qabank-page-theme-content-ul">
                  <label>添加答案</label>
                  <li>
                    <!-- <os-text-adder
                    :data="theme.answers"
                    :max="20"
                    dataKey="answer"
                    @add="addAnswer"
                    @edit="editAnswer"
                    @del="delAnswer"
                    :reg="answerTextReg"
                    warning="答案不能超过1000个字符"
                    placeholder="请输入答案，回车键新增一行。"
                  >
                  </os-text-adder> -->
                    <qa-answers
                      :list="theme.answers"
                      @setQaAnswers="setQaAnswers"
                      qaType="key"
                      :showStyle="false"
                      :avatar="avatar"
                    ></qa-answers>
                  </li>
                </ul>
              </div>
              <div class="qabank-page-theme-footer">
                <el-button
                  size="small"
                  style="min-width: 80px"
                  @click="cancelEditQa(index)"
                >
                  取消
                </el-button>
                <el-button
                  size="small"
                  type="primary"
                  style="min-width: 80px"
                  @click="saveQa"
                >
                  保存
                </el-button>
              </div>
            </div>
          </div>
          <div
            class="qabank-page-theme"
            v-if="!tableData.list.length"
            style="text-align: center; padding: 20px"
          >
            暂无数据
          </div>

          <!-- 创建问答提示 -->
          <!-- <div class="qabank-page-empty" v-if="tableData.list.length <= 0">
          <div class="qabank-page-empty-thumb"></div>
          <div class="qabank-page-empty-main">
            <span>你还没有创建任何主题，</span><a @click="openCreateTheme">点击创建</a>
          </div>
          <div class="qabank-page-empty-desc">
            技能，类似于手机 APP 的概念，一个语音技能用于解决一类用户需求，不同于<br/>手机 App 的地方在于，语音技能使用语音作为交互的入口。
          </div>
          <a>查看文档</a>
        </div> -->
        </div>
        <div class="mgt24">
          <el-pagination
            ref="pagination"
            v-if="showPagination"
            class="txt-al-c"
            @current-change="getQaPair"
            :current-page="tableData.page"
            :page-size="tableData.size"
            :total="tableData.total"
            :layout="pageLayout"
          >
          </el-pagination>
        </div>
      </div>
    </div>
    <!-- <page-leave-tips :dialog="leaveDialog" /> -->
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import dicts from '@M/dicts'
import Upload from './uploadFileKeyQA'
import SelectQa from './selectQa.vue'
import qaAnswers from './qaAnswers.vue'
import InteligientRichInput from '../skill/referSlots/inteligientRichInput.vue'
let controller

export default {
  name: 'qabank',
  data() {
    return {
      publishing: false,
      repoId: '',
      qaId: '',
      baseQaInfo: {},
      qaInfo: {},
      editingName: false,
      type: '',
      searchVal: '',
      show: false, // test
      editingTheme: {},
      emojiType: dicts.getDictArr('answerEmotion'),
      tableData: {
        loading: true,
        total: 0,
        page: 1,
        size: 10,
        list: [],
      },
      oldList: [],
      leaveDialog: {
        show: false,
      },
      cover: {
        isCover: true,
        text: '批量覆盖',
      },
      addOnly: {
        isCover: false,
        text: '批量追加',
      },
      quesTextReg: /^\s*[\s\S]{0,128}\s*$/,
      answerTextReg: /^\s*[\s\S]{0,1000}\s*$/,

      structureLoading: false,
      checkCount: 0,

      // 是否开启虚拟人开关
      avatar: true,
      aiLoading: false,
    }
  },
  computed: {
    showPagination() {
      return this.tableData.total > this.tableData.size
    },
    pageLayout() {
      if (this.tableData.total / this.tableData.size > 7) {
        return 'prev, pager, next, jumper'
      }
      return 'prev, pager, next'
    },
    ...mapGetters({
      limitCount: 'aiuiApp/limitCount',
      topicNumber: 'commonStore/topicNumber',
    }),
  },
  created() {
    if (this.$route.params.repoId) {
      this.repoId = this.$route.params.repoId
    }
    if (this.$route.params.qaId) {
      this.qaId = this.$route.params.qaId
      this.getData()
      this.getQaPair()
    } else {
      this.$router.push({
        name: 'studio-handle-platform-qabanks',
        query: { type: 'keyword' },
      })
    }
  },
  methods: {
    onAiMagicClick(theme) {
      console.log('关键词 theme---', theme)
      controller = new AbortController()
      let self = this
      if (self.editingTheme.questions.length === 0) {
        return this.$message.warning('请先添加问题')
      }
      let param = {
        qaId: this.qaId,
        questions: theme.questions.map((item) => item.question),
      }
      if (theme.topicId) {
        param.topicId = theme.topicId
      }

      this.aiLoading = true

      this.$utils.httpPost(
        this.$config.api.STUDIO_QA_KEYWORD_REWRITE,
        JSON.stringify(param),
        {
          config: {
            headers: {
              'Content-Type': 'application/json',
            },
            signal: controller.signal,
          },
          success: (res) => {
            // self.$message.success('扩写成功')
            if (res.flag) {
              const aiQuestions = (res.data.questions || []).map((item) => {
                return {
                  question: item,
                  ai: true,
                  questionKey: this.$utils.experienceUid(),
                }
              })
              self.editingTheme.questions = [
                ...aiQuestions,
                ...self.editingTheme.questions,
              ]
              const len = aiQuestions.length
              self.$message.success(
                len > 0
                  ? `为您扩写了${len}条问题`
                  : `扩写完成，没有生成新的问题`
              )
              self.aiLoading = false
            } else {
              self.$message.error(res.desc)
              self.aiLoading = false
            }

            console.log(res)
          },
          error: (err) => {
            self.aiLoading = false
          },
        }
      )
    },
    onAvatarSwitchChange(val) {
      let self = this
      if (!this.qaInfo.name) {
        return self.$message.warning('问答库名称不能为空')
      }

      if (this.qaInfo.name.length > 32) {
        return self.$message.warning('问答库名称长度不能超过32个字符')
      }
      let reg = /^[a-zA-Z0-9_\u4e00-\u9fff]+$/
      if (!reg.test(this.qaInfo.name)) {
        return self.$message.warning('问答库名称仅支持汉字/字母/数字/下划线')
      }

      this.$utils.httpPost(
        this.$config.api.STUDIO_QA_CREATE_EDIT,
        {
          avatar: val ? 1 : 0,
          repoId: this.qaInfo.repositoryId,
          name: this.qaInfo.name,
          type: 3, //type: 自定义问答 0, 开放问答 1, 知识库 2, 关键词问答 3
        },
        {
          success: (res) => {
            self.$message.success('修改成功')
            self.getData()
          },
          error: (err) => {},
        }
      )
    },
    setQaAnswers(val) {
      this.editingTheme.answers = val
    },
    // 发布问答
    publishQaPackFn() {
      if (!this.tableData.list || this.tableData.list.length == 0) {
        this.$message.error('请至少添加一个主题')
        return
      }
      this.$utils
        .publishQaPack(this.$config.api.STUDIO_KEY_QA_PUBLISH, {
          qaId: this.qaId,
        })
        .then((res) => {
          this.publishing = true
          //setContentLoad(true)
          this.$message.success('提交成功，正在发布...', 0)
          let timer = setInterval(() => {
            this.$utils
              .qaPublishCheck(this.$config.api.STUDIO_KEY_QA_CHECK, {
                qaId: this.qaId,
              })
              .then((check) => {
                if (check.data.data.publish === 'success') {
                  this.$message.success('发布成功')
                  this.publishing = false
                  //setContentLoad(false)
                  clearInterval(timer)
                } else if (check.data.data.publish === 'fail') {
                  this.$message.error('发布失败')
                  this.publishing = false
                  //setContentLoad(false)
                  clearInterval(timer)
                }
              })
          }, 1000)
        })
    },
    setLoad(val) {
      this.tableData.loading = val
    },
    getData() {
      let self = this
      this.$utils.httpGet(
        this.$config.api.STUDIO_QA_INFO,
        {
          repoId: this.repoId,
        },
        {
          success: (res) => {
            self.qaInfo = res.data
            self.avatar = res.data.avatar == 1
            self.baseQaInfo = JSON.parse(JSON.stringify(res.data))
          },
          error: (err) => {
            self.$router.push({
              name: 'studio-handle-platform-qabanks',
              query: { type: 'keyword' },
            })
          },
        }
      )
    },
    getQaPair(page, topicId) {
      let self = this
      this.tableData.loading = true
      this.$utils.httpGet(
        this.$config.api.STUDIO_KEY_QA_PAIR_QUERY,
        {
          qaId: this.qaId,
          search: this.searchVal,
          //queryRange: this.type,
          pageIndex: page || this.tableData.page,
          pageSize: this.tableData.size,
        },
        {
          success: (res) => {
            self.oldList = JSON.parse(JSON.stringify(res.data.topics))
            if (topicId) {
              self.tableData.list = (res.data.topics || []).map((item) => {
                return {
                  ...item,
                  more: topicId === item.topicId,
                }
              })
            } else {
              self.tableData.list = res.data.topics
            }

            self.tableData.total = res.data.count
            self.tableData.page = res.data.pageIndex
            self.tableData.size = res.data.pageSize
            self.tableData.loading = false

            self.$nextTick(() => {
              self.$refs.scrollDiv.scrollTop = 0
            })
          },
          error: (err) => {},
        }
      )
    },
    // 返回
    back() {
      this.$router.push({
        name: 'studio-handle-platform-qabanks',
        query: { type: 'keyword' },
      })
      // if (this.$utils.isEqual(this.tableData.list, this.oldList)) {
      //
      // } else {
      //   this.leaveDialog.show = true
      // }
    },
    toEditName() {
      this.editingName = true
      this.$nextTick(function () {
        this.$refs['qaNameInput'] && this.$refs['qaNameInput'].focus()
      })
    },
    // 编辑问答库名
    editQabankName() {
      let self = this
      if (!this.qaInfo.name) {
        return self.$message.warning('问答库名称不能为空')
      }
      if (this.qaInfo.name === this.baseQaInfo.name) {
        this.editingName = false
        return
      }
      if (!this.editingName) {
        return
      }
      if (this.qaInfo.name.length > 32) {
        return self.$message.warning('问答库名称长度不能超过32个字符')
      }
      let reg = /^[a-zA-Z0-9_\u4e00-\u9fff]+$/
      if (!reg.test(this.qaInfo.name)) {
        return self.$message.warning('问答库名称仅支持汉字/字母/数字/下划线')
      }
      this.$utils.httpPost(
        this.$config.api.STUDIO_QA_CREATE_EDIT,
        {
          repoId: this.qaInfo.repositoryId,
          name: this.qaInfo.name,
          type: 3, //type: 自定义问答 0, 开放问答 1, 知识库 2, 关键词问答 3
        },
        {
          success: (res) => {
            self.$message.success('修改成功')
            self.getData()
            self.editingName = false
          },
          error: (err) => {},
        }
      )
    },
    // 取消编辑问答库名
    cancelEditQabankName() {
      this.editingName = false
      this.qaInfo = this.$deepClone(this.baseQaInfo)
    },
    // 批量操作
    handleCommand(command) {
      let self = this
      switch (command) {
        case 'cover':
          break
        case 'questioning':
          break
        case 'export':
          this.$utils.postopen(this.$config.api.STUDIO_KEY_QA_EXCEL_EXPORT, {
            qaId: this.qaId,
          })
          break
        case 'down':
          window.open(
            'https://aiui-file.cn-bj.ufileos.com/DemoKeywordQa.xlsx',
            '_self'
          )
          break
        default:
          break
      }
    },
    // 重置
    resetSearch() {
      this.searchVal = ''
      this.type = ''
      this.getQaPair(1)
    },

    getUserLimit() {
      let self = this
      return new Promise((resolve, reject) => {
        this.$utils.httpGet(
          this.$config.api.STUDIO_KEY_QA_LIMIT,
          {},
          {
            success: (res) => {
              if (res.code == 0) {
                if (self.qaInfo.topicNumber < res.data.qa_pair_count) {
                  resolve(true)
                } else {
                  self.$message.error('主题数量超限')
                  reject(false)
                }
              } else {
                self.$message.error('请求服务失败')
              }
            },
            error: (err) => {},
          }
        )
      })
    },

    // 打开创建主题
    openCreateTheme() {
      if (this.aiLoading) {
        controller.abort()
        this.aiLoading = false
      }
      this.getUserLimit().then(
        (resolve) => {
          let self = this
          let newTheme = {
            topicName: '',
            answers: [],
            questions: [],
          }
          if (this.tableData.list[0]) {
            if (this.tableData.list[0].topicId) {
              this.tableData.list.unshift(newTheme)
            }
          } else {
            this.tableData.list.unshift(newTheme)
          }
          this.editingTheme = this.tableData.list[0]
          this.$nextTick(function () {
            self.$refs['qaValueInput0'] &&
              self.$refs['qaValueInput0'][0].focus()
          })
        },
        (reject) => {}
      )
    },
    // 删除主题
    delTheme(theme) {
      let self = this
      this.tableData.loading = true
      this.$utils.httpPost(
        this.$config.api.STUDIO_KEY_QA_PAIR_DEL,
        {
          qaId: this.qaId,
          topicId: theme.topicId,
        },
        {
          success: (res) => {
            self.$message.success('删除成功')
            if (self.tableData.list.length === 1 && self.tableData.page > 1) {
              self.tableData.page -= 1
            }
            self.getData()
            self.getQaPair()
          },
          error: (err) => {},
        }
      )
    },
    // 展开
    showMore(theme, index) {
      theme.more = !theme.more
      this.$set(this.tableData.list, index, theme)
    },
    // 编辑界面
    toEdit(theme) {
      if (this.aiLoading) {
        controller.abort()
        this.aiLoading = false
      }
      this.editingTheme = theme
    },
    // 添加问题
    addQues(item) {
      // 给前端渲染key造的字段
      this.editingTheme.questions = [
        { question: item, questionKey: this.$utils.experienceUid() },
        ...this.editingTheme.questions,
      ]
    },
    editQues(item, index) {
      item.question = item.question.trim()
    },
    delQues(item, index) {
      let quesList = Array.prototype.filter.call(
        this.editingTheme.questions,
        function (ques, quesIndex) {
          return quesIndex != index
        }
      )
      this.editingTheme.questions = quesList
    },
    // 添加答案
    addAnswer(item) {
      let answers = this.editingTheme.answers
      answers.push({
        answer: item,
        type: 1,
      })
    },
    editAnswer(item, index) {
      item.answer = item.answer.trim()
    },
    delAnswer(item, index) {
      let answers = Array.prototype.filter.call(
        this.editingTheme.answers,
        function (answer, answerIndex) {
          return answerIndex != index
        }
      )
      this.editingTheme.answers = answers
    },
    cancelEditQa(index) {
      if (this.aiLoading) {
        controller.abort()
        this.aiLoading = false
      }
      if (this.tableData.list[index].topicId) {
        // fix: 不能通过索引去寻找，因为新增的加入了，应该通过topId来找
        let originTopicData = this.oldList.find(
          (item) => item.topicId === this.tableData.list[index].topicId
        )
        this.$set(this.tableData.list, index, this.$deepClone(originTopicData))
        if (this.tableData.list[0].topicId) {
          this.editingTheme = {}
        } else {
          this.editingTheme = this.tableData.list[0]
        }
      } else {
        // 取消的是待新增的
        this.tableData.list.shift()
        this.editingTheme = {}
      }
    },
    saveQa() {
      let self = this
      let data = {}
      let questions = this.editingTheme.questions.map((it) => {
        let item = { ...it }
        delete item.questionKey
        delete item.ai
        return item
      })
      if (this.editingTheme.topicId) {
        data = {
          qaId: this.qaId,
          topicId: this.editingTheme.topicId,
          topicName: this.editingTheme.topicName,
          answers: this.editingTheme.answers,
          questions,
        }
      } else {
        data = {
          qaId: this.qaId,
          topicName: this.editingTheme.topicName,
          answers: this.editingTheme.answers,
          questions,
        }
      }
      if (!data.topicName || (data.topicName && data.topicName.length <= 0)) {
        return this.$message.warning('主题名称不能为空')
      }
      if (data.topicName && data.topicName.length > 32) {
        return this.$message.warning('主题不能超过32个字符')
      }
      if (data.questions && data.questions.length <= 0) {
        return this.$message.warning('请添加至少一个问题')
      }
      if (data.answers && data.answers.length <= 0) {
        return this.$message.warning('请添加至少一个答案')
      }

      for (let i = 0; i <= data.questions.length - 1; i++) {
        data.questions[i].question = data.questions[i].question.replace(
          /<(i|b|h|a|d|\/)[^>]*>/gi,
          ''
        )
        if (data.questions[i].question.length > 128) {
          return this.$message.warning('问题不能超过128个字符')
        }
      }

      for (let i = 0; i <= data.answers.length - 1; i++) {
        // if (this.$utils.hasDocument(data.answers[i].answer)) {
        //   return this.$message.warning(`第${i+1}条答案中不能包含html元素标签`)
        // }
        data.answers[i].answer = data.answers[i].answer.replace(
          /<(i|b|h|a|d|\/)[^>]*>/gi,
          ''
        )
        if (data.answers[i].answer.length > 1000) {
          return this.$message.warning('答案不能有超过1000个字符')
        }
      }

      if (this.aiLoading) {
        return this.$message.warning('小主请留步，AI扩写马上就好！')
      }

      data.answers = (data.answers || []).map((item) => {
        let tempAns = {
          ...item,
          type: 1,
          labels: (item.labels || []).map(({ picture, zhName, ...rest }) => {
            return { ...rest }
          }),
        }
        delete tempAns.emotion
        return tempAns
      })
      this.tableData.loading = true
      this.$utils.httpPost(
        this.$config.api.STUDIO_KEY_QA_PAIR_ADD_UPDATE,
        JSON.stringify(data),
        {
          config: {
            headers: {
              'Content-Type': 'application/json;charset=UTF-8',
            },
          },
          success: (res) => {
            self.$message.success('保存成功')
            const topicId = self.editingTheme.topicId
            self.editingTheme = {}
            self.getData()
            self.getQaPair(1, topicId)
          },
          error: (err) => {
            self.tableData.loading = false
          },
        }
      )
    },
    // 构建
    structure() {
      let that = this
      this.structureLoading = true
      // 调用接口构建
      this.$utils.httpPost(
        this.$config.api.STUDIO_KEY_QA_STRUCT,
        {
          qaId: this.qaId,
        },
        {
          success: (res) => {
            // 构建接口调用后，还需要轮询检查状态
            if (res.code == 0) {
              that.$message.success('构建成功')
              that.getQaPair(1)
            } else {
              that.$message.error(res.desc)
            }
            that.structureLoading = false
          },
          error: (err) => {
            that.structureLoading = false
          },
        }
      )
    },
  },
  components: {
    Upload,
    SelectQa,
    qaAnswers,
    InteligientRichInput,
  },
}
</script>

<style lang="scss">
.key-qa-tip {
  overflow: hidden;
  position: relative;
  right: 20px;
  color: #bfbfbf;
}
.qabank-page {
  // max-width: 1200px;
  // padding-top: 14px;
  margin: auto;
  height: 100%;
  &-form-input {
    width: 220px;
  }
  &-form-save,
  &-form-cancel {
    font-size: 18px;
    margin-left: 8px;
    cursor: pointer;
    &:hover {
      color: $primary;
    }
  }
  &-head {
    padding: 0 30px;
    font-size: 18px;
    // margin-bottom: 14px;
    height: 46px;
    line-height: 46px;
    display: flex;
    align-items: center;
    color: #545556;
    &-back {
      cursor: pointer;
      margin-right: 16px;
      color: $grey4;
    }
    &-title {
      flex: auto;
      display: flex;
      align-items: center;
    }
    &-title-select {
      display: flex;
      align-items: center;
      cursor: pointer;
      margin-right: 16px;
      i {
        padding-left: 8px;
        color: $grey5;
      }
    }
  }
  &-handle-bar {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
    gap: 8px;
    margin-bottom: 16px;
    .search-area {
      width: 450px;
      margin-right: 8px;
      font-size: 14px;
    }
    .el-select {
      width: 90px;
    }
  }

  &-empty {
    text-align: center;
    margin-top: 92px;
    &-thumb {
      width: 120px;
      height: 120px;
      border-radius: 100%;
      background-color: $grey1;
      margin: auto;
      margin-bottom: 24px;
    }
    &-main {
      margin-bottom: 24px;
      span,
      a {
        font-size: 16px;
        font-weight: 600;
      }
    }
    &-desc {
      margin-bottom: 8px;
    }
  }

  &-theme {
    position: relative;
    border: 1px solid $grey2;
    border-radius: 4px;
    padding: 20px;

    &:hover &-del {
      display: block;
    }
    &-list {
      // margin-bottom: 56px;
      height: calc(100vh - 227px);
      overflow-y: auto;
      background-color: #fff;
    }
    &-angle {
      position: absolute;
      color: $grey4;
      left: 24px;
      top: 24px;
      &-active {
        transform: rotate(180deg);
      }
    }
    &-del {
      position: absolute;
      color: $grey4;
      right: 48px;
      top: 24px;
      font-size: 20px;
      cursor: pointer;
      display: none;
    }
    &-head {
      // width: 90%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 16px;
      color: #262626;
      i {
        margin-left: 4px;
      }
    }
    &-head-name {
      font-size: 16px;
      font-weight: 500;
      display: inline-block;
      max-width: 400px;
      margin-right: 8px;
    }
    &-head-input {
      width: 496px;
    }
    &-content {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      &-ul {
        display: flex;
        flex-direction: column;
        min-width: 500px;
        margin-bottom: 10px;
        flex: 1;
        // display: inline-flex;
        // padding-right: 48px;
        > li {
          flex: auto;
          margin-top: 5px;
          span {
            word-break: break-all;
          }
        }
      }
      label {
        color: #545556;
        font-size: 14px;
        font-weight: 500;
        padding-right: 8px;
        flex: none;
      }
      &-emotion {
        width: 52px;
        input {
          border: 0;
          padding: 0 !important;
        }
        .el-input__suffix {
          right: 0;
        }
      }
    }
    &-footer {
      margin-top: 24px;
      text-align: right;
    }
  }
  &-theme + &-theme {
    margin-top: 16px;
  }

  &-hight-light {
    background-color: $success-light-30;
  }
}
</style>

<style lang="scss" scoped>
.os_scroll {
  overflow-y: hidden !important;
  background-color: $white-grey !important;
}
ul,
p {
  margin-bottom: 0;
}
.answer-outer-wrap {
  background: #f7f8fa;
  border-radius: 4px;
  padding-right: 20px;
}
.answer-wrap {
  display: flex;
  align-items: center;
  .serial-number {
    align-self: flex-start;
    margin-top: 12px;
  }
}
.answer-tag {
  white-space: nowrap;
}

.question-wrap {
  background: #f7f8fa;
  border-radius: 4px;
  > div {
    line-height: 40px;
    display: flex;
    align-items: center;
  }
}

.edit-question-wrap {
  position: relative;
  .magic-icon {
    position: absolute;
    cursor: pointer;
    z-index: 1;
    right: 30px;
    top: 21px;
    color: #797979;
    cursor: default;
    &.active {
      color: $primary;
      cursor: pointer;
    }
    :deep(.svg-icon) {
      width: 19px;
      height: 19px;
    }
  }
}

.qabank-page-head {
  height: 63px;
  // .el-icon-question {
  //   &::before {
  //     color: $primary;
  //   }
  // }
}

.theme-edit-btns {
  display: flex;
  align-items: center;

  li {
    display: flex;
    align-items: center;
    padding: 0 13px;
    cursor: pointer;
    i {
      margin-right: 7px;
      font-size: 16px;
      &::before {
        color: #b1b1b1;
      }
    }
    span {
      color: #262626;
    }
  }

  li:not(:last-child) {
    border-right: 1px solid #c2c2c2;
  }
}

.serial-number {
  width: 26px;
  height: 17px;
  background: #ffffff;
  border-radius: 2px;
  box-shadow: 0px 2px 10px 0px rgba(154, 164, 179, 0.27);

  font-size: 12px;
  color: #b1b1b1;
  line-height: 17px;
  text-align: center;
  margin: 0 16px 0 20px;
}

.theme-editting {
  background: #f7f8fa;
}

.theme-head-wrap {
  display: flex;
  align-items: center;
}
.status-tag {
  height: 22px;
  line-height: 22px;
  border-radius: 4px;
  text-align: center;
  font-size: 12px;
  padding: 0 6px;
  &.status-warning {
    color: #eb7527;
    background: #ffece1;
  }
  &.status-publishing {
    color: #255cdb;
    background: #e4ecff;
  }
  &.status-success {
    color: #318325;
    background: #e2f4e0;
  }
}
</style>
