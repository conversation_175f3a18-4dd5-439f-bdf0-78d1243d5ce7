<template>
  <os-page :options="pageOptions">
    <studio-agent-header-right-legacy slot="btn" />
    <el-tabs v-model="clouds_type" type="card" class="clouds-tabs">
      <el-tab-pane
        label="云端-工作流"
        name="work_flow"
        v-if="agent_info?.agentType === 2"
      >
        <WorkFlow ref="WorkFlow"> </WorkFlow>
      </el-tab-pane>
      <el-tab-pane
        label="云端-API"
        name="api"
        v-else-if="agent_info?.agentType === 0"
      >
        <ApiDetail
          @refresh="refresh"
          ref="ApiForm"
          :extraParam="extraParam"
        ></ApiDetail>
      </el-tab-pane>
      <el-tab-pane
        label="云端-FaaS"
        name="faas"
        v-else-if="agent_info?.agentType === 1"
      >
      </el-tab-pane>
      <el-tab-pane
        label="本地"
        name="faas"
        v-else-if="agent_info?.agentType === 3"
      >
      </el-tab-pane>
    </el-tabs>
  </os-page>
</template>

<script>
import StudioAgentHeaderRightLegacy from '../../../../components/studioAgentHeaderRightLegacy.vue'
import ApiDetail from './apiDialog.vue'
import WorkFlow from './workFlow.vue'
export default {
  name: 'IflyAIuiWebPostprocess',

  components: {
    ApiDetail,
    WorkFlo,
    StudioAgentHeaderRightLegacyw,
  },

  data() {
    return {
      pageOptions: {
        title: '后处理',
        loading: false,
      },
      clouds_type: 'api',
      agent_info: null,
      extraParam: {
        url: null,
        method: 'POST',
        type: 'bearer',
        key: this.generateRandomString(),
      },
    }
  },

  mounted() {
    this.getInfo()
  },

  methods: {
    refresh() {
      this.getInfo()
    },

    getInfo() {
      const agentId = this.$route.params.agentId
      this.$utils.httpPost(
        this.$config.api.AGENT_DETAIL_OLD,
        JSON.stringify({ agentId: agentId }),
        {
          config: {
            headers: {
              'Content-Type': 'application/json;charset=UTF-8',
            },
          },
          success: (res) => {
            console.log(res, '详情的res')
            this.agent_info = res.data
            this.clouds_type =
              this.agent_info?.agentType == 2
                ? 'work_flow'
                : this.agent_info?.agentType == 0
                ? 'api'
                : 'faas'
            if (res.data.extraParam !== '') {
              this.extraParam = JSON.parse(res.data.extraParam)
            }
            // this.$refs.ApiForm.getValues(JSON.parse(res.data.extraParam))
          },
          error: (err) => {},
        }
      )
    },

    generateRandomString() {
      const characters =
        'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%&_^*~'
      let result = ''
      const length = 30
      for (let i = 0; i < length; i++) {
        const randomIndex = Math.floor(Math.random() * characters.length)
        result += characters[randomIndex]
      }
      return result
    },
  },
}
</script>

<style lang="scss" scoped>
.clouds-tabs {
  margin-top: 10px;
}
.el-tabs__header {
  margin-top: 10px;
}
</style>
