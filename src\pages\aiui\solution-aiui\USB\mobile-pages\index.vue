<template>
  <div class="main-content">
    <MyHeader> </MyHeader>
    <section class="main-content-banner">
      <div class="banner-text">
        <h2>AIUI USB声卡开发套件</h2>

        <p class="banner-text-content">
          多麦克风阵列构型录音声卡，集成便捷，适用于智能机器人，智慧大屏等设备开发评估。
        </p>

        <div class="hor-btn">
          <el-button
            class="banner-text-button"
            @click="toConsole"
            type="primary"
            round
            >合作咨询</el-button
          >
          <el-button class="banner-text-buy" @click="toBuy" round plain
            >立即购买</el-button
          >
        </div>
      </div>
    </section>

    <section class="section-nav">
      <ul class="nav-ul" id="nav-ul">
        <li
          v-for="item in nav_list"
          :key="item.id"
          @click="gotoSelection(item.id)"
        >
          {{ item.name }}
        </li>
      </ul>

      <i
        v-if="nav_list.length > 4"
        :class="[
          'nav-btn',
          nav_flag ? 'el-icon-caret-bottom' : 'el-icon-caret-top',
        ]"
        id="nav-btn"
        @click="handleNavExpand"
      ></i>
    </section>

    <section class="section section1">
      <h2>应用场景</h2>

      <div class="section-item">
        <ul>
          <li v-for="(item, index) in app_scenario" :key="index" class="app">
            <img :src="item.src" :alt="item.alt" />
            <p class="app-text">{{ item.alt }}</p>
          </li>
        </ul>
      </div>
    </section>

    <section class="section section2">
      <h2>产品特点</h2>

      <div class="section-item">
        <ul>
          <li v-for="(item, index) in point_list" :key="index" class="app">
            <img :src="item.src" :alt="item.alt" />
            <div class="title">{{ item.title }}</div>
            <div class="sub_title">{{ item.sub_title }}</div>
          </li>
        </ul>
      </div>
    </section>

    <section class="section section3">
      <h2>接线示意图</h2>
      <div class="top-part">
        <div v-for="item in icon_list" :key="item.index">
          <img :src="item.src" alt="" />
          <div class="title">{{ item.name }}</div>
        </div>

        <svg-icon
          class="el-icon-right first-arrow"
          iconClass="right"
        ></svg-icon>
        <i class="el-icon-sort reversal-arrow"></i>
      </div>
      <ul>
        <li>· 支持线性四麦线性六麦和环形六麦</li>
        <li>· 原始音频上传给上机位</li>
        <li>·上机位回采信号接入声卡主板</li>
      </ul>
    </section>

    <section class="section section4">
      <h2>硬件接口说明</h2>
      <div class="image"></div>
      <ul>
        <li v-for="item in desc_list" :key="item.index">
          <div class="desc-num">{{ item.num }}</div>
          <div class="desc-title">{{ item.title }}</div>
        </li>
      </ul>
    </section>

    <section class="section section5">
      <h2>产品清单</h2>

      <div class="product-goods">
        <ul>
          <li>
            <div class="product-goods-title">硬件</div>
            <div class="product-goods-text">
              ·&nbsp;USB声卡主板 &nbsp;&nbsp; ·&nbsp;麦克风板 &nbsp;&nbsp;
              ·&nbsp;麦克风排线
            </div>
            <div class="product-goods-text">
              ·&nbsp;回采线 &nbsp;&nbsp; ·&nbsp;USB线
            </div>
          </li>
          <li>
            <div class="product-goods-title">软件</div>
            <div class="product-goods-text">
              ·&nbsp;唤醒SDK , 集成前端声学算法(上机位集成)&nbsp;&nbsp;
              ·&nbsp;AIUI SDK, 集成云端交互能力(上机位集成)
            </div>
          </li>
          <li>
            <div class="product-goods-title">服务</div>
            <div class="product-goods-text">·&nbsp;为其两个月的VIP技术支持</div>
            <div class="product-goods-text">·&nbsp;浅定制资源定制支持</div>
          </li>
        </ul>
      </div>
    </section>

    <section class="section section6">
      <h2>开发材料</h2>
      <ul>
        <li
          v-for="item in develop_doc"
          :key="item.index"
          @click="toDoc(item.link)"
        >
          {{ item.name }}
        </li>
      </ul>
    </section>

    <section class="section section-cooperation">
      <div class="cooperation-btn" @click="toConsole">合作咨询</div>
    </section>

    <section class="section section-footer">
      <!-- <aiuiMobileFooter> </aiuiMobileFooter> -->
      <div class="footer-title">
        联系我们
        <span class="arrow" @click="clickFooterArrow">▼</span>
      </div>
      <div class="content">
        <p>联系电话：19955108393</p>
        <p>开发者交流群：617903641</p>
        <p>商务技术支持：<EMAIL></p>
      </div>

      <div class="footer-title">
        友情链接
        <span class="arrow" @click="clickFooterArrow">▼</span>
      </div>
      <div class="content">
        <p>科大讯飞</p>
        <p>讯飞开放平台</p>
      </div>

      <div class="corporation-info">
        ©科大讯飞股份有限公司 皖ICP备05001217号
      </div>
    </section>
  </div>
</template>

<script>
import MyHeader from '@P/aiui/solution-aiui/mobile-header.vue'
export default {
  name: 'USBmobile',

  data() {
    return {
      nav_flag: true,
      nav_list: [
        { name: '应用场景', id: 1 },
        { name: '产品特点', id: 2 },
        { name: '接线示意图', id: 3 },
        { name: '硬件接口说明', id: 4 },
        { name: '产品清单', id: 5 },
        { name: '开发材料', id: 6 },
      ],

      app_scenario: [
        {
          alt: '智能机器人',
          src: require('../../../../../assets/images/solution/smart-hardware/mobile/znjqr.png'),
        },
        {
          alt: '一体机',
          src: require('../../../../../assets/images/solution/smart-hardware/mobile/ytj.png'),
        },
        {
          alt: '智能屏',
          src: require('../../../../../assets/images/solution/smart-hardware/mobile/apps4.png'),
        },
        {
          alt: '电视机',
          src: require('../../../../../assets/images/solution/smart-hardware/mobile/dsj.png'),
        },
      ],

      point_list: [
        {
          src: require('../../../../../assets/images/solution/soft-hardware/USB/td1.png'),
          title: '兼容多类型麦克风阵列',
          sub_title: '支持线性四麦/线性六麦/环形六麦 阵列构型',
        },
        {
          src: require('../../../../../assets/images/solution/soft-hardware/USB/td2.png'),
          title: '180°/360°定向拾音',
          sub_title: '支持声源定位 ，拾音距离可达5米',
        },
        {
          src: require('../../../../../assets/images/solution/soft-hardware/USB/td3.png'),
          title: '尺寸小巧易集成',
          sub_title: '89mm*38mm',
        },
        {
          src: require('../../../../../assets/images/solution/soft-hardware/USB/td4.png'),
          title: '扩展能力丰富',
          sub_title: '对接上位机可实现前端声学，语音 交互等功能',
        },
      ],

      icon_list: [
        {
          src: require('../../../../../assets/images/solution/smart-hardware/mobile/icon4.png'),
          name: '麦克风阵列',
        },
        {
          src: require('../../../../../assets/images/solution/smart-hardware/mobile/icon1.png'),
          name: '降噪主板',
        },
        {
          src: require('../../../../../assets/images/solution/smart-hardware/mobile/icon3.png'),
          name: '上位机',
        },
      ],

      desc_list: [
        { title: '调试串口接口', num: '01' },
        { title: 'wafer USB接口', num: '02' },
        { title: 'micro USB接口', num: '03' },
        { title: '独立电源接口', num: '04' },
        { title: '麦克风信号接口', num: '05' },
        { title: '麦克风信号接口', num: '06' },
        { title: '参考信号接口', num: '07' },
      ],

      develop_doc: [
        {
          name: ' • 《AIUI USB声卡开发套件产品白皮书.pdf》',
          link: 'https://aiui-doc.xf-yun.com/project-1/doc-164/',
        },
        {
          name: ' • 《AIUI USB声卡开发套件使用指南.pdf》',
          link: 'https://aiui-doc.xf-yun.com/project-1/doc-165/',
        },
      ],
    }
  },

  components: {
    MyHeader,
  },

  mounted() {},

  methods: {
    gotoSelection(id) {
      const section = document.getElementsByClassName(`section` + id)[0]
      if (section) {
        section.scrollIntoView({ behavior: 'smooth' })
      }
    },

    handleNavExpand() {
      const nav_ul = document.getElementById('nav-ul')
      const navIcon = document.getElementById('nav-btn')

      navIcon.addEventListener('click', () => {
        if (this.nav_flag) {
          nav_ul.classList.add('expanded')
        } else {
          nav_ul.classList.remove('expanded')
        }
        this.nav_flag = !this.nav_flag
      })
    },

    clickFooterArrow() {
      const titles = document.querySelectorAll('.footer-title')
      titles.forEach((title) => {
        title.addEventListener('click', () => {
          const content = title.nextElementSibling
          const arrow = title.querySelector('.arrow')

          if (content.style.display === 'none' || !content.style.display) {
            content.style.display = 'block'
            arrow.classList.add('up')
          } else {
            content.style.display = 'none'
            arrow.classList.remove('up')
          }
        })
      })
    },

    toDoc(link) {
      window.open(link)
    },

    toConsole() {
      const search = window.location.search
      if (search) {
        window.open(`/solution/apply/10${search}`)
      } else {
        window.open('/solution/apply/10')
      }
    },
    toBuy() {
      window.open(`https://www.aifuwus.com/onstage/cmddetail?id=3062`)
    },
  },
}
</script>

<style lang="scss" scoped>
.main-content {
  background-color: #f6f7f9;
  max-width: 750px;
  overflow: hidden;
  &-banner {
    background: url(~@A/images/solution/smart-hardware/mobile/banner2.jpg)
      center no-repeat;
    background-size: cover;
    height: 375px;
    overflow: hidden;
    width: 100%;
  }
  .banner-text {
    margin-top: 64px;
    padding-left: 44px;
    h2 {
      font-size: 48px;
      font-weight: 600;
      color: #000000;
      line-height: 68px;
    }
    p {
      width: 648px;
      font-size: 24px;
      font-weight: 300;
      color: #000000;
      line-height: 40px;
      text-align: left;
    }
    .hor-btn {
      display: flex;
      flex-direction: row;
      justify-content: flex-start;
      margin-top: 40px;
      div:nth-child(2) {
        margin-left: 30px;
      }
    }
  }
  .section {
    h2 {
      font-size: 48px;
      font-weight: 600;
      color: #000000;
      line-height: 68px;
      text-align: center;
      margin: 0 auto;
      margin-top: 50px;
      margin-bottom: 30px;
    }
    p,
    ul {
      margin-bottom: 0;
    }
    max-width: 750px;
    overflow: hidden;
    margin: 0 auto;
    .section-title {
      text-align: center;
      font-size: 36px;
      font-weight: 500;
      color: #444444;
      line-height: 54px;
      .arrow {
        width: 26px;
        height: 24px;
        display: inline-block;
        background-repeat: no-repeat;
        background-size: contain;
      }
      .arrow-left {
        background-position: left;
        background-image: url(~@A/images/solution/smart-hardware/arrow-left.png);
      }
      .arrow-right {
        background-position: right;
        background-image: url(~@A/images/solution/smart-hardware/arrow-right.png);
      }
    }
    .section-tabs {
      margin-top: 60px;
    }
  }

  .section-nav {
    background-color: #ffffff;
    position: relative;
    width: 100%;
    border: 1px solid #ddd;
    padding: 0 30px;
    .nav-ul {
      display: flex;
      justify-content: space-evenly;
      flex-wrap: wrap;
      overflow: hidden; /* 默认隐藏超出部分 */
      max-height: 68px; /* 折叠状态时，只显示一行 */
      transition: max-height 0.3s ease; /* 动画过渡效果 */
    }
    .nav-ul.expanded {
      max-height: 200px; /* 展开状态：允许显示多行 */
    }
    .nav-ul li {
      flex: 0 0 auto;
      margin-right: 30px;
      white-space: nowrap;
      cursor: pointer;
      font-size: 24px;
      font-weight: 400;
      text-align: center;
      line-height: 68px;
      color: #7a7a7a;
    }
    .nav-btn {
      position: absolute;
      height: 68px;
      width: 60px;
      right: 0px;
      top: 60%;
      transform: translateY(-50%);
      font-size: 48px;
      // background-color: red;
    }
  }

  .section1 {
    padding: 0 40px;
    .section-item {
      margin-top: 50px !important;

      > ul {
        display: flex;
        justify-content: flex-start;
        flex-wrap: wrap;
        gap: 30px;

        li {
          // flex: 0 0 calc(33.33%);
          width: 192px;
          height: 240px;
          position: relative;
          background: url(~@A/images/solution/smart-hardware/mobile/appborder.jpg)
            center no-repeat;
          background-size: cover;
          border: 1px solid #979797;
          border-radius: 16px;
          margin-bottom: 60px;

          img {
            width: 100%;
            height: 100%;
            position: absolute;
            right: 0;
            top: 0;
          }

          image:nth-child(3) {
            position: absolute;
            top: 20px;
            right: -80px;
          }

          p {
            height: 38px;
            width: 100%;
            text-align: center;
            margin: 0 auto;
            font-size: 24px;
            line-height: 38px;
            position: absolute;
            left: 50%;
            bottom: -70px;
            transform: translate(-50%, 0%);
            margin-bottom: 20px;
          }
        }
      }
    }
  }

  .section2 {
    width: 100%;
    padding: 0 26px;
    background-color: #fff !important;

    .section-item {
      ul {
        margin-top: 30px;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-evenly;
        gap: 20px;
        li {
          width: 300px;
          position: relative;
          // overflow: hidden;
          img {
            // margin: 0 auto !important;
            width: 100px;
            height: 100px;
            position: absolute;
            left: 50%;
            top: 0;
            transform: translate(-50%, 0);
          }
          .title {
            text-align: center;
            margin: 0 auto;
            font-weight: 500;
            color: #262626;
            font-size: 20px;
            margin-top: 100px;
          }
          .sub_title {
            text-align: center;
            margin: 0 auto;
            margin-top: 20px;
            font-size: 16px;
            font-family: PingFang SC, PingFang SC-Regular;
            color: #666666;
          }
        }
      }
    }
  }

  .section3 {
    width: 100%;
    padding: 0 26px;
    background: linear-gradient(180deg, #ffffff, #ffffff);
    border-radius: 31px;
    .top-part {
      padding-left: 30px;
      display: flex;
      justify-content: space-evenly;
      margin-bottom: 20px;
      position: relative;
      div {
        margin-right: 30px;
      }
      img {
        width: 100px;
        height: 100px;
      }
      .title {
        font-size: 28px;
        font-weight: 400;
        text-align: center;
        color: #000000;
        line-height: 42px;
      }
      .first-arrow {
        position: absolute;
        width: 40px;
        height: 40px;
        top: 50px;
        right: 430px;
        font-size: 24px;
      }
      .reversal-arrow {
        position: absolute;
        top: 50px;
        right: 230px;
        font-size: 30px;
        transform-origin: center center;
        transform: rotate(90deg);
      }
    }
    ul {
      margin-left: 30px;
      li {
        font-weight: 400;
        text-align: left;
        color: #999999;
        font-size: 22px;
        margin-bottom: 10px;
      }
    }
  }

  .section4 {
    padding: 0 26px;
    background: linear-gradient(180deg, #ffffff, #ffffff);
    border-radius: 31px;
    .image {
      background: url('../../../../../assets/images/solution/smart-hardware/mobile/usb-desc.png')
        center no-repeat !important;
      background-size: cover !important;
      width: 100%;
      height: 335px;
    }
    ul {
      display: flex;
      flex-wrap: wrap;
      justify-content: flex-start;
      margin-top: 20px;
      padding-left: 30px;
      li {
        width: 150px;
        margin-bottom: 20px;
        .desc-num {
          width: 40px;
          height: 40px;
          background: #06b4ff;
          border-radius: 50%;
          font-weight: 600;
          text-align: center;
          color: #ffffff;
          line-height: 40px;
          margin: 0 auto;
          margin-bottom: 15px;
        }
        .desc-title {
          text-align: center;
          color: #000000;
          font-size: 18px;
          margin: 0 auto;
        }
      }
    }
  }
  .section5 {
    padding: 0 26px;
    width: 100%;
    margin-top: 20px;
    .product-goods {
      width: 100%;
      background-color: #fff;
      padding-top: 25px;
      padding-left: 32px;
      padding-right: 43px;
      background: linear-gradient(180deg, #ffffff, #ffffff);
      border: 3px solid #f0f0f0;
      border-radius: 30px;
      box-shadow: 0px 3px 9px 0px rgba(151, 151, 151, 0.06);
      li {
        width: 100%;
        min-height: 160px;
        padding-left: 24px;
        padding-right: 30px;
        padding-top: 17px;
        box-shadow: 0px -1px 0px 0px #ebebeb inset;
      }
      &-title {
        font-size: 32px;
        line-height: 42px;
        text-align: left;
        color: #000000;
        margin-bottom: 10px;
      }
      &-text {
        font-size: 26px;
        font-weight: 400;
        text-align: left;
        color: #999999;
        line-height: 40px;
      }
    }
  }
  .section6 {
    padding: 0 26px;
    margin-top: 20px;
    ul {
      li {
        width: 695px;
        min-height: 94px;
        background: linear-gradient(180deg, #ffffff, #ffffff);
        border-radius: 30px;
        font-size: 26px;
        font-weight: 400;
        text-align: left;
        color: #2470ff;
        line-height: 40px;
        padding: 27px 47px;
        margin-bottom: 20px;
        padding-right: 20px;
      }
    }
  }

  .section-cooperation {
    width: 100%;
    height: 243px;
    .cooperation-btn {
      width: 222px;
      height: 81px;
      background: linear-gradient(90deg, #26bcf6, #1b7bf7 98%);
      border-radius: 52px;
      text-align: center;
      margin: 0 auto;
      font-weight: 600;
      line-height: 81px;
      font-size: 32px;
      color: #f0f0f0;
      margin-top: 60px;
    }
  }

  .section-footer {
    width: 750px;
    background-color: #061930;
    padding: 30px 20px;
    .footer-title {
      color: #c1c1c1;
      display: flex;
      justify-content: space-between;
      align-items: center;
      cursor: pointer;
      font-size: 28px;
      font-weight: bold;
      margin-bottom: 20px;
    }
    .content {
      margin-top: 10px;
      display: none; /* 默认隐藏内容 */
      font-size: 24px;
      color: #7b7e93;
    }

    .content p {
      margin: 5px 0;
      font-size: 24px;
      line-height: 49px;
    }

    .arrow {
      font-size: 26px;
      transition: transform 0.5s ease;
    }

    .arrow.up {
      transform: rotate(180deg);
    }

    .corporation-info {
      margin-top: 30px;
      color: #7b7e93;
      font-size: 24px;
      text-align: left;
      line-height: 33px;
    }
  }
}
</style>
