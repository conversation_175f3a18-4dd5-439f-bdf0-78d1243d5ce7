<template>
  <div
    class="intent_item"
    :class="{ reduce_padding: reducePadding }"
    @click="handleClick"
  >
    <div class="intent_item_top">
      <div class="icon_wrap">
        <svg-icon
          iconClass="intent-icon"
          class="menu_icon"
          :style="{ width: '20px', height: '20px' }"
        />
      </div>
      <div class="intent_item_right">
        <div class="intent-name">{{ intentData.intentName }}</div>
        <div class="intent-tags">
          <el-tag
            size="mini"
            class="official_tag"
            v-if="intentData.official == 1"
            >官方</el-tag
          >
          <el-tag size="mini">{{ intentData?.intentNameEn || '' }}</el-tag>
          <el-tag size="mini" type="info" @click.stop="toIntentDetail">
            包含示例说法{{
              intentData?.corpusCount ? intentData?.corpusCount : 0
            }}</el-tag
          >
        </div>
      </div>

      <el-dropdown @click.native.stop @command="handleDropdownCommand">
        <span>
          <i class="el-icon-more"></i>
        </span>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item
            command="detailConfig"
            v-if="agentType === 21 && intentData.official !== 1"
            >对话配置</el-dropdown-item
          >

          <el-dropdown-item
            command="detailCorpus"
            v-if="agentType === 21 && intentData.official !== 1"
            >对话示例</el-dropdown-item
          >

          <el-dropdown-item command="edit">{{
            intentData.official ? '查看' : '编辑'
          }}</el-dropdown-item>
          <el-dropdown-item command="del">{{
            intentData.official ? '取消引用' : '删除'
          }}</el-dropdown-item>
          <!-- <el-dropdown-item command="quote">取消引用</el-dropdown-item> -->
        </el-dropdown-menu>
      </el-dropdown>
    </div>

    <div class="intent_desc">
      <el-tooltip
        :disabled="!isOverflow"
        :content="intentData.intentDesc"
        placement="top"
        popper-class="card_tooltip"
      >
        <div class="intent_desc" ref="descRef" @mouseenter="checkOverflow">
          {{ intentData.intentDesc }}
        </div>
      </el-tooltip>
    </div>

    <div
      class="intent_footer"
      v-if="intentData?.toolName || intentData?.keyDataList"
    >
      <div class="intent_showInfo">
        <div class="intent_toolName" v-if="intentData?.toolName">
          <div class="sub_title">{{ intentData?.method || 'GET' }}</div>
          <div style="padding: 3px">{{ intentData?.toolName }}</div>
        </div>
        <div class="intent_keyDataInfo" v-if="intentData?.keyDataList">
          <div class="sub_title">关键信息</div>
          <div
            v-for="item in intentData?.keyDataList"
            :key="item.id"
            style="padding: 3px"
          >
            {{ item.keyDataName + ';' }}
          </div>
        </div>
      </div>

      <!-- <el-dropdown @click.native.stop @command="handleDropdownCommand">
        <span>
          <i class="el-icon-more"></i>
        </span>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item command="edit">{{
            intentData.official ? '查看' : '编辑'
          }}</el-dropdown-item>
          <el-dropdown-item command="del">{{
            intentData.official ? '取消引用' : '删除'
          }}</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown> -->
    </div>
  </div>
</template>

<script>
export default {
  name: 'IntentItem',
  props: {
    intentData: {
      type: Object,
      required: true,
      default: () => ({}),
    },
    reducePadding: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      isOverflow: false,
      agentType: 20,
    }
  },
  created() {
    this.agentType = this.$route.params.agentType * 1
  },
  methods: {
    checkOverflow() {
      this.$nextTick(() => {
        const el = this.$refs.descRef
        if (el) {
          this.isOverflow = el.scrollWidth > el.clientWidth
        }
      })
    },
    handleClick() {
      if (this.agentType === 20) {
        this.toIntentDetail()
      } else if (this.agentType === 21) {
        if (this.intentData.official !== 1) {
          this.$emit('click', this.intentData)
        } else {
          this.toIntentDetail()
        }
      } else {
        // this.$message.warning('工作流类型智能体不允许操作')
        const routeData = this.$router.resolve({
          name: 'studio-handle-platform-intent-detail',
          params: {
            intentId: this.intentData.intentId,
            agentId: this.$route.params.agentId,
            intentVersion: this.intentData.version,
            official: this.intentData.official,
          },
        })
        window.open(routeData.href, '_blank')
      }
    },
    handleDropdownCommand(command) {
      this.$emit('command', command, this.intentData)
    },
    toIntentDetail() {
      this.$router.push({
        name: 'studio-handle-platform-intent-detail',
        params: {
          intentId: this.intentData.intentId,
          agentId: this.$route.params.agentId,
          intentVersion: this.intentData.version,
          official: this.intentData.official,
        },
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.intent_item {
  overflow: hidden;
  cursor: pointer;
  background: #ffffff;
  border: 1px solid #e1e1e1;
  border-radius: 6px;
  padding: 25px 20px;
  transition: border-color 0.3s ease;
  &.reduce_padding {
    padding: 15px 10px;
  }
  &:hover {
    border-color: var(--primary-color, #409eff);
  }

  .intent_item_top {
    display: flex;
    align-items: center;

    .icon_wrap {
      width: 40px;
      height: 40px;
      background: #f3f8ff;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .intent_item_right {
      margin-left: 10px;
      flex: 1;
      width: 0;
    }
  }

  .intent-name {
    color: #000000;
    margin-bottom: 5px;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .intent-tags {
    display: flex;
    gap: 12px;

    .el-tag {
      font-size: 12px;
      margin-left: 0px;
      border: none;
      &.official_tag {
        background-color: #e5e9ff;
        color: #544aff;
      }
    }
  }

  .intent_desc {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: #7a7f8f;
    margin-top: 10px;
  }

  .intent_footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 25px;
    margin-top: 20px;
    .sub_title {
      background-color: #0083ff;
      color: #fff;
      padding: 3px 6px;
      height: 24px;
      border-radius: 5px;
      margin-right: 5px;
    }
    .intent_showInfo {
      display: flex;
      gap: 15px;
      .intent_toolName {
        display: flex;
        align-content: center;
        gap: 10px;
      }
      .intent_keyDataInfo {
        display: flex;
        align-content: center;
        gap: 10px;
      }
    }
  }
}
</style>
