<template>
  <div class="os-scroll">
    <div class="character-page" v-loading="tableData.loading">
      <div class="character-page-head">
        <!-- <i class="ic-r-angle-l-line character-page-head-back" @click="back" /> -->
        <back-icon @click="back" style="margin-right: 10px"></back-icon>
        <div v-if="!editingName" class="character-page-head-title">
          <el-popover placement="bottom" width="264" trigger="click">
            <select-character />
            <span slot="reference" class="character-page-head-title-select">
              <span
                style="max-width: 250px"
                class="txt-ellipsis-nowrap"
                :title="baseCharacter"
                >{{ baseCharacter }}</span
              >
              <i class="ic-r-triangle-down" />
            </span>
          </el-popover>
          <i
            class="ic-r-edit"
            v-if="this.type !== 2"
            style="cursor: pointer; font-size: 20px"
            @click="toEditName"
          />
        </div>
        <div
          v-else
          class="character-page-head-title"
          @keyup.enter="editCharacterName"
        >
          <el-input
            v-model="characterName"
            ref="characterNameInput"
            size="small"
            class="character-page-form-input"
            placeholder="请输入人设名称"
            @input="changename"
          />
          <i
            class="character-page-form-save el-icon-check"
            @click="editCharacterName"
          />
          <i
            class="character-page-form-cancel el-icon-close"
            @mousedown="cancelEditCharacterName"
          />
        </div>
        <div class="header-right">
          <el-button
            size="small"
            style="min-width: 80px"
            @click="openCreateDialog"
            :loading="copyLoading"
            >{{ copyLoading ? '提交中' : '另存为' }}</el-button
          >
          <el-button
            type="primary"
            size="small"
            style="min-width: 80px"
            @click="saveHandle('save')"
            :disabled="!edited"
            :loading="submitLoading"
            >{{ submitLoading ? '提交中' : '保存' }}</el-button
          >
          <el-button
            size="small"
            type="primary"
            style="min-width: 80px"
            @click="structure"
            :loading="structureLoading"
          >
            {{ structureLoading ? '构建中' : '构建' }}</el-button
          >
        </div>
      </div>
      <div v-loading="structureLoading" class="character-page-content">
        <div class="operation-area">
          <el-dropdown
            trigger="click"
            @command="handleCommand"
            placement="bottom-start"
            class="mgb32 fl dropdown-charater"
          >
            <el-button size="small">
              批量操作
              <i class="ic-r-triangle-down el-icon--right" />
            </el-button>
            <el-dropdown-menu
              style="width: 120px; text-align: center"
              slot="dropdown"
            >
              <el-dropdown-item style="padding: 0">
                <el-upload
                  :action="action"
                  accept=".csv, .xlsx, .xls, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel, text/csv"
                  :before-upload="beforeUpload"
                  :file-list="fileList"
                  :on-success="uploaded"
                  :on-error="handleUploadError"
                  :on-change="handleChange"
                  :show-file-list="false"
                  >导入配置</el-upload
                >
              </el-dropdown-item>
              <el-dropdown-item command="export">导出配置</el-dropdown-item>
              <el-dropdown-item command="download">下载模版</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
          <ul
            class="el-upload-list el-upload-list--text chrater"
            v-if="fileList.length"
          >
            <li tabindex="0" class="el-upload-list__item is-success">
              <!---->
              <a class="el-upload-list__item-name">
                <i class="el-icon-document"></i>
                {{ fileList[fileList.length - 1].name }}
              </a>
              <label class="el-upload-list__item-status-label">
                <i class="el-icon-upload-success el-icon-circle-check"></i>
              </label>
              <i class="el-icon-close" @click="handleRemove"></i>
              <i class="el-icon-close-tip">按 delete 键可删除</i>
              <!---->
              <!---->
            </li>
          </ul>
        </div>
        <div>
          <el-tabs class="noborder-tabs mgb32" v-model="activeName" type="card">
            <el-tab-pane
              v-for="tab in tabs"
              :key="tab.categoryId"
              :label="tab.categoryName"
              :name="tab.categoryId.toString()"
            >
              <keep-alive :exclude="exclude">
                <TabContent
                  :ref="'tabContent' + activeName"
                  :class="'tabContent' + activeName"
                  :tab="tab"
                  :tabsObj="tabsObj"
                  :activeName="activeName"
                  @getTableData="getData"
                  @isEdited="isEdited"
                  v-if="activeName === tab.categoryId.toString()"
                />
              </keep-alive>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </div>
    <create-character-dialog
      :dialog="dialog"
      slot="save"
      @saveAsHandle="saveHandle"
      ref="createCharacter"
    />
    <page-leave-tips
      :dialog="leaveDialog"
      @save="saveHandle('save')"
      @noSave="noSave"
      @noJump="noJump"
    />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import dicts from '@M/dicts'
import SelectCharacter from './selectCharacter.vue'
import TabContent from './tabContent.vue'
import CreateCharacterDialog from '../handlePlatform/dialog/createCharacter.vue'

export default {
  name: 'character',
  data() {
    return {
      switchModel: 0,
      value: true,
      flag: false,
      answerType: 1,
      type: '',
      characterId: '',
      baseCharacter: '',
      characterName: '',
      character: {},
      editingName: false,
      dialog: {
        show: false,
        type: 'save',
        title: '另存为',
      },
      submitLoading: false,
      copyLoading: false,
      leaveDialog: {
        show: false,
      },
      routeTo: {},
      edited: false,
      editedList: {},
      tabs: [],
      tabsObj: {},

      activeName: '1',
      tableData: {
        loading: false,
        total: 0,
        page: 1,
        size: 999,
        list: [],
      },
      fileList: [],
      file: {},
      propertyList: {},
      btnUploadOptions: {
        btnText: '上传excel文件',
        tips: '只能上传excel文件',
        sizeLimit: 1024 * 1024 * 5,
        fileType: ['excel'],
        numLimit: 1,
      },
      action: '',
      fileObj: null,
      exclude: '',
      checkCount: 0,
      structureLoading: false,
    }
  },
  computed: {},

  watch: {
    editedList: {
      handler(val, oldVal) {
        for (let key in val) {
          if (val[key]) {
            this.edited = true
            break
          } else {
            this.edited = false
          }
        }
      },
      deep: true,
    },
  },

  mounted() {},
  created() {
    if (this.$route.params.characterId) {
      this.characterId = this.$route.params.characterId
      this.action = `/aiui/web/device/property/excel/import?repositoryId=${this.characterId}`
      this.getTabs()
    } else {
      this.$router.push({ name: 'studio-handle-platform-characters' })
    }
    this.$store.dispatch('aiuiApp/setLimitCount')
  },

  beforeRouteLeave: function (to, from, next) {
    //路由跳转提示
    if (this.edited) {
      this.leaveDialog.show = true
      this.routeTo = to
      next(false)
    } else {
      next()
    }
  },
  mounted() {
    //刷新提示
    this.$nextTick(function () {
      window.onbeforeunload = () => {
        if (this.edited) return '放弃当前未保存内容而关闭页面？'
      }
    })
  },
  methods: {
    isEdited(tab, arr, v) {
      this.editedList['tab' + tab] = v
      this.editedList = Object.assign({}, this.editedList)
      this.propertyList['tab' + tab] = arr
    },
    getData(v) {
      // this.propertyList = this.propertyList.concat(v.list);
      this.characterName = v.context.name || ''
      this.baseCharacter = v.context.name || ''
      this.type = v.context.type || ''
    },
    getTabs() {
      let self = this
      this.$utils.httpPost(
        this.$config.api.STUDIO_CHARACTER_CATEGORY,
        {
          repositoryId: this.characterId,
          category: this.activeName,
          pageIndex: 1,
          pageSize: 999,
        },
        {
          success: (res) => {
            self.tabs = res.data
            res.data.map((item) => {
              this.tabsObj[item.categoryId] = item.categoryName
            })
          },
          error: (err) => {
            console.error(err)
          },
        }
      )
    },

    // 返回
    back() {
      this.$router.push({
        name: 'studio-role',
        query: { roletype: 'character' },
      })
    },
    toEditName() {
      this.editingName = true
      this.$nextTick(function () {
        this.$refs['characterNameInput'] &&
          this.$refs['characterNameInput'].focus()
      })
    },
    //另存为
    openCreateDialog() {
      this.dialog.show = true
      this.dialog.type = 'save'
    },

    //保存
    saveHandle(type, name) {
      let self = this
      if (type === 'copy') {
        self.copyLoading = true
      } else {
        self.submitLoading = true
      }
      let propertyArr = []
      let copyPropertyList = JSON.parse(JSON.stringify(this.propertyList))
      for (let key in copyPropertyList) {
        //这里要处理自定义答复列表数据结构
        copyPropertyList[key].map((item, index) => {
          let arr = []
          item.answerSet.map((answer) => {
            arr.push(answer.answer)
          })
          item.answerSet = arr
        })
        propertyArr = propertyArr.concat(copyPropertyList[key])
      }

      let params = {
        repositoryId: self.characterId,
        repositoryName: type === 'copy' ? name : self.characterName,
        propertyList: propertyArr,
        operation: type === 'copy' ? type : '',
      }

      self.$utils.httpPost(
        self.$config.api.STUDIO_CHARACTER_ATTRIBUTE_EDIT,
        JSON.stringify(params),
        {
          config: {
            headers: {
              'Content-Type': 'application/json',
            },
          },
          noErrorMessage: true,
          success: (res) => {
            self.$message.success('保存成功')
            // self.getData();
            self.editingName = false

            if (type === 'copy') {
              self.copyLoading = false
              self.dialog.show = false
              self.$refs.createCharacter.close()
              self.$router.push({
                params: { characterId: res.data.id },
              })
              self.characterName = res.data.name
              self.baseCharacter = res.data.name
            } else {
              self.$refs['tabContent' + self.activeName][0].getData()
              self.submitLoading = false
            }
            let list = self.editedList
            for (let key in list) {
              list[key] = false
            }
            self.edited = false
          },
          error: (err) => {
            if (type === 'copy') {
              self.copyLoading = false
              self.dialog.show = false
              self.$refs.createCharacter.close()
            } else {
              self.submitLoading = false
            }
            if (err.code === '328001') {
              // let errorText = ''
              // err.data.map((item) => {
              //   errorText += `<p>${item.desc}</p>`
              // })
              // this.$message({
              //   type: 'error',
              //   dangerouslyUseHTMLString: true,
              //   message: errorText,
              // })
              this.$message.error(err.desc)
            } else {
              this.$message.error(err.desc)
            }
            self.edited = true

            // self.$refs["tabContent" + self.activeName][0].getData();
          },
        }
      )
    },
    noSave() {
      this.edited = !this.edited
      if (this.routeTo.name) {
        this.$router.push({
          name: this.routeTo.name,
          params: this.routeTo.params,
        })
      }
      if (this.tabTo) {
        this.selectedTab = this.tabTo
      }
    },
    noJump() {
      this.routeTo = {}
    },
    // 编辑人设名
    editCharacterName() {
      let self = this
      if (!this.characterName) {
        return self.$message.warning('人设名称不能为空')
      }
      if (this.characterName === this.baseCharacter) {
        this.editingName = false
        return
      }
      if (!this.editingName) {
        return
      }
      if (this.characterName.length > 32) {
        return self.$message.warning('人设名称长度不能超过32个字符')
      }
      let reg = /^[a-zA-Z0-9\u4e00-\u9fff]+$/
      if (!reg.test(this.characterName)) {
        return self.$message.warning('人设名称仅支持汉字、字母、数字')
      }
      let params = {
        repositoryId: this.characterId,
        repositoryName: this.characterName,
      }
      this.$utils.httpPost(
        this.$config.api.STUDIO_CHARACTER_ATTRIBUTE_EDIT,
        JSON.stringify(params),
        {
          config: {
            headers: {
              'Content-Type': 'application/json',
            },
          },
          success: (res) => {
            self.$message.success('修改成功')
            self.editingName = false
            self.$refs['tabContent' + self.activeName][0].getData()
          },
          error: (err) => {
            console.error(err)
          },
        }
      )
    },
    changename(v) {
      let self = this
      if (v.indexOf('}') !== -1) {
        var tObj =
          self.$refs.characterNameInput.$el.getElementsByTagName('input')[0]
        var sPos = v.length - 1
      }
    },
    setCaretPosition(tObj, sPos) {
      if (tObj.setSelectionRange) {
        setTimeout(function () {
          tObj.setSelectionRange(sPos, sPos, 'backward')
          tObj.focus()
        }, 0)
      } else if (tObj.createTextRange) {
        var rng = tObj.createTextRange()
        rng.move('character', sPos)
        rng.select()
      }
    },

    // 取消编辑人设名
    cancelEditCharacterName() {
      this.editingName = false
      this.character = this.$deepClone(this.baseCharacter)
    },
    // 批量操作
    handleCommand(command) {
      let self = this
      switch (command) {
        case 'export':
          self.exportExcel()
          break
        case 'download':
          self.downloadExcel()
          break
        default:
          break
      }
    },
    //导出
    exportExcel() {
      this.$utils.postopen(this.$config.api.STUDIO_CHARACTER_EXPORT, {
        repositoryId: this.characterId,
      })
    },
    //下载
    downloadExcel() {
      window.open(
        'https://aiui-file.cn-bj.ufileos.com/Character_Demo.xlsx',
        '_self'
      )
    },
    //导入操作
    handleRemove() {
      this.fileList.splice(0, this.fileList.length)
    },
    uploaded(data) {
      this.tableData.loading = false
      if (data.code === '0') {
        this.$message.success(data.data)
        this.fileList.push(this.fileObj)
        this.exclude = 'TabContent'
        this.$refs['tabContent' + this.activeName][0].getData()
        // window.location.reload();
      } else {
        this.exclude = ''
        let errorText = ''
        data.data.map((item) => {
          errorText += `<p>${item.desc}</p>`
        })
        this.$message({
          type: 'error',
          dangerouslyUseHTMLString: true,
          message: errorText,
        })
      }
    },
    handleUploadError(error) {
      this.tableData.loading = false
      this.$message.error(error.desc)
    },
    handleChange(file) {
      this.fileObj = file
    },
    beforeUpload(files) {
      console.log(this.fileList)
      this.tableData.loading = true
      //  this.fileList.splice(0, this.fileList.length);
      //this.$message.error("最多只能上传一份文件");
    },

    beforeRemove(file, fileList) {
      return this.$confirm(`确定移除 ${file.name}？`)
    },

    // 构建
    structure() {
      let that = this
      this.structureLoading = true
      // 调用接口构建
      this.$utils.httpPost(
        this.$config.api.STUDIO_CHARACTER_STRUCT,
        {
          repositoryId: this.characterId,
        },
        {
          success: (res) => {
            // 构建接口调用后，还需要轮询检查状态
            that.$message.success('提交成功，正在构建...')
            that.checkStatus(res.data.id)
          },
          error: (err) => {
            that.structureLoading = false
          },
        }
      )
    },

    // 轮询检查状态
    checkStatus(id) {
      let self = this
      this.checkCount += 1
      this.$utils.httpGet(
        this.$config.api.STUDIO_CHARACTER_STATUS,
        {
          id,
        },
        {
          success: (res) => {
            if (String(res.data.ok) === '1') {
              if (self.structureLoading) {
                self.$message.success('构建成功')
              }
              self.structureLoading = false
              self.checkCount = 0
            } else if (String(res.data.ok) === '-1') {
              if (self.structureLoading) {
                self.$message.error('构建失败')
              }
              self.structureLoading = false
              self.checkCount = 0
            } else {
              if (self.checkCount < 300) {
                setTimeout(function () {
                  self.checkStatus(id)
                }, 2000)
              } else {
                if (self.structureLoading) {
                  self.$message.error('构建失败')
                }
                self.structureLoading = false
                self.checkCount = 0
              }
            }
          },
          error: (err) => {},
        }
      )
    },
  },
  components: {
    SelectCharacter,
    CreateCharacterDialog,
    TabContent,
  },
}
</script>

<style lang="scss">
td {
  border-top: 1px solid #fff;
  border-left: 1px solid #fff;
  border-right: 1px solid #fff;
}
.valueText {
  display: inline-block;
  width: 100%;
  height: 100%;
  display: none;
}
.valueText.active {
  display: block;
}
.valueInput {
  display: none;
}
.valueInput.active {
  display: block;
}

.character-page {
  &-form-input {
    width: 220px;
  }
  &-form-save,
  &-form-cancel {
    font-size: 18px;
    margin-left: 8px;
    cursor: pointer;
    &:hover {
      color: $primary;
    }
  }
  &-head {
    font-size: 20px;
    height: 63px;
    display: flex;
    align-items: center;
    padding: 0 24px;
    border-bottom: 1px solid #e1e1e1;
    &-back {
      cursor: pointer;
      margin-right: 16px;
      color: $grey4;
    }
    &-title {
      flex: auto;
      display: flex;
      align-items: center;
    }
    &-title-select {
      display: flex;
      align-items: center;
      cursor: pointer;
      margin-right: 16px;
      i {
        padding-left: 8px;
        color: $grey5;
      }
    }
    .txt-ellipsis-nowrap {
      font-size: 20px;
      font-weight: 600;
      color: #000;
    }
  }
  &-handle-bar {
    margin-bottom: 16px;
    display: flex;
    .search-area {
      width: 450px;
      margin-right: 8px;
      font-size: 14px;
    }
    .el-select {
      width: 90px;
    }
  }
  .ic-r-edit {
    color: $primary;
  }
  th {
    height: 39px;
    // line-height: 40px;
    box-sizing: border-box;
  }
  .operation-area {
    font-size: 0;
    overflow: hidden;
    ul.el-upload-list.el-upload-list--text.chrater {
      float: left;
      width: 280px;
      border-bottom: 2px solid #1784e9;
      margin-left: 30px;
    }
  }
}
:deep(.el-dropdown) {
  float: left;
}

.character-qabank {
  padding: 24px;
  display: flex;
  &-qa,
  &-answer {
    flex: 1;
    .text {
      float: left;
      height: 22px;
      font-size: 14px;
      font-weight: 600;
      color: rgba(140, 140, 140, 1);
      line-height: 22px;
      margin-right: 24px;
    }
    .list-content {
      float: left;
      .list {
        margin-left: 17px;
        padding-bottom: 6px;
        margin-bottom: 20px;
        &.dashed-bottom {
          border-bottom: 1px dashed #e4e7ed;
          display: none;
          &.active {
            display: block;
          }
        }
      }
      .answer-radio {
        display: block;
        margin-bottom: 16px;
      }
      li {
        list-style: decimal;
        height: 22px;
        font-size: 14px;
        font-weight: 400;
        color: rgba(38, 38, 38, 1);
        line-height: 22px;
        margin-bottom: 10px;
        &.os-text-adder-row {
          height: 37px;
          line-height: 36px;

          margin-bottom: 0px;
        }
      }
    }
  }
}

.character-page-content {
  height: calc(100vh - 63px);
  overflow: auto;
  padding: 20px;
}
</style>
