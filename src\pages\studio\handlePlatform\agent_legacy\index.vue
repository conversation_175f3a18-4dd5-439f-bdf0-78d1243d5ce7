<template>
  <div class="os-scroll">
    <div class="handle-platform-content" style="padding-top: 20px">
      <!-- <div class="mgb24" style="font-size: 0">
        <el-button
          icon="ic-r-plus"
          type="primary"
          size="medium"
          @click="openAgent('create')"
          >&nbsp;创建智能体</el-button
        >
        <div class="fr" @keyup.enter="getAgent(1)">
          <el-input
            class="search-area"
            placeholder="输入智能体名称进行查询"
            size="medium"
            v-model="searchVal">
            <i
              slot="suffix"
              class="el-input__icon el-icon-search search-area-btn"
              @click="getAgent(1)" />
          </el-input>
        </div>
      </div> -->

      <os-table
        :tableData="tableData"
        style="margin-bottom: 56px"
        class="characters-table gutter-table-style transparent-bgc"
        @change="getAgent"
        @edit="toEdit"
        @copy="copy"
        @del="toDel"
        @row-click="toEdit"
      >
        <el-table-column label="智能体名称">
          <template slot-scope="scope">
            <div v-if="scope.row.official === 0">
              {{ scope.row.agentName }}
            </div>
            <div v-if="scope.row.official === 1">
              {{ scope.row.agentName }}
            </div>
          </template>
        </el-table-column>
        <!-- <el-table-column prop="agentId" label="ID"></el-table-column> -->
        <el-table-column
          prop="agentDesc"
          label="描述"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column label="智能体类型">
          <template slot-scope="scope">
            <div v-if="scope.row.agentType === 0">云端-API</div>
            <div v-if="scope.row.agentType === 1">云端-FaaS</div>
            <div v-if="scope.row.agentType === 2">云端-工作流</div>
            <div v-if="scope.row.agentType === 3">本地</div>
          </template>
        </el-table-column>
        <el-table-column prop="updateTime" label="更新时间"></el-table-column>
        <el-table-column prop="" label="状态">
          <template slot-scope="scope">
            <div v-if="scope.row.agentStatus === 0">未发布</div>
            <div v-if="scope.row.agentStatus === 1">已发布</div>
          </template>
        </el-table-column>
      </os-table>
    </div>

    <CreateAgent ref="CreateAgent" @refresh="refresh"> </CreateAgent>
  </div>
</template>

<script>
import HandlePlatformTop from '../top.vue'
import CreateAgent from './createAgent.vue'
export default {
  name: 'AiuiWebIndex',

  components: {
    HandlePlatformTop,
    CreateAgent,
  },
  data() {
    return {
      searchVal: '',
      tableData: {
        loading: true,
        total: 0,
        page: 1,
        size: 10,
        handles: ['edit', 'del'],
        handleColumnText: '操作',
        list: [],
      },
    }
  },

  created() {
    this.getAgent(1)
  },

  mounted() {
    if (localStorage.getItem('addAgent') === 'true') {
      this.$refs.CreateAgent.show()
      localStorage.setItem('addAgent', false)
    }
  },

  methods: {
    getAgent(page, name) {
      const data = {
        pageIndex: page || this.tableData.page,
        pageSize: this.tableData.size,
        agentName: this.searchVal || name,
      }
      this.$utils.httpPost(
        this.$config.api.AGENT_TABLE_LIST_OLD,
        JSON.stringify(data),
        {
          config: {
            headers: {
              'Content-Type': 'application/json;charset=UTF-8',
            },
          },
          success: (res) => {
            console.log(res, 'agent列表的res')
            if (res.code == 0) {
              this.tableData.list = res.data.data
              this.tableData.total = res.data.totalSize
              this.tableData.page = res.data.pageIndex
              this.tableData.size = res.data.pageSize
              this.tableData.loading = false
            }
          },
          error: (err) => {
            this.tableData.loading = false
            this.$message.error(err?.desc)
          },
        }
      )
    },

    refresh() {
      this.getAgent()
    },

    openAgent() {
      this.$refs.CreateAgent.show()
    },

    toEdit(data) {
      console.log(data)
      this.$router.push({
        name: 'agent-info-legacy',
        params: {
          agentId: data.agentId,
        },
      })
    },
    copy() {},

    toDel(data) {
      console.log(data, '点击删除的data')

      let self = this
      this.$confirm(
        '智能体删除后不可恢复，请谨慎操作。',
        `确定删除该智能体吗?`,
        {
          confirmButtonText: '确定删除',
          cancelButtonText: '取消',
          confirmButtonClass: 'el-button--danger',
          type: 'warning',
          showClose: false,
        }
      )
        .then(() => {
          self.delAgent(data)
        })
        .catch(() => {})
    },
    delAgent(data) {
      const params = {
        agentId: data.agentId,
      }
      this.tableData.loading = true
      this.$utils.httpPost(
        this.$config.api.AGENT_DELETE_OLD,
        JSON.stringify(params),
        {
          config: {
            headers: {
              'Content-Type': 'application/json;charset=UTF-8',
            },
          },
          success: (res) => {
            this.$message.success('删除成功')
            if (this.tableData.list.length === 1 && this.tableData.page > 1) {
              this.tableData.page -= 1
            }
            this.getAgent()
          },
          error: (err) => {
            this.tableData.loading = false
            this.$message.error(err.desc)
          },
        }
      )
    },
  },
}
</script>

<style lang="scss" scoped>
.handle-platform-content {
  width: 100%;
  margin: auto;
}
.search-area {
  width: 480px;
}
</style>
