<template>
  <el-dialog
    class="add-category-dialog"
    :title="title[dialog.type]"
    :visible.sync="dialog.show"
    width="570px"
  >
    <div class="div-add-category-dialog">
      <ul>
        <li>
          <p>专辑名称：</p>
          <el-input
            class="content-name"
            :class="{ 'content-name-border-show': contentNameShow }"
            v-model="albumName"
            placeholder="请输入专辑名称，不超过20个字符"
            @input="checkChange($event)"
            @blur="checkBlank($event)"
          ></el-input>
          <div
            class="content-name-tips"
            :class="{ 'content-name-tips-show': contentNameShow }"
          >
            请输入专辑名称，不超过20个字符
          </div>
        </li>
        <li>
          <p>专辑封面：</p>
          <a-upload
            ref="fileUpload"
            :class="{
              'content-name-upload-border-show':
                !(fileList !== undefined && fileList.length > 0) &&
                uploadBorderShow,
            }"
            :action="`${
              this.$config.api.RECOMMEND_CONTENT_ALBUMUPLOAD +
              '?appid=' +
              this.$route.params.appId
            }`"
            list-type="picture-card"
            accept=".jpg,.jpeg,.png,.gif,.bmp"
            :file-list="fileList"
            :auto-upload="false"
            :customRequest="customRequest"
            @preview="handlePreview"
            @change="handleChange"
            :before-upload="beforeUpload"
            :headers="{ 'X-Csrf-Token': token }"
          >
            <div
              v-if="fileList !== undefined && fileList.length < 1"
              :class="{ 'album-tips-2-show': albumShow }"
            >
              <a-icon type="plus" />
              <div class="ant-upload-text">上传封面</div>
            </div>
          </a-upload>
          <a-modal
            :zIndex="999999"
            :visible="previewVisible"
            :footer="null"
            @cancel="handleCancel"
          >
            <img alt="example" style="width: 100%" :src="previewImage" />
          </a-modal>
          <div
            class="content-name-tips content-name-tips-2"
            :class="{ 'content-name-tips-2-show': albumShow }"
          >
            请上传专辑封面
          </div>
        </li>
        <li>
          <el-button
            class="template-btn"
            size="small"
            type="primary"
            style="min-width: 80px"
            @click="btnClick"
          >
            {{ '确认' }}
          </el-button>
          <el-button
            class="template-btn"
            size="small"
            style="min-width: 80px"
            @click="cancelClick"
          >
            {{ '取消' }}
          </el-button>
        </li>
        <li>
          <el-dialog
            title="图片剪裁"
            :visible.sync="dialogVisible"
            :append-to-body="true"
          >
            <div class="cropper-content">
              <div class="cropper" style="text-align: center">
                <vueCropper
                  ref="cropper"
                  :img="option.img"
                  :outputSize="option.outputSize"
                  :outputType="option.outputType"
                  :info="option.info"
                  :canScale="option.canScale"
                  :autoCrop="option.autoCrop"
                  :autoCropWidth="option.autoCropWidth"
                  :autoCropHeight="option.autoCropHeight"
                  :fixed="option.fixed"
                  :fixedBox="option.fixedBox"
                  :fixedNumber="option.fixedNumber"
                ></vueCropper>
              </div>
            </div>
            <div slot="footer" class="dialog-footer">
              <el-button @click="doCancel">取 消</el-button>
              <el-button type="primary" @click="finish" :loading="loading"
                >确认</el-button
              >
            </div>
          </el-dialog>
        </li>
      </ul>
    </div>
  </el-dialog>
</template>

<script>
import { VueCropper } from 'vue-cropper'
import Icon from 'ant-design-vue/lib/icon'
import Upload from 'ant-design-vue/lib/upload'
import Modal from 'ant-design-vue/lib/modal'
import 'ant-design-vue/lib/icon/style/css'
import 'ant-design-vue/lib/upload/style/css'
import 'ant-design-vue/lib/modal/style/css'
function getBase64(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.readAsDataURL(file)
    reader.onload = () => resolve(reader.result)
    reader.onerror = (error) => reject(error)
  })
}
export default {
  name: 'addCategoryDialog',
  components: {
    VueCropper,
    [Icon.name]: Icon,
    [Upload.name]: Upload,
    [Modal.name]: Modal,
  },
  props: {
    dialog: {
      type: Object,
      default: {
        show: false,
      },
    },
    skillId: '',
    outNumber: '',
  },
  data() {
    return {
      fileUrl: null,
      previewImage: '',
      previewVisible: false,
      albumName: null,
      cover: null,
      contentNameShow: false,
      albumShow: false,
      uploadBorderShow: false,
      title: {
        0: '编辑专辑',
        1: '新增专辑',
      },
      uploadFile: null,
      fileList: [
        /*{
              uid: '-1',
              name: 'image.png',
              status: 'done',
              url: 'https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png',
            },*/
      ],
      canUpload: false,
      imageUrl: null,
      imgUrl: null,
      dialogVisible: false,
      // 裁剪组件的基础配置option
      option: {
        img: '', // 裁剪图片的地址
        info: true, // 裁剪框的大小信息
        outputSize: 0.8, // 裁剪生成图片的质量
        outputType: 'png', // 裁剪生成图片的格式
        canScale: true, // 图片是否允许滚轮缩放
        autoCrop: true, // 是否默认生成截图框
        autoCropWidth: 200, // 默认生成截图框宽度
        autoCropHeight: 200, // 默认生成截图框高度
        fixedBox: true, // 固定截图框大小 不允许改变
        fixed: true, // 是否开启截图框宽高固定比例
        fixedNumber: [1, 1], // 截图框的宽高比例
        full: true, // 是否输出原图比例的截图
        canMoveBox: false, // 截图框能否拖动
        original: false, // 上传图片按照原始比例渲染
        centerBox: false, // 截图框是否被限制在图片里面
        infoTrue: true, // true 为展示真实输出图片宽高 false 展示看到的截图框宽高
        canMove: true,
      },
      picsList: [], //页面显示的数组
      // 防止重复提交
      loading: false,
      fileinfo: {},
      token: localStorage.getItem('AIUI_GLOBAL_VARIABLE'),
    }
  },
  watch: {
    'dialog.show'() {
      this.contentNameShow = false
      this.albumShow = false
      this.uploadBorderShow = false
      this.uploadFile = null
      this.previewImage = ''
      this.fileList = []
      if (this.dialog.show === true) {
        if (this.dialog.album) {
          this.albumName = this.dialog.album.albumName
          this.imageUrl = this.dialog.album.cover
          /*this.fileList = [
                {
                  uid: Math.random(),
                  name: 'image.png',
                  status: 'done',
                  url: this.dialog.album.cover
                }
              ]*/
        }
      } else {
        this.albumName = null
        this.cover = null
        this.contentName = null
        this.imageUrl = null
        this.fileList = []
      }
      this.$forceUpdate()
    },
    fileList() {
      if (this.fileList !== undefined && this.fileList.length > 0) {
        this.albumShow = false
        this.uploadBorderShow = false
      } /* else {
            if (this.dialog.show) {
              this.albumShow = true
              this.uploadBorderShow = true
            }
          }*/
    },
    imageUrl: function () {
      this.fileList = []
      if (this.imageUrl != null) {
        this.fileList = [
          {
            uid: Math.random(),
            name: 'image.png',
            status: 'done',
            url: this.imageUrl,
            originFile: this.uploadFile,
          },
        ]
      } else {
        this.fileList = []
      }
      this.$forceUpdate()
    },
  },
  computed: {
    appId() {
      return this.$route.params.appId
    },
  },
  methods: {
    transformFile(file) {
      return new Promise((resolve, reject) => {
        resolve(file)
      })
    },
    handleCancel() {
      this.previewVisible = false
    },
    async handlePreview(file) {
      if ((!file.url || !file.preview) && file.originFile !== null) {
        file.preview = await getBase64(file.originFile)
      }
      this.previewImage = file.preview || file.url
      this.previewVisible = true
    },
    /*async handlePreview(file) {
          if (this.dialog.type === 1) {
            file.preview = await getBase64(this.uploadFile)
          }
          this.previewImage = file.preview || file.url;
          this.previewVisible = true;
        },*/
    handleChange({ fileList }) {
      let self = this
      if (fileList == null || fileList.length == 0) {
        this.imageUrl = null
      }
    },
    customRequest(action) {
      let formData = new FormData(),
        thiz = this
      formData.append('file', this.uploadFile)
      this.$utils.httpPost(action, formData, {
        config: {
          headers: {},
          //responseType: "blob"
        },
        success: (res) => {
          if (res.flag) {
            this.dialogVisible = false
            thiz.loading = false
            setTimeout(() => {
              thiz.imageUrl = res.data.url
            }, 500)
            //thiz.$refs.fileUpload
          } else {
            this.$message.error(res.data)
          }
        },
        error: (err) => {},
      })
    },
    // base-64格式转换方法
    getBase64(file) {
      return new Promise(function (resolve, reject) {
        let reader = new FileReader()
        let imgResult = ''
        reader.readAsDataURL(file)
        reader.onload = function () {
          imgResult = reader.result
        }
        reader.onerror = function (error) {
          reject(error)
        }
        reader.onloadend = function () {
          resolve(imgResult)
        }
      })
    },
    beforeUpload(file) {
      const isFormat =
        file.type === 'image/jpg' ||
        file.type === 'image/jpeg' ||
        file.type === 'image/png' ||
        file.type === 'image/gif'
      if (!isFormat) {
        this.$message.error('图片格式只能是 JPG/JPEG/PNG/GIF 格式!')
        return false
      }
      /*const isLt2M = file.size / 1024 / 1024 < 2;

          if (!isJPG) {
            this.$message.error('上传头像图片只能是 JPG/JPEG/PNG 格式!');
            return false
          }
          if (!isLt2M) {
            this.$message.error('上传头像图片大小不能超过 2MB!');
            return false
          }*/
      this.getBase64(file).then((res) => {
        this.dialogVisible = true
        this.option.img = res
      })

      return false
    },
    finish() {
      let thiz = this
      this.$refs.cropper.getCropBlob((data) => {
        let aTime = new Date().getTime() // 取时间戳，给文件命名
        let fileName = aTime + '.' + data.type.substr(6) // 给文件名加后缀
        let file = new window.File([data], fileName, { type: data.type }) // blob转file
        thiz.uploadFile = file
        thiz.imgUrl = data
        thiz.loading = true
        thiz.$refs.fileUpload.customRequest(thiz.$refs.fileUpload.action)
      })
    },
    doCancel() {
      this.loading = false
      this.dialogVisible = false
    },
    checkBlank(e) {
      if (e.target.value) {
        this.contentNameShow = false
      } else {
        this.contentNameShow = true
        return
      }
      if (
        e.target.value.length >
        20 /*|| /[^\u4E00-\u9FA5]/g.test(e.target.value)*/
      ) {
        this.contentNameShow = true
        this.$message.error('长度不超过20个字符')
        return
      }
    },
    checkChange(e) {
      if (e) {
        this.contentNameShow = false
      } else {
        this.contentNameShow = true
      }
    },
    btnClick() {
      if (
        !this.albumName ||
        this.albumName.length >
          20 /*|| /[^\u4E00-\u9FA5]/g.test(this.albumName)*/
      ) {
        this.contentNameShow = true
      }
      if (!this.fileList.length > 0) {
        this.albumShow = true
        this.uploadBorderShow = true
      }
      if (this.contentNameShow || this.albumShow || this.uploadBorderShow) {
        return
      }
      if (this.dialog.type == 0) {
        this.$utils.httpPost(
          this.$config.api.RECOMMEND_CONTENT_UPDATE_ALBUM,
          {
            appid: this.appId,
            id: this.dialog.album.id,
            albumName: this.albumName,
            cover: this.imageUrl,
          },
          {
            noErrorMessage: true,
            success: (res) => {
              if (res.flag) {
                this.$message.success(res.desc)
                this.dialog.show = false
                //this.$emit('getSceneList')
                res.data = {
                  ...res.data,
                  type: this.dialog.album.type,
                }
                this.$emit(
                  'addOrUpdateAlbumLocal',
                  0,
                  res.data,
                  this.dialog.category,
                  this.dialog.index_1,
                  this.dialog.index_2
                )
              } else {
                this.$message.error(
                  res.desc === 'Duplicate name !' ? '名称不能重复' : res.desc
                )
              }
            },
            error: (err) => {
              this.$message.error(
                err.desc === 'Duplicate name !' ? '名称不能重复' : err.desc
              )
            },
          }
        )
      } else {
        this.$utils.httpPost(
          this.$config.api.RECOMMEND_CONTENT_ADDALBUM,
          {
            appid: this.appId,
            albumName: this.albumName,
            cover: this.imageUrl,
            contentTypeId: this.dialog.category.id, // 所属分类ID
          },
          {
            noErrorMessage: true,
            success: (res) => {
              if (res.flag) {
                this.$message.success(res.desc)
                this.dialog.show = false
                res.data = {
                  ...res.data,
                  type: -1,
                }
                this.$emit(
                  'addOrUpdateAlbumLocal',
                  1,
                  res.data,
                  this.dialog.category,
                  this.dialog.index_1,
                  this.dialog.index_2
                )
                //this.$emit('getSceneList')
                //this.$emit('getAlbumOrSongById', this.dialog.category, this.dialog.index_1, this.dialog.index_2)
              } else {
                this.$message.error(
                  res.desc === 'Duplicate name !' ? '名称不能重复' : res.desc
                )
              }
            },
            error: (err) => {
              this.$message.error(
                err.desc === 'Duplicate name !' ? '名称不能重复' : err.desc
              )
            },
          }
        )
      }
    },
    cancelClick() {
      this.fileList = []
      this.albumName = null
      this.dialog.show = false
    },
  },
}
</script>

<style>
.ant-upload-select-picture-card i {
  font-size: 32px;
  color: #999;
}

.ant-upload-select-picture-card .ant-upload-text {
  margin-top: 8px;
  color: #666;
}

.avatar-uploader {
  background: red !important;
  width: 150px;
  height: 150px;
  text-align: center;
  line-height: 150px;
}
.el-icon-plus {
  width: 150px;
  height: 150px;
  font-size: 30px;
}
.cropper-content {
  width: 500px;
  height: 500px;
  background: pink;
}
.cropper {
  width: 500px;
  height: 500px;
  background: yellow;
}
</style>

<style scoped lang="scss">
.div-add-category-dialog {
  padding-bottom: 3%;
  position: relative;
  > ul > li {
    display: flex;
    justify-content: center;
    margin-bottom: 5%;
    > p {
      width: 20%;
      line-height: 3;
      font-size: 15px;
      text-align: center;
      vertical-align: middle;
    }
    .content-name {
      /*width: 80%;*/
      transition: color 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
      :deep(&-border-show > input) {
        background-color: #fff;
        border-color: #f5222d;
      }
      &-upload-border-show {
        :deep(.ant-upload) {
          border-color: #f5222d;
        }
      }
      &-tips {
        color: #f5222d;
        position: absolute;
        display: none;
        visibility: hidden;
        margin: 0 auto;
        top: 17%;
        justify-content: left;
        left: 19.5%;
        &-2 {
          top: 62%;
          &-show {
            display: flex;
            visibility: visible;
          }
        }
        &-show {
          display: flex;
          visibility: visible;
        }
      }
    }
  }
}
</style>
