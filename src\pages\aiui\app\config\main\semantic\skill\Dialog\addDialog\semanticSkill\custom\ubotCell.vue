<template>
  <div>
    <div
      :class="['skill', { 'skill-active': item.used }]"
      @click="toKeyQa(item)"
    >
      <div :class="['content-wrap', { 'cursor-default': subAccount }]">
        <div
          v-if="item.outNumber && item.outNumber !== item.newestNumber"
          class="update-state-label"
          style="background: #ff5a5a"
        >
          可更新
        </div>
        <i class="skill-icon" :style="{ backgroundColor: item.color }">{{
          item.name && item.name.substr(0, 1)
        }}</i>
        <div class="skill-info">
          <p class="skill-title" :title="item.name">
            {{ item.name }}
          </p>

          <!-- 标题下按钮 -->
          <div class="title-btm-btn-group">
            <p class="ability-tag">关键词问答</p>
            <p
              v-if="item.outNumber"
              :title="item.outNumber"
              style="margin-left: 8px; padding-top: 4px"
              class="ability_num"
            >
              {{ item.outNumber }}
            </p>
          </div>
        </div>
      </div>

      <div @click.stop class="switch-wrap">
        <el-switch
          :disabled="!subAccountEditable"
          size="small"
          v-model="item.used"
          @change="(val) => onSwitchChange(val, item)"
        >
        </el-switch>
      </div>
      <!-- 下面的label area -->
      <div
        v-if="subAccountEditable && item.used"
        :class="[
          'label-wrap',
          {
            'cursor-default': subAccount,
          },
        ]"
      >
        <p class="skill-desc">
          <span></span>
        </p>
        <i
          @click.prevent.stop="showThresholdInfo(item)"
          class="skill-config-new AIUI-myapp-iconfont ai-myapp-setting2"
          v-if="subAccountEditable && item.used"
        ></i>
      </div>
    </div>
    <threshold-info
      :thresholdVisible="thresholdVisible"
      :configSkillItem="configSkillItem"
      :appId="appId"
      :currentScene="currentScene"
      :ubotQaConfig="ubotQaConfig"
      @thresholdVisibleChange="onThresholdVisibleChange"
      @change="$emit('change')"
    ></threshold-info>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import thresholdInfo from './ubotCellThresholdInfo.vue'

export default {
  components: { thresholdInfo },
  data() {
    return {
      thresholdVisible: false,
      configSkillItem: {},
    }
  },

  props: {
    item: Object,
    ubotQaConfig: Object,
    currentScene: Object,
    appId: String,
  },
  computed: {
    ...mapGetters({
      subAccount: 'user/subAccount',
      subAccountAppAuths: 'aiuiApp/subAccountAppAuths',
      subAccountSkillAuths: 'aiuiApp/subAccountSkillAuths',
    }),
    subAccountEditable() {
      return this.subAccountAppAuths[this.$route.params.appId] == 2
        ? false
        : true
    },
  },
  methods: {
    onThresholdVisibleChange(val) {
      this.thresholdVisible = val
    },
    showThresholdInfo(item) {
      this.configSkillItem = item
      this.thresholdVisible = true
    },
    onSwitchChange(val, item) {
      console.log('onSwitchChange', val, item)
      this.$emit('change')
      let data
      let operation = val ? 'open' : 'close'
      // 自定义问答
      data = {
        qaId: item.id,
        operation,
      }
      this.ubotQaConfig[item.id] = data
    },
    toKeyQa(item) {
      let self = this
      if (self.subAccount) return
      window.open(`/studio/keyQABank/${item.repositoryId}/${item.id}`, '_blank')
    },
  },
}
</script>
<style lang="scss" scoped>
@import '../style.scss';

.content-wrap {
  display: flex;
  // height: 70px;
}
// .skill-config-new {
//   color: #b8babf;
//   font-size: 18px;
//   cursor: pointer;
//   padding-left: 10px;
// }
</style>
