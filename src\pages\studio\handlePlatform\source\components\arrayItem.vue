<template>
  <div>
    <!-- 当前项 -->
    <el-form-item :prop="`children.${index}.defaultValue`">
      <el-row :gutter="20" class="item-row">
        <el-col :span="8">
          <el-input v-model="item.name" disabled />
        </el-col>
        <el-col :span="6">
          <el-select
            v-model="item.type"
            @change="handleTypeChange"
            :disabled="nestingLevel > 0"
          >
            <el-option label="string" value="string" />
            <el-option label="number" value="number" />
            <el-option label="object" value="object" />
            <el-option label="array" value="array" />
          </el-select>
        </el-col>
        <el-col :span="5">
          <el-input
            v-if="item.type !== 'object' && item.type !== 'array'"
            v-model="item.defaultValue"
            placeholder="默认值"
          />
          <span v-else>-</span>
        </el-col>
        <el-col :span="2">
          <i class="el-icon-remove" @click="$emit('remove', index)" />
        </el-col>
        <el-col :span="3" v-if="nestingLevel === 0">
          <template v-if="item.type === 'object'">
            <i
              class="el-icon-circle-plus"
              @click="$emit('add-child', index, 'object')"
            />
          </template>
          <template v-if="item.type === 'array'">
            <i
              class="el-icon-circle-plus"
              @click="$emit('add-child', index, 'array')"
            />
          </template>
        </el-col>
      </el-row>
    </el-form-item>

    <!-- 递归渲染子项 -->
    <template v-if="item.children && item.children.length">
      <div
        v-for="(child, childIndex) in item.children"
        :key="child.id"
        :style="{ marginLeft: `${20 * (nestingLevel + 1)}px` }"
      >
        <array-item
          :item="child"
          :index="childIndex"
          :nesting-level="nestingLevel + 1"
          @remove="(i) => handleRemoveChild(index, i)"
          @add-child="(i, t) => handleAddNestedChild(index, i, t)"
        />
      </div>
    </template>
  </div>
</template>

<script>
export default {
  name: 'ArrayItem',
  props: {
    item: Object,
    index: Number,
    nestingLevel: Number,
  },
  methods: {
    handleTypeChange(newType) {
      if (newType === 'object' || newType === 'array') {
        this.$set(this.item, 'children', [])
      } else {
        this.$set(this.item, 'children', undefined)
      }
    },

    handleRemoveChild(parentIndex, childIndex) {
      this.item.children.splice(childIndex, 1)
    },

    handleAddNestedChild(parentIndex, childIndex, type) {
      this.$emit('add-child', parentIndex, type)
    },
  },
}
</script>

<style scoped>
.item-row {
  margin-bottom: 10px;
}
</style>
