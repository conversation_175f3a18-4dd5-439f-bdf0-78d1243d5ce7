<template>
  <div>
    <div class="chain-wrapper">
      <div class="chain-list swiper" id="chain-swiper">
        <div class="swiper-wrapper">
          <div
            :class="{
              'chain-unit': true,
              small: true,
              'swiper-slide': true,
              active: currentChain === item.chainId,
              disabled: item.disabled,
            }"
            v-for="item in allChains"
            :key="item.chainName"
            @click="setActive(item)"
          >
            <div class="chain-title">{{ item.chainName }}</div>
            <!-- <div class="chain-desc" v-html="item.chainDesc"></div> -->
            <!-- <div
              class="chain-check"
              v-if="currentChain === item.chainId"
            ></div>
            <div class="chain-line" :style="{ background: item.bg }"></div> -->
            <div
              class="chain-icon"
              :style="{
                backgroundImage:
                  'url(' +
                  require(`@A/images/model-exeperience/v2/${item.localIcon}.png`) +
                  ')',
              }"
            ></div>
          </div>
        </div>
      </div>
      <div
        class="indicator indicator-pre"
        v-if="currentPage > 1"
        @click="prePage"
      >
        <i class="el-icon-arrow-left"></i>
      </div>
      <div
        class="indicator indicator-next"
        v-if="currentPage < pageNums"
        @click="nextPage"
      >
        <i class="el-icon-arrow-right"></i>
      </div>
    </div>
    <!-- <ul class="pagination">
      <li
        v-for="(item, index) in pageNums"
        :key="index"
        :class="{ active: index + 1 === currentPage }"
        @click="setCurrentPage(index)"
      ></li>
    </ul> -->
    <div class="chain-divider">
      <div class="chain-divider-line"></div>
      <span class="chain-divider-text">交互链路选择</span>
    </div>
  </div>
</template>
<script>
import Swiper from 'swiper'

const PAGESIZE = 6

export default {
  data() {
    return {
      allChains: [],
      // 翻页相关
      currentPage: 1,
      swiper: null,
    }
  },

  props: {
    currentChain: String,
    current: String,
  },
  created() {
    this.getChains()
  },
  computed: {
    pageNums() {
      return Math.ceil(this.allChains.length / PAGESIZE)
    },
  },
  methods: {
    setCurrentPage(index) {
      this.currentPage = index + 1
      this.swiper && this.swiper.slideTo(index * PAGESIZE)
    },
    nextPage() {
      if (this.currentPage < this.pageNums) {
        this.currentPage++
        this.swiper && this.swiper.slideNext()
      }
    },
    prePage() {
      if (this.currentPage > 1) {
        this.currentPage--
        this.swiper && this.swiper.slidePrev()
      }
    },
    getChains() {
      this.allChains = [
        {
          chainName: 'AIUI普通话版',
          chainDesc: '普通话输入，普通话输出<br/>关键信息结构化输出',
          chainId: 'chinese',
          localIcon: 'Chinese',
          // 语种
          language: '',
          accent: '',
        },
        {
          chainName: 'AIUI粤语版',
          chainDesc: '粤语输入，粤语输出<br/>关键信息结构化输出',
          chainId: 'cantonese',
          localIcon: 'Cantonese',
          // 语种
          language: 'zh_cn',
          accent: 'cantonese',
        },
        {
          chainName: 'AIUI英文版',
          chainDesc: '英文输入，英文输出<br/>关键信息结构化输出',
          chainId: 'english',
          localIcon: 'English',
          // 语种
          language: 'en_us',
          accent: 'mandarin',
        },
      ]
      this.$nextTick(() => {
        this.swiper = new Swiper('#chain-swiper', {
          slidesPerView: PAGESIZE,
          spaceBetween: 10,
          slidesPerGroup: PAGESIZE,
        })
      })
    },

    setActive(item) {
      this.$emit('selected', item.chainId)
    },
  },
}
</script>
<style lang="scss" scoped>
@import './common.scss';
</style>
