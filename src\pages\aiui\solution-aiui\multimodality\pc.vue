<template>
  <div class="main-content">
    <section class="main-content-banner">
      <div class="banner-text">
        <h2>多模态降噪解决方案</h2>
        <p class="banner-text-content">
          声音、图像多维感知；<br />
          支持多风格回复、个性化合成、虚拟人交互；<br />
          打造自然的人机交互体验。
        </p>

        <div class="banner-text-button" @click="toConsole">合作咨询</div>
      </div>
    </section>
    <section class="section section-1">
      <div class="section-title">
        <i class="arrow arrow-left"></i
        ><span class="section-title-bold">应用场景</span
        ><i class="arrow arrow-right"></i>
      </div>
      <div class="section-desc">
        适用于各种自助终端、机器人、大屏一体机等产品，显著提升交互体验
      </div>
      <ul class="interact-list">
        <li
          v-for="(item, index) in interactList"
          :key="index"
          :class="item.klass"
        >
          <h1>{{ item.name }}</h1>
          <div class="desc-wrap">
            <h2>{{ item.name }}</h2>
            <p>
              {{ item.desc }}
            </p>
          </div>
          <div class="overlay"></div>
        </li>
      </ul>
    </section>
    <section class="section section-2">
      <div class="section-title">
        <i class="arrow arrow-left"></i
        ><span class="section-title-bold">方案介绍</span
        ><i class="arrow arrow-right"></i>
      </div>
      <div class="section-content">
        <ul>
          <li>
            <div class="part-title">声音感知</div>
            <div class="icon-solution icon_gender_age"></div>
            <p>性别年龄检测</p>
            <p>性别年龄检测准确率超95%</p>
          </li>
          <li>
            <div class="icon-solution icon_voiceprint"></div>
            <p>声纹检测</p>
            <p>支持声纹的检索与对比验证</p>
          </li>
          <li>
            <div class="icon-solution icon_hyperdirectional_wave"></div>
            <p>超指向波束</p>
            <p>±15°范围内精准拾音</p>
          </li>
        </ul>
      </div>
      <div class="section-content">
        <ul>
          <li>
            <div class="part-title">图像感知</div>
            <div class="icon-solution icon_face_detection"></div>
            <p>人脸识别</p>
            <p>识别人脸信息、开启主动交互</p>
          </li>
          <li>
            <div class="icon-solution icon_lipping"></div>
            <p>唇动检测</p>
            <p>有效提升语音识别准确率</p>
          </li>
          <li>
            <div class="icon-solution icon_gesture"></div>
            <p>手势识别</p>
            <p>支持27种常用手势交互</p>
          </li>
        </ul>
      </div>
      <div class="section-content">
        <ul>
          <li>
            <div class="part-title">多样交互</div>
            <div class="icon-solution icon_virtual_human"></div>
            <p>虚拟人交互</p>
            <p>虚拟人实时互动，动作表情生动</p>
          </li>
          <li>
            <div class="icon-solution icon_multi_style"></div>
            <p>多风格回复</p>
            <p>成人儿童感知，回复风格自然</p>
          </li>
          <li>
            <div class="icon-solution icon_personalized_synthesis"></div>
            <p>个性化合成</p>
            <p>口语化情感化发音，交流更亲切</p>
          </li>
        </ul>
      </div>
    </section>
    <section class="section section-3">
      <div class="section-title">
        <i class="arrow arrow-left"></i
        ><span class="section-title-bold">方案特点</span
        ><i class="arrow arrow-right"></i>
      </div>
      <ul>
        <li>
          <div class="icon-adv icon_combination"></div>
          <p>支持能力随意组合</p>
        </li>
        <li>
          <div class="icon-adv icon_offline"></div>
          <p>支持离线使用</p>
        </li>
        <li>
          <div class="icon-adv icon_integration"></div>
          <p>支持软核集成</p>
        </li>
        <li>
          <div class="icon-adv icon_thirdparty"></div>
          <p>支持适配第三方</p>
        </li>
      </ul>
    </section>
    <corp @jump="toConsole">
      <template> 提交信息，我们会尽快与您联系</template>
    </corp>
    <!-- <section class="section section-4">
      <div class="section-title">
        <p class="section-title-bold">合作咨询</p>
        <p>提交信息，我们会尽快与你联系</p>
      </div>
      <div class="section-item">
        <aiui-button hasTop @click.native="toConsole">申请合作</aiui-button>
      </div>
    </section> -->
  </div>
</template>

<script>
import '../../../../../static/vue-awesome-swiper'
import information from './information.vue'
import corp from '@P/aiui/solution-aiui/components/corp.vue'

export default {
  name: 'multimodality',
  data() {
    return {
      interactList: [
        {
          name: '大屏一体机',
          desc: '使用人脸识别、唇动检测、手势识别和麦克风阵列技术，助力虚拟人在公共高噪场所同顾客亲切交互。',
          klass: 'img_integrated_machine',
        },
        {
          name: '服务机器人',
          desc: '使用人脸识别、唇动检测、多风格回复、个性化合成等技术，助力机器人实现主动唤醒、人脸跟随，同用户进行个性化交流。',
          klass: 'img_service_robot',
        },
        {
          name: '智能健身镜',
          desc: '使用手势识别、口语化合成等技术，助力健身镜读懂用户的动作、成为用户的私人教练。',
          klass: 'img_fitness_mirror',
        },
      ],

      activeName: '0',
      swiperOption: {
        loop: true,
        autoplay: {
          delay: 4000,
          // disableOnInteraction: false,
        },
      },
    }
  },
  mounted() {},
  methods: {
    toConsole() {
      // 判断是否有百度SEM营销参数，有的话，提交表单页需要带上
      const search = window.location.search
      if (search) {
        window.open(`/solution/apply/16${search}`)
      } else {
        window.open('/solution/apply/16')
      }
    },
    toggleActive(val) {
      // this.activeName = val
      this.swiper.slideToLoop(Number(val))
    },
  },
  components: { information, corp },
}
</script>

<style lang="scss" scoped>
.main-content {
  &-banner {
    background: url(~@A/images/solution/multimodality/img_multimodal_bg.png)
      center no-repeat;
    background-size: cover;
    height: 500px;
    overflow: hidden;
    width: 100%;
    .banner-text {
      max-width: 1200px;
      color: #fff;
      height: 100%;
      margin: auto;
      &-button {
        font-size: 16px;
        text-align: center;
        font-weight: 400;
        width: 140px;
        height: 40px;
        line-height: 40px;
        border: 1px solid #fff;
        border-radius: 40px;
        color: #fff;
        cursor: pointer;
        transition: 0.6s;
        // &:hover {
        //   color: #002985;
        //   background: #fff;
        //   transition: 0.3s;
        // }
      }
      h2 {
        color: #fff;
        padding-top: 148px;
        margin-bottom: 29px;
        font-size: 48px;
        font-weight: 500;
        line-height: 48px;
      }
      p {
        font-size: 18px;
        margin-bottom: 74px;
      }

      .banner-text-content {
        width: 570px;
        font-size: 16px;
        font-family: SourceHanSansSC-Regular, SourceHanSansSC;
        font-weight: 400;
        color: rgba(255, 255, 255, 0.86);
        line-height: 30px;
      }
    }
  }

  .section {
    p,
    ul {
      margin-bottom: 0;
    }
    max-width: 1200px;
    overflow: hidden;
    margin: 0 auto;
    .section-title {
      text-align: center;
      font-size: 34px;
      font-family: SourceHanSansSC-Medium, SourceHanSansSC;
      font-weight: bold;
      color: #333;
      line-height: 34px;
      position: relative;
      width: 200px;
      margin: 0 auto;
      .arrow {
        width: 160px;
        height: 8px;
        display: inline-block;
        background-repeat: no-repeat;
        background-size: contain;
        position: absolute;
        z-index: 1;
      }
      .arrow-left {
        background-position: left;
        // background-image: url(~@A/images/solution/smart-hardware/arrow-left.png);
        background-image: url(~@A/images/solution/multimodality/img_title_01.png);
        top: 50%;
        left: -160px;
      }
      .arrow-right {
        background-position: right;
        // background-image: url(~@A/images/solution/smart-hardware/arrow-right.png);
        background-image: url(~@A/images/solution/multimodality/img_title_02.png);
        top: 50%;
        right: -160px;
      }
    }
    .section-desc {
      text-align: center;
      margin-top: 40px;
      font-size: 16px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #999999;
    }
    .section-title-bold {
      font-size: 34px;
      font-family: Microsoft YaHei;
      font-weight: bold;
      color: #333333;
    }
    .section-title-spec {
      font-size: 30px;
      font-weight: 500;
      color: #656565;
      line-height: 42px;
    }
    .section-tabs {
      margin-top: 70px;
      ul {
        display: flex;
        justify-content: center;
      }
      li {
        font-size: 20px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #666;
        line-height: 30px;
        position: relative;
        cursor: pointer;
        &:hover {
          color: #1f90fe;
        }
        &.active {
          color: #1f90fe;
          &::before {
            position: absolute;
            content: ' ';
            z-index: 1;
            width: 180px;
            height: 4px;
            background: #1f90fe;
            border-radius: 2px;
            bottom: -32px;
            left: -48px;
          }
          &::after {
            position: absolute;
            content: ' ';
            display: inline-block;
            width: 0;
            height: 0;
            bottom: -100px;
            left: 50%;
            transform: translateX(-50%);
            border: 46px solid;
            border-color: transparent transparent #f4f7f9;
          }
        }
      }
      li + li {
        margin-left: 165px;
      }
    }
    .section-sub-title {
      text-align: center;
      font-size: 18px;
      font-weight: 400;
      color: #777777;
      line-height: 25px;
      margin-top: 42px;
    }
    .section-sub-title-spec {
      font-size: 17px;
      font-weight: 400;
      color: #999999;
      line-height: 28px;
      margin-top: 18px;
    }
  }

  .section-3 {
    p {
      margin-bottom: 0;
    }
  }

  .section-1 {
    margin-top: 110px;
  }

  .section-2 {
    // max-width: 1200px;
    max-width: unset !important;
    margin: 100px auto 0;
    padding-top: 110px;
    padding-bottom: 113px;
    background: #f4f7f9;
    .section-content {
      margin-top: 170px;
      ul {
        max-width: 1100px;
        margin: 0 auto;
        display: flex;
        justify-content: space-between;
        li {
          position: relative;
          text-align: center;
          .icon-solution {
            width: 146px;
            height: 143px;
            margin: 0 auto;
            &.icon_gender_age {
              background: url(~@A/images/solution/multimodality/icon_gender_age.png)
                center/100% no-repeat;
            }
            &.icon_voiceprint {
              background: url(~@A/images/solution/multimodality/icon_voiceprint.png)
                center/100% no-repeat;
            }
            &.icon_hyperdirectional_wave {
              background: url(~@A/images/solution/multimodality/icon_hyperdirectional_wave.png)
                center/100% no-repeat;
            }
            &.icon_face_detection {
              background: url(~@A/images/solution/multimodality/icon_face_detection.png)
                center/100% no-repeat;
            }
            &.icon_lipping {
              background: url(~@A/images/solution/multimodality/icon_lipping.png)
                center/100% no-repeat;
            }
            &.icon_gesture {
              background: url(~@A/images/solution/multimodality/icon_gesture.png)
                center/100% no-repeat;
            }
            &.icon_virtual_human {
              background: url(~@A/images/solution/multimodality/icon_virtual_human.png)
                center/100% no-repeat;
            }
            &.icon_multi_style {
              background: url(~@A/images/solution/multimodality/icon_multi_style.png)
                center/100% no-repeat;
            }
            &.icon_personalized_synthesis {
              background: url(~@A/images/solution/multimodality/icon_personalized_synthesis.png)
                center/100% no-repeat;
            }
          }
          p:nth-of-type(1) {
            font-size: 18px;
            font-family: Microsoft YaHei;
            font-weight: bold;
            color: #333333;
            line-height: 18px;
            text-align: center;
            margin-top: 36px;
          }
          p:nth-of-type(2) {
            font-size: 16px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            color: #999999;
            line-height: 16px;
            text-align: center;
            margin-top: 22px;
          }
          .part-title {
            font-size: 34px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            color: #666666;
            line-height: 30px;
            position: absolute;
            z-index: 1;
            top: -94px;
            left: 50%;
            transform: translateX(-50%);
            white-space: nowrap;
          }
        }
        li + li {
          // margin-left: 200px;
        }
      }
    }
    .section-content:not(:first-child) {
      margin-top: 185px;
    }
  }

  .section-3 {
    margin-top: 110px;
    ul {
      display: flex;
      justify-content: center;
      margin-top: 72px;
      padding: 0 4px 4px 4px;
      li {
        width: 285px;
        height: 177px;
        border: 2px solid #eff1f1;
        border-radius: 15px;
        text-align: center;
        padding-top: 38px;
        // cursor: pointer;
        &:hover {
          border: 1px solid rgb(31, 144, 254);
          //  box-shadow: 0px 0px 2px 1px rgba(45, 153, 255, 0.8);
          box-shadow: rgba(45, 153, 255, 0.8) 0 0 4px;
        }
        p {
          font-size: 18px;
          font-family: Microsoft YaHei;
          font-weight: 400;
          color: #666666;
          line-height: 18px;
          margin-top: 26px;
        }
        .icon-adv {
          margin: 0 auto;
        }
        .icon_combination {
          width: 57px;
          height: 60px;
          background: url(~@A/images/solution/multimodality/icon_combination.png)
            center/100% no-repeat;
        }
        .icon_offline {
          width: 68px;
          height: 52px;
          background: url(~@A/images/solution/multimodality/icon_offline.png)
            center/100% no-repeat;
        }
        .icon_integration {
          width: 58px;
          height: 60px;
          background: url(~@A/images/solution/multimodality/icon_integration.png)
            center/100% no-repeat;
        }
        .icon_thirdparty {
          width: 59px;
          height: 61px;
          background: url(~@A/images/solution/multimodality/icon_thirdparty.png)
            center/100% no-repeat;
        }
      }
      li + li {
        margin-left: 20px;
      }
    }
  }

  .section-4 {
    margin-top: 109px;
    padding-bottom: 129px;
    text-align: center;
    p {
      margin-bottom: 0;
    }
    .section-title {
      width: 300px;
      p:first-child {
        font-size: 34px;
        font-family: Microsoft YaHei;
        font-weight: bold;
        color: #333333;
      }
      p:last-child {
        font-size: 16px;
        font-weight: 400;
        color: #666;
        line-height: 22px;
        // margin-top: 18px;
        margin-top: 43px;
      }
    }
    .section-item {
      margin-top: 49px;
      .section-button {
        color: #fff;
        background: #1784e9;
        width: 195px;
        height: 50px;
        line-height: 50px;
        text-align: center;
        margin: 0 auto;
        cursor: pointer;
      }
    }
  }

  .interact-list {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 50px auto 0;

    li {
      position: relative;
      text-align: center;
      cursor: pointer;
      width: 384px;
      height: 480px;

      .desc-wrap {
        top: 0;
        // transform: translateY(-50%);
        position: absolute;
        left: 0;
        width: 100%;
        z-index: 2;
      }
      .overlay {
        display: none;
        width: 100%;
        height: 100%;
        // background: rgba(0, 0, 0, 0.3);
        position: absolute;
        top: 0;
        left: 0;
        z-index: 1;
        opacity: 0.7;
      }

      &:hover {
        p,
        h2 {
          display: block;
        }
        h1 {
          display: none;
        }
        .overlay {
          display: block;
        }
      }
      h1 {
        text-align: center;
        font-size: 26px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #ffffff;
        line-height: 26px;
        margin: 0 auto;
        position: relative;
        top: 52px;
      }
      h2 {
        display: none;
        text-align: center;
        font-size: 26px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #ffffff;
        line-height: 26px;
        margin: 0 auto;
        position: relative;
        top: 52px;
        font-weight: bold;
      }
      p {
        display: none;
        // margin-top: 32px;
        margin: 180px auto 0;
        width: 308px;
        font-size: 16px;
        font-weight: 400;
        color: #ffffff;
        // padding-left: 35px;
        text-align: left;
        width: 308px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #ffffff;
        line-height: 32px;
      }

      &.img_integrated_machine {
        background: url(~@A/images/solution/multimodality/img_integrated_machine.png)
          center/100% no-repeat;
      }
      &.img_service_robot {
        background: url(~@A/images/solution/multimodality/img_service_robot.png)
          center/100% no-repeat;
      }
      &.img_fitness_mirror {
        background: url(~@A/images/solution/multimodality/img_fitness_mirror.png)
          center/100% no-repeat;
      }
    }

    li:nth-child(1) {
      .overlay {
        background: #639dfe;
      }
    }
    li:nth-child(2) {
      .overlay {
        background: #fe6555;
      }
    }
    li:nth-child(3) {
      .overlay {
        background: #384d9e;
      }
    }

    li + li {
      margin-left: 23px;
    }
  }
}
.section-swiper {
  margin-top: 81px;
  background: #f4f7f9;
  .swiper-wrapper {
    max-width: 1200px;
  }
}
</style>
