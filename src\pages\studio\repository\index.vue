<template>
  <div class="os-scroll">
    <div class="repo-page" v-loading="tableData.loading">
      <repo-header backToParams="qabanks"></repo-header>
      <div class="mgt28 mgb16 top-edit-wrap">
        <el-button
          class="mgr16"
          icon="ic-r-plus"
          size="small"
          type="primary"
          :disabled="tableData.total >= limitCount"
          @click="openCreateQaRelation"
        >
          &nbsp;&nbsp;创建问答关系
        </el-button>
        <batch
          api="import"
          exportText="导出知识库"
          :repoId="repoId"
          @setLoad="setLoad"
          @getData="getQaRelList(1)"
        ></batch>
        <router-link
          :to="{ name: 'question-keywords', params: { repoId: repoId } }"
          target="_blank"
        >
          查看问法关键词
        </router-link>
        <div class="fr" @keyup.enter="getQaRelList(1)">
          <el-input
            class="search-area"
            placeholder="搜索问答关系"
            size="medium"
            style="width: 360px"
            v-model="searchVal"
          >
            <i
              slot="suffix"
              class="el-input__icon el-icon-search search-area-btn"
              @click="getQaRelList(1)"
            />
          </el-input>
        </div>
      </div>
      <os-table
        :class="{ 'no-data': noRepo }"
        :tableData="tableData"
        @change="getQaRelList"
        @row-click="toEdit"
      >
        <el-table-column type="index" width="80"> </el-table-column>
        <el-table-column width="330">
          <template slot="header" slot-scope="scope">
            <os-table-qahead
              label="问答关系"
              tip="如：张山的老婆是谁？问的是张山，问答关系是“老婆”。"
            />
          </template>
          <template slot-scope="scope">
            <div
              class="text-blod cp qa-relation-zh-name ellipsis"
              :title="scope.row.themeName"
            >
              {{ scope.row.themeName }}
            </div>
          </template>
        </el-table-column>
        <el-table-column width="330" label="问法示例" prop="demoQuestion">
          <template slot-scope="scope">
            <span class="ellipsis" :title="scope.row.demoQuestion">{{
              scope.row.demoQuestion || '-'
            }}</span>
          </template>
        </el-table-column>
        <el-table-column width="330" label="答案示例" prop="demoAnswer">
          <template slot-scope="scope">
            <span class="ellipsis" :title="scope.row.demoAnswer">{{
              scope.row.demoAnswer || '-'
            }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120">
          <template slot-scope="scope">
            <i class="ic-r-edit cell-handle-ic"></i>
            <i
              class="cell-handle-hovershow ic-r-delete cell-handle-ic"
              @click.prevent.stop="del(scope.row)"
            ></i>
          </template>
        </el-table-column>
      </os-table>
      <div class="create-guide" v-if="noRepo && !this.tableData.loading">
        <div class="icon"></div>
        <p class="title">
          你还没有知识库问答，<a
            href="javascript:void(0)"
            @click="openCreateQaRelation"
            >点击创建</a
          >
        </p>
        <p class="desc">
          “玻璃是什么垃圾”、“香蕉皮属于什么垃圾”，问答关系是“垃圾分类”，“玻<br />璃”、“香蕉皮”是问的对象，“可回收物”、“湿垃圾”是对应的回复语。
        </p>
        <!-- 新文档没有，暂时不动 -->
        <a
          :href="`https://aiui.xfyun.cn/doc/aiui/4_skill_develop/5_qa/knowledge_map.html`"
          target="_blank"
          >了解更多</a
        >
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import RepoHeader from './repoHeader'
import Batch from './batch'

export default {
  data() {
    return {
      searchVal: '',
      tableData: {
        loading: false,
        total: 0,
        page: 1,
        size: 10,
        list: [],
      },
      noRepo: false,
      limitCount: 20000,
    }
  },
  computed: {
    repoId() {
      return this.$route.params.repoId || ''
    },
    repoName() {
      return this.$route.query.name || ''
    },
    ...mapGetters({
      // limitCount: 'aiuiApp/limitCount',
    }),
  },
  created() {
    this.getQaRelList()
  },
  methods: {
    getQaRelList(page) {
      this.tableData.loading = true
      this.tableData.list = []
      this.$utils.httpGet(
        this.$config.api.STUDIO_REPO_REL_LIST,
        {
          repositoryId: this.repoId,
          search: this.searchVal,
          pageSize: this.tableData.size,
          pageIndex: page || this.tableData.page,
        },
        {
          success: (res) => {
            this.tableData.loading = false
            if (res.flag) {
              this.tableData.total = res.data.count || 0
              this.tableData.list = res.data.list || []
              this.noRepo =
                res.data.count == 0 && !this.searchVal ? true : false
            } else {
              this.$$message({
                message: res.desc || '数据获取失败',
                type: 'warning',
              })
            }
          },
          error: (err) => {
            this.tableData.loading = false
          },
        }
      )
    },
    openCreateQaRelation() {
      this.$router.push({
        name: 'create-qa-relation-page',
        params: { repoId: this.repoId },
      })
    },
    toEdit(item) {
      this.$router.push({
        name: 'qa-relation-detail',
        params: { repoId: this.repoId, themeId: item.id },
      })
    },
    del(row) {
      let self = this
      this.$confirm(
        '删除后该问答关系下的所有问法均不再支持。',
        `确定删除问答关系 - ${row.themeName}`,
        {
          confirmButtonText: '确定删除',
          cancelButtonText: '取消',
          confirmButtonClass: 'el-button--danger',
          type: 'warning',
          showClose: false,
        }
      )
        .then(() => {
          self.delEntity(row)
        })
        .catch(() => {})
    },
    delEntity(data) {
      let self = this
      this.$utils.httpPost(
        this.$config.api.STUDIO_REPO_REL_LIST_DEL,
        {
          repositoryId: self.repoId,
          themeId: data.id,
        },
        {
          success: (res) => {
            self.$message.success('删除成功')
            if (self.tableData.list.length === 1 && self.tableData.page > 1) {
              self.tableData.page -= 1
            }
            self.getQaRelList()
          },
          error: (err) => {
            self.$message.error(err && err.desc ? err.desc : '删除失败')
          },
        }
      )
    },
    // 批量操作
    setLoad(val) {
      this.tableData.loading = val
    },
  },
  components: { RepoHeader, Batch },
}
</script>

<style lang="scss" scoped>
.repo-page {
  max-width: 1200px;
  padding-top: 24px;
  margin: auto;
  .top-edit-wrap {
    font-size: 0;
    a {
      margin-left: 12px;
      font-size: 14px;
    }
  }
}
.create-guide {
  margin: 60px 0;
  text-align: center;
  font-size: 16px;
  color: $grey5;
  .icon {
    margin: 0 auto 24px;
    width: 120px;
    height: 120px;
    background: url(../../../assets/images/no_data_img.png) center no-repeat;
    background-size: 100%;
  }
  a {
    font-weight: 400;
    font-size: 14px;
  }
  .title {
    font-size: 16px;
    font-weight: 600;
    a {
      font-weight: 600;
      font-size: 16px;
    }
  }
  .desc {
    margin: 24px auto 8px;
    font-size: 14px;
    line-height: 22px;
  }
}
</style>
<style lang="scss">
.repo-page {
  .el-table .cell {
    cursor: pointer;
  }
  .el-table--enable-row-hover .el-table__body tr:hover > td {
    .qa-relation-zh-name {
      color: $primary;
    }
  }
  .el-table .ic-r-edit {
    color: $primary;
  }
  .no-data.os-table .el-table__empty-block {
    display: none;
  }
}
</style>
