<template>
  <div class="main-content">
    <section class="main-content-banner">
      <div class="banner-text">
        <h2>智慧地铁解决方案</h2>
        <p class="banner-text-content">
          针对地铁场景打造软硬件一体化解决方案。<br />
          让地铁设备能听会说，提升乘客购票效率及咨询体验！
        </p>
        <div class="strange-button" @click="toConsole">合作咨询</div>
      </div>
    </section>
    <section class="section section-2">
      <div class="section-purchase" style="margin-bottom: 60px">应用场景</div>
      <div class="section-item">
        <ul>
          <li v-for="(item, index) in app_scenario" :key="index" class="app">
            <img :src="item.src" :alt="item.alt" />
            <p class="app-text">{{ item.alt }}</p>
          </li>
        </ul>
      </div>
    </section>

    <section class="section section-3" style="padding-bottom: 100px">
      <div class="section-purchase" style="margin-bottom: 60px">方案介绍</div>

      <div class="section-item">
        <ul>
          <li>
            <div class="section-item-bg" style="width: 450px">
              <div class="intro-title">语音购票</div>
              <p class="intro-desc">
                多种语音购票方式，任意说：站点购票、<br />票价购票、模糊购票
              </p>
            </div>
            <div class="section-item-bg dialog">
              <div class="user-icon-div">
                <img
                  class="dialog-icon"
                  src="../../../../assets/images/solution/subway/user-icon.png"
                />
              </div>
              <div class="message message-user">“ 我要去上海火车站”</div>

              <div class="user-icon-div user-icon-div-second">
                <img
                  class="dialog-icon"
                  src="../../../../assets/images/solution/subway/user-icon.png"
                />
              </div>
              <div class="message message-user message-user-second">好的</div>

              <div class="robot-icon-div">
                <img
                  class="dialog-icon dialog-icon-robot"
                  src="../../../../assets/images/solution/subway/robot-icon.png"
                />
              </div>
              <div class="message message-robot">“ 好的，请选择购票张数”</div>

              <div class="robot-icon-div robot-icon-div-second">
                <img
                  class="dialog-icon dialog-icon-robot-second"
                  src="../../../../assets/images/solution/subway/robot-icon.png"
                />
              </div>
              <div class="message message-robot message-robot-second">
                “ 好的，请选择支付方式”
              </div>
            </div>

            <div class="section-item-bg dialog-purcgase">
              <img
                class="user-manipulate"
                src="../../../../assets/images/solution/subway/user-manipulate.png"
              />
            </div>
          </li>
        </ul>
      </div>

      <div style="top: 65px; position: relative">
        <section class="section section-2">
          <div class="section-item">
            <ul>
              <li
                style="min-width: 300px"
                v-for="(item, index) in ai_voice"
                :key="index"
                class="app"
              >
                <img :src="item.src" :alt="item.alt" />
                <p class="app-text-voice">{{ item.alt }}</p>
              </li>
              <li style="width: 300px; margin-left: 100px">
                <div>
                  <div class="intro-title">语音查询</div>
                  <div class="intro-desc">站内、站外信息，轻松问</div>
                </div>
              </li>
            </ul>
          </div>
        </section>
      </div>
    </section>

    <section class="section section-1" style="width: 100%">
      <div class="section-purchase" style="margin-bottom: 60px">方案优势</div>
      <div class="section-item">
        <ul>
          <li v-for="(item, index) in pg_advantage" :key="index">
            <div class="section-item-bg" v-if="index % 2">
              <img :src="item.src" />
            </div>
            <div class="section-item-text">
              <div class="section-item-text-title">{{ item.title }}</div>
              <p v-html="item.text" class="pc-show-ai"></p>
            </div>
            <div class="section-item-bg" v-if="!(index % 2)">
              <img :src="item.src" />
            </div>
          </li>
        </ul>
      </div>
    </section>

    <section class="section section-4">
      <div class="section-title">交付流程</div>
      <div class="section-item">
        <ul>
          <li class="pc-show">
            <!-- <img
              src="../../../assets/images/solution/subway/icon_project_integration_nomal.png"
            /> -->
            <div class="step1"></div>
            <p>
              方案集成<br /><span
                >专为地铁设计， 支持多种组合方案， 实现快速集成。</span
              >
            </p>
          </li>
          <div class="rightArrow"></div>
          <li class="pc-show">
            <!-- <img
              src="../../../assets/images/solution/subway/icon_verify_nomal.png"
            /> -->
            <div class="step2"></div>
            <p>
              项目验证<br /><span
                >提供公有云服务快速验证， 语音交互设计指导， 全流程技术支持
              </span>
            </p>
          </li>
          <div class="rightArrow"></div>
          <li class="pc-show">
            <!-- <img
              src="../../../assets/images/solution/subway/icon_online_nomal.png"
            /> -->
            <div class="step3"></div>
            <p>
              项目上线<br /><span
                >提供私有化部署， 满足地铁内网使用语音交互</span
              >
            </p>
          </li>
        </ul>
      </div>
    </section>
    <corp @jump="toConsole">
      <template>
        为地铁行业打造最流畅的语音交互体验<br />提交信息，
        我们会尽快联系你</template
      >
    </corp>
    <!-- <section class="section section-5">
      <div class="section-title">
        <div class="section-title">合作咨询</div>
        <p>
          为地铁行业打造最流畅的语音交互体验<br />提交信息， 我们会尽快联系你
        </p>
      </div>
      <div class="section-item">
        <aiui-button @click.native="toConsole" hasTop> 申请合作 </aiui-button>
      </div>
    </section> -->
  </div>
</template>

<script>
import corp from '@P/aiui/solution-aiui/components/corp.vue'

export default {
  name: 'subway',
  data() {
    return {
      app_scenario: [
        {
          alt: '地铁购票机',
          src: require('../../../../assets/images/solution/subway/img_subway_ticket_machine.png'),
        },
        {
          alt: '自助查询机',
          src: require('../../../../assets/images/solution/subway/img_self_service_machine.png'),
        },
      ],
      ai_voice: [
        {
          alt: '“ 1号线最早的1班车是几点？”',
          src: require('../../../../assets/images/solution/subway/stations.png'),
        },
        {
          alt: '“ 我要去动物园，从几号口出”',
          src: require('../../../../assets/images/solution/subway/lines.png'),
        },
      ],
      pg_advantage: [
        {
          title: '智能感知- 主动提供服务',
          text: '走到设备正前方，主动语音交互；<br>离开设备，自动终止服务。',
          src: require('../../../../assets/images/solution/subway/ai-perception.png'),
        },
        {
          title: '定向收音- 有效抑制周围噪声',
          text: '基于业界先进的定向收音技术，实现屏幕正前方<br>定向收音，屏蔽两侧噪声。',
          src: require('../../../../assets/images/solution/subway/ai-inhibit-noise.png'),
        },
        {
          title: '精准录音- 准确判断录音时刻',
          text: '乘客开口录音，闭口停止录音。<br>精准去除周边噪声干扰，提升语音识别准确率。 ',
          src: require('../../../../assets/images/solution/subway/ai-accurate-record.png'),
        },
        {
          title: '虚拟形象-提升服务体验',
          text: '从人机对话转变为人“人”对话，让服务更有温度。',
          src: require('../../../../assets/images/solution/subway/ai-virtual-image.png'),
        },
      ],
    }
  },
  methods: {
    toConsole() {
      // 判断是否有百度SEM营销参数，有的话，提交表单页需要带上
      const search = window.location.search
      if (search) {
        window.open(`/solution/apply/7${search}`)
      } else {
        window.open('/solution/apply/7')
      }
    },
  },
  components: { corp },
}
</script>

<style lang="scss" scoped>
@import '../../../../assets/scss/screen-and-subway.scss';

// .rightArrow:after {
//   content: '';
//   display: block;
//   width: 20px;
//   height: 20px;
//   border-top: 6px solid #999999;
//   border-right: 6px solid #999999;
//   transform: rotate(45deg);
// }
.rightArrow {
  width: 66px;
  height: 35px;
  background: url(~@A/images/solution/subway/icon_arrow.png) center/100%
    no-repeat;
}

.user-icon-div {
  left: 20px;
  position: relative;
  &-second {
    top: 45%;
    position: absolute;
    width: 100% !important;
  }
}

.message,
.message {
  float: left;
  /* margin: 10px; */
  left: 90px;
  background-color: #1784e9;
  border-bottom-color: #1784e9;
  color: #fff;
  font-size: 12px;
  line-height: 18px;
  padding: 5px 12px 5px 12px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border-radius: 6px;
  position: relative;
  word-break: break-all;

  &-user {
    left: 18%;
    &-second {
      position: absolute;
      top: 54%;
      left: 18%;
    }
  }

  &-robot {
    background-color: white;
    color: #000000;
    float: right;
    position: absolute;
    right: 18% !important;
    left: revert;
    top: 32%;
    width: fit-content;
    &-second {
      top: 73%;
    }
  }
}

.message-robot::after {
  content: '';
  position: absolute;
  top: 0;
  right: -20px;
  width: 20px;
  height: 20px;
  border-width: 0 0 10px 20px;
  border-style: solid;
  border-bottom-color: rgb(255, 255, 255);
  /*自动继承父元素的border-bottom-color*/
  border-left-color: transparent;
  border-radius: 0 0 60px 0;
}

/** 通过对小正方形旋转45度解决 **/
.message-user::before {
  content: '';
  position: absolute;
  top: 0;
  left: -20px;
  width: 20px;
  height: 20px;
  border-width: 0 0 10px 0;
  border-style: solid;
  border-bottom-color: inherit;
  border-left-color: transparent;
  border-radius: 0 0 0 60px;
}

.dialog {
  height: 318px;
  width: 42% !important;
  background: #e7edf2;
  border-radius: 10px;
  right: 5%;
  position: relative;
  &-purcgase {
    height: 28.17% !important;
    width: 16.98% !important;
  }
}

.dialog-icon {
  height: auto;
  width: 7.5% !important;

  &-robot {
    position: relative;
    right: 5%;
    float: right;

    &-second {
      position: absolute;
      float: right;
      right: 5%;
    }
  }
}

.robot-icon-div {
  position: relative;
  top: 8%;

  &-second {
    position: relative;
    top: 52% !important;
  }
}

.pc-show {
  padding-top: 0.9%;
  font-size: 20px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #333333;
  text-align: center;
  margin-bottom: 4.25%;

  &-ai {
    font-size: 14px;
    color: #666666;
    text-align: left;
  }
  .step1 {
    width: 77px;
    height: 80px;
    background: url(~@A/images/solution/subway/icon_project_integration_nomal.png)
      center/100% no-repeat;
    &:hover {
      background: url(~@A/images/solution/subway/icon_project_integration_hover.png)
        center/100% no-repeat;
    }
  }
  .step2 {
    width: 66px;
    height: 80px;
    background: url(~@A/images/solution/subway/icon_verify_nomal.png)
      center/100% no-repeat;
    &:hover {
      background: url(~@A/images/solution/subway/icon_verify_hover.png)
        center/100% no-repeat;
    }
  }
  .step3 {
    width: 77px;
    height: 80px;
    background: url(~@A/images/solution/subway/icon_online_nomal.png)
      center/100% no-repeat;
    &:hover {
      background: url(~@A/images/solution/subway/icon_online_hover.png)
        center/100% no-repeat;
    }
  }
}

.user-manipulate {
  right: -20%;
  position: relative;
}

.main-content {
  &-banner {
    background: url(~@A/images/solution/subway/img_wisdomsubway_bg_banner.png)
      center no-repeat;
    background-size: cover;
    height: 500px;
    overflow: hidden;
    width: 100%;
    .banner-text {
      max-width: 1200px;
      color: #fff;
      height: 100%;
      margin: auto;
      &-button {
        font-size: 16px;
        text-align: center;
        font-weight: 400;
        width: 140px;
        height: 40px;
        line-height: 40px;
        border: 1px solid #fff;
        border-radius: 40px;
        color: #fff;
        cursor: pointer;
        transition: 0.6s;
      }
      h2 {
        color: #fff;
        padding-top: 148px;
        margin-bottom: 29px;
        font-size: 48px;
        font-weight: 500;
        line-height: 48px;
      }
      p {
        font-size: 18px;
        margin-bottom: 74px;
      }

      .banner-text-content {
        width: 570px;
        font-size: 16px;
        font-family: SourceHanSansSC-Regular, SourceHanSansSC;
        font-weight: 400;
        color: rgba(255, 255, 255, 0.86);
        line-height: 30px;
      }
    }
  }
}

.strange-button {
  font-size: 16px;
  text-align: center;
  font-weight: 400;
  width: 140px;
  height: 40px;
  line-height: 40px;
  border: 1px solid #fff;
  border-radius: 40px;
  color: #fff;
  cursor: pointer;
  transition: 0.6s;
}

// @media screen and (min-width: 751px) {
//   .main-content {
//     &-banner {
//       background: url('../../../assets/images/solution/subway/banner.png')
//         center no-repeat;
//       background-size: cover;
//     }
//   }
// }
// @media screen and (max-width: 750px) {
//   .main-content {
//     &-banner {
//       background: url('../../../assets/images/solution/digital-screen-lamp/digital-banner-m.jpg')
//         center no-repeat;
//       background-size: cover;
//     }
//   }
// }
.intro-title {
  font-size: 34px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #666;
}
.intro-desc {
  font-size: 16px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #999;
  margin-top: 43px;
  line-height: 30px;
}
</style>
