import aiuiDefaultLayout from '@L/aiuiDefaultLayout'
import aiuiHome from '@L/aiuiHome'
import AiuiAppLayout from '@P/aiui/app/aiuiApplicationLayout'
import StoreSkillsLayout from '@L/storeSkillsLayout'
import StoreSkillLayout from '@L/storeSkillLayout'
import defaultLayout from '@L/defaultLayout'
import defaultLayoutWithoutHover from '@L/defaultLayoutWithoutHover'
import defaultLayoutWithoutHeader from '@L/defaultLayoutWithoutHeader'

import statisticLayout from '@P/aiui/app/statistic'

export default [
  {
    path: '/store',
    component: StoreSkillsLayout,
    meta: {
      metaInfo: {
        title: '技能商店-AIUI开放平台',
        keywords:
          'AIUI开放平台官方技能,内容信源,多场景对话,语音交互用户意图,AIUI,讯飞语音交互,讯飞语义理解,AIUI人机交互,全链路语音交互,全链路人机交互,讯飞人机交互,讯飞自然语言理解,全双工交互,语音唤醒,语音助手开发,语音交互接入,接入语音交互,语音交互控制,语音交互设计,讯飞人工智能,讯飞AIUI开放平台,讯飞云平台,讯飞星火大模型,星火交互大模型,多模态唤醒,虚拟人互动,数字人互动,多语种识别,超拟人合成,声音复刻,硬件模组,讯飞星火,人机交互,智能硬件,消费电子语音交互,消费电子人机交互,手机语音交互,电视语音交互,机器人语音交互,地铁火车轨道交通语音交互,PC语音助手,语音软硬件,语音交互控制',
        description:
          'AIUI开放平台内置200+官方技能，覆盖多场景对话任务。内置信源100+，支持接入外网信源。AIUI是以讯飞星火大模型为核心的人机交互开发平台，具备多模态唤醒、虚拟人驱动、多语种识别、超拟人合成、声音复刻等特性。广泛应用于手机、电视、机器人扫读笔、语音购票等智能硬件设备上。提供SDK、Websocket、硬件模组等，接入集成简单，开箱即用。',
      },
    },
    children: [
      // {
      //   path: '/',
      //   redirect: { name: 'store-skills', params: { skillType: 'all' } },
      // },
      {
        path: 'all',
        name: 'store-skill-all',
        component: () =>
          import(
            /* webpackChunkName: "store-skill-all" */ '@P/aiui/store/skillsAll'
          ),
      },
      {
        path: 'domain',
        name: 'store-skill-domain',
        component: () =>
          import(
            /* webpackChunkName: "store-skill-domain" */ '@P/aiui/store/skillsDomain'
          ),
      },
      {
        path: 'new',
        name: 'store-skill-new',
        component: () =>
          import(
            /* webpackChunkName: "store-skill-new" */ '@P/aiui/store/skillsNew'
          ),
      },
      // {
      //   path: ':skillType',
      //   name: 'store-skills', // 技能商店页
      //   meta: {
      //     title: 'AIUI开放平台',
      //   },
      //   component: () =>
      //     import(/* webpackChunkName: "store-skills" */ '@P/aiui/store/skills'),
      // },
    ],
  },
  {
    path: '/store/skill',
    component: StoreSkillLayout,
    meta: {
      metaInfo: {
        title: '技能商店-AIUI开放平台',
        keywords:
          'AIUI开放平台官方技能,内容信源,多场景对话,语音交互用户意图,AIUI,讯飞语音交互,讯飞语义理解,AIUI人机交互,全链路语音交互,全链路人机交互,讯飞人机交互,讯飞自然语言理解,全双工交互,语音唤醒,语音助手开发,语音交互接入,接入语音交互,语音交互控制,语音交互设计,讯飞人工智能,讯飞AIUI开放平台,讯飞云平台,讯飞星火大模型,星火交互大模型,多模态唤醒,虚拟人互动,数字人互动,多语种识别,超拟人合成,声音复刻,硬件模组,讯飞星火,人机交互,智能硬件,消费电子语音交互,消费电子人机交互,手机语音交互,电视语音交互,机器人语音交互,地铁火车轨道交通语音交互,PC语音助手,语音软硬件,语音交互控制',
        description:
          'AIUI开放平台内置200+官方技能，覆盖多场景对话任务。内置信源100+，支持接入外网信源。AIUI是以讯飞星火大模型为核心的人机交互开发平台，具备多模态唤醒、虚拟人驱动、多语种识别、超拟人合成、声音复刻等特性。广泛应用于手机、电视、机器人扫读笔、语音购票等智能硬件设备上。提供SDK、Websocket、硬件模组等，接入集成简单，开箱即用。',
      },
    },
    children: [
      {
        path: '/',
        redirect: { name: 'store-skills', params: { skillType: 'all' } },
      },
      {
        path: ':skillId',
        name: 'store-skill', // 技能详情页
        meta: {
          title: 'AIUI开放平台',
        },
        component: () =>
          import(/* webpackChunkName: "store-skill" */ '@P/aiui/store/skill'),
      },
    ],
  },
  {
    path: '/app',
    component: aiuiDefaultLayout,

    children: [
      {
        path: '/',
        name: 'apps', // 我的应用页
        meta: {
          title: 'AIUI开放平台',
          metaInfo: {
            title: '我的应用-AIUI开放平台',
            keywords:
              'AIUI,讯飞语音交互,讯飞语义理解,AIUI人机交互,全链路语音交互,全链路人机交互,讯飞人机交互,讯飞自然语言理解,全双工交互,语音唤醒,语音助手开发,语音交互接入,接入语音交互,语音交互控制,语音交互设计,讯飞人工智能,讯飞AIUI开放平台,讯飞云平台,讯飞星火大模型,星火交互大模型,多模态唤醒,虚拟人互动,数字人互动,多语种识别,超拟人合成,声音复刻,硬件模组,讯飞星火,人机交互,智能硬件,消费电子语音交互,消费电子人机交互,手机语音交互,电视语音交互,机器人语音交互,地铁火车轨道交通语音交互,PC语音助手,语音软硬件,语音交互控制',

            description:
              '创建我的应用，设置AIUI语音链路，低代码低成本开发语音交互产品。AIUI是以讯飞星火大模型为核心的人机交互开发平台，具备多模态唤醒、虚拟人驱动、多语种识别、超拟人合成、声音复刻等特性。广泛应用于手机、电视、机器人扫读笔、语音购票等智能硬件设备上。提供SDK、Websocket、硬件模组等，接入集成简单，开箱即用。',
          },
        },
        component: () =>
          import(/* webpackChunkName: "apps" */ '@P/aiui/apps/index'),
      },
      // {
      //   path: 'add',
      //   name: 'apps-add', // 新增应用页
      //   meta: {
      //     title: 'AIUI开放平台',
      //   },
      //   component: () =>
      //     import(/* webpackChunkName: "apps-add" */ '@P/aiui/apps/add'),
      // },
    ],
  },
  // 应用接入
  {
    path: '/access',
    component: aiuiHome,
    children: [
      {
        path: '/',
        name: 'access', // 应用接入页面
        meta: {
          title: 'AIUI开放平台',
        },
        component: () =>
          import(
            /* webpackChunkName: "access" */ '@P/aiui/solution-aiui/access'
          ),
      },
    ],
  },
  // 服务量购买
  // {
  //   path: '/buy',
  //   component: defaultLayout,
  //   children: [
  //     {
  //       path: '/',
  //       name: 'buy', // 服务量购买
  //       meta: {
  //         title: 'AIUI开放平台',
  //       },
  //       component: () =>
  //         import(
  //           /* webpackChunkName: "buyService" */ '@P/aiui/buy-service/buyService'
  //         ),
  //     },
  //   ],
  // },
  // 解决方案
  {
    path: '/solution',
    component: defaultLayout,
    children: [
      {
        path: 'apply/:solutionId',
        name: 'apply', // KTV解决方案
        meta: {
          title: 'AIUI开放平台',
        },
        component: () =>
          import(
            /* webpackChunkName: "solutionApply" */ '@P/aiui/solution-aiui/solutionApply'
          ),
      },
    ],
  },
  // 解决方案-英文表单申请
  {
    path: '/solution',
    component: defaultLayout,
    children: [
      {
        path: 'apply-en/:solutionId',
        name: 'apply-en', // KTV解决方案
        meta: {
          title: 'AIUI',
        },
        component: () =>
          import(
            /* webpackChunkName: "solutionApplyEn" */ '@P/aiui/solution-aiui/solutionApplyEn'
          ),
      },
    ],
  },
  // 聚合解决方案表单申请
  {
    path: '/solution',
    component: defaultLayout,
    children: [
      {
        path: 'apply-submit',
        name: 'apply-submit', // 聚合解决方案申请提交
        meta: {
          title: 'AIUI开放平台',
        },
        component: () =>
          import(
            /* webpackChunkName: "applySubmit" */ '@P/aiui/solution-aiui/solutionApplySubmit'
          ),
      },
    ],
  },
  {
    path: '/solution',
    component: aiuiHome,
    children: [
      {
        path: 'wakeup',
        name: 'wakeup', // wakeup解决方案
        meta: {
          title: 'AIUI开放平台',
          metaInfo: {
            title: '免唤醒语音交互解决方案-AIUI开放平台',
            keywords:
              '设备免唤醒,免唤醒语音点播,智能硬件免唤醒,免唤醒机器人,免唤醒投影仪,免唤醒护眼仪,免唤醒按摩椅,免唤醒语音交互,免唤醒控制',
            description:
              'AIUI免唤醒能力适用于各类消费电子设备，无需唤醒设备即可语音点播内容、控制设备交互，为产品打造自然流畅的语音交互体验。支持离线和在线环境，提供纯软和硬件模组。',
          },
        },
        component: () =>
          import(
            /* webpackChunkName: "wakeup" */ '@P/aiui/solution-aiui/wakeup'
          ),
      },
      {
        path: 'ktv',
        name: 'ktv', // KTV解决方案
        meta: {
          title: 'AIUI开放平台',
          metaInfo: {
            title: 'KTV智能交互解决方案-AIUI开放平台',
            keywords:
              '语音点歌,语音播控,语音点单,KTV智能交互,AI点歌,人工智能KTV,AI点播系统',
            description:
              'AIUI为KTV场景量身定制，可实现语音点歌、语音控制切歌、伴唱、呼叫服务、语音点单等功能，助力传统KTV升级转型，创造不一样的娱乐体验。',
          },
        },
        component: () =>
          import(/* webpackChunkName: "ktv" */ '@P/aiui/solution-aiui/ktv'),
      },
      {
        path: 'robot',
        name: 'robot', // 机器人解决方案
        meta: {
          title: 'AIUI开放平台',
          metaInfo: {
            title: '服务机器人解决方案-AIUI开放平台',
            keywords:
              '服务机器人解决方案,公共场所机器人服务,机器人厂商,迎宾机器人,配送机器人,特种机器人,清洁机器人,高噪场景,机器人交互,讯飞机器人',
            description:
              'AIUI围绕公共场所机器人服务场景，提供语音交互、导航定位、整机交互整体解决方案，支持主动迎宾、免唤醒交互、方言随意切换、离线自由说。助力机器人厂商快速完成核心能力集成及产品交付。',
          },
        },
        component: () =>
          import(/* webpackChunkName: "robot" */ '@P/aiui/solution-aiui/robot'),
      },
      {
        path: 'subway',
        name: 'subway', // 智慧地铁解决方案
        meta: {
          title: 'AIUI开放平台',
          metaInfo: {
            title: '智慧地铁解决方案-AIUI开放平台',
            keywords:
              '智慧地铁解决方案,AI购票,AI出行,多模语音增强,语音购票,AI交通,智慧交通,地铁购票平台,智慧出行,智能感知,人机购票,自助查询机',
            description:
              '支持站点购票、票价购票、模糊购票的多种语音购票方式，语音查询站内、站外信息。让地铁设备能听会说，提升乘客购票效率及咨询体验。多模态智能感知，主动服务，定向收音和精准录音抑制噪音干扰，虚拟人交互拉进对话距离。',
          },
        },
        component: () =>
          import(
            /* webpackChunkName: "subway" */ '@P/aiui/solution-aiui/subway'
          ),
      },
      {
        path: 'screen',
        name: 'screen', // 智慧大屏解决方案
        meta: {
          title: 'AIUI开放平台',
          metaInfo: {
            title: '语音大屏调度解决方案-AIUI开放平台',
            keywords:
              '智慧大屏,大屏开发,语音购票,语音调度系统,大屏语音控制,大屏语音交互,机器人大屏,语音调度,大屏交互,大屏语音调度,讯飞大屏交互,语音控制大屏,企业助手,语音互动大屏,语音智控大屏,商业大屏,展厅大屏',
            description:
              '面向指挥调度、监控中心、多功能展厅等大屏交互场景，一句话直达大屏深层页面，提高业务办理和演示效率。语音识别结果实时上屏，近场交互准确，远场交互抗噪，支持私有化部署。',
          },
        },
        component: () =>
          import(
            /* webpackChunkName: "screen" */ '@P/aiui/solution-aiui/screen'
          ),
      },
      {
        path: 'mouse',
        name: 'mouse', // 语音鼠标解决方案
        meta: {
          title: 'AIUI开放平台',
          metaInfo: {
            title: '语音鼠标解决方案-AIUI开放平台',
            keywords:
              '科大讯飞语音鼠标,飞鼠语音鼠标,语音控制,OCR识别,讯飞ai智能鼠标,讯飞智能鼠标 lite,讯飞智能鼠标 pro,讯飞lite鼠标',
            description:
              'AIUI语音鼠标能打字会翻译。融合语音转写、翻译、方言识别、语音控制等AI能力，鼠标能听懂你的声音，替你实现文字写作、网络搜索和实时翻译。',
          },
        },
        component: () =>
          import(/* webpackChunkName: "mouse" */ '@P/aiui/solution-aiui/mouse'),
      },
      {
        path: 'child-education',
        name: 'child-education', // 儿童教育解决方案
        meta: {
          title: 'AIUI开放平台',
          metaInfo: {
            title: '儿童教育硬件解决方案-AIUI开放平台',
            keywords:
              '儿童陪伴智能硬件,儿童英语,儿童歌谣,语音交互儿童,aiui儿童,儿童教育硬件解决方案,AI童话故事,儿童故事机,儿童学习机,儿童陪伴玩具,儿童早教机,儿童AI交互,儿童教育智能硬件',
            description:
              'AIUI专为儿童教育陪伴领域的智能硬件打造有料、有趣的人机交互。提供丰富的故事、儿歌、知识内容，同步课堂教材内容，配置要求低、接入方便快捷。特别提供坐姿检测、口语测评、中英翻译和个性化声音合成能力。',
          },
        },
        component: () =>
          import(
            /* webpackChunkName: "child-education" */ '@P/aiui/solution-aiui/child-education'
          ),
      },
      {
        path: 'child-toys',
        name: 'child-toys',
        meta: {
          title: 'AIUI开放平台',
          metaInfo: {
            title: '儿童玩具解决方案-AIUI开放平台',
            keywords: '',
            description: '',
          },
        },
        component: () =>
          import(
            /* webpackChunkName: "child-education" */ '@P/aiui/solution-aiui/child-toys'
          ),
      },
      {
        path: 'reflection',
        name: 'reflection', // 智能投影解决方案
        meta: {
          title: 'AIUI开放平台',
          metaInfo: {
            title: '智能投影解决方案-AIUI开放平台',
            keywords:
              '智能投影仪,多场景语音助手,电视语音助手,投影仪语音助手,语音遥控器',
            description:
              '家庭领域专属降噪和识别引擎,打造极致语音搜索和播放控制的交互体验。AIUI提供OS、语音助手、影视内容等多种合作方案，实现家庭场景语音识别准确，影视搜索随心说，区分儿童成人对话内容，大屏显示所见即可说，实时翻译字幕。',
          },
        },
        component: () =>
          import(
            /* webpackChunkName: "reflection" */ '@P/aiui/solution-aiui/reflection'
          ),
      },
      {
        path: 'smart-retail',
        name: 'smart-retail', // 智慧零售解决方案
        meta: {
          title: 'AIUI开放平台',
          metaInfo: {
            title: '智慧零售解决方案-AIUI开放平台',
            keywords:
              '智慧零售解决方案,营销机器人,商超机器人,智能导购,智能销售,智慧迎宾,智慧商超',
            description:
              'AIUI围绕商超零售场景，提供主动迎宾、语音交互、智能导航、个性化商品推荐的整体解决方案。高噪音环境收音准确，智能语音交互自然亲切，支持虚拟人驱动。降低商家经营成本、提高运营智能化水平和顾客消费体验。',
          },
        },
        component: () =>
          import(
            /* webpackChunkName: "smart-retail" */ '@P/aiui/solution-aiui/smart-retail'
          ),
      },
      {
        path: 'lamp',
        name: 'lamp', // 智能台灯解决方案
        meta: {
          title: 'AIUI开放平台',
        },
        component: () =>
          import(/* webpackChunkName: "lamp" */ '@P/aiui/solution-aiui/lamp'),
      },
      {
        path: 'mobile',
        name: 'mobile', //
        meta: {
          title: 'AIUI开放平台',
        },
        component: () =>
          import(
            /* webpackChunkName: "mobile" */ '@P/aiui/solution-aiui/mobile'
          ),
      },
      {
        path: 'morfei',
        name: 'morfei', //
        meta: {
          title: 'AIUI开放平台',
        },
        component: () =>
          import(
            /* webpackChunkName: "morfei" */ '@P/aiui/solution-aiui/morfei'
          ),
      },
      {
        path: 'shopping-guide',
        name: 'shopping-guide', //
        meta: {
          title: 'AIUI开放平台',
        },
        component: () =>
          import(
            /* webpackChunkName: "shopping-guide" */ '@P/aiui/solution-aiui/shopping-guide'
          ),
      },
      {
        path: 'webapi',
        name: 'webapi', //
        meta: {
          title: 'AIUI开放平台',
        },
        component: () =>
          import(
            /* webpackChunkName: "webapi" */ '@P/aiui/solution-aiui/webapi'
          ),
      },
      {
        path: 'wechat',
        name: 'wechat', //
        meta: {
          title: 'AIUI开放平台',
        },
        component: () =>
          import(
            /* webpackChunkName: "wechat" */ '@P/aiui/solution-aiui/wechat'
          ),
      },
      {
        path: 'development-kits',
        name: 'development-kits', //
        meta: {
          title: 'AIUI开放平台',
        },
        component: () =>
          import(
            /* webpackChunkName: "skillMovedPage" */ '@P/aiui/solution-aiui/development-kits'
          ),
      },
      {
        path: 'smart-hardware',
        name: 'smart-hardware', // 智能硬件解决方案
        meta: {
          title: 'AIUI开放平台',
        },
        component: () =>
          import(
            /* webpackChunkName: "smartHardware" */ '@P/aiui/solution-aiui/smart-hardware'
          ),
      },
      {
        path: 'assistant',
        name: 'assistant', // APP语音助手解决方案
        meta: {
          title: 'AIUI开放平台',
        },
        component: () =>
          import(
            /* webpackChunkName: "assistant" */ '@P/aiui/solution-aiui/assistant'
          ),
      },
      {
        path: 'soft-hardware',
        name: 'soft-hardware', // 软硬件一体化解决方案
        meta: {
          title: 'AIUI开放平台',
        },
        component: () =>
          import(
            /* webpackChunkName: "softHardware" */ '@P/aiui/solution-aiui/soft-hardware'
          ),
      },
      // {
      //   path: 'soft-hardware/HD6200MA',
      //   name: 'HD6200MA', // 软硬件一体化解决方案
      //   meta: {
      //     title: 'AIUI开放平台',
      //   },
      //   component: () =>
      //     import(
      //       /* webpackChunkName: "HD6200MA" */ '@P/aiui/solution-aiui/offline'
      //     ),
      // },
      {
        path: 'soft-hardware/ZG803',
        name: 'ZG803', // 软硬件一体化解决方案
        meta: {
          title: 'AIUI开放平台',
          metaInfo: {
            title: 'ZG803离线语音识别方案-AIUI开放平台',
            keywords:
              '离线语音识别,小家电语音交互开发套件,离线语音开发套件,小家电语音开发,台灯离线词唤醒,多语种语音识别,离线语音控制,文本转语音api,讯飞ZG803,文本转语音api,AI离线语音识别模块,大模型开发者api,中文语音识别api,edge语音识别',
            description:
              '集成多语种离线语音算法的高性能、低成本和高集成度的智能语音识别硬件模组。开箱即用，支持自定义命令指令，适用于国内外小家电场景。',
          },
        },
        component: () =>
          import(
            /* webpackChunkName: "RK3328" */ '@P/aiui/solution-aiui/ZG803'
          ),
      },
      {
        path: 'soft-hardware/usb-soundcard',
        name: 'USB', // 软硬件一体化解决方案
        meta: {
          title: 'AIUI开放平台',
          metaInfo: {
            title: 'USB声卡开发套件-AIUI开放平台',
            keywords:
              '机器人录音,远场收音,智能硬件开发,智能硬件设计,智能硬件收音,麦克风阵列,定向拾音,定向收音,智能硬件解决方案,录音声卡,USB声卡,USB设备',
            description:
              '多麦克风阵列构型录音声卡，适用于智能机器人，智慧大屏等设备开发评估。兼容多类型麦克风阵列；支持声源定位，180°/360°定向拾音；尺寸小巧，集成便捷。',
          },
        },
        component: () =>
          import(
            /* webpackChunkName: "usb-soundcard" */ '@P/aiui/solution-aiui/USB'
          ),
      },
      {
        path: 'soft-hardware/RK3328',
        name: 'RK3328', // 软硬件一体化解决方案
        meta: {
          title: 'AIUI开放平台',
          metaInfo: {
            title: 'RK3328降噪板开发套件-AIUI开放平台',
            keywords:
              'RK3328 AIUI降噪开发板是一款多麦克风阵列语音交互硬件模组，能直接输出降噪后音频，适用于服务机器人场景。',
            description:
              '全链路语音交互技术,语音开发板,降噪板,录音降噪,声源定位,回声消除,噪音抑制,屏蔽噪音,降噪模型,融合定位,噪声抑制,rk3328,讯飞降噪板',
          },
        },
        component: () =>
          import(
            /* webpackChunkName: "RK3328" */ '@P/aiui/solution-aiui/RK3328'
          ),
      },
      {
        path: 'soft-hardware/RK3328S',
        name: 'RK3328S', // 软硬件一体化解决方案
        meta: {
          title: 'AIUI开放平台',
          metaInfo: {
            title: 'RK3328评估板开发套件-AIUI开放平台',
            keywords:
              '语音评估板,录音降噪,声源定位,回声消除,噪音抑制,屏蔽噪音,rk3288,评估板套件',
            description:
              'RK3328 AIUI 评估板开发套件搭载 AIUI 全链路语音交互能力，能快速完成语音项目方案验证，适用于教育教培，服务机器人等场景。',
          },
        },
        component: () =>
          import(
            /* webpackChunkName: "RK3328S" */ '@P/aiui/solution-aiui/RK3328S'
          ),
      },
      {
        path: 'soft-hardware/RK3588',
        name: 'RK3588', // 软硬件一体化解决方案
        meta: {
          title: 'AIUI开放平台',
          metaInfo: {
            title: 'RK3588多模态开发套件-AIUI开放平台',
            keywords:
              'RK3588,多模态降噪,科大讯飞语音交互大模型,多模态交互,多模态语音,高噪场景收音,交互一体机,多模态识别,地铁语音购票,语音问询,多模态语音解决方案,公共场景收音,公共场景拾音,室外收音,室外语音交互,语音多模态,多模态语音识别,AIUIrk3588,rk3588电脑',
            description:
              '设备算力高，RK3588 AIUI多模态开发套件内部集成多模态交互引擎及AIUI全链路交互能力，适用于公共场景下的复杂高噪人机交互。',
          },
        },
        component: () =>
          import(
            /* webpackChunkName: "RK3588" */ '@P/aiui/solution-aiui/RK3588'
          ),
      },
      {
        path: 'soft-hardware/7911',
        name: '7911', // 软硬件一体化解决方案
        meta: {
          title: 'AIUI开放平台',
        },
        component: () =>
          import(/* webpackChunkName: "7911" */ '@P/aiui/solution-aiui/7911'),
      },
      {
        path: 'offline',
        name: 'offline', // 离线语音交互解决方案
        meta: {
          title: 'AIUI开放平台',
          metaInfo: {
            title: '离线语音交互解决方案-AIUI开放平台',
            keywords:
              '离线语音交互,弱网语音,无网语音,隐私语音交互,工业控制语音交互,家居语音交互,户外语音交互,车机语音交互,多语种识别,命令词自定义语音交互,离线语音转写,离线语音识别引擎,讯飞离线语音识别,离线语音包,讯飞离线语音,离线语音产品,离线语音合成',
            description:
              'AIUI离线语音交互是一种在无网络环境提供的语音服务，可纯离线使用，300ms实时识别出字，自定义技能支持垂直领域用户说法，适合无网、弱网、注重隐私和数据安全的场景。',
          },
        },
        component: () =>
          import(
            /* webpackChunkName: "offline" */ '@P/aiui/solution-aiui/offline'
          ),
      },
      {
        path: 'multimodality',
        name: 'multimodality', // 多模态交互解决方案
        meta: {
          title: 'AIUI开放平台',
          metaInfo: {
            title: '多模态降噪解决方案-AIUI开放平台',
            keywords:
              '智能自助终端语音交互,个性化语音合成,虚拟人交互,声纹检测,多模态交互,多模态语音,人机交互,大屏一体机语音交互,多模态融合技术,多模态识别,多模态数据融合,智能降噪,高噪场景多模态产品,多模态人机交互,多模态交互产品,AIUI多模态交互解决方案,多维感知,自助终端,多风格回复,虚拟人交互,人脸识别检测,手势识别检测,唇动识别检测,声纹识别检测,智能健身镜,大屏一体机,智能机器人,ai智能语音机器人,多模AIUI,多模态AIUI',
            description:
              '声音、图像多模态感知，智能降噪，提供声纹检测、性别年龄检测、人脸识别、唇动检测和手势识别。支持多风格回复、个性化合成、虚拟人交互。适用于各种自助终端、机器人、大屏一体机等产品，打造自然的人机交互体验。',
          },
        },
        component: () =>
          import(
            /* webpackChunkName: "multimodality" */ '@P/aiui/solution-aiui/multimodality'
          ),
      },
      {
        path: 'three-wakeup',
        name: 'three-wakeup', // 三级唤醒解决方案
        meta: {
          title: 'AIUI开放平台',
          metaInfo: {
            title: '三级唤醒解决方案-AIUI开放平台',
            keywords:
              '低功耗语音唤醒,三级语音唤醒,语音唤醒,低功耗语音交互,声纹验证,AP层低功耗,声纹注册',
            description:
              '智能硬件定制设计的语音交互方案，适用于对电池续航有要求或个人隐私敏感的智能硬件。唤醒效果好、功耗低、支持本人声纹验证；保障设备续航并解决个人数据隐私问题。',
          },
        },
        component: () =>
          import(
            /* webpackChunkName: "three-wakeup" */ '@P/aiui/solution-aiui/three-wakeup'
          ),
      },
      {
        path: 'noise-reduce',
        name: 'noise-reduce',
        meta: {
          title: 'AIUI开放平台',
        },
        component: () =>
          import(
            /* webpackChunkName: "noise-reduce" */ '@P/aiui/solution-aiui/noise-reduce'
          ),
      },
      {
        path: 'baby',
        name: 'baby',
        meta: {
          title: 'AIUI开放平台',
        },
        component: () =>
          import(/* webpackChunkName: "baby" */ '@P/aiui/solution-aiui/baby'),
      },
      {
        path: 'workflow',
        name: 'workflow',
        meta: {
          title: 'AIUI开放平台',
        },
        component: () =>
          import(
            /* webpackChunkName: "workflow" */ '@P/aiui/solution-aiui/workflow'
          ),
      },
      {
        path: 'collection',
        name: 'collection', // 解决方案聚合页H5
        meta: {
          title: '解决方案-AIUI开放平台',
        },
        component: () =>
          import(
            /* webpackChunkName: "collection" */ '@P/aiui/solution-aiui/collection'
          ),
      },
      // 前端声学
      {
        path: 'acoustics',
        name: 'acoustics', // 解决方案聚合页H5
        meta: {
          title: 'AIUI开放平台',
        },
        component: () =>
          import(
            /* webpackChunkName: "acoustics" */ '@P/aiui/solution-aiui/acoustics'
          ),
      },
      // 虚拟人
      {
        path: 'meta-human',
        name: 'meta-human',
        meta: {
          title: 'AIUI开放平台',
          metaInfo: {
            title: '虚拟人交互解决方案-AIUI开放平台',
            keywords:
              '虚拟人一体机,AI数字人互动,数字人语音交互,数字人营销,展厅数字人,数字人讲解,数字人语音交互/互动/交流,数字人解决方案,大模型数字人,数字人加盟,数字人定制,AI数字人交互,2D数字人,离线数字人,大模型数字人,教育数字人,医疗数字人,文旅数字人,营销数字人,数字人硬件,多模态虚拟人',
            description:
              'AIUI虚拟人交互方案提供公共环境下多模态录音降噪和星火大模型交互整套软硬件解决方案，为带屏硬件打造主动交互、言行灵动、知识专业的数字人对话体验，在政务大厅、党建播报、文旅和银行网点等场景的应用潜力巨大。支持数字人形象定制，实时语义控制肢体动作。',
          },
        },
        component: () =>
          import(
            /* webpackChunkName: "meta-human" */ '@P/aiui/solution-aiui/meta-human'
          ),
      },

      // 多模态交互
      {
        path: 'multimodal-interaction',
        name: 'multimodal-interaction',
        meta: {
          title: 'AIUI开放平台',
          metaInfo: {
            title: '多模态交互解决方案-AIUI开放平台',
            keywords:
              '多模态交互,实时音视频交互,手机助手,超拟人交互,数字人交互,定制虚拟人,数字人定制,个性化人设,虚拟人,百变人设,语音交互',
            description:
              'AIUI多模态交互是实时音频流交互，提供超拟人和虚拟人输出，支持1300+个性化人设回复风格，能为客户走制人设与虚拟人形象，打造专属IP形象，创造全新的人机交互体验。',
          },
        },
        component: () =>
          import(
            /* webpackChunkName: "multimodal-interaction" */ '@P/aiui/solution-aiui/multimodal-interaction'
          ),
      },

      // 多语种AI透明屏
      {
        path: 'transparent-screen',
        name: 'transparent-screen',
        meta: {
          title: 'AIUI开放平台',
          metaInfo: {
            title: '多语种AI透明屏-AIUI开放平台',
            keywords: '多语种AI透明屏',
            description: '解决公共服务窗口跨语言沟通难题，透明无界，沟通无限。',
          },
        },
        component: () =>
          import(
            /* webpackChunkName: "transparent-screen" */ '@P/aiui/solution-aiui/transparent-screen'
          ),
      },

      //虚拟人形象资产库
      {
        path: 'metahuman-image',
        name: 'metahuman-image',
        meta: {
          title: 'AIUI开放平台',
          metaInfo: {
            title: '虚拟人形象资产库-AIUI开放平台',
            keywords: '虚拟人形象资产库',
            description:
              '为带屏硬件打造主动交互、言行灵动、知识专业的虚拟人对话体验',
          },
        },
        component: () =>
          import(
            /* webpackChunkName: "metahuman-image" */ '@P/aiui/solution-aiui/metahuman-image'
          ),
      },
      {
        path: 'offline-interaction',
        name: 'offline-interaction',
        meta: {
          title: 'AIUI开放平台',
          metaInfo: {
            title: '离线交互解决方案-AIUI开放平台',
            keywords: '',
            description: '',
          },
        },
        component: () =>
          import(
            /* webpackChunkName: "offline-interaction" */ '@P/aiui/solution-aiui/offline-interaction'
          ),
      },
      {
        path: 'mobile-digital-person',
        name: 'mobile-digital-person',
        meta: {
          title: 'AIUI开放平台',
          metaInfo: {
            title: '移动端数字人解决方案-AIUI开放平台',
            keywords: '',
            description: '',
          },
        },
        component: () =>
          import(
            /* webpackChunkName: "mobile-digital-person" */ '@P/aiui/solution-aiui/mobile-digital-person'
          ),
      },
      {
        path: 'super-brain-core',
        name: 'super-brain-core',
        meta: {
          title: '讯飞超脑核心板',
          metaInfo: {
            title: '讯飞超脑核心板-AIUI开放平台',
            keywords: '',
            description: '',
          },
        },
        component: () =>
          import(
            /* webpackChunkName: "super-brain-core" */ '@P/aiui/solution-aiui/super-brain-core'
          ),
      },
      {
        path: 'super-brain-ifly',
        name: 'super-brain-ifly',
        meta: {
          title: '讯飞超脑板',
          metaInfo: {
            title: '讯飞超脑板-AIUI开放平台',
            keywords: '',
            description: '',
          },
        },
        component: () =>
          import(
            /* webpackChunkName: "super-brain-ifly" */ '@P/aiui/solution-aiui/super-brain-ifly'
          ),
      },
    ],
  },
  {
    path: '/app/:appId',
    component: AiuiAppLayout,
    children: [
      {
        path: '/',
        name: 'app', // 应用信息页
        redirect: { name: 'app-config' },
      },

      {
        path: 'info',
        name: 'app-info', // 应用信息页
        meta: {
          title: 'AIUI开放平台',
          metaInfo: {
            title: '应用信息-AIUI开放平台',
          },
        },
        component: () =>
          import(/* webpackChunkName: "app-info" */ '@P/aiui/app/info'),
      },

      {
        path: 'config',
        name: 'app-config', // 应用配置页
        meta: {
          title: 'AIUI开放平台',
          metaInfo: {
            title: '应用配置-AIUI开放平台',
          },
        },
        component: () =>
          import(
            /* webpackChunkName: "app-configs" */ '@P/aiui/app/config/main'
          ),
      },

      {
        path: 'download',
        name: 'app-tool', // 开发工具
        meta: {
          title: 'AIUI开放平台',
          metaInfo: {
            title: '资源下载-AIUI开放平台',
          },
        },
        component: () =>
          import(/* webpackChunkName: "app-tool" */ '@P/aiui/app/tool'),
      },
      {
        path: 'statistic',
        name: 'app-statistic', // 开发工具
        component: statisticLayout,
        children: [
          {
            path: '',
            name: 'statisticIndex', // 应用信息页
            redirect: 'serviceData',
          },
          {
            path: 'serviceData',
            name: 'app-statistic-service-index', // 服务统计
            meta: {
              title: 'AIUI开放平台',
              metaInfo: {
                title: '服务统计-AIUI开放平台',
              },
            },
            component: () =>
              import(
                /* webpackChunkName: "app-statistic-service-index" */ '@P/aiui/app/statisticServiceIndex'
              ),
          },
          {
            path: 'deviceData',
            name: 'app-users', // 设备统计
            meta: {
              metaInfo: {
                title: '设备统计-AIUI开放平台',
              },
            },
            component: () =>
              import(
                /* webpackChunkName: "app-statistic-user" */ '@P/aiui/app/users'
              ),
          },
          {
            path: 'online',
            name: 'app-online-device', // 激活设备明细
            meta: {
              title: 'AIUI开放平台',
              metaInfo: {
                title: '激活设备明细-AIUI开放平台',
              },
            },
            component: () =>
              import(
                /* webpackChunkName: "app-online-device" */ '@P/aiui/app/onlineDevice/index'
              ),
          },
          {
            path: 'appSources',
            name: 'app-sources', // 信源授权统计
            component: () =>
              import(
                /* webpackChunkName: "app-statistic-user" */ '@P/aiui/app/source'
              ),
          },
        ],
      },

      {
        path: 'otaUpdate',
        name: 'app-ota', // 开发工具
        meta: {
          title: 'AIUI开放平台',
          metaInfo: {
            title: '资源下载-AIUI开放平台',
          },
        },
        component: () =>
          import(
            /* webpackChunkName: "app-ota-update" */ '@P/aiui/app/otaUpdate'
          ),
      },

      {
        path: 'otaUpdateForm',
        name: 'app-ota-update-form', // 固件上传页
        meta: {
          title: 'AIUI开放平台',
        },
        component: () =>
          import(
            /* webpackChunkName: "app-ota-update-form" */ '@P/aiui/app/otaUpdateForm'
          ),
      },

      // {
      //   path: 'deviceData',
      //   name: 'app-users', // 设备统计
      //   meta: {
      //     metaInfo: {
      //       title: '设备统计-AIUI开放平台',
      //     },
      //   },
      //   component: () =>
      //     import(
      //       /* webpackChunkName: "app-statistic-user" */ '@P/aiui/app/users'
      //     ),
      // },
      // {
      //   path: 'app-sources',
      //   name: 'app-sources', // 信源授权统计
      //   component: () =>
      //     import(
      //       /* webpackChunkName: "app-statistic-user" */ '@P/aiui/app/source'
      //     ),
      // },
      // {
      //   path: 'serviceData',
      //   name: 'app-statistic-service-index', // 服务统计
      //   meta: {
      //     title: 'AIUI开放平台',
      //     metaInfo: {
      //       title: '服务统计-AIUI开放平台',
      //     },
      //   },
      //   component: () =>
      //     import(
      //       /* webpackChunkName: "app-statistic-service-index" */ '@P/aiui/app/statisticServiceIndex'
      //     ),
      // },
      // {
      //   path: 'online',
      //   name: 'app-online-device', // 激活设备明细
      //   meta: {
      //     title: 'AIUI开放平台',
      //     metaInfo: {
      //       title: '激活设备明细-AIUI开放平台',
      //     },
      //   },
      //   component: () =>
      //     import(
      //       /* webpackChunkName: "app-online-device" */ '@P/aiui/app/onlineDevice/index'
      //     ),
      // },
    ],
  },
  {
    path: '/message',
    component: defaultLayout,
    meta: {
      title: 'AIUI开放平台',
    },
    children: [
      {
        path: '/',
        name: 'message', // 消息中心页面
        component: () =>
          import(/* webpackChunkName: "message" */ '@P/aiui/message/index'),
      },
    ],
  },
  {
    path: '/app/:appId/versionDiff',
    component: defaultLayout,
    children: [
      {
        path: '/',
        name: 'app-version-diff', // 应用版本对比页
        component: () =>
          import(
            /* webpackChunkName: "app-version-diff" */ '@P/aiui/app/sandbox/versionDiff'
          ),
      },
    ],
  },
  {
    path: '/partner',
    component: aiuiDefaultLayout,
    children: [
      {
        path: '/',
        name: 'partner', // 技能搬家提示页
        meta: {
          title: 'AIUI开放平台',
        },
        component: () =>
          import(/* webpackChunkName: "partner" */ '@P/aiui/partner'),
      },
    ],
  },
  {
    path: '/updates',
    component: aiuiHome,
    children: [
      {
        path: '/',
        name: 'updates', // 动态更新
        meta: {
          title: 'AIUI开放平台',
        },
        component: () =>
          import(/* webpackChunkName: "updates" */ '@P/aiui/updates'),
      },
    ],
  },
  {
    path: '/modelExperience',
    component: defaultLayoutWithoutHeader,
    children: [
      {
        path: '/',
        name: 'modelExperience', // 动态更新
        meta: {
          title: 'AIUI开放平台',
          metaInfo: {
            title: '语音交互体验-AIUI开放平台',
            keywords:
              '体验AIUI效果,AIUI,讯飞语音交互,讯飞语义理解,AIUI人机交互,全链路语音交互,全链路人机交互,讯飞人机交互,讯飞自然语言理解,全双工交互,语音唤醒,语音助手开发,语音交互接入,接入语音交互,语音交互控制,语音交互设计,讯飞人工智能,讯飞AIUI开放平台,讯飞云平台,讯飞星火大模型,星火交互大模型,多模态唤醒,虚拟人互动,数字人互动,多语种识别,超拟人合成,声音复刻,硬件模组,讯飞星火,人机交互,智能硬件,消费电子语音交互,消费电子人机交互,手机语音交互,电视语音交互,机器人语音交互,地铁火车轨道交通语音交互,PC语音助手,语音软硬件,语音交互控制',
            description:
              '直接对话，即刻感受AIUI语音交互效果。AIUI是以讯飞星火大模型为核心的人机交互开发平台，具备多模态唤醒、虚拟人驱动、多语种识别、超拟人合成、声音复刻等特性。广泛应用于手机、电视、机器人扫读笔、语音购票等智能硬件设备上。提供SDK、Websocket、硬件模组等，接入集成简单，开箱即用。',
          },
        },
        component: () =>
          import(
            /* webpackChunkName: "modelExperience" */ '@P/aiui/modelExperience'
          ),
      },
    ],
  },
]
