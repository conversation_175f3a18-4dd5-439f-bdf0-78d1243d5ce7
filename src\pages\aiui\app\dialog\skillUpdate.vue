<template>
  <el-dialog
    class="app-config-skill-version"
    title="技能版本"
    :visible.sync="dialog.show"
    width="570px"
  >
    <os-table
      class="mgb24"
      :tableData="tableData"
      @change="getVersion"
    >
      <el-table-column
        prop="number"
        label="版本"
        width="130"
      ></el-table-column>
      <el-table-column
        label="版本说明"
        width="130"
      >
        <template slot-scope="scope">
          <span v-if="!scope.row.updateLog">-</span>
          <el-popover
            v-else
            class="update-log"
            trigger="hover"
            placement="bottom-start"
            :content="scope.row.updateLog"
          >
            <div slot="reference">{{ scope.row.updateLog }}</div>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column
        prop="date"
        label="发布时间"
        width="130"
      >
        <template slot-scope="scope">{{ scope.row.updateTime | date('yyyy-MM-dd') }}</template>
      </el-table-column>
      <el-table-column
        label="引用"
        width="55"
      >
        <template slot-scope="scope">
          <el-radio
            v-model="radio"
            :label="scope.row.number"
          ></el-radio>
        </template>
      </el-table-column>
    </os-table>
    <span slot="footer" class="dialog-footer">
      <el-button @click="dialog.show=false">取消</el-button>
      <el-button type="primary" @click="setVersion">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: 'app-config-skill-version',
  props: {
    dialog: {
      type: Object,
      default: {
        show: false
      }
    },
    skillId: '',
    outNumber: ''
  },
  data () {
    return {
      tableData: {
        total: 0,
        page: 1,
        size: 5,
        list: []
      },
      radio: '',
      skills: []
    }
  },
  watch: {
    'dialog.show'(val) {
      if(val) {
        this.getSkillVersions(1)
      }
    },
    outNumber(val) {
      this.radio = val
    }
  },
  methods: {
    getSkillVersions() {
      let self = this, tmp
      self.tableData.loading = true
      self.tableData.list = []
      this.$utils.httpGet(
        this.$config.api.AIUI_SKILL_VERSION,
        {
          businessId: self.skillId
        },
        {
          success: res => {
            self.tableData.loading = false
            self.skills = res.data.skills
            self.tableData.total = res.data.count
            this.getVersion(1);
          },
          error: err => {
            self.tableData.loading = false
          }
        }
      );
    },
    getVersion (page) {
      let self = this;
      this.tableData.page = page || 1
      self.tableData.loading = true
      let pageIndex = page || self.tableData.page
      let from = (pageIndex - 1) * this.tableData.size
      let to = from + this.tableData.size;
      self.tableData.list = self.skills.slice(from, to)
      self.tableData.loading = false
    },
    setVersion() {
      if(!this.isNumberEqual(this.radio, this.outNumber)){
        this.$emit('setNewOutNumber', this.radio)
      }
      this.dialog.show = false
    },
    isNumberEqual(num1 = "0.0.0", num2 = "0.0.0") {
      let arr1 = num1.split("."),
        arr2 = num2.split(".");
      if (arr1[0] == arr2[0] && arr1[1] == arr2[1] && arr1[2] == arr2[2]) {
        return true;
      } else {
        return false
      }
    },
  }
}
</script>

<style lang="scss">
  .app-config-skill-version {
    .el-radio__label {
      visibility: hidden;
    }
  }
</style>