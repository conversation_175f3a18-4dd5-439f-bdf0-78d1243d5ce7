<template>
  <div>
    <div class="aiui-store-skills">
      <brief-header :skill="skill" :id="id" :type="type"></brief-header>
      <div class="tab-container">
        <el-tabs v-model="tabType">
          <el-tab-pane label="技能概述" name="0" key="0"></el-tab-pane>
          <el-tab-pane
            label="信源详情"
            name="1"
            key="1"
            v-if="skill.hasSource"
          ></el-tab-pane>
          <el-tab-pane
            label="参数详情"
            name="2"
            key="2"
            v-if="skillParamsShow"
          ></el-tab-pane>
        </el-tabs>
      </div>

      <introduction-detail
        :skill="skill"
        v-if="tabType == '0'"
      ></introduction-detail>
      <param-detail :skill="skill" v-if="tabType == '2'"></param-detail>
      <source-detail
        :providers="providers"
        :sourceAgreement="sourceAgreement"
        v-if="tabType == '1'"
      ></source-detail>
    </div>
  </div>
</template>

<script>
import BriefHeader from './skillDetail/briefHeader.vue'
import ParamDetail from './skillDetail/paramDetail.vue'
import SourceDetail from './skillDetail/sourceDetail.vue'
import IntroductionDetail from './skillDetail/introductionDetail.vue'

export default {
  name: 'store-skill',
  data() {
    return {
      id: '',
      type: '',
      skill: {
        detail: {},
        faq: [],
        phrases: {},
        protocols: {},
        examples: {},
      },
      providers: [],
      sourceAgreement: [],
      tabType: '0',
    }
  },
  watch: {},
  computed: {
    skillParamsShow() {
      return this.skill.protocols.answer || this.skill.protocols.semantic
    },
  },
  created() {
    this.id = this.$route.params.skillId
    this.type = this.$route.query.type
    this.getSkillDetail()
  },
  methods: {
    getSkillDetail() {
      let self = this
      this.$utils.httpGet(
        this.$config.api.AIUI_STORE_SKILL_DETAIL,
        {
          id: this.id,
        },
        {
          success: (res) => {
            let tempData = res.data || {}
            tempData.detail = {
              ...tempData.detail,
            }
            tempData.phrases = {
              ...tempData.phrases,
            }

            self.skill = tempData
            if (res.data.hasSource) {
              self.getProviders()
              self.getSourceAgreement()
            }
            self.$store.dispatch('aiuiStore/setSkillDetail', res.data.detail)
          },
          error: (err) => {},
        }
      )
    },
    getProviders() {
      let self = this
      this.$utils.httpGet(
        this.$config.api.AIUI_STORE_SKILL_PROVIDERS,
        {
          business: this.id,
        },
        {
          success: (res) => {
            self.providers = res.data
          },
          error: (err) => {},
        }
      )
    },
    getSourceAgreement() {
      let self = this
      this.$utils.httpGet(
        this.$config.api.AIUI_STORE_SKILL_CONTENTDATA,
        {
          method: 'getFunctions',
          business: this.id,
        },
        {
          success: (res) => {
            self.sourceAgreement = res.data
          },
          error: (err) => {},
        }
      )
    },
  },
  components: {
    BriefHeader,
    ParamDetail,
    SourceDetail,
    IntroductionDetail,
  },
}
</script>
<style lang="scss" scoped>
.tab-container {
  position: sticky;
  top: -1px;
  z-index: 1;
  background: #fff;
  height: 78px;
  border-bottom: 1px solid #eff1f1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  :deep(.el-tabs__header) {
    margin: 0;
  }
  :deep(.el-tabs__item) {
    padding: 0 50px;
    font-size: 18px;
    height: 78px;
    line-height: 78px;
  }
  :deep(.el-tabs__nav-wrap::after) {
    display: none;
  }
}

// 技能商店适配不同屏幕
@media screen and (max-width: 1601px) {
  .tab-container {
    height: 48px;

    :deep(.el-tabs__item) {
      padding: 0 50px;
      font-size: 14px;
      height: 48px;
      line-height: 48px;
    }
  }
}
</style>
