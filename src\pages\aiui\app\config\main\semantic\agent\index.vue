<template>
  <div>
    <agentSOS v-if="currentScene && currentScene.sos === true" />
    <agent
      v-if="
        currentScene && currentScene.sceneBoxId && currentScene.sos !== true
      "
    />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import agent from './agent'
import agentSOS from './agentSOS'

export default {
  data() {
    return {}
  },
  computed: {
    ...mapGetters({
      currentScene: 'aiuiApp/currentScene',
      appInfo: 'aiuiApp/app',
      subAccountEditable: 'aiuiApp/subAccountEditable',
    }),
    appId() {
      return this.$route.params.appId
    },
  },
  components: {
    agent,
    agentSOS,
  },
}
</script>
<style lang="scss" scoped></style>
