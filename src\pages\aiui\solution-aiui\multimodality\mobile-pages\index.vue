<template>
  <div class="main-content">
    <MyHeader> </MyHeader>
    <section class="main-content-banner">
      <div class="banner-text">
        <h2>离线语音交互解决方案</h2>
        <p class="banner-text-content">
          没有网络也可以使用语音服务，<br />
          实现离线语音控制交互。
        </p>

        <div class="hor-btn">
          <el-button
            class="banner-text-button"
            @click="toConsole"
            type="primary"
            round
            >合作咨询</el-button
          >
        </div>
      </div>
    </section>

    <section class="section-nav">
      <ul class="nav-ul" id="nav-ul">
        <li
          v-for="item in nav_list"
          :key="item.id"
          @click="gotoSelection(item.id)"
        >
          {{ item.name }}
        </li>
      </ul>

      <i
        v-if="nav_list.length > 4"
        :class="[
          'nav-btn',
          nav_flag ? 'el-icon-caret-bottom' : 'el-icon-caret-top',
        ]"
        id="nav-btn"
        @click="handleNavExpand"
      ></i>
    </section>

    <section class="section section1">
      <h2>应用场景</h2>
      <p class="desc-title">
        适用于各种自助终端、机器人、大屏一体机等产品，显著提升交互体验
      </p>

      <ul>
        <li v-for="(item, index) in app_scenario" :key="index" class="app">
          <img :src="item.src" :alt="item.alt" />
          <p class="app-text">{{ item.alt }}</p>
        </li>
      </ul>
    </section>

    <section class="section section2">
      <h2>方案介绍</h2>

      <div class="content">
        <p class="part-title">声音感知</p>
        <ul>
          <li v-for="item in voice_list" :key="item.index">
            <img :src="item.src" alt="" />
            <p class="text">{{ item.title }}</p>
            <p class="sub-text">{{ item.sub_title }}</p>
          </li>
        </ul>
      </div>

      <div class="content">
        <p class="part-title">图像感知</p>
        <ul>
          <li v-for="item in image_list" :key="item.index">
            <img :src="item.src" alt="" />
            <p class="text">{{ item.title }}</p>
            <p class="sub-text">{{ item.sub_title }}</p>
          </li>
        </ul>
      </div>

      <div class="content">
        <p class="part-title">多样交互</p>
        <ul>
          <li v-for="item in variety_list" :key="item.index">
            <img :src="item.src" alt="" />
            <p class="text">{{ item.title }}</p>
            <p class="sub-text">{{ item.sub_title }}</p>
          </li>
        </ul>
      </div>
    </section>

    <section class="section section3">
      <h2>方案特点</h2>

      <ul>
        <li>
          <div class="icon-adv icon_combination"></div>
          <p>支持能力随意组合</p>
        </li>
        <li>
          <div class="icon-adv icon_offline"></div>
          <p>支持离线使用</p>
        </li>
        <li>
          <div class="icon-adv icon_integration"></div>
          <p>支持软核集成</p>
        </li>
        <li>
          <div class="icon-adv icon_thirdparty"></div>
          <p>支持适配第三方</p>
        </li>
      </ul>
    </section>

    <section class="section section4">
      <h2>合作咨询</h2>

      <h3>提交信息，我们会尽快与您联系</h3>

      <div class="cooperation-btn" @click="toConsole">申请合作</div>
    </section>

    <section class="section section-footer">
      <div class="footer-title">
        联系我们
        <span class="arrow" @click="clickFooterArrow">▼</span>
      </div>
      <div class="content">
        <p>联系电话：19955108393</p>
        <p>开发者交流群：617903641</p>
        <p>商务技术支持：<EMAIL></p>
      </div>

      <div class="footer-title">
        友情链接
        <span class="arrow" @click="clickFooterArrow">▼</span>
      </div>
      <div class="content">
        <p>科大讯飞</p>
        <p>讯飞开放平台</p>
      </div>

      <div class="corporation-info">
        ©科大讯飞股份有限公司 皖ICP备05001217号
      </div>
    </section>
  </div>
</template>

<script>
import MyHeader from '@P/aiui/solution-aiui/mobile-header.vue'
import '../../../../../../static/vue-awesome-swiper'
import information from '../information.vue'

export default {
  name: 'offline',
  data() {
    return {
      nav_list: [
        { name: '应用场景', id: 1 },
        { name: '方案介绍', id: 2 },
        { name: '方案特点', id: 3 },
        { name: '合作咨询', id: 4 },
      ],

      app_scenario: [
        {
          alt: '大屏一体机',
          src: require('../../../../../assets/images/solution/multimodality/img_integrated_machine.png'),
          desc: '使用人脸识别、唇动检测、手势识别和麦克风阵列技术，助力虚拟人在公共高噪场所同顾客亲切交互。',
        },
        {
          alt: '服务机器人',
          src: require('../../../../../assets/images/solution/multimodality/img_service_robot.png'),

          desc: '使用人脸识别、唇动检测、多风格回复、个性化合成等技术，助力机器人实现主动唤醒、人脸跟随，同用户进行个性化交流。',
        },
        {
          alt: '智能健身镜',
          src: require('../../../../../assets/images/solution/multimodality/img_fitness_mirror.png'),
          desc: '使用手势识别、口语化合成等技术，助力健身镜读懂用户的动作、成为用户的私人教练。',
        },
      ],

      voice_list: [
        {
          src: require('../../../../../assets/images/solution/multimodality/icon_gender_age.png'),
          title: '性别年龄检测',
          sub_title: '性别年龄检测准确率超95%',
        },
        {
          src: require('../../../../../assets/images/solution/multimodality/icon_voiceprint.png'),
          title: '声纹检测',
          sub_title: '支持声纹的检索与对比验证',
        },
        {
          src: require('../../../../../assets/images/solution/multimodality/icon_hyperdirectional_wave.png'),
          title: '超指向波束',
          sub_title: '±15°范围内精准拾音',
        },
      ],

      image_list: [
        {
          src: require('../../../../../assets/images/solution/multimodality/icon_face_detection.png'),
          title: '人脸识别',
          sub_title: '识别人脸信息、开启主动交互',
        },
        {
          src: require('../../../../../assets/images/solution/multimodality/icon_lipping.png'),
          title: '唇动检测',
          sub_title: '有效提升语音识别准确率',
        },
        {
          src: require('../../../../../assets/images/solution/multimodality/icon_gesture.png'),
          title: '手势识别',
          sub_title: '支持27种常用手势交互',
        },
      ],

      variety_list: [
        {
          src: require('../../../../../assets/images/solution/multimodality/icon_virtual_human.png'),
          title: '虚拟人交互',
          sub_title: '虚拟人实时互动，动作表情生动',
        },
        {
          src: require('../../../../../assets/images/solution/multimodality/icon_multi_style.png'),
          title: '多风格回复',
          sub_title: '成人儿童感知，回复风格自然',
        },
        {
          src: require('../../../../../assets/images/solution/multimodality/icon_personalized_synthesis.png'),
          title: '个性化合成',
          sub_title: '口语化情感化发音，交流更亲切',
        },
      ],

      interactList: [
        {
          name: '家居',
          desc: '',
          klass: 'img_full_duplex',
        },
        {
          name: '户外',
          desc: '',
          klass: 'img_free_wake_click',
        },
        {
          name: '车机',
          desc: '',
          klass: 'img_multimodal_click',
        },
        {
          name: '工业控制',
          desc: '',
          klass: 'img_offlineinteraction_click',
        },
      ],

      cells1: {
        title: '离线支持多语种识别',
        desc: '支持普通话、日语、俄语、韩语、法语、西班牙语、阿拉伯语、德语、越南语、泰语、印地语、意大利语、葡萄牙语',
        imageName: 'img_language_support',
      },
      cells2: {
        title: '适用于离线人机对话交互',
        desc: '用于人机对话交互场景<br/>每次对话音频长度不超过20秒<br/>能有效地保障人机交互效果',
        imageName: 'img_audio_requirements',
      },
      cells3: {
        title: '离线支持用户自由说',
        desc: '支持语音技能定制<br/>30000个词条，说法不限制<br/>可满足垂直场景高频说法全部覆盖',
        imageName: 'img_interactive_instructions',
      },
      cells4: {
        title: '支持动态覆盖更新',
        desc: '语音技能词条资源支持动态构建<br/>以500个词条为例，可以200ms完成覆盖<br/>更新不影响用户使用',
        imageName: 'img_update_mode',
      },
      cells5: {
        title: '系统算力要求',
        desc: '支持安卓、ios、linux(arm)系统<br/>端上1核，1000mips，内存100M可满足用户随意说<br/>仅使用200个离线命令词，算力可降50%',
        imageName: 'img_calculate_power_demand',
      },
      activeName: '0',
      swiperOption: {
        loop: true,
        autoplay: {
          delay: 4000,
          // disableOnInteraction: false,
        },
      },
    }
  },
  mounted() {
    this.swiper.on('slideChange', () => {
      this.activeName = this.swiper.realIndex + ''
    })
  },
  methods: {
    gotoSelection(id) {
      const section = document.getElementsByClassName(`section` + id)[0]
      if (section) {
        section.scrollIntoView({ behavior: 'smooth' })
      }
    },

    handleNavExpand() {
      const nav_ul = document.getElementById('nav-ul')
      const navIcon = document.getElementById('nav-btn')

      navIcon.addEventListener('click', () => {
        if (this.nav_flag) {
          nav_ul.classList.add('expanded')
        } else {
          nav_ul.classList.remove('expanded')
        }
        this.nav_flag = !this.nav_flag
      })
    },

    clickFooterArrow() {
      const titles = document.querySelectorAll('.footer-title')
      titles.forEach((title) => {
        title.addEventListener('click', () => {
          const content = title.nextElementSibling
          const arrow = title.querySelector('.arrow')

          if (content.style.display === 'none' || !content.style.display) {
            content.style.display = 'block'
            arrow.classList.add('up')
          } else {
            content.style.display = 'none'
            arrow.classList.remove('up')
          }
        })
      })
    },

    toConsole() {
      // 判断是否有百度SEM营销参数，有的话，提交表单页需要带上
      const search = window.location.search
      if (search) {
        window.open(`/solution/apply/16${search}`)
      } else {
        window.open('/solution/apply/16')
      }
    },
    toggleActive(val) {
      // this.activeName = val
      this.swiper.slideToLoop(Number(val))
    },
  },
  components: { information, MyHeader },
}
</script>

<style lang="scss" scoped>
.main-content {
  background-color: #f6f7f9;
  width: 750px;
  overflow: hidden;
  &-banner {
    background: url(~@A/images/solution/smart-hardware/mobile/banner2.jpg)
      center no-repeat;
    background-size: cover;
    height: 375px;
    overflow: hidden;
    width: 100%;
  }

  .banner-text {
    margin-top: 64px;
    padding-left: 44px;
    h2 {
      font-size: 48px;
      font-weight: 600;
      color: #000000;
      line-height: 68px;
    }
    p {
      width: 648px;
      font-size: 24px;
      font-weight: 300;
      color: #000000;
      line-height: 40px;
      text-align: left;
    }
    .hor-btn {
      display: flex;
      flex-direction: row;
      justify-content: flex-start;
      margin-top: 40px;
      div:nth-child(2) {
        margin-left: 30px;
      }
    }
  }

  .section {
    h2 {
      font-size: 48px;
      font-weight: 600;
      color: #000000;
      line-height: 68px;
      text-align: center;
      margin: 0 auto;
      margin-top: 50px;
      margin-bottom: 30px;
    }
    p,
    ul {
      margin-bottom: 0;
    }
    max-width: 750px;
    overflow: hidden;
    margin: 0 auto;
    .section-title {
      text-align: center;
      font-size: 36px;
      font-weight: 500;
      color: #444444;
      line-height: 54px;
      .arrow {
        width: 26px;
        height: 24px;
        display: inline-block;
        background-repeat: no-repeat;
        background-size: contain;
      }
      .arrow-left {
        background-position: left;
        background-image: url(~@A/images/solution/smart-hardware/arrow-left.png);
      }
      .arrow-right {
        background-position: right;
        background-image: url(~@A/images/solution/smart-hardware/arrow-right.png);
      }
    }
    .section-tabs {
      margin-top: 60px;
    }
  }
  .section-nav {
    background-color: #ffffff;
    position: relative;
    width: 100%;
    border: 1px solid #ddd;
    padding: 0 30px;
    .nav-ul {
      display: flex;
      justify-content: space-evenly;
      flex-wrap: wrap;
      overflow: hidden; /* 默认隐藏超出部分 */
      max-height: 68px; /* 折叠状态时，只显示一行 */
      transition: max-height 0.3s ease; /* 动画过渡效果 */
    }
    .nav-ul.expanded {
      max-height: 200px; /* 展开状态：允许显示多行 */
    }
    .nav-ul li {
      flex: 0 0 auto;
      margin-right: 30px;
      white-space: nowrap;
      cursor: pointer;
      font-size: 24px;
      font-weight: 400;
      text-align: center;
      line-height: 68px;
      color: #7a7a7a;
    }
    .nav-btn {
      position: absolute;
      height: 68px;
      width: 60px;
      right: 0px;
      top: 60%;
      transform: translateY(-50%);
      font-size: 48px;
      // background-color: red;
    }
  }

  .section1 {
    padding: 0 29px;

    .desc-title {
      font-size: 24px;
      text-align: center;
      color: #7a7a7a;
      margin-bottom: 20px;
    }

    > ul {
      display: flex;
      justify-content: space-between;
      li {
        width: 30%;
        // height: 400px;
        height: auto;
        margin-bottom: 60px;

        img {
          width: 100%;
          // height: 100%;
          margin-bottom: 10px;
        }

        p {
          height: 38px;
          text-align: center;
          margin: 0 auto;
          font-size: 24px;
          line-height: 38px;
          margin-bottom: 20px;
        }
      }
    }
  }

  .section2 {
    padding: 0 29px;
    .content {
      margin-bottom: 50px;
      .part-title {
        font-size: 28px;
        color: #7a7a7a;
        text-align: center;
        margin: 10px auto;
      }
      ul {
        display: flex;

        justify-content: space-evenly;
        li {
          width: 30%;
          text-align: center;
          margin: 0 auto;
          img {
            width: 130px;
            height: 130px;
            margin-bottom: 10px;
          }
          .text {
            font-weight: 600;
            color: #000000;
            font-size: 24px;
            height: 35px;
            margin-bottom: 10px;
          }
          .sub-text {
            font-size: 20px;
            color: #bfa599;
          }
        }
      }
    }
  }
  .section3 {
    ul {
      display: flex;
      justify-content: center;
      margin-top: 20px;
      li {
        width: 285px;
        height: 177px;
        border: 2px solid #eff1f1;
        border-radius: 15px;
        text-align: center;
        padding-top: 38px;
        // cursor: pointer;
        &:hover {
          border: 1px solid rgb(31, 144, 254);
          //  box-shadow: 0px 0px 2px 1px rgba(45, 153, 255, 0.8);
          box-shadow: rgba(45, 153, 255, 0.8) 0 0 4px;
        }
        p {
          font-size: 18px;
          font-family: Microsoft YaHei;
          font-weight: 400;
          color: #666666;
          line-height: 18px;
          margin-top: 26px;
        }
        .icon-adv {
          margin: 0 auto;
        }
        .icon_combination {
          width: 57px;
          height: 60px;
          background: url(~@A/images/solution/multimodality/icon_combination.png)
            center/100% no-repeat;
        }
        .icon_offline {
          width: 68px;
          height: 52px;
          background: url(~@A/images/solution/multimodality/icon_offline.png)
            center/100% no-repeat;
        }
        .icon_integration {
          width: 58px;
          height: 60px;
          background: url(~@A/images/solution/multimodality/icon_integration.png)
            center/100% no-repeat;
        }
        .icon_thirdparty {
          width: 59px;
          height: 61px;
          background: url(~@A/images/solution/multimodality/icon_thirdparty.png)
            center/100% no-repeat;
        }
      }
      li + li {
        margin-left: 20px;
      }
    }
  }

  .section4 {
    padding: 0 29px;
    text-align: center;
    margin-bottom: 20px;
    h3 {
      font-size: 24px;
      color: #7a7a7a;
      margin: 30px auto;
      margin-bottom: 20px;
    }
    .cooperation-btn {
      width: 222px;
      height: 81px;
      background: linear-gradient(90deg, #26bcf6, #1b7bf7 98%);
      border-radius: 52px;
      text-align: center;
      margin: 0 auto;
      font-weight: 600;
      line-height: 81px;
      font-size: 32px;
      color: #f0f0f0;
      margin-top: 60px;
    }
  }

  .section-footer {
    width: 750px;
    background-color: #061930;
    padding: 30px 20px;
    .footer-title {
      color: #c1c1c1;
      display: flex;
      justify-content: space-between;
      align-items: center;
      cursor: pointer;
      font-size: 28px;
      font-weight: bold;
      margin-bottom: 20px;
    }
    .content {
      margin-top: 10px;
      display: none; /* 默认隐藏内容 */
      font-size: 24px;
      color: #7b7e93;
    }

    .content p {
      margin: 5px 0;
      font-size: 24px;
      line-height: 49px;
    }

    .arrow {
      font-size: 26px;
      transition: transform 0.5s ease;
    }

    .arrow.up {
      transform: rotate(180deg);
    }

    .corporation-info {
      margin-top: 30px;
      color: #7b7e93;
      font-size: 24px;
      text-align: left;
      line-height: 33px;
    }
  }
}
</style>
