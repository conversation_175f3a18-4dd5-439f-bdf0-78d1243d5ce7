<template>
  <div class="role-card" @click="cardClick()">
    <div class="avatar-section">
      <div class="avatar-wrapper">
        <img class="avatar" :src="cardData.image" alt="头像" />
      </div>
    </div>

    <div class="info-section">
      <div class="name-line ellipsis">{{ cardData.name }}</div>
      <div class="desc-line">{{ cardData.description }}</div>
      <div class="action-line">
        <div>
          <span class="label">被引用量&nbsp;{{ cardData.useCount }}</span>
          <span class="label">大模型角色</span>
        </div>
        <el-popover
          placement="right"
          trigger="hover"
          popper-class="role-card-popover"
          v-model="visible"
        >
          <ul>
            <li @click="copy">
              <a>创建副本</a>
            </li>
            <li @click="cardClick">
              <a>编辑</a>
            </li>
            <li @click="del">
              <a style="color: #ff4242">删除</a>
            </li>
          </ul>
          <i class="el-icon-more" slot="reference" @click.stop></i>
        </el-popover>
      </div>
    </div>
  </div>
</template>

<script>
import CreateRoleDialog from './dialog/createRole.vue'

export default {
  name: 'role-card',
  props: {
    cardData: {
      type: Object,
      default: () => ({
        name: '角色名称角色名称角色名称角色名称角色名称',
        description: '角色描述',
        type: '角色类型',
      }),
    },
    dialog: {
      type: Object,
    },
  },
  components: {
    CreateRoleDialog,
  },
  data() {
    return {
      visible: false,
    }
  },
  methods: {
    cardClick() {
      this.$router.push({
        name: 'role',
        params: {
          roleId: this.cardData.id,
        },
      })
    },
    del() {
      if (this.cardData.useCount) {
        this.$message.warning(`该角色已被设备/应用引用，请先取消引用后再删除`)
        return
      }
      this.$confirm(
        `角色删除后不可恢复，请谨慎操作。`,
        `确定删除角色 - ${this.cardData.name}`,
        {
          confirmButtonText: '确定删除',
          cancelButtonText: '取消',
          confirmButtonClass: 'el-button--danger',
          type: 'warning',
          showClose: false,
        }
      )
        .then(() => {
          this.delRole(this.cardData)
        })
        .catch(() => {})
    },
    delRole(data) {
      this.$utils.httpGet(
        this.$config.api.AIUI_ROLE_DELETE,
        {
          roleId: data.id,
        },
        {
          success: (res) => {
            if (res.code === '0') {
              this.$message.success('删除成功')
              this.$emit('change')
            }
          },
          error: (err) => {},
        }
      )
    },
    copy() {
      if (this.cardData.name && this.cardData.name.length > 28) {
        this.dialog.type = 'copy'
        this.dialog.fromId = this.cardData.id
        this.dialog.show = true
        return
      }
      let params = {
        fromRoleId: this.cardData.id,
      }
      let api = this.$config.api.AIUI_ROLE_CREATE
      this.$utils.httpPost(api, JSON.stringify(params), {
        config: {
          headers: {
            'Content-Type': 'application/json;charset=UTF-8',
          },
        },
        success: (res) => {
          this.$message.success('成功创建副本。')
          this.visible = false
          this.$emit('change')
        },
        error: (err) => {},
      })
    },
  },
  created() {},
}
</script>

<style lang="scss" scoped>
.role-card {
  width: 100%;
  min-width: 0;
  display: flex;
  gap: 16px;
  background: linear-gradient(353deg, rgba(255, 255, 255, 0) 0%, #eff8ff 100%);
  border: 1.5px solid #ffffff;
  border-radius: 8px;
  padding: 29px 22px;
  cursor: pointer;
  transition: box-shadow 0.3s ease;
  .avatar-wrapper {
    width: 92px;
    height: 92px;
    border-radius: 50%;
    padding: 2px;
    background: linear-gradient(135deg, #7fcffd, #006eff);
    display: flex;
    align-items: center;
    justify-content: center;
    > img {
      width: 88px;
      height: 88px;
      border-radius: 50%;
      object-fit: cover;
      // border: 2px solid #fff;
    }
  }
  .info-section {
    flex: 1;
    min-width: 0;
    .name-line {
      font-size: 16px;
      font-family: PingFang SC, PingFang SC-500;
      font-weight: 500;
      margin-bottom: 5px;
    }
    .desc-line {
      color: #c8c8ce;
      font-size: 12px;
      font-weight: 400;
      height: 40px;
      line-height: 20px;
      word-break: break-all;
      overflow: hidden;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      margin-bottom: 15px;
    }
  }
  .action-line {
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: #8d8d99;
    .label {
      font-size: 12px;
      font-family: PingFang SC, PingFang SC-400;
      font-weight: 400;
    }
    .label + .label {
      margin-left: 20px;
    }
    .el-icon-more {
      margin-left: auto;
    }
  }
  &:hover {
    box-shadow: 2px 2px 15.8px 0px rgba(212, 212, 212, 0.71);
  }
}
</style>

<style lang="scss">
.role-card-popover {
  padding: 9px;
  border-radius: 10px;
  li {
    width: 97px;
    height: 36px;
    line-height: 36px;
    padding: 0 8px;
    border-radius: 10px;
    cursor: pointer;
    &:hover {
      background: #f6f8f9;
    }
  }
}
</style>
