<template>
  <div>
    <repoSOS v-if="currentScene && currentScene.sos === true" />
    <repo
      v-if="
        currentScene && currentScene.sceneBoxId && currentScene.sos !== true
      "
    />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import repo from './repo'
import repoSOS from './repoSOS'

export default {
  data() {
    return {}
  },
  computed: {
    ...mapGetters({
      currentScene: 'aiuiApp/currentScene',
      appInfo: 'aiuiApp/app',
      subAccountEditable: 'aiuiApp/subAccountEditable',
    }),
    appId() {
      return this.$route.params.appId
    },
  },
  components: {
    repo,
    repoSOS,
  },
}
</script>
<style lang="scss" scoped></style>
