<template>
  <div class="content-container">
    <div class="content-container-content" ref="scrollContent">
      <div class="form-menu">
        <ul>
          <li
            v-for="(item, index) in menuList"
            :key="item.name"
            :class="{ hd2: true, active: item.name === activeModule }"
            @click="jumpTo(item.name)"
          >
            {{ item.value }}
          </li>
        </ul>
      </div>
      <div ref="basic"><basic /></div>

      <div class="gutter" ref="semantic">
        <semantic></semantic>
      </div>
      <!-- currentScene.sos === true 表示新的智能体模式场景应用-->
      <div class="gutter" v-if="sceneRoleId" ref="role">
        <role></role>
      </div>
      <div class="gutter" ref="recognition">
        <recognition></recognition>
      </div>

      <div class="gutter" ref="other">
        <other></other>
      </div>
    </div>
  </div>
</template>
<script>
import { throttle } from 'lodash-es'

import { mapGetters } from 'vuex'
import basic from './basic'
import recognition from './recognition'
import semantic from './semantic'
import role from './role'
import synthesis from './synthesis'
import other from './other'

export default {
  data() {
    return {
      activeModule: 'basic',
    }
  },
  computed: {
    ...mapGetters({
      currentScene: 'aiuiApp/currentScene',
      app: 'aiuiApp/app',
      sceneRoleId: 'aiuiApp/sceneRoleId',
      subAccountEditable: 'aiuiApp/subAccountEditable',
    }),
    appId() {
      return this.$route.params.appId
    },

    menuList() {
      return [
        {
          name: 'basic',
          value: '基础配置',
        },

        {
          name: 'semantic',
          value: '语义模型配置',
        },
        this.sceneRoleId
          ? {
              name: 'role',
              value: '回复角色配置',
            }
          : null,
        {
          name: 'recognition',
          value: '语音识别配置',
        },
        {
          name: 'other',
          value: '其他配置',
        },
      ].filter(Boolean)
    },
  },

  methods: {
    jumpTo(module) {
      let el = this.$refs[module]

      if (el) {
        // el.scrollIntoView({
        //   behavior: 'smooth',
        //   block: 'start',
        //   inline: 'nearest',
        // })
        this.$refs['scrollContent'].scrollTo({
          top: el.offsetTop,
          behavior: 'smooth',
        })
      }
      // this.activeModule = module
    },

    handleScroll() {
      let scrollDom = this.$refs['scrollContent']

      if (
        this.$refs['other'] &&
        scrollDom.scrollTop > this.$refs['other'].offsetTop - 1
      ) {
        this.activeModule = 'other'
        return
      }
      if (
        this.$refs['recognition'] &&
        scrollDom.scrollTop > this.$refs['recognition'].offsetTop - 1
      ) {
        this.activeModule = 'recognition'
        return
      }

      if (
        this.$refs['role'] &&
        scrollDom.scrollTop > this.$refs['role'].offsetTop - 1
      ) {
        this.activeModule = 'role'
        return
      }
      if (
        this.$refs['semantic'] &&
        scrollDom.scrollTop > this.$refs['semantic'].offsetTop - 1
      ) {
        this.activeModule = 'semantic'
        return
      }
      this.activeModule = 'basic'
    },
  },

  mounted() {
    const dom = this.$refs['scrollContent']
    if (dom) {
      // dom.addEventListener(
      //   'scroll',
      //   throttle(() => {
      //     this.handleScroll()
      //   }, 10)
      // )
      dom.addEventListener('scroll', this.handleScroll)
    }
  },

  destroyed() {
    const dom = this.$refs['scrollContent']
    if (dom) {
      dom.removeEventListener('scroll', this.handleScroll)
    }
  },

  // watch: {
  //   activeModule(val) {
  //     this.$nextTick(() => {
  //       let scrollDom = this.$refs['scrollContent']
  //       if (val === 'basic') {
  //         scrollDom.scrollTo({
  //           top: 0,
  //           behavior: 'smooth',
  //         })
  //       } else if (val === 'semantic') {
  //         scrollDom.scrollTo({
  //           top: this.$refs['semantic'].offsetTop,
  //           behavior: 'smooth',
  //         })
  //       } else if (val === 'role') {
  //         scrollDom.scrollTo({
  //           top: this.$refs['role'].offsetTop,
  //           behavior: 'smooth',
  //         })
  //       } else if (val === 'recognition') {
  //         scrollDom.scrollTo({
  //           top: this.$refs['recognition'].offsetTop,
  //           behavior: 'smooth',
  //         })
  //       } else if (val === 'other') {
  //         scrollDom.scrollTo({
  //           top: this.$refs['other'].offsetTop,
  //           behavior: 'smooth',
  //         })
  //       }
  //     })
  //   },
  // },

  components: {
    basic,
    recognition,
    semantic,
    role,
    synthesis,
    other,
  },
}
</script>
<style lang="scss" scoped>
@import './common.scss';
.gutter {
  margin-top: 10px;
}
.content-container {
  background: $secondary-bgc;
  position: relative;
}

.content-container-content {
  padding: 14px 16px;
  height: calc(100vh - 128px);
  overflow: auto;
}
</style>
