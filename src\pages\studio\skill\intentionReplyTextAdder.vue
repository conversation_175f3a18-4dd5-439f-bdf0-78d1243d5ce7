<template>
  <div>
    <div class="container">
      <ul
        v-if="data && data.length > 0"
        class="confirm-list"
        :style="{ paddingBottom: data.length < max ? '0px' : '15px' }"
      >
        <li v-for="(item, index) in data" :key="item.answer">
          <div class="content-container">
            <span class="number-label">{{ index + 1 }}</span>
            <!-- <span>{{ item.replace(/\|1/, '').replace(/\|0/, '') }}</span> -->
            <inteligient-rich-input
              :placeholder="editPlaceholder"
              :value="{
                text: item.answer,
                labels: item.labels,
                changed: item.changed,
              }"
              :showAdd="false"
              :showSwitch="false"
              :disabled="disabled"
              :edit="true"
              :editIndex="index"
              @onEdit="onEdit"
              @change="onChange(index)"
              :saveOnBlur="true"
            >
            </inteligient-rich-input>
          </div>
          <div class="style-container">
            <span>回复风格：</span>
            <a-switch
              :disabled="disabled"
              checked-children="儿童"
              un-checked-children="成人"
              :checked="item.type == 1"
              @change="onItemStyleChange(item, index)"
            />
          </div>

          <i
            class="delete ic-r-delete"
            @click="onDelClick(item)"
            v-if="!disabled"
          ></i>
        </li>
      </ul>
      <div class="confirm-adder" v-show="data.length < max && !disabled">
        <span class="number-label">{{ data.length + 1 }}</span>

        <!-- <inteligient-input
          :showAdd="false"
          :showSwitch="true"
          @onAdd="onAdd"
          v-model="text"
          :placeholder="placeholder"
          @checkedChange="onCheckedChange"
          ref="intelInput"
        ></inteligient-input> -->

        <inteligient-rich-input
          v-model="textObj"
          :showAdd="false"
          :showSwitch="true"
          @onAdd="onAdd"
          :placeholder="placeholder"
          @checkedChange="onCheckedChange"
          ref="intelInput"
          key="intentionReply"
        >
        </inteligient-rich-input>
      </div>
    </div>
    <div class="new-plus" v-show="data.length < max">
      <el-button
        type="text"
        size="small"
        @click="onIconAdd"
        :disabled="disabled"
      >
        + 添加文本回复
      </el-button>
    </div>
  </div>
</template>

<script>
import InteligientInput from './referSlots/inteligientInput.vue'
import InteligientRichInput from './referSlots/inteligientRichInput.vue'
import Switch from 'ant-design-vue/lib/switch'
import 'ant-design-vue/lib/switch/style/css'

export default {
  components: { InteligientInput, InteligientRichInput, [Switch.name]: Switch },
  name: 'intention-reply-text-adder',
  props: {
    data: Array,
    disabled: Boolean,
    slotNames: Array,
    reg: {
      default: '',
    },
    warning: {
      type: String,
      default: '输入有误',
    },
    placeholder: String,
    editPlaceholder: String,
    max: {
      type: Number,
      default: 5,
    },
  },
  data() {
    return {
      // text: '',
      textObj: { text: '', type: '0', labels: [] },
      checked: false,
    }
  },
  methods: {
    checkIntentionSlots(val) {
      let reg = /\{(.*?)\}/g
      let tmp = val.match(reg)
      if (!tmp) {
        return true
      }
      if (!this.slotNames.length) {
        this.$message.warning('当前意图无语义槽，请勿使用花括号')
        return false
      }
      for (let i = 0; i < tmp.length; i++) {
        let slot = tmp[i].substring(1, tmp[i].length - 1)
        if (this.slotNames.indexOf(slot) == -1) {
          this.$message.warning(
            '花括号内参数名称需和当前意图下的必选语义槽名相同'
          )
          return false
        }
      }
      return true
    },
    onDelClick(text) {
      this.$emit('del', text)
    },

    onAdd(textObj) {
      let val = textObj.text.trim()
      let type
      if (!val) {
        return this.$message.warning('输入不能为空')
      }
      if (val.replace(/[^\{\}]/g, '').replace(/(\{\})/g, '')) {
        return this.$message.warning('花括号必须成对出现')
      }
      const idx = this.data.findIndex((item) => item.answer === val)
      if (idx > -1) {
        return this.$message.warning('不能重复添加')
      }
      if (this.reg && !this.reg.test(val)) {
        return this.$message.warning(this.warning)
      }
      if (!this.checkIntentionSlots(textObj.text)) {
        return
      }
      if (this.checked) {
        // 选择了儿童
        // val = val + '|1'
        type = '1'
      } else {
        // val = val + '|0'
        type = '0'
      }
      this.$emit('add', { answer: textObj.text, type, labels: textObj.labels })
      // this.$refs.intelInput.resetCheckedValue()
      // this.$refs.intelInput.clearHTML()
      // this.textObj = { text: '', labels: [] }
    },

    resetInitialStatus() {
      this.$refs.intelInput.resetCheckedValue()
      this.$refs.intelInput.clearHTML()
      this.textObj = { text: '', labels: [] }
    },
    onEdit(textObj, index) {
      let val = textObj.text.trim()
      let type
      if (!val) {
        return this.$message.warning('输入不能为空')
      }
      if (val.replace(/[^\{\}]/g, '').replace(/(\{\})/g, '')) {
        return this.$message.warning('花括号必须成对出现')
      }
      // const idx = this.data.findIndex((item) => item.answer === val)
      // if (idx > -1) {
      //   return this.$message.warning('不能重复添加')
      // }
      if (this.reg && !this.reg.test(val)) {
        return this.$message.warning(this.warning)
      }
      if (!this.checkIntentionSlots(textObj.text)) {
        return
      }
      this.$emit(
        'edit',
        { answer: textObj.text, labels: textObj.labels },
        index
      )
    },
    onIconAdd() {
      this.$refs.intelInput.doAdd()
    },
    onCheckedChange(val) {
      this.checked = val
    },
    onItemStyleChange(item, index) {
      // let newItem = item
      // if (item.endsWith('|0')) {
      //   newItem = item.replace(/\|0/, '|1')
      // } else if (item.endsWith('|1')) {
      //   newItem = item.replace(/\|1/, '|0')
      // } else {
      //   // 没有是老数据, 默认是成人
      //   newItem = item + '|1'
      // }
      let newData = this.data.map((it, i) => {
        if (i === index) {
          return { ...it, type: it.type == '1' ? '0' : '1' }
        } else {
          return it
        }
      })
      this.$emit('modifyStyle', newData)
    },
    onChange(index) {
      console.log('receive onChange', index)
      if (index !== -1) {
        this.$emit('change', index)
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.new-plus {
  text-align: center;
  // padding: 10px 0;
  border-bottom: 1px solid #d5d8de;
  border-left: 1px solid #d5d8de;
  border-right: 1px solid #d5d8de;
  cursor: pointer;
}
.container {
  width: 100%;
  border: 1px solid #d5d8de;
  padding: 0 16px;
  .confirm-adder {
    display: flex;
    align-items: center;
    padding-right: 20px;
    margin-top: 18px;
    margin-bottom: 10px;
  }
  .confirm-list {
    padding-top: 15px;

    > li {
      display: flex;
      align-items: center;
      justify-content: space-between;
      position: relative;
      padding-right: 20px;
      &:hover {
        .delete {
          display: block;
        }
      }
      .delete {
        position: absolute;
        right: 0;
        // color: #b8babf;
        color: #1784e9;
        cursor: pointer;
        display: none;
        font-size: 20px;
        // &:hover {
        //   color: #1784e9;
        // }
      }
    }
    li + li {
      margin-top: 18px;
    }
    margin-bottom: 0;
  }
  .number-label {
    margin-right: 20px;
    color: #b8babf;
  }
  .style-container {
    min-width: 136px;
    margin-left: 5px;
  }
  .content-container {
    max-width: calc(100% - 136px);
    display: flex;
    align-items: center;
    flex: 1;
  }
}
</style>
<style lang="scss" scoped>
.container {
  :deep(.el-input__inner) {
    border: none;
  }
  :deep(.ant-switch) {
    min-width: 60px !important;
  }
}
</style>
