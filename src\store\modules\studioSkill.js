// @服务信息
import { utils } from '@U'
import { api } from '@/config'
import Router from '../../router'

export default {
  namespaced: true,
  state: {
    id: '',
    skill: {},
    intention: {},
    intentionReferenced: {},
    debugConsole: [],
    utteranceColor: {},
    entityPopover: {
      show: false,
      showType: null,
      data: {},
      style: {},
      rect: {},
      showTop: false,
    },
    utteranceEntityPopover: {
      show: false,
      data: {},
      rect: {},
      showTop: false,
    },
    skillPopover: {
      show: false,
      rect: {},
    },
    createIntentDialog: {
      show: false,
    },
    hasSkillEntity: false,
    hasSkillAuxiliary: false,
    hasBlackIntent: false,
    subAccountSkillAuths: [],
    rightTestOpen: false,
    entityInIntent: {}, // 意图中的entities，用于测试栏
    publishResultForTest: '',
    agentPlatformInfo: {
      platform: null,
      resourceType: null,
      queryKey: 'query',
    },
    skillIconBgColors: ['#7B67FB', '#F18A56', '#009BFF', '#32C9FB'],
  },

  actions: {
    setPublishResultForTest({ state, commit, rootState }, val) {
      commit('setPublishResultForTest', val)
    },
    setSkill({ state, commit, rootState }, skillId) {
      commit('setId', skillId)
      commit('setSkill', {})
      utils.httpGet(
        api.STUDIO_SKILL_DETAIL,
        {
          skillId: skillId,
        },
        {
          success: (res) => {
            if (!res.flag) {
              Router.push({ name: 'studio-handle-platform-skills' })
            }
            // 5-原来的os开放技能,增加一种9-AIUI中新建的开放技能
            if (
              parseInt(res.data.type) !== 5 &&
              parseInt(res.data.type) !== 9
            ) {
              res.data.privateSkill = true
            }
            if (res.data.latestVersion) {
              res.data.latestVersionArr = res.data.latestVersion.split('.')
              res.data.latestVersionArr[res.data.latestVersionArr.length - 1] =
                parseInt(
                  res.data.latestVersionArr[
                    res.data.latestVersionArr.length - 1
                  ]
                ) + 1
            } else if (res.data.onlineVersion) {
              res.data.latestVersionArr = res.data.onlineVersion.split('.')
              res.data.latestVersionArr[res.data.latestVersionArr.length - 1] =
                parseInt(
                  res.data.latestVersionArr[
                    res.data.latestVersionArr.length - 1
                  ]
                ) + 1
            } else {
              res.data.latestVersionArr = []
            }
            commit('setSkill', {
              ...res.data,
              // 两个版本相同，证明上次是刚发布的，这时界面上updateLog应该清空
              updateLog:
                res.data.latestVersion === res.data.onlineVersion
                  ? ''
                  : res.data.updateLog,
            })
          },
          error: (err) => {
            Router.push({ name: 'studio-handle-platform-skills' })
          },
        }
      )
    },
    setId({ state, commit, rootState }, id) {
      commit('setId', id)
    },
    setIntention({ state, commit, rootState }, intentId) {
      utils.httpGet(
        api.STUDIO_INTENT_DETAIL,
        {
          businessId: state.id,
          intentId: intentId,
        },
        {
          success: (res) => {
            commit('setIntention', res.data)
          },
          error: (err) => {
            commit('setIntention', {})
          },
        }
      )
    },
    setIntentionReferenced(
      { state, commit, rootState },
      { quoteId, businessId }
    ) {
      utils.httpGet(
        api.STUDIO_SYSTEM_INTENTS_DETAIL,
        {
          quoteId,
          businessId,
        },
        {
          success: (res) => {
            commit('setIntentionReferenced', res.data)
          },
          error: (err) => {
            commit('setIntentionReferenced', {})
          },
        }
      )
    },
    resetIntentionReferenced({ state, commit, rootState }) {
      commit('resetIntentionReferenced')
    },
    setDebugConsole({ state, commit, rootState }, debugConsole) {
      commit('setDebugConsole', debugConsole)
    },
    setUtteranceColor({ state, commit, rootState }, slotName) {
      let utteranceColor = state.utteranceColor
      let keys = Object.keys(utteranceColor)
      let colorCount = 12
      let colors = [
        'rgba(255, 213, 0, 0.3)',
        'rgba(35, 217, 176, 0.3)',
        'rgba(82, 128, 255, 0.3)',
        'rgba(255, 77, 77, 0.3)',
        'rgba(23, 132, 233, 0.3)',
        'rgba(24, 218, 0, 0.3)',
        'rgba(157, 218, 0, 0.3)',
        'rgba(255, 164, 0, 0.3)',
        'rgba(170, 150, 200, 0.3)',
        'rgba(150, 188, 200, 0.3)',
        'rgba(169, 200, 150, 0.3)',
        'rgba(200, 150, 150, 0.3)',
      ]
      if (!utils.inArray(keys, slotName)) {
        let number =
          keys.length + 1 - colorCount * parseInt(keys.length / colorCount)
        utteranceColor[slotName] = {
          'background-color': colors[number - 1],
        }
      }
      commit('setUtteranceColor', utteranceColor)
    },
    setEntityPopover({ state, commit, rootState }, data) {
      if (data.show) {
        data.rect = JSON.parse(JSON.stringify(data.rect))
        data.pageY = document.body.clientHeight - 60
        commit('openEntityPopover', data)
      } else {
        commit('closeEntityPopover')
      }
    },
    setUtteranceEntityPopover({ state, commit, rootState }, data) {
      if (
        data.data &&
        data.data.selectedText.markId &&
        state.utteranceEntityPopover.show &&
        state.utteranceEntityPopover.data.selectedText.rangeStartMark ===
          state.utteranceEntityPopover.data.selectedText.rangeEndMark &&
        parseInt(
          state.utteranceEntityPopover.data.selectedText.rangeStartMark
        ) === data.data.selectedText.markId
      ) {
        data = JSON.parse(JSON.stringify(state.utteranceEntityPopover))
        data.data.selectedText.destory = true
      }
      if (data.show) {
        data.rect = JSON.parse(JSON.stringify(data.rect))
        data.pageY = document.body.clientHeight - 60
        commit('openUtteranceEntityPopover', data)
      } else {
        commit('closeUtteranceEntityPopover')
      }
    },
    setSkillPopover({ state, commit, rootState }, data) {
      if (data.show) {
        data.rect = JSON.parse(JSON.stringify(data.rect))
        commit('openSkillPopover', data)
      } else {
        commit('closeSkillPopover')
      }
    },
    openCreateIntentDialog({ state, commit, rootState }) {
      commit('setCreateIntentDialog', true)
    },
    closeCreateIntentDialog({ state, commit, rootState }) {
      commit('setCreateIntentDialog', false)
    },
    initHasSkillQuote({ state, commit, rootState }, type) {
      utils.httpGet(
        api.STUDIO_SKILL_ENTITYS,
        {
          businessId: state.id,
          pageIndex: 1,
          pageSize: 10,
          search: '',
          type: type || 0,
        },
        {
          success: (res) => {
            switch (type) {
              case 0:
                if (res.data.count) {
                  commit('setHasSkillEntity', true)
                } else {
                  commit('setHasSkillEntity', false)
                }
                break
              case 1:
                if (res.data.count) {
                  commit('setHasSkillAuxiliary', true)
                } else {
                  commit('setHasSkillAuxiliary', false)
                }
                break
              default:
                break
            }
          },
          error: (err) => {},
        }
      )
    },
    setHasBlackIntent({ state, commit }, data) {
      commit('setHasBlackIntent', data)
    },
    setSubAccountSkillAuths({ commit }) {
      utils.httpGet(
        api.SUB_USER_SKILL_AUTH,
        {},
        {
          success: (res) => {
            commit('setSubAccountSkillAuths', res.data.edit)
          },
          error: (err) => {},
        }
      )
    },
    setRightTestOpen({ commit }, data) {
      commit('setRightTestOpen', data)
    },
    setEntityInIntent({ state, commit }, data) {
      utils.httpGet(api.STUDIO_INTENT_SLOTS, data, {
        success: (res) => {
          let tmp = {}
          res.data.forEach((item) => {
            tmp[item.slotName] = item.entityName
          })
          commit('setEntityInIntent', tmp)
        },
        error: (err) => {},
      })
    },
    setAgentPlatformInfo({ commit }, data) {
      commit('setAgentPlatformInfo', data)
    },
  },

  mutations: {
    setId(state, id) {
      state.id = id
    },
    setPublishResultForTest(state, val) {
      state.publishResultForTest = val
    },
    setSkill(state, skill) {
      state.skill = skill
    },
    setIntention(state, intention) {
      state.intention = intention
    },
    setIntentionReferenced(state, intention) {
      state.intentionReferenced = intention
    },
    resetIntentionReferenced(state) {
      state.intentionReferenced = {}
    },
    setDebugConsole(state, debugConsole) {
      state.debugConsole = debugConsole
    },
    setUtteranceColor(state, utteranceColor) {
      state.utteranceColor = utteranceColor
    },
    openEntityPopover(state, data) {
      state.entityPopover.show = true
      state.entityPopover.showType = data.showType
      state.entityPopover.data = JSON.parse(JSON.stringify(data.data))
      state.entityPopover.style =
        data.style && JSON.parse(JSON.stringify(data.style))
      state.entityPopover.rect = JSON.parse(JSON.stringify(data.rect))
      state.entityPopover.showTop = data.pageY - data.rect.y < 300
    },
    closeEntityPopover(state) {
      state.entityPopover.show = false
      state.entityPopover.showType = null
      state.entityPopover.data = {}
      state.entityPopover.style = {}
      state.entityPopover.rect = {}
    },
    openUtteranceEntityPopover(state, data) {
      state.utteranceEntityPopover.show = true
      state.utteranceEntityPopover.data = JSON.parse(JSON.stringify(data.data))
      state.utteranceEntityPopover.rect = JSON.parse(JSON.stringify(data.rect))
      state.utteranceEntityPopover.showTop = data.pageY - data.rect.y < 300
    },
    closeUtteranceEntityPopover(state) {
      state.utteranceEntityPopover.show = false
      state.utteranceEntityPopover.data = {}
      state.utteranceEntityPopover.rect = {}
    },
    openSkillPopover(state, data) {
      state.skillPopover.show = true
      state.skillPopover.rect = data.rect
    },
    closeSkillPopover(state) {
      state.skillPopover.show = false
      state.skillPopover.rect = {}
    },
    setCreateIntentDialog(state, data) {
      state.createIntentDialog.show = data
    },
    setHasSkillEntity(state, data) {
      state.hasSkillEntity = data
    },
    setHasSkillAuxiliary(state, data) {
      state.hasSkillAuxiliary = data
    },
    setHasBlackIntent(state, data) {
      state.hasBlackIntent = data
    },
    setSubAccountSkillAuths(state, data) {
      state.subAccountSkillAuths = data
    },
    setRightTestOpen(state, data) {
      state.rightTestOpen = data
    },
    setEntityInIntent(state, data) {
      state.entityInIntent = data
    },
    setAgentPlatformInfo(state, data) {
      state.agentPlatformInfo = data
    },
  },

  getters: {
    id(state, getters, rootState) {
      return state.id
    },
    skill(state, getters, rootState) {
      return state.skill
    },
    intention(state, getters, rootState) {
      return state.intention
    },
    intentionReferenced(state, getters, rootState) {
      return state.intentionReferenced
    },
    debugConsole(state, getters, rootState) {
      return state.debugConsole
    },
    utteranceColor(state, getters, rootState) {
      return state.utteranceColor
    },
    entityPopover(state, getters, rootState) {
      return state.entityPopover
    },
    utteranceEntityPopover(state, getters, rootState) {
      return state.utteranceEntityPopover
    },
    skillPopover(state, getters, rootState) {
      return state.skillPopover
    },
    createIntentDialog(state, getters, rootState) {
      return state.createIntentDialog
    },
    hasSkillEntity(state, getters, rootState) {
      return state.hasSkillEntity
    },
    hasSkillAuxiliary(state, getters, rootState) {
      return state.hasSkillAuxiliary
    },
    hasBlackIntent(state, getters, rootState) {
      return state.hasBlackIntent
    },
    subAccountSkillAuths(state) {
      return state.subAccountSkillAuths
    },
    rightTestOpen(state) {
      return state.rightTestOpen
    },
    entityInIntent(state) {
      return state.entityInIntent
    },
    publishResultForTest(state) {
      return state.publishResultForTest
    },
    agentPlatformInfo(state) {
      return state.agentPlatformInfo
    },
    skillIconBgColors(state) {
      return state.skillIconBgColors
    },
  },
}
