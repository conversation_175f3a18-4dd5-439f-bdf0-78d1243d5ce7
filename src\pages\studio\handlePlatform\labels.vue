<template>
  <div class="os-scroll">
    <handle-platform-top></handle-platform-top>
    <div class="handle-platform-content">
      <div class="mgb24">
        <div class="if-alc">
          <el-button
            icon="ic-r-plus"
            type="primary"
            size="medium"
            @click="openCreateLabel"
          >
            创建动作
          </el-button>
          <el-button
            type="text"
            size="small"
            style="margin-left: 24px"
            @click="linkOfficialLabel"
          >
            查看官方交互标签
          </el-button>
        </div>
        <div class="fr" @keyup.enter="getLabels(1)">
          <el-input
            class="search-area"
            placeholder="通过名称、英文标识搜索标签"
            size="medium"
            v-model="searchVal"
          >
            <i
              slot="suffix"
              class="el-input__icon el-icon-search search-area-btn"
              @click="getLabels(1)"
            />
          </el-input>
        </div>
      </div>
      <os-table
        :tableData="tableData"
        class="auxiliar-table"
        style="margin-bottom: 56px; cursor: pointer"
        @change="getLabels"
        @edit="toEdit"
        @del="del"
        @row-click="toEdit"
        v-if="hasItem"
      >
        <el-table-column prop="zhName" width="120" label="交互标签名称">
          <template slot-scope="scope">
            <div>{{ scope.row.zhName || '-' }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="value" width="120" label="英文标识">
          <template slot-scope="scope">
            <div class="auxiliarie-name" :title="scope.row.value">
              {{ scope.row.value }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="description" width="200" label="描述">
          <template slot-scope="scope">
            <div>{{ scope.row.description || '-' }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="label" width="100" label="类型">
          <template slot-scope="scope">
            <div>{{ scope.row.label }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="picture" width="100" label="示意">
          <template slot-scope="scope">
            <div>
              <img
                :src="
                  scope.row.picture ||
                  'https://aiui-file.cn-bj.ufileos.com/avatar/default.png'
                "
                class="label-picture"
              />
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="updateTime" width="200" label="更新时间">
          <template slot-scope="scope">
            <div>{{ scope.row.updateTime | date('yyyy-MM-dd hh:mm:ss') }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="number" width="120" label="被引用数">
          <template slot-scope="scope">
            <!-- <span
              v-if="scope.row.count"
              class="text-primary"
              style="cursor: pointer"
              @click="openCountDialog(scope.row)"
              >{{ scope.row.count }}</span
            > -->
            <span>{{ scope.row.number }}</span>
          </template>
        </el-table-column>
      </os-table>
      <div class="create-guide" v-else>
        <div class="icon"></div>
        <p class="title">
          你还没有创建任何标签，
          <a @click="openCreateLabel"> 点击创建 </a>
        </p>
      </div>
    </div>
    <create-label :dialog="dialog" @change="getLabels" />
  </div>
</template>

<script>
import HandlePlatformTop from './top.vue'
import CreateLabel from './dialog/createLabel.vue'
// import SkillQuoteDialog from './dialog/skillQuote.vue'
import { mapGetters } from 'vuex'

export default {
  name: 'studio-handle-platform-labels',
  data() {
    return {
      nav: 'auxiliaries',
      searchVal: '',
      tableData: {
        loading: true,
        total: 0,
        page: 1,
        size: 10,
        handles: ['edit', 'del'],
        handleColumnText: '操作',
        list: [],
      },
      dialog: {
        show: false,
      },
      hasItem: true,
      // countDialog: {
      //   show: false,
      //   entityId: '',
      // },
    }
  },
  computed: {
    ...mapGetters({
      subAccount: 'user/subAccount',
    }),
  },
  created() {
    this.getLabels(1)
    // this.dialog.show = true
    if (localStorage.getItem('pageHandle') === 'createLabel') {
      this.dialog.show = true
      localStorage.setItem('pageHandle', null)
    }
  },
  methods: {
    getLabels(page) {
      let self = this
      this.tableData.loading = true
      this.$utils.httpGet(
        this.$config.api.STUDIO_LABEL_LIST,
        {
          pageIndex: page || this.tableData.page,
          pageSize: this.tableData.size,
          search: this.searchVal,
        },
        {
          success: (res) => {
            if (res.data.count <= 0 && !self.searchVal) {
              self.hasItem = false
            } else {
              self.hasItem = true
            }
            self.tableData.list = res.data.labels
            self.tableData.total = res.data.count
            self.tableData.page = res.data.pageIndex
            self.tableData.size = res.data.pageSize
            self.tableData.loading = false
          },
          error: (err) => {},
        }
      )
    },
    openCreateLabel() {
      this.dialog.show = true
    },
    // 查看官方交互标签
    linkOfficialLabel() {
      let routeName = this.subAccount
        ? 'sub-studio-official-labels'
        : 'studio-official-labels'
      let routeData = this.$router.resolve({
        name: routeName,
      })
      window.open(routeData.href, '_blank')
    },

    toEdit(data) {
      this.subAccount
        ? this.$router.push({
            name: 'sub-label',
            params: { labelId: data.id },
          })
        : this.$router.push({
            name: 'label',
            params: { labelId: data.id },
          })
    },
    del(data) {
      let self = this
      if (data.number > 0) {
        return this.$message.warning('该交互标签已被引用，请取消引用后再删除')
      }

      this.$confirm(
        '交互标签删除后不可恢复，请谨慎操作。',
        `确定删除交互标签 - ${data.value}`,
        {
          confirmButtonText: '确定删除',
          cancelButtonText: '取消',
          confirmButtonClass: 'el-button--danger',
          type: 'warning',
          showClose: false,
        }
      )
        .then(() => {
          self.doDel(data)
        })
        .catch(() => {})
    },
    doDel(data) {
      let self = this
      // 根据count 引用数，如果count 大于 0，直接弹出不可以删除
      // if (data.number > 0) {
      //   return this.$message.warning('该交互标签已被引用，请取消引用后再删除')
      // }
      this.tableData.loading = true
      this.$utils.httpPost(
        this.$config.api.STUDIO_LABEL_DEL,
        {
          id: data.id,
        },
        {
          success: (res) => {
            self.$message.success('删除成功')
            if (self.tableData.list.length === 1 && self.tableData.page > 1) {
              self.tableData.page -= 1
            }
            self.getLabels()
          },
          error: (err) => {},
        }
      )
    },
  },
  components: {
    HandlePlatformTop,
    CreateLabel,
  },
}
</script>

<style lang="scss" scoped>
.label-picture {
  width: 20px;
  height: 20px;
}
.handle-platform-content {
  max-width: 1200px;
  margin: auto;
}
.search-area {
  width: 480px;
}

.entity-status {
  width: 8px;
  height: 8px;
  border: 2px solid;
  border-radius: 8px;
  margin-right: 6px;
  &-2 {
    border-color: $grey4;
  }
  &-1 {
    border-color: $success;
  }
  &-txt {
    color: $grey5;
  }
  &-count {
    color: $primary;
  }
}
.text-blod,
.auxiliarie-name {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.create-guide {
  margin: 76px 0;
  text-align: center;
  font-size: 16px;
  color: $grey5;
  .icon {
    margin: 0 auto 24px;
    width: 120px;
    height: 120px;
    background: url(../../../assets/images/app/create-app.png) center no-repeat;
    background-size: 100%;
  }
  .title {
    font-size: 16px;
    font-weight: 600;
    a {
      font-weight: 600;
    }
  }
  .desc {
    margin: 24px auto;
    width: 480px;
    font-size: 14px;
    line-height: 22px;
  }
}
</style>
<style lang="scss">
.el-table--enable-row-hover .el-table__body tr:hover > td {
  .auxiliaries-page-entity-zh-name {
    color: $primary;
  }
}
.el-table .ic-r-edit {
  color: $primary;
}
</style>
<style lang="scss"></style>
