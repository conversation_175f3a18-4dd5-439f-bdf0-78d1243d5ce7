<template>
  <div class="main-content">
    <section class="main-content-banner">
      <div class="banner-text">
        <h2>语音大屏调度解决方案</h2>
        <p class="pc-show banner-text-content">
          面向政务、交通、医疗、商显等大屏交互场景，语音交互赋能大屏一<br />
          句话直达深层页面，让大屏更懂用户，提高业务办理和演示效率。
        </p>
        <p class="m-show">
          面向政务、交通、医疗、商显等<br />大屏交互场景，语音交互赋能大屏
        </p>
        <p class="m-show">
          一句话直达深层页面，让大屏更懂用户，<br />提高业务办理和演示效率。
        </p>
        <div class="banner-text-button" @click="toConsole">合作咨询</div>
      </div>
    </section>
    <section class="section section-2">
      <div class="section-title">应用场景</div>
      <div class="section-sub-title">
        应用于指挥调度、监控中心、多功能展厅等场景下，实现语音操控，打造智能化交互体验
      </div>
      <div class="section-item">
        <ul>
          <li v-for="(item, index) in app_scenario" :key="index" class="app">
            <img :src="item.src" :alt="item.alt" />
            <p class="app-text">{{ item.alt }}</p>
          </li>
        </ul>
      </div>
    </section>
    <section class="section section-3-1">
      <div class="section section-3-1 bg">
        <div class="section-title">方案架构</div>
        <div class="section-hor">
          <p>拾音设备</p>
          <p>语音语义服务</p>
          <p>业务后处理服务</p>
          <p>大屏系统</p>
        </div>
        <div class="section-ver">
          <p>内容播报</p>
          <p>调度控制</p>
          <p>知识问答</p>
        </div>
      </div>
    </section>
    <section class="section section-3">
      <div class="section-title">方案优势</div>
      <div class="section-item">
        <ul>
          <li v-for="(item, index) in pg_advantage" :key="index">
            <div class="section-item-bg" v-if="index % 2">
              <img :src="item.src" />
            </div>
            <div class="section-item-text">
              <img :src="item.icon" />
              <div class="section-item-text-title">{{ item.title }}</div>
              <p v-html="item.text" class="pc-show"></p>
              <p
                v-html="item.m_text ? item.m_text : item.text"
                class="m-show"
              ></p>
            </div>
            <div class="section-item-bg" v-if="!(index % 2)">
              <img :src="item.src" />
            </div>
          </li>
        </ul>
      </div>
    </section>
    <section class="section section-4">
      <div class="section-title">合作案例</div>
      <div class="section-item">
        <img
          src="../../../../assets/images/solution/digital-screen-lamp/section-4-1.png"
        />
        <div class="section-item-text">
          <p>能源电力语音调度</p>
          <p>
            某能源电力集团在数据大屏上集成语音调度解决方案，实现语音打开页面、控制风机、查询发电量、电力知识问答等功能。同时，方案提供声纹认证等能力，用于风机控制时的身份确认，替代传统鼠标和键盘繁杂操作的同时，提高语音操作的安全性。
          </p>
        </div>
      </div>
      <corp @jump="toConsole">
        <template> 提交信息，我们会尽快与您联系</template>
      </corp>
    </section>
    <!-- <section class="section section-5">
      <div class="section-title">
        合作咨询
        <p>提交信息，我们会尽快与您联系</p>
      </div>
      <div class="section-item">
        <aiui-button hasTop @click.native="toConsole">申请合作</aiui-button>
      </div>
    </section> -->
    <!-- <div class="contact-wrap">
      <div class="title">合作咨询</div>
      <p class="desc">提交信息，我们会尽快与您联系</p>

      <aiui-button>
        <a hasTop @click="toConsole">申请合作</a>
      </aiui-button>
    </div> -->
  </div>
</template>

<script>
import corp from '@P/aiui/solution-aiui/components/corp.vue'

export default {
  layout: 'aiuiHome',
  data() {
    return {
      app_scenario: [
        {
          alt: '指挥调度系统',
          src: require('../../../../assets/images/solution/digital-screen-lamp/digital-section-1.png'),
        },
        {
          alt: '监控中心',
          src: require('../../../../assets/images/solution/digital-screen-lamp/digital-section-2.png'),
        },
        {
          alt: '多功能展厅',
          src: require('../../../../assets/images/solution/digital-screen-lamp/digital-section-3.png'),
        },
      ],
      pg_advantage: [
        {
          title: '大屏语音助手',
          text: '提供配套的语音助手软件，启动即用，<br/>识别结果实时上屏',
          m_text: '提供配套的语音助手软件，启动即用，识别结果实时上屏',
          src: require('../../../../assets/images/solution/digital-screen-lamp/digital-section-5.png'),
          icon: require('../../../../assets/images/solution/digital-screen-lamp/digital-section-5-icon.png'),
        },
        {
          title: '近场交互准确度高',
          text: '近场通过演示器收音，识别准确性高，同时演示器<br/>自带激光飞鼠键，可辅助演示',
          m_text:
            '近场通过演示器收音，识别准确性高，同时演示器自带激光飞鼠键，可辅助演示',
          src: require('../../../../assets/images/solution/digital-screen-lamp/digital-section-6.png'),
          icon: require('../../../../assets/images/solution/digital-screen-lamp/digital-section-6-icon.png'),
        },
        {
          title: '远场交互抗噪性强',
          text: '人脸、唇形、语音等多模技术融合，实现人声噪音抑制，<br/>5米内可走动交互',
          m_text:
            '人脸、唇形、语音等多模技术融合，实现人声噪音抑制，5米内可走动交互',
          src: require('../../../../assets/images/solution/digital-screen-lamp/digital-section-7.png'),
          icon: require('../../../../assets/images/solution/digital-screen-lamp/digital-section-7-icon.png'),
        },
        {
          title: '企业专属的语义模型',
          text: '根据业务场景训练专属的语义模型，并提供语义配置平台',
          src: require('../../../../assets/images/solution/digital-screen-lamp/digital-section-8.png'),
          icon: require('../../../../assets/images/solution/digital-screen-lamp/digital-section-8-icon.png'),
        },
        {
          title: '支持私有化部署',
          text: '语音语义全链路服务支持私有化，业务数据不出外网更安全',
          src: require('../../../../assets/images/solution/digital-screen-lamp/digital-section-9.png'),
          icon: require('../../../../assets/images/solution/digital-screen-lamp/digital-section-9-icon.png'),
        },
      ],
    }
  },
  methods: {
    toConsole() {
      // 判断是否有百度SEM营销参数，有的话，提交表单页需要带上
      const search = window.location.search
      if (search) {
        window.open(`/solution/apply/3${search}`)
      } else {
        window.open('/solution/apply/3')
      }
    },
  },
  components: { corp },
}
</script>

<style lang="scss" scoped>
@import '../../../../assets/scss/screen-and-lamp.scss';
@media screen and (min-width: 751px) {
  .main-content {
    &-banner {
      background: url(~@A/images/solution/screen/img_bg_voicebig_banner.png)
        center no-repeat;
      background-size: cover;
      height: 500px;
      overflow: hidden;
      width: 100%;
      .banner-text {
        max-width: 1200px;
        color: #fff;
        height: 100%;
        margin: auto;
        &-button {
          font-size: 16px;
          text-align: center;
          font-weight: 400;
          width: 140px;
          height: 40px;
          line-height: 40px;
          border: none;
          color: #fff;
          cursor: pointer;
          transition: 0.6s;
          background: linear-gradient(0deg, #00b4ff 0%, #2371ff 100%);
          border-radius: 20px;
        }
        h2 {
          color: #fff;
          padding-top: 148px;
          margin-bottom: 29px;
          font-size: 48px;
          font-weight: 500;
          line-height: 48px;
        }
        p {
          font-size: 18px;
          margin-bottom: 74px;
        }

        .banner-text-content {
          width: 570px;
          font-size: 16px;
          font-family: SourceHanSansSC-Regular, SourceHanSansSC;
          font-weight: 400;
          color: rgba(255, 255, 255, 0.86);
          line-height: 30px;
        }
      }
    }
  }
  .section-title {
    font-size: 34px;
    font-weight: bold;
    color: #333;
    margin-top: 0 !important;
    margin-bottom: 30px !important;
  }
  .section-sub-title {
    font-size: 16px;
    font-weight: 400;
    text-align: center;
    color: #666666;
    margin-bottom: 60px !important;
  }
  .app-text {
    color: #666;
  }
  .section-3 {
    ul {
      li:nth-child(2),
      li:nth-child(4) {
        .section-item-text {
          padding-left: 10%;
        }
      }
    }
  }
}
@media screen and (max-width: 750px) {
  .main-content {
    &-banner {
      background: url('../../../../assets/images/solution/digital-screen-lamp/digital-banner-m.jpg')
        center no-repeat;
      background-size: cover;
    }
  }
}

.contact-wrap {
  // padding-top: 100px;
  height: 400px;
  text-align: center;
  .title {
    margin-bottom: 16px;
    font-size: 34px;
    color: #333;
    font-weight: bold;
  }
  .desc {
    font-size: 16px;
    color: #666;
    margin-bottom: 50px;
  }
  .apply-btn {
    margin: 60px auto 0;
    background: #1784e9;
    &:hover {
      color: #fff;
    }
  }
}
</style>
