<template>
  <div class="lib-solution main-content">
    <section class="main-content-banner">
      <div class="banner-text">
        <h2>智慧零售解决方案</h2>
        <p class="banner-text-content">
          围绕商超零售场景，提供主动迎宾、语音交互、智能导航、个性化推荐整体解决方案，降低商家经营成本、提高运营智能化水平和顾客消费体验。<br />
        </p>
        <div class="banner-text-button" @click="toConsole">合作咨询</div>
      </div>
    </section>
    <div class="section section1">
      <div class="section-title">应用场景</div>
      <div class="section-content">
        <div class="content-item">购物中心</div>
        <div class="content-item">大型超市</div>
        <div class="content-item">连锁品牌店</div>
        <div class="content-item">独立门店</div>
      </div>
    </div>
    <div class="section section2">
      <div class="section-title">产品方案</div>
      <div class="section-content section-content1">
        <div class="content-item">
          <div class="content-item-left text-content">
            <div class="title">智慧迎宾</div>
            <div class="content">
              <p><span></span>&nbsp;自动唤醒、主动交互</p>
              <p><span></span>&nbsp;路径规划、智能导航</p>
              <p><span></span>&nbsp;促销宣传、揽客引流</p>
            </div>
          </div>
          <div class="content-item-right"></div>
        </div>
      </div>
      <div class="section-content section-content2">
        <div class="content-item">
          <div class="content-item-left"></div>
          <div class="content-item-right text-content">
            <div class="title">智能导购</div>
            <div class="content">
              <p><span></span>&nbsp;语音检索商品</p>
              <p><span></span>&nbsp;参数详情问询</p>
              <p><span></span>&nbsp;商品智能推荐</p>
              <p><span></span>&nbsp;产品对比介绍</p>
            </div>
          </div>
        </div>
      </div>
      <div class="section-content section-content3">
        <div class="content-item">
          <div class="content-item-left text-content">
            <div class="title">营销助理</div>
            <div class="content">
              <p><span></span>&nbsp;客流统计</p>
              <p><span></span>&nbsp;画像生成</p>
              <p><span></span>&nbsp;客群分类</p>
              <p><span></span>&nbsp;销售分析</p>
            </div>
          </div>
          <div class="content-item-right"></div>
        </div>
      </div>
      <div class="section-content section-content4">
        <div class="content-item">
          <div class="content-item-left"></div>
          <div class="content-item-right text-content">
            <div class="title">销售培训</div>
            <div class="content">
              <p><span></span>&nbsp;企业知识题库智能生成</p>
              <p><span></span>&nbsp;真实场景模拟互动演练</p>
              <p><span></span>&nbsp;多维度综合评价体系</p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="section section3">
      <div class="section-title">方案优势</div>
      <div class="methods-content">
        <div class="methods-item">
          <h3>高噪环境下的精准收音</h3>
          <p>
            结合人脸识别、唇形检测和麦克风阵列技术，实现主动交互、正前方定向收音、复杂环境背景噪声抑制和说话人语音精准提取。
          </p>
          <div class="pic"></div>
        </div>
        <div class="methods-item">
          <h3>亲切自然的交互过程</h3>
          <p>
            人类自然语言准确理解、跨领域多轮连续对话，多情感多风格回复，交互体验更加亲切有趣。
          </p>
          <div class="pic"></div>
        </div>

        <div class="methods-item">
          <h3>虚拟人驱动</h3>
          <p>
            丰富的2D/3D形象，根据不同的对话情境生成特定的虚拟人声音与动效，形象更加生动自然。
          </p>
          <div class="pic"></div>
        </div>
      </div>
    </div>
    <div class="last">
      <div class="banner-text">
        <h2>立即联系您的专属顾问</h2>
        <p class="banner-text-content">
          免费咨询专属顾问 为您量身定制产品推荐方案<br />
        </p>
        <div class="banner-text-button" @click="toConsole">合作咨询</div>
      </div>
    </div>
  </div>
</template>

<script>
import corp from '@P/aiui/solution-aiui/components/corp.vue'

export default {
  layout: 'aiuiHome',
  methods: {
    toConsole() {
      // 判断是否有百度SEM营销参数，有的话，提交表单页需要带上
      const search = window.location.search
      if (search) {
        window.open(`/solution/apply/28${search}`)
      } else {
        window.open('/solution/apply/28')
      }
    },
  },
  components: { corp },
}
</script>

<style lang="scss" scoped>
.main-content {
  &-banner {
    background: url(~@A/images/solution/smart-retail/banner.png) center
      no-repeat;
    background-size: cover;
    height: 500px;
    overflow: hidden;
    width: 100%;

    .banner-text {
      max-width: 1200px;
      color: #fff;
      height: 100%;
      margin: auto;

      &-button {
        font-size: 16px;
        text-align: center;
        font-weight: 400;
        width: 140px;
        height: 40px;
        line-height: 40px;
        color: #fff;
        cursor: pointer;
        background: $primary;
        border-radius: 4px;
      }

      h2 {
        font-family: PingFang SC, PingFang SC-Semibold;
        color: #181818;
        padding-top: 148px;
        margin-bottom: 24px;
        font-size: 44px;
        font-weight: 600;
        line-height: 44px;
      }

      p {
        font-size: 16px;
        margin-bottom: 24px;
      }

      .banner-text-content {
        width: 705px;
        font-size: 16px;
        font-family: SourceHanSansSC-Regular, SourceHanSansSC;
        font-weight: 400;
        color: #444444;
        line-height: 25px;
      }
    }
  }

  .section-title {
    text-align: center;
    font-size: 32px;
    font-weight: bold;
    color: #262626;
    margin-top: 0 !important;
    margin-bottom: 30px !important;
  }

  .section-sub-title {
    font-size: 16px;
    font-weight: 400;
    text-align: center;
    color: #666666;
    margin-bottom: 60px !important;
  }

  .app-text {
    color: #666;
  }

  .section1 {
    padding: 70px 0;
    text-align: center;

    .section-title {
      font-size: 32px;
      font-weight: bold;
      color: #262626;
    }

    .section-content {
      margin: 50px auto 0;
      width: 1200px;
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
    }

    .content-item {
      position: relative;
      flex: 0 0 auto;
      padding-top: 30px;
      width: 285px;
      height: 320px;
      text-align: center;
      font-size: 18px;
      padding-left: 30px;
      padding-right: 30px;
      color: #212122;
      background: url('../../../assets/images/solution/smart-retail/app_1.png')
        center no-repeat;
      background-size: cover;
      overflow: hidden;

      &:last-child {
        background: url('../../../assets/images/solution/smart-retail/app_4.png')
          center no-repeat;
        background-size: cover;
      }

      &:nth-child(3) {
        background: url('../../../assets/images/solution/smart-retail/app_3.png')
          center no-repeat;
        background-size: cover;
      }

      &:nth-child(2) {
        background: url('../../../assets/images/solution/smart-retail/app_2.png')
          center no-repeat;
        background-size: cover;
      }
    }
  }

  .section2 {
    overflow: hidden;
    width: 100%;

    .section-title {
      margin-bottom: 55px !important;
    }

    .section-content {
      width: 100%;
      height: 460px;
      background: #ebf3fe;
      margin: 0 auto;

      .content-item {
        width: 1200px;
        margin: 0 auto;
        display: flex;

        .text-content {
          .title {
            margin-left: 8px;
          }

          .content p {
            display: flex;
            align-items: center;

            span {
              display: inline-block;
              width: 4px;
              height: 4px;
              background: $primary;
              border-radius: 50%;
            }
          }
        }
      }
    }

    .section-content1 {
      .content-item {
        .content-item-left {
          width: 515px;
          height: 353px;
          background: url(~@A/images/solution/smart-retail/product_bg.png)
            center no-repeat;
          background-size: cover;
          margin-top: 71px;
          margin-right: 10px;
          padding: 64px 0 0 132px;

          .title {
            font-size: 20px;
            font-weight: 600;
            color: #262626;
            margin-bottom: 46px;
          }

          .content {
            p {
              font-size: 16px;
              margin-bottom: 24px;
              color: #707070;
            }
          }
        }

        .content-item-right {
          width: 696px;
          height: 429px;
          margin-top: 22px;
          background: url(~@A/images/solution/smart-retail/product_1.png) center
            no-repeat;
          background-size: cover;
        }
      }
    }

    .section-content2 {
      background: #f4f9ff;

      .content-item-right {
        width: 470px;
        height: 308px;
        background: url(~@A/images/solution/smart-retail/product_bg.png) center
          no-repeat;
        background-size: cover;
        margin-top: 96px;
        padding: 49px 0 0 152px;
        margin-left: -42px;

        .title {
          font-size: 20px;
          font-weight: 600;
          color: #262626;
          margin-bottom: 36px;
        }

        .content {
          p {
            font-size: 16px;
            margin-bottom: 20px;
            color: #707070;
          }
        }
      }

      .content-item-left {
        width: 757px;
        height: 410px;
        margin-top: 44px;
        background: url(~@A/images/solution/smart-retail/product_2.png) center
          no-repeat;
        background-size: cover;
      }
    }

    .section-content3 {
      background: #ebf3fe;

      .content-item-left {
        width: 470px;
        height: 308px;
        background: url(~@A/images/solution/smart-retail/product_bg.png) center
          no-repeat;
        background-size: cover;
        margin-top: 96px;
        padding: 49px 0 0 110px;

        .title {
          font-size: 20px;
          font-weight: 600;
          color: #262626;
          margin-bottom: 36px;
        }

        .content {
          p {
            font-size: 16px;
            margin-bottom: 20px;
            color: #707070;
          }
        }
      }

      .content-item-right {
        width: 786px;
        height: 459px;
        margin-left: -42px;
        background: url(~@A/images/solution/smart-retail/product_3.png) center
          no-repeat;
        background-size: cover;
      }
    }

    .section-content4 {
      background: #f4f9ff;

      .content-item-right {
        width: 470px;
        height: 308px;
        background: url(~@A/images/solution/smart-retail/product_bg.png) center
          no-repeat;
        background-size: cover;
        margin-top: 96px;
        padding: 49px 0 0 152px;
        margin-left: 15px;

        .title {
          font-size: 20px;
          font-weight: 600;
          color: #262626;
          margin-bottom: 36px;
        }

        .content {
          p {
            font-size: 16px;
            margin-bottom: 20px;
            color: #707070;
          }
        }
      }

      .content-item-left {
        width: 718px;
        height: 433px;
        margin-top: 26px;
        background: url(~@A/images/solution/smart-retail/product_4.png) center
          no-repeat;
        background-size: cover;
      }
    }
  }

  .section3 {
    margin: 70px auto;
    width: 1200px;

    .section-title {
      font-size: 32px;
      font-weight: bold;
      color: #262626;
    }

    .methods-content {
      background-color: #fff;
      border: 2px solid #ffffff;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      margin: 50px auto 0;
      width: 1200px;
      text-align: left;

      .methods-item {
        background: linear-gradient(187deg, #f5f7f9 0%, #ffffff 78%);
        border: 2px solid #ffffff;
        box-shadow: -7px 0px 27.55px 0.35px rgba(188, 198, 216, 0.3);
        position: relative;
        flex: 0 0 auto;
        width: 380px;
        height: 170px;
        padding-left: 30px;
        padding-right: 30px;
        padding-top: 30px;

        h3 {
          font-weight: 600;
          font-size: 18px;
          line-height: 56px;
          color: #262626;
        }

        p {
          width: 322px;
          font-size: 12px;
          color: #666666;
          line-height: 22px;
        }

        .pic {
          position: absolute;
          top: -40px;
          right: 10px;
          width: 130px;
          height: 130px;
          background: url('../../../assets/images/solution/smart-retail/programme_1.png')
            center no-repeat;
          background-size: cover;
        }

        &:nth-child(2) {
          .pic {
            background: url('../../../assets/images/solution/smart-retail/programme_2.png')
              center no-repeat;
            background-size: cover;
          }
        }

        &:nth-child(3) {
          .pic {
            background: url('../../../assets/images/solution/smart-retail/programme_3.png')
              center no-repeat;
            background-size: cover;
          }
        }
      }
    }
  }

  .last {
    background: url(~@A/images/solution/reflection/img_guwenBG.png) center
      no-repeat;
    background-size: cover;
    height: 300px;
    overflow: hidden;
    width: 100%;

    .banner-text {
      max-width: 1200px;
      color: #fff;
      height: 100%;
      margin: auto;

      &-button {
        font-size: 16px;
        text-align: center;
        font-weight: 400;
        width: 140px;
        height: 40px;
        line-height: 40px;
        border-radius: 4px;
        color: #fff;
        cursor: pointer;
        background: $primary;
      }

      h2 {
        color: #181818;
        padding-top: 50px;
        margin-bottom: 20px;
        font-size: 36px;
        font-weight: 400;
        line-height: 48px;
      }

      p {
        font-size: 18px;
        margin-bottom: 50px;
      }

      .banner-text-content {
        width: 570px;
        font-size: 16px;
        font-family: SourceHanSansSC-Regular, SourceHanSansSC;
        font-weight: 400;
        color: #444444;
        line-height: 30px;
      }
    }
  }
}
</style>
