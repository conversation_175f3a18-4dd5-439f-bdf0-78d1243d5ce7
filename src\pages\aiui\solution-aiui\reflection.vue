<template>
  <div class="lib-solution main-content">
    <section class="main-content-banner">
      <div class="banner-text">
        <h2>智能投影解决方案</h2>
        <p class="banner-text-content">
          家庭领域专属降噪和识别引擎,提供OS、语音助手、影视内容等多种合作方案,打造极致的语音搜索和播放控制的交互体验。<br />
        </p>

        <div class="banner-text-button" @click="toConsole">合作咨询</div>
      </div>
    </section>

    <div class="section section1">
      <div class="section-title">应用场景</div>
      <p class="section-sub-title">适用于家庭娱乐类大屏智能硬件</p>
      <div class="section-content">
        <div class="content-item">
          大屏电视
          <div class="word">
            <p>
              AIUI赋能电视语音助手,采用标准线性四麦与蓝牙遥控器、支持远场与近场收音、收音最远距离可达5m。
            </p>
          </div>
        </div>
        <div class="content-item">
          激光电视
          <div class="word">
            <p>
              AIUI赋能电视语音助手,采用标准线性四麦与蓝牙遥控器、支持远场与近场收音、收音最远距离可达5m。
            </p>
          </div>
        </div>
        <div class="content-item">
          长焦投影仪
          <div class="word">
            <p>
              AIUI赋能投影仪语音助手,采用标准环形四麦与蓝牙遥控器、支持远场与近场收音、收音最远距离可达5m。
            </p>
          </div>
        </div>
        <div class="content-item">
          微投
          <div class="word">
            <p>
              AIUI赋能投影仪语音助手,采用标准环形四麦与蓝牙遥控器、支持远场与近场收音、收音最远距离可达5m。
            </p>
          </div>
        </div>
      </div>
    </div>

    <div class="section section2">
      <div class="section-title">远近场收音方案</div>
      <p class="section-sub-title">遥控器、麦克风阵列语音交互无缝切换</p>

      <div class="section-content2">
        <div class="content-item2">
          <h3>语音遥控器</h3>
          <p>可提供蓝牙模组或成品硬件，支持纯软合作</p>
          <div class="pic"></div>
        </div>

        <div class="content-item2 right">
          <h3>麦克风阵列</h3>
          <p>家庭场景降噪模型，音频更干净 支持3-5米语音交互</p>
          <div class="pic"></div>
        </div>
      </div>
    </div>

    <div class="section section3">
      <div class="methods">
        <h2>方案优势</h2>
        <p>深耕家庭娱乐交互场景,助力观影体验升级</p>
      </div>

      <div class="methods-content">
        <div class="methods-item">
          <h3>场景专属语音模型</h3>
          <p>
            针对家庭影院场景定制降噪模型,音频更纯净。电视投影领域交互说法训练,语音识别更准确
          </p>
          <div class="pic"></div>
        </div>
        <div class="methods-item">
          <h3>领域语义技能打磨</h3>
          <p>
            影音等常用技能效果打磨，交互成功率更高。成人儿童闲聊分离,让内容更健康。
          </p>
          <div class="pic"></div>
        </div>

        <div class="methods-item">
          <h3>更贴心的个性化交互</h3>
          <p>支持声纹聚类，依据交互人身份给出不同内容。</p>
          <div class="pic"></div>
        </div>
        <div style="width: 100%"></div>

        <div class="methods-item">
          <h3>屏幕所见即可说</h3>
          <p>屏幕展示内容，动态增强识别和语义效果，语音交互更直观准确。</p>
          <div class="pic"></div>
        </div>

        <div class="methods-item">
          <h3>语义VAD智能判停</h3>
          <p>
            根据语句完整性动态判断是否结束录音。完美解决用户电影搜索等交互时思考导致的误截断问题。
          </p>
          <div class="pic"></div>
        </div>

        <div class="methods-item">
          <h3>实时翻译字幕</h3>
          <p>支持中英日韩片源语种实时翻译字幕，听不懂外语，也能轻松看新片。</p>
          <div class="pic"></div>
        </div>
      </div>
    </div>

    <div class="section section4">
      <h2>接入方式</h2>
      <p>接入灵活,提供纯软接入和RTOS硬件模组及小程序源码,打通云端内容服务</p>
      <div class="three">
        <div class="item">
          <h3>OS (使用OS界面)</h3>
          <p>提供已适配语音交互的OS内容完善,简单适配即可使用。</p>
        </div>

        <div class="item">
          <h3>语音助手SDK</h3>
          <p>语音助手SDK可大度提供,提供UI和交互设计参考。</p>
        </div>

        <div class="item">
          <h3>内 容</h3>
          <p>输出投影电视常用APK及应用市场,APK打开后支持语音控制。</p>
        </div>
      </div>
    </div>

    <div class="last">
      <div class="banner-text">
        <h2>立即联系您的专属顾问</h2>
        <p class="banner-text-content">
          免费咨询专属顾问 为您量身定制产品推荐方案<br />
        </p>
        <div class="banner-text-button" @click="toConsole">合作咨询</div>
      </div>
    </div>
  </div>
</template>

<script>
import utils from '../../../assets/lib/utils.js'
import bus from '../../../assets/lib/bus.js'
import corp from '@P/aiui/solution-aiui/components/corp.vue'

export default {
  layout: 'aiuiHome',
  methods: {
    // toConsole() {
    //   if (utils.isMobile()) {
    //     bus.$emit('osMessageShow')
    //   } else {
    //     location.href = '/console'
    //   }
    // },
    toConsole() {
      // 判断是否有百度SEM营销参数，有的话，提交表单页需要带上
      const search = window.location.search
      if (search) {
        window.open(`/solution/apply/27${search}`)
      } else {
        window.open('/solution/apply/27')
      }
    },
  },
  components: { corp },
}
</script>

<style lang="scss" scoped>
.main-content {
  &-banner {
    background: url(~@A/images/solution/reflection/img_banner_BG.png) center
      no-repeat;
    background-size: cover;
    height: 500px;
    overflow: hidden;
    width: 100%;
    .banner-text {
      max-width: 1200px;
      color: #fff;
      height: 100%;
      margin: auto;
      &-button {
        font-size: 16px;
        text-align: center;
        font-weight: 400;
        width: 140px;
        height: 40px;
        line-height: 40px;
        // border: 1px solid #fff;
        // border-radius: 40px;
        color: #fff;
        cursor: pointer;
        background: $primary;
        border-radius: 4px;
        // background: linear-gradient(0deg, #00b4ff 0%, #2371ff 100%);
        // transition: 0.6s;
      }
      h2 {
        font-family: PingFang SC, PingFang SC-Semibold;
        color: #181818;
        padding-top: 148px;
        margin-bottom: 29px;
        font-size: 48px;
        font-weight: 600;
        line-height: 48px;
      }
      p {
        font-size: 16px;
        margin-bottom: 74px;
      }

      .banner-text-content {
        width: 700px;
        font-size: 16px;
        font-family: SourceHanSansSC-Regular, SourceHanSansSC;
        font-weight: 400;
        // color: rgba(255, 255, 255, 0.86);
        color: #444444;
        line-height: 30px;
      }
    }
  }

  .section-title {
    text-align: center;
    font-size: 34px;
    font-weight: bold;
    color: #333;
    margin-top: 0 !important;
    margin-bottom: 30px !important;
  }
  .section-sub-title {
    font-size: 16px;
    font-weight: 400;
    text-align: center;
    color: #666666;
    margin-bottom: 60px !important;
  }
  .app-text {
    color: #666;
  }

  .section {
    padding: 30px 0 84px;
    text-align: center;
    .section-title {
      margin-bottom: 40px;
      font-size: 40px;
      font-weight: 500;
    }
    .section-content {
      margin: 50px auto 0;
      width: 1200px;
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
    }
    .content-item {
      flex: 0 0 auto;
    }
  }
  .content-item {
    position: relative;
    flex: 0 0 auto;
    padding-top: 30px;
    width: 285px;
    height: 290px;
    text-align: center;
    font-size: 18px;
    padding-left: 30px;
    padding-right: 20px;
    color: #fff;
    background: url('../../../assets/images/solution/reflection/img_screen.png')
      center no-repeat;
    background-size: cover;
    overflow: hidden;
    .word {
      text-align: left;
      width: 230px;
      margin-top: 20px;
      font-size: 14px;
      line-height: 24px;
      visibility: hidden;
    }
    &:hover {
      background: url('../../../assets/images/solution/reflection/img_mask1.png')
        center no-repeat;
      .word {
        visibility: visible;
      }
    }
    &:last-child {
      background: url('../../../assets/images/solution/reflection/img_weitou.png')
        center no-repeat;
      background-size: cover;
      &:hover {
        background: url('../../../assets/images/solution/reflection/img_mask4.png')
          center no-repeat;
      }
    }
    &:nth-child(3) {
      background: url('../../../assets/images/solution/reflection/img_changjiao.png')
        center no-repeat;
      background-size: cover;
      &:hover {
        background: url('../../../assets/images/solution/reflection/img_mask3.png')
          center no-repeat;
      }
    }
    &:nth-child(2) {
      background: url('../../../assets/images/solution/reflection/img_jiguang.png')
        center no-repeat;
      background-size: cover;
      &:hover {
        background: url('../../../assets/images/solution/reflection/img_mask2.png')
          center no-repeat;
      }
    }
  }
  .section2 {
    background: url(~@A/images/solution/reflection/img_fanganBG.png) center
      no-repeat;
    background-size: cover;
    height: 600px;
    overflow: hidden;
    width: 100%;
    .section-content2 {
      display: flex;
      justify-content: center;
      .content-item2 {
        position: relative;
        background: url(~@A/images/solution/reflection/img_juxing.png) center
          no-repeat;
        width: 600px;
        height: 251px;
        // margin-left: 150px;
        // border: 1px solid #f1f1f1;
        box-shadow: -4px 0px 14px 4px rgba(188, 198, 216, 0.3);
        h3 {
          position: absolute;
          left: 340px;
          top: 80px;
          color: #262626;
          font-size: 18px;
          text-align: left;
        }
        p {
          position: absolute;
          left: 340px;
          top: 120px;
          width: 200px;
          color: #666666;
          font-size: 12px;
          line-height: 34px;
          text-align: left;
        }
        .pic {
          position: absolute;
          left: 0;
          top: -30px;
          background: url(~@A/images/solution/reflection/img_yaokongqi.png)
            center no-repeat;
          width: 300px;
          height: 266px;
          margin-left: 25px;
          // margin-bottom: 200px;
        }
        &:nth-child(2) {
          .pic {
            background: url(~@A/images/solution/reflection/img_microphone.png)
              center no-repeat;
          }
        }
      }
      .right {
        margin-left: 40px;
      }
    }
  }

  .section3 {
    margin: auto;
    width: 1200px;
    .methods {
      h2 {
        font-weight: 600;
        font-size: 32px;
        color: #262626;
        line-height: 60px;
      }
      p {
        font-weight: 400;
        font-size: 16px;
        line-height: 40px;
      }
    }
    .methods-content {
      // background: url('../../../assets/images/solution/reflection/img_juxing.png')
      //   center no-repeat;
      // background-size: cover;
      background-color: #fff;
      border: 2px solid #ffffff;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      margin: 50px auto 0;
      width: 1200px;
      text-align: left;
      .methods-item {
        background: linear-gradient(187deg, #f5f7f9 0%, #ffffff 78%);
        border: 2px solid #ffffff;
        box-shadow: -7px 0px 27.55px 0.35px rgba(188, 198, 216, 0.3);
        position: relative;
        flex: 0 0 auto;
        width: 380px;
        height: 170px;
        margin-bottom: 50px;
        padding-left: 20px;
        padding-top: 30px;
        h3 {
          font-weight: 500;
          font-size: 18px;
          line-height: 56px;
          color: #262626;
        }
        p {
          width: 322px;
          font-size: 12px;
          color: #666666;
        }
        .pic {
          position: absolute;
          top: -40px;
          left: 230px;
          width: 100px;
          height: 100px;
          background: url('../../../assets/images/solution/reflection/1.png')
            center no-repeat;
          background-size: cover;
        }
        &:nth-child(2) {
          .pic {
            background: url('../../../assets/images/solution/reflection/2.png')
              center no-repeat;
            background-size: cover;
          }
        }
        &:nth-child(3) {
          .pic {
            background: url('../../../assets/images/solution/reflection/3.png')
              center no-repeat;
            background-size: cover;
          }
        }
        &:nth-child(5) {
          .pic {
            background: url('../../../assets/images/solution/reflection/4.png')
              center no-repeat;
            background-size: cover;
          }
        }
        &:nth-child(6) {
          .pic {
            background: url('../../../assets/images/solution/reflection/5.png')
              center no-repeat;
            background-size: cover;
          }
        }
        &:last-child {
          .pic {
            background: url('../../../assets/images/solution/reflection/6.png')
              center no-repeat;
            background-size: cover;
          }
        }
      }
    }
  }

  .section4 {
    position: relative;
    background: url(~@A/images/solution/reflection/img_jieruBG1.png) center
      no-repeat;
    background-size: cover;
    height: 305px;
    margin: 0 auto;
    overflow: hidden;
    width: 100%;
    h2 {
      font-size: 30px;
      line-height: 60px;
      color: #262626;
    }
    p {
      font-size: 16px;
      line-height: 40px;
      color: #444444;
    }
    .three {
      margin: auto;
      margin-top: 20px;
      width: 1200px;
      display: flex;
      justify-content: center;
      // margin-left: 160px;
      // margin-top: 30px;
    }
    .item {
      width: 340px;
      height: 120px;
      opacity: 0.8;
      background: #f7faff;
      border-radius: 4px;
      box-shadow: -6px 0px 16.2px 0.6px rgba(173, 197, 232, 0.28);
      padding-top: 10px;
      padding-left: 40px;
      margin-bottom: 30px;
      h3 {
        margin-right: 30px;
        text-align: center;
        color: #262626;
        font-size: 18px;
        margin-bottom: 10px;
      }
      p {
        font-size: 14px;
        line-height: 24px;
        width: 240px;
        color: #666666;
        text-align: left;
      }
      &:nth-child(2) {
        margin-left: 100px;
        margin-right: 100px;
      }
    }
  }

  .last {
    background: url(~@A/images/solution/reflection/img_guwenBG.png) center
      no-repeat;
    background-size: cover;
    height: 300px;
    overflow: hidden;
    width: 100%;
    .banner-text {
      max-width: 1200px;
      color: #fff;
      height: 100%;
      margin: auto;
      // margin-left: 35px;
      &-button {
        font-size: 16px;
        text-align: center;
        font-weight: 400;
        width: 140px;
        height: 40px;
        line-height: 40px;
        // border: 1px solid #fff;
        border-radius: 4px;
        color: #fff;
        cursor: pointer;
        background: $primary;
        // background: linear-gradient(0deg, #00b4ff 0%, #2371ff 100%);
        // transition: 0.6s;
      }
      h2 {
        color: #181818;
        padding-top: 50px;
        margin-bottom: 20px;
        font-size: 36px;
        font-weight: 400;
        line-height: 48px;
      }
      p {
        font-size: 18px;
        margin-bottom: 50px;
      }

      .banner-text-content {
        width: 570px;
        font-size: 16px;
        font-family: SourceHanSansSC-Regular, SourceHanSansSC;
        font-weight: 400;
        // color: rgba(255, 255, 255, 0.86);
        color: #444444;
        line-height: 30px;
      }
    }
  }
}
</style>
