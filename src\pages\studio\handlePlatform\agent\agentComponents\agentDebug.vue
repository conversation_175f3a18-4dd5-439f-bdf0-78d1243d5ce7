<template>
  <div class="debug-wrap">
    <div class="debug-header">
      <div class="collapse-btn" @click="closeRightTest">
        <i class="iconfont icon-zhankai"></i>
        <span>模拟测试</span>
      </div>
    </div>
    <div class="clear" @click="cleanHistory">
      <i class="iconfont icon-shanchu"></i>
    </div>

    <div class="debug-dialog" ref="list">
      <div
        :class="['dialog dialog-' + item.type]"
        v-for="(item, index) in dialogList"
        :key="index"
      >
        <template v-if="item.type === 'answer'">
          <div class="msg-answer-item">
            <i class="robot-avatar"></i>
            <div class="ib message">
              <template v-if="item.data.answer">
                <div style="padding: 5px 15px">
                  <vue-markdown
                    :source="item.data[item.type]"
                    @rendered="onRendered"
                    :html="false"
                    ref="markdownRef"
                  ></vue-markdown>
                  <span class="inputing-cursor" v-if="submitting"></span>
                </div>
              </template>
              <template v-if="item.data.loading">
                <div style="padding: 5px 15px">
                  <span>正在处理</span>&nbsp;<inputing></inputing>
                </div>
              </template>
            </div>
          </div>
        </template>
        <div class="ib message" v-else>
          {{ item.data[item.type] }}
        </div>
      </div>
    </div>

    <div class="send-wrap">
      <el-input
        class="debug-input"
        :maxlength="120"
        v-model="question"
        size="medium"
        @keyup.enter.native="experience"
        @keyup.up.native="preQuestion"
        @keyup.down.native="nextQuestion"
        placeholder="输入文本，回车体验"
      ></el-input>
      <span
        :class="['debug-send', { 'debug-send-active': question }]"
        @click="experience"
      >
        <svg-icon iconClass="send" />&nbsp;发送</span
      >
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import VueMarkdown from 'vue-markdown'
import Inputing from '@P/aiui/model-experience/Inputing.vue'
import '@/assets/lib/prism/prism.js'
import '@/assets/lib/prism/prism.css'

export default {
  name: 'app-debug45',
  components: { VueMarkdown, Inputing },
  props: {
    debugType: {
      default: 'app',
      type: String,
    },
  },
  data() {
    return {
      submitting: false,
      question: '',
      questionList: [],
      questionIndex: 0, //记录按上下键问题开始位置
      uid: this.$utils.experienceUid(),
      dialogList: [
        {
          type: 'answer',
          data: {
            answer: '你好，我是智能的小飞～',
          },
        },
        // {
        //   type: 'question',
        //   data: {
        //     question:
        //       '合肥的天气',
        //   },
        // },
      ],
      //  chatList: [
      //   {
      //     people: 'ai',
      //     con: '你好，我是智能的小飞~',
      //     type: 'text',
      //     preset: true,
      //   },
      // ],
    }
  },

  computed: {
    ...mapGetters({
      rightTestOpen: 'studioSkill/rightTestOpen',
      agentPlatformInfo: 'studioSkill/agentPlatformInfo',
    }),
  },
  watch: {
    submitting(val, oldVal) {
      if (!val && oldVal) {
        this.$nextTick(() => {
          Array.from(
            document.getElementsByClassName('inputing-cursor')
          ).forEach((item) => {
            item.remove()
          })
        })
      }
    },
  },
  methods: {
    onRendered() {
      this.$nextTick(() => {
        window.Prism && window.Prism.highlightAll()
      })
    },
    preQuestion() {
      if (this.questionIndex > 0) {
        this.questionIndex--
        this.question = this.questionList[this.questionIndex]
      } else {
        this.question = ''
        this.questionIndex = -1
      }
    },
    nextQuestion() {
      if (this.questionIndex < this.questionList.length) {
        this.questionIndex++
        this.question = this.questionList[this.questionIndex]
      } else {
        this.question = ''
        this.questionIndex = this.questionList.length
      }
    },
    addDialog(type, data) {
      this.dialogList.push({
        type: type,
        data: data,
        httpCode: null,
      })

      this.scrollToBottom()
    },

    scrollToBottom() {
      this.$nextTick(() => {
        if (this.$refs.list) {
          this.$refs.list.scrollTop = this.$refs.list.scrollHeight
        }
      })
    },

    removeLoading() {
      this.dialogList = this.dialogList.filter(
        (item) => !(item.type === 'answer' && item.data.loading)
      )
    },
    experience() {
      if (this.submitting) {
        return this.$message.warning('请等机器人回复完再进行提问')
      }
      if (!this.question) return
      if (!('EventSource' in window)) {
        throw new Error('浏览器不支持sse')
      }

      if (this.question && this.question.length > 500) {
        return this.$message.warning('输入文本不能超过500字符')
      }

      // 保存当前问题到历史记录，方便后续使用上下键查询
      const currentQuestion = this.question.trim()
      if (
        currentQuestion &&
        this.questionList.indexOf(currentQuestion) === -1
      ) {
        this.questionList.push(currentQuestion)
        this.questionIndex = this.questionList.length
      }

      this.addDialog('question', {
        question: currentQuestion,
      })
      if (this.eventSource) {
        this.eventSource.close()
        this.eventSource = null
      }

      // 构造动态输入参数对象
      let inputParams = {}

      // 根据platform和resourceType决定使用哪个输入参数字段名
      if (
        this.agentPlatformInfo.platform === 'maas' &&
        this.agentPlatformInfo.resourceType === 'workflow'
      ) {
        inputParams['body#AGENT_USER_INPUT'] = this.question
      } else if (
        this.agentPlatformInfo.platform === 'coze' &&
        this.agentPlatformInfo.resourceType === 'workflow'
      ) {
        inputParams[`body#${this.agentPlatformInfo.queryKey}`] = this.question
      } else {
        inputParams['body#query'] = this.question
      }

      // 将参数对象转换为 JSON 字符串并进行 URL 编码
      const inputStr = encodeURIComponent(JSON.stringify(inputParams))
      const base = this.$store?.state?.user?.baseUrl || '/aiui/web'
      // 构造完整的 URL
      let eventSourceUrl = `${base}/user/chat?version=vflowtest&expUid=${this.uid}&pluginId=${this.$route.params.agentId}&input=${inputStr}`

      this.submitting = true
      this.question = ''

      // 正在思考中
      this.addDialog('answer', {
        loading: true,
      })

      this.eventSource = new EventSource(eventSourceUrl, {
        withCredentials: true,
      })
      this.eventSource.onopen = (event) => {
        console.log('event onopen')
      }

      this.eventSource.onerror = (event) => {
        console.error('onerror', event)
        this.submitting = false
        if (event.data) {
          let data = JSON.parse(event.data)
          if (data.desc) {
            this.$message.error(data.desc)
            this.removeLoading()
          }
        }
        this.closeEventSource()
      }

      this.eventSource.addEventListener(
        'result',
        (event) => {
          try {
            const result = JSON.parse(event.data || '{}')
            console.log('返回结果=>', result)

            // 检查响应是否成功
            if (result.code === '0' && result.flag) {
              // 处理每一条信息
              const data = result.data
              this.handleMessage(data)
            } else {
              console.error('返回错误:', result.desc)
              this.submitting = false
              this.removeLoading()
              this.closeEventSource()
            }
          } catch (error) {
            console.error('解析响应数据失败:', error)
            this.submitting = false
            this.removeLoading()
            this.closeEventSource()
          }
        },
        false
      )
    },

    // 关闭EventSource连接的方法
    closeEventSource() {
      if (this.eventSource) {
        this.eventSource.close()
        this.eventSource = null
      }
    },

    handleMessage(data) {
      // 过来了一条推送数据，先过滤loading
      this.removeLoading()

      // 处理消息
      if (data.text) {
        const index = this.dialogList.findIndex(
          (item) => item.data.logId === data.logId
        )

        if (index === -1) {
          // 没找到第一次添加回复数据
          this.addDialog('answer', {
            answer: data.text,
            logId: data.logId,
          })
        } else {
          // 已经存在该logId的消息，追加文本内容
          this.dialogList = this.dialogList.map((item) => {
            if (item.type === 'answer' && item.data.logId === data.logId) {
              return {
                ...item,
                data: {
                  ...item.data,
                  answer: item.data.answer + data.text,
                },
              }
            } else {
              return item
            }
          })
          this.scrollToBottom()
        }

        this.$nextTick(() => {
          let i = this.dialogList
            .filter((item) => item.type === 'answer')
            .findIndex((item) => item.data.logId === data.logId)
          this.insertCursor(i)
        })
      } else if (data.finish && data.nodeStatus) {
        // 如果是最后一条消息但没有文本内容，但nodeStatus为true，表示处理成功
        console.log('收到最终完成消息:', data)
      }

      // 处理完成标志
      if (data.finish) {
        console.log('对话完成，总耗时:', data.allTime || data.time, 'ms')
        this.submitting = false

        // 关闭EventSource连接
        this.closeEventSource()
      }
    },

    closeRightTest() {
      this.$store.dispatch('studioSkill/setRightTestOpen', false)
    },
    cleanHistory() {
      // 关闭EventSource连接
      this.closeEventSource()
      this.dialogList = [
        {
          type: 'answer',
          data: {
            answer: '你好，我是智能的小飞~',
          },
        },
      ]
      this.uid = this.$utils.experienceUid()
      this.submitting = false
      this.$message.success('清除会话历史成功')
    },

    insertCursor(index) {
      // 获取展示内容的容器
      const parent = this.$refs.markdownRef[index]?.$el
      // console.log('markdownRef', parent)
      if (!parent) return
      // 获取最后一个子元素节点
      let lastChild = parent.lastElementChild || parent
      // 如果是pre标签，就在pre标签中找到class为hljs的元素
      if (lastChild.tagName === 'PRE') {
        lastChild = lastChild.getElementsByClassName('hljs')[0] || lastChild
      }
      // 兼容是ul标签的情况，找到OL标签内部的最后一个元素
      if (lastChild.tagName === 'OL') {
        lastChild = this.findLastElement(lastChild)
      }
      // 向最后一个子元素中插入span标签实现光标
      lastChild?.insertAdjacentHTML(
        'beforeend',
        '<span class="inputing-cursor"></span>'
      )
    },
    // 递归找到DOM下最后一个元素节点
    findLastElement(element) {
      // 如果该DOM没有子元素，则返回自身
      if (!element.children.length) {
        return element
      }
      const lastChild = element.children[element.children.length - 1]
      // 如果最后一个子元素是元素节点，则递归查找
      if (lastChild.nodeType === Node.ELEMENT_NODE) {
        return this.findLastElement(lastChild)
      }
      return element
    },
  },
}
</script>

<style lang="scss" scoped>
@import '@C/debug.scss';
</style>
<style lang="scss">
.inputing-cursor {
  position: relative;
}
.inputing-cursor::after {
  content: ' ';
  position: absolute;
  top: 0;
  right: 0;
  height: 100%;
  width: 2px;
  background-color: black;
  animation-name: blink;
  animation-duration: 1s;
  animation-iteration-count: infinite;
}

@keyframes blink {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
</style>
