import RECOGNITION_LLM_SEMANTIC_State from './RECOGNITION_LLM_SEMANTIC_State'
import RECOGNITION_LLM_SEMANTIC_SYNTHESIS_State from './RECOGNITION_LLM_SEMANTIC_SYNTHESIS_State'
import RECOGNITION_POSTPROCESS_State from './RECOGNITION_POSTPROCESS_State'
import RECOGNITION_SEMANTIC_POSTPROCESS_State from './RECOGNITION_SEMANTIC_POSTPROCESS_State'

import RECOGNITION_SEMANTIC_State from './RECOGNITION_SEMANTIC_State'
import RECOGNITION_SEMANTIC_SYNTHESIS_State from './RECOGNITION_SEMANTIC_SYNTHESIS_State'

import RECOGNITION_State from './RECOGNITION_State'
import RECOGNITION_SYNTHESIS_State from './RECOGNITION_SYNTHESIS_State'
import RECOGNITION_TRANSLATE_State from './RECOGNITION_TRANSLATE_State'
import RECOGNITION_TRANSLATE_SYNTHESIS_State from './RECOGNITION_TRANSLATE_SYNTHESIS_State'

import RECOGNITION_SEMANTIC_POSTPROCESS_SYNTHESIS_State from './RECOGNITION_SEMANTIC_POSTPROCESS_SYNTHESIS_State'
import RECOGNITION_POSTPROCESS_SYNTHESIS_State from './RECOGNITION_POSTPROCESS_SYNTHESIS_State'
import INIT_State from './State'

import normalizeStateValue from './normalizeStateValue'

const StateRegistry = [
  new RECOGNITION_LLM_SEMANTIC_State(),
  new RECOGNITION_LLM_SEMANTIC_SYNTHESIS_State(),
  new RECOGNITION_POSTPROCESS_State(),
  new RECOGNITION_SEMANTIC_POSTPROCESS_State(),
  new RECOGNITION_SEMANTIC_State(),
  new RECOGNITION_SEMANTIC_SYNTHESIS_State(),
  new RECOGNITION_State(),
  new RECOGNITION_SYNTHESIS_State(),
  new RECOGNITION_TRANSLATE_State(),
  new RECOGNITION_TRANSLATE_SYNTHESIS_State(),
  new RECOGNITION_SEMANTIC_POSTPROCESS_SYNTHESIS_State(),
  new RECOGNITION_POSTPROCESS_SYNTHESIS_State(),
]

function findStateByValue(value) {
  const normalized = normalizeStateValue(value)
  return StateRegistry.find((state) => state.value === normalized) || null
}

class Context {
  constructor(initialState, initSwitches) {
    this.state = initialState || new INIT_State()
    this.switches = initSwitches || []
    console.log(`初始状态：${this.state.name}`)
  }

  /**
   * numStr 兼容格式 '1,2'
   * @param {*} numStr
   */
  addSwitches(numStr = '') {
    const switches = this.switches.slice()
    switches.push(...numStr.split(','))
    const point = this.normalizeSwitches(switches).join(',')
    this.setStateByValue(point)
    this.switches = point.split(',')
  }

  removeSwitch(numStr = '') {
    let switches = []
    let point = ''
    if (numStr === '2') {
      switches = this.switches.filter(
        (it) => String(it) !== String(numStr) && String(it) !== '13'
      )
      point = this.normalizeSwitches(switches).join(',')
    } else if (numStr === '8') {
      switches = this.switches.filter(
        (it) => String(it) !== String(numStr) && String(it) !== '14'
      )
      point = this.normalizeSwitches(switches).join(',')
    } else {
      switches = this.switches.filter((it) => String(it) !== String(numStr))
      point = this.normalizeSwitches(switches, this.switches).join(',')
    }
    this.setStateByValue(point)
    this.switches = point.split(',')
  }

  /**
   *
   * @param {*} rawSwitches  string[]
   * @returns string[]
   */
  normalizeSwitches(switches, rawSwitches = []) {
    const set = new Set(switches)

    // 规则 1：识别模块始终存在
    set.add('1')

    const has13 = set.has('13')
    const has14 = set.has('14')
    const has2 = set.has('2')
    const has3 = set.has('3')
    const has8 = set.has('8')

    if (has13) {
      // 规则 2：13（LLM语义）存在，2（普通语义）应当被排除
      set.delete('2')

      if (has3) {
        set.delete('3')
      }

      // 规则 3：LLM语义存在时，如果存在普通合成 8，则替换为 LLM 合成 14
      if (has8) {
        set.delete('8')
        set.add('14')
      }
    } else {
      // LLM语义被关闭时
      if (has14) {
        // 规则 4：关闭 LLM 语义时，如果有 LLM 合成，则换成普通合成
        set.delete('14')
        set.add('8')
      }

      if (has3) {
        set.add('3')
      }

      // 规则 5：关闭 LLM 语义时，恢复普通语义（13 => 2）
      if (!set.has('2') && rawSwitches.includes('13')) {
        set.add('2')
      }
      if (!set.has('8') && rawSwitches.includes('14')) {
        set.add('8')
      }
    }

    return Array.from(set).sort((a, b) => Number(a) - Number(b))
  }

  setState(newState) {
    console.log(`状态切换：${this.state.name} -> ${newState.name}`)
    this.state = newState
  }

  doAction(action) {
    this.state.handle(this, action)
  }

  getState() {
    return this.state
  }

  getSwitches() {
    return this.switches
  }

  getStateValue() {
    return this.state.value
  }

  isCurrentState(stateClass) {
    return this.state instanceof stateClass
  }

  isCurrentStateValue(value) {
    return this.state.isMatch(value)
  }

  isStateOf(stateValue, StateClass) {
    const normalizedInput = normalizeStateValue(stateValue)
    const normalizedState = new StateClass().value
    return normalizedInput === normalizedState
  }

  setStateByValue(value) {
    const state = findStateByValue(value)
    if (state) {
      this.setState(state)
    } else {
      console.log(`找不到状态值为 ${value} 的状态`)
    }
  }
}

export default Context
