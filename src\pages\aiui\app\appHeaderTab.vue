<template>
  <div class="tabs-container">
    <div
      v-for="menu in menus"
      :key="menu.key"
      :class="['tab-item']"
      @click="onTabsClick(menu)"
    >
      <router-link :to="{ name: menu.key }" active-class="active">{{
        menu.name
      }}</router-link>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      // tabs: ['应用配置', '资源下载', '统计分析'],
      // routeNames: ['app-config', 'app-tool', 'app-statistic'],
      activeTab: 0,

      showOta: false,
    }
  },

  computed: {
    menus() {
      return [
        {
          key: 'app-config',
          name: '应用配置',
        },
        {
          key: 'app-tool',
          name: '资源下载',
        },
        {
          key: 'app-statistic',
          name: '统计分析',
        },
        this.showOta
          ? {
              key: 'app-ota',
              name: '固件升级',
            }
          : null,
      ].filter(Boolean)
    },
  },

  created() {
    this.getWakeupAuth()
  },

  methods: {
    onTabsClick(menu) {
      // this.activeTab = index
      // TODO: 不加此行特殊处理 会导致点击统计分析时页面显示空白，
      if (menu.key === 'app-statistic') {
        this.$router.push({ name: 'app-statistic-service-index' })
      }
    },

    getWakeupAuth() {
      let self = this
      this.$utils.httpGet(
        this.$config.api.AWAKEN_CONFIG,
        {
          appid: this.$route.params.appId,
        },
        {
          success: (res) => {
            if (!res.data || !res.data.awakeConfig) return
            // self.contentReviewConfig =
            //   (res.data.contentReviewConfig &&
            //     JSON.parse(res.data.contentReviewConfig)) ||
            //   {}
            // let config =
            //   res.data.awakeConfig && JSON.parse(res.data.awakeConfig)
            if (res.data.otaConfig) {
              self.showOta = true
            }
          },
          error: (err) => {},
        }
      )
    },
  },
}
</script>

<style scoped lang="scss">
.tabs-container {
  // width: 260px;
  height: 42px;
  background: #eef0f1;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 4px;
  box-sizing: border-box;
}

.tab-item {
  flex: 1;
  height: 34px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 400;
  text-align: center;
  a {
    color: #555454;
    padding: 0 20px;
  }
  a.active {
    display: inline-block;
    color: $primary;
    width: 80px;
    background: rgba(255, 255, 255, 0.8);
    width: 100%;
    height: 100%;
    line-height: 34px;
    border-radius: 6px;
  }

  line-height: 20px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}
// .tab-item + .tab-item {
//   margin-left: 10px;
// }

// .tab-item.active {
//   .active {
//     width: 80px;
//     background: rgba(255, 255, 255, 0.8);
//     a {
//       color: $primary; /* 可调整为选中时的字体颜色 */
//     }
//   }
// }
</style>
