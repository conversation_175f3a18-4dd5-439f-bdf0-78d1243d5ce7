<template>
  <div class="header-right">
    <el-button
      size="small"
      type="primary"
      @click="constructAgent"
      :loading="loading"
    >
      {{ loading ? '构建中...' : '构建智能体' }}
    </el-button>
  </div>
</template>

<script>
export default {
  name: 'studioAgentHeaderRight',
  data() {
    return {
      loading: false,
    }
  },
  computed: {},
  watch: {},
  created() {},
  methods: {
    openDialog() {
      this.$refs.CreateAgentDialog.show()
    },
    constructAgent() {
      const params = {
        agentId: this.$route.params.agentId,
      }
      this.loading = true
      this.$utils.httpPost(
        this.$config.api.AGENT_BUILD_OLD,
        JSON.stringify(params),
        {
          config: {
            headers: {
              'Content-Type': 'application/json;charset=UTF-8',
            },
          },
          success: (res) => {
            this.$message.success('构建成功')
            this.loading = false
            // this.$refs.ApiForm.getValues(JSON.parse(res.data.extraParam))
          },
          error: (err) => {
            this.$message.error(err.desc)
            this.loading = false
          },
        }
      )
    },
  },
}
</script>

<style lang="scss">
.header-right {
  display: flex;
  align-items: center;
}
.header-save-time {
  font-size: 12px;
  color: #909399;
  margin-right: 24px;
}
// .header-more {
//   font-size: 16px;
//   color: $grey5;
//   margin-right: 24px;
//   cursor: pointer;
// }
.header-qa {
  font-size: 16px;
  color: #ddd;
  margin-right: 8px;
  cursor: pointer;
}
</style>
