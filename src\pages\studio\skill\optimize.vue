<template>
  <os-page :options="pageOptions" class="optimize-page">
    <el-form
      class="selection-form"
      inline
      size="medium"
      :model="searchFilter"
      ref="searchForm"
    >
      <el-form-item label="关键字" prop="keyword">
        <el-input
          style="width: 400px"
          placeholder="请输入语料关键字"
          v-model="searchFilter.utterance"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select
          style="width: 120px"
          v-model="searchFilter.status"
          placeholder="请选择"
        >
          <el-option
            v-for="status in statusList"
            :key="status.label"
            :label="status.label"
            :value="status.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="字数" prop="wordCount">
        <el-select
          style="width: 120px"
          v-model="searchFilter.len"
          placeholder="请选择"
        >
          <el-option
            v-for="len in lens"
            :key="len.label"
            :label="len.label"
            :value="len.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="导入日期" prop="status">
        <el-date-picker
          v-model="searchFilter.datePicker"
          type="daterange"
          :clearable="false"
          range-separator="~"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="timestamp"
          style="width: 310px"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          size="medium"
          style="min-width: 80px"
          @click="getData(1, true)"
          >搜索</el-button
        >
        <el-button
          size="medium"
          style="min-width: 80px; margin-left: 4px"
          @click="resetSearch"
          >重置</el-button
        >
      </el-form-item>
    </el-form>
    <div class="mgb16">
      <span>批量操作：</span>
      <el-button
        class="status-btn"
        type="primary"
        size="mini"
        :disabled="batchBtn1Disabled"
        @click="optimizeHandle()"
        >优化<span v-if="idDataSets[0].length && !tableSelectAll"
          >({{ idDataSets[0].length }})</span
        ></el-button
      >
      <el-button
        class="status-btn"
        size="mini"
        :disabled="batchBtn1Disabled"
        @click="ignoreHandle()"
        >忽略<span v-if="idDataSets[0].length && !tableSelectAll"
          >({{ idDataSets[0].length }})</span
        ></el-button
      >
      <el-button
        class="status-btn"
        size="mini"
        :disabled="batchBtn2Disabled"
        @click="revokeIgnoreHandle()"
        >撤销忽略<span v-if="idDataSets[2].length && !tableSelectAll"
          >({{ idDataSets[2].length }})</span
        ></el-button
      >
    </div>
    <os-table
      class="optimize-table"
      ref="optimizeTable"
      :tableData="tableData"
      @change="getData"
      @select="handleTableSelect"
      @select-all="handleTableSelectAll"
    >
      <el-table-column
        type="selection"
        width="42"
        :selectable="checkSelectable"
      ></el-table-column>
      <el-table-column width="38" class="fillter">
        <template slot="header" slot-scope="scope">
          <i class="ic-r-triangle-down" @click.stop="openFilterPopover"></i>
        </template>
      </el-table-column>
      <el-table-column label="APPID" prop="appid" width="104"></el-table-column>
      <el-table-column label="情景模式" prop="scene" width="120">
      </el-table-column>
      <el-table-column label="用户语料" prop="query">
        <template slot-scope="scope">
          <el-popover
            class="update-log"
            trigger="hover"
            placement="bottom-start"
            :content="scope.row.query"
          >
            <div slot="reference">
              <span class="ellipsis utter-item">{{ scope.row.query }}</span
              ><i
                class="ic-r-copy"
                title="复制语料"
                @click="copyUtter(scope.row.query)"
              ></i>
            </div>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column label="字数" prop="len" width="60"> </el-table-column>
      <el-table-column label="最近出现时间" width="196">
        <template slot-scope="scope">{{
          scope.row.insertTime | date('yyyy-MM-dd hh:mm:ss')
        }}</template>
      </el-table-column>
      <el-table-column label="操作" prop="status" width="176">
        <template slot-scope="scope">
          <template v-if="scope.row.status >= 0">
            <div class="status-left">
              <i
                v-if="scope.row.status !== 0"
                :class="scope.row.status == 2 ? 'icon-status' : 'ic-r-tick'"
              ></i>
              <span
                :class="
                  [1, 2].includes(scope.row.status) ? 'grey-text' : 'blue-text'
                "
                @click="
                  beforeUpdateSingle(
                    scope.row,
                    tableStatus[scope.row.status][0]
                  )
                "
                >{{ tableStatus[scope.row.status][0] }}</span
              >
            </div>
            <span
              class="status-right"
              v-if="tableStatus[scope.row.status][1]"
              @click="
                beforeUpdateSingle(scope.row, tableStatus[scope.row.status][1])
              "
              >{{ tableStatus[scope.row.status][1] }}</span
            >
          </template>
          <span v-else>-</span>
        </template>
      </el-table-column>
    </os-table>
    <select-list-popover
      :variablePopover="variablePopover"
      @filterHandle="filterHandle"
      :filterType="filterType"
    />
  </os-page>
</template>

<script>
import SelectListPopover from './dialog/selectListPopover.vue'
export default {
  name: 'skill-optimize',
  props: {
    subAccountEditable: Boolean,
  },
  data() {
    return {
      pageOptions: {
        title: '推荐优化',
        loading: false,
      },
      searchFilter: {
        utterance: '',
        status: '',
        len: '',
        datePicker: '',
      },
      initStartTime: '',
      initEndTime: '',
      statusList: [
        {
          label: '全部',
          value: '',
        },
        {
          label: '待优化',
          value: 0,
        },
        {
          label: '已优化',
          value: 1,
        },
        {
          label: '已忽略',
          value: 2,
        },
      ],
      lens: [
        {
          label: '所有长度',
          value: '',
        },
        {
          label: '25字内',
          value: 25,
        },
        {
          label: '20字内',
          value: 20,
        },
        {
          label: '15字内',
          value: 15,
        },
        {
          label: '10字内',
          value: 10,
        },
        {
          label: '8字内',
          value: 8,
        },
        {
          label: '6字内',
          value: 6,
        },
        {
          label: '5字内',
          value: 5,
        },
        {
          label: '4字内',
          value: 4,
        },
        {
          label: '3字内',
          value: 3,
        },
        {
          label: '2字内',
          value: 2,
        },
      ],
      variablePopover: {
        show: false,
        rect: null,
      },
      filterType: {
        type: '',
      },
      tableStatus: {
        0: ['优化', '忽略'],
        2: ['已忽略', '撤销'],
        1: ['已优化'],
      },
      tableData: {
        loading: false,
        total: 0,
        page: 1,
        size: 10,
        handleColumnText: '操作',
        list: [],
      },
      optimizedStatus: 1, //已优化的statu值
      tableSelection: [],
      tableSelectAll: false,
      tableUnSelection: [],
      tableCanNotSelection: {}, //记录页面对应的已优化的数量
      idDataSets: {
        0: [],
        2: [],
      },
      currentPageToggledByFilter: false,
    }
  },
  computed: {
    identify() {
      return this.$store.state.studioSkill.skill.identify || ''
    },
    batchBtn1Disabled() {
      if (!this.subAccountEditable) return true
      if (this.tableSelectAll) return false
      if (!this.idDataSets[0].length) return true
      return false
    },
    batchBtn2Disabled() {
      if (!this.subAccountEditable) return true
      if (this.tableSelectAll) return false
      if (!this.idDataSets[2].length) return true
      return false
    },
    canNotSelectionOfPage() {
      //当前页已优化的数量
      return this.tableCanNotSelection[this.tableData.page] || 0
    },
    canSelectionOfPage() {
      return this.tableData.list.length - this.canNotSelectionOfPage || 0
    },
    selectedNum() {
      // 当前页 toggleSelection 的数量
      return this.$refs.optimizeTable.$refs.table.selection.length || 0
    },
  },
  watch: {
    'tableSelection.length': function (val, oldVal) {
      if (val) {
        if (this.tableSelectAll) {
          this.filterType.type = !this.tableUnSelection.length ? 'all' : ''
        } else if (
          (this.selectedNum === val && this.canSelectionOfPage === val) ||
          this.currentPageToggledByFilter
        ) {
          this.filterType.type = 'currentPage'
        } else {
          this.filterType.type = ''
        }
      } else {
        this.filterType.type = ''
      }
    },
  },
  created() {
    this.refreshData(1)
  },
  methods: {
    resetSearch() {
      this.$refs.searchForm.resetFields()
      this.searchFilter = {
        utterance: '',
        status: '',
        len: '',
        datePicker: [this.initStartTime, this.initEndTime],
      }
      this.getData(1, true)
    },
    refreshData(page = 1) {
      this.initDataPicker()
      this.getData(page)
    },
    initDataPicker() {
      let end = new Date().getTime()
      let start = end - 3600 * 1000 * 24 * 30
      this.initStartTime = start
      this.initEndTime = end
      this.searchFilter.datePicker = [start, end]
    },
    getData(page, clear = false) {
      this.tableData.loading = true
      this.idDataSets = {
        0: [],
        2: [],
      }
      let self = this
      let data = {
        identify: this.identify,
        utterance: this.searchFilter.utterance,
        status: this.searchFilter.status,
        len: this.searchFilter.len,
        startTime:
          this.searchFilter.datePicker && this.searchFilter.datePicker[0],
        endTime:
          this.searchFilter.datePicker && this.searchFilter.datePicker[1],
        pageIndex: page || this.tableData.page,
        pageSize: this.tableData.size,
      }
      this.$utils.httpGet(this.$config.api.STUDIO_OPTIMIZE_GET_DATA, data, {
        success: (res) => {
          this.tableData.loading = false
          this.tableData.total = res.data.count
          this.tableData.page = res.data.pageIndex
          this.tableData.list = res.data.list
          if (clear) {
            this.tableSelectAll = false
            this.tableSelection = []
            this.tableUnSelection = []
          }
          this.$nextTick(self.toggleTableSelection)
        },
        error: (err) => {
          this.tableData.loading = false
          console.log('page=>>')
          console.log(err)
        },
      })
    },
    checkSelectable(row) {
      if (row.status === 1) return false
      return true
    },
    openFilterPopover(e) {
      const rect = e.target.getBoundingClientRect()
      this.variablePopover = {
        show: true,
        rect: rect,
      }
    },
    filterHandle(val) {
      let self = this
      this.filterType.type = val
      if (this.selectedNum) {
        self.tableAllSelection()
        if (this.selectedNum !== this.canSelectionOfPage) {
          self.$refs.optimizeTable.$refs.table.toggleAllSelection()
        }
        if (
          this.filterType.type === 'currentPage' &&
          this.selectedNum !== this.canSelectionOfPage
        ) {
          this.currentPageToggledByFilter = true
        } else {
          this.currentPageToggledByFilter = false
        }
      } else {
        self.tableAllUnselection()
        self.$refs.optimizeTable.$refs.table.toggleAllSelection()
      }
    },
    toggleTableSelection() {
      let self = this
      let cb = null
      if (self.tableSelectAll) {
        cb = function (row) {
          if (
            !self.$utils.inArray(self.tableUnSelection, row.id) &&
            row.status !== 1
          ) {
            self.$refs.optimizeTable.toggleRowSelection(row)
          }
        }
      } else {
        cb = function (row) {
          if (
            self.$utils.inArray(self.tableSelection, row.id) &&
            row.status !== 1
          ) {
            self.$refs.optimizeTable.toggleRowSelection(row)
          }
        }
      }
      let count = 0
      this.tableData.list.forEach((row) => {
        cb(row)
        if (row.status === 1) count++
      })
      cb = null
      this.$set(this.tableCanNotSelection, this.tableData.page, count)
      if (
        !this.tableSelectAll &&
        this.selectedNum == this.canSelectionOfPage &&
        this.selectedNum == this.tableSelection.length
      ) {
        this.filterType.type = 'currentPage'
      } else {
        this.filterType.type = ''
      }
    },
    handleTableSelect(selection, row) {
      let add = selection.some((item) => {
        return item.id === row.id && row.status !== this.optimizedStatus
      })
      if (add) {
        this.tableSelection.push(row.id)
        this.idDataSets[row.status].push(row.id)
        this.tableUnSelection = this.tableUnSelection.filter(
          (item) => item != row.id
        )
      } else {
        if (this.tableSelectAll) {
          this.tableUnSelection.push(row.id)
          this.filterType.type =
            this.filterType.type === 'all' ? '' : this.filterType.type
        } else {
          this.tableSelection = this.tableSelection.filter(
            (item) => item != row.id
          )
          this.idDataSets[row.status] = this.idDataSets[row.status].filter(
            (item) => item != row.id
          )
        }
      }
    },
    handleTableSelectAll(selection) {
      if (selection.length) {
        this.tableSelectAll = true
        this.tableAllSelection()
      } else {
        this.tableSelectAll = false
        this.tableAllUnselection()
      }
    },
    tableAllSelection() {
      this.tableSelection = []
      this.idDataSets = {
        0: [],
        2: [],
      }
      if (this.filterType.type === 'all') {
        this.tableUnSelection = []
        this.tableSelectAll = true
      } else {
        this.tableSelectAll = false
        this.tableData.list.forEach((item) => {
          if (item.status !== this.optimizedStatus) {
            this.tableSelection.push(item.id)
            this.idDataSets[item.status].push(item.id)
          }
          this.tableUnSelection.filter((element) => element != item.id)
        })
      }
    },
    tableAllUnselection() {
      this.tableSelectAll = false
      this.tableSelection = []
      this.idDataSets = {
        0: [],
        2: [],
      }
      if (this.filterType.type === 'all') {
        this.tableUnSelection = []
      } else {
        this.tableData.list.forEach((item) => {
          if (!this.tableUnSelection.includes(item.id)) {
            this.tableUnSelection.push(item.id)
          }
        })
      }
    },
    beforeUpdate(desc, type, currentStatus, status, singleId) {
      let self = this
      this.$confirm(desc, `确定${type}所选语料？`, {
        confirmButtonText: `确定${type}`,
        cancelButtonText: '取消',
        type: 'warning',
        showClose: false,
      })
        .then(() => {
          if (singleId) {
            return self.updateSingle(status, singleId)
          }
          if (self.tableSelectAll) {
            self.updateAll(currentStatus, status, '')
          } else {
            let ids = self.idDataSets[currentStatus].join(',')
            self.updateSingle(status, ids)
          }
        })
        .catch(() => {})
    },
    beforeUpdateSingle(row, type) {
      if (!this.subAccountEditable) return
      let list = ['优化', '忽略', '撤销']
      if (!list.includes(type)) return
      let tmpStatus = type === '撤销' ? 2 : 0
      switch (type) {
        case '优化':
          this.optimizeHandle(row.id)
          break
        case '忽略':
          this.ignoreHandle(row.id)
          break
        case '撤销':
          this.revokeIgnoreHandle(row.id)
          break
        default:
          break
      }
    },
    optimizeHandle(singleId) {
      let desc =
          '确定优化后，相同的语料将不再推荐。你确认已经在技能里优化了该语料吗？',
        type = '优化',
        currentStatus = 0,
        status = 1
      this.beforeUpdate(desc, type, currentStatus, status, singleId)
    },
    ignoreHandle(singleId) {
      let desc = '确定忽略后，相同语料将不再推荐。你确认忽略所选语料吗？',
        type = '忽略',
        currentStatus = 0,
        status = 2
      this.beforeUpdate(desc, type, currentStatus, status, singleId)
    },
    revokeIgnoreHandle(singleId) {
      let desc = '撤销后该条语料恢复为待优化状态，你确认要撤销忽略吗？',
        type = '撤销忽略',
        currentStatus = 2,
        status = 0
      this.beforeUpdate(desc, type, currentStatus, status, singleId)
    },
    updateSingle(status, ids) {
      let data = {
        status: status,
        ids: ids,
      }
      this.$utils.httpPost(this.$config.api.STUDIO_OPTIMIZE_UPDATE, data, {
        success: (res) => {
          this.resetDataAfterUpdate()
          this.$message.success(res.desc || '操作成功')
        },
        error: (err) => {
          this.tableData.loading = false
          console.log('page=>>')
          console.log(err)
        },
      })
    },
    updateAll(currentStatus, status, ids = '') {
      ids = this.tableUnSelection.join(',') || ''
      let data = {
        identify: this.identify,
        utterance: this.searchFilter.utterance,
        status: currentStatus,
        len: this.searchFilter.len,
        startTime:
          this.searchFilter.datePicker && this.searchFilter.datePicker[0],
        endTime:
          this.searchFilter.datePicker && this.searchFilter.datePicker[1],
        updateStatus: status,
        ids: ids,
      }
      this.$utils.httpPost(this.$config.api.STUDIO_OPTIMIZE_UPDATE_ALL, data, {
        success: (res) => {
          this.resetDataAfterUpdate()
          this.$message.success(res.desc || '操作成功')
        },
        error: (err) => {
          this.tableData.loading = false
          console.log('page=>>')
          console.log(err)
        },
      })
    },
    resetDataAfterUpdate() {
      this.tableSelectAll = false
      this.filterType.type = 'currentPage'
      this.tableSelection = []
      this.idDataSets = {
        0: [],
        2: [],
      }
      this.tableUnSelection = []
      this.refreshData(this.tableData.page)
    },
    copyUtter(data) {
      this.$utils.copyClipboard(JSON.stringify(data, null, '    '))
    },
  },
  components: {
    SelectListPopover,
  },
}
</script>

<style lang="scss" scoped>
.selection-form {
  margin: 28px 0;
  padding: 32px 32px 8px;
  box-shadow: 0px 3px 9px 0px rgba(0, 0, 0, 0.03);
  border-radius: 12px;
  border: 1px solid rgba(242, 245, 247, 1);
}
:deep(.el-form) {
  font-size: 0;
  .el-form-item {
    margin-right: 24px;
  }
}
.status-btn {
  min-width: unset;
  padding: 6px 12px;
}
.grey-text {
  color: $grey3;
}
.blue-text {
  color: $primary;
  cursor: pointer;
}
.status-left {
  vertical-align: middle;
  display: inline-block;
  width: 80px;
}
.status-right {
  vertical-align: middle;
  display: inline-block;
  width: 65px;
  color: $primary;
  cursor: pointer;
}
.icon-status {
  vertical-align: middle;
  display: inline-block;
  font-size: 12px;
  width: 12px;
  height: 12px;
  border: 2px solid $grey3;
  border-radius: 50%;
}
.ic-r-tick {
  color: $success;
}
.ic-r-copy {
  font-size: 12px;
  color: $primary;
  cursor: pointer;
}
.utter-item {
  display: inline-block;
  vertical-align: bottom;
  margin-right: 2px;
  max-width: calc(100% - 20px);
}
</style>
