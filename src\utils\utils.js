export function encryptedmailbox(email) {
  if (String(email).indexOf('@') > 0) {
    var str = email.split('@')
    var _s = ''
    var new_email = ''
    if (str[0].length > 3) {
      //@前面多于3位
      for (var i = 3; i < str[0].length; i++) {
        _s += '*'
      }
      new_email = str[0].substr(0, 3) + '***' + '@' + str[1]
    } else {
      //@前面小于等于于3位
      for (var i = 1; i < str[0].length; i++) {
        _s += '*'
      }
      new_email = str[0].substr(0, 1) + '***' + '@' + str[1]
    }
  }
  return new_email
}
