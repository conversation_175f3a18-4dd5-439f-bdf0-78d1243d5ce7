<template>
  <div class="aiui" id="aiui_id">
    <TopSection />
    <WhatSection />
    <TechSection />
    <IndustrySection />
    <AppSection />
    <EnvSection />

    <!-- <portal to="destination3">
      <div class="modal-gift-mask" :class="{
        'modal-gift-mask-hidden': giftMask,
        'modal-gift-mask-visible': giftMaskVisible,
      }">
        <img v-lazy="require(`@A/images/aiui/gift/gift.png`)" @click="toGiftCollect" />
        <div class="modal-gift-mask-close" @click="giftMask = !giftMask">
          <i class="el-dialog__close el-icon el-icon-close"></i>
        </div>
      </div>
    </portal> -->
    <portal to="destination2">
      <BackTop @backtop="goTop" :showBackTop="showBackTop" />
    </portal>
  </div>
</template>

<script>
import BackTop from '@C/backToTop'
import '../../../static/vue-awesome-swiper'
import { utils } from '../../utils/index'

import Store from '../../store'
import { throttle } from 'lodash-es'
import isLogin from '@U/isLogin'

import TopSection from './index-aiui/topSection.vue'
import WhatSection from './index-aiui/whatSection.vue'
import TechSection from './index-aiui/techSection.vue'
import AppSection from './index-aiui/appSection.vue'
import EnvSection from './index-aiui/envSection.vue'
import IndustrySection from './index-aiui/industrySection.vue'

const WOW = require('wow.js')

function getWindowHeight() {
  return 'innerHeight' in window
    ? window.innerHeight
    : document.documentElement.offsetHeight
}

export default {
  layout: 'aiuiHome',
  components: {
    BackTop,
    TopSection,
    WhatSection,
    TechSection,
    AppSection,
    EnvSection,
    IndustrySection,
  },

  data() {
    return {
      giftMask: false,
      giftPopup: false,
      giftMaskVisible: false,
      showBackTop: false,
      hasLogin: false,
    }
  },
  mounted() {
    // this.adjustContent()

    // this.resizeAjust = throttle(() => {
    //   this.adjustContent()
    // }, 1000)
    // window.addEventListener('resize', this.resizeAjust)

    this.handleScroll = throttle(() => {
      this.adjustScroll()
    }, 1000)

    let dom = document.getElementsByClassName('home-main')[0]
    dom && dom.addEventListener('scroll', this.handleScroll)

    this.hasLogin = isLogin()

    this.$nextTick(() => {
      if (process.browser) {
        // 在页面mounted生命周期里面 根据环境实例化WOW
        const wow = new WOW({
          boxClass: 'wow', // 类名
          animateClass: 'animated', // 动画类名前缀
          offset: 0, // 距离可视区域多远时开始动画
          mobile: true, // 是否在移动设备上激活
          live: true, // 如果动态添加元素是否也应用动画
          scrollContainer: '.home-main',
        })
        wow.init()
      }
    })
  },
  beforeDestroy() {
    // window.removeEventListener('resize', this.resizeAjust)
    let dom = document.getElementsByClassName('home-main')[0]
    dom && dom.removeEventListener('scroll', this.handleScroll)
  },
  methods: {
    adjustContent() {
      // 设置高度
      let height = Math.max(getWindowHeight(), 500)

      Array.from(document.getElementsByClassName('aiui-section')).forEach(
        (item, index) => {
          if (index === 0) {
            let headerHeight = 60
            if (window.innerWidth < 1601) {
              headerHeight = 40
            }
            item.style.height = `${height - headerHeight}px`
          } else {
            item.style.height = `${height}px`
          }
        }
      )
    },
    adjustScroll() {
      let scrollTop =
        document.getElementsByClassName('home-main')[0].scrollTop || 0
      console.log('homemain scroll top', scrollTop)
      if (scrollTop > 500) {
        this.showBackTop = true
      } else {
        this.showBackTop = false
      }
    },
    goTop() {
      let dom = document.getElementsByClassName('home-main')[0]
      dom && dom.scrollTo({ top: 0, behavior: 'smooth' })
    },
    giftPopupMask() {
      utils.httpGet(
        this.$config.api.GIFT_POPUP,
        {},
        {
          noMessage: true,
          noLogin: true,
          success: (res) => {
            this.giftPopup = res.data.popup
          },
          error: (err) => {},
        }
      )
    },
    loginState() {
      return (
        (Store.state.user.userInfo &&
          Store.state.user.userInfo.token ===
            utils.getCookie('ssoSessionId')) ||
        (Store.state.user.subAccountInfo &&
          Store.state.user.subAccountInfo.token ===
            utils.getCookie('subSessionId'))
      )
    },
    toGiftCollect() {
      let hasLogin = this.loginState(),
        loginPath = `${location.origin}/user/login?pageFrom=${
          location.origin + '/user/gift'
        }`
      if (!hasLogin) {
        utils.toPage(loginPath)
      } else {
        utils.toPage('/user/gift')
      }
    },
    getTicket() {
      const login = isLogin()
      if (login) {
        // 登录后调用资源接口，判断用户是否有访问权限

        window.open('/modelExperience?chan=AIUI&way=banner', '_blank')
      } else {
        let loginPath = `${location.origin}/user/login`
        utils.toPage(loginPath)
      }
    },
  },
}
</script>
<style lang="scss" scoped>
h1,
h2,
p,
ul,
dl,
ol {
  margin-bottom: 0;
}

.modal-gift-mask {
  position: fixed;
  top: 0%;
  left: 0%;
  background: rgba(0, 0, 0, 0.6) no-repeat center center;
  // width: 100%;
  // height: 100%;
  width: 100vw;
  height: 100vh;
  z-index: 9999998 !important;
  overflow: auto;
  margin: 0 auto;
  visibility: hidden;
  display: none;
  justify-content: center;

  &-visible {
    visibility: visible;
    display: flex;
  }

  &-hidden {
    visibility: hidden;
    display: none;
  }

  > img {
    z-index: 99999999 !important;
    opacity: 1 !important;
    mask-repeat: no-repeat;
    background-repeat: no-repeat;
    // width: auto;
    // height: auto;
    // max-width: 100vw;
    // max-height: 100vw;
    height: 100vh;
    aspect-ratio: 635 / 647;
    top: 50%;
    position: relative;
    transform: translateY(-50%);
  }

  &-close {
    position: relative;
    display: flex;
    justify-content: center;
    top: 6%;
    right: 10px;
    cursor: pointer !important;

    .el-icon-close {
      color: #909399;
      font-size: 24px;

      &:before {
        content: '\E60F';
        -webkit-box-sizing: border-box;
        box-sizing: border-box;
      }
    }
  }
}
</style>
