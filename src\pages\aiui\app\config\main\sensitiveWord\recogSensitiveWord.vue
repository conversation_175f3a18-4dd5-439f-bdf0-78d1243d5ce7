<template>
  <div class="div-sensitive-word-type">
    <p class="item-title">
      {{ `${sensitiveData[type].title}敏感词过滤` }}
      <el-tooltip class="item" effect="dark" placement="right">
        <div slot="content" v-html="sensitiveData[type].tips"></div>
        <i class="el-icon-question" />
      </el-tooltip>
    </p>
    <p class="item-desc">{{ sensitiveData[type].desc }}</p>

    <div class="sensitive-word-type">
      <ul v-for="(item, index) in sensitiveWordType">
        <li>{{ ++index }}.</li>
        <li :title="item.name">{{ item.name }}</li>
        <div>
          <div
            @click.prevent.stop="configSet(item)"
            v-show="switchList[--index]"
          >
            <i class="ic-r-setting"></i>
            <span>去设置</span>
          </div>
          <div>
            <el-switch
              :disabled="!subAccountEditable"
              @change="doSwitchKey($event, index, item)"
              v-model="switchList[index]"
            ></el-switch>
          </div>
        </div>
      </ul>
    </div>

    <div
      class="hot-word-wrap"
      v-if="JSON.stringify(sensitiveWordInfo) !== '{}'"
    >
      <p class="hot-word-title">{{ sensitiveWordInfo.fileName }}</p>
      <p class="hot-word-time">
        {{ $utils.dateFormat(sensitiveWordInfo.date, 'yyyy-MM-dd hh:mm:ss') }}
      </p>
      <div class="hot-word-config">
        <a class="mgr8" @click="downloadFile">下载</a>
        <a @click="deleteSensitiveWords">删除</a>
      </div>
    </div>

    <div class="config-content">
      <el-upload
        class="ib"
        ref="hotWordUpload"
        :action="`${$config.server}/aiui/${
          subAccount ? 'sub' : ''
        }web/app/sensitive/word/uploadFDFS?appid=${appId}&sceneId=${
          currentScene.sceneBoxId
        }&type=${type == 0 ? 1 : 2}`"
        :show-file-list="false"
        :before-upload="beforeUpload"
        :on-success="handleUploadSuccess"
        :disabled="!subAccountEditable"
      >
        <el-button
          size="small"
          class="mgr16"
          :loading="isUploading"
          :disabled="!subAccountEditable"
          type="default"
          >{{
            JSON.stringify(sensitiveWordInfo) === '{}'
              ? '上传敏感词'
              : '重新上传'
          }}</el-button
        >
      </el-upload>
      <a @click="downloadHotWordTemplate">下载模板</a>
    </div>

    <LevelDialog
      :type="type"
      @doClose="doCancel"
      @doConfirm="doConfirm"
      :sensitiveLevelVisible="sensitiveLevelVisible"
      :sensitiveWordTypeCopySingle="sensitiveWordTypeCopySingle"
      :sensitiveWordTypeCopySingleCopy="sensitiveWordTypeCopySingleCopy"
    />
  </div>
</template>

<script>
import LevelDialog from './dialog/LevelDialog'
export default {
  name: 'recogSensitiveWord',
  props: {
    type: Number, // 0: 识别敏感词；1: 语义敏感词
    changeType: Number,
    appId: '',
    appInfo: Object,
    saving: Boolean,
    limitCount: Object,
    currentScene: Object,
    subAccount: Boolean,
    subAccountEditable: Boolean,
    form: Object,
    sensitiveWordTypeAll: Array,

    show: Boolean,
  },
  components: {
    LevelDialog,
  },
  data: () => {
    return {
      excelUpload: {},
      currentSensitiveLevel: 0,
      replyWordsCopy: [],
      newReplyWord: '',
      newReplyWordVisible: false,
      sensitiveData: [
        {
          title: '识别',
          desc: '识别结果中包含敏感信息，可以通过设置不被展示。支持自定义增加。',
          tips: '过滤级别高，过滤所有带有敏感信息的词；级别中<br/>将过滤高敏感和比较敏感的词；级别低仅过滤高敏<br/>感信息的词。',
        },
        {
          title: '语义',
          desc: '给用户回复的信息中包含敏感信息，可以通过设置过滤掉。支持自定义增加。',
          tips: '过滤级别高，过滤所有带有敏感信息的词；级别中<br/>将过滤高敏感和比较敏感的词；级别低仅过滤高敏<br/>感信息的词。',
        },
      ],
      sensitiveLevelVisible: false,
      currentSensitiveItem: {
        name: '',
      },
      isUploading: false,
      sensitiveWordInfo: {},
      switchList: [],
      sensitiveWordTypeCopySingle: {},
      sensitiveWordTypeCopySingleCopy: {},
      sensitiveWordTypeCopy: [],
      sensitiveWordType: [],
      recognizeType: 1, // 识别敏感词
      semanticType: 2, // 语义敏感词
    }
  },
  watch: {
    sensitiveLevelVisible: function () {
      if (this.sensitiveLevelVisible) {
        if (!this.sensitiveWordTypeCopySingle.level) {
          this.sensitiveWordTypeCopySingle.level = '0'
        }
      }
    },
    currentScene(scene) {
      if (scene && scene.sceneBoxId) {
        this.getSensitiveWordType()
        this.getHotWordInfo()
      }
    },

    show(val) {
      if (val) {
        this.getSensitiveWordType()
        this.getHotWordInfo()
      }
    },
  },

  created() {
    this.getSensitiveWordType()
    this.getHotWordInfo()
  },

  methods: {
    changeSaving(flag) {
      this.$emit('changeSaving', flag)
    },
    emitChangeSensitiveWord(key, flag) {
      this.$emit('changeSensitiveWord', key, flag)
    },
    showNewKeyword() {
      if (!this.subAccountEditable) return
      this.newReplyWordVisible = true
      this.$nextTick((_) => {
        this.$refs.saveTagInput.$refs.input.focus()
      })
    },
    getSensitiveWordType() {
      let thiz = this
      this.$utils.httpGet(
        this.$config.api.SENSITIVE_TYPE_AND_CONFIG,
        {
          appid: thiz.appId,
          sceneId: thiz.currentScene.sceneBoxId,
          type: thiz.type === 0 ? 1 : 2,
        },
        {
          success: (res) => {
            if (res.flag) {
              this.sensitiveWordType = res.data
              this.sensitiveWordTypeCopy = JSON.parse(
                JSON.stringify(this.sensitiveWordType)
              )
              this.sensitiveWordTypeCopy.forEach((reply, index) => {
                if (reply.level) {
                  this.switchList[index] = reply.status === '1' // 判断是否启用
                }
              })
            }
          },
          error: (err) => {
            if (err) thiz.$message.error(err)
          },
        }
      )
    },
    doCancel() {
      this.sensitiveLevelVisible = false
      //this.sensitiveWordTypeCopy = this.sensitiveWordType
      //this.sensitiveWordType = this.sensitiveWordTypeCopy
    },
    doConfirm() {
      let thiz = this
      this.sensitiveWordType = Array.prototype.map.call(
        this.sensitiveWordType,
        (item, index) => {
          if (item.id === thiz.sensitiveWordTypeCopySingle.id) {
            return thiz.sensitiveWordTypeCopySingle
          }
          return item
        }
      )
      thiz.sensitiveLevelVisible = false

      let tmp = this.sensitiveWordType.filter((item, index) => {
        item.type = thiz.type === 0 ? 1 : 2
        item.appid = thiz.appid
        item.sceneName = thiz.currentScene.sceneName
        return item.level && item.status
      })

      let result = tmp.slice()
      this.updateSensitiveWordType(result)
      // debugger
      // tmp.forEach((im) => {
      //   const index = this.sensitiveWordTypeAll.findIndex(
      //     (it) => it.id === im.id && it.name === im.name && it.type === im.type
      //   )
      //   if (index === -1) {
      //     result = result.concat(im)
      //   }
      // })
      // 去重------没看懂

      // thiz.updateSensitiveWordTypeAll(result)
      // let keyType
      // if (thiz.changeType === 0) {
      //   keyType = 9
      // } else if (thiz.changeType === 1) {
      //   keyType = 10
      // } else if (thiz.changeType === 2) {
      //   keyType = 11
      // }
      // thiz.emitChangeSensitiveWord(keyType, true)
    },

    // 更新敏感词类型
    updateSensitiveWordType(result) {
      let thiz = this

      let url =
        this.$config.api.SENSITIVE_TYPE_AND_CONFIG_UPDATE +
        '?appid=' +
        thiz.appId +
        '&sceneId=' +
        thiz.currentScene.sceneBoxId
      //+ '&type=' + (thiz.type === 0 ? 1 : 2)
      this.$utils.httpPost(url, JSON.stringify(result), {
        config: {
          headers: {
            'Content-Type': 'application/json;charset=UTF-8',
          },
        },
        noErrorMessage: true,
        success: (res) => {
          if (res.flag) {
            // thiz.sensitiveWordTypeAll = []
            // thiz.changeSaving(true)
            // let keys = []
            // if (this.itemChangeList[9] && this.change) {
            //   keys.push(9)
            //   thiz.$refs.recognition.getSensitiveWordType()
            // }
            // if (this.itemChangeList[10] && this.change) {
            //   keys.push(10)
            //   thiz.$refs.semantic.getSensitiveWordType()
            // }
            // if (this.itemChangeList[11] && this.change) {
            //   keys.push(11)
            //   thiz.$refs.translate.getSensitiveWordType()
            // }
            // thiz.setChangeFlagSensitiveAll(keys, false)
          } else {
            // thiz.changeSaving(false)
          }
        },
        error: (err) => {
          thiz.changeSaving(false)
          if (err) thiz.$message.error(err.desc)
        },
      })
    },
    doSwitchKey(e, index, item) {
      // 重置敏感词分类状态：
      if (!item.level) item.level = '0'
      if (e) {
        item.status = '1'
      } else {
        item.status = '0'
      }
      this.sensitiveWordTypeCopySingle = item
      this.doConfirm()
    },
    configSet(item) {
      this.sensitiveLevelVisible = true
      this.sensitiveWordTypeCopySingle = JSON.parse(JSON.stringify(item))
      this.sensitiveWordTypeCopySingleCopy = JSON.parse(JSON.stringify(item))
    },
    getReplyWords() {
      let thiz = this
      this.$utils.httpGet(
        this.$config.api.SENSITIVE_TYPE_AND_CONFIG,
        {
          appid: thiz.appId,
          sceneId: thiz.currentScene.sceneBoxId,
          type: thiz.recognizeType,
        },
        {
          success: (res) => {
            if (res.flag) {
              this.sensitiveWordType = res.data
            }
          },
          error: (err) => {
            if (err) thiz.$message.error(err)
          },
        }
      )
    },
    deleteSensitiveWords() {
      let self = this
      let data = {
        appid: self.appId,
        sceneId: self.currentScene.sceneBoxId,
        sceneName: self.currentScene.sceneBoxName,
        type: this.type == 0 ? 1 : 2,
      }

      this.$utils.httpPost(this.$config.api.SENSITIVE_TYPE_TRUNCATE, data, {
        success: (res) => {
          self.sensitiveWordInfo = {}
        },
      })
    },
    beforeUpload(file, fileList) {
      let reg = /\.xls(x)?$/i
      let type = reg.test(file.name)
      let fileSize = this.limitCount['app_sensitive_word_file_size'] || 5 // 默认5M
      let unExceed = file.size < 1024 * 1024 * fileSize
      if (!type) {
        this.$message.error('仅支持xls或xlsx文件')
        return false
      }
      if (!unExceed) {
        this.$message.error(`文件不能超过${fileSize}M`)
        return false
      }
      this.isUploading = true
    },
    handleUploadSuccess(data) {
      if (data.flag) {
        this.excelUpload.uploaded = true
        this.getHotWordInfo()
      } else {
        this.$message.warning(data.desc)
      }
      this.isUploading = false
    },
    getHotWordInfo() {
      let self = this
      console.log('getHotWordInfo', {
        appid: this.appId,
        sceneId: this.currentScene.sceneBoxId,
        sceneName: this.currentScene.sceneBoxName,
        type: this.type == 0 ? 1 : 2,
      })
      this.$utils.httpGet(
        this.$config.api.SENSITIVE_TYPE_GET_UPLOADED,
        {
          appid: this.appId,
          sceneId: this.currentScene.sceneBoxId,
          sceneName: this.currentScene.sceneBoxName,
          type: this.type == 0 ? 1 : 2,
        },
        {
          success: (res) => {
            if (res.flag && res.data) {
              self.sensitiveWordInfo = res.data
            }
          },
        }
      )
    },
    downloadFile() {
      let self = this
      if (!this.downloading) {
        let isSubAccount = this.subAccount ? true : false
        this.downloading = true
        this.$utils.postopen(
          this.$config.api.SENSITIVE_TYPE_DOWNLOAD,
          {
            appid: this.appId,
            sceneId: this.currentScene.sceneBoxId,
            sceneName: this.currentScene.sceneBoxName,
            type: this.type == 0 ? 1 : 2,
          },
          isSubAccount
        )

        setTimeout(function () {
          self.downloading = false
        }, 5000)
      } else {
        this.$message.warning('操作过快，请稍后再试')
      }
    },
    downloadHotWordTemplate() {
      let self = this
      if (!this.downloadTemplate) {
        this.downloadTemplate = true
        window.open(
          `https://aiui-file.cn-bj.ufileos.com/sensitive_words_template.xlsx`,
          '_self'
        )
        setTimeout(function () {
          self.downloadTemplate = false
        }, 5000)
      } else {
        this.$message.warning('操作过快，请稍后再试')
      }
    },
  },
}
</script>

<style scoped lang="scss">
.reply > span {
  display: block;
  width: fit-content;
}

.keyword {
  /*display: inline-grid;*/
  margin: 0 8px 8px 0;
}
.new-keyword-input {
  width: 200px;
}
.button-new-keyword {
  display: inline-block;
  width: 36px;
  height: 36px;
  font-size: 24px;
  line-height: 30px;
  cursor: pointer;
  color: $primary;
  text-align: center;
  vertical-align: top;
  border: 1px solid $grey3;
  border-radius: 2px;
}

.ic-r-setting {
  font-size: 18px;
  color: #1f90fe;
  cursor: pointer;
}

.item-title {
  position: relative;
  font-size: 16px;
  font-weight: 500;
  padding-left: 10px;
  margin-top: 24px;
  margin-bottom: 8px;
  &:before {
    width: 2px;
    height: 16px;
    background-color: $primary;
    content: ' ';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    margin: auto;
  }
}
.item-desc {
  color: #a5a5b8;
  margin-bottom: 18px;
}
.sensitive-word-type {
  > ul {
    position: relative;
    border-bottom: 1px solid #e4e7ed;
    display: flex;
    justify-content: space-between;
    margin-bottom: 0;
    height: 36px;
    line-height: 36px;
    > li {
      display: inline-flex;
      flex: 1;
      margin-bottom: 0;
    }
    > li:nth-child(1) {
      flex: 0.05;
    }
    > li:nth-child(2) {
      display: block;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
    > div {
      display: flex;
      justify-content: flex-end;
      width: 120px;
      > div:nth-child(1) {
        display: flex;
        width: 120px;
        color: #1f90fe;
        align-items: center;
        cursor: pointer;
      }
      > div:nth-child(2) {
        flex: 1;
        display: flex;
        justify-content: flex-end;
        align-items: center;
      }
    }
  }
}
.config-content {
  margin-top: 10px;
}

.hot-word-wrap {
  position: relative;
  height: 80px;
  border-radius: 2px;
  padding: 18px;
  border: 1px dashed $grey4;
  margin-bottom: 24px;
}
.hot-word-title {
  font-size: 16px;
  font-weight: 500;
  color: $semi-black;
}
.hot-word-time {
  color: $grey5;
  font-size: 12px;
}
.hot-word-config {
  position: absolute;
  top: 0;
  right: 32px;
  line-height: 80px;
}
</style>
