<template>
  <ul class="menu-container">
    <li
      v-for="(item, index) in menus[0].menus"
      :class="{
        active: routePath === item.path,
      }"
      :key="index"
      @click="toggleActive(item)"
    >
      <i :class="['os-menu-icon', 'iconfont', item.icon]"></i>
      <span>{{ item.value }}</span>
    </li>
  </ul>
</template>
<script>
export default {
  name: 'store-menu',
  props: {
    menus: Array,
  },
  data() {
    return {
      routeName: '',
      routePath: '',
    }
  },
  created() {
    this.setRoute()
  },
  watch: {
    $route(to, from) {
      this.setRoute()
    },
  },
  methods: {
    setRoute() {
      let self = this
      if (self.$router.match(location.pathname)) {
        self.routeName = self.$router.match(location.pathname).name
        self.routePath = self.$router.match(location.pathname).path
        // console.log(
        //   'self.routeName, self.routePath',
        //   self.routeName,
        //   self.routePath
        // )
      }
    },
    toggleActive(item) {
      if (item.path !== this.routePath) {
        this.routePath = item.path
        this.$router.push({ path: item.path })
      }
    },
  },
}
</script>
<style scoped lang="scss">
.menu-container {
  > li {
    position: relative;
    height: 80px;
    line-height: 80px;
    cursor: pointer;
    padding-left: 24px;
    display: flex;
    align-items: center;
    span {
      font-size: 20px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: $grey001;
      margin-left: 16px;
    }
    &.active {
      background: #e8f3fd;
      i {
        background-image: linear-gradient(to right, #1eb5eb, #1f76fc);
        background-clip: text;
        -webkit-text-fill-color: transparent;
      }
      span {
        color: $primary;
      }
    }
    &.active::before {
      content: ' ';
      position: absolute;
      z-index: 1;
      width: 6px;
      height: 34px;
      background: $primary;
      border-radius: 3px;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
    }
  }
  .iconfont {
    font-size: 22px;
  }
  // .icon-quanbufenlei {
  //   font-size: 26px;
  // }
}
// 技能商店适配不同屏幕
@media screen and (max-height: 801px) {
  .menu-container {
    > li {
      height: 54px;
      line-height: 54px;
      span {
        font-size: 13px;
      }
    }
    &.active::before {
      width: 4px;
      height: 23px;
      border-radius: 2px;
    }
  }
}
</style>
