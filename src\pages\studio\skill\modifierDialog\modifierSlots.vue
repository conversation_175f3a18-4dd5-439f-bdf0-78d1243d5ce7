<template>
  <div>
    <os-table
      class="slot-table"
      :border="true"
      :tableData="auxiliarySlotsData"
      ref="auxiliarySlotTable"
    >
      <el-table-column label="槽位标识" prop="slotName" width="120">
      </el-table-column>
      <el-table-column width="200" label="对应实体/辅助词">
        <template slot-scope="scope">
          <template v-if="scope.row.entityName">
            <span
              :style="utteranceColor[scope.row.slotName]"
              @click.stop="
                openSelectEntity(
                  scope.row,
                  $event,
                  utteranceColor[scope.row.slotName]
                )
              "
            >
              {{ scope.row.entityType === 1 ? '@' : '#'
              }}{{ scope.row.entityName }}
            </span>
            <i
              v-if="scope.row.entityName.match('IFLYTEK.Wildcard')"
              class="ic-r-edit cp wildcard"
              style="margin-left: 8px"
              @click.stop="openSetWildcardDialog(scope.row, $event)"
            />
          </template>
          <a v-else @click.stop="openSelectEntity(scope.row, $event)"
            >设置对应实体/辅助词</a
          >
        </template>
      </el-table-column>
    </os-table>
    <select-entity-popover
      @change="getAuxiliarySlots"
      :showType="'modifierSlot'"
    />
    <set-wildcard-dialog
      :variablePopover="wildCardPopover"
      @change="getAuxiliarySlots"
    />
  </div>
</template>

<script>
import SelectEntityPopover from '../dialog/selectEntity.vue'
import SetWildcardDialog from '../dialog/setWildcard.vue'
export default {
  name: 'modifier-slots',
  props: {
    businessId: String,
    dialog: {
      type: Object,
      default: () => ({
        show: false,
        modifierId: '',
      }),
    },
  },
  data() {
    return {
      // 辅助词
      auxiliarySlotsData: {
        loading: false,
        total: 0,
        page: 1,
        size: 5,
        list: [],
      },
      oldAuxiliarySlotList: [],
      wildCardPopover: {
        show: false,
        rect: null,
      },
    }
  },
  computed: {
    utteranceColor() {
      return this.$store.state.studioSkill.utteranceColor
    },
  },
  created() {
    this.getAuxiliarySlots()
  },
  methods: {
    // 获取辅助词槽位
    getAuxiliarySlots() {
      if (!this.dialog.modifierId) return
      let self = this
      this.auxiliarySlotsData.loading = true
      this.auxiliarySlotsData.list = []
      this.$utils.httpGet(
        this.$config.api.STUDIO_MODIFIER_SLOTS,
        {
          businessId: this.businessId,
          modifierId: this.dialog.modifierId,
        },
        {
          success: (res) => {
            res.data.forEach(function (item, index) {
              if (item.slotName) {
                self.$store.dispatch(
                  'studioSkill/setUtteranceColor',
                  item.slotName
                )
              }
            })
            this.auxiliarySlotsData.list = res.data
            this.oldAuxiliarySlotList = JSON.parse(JSON.stringify(res.data))
            this.auxiliarySlotsData.loading = false
          },
          error: (err) => {
            this.auxiliarySlotsData.loading = false
          },
        }
      )
    },
    openSelectEntity(data, event, style) {
      let rect = event.target.getBoundingClientRect()
      this.$store.dispatch('studioSkill/setEntityPopover', {
        show: true,
        showType: 'modifierSlot',
        data: data,
        style: style,
        rect: {
          top: rect.top,
          left: rect.left,
          width: rect.width,
          y: rect.y,
        },
      })
    },
    // 打开设置通配实体dialog
    openSetWildcardDialog(row, e) {
      const rect = e.target.getBoundingClientRect()
      this.wildCardPopover = {
        show: true,
        data: row,
        rect: {
          top: rect.top,
          left: rect.left,
          width: rect.width,
          y: rect.y,
        },
      }
    },
  },
  components: {
    SelectEntityPopover,
    SetWildcardDialog,
  },
}
</script>

<style lang="scss" scoped></style>
