<template>
  <div class="main-content">
    <MyHeader> </MyHeader>

    <section class="main-content-banner">
      <div class="banner-text">
        <h2>儿童玩具解决方案</h2>

        <p class="banner-text-content">
          有趣的大模型语音交互 角色互动、情感对话和知识启蒙
          接入开源，快速完成玩具方案验证
        </p>

        <div class="hor-btn">
          <el-button
            class="banner-text-button"
            @click="toConsole"
            type="primary"
            round
            >合作咨询</el-button
          >
        </div>
      </div>
    </section>

    <section class="section-nav">
      <ul class="nav-ul" id="nav-ul">
        <li
          v-for="item in nav_list"
          :key="item.id"
          @click="gotoSelection(item.id)"
        >
          {{ item.name }}
        </li>
      </ul>

      <i
        v-if="nav_list.length > 4"
        :class="[
          'nav-btn',
          nav_flag ? 'el-icon-caret-bottom' : 'el-icon-caret-top',
        ]"
        id="nav-btn"
        @click="handleNavExpand"
      ></i>
    </section>

    <section class="section section1">
      <h2>应用场景</h2>

      <p>对话式儿童陪伴玩具</p>

      <div class="section-item">
        <ul>
          <li v-for="(item, index) in app_scenario" :key="index" class="app">
            <img :src="item.src" :alt="item.alt" />
            <p class="app-text">{{ item.alt }}</p>
          </li>
        </ul>
      </div>
    </section>

    <section class="section section2">
      <h2>方案优势</h2>

      <ul>
        <li v-for="item in pics" :key="item.index">
          <h3>{{ item.title }}</h3>
          <p>{{ item.sub_title }}</p>

          <img :src="item.src" alt="" v-if="item.visible" />

          <i
            class="el-icon-arrow-up my-up-btn"
            v-if="item.visible"
            @click="item.visible = !item.visible"
          ></i>
          <i
            class="el-icon-arrow-down my-down-btn"
            v-else
            @click="item.visible = !item.visible"
          ></i>
        </li>
      </ul>
    </section>

    <section class="section section3">
      <h2>接入方式</h2>
      <div class="join-way">
        <div class="border">
          <div class="title">纯软接入</div>

          <div class="desc">接入代码开源，快速实现大模型语音交互</div>

          <div class="sdk-button" @click="gotoSDK">开源SDK</div>

          <div class="other-btn">
            <div class="single">RTOS</div>
            <div class="single">安卓</div>
            <div class="single">Linux</div>
            <div class="single">鸿蒙</div>
          </div>
        </div>

        <div class="border">
          <div class="title">玩具硬件模组</div>
          <div class="desc">开箱即用，适合项目验证及产品量产</div>
          <div class="model-item" @click="gotoClick"></div>
        </div>
      </div>
    </section>

    <section class="section section4">
      <h2>接入儿童大模型交互</h2>

      <h3>免费咨询专属顾问 为您量身定制产品推荐方案</h3>

      <div class="cooperation-btn" @click="toConsole">合作咨询</div>
    </section>

    <section class="section section-footer">
      <!-- <aiuiMobileFooter> </aiuiMobileFooter> -->
      <div class="footer-title">
        联系我们
        <span class="arrow" @click="clickFooterArrow">▼</span>
      </div>
      <div class="content">
        <p>联系电话：19955108393</p>
        <p>开发者交流群：617903641</p>
        <p>商务技术支持：<EMAIL></p>
      </div>

      <div class="footer-title">
        友情链接
        <span class="arrow" @click="clickFooterArrow">▼</span>
      </div>
      <div class="content">
        <p>科大讯飞</p>
        <p>讯飞开放平台</p>
      </div>

      <div class="corporation-info">
        ©科大讯飞股份有限公司 皖ICP备05001217号
      </div>
    </section>
  </div>
</template>

<script>
import MyHeader from '@P/aiui/solution-aiui/mobile-header.vue'
export default {
  name: 'AiuiWebIndex',

  components: {
    MyHeader,
  },

  data() {
    return {
      nav_flag: true,

      expandFlag: true,
      nav_list: [
        { name: '应用场景', id: 1 },
        { name: '方案优势', id: 2 },
        { name: '接入方式', id: 3 },
      ],

      app_scenario: [
        {
          alt: '毛绒玩具',
          src: require('../../../../../assets/images/solution/child-toys/toys1.png'),
        },
        {
          alt: '塑料玩具',
          src: require('../../../../../assets/images/solution/child-toys/toys2.png'),
        },
        {
          alt: '儿童陪伴机器人',
          src: require('../../../../../assets/images/solution/child-toys/toys3.png'),
        },
        {
          alt: '儿童故事机',
          src: require('../../../../../assets/images/solution/child-toys/toys4.png'),
        },
      ],

      pics: [
        {
          title: '快速打造玩具IP',
          sub_title:
            '自定义玩具人设及对话风格；自定义大模型文档问答，实现品牌知识问答',
          visible: true,
          src: require('../../../../../assets/images/solution/child-toys/option1.png'),
        },
        {
          title: '有趣有料的儿童专属内容',
          sub_title:
            '丰富的音乐、故事等正版儿童音频信源；孩子与大模型共同创作故事，释放孩子想象力',
          visible: false,
          src: require('../../../../../assets/images/solution/child-toys/option2.png'),
        },
        {
          title: '儿童科普满足好奇心',
          sub_title: '科普大模型，随时随地知识问答；孩子听得懂的趣味讲解',
          visible: false,
          src: require('../../../../../assets/images/solution/child-toys/option3.png'),
        },
        {
          title: '儿童口语启蒙，锻炼表达能力',
          sub_title: '提供口语老师教学陪练；支持智能评价反馈',
          visible: false,
          src: require('../../../../../assets/images/solution/child-toys/option4.png'),
        },

        {
          title: '儿童共情闲聊，呵护心理健康',
          sub_title:
            '大模型在对话中识别情绪变化；正面回应孩子心理需求，给予鼓励与引导',
          visible: false,
          src: require('../../../../../assets/images/solution/child-toys/option5.png'),
        },

        {
          title: '复刻家人声音的情感陪伴',
          sub_title: '一句话复刻真人声音；有温度的玩具对话，宛如亲人时刻陪伴',
          visible: false,
          src: require('../../../../../assets/images/solution/child-toys/option6.png'),
        },
        {
          title: '多模态交互，与孩子共同探索世界',
          sub_title:
            '支持音视频输入，让玩具感知现实世界；提供摄像头、麦克风阵列等硬件设备',
          visible: false,
          src: require('../../../../../assets/images/solution/child-toys/option7.png'),
        },
      ],
    }
  },

  mounted() {},

  methods: {
    gotoSelection(id) {
      const section = document.getElementsByClassName(`section` + id)[0]
      if (section) {
        section.scrollIntoView({ behavior: 'smooth' })
      }
    },

    toDoc(link) {
      window.open(link)
    },

    handleNavExpand() {
      const nav_ul = document.getElementById('nav-ul')
      const navIcon = document.getElementById('nav-btn')

      navIcon.addEventListener('click', () => {
        if (this.nav_flag) {
          nav_ul.classList.add('expanded')
        } else {
          nav_ul.classList.remove('expanded')
        }
        this.nav_flag = !this.nav_flag
      })
    },

    clickFooterArrow() {
      const titles = document.querySelectorAll('.footer-title')
      titles.forEach((title) => {
        title.addEventListener('click', () => {
          const content = title.nextElementSibling
          const arrow = title.querySelector('.arrow')

          if (content.style.display === 'none' || !content.style.display) {
            content.style.display = 'block'
            arrow.classList.add('up')
          } else {
            content.style.display = 'none'
            arrow.classList.remove('up')
          }
        })
      })
    },

    toConsole() {
      const search = window.location.search
      if (search) {
        window.open(`/solution/apply/36${search}`)
      } else {
        window.open('/solution/apply/36')
      }
    },

    gotoSDK() {
      window.open('https://aiui-doc.xf-yun.com/project-1/doc-418/')
    },

    gotoClick() {
      window.open('https://www.aifuwus.com/onstage/cmddetail?id=4434')
    },
  },
}
</script>

<style lang="scss" scoped>
.main-content {
  background-color: #f6f7f9;
  width: 750px;
  overflow: hidden;
  &-banner {
    background: url(~@A/images/solution/smart-hardware/mobile/banner2.jpg)
      center no-repeat;
    background-size: cover;
    height: 375px;
    overflow: hidden;
    width: 100%;
  }
  .banner-text {
    margin-top: 64px;
    padding-left: 44px;
    h2 {
      font-size: 48px;
      font-weight: 600;
      color: #000000;
      line-height: 68px;
    }
    p {
      width: 648px;
      font-size: 24px;
      font-weight: 300;
      color: #000000;
      line-height: 40px;
      text-align: left;
    }
    .hor-btn {
      display: flex;
      flex-direction: row;
      justify-content: flex-start;
      margin-top: 40px;
      div:nth-child(2) {
        margin-left: 30px;
      }
    }
  }

  .section {
    h2 {
      font-size: 48px;
      font-weight: 600;
      color: #000000;
      line-height: 68px;
      text-align: center;
      margin: 0 auto;
      margin-top: 50px;
      margin-bottom: 30px;
    }
    p,
    ul {
      margin-bottom: 0;
    }
    max-width: 750px;
    overflow: hidden;
    margin: 0 auto;
    .section-title {
      text-align: center;
      font-size: 36px;
      font-weight: 500;
      color: #444444;
      line-height: 54px;
      .arrow {
        width: 26px;
        height: 24px;
        display: inline-block;
        background-repeat: no-repeat;
        background-size: contain;
      }
      .arrow-left {
        background-position: left;
        background-image: url(~@A/images/solution/smart-hardware/arrow-left.png);
      }
      .arrow-right {
        background-position: right;
        background-image: url(~@A/images/solution/smart-hardware/arrow-right.png);
      }
    }
    .section-tabs {
      margin-top: 60px;
    }
  }

  .section-nav {
    background-color: #ffffff;
    position: relative;
    width: 100%;
    border: 1px solid #ddd;
    padding: 0 30px;
    .nav-ul {
      display: flex;
      justify-content: space-evenly;
      flex-wrap: wrap;
      overflow: hidden; /* 默认隐藏超出部分 */
      max-height: 68px; /* 折叠状态时，只显示一行 */
      transition: max-height 0.3s ease; /* 动画过渡效果 */
    }
    .nav-ul.expanded {
      max-height: 200px; /* 展开状态：允许显示多行 */
    }
    .nav-ul li {
      flex: 0 0 auto;
      margin-right: 30px;
      white-space: nowrap;
      cursor: pointer;
      font-size: 24px;
      font-weight: 400;
      text-align: center;
      line-height: 68px;
      color: #7a7a7a;
    }
    .nav-btn {
      position: absolute;
      height: 68px;
      width: 60px;
      right: 0px;
      top: 60%;
      transform: translateY(-50%);
      font-size: 48px;
      // background-color: red;
    }
  }

  .section1 {
    padding: 0 40px;
    p {
      text-align: center;
      font-size: 20px;
      color: #7a7a7a;
    }

    .section-item {
      margin-top: 50px !important;

      > ul {
        display: flex;
        justify-content: flex-start;
        flex-wrap: wrap;
        gap: 30px;

        li {
          // flex: 0 0 calc(33.33%);
          width: 192px;
          height: 240px;
          position: relative;
          background: url(~@A/images/solution/smart-hardware/mobile/appborder.jpg)
            center no-repeat;
          background-size: cover;
          border: 1px solid #979797;
          border-radius: 16px;
          margin-bottom: 60px;

          img {
            width: 50%;
            height: 50%;
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
          }

          img:nth-child(3) {
            position: absolute;
            top: 20px;
            right: 30px;
          }

          p {
            height: 38px;
            width: 100%;
            text-align: center;
            margin: 0 auto;
            font-size: 20px;
            line-height: 38px;
            position: absolute;
            left: 50%;
            bottom: -70px;
            transform: translate(-50%, 0%);
            margin-bottom: 20px;
          }
        }
      }
    }
  }

  .section2 {
    padding: 0 29px;
    ul {
      li {
        min-height: 133px;
        padding: 20px;
        padding-right: 50px;
        padding-bottom: 30px;
        background: linear-gradient(180deg, #ffffff, #ffffff);
        border-radius: 30px;
        box-shadow: 0px -8px 10px 0px rgba(173, 173, 173, 0.11);
        margin-bottom: 20px;
        position: relative;
        h3 {
          font-size: 32px;
          height: 42px;
          color: #000000;
          line-height: 42px;
          font-weight: 500;
          text-align: left;
          margin-bottom: 15px;
        }
        p {
          font-size: 24px;
          font-weight: 400;
          text-align: left;
          color: #666666;
          line-height: 38px;
        }
        img {
          width: 650px;
          height: 470px;
        }
        video {
          margin-top: 20px;
          width: 100%;
        }

        .my-up-btn {
          font-size: 32px;
          position: absolute;
          bottom: 5px;
          left: 50%;
          transform: translateX(-50%);
        }
        .my-down-btn {
          font-size: 32px;
          position: absolute;
          right: 20px;
          top: 66px;
          transform: translateY(-50%);
        }
      }
    }
  }

  .section3 {
    .border {
      margin: 30px auto 0;
      margin-top: 30px;
      width: 650px;
      height: 400px;
      background: url(~@A/images/solution/child-toys/inter-border.png) center
        no-repeat;
      background-size: cover;
      .title {
        text-align: center;
        line-height: 50px;
        height: 50px;
        font-size: 24px;
        color: #fff;
      }
      .desc {
        font-size: 18px;
        text-align: center;
        color: #262b4f;
        margin: 30px 20px;
        margin-top: 50px;
      }
      .sdk-button {
        background: url(~@A/images/solution/child-toys/btn-border.png) center
          no-repeat;
        background-size: cover;
        width: 100%;
        height: 60px;
        font-weight: 400;
        margin: 0 auto;
        text-align: center;
        color: #049bfd;
        line-height: 60px;
        cursor: pointer;
      }
      // .sdk-button:hover {
      //   cursor: pointer;
      // }
      .other-btn {
        display: flex;
        flex-wrap: wrap;
        margin-top: 20px;
        gap: 10px;
        justify-content: center;
        .single {
          width: 200px;
          height: 39px;
          background: #f2f9ff;
          border-radius: 14px;
          font-weight: 400;
          text-align: center;
          color: #049bfd;
          line-height: 39px;
        }
      }
    }
    .border:nth-child(2) {
      .desc {
        text-align: center;
        margin-bottom: 10px;
      }
      .model-item {
        cursor: pointer;
        width: 154px;
        height: 190px;
        background: url(~@A/images/solution/child-toys/model.png) center
          no-repeat;
        background-size: cover;
        margin: 0 auto;
      }
    }
  }
  .section4 {
    padding: 0 29px;
    text-align: center;
    margin-bottom: 20px;
    h3 {
      font-size: 24px;
      color: #7a7a7a;
      margin: 30px auto;
      margin-bottom: 20px;
    }
    .cooperation-btn {
      width: 222px;
      height: 81px;
      background: linear-gradient(90deg, #26bcf6, #1b7bf7 98%);
      border-radius: 52px;
      text-align: center;
      margin: 0 auto;
      font-weight: 600;
      line-height: 81px;
      font-size: 32px;
      color: #f0f0f0;
      margin-top: 60px;
    }
  }

  .section-footer {
    width: 750px;
    background-color: #061930;
    padding: 30px 20px;
    .footer-title {
      color: #c1c1c1;
      display: flex;
      justify-content: space-between;
      align-items: center;
      cursor: pointer;
      font-size: 28px;
      font-weight: bold;
      margin-bottom: 20px;
    }
    .content {
      margin-top: 10px;
      display: none; /* 默认隐藏内容 */
      font-size: 24px;
      color: #7b7e93;
    }

    .content p {
      margin: 5px 0;
      font-size: 24px;
      line-height: 49px;
    }

    .arrow {
      font-size: 26px;
      transition: transform 0.5s ease;
    }

    .arrow.up {
      transform: rotate(180deg);
    }

    .corporation-info {
      margin-top: 30px;
      color: #7b7e93;
      font-size: 24px;
      text-align: left;
      line-height: 33px;
    }
  }
}
</style>
