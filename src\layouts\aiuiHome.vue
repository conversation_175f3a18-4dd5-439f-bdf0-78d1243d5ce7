<template>
  <div class="home-container">
    <portal-target name="destination"> </portal-target>
    <portal-target name="destination2"> </portal-target>
    <portal-target name="destination3"> </portal-target>
    <div
      class="home-main"
      id="home-main-id"
      ref="scroll"
      @scroll="handleScroll"
    >
      <aiui-header :headerFixed="headerFixed" />
      <router-view></router-view>
      <aiui-footer />
      <portal to="destination">
        <feedBackHover />
      </portal>
    </div>
  </div>
</template>

<script>
import aiuiHeader from '../components/aiuiHeader'
import aiuiFooter from '../components/aiuiFooter'
import utils from '../assets/lib/utils.js'
import feedBackHover from '../components/feedBackHover'
export default {
  head: {
    title: 'AIUI开放平台',
    meta: [{ charset: 'utf-8' }],
  },
  data() {
    return {
      scroll: null,
      headerFixed: false,
    }
  },
  components: {
    aiu<PERSON><PERSON>eader,
    aiu<PERSON><PERSON>ooter,
    feedBackHover,
  },
  watch: {
    $route: function (to, from) {
      let self = this
      self.$refs.scroll.scrollTo(0, 0)
    },
  },
  mounted() {},
  methods: {
    handleScroll() {
      var scrollTop =
        this.$refs.scroll.pageYOffset || this.$refs.scroll.scrollTop
      this.headerFixed = scrollTop > 640
    },
  },
}
</script>

<style lang="scss">
@mixin noscroll() {
  &::-webkit-scrollbar {
    width: 0px;
  }
}

@mixin scroll() {
  // 定义滚动条高宽及背景 高宽分别对应横竖滚动条的尺寸
  -webkit-overflow-scrolling: touch;
  over-flow: auto;

  &::-webkit-scrollbar {
    width: 4px;
  }

  // 定义滚动条轨道 内阴影+圆角
  &::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 2px rgba(0, 0, 0, 0.2);
    border-radius: 4px;
    background-color: #ffffff;
  }

  // 定义滑块 内阴影+圆角
  &::-webkit-scrollbar-thumb {
    border-radius: 2px;
    background-color: #ccc;
  }

  //
  &::-webkit-scrollbar-thumb:hover {
    border-radius: 2px;
    background-color: #999;
  }
}

.home-main {
  position: relative;
  height: 100%;
  width: 100%;
  // min-width: 1200px;
  overflow-y: scroll;
  overflow-x: hidden;
  scroll-behavior: smooth;
  @include scroll();
}

.home-container {
  width: 100%;
  height: 100%;
}
</style>
