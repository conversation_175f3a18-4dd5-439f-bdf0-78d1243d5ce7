<template>
  <div class="select-entity-popover">
    <div class="character-select-popover__head">
      <div class="character-select-popover-search">
        <el-input class="search-area" placeholder="搜索设备人设" v-model="searchName" ></el-input>
      </div>
    </div>
    <div class="character-select-popover__body">
      <div class="character-select-popover-list" v-loadmore="scrollLoad" v-loading="loading">
        <div
          class="character-select-popover-item"
          v-for="(item, index) in characterData.list"
          :key="index"
          @click="selectItem(item)"
        >
          <span :title="item.name">{{item.name}}</span>
        </div>
        <div class="el-table__empty-block" v-if="!characterData.list.length">
          <span class="el-table__empty-text">暂无数据</span>
        </div>
      </div>
      <os-divider />
      <div class="character-select-popover-add">
        <i class="ic-r-plus" />
        <a @click="addCharacter">创建设备人设</a>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";

export default {
  data() {
    return {
      searchName: "",
      loading: false,
      characterData: {
        loading: true,
        loadend: false,
        total: 0,
        page: 1,
        size: 20,
        list: []
      },
      debounced: null
    };
  },
  computed: {},
  watch: {
    searchName: function(val, oldVal) {
      this.debounced();
    }
  },
  mounted() {
    this.initData(1);
    this.setDebounce();
  },
  beforeDestroy() {
    this.debounced = null;
  },
  methods: {
    setDebounce() {
      this.debounced = this.$utils.debounce(
        () => {
          this.initData(1);
        },
        500,
        true
      );
    },
   
    initData(page) {
      let self = this;
      this.characterData.loading = true;
      this.loading = true;
      this.$utils.httpGet(
        this.$config.api.STUDIO_CHARACTER_LIST,
        {
          pageIndex: page || this.characterData.page,
          pageSize: this.characterData.size,
          search: this.searchName
        },
        {
          success: res => {
            if (res.data.pageIndex === 1) {
              self.characterData.list = res.data.list;
            } else {
              self.characterData.list = self.characterData.list.concat(
                res.data.list
              );
            }
            self.characterData.total = res.data.count;
            self.characterData.page = res.data.pageIndex;
            self.characterData.size = res.data.pageSize;
            self.characterData.loading = false;
            self.loading = false;
            self.characterData.loadend =
              res.data.pageSize * res.data.pageIndex >= res.data.count;
          },
          error: err => {
            self.loading = false;
          }
        }
      );
    },
    scrollLoad() {
      if (this.characterData.loading || this.characterData.loadend) {
        return;
      }
      this.initData(this.characterData.page + 1);
    },
    selectItem(item) {
      let routeData = this.$router.resolve({
        name: "character",
        params: { characterId: item.id }
      });
      location.href = routeData.href;
    },
    addCharacter() {
      let routeData = this.$router.resolve({
        name: "studio-handle-platform-characters"
      });
      localStorage.setItem("addCharacter", "true");
      window.open(routeData.href, "_blank");
    }
  },
  components: {}
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="scss">
.select-entity-popover {
  position: absolute;
  top: 0;
  left: 0;
  background: #fff;
  width: 264px;
  border-radius: 8px;
  border: 1px solid #ebeef5;
}

.character-select-popover__head {
  display: flex;
  align-items: center;
}
.character-select-popover__body {
  // margin-top: 10px;
}
.character-select-popover-search {
  width: 100%;
  border-bottom: 1px solid $grey2;
}
.character-select-popover-search input {
  border: 0;
  border-radius: 8px;
}
.character-select-popover-list {
  width: 100%;
  height: 236px;
  overflow-y: scroll;
}
.character-select-popover-item {
  height: 36px;
  line-height: 36px;
  padding: 0 16px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: pointer;
  &:hover {
    background: $primary-light-12;
  }
}
.character-select-popover-add {
  height: 48px;
  line-height: 48px;
  padding: 0 16px;
  display: flex;
  i {
    color: $grey4;
  }
  a {
    font-weight: 600;
    padding-left: 6px;
  }
}
</style>
