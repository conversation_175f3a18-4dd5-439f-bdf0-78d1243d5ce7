<template>
  <card>
    <template #title>
      结构化语义配置 &nbsp;
      <el-switch
        v-if="
          !(
            currentScene &&
            currentScene.chainId === 'sos_app' &&
            currentScene.point === '1,13'
          )
        "
        :value="isOn"
        class="mgr16"
        :disabled="!subAccountEditable"
        @change="onSwitchChange"
      ></el-switch
    ></template>
    <div>
      <p class="item-title" style="margin-top: 0">添加工具</p>
      <!-- <repo />
      <agent /> -->
      <skill />

      <!-- <p class="item-title">搜索设置</p>
      <search /> -->
    </div>
  </card>
</template>
<script>
import card from '../components/card'

// import repo from './repo'
// import agent from './agent'
import skill from './skill'

// import prompt from './prompt'
// import search from './search'
import { mapGetters } from 'vuex'

import RECOGNITION_LLM_SEMANTIC_State from '@U/AIUIState/RECOGNITION_LLM_SEMANTIC_State'
import RECOGNITION_LLM_SEMANTIC_SYNTHESIS_State from '@U/AIUIState/RECOGNITION_LLM_SEMANTIC_SYNTHESIS_State'
import RECOGNITION_SEMANTIC_State from '@U/AIUIState/RECOGNITION_SEMANTIC_State'
import RECOGNITION_SEMANTIC_SYNTHESIS_State from '@U/AIUIState/RECOGNITION_SEMANTIC_SYNTHESIS_State'
import RECOGNITION_SEMANTIC_POSTPROCESS_SYNTHESIS_State from '@U/AIUIState/RECOGNITION_SEMANTIC_POSTPROCESS_SYNTHESIS_State'
import RECOGNITION_SEMANTIC_POSTPROCESS_State from '@U/AIUIState/RECOGNITION_SEMANTIC_POSTPROCESS_State'

export default {
  data() {
    return {}
  },
  computed: {
    ...mapGetters({
      currentScene: 'aiuiApp/currentScene',
      app: 'aiuiApp/app',
      context: 'aiuiApp/context',
      subAccountEditable: 'aiuiApp/subAccountEditable',
    }),
    appId() {
      return this.$route.params.appId
    },
    isOn() {
      if (this.currentScene && this.currentScene.point && this.context) {
        const point = this.currentScene.point
        const context = this.context

        if (
          context.isCurrentState(RECOGNITION_SEMANTIC_State) ||
          context.isCurrentState(RECOGNITION_SEMANTIC_POSTPROCESS_State) ||
          context.isCurrentState(RECOGNITION_SEMANTIC_SYNTHESIS_State) ||
          context.isCurrentState(RECOGNITION_LLM_SEMANTIC_State) ||
          context.isCurrentState(RECOGNITION_LLM_SEMANTIC_SYNTHESIS_State) ||
          context.isCurrentState(
            RECOGNITION_SEMANTIC_POSTPROCESS_SYNTHESIS_State
          )
        ) {
          return true
        }
      }
      return false
    },
  },
  methods: {
    onSwitchChange(val) {
      // console.log('onSwitchChange', val)
      // let originPoint = this.currentScene.point
      if (val) {
        // 处理即将打开的情况，可能的情况组合是
        this.$store.dispatch('aiuiApp/addSwitches', '2')
      } else {
        // 处理即将关闭的情况
        // ，如果point中有14（大模型合成），要更改为8
        this.$store.dispatch('aiuiApp/removeSwitch', '2')
      }
    },
  },
  components: {
    card,
    // repo,
    // agent,
    skill,
    // prompt,
    // search,
  },
}
</script>
<style lang="scss" scoped>
@import '../common.scss';
</style>
