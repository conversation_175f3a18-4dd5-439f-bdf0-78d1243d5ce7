.main-content-container {
  padding: 14px 16px;
  max-height: calc(100vh - 70px);
  overflow: auto;
  // background: #f1f3f4;
}

.item-title {
  position: relative;
  font-size: 16px;
  font-weight: 500;
  padding-left: 10px;
  margin-top: 24px;
  margin-bottom: 8px;
  &:before {
    width: 2px;
    height: 16px;
    background-color: $primary;
    content: ' ';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    margin: auto;
  }
}
.item-desc {
  color: #a5a5b8;
  margin-bottom: 18px;
}

.form-menu {
  position: absolute;
  z-index: 100;
  height: min-content;
  top: 16px;
  right: 100px;
  width: 174px;
  background: #ffffff;
  border-radius: 10px;
  padding: 7px 16px;
  box-shadow: 0px 0px 20.2px 0px rgba(194, 194, 194, 0.25);
  .hd1 {
    font-size: 14px;
    font-family: PingFang SC, PingFang SC-500;
    font-weight: 500;
    line-height: 22px;
    margin: 8px 0;
  }
  .hd2 {
    padding: 6px 0 6px 13px;
    font-size: 14px;
    font-family: PingFang SC, PingFang SC-400;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.6);
    margin: 8px 0;
    cursor: pointer;
    border-radius: 3px;
    position: relative;
    &:hover,
    &.active {
      background: #f2f7ff;
      color: $primary;
    }
    &.active {
      &::before {
        content: ' ';
        position: absolute;
        z-index: 1;
        width: 3px;
        height: 40px;
        background-color: $primary;
        left: -13px;
        top: -5px;
      }
    }
  }
}
