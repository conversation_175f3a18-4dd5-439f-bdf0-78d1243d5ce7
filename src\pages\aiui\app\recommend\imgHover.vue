<template>
    <div class="div-img-hover">
      <ul>
        <li>
          <img class="div-img-hover-img" :src="imgData.album.cover">
          <i class="album-text entity-page-form-cancel el-icon-close" style="z-index: 999" @click="confirmDeleteAlbum"></i>
          <p class="album-text" @click="editAlbum">{{"编辑专辑"}}</p>
          <p class="album-text album-text-2" @click="editAlbumContent">{{"编辑内容"}}</p>
          <p class="album-text album-text-3" @click="replaceCover">{{"替换封面"}}</p>
        </li>
        <li>
          <div class="album-text-div" @dblclick="doEdit">
            <template v-if="!edit">
              <span class="entity-name" :title="imgData.album.albumName">{{imgData.album.albumName}}</span>
            </template>
            <div v-else class="mgb16">
              <el-input v-model="imgData.album.albumName"
                        :title="imgData.album.albumName"
                        ref="entityNameInput"
                        class="entity-page-form-input album-text-input"
                        placeholder="请输入专辑名称，不超过20个字符"
                        size="medium"
                        @input.native="doInput"
                        @keydown.enter.native="updateEdit"
                        />
              <i class="entity-page-form-save el-icon-check" @click="updateEdit"/>
              <i class="entity-page-form-cancel el-icon-close" data-action="del" @mousedown="cancelEdit"/>
            </div>
          </div>
        </li>
      </ul>
      <cropperDialog :dialog="dialogCropper" @updateEdit="updateEdit"></cropperDialog>
      <albumContent :dialog="albumContentDialog"></albumContent>
    </div>
</template>

<script>
    import cropperDialog from "./cropperDialog";
    import albumContent from './albumContent';

    export default {
        name: "imgHover",
      components: {
        cropperDialog,
        albumContent
      },
      props: {
          imgData: {
            type: Object,
            default: {
              'album': {},
              'category': {}
            }
          }
      },
      data: function () {
        return {
          albumName: "",
          edit: false,
          originImgData: {},
          dialogCropper: {
            show: false
          },
          albumContentDialog: {
            show: false
          }
        }
      },
      watch: {
        /*'imgData.album': function () {
          this.originImgData = JSON.parse(JSON.stringify(this.imgData))
          console.log(JSON.stringify(this.originImgData))
        }*/
      },
      created() {
        //this.originImgData = JSON.parse(JSON.stringify(this.imgData))
      },
      computed: {
        appId() {
          return this.$route.params.appId
        }
      },
      methods: {
        editAlbumContent(e) {
          this.albumContentDialog = {
            show: true,
            album: this.imgData.album,
            type: this.imgData.album.type
          }
        },
        replaceCover() {
          var  inputObj=document.createElement( 'input' )
          inputObj.setAttribute( 'id' , '_ef' );
          inputObj.setAttribute( 'type' , 'file' );
          inputObj.setAttribute( 'accept' , '.jpg,.png,.bmp,.jpeg,.gif' );
          inputObj.setAttribute( "style" , 'visibility:hidden' );
          document.body.appendChild(inputObj);
          inputObj.click();
          let thiz = this
          inputObj.onchange = function (e) {
            var file = inputObj.files[0];
            const isFormat = file.type === 'image/jpg' || file.type === 'image/jpeg' || file.type==='image/png' || file.type==='image/gif';
            if (!isFormat) {
              thiz.$message.error('图片格式只能是 JPG/JPEG/PNG/GIF 格式!');
              return
            }
            thiz.getBase64(file).then(res => {
              thiz.dialogCropper.img = res
              thiz.dialogCropper.show = true
            })
          }
        },
        getBase64 (file) {
          return new Promise(function (resolve, reject) {
            let reader = new FileReader()
            let imgResult = ''
            reader.readAsDataURL(file)
            reader.onload = function () {
              imgResult = reader.result
            }
            reader.onerror = function (error) {
              reject(error)
            }
            reader.onloadend = function () {
              resolve(imgResult)
            }
          })
        },
        editAlbum () {
          this.$emit('editAlbum', 0, this.imgData.album, this.imgData.category, this.imgData.index_1, this.imgData.index_2)
        },
        updateEdit (cover) {
          let thiz = this
          if (!this.imgData.album.albumName || this.imgData.album.albumName.length > 20 /*|| /[^\u4E00-\u9FA5]/g.test(this.imgData.album.albumName)*/) {
            this.$message.error("长度不超过20个字符")
            return
          }
          this.$utils.httpPost(this.$config.api.RECOMMEND_CONTENT_UPDATE_ALBUM, {
            appid: this.appId,
            id: this.imgData.album.id,
            albumName: this.imgData.album.albumName,
            cover: typeof cover == 'string' ? cover : this.imgData.album.cover
          },{
            success: res => {
              if (res.flag) {
                this.$message.success(res.desc)
                this.edit = false
                this.imgData.album = {
                  ...this.imgData.album,
                  'albumName': res.data.albumName,
                  'cover': res.data.cover
                }
                thiz.$emit('updateAlbumLocal', thiz.imgData.album, thiz.imgData.category)
                //this.$emit('getAlbumOrSongById', this.imgData.category)
              } else {
                this.$message.error(res.desc)
              }
            },
            error: err => {
            }
          })
        },
        cancelEdit () {
          this.edit = false
          this.imgData.album.albumName = this.originImgData.album.albumName
          this.$forceUpdate()
        },
        editEntityBlur (e) {
          e.target.blur()
        },
        confirmDeleteAlbum() {
          const h = this.$createElement
          let thiz = this
          this.$confirm('删除提示', {
            title: '删除提示',
            confirmButtonText: '删除',
            cancelButtonText: '取消',
            type: 'warning',
            message: h('div', {class: 'el-div-question'}, [
              h('p', {class: 'el-div-content'},'删除发布后该内容不再展示给用户，你确认要删除吗？'),
            ]),
          }).then(() => {
            thiz.deleteAlbum()
          })
        },
        deleteAlbum () {
          let thiz = this
          this.$utils.httpGet(this.$config.api.RECOMMEND_CONTENT_DELETE_ALBUM, {
            appid: this.appId,
            albumId: this.imgData.album.id
          },{
            success: res => {
              if (res.flag) {
                this.$message.success(res.desc)
                thiz.$emit('deleteAlbumLocal', thiz.imgData.album, thiz.imgData.category)
                //this.$emit('getAlbumOrSongById', this.imgData.category)
              } else {
                this.$message.error(res.desc)
              }
            },
            error: err => {
            }
          })
        },
        doInput (e) {
          this.imgData.album.albumName = e.target.value
          this.$forceUpdate()
        },
        checkBlank (e) {
          if (e.target.value.length > 20 /*|| /[^\u4E00-\u9FA5]/g.test(e.target.value)*/) {
            this.$message.error("长度不超过20个字符")
            return
          }
        },
        doEdit (e) {
          if (this.imgData.album.albumName && this.edit === false) {
            this.edit = !this.edit
            this.originImgData = JSON.parse(JSON.stringify(this.imgData))
          }
        },
        doBlur (e) {
          this.edit = !this.edit
        }
      }
    }
</script>
<style>
  .entity-page-form-input.album-text-input .el-input__inner{
    padding-left: 6% !important;
    padding-right: 6% !important;
    text-align: center;
  }
</style>

<style scoped lang="scss">

  .mgb16 {
    display: inline-flex;
  }

  .entity-name{
    display: inline-block;
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .entity-edit-btn {
    vertical-align: top;
    margin-left: 9px;
    color: $primary;
    cursor: pointer;
  }
  .entity-page-form-input {
    width: 188px;
  }


  .div-img-hover {
    width: 100%;
    max-width: 100%;
    position: relative;
   /* margin-bottom: 8%;*/
    .album-text-div {
      position: absolute;
      width: 100%;
    }
    > ul > li:nth-of-type(1) {
      position: relative;
      width: 100%;
      &:hover .album-text {
        display: flex;
        width: 100%;
      }
    }
    > ul > li:nth-of-type(2) {
      position: relative;
      /*margin-bottom: 20% !important;*/
      > div > * {
        color: #000000;
        position: absolute;
        padding-top: 12%;
        justify-content: center;
        text-align: center;
        align-items: center;
        width: 100%;
        margin: 0 auto;
      }
    }
    &-img {
      width: 100%;
    }
    .entity-page-form-cancel {
      position: relative;
      top: 0;
      max-width: 15%;
      max-height: 15%;
      min-width: 15%;
      min-height: 15%;
      right: 0;
      border-radius: 50%;
    }
    .album-text {
      &-div {
        background: #FFF !important;
      }
      word-wrap:break-word;
      word-break:normal;
      color: #fff;
      background-color: rgba(0,0,0,.3);
      position: absolute;
      width: 100%;
      height: 28px;
      font-size: 12px;
      margin: 0 auto;
      bottom: 0;
      -webkit-box-orient: vertical;
      -webkit-box-direction: normal;
      -ms-flex-direction: column;
      flex-direction: column;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      -webkit-box-pack: center;
      -ms-flex-pack: center;
      justify-content: center;
      display: none;
      cursor: pointer;
      &:hover {
        background-color: #1784e9;
      }
      &-2 {
        margin-bottom: 28px;
      }
      &-3 {
        margin-bottom: 56px;
      }
    }
  }

</style>
