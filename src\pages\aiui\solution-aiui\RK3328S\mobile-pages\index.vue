<template>
  <div class="main-content">
    <MyHeader> </MyHeader>

    <section class="main-content-banner">
      <div class="banner-text">
        <h2>RK3328 AIUI 评估板开发套件</h2>

        <p class="banner-text-content">
          搭载 AIUI
          全链路语音交互能力，快速完成语音项目方案验证，适用于教育教培，服务机器人等场景
        </p>

        <div class="hor-btn">
          <el-button
            class="banner-text-button"
            @click="toConsole"
            type="primary"
            round
            >合作咨询</el-button
          >
          <el-button class="banner-text-buy" @click="toBuy" round plain
            >立即购买</el-button
          >
        </div>
      </div>
    </section>

    <section class="section-nav">
      <ul class="nav-ul" id="nav-ul">
        <li
          v-for="item in nav_list"
          :key="item.id"
          @click="gotoSelection(item.id)"
        >
          {{ item.name }}
        </li>
      </ul>

      <i
        v-if="nav_list.length > 4"
        :class="[
          'nav-btn',
          nav_flag ? 'el-icon-caret-bottom' : 'el-icon-caret-top',
        ]"
        id="nav-btn"
        @click="handleNavExpand"
      ></i>
    </section>

    <section class="section section1">
      <h2>应用场景</h2>

      <div class="section-item">
        <ul>
          <li v-for="(item, index) in app_scenario" :key="index" class="app">
            <img :src="item.src" :alt="item.alt" />
            <p class="app-text">{{ item.alt }}</p>
          </li>
        </ul>
      </div>
    </section>

    <section class="section section2">
      <h2>产品功能</h2>
      <ul>
        <li v-for="item in product_info" :key="item.index">
          <p class="product-title">{{ item.title }}</p>
          <p class="product-desc">· {{ item.desc }}</p>
        </li>
      </ul>
    </section>

    <section class="section section3">
      <h2>产品图片</h2>
      <div class="entity-pic">
        <div class="product">
          <div class="product-pic"></div>
          <div class="product-text">线性驻极体6麦</div>
        </div>

        <div class="product">
          <div class="product-pic"></div>
          <div class="product-text">线性模拟6硅麦</div>
        </div>

        <div class="product">
          <div class="product-pic"></div>
          <div class="product-text">线性模拟4硅麦</div>
        </div>

        <div class="product">
          <div class="product-pic"></div>
          <div class="product-text">线性驻极体4麦</div>
        </div>

        <div class="product">
          <div class="product-pic"></div>
          <div class="product-text">RK3328评估板</div>
        </div>

        <div class="product-flex">
          <div>
            <div class="product-pic-flex1"></div>
            <div class="product-text">环形驻极体6麦</div>
          </div>
          <div>
            <div class="product-pic-flex2"></div>
            <div class="product-text">模拟6硅麦</div>
          </div>
        </div>
      </div>
    </section>

    <section class="section section4">
      <h2>主板接口说明</h2>
      <div class="explain-content">
        <div class="explain-content-pic"></div>
        <ul class="explain-content-list">
          <li v-for="(item, index) in explain_board_list" :key="index">
            <div class="explain-content-list-title">
              {{ 0 + index + 1 }} - {{ item.title }}
            </div>
            <div class="explain-content-list-text">· {{ item.sub_title }}</div>
          </li>
        </ul>

        <i
          class="el-icon-arrow-down expand-arrow"
          v-if="!expandFlag"
          @click="doBoardExpand"
        ></i>
        <i
          class="el-icon-arrow-up shrink-arrow"
          v-else
          @click="doBoardExpand"
        ></i>
      </div>
    </section>

    <section class="section section5">
      <h2>接线示意图</h2>

      <div class="top-part">
        <div v-for="item in icon_list" :key="item.index">
          <img :src="item.src" alt="" />
          <div class="title">{{ item.name }}</div>
        </div>

        <svg-icon
          class="el-icon-right first-arrow"
          iconClass="right"
        ></svg-icon>
        <i class="el-icon-sort reversal-arrow"></i>
      </div>
      <ul>
        <li>· 录制原始音频给主板</li>
        <li>· 语音处理结果传给上位机</li>
        <li>· 上机位回采信号输入到主板</li>
      </ul>
    </section>

    <section class="section section6">
      <h2>硬件参数</h2>

      <ul>
        <li v-for="(item, index) in hard_list" :key="index" class="item">
          <div class="left">
            <img :src="item.src" />
          </div>
          <div class="right">
            <div class="title">{{ item.name }}</div>
            <span class="content">{{ item.content }}</span>
            <span v-show="item.sub_content" class="sub-content">{{
              item.sub_content
            }}</span>
          </div>
        </li>
      </ul>
    </section>

    <section class="section section7">
      <h2>产品清单</h2>
      <div class="product-goods">
        <ul>
          <li>
            <div class="product-goods-title">硬件</div>
            <div class="product-goods-text">
              ·&nbsp;评估板套件 &nbsp;&nbsp; ·&nbsp;麦克风板 &nbsp;&nbsp;
              ·&nbsp;麦克风排线
            </div>
            <div class="product-goods-text">
              回菜线 &nbsp;&nbsp; ·&nbsp;USB线
            </div>
          </li>
          <li>
            <div class="product-goods-title">软件</div>
            <div class="product-goods-text">
              ·&nbsp;唤醒SDK , 集成前端声学算法AIUI SDK
            </div>
            <div class="product-goods-text">·&nbsp;集成云端交互能力</div>
          </li>
          <li>
            <div class="product-goods-title">服务</div>
            <div class="product-goods-text">·&nbsp;为其两个月的VIP技术支持</div>

            <div class="product-goods-text">·&nbsp;浅定制资源定制支持</div>
          </li>
        </ul>
      </div>
    </section>

    <section class="section section8">
      <h2>开发材料</h2>
      <ul>
        <li
          v-for="item in develop_doc"
          :key="item.index"
          @click="toDoc(item.link)"
        >
          {{ item.name }}
        </li>
      </ul>
    </section>

    <section class="section section-cooperation">
      <div class="cooperation-btn" @click="toConsole">合作咨询</div>
    </section>

    <section class="section section-footer">
      <!-- <aiuiMobileFooter> </aiuiMobileFooter> -->
      <div class="footer-title">
        联系我们
        <span class="arrow" @click="clickFooterArrow">▼</span>
      </div>
      <div class="content">
        <p>联系电话：19955108393</p>
        <p>开发者交流群：617903641</p>
        <p>商务技术支持：<EMAIL></p>
      </div>

      <div class="footer-title">
        友情链接
        <span class="arrow" @click="clickFooterArrow">▼</span>
      </div>
      <div class="content">
        <p>科大讯飞</p>
        <p>讯飞开放平台</p>
      </div>

      <div class="corporation-info">
        ©科大讯飞股份有限公司 皖ICP备05001217号
      </div>
    </section>
  </div>
</template>

<script>
import MyHeader from '@P/aiui/solution-aiui/mobile-header.vue'
export default {
  name: 'AiuiWebIndex',

  components: {
    MyHeader,
  },

  data() {
    return {
      nav_flag: true,

      expandFlag: true,
      nav_list: [
        { name: '应用场景', id: 1 },
        { name: '产品功能', id: 2 },
        { name: '产品图片', id: 3 },
        { name: '主板接口说明', id: 4 },
        { name: '接线示意图', id: 5 },
        { name: '硬件参数', id: 6 },
        { name: '产品清单', id: 7 },
        { name: '开发材料', id: 8 },
      ],

      app_scenario: [
        {
          alt: '人工智能教培教具',
          src: require('../../../../../assets/images/solution/smart-hardware/mobile/apps1.png'),
        },
        {
          alt: '家庭智能服务机器人',
          src: require('../../../../../assets/images/solution/smart-hardware/mobile/apps2.png'),
        },
        {
          alt: '地铁自助服务终端',
          src: require('../../../../../assets/images/solution/smart-hardware/mobile/apps3.png'),
        },
        {
          alt: '智慧会议室',
          src: require('../../../../../assets/images/solution/smart-hardware/mobile/apps4.png'),
        },
      ],

      product_info: [
        { title: '录音', desc: '基于麦克风阵列录制原始音频' },
        { title: '降噪', desc: '基于前端声学算法,可以将外部噪音进行过滤' },
        {
          title: '回声消除',
          desc: '可以介入设备本身的参考信号,屏蔽设备本身音频噪音',
        },
        { title: '声源定位', desc: '可以实现定向拾音,过滤波束外噪音' },
        { title: '唤醒', desc: '集成唤醒SDK,让设备开始交互第一步' },
        { title: '在线语音', desc: '内部集成AIUI SDK可体验全链路在线语音能力' },
      ],

      explain_board_list: [
        { title: '网口', sub_title: '连接网线' },
        { title: 'HDMI', sub_title: '连接电视输入' },
        { title: '复位键', sub_title: '刷机按键' },
        { title: 'USB接口', sub_title: 'USB Host接口' },
        { title: 'USB接口', sub_title: 'USB OTG口(刷机时需外接电源)' },
        { title: '电源', sub_title: 'DC12V2A,  DC5.5-2.1' },
        { title: '电源座子', sub_title: '电源插头PH2.54-2pin' },
        { title: '喇叭', sub_title: '10w 4Ω扬声器,PH2.54-4pin' },
        { title: '耳机口', sub_title: '左右声道立体声输出' },
        { title: '12S', sub_title: 'GPIO口复用12S' },
        { title: 'MIC口', sub_title: '4/6麦麦克风阵列接口' },
        { title: '红外', sub_title: '红外控制' },
        { title: '12C', sub_title: '驱动灯板' },
        {
          title: 'UART1',
          sub_title: '协议串口TTL3.3V波特率:由应用指定, PH2.0-4P',
        },
        {
          title: 'UART2',
          sub_title: '协议串口TTL3.3V波特率:1500000, PH2.0-4P',
        },
      ],

      icon_list: [
        {
          src: require('../../../../../assets/images/solution/smart-hardware/mobile/icon4.png'),
          name: '麦克风阵列',
        },
        {
          src: require('../../../../../assets/images/solution/smart-hardware/mobile/icon1.png'),
          name: '评估板主板',
        },
        {
          src: require('../../../../../assets/images/solution/smart-hardware/mobile/icon3.png'),
          name: '上位机',
        },
      ],

      hard_list: [
        {
          name: 'CPU',
          src: require('../../../../../assets/images/solution/soft-hardware/3328s/yj1.png'),
          content: '四核Cortex-A53',
        },
        {
          name: '主频',
          src: require('../../../../../assets/images/solution/soft-hardware/3328s/yj2.png'),
          content: '1.5GHz',
        },
        {
          name: '操作系统',
          src: require('../../../../../assets/images/solution/soft-hardware/3328s/yj3.png'),
          content: 'Andriod',
        },
        {
          name: '电压',
          src: require('../../../../../assets/images/solution/soft-hardware/3328s/yj4.png'),
          content: 'DC12V',
        },
        {
          name: '电流',
          src: require('../../../../../assets/images/solution/soft-hardware/3328s/yj5.png'),
          content: '典型值0.13A,max0.24A',
        },
        {
          name: '扬声器功率',
          src: require('../../../../../assets/images/solution/soft-hardware/3328s/yj6.png'),
          content: '双10W 4Ω',
        },
        {
          name: '以太网',
          src: require('../../../../../assets/images/solution/soft-hardware/3328s/yj7.png'),
          content: '10/100Mbps',
        },
        {
          name: 'WIFI',
          src: require('../../../../../assets/images/solution/soft-hardware/3328s/yj8.png'),
          content: '2.4G/5G',
        },
        {
          name: '麦克风',
          src: require('../../../../../assets/images/solution/soft-hardware/3328s/yj9.png'),
          content: '默认模拟硅麦',
          sub_content: '(驻极体麦可联系商务提供)',
        },
        {
          name: '灵敏度',
          src: require('../../../../../assets/images/solution/soft-hardware/3328s/yj10.png'),
          content: '-32dB',
        },
        {
          name: '信噪比',
          src: require('../../../../../assets/images/solution/soft-hardware/3328s/yj11.png'),
          content: '驻极体麦74dB,模拟硅麦65dB',
        },
        {
          name: '主板尺寸',
          src: require('../../../../../assets/images/solution/soft-hardware/3328s/yj12.png'),
          content: '126mm*81.4mm',
        },
        {
          name: '工作环境',
          src: require('../../../../../assets/images/solution/soft-hardware/3328s/yj13.png'),
          content: '10~75°,相对湿度≤80%',
        },
      ],

      develop_doc: [
        {
          name: ' • 《RK3328 AIUI评估板开发套件产品白皮书.pdf》',
          link: 'https://aiui-doc.xf-yun.com/project-1/doc-184/',
        },
        {
          name: ' • 《RK3328 AIUI评估板开发套件产品规格书.pdf》',
          link: 'https://aiui-doc.xf-yun.com/project-1/doc-186/',
        },
        {
          name: ' • 《RK3328 AIUI评估板开发套件产品使用手册.pdf》',
          link: 'https://aiui-doc.xf-yun.com/project-1/doc-185/',
        },
        {
          name: ' • 《RK3328 AIUI评估板开发套件开发手册.pdf》',
          link: 'https://aiui-doc.xf-yun.com/project-1/doc-187/',
        },
      ],
    }
  },

  mounted() {},

  methods: {
    gotoSelection(id) {
      const section = document.getElementsByClassName(`section` + id)[0]
      if (section) {
        section.scrollIntoView({ behavior: 'smooth' })
      }
    },

    toDoc(link) {
      window.open(link)
    },

    handleNavExpand() {
      const nav_ul = document.getElementById('nav-ul')
      const navIcon = document.getElementById('nav-btn')

      navIcon.addEventListener('click', () => {
        if (this.nav_flag) {
          nav_ul.classList.add('expanded')
        } else {
          nav_ul.classList.remove('expanded')
        }
        this.nav_flag = !this.nav_flag
      })
    },

    clickFooterArrow() {
      const titles = document.querySelectorAll('.footer-title')
      titles.forEach((title) => {
        title.addEventListener('click', () => {
          const content = title.nextElementSibling
          const arrow = title.querySelector('.arrow')

          if (content.style.display === 'none' || !content.style.display) {
            content.style.display = 'block'
            arrow.classList.add('up')
          } else {
            content.style.display = 'none'
            arrow.classList.remove('up')
          }
        })
      })
    },

    doBoardExpand() {
      document.addEventListener('click', () => {
        const shrink_list =
          document.getElementsByClassName('explain-content')[0]
        if (this.expandFlag) {
          shrink_list.style.maxHeight = '500px'
        } else {
          shrink_list.style.maxHeight = 'none'
        }
        this.expandFlag = !this.expandFlag
      })
    },

    toBuy() {
      window.open('https://www.aifuwus.com/onstage/cmddetail?product_type=4436')
    },

    toConsole() {
      const search = window.location.search
      if (search) {
        window.open(`/solution/apply/12${search}`)
      } else {
        window.open(`/solution/apply/12`)
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.main-content {
  background-color: #f6f7f9;
  width: 750px;
  overflow: hidden;
  &-banner {
    background: url(~@A/images/solution/smart-hardware/mobile/banner2.jpg)
      center no-repeat;
    background-size: cover;
    height: 375px;
    overflow: hidden;
    width: 100%;
  }
  .banner-text {
    margin-top: 64px;
    padding-left: 44px;
    h2 {
      font-size: 48px;
      font-weight: 600;
      color: #000000;
      line-height: 68px;
    }
    p {
      width: 648px;
      font-size: 24px;
      font-weight: 300;
      color: #000000;
      line-height: 40px;
      text-align: left;
    }
    .hor-btn {
      display: flex;
      flex-direction: row;
      justify-content: flex-start;
      margin-top: 40px;
      div:nth-child(2) {
        margin-left: 30px;
      }
    }
  }

  .section {
    h2 {
      font-size: 48px;
      font-weight: 600;
      color: #000000;
      line-height: 68px;
      text-align: center;
      margin: 0 auto;
      margin-top: 50px;
      margin-bottom: 30px;
    }
    p,
    ul {
      margin-bottom: 0;
    }
    max-width: 750px;
    overflow: hidden;
    margin: 0 auto;
    .section-title {
      text-align: center;
      font-size: 36px;
      font-weight: 500;
      color: #444444;
      line-height: 54px;
      .arrow {
        width: 26px;
        height: 24px;
        display: inline-block;
        background-repeat: no-repeat;
        background-size: contain;
      }
      .arrow-left {
        background-position: left;
        background-image: url(~@A/images/solution/smart-hardware/arrow-left.png);
      }
      .arrow-right {
        background-position: right;
        background-image: url(~@A/images/solution/smart-hardware/arrow-right.png);
      }
    }
    .section-tabs {
      margin-top: 60px;
    }
  }

  .section-nav {
    background-color: #ffffff;
    position: relative;
    width: 100%;
    border: 1px solid #ddd;
    padding: 0 30px;
    .nav-ul {
      display: flex;
      justify-content: space-evenly;
      flex-wrap: wrap;
      overflow: hidden; /* 默认隐藏超出部分 */
      max-height: 68px; /* 折叠状态时，只显示一行 */
      transition: max-height 0.3s ease; /* 动画过渡效果 */
    }
    .nav-ul.expanded {
      max-height: 200px; /* 展开状态：允许显示多行 */
    }
    .nav-ul li {
      flex: 0 0 auto;
      margin-right: 30px;
      white-space: nowrap;
      cursor: pointer;
      font-size: 24px;
      font-weight: 400;
      text-align: center;
      line-height: 68px;
      color: #7a7a7a;
    }
    .nav-btn {
      position: absolute;
      height: 68px;
      width: 60px;
      right: 0px;
      top: 60%;
      transform: translateY(-50%);
      font-size: 48px;
      // background-color: red;
    }
  }

  .section1 {
    padding: 0 40px;
    .section-item {
      margin-top: 50px !important;

      > ul {
        display: flex;
        justify-content: flex-start;
        flex-wrap: wrap;
        gap: 30px;

        li {
          // flex: 0 0 calc(33.33%);
          width: 192px;
          height: 240px;
          position: relative;
          background: url(~@A/images/solution/smart-hardware/mobile/appborder.jpg)
            center no-repeat;
          background-size: cover;
          border: 1px solid #979797;
          border-radius: 16px;
          margin-bottom: 60px;

          img {
            width: 100%;
            height: 100%;
            position: absolute;
            right: 0;
            top: 0;
          }

          img:nth-child(3) {
            position: absolute;
            top: 20px;
            right: 30px;
          }

          p {
            height: 38px;
            width: 100%;
            text-align: center;
            margin: 0 auto;
            font-size: 20px;
            line-height: 38px;
            position: absolute;
            left: 50%;
            bottom: -70px;
            transform: translate(-50%, 0%);
            margin-bottom: 20px;
          }
        }
      }
    }
  }

  .section2 {
    padding: 0 26px;
    ul {
      margin-top: 20px;
    }
    li {
      width: 686px;
      // height: 311px;
      background: linear-gradient(180deg, #ffffff, #ffffff);
      border-radius: 31px;
      padding: 26px 56px;
      margin-bottom: 20px;

      .product-title {
        text-align: left;
        height: 42px;
        font-size: 32px;
        margin-bottom: 20px;
      }
      .product-desc {
        font-size: 26px;
        font-weight: 400;
        text-align: left;
        color: #999999;
        line-height: 40px;
      }
    }
  }

  .section3 {
    padding: 0 26px;
    width: 683px;
    h2 {
      font-size: 48px;
      font-weight: 600;
      color: #000000;
      line-height: 68px;
      text-align: center;
      margin: 36px auto;
    }
    .entity-pic {
      width: 683px;
      height: 1342px;
      background: url(~@A/images/solution/smart-hardware/mobile/product_bg.jpg)
        center no-repeat;
      .product {
        margin: 0 auto;
        .product-pic {
          margin: 0 auto;
        }
        .product-text {
          font-size: 24px;
          margin: 0 auto;
          text-align: center;
          color: #ffffff;
          line-height: 42px;
          font-weight: 400;
          margin-bottom: 15px;
        }
      }
      .product:nth-child(1) .product-pic {
        width: 666px;
        height: 99px;
        background: url(~@A/images/solution/smart-hardware/mobile/product_14.png)
          center no-repeat;
        background-size: cover;
      }
      .product:nth-child(2) .product-pic {
        width: 665px;
        height: 97px;
        background: url(~@A/images/solution/smart-hardware/mobile/product_08.png)
          center no-repeat;
        background-size: cover;
      }
      .product:nth-child(3) .product-pic {
        width: 526px;
        height: 96px;
        background: url(~@A/images/solution/smart-hardware/mobile/product_09.png)
          center no-repeat;
        background-size: cover;
      }
      .product:nth-child(4) .product-pic {
        width: 536px;
        height: 99px;
        background: url(~@A/images/solution/smart-hardware/mobile/product_15.png)
          center no-repeat;
        background-size: cover;
      }
      .product:nth-child(5) .product-pic {
        width: 432px;
        height: 281px;
        background: url(~@A/images/solution/smart-hardware/mobile/product_20.png)
          center no-repeat;
        background-size: cover;
      }
      .product-flex {
        width: 100%;
        display: flex;
        justify-content: space-evenly;
        .product-text {
          font-size: 24px;
          margin: 0 auto;
          text-align: center;
          color: #ffffff;
          line-height: 42px;
          font-weight: 400;
          margin-bottom: 15px;
        }
        .product-pic-flex1 {
          background: url(~@A/images/solution/smart-hardware/mobile/product_22.png)
            center no-repeat;
          background-size: cover;
          width: 277px;
          height: 276px;
        }
        .product-pic-flex2 {
          background: url(~@A/images/solution/smart-hardware/mobile/product_23.png)
            center no-repeat;
          background-size: cover;
          width: 275px;
          height: 281px;
        }
      }
    }
  }

  .section4 {
    padding: 0 26px;
    width: 700px;

    .explain-content {
      position: relative;
      width: 100%;
      min-height: 1020px;
      background-color: #fff;
      padding-top: 33px;
      padding-left: 32px;
      padding-right: 43px;
      background: linear-gradient(180deg, #ffffff, #ffffff);
      border: 3px solid #f0f0f0;
      border-radius: 30px;
      box-shadow: 0px 3px 9px 0px rgba(151, 151, 151, 0.06);
      &-pic {
        margin: 0 auto;
        background: url(~@A/images/solution/smart-hardware/mobile/product_spe.png);
        background-size: cover;
        width: 414px;
        height: 365px;
      }

      &-list {
        transition: max-height 0.3s ease;
        li {
          width: 100%;
          height: 124px;
          padding-left: 24px;
          padding-top: 17px;
          box-shadow: 0px -1px 0px 0px #ebebeb inset;
        }
        &-title {
          font-size: 32px;
          line-height: 42px;
          text-align: left;
          color: #000000;
        }
        &-text {
          font-size: 26px;
          font-weight: 400;
          text-align: left;
          color: #999999;
          line-height: 40px;
        }
      }

      .expand-arrow {
        font-size: 40px;
        margin: 0 auto;
        text-align: center;
        position: absolute;
        bottom: -10px;
        left: 50%;
        translate: -50%;
      }
      .shrink-arrow {
        font-size: 40px;
        margin: 0 auto;
        text-align: center;
        position: absolute;
        bottom: 0;
        left: 50%;
        translate: -50%;
      }
    }
  }

  .section5 {
    width: 100%;
    padding: 0 26px;
    background: linear-gradient(180deg, #ffffff, #ffffff);
    border-radius: 31px;
    .top-part {
      padding-left: 30px;
      display: flex;
      justify-content: space-evenly;
      margin-bottom: 20px;
      position: relative;
      div {
        margin-right: 30px;
      }
      img {
        width: 100px;
        height: 100px;
      }
      .title {
        font-size: 28px;
        font-weight: 400;
        text-align: center;
        color: #000000;
        line-height: 42px;
      }
      .first-arrow {
        position: absolute;
        width: 40px;
        height: 40px;
        top: 50px;
        right: 430px;
        font-size: 24px;
      }
      .reversal-arrow {
        position: absolute;
        top: 50px;
        right: 230px;
        font-size: 30px;
        transform-origin: center center;
        transform: rotate(90deg);
      }
    }
    ul {
      margin-left: 30px;
      li {
        font-weight: 400;
        text-align: left;
        color: #999999;
        font-size: 22px;
        margin-bottom: 10px;
      }
    }
  }

  .section6 {
    padding: 0 26px;
    width: 100%;
    position: relative;
    ul {
      width: 100%;
      display: flex;
      flex-wrap: wrap;
      gap: 30px;

      li {
        width: 30%;
        display: flex;
        // align-items: center;
        justify-content: space-evenly;
        margin-top: 36px;

        .left {
          width: 50%;
          img {
            width: 74px;
            height: 74px;
          }
        }

        .right {
          width: 50%;
          .title {
            width: 100%;
            font-size: 18px;
            font-family: PingFang SC, PingFang SC-Semibold;
            font-weight: 600;
            text-align: left;
            color: #262626;
          }

          .content {
            margin-block: 5px;
            width: 100%;
            font-size: 16px;
            font-family: PingFang SC, PingFang SC-Regular;
            font-weight: 400;
            text-align: justifyLeft;
            color: #666666;
          }

          .sub-content {
            display: inline-block;
            font-size: 14px;
            font-family: PingFang SC, PingFang SC-Regular;
            font-weight: 400;
            text-align: justifyLeft;
            color: rgb(167, 167, 167);
          }
        }
      }
    }
  }
  .section7 {
    padding: 0 26px;
    width: 100%;
    margin-top: 20px;
    .product-goods {
      width: 100%;
      background-color: #fff;
      padding-top: 33px;
      padding-left: 32px;
      padding-right: 43px;
      background: linear-gradient(180deg, #ffffff, #ffffff);
      border: 3px solid #f0f0f0;
      border-radius: 30px;
      box-shadow: 0px 3px 9px 0px rgba(151, 151, 151, 0.06);
      li {
        width: 100%;
        height: 150px;
        padding-left: 24px;
        padding-right: 30px;
        padding-top: 17px;
        box-shadow: 0px -1px 0px 0px #ebebeb inset;
      }
      &-title {
        font-size: 32px;
        line-height: 42px;
        text-align: left;
        color: #000000;
      }
      &-text {
        font-size: 26px;
        font-weight: 400;
        text-align: left;
        color: #999999;
        line-height: 40px;
      }
    }
  }
  .section8 {
    padding: 26px;
    width: 100%;
    ul {
      li {
        width: 695px;
        min-height: 94px;
        background: linear-gradient(180deg, #ffffff, #ffffff);
        border-radius: 30px;
        font-size: 26px;
        font-weight: 400;
        text-align: left;
        color: #2470ff;
        line-height: 40px;
        padding: 27px 47px;
        margin-bottom: 20px;
        padding-right: 20px;
      }
    }
  }

  .section-cooperation {
    width: 100%;
    height: 243px;
    .cooperation-btn {
      width: 222px;
      height: 81px;
      background: linear-gradient(90deg, #26bcf6, #1b7bf7 98%);
      border-radius: 52px;
      text-align: center;
      margin: 0 auto;
      font-weight: 600;
      line-height: 81px;
      font-size: 32px;
      color: #f0f0f0;
      margin-top: 60px;
    }
  }

  .section-footer {
    width: 750px;
    background-color: #061930;
    padding: 30px 20px;
    .footer-title {
      color: #c1c1c1;
      display: flex;
      justify-content: space-between;
      align-items: center;
      cursor: pointer;
      font-size: 28px;
      font-weight: bold;
      margin-bottom: 20px;
    }
    .content {
      margin-top: 10px;
      display: none; /* 默认隐藏内容 */
      font-size: 24px;
      color: #7b7e93;
    }

    .content p {
      margin: 5px 0;
      font-size: 24px;
      line-height: 49px;
    }

    .arrow {
      font-size: 26px;
      transition: transform 0.5s ease;
    }

    .arrow.up {
      transform: rotate(180deg);
    }

    .corporation-info {
      margin-top: 30px;
      color: #7b7e93;
      font-size: 24px;
      text-align: left;
      line-height: 33px;
    }
  }
}
</style>
