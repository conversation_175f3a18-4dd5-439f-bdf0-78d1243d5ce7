<template>
  <div
    v-if="utteranceEntityPopover.show"
    v-clickoutside="closePopover"
    v-scrollcb="handleScroll"
    ref="selectEntityPopover"
    class="el-popover el-popper el-popover--plain editor-select-popover"
    :style="popperStyle"
    :x-placement="utteranceEntityPopover.showTop ? 'top-start' : 'bottom'">
    <div>
      <div class="editor-select-popover__head">
        <span class="lump">
          <div
            style="display: inline;line-height: 28px;"
            :style="utteranceColor[selectedText.slotName]"
          >
            <template v-if="selectedText.entityName">
              {{selectedText.slotType === 0 ? "@" : "#"}}{{selectedText.entityName}}
            </template>
          </div>
        </span>
        <el-button size="mini" style="min-width:72px;" @click="changeSlotType">{{type === 0 ? '实体' : '辅助词'}}<i class="ic-r-exchange el-icon--right"></i></el-button>
        <el-button type="text" size="mini" icon="ic-r-plus" style="min-width:46px;" @click="toCreate">创建</el-button>
      </div>
      <div class="editor-select-popover__body">
        <div class="editor-select-popover-search">
          <el-input
            class="search-area"
            ref="searchAreaInput"
            size="medium"
            :placeholder="type === 0 ? '搜索实体' : '搜索辅助词'"
            v-model="searchName">
          </el-input>
        </div>
        <div class="editor-select-popover-list" v-loading="loading">
          <div class="editor-select-popover-item"
            v-for="(item, key) in entityList"
            @click="selectItem(item)">
            <span :class="['txt-ellipsis-nowrap', {'bold': item.type == 2 || item.type == 3 || item.type == 5}]" :title="item.name">{{item.name}}</span>
            <span class="txt-ellipsis-nowrap" style="float: right;" :title="item.value">{{item.value}}</span>
          </div>
          <div class="el-table__empty-block" v-if="!entityList.length">
            <span class="el-table__empty-text">暂无数据</span>
          </div>
        </div>
      </div>
    </div>
    <div x-arrow="" class="popper__arrow" style="left: 20px;"></div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  data () {
    return {
      baseUtterance: {},
      selectedText: {},
      rect: {
        top: 0,
        left: 0,
        width: 0
      },
      baseRectTop: 0,
      type: 0,
      searchName: '',
      entityList: [],
      loading: true,
      debounced: null
    }
  },
  computed: {
    ...mapGetters({
      intention: 'studioSkill/intention',
      utteranceEntityPopover: 'studioSkill/utteranceEntityPopover',
      utteranceColor: 'studioSkill/utteranceColor'
    }),
    popperStyle () {
      let top = 0,
          left = 0
      if (this.rect) {
        if (this.utteranceEntityPopover.showTop) {
          top = this.rect.top - 340
        } else {
          top = this.rect.top + 20
        }
        left = this.rect.left - (56 - this.rect.width) / 2
        return {
          'top': `${top}px`,
          'left': `${left}px`
        }
      } else {
        return {
          'display': `none`
        }
      }
    }
  },
  watch: {
    'utteranceEntityPopover.show': function (val, oldVal) {
      if (val) {
        this.searchName = ''
        this.initData()
      }
    },
    'utteranceEntityPopover.rect': function (val, oldVal) {
      this.rect = JSON.parse(JSON.stringify(this.utteranceEntityPopover.rect))
      this.baseRectTop = JSON.parse(JSON.stringify(this.utteranceEntityPopover.rect)).top
    },
    'searchName': function (val, oldVal) {
      this.debounced()
    }
  },
  created () {

  },
  mounted () {
    this.setDebounce()
  },
  beforeDestroy(){
    this.debounced = null
  },
  methods: {
    setDebounce(){
      this.debounced = this.$utils.debounce(() => { this.getData(1) }, 500, true)
    },
    initData () {
      let self = this
      this.baseUtterance = this.$deepClone(this.utteranceEntityPopover.data.utterance)
      this.selectedText = this.$deepClone(this.utteranceEntityPopover.data.selectedText)
      this.type = this.$deepClone(this.utteranceEntityPopover.data.selectedText.slotType || 0)
      this.getData()
      if (this.selectedText.markId) {
        this.$nextTick( function() {
          self.$refs.searchAreaInput.focus()
        })
      }
    },
    getData () {
      let self = this
      this.loading = true
      this.$utils.httpGet(this.$config.api.STUDIO_SKILL_ALL_ENTITYS, {
        type: this.type,
        businessId: this.intention.businessId,
        search: this.searchName
      }, {
        success: (res) => {
          let arr = []
          if (this.type === 0) {
            arr = [...res.data.used, ...res.data.private, ...res.data.personal, ...res.data.public]
          } else {
            arr = [...res.data.used, ...res.data.auxiliary]
          }
          self.entityList = arr
          self.loading = false
        },
        error: (err) => {
          console.log('page=>>');
          console.log(err);
        }
      })
    },
    changeSlotType () {
      this.type = this.type === 0 ? 1 : 0
      this.getData()
    },
    toCreate () {
      let routeNamePrefix = this.$route.path.includes('/sub/') ? 'sub-' : ''
      let routeNameSuffix = this.type === 0 ? 'studio-handle-platform-entities' : 'studio-handle-platform-auxiliaries'
      let routeName = routeNamePrefix + routeNameSuffix
      let routeData = this.$router.resolve({name: routeName})
      localStorage.setItem('pageHandle', 'create')
      window.open(routeData.href, '_blank')
    },
    handleScroll (e, top) {
      this.rect.top = this.baseRectTop - top
    },
    closePopover () {
      this.$store.dispatch('studioSkill/setUtteranceEntityPopover', {
        show: false
      })
    },
    selectItem (item) {
      if (this.selectedText.markId) {
        this.editMark(item)
      } else {
        if (this.selectedText.rangeStartMark === this.selectedText.rangeEndMark && !this.selectedText.destory) {
          console.log('简单的增加');
          this.addMark(item)
        } else {
          console.log('破坏性修改');
          this.destoryChangeMark(item)
        }
      }
    },
    addMark (item) {
      let self = this
      let marks = this.addChangeMark(this.baseUtterance.mark, this.selectedText.startContainer, item, this.selectedText.startOffset)

      self.updateMarks(marks)
    },
    editMark (item) {
      let self = this
      let data = {
        markId: this.selectedText.markId,
        businessId: this.intention.businessId,
        intentId: this.intention.id,
        slotName: this.selectedText.slotName,
        slotType: 0, // 0是实体，1是辅助词
        entityId: item.id,
        optional: this.selectedText.optional
      }

      this.$utils.httpPost(this.$config.api.STUDIO_INTENT_UTTERANCE_MARK_UPDATE, data, {
        success: (res) => {
          self.$message.success('槽位修改成功')
          self.initSkillQuote()
          self.closePopover()
          self.$emit('change')
        },
        error: (err) => {
          console.log('page=>>');
          console.log(err);
        }
      })
    },
    destoryChangeMark (item) {
      let self = this
      let start = false
      let end = false
      let destoryMarks = []
      let destoryMark = {
        text: ''
      }
      let sort = 0
      let addedDestoryMark = false
      let startOffset = 0 // 划词的startIdx，处于text的第几个
      this.baseUtterance.mark.forEach(function (mark, index) {
        if (!start) {
          // 没到开始的mark
          if (mark.markId === parseInt(self.selectedText.rangeStartMark) || mark.markId === parseInt(self.selectedText.rangeEndMark)) {
            // 到达start的mark
            start = true
            startOffset = destoryMark.text.length + self.selectedText.startOffset
            destoryMark.text += mark.text
          } else {
            // 没到达start的mark
            if (mark.slotName) {
              if (destoryMark.text) {
                // 前面还有text的mark
                destoryMark.sort = sort++
                destoryMarks.push(JSON.parse(JSON.stringify(destoryMark)))
                // 重置destoryMark
                destoryMark.text = ''
              }
              mark.sort = sort++
              destoryMarks.push(mark)
            } else {
              // text的mark放进destoryMark里
              destoryMark.text += mark.text
            }
          }
        } else if (!end) {
          // 没到结束的mark
          destoryMark.text += mark.text
          if (mark.markId === parseInt(self.selectedText.rangeStartMark) || mark.markId === parseInt(self.selectedText.rangeEndMark) || self.selectedText.destory) {
            // 到达结束的mark
            end = true
          }
        } else {
          // 后面的mark
          if (mark.slotName) {
            if (addedDestoryMark) {
              // destoryMark已经添加了，后面的mark直接进
              mark.sort = sort++
              destoryMarks.push(mark)
            } else {
              destoryMark.sort = sort++
              destoryMarks.push(destoryMark)
              addedDestoryMark = true
              mark.sort = sort++
              destoryMarks.push(mark)
            }
          } else {
            // text的mark
            if (addedDestoryMark) {
              // destoryMark 已经添加了，直接进
              mark.sort = sort++
              destoryMarks.push(mark)
            } else {
              destoryMark.text += mark.text
            }
          }
        }
      })
      if (!addedDestoryMark) {
        destoryMark.sort = sort++
        destoryMarks.push(destoryMark)
      }

      let marks = this.addChangeMark(destoryMarks, destoryMark.text, item, startOffset)

      self.updateMarks(marks)

    },
    addChangeMark (defaultMarks, mainText, item, startOffset) {
      let self = this
      let marks = []
      let sort = 0
      let mainTextLength = mainText.length
      let endOffset = startOffset + this.selectedText.text.length - 1
      while (this.$utils.isEng(this.selectedText.text[0]) && startOffset > 0 && this.$utils.isEng(mainText[startOffset - 1])) {
        this.selectedText.text = `${mainText[--startOffset]}${this.selectedText.text}`
      }
      while (this.$utils.isEng(this.selectedText.text[this.selectedText.text.length - 1]) && endOffset < mainTextLength - 1 && this.$utils.isEng(mainText[endOffset + 1])) {
        this.selectedText.text = `${this.selectedText.text}${mainText[++endOffset]}`
      }
      defaultMarks.forEach(function (mark, index) {
        if (mark.slotName) {
          mark.sort = sort++
          marks.push(mark)
        } else if (mark.text && mark.text.trim() === mainText.trim()) {
          // let strSplit = mark.text.split(self.selectedText.text)
          let strSplit = []
          strSplit[0] = mark.text.substr(0, startOffset)
          strSplit[1] = self.selectedText.text
          strSplit[2] = mark.text.substr(self.selectedText.text.length + startOffset)
          if (strSplit[0]) {
            marks.push({
              text: strSplit[0],
              sort: sort++
            })
          }
          marks.push({
            entityId: item.id,
            entityName: item.name,
            slotName: item.name.replace('IFLYTEK.', '').toLowerCase(),
            slotType: 0, // 0是实体，1是辅助词
            optional: 1, // 新增的默认为1
            text: self.selectedText.text,
            sort: sort++
          })
          if (strSplit[2]) {
            marks.push({
              text: strSplit[2],
              sort: sort++
            })
          }
        } else {
          mark.sort = sort++
          marks.push(mark)
        }

      })
      return marks
    },
    updateMarks (marks, item) {
      let self = this
      let data = {
        template: this.baseUtterance.template,
        id: this.baseUtterance.id,
        utterance: this.baseUtterance.utterance,
        businessId: this.intention.businessId,
        intentId: this.intention.id,
        mark: JSON.stringify(marks)
      }

      this.$utils.httpPost(this.$config.api.STUDIO_INTENT_UTTERANCE_MARKS_UPDATE, data, {
        success: (res) => {
          self.$message.success('修改语义成功')
          self.closePopover()
          self.$emit('change')
        },
        error: (err) => {
          console.log('page=>>');
          console.log(err);
        }
      })
    },
    // 重置是否有 引用实体 和 引用辅助词
    initSkillQuote () {
      // this.$store.dispatch('studioSkill/initHasSkillQuote', 0)
      // this.$store.dispatch('studioSkill/initHasSkillQuote', 1)
    }
  },
  components: {

  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="scss">
.editor-select-popover {
  position: fixed;
  width: 318px;
  transform-origin: center top 0px;
  z-index: 2000;
  padding: 0;
  padding-top: 18px;
}

.editor-select-popover__head {
  display: flex;
  align-items: center;
  padding: 0 20px;
}
.editor-select-popover__body {
  margin-top: 10px;
}
.editor-select-popover-search {
  border-top: 1px solid $grey2;
  border-bottom: 1px solid $grey2;
}
.editor-select-popover-search input {
  border: 0;
}
.editor-select-popover-list {
  width: 100%;
  height: 236px;
  overflow-y: scroll;
}
.editor-select-popover-item {
  height: 36px;
  line-height: 36px;
  padding: 0 16px;
  cursor: pointer;
  &:hover {
    background: $primary-light-12;
  }
  span:first-child {
    flex: auto;
    max-width: calc( 100% - 75px );
    margin-right: 10px;
  }
  .bold {
    font-weight: 600;
  }
  span:last-child {
    width: 64px;
    text-align: right;
    color: $grey4;
  }
}
</style>
