<template>
  <el-date-picker
    class="date-range-wrap"
    v-model="timeRange"
    type="daterange"
    range-separator=" ~ "
    :editable="false"
    :clearable="true"
    size="medium"
    start-placeholder="开始日期"
    end-placeholder="结束日期"
    :picker-options="pickerOptions"
  ></el-date-picker>
</template>
<script>
const yesterday = new Date().getTime() - 3600 * 1000 * 24
export default {
  name: 'date-picker',
  data() {
    return {
      timeRange: '',
      startDate: '',
      endDate: '',
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now() - 3600 * 1000 * 24
        },
        shortcuts: [
          {
            text: '昨日',
            onClick(picker) {
              picker.$emit('pick', [yesterday, yesterday])
            },
          },
          {
            text: '本周',
            onClick(picker) {
              const end = yesterday
              const start = new Date()
              start.setTime(yesterday - 3600 * 1000 * 24 * 7)
              picker.$emit('pick', [start, end])
            },
          },
          {
            text: '本月',
            onClick(picker) {
              const end = yesterday
              const start = new Date()
              start.setTime(yesterday - 3600 * 1000 * 24 * 30)
              picker.$emit('pick', [start, end])
            },
          },
        ],
      },
    }
  },
  watch: {
    timeRange(val) {
      if (!!val) {
        this.startDate = this.$utils.dateFormat(val[0], 'yyyy-MM-dd')
        this.endDate = this.$utils.dateFormat(val[1], 'yyyy-MM-dd')
      } else {
        this.startDate = ''
        this.endDate = ''
      }
      this.$emit('setTime', this.startDate, this.endDate)
    },
  },
  created() {
    this.initTime()
  },
  methods: {
    initTime() {
      let start = new Date(),
        end = new Date()
      start.setDate(start.getDate() - 1)
      start.setMonth(start.getMonth() - 1)
      end.setDate(end.getDate() - 1)
      this.startDate = this.$utils.dateFormat(start, 'yyyy-MM-dd')
      this.endDate = this.$utils.dateFormat(end, 'yyyy-MM-dd')
      this.timeRange = [this.startDate, this.endDate]
      // this.$emit('setTime', this.startDate, this.endDate)
    },
  },
}
</script>
<style lang="scss">
.date-range-wrap.el-date-editor--daterange {
  padding: 0;
  width: 262px;
  overflow: hidden;
  .el-icon-date {
    position: absolute;
    right: 10px;
  }
  .el-range-input {
    width: 112px;
    color: $semi-black;
  }
  .el-range-separator {
    width: 20px;
    line-height: 36px;
  }
}
</style>
