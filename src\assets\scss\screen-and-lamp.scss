@media screen and (min-width: 751px) {
  .main-content {
    .m-show {
      display: none;
    }
    &-banner {
      height: 500px;
      overflow: hidden;
      width: 100%;
      .banner-text {
        max-width: 1200px;
        color: #fff;
        height: 100%;
        margin: auto;
        h2 {
          color: #fff;
          padding-top: 160px;
          margin-bottom: 12px;
          font-size: 46px;
          font-weight: 500;
        }
        p {
          font-size: 20px;
        }
        p:nth-of-type(3) {
          margin-bottom: 50px;
        }
      }
    }

    .section {
      max-width: 1200px;
      overflow: hidden;
      margin: 0 auto;
      padding: 50px 0;
      &-title {
        font-size: 34px;
        margin: 15px 0 20px;
        text-align: center;
        p {
          font-size: 18px;
          color: #8c8c8c;
          padding: 8px 0;
        }
      }
    }
    .section:last-of-type {
      //margin-bottom: 50px;
    }

    .section-2 {
      .section-item {
        > ul {
          display: flex;
          justify-content: space-evenly;
          li {
            width: 30%;
            img {
              width: 100%;
            }
          }
        }
      }
    }
    .section-3-1 {
      background: url(~@A/images/solution/digital-screen-lamp/bg2-section-3-1.png)
        center no-repeat;
      background-size: cover;
      height: 500px;
      width: 100%;
      max-width: 100%;
      position: relative;

      .bg {
        background: url(~@A/images/solution/digital-screen-lamp/bg-section-3-1.png)
          center no-repeat;
        background-size: cover;
        height: 400px;
        overflow: hidden;
        padding: 0;
        max-width: 1000px;
        width: 100%;
      }
      .section-hor {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        position: absolute;
        bottom: 21%;
        left: 26%;
        p:nth-child(1) {
          font-size: 16px;
          font-weight: 600;
          text-align: center;
          color: #262626;
        }
        p:nth-child(2) {
          font-size: 16px;
          font-weight: 600;
          text-align: center;
          color: #262626;
          margin-left: 42px;
        }
        p:nth-child(3) {
          font-size: 16px;
          font-weight: 600;
          text-align: center;
          color: #262626;
          margin-left: 64px;
        }
        p:nth-child(4) {
          font-size: 16px;
          font-weight: 600;
          text-align: center;
          color: #262626;
          margin-left: 80px;
        }
      }
      .section-ver {
        position: absolute;
        right: 40px;
        top: 32%;
        display: flex;
        height: 154px;
        flex-direction: column;
        justify-content: space-between;
        p {
          font-size: 14px;
          font-weight: 400;
          text-align: left;
          color: #666666;
        }
      }
    }
    .section-3 {
      background: #f2f5f7;
      max-width: 100%;
      width: 100%;
      .section-item {
        max-width: 1200px;
        margin: 0 auto;
        > ul {
          li {
            display: flex;
            justify-content: center;
            align-items: center;
            .section-item-text {
              width: 45%;
              text-align: left;
              color: #999;
              line-height: 30px;
              padding: 0 60px;
              img {
                width: 60px;
                height: 60px;
              }

              &-title {
                font-size: 28px;
                font-weight: 600;
                text-align: left;
                color: #262626;
                margin-top: 37px;
              }
              p {
                margin-top: 30px;
                font-size: 16px;
                font-weight: 400;
                text-align: left;
                color: #666666;
              }
            }
            .section-item-bg {
              width: 45%;
              height: 100%;
              img {
                width: 100%;
                height: 100%;
                vertical-align: middle;
              }
            }
          }
        }
      }
    }
    .section-4 {
      // padding-bottom: 170px;
      padding-top: 60px;
      max-width: 100%;
      background: url(~@A/images/solution/digital-screen-lamp/bg-section-5.png)
        center no-repeat;
      background-size: cover;
      height: 850px;
      overflow: hidden;
      width: 100%;
      .section-item {
        max-width: 1100px;
        margin: 50px auto 0;
        background: url(~@A/images/solution/digital-screen-lamp/bg-section-4.png)
          center no-repeat;
        background-size: cover;
        overflow: hidden;
        width: 100%;
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        padding: 60px 80px;

        img {
          width: 35%;
          height: 60%;
        }

        .section-item-text {
          flex-direction: column;
          justify-content: center;
          align-items: center;
          width: 100%;
          height: 100%;
          margin-left: 60px;
          p:nth-child(1) {
            font-size: 18px;
            font-weight: 600;
            text-align: left;
            color: #262626;
          }
          p:nth-child(2) {
            font-size: 14px;
            font-weight: 400;
            text-align: left;
            color: #666666;
          }
        }
      }
    }
  }
}

.section-2 {
  .app {
    position: relative;
    .app-text {
      z-index: 3;
      font-size: 16px;
      font-weight: 400;
      text-align: center;
      margin-top: 40px;
      color: #262626;
    }
  }
}
.banner-text-button {
  font-size: 18px;
  text-align: center;
  font-weight: 500;
  width: 240px;
  height: 52px;
  line-height: 52px;
  letter-spacing: 1px;
  border: 1px solid #fff;
  border-radius: 1px;
  color: #fff;
  cursor: pointer;
  transition: 0.6s;
  // &:hover {
  //   color: #002985;
  //   background: #fff;
  //   transition: .3s;
  // }
}
.section-5 {
  .section-item {
    .section5-button {
      color: #fff;
      background: #1784e9;
      width: 195px;
      height: 50px;
      line-height: 50px;
      text-align: center;
      margin: 0 auto;
      cursor: pointer;
      &:hover {
        color: #1784e9;
        border: 1px solid #1784e9;
        background: #fff;
        transition: 0.3s;
      }
    }
  }
}

@media screen and (max-width: 750px) {
  .main-content {
    width: 100%;
    margin-top: 60px;
    .pc-show {
      display: none;
    }
    &-banner {
      height: 460px;
      overflow: hidden;
      width: 100%;
      background-size: cover;
      .banner-text {
        max-width: 1200px;
        color: #fff;
        height: 100%;
        margin: auto;
        padding-left: 10px;
        text-align: center;
        h2 {
          padding-top: 40px;
          font-size: 28px;
          font-weight: 500;
          margin-bottom: 10px;
          color: white;
        }
        p {
          font-size: 14px;
          padding: 0 15px;
        }
        p:nth-of-type(4) {
          margin-bottom: 30px;
        }
        .banner-text-button {
          font-size: 16px;
          width: 160px;
          margin: 0 auto;
        }
      }
    }

    .section {
      padding: 20px 10px;
      &-title {
        font-size: 26px;
        margin: 0 0 35px;
        text-align: center;
        p {
          font-size: 18px;
          color: #8c8c8c;
          padding: 8px 0;
        }
      }
    }
    .section:last-of-type {
      margin-bottom: 50px;
    }

    .section-2 {
      .section-item {
        > ul {
          padding: 0 10px;
          li {
            width: 100%;
            margin-bottom: 20px;
            img {
              width: 100%;
            }
          }
        }
      }
      .awake {
        margin: 0 auto;
        width: 100%;
        display: flex;
        justify-content: center;
      }
    }
    .section-3 {
      background: #f2f5f7;
      width: 100%;
      .section-item {
        width: 100%;
        > ul {
          li {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 10px 0;
            .section-item-text {
              text-align: center;
              color: #7d7d7d;
              padding: 0 10px;
              width: 50%;
              &-title {
                font-size: 18px;
                color: #1b1b1b;
                padding: 10px 0;
              }
              p {
                line-height: 22px;
                font-size: 14px;
              }
            }
            .section-item-bg {
              width: 50%;
              display: flex;
              align-items: center;
              img {
                width: 100%;
              }
            }
          }
        }
      }
    }
    .section-4 {
      padding-bottom: 10px;
      margin-bottom: 20px;
      border-bottom: 1px solid #e4e7ed;
      .section-item {
        > ul {
          display: flex;
          width: 100%;
          padding: 0 40px;
          align-items: center;
          .m-step {
            position: relative;
            padding-right: 60px;
            padding-left: 30px;
            width: 100%;
            .m-big-arrow {
              height: 120px;
              position: absolute;
              right: -40px;
              top: 0;
              img {
                height: 100%;
              }
            }
            &-1,
            &-2 {
              display: flex;
              height: 115px;
              margin-top: -5px;
              > div:nth-of-type(odd) {
                position: relative;
                p {
                  position: absolute;
                  text-align: center;
                  width: 150px;
                  top: 30px;
                  left: 50%;
                  font-size: 14px;
                  transform: translate(-50%, 0);
                  span {
                    font-size: 12px;
                    color: #8c8c8c;
                  }
                }
              }
              .m-step-arrow {
                width: 100%;
                position: relative;
                height: 1px;
                border-bottom: 2px solid #81b8ef;
                margin: 10px 15px 0;
                &-san {
                  display: inline-block;
                  width: 8px;
                  height: 8px;
                  background: transparent;
                  border-left: 2px solid #81b8ef;
                  border-top: 2px solid #81b8ef;
                  // border-radius: 3px;
                  transform: rotate(-45deg) translate(50%, -50%);
                  position: absolute;
                  top: 50%;
                  left: 50%;
                  margin-top: 2px;
                }
              }
            }
            &-1 {
              .m-step-arrow {
                &-san {
                  transform: rotate(135deg) translate(50%, -50%);
                  margin-top: -8px;
                }
              }
            }
            .step-num {
              display: inline-block;
              width: 20px;
              height: 20px;
              border-radius: 50%;
              color: #fff;
              background: #0162fe;
              text-align: center;
              line-height: 20px;
            }
          }
        }
      }
    }

    .section-7 {
      margin: 0 auto;
      padding-left: 0;
      padding-right: 0;
      padding-bottom: 0;
      .section-item {
        > ul {
          flex-direction: column;
          li {
            margin-left: unset !important;
            width: revert !important;
            height: revert !important;
            .section-4-bg {
              width: 100%;
            }
          }
        }
      }
    }
  }
}
