<template>
  <section
    class="main-content-banner"
    :style="{
      backgroundImage: 'url(' + require(`@A/images/${src}`) + ')',
    }"
  >
    <div class="banner-text">
      <h2><slot name="title"></slot></h2>
      <p class="banner-text-content">
        <slot></slot>
      </p>
      <div class="banner-text-button" @click="toConsole">合作咨询</div>
    </div>
  </section>
</template>
<script>
export default {
  data() {
    return {}
  },
  props: {
    src: String,
  },
  methods: {
    toConsole() {
      this.$emit('jump')
    },
  },
}
</script>
<style scoped lang="scss">
.main-content-banner {
  // background: url(~@A/images/solution/wakeup/img_free_wake.png) center
  //   no-repeat;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
  height: 500px;
  overflow: hidden;
  width: 100%;
  .banner-text {
    max-width: 1200px;
    color: #fff;
    height: 100%;
    margin: auto;
    &-button {
      font-size: 16px;
      text-align: center;
      font-weight: 400;
      width: 140px;
      height: 40px;
      line-height: 40px;
      border: 1px solid #fff;
      border-radius: 40px;
      color: #fff;
      cursor: pointer;
      transition: 0.6s;
    }
    h2 {
      color: #fff;
      padding-top: 148px;
      margin-bottom: 29px;
      font-size: 48px;
      font-weight: 500;
      line-height: 48px;
    }
    p {
      font-size: 18px;
      margin-bottom: 74px;
    }

    .banner-text-content {
      width: 570px;
      font-size: 16px;
      font-family: SourceHanSansSC-Regular, SourceHanSansSC;
      font-weight: 400;
      color: rgba(255, 255, 255, 0.86);
      line-height: 30px;
    }
  }
}
</style>
