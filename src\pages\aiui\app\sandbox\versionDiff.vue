<template>
  <el-dialog
    :title="`${appName} 版本对比`"
    :visible.sync="versionDiff.show"
    width="1200px"
    :append-to-body="true"
  >
    <div class="scroll-content" ref="scrollContent">
      <!-- <div :class="{ fixed: fixedItemShow }">
        <div class="diff-header">
          <p class="app-name" :title="appName">{{ appName }}</p>
          <span class="app-name page-title"> 版本对比</span>
          <i
            class="ic-r-cross-thin btn-i-close"
            @click="versionDiff.show = false"
          ></i>
          <scene-list
            class="scene-list-in-header"
            :sceneList="sceneList"
            :showDiff="onlyShowDiff"
            :currentSceneName="currentSceneName"
            @setScene="setScene"
            @setDisplay="setDisplay"
          ></scene-list>
        </div>
      </div> -->
      <!-- <div
        :class="[
          'diff-header diff-header-hide',
          { 'diff-header-block-show': fixedItemShow },
        ]"
      ></div> -->
      <scene-list
        class="scene-wrap"
        :sceneList="sceneList"
        :showDiff="onlyShowDiff"
        :currentSceneName="currentSceneName"
        @setScene="setScene"
        @setDisplay="setDisplay"
      ></scene-list>
      <ul class="diff-module-title-wrap sticky">
        <li class="module-title"></li>
        <li class="module-title">生产配置 (线上{{ number }}版本)</li>
        <li class="module-title">测试配置 (当前配置)</li>
      </ul>

      <!-- <div
        class="diff-module-title-wrap title-wrap-hide"
        :class="{ 'title-wrap-show': fixedItemShow }"
      ></div> -->
      <div
        class="diff-collapse"
        v-loading="loading"
        v-if="versionInfo && Object.keys(versionInfo).length"
      >
        <div
          v-for="(item, key) in versionInfo"
          class="diff-collapse-item"
          :key="key"
        >
          <div
            class="diff-collapse-item-title"
            v-if="item && Object.keys(item).length"
            :class="{
              'diff-collapse-item-title-show':
                collapseActiveList.indexOf(key) !== -1,
            }"
            @click="handleCollapse(item, key)"
          >
            {{ key | appVersionDiff }}
            <i
              class="collapse-ic ic-r-angle-d"
              :class="{
                'is-active': collapseActiveList.indexOf(key) !== -1,
              }"
            ></i>
          </div>

          <div
            class="diff-collapse-item-content"
            v-if="item && Object.keys(item).length"
            :class="{
              'diff-collapse-item-content-show':
                collapseActiveList.indexOf(key) !== -1,
            }"
          >
            <template
              v-if="
                key == 'asr' ||
                key == 'tts' ||
                key == 'appHandle' ||
                key == 'itrans' ||
                key == 'avatar'
              "
            >
              <div
                class="diff-item-wrap"
                v-for="(subItem, subKey) in item"
                :key="subKey"
              >
                <div
                  v-if="subKey == 'platform' || subKey == 'ent'"
                  style="visibility: hidden"
                ></div>
                <template
                  v-else-if="
                    subKey == 'recognizeSensitiveWord' &&
                    (subItem.online || subItem.test)
                  "
                >
                  <div class="diff-item">
                    <p class="left-title">
                      {{ subKey | appVersionDiff }}
                    </p>
                  </div>
                  <ul class="diff-item diff-desc-wrap">
                    <li style="height: auto; min-height: 56px">
                      <div style="flex: 0.5" class="diff-desc-online">
                        <div
                          class="assist-item"
                          v-for="(item, index) in subItem.online"
                        >
                          <span
                            v-if="!item.hasExcel && item.swConfigTypeName"
                            class="diff-desc diff-desc-online"
                            :class="{ 'diff-line': item.diff }"
                          >
                            <p class="assist-config">
                              {{ ++index }}. {{ item.swConfigTypeName }}
                            </p>
                            <p class="assist-desc">
                              {{ `过滤级别` }}:
                              {{ item.level | appVersionDiff }}
                            </p>
                            <p class="assist-desc">
                              {{ `启用状态` }}:
                              {{ item.status | filterLevel }}
                            </p>
                          </span>
                          <span
                            v-else-if="item.hasExcel"
                            class="diff-desc diff-desc-online"
                            :class="{ 'diff-line': item.diff }"
                          >
                            <p class="assist-desc" :title="item.excelName">
                              {{ item.excelName }}
                            </p>
                          </span>
                        </div>
                      </div>
                      <div style="flex: 0.5" class="diff-desc-test">
                        <div
                          class="assist-item"
                          v-for="(item, index) in subItem.test"
                        >
                          <span
                            v-if="!item.hasExcel && item.swConfigTypeName"
                            class="diff-desc diff-desc-test"
                            :class="{ 'diff-line': item.diff }"
                          >
                            <p class="assist-config">
                              {{ ++index }}. {{ item.swConfigTypeName }}
                            </p>
                            <p class="assist-desc">
                              {{ `过滤级别` }}:
                              {{ item.level | appVersionDiff }}
                            </p>
                            <p class="assist-desc">
                              {{ `启用状态` }}:
                              {{ item.status | filterLevel }}
                            </p>
                            <!--<ul>
                                    <li>
                                      敏感词类型: {{item.swConfigTypeName}}
                                    </li>
                                    <li>
                                      过滤级别: {{item.level | appVersionDiff}}
                                    </li>
                                    <li>
                                      状态: {{item.status | filterLevel}}
                                    </li>
                                  </ul>-->
                          </span>
                          <span
                            v-else-if="item.hasExcel"
                            class="diff-desc diff-desc-test"
                            :class="{ 'diff-line': item.diff }"
                          >
                            <p class="assist-desc" :title="item.excelName">
                              {{ item.excelName }}
                            </p>
                          </span>
                        </div>
                      </div>
                    </li>
                  </ul>
                </template>
                <template v-else>
                  <div class="diff-item">
                    <p class="left-title">
                      {{ subKey | appVersionDiff }}
                    </p>
                  </div>
                  <ul class="diff-item diff-desc-wrap">
                    <li style="height: auto; min-height: 56px">
                      <span
                        class="diff-desc diff-desc-online"
                        :class="{ 'diff-line': subItem.diff }"
                      >
                        <template v-if="subKey != 'advancedsetting'">
                          <template v-if="subKey == 'isEncrypt'"
                            >{{ subItem.online == '0' ? '否 ' : '是' }}
                          </template>
                          <template
                            v-if="subKey == 'moreConfig' && subItem.online"
                          >
                            <span
                              >备用链接:
                              {{
                                JSON.parse(subItem.online)[0]['url'] || '-'
                              }}</span
                            ><br />
                            尝试次数:
                            {{ JSON.parse(subItem.online)[0]['reties'] || '-' }}
                          </template>
                          <template v-if="subKey == 'sad'">
                            {{ subItem.online | formatSAD }}
                          </template>
                          <template v-else>{{
                            subItem.online || '—'
                          }}</template>
                        </template>
                        <div v-else style="padding: 15px 0">
                          <p
                            style="line-height: 1.5"
                            v-for="(item, index) of subItem.online"
                            :key="index"
                          >
                            {{ item }}
                          </p>
                        </div>
                      </span>
                      <span class="diff-desc diff-desc-test">
                        <template v-if="subKey != 'advancedsetting'">
                          <template v-if="subKey == 'isEncrypt'"
                            >{{ subItem.test == '0' ? '否 ' : '是' }}
                          </template>
                          <template
                            v-if="subKey == 'moreConfig' && subItem.test"
                          >
                            <span
                              >备用链接:
                              {{
                                JSON.parse(subItem.test)[0]['url'] || '-'
                              }}</span
                            ><br />
                            尝试次数:
                            {{ JSON.parse(subItem.test)[0]['reties'] || '-' }}
                          </template>
                          <template v-if="subKey == 'sad'">
                            {{ subItem.test | formatSAD }}
                          </template>
                          <template v-else>{{ subItem.test || '—' }}</template>
                        </template>
                        <div v-else style="padding: 15px 0">
                          <p
                            style="line-height: 1.5"
                            v-for="(item, index) of subItem.test"
                            :key="index"
                          >
                            {{ item }}
                          </p>
                        </div>
                      </span>
                    </li>
                  </ul>
                </template>
              </div>
            </template>
            <template v-if="key == 'nlp'">
              <div
                class="diff-item-wrap"
                v-for="(subItem, subKey, index) in item"
                :key="index"
              >
                <template v-if="subKey !== 'storeQa'">
                  <template v-if="subKey == 'keyword'">
                    <div class="diff-item">
                      <p class="left-title">
                        {{ subKey | appVersionDiff }}
                      </p>
                    </div>
                    <ul class="diff-item diff-desc-wrap">
                      <li style="height: auto">
                        <span
                          class="diff-desc diff-desc-online"
                          :class="{ 'diff-line': subItem.diff }"
                        >
                          <template v-if="subItem && subItem.online">
                            {{ subItem.online.join('；') }}
                          </template>
                          <template v-else> — </template>
                        </span>
                        <span class="diff-desc diff-desc-test">
                          <template v-if="subItem && subItem.test">
                            {{ subItem.test.join('；') }}
                          </template>
                          <template v-else> — </template>
                        </span>
                      </li>
                    </ul>
                  </template>
                  <template v-else-if="subKey == 'mustAnswer'">
                    <template
                      v-if="
                        (subItem.mustAnswer && subItem.mustAnswer.online) ||
                        (subItem.mustAnswer && subItem.mustAnswer.test)
                      "
                    >
                      <div class="diff-item">
                        <p class="left-title">
                          {{ subKey | appVersionDiff }}
                        </p>
                      </div>
                      <ul class="diff-item diff-desc-wrap">
                        <li style="height: auto">
                          <span
                            class="diff-desc diff-desc-online"
                            :class="{
                              'diff-line':
                                subItem.mustAnswer && subItem.mustAnswer.diff,
                            }"
                          >
                            <template
                              v-if="
                                subItem.mustAnswer &&
                                typeof subItem.mustAnswer.online === 'number'
                              "
                            >
                              {{ subItem.mustAnswer.online ? '开启' : '关闭' }}
                            </template>
                            <template v-else> — </template>
                          </span>
                          <span class="diff-desc diff-desc-test">
                            <template
                              v-if="
                                subItem.mustAnswer &&
                                typeof subItem.mustAnswer.test === 'number'
                              "
                            >
                              {{ subItem.mustAnswer.test ? '开启' : '关闭' }}
                            </template>
                            <template v-else> — </template>
                          </span>
                        </li>
                      </ul>
                    </template>
                  </template>
                  <template v-else-if="subKey == 'qc'">
                    <div class="diff-item">
                      <p class="left-title">
                        {{ subKey | appVersionDiff }}
                      </p>
                    </div>
                    <ul class="diff-item diff-desc-wrap">
                      <li style="height: auto">
                        <span
                          class="diff-desc diff-desc-online"
                          :class="{ 'diff-line': subItem.diff }"
                        >
                          <template v-if="subItem && subItem.online">
                            <p v-if="subItem.online.skillIdentify">
                              QC技能: {{ subItem.online.skillIdentify }}
                              <span
                                style="margin-left: 36px"
                                v-if="subItem.online.outNumber"
                                >QC技能版本号:
                                {{ subItem.online.outNumber }}</span
                              >
                            </p>
                            <p v-if="subItem.online.jsName">
                              后处理: {{ subItem.online.jsName }}
                              <span
                                style="margin-left: 36px"
                                v-if="subItem.online.verNo"
                                >后处理版本号: {{ subItem.online.verNo }}</span
                              >
                            </p>
                          </template>
                          <template v-else> — </template>
                        </span>
                        <span class="diff-desc diff-desc-test">
                          <template v-if="subItem && subItem.test">
                            <p v-if="subItem.test.skillIdentify">
                              QC技能: {{ subItem.test.skillIdentify }}
                              <span
                                style="margin-left: 36px"
                                v-if="subItem.test.outNumber"
                                >QC技能版本号:
                                {{ subItem.test.outNumber }}</span
                              >
                            </p>
                            <p v-if="subItem.test.jsName">
                              后处理: {{ subItem.test.jsName }}
                              <span
                                style="margin-left: 36px"
                                v-if="subItem.test.verNo"
                                >后处理版本号: {{ subItem.test.verNo }}</span
                              >
                            </p>
                          </template>
                          <template v-else> — </template>
                        </span>
                      </li>
                    </ul>
                  </template>
                  <template
                    v-else-if="
                      subKey == 'semanticSensitiveWord' &&
                      (subItem.online || subItem.test)
                    "
                  >
                    <div class="diff-item">
                      <p class="left-title">
                        {{ subKey | appVersionDiff }}
                      </p>
                    </div>
                    <ul
                      class="ul-sensitive diff-item diff-desc-wrap"
                      style="width: calc(100% - 200px)"
                    >
                      <li style="height: auto; min-height: 56px">
                        <div style="flex: 0.5" class="diff-desc-online">
                          <div
                            class="assist-item"
                            v-for="(item, index) in subItem.online"
                          >
                            <span
                              v-if="!item.hasExcel && item.swConfigTypeName"
                              class="diff-desc diff-desc-online"
                              :class="{ 'diff-line': item.diff }"
                            >
                              <p class="assist-config">
                                {{ ++index }}. {{ item.swConfigTypeName }}
                              </p>
                              <p class="assist-desc">
                                {{ `过滤级别` }}:
                                {{ item.level | appVersionDiff }}
                              </p>
                              <p class="assist-desc">
                                {{ `启用状态` }}:
                                {{ item.status | filterLevel }}
                              </p>
                              <p class="assist-desc" v-if="item.reply">
                                {{ `回复语(共${item.reply.length}条)` }}:
                              </p>
                              <p
                                class="assist-desc"
                                v-for="(replyItem, index) in item.reply"
                              >
                                {{ replyItem.label }}
                              </p>
                            </span>
                            <span
                              v-else-if="item.hasExcel"
                              class="diff-desc diff-desc-online"
                              :class="{ 'diff-line': item.diff }"
                            >
                              <p class="assist-desc" :title="item.excelName">
                                {{ item.excelName }}
                              </p>
                            </span>
                          </div>
                        </div>
                        <div style="flex: 0.5" class="diff-desc-test">
                          <div
                            class="assist-item"
                            v-for="(item, index) in subItem.test"
                          >
                            <span
                              v-if="!item.hasExcel && item.swConfigTypeName"
                              class="diff-desc diff-desc-test"
                              :class="{ 'diff-line': item.diff }"
                            >
                              <p class="assist-config">
                                {{ ++index }}. {{ item.swConfigTypeName }}
                              </p>
                              <p class="assist-desc">
                                {{ `过滤级别` }}:
                                {{ item.level | appVersionDiff }}
                              </p>
                              <p class="assist-desc">
                                {{ `启用状态` }}:
                                {{ item.status | filterLevel }}
                              </p>
                              <p class="assist-desc" v-if="item.reply">
                                {{ `回复语(共${item.reply.length}条)` }}:
                              </p>
                              <p
                                class="assist-desc"
                                :title="replyItem.label"
                                v-for="(replyItem, index) in item.reply"
                              >
                                {{ replyItem.label }}
                              </p>
                              <!--<ul>
                                    <li>
                                      敏感词类型: {{item.swConfigTypeName}}
                                    </li>
                                    <li>
                                      过滤级别: {{item.level | appVersionDiff}}
                                    </li>
                                    <li>
                                      状态: {{item.status | filterLevel}}
                                    </li>
                                  </ul>-->
                            </span>
                            <span
                              v-else-if="item.hasExcel"
                              class="diff-desc diff-desc-test"
                              :class="{ 'diff-line': item.diff }"
                            >
                              <p class="assist-desc" :title="item.excelName">
                                {{ item.excelName }}
                              </p>
                            </span>
                          </div>
                        </div>
                      </li>
                    </ul>
                  </template>
                  <template v-else-if="subKey == 'simpleProtocol'">
                    <div class="diff-item">
                      <p class="left-title">精简协议</p>
                    </div>
                    <ul class="diff-item diff-desc-wrap">
                      <li
                        style="height: auto"
                        v-for="(nlpConfig, nlpConfigKey, index) in subItem"
                        :key="index"
                      >
                        <span
                          class="diff-desc diff-desc-online"
                          :class="{ 'diff-line': nlpConfig.diff }"
                          >{{
                            nlpConfig.online === 1
                              ? '开启'
                              : nlpConfig.online === 0
                              ? '关闭'
                              : '—'
                          }}</span
                        >
                        <span class="diff-desc diff-desc-test">{{
                          nlpConfig.test === 1
                            ? '开启'
                            : nlpConfig.test === 0
                            ? '关闭'
                            : '—'
                        }}</span>
                      </li>
                    </ul>
                  </template>
                  <template v-else>
                    <div
                      class="diff-item"
                      :class="{
                        'hidden-border': index == Object.keys(item).length - 1,
                      }"
                    >
                      <p class="left-title">
                        {{ subKey | appVersionDiff }}
                      </p>
                    </div>
                    <ul class="diff-item diff-desc-wrap">
                      <li
                        style="height: auto; border-bottom: none"
                        v-if="
                          subKey !== 'ubotQa' &&
                          subKey !== 'privateQa' &&
                          subKey !== 'deviceProperty'
                        "
                      >
                        <div class="diff-desc diff-desc-online nlp-diff-desc">
                          <template v-if="Object.keys(subItem).length">
                            <div
                              class="npl-conig"
                              :class="{ 'diff-line': nlpConfig.diff }"
                              v-for="(
                                nlpConfig, nlpConfigKey, index
                              ) in subItem"
                              :key="index"
                            >
                              <span v-if="!nlpConfig.online">—</span>
                              <template v-else>
                                <span>{{ nlpConfig.online.zhName }}</span>
                                <span
                                  class="skill-number"
                                  v-if="nlpConfig.online.outNumber"
                                  >v{{ nlpConfig.online.outNumber }}</span
                                >
                                <span
                                  class="source-item"
                                  v-if="
                                    nlpConfig.online.source &&
                                    nlpConfig.online.source.length
                                  "
                                  >信源:
                                  {{ nlpConfig.online.source.join('; ') }}</span
                                >
                              </template>
                            </div>
                          </template>
                          <span class="diff-desc" v-else>—</span>
                        </div>
                        <div class="diff-desc diff-desc-test nlp-diff-desc">
                          <template v-if="Object.keys(subItem).length">
                            <div
                              class="npl-conig"
                              v-for="(
                                nlpConfig, nlpConfigKey, index
                              ) in subItem"
                              :key="index"
                            >
                              <span v-if="!nlpConfig.test">—</span>
                              <template v-else>
                                <span>{{ nlpConfig.test.zhName }}</span>
                                <span
                                  class="skill-number"
                                  v-if="nlpConfig.test.outNumber"
                                  >v{{ nlpConfig.test.outNumber }}</span
                                >
                                <span
                                  class="source-item"
                                  v-if="
                                    nlpConfig.test.source &&
                                    nlpConfig.test.source.length
                                  "
                                  >信源:
                                  {{ nlpConfig.test.source.join('; ') }}</span
                                >
                              </template>
                            </div>
                          </template>
                          <span class="diff-desc" v-else>—</span>
                        </div>
                      </li>
                      <template
                        v-if="
                          Object.keys(subItem).length &&
                          subKey == 'deviceProperty'
                        "
                      >
                        <li
                          style="
                            height: auto;
                            border-bottom: 1px solid #e4e7ed;
                            border-top: none;
                          "
                          v-for="(nlpConfig, nlpConfigKey, index) in subItem"
                          :key="index"
                        >
                          <span
                            class="diff-desc diff-desc-online"
                            :class="{ 'diff-line': nlpConfig.diff }"
                            >{{ nlpConfig.online.name || '—' }}</span
                          >
                          <span class="diff-desc diff-desc-test">{{
                            nlpConfig.test.name || '—'
                          }}</span>
                        </li>
                      </template>
                      <template
                        v-if="
                          !Object.keys(subItem).length &&
                          subKey == 'deviceProperty'
                        "
                      >
                        <li>
                          <span class="diff-desc diff-desc-online">—</span>
                          <span class="diff-desc diff-desc-test">—</span>
                        </li>
                      </template>
                      <template
                        v-if="
                          Object.keys(subItem).length &&
                          (subKey == 'privateQa' || subKey == 'ubotQa')
                        "
                      >
                        <li
                          style="height: auto"
                          v-for="(nlpConfig, nlpConfigKey, index) in subItem"
                          :key="index"
                        >
                          <span
                            class="diff-desc diff-desc-online"
                            :class="{ 'diff-line': nlpConfig.diff }"
                            >{{ nlpConfig.online.name || '—' }}
                            <span
                              class="skill-number"
                              v-if="nlpConfig.online.outNumber"
                              >v{{ nlpConfig.online.outNumber }}</span
                            ></span
                          >
                          <span class="diff-desc diff-desc-test"
                            >{{ nlpConfig.test.name || '—' }}
                            <span
                              class="skill-number"
                              v-if="nlpConfig.test.outNumber"
                              >v{{ nlpConfig.test.outNumber }}</span
                            ></span
                          >
                        </li>
                      </template>
                      <template
                        v-if="
                          !Object.keys(subItem).length &&
                          (subKey == 'privateQa' || subKey == 'ubotQa')
                        "
                      >
                        <li>
                          <span class="diff-desc diff-desc-online">—</span>
                          <span class="diff-desc diff-desc-test">—</span>
                        </li>
                      </template>

                      <!-- <template
                              v-if="
                                Object.keys(subItem).length &&
                                subKey == 'ubotQa'
                              "
                            >
                              <li
                                style="
                                  height: auto;
                                  border-bottom: 1px solid #e4e7ed;
                                "
                                v-for="(
                                  nlpConfig, nlpConfigKey, index
                                ) in subItem"
                                :key="index"
                              >
                                <span
                                  class="diff-desc diff-desc-online"
                                  :class="{ 'diff-line': nlpConfig.diff }"
                                  >{{ nlpConfig.online.name || '—' }}</span
                                >
                                <span class="diff-desc diff-desc-test">{{
                                  nlpConfig.test.name || '—'
                                }}</span>
                              </li>
                            </template>
                            <template
                              v-if="
                                !Object.keys(subItem).length &&
                                subKey == 'ubotQa'
                              "
                            >
                              <li>
                                <span class="diff-desc diff-desc-online"
                                  >—</span
                                >
                                <span class="diff-desc diff-desc-test">—</span>
                              </li>
                            </template> -->
                    </ul>
                  </template>
                </template>
              </div>
            </template>
            <template v-if="key == 'assist'">
              <div class="diff-item-wrap">
                <div class="diff-item">
                  <p class="left-title">兜底配置</p>
                </div>
                <ul
                  class="diff-item diff-desc-wrap"
                  :class="{ 'diff-line': item.diff }"
                >
                  <li style="height: auto">
                    <div
                      class="diff-desc diff-desc-online"
                      style="padding-bottom: 15px"
                      v-if="
                        item.online &&
                        item.online.switch == 'on' &&
                        item.online &&
                        item.online.hasOwnProperty('assistConfig')
                      "
                    >
                      <div
                        v-for="(subItem, subKey) of item.online.assistConfig"
                        :key="subKey"
                      >
                        <div class="assist-item" v-if="subItem.level">
                          <p class="assist-config">
                            {{ subItem.level }}. {{ subItem.serviceName }}
                          </p>
                          <p class="assist-desc" v-if="subItem.source">
                            - {{ subItem.source | appVersionDiff }}
                          </p>
                          <p
                            class="assist-desc"
                            v-if="
                              subItem.service == 'Turing' &&
                              subItem.serviceLabel &&
                              subItem.serviceLabel.length
                            "
                          >
                            - 问答库
                          </p>
                          <p
                            class="assist-desc"
                            v-for="(source, index) of subItem.serviceLabel"
                            :key="index"
                          >
                            <template
                              v-if="
                                source.isUsed != 0 &&
                                source.label != 'LastGuard' &&
                                subItem.service != 'iFlytekVideoSearch'
                              "
                            >
                              <span v-if="subItem.service == 'Turing'"
                                >&nbsp;&nbsp;·&nbsp;&nbsp;{{
                                  source.labelName
                                }}</span
                              >
                              <span v-else
                                >·&nbsp;&nbsp;{{ source.labelName }}</span
                              >
                            </template>
                          </p>
                        </div>
                      </div>
                      <template v-if="onlineHasLastGuard">
                        <p
                          class="assist-desc"
                          v-for="lastGuard of item.online.ensureConfig"
                          :key="lastGuard"
                        >
                          ·&nbsp;&nbsp;{{ lastGuard }}
                        </p>
                      </template>
                    </div>
                    <div v-else class="diff-desc diff-desc-online">—</div>
                    <div
                      class="diff-desc diff-desc-test"
                      style="padding-bottom: 15px"
                      v-if="
                        item.test && item.test.hasOwnProperty('assistConfig')
                      "
                    >
                      <div
                        v-for="(subItem, subKey) of item.test.assistConfig"
                        :key="subKey"
                      >
                        <div class="assist-item" v-if="subItem.level">
                          <p class="assist-config">
                            {{ subItem.level }}. {{ subItem.serviceName }}
                          </p>
                          <p class="assist-desc" v-if="subItem.source">
                            - {{ subItem.source | appVersionDiff }}
                          </p>
                          <p
                            class="assist-desc"
                            v-for="(source, index) of subItem.serviceLabel"
                            :key="index"
                          >
                            <template
                              v-if="
                                source.isUsed != 0 &&
                                source.label != 'LastGuard' &&
                                subItem.service != 'iFlytekVideoSearch'
                              "
                            >
                              <span v-if="subItem.service == 'Turing'"
                                >&nbsp;&nbsp;·&nbsp;&nbsp;{{
                                  source.labelName
                                }}</span
                              >
                              <span v-else
                                >·&nbsp;&nbsp;{{ source.labelName }}</span
                              >
                            </template>
                          </p>
                        </div>
                      </div>
                      <template v-if="testHasLastGuard">
                        <p
                          class="assist-desc"
                          v-for="lastGuard of item.test.ensureConfig"
                          :key="lastGuard"
                        >
                          ·&nbsp;&nbsp;{{ lastGuard }}
                        </p>
                      </template>
                    </div>
                    <div v-else class="diff-desc diff-desc-test">—</div>
                  </li>
                </ul>
              </div>
            </template>
          </div>
        </div>
      </div>
      <div v-else class="no-diff">暂无数据</div>
    </div>
  </el-dialog>
</template>
<script>
import SceneList from './sceneList'
import { HappyScroll } from 'vue-happy-scroll'
import 'vue-happy-scroll/docs/happy-scroll.css'

export default {
  name: 'app-publish',
  props: {
    appName: '',
    versionDiff: {
      type: Object,
      default: () => ({
        show: false,
      }),
    },
    number: '',
  },
  data() {
    return {
      sceneList: [],
      currentScene: {},
      loading: false,
      versionInfo: {},
      oldVersionInfo: {},
      activeNames: ['1'],
      collapseActiveList: [],
      advancedsetting: ['nunum', 'dwa', 'ptt'],
      onlyShowDiff: {
        show: false,
      },
      currentSceneName: {
        name: 'main_box',
      },
      onlineHasLastGuard: false,
      testHasLastGuard: false,
      noDiff: false,
      scrollTop: 0,
      scrollLeft: 0,
      fixedItemShow: false,
    }
  },
  computed: {
    appId() {
      return this.$route.params.appId
    },
  },
  watch: {
    'currentScene.sceneBoxId': function (val, oldVal) {
      if (val && oldVal) {
        this.getVersionDiff()
      }
      this.onlyShowDiff = {
        show: false,
      }
    },
    'versionDiff.show': function (val) {
      if (val) {
        this.getSceneList()
      }
      this.onlyShowDiff = {
        show: false,
      }
      this.currentSceneName = {
        name: 'main_box',
      }
    },
    scrollTop(val) {
      let self = this
      let outHeight =
        self.$refs.versionDiffWrap && self.$refs.versionDiffWrap.clientHeight
      let innerHeight =
        self.$refs.scrollContent && self.$refs.scrollContent.clientHeight
      if (val > 255 && innerHeight - outHeight > 255) {
        self.fixedItemShow = true
      } else {
        self.fixedItemShow = false
      }
    },
  },
  filters: {
    formatSAD(val) {
      if (val) {
        return {
          1: '开启',
          0: '关闭',
        }[val]
      } else {
        return '—'
      }
    },
  },
  methods: {
    getSceneList() {
      let self = this
      this.$utils.httpGet(
        this.$config.api.AIUI_SCENE_LIST,
        {
          appid: this.appId,
        },
        {
          success: (res) => {
            if (res.flag) {
              self.sceneList = res.data
              self.currentScene = Array.prototype.find.call(
                res.data,
                (item) => item.sceneName === 'main'
              )
              self.getVersionDiff()
            } else {
              this.$router.push({ name: 'apps' })
            }
          },
          error: (err) => {
            console.log(err)
          },
        }
      )
    },
    setScene(scene) {
      this.currentScene = scene
      this.scrollTop = 0
      this.currentSceneName = {
        name: this.currentScene.sceneBoxName,
      }
    },
    setDisplay(showDiff) {
      this.onlyShowDiff = showDiff
      if (showDiff.show) {
        this.setShowDiff()
      } else {
        this.versionInfo = this.oldVersionInfo
      }
    },
    getVersionDiff() {
      let self = this
      self.loading = true
      self.collapseActiveList.splice(0)
      let data = {
        appid: self.appId,
        sceneId: self.currentScene.sceneId,
        sceneName: self.currentScene.sceneName,
      }
      let strArr = [
        '· 识别结果添加标点',
        '· 识别结果优先阿拉伯数字',
        '· progressive 流式识别',
      ]
      this.$utils.httpGet(this.$config.api.AIUI_APP_VERSION_DIFF, data, {
        success: (res) => {
          if (res.flag) {
            let newAsr = {}
            let tmp = {},
              tmp2 = {
                diff: false,
                online: [],
                test: [],
              }
            // 对敏感词数据结构的处理
            self.versionInfo = res.data
            self.loading = false
            if (res.data && res.data.hasOwnProperty('asr')) {
              for (let key in res.data.asr) {
                if (self.advancedsetting.indexOf(key) !== -1) {
                  tmp[key] = JSON.parse(JSON.stringify(res.data.asr[key]))
                } else {
                  newAsr[key] = res.data.asr[key]
                }
              }
              for (let key in tmp) {
                if (tmp[key]['diff']) {
                  tmp2['diff'] = true
                }
                if (key == 'nunum') {
                  tmp2.online.push(tmp[key].online != 0 ? strArr[1] : '')
                  tmp2.test.push(tmp[key].test != 0 ? strArr[1] : '')
                }
                if (key == 'ptt') {
                  tmp2.online.push(tmp[key].online != 0 ? strArr[0] : '')
                  tmp2.test.push(tmp[key].test != 0 ? strArr[0] : '')
                }
                if (key == 'dwa') {
                  tmp2.online.push(tmp[key].online != 0 ? strArr[2] : '')
                  tmp2.test.push(tmp[key].test != 0 ? strArr[2] : '')
                }
              }
              newAsr.advancedsetting = tmp2
            }
            if (res.data && res.data.hasOwnProperty('assist')) {
              let { online = [], test = [] } = res.data.assist
              this.onlineHasLastGuard = this.isUsedLastGuard(
                online.assistConfig
              )
              this.testHasLastGuard = this.isUsedLastGuard(test.assistConfig)
            }
            if (res.data && res.data.hasOwnProperty('sensitive')) {
              let { online = [], test = [] } = res.data.sensitive
              this.onlineHasLastGuard = this.isUsedLastGuard(
                online.assistConfig
              )
              this.testHasLastGuard = this.isUsedLastGuard(test.assistConfig)
            }
            res.data.asr = newAsr
            self.oldVersionInfo = JSON.parse(JSON.stringify(res.data))
          } else {
            this.$router.push({ name: 'apps' })
          }
        },
        error: (err) => {
          self.loading = false
          console.log(err)
        },
      })
    },
    pickBy(obj, fn) {
      return Object.keys(obj)
        .filter((k) => fn(obj[k], k))
        .reduce((acc, key) => ((acc[key] = obj[key]), acc), {})
    },
    filterFn() {
      return x.diff
    },
    isUsedLastGuard(val = []) {
      let tmp = []
      tmp = val.filter((item) => {
        return item.service == 'LastGuard' && item.level
      })
      return tmp.length > 0
    },
    setShowDiff() {
      let tmp = {}
      let self = this
      let {
        asr = {},
        assist = {},
        nlp = {},
        itrans = {},
        appHandle = {},
        tts = {},
      } = JSON.parse(JSON.stringify(self.oldVersionInfo))
      asr = self.pickBy(asr, (x) => {
        return x.diff || JSON.stringify(x).indexOf(true) != -1
      })
      let { recognizeSensitiveWord = {} } = asr
      let [test1 = [], online1 = []] = [
        recognizeSensitiveWord.test,
        recognizeSensitiveWord.online,
      ]
      let tmpTest1 = test1.filter((item, index) => {
        return item.diff === true
      })

      let tmpOnline1 = online1.filter((item, index) => {
        return item.diff === true
      })
      if (tmpTest1 !== undefined && tmpTest1.length) {
        recognizeSensitiveWord.test = tmpTest1
      } else {
        delete recognizeSensitiveWord.test
      }
      if (tmpOnline1 !== undefined && tmpOnline1.length) {
        recognizeSensitiveWord.online = tmpOnline1
      } else {
        delete recognizeSensitiveWord.online
      }
      itrans = self.pickBy(itrans, (x) => {
        return x.diff
      })
      appHandle = self.pickBy(appHandle, (x) => {
        return x.diff
      })
      /*// asr 敏感词
      if( Object.keys(recognizeSensitiveWord).length ) {
        asr.recognizeSensitiveWord = recognizeSensitiveWord
      }*/
      //nlp
      let {
        qc = {},
        keyword = {},
        mustAnswer = {},
        privateSkill = {},
        storeSkill = {},
        privateQa = {},
        ubotQa = {},
        deviceProperty = {},
        semanticSensitiveWord = {},
        simpleProtocol = {},
      } = nlp
      nlp = {}
      privateSkill = self.pickBy(privateSkill, (x) => {
        return x.diff
      })
      storeSkill = self.pickBy(storeSkill, (x) => {
        return x.diff
      })
      simpleProtocol = self.pickBy(simpleProtocol, (x) => {
        return x.diff
      })
      privateQa = self.pickBy(privateQa, (x) => {
        return x.diff
      })
      ubotQa = self.pickBy(ubotQa, (x) => {
        return x.diff
      })
      deviceProperty = self.pickBy(deviceProperty, (x) => {
        return x.diff
      })
      let [test = [], online = []] = [
        semanticSensitiveWord.test,
        semanticSensitiveWord.online,
      ]

      let tmpTest = test.filter((item, index) => {
        return item.diff === true
      })

      let tmpOnline = online.filter((item, index) => {
        return item.diff === true
      })

      if (tmpTest !== undefined && tmpTest.length) {
        semanticSensitiveWord.test = tmpTest
      } else {
        delete semanticSensitiveWord.test
      }
      if (tmpOnline !== undefined && tmpOnline.length) {
        semanticSensitiveWord.online = tmpOnline
      } else {
        delete semanticSensitiveWord.online
      }

      if (qc && qc.diff) {
        nlp.qc = qc
      }
      if (keyword && keyword.diff) {
        nlp.keyword = keyword
      }
      if (
        mustAnswer &&
        mustAnswer.hasOwnProperty('mustAnswer') &&
        mustAnswer.mustAnswer.diff
      ) {
        nlp.mustAnswer = mustAnswer
      }
      if (Object.keys(privateSkill).length) {
        nlp.privateSkill = privateSkill
      }
      if (Object.keys(storeSkill).length) {
        nlp.storeSkill = storeSkill
      }
      if (Object.keys(simpleProtocol).length) {
        nlp.simpleProtocol = simpleProtocol
      }
      if (Object.keys(privateQa).length) {
        nlp.privateQa = privateQa
      }
      if (Object.keys(ubotQa).length) {
        nlp.ubotQa = ubotQa
      }
      if (Object.keys(deviceProperty).length) {
        nlp.deviceProperty = deviceProperty
      }
      if (Object.keys(semanticSensitiveWord).length) {
        nlp.semanticSensitiveWord = semanticSensitiveWord
      }
      if (Object.keys(asr).length) {
        tmp.asr = asr
      }
      if (Object.keys(itrans).length) {
        tmp.itrans = itrans
      }
      if (Object.keys(privateQa).length) {
        tmp.appHandle = appHandle
      }
      // if( Object.keys(privateQa).length ) {
      //   tmp.appHandle = appHandle
      // }
      if (Object.keys(nlp).length) {
        tmp.nlp = nlp
      }
      if (assist.diff) {
        tmp.assist = assist
      }
      self.versionInfo = tmp
    },
    handleCollapse(item, key) {
      let index = this.collapseActiveList.indexOf(key)
      if (index === -1) {
        this.collapseActiveList.push(key)
      } else {
        this.collapseActiveList.splice(index, 1)
      }
    },
  },
  components: {
    SceneList,
    HappyScroll,
  },
}
</script>
<style lang="scss" scoped>
.ul-sensitive {
  width: calc(100% - 200px);
  > li > div {
    flex: 0.5;
    max-width: 50%;
  }
}
.assist-item:last-of-type {
  border-bottom: 0px solid #e4e7ed;
}
.assist-item {
  top: -6px;
  border-bottom: 1px solid #e4e7ed;
  padding-top: 0 !important;
  line-height: initial;
  > span {
    width: 100% !important;
    padding: 0 !important;
    padding-bottom: 0 !important;
    margin-bottom: 0 !important;
    position: relative;
    /*padding-top: 0;
      padding-bottom: 0;*/
    > p {
      border-bottom: 1px solid #e4e7ed;
      width: 100%;
      position: relative;
      /*padding-left: 4%;*/
      /*padding: 0;
        bottom: 0;*/
      margin-bottom: 0 !important;
      line-height: 3em;
      display: block;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
}
.version-diff-wrap {
  position: absolute;
  top: 60px;
  width: 100vw;
  left: 0;
  background: $white;
  height: calc(100vh - 60px);
  z-index: 2000;
}
.scroll-container {
  min-width: 1000px;
  width: 100%;
  height: 100%;
  padding: 0 4px;
  white-space: nowrap;
}
.scroll-content {
  // padding: 0 120px;
  // padding: 0 120px;
  // height: calc( 100vh - 60px);
  // height: 100%;
  padding-bottom: 15px;
}

.diff-header {
  position: relative;
  height: 64px;
  line-height: 64px;
}
.app-name {
  display: inline-block;
  margin: 16px 0;
  margin-right: 8px;
  max-width: 200px;
  width: auto;
  text-overflow: ellipsis;
  overflow: hidden;
  font-size: 24px;
  font-weight: 600;
  line-height: 1.33;
}
.page-title {
  font-weight: normal;
}
.btn-i-close {
  position: absolute;
  right: 0;
  top: 10px;
  line-height: 32px;
  font-size: 32px;
  color: $grey4;
  cursor: pointer;
}
.scene-wrap {
  padding: 12px 0 32px;
  border-bottom: 1px solid $grey2;
}

.diff-module-title-wrap,
.diff-item-wrap {
  display: flex;
}

.module-title,
.diff-item {
  flex: 1;
  &:first-child {
    flex: 0 0 200px;
    margin-left: 0;
  }
}

.module-title {
  margin-left: 2px;
  padding: 0 16px;
  height: 64px;
  line-height: 64px;
  font-size: 16px;
  font-weight: 600;
  background-color: $grey2;
}
.diff-item {
  color: $grey5;
  border-bottom: 1px solid $grey2;
  min-height: 56px;
  position: relative;
  &:last-child {
    border-bottom: none;
  }
  &.hidden-border {
    border-bottom: none;
  }
}

.left-title {
  position: absolute;
  top: 40%;
  padding-left: 12px;
}

.diff-desc-wrap {
  margin: 0 auto;
  li {
    display: flex;
    height: 56px;
    line-height: 56px;
    border-bottom: 1px solid $grey2;
    &:last-child {
      border-bottom: none;
    }
    &:first-child {
      border-bottom: 1px solid $grey2;
    }
  }
  .diff-desc {
    flex: auto;
    display: inline-block;
    padding: 0 12px;
    width: 50%;
    color: $semi-black;
  }
  .diff-desc-online {
    background-color: #fbfbfb;
  }
}
.nlp-diff-desc {
  padding-left: 0 !important;
  padding-right: 0 !important;
}

.diff-line {
  border-left: 1px solid $primary;
}

.assist-item {
  /*padding-top: 15px;*/
}
.assist-config {
  line-height: 1.5;
}
.assist-desc {
  padding-left: 17px;
  line-height: 1.5;
}
.npl-conig {
  padding-left: 12px;
  min-height: 56px;
  height: auto;
  border-bottom: 1px solid $grey2;
}
.source-item {
  line-height: 1;
  font-size: 12px;
  color: $grey5;
}

.collapse-ic {
  position: absolute;
  right: 24px;
  color: $grey3;
  cursor: pointer;
  transition: transform 0.5s;
}
.diff-collapse-item-title:hover,
.collapse-ic:hover {
  .collapse-ic:after {
    content: '';
    display: block;
    position: absolute;
    top: 20px;
    right: -8px;
    z-index: -1;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: $grey1;
  }
}

.ic-r-angle-d.is-active {
  transform: rotate(180deg);
}
.diff-collapse {
  margin-bottom: 50px;
  width: 100%;
  border-bottom: 1px solid $grey2;
}
.diff-collapse-item {
  border: 1px solid $grey2;
  border-bottom: 0;
  transition: all 0.2s;
}
.diff-collapse-item-title {
  position: relative;
  margin-top: 16px;
  padding: 0 12px;
  height: 74px;
  line-height: 74px;
  font-size: 20px;
  font-weight: 500;
  cursor: pointer;
  &-show {
    border-bottom: 1px solid $grey2;
  }
}

.diff-collapse-item-content {
  max-height: 0;
  overflow: hidden;
  &-show {
    transition: height 0.2s ease-in;
    // max-height: 900px;
    max-height: unset;
    height: auto;
  }
}

.no-diff {
  line-height: 150px;
  text-align: center;
  border: 1px solid #e4e7ed;
  border-top: none;
}
.skill-number {
  display: inline-block;
  margin-left: 10px;
  width: 55px;
  height: 20px;
  line-height: 20px;
  text-align: center;
  font-size: 12px;
  border-radius: 12px;
  border: solid 0.5px $grey3;
}

//滚动时fixed
.scene-list-in-header {
  position: absolute;
  top: 0px;
  left: 320px;
  display: none;
}

.fixed {
  position: fixed;
  width: calc(100% - 248px);
  background: $white;
  overflow: hidden;
  z-index: 5;
  transition: all 0.05s ease-in-out;
  .scene-list-in-header {
    display: block;
  }
}
.hide {
  opacity: 0;
  transition: all 0.1s;
}

.table-header-fixed {
  padding-top: 16px;
  top: 112px;
}
.diff-header-hide,
.title-wrap-hide {
  height: 64px;
  display: none;
}
.diff-header-block-show,
.title-wrap-show {
  display: block;
}
.sticky {
  position: sticky;
  top: 0;
  z-index: 1;
}
</style>
<style lang="scss">
.os-bottom-in-enter-active {
  height: calc(100% - 124px) !important;
}
.os-bottom-in-leave-active {
  height: calc(100%) !important;
}
.os-bottom-in-enter-active,
.os-bottom-in-leave-active {
  transition: all 1s ease;
}

.os-bottom-in-enter,
.os-bottom-in-leave-to {
  transform: translateY(100%);
}

.happy-scroll-container,
.happy-scroll-content {
  width: 100% !important;
  height: 100% !important;
}
</style>
