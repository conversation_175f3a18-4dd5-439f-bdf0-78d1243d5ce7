// @服务信息
import { utils } from '@U'
import { api } from '@/config'

export default {
  namespaced: true,
  state: {
    plat: 'studio',
    subAccount: false,
    subAccountInfo: null,
    userInfo: {
      email: null,
      mobile: null,
      status: 0,
    },
    messageUnRead: 0,
    showGiftMenu: false,
    baseUrl: '/aiui/web',
  },

  actions: {
    /*setShowGiftMenu ({ state, commit, rootState }) {
      utils.httpGet(api.GIFT_SHOW, {}, {
        noMessage: true,
        noLogin: true,
        success: (res) => {
          commit('setShowGiftMenu', res.data.show)
        },
        error: (err) => {
          commit('setShowGiftMenu', false)
        }
      })
    },*/
    setAccountType({ commit }, data) {
      if (data === 'sub') {
        commit('setSubAccount', true)
        commit('setBaseUrl', '/aiui/subweb')
      } else {
        commit('setSubAccount', false)
        commit('setBaseUrl', '/aiui/web')
      }
    },
    setUserInfo({ state, commit, rootState }, data) {
      let userInfo = data
      window.userDetailInfo = data
      if (userInfo) {
        userInfo.token = utils.getCookie('ssoSessionId')
      }
      commit('setUserInfo', userInfo)
    },
    setSubAccountInfo({ state, commit, rootState }, data) {
      data.subAccount = true
      let subAccountInfo = data
      window.userDetailInfo = data
      if (subAccountInfo) {
        subAccountInfo.token = utils.getCookie('subSessionId')
      }
      commit('setSubAccountInfo', subAccountInfo)
    },
    setMessageUnReadCount({ state, commit, rootState }) {
      utils.httpGet(
        api.USER_MESSAGE_UNREAD_COUNT,
        {},
        {
          noMessage: true,
          noLogin: true,
          success: (res) => {
            if (window.header && window.header.messageUnRead) {
              window.header.messageUnRead = res.data
            }
            commit('setMessageUnReadCount', res.data)
          },
          error: (err) => {
            commit('setMessageUnReadCount', 0)
          },
        }
      )
    },
    setBaseUrl({ state, commit, rootState }, baseUrl) {
      commit('setBaseUrl', baseUrl)
    },
  },

  mutations: {
    setShowGiftMenu(state, showGiftMenu) {
      state.showGiftMenu = showGiftMenu
    },
    setUserInfo(state, userInfo) {
      state.userInfo = userInfo
    },
    setSubAccountInfo(state, subAccountInfo) {
      state.subAccountInfo = subAccountInfo
    },
    setMessageUnReadCount(state, userInfo) {
      state.messageUnRead = userInfo
    },
    setBaseUrl(state, baseUrl) {
      state.baseUrl = baseUrl
    },
    setSubAccount(state, data) {
      state.subAccount = data
    },
  },

  getters: {
    showGiftMenu(state, getters, rootState) {
      return state.showGiftMenu
    },
    userInfo(state, getters, rootState) {
      return state.userInfo
    },
    messageUnRead(state, getters, rootState) {
      return state.messageUnRead
    },
    baseUrl(state, getters, rootState) {
      return state.baseUrl
    },
    subAccount(state) {
      return state.subAccount
    },
    subAccountInfo(state, getters, rootState) {
      return state.subAccountInfo
    },
  },
}
