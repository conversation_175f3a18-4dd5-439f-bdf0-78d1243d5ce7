<template>
  <card>
    <template #title> 其他配置 </template>
    <!-- 最新sos链路 -->
    <llmPostProcessSOS v-if="currentScene && currentScene.sos === true" />

    <!-- 含有交互大模型的后处理 -->
    <llmPostProcess v-if="showLLMPostProcess" />

    <!-- 老aiui4.0后处理 -->
    <postProcess v-if="showPostProcess" />
  </card>
</template>
<script>
//     RECOGNITION: '1',
//     RECOGNITION_SEMANTIC: '1,2',
//     RECOGNITION_SEMANTIC_SYNTHESIS: '1,2,8',
//     RECOGNITION_TRANSLATE: '1,4',
//     RECOGNITION_TRANSLATE_SYNTHESIS: '1,4,8',
//     RECOGNITION_LLM_SEMANTIC: '1,13',
//     RECOGNITION_LLM_SEMANTIC_SYNTHESIS: '1,13,14',
//     RECOGNITION_POSTPROCESS: '1,3',
//     RECOGNITION_SEMANTIC_POSTPROCESS: '1,2,3',
//     RECOGNITION_SYNTHESIS: '1,8'
import RECOGNITION_LLM_SEMANTIC_State from '@U/AIUIState/RECOGNITION_LLM_SEMANTIC_State'
import RECOGNITION_LLM_SEMANTIC_SYNTHESIS_State from '@U/AIUIState/RECOGNITION_LLM_SEMANTIC_SYNTHESIS_State'

import RECOGNITION_State from '@U/AIUIState/RECOGNITION_State'
import RECOGNITION_SEMANTIC_State from '@U/AIUIState/RECOGNITION_SEMANTIC_State'
import RECOGNITION_SEMANTIC_SYNTHESIS_State from '@U/AIUIState/RECOGNITION_SEMANTIC_SYNTHESIS_State'

import RECOGNITION_POSTPROCESS_State from '@U/AIUIState/RECOGNITION_POSTPROCESS_State'
import RECOGNITION_SEMANTIC_POSTPROCESS_State from '@U/AIUIState/RECOGNITION_SEMANTIC_POSTPROCESS_State'
import RECOGNITION_SYNTHESIS_State from '@U/AIUIState/RECOGNITION_SYNTHESIS_State'

import RECOGNITION_TRANSLATE_SYNTHESIS_State from '@U/AIUIState/RECOGNITION_TRANSLATE_SYNTHESIS_State'
import RECOGNITION_TRANSLATE_State from '@U/AIUIState/RECOGNITION_TRANSLATE_State'

import llmPostProcess from './llmPostProcess'
import llmPostProcessSOS from './llmPostProcessSOS'
import postProcess from './postProcess'

import card from '../components/card'
import { mapGetters } from 'vuex'

export default {
  data() {
    return {}
  },
  computed: {
    ...mapGetters({
      currentScene: 'aiuiApp/currentScene',
      appInfo: 'aiuiApp/app',
      context: 'aiuiApp/context',
      subAccountEditable: 'aiuiApp/subAccountEditable',
    }),
    appId() {
      return this.$route.params.appId
    },

    showLLMPostProcess() {
      //  currentScene && currentScene.sceneBoxId && currentScene.sos !== true
      if (
        this.currentScene &&
        this.currentScene.sceneBoxId &&
        this.currentScene.sos !== true &&
        this.context
      ) {
        const point = this.currentScene.point
        const context = this.context
        if (
          context.isCurrentState(RECOGNITION_LLM_SEMANTIC_State) ||
          context.isCurrentState(RECOGNITION_LLM_SEMANTIC_SYNTHESIS_State)
        ) {
          return true
        }
      }
      return false
    },

    //     import RECOGNITION_State from '@U/AIUIState/RECOGNITION_State'
    // import RECOGNITION_SEMANTIC_State from '@U/AIUIState/RECOGNITION_SEMANTIC_State'
    // import RECOGNITION_SEMANTIC_SYNTHESIS_State from '@U/AIUIState/RECOGNITION_SEMANTIC_SYNTHESIS_State'

    // import RECOGNITION_POSTPROCESS_State from '@U/AIUIState/RECOGNITION_POSTPROCESS_State'
    // import RECOGNITION_SEMANTIC_POSTPROCESS_State from '@U/AIUIState/RECOGNITION_SEMANTIC_POSTPROCESS_State'
    // import RECOGNITION_SYNTHESIS_State from '@U/AIUIState/RECOGNITION_SYNTHESIS_State'

    showPostProcess() {
      if (
        this.currentScene &&
        this.currentScene.sceneBoxId &&
        this.currentScene.sos !== true &&
        this.context
      ) {
        const point = this.currentScene.point
        const context = this.context
        if (
          // context.isCurrentState( RECOGNITION_State) ||
          // context.isCurrentState( RECOGNITION_SEMANTIC_State) ||
          // context.isCurrentState( RECOGNITION_SEMANTIC_SYNTHESIS_State) ||
          // context.isCurrentState( RECOGNITION_POSTPROCESS_State) ||
          // context.isCurrentState( RECOGNITION_SEMANTIC_POSTPROCESS_State) ||
          // context.isCurrentState( RECOGNITION_SYNTHESIS_State)
          !context.isCurrentState(RECOGNITION_TRANSLATE_State) &&
          !context.isCurrentState(RECOGNITION_TRANSLATE_SYNTHESIS_State) &&
          !context.isCurrentState(RECOGNITION_LLM_SEMANTIC_State) &&
          !context.isCurrentState(RECOGNITION_LLM_SEMANTIC_SYNTHESIS_State)
        ) {
          return true
        }
      }
      return false
    },

    isOn() {},
  },
  components: {
    llmPostProcess,
    llmPostProcessSOS,
    postProcess,
    card,
  },
}
</script>
<style lang="scss" scoped>
@import '../common.scss';
</style>
