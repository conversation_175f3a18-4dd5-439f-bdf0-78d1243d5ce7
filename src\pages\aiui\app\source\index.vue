<template>
  <div class="content-container">
    <div class="source-statistic-page">
      <p class="p-tips">
        {{ tips }}
        <a @click="copy(`${email}`)">复制</a>
      </p>

      <os-table class="mgb40" :tableData="devicesActivedReport">
        <el-table-column prop="providerName" label="类型" width="96">
        </el-table-column>
        <el-table-column prop="activeLimitCount" label="授权总量" width="96">
        </el-table-column>
        <el-table-column prop="lastAddtime" label="最近授权时间" width="110">
        </el-table-column>
        <el-table-column prop="activeCount" label="使用量" width="96">
        </el-table-column>
        <el-table-column prop="availableCount" label="剩余量" width="96">
        </el-table-column>
        <el-table-column label="操作" width="96">
          <template slot-scope="scope">
            <el-tooltip
              class="item"
              effect="dark"
              content=""
              placement="bottom"
            >
              <div slot="content">
                <p class="p-tips">
                  {{ tipsHover }}
                  <span style="color: #ffa400">{{ email }}</span>
                  <a @click="copy(`${email}`)" style="cursor: pointer">复制</a>
                </p>
              </div>
              <a @click="promoteAuthorization">提升授权量</a>
            </el-tooltip>
          </template>
        </el-table-column>
      </os-table>

      <div>
        <ul class="skill-tab">
          <li
            :class="{ active: search.active === item.provider }"
            @click="search.active = item.provider"
            v-for="(item, index) in paidSources"
          >
            {{ item.providerName }}
          </li>
          <span>&nbsp;</span>
        </ul>
      </div>

      <div class="online-device" style="width: 100%; margin: 0 auto">
        <el-row class="online-device-header" style="width: 100%">
          <el-col :span="9" class="online-device-header-item">
            <div class="grid-content">
              <span>查询SN</span>
              <el-input
                class="search-input"
                v-model="search.serialNumber"
                placeholder="请输入SN查询"
                style="width: calc(100% - 80px)"
                size="small"
                clearable
                @keyup.native.enter="getSearchResult"
              >
              </el-input>
            </div>
          </el-col>
          <el-col :span="11" class="online-device-header-item">
            <div class="grid-content" style="display: inline-block">
              <date-range @setTime="setTime"></date-range>
            </div>
          </el-col>
          <el-col :span="10" class="online-device-header-item">
            <div
              class="grid-content"
              style="display: inline-flex; align-items: center"
            >
              <span>使用状态</span>
              <el-select
                size="small"
                style="width: 60%"
                v-model="search.status"
                @change="onChangeStatus"
                placeholder="请选择"
                clearable
              >
                <el-option
                  v-for="(item, index) in statusArr"
                  :key="index"
                  :value="item.value"
                  :label="item.label"
                >
                </el-option>
              </el-select>
            </div>
          </el-col>
          <el-col :span="5" class="online-device-header-item">
            <div class="grid-content" style="display: inline-flex">
              <el-button
                class="bt-source"
                type="primary"
                size="small"
                @click="getSearchResult"
                :disabled="!search.active"
                :loading="tableData.loading"
                >查询</el-button
              >
              <el-button
                class="bt-source"
                type="primary"
                size="small"
                @click="exportSnList"
                :disabled="!search.active"
                :loading="downloading"
                >导出</el-button
              >
            </div>
          </el-col>
        </el-row>
        <div style="color: #ff6300">
          共搜索到 {{ tableData.total }} 个设备信息
        </div>
        <os-table
          class="app-list-table"
          :tableData="tableData"
          style="margin-bottom: 56px"
          @change="getDeviceList"
        >
          <el-table-column prop="serialNumber" label="设备信息">
          </el-table-column>
          <el-table-column prop="statusDesc" label="当前状态">
          </el-table-column>
          <el-table-column label="激活时间">
            <template slot-scope="scope">
              {{ $utils.dateFormat(scope.row.activeTime) }}
            </template>
          </el-table-column>
          <el-table-column label="到期时间">
            <template slot-scope="scope">
              {{ $utils.dateFormat(scope.row.expiredTime) }}
            </template>
          </el-table-column>
        </os-table>
      </div>
    </div>
  </div>
</template>

<script>
import dateRange from '../dateRange'
export default {
  name: 'index',
  components: {
    dateRange,
  },
  data() {
    return {
      pageOptions: {
        title: '信源统计',
        loading: false,
        returnBtn: false,
      },
      email: '<EMAIL>',
      tips: '提示： 收费信源授权请邮件联系商务咨询  <EMAIL>',
      tipsHover: '提示： 收费信源授权请邮件联系商务咨询  ',
      devicesActivedReport: {
        total: 0,
        list: [],
      },
      paidSources: [],
      tableData: {
        loading: false,
        total: 0,
        page: 1,
        size: 10,
        list: [],
        pages: 0,
        showPaginationForever: false,
      },
      search: {
        active: '',
        serialNumber: '', // 设备号，模糊查询
        activeTimeStart: '', // 设备激活开始时间，毫秒
        activeTimeEnd: '', // 设备激活截止时间，毫秒
        timeChanged: false,
        status: '', // 设备状态，1：已激活 2：已过期
        pageindex: 1, // 页码
        pagesize: 10, // 每页大小
      },
      statusArr: [
        {
          value: 1,
          label: '已激活',
        },
        {
          value: 2,
          label: '已过期',
        },
      ],
      downloading: false,
    }
  },
  computed: {
    appId() {
      return this.$route.params.appId
    },
  },
  created() {
    this.getDevicesActivedReport()
    this.getPaidSource()
  },
  watch: {
    'search.active': function () {
      this.getSearchResult()
    },
  },
  methods: {
    copy(value) {
      this.$utils.copyClipboard(value)
      this.$message.success('已复制到剪切板')
    },
    promoteAuthorization() {},
    getDevicesActivedReport() {
      this.$utils.httpGet(
        this.$config.api.SOURCE_STATISTIC_DEVICEREPORT,
        {
          appid: this.appId,
        },
        {
          success: (res) => {
            if (res.flag) {
              if (res.data) {
                this.devicesActivedReport.list = res.data
                this.devicesActivedReport.total =
                  this.devicesActivedReport.list.length
              }
            } else {
              this.$message.error(res.desc)
            }
          },
        }
      )
    },
    getSearchResult() {
      this.getDeviceList(1)
    },
    getPaidSource() {
      this.$utils.httpGet(
        this.$config.api.SOURCE_STATISTIC_PAIDSOURCE,
        {
          appid: this.appId,
        },
        {
          success: (res) => {
            if (res.flag) {
              if (!!res.data && !!res.data.length) {
                this.paidSources = res.data
                this.paidSources.forEach((item, index) => {
                  if (index === 0) {
                    if (item.provider) {
                      this.search.active = item.provider
                      return
                    }
                  }
                })
              }
            } else {
              this.$message.error(res.desc)
            }
          },
        }
      )
    },
    exportSnList() {
      if (this.downloading) {
        this.$message.warning('操作过快，请稍后再试')
      } else {
        this.downloading = true
        if (!this.appId) {
          this.$message.error('appid不能为空！')
          return
        }
        if (!this.search.active) {
          this.$message.error('提供商code不能为空！')
          return
        }
        let data = {
          appid: this.appId,
          provider: this.search.active,
          pageindex: this.search.pageindex,
          pagesize: this.search.pagesize,
        }

        if (!!this.search.serialNumber) {
          data = {
            ...data,
            serialNumber: this.search.serialNumber,
          }
        }
        if (!!this.search.activeTimeStart) {
          data = {
            ...data,
            activeTimeStart: new Date(this.search.activeTimeStart).valueOf(),
          }
        }
        if (!!this.search.activeTimeEnd) {
          data = {
            ...data,
            activeTimeEnd: new Date(this.search.activeTimeEnd).getTime(),
          }
        }
        if (!!this.search.status && this.search.status != '') {
          data = {
            ...data,
            status: this.search.status,
          }
        }
        this.$utils.postopen(
          this.$config.api.SOURCE_STATISTIC_ACTIVEEXPORT,
          data
        )
        setTimeout(() => {
          this.downloading = false
        }, 5000)
      }
    },
    getDeviceList(page) {
      if (!this.appId) {
        this.$message.error('appid不能为空！')
        return
      }
      if (!this.search.active) {
        this.$message.error('提供商code不能为空！')
        return
      }

      if (!!page) {
        this.search.pageindex = page
      }
      this.tableData.loading = true

      let data = {
        appid: this.appId,
        provider: this.search.active,
        pageindex: this.search.pageindex,
        pagesize: this.search.pagesize,
      }

      if (!!this.search.serialNumber) {
        data = {
          ...data,
          serialNumber: this.search.serialNumber,
        }
      }
      if (!!this.search.activeTimeStart) {
        data = {
          ...data,
          activeTimeStart: new Date(this.search.activeTimeStart).valueOf(),
        }
      }
      if (!!this.search.activeTimeEnd) {
        data = {
          ...data,
          activeTimeEnd: new Date(this.search.activeTimeEnd).getTime(),
        }
      }
      if (!!this.search.status && this.search.status != '') {
        data = {
          ...data,
          status: this.search.status,
        }
      }

      this.$utils.httpGet(
        this.$config.api.SOURCE_STATISTIC_ACTIVEDETAIL,
        data,
        {
          success: (res) => {
            this.tableData.loading = false
            if (res.flag) {
              if (res.data) {
                this.tableData.list = res.data.list
                this.tableData.total = res.data.total
                this.tableData.page = res.data.pageindex
                this.tableData.size = res.data.pagesize
                this.tableData.pages = res.data.pages
              }
            } else {
              this.$message.error(res.desc)
            }
          },
          error: (error) => {
            this.tableData.loading = false
            this.$message.error(error)
          },
        }
      )
    },
    setTime(start, end) {
      this.search.activeTimeStart = start
      this.search.activeTimeEnd = end
      this.search.timeChanged = true
    },
    getQaPair(page) {
      this.search.pageindex = page
      this.getDeviceList()
    },
    handleSizeChange(pagesize) {
      this.search.pagesize = pagesize
      this.getDeviceList()
    },
    onChangeStatus(status) {
      this.search.status = status
    },
  },
}
</script>

<style scoped lang="scss">
@import '../config/main/common.scss';

.bt-source {
  max-width: 4rem;
  min-width: 4rem;
}
.online-device-header {
  display: inline-flex;
}
.source-statistic-page {
  > p:nth-of-type(1),
  .p-tips {
    color: $warning;
    font-size: 1.3rem;
    margin-top: 4%;
    > a {
      left: 2%;
      position: relative;
      &:hover {
        cursor: pointer;
      }
    }
  }
  .skill-tab {
    // width: 465px;
    height: 40px;
    border-bottom: 1px solid $grey2;

    li {
      position: relative;
      left: 2%;
    }
    li:first-of-type {
      position: relative;
      left: unset;
    }

    li {
      display: inline-block;
      height: 40px;
      line-height: 40px;
      cursor: pointer;
      font-size: 1.3rem;
      &:hover {
        color: $primary;
      }
    }
    .active {
      color: $primary;
      border-bottom: 1px solid $primary;
    }
    span {
      margin: 0 8px;
      color: $grey4;
    }
  }
}
</style>
