<template>
  <div style="margin-top: 16px">
    <el-card
      class="box-card"
      shadow="never"
      :body-style="{ padding: '10 20', background: '#F9F9F9' }"
    >
      <div slot="header" class="clearfix">
        <span class="header-title">智能体</span>
        <div style="float: right">
          <a @click="add"
            ><i class="el-icon-circle-plus-outline"></i>&nbsp;添加</a
          >
        </div>
      </div>
      <div class="text">
        <div class="description" v-if="agentDataUsed.length > 0">
          已配置
          <span v-if="agentDataUsed.length > 20">
            <span v-for="(v, i) in Array(20)" :key="i" class="skill-cell">
              {{ agentDataUsed[i].pluginName }}<span v-if="i < 20 - 1">、</span>
            </span>
            等
            {{ agentDataUsed.length }}
            个智能体
          </span>
          <span v-else>
            <span v-for="(v, i) in agentDataUsed" :key="i" class="skill-cell">
              {{ v.pluginName
              }}<span v-if="i < agentDataUsed.length - 1">、</span>
            </span>
          </span>
          共<span style="">{{ agentDataUsed.length }}</span
          >个智能体
        </div>
        <div class="description" v-else>暂未配置智能体</div>
      </div>
    </el-card>

    <addDialog
      :dialog="dialogAdd"
      :appId="appId"
      :currentScene="currentScene"
      @saveSuccess="onRepoAddSaveSuccess"
    ></addDialog>
  </div>
</template>
<script>
import { mapGetters } from 'vuex'
import addDialog from './Dialog/addDialogSOS/index.vue'

export default {
  data() {
    return {
      agentDataUsed: [],
      dialogAdd: {
        show: false,
      },
    }
  },
  computed: {
    ...mapGetters({
      currentScene: 'aiuiApp/currentScene',
      appInfo: 'aiuiApp/app',
    }),
    appId() {
      return this.$route.params.appId
    },
  },
  created() {
    if (this.currentScene && this.currentScene.sceneBoxId) {
      this.getAgentConfig() // 获取配置列表
    }
  },
  watch: {
    currentScene(scene) {
      if (scene && scene.sceneBoxId) {
        this.getAgentConfig() // 获取配置列表
      }
    },
  },

  methods: {
    // 获取已配置的文档问答信息
    getAgentConfig() {
      let that = this
      this.loading = false
      this.$utils.httpGet(
        this.$config.api.AIUI_BOT_CONFIG_GET_BotAgentPlugins,
        {
          botId: this.currentScene.botBoxId,
        },
        {
          success: (res) => {
            that.agentDataUsed = (res.data || []).filter(
              (item) => item.selected
            )
          },
          error: (res) => {},
        }
      )
    },

    add() {
      this.dialogAdd.show = true
    },
    onSearchConfigSaveSuccess() {},
    onRepoAddSaveSuccess() {
      this.getAgentConfig()
    },
  },
  components: { addDialog },
}
</script>
<style lang="scss" scoped>
@import '../card.scss';
</style>
