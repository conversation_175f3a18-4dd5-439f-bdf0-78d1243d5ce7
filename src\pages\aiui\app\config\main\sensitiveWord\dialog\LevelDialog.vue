<template>
  <el-dialog
    :title="`${sensitiveWordTypeCopySingle.name}敏感词过滤`"
    :visible.sync="sensitiveLevelVisible"
    :close-on-click-modal="false"
    :before-close="handleClose"
    :append-to-body="true"
    width="467px"
  >
    <div class="dialog-div">
      <ul>
        <li>过滤优先级</li>
        <li v-for="(item, index) in sensitiveLevel">
          <el-radio
            v-model="sensitiveWordTypeCopySingle.level"
            :label="item.key"
            @change="doChange($event, item.key)"
            >{{ item.title }}</el-radio
          >
        </li>
      </ul>
      <ul>
        <li>
          {{
            sensitiveLevel.find((item) => {
              return item.key == currentSensitiveLevel
            }).tips
          }}
        </li>
      </ul>
      <ul v-if="type === 1">
        <li>
          <p>过滤后回复语</p>
        </li>
      </ul>
      <ul v-if="type === 1" class="li-scroll">
        <replyELTag
          ref="replyELTag"
          @pushReply="pushReply"
          @doModifyReply="doModifyReply"
          :canDoConfirm="canDoConfirm"
          :sensitiveWordTypeCopySingle="sensitiveWordTypeCopySingle"
        />
      </ul>
      <ul>
        <li>
          <el-button
            class="btn-confirm-collect"
            type="primary"
            @click="doConfirm"
            >确定
          </el-button>
          <el-button
            class="btn-confirm-collect"
            type="default"
            @click="doCancel"
            >取消
          </el-button>
        </li>
      </ul>
    </div>
  </el-dialog>
</template>

<script>
import replyELTag from '../replyELTag'
export default {
  name: 'LevelDialog',
  props: {
    type: Number,
    sensitiveWordTypeCopySingleCopy: {
      type: Object,
      default: {},
      deep: true,
    },
    sensitiveWordTypeCopySingle: {
      type: Object,
      default: {},
      deep: true,
    },
    sensitiveLevelVisible: Boolean,
  },
  components: {
    replyELTag,
  },
  data: function () {
    return {
      canDoConfirm: {
        isOk: true,
        clean: false,
        random: Math.random,
      },
      currentSensitiveLevel: 0,
      sensitiveLevel: [
        {
          key: '0',
          title: '高',
          tips: '过滤所有带有敏感信息的词',
        },
        {
          key: '-1',
          title: '中',
          tips: '过滤高敏感和比较敏感的词',
        },
        {
          key: '-2',
          title: '低',
          tips: '仅过滤高敏感信息的词',
        },
      ],
    }
  },
  watch: {
    sensitiveLevelVisible: function () {
      if (this.sensitiveLevelVisible) {
        let level = parseInt(this.sensitiveWordTypeCopySingle.level || 0)
        this.currentSensitiveLevel = level
      }
      if (!this.sensitiveLevelVisible && !this.canDoConfirm.isOk) {
        this.canDoConfirm.clean = true
        this.canDoConfirm.random = Math.random()
      }
    },
    'sensitiveWordTypeCopySingle.length': function () {
      if (JSON.stringify(this.sensitiveWordTypeCopySingle) === '{}') {
        this.canDoConfirm.isOk = true
      }
    },
    canDoConfirm: {
      handler(newVal, oldVal) {
        if (newVal.clean) {
          if (this.type === 1) {
            this.$refs['replyELTag'].inputValue = ''
            this.$refs['replyELTag'].inputVisible = false
            let tmp = Array.prototype.map.call(
              this.$refs['replyELTag'].editable,
              function (item, index) {
                item = false
                return item
              }
            )
            this.$refs['replyELTag'].editable = tmp
            //this.$refs['replyELTag'].sensitiveWordTypeCopySingle.reply = this.sensitiveWordTypeCopySingle.reply
            this.$forceUpdate()
            return
          }
        }
        if (!newVal.isOk || !newVal.clean) {
          this.$message.error('回复语仅支持中英文和数字')
        }
      },
      deep: true,
    },
  },
  methods: {
    pushReply(reply) {
      this.$set(this.sensitiveWordTypeCopySingle, 'reply', reply)
    },
    doModifyReply(replyWords) {
      this.sensitiveWordTypeCopySingle.reply = replyWords
    },
    doChange(e, value) {
      this.sensitiveWordTypeCopySingle.level = value
      this.currentSensitiveLevel = value
      this.$forceUpdate()
    },
    handleClose(done) {
      this.doCancel()
    },
    doCancel() {
      this.$message && this.$message.close()
      if (this.type === 1) {
        let tmp = Array.prototype.map.call(
          this.$refs['replyELTag'].editable,
          function (item, index) {
            item = false
            return item
          }
        )
        this.$refs['replyELTag'].editable = tmp
      }
      this.canDoConfirm.isOk = true
      this.$emit('doClose')
    },
    doConfirm() {
      let flag = false
      if (this.type === 1) {
        this.$refs['replyELTag'].editable.forEach((item, index) => {
          if (!!item) {
            flag = item
            return
          }
        })
      }

      if (this.canDoConfirm.isOk === true && !flag) {
        this.$emit('doConfirm')
      } else {
        this.canDoConfirm.clean = false
        this.canDoConfirm.random = Math.random()
      }
    },
  },
}
</script>

<style scoped lang="scss">
.btn-confirm-collect {
  box-shadow: 0px 2px 4px 0px rgba(23, 132, 233, 0.2);
  border-radius: 4px;
  margin-top: 15.24%;
  position: relative;
  padding: 4%;
  max-width: 20.47% !important;
  min-width: 40.47% !important;
}
.dialog-div {
  > ul:nth-child(1) {
    display: flex;
    > li {
      flex: 1;
      font-size: 15px;
      font-weight: 500;
    }
    > li:nth-child(1) {
      //font-size: 14px;
    }
  }
  > ul:nth-child(2) {
    > li {
      text-align: center;
      color: $warning;
    }
  }
  > ul:nth-child(4) {
    border: 1px dotted;
    padding: 1%;
    max-height: 230px;
    min-height: 230px;
    overflow: hidden;
    overflow-y: auto;
  }
  .li-scroll {
    overflow-y: auto !important;
    border: 1px solid black;
    height: 3em;
    //width: 10em;
    line-height: 1em;
  }

  .li-scroll::-webkit-scrollbar {
    -webkit-appearance: none;
  }

  .li-scroll::-webkit-scrollbar:vertical {
    width: 11px;
  }

  .li-scroll::-webkit-scrollbar:horizontal {
    height: 11px;
  }

  .li-scroll::-webkit-scrollbar-thumb {
    border-radius: 8px;
    border: 2px solid white; /* should match background, can't be transparent */
    background-color: rgba(0, 0, 0, 0.5);
  }
  > ul:last-child {
    position: relative;
    display: flex;
    bottom: 1em;
    margin-top: 14%;
    > li {
      width: 100%;
      margin: 0 auto;
      margin-right: 0 !important;
      justify-content: right;
      flex: 0.4;
    }
  }
}
</style>
