<template>
  <div class="os-scroll">
    <handle-platform-top v-if="subAccount"></handle-platform-top>
    <div
      class="handle-platform-content"
      v-loading="exportLoading"
      element-loading-text="正在导出中，请稍候"
      :style="{ padding: subAccount ? '10px' : 0 }"
    >
      <div class="">
        <div class="if-alc mgb24">
          <el-button
            icon="ic-r-plus"
            type="primary"
            size="medium"
            style="min-width: 100px"
            @click="openCreateEntity"
          >
            创建实体
          </el-button>
          <el-button
            v-if="!subAccount"
            type="text"
            size="small"
            style="margin-left: 8px"
            @click="linkOfficialEntity"
          >
            查看官方实体
          </el-button>
          <el-button
            size="small"
            type="text"
            @click="openSecretKey"
            style="margin-left: 0px"
          >
            动态实体密钥
          </el-button>
          <el-button
            size="small"
            type="text"
            @click="exportAll"
            style="margin-left: 0px"
          >
            一键导出实体
          </el-button>
        </div>
        <el-form
          inline
          label-width="70px"
          class="search-form"
          :model="searchFilter"
          size="medium"
          ref="searchForm"
        >
          <el-form-item label="实体类型" prop="type" style="margin-right: 0">
            <el-select
              v-model="searchFilter.type"
              class="mgr8"
              placeholder="请选择"
              size="medium"
              style="width: 130px"
            >
              <el-option
                v-for="item in filterTypes"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            label="关键字"
            prop="searchVal"
            style="margin-right: 14px"
          >
            <el-input
              placeholder="实体,英文,资源,词条名称"
              v-model="searchFilter.searchVal"
              style="width: 190px"
              @keydown.native.enter.prevent="getEntities(1)"
            />
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              size="medium"
              style="min-width: 80px"
              @click="getEntities(1)"
              >搜索</el-button
            >
            <el-button
              size="medium"
              style="min-width: 80px; margin-left: 4px"
              @click="resetSearch"
              >重置</el-button
            >
          </el-form-item>
        </el-form>
      </div>
      <os-table
        v-if="hasEntity"
        :tableData="tableData"
        class="entities-table gutter-table-style transparent-bgc"
        :height="'calc(100vh - 291px)'"
        style="margin-bottom: 15px"
        @change="getEntities"
        @edit="toEdit"
        @del="del"
        @row-click="toEdit"
      >
        <el-table-column prop="value" width="240" label="实体名称">
          <template slot-scope="scope">
            <div
              class="text-blod cp entities-page-entity-zh-name"
              :title="scope.row.value || '-'"
              @click.stop.prevent="toEdit(scope.row)"
            >
              {{ scope.row.value || '-' }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="name" width="130" label="英文标识">
          <template slot-scope="scope">
            <div class="entity-name" :title="scope.row.name">
              {{ scope.row.name }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="type" width="120" label="实体类型">
          <template slot-scope="scope">
            <div style="display: flex; align-items: center">
              <span style="margin-right: 2px">
                {{ scope.row.type | entityType }}
              </span>
              <div
                class="intent-tag ib"
                :class="'intent-tag-entity-' + scope.row.type"
              >
                {{ entityTypeTag(scope.row.type) }}
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="updateTime" width="200" label="更新时间">
          <template slot-scope="scope">
            <div>{{ scope.row.updateTime | date('yyyy-MM-dd hh:mm:ss') }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="count" width="90" label="被引用数">
          <template slot-scope="scope">
            <div
              v-if="scope.row.count"
              class="text-primary"
              style="cursor: pointer; height: 40px; line-height: 40px"
              @click.stop.prevent="openCountDialog(scope.row)"
            >
              {{ scope.row.count }}
            </div>
            <span v-else>{{ scope.row.count }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" width="90" label="状态">
          <template slot-scope="scope">
            <template v-if="scope.row.type == 2">
              <div
                class="ib entity-status"
                :class="'entity-status-' + (scope.row.status === 1 ? '1' : '2')"
              />
              <span :class="{ 'entity-status-txt': scope.row.status !== 1 }">{{
                scope.row.status === 1 ? '已构建' : '未构建'
              }}</span>
            </template>
            <span v-else>-</span>
          </template>
        </el-table-column>
      </os-table>
      <div class="create-guide" v-else>
        <div class="icon"></div>
        <p class="title">
          你还没有创建任何实体，
          <a @click="openCreateEntity"> 点击创建 </a>
        </p>
      </div>
    </div>
    <create-entity-dialog :dialog="dialog" @change="getEntities" />
    <skill-quote-dialog :dialog="countDialog" />
    <secret-key-dialog :dialog="secretKeyDialog" />
  </div>
</template>

<script>
import HandlePlatformTop from './top.vue'
import CreateEntityDialog from './dialog/createEntity.vue'
import SkillQuoteDialog from './dialog/skillQuote.vue'
import SecretKeyDialog from '../handlePlatform/dialog/secretKey.vue'
import { mapGetters } from 'vuex'

export default {
  name: 'studio-handle-platform-entities',
  data() {
    return {
      nav: 'entities',
      tableData: {
        loading: true,
        total: 0,
        page: 1,
        size: 10,
        handles: ['edit', 'del'],
        handleColumnText: '操作',
        list: [],
      },
      dialog: {
        show: false,
      },
      countDialog: {
        show: false,
        entityId: '',
      },
      secretKeyDialog: {
        show: false,
      },
      searchFilter: {
        type: '',
        searchVal: '',
      },
      filterTypes: [
        {
          value: '',
          label: '全部',
        },
        {
          value: 2,
          label: '静态实体',
        },
        {
          value: 3,
          label: '动态实体',
        },
        {
          value: 5,
          label: '所见即可说',
        },
      ],
      hasEntity: true,
      exportLoading: false,
    }
  },
  created() {
    this.getEntities(1)
    if (localStorage.getItem('pageHandle') === 'create') {
      this.dialog.show = true
      localStorage.setItem('pageHandle', null)
    }
    if (this.$route.query && this.$route.query.option == 'createEntity') {
      this.openCreateEntity()
    }
  },
  computed: {
    ...mapGetters({
      subAccount: 'user/subAccount',
    }),
  },
  methods: {
    resetSearch() {
      this.$refs.searchForm.resetFields()
      this.getEntities(1)
    },
    entityTypeTag(type) {
      let tag = ''
      switch (type) {
        case 2:
          tag = '静'
          break
        case 3:
          tag = '动'
          break
        case 5:
          tag = '见'
          break
      }
      return tag
    },
    getEntities(page) {
      let self = this
      this.tableData.loading = true
      this.$utils.httpGet(
        this.$config.api.STUDIO_ENTITY_LIST,
        {
          pageIndex: page || this.tableData.page,
          pageSize: this.tableData.size,
          search: this.searchFilter.searchVal || '',
          type: this.searchFilter.type || '',
        },
        {
          success: (res) => {
            if (res.data.count <= 0 && !self.searchFilter.searchVal) {
              self.hasEntity = false
            } else {
              self.hasEntity = true
            }
            self.tableData.list = res.data.results
            self.tableData.total = res.data.count
            self.tableData.page = res.data.pageIndex
            self.tableData.size = res.data.pageSize
            self.tableData.loading = false
          },
          error: (err) => {},
        }
      )
    },
    // 查看官方实体
    linkOfficialEntity() {
      let routeData = this.$router.resolve({ name: 'studio-official-entities' })
      window.open(routeData.href, '_blank')
    },
    openSecretKey() {
      this.secretKeyDialog = {
        show: true,
      }
    },
    // 打开创建实体dialog
    openCreateEntity() {
      this.dialog.show = true
    },
    // 打开引用技能的dialog
    openCountDialog(entity) {
      this.countDialog.show = true
      this.countDialog.entityId = entity.id
    },
    toEdit(data) {
      let routeData
      if (this.subAccount) {
        routeData = this.$router.resolve({
          name: 'sub-entity',
          params: { entityId: data.id },
        })
      } else {
        routeData = this.$router.resolve({
          name: 'entity',
          params: { entityId: data.id },
        })
      }
      window.open(routeData.href, '_blank')
      // this.subAccount
      //   ? this.$router.push({
      //       name: 'sub-entity',
      //       params: { entityId: data.id },
      //     })
      //   : this.$router.push({ name: 'entity', params: { entityId: data.id } })
    },
    del(data) {
      let self = this
      if (data.count) {
        this.$message.warning('该实体被技能引用，请先取消引用后再删除')
        return
      }
      this.$confirm(
        '实体删除后不可恢复，请谨慎操作。',
        `确定删除实体 - ${data.value}?`,
        {
          confirmButtonText: '确定删除',
          cancelButtonText: '取消',
          confirmButtonClass: 'el-button--danger',
          type: 'warning',
          showClose: false,
        }
      )
        .then(() => {
          self.delEntity(data)
        })
        .catch(() => {})
    },
    delEntity(data) {
      let self = this
      this.tableData.loading = true
      this.$utils.httpPost(
        this.$config.api.STUDIO_ENTITY_DEL,
        {
          entityId: data.id,
        },
        {
          success: (res) => {
            self.$message.success('删除成功')
            if (self.tableData.list.length === 1 && self.tableData.page > 1) {
              self.tableData.page -= 1
            }
            self.getEntities()
          },
          error: (err) => {},
        }
      )
    },
    exportAll() {
      this.exportLoading = true
      this.$utils
        .postopen(this.$config.api.STUDIO_ENTITYS_EXCEL, {
          fileName: '实体压缩包',
          entityIds: '',
          isAuxiliary: '0',
        })
        .then(() => {
          this.exportLoading = false
        })
    },
  },
  components: {
    HandlePlatformTop,
    CreateEntityDialog,
    SkillQuoteDialog,
    SecretKeyDialog,
  },
}
</script>

<style lang="scss" scoped>
.handle-platform-content {
  // max-width: 1200px;
  width: 100%;
  margin: auto;
}
.search-area {
  width: 320px;
}

.entity-status {
  width: 8px;
  height: 8px;
  border: 2px solid;
  border-radius: 8px;
  margin-right: 6px;
  &-0 {
    border-color: $grey5;
  }
  &-1 {
    border-color: $success;
  }
  &-2 {
    border-color: $grey4;
  }
  &-txt {
    color: $grey5;
  }
  &-count {
    color: $primary;
  }
}
.text-blod,
.entity-name {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.search-form {
  float: right;
  vertical-align: top;
}

.create-guide {
  margin: 76px 0;
  text-align: center;
  font-size: 16px;
  color: $grey5;
  .icon {
    margin: 0 auto 24px;
    width: 120px;
    height: 120px;
    background: url(../../../assets/images/app/create-app.png) center no-repeat;
    background-size: 100%;
  }
  .title {
    font-size: 16px;
    font-weight: 600;
    a {
      font-weight: 600;
    }
  }
  .desc {
    margin: 24px auto;
    width: 480px;
    font-size: 14px;
    line-height: 22px;
  }
}
</style>
<style lang="scss">
.el-table--enable-row-hover .el-table__body tr:hover > td {
  .entities-page-entity-zh-name {
    color: $primary;
  }
}
.el-table .ic-r-edit {
  color: $primary;
}
.entities-table {
  tr {
    cursor: pointer;
  }
}
</style>
