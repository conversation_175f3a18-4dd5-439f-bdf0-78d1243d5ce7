<template>
  <div>
    <el-card class="box-card">
      <el-form :model="schemaForm" :rules="schemaRules" ref="schemaForm">
        <div class="box-header">
          <div class="box-header-left">
            <i
              @click="isExpand = !isExpand"
              :class="isExpand ? 'el-icon-arrow-down' : 'el-icon-arrow-right'"
              class="icon-flod"
            ></i>
            <img src="@/assets/images/Api_icon.png" alt="" />

            <div class="tool-name">
              <div v-show="!isEdit">{{ ApiInfo?.toolName }}</div>

              <el-form-item
                prop="toolName"
                v-show="isEdit"
                style="margin-bottom: 0px"
              >
                <el-input
                  v-model="schemaForm.toolName"
                  placeholder="接口名称最长32个字"
                ></el-input>
              </el-form-item>

              <!-- <el-tag type="danger" v-if="ApiInfo?.status === 0" size="mini"
                >未发布</el-tag
              > -->

              <el-tag type="success" v-if="ApiInfo?.status === 1" size="mini"
                >已发布</el-tag
              >

              <el-tag
                type="warning"
                v-if="ApiInfo?.status === 0 && ApiInfo?.debugStatus === 0"
                size="mini"
                >待调试</el-tag
              >

              <el-tag
                type="danger"
                v-if="ApiInfo?.status === 0 && ApiInfo?.debugStatus === -1"
                size="mini"
                >调试失败</el-tag
              >

              <el-tag
                v-if="ApiInfo?.status === 0 && ApiInfo?.debugStatus === 1"
                size="mini"
                >待发布</el-tag
              >
            </div>
          </div>

          <div class="btns-wrap">
            <el-button
              slot="append"
              plain
              style="margin-left: 10px"
              @click="doApiAuth"
            >
              鉴权
            </el-button>

            <el-button type="primary" @click="doDebugging">调试</el-button>

            <!-- 编辑按钮：非编辑状态下显示 -->
            <el-button
              v-if="!isEdit"
              type="primary"
              @click="doEdit"
              style="margin-right: 10px"
            >
              编辑
            </el-button>
            <!-- 保存按钮：编辑状态下显示 -->
            <el-button v-if="isEdit" type="primary" @click="saveCardData">
              保存
            </el-button>

            <!-- 取消按钮：编辑状态下显示 -->
            <el-button
              v-if="isEdit"
              @click="cancelEdit"
              style="margin-right: 10px"
            >
              取消
            </el-button>

            <el-dropdown trigger="click">
              <span class="el-dropdown-link">
                <i
                  class="el-icon-more"
                  style="font-size: 18px; line-height: 36px"
                ></i>
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item>
                  <el-button @click="doDelApi" size="mini" type="text"
                    >删除</el-button
                  >
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </div>

        <div class="api-content">
          <div class="tool-info">
            <el-form-item label-width="0px" style="width: 100%" prop="endPoint">
              <el-input
                placeholder="请输入内容"
                v-model="schemaForm.endPoint"
                :disabled="!isEdit"
              >
                <el-select
                  v-model="schemaForm.method"
                  slot="prepend"
                  placeholder="请选择方法"
                  style="width: 180px"
                  :disabled="!isEdit"
                >
                  <el-option label="GET" value="get"></el-option>
                  <el-option label="POST" value="post"></el-option>
                  <el-option label="PUT" value="put"></el-option>
                  <el-option label="DELETE" value="delete"></el-option>
                  <el-option label="PATCH" value="patch"></el-option>
                </el-select>
              </el-input>
            </el-form-item>
          </div>

          <div v-if="isExpand">
            <div class="sub_title">请求参数</div>
            <el-row style="margin-bottom: 15px">
              <el-col :span="4" class="text-left">参数名称</el-col>
              <el-col :span="2" class="text-left" style="padding-left: 1px"
                >参数类型</el-col
              >
              <el-col :span="2" class="text-left" style="padding-left: 2px"
                >传入方法</el-col
              >
              <el-col :span="7" class="text-left" style="padding-left: 3px"
                >参数描述</el-col
              >
              <el-col :span="3" class="text-left" style="padding-left: 10px"
                >默认值</el-col
              >
              <el-col :span="2" class="text-left" style="padding-left: 7px"
                >是否必填</el-col
              >
              <el-col :span="2" class="text-left" style="padding-left: 10px"
                >开启</el-col
              >
              <el-col :span="2" class="text-left" style="padding-left: 10px"
                >操作</el-col
              >
            </el-row>

            <div v-for="(item, index) in schemaForm.inputForm" :key="item.id">
              <form-row
                :ref="`row-${index}`"
                :form-data="item"
                :path="['inputForm', index]"
                area="toolRequestInput"
                @change="(newData) => handleItemChange('input', index, newData)"
                @remove-child="() => handleRemoveItem('input', index)"
                @edit-array="(data) => handleEditArray('input', data)"
                :nesting-level="0"
                :all-names="allInputNames"
                :disabled="!isEdit"
              />
            </div>

            <div class="add_btn_wrap" ref="inputAddBtnWrap" v-show="isEdit">
              <el-button
                plain
                icon="el-icon-plus"
                @click="addRootItem('input')"
                class="add-root-btn"
                :disabled="!isEdit"
              >
                新增参数
              </el-button>
            </div>

            <div class="sub_title">输出参数</div>
            <el-row style="margin-bottom: 15px">
              <el-col :span="7" class="text-left">参数名称</el-col>
              <el-col :span="3" class="text-left" style="padding-left: 2px"
                >参数类型</el-col
              >
              <el-col :span="10" class="text-left" style="padding-left: 4px"
                >参数描述</el-col
              >
              <el-col :span="2" class="text-left" style="padding-left: 10px"
                >开启</el-col
              >
              <el-col :span="2" class="text-left" style="padding-left: 10px"
                >操作</el-col
              >
            </el-row>

            <div v-for="(item, index) in schemaForm.outputForm" :key="item.id">
              <form-row
                :ref="`row-${index}`"
                :form-data="item"
                :path="['outputForm', index]"
                area="toolRequestOutput"
                @change="
                  (newData) => handleItemChange('output', index, newData)
                "
                @remove-child="() => handleRemoveItem('output', index)"
                @edit-array="(data) => handleEditArray('output', data)"
                :nesting-level="0"
                :all-names="allOutputNames"
                :disabled="!isEdit"
              />
            </div>

            <div class="add_btn_wrap" ref="outputAddBtnWrap" v-show="isEdit">
              <el-button
                plain
                icon="el-icon-plus"
                @click="addRootItem('output')"
                class="add-root-btn"
                :disabled="!isEdit"
              >
                新增参数
              </el-button>
            </div>

            <!-- <div class="api-footer">
              <el-button @click="cancelApi"> 取消</el-button>
              <el-button
                type="primary"
                @click="saveCardData"
                style="margin-right: 20px"
                >保存</el-button
              >
            </div> -->
          </div>
        </div>
      </el-form>
    </el-card>

    <auth-info-modal
      ref="AuthInfoModal"
      @saveCustomInfo="saveCustomInfo"
      :defaultAuthConfig="defaultAuthConfig"
      @refresh="refreshCard"
    />

    <array-modal
      v-model="arrayModalVisible"
      :array-data="currentArrayData"
      @submit="handleArraySave"
    />

    <debug-modal
      ref="DebugModal"
      :input-params="schemaForm.inputForm"
      :openApiForm="schemaForm"
      :toolId="ApiInfo?.toolId"
      :ApiInfo="ApiInfo"
      :defaultAuthConfig="defaultAuthConfig"
      @refresh="refreshCard"
    />
  </div>
</template>

<script>
import AuthInfoModal from './authInfoModal.vue'
import { v4 as uuidv4 } from 'uuid'
import FormRow from './formRow.vue'
import ArrayModal from './arrayModal.vue'
import DebugModal from './debugModal.vue'
export default {
  props: {
    ApiInfo: {
      type: Object,
    },
    defaultAuthConfig: {
      type: Object,
    },
    addFlag: {
      type: Boolean,
    },
  },
  name: 'source-detail-api-card',
  components: { FormRow, ArrayModal, DebugModal, AuthInfoModal },
  computed: {
    // 获取所有输入参数名称（用于检查重名，不包括当前正在编辑的项）
    allInputNames() {
      // 返回一个函数，接收当前正在编辑的项的ID和父节点路径作为参数
      return (currentItemId, parentPath = null) => {
        try {
          console.log('allInputNames 被调用:', currentItemId, parentPath)

          // 如果没有提供父路径，则检查顶层参数
          if (!parentPath) {
            const names = this.schemaForm.inputForm
              .filter((item) => item.id !== currentItemId) // 排除当前正在编辑的项
              .map((item) => item.name)
            console.log('顶层参数名称:', names)
            return names
          }

          // 如果提供了父路径，则查找该父节点下的所有子参数
          const parent = this.findItemByPath(null, parentPath)
          console.log('找到的父节点:', parent)

          if (parent && Array.isArray(parent)) {
            // 如果返回的是数组（例如children数组）
            const names = parent
              .filter((item) => item.id !== currentItemId)
              .map((item) => item.name)
            console.log('子参数名称(数组):', names)
            return names
          } else if (parent && parent.children) {
            // 如果返回的是对象，且有children属性
            const names = parent.children
              .filter((item) => item.id !== currentItemId)
              .map((item) => item.name)
            console.log('子参数名称(对象.children):', names)
            return names
          }

          console.log('没有找到子参数')
          return []
        } catch (error) {
          console.error('获取输入参数名称出错:', error)
          return []
        }
      }
    },
    // 获取所有输出参数名称（用于检查重名，不包括当前正在编辑的项）
    allOutputNames() {
      // 返回一个函数，接收当前正在编辑的项的ID和父节点路径作为参数
      return (currentItemId, parentPath = null) => {
        try {
          console.log('allOutputNames 被调用:', currentItemId, parentPath)

          // 如果没有提供父路径，则检查顶层参数
          if (!parentPath) {
            const names = this.schemaForm.outputForm
              .filter((item) => item.id !== currentItemId) // 排除当前正在编辑的项
              .map((item) => item.name)
            console.log('顶层参数名称:', names)
            return names
          }

          // 如果提供了父路径，则查找该父节点下的所有子参数
          const parent = this.findItemByPath(null, parentPath)
          console.log('找到的父节点:', parent)

          if (parent && Array.isArray(parent)) {
            // 如果返回的是数组（例如children数组）
            const names = parent
              .filter((item) => item.id !== currentItemId)
              .map((item) => item.name)
            console.log('子参数名称(数组):', names)
            return names
          } else if (parent && parent.children) {
            // 如果返回的是对象，且有children属性
            const names = parent.children
              .filter((item) => item.id !== currentItemId)
              .map((item) => item.name)
            console.log('子参数名称(对象.children):', names)
            return names
          }

          console.log('没有找到子参数')
          return []
        } catch (error) {
          console.error('获取输出参数名称出错:', error)
          return []
        }
      }
    },
  },
  data() {
    return {
      isExpand: false,
      isEdit: false,
      saving: false,
      originalFormData: null,

      arrayModalVisible: false,
      currentArrayData: null,
      currentArrayIndex: null,
      currentArea: null,

      customInfo: {},

      schemaForm: {
        endPoint: this.ApiInfo.endPoint,
        method: this.ApiInfo.method,
        inputForm: JSON.parse(this.ApiInfo?.webSchema)?.toolRequestInput || [],
        outputForm:
          JSON.parse(this.ApiInfo?.webSchema)?.toolRequestOutput || [],
        toolName: null,
      },
      schemaRules: {
        toolName: [
          {
            required: true,
            message: '请输入接口名称',
            trigger: 'blur',
          },
          {
            max: 32,
            message: '最多输入32个字符',
            trigger: 'blur',
          },
        ],
        endPoint: [
          { required: true, message: '请输入路径', trigger: 'blur' },
          {
            pattern:
              /^(http|https):\/\/[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+(:[0-9]{1,5})?(\/[-a-zA-Z0-9%_\.~#]*)*(\?[a-zA-Z0-9%_\.~#=-]*)?(#[-a-zA-Z0-9_]*)?$/,
            message: 'API链接必须是合法的HTTP/HTTPS URL格式',
            trigger: ['change', 'blur'],
          },
          // {
          //   pattern:
          //     /^(https?:\/\/)?(localhost|(\d{1,3}\.){3}\d{1,3}|([\w-]+\.)+[\w-]+)(:\d+)?(\/[\w\-./?%&=]*)?$/,
          //   message: '请输入有效的URL地址',
          // },
        ],
        method: [{ required: true, message: '请选择方法', trigger: 'blur' }],
      },
    }
  },

  created() {
    if (this.ApiInfo.toolId) {
      this.schemaForm.toolName = this.ApiInfo.toolName
      if (this.ApiInfo?.authType === 2) {
        this.customInfo = {
          authType: this.ApiInfo?.authType,
          authInfo: JSON.parse(this.ApiInfo?.authInfo),
        }
      } else {
        this.customInfo = {
          authType: -1,
          authInfo: '',
        }
      }
      // 如果是已有API，默认不进入编辑状态
      this.isEdit = false
      this.isExpand = false
    } else {
      // 如果是新建API，默认进入编辑状态
      this.isEdit = true
      this.isExpand = true
    }
  },
  methods: {
    saveCardData() {
      this.$refs.schemaForm.validate((valid) => {
        if (!this.schemaForm.outputForm.length > 0) {
          this.$message.warning('必须填输出参数')
        } else {
          if (valid) {
            console.log(this.customInfo, 'this.customInfo')
            console.log(this.defaultAuthConfig, 'this.defaultAuthConfig')
            let params = {
              sourceId: this.$route.params.sourceId,
              toolName: this.schemaForm.toolName,
              endPoint: this.schemaForm.endPoint,
              method: this.schemaForm.method,
              webSchema: JSON.stringify({
                toolRequestInput: this.schemaForm.inputForm,
                toolRequestOutput: this.schemaForm.outputForm,
              }),
              toolId: this.ApiInfo?.toolId,

              authType: this.customInfo?.authType
                ? this.customInfo?.authType
                : this.defaultAuthConfig
                ? 2
                : -1,
              authInfo: this.customInfo?.authInfo?.location
                ? JSON.stringify(this.customInfo?.authInfo)
                : this.defaultAuthConfig
                ? JSON.stringify(this.defaultAuthConfig)
                : '',
            }
            if (this.customInfo?.authType === -1) {
              if (this.defaultAuthConfig?.location) {
                params.authType = 2
                params.authInfo = JSON.stringify(this.defaultAuthConfig)
              } else {
                params.authType = -1
                params.authInfo = ''
              }
            } else if (this.customInfo?.authType === 2) {
              params['authType'] = 2
              params['authInfo'] = this.customInfo?.authInfo?.location
                ? JSON.stringify(this.customInfo?.authInfo)
                : this.defaultAuthConfig
                ? JSON.stringify(this.defaultAuthConfig)
                : ''
            } else {
              if (this.defaultAuthConfig?.location) {
                params['authType'] = 2
                params['authInfo'] = JSON.stringify(this.defaultAuthConfig)
              } else {
                params['authType'] = -1
                params['authInfo'] = ''
              }
            }
            console.log(params, '最后的params')

            this.saving = true
            if (this.ApiInfo?.toolId) {
              this.$utils.httpPost(
                `/aiui-agent/openPlatform/source/apiSave`,
                JSON.stringify(params),
                {
                  config: {
                    headers: {
                      'Content-Type': 'application/json;charset=UTF-8',
                    },
                  },
                  success: (res) => {
                    if (res.code === '0') {
                      this.saving = false
                      this.$message.success('操作成功')
                      this.isEdit = false
                      this.isExpand = false
                      this.$emit('refresh')
                    }
                  },
                  error: (err) => {
                    this.$message.error(err.desc)
                    this.saving = false
                  },
                }
              )
            } else {
              delete params['toolId']
              this.$utils.httpPost(
                `/aiui-agent/openPlatform/source/apiAdd`,
                JSON.stringify(params),
                {
                  config: {
                    headers: {
                      'Content-Type': 'application/json;charset=UTF-8',
                    },
                  },
                  success: (res) => {
                    if (res.code === '0') {
                      this.$message.success('操作成功')
                      this.saving = false
                      this.isEdit = false
                      this.isExpand = false
                      this.$emit('refresh')
                    }
                  },
                  error: (err) => {
                    this.$message.error(err?.desc || '新增失败')
                    this.saving = false
                  },
                }
              )
            }
          }
        }
      })
    },

    saveCustomInfo(customInfo) {
      //  自定义授权保存的ApiInfo
      console.log(customInfo, '在card中接收的自定义customInfo')
      this.customInfo = customInfo
    },

    cancelApi() {
      this.isExpand = false
      this.isEdit = false
      this.$emit('refresh')
    },

    refreshCard() {
      this.$emit('refresh')
    },

    doFold() {
      this.isExpand = false
      this.isEdit = false
    },

    doEdit() {
      this.isEdit = true
      this.isExpand = true

      // 保存原始数据，以便取消时恢复
      this.originalFormData = JSON.parse(
        JSON.stringify({
          toolName: this.schemaForm.toolName,
          endPoint: this.schemaForm.endPoint,
          method: this.schemaForm.method,
          inputForm: this.schemaForm.inputForm,
          outputForm: this.schemaForm.outputForm,
        })
      )
    },

    cancelEdit() {
      this.isEdit = false

      // 恢复原始数据
      if (this.originalFormData) {
        this.schemaForm.toolName = this.originalFormData.toolName
        this.schemaForm.endPoint = this.originalFormData.endPoint
        this.schemaForm.method = this.originalFormData.method
        this.schemaForm.inputForm = JSON.parse(
          JSON.stringify(this.originalFormData.inputForm)
        )
        this.schemaForm.outputForm = JSON.parse(
          JSON.stringify(this.originalFormData.outputForm)
        )
      }
    },

    doExpand() {
      // this.isExpand = true
      this.isEdit = true
      this.isExpand = true
      this.schemaForm.toolName = this.ApiInfo.toolName
    },

    doApiAuth() {
      this.$refs.AuthInfoModal.show(
        2,
        this.ApiInfo?.toolId,
        this.customInfo,
        this.ApiInfo?.toolName
      )
    },
    doDelApi() {
      this.$confirm('将删除此Api, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          if (!this.ApiInfo?.toolId) {
            this.$emit('doNoIdDel')
          } else {
            let toolId = this.ApiInfo.toolId
            this.$utils.httpPost(
              `/aiui-agent/openPlatform/source/apiDelete?toolId=${toolId}`,
              {},
              {
                config: {
                  headers: {
                    'Content-Type': 'application/json;charset=UTF-8',
                  },
                },
                success: (res) => {
                  if (res.code === '0') {
                    this.$message.success('操作成功')
                    this.$emit('refresh')
                  }
                },
                error: (err) => {},
              }
            )
          }
        })
        .catch(() => {})
    },

    async doDebugging() {
      try {
        // 1. 先校验主表单
        await this.$refs.schemaForm.validate()
        // 2. 校验所有动态表单项（包括嵌套的表单项）
        const validateAll = await Promise.all([
          this.validateFormItems('inputForm'),
          this.validateFormItems('outputForm'),
        ])
        if (validateAll.every((valid) => valid)) {
          // 所有校验通过，执行提交逻辑
          // this.submitForm()
          console.log(this.schemaForm, '调试的·························')
          this.$refs.DebugModal.show()
        } else {
          this.$message.error('请检查表单填写是否正确')
        }
      } catch (error) {}
    },

    async validateFormItems(formName) {
      try {
        // 获取所有 form-row 组件的引用
        const formItems = this.$refs[`row-${formName}`] || []

        // 扁平化处理嵌套的 form-row
        const allFormItems = []
        const flattenFormItems = (items) => {
          items.forEach((item) => {
            allFormItems.push(item)
            if (item.$refs) {
              Object.values(item.$refs).forEach((ref) => {
                if (Array.isArray(ref)) {
                  flattenFormItems(ref)
                }
              })
            }
          })
        }

        flattenFormItems(formItems)

        // 校验每个表单项
        const results = await Promise.all(
          allFormItems.map((item) => {
            if (item.$refs && item.$refs.formItem) {
              return item.$refs.formItem.validate()
            }
            return Promise.resolve(true)
          })
        )

        return results.every((valid) => valid)
      } catch (error) {
        console.error(`${formName} 验证失败:`, error)
        return false
      }
    },

    handleItemChange(type, index, newData) {
      if (type === 'input') {
        this.$set(this.schemaForm.inputForm, index, newData)
      } else {
        this.$set(this.schemaForm.outputForm, index, newData)
      }
    },

    handleRemoveItem(type, index) {
      if (type === 'input') {
        this.schemaForm.inputForm.splice(index, 1)
      } else {
        this.schemaForm.outputForm.splice(index, 1)
      }
    },

    handleEditArray(area, { path, data }) {
      console.log('handleEditArray触发了没')
      this.currentArea = area
      this.currentArrayPath = path
      this.currentArrayData = JSON.parse(JSON.stringify(data))
      this.arrayModalVisible = true
    },

    handleArraySave(formData) {
      if (this.currentArea === 'input') {
        // 更新输入参数的数组
        const [_, index] = this.currentArrayPath // 解构路径 ['inputForm', index]
        this.$set(this.schemaForm.inputForm, index, formData)
      } else {
        // 更新输出参数的数组
        const [_, index] = this.currentArrayPath // 解构路径 ['outForm', index]
        this.$set(this.schemaForm.outputForm, index, formData)
      }
      this.arrayModalVisible = false
    },

    addRootItem(area) {
      const newItem = {
        id: uuidv4(),
        type: 'string',
        location: 'query',
        required: true,
        from: 2,
        open: true,
        startDisabled: false,
        nameErrMsg: '',
        descriptionErrMsg: '',
        name: '',
        description: '',
        defaultValue: '',
        // 不包含 fatherType 和 arraySon
      }
      if (area === 'input') {
        this.schemaForm.inputForm.push(newItem)
        // 添加后等待DOM更新，然后滚动到添加按钮位置
        this.$nextTick(() => {
          const inputAddBtnWrap = this.$refs.inputAddBtnWrap
          if (inputAddBtnWrap) {
            inputAddBtnWrap.scrollIntoView({ behavior: 'smooth', block: 'end' })
          }
        })
      } else {
        this.schemaForm.outputForm.push(newItem)
        // 添加后等待DOM更新，然后滚动到添加按钮位置
        this.$nextTick(() => {
          const outputAddBtnWrap = this.$refs.outputAddBtnWrap
          if (outputAddBtnWrap) {
            outputAddBtnWrap.scrollIntoView({
              behavior: 'smooth',
              block: 'end',
            })
          }
        })
      }
    },

    findItemByPath(items, path) {
      if (!path || !path.length) return null

      // 如果路径是数组索引的形式，例如 ['inputForm', 0, 'children', 1]
      if (Array.isArray(path)) {
        // 确定起始集合
        let current
        let startIndex = 0

        if (path[0] === 'inputForm') {
          current = this.schemaForm.inputForm
          startIndex = 1 // 跳过 'inputForm'
        } else if (path[0] === 'outputForm') {
          current = this.schemaForm.outputForm
          startIndex = 1 // 跳过 'outputForm'
        } else {
          // 如果路径不是以 'inputForm' 或 'outputForm' 开头，
          // 则尝试从当前项开始
          current = items || this.schemaForm.inputForm
        }

        // 遍历路径
        for (let i = startIndex; i < path.length; i++) {
          if (!current) return null

          const key = path[i]

          if (key === 'children') {
            // 如果是 'children' 属性，但是没有下一个索引，直接返回children数组
            if (i === path.length - 1) {
              return current.children || []
            }

            // 否则继续向下遍历
            if (current.children) {
              current = current.children
            } else {
              return null
            }
          } else if (typeof key === 'number' || !isNaN(parseInt(key))) {
            // 如果是数字索引
            const index = parseInt(key)
            if (Array.isArray(current) && index < current.length) {
              current = current[index]
            } else {
              return null // 索引超出范围
            }
          } else {
            // 其他属性
            if (current[key] !== undefined) {
              current = current[key]
            } else {
              return null // 属性不存在
            }
          }
        }

        return current
      }

      // 如果路径是对象ID形式
      return this.findItemById(items || this.schemaForm.inputForm, path)
    },

    // 通过ID查找项
    findItemById(items, id) {
      if (!items || !Array.isArray(items)) return null

      // 直接在当前层级查找
      const found = items.find((item) => item.id === id)
      if (found) return found

      // 递归查找子项
      for (const item of items) {
        if (item.children && Array.isArray(item.children)) {
          const result = this.findItemById(item.children, id)
          if (result) return result
        }
      }

      return null
    },
  },
}
</script>

<style lang="scss" scoped>
.box-card {
  height: 100%;
  overflow-y: auto;
  .box-header {
    display: flex;
    justify-content: space-between;
    align-content: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #d8d8d8;
    .box-header-left {
      display: flex;
      align-content: center;
      gap: 15px;
      img {
        width: 40px;
        height: 40px;
      }
      .icon-flod {
        display: inline-block;
        font-size: 16px;
        cursor: pointer;
        height: 36px;
        line-height: 36px;
      }
      .tool-name {
        font-size: 16px;
        transform: translateY(-2px);
        .el-tag {
          font-size: 10px;
          height: 18px;

          line-height: 17px;
        }
      }
    }

    .btns-wrap {
      display: flex;
      align-content: center;
      justify-content: space-between;
      .bnt {
        width: 50px;
        padding: 0px;
      }
    }
  }
  .api-content {
    .sub_title {
      font-size: 18px;
      color: #000;
      font-weight: 700;
      padding: 30px 0px 15px 0;
      border-bottom: 1px solid #d8d8d8;
      margin-bottom: 15px;
    }
    .tool-info {
      display: flex;
      align-content: center;
      justify-content: space-between;
      .el-form-item {
        margin-bottom: 0;
      }
      // 禁用输入框隐藏边框
      :deep(.el-input.is-disabled .el-input__inner) {
        border: none;
        box-shadow: none;
      }

      // 确保禁用状态下整个输入组的样式一致
      :deep(.el-input.is-disabled) {
        .el-input-group__prepend,
        .el-input-group__append,
        .el-input__inner {
          border: none;
          box-shadow: none;
        }
      }
    }
    .add_btn_wrap {
      padding: 15px 0 25px 0px;
      border-top: 1px solid #d8d8d8;
      button {
        border: none;
        color: #656769;
        background-color: #f5f6f8;
      }
    }
  }
  .api-footer {
    float: right;
    margin: 15px 0px;
  }
}
</style>
