<template>
  <el-dialog
    title="创建意图"
    :visible.sync="createIntentDialog.show"
    :close-on-click-modal="false"
    :before-close="handleClose"
    width="480px"
  >
    <el-form :model="form" :rules="rules" ref="intentForm" label-position="top">
      <!-- <el-form-item label="创建方式" prop="type" class="intent-type-wrap">
        <el-button :type="form.type == 2 ? 'primary' : ''" 
          plain @click="changeIntentType(2)">创建自定义意图</el-button>
        <el-button :type="form.type == 6 ? 'primary' : ''" 
          plain @click="changeIntentType(6)">引用官方意图</el-button>
      </el-form-item> -->
      <template v-if="form.type == 2">
        <el-form-item label="意图名称" prop="zhName">
          <el-input
            ref="zhNameInput"
            v-model.trim="form.zhName"
            placeholder="支持中文/英文/数字/小数点/下划线格式，不超过32个字符"
            @keyup.enter.native="toName"
          ></el-input>
        </el-form-item>
        <el-form-item label="英文标识" prop="name">
          <el-input
            ref="nameInput"
            v-model.trim="form.name"
            placeholder="支持英文/数字/小数点/下划线格式，不超过32个字符"
            @keyup.enter.native="save"
          />
        </el-form-item>
      </template>
      <template v-else>
        <el-form-item label="官方意图" prop="zhName">
          <el-select
            v-model="searchText"
            filterable
            placeholder="搜索 / 选择官方意图"
            style="width: 100%"
            no-match-text="暂无数据"
            :loading="searchLoading"
            @change="selectedItem"
            @visible-change="checkFormName"
          >
            <el-option
              v-for="item in systemIntents"
              :key="item.value"
              :label="item.label"
              :value="item"
              :disabled="item.checked ? true : false"
            >
              <span style="float: left">{{ item.label }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">{{
                item.value
              }}</span>
            </el-option>
          </el-select>
        </el-form-item>
      </template>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button
        class="dialog-btn"
        type="primary"
        @click="save"
        :loading="submitLoading"
      >
        {{ submitLoading ? '创建中...' : '创建' }}
      </el-button>
    </span>
  </el-dialog>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  data() {
    return {
      submitLoading: false,
      show: false,
      form: {
        type: 2,
        name: '',
        zhName: '',
      },
      rules: {
        type: [this.$rules.required()],
        name: [
          this.$rules.required('英文标识不能为空'),
          this.$rules.lengthLimit(1, 32, '英文标识长度不能超过32个字符'),
          this.$rules.englishReglimitForSkillIntent(),
          { validator: this.checkBoundary, trigger: ['blur'] },
        ],
        zhName: [
          this.$rules.required('意图名称不能为空'),
          this.$rules.lengthLimit(1, 32, '意图名称长度不能超过32个字符'),
          this.$rules.limitForSkillIntent(),
          { validator: this.checkBoundary, trigger: ['blur'] },
        ],
      },
      searchText: '',
      systemIntents: [],
      searchLoading: false,
    }
  },
  computed: {
    ...mapGetters({
      subAccount: 'user/subAccount',
    }),
    businessId() {
      return this.$store.state.studioSkill.id
    },
    createIntentDialog() {
      return this.$store.state.studioSkill.createIntentDialog
    },
  },
  watch: {
    'createIntentDialog.show': function (val, oldVal) {
      let self = this
      this.searchText = ''
      this.$refs['intentForm'] && this.$refs['intentForm'].resetFields()
      this.form = {
        type: 2,
        name: '',
        zhName: '',
      }
      this.$nextTick(function () {
        self.$refs.zhNameInput && self.$refs.zhNameInput.focus()
      })
      val ? this.getSystemIntents() : ''
    },
  },
  methods: {
    checkBoundary(rule, value, callback) {
      let reg = /^iFLYTEK\./i
      if (reg.test(value)) {
        callback(new Error('不能以iFLYTEK. 开头，不分大小写'))
      }
      callback()
    },
    getSystemIntents() {
      this.searchLoading = true
      this.systemIntents = []
      this.$utils.httpGet(
        this.$config.api.STUDIO_SYSTEM_INTENTS,
        {
          businessId: this.businessId,
          search: this.searchText,
        },
        {
          success: (res) => {
            this.searchLoading = false
            if (res && res.data && !res.data.count) {
              this.systemIntents = []
              return
            }
            Array.isArray(res.data.intents) &&
              res.data.intents.forEach((item) => {
                item.value = item.name
                item.label = item.zhName
                this.systemIntents.push(item)
              })
          },
          error: (err) => {
            this.searchLoading = false
            console.log('page=>>')
            console.log(err)
          },
        }
      )
    },
    checkZhName(rule, value, callback) {
      if (this.form.type == 2 && !value.trim()) {
        return callback(new Error('意图名称不能为空'))
      }
      if (this.form.type == 6 && !value.trim()) {
        return callback(new Error('官方意图不能为空'))
      }
      callback()
    },
    toName() {
      this.$refs.nameInput && this.$refs.nameInput.focus()
    },
    save() {
      let self = this
      let data = {
        businessId: this.businessId,
        name: this.form.name,
        zhName: this.form.zhName,
        type: this.form.type,
      }
      //判断是否是官方
      if (this.form.type == 6) {
        data.quoteId = this.form.quoteId
        data.name = this.form.name.substr(8)
      } else {
        data.arcSwitch = 1
      }

      this.$refs.intentForm.validate((valid) => {
        if (valid) {
          this.submitLoading = true
          this.$utils.httpPost(this.$config.api.STUDIO_ADD_EDIT_INTENT, data, {
            success: (res) => {
              self.$message.success('创建成功')
              self.submitLoading = false
              this.$store.dispatch('studioSkill/closeCreateIntentDialog')
              self.$emit('change', 1)
              if (self.form.type == 6) return
              this.subAccount
                ? self.$router.push({
                    name: 'sub-skill-intention',
                    params: { intentId: res.data },
                  })
                : self.$router.push({
                    name: 'skill-intention',
                    params: { intentId: res.data },
                  })
            },
            error: (err) => {
              this.submitLoading = false
              console.log('page=>>')
              console.log(err)
            },
          })
        }
      })
    },
    handleClose(done) {
      this.$store.dispatch('studioSkill/closeCreateIntentDialog')
      done()
    },
    changeIntentType(val) {
      let self = this
      this.$nextTick(function () {
        self.searchText = ''
        self.form = {
          type: val,
          name: '',
          zhName: '',
        }
      })
      this.$refs['intentForm'] && this.$refs['intentForm'].resetFields()
    },
    checkFormName(val) {
      if (!val) {
        this.$refs.intentForm &&
          this.$refs.intentForm.validate().catch((err) => {})
      }
    },
    selectedItem(val) {
      this.form.name = val.value
      this.form.zhName = val.label
      if (this.form.type == 6) {
        this.form.quoteId = val.id
      }
    },
  },
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="scss">
.intent-type-wrap .el-form-item__content {
  font-size: 0;
  .el-button {
    margin-left: 0;
    padding: 10px 16px;
    min-width: unset;
    width: 208px;
    background: $white;
    border-color: $grey3;
    border-right: none;
    border-radius: 0;
    &:first-child {
      border-radius: 2px 0 0 2px;
    }
    &:last-child {
      border-right: 1px solid $grey3;
      border-radius: 0 2px 2px 0;
      &:hover {
        border-right: 1px solid $primary;
      }
    }
  }
  .el-button:hover,
  .el-button:focus,
  .el-button--primary {
    border-color: $primary;
    background-color: $primary-light-12;
  }
  .el-button:hover + .el-button {
    border-left-color: $primary;
  }
  .el-button:focus + .el-button,
  .el-button--primary + .el-button {
    border-left-color: transparent;
  }
  .el-button--primary:hover,
  .el-button--primary:focus {
    box-shadow: unset;
  }
  .el-button--primary {
    color: $primary;
    border: 1px solid $primary !important;
  }
}
</style>
