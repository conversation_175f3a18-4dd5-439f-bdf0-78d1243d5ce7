'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var elementUi = require('element-ui');

var Main$1 = {
  render: function render() {
    var _vm = this;var _h = _vm.$createElement;var _c = _vm._self._c || _h;return _c('el-table-column', _vm._g(_vm._b({ scopedSlots: _vm._u([{ key: "header", fn: function fn(scope) {
          return [_vm._v("\n    " + _vm._s(_vm.$attrs.label) + "\n    "), _vm.$attrs.tip ? _c('el-tooltip', { attrs: { "effect": "dark", "content": _vm.$attrs.tip, "placement": "right" } }, [_c('i', { class: _vm.$attrs.icon })]) : _vm._e()];
        } }]) }, 'el-table-column', _vm.$attrs, false), _vm.$listeners), [_vm._v(" "), _vm._t("default")], 2);
  },
  staticRenderFns: [],
  name: 'OsTableColumn',
  props: {
    search: ''
  },
  computed: {},
  mounted: function mounted() {},

  methods: {}
};

var Main$2 = {
  render: function render() {
    var _vm = this;var _h = _vm.$createElement;var _c = _vm._self._c || _h;return _c('span', [_vm._v("\n  " + _vm._s(_vm.$attrs.label) + "\n  "), _vm.$attrs.tip ? _c('el-tooltip', { attrs: { "effect": "dark", "content": _vm.$attrs.tip, "placement": "right" } }, [_c('i', { staticClass: "el-icon-question" })]) : _vm._e()], 1);
  },
  staticRenderFns: [],
  name: 'OsTableQahead',
  props: {
    search: ''
  },
  computed: {},
  mounted: function mounted() {},

  methods: {}
};

var Main$3 = {
  render: function render() {
    var _vm = this;var _h = _vm.$createElement;var _c = _vm._self._c || _h;return _c('div', { staticClass: "os-icon-radios", style: _vm.styles.radios }, _vm._l(_vm.data, function (item, index) {
      return _c('div', { staticClass: "os-icon-radios-col", style: _vm.styles.radiosCol }, [_c('div', { staticClass: "os-icon-radio", class: { 'os-icon-radio-active': _vm.value == item.name }, on: { "click": function click($event) {
            return _vm.select(item.name);
          } } }, [_c('i', { class: item.icon }), _vm._v(" "), _c('p', [_vm._v(_vm._s(item.name))])])]);
    }), 0);
  },
  staticRenderFns: [],
  name: 'OsIconRadio',
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: {},
    data: Array,
    gutter: {
      type: Number,
      default: 20
    }
  },
  data: function data() {
    return {};
  },

  computed: {
    styles: function styles() {
      return {
        radios: {
          'margin-left': '-' + this.gutter / 2 + 'px',
          'margin-right': '-' + this.gutter / 2 + 'px'
        },
        radiosCol: {
          'padding-left': this.gutter / 2 + 'px',
          'padding-right': this.gutter / 2 + 'px'
        }
      };
    }
  },
  mounted: function mounted() {},

  methods: {
    select: function select(name) {
      this.$emit('change', name);
    }
  }
};

var Main$4 = {
  render: function render() {
    var _vm = this;var _h = _vm.$createElement;var _c = _vm._self._c || _h;return _c('div', { staticClass: "os-text-adder", class: { 'os-text-adder-disabled': _vm.disabled }, style: { width: _vm.width } }, [_c('ul', [_vm._l(_vm.data, function (item, index) {
      return _c('li', { key: index, staticClass: "os-text-adder-row" }, [_c('label', { staticClass: "os-text-adder-row-index" }, [_vm._v(_vm._s(index + 1))]), _vm._v(" "), _c('p', { staticClass: "os-text-adder-row-text", on: { "keyup": function keyup($event) {
            if (!$event.type.indexOf('key') && _vm._k($event.keyCode, "enter", 13, $event.key, "Enter")) {
              return null;
            }return _vm.inputBlur(item, index);
          } } }, [!_vm.dataKey ? [_c('el-input', { staticClass: "os-text-adder-row-input nodatakey", attrs: { "size": "medium", "placeholder": _vm.placeholder, "title": item, "readonly": _vm.readonly, "disabled": _vm.disabled }, on: { "blur": function blur($event) {
            return _vm.editt(_vm.data[index], index, arguments);
          }, "input": function input($event) {
            return _vm.changet(false, _vm.data[index], index);
          }, "focus": _vm.focusHandle }, model: { value: _vm.data[index], callback: function callback($$v) {
            _vm.$set(_vm.data, index, $$v);
          }, expression: "data[index]" } })] : [_c('el-input', { staticClass: "os-text-adder-row-input dataKey", attrs: { "size": "medium", "placeholder": _vm.placeholder, "disabled": _vm.disabled }, on: { "focus": _vm.focusHandle, "blur": function blur($event) {
            return _vm.editt(item, index, arguments);
          }, "input": function input($event) {
            return _vm.changet(false, item, index);
          } }, model: { value: item[_vm.dataKey], callback: function callback($$v) {
            _vm.$set(item, _vm.dataKey, $$v);
          }, expression: "item[dataKey]" } })], _vm._v(" "), _vm._t("default", null, { "item": item }), _vm._v(" "), !_vm.disabled ? _c('i', { staticClass: "os-text-adder-row-text-del el-icon-delete", on: { "click": function click($event) {
            return _vm.del(item, index);
          } } }) : _vm._e()], 2)]);
    }), _vm._v(" "), _vm.data.length < _vm.max ? _c('li', { staticClass: "os-text-adder-row 12 input-text" }, [_c('label', { staticClass: "os-text-adder-row-index" }, [_vm._v(_vm._s(_vm.data.length + 1))]), _vm._v(" "), _c('p', { staticClass: "os-text-adder-row-text", on: { "keyup": function keyup($event) {
          if (!$event.type.indexOf('key') && _vm._k($event.keyCode, "enter", 13, $event.key, "Enter")) {
            return null;
          }return _vm.add($event);
        } } }, [_c('el-input', { staticClass: "os-text-adder-row-input", attrs: { "size": "medium", "placeholder": _vm.placeholder, "disabled": _vm.disabled }, on: { "focus": _vm.focusHandle, "blur": _vm.add, "input": function input($event) {
          return _vm.change(true, arguments);
        } }, model: { value: _vm.text, callback: function callback($$v) {
          _vm.text = $$v;
        }, expression: "text" } }), _vm._v(" "), _c('input', { staticStyle: { "display": "none" }, attrs: { "type": "text" } })], 1)]) : _vm._e()], 2)]);
  },
  staticRenderFns: [],
  name: 'OsTextAdder',
  props: {
    data: Array,
    dataKey: {
      type: String,
      default: ''
    },
    width: {
      type: String,
      default: '100%'
    },
    placeholder: {
      type: String,
      default: ''
    },
    reg: {
      default: ''
    },
    warning: {
      type: String,
      default: '输入有误'
    },
    max: {
      type: Number,
      default: 999
    },
    readonly: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data: function data() {
    return {
      text: ''
    };
  },

  computed: {},
  methods: {
    add: function add() {
      this.removeHover(event.target.parentNode.parentNode.parentNode);
      var self = this;
      var emit = true;

      this.text = this.text.trim();
      if (!this.text) {
        return;
      }
      this.data.forEach(function (item, index) {
        var itemText = '';
        if (self.dataKey) {
          itemText = item[self.dataKey];
        } else {
          itemText = item;
        }
        if (itemText === self.text) {
          emit = false;
          return self.$message.warning('请勿重复添加');
        }
      });
      if (this.reg && !this.reg.test(this.text)) {
        emit = false;
        return this.$message.warning(this.warning);
      }
      if (emit) {
        this.$emit('add', this.text);
        this.text = '';
        document.getElementsByClassName('input-text')[0].className += ' hover';
      }
    },
    inputBlur: function inputBlur(item, index) {
      if (!item) {
        this.$emit('del', item, index);
      }
      event.target.blur();
    },
    edit: function edit(index, event) {
      event.target.value = event.target.value.trim();
      this.$emit('edit', event.target.value, index);
    },
    editt: function editt(item, index, arr) {
      this.removeHover(arr[0].srcElement.parentNode.parentNode.parentNode);
      var itemText = '';
      if (this.dataKey) {
        itemText = item[this.dataKey];
      } else {
        itemText = item;
      }
      if (this.reg && !this.reg.test(itemText)) {
        return this.$message.warning(this.warning);
      } else {
        this.$emit('edit', item, index);
      }
    },
    del: function del(item, index) {
      this.$emit('del', item, index);
    },
    delText: function delText() {
      this.text = '';
    },
    change: function change(v, arr) {
      this.$emit('change', v, event.target.value);
    },
    changet: function changet(v, item, index) {
      var itemText = '';
      if (this.dataKey) {
        itemText = item[this.dataKey];
      } else {
        itemText = item;
      }
      if (!(this.reg && !this.reg.test(itemText))) {
        this.$emit('edit', item, index);
        this.$emit('change', v, event.target.value, index);
      }
    },
    addHover: function addHover(e) {
      if (e.className.indexOf('hover') === -1) {
        e.className += ' hover';
      }
    },
    removeHover: function removeHover(e) {
      if (e.className.indexOf('hover') !== -1) {
        e.className = e.className.replace('hover', '');
        e.className = e.className.replace(/\s*$/g, '');
      }
    },
    changeInputValue: function changeInputValue(v) {
      this.text = v;
    },
    focusHandle: function focusHandle(e) {
      this.addHover(e.srcElement.parentNode.parentNode.parentNode);
    }
  }
};

var Main$5 = {
  render: function render() {
    var _vm = this;var _h = _vm.$createElement;var _c = _vm._self._c || _h;return _c('div', { staticClass: "os-text-star-adder", style: { 'width': _vm.width } }, [_c('ul', [_vm._l(_vm.data, function (item, index) {
      return _c('li', { key: index, staticClass: "os-text-star-adder-row" }, [_c('label', { staticClass: "os-text-star-adder-row-index" }, [_vm._v(_vm._s(index + 1))]), _vm._v(" "), _c('div', { staticClass: "os-text-star-adder-row-text", on: { "keyup": function keyup($event) {
            if (!$event.type.indexOf('key') && _vm._k($event.keyCode, "enter", 13, $event.key, "Enter")) {
              return null;
            }return _vm.inputBlur($event);
          } } }, [_c('el-input', { staticClass: "os-text-star-adder-row-input", attrs: { "size": "big", "placeholder": _vm.placeholder, "value": item.text, "title": item.text, "readonly": _vm.readonly }, on: { "blur": function blur($event) {
            return _vm.edit(index, $event);
          } }, model: { value: item.text, callback: function callback($$v) {
            _vm.$set(item, "text", $$v);
          }, expression: "item.text" } }), _vm._v(" "), _vm._t("default", null, { "item": item }), _vm._v(" "), item && item.score > 0 ? _c('div', [_c('p', { staticClass: "star-wrap" }, _vm._l(item.score, function (star) {
        return _c('i', { key: star, class: ['os-text-star-adder-row-text-star el-icon-star-on', { 'star-success': item.score && item.score > 3 }] });
      }), 0), _vm._v(" "), _c('p', { class: ['star-desc', { 'star-desc-success': item.score && item.score > 3 }] }, [_vm._v(_vm._s(item.desc))]), _vm._v(" "), _c('i', { staticClass: "os-text-star-adder-row-del el-icon-delete", on: { "click": function click($event) {
            return _vm.del(item, index);
          } } })]) : _c('div', { directives: [{ name: "loading", rawName: "v-loading", value: _vm.starLoading, expression: "starLoading" }] })], 2)]);
    }), _vm._v(" "), _vm.data.length < _vm.max ? _c('li', { staticClass: "os-text-star-adder-row" }, [_c('label', { staticClass: "os-text-star-adder-row-index" }, [_vm._v(_vm._s(_vm.data.length + 1))]), _vm._v(" "), _c('p', { staticClass: "os-text-star-adder-row-text", on: { "keyup": function keyup($event) {
          if (!$event.type.indexOf('key') && _vm._k($event.keyCode, "enter", 13, $event.key, "Enter")) {
            return null;
          }return _vm.add($event);
        } } }, [_c('el-input', { staticClass: "os-text-star-adder-row-input", attrs: { "size": "big", "placeholder": _vm.placeholder }, on: { "blur": _vm.add }, model: { value: _vm.text, callback: function callback($$v) {
          _vm.text = typeof $$v === 'string' ? $$v.trim() : $$v;
        }, expression: "text" } }), _vm._v(" "), _c('input', { staticStyle: { "display": "none" }, attrs: { "type": "text" } })], 1)]) : _vm._e()], 2)]);
  },
  staticRenderFns: [],
  name: 'OsTextStarAdder',
  props: {
    data: Array,
    width: {
      type: String,
      default: '100%'
    },
    placeholder: {
      type: String,
      default: ''
    },
    reg: {
      default: ''
    },
    warning: {
      type: String,
      default: '输入有误'
    },
    max: {
      type: Number,
      default: 999
    },
    readonly: {
      type: Boolean,
      default: false
    }
  },
  data: function data() {
    return {
      text: '',
      starLoading: true
    };
  },

  methods: {
    add: function add() {
      var self = this;
      var emit = true;
      if (!this.text) {
        return;
      }
      self.data.forEach(function (item, index) {
        var itemText = item.text;
        if (itemText === self.text) {
          emit = false;
          return self.$message.warning('请勿重复添加');
        }
      });
      if (this.reg && !this.reg.test(this.text)) {
        emit = false;
        return this.$message.warning(this.warning);
      }
      if (emit) {
        this.$emit('add', this.text);
        this.text = '';
      }
    },
    inputBlur: function inputBlur(event) {
      event.target.blur();
    },
    edit: function edit(index, event) {
      if (!event.target.value) {
        this.del('', index);
        return;
      }
      this.$emit('edit', event.target.value, index);
    },
    editt: function editt(item, index) {
      this.$emit('edit', item, index);
    },
    del: function del(item, index) {
      this.$emit('del', item, index);
    },
    delText: function delText() {
      this.text = '';
    }
  }
};

var BlueFileSvg = 'data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMjRweCIgaGVpZ2h0PSIyNHB4IiB2aWV3Qm94PSIwIDAgMjQgMjQiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8IS0tIEdlbmVyYXRvcjogU2tldGNoIDUyLjYgKDY3NDkxKSAtIGh0dHA6Ly93d3cuYm9oZW1pYW5jb2RpbmcuY29tL3NrZXRjaCAtLT4KICAgIDx0aXRsZT5faW1hZ2VzIC8gVGludGVkIEljb25zIC8gaWMtZmlsZS1ibHVlPC90aXRsZT4KICAgIDxkZXNjPkNyZWF0ZWQgd2l0aCBTa2V0Y2guPC9kZXNjPgogICAgPGcgaWQ9Il9pbWFnZXMtLy1UaW50ZWQtSWNvbnMtLy1pYy1maWxlLWJsdWUiIHN0cm9rZT0ibm9uZSIgc3Ryb2tlLXdpZHRoPSIxIiBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPgogICAgICAgIDxwYXRoIGQ9Ik00LjUsMiBMMTQsMiBMMjAuMDUzNTM5OSw2LjA1MzI0ODQxIEwyMC4wNTM1Mzk5LDIxLjUgQzIwLjA1MzUzOTksMjEuNzc2MTQyNCAxOS44Mjk2ODIzLDIyIDE5LjU1MzUzOTksMjIgTDQuNSwyMiBDNC4yMjM4NTc2MywyMiA0LDIxLjc3NjE0MjQgNCwyMS41IEw0LDIuNSBDNCwyLjIyMzg1NzYzIDQuMjIzODU3NjMsMiA0LjUsMiBaIiBpZD0iUmVjdGFuZ2xlIiBmaWxsLW9wYWNpdHk9IjAuMTIiIGZpbGw9IiMxNzg0RTkiPjwvcGF0aD4KICAgICAgICA8cGF0aCBkPSJNNC4zMzMzMzMzMywxLjAwMTg2MTU3IEwxNC4yMTg5NjQxLDEuMDAxODYxNTcgQzE0LjQ0MTMzOTksMS4wMDE4NjE1NyAxNC42NTczNzE4LDEuMDc1OTgzNzggMTQuODMyOTA0NywxLjIxMjUwOTM1IEwyMC42MTM5NDA2LDUuNzA4ODcwNjIgQzIwLjg1NzUyNzIsNS44OTgzMjY4MiAyMSw2LjE4OTYzMjQxIDIxLDYuNDk4MjIyODQgTDIxLDIxLjY1MTk1MTYgQzIxLDIyLjM4ODMzMTMgMjAuNDAzMDQ2MywyMyAxOS42NjY2NjY3LDIzIEw0LjMzMzMzMzMzLDIzIEMzLjU5Njk1MzY3LDIzIDMsMjIuMzg4MzMxMyAzLDIxLjY1MTk1MTYgTDMsMi4zMzUxOTQ5MSBDMywxLjU5ODgxNTI0IDMuNTk2OTUzNjcsMS4wMDE4NjE1NyA0LjMzMzMzMzMzLDEuMDAxODYxNTcgWiBNMTQuMzMzMzMzMyw2LjMzNTE5NDkxIEwxOS4yNDc0NTIsNi4zMzUxOTQ5MSBMMTQuMzMzMzMzMywyLjUxMzEwMjYzIEwxNC4zMzMzMzMzLDYuMzM1MTk0OTEgWiBNMTMsMi4zMzUxOTQ5MSBMNC4zMzMzMzMzMywyLjMzNTE5NDkxIEw0LjMzMzMzMzMzLDIxLjY1MTk1MTYgTDE5LjY2NjY2NjcsMjEuNjUxOTUxNiBMMTkuNjY2NjY2Nyw3LjY2ODUyODI0IEwxNC4zMzMzMzMzLDcuNjY4NTI4MjQgQzEzLjU5Njk1MzcsNy42Njg1MjgyNCAxMyw3LjA3MTU3NDU3IDEzLDYuMzM1MTk0OTEgTDEzLDIuMzM1MTk0OTEgWiIgaWQ9IlNoYXBlIiBmaWxsPSIjMTc4NEU5IiBmaWxsLXJ1bGU9Im5vbnplcm8iPjwvcGF0aD4KICAgIDwvZz4KPC9zdmc+';

var RedFileSvg = 'data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMjRweCIgaGVpZ2h0PSIyNHB4IiB2aWV3Qm94PSIwIDAgMjQgMjQiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8IS0tIEdlbmVyYXRvcjogU2tldGNoIDUyLjYgKDY3NDkxKSAtIGh0dHA6Ly93d3cuYm9oZW1pYW5jb2RpbmcuY29tL3NrZXRjaCAtLT4KICAgIDx0aXRsZT5faW1hZ2VzIC8gVGludGVkIEljb25zIC8gaWMtZmlsZS1yZWQ8L3RpdGxlPgogICAgPGRlc2M+Q3JlYXRlZCB3aXRoIFNrZXRjaC48L2Rlc2M+CiAgICA8ZyBpZD0iX2ltYWdlcy0vLVRpbnRlZC1JY29ucy0vLWljLWZpbGUtcmVkIiBzdHJva2U9Im5vbmUiIHN0cm9rZS13aWR0aD0iMSIgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj4KICAgICAgICA8cGF0aCBkPSJNNC41LDIgTDE0LDIgTDIwLjA1MzUzOTksNi4wNTMyNDg0MSBMMjAuMDUzNTM5OSwyMS41IEMyMC4wNTM1Mzk5LDIxLjc3NjE0MjQgMTkuODI5NjgyMywyMiAxOS41NTM1Mzk5LDIyIEw0LjUsMjIgQzQuMjIzODU3NjMsMjIgNCwyMS43NzYxNDI0IDQsMjEuNSBMNCwyLjUgQzQsMi4yMjM4NTc2MyA0LjIyMzg1NzYzLDIgNC41LDIgWiIgaWQ9IlJlY3RhbmdsZSIgZmlsbC1vcGFjaXR5PSIwLjEyIiBmaWxsPSIjRkYzODM4Ij48L3BhdGg+CiAgICAgICAgPHBhdGggZD0iTTQuMzMzMzMzMzMsMS4wMDE4NjE1NyBMMTQuMjE4OTY0MSwxLjAwMTg2MTU3IEMxNC40NDEzMzk5LDEuMDAxODYxNTcgMTQuNjU3MzcxOCwxLjA3NTk4Mzc4IDE0LjgzMjkwNDcsMS4yMTI1MDkzNSBMMjAuNjEzOTQwNiw1LjcwODg3MDYyIEMyMC44NTc1MjcyLDUuODk4MzI2ODIgMjEsNi4xODk2MzI0MSAyMSw2LjQ5ODIyMjg0IEwyMSwyMS42NTE5NTE2IEMyMSwyMi4zODgzMzEzIDIwLjQwMzA0NjMsMjMgMTkuNjY2NjY2NywyMyBMNC4zMzMzMzMzMywyMyBDMy41OTY5NTM2NywyMyAzLDIyLjM4ODMzMTMgMywyMS42NTE5NTE2IEwzLDIuMzM1MTk0OTEgQzMsMS41OTg4MTUyNCAzLjU5Njk1MzY3LDEuMDAxODYxNTcgNC4zMzMzMzMzMywxLjAwMTg2MTU3IFogTTE0LjMzMzMzMzMsNi4zMzUxOTQ5MSBMMTkuMjQ3NDUyLDYuMzM1MTk0OTEgTDE0LjMzMzMzMzMsMi41MTMxMDI2MyBMMTQuMzMzMzMzMyw2LjMzNTE5NDkxIFogTTEzLDIuMzM1MTk0OTEgTDQuMzMzMzMzMzMsMi4zMzUxOTQ5MSBMNC4zMzMzMzMzMywyMS42NTE5NTE2IEwxOS42NjY2NjY3LDIxLjY1MTk1MTYgTDE5LjY2NjY2NjcsNy42Njg1MjgyNCBMMTQuMzMzMzMzMyw3LjY2ODUyODI0IEMxMy41OTY5NTM3LDcuNjY4NTI4MjQgMTMsNy4wNzE1NzQ1NyAxMyw2LjMzNTE5NDkxIEwxMywyLjMzNTE5NDkxIFoiIGlkPSJTaGFwZSIgZmlsbD0iI0ZGMzgzOCIgZmlsbC1ydWxlPSJub256ZXJvIj48L3BhdGg+CiAgICA8L2c+Cjwvc3ZnPg==';

var UploadingItem = {
  render: function render() {
    var _vm = this;var _h = _vm.$createElement;var _c = _vm._self._c || _h;return _c('li', { staticClass: "os-upload__file" }, [_c('embed', { staticClass: "os-upload__file__ic-file", attrs: { "src": _vm.BlueFileSvg, "width": "16", "height": "18", "type": "image/svg+xml" } }), _vm._v(" "), _c('span', { staticClass: "os-upload__file__name" }, [_vm._v(_vm._s(_vm.uploadStatus.name))]), _vm._v(" "), _vm.uploadStatus.loading ? _c('span', { staticClass: "os-upload__file__percent" }, [_vm._v(_vm._s(_vm.percent))]) : [_vm.uploadStatus.success ? _c('i', { staticClass: "os-upload__file__ic-success ic-r-tick-oval" }) : _vm._e()], _vm._v(" "), _c('div', { staticClass: "os-upload__file__bar" }, [_c('div', { staticClass: "os-upload__file__bar-inner", style: _vm.barPercent })])], 2);
  },
  staticRenderFns: [],
  props: {
    uploadStatus: Object
  },
  data: function data() {
    return {
      BlueFileSvg: BlueFileSvg
    };
  },

  computed: {
    percent: function percent() {
      return parseInt(this.uploadStatus.loaded / this.uploadStatus.total * 100) + '%';
    },
    barPercent: function barPercent() {
      return {
        'width': this.uploadStatus.loaded / this.uploadStatus.total * 100 + '%'
      };
    }
  },
  mounted: function mounted() {},

  methods: {}
};

var UploadedItem = {
  render: function render() {
    var _vm = this;var _h = _vm.$createElement;var _c = _vm._self._c || _h;return _c('li', { staticClass: "os-upload__file os-upload__file-uploaded", class: { 'os-upload__file__active': _vm.visible } }, [_c('embed', { staticClass: "os-upload__file__ic-file", attrs: { "src": _vm.BlueFileSvg, "width": "16", "height": "18", "type": "image/svg+xml" } }), _vm._v(" "), _c('span', { staticClass: "os-upload__file__name" }, [_vm._v(_vm._s(_vm.file.name))]), _vm._v(" "), _vm.success ? _c('i', { staticClass: "os-upload__file__ic-success ic-r-tick-oval" }) : [_c('i', { staticClass: "os-upload__file__ic-download ic-r-download", on: { "click": function click($event) {
          return _vm.download(_vm.file);
        } } }), _vm._v(" "), _c('el-popover', { attrs: { "placement": "bottom-end", "width": "240", "trigger": "click" }, model: { value: _vm.visible, callback: function callback($$v) {
          _vm.visible = $$v;
        }, expression: "visible" } }, [_c('div', { staticClass: "give-up-save-title" }, [_c('i', { staticClass: "ic-r-exclamation" }), _vm._v(" "), _c('span', [_vm._v("确定删除吗？")])]), _vm._v(" "), _c('p', { staticClass: "give-up-save-content" }, [_vm._v("删除后不可恢复")]), _vm._v(" "), _c('div', { staticStyle: { "text-align": "right", "margin": "0" } }, [_c('el-button', { staticStyle: { "min-width": "64px" }, attrs: { "size": "mini" }, on: { "click": function click($event) {
          _vm.visible = false;
        } } }, [_vm._v("取消")]), _vm._v(" "), _c('el-button', { staticStyle: { "min-width": "64px" }, attrs: { "type": "danger", "size": "mini" }, on: { "click": _vm.remove } }, [_vm._v("确定")])], 1), _vm._v(" "), _c('i', { staticClass: "os-upload__file__ic-del ic-r-delete", attrs: { "slot": "reference" }, slot: "reference" })])]], 2);
  },
  staticRenderFns: [],
  props: {
    file: Object
  },
  data: function data() {
    return {
      BlueFileSvg: BlueFileSvg,
      success: false,
      visible: false
    };
  },

  computed: {},
  mounted: function mounted() {
    var self = this;
    if (this.file && this.file.loadingEnd) {
      this.success = true;
      setTimeout(function () {
        self.success = false;
      }, 2000);
    }
  },

  watch: {},
  methods: {
    download: function download(file) {
      if (!/(http|https):\/\/([\w.]+\/?)\S*/.test(file.url)) {
        this.$emit('downLoadFromOss', file.url);
      } else {
        var url = file.url;
        if (!file.isBlobFile) {
          //文件可以直接下载
          window.open(url);
          return;
        }
        var filename = file.name || url.substring(url.lastIndexOf('/') + 1).split('?')[0];
        var xhr = new XMLHttpRequest();
        xhr.responseType = 'blob';
        xhr.onload = function () {
          var a = document.createElement('a');
          a.href = window.URL.createObjectURL(xhr.response);
          a.download = filename;
          a.style.display = 'none';
          document.body.appendChild(a);
          a.click();
        };
        xhr.open('GET', url);
        xhr.send();
      }
    },
    remove: function remove() {
      this.$parent.remove(this.file);
      this.visible = false;
    }
  }
};

var crypt = require('crypto');

var FileTypes = {
  'image': 'image/*',
  'png': 'image/png, .png',
  'jpg': 'image/jpeg, .jpg, .jpeg',
  'doc': '.doc,.docx,.xml,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'apk': 'application/vnd.android.package-archive',
  'zip': 'application/zip',
  'mp3': '.mp3, audio/mp3',
  'm3u': '.m3u, .m3u8, audio/m3u, audio/mpegurl, application/x-mpegurl, audio/x-mpegurl',
  'aac': '.aac, audio/aac, audio/vnd.dlna.adts',
  'excel': '.csv, .xlsx, .xls, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel, text/csv',
  'js': 'text/javascript, application/javascript',
  'mp4': '.mp4, audio/mp4, video/mp4',
  'm4a': '.m4a, audio/x-m4a, video/x-m4a, audio/m4a, video/m4a',
  'wav': '.wav, audio/wav, video/wav',
  'pcm': '.pcm, audio/pcm, video/pcm',
  'json': '.json',
  'pdf': '.pdf'
};
var Main$6 = {
  render: function render() {
    var _vm = this;var _h = _vm.$createElement;var _c = _vm._self._c || _h;return _c('div', { staticClass: "os-upload", class: { 'os-upload__list-right': _vm.options.listRight, 'os-upload__list-right-middle': _vm.options.numLimit === 1 && _vm.type === 'button' } }, [_c('el-upload', { ref: "upload", class: { 'os-drap-upload': _vm.type === 'drag', 'os-btn-upload': _vm.type === 'button' }, attrs: { "drag": _vm.type === 'drag', "action": _vm.options.action || '', "data": _vm.options.data, "multiple": true, "on-exceed": _vm.exceed, "limit": _vm.options.numLimit, "show-file-list": false, "file-list": _vm.fileList, "on-change": _vm.change, "auto-upload": _vm.options.autoUpload !== undefined ? _vm.options.autoUpload : true, "disabled": _vm.options.disabled !== undefined ? _vm.options.disabled : false, "before-upload": _vm.beforeUpload, "http-request": _vm.upload, "accept": _vm.accept } }, [_vm.type === 'drag' ? [_c('i', { staticClass: "ic-r-upload" }), _vm._v(" "), _c('p', { staticClass: "el-upload__text" }, [_vm._v(_vm._s(_vm.options.mainText))]), _vm._v(" "), _c('p', { staticClass: "el-upload__tips" }, [_vm._v(_vm._s(_vm.options.mainTips))]), _vm._v(" "), _c('div', { staticClass: "el-upload__tip", attrs: { "slot": "tip" }, slot: "tip" }, [_vm._v(_vm._s(_vm.options.tips))])] : _vm._e(), _vm._v(" "), _vm.type === 'button' ? [_c('el-button', { class: _vm.options.btnClass || '', attrs: { "size": _vm.btnSize || 'small', "type": _vm.options.btnType || 'primary' } }, [!_vm.options.btnNoIcon ? _c('i', { staticClass: "el-icon-upload" }) : _vm._e(), _vm._v("\n        " + _vm._s(_vm.options.btnText || '点击上传') + "\n      ")]), _vm._v(" "), _vm.options.mainText ? _c('p', { staticStyle: { "margin-left": "16px" }, domProps: { "innerHTML": _vm._s(_vm.options.mainText) } }) : _vm._e()] : _vm._e()], 2), _vm._v(" "), _c('ul', { staticClass: "os-upload__file-list" }, [_vm._l(_vm.uploadFileList, function (file, index) {
      return _c('uploading-item', { key: "index" + index, attrs: { "uploadStatus": file } });
    }), _vm._v(" "), _vm._l(_vm.fileList, function (file, index) {
      return _c('uploaded-item', { key: index, attrs: { "file": file }, on: { "downLoadFromOss": _vm.downLoadFromOss } });
    })], 2)], 1);
  },
  staticRenderFns: [],
  name: 'OsUpload',
  props: {
    type: {
      type: String,
      default: 'drag'
    },
    options: Object,
    fileList: Array
  },
  data: function data() {
    return {
      btnSize: '',
      FileTypes: FileTypes,
      BlueFileSvg: BlueFileSvg,
      RedFileSvg: RedFileSvg,
      uploadFileList: [],
      visible: false
    };
  },

  computed: {
    sizeLimit: function sizeLimit() {
      var size = this.options.sizeLimit / 1;
      var count = 0;
      while (size > 1024) {
        size = size / 1024;
        count += 1;
      }
      var units = {
        0: 'b',
        1: 'kb',
        2: 'M',
        3: 'G'
      };
      return '' + size + units[count];
    },
    accept: function accept() {
      var accept = '';
      if (this.options.fileType && this.options.fileType.length) {
        accept = '';
        for (var i = 0; i <= this.options.fileType.length - 1; i++) {
          if (accept) {
            accept += ',' + FileTypes[this.options.fileType[i]];
          } else {
            accept = FileTypes[this.options.fileType[i]];
          }
        }
      } else if (this.options.fileTypeStr) {
        accept = this.options.fileTypeStr;
      }
      return accept;
    }
  },
  mounted: function mounted() {},

  methods: {
    change: function change(file, fileList) {
      var flag = void 0;
      var files = this.$refs.upload.$el.getElementsByClassName('el-upload__input')[0].files;
      if (this.options.beforeUpload) {
        // console.log(files)
        // let arr = files ? Array.from(files, {}) : []
        flag = this.options.beforeUpload(files);
        flag.then(function (done) {
          if (done) {
            fileList.splice(0, fileList.length);
            console.log('清空filelist', fileList);
          }
        });
      }
    },
    exceed: function exceed() {
      if (this.fileList && this.fileList.length >= this.options.numLimit) {
        this.$message.error('超过上传数量');
      }
    },
    beforeUpload: function beforeUpload(file) {
      var sizeLt = file.size < this.options.sizeLimit;
      if (!sizeLt) {
        this.$message.error('\u6587\u4EF6\u5927\u5C0F\u4E0D\u80FD\u8D85\u8FC7' + this.sizeLimit);
        return false;
      }
      var isType = false;
      if (this.options.fileType && this.options.fileType.length) {
        for (var i = 0; i <= this.options.fileType.length - 1; i++) {
          isType = file.type === this.options.fileType[i] || FileTypes[this.options.fileType[i]].indexOf(file.type) >= 0 || file.type.indexOf(this.options.fileType[i]) >= 0;
          if (isType) {
            break;
          }
        }
        if (!isType) {
          this.$message.error('\u4E0D\u652F\u6301\u8BE5\u6587\u4EF6\u7C7B\u578B');
        }
      } else if (this.options.fileTypeStr) {
        console.log(file);
        if (file.type) {
          isType = this.options.fileTypeStr.indexOf(file.type) >= 0 || file.type.indexOf(this.options.fileTypeStr) >= 0;
        } else {
          var arr = file.name && file.name.split('.');
          var type = arr[arr.length - 1];
          isType = this.options.fileTypeStr.indexOf(type) >= 0 || type.indexOf(this.options.fileTypeStr) >= 0;
        }
        if (!isType) {
          this.$message.error('\u4E0D\u652F\u6301\u8BE5\u6587\u4EF6\u7C7B\u578B');
        }
      } else {
        isType = true;
      }
      // return sizeLt && isType
      var fileNameAccept = true;
      if (this.options.fileNameRule && !new RegExp(this.options.fileNameRule).test(file.name)) {
        this.$message.error(this.options.fileNameTips || '文件名格式有误');
        fileNameAccept = false;
      }
      return sizeLt && isType && fileNameAccept;
    },
    upload: function upload(param) {
      var _this = this;

      var self = this;
      var md5 = crypt.createHash('md5');
      var md5string = param.file.name + param.file.lastModified + param.file.size;
      var fileNameSplit = param.file.name.split('.');
      var tail = '';
      var name = '';
      if (fileNameSplit.length > 1) {
        tail = '.' + fileNameSplit[fileNameSplit.length - 1];
        fileNameSplit.pop();
        name = fileNameSplit.join('.');
      } else {
        name = fileNameSplit[0];
      }

      var fileName = void 0;
      console.log(this.options.md5);
      md5.update(md5string);
      fileName = (this.options.setPath && this.options.setPath(param.file) || this.options.stsPath) + '/' + (this.options.noFileName ? '' : name + '.') + md5.digest('hex') + tail;
      if (this.options.md5 === false) {
        fileName = (this.options.setPath && this.options.setPath(param.file) || this.options.stsPath) + '/' + (this.options.noFileName ? '' : name) + tail;
      }
      console.log(fileName);
      this.uploadFileList.unshift({
        name: param.file.name,
        uploadName: fileName,
        total: 100,
        loaded: 0,
        loading: true,
        success: false
      });
      if (this.options.ossService) {
        this.options.ossService.putToOss(this, fileName, param.file, {
          progress: self.uploadProgress,
          complete: self.uploadComplete,
          fail: self.uploadFail
        });
      } else {
        var formData = new FormData();
        formData.append('file', param.file);
        formData.append('type', param.file.type);
        var keys = [];
        for (var key in this.options.data || {}) {
          keys.push(key);
        }
        Array.prototype.forEach.call(keys, function (key) {
          formData.append(key, _this.options.data[key]);
        });
        console.log(formData.values());
        if (this.$utils && this.$utils.httpPost) {
          this.$utils.httpPost(param.action, formData, {
            config: {
              onUploadProgress: self.uploadProgress
            },
            noErrMsg: true,
            success: self.uploadComplete,
            error: self.uploadFail
          });
        } else {
          this.uploadFail();
        }
      }
    },

    // 上传过程
    uploadProgress: function uploadProgress(res, key) {
      if (key) {
        this.uploadFileList = Array.prototype.map.call(this.uploadFileList, function (item, index) {
          if (item.uploadName === key) {
            item.total = res.total;
            item.loaded = res.loaded;
          }
          return item;
        });
      } else {
        this.uploadFileList[0].total = res.total;
        this.uploadFileList[0].loaded = res.loaded;
      }
    },

    // 上传完成
    uploadComplete: function uploadComplete(res, key) {
      var name = '';
      if (key) {
        this.uploadFileList = Array.prototype.map.call(this.uploadFileList, function (item, index) {
          if (item.uploadName === key) {
            name = item.name;
          }
          return item;
        });
        this.uploadFileList = Array.prototype.filter.call(this.uploadFileList, function (item, index) {
          return item.uploadName !== key;
        });
        this.$emit('completecb', { name: name, url: key, loadingEnd: true });
      } else {
        var self = this;
        this.uploadFileList = Array.prototype.map.call(this.uploadFileList, function (item, index) {
          item.loading = false;
          item.success = true;
          item.total = 100;
          item.loaded = 100;
          return item;
        });
        setTimeout(function () {
          self.uploadFileList = [];
          self.$emit('completecb', res);
        }, 2000);
      }
    },

    // 上传失败
    uploadFail: function uploadFail(res, key) {
      this.$message.error('上传失败，请重试');
      if (key) {
        this.uploadFileList = Array.prototype.filter.call(this.uploadFileList, function (item, index) {
          return item.uploadName !== key;
        });
      } else {
        this.uploadFileList = [];
      }
      this.$emit('failcb', res);
    },
    remove: function remove(file) {
      this.$emit('remove', file);
    },
    downLoadFromOss: function downLoadFromOss(key) {
      if (this.options.ossService) {
        this.options.ossService.getFromOss(key);
      }
    }
  },
  components: {
    UploadingItem: UploadingItem,
    UploadedItem: UploadedItem
  }
};

var Main$7 = {
  render: function render() {
    var _vm = this;var _h = _vm.$createElement;var _c = _vm._self._c || _h;return _c('div', { staticClass: "el-pagination el-pagination--small el-pagination--simple" }, [_c('button', { staticClass: "btn-prev", attrs: { "type": "button", "disabled": !_vm.page || _vm.page === 1 }, on: { "click": _vm.prePage } }, [_c('i', { staticClass: "el-icon el-icon-arrow-left" })]), _vm._v(" "), _c('span', { staticClass: "el-pagination__jump", on: { "keyup": function keyup($event) {
          if (!$event.type.indexOf('key') && _vm._k($event.keyCode, "enter", 13, $event.key, "Enter")) {
            return null;
          }return _vm.handleEnter($event);
        } } }, [_c('el-input', { ref: "pageInput", staticClass: "el-pagination__editor is-in-pagination", attrs: { "type": "number", "autocomplete": "off", "min": "1", "max": _vm.totalPage }, on: { "blur": function blur($event) {
          return _vm.$emit('change', _vm.page);
        } }, model: { value: _vm.page, callback: function callback($$v) {
          _vm.page = $$v;
        }, expression: "page" } }), _vm._v(" "), _vm._v("\n    / " + _vm._s(_vm.totalPage) + "\n  ")], 1), _vm._v(" "), _c('button', { staticClass: "btn-next", attrs: { "type": "button", "disabled": !_vm.page || _vm.page === _vm.totalPage }, on: { "click": _vm.nextPage } }, [_c('i', { staticClass: "el-icon el-icon-arrow-right" })])]);
  },
  staticRenderFns: [],
  name: 'OsPagination',
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: {},
    total: {
      type: Number,
      default: 0
    },
    size: {
      type: Number,
      default: 1
    }
  },
  data: function data() {
    return {
      page: 1
    };
  },

  watch: {
    value: function value(val) {
      this.page = val;
    },
    page: function page(val) {
      val = parseInt(val);
      if (val <= 0) {
        this.page = 1;
      } else if (val > this.totalPage) {
        this.page = this.totalPage;
      }
      // this.$emit('change', val)
    }
  },
  computed: {
    totalPage: function totalPage() {
      return Math.ceil(this.total / this.size) || 1;
    }
  },
  mounted: function mounted() {},

  methods: {
    nextPage: function nextPage() {
      this.page += 1;
      this.$emit('change', this.page);
    },
    prePage: function prePage() {
      this.page -= 1;
      this.$emit('change', this.page);
    },
    handleEnter: function handleEnter() {
      this.$refs.pageInput.blur();
    }
  }
};

var Main$8 = {
  render: function render() {
    var _vm = this;var _h = _vm.$createElement;var _c = _vm._self._c || _h;return _c('div', { staticClass: "os-collapse", class: {
        'os-collapse-close': !_vm.open,
        'os-collapse-large': _vm.size === 'large'
      } },
      [_c('div', { staticClass: "os-collapse-title", on: { "click": _vm.change } },
        [_c('i', { staticClass: "ic-r-angle-d" }),
          _vm._v(" "), _vm.title ? [_vm._v(_vm._s(_vm.title))] : _vm._t("title")], 2),
        _vm._v(" "),
        _c('el-collapse-transition',
          [_c('div', { directives:
              [{ name: "show", rawName: "v-show", value: _vm.open, expression: "open" }],
            ref: "collapseContent", staticClass: "os-collapse-content" },
            [_vm._t("default")], 2)
          ]
          )
      ], 1);
  },
  staticRenderFns: [],
  name: 'OsCollapse',
  props: {
    size: {
      type: String,
      default: function _default() {
        return '';
      }
    },
    title: {
      type: String,
      default: function _default() {
        return '';
      }
    },
    default: {
      type: Boolean,
      default: function _default() {
        return true;
      }
    },
    value: {
      type: Boolean
    }
  },
  data: function data() {
    return {
      open: this.value
    };
  },

  watch: {
    default: function _default(val) {
      this.open = val;
    },
    value: function value(val) {
      this.open = val;
    },
    open: function open(val) {
      this.$emit('input', val);
    }
  },
  created: function created() {
    this.open = !!this.default;
  },

  methods: {
    change: function change() {
      this.open = !this.open;
    }
  }
};

var Main$9 = {
  render: function render() {
    var _vm = this;var _h = _vm.$createElement;var _c = _vm._self._c || _h;return _c('div', { staticClass: "os-alert" }, [_c('el-alert', _vm._b({}, 'el-alert', _vm.$attrs, false), [_vm._t("default")], 2)], 1);
  },
  staticRenderFns: [],
  name: 'OsAlert',
  mounted: function mounted() {},

  methods: {}
};

var components = [ Main$1, Main$3, Main$2, Main$4, Main$6, Main$5,
// @2019.5.8 add by zhuobin. -- end
Main$7,
// @2019.7.29 add by zhuobin. -- end
Main$8,
// @2019.11.19 add by zhuobin. -- end
Main$9];

function osELement(Vue, _) {
  Vue.use(elementUi.Autocomplete);
  Vue.use(elementUi.Button);
  Vue.use(elementUi.ButtonGroup);
  Vue.use(elementUi.Input);
  Vue.use(elementUi.InputNumber);
  Vue.use(elementUi.Table);
  Vue.use(elementUi.TableColumn);
  Vue.use(elementUi.Pagination);
  Vue.use(elementUi.Loading.directive);
  Vue.use(elementUi.Tooltip);
  Vue.use(elementUi.Radio);
  Vue.use(elementUi.RadioGroup);
  Vue.use(elementUi.RadioButton);
  Vue.use(elementUi.Checkbox);
  Vue.use(elementUi.CheckboxGroup);
  Vue.use(elementUi.Switch);
  Vue.use(elementUi.Slider);
  Vue.use(elementUi.Menu);
  Vue.use(elementUi.MenuItem);
  Vue.use(elementUi.Submenu);
  Vue.use(elementUi.MenuItemGroup);
  Vue.use(elementUi.Select);
  Vue.use(elementUi.Option);
  Vue.use(elementUi.DatePicker);
  Vue.use(elementUi.Form);
  Vue.use(elementUi.FormItem);
  Vue.use(elementUi.Dialog);
  Vue.use(elementUi.Dropdown);
  Vue.use(elementUi.DropdownMenu);
  Vue.use(elementUi.DropdownItem);
  Vue.use(elementUi.Popover);
  Vue.use(elementUi.Progress);
  Vue.use(elementUi.Upload);
  Vue.use(elementUi.Collapse);
  Vue.use(elementUi.CollapseItem);
  Vue.use(elementUi.Tag);
  Vue.use(elementUi.Alert);
  Vue.use(elementUi.Tabs);
  Vue.use(elementUi.TabPane);
  Vue.use(elementUi.Badge);
  Vue.use(elementUi.Scrollbar);
  // @2019.1.16 add by zhuobin. -- start
  Vue.use(elementUi.Container);
  Vue.use(elementUi.Aside);
  Vue.use(elementUi.Main);
  Vue.use(elementUi.Row);
  Vue.use(elementUi.Col);
  Vue.use(elementUi.Card);
  Vue.use(elementUi.Breadcrumb);
  Vue.use(elementUi.BreadcrumbItem);
  Vue.use(elementUi.Cascader);
  Vue.use(elementUi.CascaderPanel);
  // @2019.1.16 add by zhuobin. -- end
  Vue.use(elementUi.OptionGroup);
  Vue.use(elementUi.Step);
  Vue.use(elementUi.Steps);
  Vue.use(elementUi.Carousel);
  Vue.use(elementUi.CarouselItem);
  Vue.component(elementUi.CollapseTransition.name, elementUi.CollapseTransition);
  Vue.prototype.$message = elementUi.Message;
  Vue.prototype.$alert = elementUi.MessageBox.alert;
  Vue.prototype.$confirm = elementUi.MessageBox.confirm;
  components.forEach(function (component) {
    Vue.component(component.name, component);
  });
}

exports.osELement = osELement;
