{% extends "configmap.yml" %}

{% block data %}
  default.conf: |
    server_tokens off;
    
    server {
        listen       {{ HTTP_LISTEN_PORT }};
        server_name  localhost;

        gzip on;
        gzip_static on;
        gzip_min_length 1k;
        gzip_buffers 4 16k;
        #gzip_http_version 1.0;
        gzip_comp_level 2;
        gzip_types text/plain application/javascript application/x-javascript text/css application/xml text/javascript application/x-httpd-php image/jpeg image/gif image/png;
        gzip_vary off;
        gzip_disable "MSIE [1-6]\.";
        
        location / {
          root /usr/share/nginx/html;
          index index.html;
          rewrite ^/os-platform/(.*)$ /os-platform/$1 break;
          try_files $uri $uri/ @router;
          if ($request_filename ~* .*\.(?:htm|html)$)
          {
            add_header Cache-Control "private, no-store, no-cache, must-revalidate, proxy-revalidate";
          }
        }

        location @router {
          # add_header Cache-Control 'no-cache, must-revalidate, proxy-revalidate, max-age=0';
          rewrite ^.*$ /index.html last;
        }
    }
{% endblock %}