<template>
  <el-dialog
    :title="title"
    :visible.sync="isShow"
    :close-on-click-modal="false"
    @close="cancel"
    style="agent-dialog"
  >
    <el-form
      :model="form"
      ref="agentForm"
      :rules="rules"
      label-width="100px"
      label-position="left"
    >
      <el-form-item label="智能体id" v-if="agent_id_edit == 1" prop="agentId">
        <el-input
          v-model="form.agentId"
          :disabled="title === '编辑智能体'"
        ></el-input>
      </el-form-item>

      <el-form-item label="智能体名称" prop="agentName">
        <el-input
          placeholder="支持中英文/数字/小数点/短横线/下划线,不超过32个字符"
          v-model="form.agentName"
        ></el-input>
      </el-form-item>

      <el-form-item label="描述" prop="agentDesc">
        <el-input
          type="textarea"
          :rows="3"
          placeholder="请输入智能体描述,不超过250个字符"
          v-model="form.agentDesc"
        ></el-input>
      </el-form-item>

      <el-form-item label="智能体类型" prop="agentType">
        <el-select v-model="form.agentType">
          <el-option
            v-for="item in agentTypeList"
            :key="item.index"
            :label="item.name"
            :value="item.code"
            :disabled="item.code == 1"
          ></el-option>
        </el-select>
      </el-form-item>

      <!-- <el-form-item label="落域" prop="classifyType">
        <el-select v-model="form.classifyType" :disabled="title !== '创建智能体'">
          <el-option
            v-for="item in classify_type_list"
            :key="item.code"
            :label="item.name"
            :value="item.code"
          ></el-option>
        </el-select>
      </el-form-item> -->

      <!-- <el-form-item label="URL" v-if="form.agentType === 0">
        <el-input v-model="form.url"></el-input>
      </el-form-item>

      <el-form-item label="Method" v-if="form.agentType === 0">
        <el-input v-model="form.method"></el-input>
      </el-form-item>

      <el-form-item label="auth" v-if="form.agentType === 0">
        <el-input v-model="form.auth"></el-input>
      </el-form-item>

      <el-form-item label="type" v-if="form.agentType === 0">
        <el-input v-model="form.type"></el-input>
      </el-form-item>

      <el-form-item label="key" v-if="form.agentType === 0">
        <el-input v-model="form.key" type="textarea" :rows="2"></el-input>
      </el-form-item> -->
    </el-form>

    <span slot="footer" class="dialog-footer">
      <el-button @click="cancel">取消</el-button>
      <el-button
        class="dialog-btn"
        type="primary"
        style="min-width: 104px"
        @click="save"
        :loading="saving"
      >
        {{ saving ? '创建中...' : '确定' }}
      </el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: 'IflyAIuiWebCreateAgent',

  data() {
    return {
      agent_id_edit: null,
      saving: false,
      isShow: false,
      title: '创建智能体',
      agentId: null,
      form: {
        agentId: null,
        agentName: null,
        agentDesc: null,
        agentType: null,
        classifyType: 0,
        // url: null,
        // method: null,
        // auth: null,
        // type: null,
        // key: null,
      },
      agentTypeList: [],
      classify_type_list: [],
      rules: {
        agentName: [
          { required: true, message: '请输入智能体名称', trigger: 'blur' },
          {
            pattern: /^[\u4e00-\u9fa5a-zA-Z0-9._-]{0,32}$/,
            message:
              '名称只能包含中文、英文字母、数字、小数点、短横线和下划线,长度不超过32个字符',
            trigger: 'blur',
          },
        ],
        agentDesc: [
          { required: true, message: '请输入智能体描述', trigger: 'blur' },
          { max: 250, message: '描述不能超过250个字符', trigger: 'blur' },
        ],
        agentType: [{ required: true, message: '请选择类型', trigger: 'blur' }],
        // classifyType: [{ required: true, message: '请选择落阈类型', trigger: 'blur' }],
        agentId: [{ required: false, trigger: 'blur' }],
      },
    }
  },

  mounted() {
    this.getAgentTypeList()
    // this.getClassifyType()
    this.getUserLimit()
  },

  methods: {
    show(data) {
      this.isShow = true
      this.$refs.agentForm.resetFields()
      if (data) {
        this.form = {
          agentName: data.agentName,
          agentDesc: data.agentDesc,
          agentType: data.agentType,
          agentId: data.agentId,
          classifyType: data.classifyType,
        }
        this.title = '编辑智能体'
        this.agentId = data.agentId
      } else {
        this.title = '创建智能体'
        this.form = {
          agentId: null,
          agentName: null,
          agentDesc: null,
          agentType: null,
          classifyType: 0,
        }
      }
    },
    getAgentTypeList() {
      this.$utils.httpGet(
        this.$config.api.AGENT_TYPE_LIST_OLD,
        {},
        {
          success: (res) => {
            if (res.code == 0) {
              this.agentTypeList = res.data
            }
          },
          error: (err) => {},
        }
      )
    },

    getClassifyType() {
      this.$utils.httpGet(
        this.$config.api.AGENT_CLASSIFY_TYPE_OLD,
        {},
        {
          success: (res) => {
            if (res.code == 0) {
              this.classify_type_list = res.data
            }
          },
          error: (err) => {},
        }
      )
    },

    save() {
      this.$refs.agentForm.validate((valid) => {
        if (valid) {
          const paramsData = {
            agentName: this.form.agentName,
            agentDesc: this.form.agentDesc,
            agentType: this.form.agentType,
            classifyType: this.form.classifyType,
          }
          this.saving = true
          if (this.title === '编辑智能体') {
            this.update({ ...paramsData, agentId: this.agentId })
          } else {
            this.create({ ...paramsData, agentId: this.form.agentId })
          }
        }
      })
    },

    create(data) {
      this.$utils.httpPost(
        this.$config.api.AGENT_CREATE_OLD,
        JSON.stringify(data),
        {
          config: {
            headers: {
              'Content-Type': 'application/json;charset=UTF-8',
            },
          },
          success: (res) => {
            if (res.code == 0) {
              this.$message.success(res.desc)
              this.cancel()
              this.$emit('refresh')
            }
          },
          error: (err) => {
            this.saving = false
            this.$message.error(err.desc)
          },
        }
      )
    },

    update(data) {
      this.$utils.httpPost(
        this.$config.api.AGENT_EDIT_OLD,
        JSON.stringify(data),
        {
          config: {
            headers: {
              'Content-Type': 'application/json;charset=UTF-8',
            },
          },
          success: (res) => {
            if (res.code == 0) {
              this.$message.success(res.desc)
              this.cancel()
              this.$emit('refresh')
            }
          },
          error: (err) => {
            this.$message.error(err.desc)
            this.saving = false
          },
        }
      )
    },
    getUserLimit() {
      this.$utils.httpGet(
        this.$config.api.USER_LIMIT_COUNT_OLD,
        {},
        {
          success: (res) => {
            if (res.data.agent_id_edit && res.data.agent_id_edit == 1) {
              this.agent_id_edit = res.data.agent_id_edit
            }
          },
          error: (err) => {},
        }
      )
    },

    cancel() {
      this.saving = false
      this.isShow = false
      this.agent_id_edit = null
      this.agentId = null
      this.$refs.agentForm.resetFields()
      this.getUserLimit()
    },
  },
}
</script>

<style lang="scss" scoped>
.agent-dialog {
  // height: 500px;
  width: 800px;
  z-index: 99999999;
}
.el-dialog {
  height: 500px;
}
</style>
