<template>
  <pc v-if="userAgent === 'pc'"></pc>
  <mobile v-else-if="userAgent === 'mobile'"></mobile>
  <span v-else>加载中...</span>
</template>

<script>
import pc from './RK3328S/pc.vue'
import mobile from './RK3328S/mobile-pages/index.vue'
import utils from '@A/lib/utils.js'

export default {
  name: 'RK3328S',
  data() {
    return {
      userAgent: '',
    }
  },
  created() {
    // 获取是否是h5
    const isMobile = utils.isMobile()
    if (isMobile) {
      this.userAgent = 'mobile'
    } else {
      this.userAgent = 'pc'
    }
  },
  components: { pc, mobile },
}
</script>

<style lang="scss"></style>
