<template>
  <div>
    <div class="aiui-store-skills">
      <brief-header :skill="skill" :id="id" :type="type"></brief-header>
      <div class="tab-container">
        <el-tabs v-model="tabType">
          <el-tab-pane label="技能概述" name="0" key="0"></el-tab-pane>
          <el-tab-pane
            label="信源详情"
            name="1"
            key="1"
            v-if="skill.hasSource"
          ></el-tab-pane>
          <el-tab-pane
            label="参数详情"
            name="2"
            key="2"
            v-if="skillParamsShow"
          ></el-tab-pane>
          <el-tab-pane
            label="参数详情"
            name="8"
            key="8"
            v-if="skillParamsShow2"
          ></el-tab-pane>
        </el-tabs>
      </div>

      <introduction-detail
        :skill="skill"
        v-if="tabType == '0'"
      ></introduction-detail>
      <param-detail :skill="skill" v-if="tabType == '2'"></param-detail>
      <param-detail8
        :semantic="semantic"
        :intents="intents"
        v-if="tabType == '8'"
      ></param-detail8>
      <source-detail
        :providers="providers"
        :sourceAgreement="sourceAgreement"
        v-if="tabType == '1'"
      ></source-detail>
    </div>
  </div>
</template>

<script>
import BriefHeader from './skillDetail/briefHeader.vue'
import IntroductionDetail from './skillDetail/introductionDetail.vue'
import ParamDetail from './skillDetail/paramDetail.vue'
import ParamDetail8 from './skillDetail/paramDetail8.vue'
import SourceDetail from './skillDetail/sourceDetail.vue'

export default {
  name: 'store-skill',
  data() {
    return {
      id: '',
      type: '',
      skill: {
        detail: {},
        examples: {},
        phrases: {},
        // 自定义存放非封闭技能参数详情的字段
        converted: {
          intents: [],
          semantic: [],
        },
      },
      intents: [],
      semantic: [],
      dialog: {
        show: false,
        data: {},
      },
      isGotSkillType: false,
      tabType: '0',
      providers: [],
      sourceAgreement: [],
    }
  },
  created() {
    this.id = this.$route.params.skillId
    this.type = this.$route.query.type
    this.getSkillDetail()
  },
  computed: {
    skillParamsShow() {
      return (
        this.skill.openSkill &&
        this.skill.protocols &&
        (this.skill.protocols.answer || this.skill.protocols.semantic)
      )
    },
    skillParamsShow2() {
      return !!this.skill.fieldDesc
    },
  },
  methods: {
    getSkillDetail() {
      let self = this
      this.$utils.httpGet(
        this.$config.api.AIUI_STORE_OTHER_SKILL_DETAIL,
        {
          id: this.id,
        },
        {
          success: (res) => {
            let tempData = res.data || {}
            tempData.detail = {
              url: tempData.picUrl,
              zhName: tempData.name,
              name: tempData.skillIdentify,
              count: tempData.count,
              description: tempData.briefIntroduction,
              provider: tempData.provider,
              mc: tempData.mc,
            }
            if (tempData.openSkill) {
              tempData.detail.description = tempData.briefIntroduction
            } else {
              tempData.detail.description = tempData.allIntroduction
            }
            tempData.phrases = {
              single: tempData.examplePhrase,
              many: tempData.multiExamplePhrase,
            }

            self.skill = tempData
            self.isGotSkillType = true
            if (!self.skill.openSkill) {
              if (res.data.fieldDesc && res.data.fieldDesc.intents) {
                self.intents = self.objToArray(res.data.fieldDesc.intents)
              } else {
                self.intents = []
              }
              if (res.data.fieldDesc && res.data.fieldDesc.slots) {
                self.semantic = self.objToArray(res.data.fieldDesc.slots)
              } else {
                self.semantic = []
              }
              let protocols = self.skill.protocols || {}
              protocols.semantic = self.semantic
              protocols.operation = self.intents
              self.skill.protocols = protocols
            }

            if (res.data.hasSource) {
              self.getProviders()
              self.getSourceAgreement()
            }

            self.$store.dispatch('aiuiStore/setSkillDetail', {
              zhName: res.data.name,
              name: res.data.skillIdentify,
              sysNumber: res.data.sysNumber,
            })
          },
        }
      )
    },
    semanticSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        if (rowIndex % this.skill.protocols.semantic.length === 0) {
          return {
            rowspan: this.skill.protocols.semantic.length,
            colspan: 1,
          }
        } else {
          return {
            rowspan: 0,
            colspan: 0,
          }
        }
      }
    },
    semanticSpanMethod2({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        if (rowIndex % this.semantic.length === 0) {
          return {
            rowspan: this.semantic.length,
            colspan: 1,
          }
        } else {
          return {
            rowspan: 0,
            colspan: 0,
          }
        }
      }
    },

    objToArray(obj) {
      let arr = []
      for (let key in obj) {
        arr.push({
          key: key,
          value: obj[key],
        })
      }
      return arr
    },

    getProviders() {
      let self = this
      this.$utils.httpGet(
        this.$config.api.AIUI_STORE_SKILL_PROVIDERS,
        {
          business: this.id,
        },
        {
          success: (res) => {
            self.providers = res.data
          },
          error: (err) => {},
        }
      )
    },
    getSourceAgreement() {
      let self = this
      this.$utils.httpGet(
        this.$config.api.AIUI_STORE_SKILL_CONTENTDATA,
        {
          method: 'getFunctions',
          business: this.id,
        },
        {
          success: (res) => {
            self.sourceAgreement = res.data
          },
          error: (err) => {},
        }
      )
    },
  },
  components: {
    BriefHeader,
    IntroductionDetail,
    ParamDetail,
    ParamDetail8,
    SourceDetail,
  },
}
</script>
<style lang="scss" scoped>
.store-skill-detail-tag {
  .icon-bieming {
    color: #1b9adc;
  }
}
.tab-container {
  position: sticky;
  top: -1px;
  z-index: 1;
  height: 78px;
  background: #fff;
  // padding: 0 35px;
  border-bottom: 1px solid #eff1f1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  :deep(.el-tabs__header) {
    margin: 0;
  }
  :deep(.el-tabs__item) {
    padding: 0 50px;
    font-size: 18px;
    height: 78px;
    line-height: 78px;
  }
  :deep(.el-tabs__nav-wrap::after) {
    display: none;
  }
}

// 技能商店适配不同屏幕
@media screen and (max-width: 1601px) {
  .tab-container {
    height: 48px;

    :deep(.el-tabs__item) {
      padding: 0 50px;
      font-size: 14px;
      height: 48px;
      line-height: 48px;
    }
  }
}
</style>
