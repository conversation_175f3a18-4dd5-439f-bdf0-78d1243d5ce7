<template>
  <div class="main-content">
    <section class="main-content-banner">
      <div class="banner-text">
        <h2>智能台灯解决方案</h2>
        <p class="pc-show">
          软硬件一体化智能台灯解决方案，让桌面上的小伙伴能听会说，
        </p>
        <p class="m-show">
          软硬件一体化智能台灯解决方案，<br />让桌面上的小伙伴能听会说，
        </p>
        <p class="pc-show">
          能理解会思考；灯光、模式、色温、亮度，你“说”了算。
        </p>
        <p class="m-show">
          能理解会思考；灯光、模式、色温、<br />亮度，你“说”了算。
        </p>
        <div class="banner-text-button" @click="toConsole">合作咨询</div>
      </div>
    </section>
    <section class="section section-2">
      <div class="section-title">应用场景</div>
      <div class="section-item">
        <ul>
          <li v-for="(item, index) in app_scenario" :key="index" class="app">
            <p class="app-text">{{ item.alt }}</p>
            <img :src="item.src" :alt="item.alt" />
          </li>
        </ul>
      </div>
    </section>
    <section class="section section-3">
      <div class="section-title">方案优势</div>
      <div class="section-item">
        <ul>
          <li v-for="(item, index) in pg_advantage" :key="index">
            <div class="section-item-bg" v-if="index % 2">
              <img :src="item.src" />
            </div>
            <div class="section-item-text">
              <div class="section-item-text-title">{{ item.title }}</div>
              <p v-html="item.text" class="pc-show"></p>
              <p
                v-html="item.m_text ? item.m_text : item.text"
                class="m-show"
              ></p>
            </div>
            <div class="section-item-bg" v-if="!(index % 2)">
              <img :src="item.src" />
            </div>
          </li>
        </ul>
      </div>
    </section>
    <section class="section section-4">
      <div class="section-title">接入流程</div>
      <div class="section-item">
        <ul>
          <li class="pc-show">
            <span>1</span>
            <p>前端声学集成<br /><span>提供在线、在线+离线方式</span></p>
          </li>
          <li class="pc-show"></li>
          <li class="pc-show">
            <span>2</span>
            <p>iFLYOS接入<br /><span>支持IVS、EVS两种协议</span></p>
          </li>
          <li class="pc-show"></li>
          <li class="pc-show">
            <span>3</span>
            <p>语音技能开发<br /><span>语音控制技能开发</span></p>
          </li>
          <li class="pc-show"></li>
          <li class="pc-show">
            <span>4</span>
            <p>发布上线<br /><span>产品发布上线</span></p>
          </li>
          <li class="m-show m-step">
            <div class="m-step-box">
              <div class="m-step-1">
                <div class="m-step-box">
                  <span class="step-num">1</span>
                  <p class="m-step-num-p">
                    前端声学集成<br /><span>提供在线、在线+离线方式</span>
                  </p>
                </div>
                <div class="m-step-arrow">
                  <span class="m-step-arrow-san"></span>
                </div>
                <div class="m-step-box">
                  <span class="step-num">2</span>
                  <p class="m-step-num-p">
                    iFLYOS接入<br /><span>支持IVS、EVS两种协议</span>
                  </p>
                </div>
              </div>
              <div class="m-step-2">
                <div class="m-step-box">
                  <span class="step-num">4</span>
                  <p class="m-step-num-p">
                    发布上线<br /><span>产品发布上线</span>
                  </p>
                </div>
                <div class="m-step-arrow">
                  <span class="m-step-arrow-san"></span>
                </div>
                <div class="m-step-box">
                  <span class="step-num">3</span>
                  <p class="m-step-num-p">
                    语音技能开发<br /><span>语音控制技能开发</span>
                  </p>
                </div>
              </div>
            </div>
            <div class="m-big-arrow">
              <img
                src="../../../assets/images/solution/digital-screen-lamp/step-big.png"
              />
            </div>
          </li>
        </ul>
      </div>
    </section>
    <section class="section section-6">
      <div class="section-title">成功案例</div>
      <div class="section-item">
        <div class="section-item-left">
          <img
            src="../../../assets/images/solution/digital-screen-lamp/lamp-section-10.jpg"
          />
        </div>
        <div class="section-item-right">
          <div class="section-item-right-head">
            <!-- <img src="~assets/images/digital-screen-lamp/lamp-section-11.jpg" /> -->
            <div>广州视康照明 智能台灯</div>
          </div>
          <div class="section-item-right-main">
            <p>
              基于iFLYOS
              XR872模块开发，实现台灯的语音控制，让AI能力在台灯领域得到应用。
            </p>
            <p>
              使用唤醒词唤醒台灯后，即可对台灯进行语音控制，如“打开台灯”、“把灯光调亮一点”，台灯就会自动打开和调节亮度。同时接入了iFLYOS海量的内容资源，教育百科、天气等技能随心体验，让台灯变身智能小助手。
            </p>
          </div>
        </div>
      </div>
    </section>
    <section class="section section-5">
      <div class="section-title">
        合作咨询
        <p>提交信息，我们会尽快与你联系。</p>
      </div>
      <div class="section-item">
        <aiui-button hasTop @click.native="toConsole">申请合作</aiui-button>
      </div>
    </section>
  </div>
</template>

<script>
import aiuiButton from '../../../components/aiuiButton.vue'
export default {
  components: { aiuiButton },
  layout: 'aiuiHome',
  data() {
    return {
      app_scenario: [
        {
          alt: '语音控制',
          src: require('../../../assets/images/solution/digital-screen-lamp/lamp-section-1.jpg'),
        },
        {
          alt: '智能提醒',
          src: require('../../../assets/images/solution/digital-screen-lamp/lamp-section-2.jpg'),
        },
        {
          alt: '学习帮手',
          src: require('../../../assets/images/solution/digital-screen-lamp/lamp-section-3.jpg'),
        },
      ],
      pg_advantage: [
        {
          title: '全链路语音交互，快速集成',
          text: '提供唤醒、识别、语义、合成全链路语音能力，集成更便捷。',
          src: require('../../../assets/images/solution/digital-screen-lamp/lamp-section-4.png'),
        },
        {
          title: '在线、离线双模式语音交互',
          text: '提供在线和离线两种交互模式，没有网络时台灯<br />的语音控制也可轻松完成。',
          m_text:
            '提供在线和离线两种交互模式，没有网络时台灯的语音控制也可轻松完成。',
          src: require('../../../assets/images/solution/digital-screen-lamp/lamp-section-5.png'),
        },
        {
          title: '高度自定义',
          text: '支持唤醒词、命令词、发音人、设备人设等自定义，<br />打造专属的品牌形象。',
          m_text:
            '支持唤醒词、命令词、发音人、设备人设等自定义，打造专属的品牌形象。',
          src: require('../../../assets/images/solution/digital-screen-lamp/lamp-section-6.png'),
        },
        {
          title: '海量内容资源',
          text: '教育百科、国学启蒙、绘本故事、学科教育、<br />K12教育等内容资源精心选择。',
          m_text:
            '教育百科、国学启蒙、绘本故事、学科教育、K12教育等内容资源精心选择。',
          src: require('../../../assets/images/solution/digital-screen-lamp/lamp-section-7.png'),
        },
        {
          title: '智能教育场景AI能力一站式配齐',
          text: '评测，教材点读、拍照搜题、翻译能力等可灵活选择。',
          src: require('../../../assets/images/solution/digital-screen-lamp/lamp-section-8.png'),
        },
        {
          title: '高性价比语音模组',
          text: '多种语音模组可选，模组已集成语音唤醒、识别等AI能力。',
          src: require('../../../assets/images/solution/digital-screen-lamp/lamp-section-9.png'),
        },
      ],
    }
  },
  methods: {
    toConsole() {
      window.open('/solution/apply/5')
    },
  },
}
</script>

<style lang="scss" scoped>
@import '../../../assets/scss/screen-and-lamp.scss';
@media screen and (min-width: 751px) {
  .main-content {
    &-banner {
      background: url('../../../assets/images/solution/digital-screen-lamp/lamp-banner.jpg')
        center no-repeat;
      background-size: cover;
    }
    .section-2 {
      .section-item {
        > ul {
          li {
            width: 32%;
            img {
              width: 100%;
            }
          }
        }
      }
    }
    .section-6 {
      padding-bottom: 0;
      .section-item {
        display: flex;
        justify-content: space-around;
        padding: 20px;
        border: 1px solid #e4e7ed;
        border-radius: 15px;
        &-left {
          text-align: center;
          padding: 0 50px 0 20px;
        }
        &-right {
          &-head {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            > div {
              font-size: 26px;
            }
          }
          &-main {
            line-height: 35px;
            color: #8c8c8c;
            font-size: 16px;
            padding-right: 45px;
          }
        }
      }
    }
  }
}
@media screen and (max-width: 750px) {
  .main-content {
    &-banner {
      background: url('../../../assets/images/solution/digital-screen-lamp/lamp-banner-m.jpg')
        center no-repeat;
      background-size: cover;
    }
    .section-6 {
      .section-item {
        padding: 20px;
        border: 1px solid #e4e7ed;
        border-radius: 15px;
        &-left {
          text-align: center;
          padding: 0 50px 0 20px;
        }
        &-right {
          &-head {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
            > div {
              font-size: 18px;
            }
          }
          &-main {
            line-height: 28px;
            color: #8c8c8c;
            font-size: 16px;
            p {
              text-indent: 2em;
            }
          }
        }
      }
    }
  }
}
</style>
