<template>
  <div class="os-container" style="flex-direction: column">
    <aiui-header />
    <div class="os-container">
      <div class="os-main">
        <!-- <div class="store-skill-title">
          <i class="ai-r-angle-l-line" @click="back" />
          <span>技能详情</span>
        </div>
        <os-divider /> -->
        <router-view></router-view>
      </div>
      <div
        class="os-side-right"
        :class="{ 'os-side-right-open': rightTestOpen }"
      >
        <right-test-close
          v-if="!rightTestOpen"
          :rightTestOpen="rightTestOpen"
        ></right-test-close>
        <skill-debug v-show="rightTestOpen" debugType="store" />
        <feedBackHover :rightTestOpen="rightTestOpen" />
      </div>
    </div>
  </div>
</template>

<script>
import RightTestClose from '@C/rightTestClose'
import feedBackHover from '../components/feedBackHover'
import { mapGetters } from 'vuex'
export default {
  data() {
    return {}
  },
  computed: {
    ...mapGetters({
      rightTestOpen: 'studioSkill/rightTestOpen',
    }),
  },
  created() {
    this.$store.dispatch('studioSkill/setRightTestOpen', true)
  },
  methods: {
    back() {
      this.$router.back()
    },
  },
  components: {
    RightTestClose,
    feedBackHover,
  },
}
</script>

<style lang="scss" scoped>
.store-skill-title {
  display: flex;
  align-items: center;
  padding: 24px 120px 23px;
  i {
    cursor: pointer;
    width: 25px;
    height: 25px;
    font-size: 25px;
    color: $grey4;
  }
  span {
    font-size: 24px;
    color: $semi-black;
    padding-left: 10px;
  }
}
</style>
