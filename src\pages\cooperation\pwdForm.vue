<template>
  <el-dialog
    title="修改密码"
    :visible.sync="dialog.show"
    width="480px"
  >
   <el-form :model="form" :rules="rules" ref="form" label-position="top">
      <el-form-item label="旧密码" prop="oldPwd">
        <el-input v-model.trim="form.oldPwd"
          placeholder="请输入旧密码"
          ref="oldPwdInput" 
          @keyup.enter.native="toOldPwd"/>
      </el-form-item>
      <el-form-item label="新密码" prop="newPwd">
        <el-input v-model.trim="form.newPwd"
          placeholder=""
          ref="newPwdInput" 
          @keyup.enter.native="toReNewPwd"/>
      </el-form-item>
      <el-form-item label="确认密码" prop="reNewPwd">
        <el-input ref="reNewPwdInput" v-model.trim="form.reNewPwd"
          placeholder="请输入确认密码"
          @keyup.enter.native="submitForm"/>
      </el-form-item>
      <el-form-item style="text-align: right;">
        <el-button type="primary"
          size="medium"
          style="margin-bottom:32px; min-width: 104px;" @click="submitForm">确定</el-button>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script>
import md5 from 'blueimp-md5'
export default {
  props: {
    dialog: {}
  },
  data() {
    return {
      form: {
        oldPwd: '',
        newPwd: '',
        reNewPwd: ''
      },
      rules: {
        oldPwd: this.$rules.required('请输入旧密码'),
        newPwd: this.$rules.required('请输入新密码'),
        reNewPwd: [this.$rules.required('请输入确认密码'), 
        { validator: this.isEqual, trigger: ['blur'] }]
      }
    }
  },
  watch: {
    'dialog.show': function(val, oldVal) {
      let self = this
      if (val) {
        this.$nextTick(function () {
          self.$refs.oldPwdInput && self.$refs.oldPwdInput.focus()
        })
      }
      this.$refs.form && this.$refs.form.resetFields()
    }
  },
  methods: {
    toOldPwd() {
      this.$refs.newPwdInput && this.$refs.newPwdInput.focus()
    },
    toReNewPwd() {
      this.$refs.reNewPwdInput && this.$refs.reNewPwdInput.focus()
    },
    isEqual(rule, val, callback){
      if(val !== this.form.newPwd){
        callback(new Error('两次密码不一致'))
      } else {
        callback()
      }
    },
    submitForm() {
      let self = this
      let data = {
        oldPassword: md5(self.form.oldPwd),
        newPassword: md5(self.form.newPwd)
      }
      this.$refs.form.validate((valid) => {
        if(valid) {
          this.$utils.httpPost(this.$config.api.COOP_UPDATE_PWD, data, {
            success: (res) => {
              this.dialog.show = false
              self.$message.success('修改成功')
            },
            error: (err) => {
              this.dialog.show = false
            }
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.userinfo-page {
  width: 600px;
  margin: auto;
  padding-top: 48px;
}
.userinfo-page-detail{
  width: 100%;
  height: 144px;
  border: 1px solid #e5e5e5;
  box-shadow: 0px 12px 24px 0px rgba(0, 0, 0, 0.08);
  padding: 36px 48px;
  border-radius: 12px;
}
.text-mid-grey {
  margin-right: 24px;
  font-size: 24px;
  color: #8c8c8c;
}
.text-black {
  color: $semi-black;
  font-size: 16px;
}
</style>