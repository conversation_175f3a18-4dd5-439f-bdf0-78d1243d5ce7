<template>
  <div>
    <div class="conf-title item-title" style="margin-top: 0">
      <div style="margin-right: 10px">应用后处理</div>
      <el-switch
        :value="isOn"
        class="mgr16"
        :disabled="!subAccountEditable"
        @change="configSwitch"
      ></el-switch>
    </div>

    <p class="item-desc">
      对语音识别和语义理解的结果进行自定义处理。<a
        :href="`${$config.docs}doc-5/`"
        target="_blank"
        >了解后处理</a
      >
    </p>

    <div>
      <el-button
        class="mgb24"
        type="primary"
        :disabled="!subAccountEditable"
        @click="switchMorfeiMode"
        v-if="
          appInfo.platform &&
          (appInfo.platform === 'morfei' || appInfo.platform === 'MorfeiCore')
        "
      >
        <span
          v-if="
            form.platform &&
            (form.platform === 'morfei' || form.platform === 'morfeicore')
          "
          >切换到自定义后处理</span
        >
        <span v-else
          >切换到{{
            form.platform &&
            form.platform === 'other' &&
            appInfo.platform === 'MorfeiCore'
              ? '涂鸦IOT'
              : 'MORFEI LINK家居'
          }}平台托管</span
        >
      </el-button>

      <radio-tab
        v-if="
          shouldMorfeiPostprocess &&
          appInfo.platform &&
          appInfo.platform !== 'morfei' &&
          appInfo.platform !== 'MorfeiCore'
        "
        v-model="form.platform"
        @input="onPlatformChange"
        :data="[
          { label: '切换到自定义后处理', value: 'other' },
          { label: '切换到MORFEI LINK家居托管', value: 'morfei' },
          { label: '切换到涂鸦IOT平台托管', value: 'morfeicore' },
        ]"
      ></radio-tab>

      <el-tooltip
        placement="right"
        class="synthetic-tips"
        v-if="
          appInfo.platform &&
          (appInfo.platform === 'morfei' || appInfo.platform === 'MorfeiCore')
        "
      >
        <div slot="content" v-if="form.platform && form.platform === 'morfei'">
          如需通过语音实现对MORFEI LINK
          家居平台上设备的控制，请保持在该模式下；<br />如后处理需求与MORFEI
          LINK家居平台无关，请进行切换
        </div>
        <div
          slot="content"
          v-else-if="form.platform && form.platform === 'morfeicore'"
        >
          如果通过语音实现接入讯飞设备控制智能家居类产品，请保持在该模式下。<br />如果处理需求与讯飞家居平台无关，请进行切换。
        </div>
        <div slot="content" v-else>
          如需通过语音实现对MORFEI LINK 家居平台上设备的控制，请进行切换；<br />如后处理需求与MORFEI
          LINK家居平台无关，则无需切换。
        </div>
        <i class="el-icon-question" />
      </el-tooltip>

      <p class="mgb24" v-if="form.platform && form.platform === 'morfei'">
        当前模式的后处理由MORFEI LINK家居平台托管，跳转到<a
          href="https://morfeilink.himorfei.com/console/platform "
          target="_blank"
          >MORFEI LINK 家居平台控制台</a
        >了解详情。
      </p>
      <p
        class="mgb24"
        v-else-if="form.platform && form.platform === 'morfeicore'"
      >
        当前模式的后处理由涂鸦IOT平台托管
      </p>
      <div class="form-wrap" v-else>
        <el-form
          ref="form"
          :model="form"
          :rules="rules"
          :disabled="!subAccountEditable"
          label-width="110px"
          label-position="left"
          style="min-width: 745px"
        >
          <el-form-item
            label="后处理链接"
            prop="url"
            class="inline-form-item-01"
          >
            <el-input
              ref="url"
              class="config-input"
              v-model.trim="form.url"
              @blur="emitChange"
              placeholder="请参考文档协议进行URL验证"
            ></el-input>
          </el-form-item>
          <el-form-item label="尝试次数" class="inline-form-item-02">
            <el-select
              class="config-select"
              v-model="form.reties"
              @change="changeReties"
            >
              <el-option
                v-for="(item, index) in repeatTimeList"
                :key="index"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select> </el-form-item
          ><br />
          <el-form-item
            label="备用链接"
            prop="backupUrl"
            class="inline-form-item-01"
          >
            <el-input
              class="config-input"
              v-model.trim="form.backupUrl"
              @change="backupUrlChange"
              placeholder="请参考文档协议进行URL验证，主链接无效时自动启用"
              @blur="emitChange"
            ></el-input>
          </el-form-item>
          <el-form-item label="尝试次数" class="inline-form-item-02">
            <el-select
              class="config-select"
              v-model="form.backupReties"
              @change="changeReties"
            >
              <el-option
                v-for="(item, index) in repeatTimeList"
                :key="index"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="超时时间" prop="timeOut">
            <el-input
              class="config-input"
              ref="timeOut"
              v-model.trim="form.timeOut"
              :min="200"
              @change="emitChange"
              :placeholder="`尝试次数为${form.reties}次时，超时时间可设范围为100-${timeOutLimit}ms`"
            ></el-input>
            ms
          </el-form-item>
          <el-form-item label="校验token">
            <span class="mgr24">{{ form.h_token }}</span>
            <a class="mgr24" @click="copy(form['h_token'])">复制</a>
            <a v-if="subAccountEditable" @click="changeToken('h_token')"
              >重新生成</a
            >
          </el-form-item>
          <el-form-item label="消息是否加密">
            <el-switch
              v-model="form.isEncrypt"
              @change="emitChange"
              active-value="1"
              inactive-value="0"
            ></el-switch>
          </el-form-item>
          <el-form-item label="加密AES KEY">
            <span class="mgr24">{{ form.aeskey }}</span>
            <a class="mgr24" @click="copy(form['aeskey'])">复制</a>
            <a v-if="subAccountEditable" @click="changeToken('aeskey')"
              >重新生成</a
            >
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script>
import RECOGNITION_POSTPROCESS_State from '@U/AIUIState/RECOGNITION_POSTPROCESS_State'
import RECOGNITION_SEMANTIC_POSTPROCESS_State from '@U/AIUIState/RECOGNITION_SEMANTIC_POSTPROCESS_State'
import RECOGNITION_SEMANTIC_POSTPROCESS_SYNTHESIS_State from '@U/AIUIState/RECOGNITION_SEMANTIC_POSTPROCESS_SYNTHESIS_State'
import RECOGNITION_POSTPROCESS_SYNTHESIS_State from '@U/AIUIState/RECOGNITION_POSTPROCESS_SYNTHESIS_State'

import md5 from 'blueimp-md5'
import { mapGetters } from 'vuex'

export default {
  name: 'postProcess',

  data() {
    const checkUrl = (rule, value, callback) => {
      if (!value) return callback()
      if (
        /^([hH][tT]{2}[pP]:\/\/|[hH][tT]{2}[pP][sS]:\/\/)((192\.168|172\.([1][6-9]|[2]\d|3[01]))(\.([2][0-4]\d|[2][5][0-5]|[01]?\d?\d)){2}|10(\.([2][0-4]\d|[2][5][0-5]|[01]?\d?\d)){3})/.test(
          value
        )
      ) {
        callback(new Error('不支持内网IP'))
        return
      }
      value &&
        this.$utils.httpPost(
          this.$config.api.AIUI_APP_CALLBACK_CHECKURL,
          {
            appid: this.appId,
            url: value,
            h_token: this.form.h_token,
          },
          {
            success: (res) => {
              if (!res.flag) {
                callback(new Error(res.desc))
              } else {
                callback()
              }
            },
            error: (err) => {
              callback(new Error(err?.desc))
            },
          }
        )
    }
    const checkTimeout = (rule, value, callback) => {
      if (value < 100 || value > this.timeOutLimit) {
        // callback(new Error(`超时时间可设范围为100-${this.timeOutLimit}ms`))
        if (this.backupUrlValid && this.form.backupUrl) {
          callback(
            new Error(
              `尝试次数为${
                parseInt(this.form.reties) + parseInt(this.form.backupReties)
              }次时，超时时间可设范围为100-${this.timeOutLimit}ms`
            )
          )
        } else {
          callback(
            new Error(
              `尝试次数为${this.form.reties}次时，超时时间可设范围为100-${this.timeOutLimit}ms`
            )
          )
        }
      }
      callback()
    }
    const isUrlRepeat = (rule, value, callback) => {
      if (value && value == this.form.backupUrl) {
        callback(new Error('链接不能重复'))
      }
      callback()
    }
    const checkUrlRepeat = (rule, value, callback) => {
      if (value && value == this.form.url) {
        callback(new Error('链接不能重复'))
      }
      callback()
    }
    return {
      form: {
        url: '',
        isEncrypt: false,
        h_token: this.getToken(),
        aeskey: this.getToken(),
        platform: 'other',
        timeOut: '3000',
        reties: 1,
        backupUrl: '',
        backupReties: 1,
      },
      rules: {
        url: [
          { required: true, message: '后处理URL不能为空', trigger: 'blur' },
          {
            pattern:
              /^([hH][tT]{2}[pP]:\/\/|[hH][tT]{2}[pP][sS]:\/\/)(([A-Za-z0-9-~]+).)+([A-Za-z0-9-~\/])+$/,
            message: 'URL格式不正确',
            trigger: 'blur',
          },
          { validator: checkUrl, trigger: 'blur' },
          { validator: isUrlRepeat, trigger: 'blur' },
        ],
        timeOut: [
          {
            required: true,
            message: '超时时间不能为空',
            trigger: ['blur', 'change'],
          },
          {
            pattern: /^[0-9]+$/,
            message: '仅支持数字',
            trigger: ['blur', 'change'],
          },
          { validator: checkTimeout, trigger: ['blur', 'change'] },
        ],
        backupUrl: [
          {
            pattern:
              /^([hH][tT]{2}[pP]:\/\/|[hH][tT]{2}[pP][sS]:\/\/)(([A-Za-z0-9-~]+).)+([A-Za-z0-9-~\/])+$/,
            message: 'URL格式不正确',
            trigger: 'blur',
          },
          { validator: checkUrlRepeat, trigger: 'blur' },
          { validator: checkUrl, trigger: 'blur' },
        ],
      },
      repeatTimeList: [
        {
          label: '1次',
          value: 1,
        },
        {
          label: '2次',
          value: 2,
        },
        {
          label: '3次',
          value: 3,
        },
      ],
      isBackupUrlValid: true,
    }
  },
  computed: {
    ...mapGetters({
      limitCount: 'aiuiApp/limitCount',
      currentScene: 'aiuiApp/currentScene',
      appInfo: 'aiuiApp/app',
      limitCount: 'aiuiApp/limitCount',
      subAccount: 'user/subAccount',
      context: 'aiuiApp/context',
      subAccountEditable: 'aiuiApp/subAccountEditable',
    }),
    appId() {
      return this.$route.params.appId
    },
    timeOutLimit() {
      let num = 3000
      let tmp = parseInt(this.form.reties) + parseInt(this.form.backupReties)
      if (this.isBackupUrlValid && this.form.backupUrl) {
        num = 9000 / tmp
        return num
      } else {
        num = this.form.reties == 3 ? 3000 : this.form.reties == 2 ? 4500 : 9000
        return num
      }
    },
    shouldMorfeiPostprocess() {
      return (
        typeof this.limitCount['morfei_postprocess'] !== 'undefined' &&
        Number(this.limitCount['morfei_postprocess']) > 0
      ) // 0：不能；>0： 能 使用mofei mofeicore后处理
    },

    isOn() {
      if (this.currentScene && this.currentScene.point && this.context) {
        const point = this.currentScene.point
        const context = this.context

        if (
          context.isCurrentState(RECOGNITION_POSTPROCESS_State) ||
          context.isCurrentState(RECOGNITION_SEMANTIC_POSTPROCESS_State) ||
          context.isCurrentState(
            RECOGNITION_SEMANTIC_POSTPROCESS_SYNTHESIS_State
          ) ||
          context.isCurrentState(RECOGNITION_POSTPROCESS_SYNTHESIS_State)
        ) {
          return true
        }
      }
      return false
    },
  },
  methods: {
    backupUrlValid() {
      this.$refs.form &&
        this.$refs.form.validateField(['backupUrl'], (err) => {
          if (!err) {
            this.isBackupUrlValid = true
          } else {
            this.isBackupUrlValid = false
          }
        })
    },
    emitChange() {
      this.$refs['form'] &&
        this.$refs['form'].validate((valid) => {
          if (valid) {
            //   this.$emit('change')
            this.saveConf()
          } else {
            this.$emit('validFail')
          }
        })
    },
    changeReties() {
      this.$refs.form.validateField(['timeOut', 'backupUrl'])
      this.emitChange()
    },
    backupUrlChange() {
      this.$refs.form.validateField(['timeOut', 'url'])
      // this.emitChange()
    },

    configSwitch(val) {
      if (val) {
        if (this.form.platform === 'other') {
          this.$refs.form.validate((valid) => {
            if (valid) {
              this.$store.dispatch('aiuiApp/addSwitches', '3')
            } else {
              this.$message.warning('请检查后处理配置')
            }
          })
        } else {
          this.$store.dispatch('aiuiApp/addSwitches', '3')
        }
      } else {
        this.$store.dispatch('aiuiApp/removeSwitch', '3')
      }
    },
    getToken() {
      let rand = parseInt(Math.random() * 15)
      let timeStamp = new Date().getTime()
      let res = timeStamp + rand
      res = md5(res)
      return res.substring(rand, rand + 16)
    },
    changeToken(id) {
      let self = this
      this.form[id] = this.getToken()
      self.emitChange()
    },
    copy(value) {
      this.$utils.copyClipboard(value)
      this.$message.success('已复制到剪切板')
    },
    getAahConf() {
      let self = this
      this.$utils.httpGet(
        this.$config.api.AIUI_APP_CALLBACK_AAHCONF,
        {
          appid: this.appId,
          sceneId: this.currentScene.sceneBoxId,
        },
        {
          success: (res) => {
            if (res.flag) {
              if (
                res.data.url ||
                res.data.platform === 'morfei' ||
                res.data.platform === 'morfeicore'
              ) {
                // 如果配置中存在URL 或者是MORFEI 平台则可直接打开

                if (
                  (res.data.platform === 'morfei' ||
                    res.data.platform === 'morfeicore') &&
                  !res.data.h_token
                ) {
                  // 防止魔飞应用第一次打开token 为空
                  res.data.h_token = self.getToken()
                  res.data.aeskey = self.getToken()
                }
                self.form = res.data
                self.form.reties = parseInt(self.form.reties) || 1
                this.$set(self.form, 'backupUrl', '')
                this.$set(self.form, 'backupReties', 1)
                let tmp = res.data.moreConfig && JSON.parse(res.data.moreConfig)
                if (Array.isArray(tmp)) {
                  self.form.backupUrl = tmp[0]['url']
                  self.form.backupReties = tmp[0].reties || 1
                }
              } else {
                self.emitChange()
              }
            } else {
              self.$message.error(res.desc)
            }
          },
        }
      )
    },
    switchMorfeiMode() {
      if (
        this.form.platform === 'morfei' ||
        this.form.platform === 'morfeicore'
      ) {
        this.form.platform = 'other'
      } else if (
        (this.form.platform = 'other' && this.appInfo.platform === 'morfei')
      ) {
        this.form.platform = 'morfei'
      } else if (
        (this.form.platform = 'other' && this.appInfo.platform === 'MorfeiCore')
      ) {
        this.form.platform = 'morfeicore'
      }
      this.emitChange()
    },
    saveConf() {
      let self = this
      let data = {
        appid: this.appId,
        sceneId: this.currentScene.sceneBoxId,
        url: this.form.url || 'http://www.himorfei.com', //魔飞使用IoT模式时不需要URL，这里后端限制了必传。所以传个默认值
        h_token: this.form.h_token || this.getToken(),
        aeskey: this.form.aeskey || this.getToken(),
        isEncrypt: this.form.isEncrypt,
        timeOut: this.form.timeOut,
        reties: this.form.reties,
        platform: this.form.platform,
      }
      if (this.form.backupUrl) {
        data.moreConfig = JSON.stringify([
          {
            url: this.form.backupUrl,
            reties: this.form.backupReties,
          },
        ])
      } else {
        this.form.backupReties = 1
        data.moreConfig = ''
      }
      self.$utils.httpPost(
        this.$config.api.AIUI_APP_CALLBACK_SAVEAAHCONF,
        data,
        {
          success: (res) => {
            if (res.flag) {
              self.change = false
              self.$emit('saveSuccess')
            } else {
              self.$message.error(res.desc)
              self.$emit('saveFail')
            }
          },
          error: (err) => {
            self.$emit('saveFail')
          },
        }
      )
    },
    // validForm() {
    //   if (this.form.platform === 'other') {
    //     this.$refs.form &&
    //       this.$refs.form.validate((valid) => {
    //         if (valid) {
    //           this.$emit('validSuccess')
    //         } else {
    //           // this.$message.error('后处理参数配置有误')
    //           this.$emit('validFail')
    //         }
    //       })
    //   } else {
    //     this.$emit('validSuccess')
    //   }
    // },
    onPlatformChange() {
      this.emitChange()
    },
  },
  watch: {
    // status() {
    //   this.contentShow = this.status
    //   if (this.status) {
    //     this.getAahConf()
    //   }
    // },
    // saving() {
    //   if (this.saving) {
    //     if (this.status && this.change) {
    //       this.submitForm()
    //     } else {
    //       this.$emit('saveSuccess')
    //     }
    //   }
    // },
    currentScene(scene) {
      if (scene && scene.sceneBoxId) {
        this.getAahConf()
      }
    },
    // 'form.backupUrl': function () {
    //   this.backupUrlValid()
    // },
  },
  created() {
    if (this.currentScene && this.currentScene.sceneBoxId) {
      this.getAahConf() // 获取配置列表
    }
  },
  mounted() {
    // 首次进入时clearValidate 清除校验
    this.$nextTick(() => {
      if (this.$refs.form) {
        setTimeout(() => {
          this.$refs.form.clearValidate()
        }, 1000)
      }
    })
  },
}
</script>

<style lang="scss" scoped>
@import '../../common.scss';
@import '../style.scss';
</style>
