<template>
  <div>
    <searchSOS v-if="currentScene && currentScene.sos === true" />
    <search
      v-if="
        currentScene && currentScene.sceneBoxId && currentScene.sos !== true
      "
    />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import search from './search'
import searchSOS from './searchSOS'

export default {
  data() {
    return {}
  },
  computed: {
    ...mapGetters({
      currentScene: 'aiuiApp/currentScene',
      appInfo: 'aiuiApp/app',
      subAccountEditable: 'aiuiApp/subAccountEditable',
    }),
    appId() {
      return this.$route.params.appId
    },
  },
  components: {
    search,
    searchSOS,
  },
}
</script>
<style lang="scss" scoped></style>
