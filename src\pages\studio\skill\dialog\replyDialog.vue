<template>
  <el-dialog
    title="回答语料"
    class="reply-utter-dialog"
    :visible.sync="dialog.show"
    :close-on-click-modal="false"
    width="960px"
    ref="dialog"
  >
    <el-scrollbar style="padding-right: 12px; height: 500px">
      <p v-if="!qaSentence"><span class="qa-sentence">追问话术</span>-</p>
      <template v-else>
        <os-collapse
          class="qa-collapse"
          :default="false"
          size="small"
          title="对应追问话术"
        >
          <span class="qa-sentence" v-if="!qaSentence">-</span>
          <template v-else>
            <p
              class="qa-sentence"
              v-for="(qa, index) in qaSentence"
              :key="index"
            >
              {{ qa }}
            </p>
          </template>
        </os-collapse>
        <os-divider />
      </template>
      <os-collapse :default="true" size="small" title="用户回答语料">
        <p class="reply-utter-tip">
          仅支持模版语料写法。输入“{”时，可以引用已有的槽位，也可以自由输入还未有的槽位标识，新建的槽位会归到辅助词槽位列表里。<br />
          实体槽位只能引用意图内现有的，不可在此新建。每句回答语料中，必须包含此意图内实体槽位
          ，否则将无法保存。
        </p>
        <div
          class="mgb24"
          @keyup="flowerBracketHandle"
          @keyup.enter="addUtteranceBlur"
        >
          <el-input
            class="utterance-add-area"
            ref="addUtter"
            placeholder="回车添加用户回答的常用表达"
            :disabled="!subAccountEditable"
            @blur="addUtterance"
            @keyup.native.up.stop.prevent="handleUp"
            @keyup.native.down.stop.prevent="handleDown"
            v-model="utteranceAddName"
          >
            <i slot="prefix" class="el-input__icon ic-r-plus"></i>
            <el-button
              slot="suffix"
              type="text"
              class="utterance-add-area-addbtn"
              size="small"
            >
              添加
            </el-button>
          </el-input>
        </div>
        <div v-loading="utterancesData.loading">
          <div
            v-for="(utterance, index) in oldUtterances"
            class="utterance-collapse-item"
            :key="index"
          >
            <div class="utterance-collapse-item-title">
              <div class="utterance-area">
                <i class="utterance-area-template ic-brace" />
                <reply-editor
                  :utterance="utterance"
                  :subAccountEditable="subAccountEditable"
                  :slotId="slotId"
                  :slotNames="slotNames"
                  @change="refreshData"
                />
                <i
                  class="utterance-area-del"
                  v-if="subAccountEditable"
                  @click="delReply(utterance, index)"
                  ><i class="ic-r-delete"
                /></i>
              </div>
            </div>
          </div>
        </div>
        <os-pagination
          class="utter-pagination"
          v-model="utterancesData.page"
          :total="utterancesData.total"
          :size="utterancesData.size"
          @change="getUtterances"
        />
      </os-collapse>
      <os-divider />
      <os-collapse :default="true" size="small" title="辅助词">
        <os-table
          class="slot-table"
          :border="true"
          :tableData="auxiliarySlotsData"
          ref="auxiliarySlotTable"
        >
          <el-table-column label="槽位标识" prop="slotName" width="120">
          </el-table-column>
          <el-table-column width="200" label="对应辅助词">
            <template slot-scope="scope">
              <span
                v-if="scope.row.entityName"
                :style="utteranceColor[scope.row.slotName]"
                @click.stop="
                  openSelectEntity(
                    scope.row,
                    $event,
                    utteranceColor[scope.row.slotName]
                  )
                "
              >
                #{{ scope.row.entityName }}
              </span>
              <a v-else @click="openSelectEntity(scope.row, $event)"
                >设置对应辅助词</a
              >
            </template>
          </el-table-column>
        </os-table>
      </os-collapse>
    </el-scrollbar>
    <span slot="footer" class="dialog-footer"></span>
    <select-entity-popover
      @change="refreshData"
      :useInReplyDialog="useInReplyDialog"
      showType="replyDialog"
    />
    <entity-auxiliary-popover
      ref="entityAuxiliaryPopover"
      :variablePopover="variablePopover"
      inAddInput="true"
      @setSlot="setSlot"
    />
  </el-dialog>
</template>

<script>
import SelectEntityPopover from './selectEntity.vue'
import EntityAuxiliaryPopover from './replyEntityAuxiliaryPopover.vue'
import ReplyEditor from './replyEditor.vue'
import getTextBoundingRect from '@U/getTextBoundingRect.js'
export default {
  name: 'reply-dialog',
  props: {
    dialog: {
      type: Object,
      default: {},
    },
    subAccountEditable: Boolean,
    slotId: [String, Number],
    businessId: String,
    intentId: [String, Number],
    qaSentence: [String, Array],
    slotNames: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      saving: false,
      textReg: /^[\u4e00-\u9fffa-zA-Z0-9 ]{1,50}$/,
      questionSentence: [],
      qaLoading: false,
      replies: [],
      utteranceAddName: '',
      utterancesData: {
        loading: true,
        total: 0,
        page: 1,
        size: 5,
        list: [],
      },
      oldUtterances: [],
      // 辅助词
      auxiliarySlotsData: {
        loading: true,
        total: 0,
        page: 1,
        size: 5,
        list: [],
      },
      oldAuxiliarySlotList: [],
      variablePopover: {
        show: false,
        rect: null,
      },
      placeholder: '回车添加用户回答的常用表达',
      editSlot: false,
      cursorPos: -1,
      useInReplyDialog: true,
    }
  },
  computed: {
    utteranceColor() {
      return this.$store.state.studioSkill.utteranceColor
    },
  },
  watch: {
    'dialog.show': function (val, oldVal) {
      if (val) {
        this.refreshData(1)
      } else {
        this.utteranceAddName = ''
        this.$emit('change')
      }
    },
    'variablePopover.show': function (val) {
      if (val) {
        this.editSlot = true
      } else {
        this.editSlot = false
      }
    },
  },
  methods: {
    // 获取语料列表
    getUtterances(page) {
      let self = this
      this.utterancesData.loading = true
      this.$utils.httpGet(
        this.$config.api.STUDIO_INTENT_UTTERANCES,
        {
          businessId: this.businessId,
          intentId: this.intentId,
          pageIndex: page || this.utterancesData.page || 1,
          pageSize: this.utterancesData.size,
          // utterance: this.utteranceSearchName,
          type: 3,
          slotId: this.slotId,
        },
        {
          success: (res) => {
            self.utterancesData.list = res.data.utterances
            self.oldUtterances = []
            self.utterancesData.total = res.data.count
            self.utterancesData.page = res.data.pageIndex
            self.utterancesData.size = res.data.pageSize
            self.$nextTick(function () {
              self.oldUtterances = JSON.parse(
                JSON.stringify(res.data.utterances)
              )
            })
            self.utterancesData.loading = false
          },
          error: (err) => {
            self.utterancesData.loading = false
          },
        }
      )
    },
    addUtteranceBlur(event) {
      event.target.blur()
    },
    utterHasSlots(data) {
      let self = this
      let tmp = data.split(/[{}]/) || []
      const result = tmp.filter((item) => self.slotNames.includes(item))
      if (result.length) {
        return true
      }
      return false
    },
    addUtterance() {
      let self = this
      if (self.editSlot) return
      if (self.utteranceAddName && self.utterancesData.total >= 50) {
        return self.$message.warning('一个槽位最多仅能创建50条回答语料')
      }
      this.utteranceAddName = this.$utils.strictTrimSpace(this.utteranceAddName)
      if (!this.utteranceAddName) {
        return
      }
      let nameValid = this.$rules.judgeUtteranceParams(
        this.utteranceAddName,
        50
      )
      if (!nameValid.valid) {
        return self.$message.warning(nameValid.data.message)
      }
      if (!self.utterHasSlots(this.utteranceAddName)) {
        return self.$message.warning('回答语料中必须包含此意图内实体槽位')
      }
      this.$utils.httpPost(
        this.$config.api.STUDIO_INTENT_ADD_EDIT_UTTERANCE,
        {
          businessId: this.businessId,
          intentId: this.intentId,
          utterance: this.utteranceAddName,
          type: 3,
          slotId: this.slotId,
        },
        {
          success: (res) => {
            self.$message.success('添加成功')
            let nowTotal = this.utterancesData.total + 1
            let basePage = nowTotal % this.utterancesData.size ? 1 : 0
            self.utteranceAddName = ''
            self.refreshData(1)
            self.$refs.addUtter && self.$refs.addUtter.$refs.input.focus()
          },
          error: (err) => {},
        }
      )
    },
    refreshData(utterancePage) {
      this.getUtterances(utterancePage)
      this.getAuxiliarySlots()
    },
    delReply(utterance, index) {
      let self = this

      this.$utils.httpPost(
        this.$config.api.STUDIO_INTENT_DEL_UTTERANCE,
        {
          id: utterance.id,
          businessId: this.businessId,
          intentId: this.intentId,
          utterance: this.oldUtterances[index].utterance,
        },
        {
          success: (res) => {
            self.$message.success('删除成功')
            let nowTotal = this.utterancesData.total - 1
            let basePage = nowTotal % this.utterancesData.size ? 1 : 0
            let page =
              parseInt(nowTotal / this.utterancesData.size) + basePage >
              this.utterancesData.page
                ? this.utterancesData.page
                : parseInt(nowTotal / this.utterancesData.size) + basePage
            self.refreshData(page)
          },
          error: (err) => {},
        }
      )
    },
    // 获取辅助词槽位
    getAuxiliarySlots() {
      let self = this
      this.auxiliarySlotsData.loading = true
      this.auxiliarySlotsData.list = []
      this.$utils.httpGet(
        this.$config.api.STUDIO_INTENT_SLOTS,
        {
          businessId: this.businessId,
          intentId: this.intentId,
          type: 2,
          slotId: this.slotId,
        },
        {
          success: (res) => {
            res.data.forEach(function (item, index) {
              if (item.slotName) {
                self.$store.dispatch(
                  'studioSkill/setUtteranceColor',
                  item.slotName
                )
              }
            })
            this.auxiliarySlotsData.list = res.data
            this.oldAuxiliarySlotList = JSON.parse(JSON.stringify(res.data))
            this.auxiliarySlotsData.loading = false
          },
          error: (err) => {
            this.auxiliarySlotsData.loading = false
          },
        }
      )
    },
    openSelectEntity(data, event, style) {
      let rect = event.target.getBoundingClientRect()
      this.$store.dispatch('studioSkill/setEntityPopover', {
        show: true,
        showType: 'replyDialog',
        data: data,
        style: style,
        rect: {
          top: rect.top,
          left: rect.left,
          width: rect.width,
          y: rect.y,
        },
      })
    },
    flowerBracketHandle(e) {
      let self = this
      let flowerKeyCode = [219, 37] // 37 是中文的 }, 搜狗输入法中文状态时输入 { 时会自动补全 {}
      // 上下箭头
      if (e.keyCode === 38 || e.keyCode === 40) return
      if (!flowerKeyCode.includes(e.keyCode) && self.cursorPos === -1) return
      const cursor = this.$refs.addUtter.$refs.input.selectionStart //input 里的鼠标当前位置
      const value = e.target.value
      let inputWrap = this.$refs.addUtter
      // const rect = e.target.getBoundingClientRect()
      const input = this.$refs.addUtter.$refs.input
      const { selectionStart, selectionEnd } = input
      const rect = getTextBoundingRect(
        input,
        selectionStart,
        selectionEnd,
        false
      )
      let searchVal = ''
      if (value[cursor - 1] === '{') {
        self.cursorPos = cursor
      }
      if (value.substring(cursor - 2, cursor) == '{}') {
        self.cursorPos = cursor - 1
      }
      if (self.cursorPos > cursor) {
        return (self.variablePopover.show = false)
      }
      self.cursorPos !== -1 &&
        setTimeout(function () {
          self.variablePopover = {
            show: true,
            rect: rect,
            cursorPos: cursor,
            searchVal: value.substring(self.cursorPos).replace(/}+/g, ''),
          }
          self.$nextTick(() => {
            self.$refs.entityAuxiliaryPopover.inputFocus()
          })
        }, 0)
    },
    setSlot(item) {
      if (!item) return (this.cursorPos = -1)
      let regL = /{+/g
      let regR = /}+/g
      let searchValLen = this.variablePopover.searchVal
        ? this.variablePopover.searchVal.length
        : 0
      this.utteranceAddName =
        this.utteranceAddName.substring(0, this.cursorPos) +
        item +
        '}' +
        this.utteranceAddName.substring(this.cursorPos + searchValLen)
      this.utteranceAddName = this.utteranceAddName
        .replace(regL, '{')
        .replace(regR, '}')
      let currentCursorPos = this.cursorPos + item.length + 1
      let input = this.$refs.addUtter.$refs.input
      input.setSelectionRange(this.cursorPos, currentCursorPos)
      input.focus()
      this.cursorPos = -1
    },
    handleUp() {
      if (!this.variablePopover.show) return
      this.$refs.entityAuxiliaryPopover.handleUp()
    },
    handleDown() {
      if (!this.variablePopover.show) return
      this.$refs.entityAuxiliaryPopover.handleDown()
    },
  },
  components: {
    SelectEntityPopover,
    ReplyEditor,
    EntityAuxiliaryPopover,
  },
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="scss" scoped>
.utterance-collapse-item {
  border: 1px solid $grey2;
  border-bottom: 0;
  transition: all 0.2s;
  &-show {
    border-bottom: 2px solid $primary;
  }
}
.utterance-collapse-item-title {
  position: relative;
  &-del {
    position: absolute;
    right: 16px;
    top: 0;
    bottom: 0;
    margin: auto;
    line-height: 44px;
    cursor: pointer;
    display: none;
  }
  &:hover {
    .utterance-collapse-item-title-del {
      display: block;
    }
  }
}
.utterance-area {
  padding: 11px 27px 11px 11px;
  height: auto;
  min-height: 44px;
  background-color: #fff;
  z-index: 1;
  display: flex;
  align-items: center;
  overflow: hidden;
  &-template {
    color: $grey3;
    margin-right: 20px;
  }
  &-del {
    line-height: 22px;
    display: none;
    cursor: pointer;
    color: $grey4;
    position: absolute;
    right: 10px;
  }
  &:hover {
    .mark1,
    .mark2,
    .utterance-area-del {
      display: block;
    }
  }
}
.utterance-area-disabled {
  background-color: #f2f5f7;
}

.section-title {
  margin: 28px 0 16px;
  font-size: 18px;
}

.add-reply {
  height: 44px;
  line-height: 44px;
  border: 1px solid $grey2;
  padding: 11px 27px 11px 11px;
  background-color: #fff;
  z-index: 1;
  display: flex;
  align-items: center;
}
.add-reply:empty:before {
  content: attr(placeholder);
  color: #b8babf;
}
.qa-sentence {
  margin-right: 24px;
  padding: 2px 15px;
  color: $grey5;
}
.reply-utter-tip {
  margin-bottom: 16px;
  padding: 9px 16px;
  line-height: 22px;
  background: rgba(23, 132, 233, 0.07);
  border-radius: 3px;
}
.utter-pagination {
  margin: 16px auto 28px;
  text-align: center;
}
.reply-utter-dialog {
  :deep(.el-dialog) {
    height: 640px;
  }
  :deep(.os-collapse-title) {
    margin: 28px 0 24px;
  }
  :deep(.el-dialog__body) {
    padding: 5px 20px 0 32px;
  }
}
.qa-collapse {
  margin-bottom: 28px;
  :deep(.os-collapse-title) {
    margin-top: 0;
  }
}
</style>
