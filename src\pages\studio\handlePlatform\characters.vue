<template>
  <div class="os-scroll">
    <handle-platform-top></handle-platform-top>
    <div class="handle-platform-content" style="padding-top: 20px">
      <div class="mgb24" style="font-size: 0">
        <el-button
          icon="ic-r-plus"
          type="primary"
          size="medium"
          @click="openCharacter('create')"
          >&nbsp;设备人设</el-button
        >
        <div class="fr" @keyup.enter="getCharacters(1)">
          <el-input
            class="search-area"
            placeholder="搜索人设"
            size="medium"
            v-model="searchVal"
          >
            <i
              slot="suffix"
              class="el-input__icon el-icon-search search-area-btn"
              @click="getCharacters(1)"
            />
          </el-input>
        </div>
      </div>
      <os-table
        :tableData="tableData"
        style="margin-bottom: 56px"
        class="characters-table"
        @change="getCharacters"
        @edit="toEdit"
        @copy="copy"
        @del="del"
        @row-click="toEdit"
      >
        <el-table-column prop="name" width="320" label="设备人设">
          <template slot-scope="scope">
            <os-character-simple-item
              class="cp characters-page-character-zh-name"
              :name="scope.row.name || '-'"
              :url="scope.row.image"
              @click.native.stop.prevent="toEdit(scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column prop="propertyCount" width="200" label="属性数量">
          <template slot-scope="scope">{{ scope.row.propertyCount }}</template>
        </el-table-column>
        <el-table-column prop="updateTime" width="280" label="更新时间">
          <template slot-scope="scope">
            <div>{{ scope.row.updateTime | date('yyyy-MM-dd hh:mm:ss') }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="useCount" width="200" label="被引用数">
          <template slot-scope="scope">
            <div
              v-if="scope.row.useCount"
              class="text-primary"
              style="cursor: pointer; height: 40px; line-height: 40px"
              @click.stop="openCountDialog(scope.row)"
            >
              {{ scope.row.useCount }}
            </div>
            <span v-else>{{ scope.row.useCount }}</span>
          </template>
        </el-table-column>
      </os-table>
      <div class="create-guide" v-if="tableData.list.length == 1">
        <div class="icon"></div>
        <p class="title">
          你还没有创建自己的设备人设，
          <a @click="openCharacter('create')">点击创建</a>
        </p>
        <p class="desc">
          设备人设由能够代表设备形象的多种属性构成，通过配置人设中属性的属性值与回复，使产品拟人化，满足厂商与开发者快速自定义产品形象的需求。
        </p>
        <a :href="`${$config.docs}doc-68/`" target="_blank">了解更多</a>
      </div>
    </div>
    <create-character-dialog :dialog="dialog" slot="character" />
    <skill-quote-dialog :dialog="countDialog" type="character" />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import HandlePlatformTop from './top.vue'
import CreateCharacterDialog from './dialog/createCharacter.vue'
import SkillQuoteDialog from './dialog/skillQuotecharater.vue'

export default {
  name: 'studio-handle-platform-entities',
  data() {
    return {
      nav: 'characters',
      hasCharacter: true,
      searchVal: '',
      tableData: {
        loading: true,
        total: 0,
        page: 1,
        size: 10,
        handles: ['edit', 'copy', 'del'],
        handleColumnText: '操作',
        list: [],
      },
      dialog: {
        show: false,
        type: 'save',
        name: '',
      },
      countDialog: {
        show: false,
        id: '',
      },
    }
  },
  computed: {
    ...mapGetters({
      limitCount: 'aiuiApp/limitCount',
    }),
  },
  created() {
    this.getCharacters(1)
    this.$store.dispatch('aiuiApp/setLimitCount')
    if (this.$route.query.create) {
      this.dialog.show = true
      this.dialog.type = 'create'
    }
  },
  mounted() {
    if (localStorage.getItem('addCharacter') === 'true') {
      this.openCharacter('create')
      localStorage.setItem('addCharacter', false)
    }
  },
  methods: {
    // 创建
    openCharacter(type, data) {
      this.dialog.type = type
      this.dialog.show = true
      this.dialog.name = data ? data.name : ''
      this.dialog.id = data ? data.id : ''
    },
    // 获取列表
    getCharacters(page) {
      let self = this
      this.tableData.loading = true
      this.$utils.httpGet(
        this.$config.api.STUDIO_CHARACTER_LIST,
        {
          pageIndex: page || this.tableData.page,
          pageSize: this.tableData.size,
          search: this.searchVal,
        },
        {
          success: (res) => {
            if (res.data.list && !res.data.list.length && !self.searchVal) {
              self.hasCharacter = false
            } else {
              self.hasCharacter = true
            }
            self.tableData.list = res.data.list
            self.tableData.total = res.data.count
            self.tableData.page = res.data.pageIndex
            self.tableData.size = res.data.pageSize
            self.tableData.loading = false
          },
          error: (err) => {},
        }
      )
    },
    // 打开引用dialog
    openCountDialog(item) {
      this.countDialog.show = true
      this.countDialog.id = item.id
    },
    // 编辑
    toEdit(data) {
      let routeData
      routeData = this.$router.resolve({
        name: 'character',
        params: { characterId: data.id },
      })
      window.open(routeData.href, '_blank')

      // this.$router.push({
      //   name: 'character',
      //   params: { characterId: data.id },
      // })
    },
    // 复制
    copy(data) {
      if (data.name && data.name.length > 28) {
        this.openCharacter('copy', data)
        return
      }
      let params = {
        fromId: data.id,
      }
      this.tableData.loading = true
      let api = this.$config.api.STUDIO_CHARACTER_ADD
      this.$utils.httpPost(api, params, {
        success: (res) => {
          this.tableData.loading = false
          this.$message.success('成功创建副本。')
          this.getCharacters()
        },
        error: (err) => {
          if (err.code === '328001') {
            // this.openCharacter('copy', data)
          }
          this.tableData.loading = false
        },
      })
    },
    // 删除
    del(data) {
      let self = this
      if (data.type === 2) {
        this.$message.warning('默认人设不支持删除')
        return
      }
      if (data.useCount) {
        this.$message.warning(
          '设备人设已被引用，为了您的设备/应用保持正常的语音交互，请取消引用后再删除'
        )
        return
      }
      this.$confirm(
        '删除后不可恢复，请谨慎操作。',
        `要删除设备人设 - ${data.name}？`,
        {
          confirmButtonText: '确定删除',
          cancelButtonText: '取消',
          confirmButtonClass: 'el-button--danger',
          type: 'warning',
          showClose: false,
        }
      )
        .then(() => {
          self.delCharacter(data)
        })
        .catch(() => {})
    },
    delCharacter(data) {
      let params = {
        id: data.id,
      }
      let api = this.$config.api.STUDIO_CHARACTER_DEL
      this.$utils.httpPost(api, params, {
        success: (res) => {
          this.$message.success('删除成功')
          this.getCharacters()
        },
        error: (err) => {
          console.log(err)
        },
      })
    },
  },
  components: {
    HandlePlatformTop,
    CreateCharacterDialog,
    SkillQuoteDialog,
  },
}
</script>

<style lang="scss" scoped>
.handle-platform-content {
  max-width: 1200px;
  margin: auto;
}
.search-area {
  width: 480px;
}
.character-status {
  width: 8px;
  height: 8px;
  border: 2px solid;
  border-radius: 8px;
  margin-right: 6px;
  &-1 {
    border-color: $warning;
  }
  &-2 {
    border-color: $primary;
  }
  &-3 {
    border-color: $success;
  }
  &-4 {
    border-color: $dangerous;
  }
  &-5 {
    border-color: $grey5;
  }
}
.characters-table {
  :deep(.el-table .cell) {
    text-overflow: initial;
  }
  :deep(.el-table tr) {
    cursor: pointer;
  }
}
.characters-table-name {
  cursor: pointer;
}
.os-character-simple-item {
  width: 300px;
}

.btn-create-extend-character {
  font-size: 14px;
  margin-left: 32px;
}

.intent-tag.private {
  color: $semi-black;
  background-color: $grey1;
}

.create-guide {
  margin: 76px 0;
  text-align: center;
  font-size: 16px;
  color: $semi-black;
  .icon {
    margin: 0 auto 24px;
    width: 120px;
    height: 120px;
    background: url(../../../assets/images/app/create-app.png) center no-repeat;
    background-size: 100%;
  }
  .title {
    font-size: 16px;
    font-weight: 600;
    a {
      font-weight: 600;
    }
  }
  .desc {
    margin: 24px auto;
    width: 480px;
    font-size: 14px;
    line-height: 22px;
  }
}
</style>
<style lang="scss">
.characters-table {
  .el-table td {
    padding: 0;
  }
}
tr:hover > td {
  .characters-page-character-zh-name > p {
    color: $primary;
  }
}
.el-table .ic-r-edit {
  color: $primary;
}
</style>
