<template>
  <el-dialog
    title="问答库设置"
    :visible="thresholdVisible"
    width="570px"
    @close="onClose"
    :modal="false"
  >
    <!-- <p class="add-skill-title skill-version-title">问答库版本</p> -->
    <os-table
      class="mgb24 table-container"
      :tableData="tableData"
      @change="getVersion"
    >
      <el-table-column
        prop="outNumber"
        label="版本"
        width="130"
      ></el-table-column>
      <el-table-column label="版本说明" width="130">
        <template slot-scope="scope">
          <span v-if="!scope.row.updateLog">-</span>
          <el-popover
            v-else
            class="update-log"
            trigger="hover"
            placement="bottom-start"
            :content="scope.row.updateLog"
          >
            <div slot="reference">{{ scope.row.updateLog }}</div>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column prop="date" label="发布时间" width="130">
        <template slot-scope="scope">{{
          scope.row.createTime | date('yyyy-MM-dd')
        }}</template>
      </el-table-column>
      <el-table-column label="引用" width="55">
        <template slot-scope="scope">
          <el-radio
            v-model="radio"
            :label="scope.row.id"
            @change="setQaVersion(scope.row)"
            >&nbsp;</el-radio
          >
        </template>
      </el-table-column>
    </os-table>

    <span slot="footer" class="dialog-footer">
      <el-button @click="toggleThresholdVisible(false)">取消</el-button>
      <el-button type="primary" @click="thresholdConfirm">确定</el-button>
    </span>
  </el-dialog>
</template>
<script>
export default {
  props: {
    thresholdVisible: Boolean,
    configSkillItem: Object,
    appId: String,
    currentScene: Object,
    ubotQaConfig: Object,
  },
  data() {
    return {
      qaVersionChange: false,
      mallId: Number,
      outNumber: '',
      radio: '',
      tableData: {
        total: 0,
        page: 1,
        size: 3,
        list: [],
      },
    }
  },
  watch: {
    thresholdVisible(val) {
      this.$set(this.tableData, 'page', 1)
      if (val) {
        this.configThreshold(this.configSkillItem)
      }
    },
  },
  methods: {
    onClose() {
      this.toggleThresholdVisible(false)
    },
    toggleThresholdVisible(val) {
      this.$emit('thresholdVisibleChange', val)
    },

    configThreshold(item) {
      let self = this
      this.configSkillItem = item
      this.qaVersionChange = false
      this.getVersions(item)
    },

    thresholdConfirm() {
      if (!this.qaVersionChange) {
        this.toggleThresholdVisible(false)
        return
      }
      debugger

      if (this.qaVersionChange) {
        let data = {}
        if (this.ubotQaConfig.hasOwnProperty(this.configSkillItem.id)) {
          console.log('111', this.ubotQaConfig)
          console.log('222', this.configSkillItem)
          data = this.ubotQaConfig[this.configSkillItem.id]
          data.qaId = this.configSkillItem.id
        } else {
          console.log('this.configSkillItem', this.configSkillItem)
          data = this.configSkillItem
          data.qaId = data.id
          data.operation = 'open'
        }
        data.verId = this.mallId

        console.log('this.qaVersionChange', data)

        this.ubotQaConfig[data.qaId] = data
      }
      this.$emit('change')
      // 关闭弹窗
      this.toggleThresholdVisible(false)
    },

    getVersions(item) {
      let self = this,
        tmp = {}
      self.tableData.loading = true
      this.$utils.httpGet(
        this.$config.api.STUDIO_KEY_QA_VERSIONS,
        {
          qaId: item.id,
        },
        {
          success: (res) => {
            self.tableData.loading = false
            self.qaVersion = res.data.list
            self.tableData.total = res.data.count
            self.tableData.list = []
            tmp = self.qaVersion.find(function (arr, index) {
              if (self.isNumberEqual(item.outNumber, arr.outNumber)) {
                self.radio = arr.id
                let i = index / self.tableData.size
                self.currentPage6 = parseInt(i) + 1
                return arr
              }
            })
            this.getVersion(self.currentPage6)
          },
          error: (err) => {
            self.tableData.loading = false
          },
        }
      )
    },
    getVersion(page) {
      let self = this
      this.tableData.page = page || 1
      self.tableData.loading = true
      let pageIndex = page || self.tableData.page
      let from = (pageIndex - 1) * this.tableData.size
      let to = from + this.tableData.size
      self.tableData.list = self.qaVersion.slice(from, to)
      self.tableData.loading = false
    },
    isNumberEqual(num1 = '0.0.0', num2 = '0.0.0') {
      let arr1 = num1.split('.'),
        arr2 = num2.split('.')
      if (arr1[0] == arr2[0] && arr1[1] == arr2[1] && arr1[2] == arr2[2]) {
        return true
      }
    },
    setQaVersion(mall) {
      this.mallId = mall.id
      this.outNumber = mall.outNumber
      this.qaVersionChange = true
    },
  },
}
</script>
<style lang="scss" scoped>
@import '../style.scss';
.table-container {
  :deep(.el-pagination) {
    overflow: auto;
  }
}
</style>
