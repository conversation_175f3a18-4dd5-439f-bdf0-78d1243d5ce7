<template>
  <div style="height: 100%">
    <label-selector
      @select="onLabelSelect"
      title="问答回复"
      v-if="avatar"
    ></label-selector>

    <qa-answers-text-adder
      :data="qaAnswers || []"
      :disabled="false"
      @add="add"
      @del="del"
      @edit="edit"
      @change="onInputChange"
      @dataChange="onDataChange"
      :reg="textReg"
      :warning="warning"
      :max="10000"
      editPlaceholder="输入答案，回车添加"
      placeholder="输入答案，回车添加"
      ref="qaAnswerRef"
      :qaType="qaType"
      :showStyle="showStyle"
      :avatar="avatar"
    >
    </qa-answers-text-adder>
  </div>
</template>

<script>
import qaAnswersTextAdder from './qaAnswersTextAdder.vue'
import labelSelector from '../skill/labelSelector.vue'
export default {
  components: { qaAnswersTextAdder, labelSelector },
  props: {
    list: [],
    qaType: {
      type: String,
      default: 'sentence',
    },
    showStyle: {
      type: Boolean,
      default: false,
    },
    avatar: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      saving: false,
      qaAnswers: [],
      textReg: /^\s*[\s\S]{1,1000}\s*$/,
      // textReg: /^((?!\<avatar\>|\<avatar\/\>|\<action\>|\<action\/\>)[\s\S]){1,1000}$/,
      // textReg: /(<([^>]+)>){1,1000}$/,
      warning: '答案为1000字符以内',
    }
  },

  mounted() {
    this.qaAnswers = (this.list.slice() || []).map((item) => {
      return {
        ...item,
        changed: false,
      }
    })
  },
  watch: {
    avatar(val) {
      console.log('avatar changed in qaAnswers', val)
    },
  },
  methods: {
    onInputChange(index) {
      this.qaAnswers = (this.qaAnswers || []).map((item, i) => {
        if (index === i) {
          return {
            ...item,
            changed: true,
          }
        } else {
          return {
            ...item,
          }
        }
      })
    },
    onLabelSelect(label) {
      this.$refs.qaAnswerRef.$refs.intelInput.insertLabel(label)
    },

    add(text) {
      // debugger
      // let qaAnswers = JSON.parse(JSON.stringify(this.qaAnswers))
      // qaAnswers[qaAnswers.length] = text
      const qaAnswers = [text, ...this.qaAnswers]
      this.save(qaAnswers, 'add')
    },
    del(text, index) {
      if (this.saving) {
        return
      }
      this.saving = true
      let qaAnswers = (this.qaAnswers = Array.prototype.filter.call(
        this.qaAnswers,
        function (item, i) {
          return index !== i
        }
      ))
      this.save(qaAnswers, 'sub')
    },
    edit(text, index) {
      const otherItems = (this.qaAnswers || []).filter(
        (_, idx) => idx !== index
      )
      const find = otherItems.findIndex((itm) => itm.answer === text.answer)
      if (find !== -1) {
        return this.$message.error('不得与其他条目重复')
      }
      let qaAnswers = (this.qaAnswers || []).map((item, i) => {
        if (index === i) {
          return {
            ...item,
            ...text,
          }
        } else {
          return { ...item }
        }
      })
      this.save(qaAnswers, 'edit', index)
    },
    save(list, mode, index) {
      let self = this
      if (mode === 'edit') {
        self.qaAnswers = (list || []).map((it, i) => {
          if (index === i) {
            return {
              ...it,
              changed: false,
            }
          } else {
            return { ...it }
          }
        })
      } else {
        self.qaAnswers = list || []
      }
      let resultList = (list || []).map((item) => {
        let obj = {
          ...item,
          answer: item.answer,
          labels: (item.labels || []).map(({ ...rest }) => {
            return { ...rest }
          }),
        }
        delete obj.changed
        return obj
      })
      this.$emit('setQaAnswers', resultList)
      this.saving = false
    },

    onDataChange(data) {
      this.save(data, 'modify')
    },
  },
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="scss" scoped>
.dialog-official-intents {
  margin-bottom: 15px;
}
.dialog-official-intent-selectnum {
  color: $grey4;
  margin-right: 24px;
}

.container {
  width: 100%;
  // border: 1px solid #d5d8de;
  // padding: 0 16px;
  .confirm-adder {
    display: flex;
    align-items: center;
  }
  .confirm-list {
    padding-top: 15px;
    > li {
      display: flex;
      align-items: center;
      position: relative;
      padding-right: 20px;
      &:hover {
        .delete {
          display: block;
        }
      }
      .delete {
        position: absolute;
        right: 0;
        color: #b8babf;
        cursor: pointer;
        display: none;
        color: #1784e9;
        // &:hover {
        //   color: #1784e9;
        // }
      }
    }
    li + li {
      margin-top: 10px;
    }
    margin-bottom: 0;
  }
  .number-label {
    margin-right: 20px;
    color: #b8babf;
  }
}
</style>
