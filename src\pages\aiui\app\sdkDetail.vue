<template>
  <div>
    <div class="whitelist-conf-title">SDK详情列表</div>
    <table class="table">
      <thead>
        <tr>
          <th colspan="1" rowspan="1" width="140">能力名称</th>
          <th colspan="1" rowspan="1">主要功能</th>
          <th colspan="1" rowspan="1" width="140">SDK名称</th>
          <th colspan="1" rowspan="1" width="140">版本号</th>
          <th colspan="1" rowspan="1" width="120">开发者名称</th>
          <th colspan="1" rowspan="1" width="70">平台</th>
          <th colspan="1" rowspan="1" width="130">个人信息处理规则</th>
          <th colspan="1" rowspan="1" width="130">合规使用说明</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td colspan="1" rowspan="4">AIUI语音交互</td>
          <td colspan="1" rowspan="4">
            全链路的语音交互接入，支持大模型高阶语义理解
          </td>
          <td colspan="1" rowspan="1">Android SDK</td>
          <td colspan="1" rowspan="1">{{ androidVersion }}</td>
          <td colspan="1" rowspan="48">科大讯飞股份有限公司</td>
          <td colspan="1" rowspan="1">Android</td>
          <td colspan="1" rowspan="4">
            <a
              href="https://aiui-doc.xf-yun.com/project-1/doc-191/"
              target="blank"
              i_l_d_20180504="t"
              >点击查看</a
            >
          </td>
          <td colspan="1" rowspan="4">
            <a
              href="https://aiui-doc.xf-yun.com/project-1/doc-192/"
              target="blank"
              i_l_d_20180504="t"
              >点击查看</a
            >
          </td>
        </tr>
        <tr>
          <td colspan="1" rowspan="1">Linux SDK</td>
          <td colspan="1" rowspan="1">{{ linuxVersion }}</td>
          <td colspan="1" rowspan="1">Linux</td>
        </tr>
        <tr>
          <td colspan="1" rowspan="1">Windows SDK</td>
          <td colspan="1" rowspan="1">{{ windowsVersion }}</td>
          <td colspan="1" rowspan="1">Windows</td>
        </tr>
        <tr>
          <td colspan="1" rowspan="1">iOS SDK</td>
          <td colspan="1" rowspan="1">{{ iOSVersion }}</td>
          <td colspan="1" rowspan="1">iOS</td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script>
export default {
  name: 'sdk-log',
  props: {
    platform: String,
  },
  data() {
    return {
      sdkLog: [],
      linuxVersion: '',
      windowsVersion: '',
      iOSVersion: '',
      androidVersion: '',
    }
  },
  created() {
    this.getSDKVersions()
  },
  methods: {
    getSdkLog(platform) {
      return new Promise((resolve, reject) => {
        if (!platform) reject()
        let self = this
        this.$utils.httpGet(
          this.$config.api.AIUI_APP_SDKVERSION,
          {
            platform: platform,
            appid: this.$route.params.appId,
          },
          {
            success: (res) => {
              if (res.flag) {
                if ((res.data || []).length > 0) {
                  resolve(res.data[0].sdkVersion)
                }
              } else {
                reject()
              }
            },
          }
        )
      })
    },
    getSDKVersions() {
      this.getSdkLog('Linux').then((res) => {
        this.linuxVersion = res
      })
      this.getSdkLog('Windows').then((res) => {
        this.windowsVersion = res
      })
      this.getSdkLog('iOS').then((res) => {
        this.iOSVersion = res
      })
      this.getSdkLog('Android').then((res) => {
        this.androidVersion = res
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.whitelist-conf-title {
  position: relative;
  font-size: 14px;
  font-weight: bold;
  padding-left: 10px;
  margin-top: 15px;
  margin-bottom: 8px;
  color: #555454;
  &:before {
    width: 2px;
    height: 16px;
    background-color: $primary;
    content: ' ';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    margin: auto;
  }
}
.table {
  border: 1px solid #e4e7ed;
  border-collapse: collapse;
  margin-bottom: 24px;
  margin-top: 5px;
  width: 100%;
  thead {
    tr {
      th {
        border: 1px solid #e4e7ed;
        font-size: 14px;
        padding: 10px 6px;
        text-align: center;
        background-color: #f2f5f7;
      }
    }
  }
  tbody {
    tr {
      td {
        text-align: center;
        padding: 6px;
        vertical-align: middle;
        border-collapse: collapse;
        font-size: 14px;
        border: 1px solid #e4e7ed;
        color: #595959;
      }
    }
  }
}
</style>
