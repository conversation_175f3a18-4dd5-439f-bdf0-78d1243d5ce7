<template>
  <el-dialog title="添加资源" :visible.sync="dialog.show" width="480px">
    <el-form ref="form" :model="form" :rules="rules" label-position="top">
      <el-form-item label="资源名称" prop="name">
        <el-input
          v-model.trim="form.name"
          placeholder="支持字母/数字/下划线，不超过20个字符"
        />
      </el-form-item>
      <el-form-item label="资源维度" prop="type">
        <el-select
          v-model="form.type"
          placeholder="请选择"
          style="width: 416px"
        >
          <el-option
            v-for="(item, index) in types"
            :key="index"
            :label="item.label"
            :value="item.type"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="维度名" v-if="form.type === 3" prop="title">
        <el-input v-model.trim="form.title" placeholder="请输入维度名" />
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button
        class="dialog-btn"
        type="primary"
        size="small"
        style="min-width: 80px"
        :disabled="!canSave"
        @click="submitForm"
      >
        确定
      </el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: 'add-resource',
  props: {
    dialog: {
      type: Object,
      default: () => ({}),
    },
    oldList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      form: {
        personalId: -1,
        name: '', //英文
        type: '',
        title: '',
        mainField: 'name',
        slaveField: 'alias',
        preprocess: '0,0,0',
        extend: 'none',
        disabled: false,
      },
      rules: {
        name: [
          this.$rules.required('资源名称不能为空'),
          { validator: this.checkName, trigger: 'blur' },
        ],
        type: [this.$rules.required('资源维度不能为空')],
        title: [
          this.$rules.required('维度名不能为空'),
          { validator: this.checkTitle, trigger: 'blur' },
        ],
      },
      types: [
        { type: 1, label: 'AIUI应用级' },
        { type: 2, label: 'AIUI用户级' },
        { type: 3, label: '自定义级' },
        // {type: 4, label: 'OS产品级'},
        // {type: 5, label: 'OS设备级'},
        // {type: 6, label: 'OS用户级'},
      ],
      nameRepeated: null,
    }
  },
  computed: {
    canSave() {
      return this.form.name && this.form.type ? true : false
    },
  },
  watch: {
    'dialog.show': function (val, oldVal) {
      if (val) {
        this.form = {
          personalId: -1,
          name: '',
          type: '',
          title: '',
          mainField: 'name',
          slaveField: 'alias',
          preprocess: '0,0,0',
          extend: 'none',
          disabled: false,
        }
        this.$refs.form && this.$refs.form.resetFields()
        this.nameRepeated = null
      }
    },
  },
  methods: {
    checkName(rule, name, callback) {
      let self = this
      if (!name) {
        return callback(new Error('资源名称不能为空'))
      }
      if (name.length > 20) {
        return callback(new Error('资源名称长度不能超过20个字符'))
      }
      if (!/^[a-zA-Z0-9_]{0,}$/.test(name)) {
        return callback(new Error('资源名称仅支持字母/数字/下划线'))
      }
      if (self.nameRepeated === name) {
        return callback(new Error('资源名称已存在'))
      }
      this.$utils.httpPost(
        this.$config.api.STUDIO_ENTITY_PERSONAL_CHECKNAME,
        {
          entityId: self.$route.params.entityId,
          name: name || '',
        },
        {
          success: (res) => {
            self.nameRepeated = null
            callback()
          },
          error: (err) => {
            self.nameRepeated = name
            callback(new Error('资源名称已存在'))
          },
        }
      )
    },
    checkTitle(rule, val, callback) {
      if (val && val.length > 20) {
        return callback(new Error('维度名长度不能超过20个字符'))
      }
      let osResourceTitle = [
        'none',
        'os_client_id',
        'os_device_id',
        'os_user_id',
      ]
      if (osResourceTitle.includes(val.toLowerCase())) {
        return callback(
          new Error(
            '维度名不能为: none, os_client_id, os_device_id, os_user_id'
          )
        )
      }
      callback()
    },
    submitForm() {
      let self = this
      if (!self.canSave) {
        return
      }
      let newResource = JSON.parse(JSON.stringify(self.form))
      newResource.type = newResource.type >= 3 ? 3 : newResource.type
      switch (self.form.type) {
        case 4:
          newResource.title = 'os_client_id'
          break
        case 5:
          newResource.title = 'os_device_id'
          break
        case 6:
          newResource.title = 'os_user_id'
          break
        default:
          newResource.title = self.form.title || ''
          break
      }
      let mergeObj = [newResource, ...self.oldList]
      let dynamicJson = Array.prototype.map.call(
        mergeObj,
        function (item, index) {
          let newItem = self.$deepClone(item)
          newItem.preprocess = newItem.preprocess.toString()
          return newItem
        }
      )
      self.$refs['form'].validate((valid) => {
        if (valid) {
          self.$utils.httpPost(
            self.$config.api.STUDIO_ENTITY_PERSONAL_SAVE,
            {
              entityId: self.$route.params.entityId,
              dynamicJson: JSON.stringify(dynamicJson),
            },
            {
              success: (res) => {
                self.$message.success('添加成功')
                self.$emit('getData')
                self.dialog.show = false
              },
              error: (err) => {},
            }
          )
        } else {
          return false
        }
      })
    },
  },
}
</script>
<style scoped></style>
