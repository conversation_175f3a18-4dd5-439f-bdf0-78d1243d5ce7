<template>
  <el-dialog
    :visible.sync="isShow"
    :title="title"
    :close-on-click-modal="false"
    @close="cancel"
    :modal-append-to-body="false"
  >
    <el-form :model="form" :rules="rules" ref="intentForm" size="medium">
      <el-form-item label="意图名称" prop="intentName">
        <el-input
          v-model="form.intentName"
          placeholder="支持中英文/数字/小数点/短横线/下划线,不超过32个字符"
          maxlength="32"
          :disabled="official"
        ></el-input>
      </el-form-item>

      <el-form-item label="英文标识" prop="intentNameEn">
        <el-input
          v-model="form.intentNameEn"
          placeholder="支持英文/数字/小数点/短横线/下划线,不超过32个字符"
          maxlength="32"
          :disabled="official"
        ></el-input>
      </el-form-item>

      <el-form-item label="描述" prop="intentDesc">
        <el-input
          v-model="form.intentDesc"
          type="textarea"
          :rows="3"
          placeholder="请输入意图描述,不超过250个字符"
          maxlength="250"
          show-word-limit
          :disabled="official"
        ></el-input>
      </el-form-item>
    </el-form>

    <span slot="footer" class="dialog-footer">
      <el-button @click="cancel">取消</el-button>
      <el-button
        class="dialog-btn"
        type="primary"
        style="min-width: 104px"
        @click="handleConfirm"
        :loading="saving"
        :disabled="official"
      >
        {{ saving ? '创建中...' : '确定' }}
      </el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: 'IflyAIuiWebIndentDialog',

  data() {
    return {
      isShow: false,
      title: '创建意图',
      saving: false,
      agentId: null,
      form: {
        intentName: null,
        intentNameEn: null,
        intentDesc: null,
      },
      rules: {
        intentName: [
          { required: true, message: '请填写意图名称', trigger: 'blur' },
          {
            pattern: /^[\u4e00-\u9fa5a-zA-Z0-9._-]{0,32}$/,
            message:
              '输入内容只能包含中文、英文字母、数字、小数点、短横线和下划线，且长度不超过32个字符',
            trigger: 'blur',
          },
        ],
        intentNameEn: [
          { required: true, message: '请填写意图标识', trigger: 'blur' },
          {
            pattern: /^[a-zA-Z0-9._-]{0,32}$/,
            message:
              '输入内容只能包含英文字母、数字、小数点、短横线和下划线,且长度不超过32个字符',
            trigger: 'blur',
          },
        ],
        intentDesc: [
          { required: true, message: '请填写描述', trigger: 'blur' },
          { max: 250, message: '描述不能超过250个字符', trigger: 'blur' },
        ],
      },
      intentId: null,
      official: false,
    }
  },

  mounted() {},

  methods: {
    show(agentId, intendInfo) {
      console.log('这个是intendInfo=>', intendInfo)
      this.isShow = true
      this.agentId = agentId
      this.$nextTick(() => {
        this.$refs.intentForm.resetFields()
        if (intendInfo) {
          this.title = intendInfo.official ? '查看意图' : '编辑意图'
          this.form.intentName = intendInfo.intentName
          this.form.intentNameEn = intendInfo.intentNameEn
          this.form.intentDesc = intendInfo.intentDesc
          this.intentId = intendInfo.intentId
          this.official = !!intendInfo.official
        }
      })
    },
    handleConfirm() {
      this.$refs.intentForm.validate((valid) => {
        if (valid) {
          const params = this.intentId
            ? {
                intentName: this.form.intentName,
                intentNameEn: this.form.intentNameEn,
                intentDesc: this.form.intentDesc,
                intentId: this.intentId, //   编辑意图 传intentId
              }
            : {
                intentName: this.form.intentName,
                intentNameEn: this.form.intentNameEn,
                intentDesc: this.form.intentDesc,
                pluginId: this.$route.params.agentId, //   新增意图 传agentId
              }
          if (this.intentId) {
            this.edit(params)
          } else {
            this.add(params)
          }
        }
      })
    },

    add(params) {
      this.saving = true
      this.$utils.httpPost(
        this.$config.api.AGENT_INTENT_CREATE,
        JSON.stringify(params),
        {
          config: {
            headers: {
              'Content-Type': 'application/json;charset=UTF-8',
            },
          },
          success: (res) => {
            if (res.code == '0') {
              this.saving = false
              this.$message.success('新建成功')
              this.cancel()
              this.$emit('refresh')
            }
          },
          error: (err) => {
            this.saving = false
            this.$message.error(err.desc)
          },
        }
      )
    },

    edit(params) {
      this.saving = true
      this.$utils.httpPost(
        this.$config.api.AGENT_INTENT_EDIT,
        JSON.stringify(params),
        {
          config: {
            headers: {
              'Content-Type': 'application/json;charset=UTF-8',
            },
          },
          success: (res) => {
            if (res.code == '0') {
              this.saving = false
              this.$message.success('编辑成功')
              this.cancel()
              this.$emit('refresh')
            }
          },
          error: (err) => {
            this.saving = false
            this.$message.error(err?.desc)
          },
        }
      )
    },
    cancel() {
      this.isShow = false
      this.agentId = null
      this.intentId = null
      this.title = '创建意图'
    },
  },
}
</script>

<style lang="scss" scoped>
:deep(.el-form, .el-textarea) {
  .el-input__count {
    color: #909399;
    position: absolute;
    font-size: 12px;
    bottom: -28px;
    right: 10px;
  }
}
</style>
