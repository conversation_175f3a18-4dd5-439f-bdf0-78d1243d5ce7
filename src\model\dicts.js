/**
 * dicts [字典文件 -- 数据字典]
 */
export default {
  dictArr: {
    skillType: [
      { key: 2, value: 'aiui技能' },
      { key: 5, value: '开放技能' },
      { key: 6, value: 'iFLYOS技能' },
    ],
    skillServicePlat: [
      { key: 2, value: 'AIUI' },
      { key: 3, value: '业务定制' },
      { key: 5, value: '开放技能' },
      { key: 6, value: 'iFLYOS' },
    ],
    appStatus: [
      { key: 1, value: '审核中' },
      { key: 2, value: '已上线' },
      { key: 3, value: '审核未通过' },
      { key: 4, value: '未审核' },
      { key: 5, value: '-' },
    ],
    skillStatus: [
      { key: 1, value: '未发布' },
      { key: 2, value: '审核中' },
      { key: 3, value: '已发布' },
      { key: 4, value: '审核未通过' },
      { key: 5, value: '已下架' },
    ],
    // "entityType": 1, // 1 开放实体,2 静态实体,3 动态实体,4 定制实体, 5所见即可说, 6辅助词
    entityType: [
      { key: 2, value: '静态实体' },
      { key: 3, value: '动态实体' },
      { key: 5, value: '所见即可说' },
    ],
    answerEmotion: [
      { key: 'default', value: '默认情绪' },
      { key: 'sorrow', value: '悲伤' },
      { key: 'angry', value: '生气' },
      { key: 'happy', value: '高兴' },
      { key: 'neutral', value: '中立' },
    ],
    answerStyle: [
      { key: 1, value: '正式' },
      { key: 2, value: '可爱' },
      { key: 4, value: '儿童' },
    ],
    answerStyleRender: [
      { key: 1, value: '正式' },
      { key: 2, value: '可爱' },
      { key: 3, value: '正式，可爱' },
      { key: 4, value: '儿童' },
      { key: 5, value: '正式，儿童' },
      { key: 6, value: '可爱，儿童' },
      { key: 7, value: '正式，可爱，儿童' },
    ],
    personalEntityType: [
      { key: 1, value: '应用级' },
      { key: 2, value: '用户级' },
      { key: 3, value: '自定义' },
      { key: 'os_client_id', value: 'OS产品级' },
      { key: 'os_device_id', value: 'OS设备级' },
      { key: 'os_user_id', value: 'OS用户级' },
    ],
    filterLevel: [
      { key: 0, value: '未启用' },
      { key: 1, value: '已启用' },
    ],
    // 应用版本对比
    appVersionDiff: [
      { key: 0, value: '高' },
      { key: -1, value: '中' },
      { key: -2, value: '低' },
      // 大类
      { key: 'asr', value: '语音识别' },
      { key: 'assist', value: '兜底配置' },
      { key: 'nlp', value: '语义理解' },
      { key: 'itrans', value: '语音翻译' },
      { key: 'appHandle', value: '后处理' },
      { key: 'tts', value: '语音合成' },
      { key: 'avatar', value: '虚拟人交互' },

      // 二级类
      // 语音识别
      { key: 'language', value: '语种' },
      { key: 'domain', value: '领域' },
      { key: 'isFar', value: '距离' },
      { key: 'accent', value: '方言' },
      { key: 'advancedsetting', value: '高级设置' },
      { key: 'nunum', value: '优先阿拉伯数字' },
      { key: 'dwa', value: '流式识别' },
      { key: 'ptt', value: '添加标点' },
      { key: 'hotword', value: '识别热词' },
      { key: 'recognizeSensitiveWord', value: '识别敏感词' },
      { key: 'sad', value: '语义VAD' },
      // 语义理解
      { key: 'qc', value: 'QC技能' },
      { key: 'keyword', value: '关键词过滤' },
      { key: 'storeQa', value: '开放问答' },
      { key: 'privateSkill', value: '自定义技能' },
      { key: 'storeSkill', value: '商店技能' },
      { key: 'privateQa', value: '自定义问答' },
      { key: 'ubotQa', value: '关键词问答' },
      { key: 'deviceProperty', value: '设备人设' },
      { key: 'mustAnswer', value: '有问必答' },
      { key: 'semanticSensitiveWord', value: '语义敏感词' },
      { key: 'simpleProtocol', value: '精简协议' },

      // 兜底
      { key: 'test', value: '兜底配置' },
      { key: 'turing', value: '通用版' },
      { key: 'turingchild', value: '儿童版' },

      // 后处理
      { key: 'moreConfig', value: '备用信息' },
      // { key: 'url', value: '备用链接' },
      { key: 'reties', value: '主链接尝试次数' },
      { key: 'timeOut', value: '超时时间' },
      { key: 'url', value: '后处理链接' },
      { key: 'h_token', value: '校验token' },
      { key: 'isEncrypt', value: '消息是否加密' },
      { key: 'aeskey', value: '加密AES KEY' },

      // 语音合成
      { key: 'vcn', value: '发音人' },
      { key: 'volume', value: '音量' },
      { key: 'speed', value: '语速' },
      { key: 'ent', value: '试听文本' },

      // 翻译
      { key: 'source', value: '源语言' },
      { key: 'target', value: '目标语言' },
      { key: 'translateSensitiveWord', value: '识别敏感词' },

      // 虚拟人交互
      { key: 'version', value: '版本' },
      { key: 'anchorName', value: '虚拟人名称' },
    ],
    statisticUsedInfo: [
      { key: 'SDK', value: 'SDK' },
      { key: 'WebAPI', value: 'WebAPI' },
      { key: 'IFLYOS', value: 'iFLYOS 拦截器' },
      { key: 'source', value: '信源' },
      { key: 'model', value: 'AIUI大模型版' },
    ],
    orderStatusList: [
      { key: 0, value: '已取消' },
      { key: 1, value: '待支付' },
      { key: 2, value: '已支付' },
      { key: 3, value: '待发货' },
      { key: 4, value: '待确认' },
      { key: 5, value: '交易完成' },
      { key: 6, value: '交易完成' },
      { key: 7, value: '退款中' },
      { key: 8, value: '待发货' },
      { key: 9, value: '已发货' },
      { key: 10, value: '已关闭' },
      { key: 11, value: '交易完成' },
    ],
    taxType: [
      { key: 'ordinary', value: '增值普通发票' },
      { key: 'special', value: '增值专用发票' },
    ],
  },

  getDict: function (key) {
    var arr = this.dictArr[key]
    var dict = {}
    for (var i = 0; i < arr.length; i++) {
      dict[arr[i].key + ''] = arr[i].value
    }
    return dict
  },

  getDictArr: function (key) {
    return this.dictArr[key]
  },

  skillTypeReverse: {
    AIUI: 2,
    开放技能: 5,
    iFLYOS: 6,
  },

  aiuiAppPlatform: {
    1: 'Windows',
    2: 'iOS',
    3: 'Linux',
    4: 'Android',
    5: 'WebAPI',
    10: 'aiui',
    11: 'morfei',
    12: 'WeChat',
    13: '讯飞RTOS硬件模组',
    14: 'MorfeiCore',
    all: 'all',
  },
  lineOption: {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985',
        },
      },
    },
    legend: {
      data: [],
    },
    grid: { left: '2%', right: '4%', bottom: '3%', containLabel: true },
    xAxis: [
      {
        type: 'category',
        boundaryGap: false,
        data: [],
      },
    ],
    yAxis: [
      {
        type: 'value',
      },
    ],
    color: ['#1784e9'],
    series: [
      {
        name: '',
        type: 'line',
        // areaStyle: {},
        data: [],
      },
    ],
  },
  barOption: {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985',
        },
      },
    },
    dataset: {
      source: [],
    },
    grid: { left: '10px', right: '4%', bottom: '3%', containLabel: true },
    xAxis: { name: '' },
    yAxis: { type: 'category' },
    color: ['#3398DB'],
    series: [
      {
        type: 'bar',
        encode: {
          // Map the "amount" column to X axis.
          x: '',
          // Map the "product" column to Y axis
          y: '',
        },
      },
    ],
  },
}
