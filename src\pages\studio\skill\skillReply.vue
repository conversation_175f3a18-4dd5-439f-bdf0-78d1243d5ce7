<template>
  <div>
    <label-selector @select="onLabelSelect" title="技能回复"></label-selector>
    <skill-reply-text-adder
      :data="intention.reply || []"
      :slotNames="slotNames"
      @add="addIntentionReply"
      @del="delIntentionReply"
      @edit="editIntentionReply"
      @modifyStyle="modifyIntentionReplyStyle"
      @change="onInputChange"
      :disabled="!subAccountEditable"
      :reg="textReg"
      :warning="warning"
      editPlaceholder="使用“{”符号引用槽位，回车或点击右边提交按钮保存"
      placeholder="使用“{”符号引用槽位，回车或点击下方按钮添加文本回复，不超过10条，每次随机抽取一条"
      :max="10"
      ref="intentionRef"
    >
    </skill-reply-text-adder>
    <p class="welcom-words-tip">
      温馨提示：当后处理与技能回复语同时使用时，技能回复语不生效，优先走后处理效果。
    </p>
  </div>
</template>

<script>
import skillReplyTextAdder from './skillReplyTextAdder.vue'
import labelSelector from './labelSelector.vue'

import { mapGetters } from 'vuex'

export default {
  name: 'skill-reply',
  props: {
    intentionObj: {
      type: Object,
      default: {},
    },
    intentId: {
      type: String | Number,
      default: '',
    },
    subAccountEditable: Boolean,
    slotNames: Array,
  },
  data() {
    return {
      intention: { reply: [] },
      // textReg:
      //   /^[\u4e00-\u9fa5_a-zA-Z0-9\s\·\~\！\@\#\￥\%\……\&\*\（\）\——\-\+\=\【\】\{\}\、\|\；\‘\’\：\“\”\《\》\？\，\。\、\`\~\!\#\$\%\^\&\*\(\)\_\[\]{\}\\\|\;\'\'\:\"\"\,\.\/\<\>\?]{1,100}$/,
      // warning: '仅支持中英文、数字和标点符号，每条不超过100字'
      textReg: /^[\u4e00-\u9fffa-zA-Z0-9 {}_?？°]{1,100}$/,
      warning: '仅支持汉字/字母/数字/空格/{}/_/?/°，且每条不超过100字',
      loading: false,
    }
  },
  watch: {
    intentionObj(val) {
      if (val) {
        let newVal = { ...val, reply: [] }
        if (!val.reply) {
          this.intention = newVal
        } else {
          this.intention = {
            ...val,
            reply: (val.reply || []).map((item) => {
              return {
                ...item,
                changed: false,
              }
            }),
          }
        }
      }
    },
  },
  computed: {
    ...mapGetters({
      businessId: 'studioSkill/id',
      skill: 'studioSkill/skill',
    }),
  },
  methods: {
    onInputChange(index) {
      this.$set(
        this.intention,
        'reply',
        (this.intention.reply || []).map((item, i) => {
          if (index === i) {
            return {
              ...item,
              changed: true,
            }
          } else {
            return {
              ...item,
            }
          }
        })
      )
    },
    onLabelSelect(label) {
      this.$refs.intentionRef.$refs.intelInput.insertLabel(label)
    },
    modifyIntentionReplyStyle(data) {
      this.saveIntentionReply(data, '', 'modify')
    },
    editIntentionReply(text, index) {
      const otherReplys = (this.intention.reply || []).filter(
        (_, idx) => idx !== index
      )
      const find = otherReplys.findIndex((itm) => itm.answer === text.answer)
      if (find !== -1) {
        return this.$message.error('不得与其他条目重复')
      }

      let reply = (this.intention.reply || []).map((item, i) => {
        if (index === i) {
          return {
            ...item,
            ...text,
          }
        } else {
          return { ...item }
        }
      })
      this.saveIntentionReply(reply, '', 'modify', index)
    },
    // 保存意图确认文本
    addIntentionReply(text) {
      if (this.loading) {
        return
      }
      let reply = (this.intention.reply || []).slice()
      reply.push(text)
      this.saveIntentionReply(reply, text, 'add')
    },
    delIntentionReply(text) {
      let reply = Array.prototype.filter.call(
        this.intention.reply,
        function (item, index) {
          return item != text
        }
      )
      this.saveIntentionReply(reply, text, 'sub')
    },
    saveIntentionReply(list, text, mode, index) {
      let self = this
      let param = {
        businessId: this.businessId,
        intentId: this.intentId,
        reply: JSON.stringify(
          list.map((item) => {
            return {
              answer: item.answer,
              type: item.type,
              labels: (item.labels || []).map(({ picture, ...rest }) => {
                return { ...rest }
              }),
            }
          })
        ),
      }
      console.log(param)
      if (mode === 'sub' && list.length === 0) {
        param.reply = []
      }
      this.loading = true
      this.$utils.httpPost(this.$config.api.STUDIO_INTENT_SAVE_REPLY, param, {
        success: (res) => {
          this.loading = false
          let textInfo = mode === 'sub' ? '删除成功' : '保存成功'
          self.$message.success(textInfo)
          // self.$set(self.intention, 'reply', list)
          if (mode === 'add') {
            self.$refs.intentionRef.resetInitialStatus()
            self.intention.reply.push(text)
          } else if (mode === 'sub') {
            const delIndex = self.intention.reply.findIndex((im) => im === text)
            self.intention.reply.splice(delIndex, 1)
          } else if (mode === 'modify') {
            self.intention.reply = (list || []).map((it, i) => {
              if (index === i) {
                return {
                  ...it,
                  changed: false,
                }
              } else {
                return { ...it }
              }
            })
          }
        },
        error: (err) => {
          this.loading = false
          this.$message.error('请求失败，请稍后重试')
        },
      })
    },
  },
  components: { skillReplyTextAdder, labelSelector },
}
</script>

<style lang="scss" scoped>
.welcom-words-tip {
  padding-left: 16px;
  height: 40px;
  color: $grey5;
  background: $grey4-15;
  border-radius: 2px;
  line-height: 40px;
  margin-top: 10px;
}
</style>
