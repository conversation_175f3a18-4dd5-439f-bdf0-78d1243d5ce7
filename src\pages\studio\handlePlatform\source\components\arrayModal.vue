<template>
  <el-dialog
    title="编辑数组参数"
    :visible.sync="visible"
    width="60%"
    @close="handleClose"
    :close-on-click-modal="false"
  >
    <el-form :model="form" label-position="left">
      <el-row :gutter="20" style="margin-bottom: 10px">
        <el-col :span="8">参数名称</el-col>
        <el-col :span="6">参数类型</el-col>
        <el-col :span="5">默认值</el-col>
        <el-col :span="5" class="operation-header">
          <div class="operation-title">
            <span>操作</span>
            <i class="el-icon-circle-plus operation-add" @click="addItem"></i>
          </div>
        </el-col>
      </el-row>
      <array-item-component
        v-for="(item, index) in formState.children"
        :key="item.id + '-' + updateTrigger"
        :item="item"
        :level="0"
        :parent-type="'root'"
        @remove="removeItem(index)"
        @add-array-item="addArrayItem"
        @remove-array-item="removeArrayItem"
      />
    </el-form>

    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取 消</el-button>
      <el-button type="primary" @click="handleSubmit">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { v4 as uuidv4 } from 'uuid'
import ArrayItemComponent from './ArrayItemComponent.vue'

export default {
  components: {
    ArrayItemComponent,
  },
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    arrayData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      visible: this.value,
      form: {
        children: [],
      },
      updateTrigger: 0,
      arrayItemTypes: {},
    }
  },
  computed: {
    formState() {
      return {
        children: this.form.children,
        updateTrigger: this.updateTrigger,
      }
    },
  },
  watch: {
    value(val) {
      this.visible = val
    },
    visible(val) {
      this.$emit('input', val)
    },
    arrayData: {
      immediate: true,
      handler(val) {
        if (val && val.children) {
          this.form = JSON.parse(JSON.stringify(val))
          this.initCollapseState(this.form.children)
          this.saveArrayItemTypes(this.form.children)
        }
      },
    },
  },
  methods: {
    saveArrayItemTypes(items) {
      if (!items) return

      items.forEach((item) => {
        if (
          item.type === 'array' &&
          item.children &&
          item.children.length > 0
        ) {
          const firstChild = item.children[0]
          if (firstChild) {
            this.arrayItemTypes[item.id] = firstChild.type
          }

          this.saveArrayItemTypes(item.children)
        } else if (item.type === 'object' && item.children) {
          this.saveArrayItemTypes(item.children)
        }
      })
    },

    initCollapseState(items) {
      if (!items) return

      items.forEach((item) => {
        if (item.type === 'object' || item.type === 'array') {
          if (item.collapsed === undefined) {
            this.$set(item, 'collapsed', false)
          }
          if (item.children && item.children.length) {
            this.initCollapseState(item.children)
          }
        }
      })
    },

    toggleCollapse(item) {
      if (item.type === 'object' || item.type === 'array') {
        this.$set(item, 'collapsed', !item.collapsed)
        this.updateTrigger += 1
        console.log('Toggle collapse:', item.name, item.collapsed)
      }
    },

    addItem() {
      if (this.arrayData.children && this.arrayData.children.length > 0) {
        const template = this.arrayData.children[0]
        const newItem = this.clearDefaultValue(template)
        if (template.type === 'object' && template.children) {
          newItem.children = template.children.map((child) => {
            const newChild = {
              ...child,
              id: uuidv4(),
              defaultValue: '',
            }

            // 如果子项是数组类型，保存其子项的类型信息
            if (
              child.type === 'array' &&
              child.children &&
              child.children.length > 0
            ) {
              this.arrayItemTypes[newChild.id] = child.children[0].type
            }

            return newChild
          })
        }
        this.form.children.push(newItem)
      } else {
        this.form.children.push({
          id: uuidv4(),
          name: '[Array Item]',
          type: 'string',
          defaultValue: '',
        })
      }
    },

    removeItem(index) {
      this.form.children.splice(index, 1)
    },

    addArrayItem(arrayItem) {
      if (!arrayItem.children) {
        this.$set(arrayItem, 'children', [])
      }

      let itemType = 'string'

      if (arrayItem.children && arrayItem.children.length > 0) {
        itemType = arrayItem.children[0].type
      } else if (this.arrayItemTypes[arrayItem.id]) {
        itemType = this.arrayItemTypes[arrayItem.id]
      } else if (this.arrayData && this.arrayData.children) {
        // 尝试在原始数据中查找相同名称的数组
        const originalArray = this.findArrayItemByName(
          this.arrayData.children,
          arrayItem.name
        )
        if (
          originalArray &&
          originalArray.children &&
          originalArray.children.length > 0
        ) {
          itemType = originalArray.children[0].type
        }
      }

      const template = arrayItem.children[0] || {
        id: uuidv4(),
        name: '[Array Item]',
        type: itemType,
        defaultValue: '',
        fatherType: 'array',
        arraySon: true,
      }

      const newItem = this.clearDefaultValue(template)
      arrayItem.children.push(newItem)

      this.arrayItemTypes[arrayItem.id] = itemType
    },

    findArrayItemById(items, id) {
      if (!items) return null

      for (const item of items) {
        if (item.id === id) {
          return item
        }

        if (item.children) {
          const found = this.findArrayItemById(item.children, id)
          if (found) return found
        }
      }

      return null
    },

    // 新增方法：通过名称查找数组项
    findArrayItemByName(items, name) {
      if (!items) return null

      for (const item of items) {
        if (item.name === name) {
          return item
        }

        if (item.children) {
          const found = this.findArrayItemByName(item.children, name)
          if (found) return found
        }
      }

      return null
    },

    removeArrayItem({ parent, child }) {
      if (!parent || !parent.children) return

      const index = parent.children.findIndex((item) => item.id === child.id)
      if (index !== -1) {
        parent.children.splice(index, 1)
        this.updateTrigger += 1
      }
    },

    clearDefaultValue(item) {
      const cloned = JSON.parse(JSON.stringify(item))
      cloned.id = uuidv4()

      if (cloned.type === 'array') {
        if (cloned.children && cloned.children.length) {
          // 保存数组子项的类型信息
          const childType = cloned.children[0].type

          cloned.children = cloned.children.map((child) =>
            this.clearDefaultValue(child)
          )

          // 为新创建的数组项保存类型信息
          this.arrayItemTypes[cloned.id] = childType
        }
        this.$set(cloned, 'collapsed', false)
      } else if (cloned.type === 'object') {
        if (cloned.children && cloned.children.length) {
          cloned.children = cloned.children.map((child) => {
            const newChild = {
              ...child,
              id: uuidv4(),
              defaultValue: '',
              children:
                child.type === 'object' && child.children
                  ? child.children.map((grandChild) => ({
                      ...grandChild,
                      id: uuidv4(),
                      defaultValue: '',
                    }))
                  : undefined,
            }

            // 如果子项是数组类型，保存其子项的类型信息
            if (
              child.type === 'array' &&
              child.children &&
              child.children.length > 0
            ) {
              this.arrayItemTypes[newChild.id] = child.children[0].type
            }

            return newChild
          })
        }
        this.$set(cloned, 'collapsed', false)
      } else {
        cloned.defaultValue = ''
      }
      return cloned
    },

    handleSubmit() {
      this.$emit('submit', this.form)
      this.visible = false
    },

    handleClose() {
      this.$emit('close')
    },
  },
}
</script>

<style lang="scss" scoped>
.nested-item {
  margin-top: 10px;
}

.name-col {
  .name-wrapper {
    display: flex;
    align-items: center;

    &.level-0 {
      padding-left: 0;
    }

    &.level-1 {
      padding-left: 20px;
    }

    &.level-2 {
      padding-left: 40px;
    }

    &.level-3 {
      padding-left: 60px;
    }

    .collapse-icon {
      margin-right: 8px;
      cursor: pointer;
    }

    .property-name {
      font-weight: 500;
      margin-right: 4px;
    }
  }
}

.el-icon-circle-plus,
.el-icon-remove {
  cursor: pointer;
  font-size: 16px;
  color: #409eff;

  &:hover {
    opacity: 0.8;
  }
}

.el-icon-remove {
  color: #f56c6c;
}

.operation-header {
  .operation-title {
    display: flex;
    align-items: center;
  }
}

.operation-add {
  margin-left: 8px;
  cursor: pointer;
  color: #409eff;
  font-size: 18px;
  &:hover {
    opacity: 0.8;
  }
}
</style>
