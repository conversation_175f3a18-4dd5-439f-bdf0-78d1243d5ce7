<template>
  <el-dialog
    title="无回复兜底"
    :visible.sync="dialog.show"
    width="568px"
    :append-to-body="true"
  >
    <os-text-adder
      class="mgb24"
      :data="list"
      @add="add"
      @del="del"
      :readonly="true"
      :max="30"
      :placeholder="`最多添加30条，每段不超过${lastGuardLen}字，回车新增`"
    />
    <span slot="footer" class="dialog-footer"></span>
  </el-dialog>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  props: {
    dialog: {
      type: Object,
      default: {},
    },
    configList: {
      type: Array,
      default: [],
    },
    saving: Boolean,
    currentScene: Object,
  },
  data() {
    return {
      change: false,
      list: [],
    }
  },
  computed: {
    ...mapGetters({
      limitCount: 'aiuiApp/limitCount',
    }),
    appId() {
      return this.$store.state.aiuiApp.id
    },
    // 无回复兜底每条字符数
    lastGuardLen() {
      if (
        this.limitCount &&
        this.limitCount.last_guard_len &&
        !Number.isNaN(Number(this.limitCount.last_guard_len))
      ) {
        // 是数字
        let num = Number(this.limitCount.last_guard_len)
        if (num < 100) {
          return 100
        } else if (num > 500) {
          return 500
        } else {
          return num
        }
      } else {
        return 100
      }
    },
  },
  watch: {
    configList(val) {
      if (val && val.length) {
        this.list = val
      }
    },
    // saving() {
    //   if (this.saving && this.change) {
    //     this.save()
    //   }
    // },
  },
  methods: {
    emitChange() {
      // this.change = true
      // this.$emit('change')
      this.save()
    },
    add(text) {
      if (this.list.length == 30) {
        this.$message.error('仅能添加30条')
        return
      }
      if (text.trim().length > this.lastGuardLen) {
        this.$message.error(`不能超过${this.lastGuardLen}个字符`)
        return
      }
      this.list.push(text)
      this.emitChange()
    },
    del(text) {
      this.list = Array.prototype.filter.call(
        this.list,
        function (item, index) {
          return item != text
        }
      )
      this.emitChange()
    },
    save() {
      let tmp = []
      this.list.forEach((item) => {
        tmp.push({ config: item })
      })
      this.$utils.httpPost(
        this.$config.api.AIUI_SAVE_LAST_GUARD,
        {
          appid: this.appId,
          sceneId: this.currentScene.sceneBoxId,
          ensureConfig: JSON.stringify(tmp),
        },
        {
          success: (res) => {
            // this.dialog.show = false
            // this.$emit('saveSuccess')
          },
          error: (err) => {
            this.$emit('saveFail')
          },
        }
      )
    },
  },
}
</script>

<style lang="scss" scoped></style>
