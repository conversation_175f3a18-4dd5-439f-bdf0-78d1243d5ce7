<template>
  <os-page :options="pageOptions" @returnCb="returnCb">
    <div class="s-content">
      <a-form
        :form="form"
        :label-col="{ span: 5 }"
        :wrapper-col="{ span: 12 }"
        @submit="handleSubmit"
      >
        <a-form-item label="标题">
          <a-input
            placeholder="请输入标题"
            v-decorator="[
              'preset_title',
              {
                rules: [
                  {
                    required: true,
                    message: '请输入标题',
                  },
                ],
              },
            ]"
          />
        </a-form-item>
        <a-form-item label="问题类型">
          <a-radio-group
            v-model="typeId"
            v-decorator="[
              'typeId',
              { rules: [{ required: true, message: '请选择问题类型' }] },
            ]"
          >
            <a-radio-button
              v-for="(type, index) in workerOrderTypes"
              :key="index"
              :value="type.id"
            >
              {{ type.name }}
            </a-radio-button>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="详细分类">
          <a-select
            ref="tid"
            placeholder="请选择"
            @change="changeValue"
            v-decorator="[
              'tid',
              { rules: [{ required: true, message: '请选择详细分类' }] },
            ]"
            :getPopupContainer="
              (triggerNode) => {
                return triggerNode.parentNode || document.body
              }
            "
          >
            <a-select-option
              v-for="(child, index_i) in workerOrderTypeChild"
              :key="index_i"
              :value="child.id"
            >
              {{ child.name }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="电子邮箱">
          <a-input
            placeholder="请填写正确的邮箱"
            v-decorator="[
              'preset_mail',
              {
                rules: [{ validator: checkEmail }],
              },
            ]"
          />
        </a-form-item>
        <a-form-item label="联系人">
          <a-input
            placeholder="请提供我们联系人姓名"
            v-decorator="['preset_contact_name', { rules: [] }]"
          />
        </a-form-item>
        <a-form-item label="手机号码" class="input-inline">
          <a-input
            class="input-inline-inner"
            placeholder="请留下正确的手机号以备接收反馈"
            v-decorator="[
              'preset_telephone',
              {
                rules: [
                  { validator: checkTel },
                  { required: true, message: '请留下正确的手机号以备接收反馈' },
                ],
                initialValue: userInfo.originalMobile,
              },
            ]"
          />
        </a-form-item>
        <a-form-item label="问题描述">
          <a-textarea
            class="fb_content_main"
            style="width: 100%; height: 100%"
            placeholder="请具体描述您遇到的问题，我们将全力为您解答！限定200字。"
            :rows="4"
            :maxLength="200"
            v-decorator="[
              'preset_detail',
              { rules: [{ required: true, message: '请输入需求内容' }] },
            ]"
          />
          <div class="div-upload">
            <a-upload
              :action="`${
                this.$utils.BaseURI + this.$config.api.WORKER_ORDER_UPLOAD_POST
              }`"
              :file-list="defaultFileList"
              :beforeUpload="beforeUpload"
              :remove="handleRemove"
              @change="handleChange"
              :headers="{ 'X-Csrf-Token': token }"
            >
              <a-button> <a-icon type="upload" />文件上传</a-button>
              <div class="el-upload__tip">
                上传附件支持jpg、png、txt、zip、rar、doc、xls格式，最大不超过5M
              </div>
            </a-upload>
          </div>
        </a-form-item>
        <a-form-item :wrapper-col="{ span: 12, offset: 10 }">
          <a-button type="primary" html-type="submit"> 提交 </a-button>
        </a-form-item>
      </a-form>
    </div>
  </os-page>
</template>

<script>
import { mapGetters } from 'vuex'
import Icon from 'ant-design-vue/lib/icon'
import Form from 'ant-design-vue/lib/form'
import Input from 'ant-design-vue/lib/input'
import Button from 'ant-design-vue/lib/button'
import Select from 'ant-design-vue/lib/select'
import Radio from 'ant-design-vue/lib/radio'
import Upload from 'ant-design-vue/lib/upload'

import 'ant-design-vue/lib/icon/style/css'
import 'ant-design-vue/lib/form/style/css'
import 'ant-design-vue/lib/input/style/css'
import 'ant-design-vue/lib/button/style/css'
import 'ant-design-vue/lib/select/style/css'
import 'ant-design-vue/lib/radio/style/css'
import 'ant-design-vue/lib/upload/style/css'

export default {
  name: 'submitAsk',
  data() {
    return {
      pageOptions: {
        title: '提交问题',
        loading: false,
        returnBtn: true,
        screen: true,
      },
      typeId: '',
      workerOrderTypes: [],
      workerOrderTypeChild: {},
      form: this.$form.createForm(this, { name: 'submitAsk' }),
      attachment_names: [],
      attachment_sizes: [],
      attachment_urls: [],
      defaultFileList: [
        /*{
              uid: '1',
              name: 'xxx.png',
              status: 'done',
              response: 'Server Error 500', // custom error message to show
              url: 'http://www.baidu.com/xxx.png',
            }*/
      ],
      token: localStorage.getItem('AIUI_GLOBAL_VARIABLE'),
    }
  },
  computed: {
    ...mapGetters({
      userInfo: 'user/userInfo',
    }),
  },
  watch: {
    typeId: function () {
      let tmp = this.workerOrderTypes.find((type) => {
        return type.id === this.typeId
      })
      this.workerOrderTypeChild = tmp.child
      this.form.resetFields(['tid'])
      /*this.form.setFieldsValue({
              'tid': []
            })*/
    },
  },
  created() {
    this.getWorkerOrderTypes()
  },
  methods: {
    changeValue(e) {
      this.$forceUpdate()
    },
    handleChange({ file, fileList }) {
      let thiz = this
      if (file.status === 'done') {
        if (file.response.flag) {
          thiz.attachment_names.push(file.name)
          thiz.attachment_sizes.push(file.size)
          thiz.attachment_urls.push(file.response.data.url)
        }
      }
    },
    handleRemove(file) {
      const index = this.defaultFileList.indexOf(file)
      const newFileList = this.defaultFileList.slice()
      newFileList.splice(index, 1)
      this.defaultFileList = newFileList

      const attachment_names_ = this.attachment_names.slice()
      attachment_names_.splice(index, 1)
      this.attachment_names = attachment_names_

      const attachment_sizes_ = this.attachment_sizes.slice()
      attachment_sizes_.splice(index, 1)
      this.attachment_sizes = attachment_sizes_

      const attachment_urls_ = this.attachment_urls.slice()
      attachment_urls_.splice(index, 1)
      this.attachment_urls = attachment_urls_
    },
    beforeUpload(file) {
      const isImage =
        file.type === 'image/jpeg' ||
        file.type === 'image/png' ||
        file.type === 'text/plain' ||
        file.type ===
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
        file.type ===
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||
        file.type === 'application/vnd.ms-excel' ||
        file.type === 'application/msword' ||
        //|| file.type === 'application/x-rar-compressed,application/zip,application/x-zip-compressed'
        file.type === 'application/zip' ||
        (file.type === '' &&
          file.name.substr(file.name.lastIndexOf('.')).toLowerCase() ===
            '.zip') ||
        (file.type === '' &&
          file.name.substr(file.name.lastIndexOf('.')).toLowerCase() === '.rar')
      if (!isImage) {
        this.$message.error('文件格式错误')
        return
      }

      const sizeLt = file.size < 1024 * 1024 * 5
      // / 1024 / 1024 < 2;
      if (!sizeLt) {
        this.$message.error('文件大小不能超过5MB')
        return
      }
      if (!(sizeLt && isImage)) {
        const index = this.defaultFileList.indexOf(file)
        const newFileList = this.defaultFileList.slice()
        if (index > -1) {
          newFileList.splice(index, 1)
        }
        this.defaultFileList = newFileList
      } else {
        file = {
          ...file,
          name: file.name,
          size: file.size,
          type: file.type,
        }
        this.defaultFileList = [...this.defaultFileList, file]
      }

      return sizeLt && isImage
    },
    handleSubmit(e) {
      e.preventDefault()
      this.form.validateFields((err, values) => {
        if (!err) {
          delete values.typeId
          for (let k in values) {
            if (!values[k]) {
              values[k] = ''
            }
          }
          this.$utils.httpPost(
            this.$config.api.WORKER_ORDER_SUBMIT_POST,
            {
              ...values,
              attachment_names: this.attachment_names.join(','),
              attachment_sizes: this.attachment_sizes.join(','),
              attachment_urls: this.attachment_urls.join(','),
            },
            {
              success: (res) => {
                if (res.code == 0 && res.flag) {
                  this.$message.success('提交成功')
                  this.form.resetFields()
                  this.defaultFileList = []
                  this.attachment_names = []
                  this.attachment_sizes = []
                  this.attachment_urls = []
                  this.$router.push({ name: 'ask' })
                } else {
                  this.$message.success
                }
              },
              error: (err) => {},
            }
          )
        } else {
          var a = document.getElementsByClassName('s-content')
          a.length &&
            a[0].scrollIntoView({
              behavior: 'smooth',
              block: 'center',
              inline: 'center',
            })
        }
      })
    },
    checkEmail(rule, value, callbackFn) {
      if (!value) {
        callbackFn()
        return
      }
      const reg = /^([a-zA-Z]|[0-9])(\w|\-)+@[a-zA-Z0-9]+\.([a-zA-Z]{2,4})$/
      if (!reg.test(value)) {
        callbackFn('请检查邮箱格式')
        return
      }
      callbackFn()
    },
    checkTel(rule, value, callbackFn) {
      if (!value) {
        callbackFn()
        return
      }
      const reg = /^[1][3,4,5,6,7,8,9][0-9]{9}$/
      if (!reg.test(value)) {
        callbackFn('请检查手机号码格式')
        return
      }
      callbackFn()
    },
    getWorkerOrderTypes() {
      let thiz = this
      this.$utils.httpGet(
        this.$config.api.WORKER_ORDER_TYPES_GET,
        {},
        {
          success: (res) => {
            if (res.flag) {
              thiz.workerOrderTypes = res.data
            }
          },
          error: (err) => {},
        }
      )
    },
    returnCb() {
      this.$router.push({ name: 'ask' })
    },
  },
  components: {
    [Form.name]: Form,
    [Form.Item.name]: Form.Item,
    [Input.name]: Input,
    [Button.name]: Button,
    [Input.TextArea.name]: Input.TextArea,
    [Upload.name]: Upload,
    [Icon.name]: Icon,
    [Select.name]: Select,
    [Select.Option.name]: Select.Option,
    [Radio.Group.name]: Radio.Group,
    [Radio.Button.name]: Radio.Button,
  },
}
</script>

<style scoped lang="scss">
.div-upload {
  border: 1px solid $grey3;
  border-bottom-right-radius: 5px;
  border-bottom-left-radius: 5px;
  background-color: $grey1;
  padding: 2%;
}
:deep(.upload-demo) {
  display: inline-flex;
  position: relative;
  top: 10%;

  .btn-file {
    line-height: 50%;
    max-width: 80%;
    min-width: 80%;
    position: relative;
    top: 20%;
    left: 10%;
  }
  .el-upload__tip {
    position: relative;
    left: 2%;
  }
}
:deep(.input-inline) {
  position: relative;
  width: 100% !important;
  :deep(&-inner) {
    //width: 70%;
    flex: 1;
  }
}
</style>
