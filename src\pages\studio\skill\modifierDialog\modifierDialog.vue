<template>
  <el-dialog
    class="reply-utter-dialog"
    :visible.sync="dialog.show"
    :close-on-click-modal="false"
    width="960px"
    ref="dialog"
    @close="$emit('change')"
  >
    <div slot="title" class="dialog-title">
      自定义修饰语
      <el-tooltip class="item" effect="dark" placement="right-start">
        <div slot="content">
          例如语料“我要听{artist} [的] {datetime}[的]歌曲”，可以新增<br />
          修饰语custom，并将“{artist} [的] {datetime}[的]”设置为修饰<br />
          语内容；设置修饰语后语料可简化为：“我要听{{ tooltipWord }} 的歌”
        </div>
        <i class="el-icon-question" />
      </el-tooltip>
    </div>
    <el-scrollbar style="padding-right: 12px; height: 500px">
      <os-collapse class="qa-collapse" :default="true" title="基本信息">
        <basic-info
          :businessId="businessId"
          :dialog="dialog"
          @setNewModifierId="setNewModifierId"
        ></basic-info>
      </os-collapse>
      <os-divider />
      <os-collapse :default="true" title="内容">
        <utter
          :businessId="businessId"
          :dialog="dialog"
          @reloadSlots="$refs.slotsTable.getAuxiliarySlots()"
          @reloadModifier="reloadModifier"
        ></utter>
      </os-collapse>
      <os-divider />
      <os-collapse :default="true" title="实体和辅助词">
        <modifier-slots
          ref="slotsTable"
          :businessId="businessId"
          :dialog="dialog"
        ></modifier-slots>
      </os-collapse>
      <modifier
        ref="modifierTable"
        :businessId="businessId"
        :dialog="dialog"
      ></modifier>
    </el-scrollbar>
  </el-dialog>
</template>

<script>
import BasicInfo from '../modifierDialog/modifierBasicInfo'
import Utter from '../modifierDialog/modifierUtter.vue'
import ModifierSlots from '../modifierDialog/modifierSlots.vue'
import Modifier from './modifierInModifierDialog'
export default {
  name: 'modifier-dialog',
  props: {
    dialog: {
      type: Object,
      default: () => ({
        show: false,
        modifierId: '',
      }),
    },
  },
  data() {
    return {
      tooltipWord: '<custom>',
    }
  },
  computed: {
    businessId() {
      return this.$store.state.studioSkill.id
    },
  },
  methods: {
    utterHasSlots(data) {
      let self = this
      let tmp = data.split(/[{}]/) || []
      const result = tmp.filter((item) => self.slotNames.includes(item))
      if (result.length) {
        return true
      }
      return false
    },
    setNewModifierId(val) {
      this.$set(this.dialog, 'modifierId', val)
    },
    reloadModifier() {
      this.$refs.modifierTable && this.$refs.modifierTable.getData()
    },
  },
  components: {
    BasicInfo,
    Utter,
    ModifierSlots,
    Modifier,
  },
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="scss" scoped>
.dialog-title {
  font-size: 20px;
}
:deep(.el-dialog__body) {
  padding-bottom: 32px;
}
:deep(.el-icon-question) {
  font-size: 14px;
  color: $grey3;
}
:deep(.os-collapse-title) {
  margin: 28px 0 24px;
  font-size: 16px;
}
</style>
