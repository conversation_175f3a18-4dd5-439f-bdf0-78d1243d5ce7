import Vue from 'vue'
import { utils } from '@U'

Vue.directive('clickoutside', {
  bind(el, binding) {
    function documentHandler(e) {
      // 这里判断点击的元素是否是本身，是本身，则返回
      // console.log('-------clickoutside------', el, e.target)
      if (el.contains(e.target)) {
        return false
      }
      // 判断指令中是否绑定了函数
      if (binding.expression) {
        binding.value(e)
      }
    }
    // 给当前元素绑定个私有变量，方便在unbind中可以解除事件监听
    el.__vueClickOutside__ = documentHandler
    document.addEventListener('click', documentHandler)
  },
  update() {},
  unbind(el, binding) {
    // 解除事件监听
    document.removeEventListener('click', el.__vueClickOutside__)
    delete el.__vueClickOutside__
  },
})

Vue.directive('loadmore', {
  bind(el, binding) {
    let self = this
    el.addEventListener('scroll', function () {
      let sign = 100
      const scrollDistance =
        this.scrollHeight - this.scrollTop - this.clientHeight
      if (scrollDistance <= sign) {
        binding.value()
      }
    })
  },
})

Vue.directive('scrollcb', {
  bind(el, binding) {
    function documentHandler(e) {
      // 判断指令中是否绑定了函数
      if (binding.expression) {
        binding.value(e, this.scrollTop - el.baseTop)
      }
    }
    // 给当前元素绑定个私有变量，方便在unbind中可以解除事件监听
    el.baseTop = document.getElementById('scrollDom').scrollTop
    el.__vueScrollCb__ = documentHandler
    document
      .getElementById('scrollDom')
      .addEventListener('scroll', documentHandler)
  },
  update() {},
  unbind(el, binding) {
    // 解除事件监听
    document.removeEventListener('scroll', el.__vueScrollCb__)
    delete el.__vueScrollCb__
  },
})

Vue.directive('inputformat', {
  bind(el, binding) {
    let self = this
    el.children[0].addEventListener('input', function () {
      console.log(el.children[0].value)
      console.log(el.children[0].value.trim())
      return el.children[0].value.trim()
    })
  },
  // update (el, binding) {
  //   // 判断指令中是否绑定了函数
  //   if (binding.expression) {
  //     binding.value = binding.value.trim()
  //     el.children[0].value = el.children[0].value.trim()
  //     console.log(binding);
  //   }
  // }
})
