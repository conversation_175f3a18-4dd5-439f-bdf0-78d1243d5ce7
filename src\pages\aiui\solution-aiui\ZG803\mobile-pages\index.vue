<template>
  <div class="main-content">
    <MyHeader> </MyHeader>
    <section class="main-content-banner">
      <div class="banner-text">
        <h2>ZG803 AIUI离线语音识别方案</h2>

        <p class="banner-text-content">
          集成多语种离线语音算法的高性能，低成本，高集成度的智能语音识别方案，适用于国内外小家电场景
        </p>

        <div class="hor-btn">
          <el-button
            class="banner-text-button"
            @click="toConsole"
            type="primary"
            round
            >合作咨询</el-button
          >
          <!-- <el-button class="banner-text-buy" @click="toBuy" round plain
            >立即购买</el-button
          > -->
        </div>
      </div>
    </section>

    <section class="section-nav">
      <ul class="nav-ul" id="nav-ul">
        <li
          v-for="item in nav_list"
          :key="item.id"
          @click="gotoSelection(item.id)"
        >
          {{ item.name }}
        </li>
      </ul>

      <i
        v-if="nav_list.length > 4"
        :class="[
          'nav-btn',
          nav_flag ? 'el-icon-caret-bottom' : 'el-icon-caret-top',
        ]"
        id="nav-btn"
        @click="handleNavExpand"
      ></i>
    </section>

    <section class="section section1">
      <h2>应用场景</h2>
      <div class="section-item">
        <ul>
          <li v-for="(item, index) in app_scenario" :key="index" class="app">
            <img :src="item.src" :alt="item.alt" />
            <p class="app-text">{{ item.alt }}</p>
          </li>
        </ul>
      </div>
    </section>

    <section class="section section2">
      <h2>产品功能</h2>
      <ul>
        <li>
          <p class="product-title">多种发音人</p>
          <p class="product-desc">· 支持标准男声、女声等不同音色</p>
        </li>
        <li>
          <p class="product-title">语音唤醒</p>
          <p class="product-desc">· 95%以上唤醒率</p>
          <p class="product-desc">· 支持自定义唤醒词定制</p>
        </li>
        <li>
          <p class="product-title">离线识别</p>
          <p class="product-desc">· 支持80条本地指令离线识别</p>
          <p class="product-desc">· 支持自定义离线命令词</p>
        </li>
        <li>
          <p class="product-title">客制化</p>
          <p class="product-desc">· 可提供唤醒词定制工具，快速便捷实现定制</p>
        </li>
        <li>
          <p class="product-title">串口通信</p>
          <p class="product-desc">· 支持串口下发指令</p>
        </li>
      </ul>
    </section>

    <section class="section section3">
      <h2>产品视频</h2>
      <el-tabs v-model="product_videos" @tab-click="tabChange">
        <el-tab-pane label="产品宣传视频" name="propagate_videos">
          <video
            :src="videoSrcPublicize"
            width="100%"
            controls
            v-if="isPlaying1"
            class="video-player"
            autoplay
          ></video>
          <div
            v-else
            class="video-cover"
            :style="{ backgroundImage: 'url(' + videoCover + ')' }"
            @click="
              isPlaying1 = true
              isPlaying2 = false
            "
          >
            <div class="play-icon"></div>
          </div>
        </el-tab-pane>

        <el-tab-pane label="产品使用视频" name="use_videos">
          <video
            :src="videoSrcUse"
            width="100%"
            controls
            autoplay
            v-if="isPlaying2"
          ></video>
          <div
            v-else
            class="video-cover"
            :style="{ backgroundImage: 'url(' + videoCover1 + ')' }"
            @click="
              isPlaying2 = true
              isPlaying1 = false
            "
          >
            <div class="play-icon"></div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </section>

    <section class="section section4">
      <h2>使用说明</h2>
      <!-- <ul>
        <li>
          <p class="title">免唤醒交互</p>
          <p class="text">
            本地离线方式支持免唤醒交互，即在设备未唤醒情况，直接说出离线识别命令词就可以与模组经行交互。
          </p>
        </li>
        <li>
          <p class="title">唤醒词交互</p>
          <p class="text">
            本地离线方式支持免唤醒交互，即在设备未唤醒情况，直接说出离线识别命令词就可以与模组经行交互。
          </p>
        </li>
      </ul> -->

      <div class="content">
        <div class="title">免唤醒交互</div>
        <div class="main">
          本地离线交互方式支持免唤醒交互，即在设备未唤醒情况，直接说出离线识别命令词就可以与模组进行交互。
        </div>
        <!-- <img
          :src="
            require('../../../../../assets/images/solution/soft-hardware/803/sm1.png')
          "
        /> -->
      </div>
      <div class="content">
        <div class="title">唤醒词交互</div>
        <div class="main">
          支持唤醒词+离线命令词的方式交互，即先说主唤醒词将设备唤醒，然后可以进行语音交互（默认唤醒词小飞小飞）。
        </div>
        <!-- <img
          :src="
            require('../../../../../assets/images/solution/soft-hardware/803/sm2.png')
          "
        /> -->
      </div>
    </section>

    <section class="section section5">
      <h2>硬件参数</h2>
      <ul>
        <li v-for="(item, index) in hard_list" :key="index">
          <img :src="item.src" class="left" />

          <div class="right">
            <div class="title">{{ item.name }}</div>
            <span>{{ item.content }}</span>
          </div>
        </li>
      </ul>
    </section>

    <section class="section section6">
      <h2>产品清单</h2>
      <ul>
        <li>
          <p class="title">硬件</p>
          <p class="text">· 整机开发套件</p>
          <p class="text">· USB连接线</p>
        </li>
        <li>
          <p class="title">软件</p>
          <p class="text">· 灯具离线唤醒资源</p>
          <p class="text">· 自定义唤醒词工具</p>
        </li>
      </ul>
    </section>

    <section class="section section7">
      <h2>开发材料</h2>
      <ul>
        <li @click="toDoc">·《ZG803AIUI离线语音方案产品白皮书》</li>
      </ul>
    </section>

    <section class="section section-cooperation">
      <div class="cooperation-btn" @click="toConsole">合作咨询</div>
    </section>

    <section class="section section-footer">
      <!-- <aiuiMobileFooter> </aiuiMobileFooter> -->
      <div class="footer-title">
        联系我们
        <span class="arrow" @click="clickFooterArrow">▼</span>
      </div>
      <div class="content">
        <p>联系电话：19955108393</p>
        <p>开发者交流群：617903641</p>
        <p>商务技术支持：<EMAIL></p>
      </div>

      <div class="footer-title">
        友情链接
        <span class="arrow" @click="clickFooterArrow">▼</span>
      </div>
      <div class="content">
        <p>科大讯飞</p>
        <p>讯飞开放平台</p>
      </div>

      <div class="corporation-info">
        ©科大讯飞股份有限公司 皖ICP备05001217号
      </div>
    </section>
  </div>
</template>

<script>
import MyHeader from '@P/aiui/solution-aiui/mobile-header.vue'
import UseVideo from '../../../../../assets/images/solution/soft-hardware/803/use.mp4'
import PublicizeVideo from '../../../../../assets/images/solution/soft-hardware/803/publicize.mp4'
import VideoPlayer from '../../../../../components/videoPlayer'
export default {
  name: 'ZG803mobile',

  data() {
    return {
      nav_flag: true,

      isPlaying1: false,
      isPlaying2: false,
      product_videos: 'propagate_videos',
      videoCover: require('../../../../../assets/images/solution/soft-hardware/803/1.png'),
      videoCover1: require('../../../../../assets/images/solution/soft-hardware/803/2.png'),
      videoSrcUse: UseVideo,
      videoSrcPublicize: PublicizeVideo,

      nav_list: [
        { name: '应用场景', id: 1 },
        { name: '产品功能', id: 2 },
        { name: '产品视频', id: 3 },
        { name: '使用说明', id: 4 },
        { name: '硬件参数', id: 5 },
        { name: '产品清单', id: 6 },
        { name: '开发材料', id: 7 },
      ],

      app_scenario: [
        {
          alt: '台灯',
          src: require('../../../../../assets/images/solution/smart-hardware/mobile/td.png'),
        },
        {
          alt: '床头灯',
          src: require('../../../../../assets/images/solution/smart-hardware/mobile/ctd.png'),
        },
        {
          alt: '小夜灯',
          src: require('../../../../../assets/images/solution/smart-hardware/mobile/xyd.png'),
        },
        {
          alt: '镜前灯',
          src: require('../../../../../assets/images/solution/smart-hardware/mobile/jqd.png'),
        },
        {
          alt: '挂脖风扇',
          src: require('../../../../../assets/images/solution/smart-hardware/mobile/gbfs.png'),
        },
        {
          alt: '常温壶',
          src: require('../../../../../assets/images/solution/smart-hardware/mobile/cwh.png'),
        },
        {
          alt: '按摩椅',
          src: require('../../../../../assets/images/solution/smart-hardware/mobile/amy.png'),
        },
        {
          alt: '儿童玩具',
          src: require('../../../../../assets/images/solution/smart-hardware/mobile/etwj.png'),
        },
      ],

      hard_list: [
        {
          name: 'CPU',
          src: require('../../../../../assets/images/solution/soft-hardware/803/cpu.png'),
          content: '32-bit DSP 主频 160MHz',
        },
        {
          name: 'SDRAM',
          src: require('../../../../../assets/images/solution/soft-hardware/803/sdram.png'),
          content: '128KB',
        },
        {
          name: 'Flash',
          src: require('../../../../../assets/images/solution/soft-hardware/803/flash.png'),
          content: '512KB',
        },
      ],
    }
  },

  mounted() {},
  components: {
    VideoPlayer,
    MyHeader,
  },

  methods: {
    handleNavExpand() {
      const nav_ul = document.getElementById('nav-ul')
      const navIcon = document.getElementById('nav-btn')

      navIcon.addEventListener('click', () => {
        if (this.nav_flag) {
          nav_ul.classList.add('expanded')
        } else {
          nav_ul.classList.remove('expanded')
        }
        this.nav_flag = !this.nav_flag
      })
    },

    tabChange(tab, event) {
      this.isPlaying1 = false
      this.isPlaying2 = false
    },

    toBuy() {
      window.open('https://www.aifuwus.com/onstage/cmddetail?product_type=4436')
    },

    toDoc() {
      window.open('https://aiui-doc.xf-yun.com/project-1/doc-172/')
    },

    toConsole() {
      const search = window.location.search
      if (search) {
        window.open(`/solution/apply/22${search}`)
      } else {
        window.open(`/solution/apply/22`)
      }
    },

    gotoSelection(id) {
      const section = document.getElementsByClassName(`section` + id)[0]
      if (section) {
        section.scrollIntoView({ behavior: 'smooth' })
      }
    },

    clickFooterArrow() {
      const titles = document.querySelectorAll('.footer-title')
      titles.forEach((title) => {
        title.addEventListener('click', () => {
          const content = title.nextElementSibling
          const arrow = title.querySelector('.arrow')

          if (content.style.display === 'none' || !content.style.display) {
            content.style.display = 'block'
            arrow.classList.add('up')
          } else {
            content.style.display = 'none'
            arrow.classList.remove('up')
          }
        })
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.main-content {
  background-color: #f6f7f9;
  max-width: 750px;
  overflow: hidden;
  &-banner {
    background: url(~@A/images/solution/smart-hardware/mobile/banner2.jpg)
      center no-repeat;
    background-size: cover;
    height: 375px;
    overflow: hidden;
    width: 100%;
  }
  .banner-text {
    margin-top: 64px;
    padding-left: 44px;
    h2 {
      font-size: 48px;
      font-weight: 600;
      color: #000000;
      line-height: 68px;
    }
    p {
      width: 648px;
      font-size: 24px;
      font-weight: 300;
      color: #000000;
      line-height: 40px;
      text-align: left;
    }
    .hor-btn {
      display: flex;
      flex-direction: row;
      justify-content: flex-start;
      margin-top: 40px;
      div:nth-child(2) {
        margin-left: 30px;
      }
    }
  }

  .section {
    h2 {
      font-size: 48px;
      font-weight: 600;
      color: #000000;
      line-height: 68px;
      text-align: center;
      margin: 0 auto;
      margin-top: 50px;
      margin-bottom: 30px;
    }
    p,
    ul {
      margin-bottom: 0;
    }
    max-width: 750px;
    overflow: hidden;
    margin: 0 auto;
    .section-title {
      text-align: center;
      font-size: 36px;
      font-weight: 500;
      color: #444444;
      line-height: 54px;
      .arrow {
        width: 26px;
        height: 24px;
        display: inline-block;
        background-repeat: no-repeat;
        background-size: contain;
      }
      .arrow-left {
        background-position: left;
        background-image: url(~@A/images/solution/smart-hardware/arrow-left.png);
      }
      .arrow-right {
        background-position: right;
        background-image: url(~@A/images/solution/smart-hardware/arrow-right.png);
      }
    }
    .section-tabs {
      margin-top: 60px;
    }
  }

  .section-nav {
    background-color: #ffffff;
    position: relative;
    width: 100%;
    border: 1px solid #ddd;
    padding: 0 30px;
    .nav-ul {
      display: flex;
      justify-content: space-evenly;
      flex-wrap: wrap;
      overflow: hidden; /* 默认隐藏超出部分 */
      max-height: 68px; /* 折叠状态时，只显示一行 */
      transition: max-height 0.3s ease; /* 动画过渡效果 */
    }
    .nav-ul.expanded {
      max-height: 200px; /* 展开状态：允许显示多行 */
    }
    .nav-ul li {
      flex: 0 0 auto;
      margin-right: 30px;
      white-space: nowrap;
      cursor: pointer;
      font-size: 24px;
      font-weight: 400;
      text-align: center;
      line-height: 68px;
      color: #7a7a7a;
    }
    .nav-btn {
      position: absolute;
      height: 68px;
      width: 60px;
      right: 0px;
      top: 60%;
      transform: translateY(-50%);
      font-size: 48px;
      // background-color: red;
    }
  }

  .section1 {
    padding: 0 40px;
    .section-item {
      margin-top: 50px !important;

      > ul {
        display: flex;
        justify-content: flex-start;
        flex-wrap: wrap;
        gap: 30px;

        li {
          // flex: 0 0 calc(33.33%);
          width: 192px;
          height: 240px;
          position: relative;
          background: url(~@A/images/solution/smart-hardware/mobile/appborder.jpg)
            center no-repeat;
          background-size: cover;
          border: 1px solid #979797;
          border-radius: 16px;
          margin-bottom: 60px;

          img {
            width: 100%;
            height: 100%;
            position: absolute;
            right: 0;
            top: 0;
          }

          image:nth-child(3) {
            position: absolute;
            top: 20px;
            right: -80px;
          }

          p {
            height: 38px;
            width: 100%;
            text-align: center;
            margin: 0 auto;
            font-size: 24px;
            line-height: 38px;
            position: absolute;
            left: 50%;
            bottom: -70px;
            transform: translate(-50%, 0%);
            margin-bottom: 20px;
          }
        }
      }
    }
  }

  .section2 {
    padding-left: 36px;
    padding-right: 28px;
    ul {
      margin-top: 20px;
    }
    li {
      width: 686px;
      background: linear-gradient(180deg, #ffffff, #ffffff);
      border-radius: 31px;
      padding: 26px 56px;
      margin-bottom: 20px;

      .product-title {
        text-align: left;
        height: 42px;
        font-size: 32px;
        margin-bottom: 20px;
      }
      .product-desc {
        font-size: 26px;
        font-weight: 400;
        text-align: left;
        color: #999999;
        line-height: 40px;
      }
    }
  }
  .section3 {
    padding: 0 26px;
    .video-player {
      border: 2px solid;
      border-image: linear-gradient(to right, #b2cbf6, #4db9f8) 4;
    }
    .video-cover {
      width: 100%;
      height: 450px;
      background-size: cover;
      background-repeat: no-repeat;
      position: relative;
      cursor: pointer;
      .play-icon {
        position: absolute;
        width: 100px;
        height: 100px;
        z-index: 1;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: url(~@A/images/solution/multimodal-interaction/<EMAIL>)
          center/contain no-repeat;
      }
    }

    ::v-deep(.el-tabs__nav-scroll) {
      padding: 0 130px;
      display: flex;
      justify-content: space-between;
    }
  }

  :deep(.el-tabs__item) {
    font-size: 32px;
    color: #000000;
    text-align: center;
    margin: 0 auto;
  }

  .section4 {
    width: 100%;
    margin: 0 auto;
    padding: 0 26px;
    .content {
      width: 100%;
      height: 300px;
      background: #ffffff;
      border: 1px solid #f1f1f1;
      box-shadow: -4px 0px 14px 4px rgba(188, 198, 216, 0.3);
      margin-bottom: 20px;
      padding: 48px 36px;
      position: relative;

      .title {
        font-size: 24px;
        font-family: PingFang SC, PingFang SC-Semibold;
        font-weight: 600;
        color: #262626;
        line-height: 30px;
      }
      .main {
        width: 100%;
        font-size: 18px;
        margin-top: 24px;
        font-family: PingFang SC, PingFang SC-Regular;
        text-align: justifyLeft;
        color: #666666;
        line-height: 42px;
      }
      // img {
      //   position: absolute;
      //   right: 0;
      //   bottom: 0;
      // }
    }
  }

  .section5 {
    padding: 0 10px;
    width: 100%;
    ul {
      display: flex;
      justify-content: space-evenly;
      width: 100%;
      li {
        display: flex;
        width: 33%;
        // margin-right: 10px;
        justify-content: space-evenly;

        .left {
          width: 100px;
          margin: 0 auto;
        }
        .right {
          margin-left: 10px;
          margin: 0 auto;
          width: 50%;
          .title {
            margin-top: 20px;
            font-size: 18px;
            font-weight: 600;
            text-align: left;
            color: #262626;
            line-height: 30px;
          }
        }
      }
    }
  }

  .section6 {
    padding-left: 36px;
    padding-right: 28px;
    ul {
      margin-top: 20px;
    }
    li {
      width: 686px;
      background: linear-gradient(180deg, #ffffff, #ffffff);
      border-radius: 31px;
      padding: 26px 56px;
      margin-bottom: 20px;

      .title {
        text-align: left;
        height: 42px;
        font-size: 32px;
        margin-bottom: 20px;
      }
      .text {
        font-size: 26px;
        font-weight: 400;
        text-align: left;
        color: #999999;
        line-height: 40px;
      }
    }
  }

  .section7 {
    padding: 0 26px;
    margin-top: 20px;
    ul {
      li {
        width: 695px;
        min-height: 94px;
        background: linear-gradient(180deg, #ffffff, #ffffff);
        border-radius: 30px;
        font-size: 26px;
        font-weight: 400;
        text-align: left;
        color: #2470ff;
        line-height: 40px;
        padding: 27px 47px;
        margin-bottom: 20px;
        padding-right: 20px;
        text-align: center;
        margin: 0 auto;
      }
    }
  }

  .section-cooperation {
    width: 100%;
    height: 243px;
    .cooperation-btn {
      width: 222px;
      height: 81px;
      background: linear-gradient(90deg, #26bcf6, #1b7bf7 98%);
      border-radius: 52px;
      text-align: center;
      margin: 0 auto;
      font-weight: 600;
      line-height: 81px;
      font-size: 32px;
      color: #f0f0f0;
      margin-top: 60px;
    }
  }

  .section-footer {
    width: 750px;
    background-color: #061930;
    padding: 30px 20px;
    .footer-title {
      color: #c1c1c1;
      display: flex;
      justify-content: space-between;
      align-items: center;
      cursor: pointer;
      font-size: 28px;
      font-weight: bold;
      margin-bottom: 20px;
    }
    .content {
      margin-top: 10px;
      display: none; /* 默认隐藏内容 */
      font-size: 24px;
      color: #7b7e93;
    }

    .content p {
      margin: 5px 0;
      font-size: 24px;
      line-height: 49px;
    }

    .arrow {
      font-size: 26px;
      transition: transform 0.5s ease;
    }

    .arrow.up {
      transform: rotate(180deg);
    }

    .corporation-info {
      margin-top: 30px;
      color: #7b7e93;
      font-size: 24px;
      text-align: left;
      line-height: 33px;
    }
  }
}
</style>
