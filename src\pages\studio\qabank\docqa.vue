<template>
  <div class="docqa-info os-scroll">
    <div class="form-area">
      <el-form
        ref="form"
        :model="ruleForm"
        label-width="118px"
        label-position="left"
        :rules="rules"
      >
        <!-- <el-form-item label="知识库类型" prop="repoId">
          <el-select
            v-model="ruleForm.repoId"
            placeholder="请勾选知识库类型"
            :style="{ width: '430px' }"
            :disabled="true"
          >
            <el-option
              v-for="item in optionList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
          <img :src="IconAdd" class="kl-add" @click="handleKnowAdd" />
        </el-form-item> -->
        <span class="item-title">基本信息</span>

        <el-form-item label="文档库名称" prop="name">
          <!-- 40个字符限制 -->
          <el-input
            v-model="ruleForm.name"
            :style="{ width: '430px' }"
            :disabled="detailDisabled"
          ></el-input>
        </el-form-item>

        <el-form-item label="描述" prop="description">
          <el-input
            v-model="ruleForm.description"
            type="textarea"
            :rows="3"
            :disabled="detailDisabled"
            :style="{ width: '430px' }"
            :maxlength="200"
            placeholder="不超过200字符"
          ></el-input>
        </el-form-item>
        <el-form-item label="标签">
          <custom-tag v-model="ruleForm.labels"></custom-tag>
        </el-form-item>

        <span class="item-title">知识构建配置</span>
        <el-form-item label="文档知识类型">
          <el-radio-group v-model="ruleForm.x">
            <el-radio :label="3">图文知识</el-radio>
            <el-radio :label="6">表格知识</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="构建策略">
          <el-radio-group v-model="ruleForm.y">
            <el-radio :label="3">说明书策略</el-radio>
            <el-radio :label="6">Q-A对策略</el-radio>
            <el-radio :label="6">长文本策略</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item>
          <div class="btn-group">
            <el-button
              type="primary"
              @click="submitForm"
              :disabled="saveDisabled"
              :loading="saveLoading"
              v-show="!detailDisabled"
              size="small"
              >保存文档</el-button
            >
            <el-button @click="cancelCreate" size="small">取消</el-button>
          </div>
        </el-form-item>
      </el-form>
    </div>
    <el-dialog
      title="新增自定义库"
      :visible.sync="dialogVisible"
      :width="'530px'"
      class="create-dialog"
    >
      <el-form
        ref="addForm"
        :model="addForm"
        label-width="70px"
        :rules="rules"
        :hide-required-asterisk="true"
      >
        <el-form-item label="库名称：" prop="addName">
          <el-input
            v-model="addForm.addName"
            :style="{ width: '400px' }"
            placeholder="输入自定义库名称，中文/英文/数字/下划线，不超过40个字符"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          size="medium"
          style="min-width: 90px"
          @click="submitKnowAdd"
          :loading="addLoading"
          >保存</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import customTag from '@C/customTag.vue'
import IconAdd from '@A/images/plugin/add.png'
import axios from 'axios'
import { mapGetters } from 'vuex'
export default {
  name: 'qaBank-docqa-create',
  data() {
    return {
      IconAdd,

      dialogVisible: false,
      repoId: null,
      id: null,
      addLoading: false,
      buildLoading: false,
      saveLoading: false,
      saveDisabled: false,
      detailDisabled: false,
      fileSize: 0,
      ruleForm: {
        repoId: '',
        name: '',
        description: '',
        labels: [],
        x: 3,
        y: 3,
      },
      fileList: [],
      beforeList: [],
      optionList: [],
      modelUrl: [],
      addForm: {
        addName: '',
      },
      rules: {
        repoId: [
          { required: true, message: '请选择知识库类型', trigger: 'change' },
        ],
        name: [
          { required: true, message: '请输入文档名称', trigger: 'blur' },
          { max: 40, message: '文档名称不得超过40个字符', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              const regExp = /^[a-zA-Z0-9_\u4e00-\u9fff]+$/ // 正则表达式匹配中英文、数字，下划线

              if (!regExp.test(value)) {
                callback(new Error('文档名称只能包含中文/英文/数字/下划线'))
              } else {
                callback()
              }
            },
            trigger: 'change',
          },
        ],
        addName: [
          {
            required: true,
            message: '请输入新增知识库名称',
            trigger: 'blur',
          },
          {
            max: 40,
            message: '内容长度不超过40个字符',
            trigger: 'change',
          },
          {
            validator: (rule, value, callback) => {
              const regExp = /^[a-zA-Z0-9_\u4e00-\u9fff]+$/ // 正则表达式匹配中英文、数字，下划线
              if (!regExp.test(value)) {
                callback(new Error('知识库名称只能包含中英文/数字/下划线'))
              } else {
                callback()
              }
            },
            trigger: 'change',
          },
        ],
        description: [
          { max: 200, message: '文档描述不得超过200个字符', trigger: 'change' },
        ],
      },
      originName: '',

      timer: null,

      canToast: false,
    }
  },
  methods: {
    handleKnowAdd() {
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs.addForm.resetFields()
      })
    },
    getOptionList() {
      let self = this
      this.$utils.httpGet(
        this.$config.api.KNOWLEDGE_TYPE_LIST,
        {},
        {
          success: (res) => {
            self.optionList = res.data || []
          },
          error: (err) => {},
        }
      )
    },
    getModel() {
      this.$utils.httpGet(
        this.$config.api.KNOWLEDGE_STORE_MODEL,
        {},
        {
          success: (res) => {
            if (res.code === '0') {
              this.modelUrl = res.data
            }
          },
          error: (err) => {},
        }
      )
    },
    downloadFile(file) {},

    submitKnowAdd() {
      let self = this
      this.$refs['addForm'].validate((valid) => {
        if (valid) {
          self.addLoading = true
          let api = this.$config.api.KNOWLEDGE_TYPE_CREATE
          let data = {
            name: self.addForm.addName,
          }
          this.$utils.httpPost(api, data, {
            success: (res) => {
              if (res) {
                self.addLoading = false
                self.$message.success('知识库创建成功')
                this.dialogVisible = false
                // self.getOptionList()
              }
            },
            error: (err) => {
              self.addLoading = false
            },
          })
        } else {
          return false
        }
      })
    },

    cancelCreate() {
      // this.$router.go(-1)
      this.$router.push({ name: 'studio-handle-platform-qadocs' })
    },
    submitForm() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          if (this.originName !== this.ruleForm.name) {
            this.updateRepoInfo()
          } else {
          }
        }
      })
    },

    updateRepoInfo() {
      let self = this
      let data = {
        repoId: this.$route.params.repoId,
        name: this.ruleForm.name,
        type: '5',
      }

      const api = this.$config.api.STUDIO_QA_CREATE_EDIT
      this.$utils.httpPost(api, data, {
        success: (res) => {
          self.$store.dispatch('studioQa/setQa', this.$route.params.repoId)
        },
        error: (err) => {
          console.log('page=>>')
          console.log(err)
        },
      })
    },

    getDocDisplayStatus(data, docItem) {
      /**
       * data.extractStatus==3  解析失败
          data.extractStatus==2  解析中  
        需要去查看docList 的 文件 status  (1:解析完成， 2：解析中，3：解析失败)

        data.extractStatus==1  解析成功 所有文件统一查看并展示 依据：data.buildStatus  (1:已构建， 2：构建中，3：构建失败)
       */
      if (data.extractStatus === 1) {
        if (data.buildStatus === 0) {
          return {
            displayStatus: '100',
            displayStatusName: '已解析未发布',
          }
        } else if (data.buildStatus === 1) {
          return {
            displayStatus: '101',
            displayStatusName: '发布成功',
          }
        } else if (data.buildStatus === 2) {
          return {
            displayStatus: '102',
            displayStatusName: '发布中',
          }
        } else if (data.buildStatus === 3) {
          return {
            displayStatus: '103',
            displayStatusName: '发布失败',
          }
        } else {
          return {
            displayStatus: '',
            displayStatusName: '',
          }
        }
      } else if (data.extractStatus === 2 || data.extractStatus === 3) {
        if (docItem.status === 1) {
          return {
            displayStatus: '201',
            displayStatusName: '已解析未发布',
          }
        } else if (docItem.status === 2) {
          return {
            displayStatus: '202',
            displayStatusName: '解析中',
          }
        } else if (docItem.status === 3) {
          return {
            displayStatus: '203',
            displayStatusName: '解析失败',
          }
        } else {
          return {
            displayStatus: '',
            displayStatusName: '',
          }
        }
      } else {
        return {
          displayStatus: '',
          displayStatusName: '',
        }
      }
    },

    getRepoInfo() {
      let self = this
      this.$utils.httpGet(
        this.$config.api.STUDIO_QA_INFO,
        {
          repoId: this.$route.params.repoId,
        },
        {
          success: (res) => {
            self.originName = res.data.name
            self.$set(self.ruleForm, 'name', res.data.name)
          },
          error: (err) => {},
        }
      )
    },

    getRepoDocInfo() {
      let params = this.$route.params
      let data = {
        repoId: params.repoId,
      }
      if (!params.repoId) {
        return
      }
      const self = this
      this.$utils.httpGet(this.$config.api.QA_GETREPODOCINFO, data, {
        success: (res) => {
          if (res.data) {
            // 去这里的字段，但是name不从这个接口取
            let keys = Object.keys(res.data).filter((it) => it !== 'name')
            keys.forEach((k) => {
              self.$set(self.ruleForm, k, res.data[k])
            })

            self.repoId = res.data.repoId || ''
            self.id = res.data.id || ''
            if (res.data.id) {
              self.$store.dispatch('pluginKnowledge/setKnowIdSync', res.data.id)
            }
            self.beforeList = (res.data.docList || []).map((it) => {
              return {
                ...it,
                ...self.getDocDisplayStatus(res.data, it),
              }
            })

            self.$store.dispatch(
              'pluginKnowledge/setRepoIds',
              res.data.repoId || ''
            )
            // 发布成功状态
            if (res.data.extractStatus === 1) {
              if (res.data.buildStatus === 1) {
                if (this.canToast) {
                  this.$message.success(
                    '您的知识文档构建发布成功，请在右侧进行知识体验。'
                  )
                  this.canToast = false
                }
              }
            }

            if (
              // 解析中
              // 解析成功，构建中
              res.data.extractStatus === 2 ||
              (res.data.extractStatus === 1 && res.data.buildStatus === 2)
            ) {
              self.timer = setTimeout(() => {
                self.getRepoDocInfo()
              }, 2000)
            } else if (
              // 待构建状态(待发布)
              res.data.extractStatus === 1 &&
              res.data.buildStatus === 0
            ) {
              self.canToast = true
              self.timer = setTimeout(() => {
                self.getRepoDocInfo()
              }, 3000)
            }
          }
        },
        error: (err) => {},
      })
    },
  },
  created() {
    const repoId = this.$route.params.repoId
    const id = this.$route.params.id
    this.repoId = repoId
    this.id = id

    this.detailDisabled = this.$route.params.disabled
    console.log('路由中params', this.$route.params)

    // this.getOptionList()
    this.getRepoInfo()
    this.getModel()
    this.getRepoDocInfo()
  },

  destroyed() {
    if (this.timer) {
      clearTimeout(this.timer)
    }
  },

  components: { customTag },
}
</script>

<style lang="scss" scoped>
.docqa-info {
  padding: 20px;
  :deep(.el-radio-group) {
    display: inline-flex;
  }
}
.item-title {
  position: relative;
  font-size: 16px;
  font-weight: 500;
  padding-left: 10px;
  margin-right: 18px;

  &:before {
    width: 2px;
    height: 16px;
    background-color: $primary;
    content: ' ';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    margin: auto;
  }
}

.form-area {
  padding: 20px 24px 70px 0px;
  white-space: nowrap;

  .kl-add {
    width: 24px;
    height: 24px;
    margin-left: 23px;
    cursor: pointer;
  }

  .custom-upload {
    :deep(.el-upload) {
      width: 430px;
    }

    :deep(.el-upload-dragger) {
      width: 430px;
      height: 170px;
    }

    :deep(.el-upload-list) {
      width: 400px;
    }

    .el-icon-upload {
      margin: 26px 0 0px;
    }

    .upload-title {
      font-size: 14px;
      font-family: PingFangSC, PingFangSC-Regular;
      font-weight: 400;
      color: #303133;
      line-height: 22px;
      margin-bottom: 10px;
    }

    .el-upload__text {
      font-size: 12px;
      font-family: PingFangSC, PingFangSC-Regular;
      font-weight: 400;
      color: #9ea5bd;
    }

    .el-upload__tip {
      width: 430px;

      > a {
        float: right;
      }
    }
  }

  .btn-group {
    width: 770px;
  }

  .file-whole {
    display: flex;
    align-items: center;
  }
  .file-status-success {
    color: #18da00;
  }
  .file-status-error {
    color: #ff3838;
  }
  .file-status-processing {
    color: #ffa400;
  }

  .file-history {
    width: 330px;
    height: 41px;
    background: #fafcfe;
    border: 1px solid #dcdfe6;
    border-radius: 2px;
    padding: 0 12px;
    margin: 10px 10px 10px 0;

    // display: inline-block;
    &:hover {
      background: #ebeef5;
    }

    .file-name {
      width: 280px;
      display: inline-block;
      line-height: 41px;
    }

    .file-del {
      font-size: 15px;
      position: relative;
      bottom: 15px;
      cursor: pointer;
    }
  }
}
</style>
