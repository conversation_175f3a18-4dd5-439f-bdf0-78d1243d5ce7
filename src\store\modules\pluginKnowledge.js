import { utils } from '@U'
import { api } from '@/config'
import Router from '../../router'

export default {
  namespaced: true,
  state: {
    rightTestOpen: false,
    repoIds: '',
    floatOpen: 'min', //min,close,open 三种状态
    name: '-',
    knowledgeId: '',
  },
  actions: {
    setRightTestOpen({ commit }, data) {
      commit('setRightTestOpen', data)
    },
    setRepoIds({ commit }, data) {
      commit('setRepoIds', data)
    },
    setFloatOpen({ commit }, data) {
      commit('setFloatOpen', data)
    },
    setKnowName({ commit }, data) {
      commit('setKnowName', data)
    },
    setKnowIdSync({ commit }, knowId) {
      commit('setKnowId', knowId)
    },
    setKnowId({ commit }, repoId) {
      utils.httpGet(
        api.QA_GETREPODOCINFO,
        {
          repoId,
        },
        {
          success: (res) => {
            // 注意： 参数是hash值服务端对应的repoId， 返回值是平台的repoId，是个整形
            if (res.data.repoId) {
              commit('setRepoIds', res.data.repoId)
            }
            // 注意：！！下面的if需要放在后面，后面会监测id
            if (res.data.id) {
              commit('setKnowId', res.data.id)
            }
          },
          error: (err) => {
            Router.push({ name: 'studio-handle-platform-qabanks' })
          },
        }
      )
    },
  },
  mutations: {
    setRightTestOpen(state, data) {
      state.rightTestOpen = data
    },
    setRepoIds(state, data) {
      state.repoIds = data
    },
    setFloatOpen(state, data) {
      state.floatOpen = data
    },
    setKnowName(state, data) {
      state.name = data
      sessionStorage.setItem('name', data)
    },
    setKnowId(state, data) {
      state.knowledgeId = data
    },
  },
  getters: {
    rightTestOpen(state) {
      return state.rightTestOpen
    },
    repoIds(state) {
      return state.repoIds
    },
    floatOpen(state) {
      return state.floatOpen
    },
    name(state) {
      return state.name
    },
    knowledgeId(state) {
      return state.knowledgeId
    },
  },
}
