<template>
  <div class="debug-wrap">
    <div class="debug-header">
      <div
        class="notice-title"
        v-if="debugType === 'store'"
        style="height: 36px; line-height: 36px"
      >
        <i class="circle"></i>
        <span class="title">技能体验<span class="plus">+</span></span>
      </div>
      <div class="collapse-btn" @click="closeRightTest" v-else>
        <i class="iconfont icon-zhankai"></i>
        <span>{{ debugType == 'app' ? '模拟测试' : '技能体验' }}</span>
      </div>
    </div>
    <div class="clear">
      <!-- <i
        class="el-icon-setting"
        v-if="showThresholdEditWrap"
        @click="dialog.show = true"
      ></i>
      <i class="iconfont icon-shanchu" @click="cleanHistory"></i> -->
      <div @click="dialog.show = true" v-if="showThresholdEditWrap">
        <svg-icon iconClass="setting" />
      </div>
      <div
        class="divider"
        style="margin: 0 11px"
        v-if="showThresholdEditWrap"
      ></div>
      <div @click="cleanHistory"><svg-icon iconClass="delete" /></div>
    </div>

    <div class="debug-dialog" ref="list">
      <div
        :class="['dialog dialog-' + item.type]"
        v-for="(item, index) in dialogList"
        :key="index"
      >
        <template v-if="item.type === 'answer'">
          <div class="msg-answer-item">
            <i class="robot-avatar"></i>
            <div class="ib message">
              <!-- 非iflyos技能 -->
              <template
                v-if="
                  item.data.res &&
                  item.data.res.semantic &&
                  Array.isArray(item.data.res.semantic)
                "
              >
                <p class="msg-item">{{ item.data[item.type] }}</p>
                <div class="intent-info-wrap">
                  <div
                    v-if="
                      item.data.res.semantic[0] &&
                      item.data.res.semantic[0].intent
                    "
                  >
                    <span class="intent-info-title">意图</span>
                    <span>{{ item.data.res.semantic[0].intent }}</span>
                  </div>
                  <template
                    v-if="
                      item.data.res.semantic[0] &&
                      item.data.res.semantic[0].slots &&
                      item.data.res.semantic[0].slots.length
                    "
                  >
                    <span class="intent-info-title">实体槽位</span>
                    <!-- <el-tag
                    v-for="(tag, index) in item.data.res.semantic[0].slots"
                    :key="index"
                    :disable-transitions="false"
                  >
                    <span
                      class="slot-tag"
                      :title="tag.name"
                      @click="
                        setSlotValue(
                          item.data.res.semantic[0].slots[index],
                          $event
                        )
                      "
                      >{{ tag.name }}</span
                    >
                  </el-tag> -->
                    <span class="tag-wrap">
                      <el-popover
                        placement="bottom-start"
                        trigger="click"
                        v-for="(tag, index) in item.data.res.semantic[0].slots"
                        :key="index"
                      >
                        <p class="entity-item">
                          <span class="tag">槽位标识</span>{{ tag.name }}
                        </p>
                        <p
                          v-if="
                            entityInIntent &&
                            Object.keys(entityInIntent).length &&
                            entityInIntent[tag.name]
                          "
                          class="entity-item"
                        >
                          <span class="tag">对应实体</span>@{{
                            entityInIntent[tag.name]
                          }}
                        </p>
                        <p class="entity-item">
                          <span class="tag">对应短语</span>{{ tag.value }}
                        </p>
                        <el-tag slot="reference" :disable-transitions="false">
                          <span class="slot-tag" :title="tag.name">{{
                            tag.name
                          }}</span>
                        </el-tag>
                      </el-popover></span
                    >
                  </template>
                </div>
                <div class="view-button-group">
                  <a
                    class="view-button"
                    v-if="!!item.data.res || !!item.data.req"
                    @click="openJsonDialog(item.data)"
                    ><i class="iconfont icon-chakan"></i
                    ><span style="white-space: nowrap">&nbsp;查看JSON</span></a
                  >
                  <a
                    class="msg-item"
                    v-if="
                      item.data[item.type] &&
                      item.data[item.type].indexOf('error:') !== -1
                    "
                    :href="`${$config.docs}doc-61/`"
                    target="_blank"
                    >查看文档</a
                  >
                </div>
              </template>
              <!-- iflyos技能 -->
              <template
                v-else-if="
                  item.data.res &&
                  item.data.res.response &&
                  item.data.res.response.directives &&
                  item.data.res.response.directives[0] &&
                  item.data.res.response.directives[0].updatedIntent
                "
              >
                <p class="msg-item">{{ item.data[item.type] }}</p>
                <div class="intent-info-wrap">
                  <div
                    v-if="
                      item.data.res.response.directives[0].updatedIntent.name
                    "
                  >
                    <span class="intent-info-title">意图</span>
                    <span>{{
                      item.data.res.response.directives[0].updatedIntent.name
                    }}</span>
                  </div>
                  <template
                    v-if="
                      item.data.res.response.directives[0].updatedIntent
                        .slots &&
                      Object.keys(
                        item.data.res.response.directives[0].updatedIntent.slots
                      ).length
                    "
                  >
                    <span class="intent-info-title">槽位标识</span>
                    <!-- <el-tag
                    v-for="(tag, index) in item.data.res.response.directives[0]
                      .updatedIntent.slots"
                    :key="index"
                    :disable-transitions="false"
                  >
                    <span
                      class="slot-tag"
                      :title="tag.name"
                      @click="
                        setIflyosSlotValue(
                          item.data.res.response.directives[0].updatedIntent
                            .slots[index],
                          $event
                        )
                      "
                      >{{ tag.name }}</span
                    >
                  </el-tag> -->
                    <span class="tag-wrap"
                      ><el-popover
                        placement="bottom-start"
                        trigger="click"
                        v-for="(tag, index) in item.data.res.response
                          .directives[0].updatedIntent.slots"
                        :key="index"
                      >
                        <p class="entity-item">
                          <span class="tag">槽位标识</span>{{ tag.name }}
                        </p>
                        <p
                          v-if="
                            entityInIntent &&
                            Object.keys(entityInIntent).length &&
                            entityInIntent[tag.name]
                          "
                          class="entity-item"
                        >
                          <span class="tag">对应实体</span>@{{
                            entityInIntent[tag.name]
                          }}
                        </p>
                        <p class="entity-item">
                          <span class="tag">对应短语</span>{{ tag.value }}
                        </p>
                        <el-tag slot="reference" :disable-transitions="false">
                          <span class="slot-tag" :title="tag.name">{{
                            tag.name
                          }}</span>
                        </el-tag>
                      </el-popover></span
                    >
                  </template>
                </div>
                <div class="view-button-group">
                  <a
                    class="view-button"
                    v-if="!!item.data.res || !!item.data.req"
                    @click="openJsonDialog(item.data)"
                    ><i class="iconfont icon-chakan"></i
                    ><span style="white-space: nowrap">&nbsp;查看JSON</span></a
                  >
                  <a
                    class="msg-item"
                    v-if="
                      item.data[item.type] &&
                      item.data[item.type].indexOf('error:') !== -1
                    "
                    :href="`${$config.docs}doc-61/`"
                    target="_blank"
                    >查看文档</a
                  >
                </div>
              </template>
              <div v-else style="padding: 0 16px">
                <span>{{ item.data[item.type] }}</span>
                <template
                  v-if="
                    !item.data.hasOwnProperty('hasscript') ||
                    (item.data.hasOwnProperty('hasscript') &&
                      item.data.hasscript)
                  "
                >
                  <div class="view-button-group">
                    <a
                      class="view-button"
                      v-if="item.data.httpCode == 204"
                      @click="openJsonDialog(item.data, index)"
                      ><i class="iconfont icon-chakan"></i
                      ><span style="white-space: nowrap"
                        >&nbsp;查看JSON</span
                      ></a
                    >
                    <a
                      v-else-if="
                        item.data.httpCode == 206 || item.data.httpCode == 400
                      "
                      :href="`${$config.docs}doc-61/`"
                      target="_blank"
                      >查看文档</a
                    >
                    <a
                      class="view-button"
                      v-else-if="!!item.data.res || !!item.data.req"
                      @click="openJsonDialog(item.data)"
                      ><i class="iconfont icon-chakan"></i
                      ><span style="white-space: nowrap"
                        >&nbsp;查看JSON</span
                      ></a
                    >
                  </div>
                </template>
                <template
                  v-if="
                    item.data.hasOwnProperty('hasscript') &&
                    !item.data.hasscript
                  "
                >
                  <span v-if="$route.path.match(/postprocess/g)"
                    >请打开技能后处理后再测试！</span
                  >
                  <span v-else
                    >请打开<a @click="toPostprocess">技能后处理</a
                    >后再测试！</span
                  >
                </template>
              </div>
            </div>
          </div>
        </template>
        <div class="ib message" v-else>
          {{ item.data[item.type] }}
        </div>
      </div>
    </div>
    <!-- <div v-if="showThresholdEditWrap" class="debug-threshold">
      <p class="ib">
        调整模糊匹配阈值：0.
        <span v-if="!thresholdEdit" @click="toThresholdEdit">{{
          threshold
        }}</span>
        <el-input
          ref="thresholdInput"
          size="mini"
          :min="50"
          :max="99"
          :step="1"
          :maxlength="2"
          auto-complete="off"
          class="threshold-input"
          v-model="threshold"
          v-if="thresholdEdit"
          @change.native="thresholdChange"
          @blur="thresholdEdit = false"
        ></el-input>
      </p>
      <a class="fr" :href="`${$config.docs}doc-50/`" target="_blank"
        >查看文档</a
      >
    </div> -->

    <div class="send-wrap">
      <el-input
        class="debug-input"
        :maxlength="120"
        v-model="question"
        size="medium"
        @keyup.enter.native="experience"
        @keyup.up.native="preQuestion"
        @keyup.down.native="nextQuestion"
        :placeholder="
          storeSkill && skill.secondType != 2
            ? '打开{技能名称}即可开始体验'
            : '输入语料，回车体验'
        "
      ></el-input>
      <span
        :class="['debug-send', { 'debug-send-active': question }]"
        @click="experience"
        ><svg-icon iconClass="send" />&nbsp;发送</span
      >
    </div>

    <el-dialog
      class="debug-json-dialog"
      title="JSON"
      :visible.sync="showJson"
      width="50%"
    >
      <div class="request-json">
        <template
          v-if="
            skill.protocolVersion == '2.1' &&
            skill.type !== '2' &&
            skill.type != '9'
          "
        >
          <template v-if="resJson.httpCode && resJson.httpCode == '204'">
            <i
              class="ic-r-copy"
              title="复制代码"
              @click="copyJson(resJson)"
            ></i>
            <json-view class="json-wrap" :data="resJson"></json-view>
          </template>
          <el-tabs v-else v-model="activeType" type="card">
            <el-tab-pane label="Request" name="Request">
              <i
                class="ic-r-copy"
                title="复制代码"
                @click="copyJson(reqJson)"
              ></i>
              <json-view class="json-wrap" :data="reqJson"></json-view>
            </el-tab-pane>
            <el-tab-pane label="Response" name="Response">
              <i
                class="ic-r-copy"
                title="复制代码"
                @click="copyJson(resJson)"
              ></i>
              <json-view class="json-wrap" :data="resJson"></json-view>
            </el-tab-pane>
          </el-tabs>
        </template>

        <template v-else>
          <i class="ic-r-copy" title="复制代码" @click="copyJson(resJson)"></i>
          <json-view class="json-wrap" :data="resJson"></json-view>
        </template>
      </div>
      <div class="dialog-bottom"></div>
    </el-dialog>
    <entity-popover
      :slotInfo="slotInfo"
      :variablePopover="variablePopover"
    ></entity-popover>

    <!-- 技能体验调整模糊匹配阈值 -->
    <el-dialog
      title="配置模糊匹配阈值"
      :visible.sync="dialog.show"
      width="760px"
      top="5vh"
      :show-close="false"
    >
      <el-form
        ref="form"
        :model="form"
        label-width="120px"
        style="padding-top: 10px"
      >
        <el-form-item label="模糊匹配阈值">
          <el-col :span="8">
            <el-input-number
              style="width: 170px"
              v-model="form.threshold"
              :min="0.5"
              :max="0.99"
              :precision="2"
              :step="0.01"
              :step-strictly="true"
            ></el-input-number>
          </el-col>

          <el-col :span="8">
            取值为0.5-0.99
            <a :href="`${$config.docs}doc-50/`" target="_blank"
              >&nbsp;查看文档</a
            ></el-col
          >
        </el-form-item>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialog.show = false">取消</el-button>
        <el-button
          size="small"
          type="primary"
          @click="saveChangeData"
          :loading="false"
          :disabled="false"
        >
          保存
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import EntityPopover from './skillDebugEntityPopover'

export default {
  name: 'skill-debug',
  props: ['debugType'],
  data() {
    return {
      submitting: false,
      rc4Answer: [
        '啊哦~~这个问题太难了，换个问题吧！',
        '不好意思，我好像没听懂。。。',
      ],
      rc4Index: 0,
      question: '',
      questionList: [],
      questionIndex: 0, //记录按上下键问题开始位置
      uid: this.$utils.experienceUid(),
      dialogList: [
        {
          type: 'answer',
          data: {
            answer:
              this.debugType === 'skill'
                ? '你好，我是智能的小飞～你可以点击构建，然后测试技能效果(^_-)～'
                : '你好，我是智能的小飞~',
          },
        },
      ],
      thresholdEdit: false,
      threshold: 0.82,
      showJson: false,
      resJson: {},
      reqJson: {},
      hasShowEntityTip: false, // 部分商店技能体验需动态实体提示
      activeType: 'Request',
      slotInfo: {},
      variablePopover: {
        show: false,
        rect: null,
      },

      form: {
        threshold: 0.5,
      },
      dialog: {
        show: false,
      },
    }
  },
  computed: {
    ...mapGetters({
      skill: 'studioSkill/skill',
      app: 'aiuiApp/app',
      currentScene: 'aiuiApp/currentScene',
      appConfigChange: 'aiuiApp/configChange',
      translateConfig: 'aiuiApp/translateConfig',
      skillDetail: 'aiuiStore/skillDetail',
      entityInIntent: 'studioSkill/entityInIntent',
    }),
    showThresholdEditWrap() {
      if (this.debugType !== 'skill') return false
      if (!this.skill.isFuzzy) return false
      if (this.skill.type == 2 || this.skill.type == 3) {
        return true
      } else {
        return false
      }
    },
    storeSkill() {
      return (
        this.debugType == 'skill' &&
        (this.skill.type == 5 || this.skill.type == 9)
      )
    },
  },
  created() {},
  updated() {
    if (this.$refs.list) {
      this.$refs.list.scrollTop = 100000
    }
  },
  watch: {
    'dialog.show'(val) {
      if (val) {
        this.form.threshold = this.threshold
      }
    },
  },
  methods: {
    saveChangeData() {
      this.threshold = this.form.threshold
      this.dialog.show = false
    },
    toThresholdEdit() {
      this.thresholdEdit = true
      this.$nextTick(() => {
        this.$refs.thresholdInput &&
          this.$refs.thresholdInput.$refs.input.focus()
      })
    },
    thresholdChange() {
      let value = this.threshold.replace(/[^0-9]/gi, '')
      if (value === '') {
        this.threshold = 82
        return
      }

      value = parseInt(value)

      if (value >= 5 && value <= 9) {
        this.threshold *= 10
      } else if (value < 50) {
        this.threshold = 50
      } else if (value > 99) {
        this.threshold = 99
      }
    },
    preQuestion() {
      if (this.questionIndex > 0) {
        this.questionIndex--
        this.question = this.questionList[this.questionIndex]
      } else {
        this.question = ''
        this.questionIndex = -1
      }
    },
    nextQuestion() {
      if (this.questionIndex < this.questionList.length) {
        this.questionIndex++
        this.question = this.questionList[this.questionIndex]
      } else {
        this.question = ''
        this.questionIndex = this.questionList.length
      }
    },
    addDialog(type, data) {
      this.dialogList.push({
        type: type,
        data: data,
        httpCode: null,
      })
    },
    experience() {
      if (this.debugType === 'app') {
        if (
          this.currentScene.point &&
          this.currentScene.point.split(',').indexOf('4') >= 0
        ) {
          this.translateExperience()
        } else {
          this.appExperience()
        }
      } else if (this.debugType === 'skill') {
        this.skillExperience()
      } else if (this.debugType === 'store') {
        this.storeExperience()
      }
    },
    cleanHistory() {
      if (this.debugType === 'app') {
        this.appCleanHistory()
      } else if (this.debugType === 'skill') {
        this.skillCleanHistory()
      } else if (this.debugType === 'store') {
        this.storeCleanHistory()
      }
    },
    appExperience() {
      if (this.submitting) return
      if (!this.question) return

      let self = this
      let data = {
        appid: this.app.appid,
        text: this.question.trim(),
        sceneId: '',
        sceneName: 'main_box',
        uid: this.uid,
      }
      let validRouteNameList = ['app-config', 'sub-app-config']
      if (
        validRouteNameList.indexOf(this.$route.name) !== -1 &&
        this.currentScene.sceneName &&
        this.currentScene.sceneName !== 'main'
      ) {
        data.sceneId = this.currentScene.sceneBoxId
        data.sceneName = this.currentScene.sceneBoxName
      }

      this.submitting = true
      this.$utils.httpPost(this.$config.api.AIUI_APP_EXPERIENCE, data, {
        success: (res) => {
          this.submitting = false
          if (res.flag) {
            self.addDialog('question', {
              question: self.question.trim(),
            })
            self.questionList.push(self.question)
            self.questionIndex = self.questionList.length
            self.question = ''

            res.data.res = JSON.parse(res.data.res)
            if (res.data.res.rc == 4) {
              res.data.answer = self.rc4Answer[self.rc4Index]
              self.rc4Index = 1
            }

            if (self.$route.name === 'app-config' && self.appConfigChange) {
              res.data.answer += '(您的应用存在修改但是尚未保存)'
            }

            self.addDialog('answer', res.data)
          }
        },
        error: (err) => {
          this.submitting = false
        },
      })
    },
    translateExperience() {
      if (this.submitting) return
      if (!this.question) return

      let self = this
      let data = {
        appid: this.app.appid,
        text: this.question.trim(),
        source: this.translateConfig.source,
        target: this.translateConfig.target,
      }

      this.submitting = true
      this.$utils.httpPost(this.$config.api.AIUI_APP_TRANSLATION_TASTE, data, {
        success: (res) => {
          this.submitting = false
          if (res.flag) {
            self.addDialog('question', {
              question: self.question.trim(),
            })
            self.questionList.push(self.question)
            self.questionIndex = self.questionList.length
            self.question = ''

            res.data.res = JSON.parse(JSON.stringify(res.data))
            res.data.answer = res.data.trans_result.dst

            if (self.$route.name === 'app-config' && self.appConfigChange) {
              res.data.answer += '(您的应用存在修改但是尚未保存)'
            }

            self.addDialog('answer', res.data)
          }
        },
        error: (err) => {
          this.submitting = false
        },
      })
    },
    appCleanHistory() {
      let self = this
      let data = {
        uid: this.uid,
        appid: this.app.appid,
        type: 3,
      }

      this.$utils.httpGet(this.$config.api.AIUI_APP_EXPERIENCE_CLEAN, data, {
        success: (res) => {
          self.dialogList.splice(1)
        },
        error: (err) => {
          console.log('page=>>')
          console.log(err)
        },
      })
    },
    skillExperience() {
      if (this.submitting) return
      if (!this.question) return

      let self = this
      let data = {
        businessId: this.skill.id,
        text: this.question.trim(),
        name: this.skill.name,
        tyuid: this.uid,
      }
      // if (this.skill.isFuzzy) data.threshold = this.threshold / 100
      if (this.skill.isFuzzy) data.threshold = this.threshold

      this.submitting = true
      this.$utils.httpPost(this.$config.api.STUDIO_SKILL_EXPERIENCE, data, {
        success: (res) => {
          this.submitting = false
          if (res.flag) {
            self.addDialog('question', {
              question: self.question.trim(),
            })
            self.questionList.push(self.question)
            self.questionIndex = self.questionList.length
            self.question = ''

            // 规避res.data.res 为空。2.1协议的 res.data 在httpCode != 200 时， res.data.res 为空
            res.data.res = res.data.res
              ? JSON.parse(res.data.res)
              : res.data.res

            // 2.0协议技能相关
            if (res.data.res.hasOwnProperty('rc') && res.data.res.rc == 4) {
              res.data.answer = self.rc4Answer[self.rc4Index]
              self.rc4Index = 1
            }
            if (res.data.res.hasOwnProperty('console')) {
              self.$store.dispatch(
                'studioSkill/setDebugConsole',
                res.data.res.console
              )
              delete res.data.res.console
            }

            // 2.1协议技能相关
            if (res.data.hasOwnProperty('httpCode')) {
              if (res.data.hasOwnProperty('hasscript') && !res.data.hasscript) {
                // res.data.answer = '请打开技能后处理的云函数后再测试！'
                // 跳转到技能后处理页
              } else if (res.data.httpCode !== 200) {
                if (res.data.httpCode == 204) {
                  res.data.answer = '不好意思，我好像没听懂...'
                  res.data.httpCode = '204'
                  res.data.res = {
                    httpCode: res.data.httpCode,
                    userId: self.uid,
                    requestId: res.data.requestId,
                    text: res.data.text,
                  }
                }
                if (res.data.httpCode == 206) {
                  // res.data.answer = `服务超时: ${res.data.httpCode}`
                  res.data.answer = '服务超时'
                  res.data.httpCode = '206'
                }
                if (res.data.httpCode == 400) {
                  res.data.answer = '无效输入'
                  res.data.httpCode = '400'
                }
              } else {
                if (res.data && !res.data.hasOwnProperty('answer')) {
                  res.data.answer = '请点击json按钮，查看语义结果！'
                }
              }
            } else {
              if (res.data.res.rc == 4) {
                res.data.answer = self.rc4Answer[self.rc4Index]
                self.rc4Index = 1
              }
            }

            // 处理云函数日志
            if (
              res.data.hasOwnProperty('scriptResponse') &&
              res.data.scriptResponse
            ) {
              let scriptResponse = JSON.parse(res.data.scriptResponse)
              if (scriptResponse) {
                let { response = {} } = scriptResponse
                if (
                  response &&
                  response.hasOwnProperty('console') &&
                  response.console
                ) {
                  self.$store.dispatch(
                    'studioSkill/setDebugConsole',
                    scriptResponse.response.console
                  )
                }
              }
              if (
                res.data.res.hasOwnProperty('response') &&
                res.data.res.response
              ) {
                delete res.data.res.response.console
              }
            }

            // 判断是否构建
            if (!res.data.isStructure) {
              res.data.answer += '(您的技能尚未构建)'
            }
            self.addDialog('answer', res.data)
          }
        },
        error: (err) => {
          this.submitting = false
        },
      })
    },
    skillCleanHistory() {
      let self = this
      let data = {
        businessId: this.skill.id,
        tyuid: this.uid,
        protocolVersion: this.skill.protocolVersion,
      }

      this.$utils.httpGet(
        this.$config.api.STUDIO_SKILL_EXPERIENCE_CLEAN,
        data,
        {
          success: (res) => {
            self.dialogList.splice(1)
            self.$store.dispatch('studioSkill/setDebugConsole', [])
          },
          error: (err) => {
            console.log('page=>>')
            console.log(err)
          },
        }
      )
    },
    storeExperience() {
      if (this.submitting) return
      if (!this.question) return

      let self = this
      let data = {
        text: this.question.trim(),
        uid: this.uid,
        appid: 'all',
        category: this.skillDetail.name.replace('IFLYTEK.', ''),
      }

      if (this.skillDetail.sysNumber) {
        data.sysNumber = this.skillDetail.sysNumber
      }
      if (this.skillDetail.hasOwnProperty('currentSkillId')) {
        data.currentSkillId = this.skillDetail.currentSkillId
      }

      this.submitting = true
      this.$utils.httpPost(this.$config.api.AIUI_STORE_TASTE, data, {
        success: (res) => {
          this.submitting = false
          if (res.flag) {
            self.addDialog('question', {
              question: self.question.trim(),
            })
            self.questionList.push(self.question)
            self.questionIndex = self.questionList.length
            self.question = ''

            res.data.res = JSON.parse(res.data.res)
            if (res.data.res.rc == 4) {
              res.data.answer = self.rc4Answer[self.rc4Index]
              self.rc4Index = 1
            }

            self.addDialog('answer', res.data)
          }
        },
        error: (err) => {
          this.submitting = false
        },
      })

      if (
        !this.hasShowEntityTip &&
        (this.skillDetail.name === 'IFLYTEK.telephone' ||
          this.skillDetail.name === 'IFLYTEK.message' ||
          this.skillDetail.name === 'IFLYTEK.weixin' ||
          this.skillDetail.name.split('_')[1] === 'smartHome')
      ) {
        this.$alert(
          'AIUI暂未提供体验示例的动态实体信息，将无法返回示例的单轮和多轮演示效果。如需真实体验，请至应用管理页面选择该技能并设置动态实体。',
          '提示',
          {
            type: 'info',
            confirmButtonText: '确定',
            callback: (action) => {},
          }
        )

        this.hasShowEntityTip = true
      }
    },
    storeCleanHistory() {
      let self = this
      let data = {
        uid: this.uid,
        type: 1,
      }

      this.$utils.httpGet(this.$config.api.AIUI_STORE_EXPERIENCE_CLEAN, data, {
        success: (res) => {
          self.dialogList.splice(1)
        },
        error: (err) => {
          console.log('page=>>')
          console.log(err)
        },
      })
    },
    copyJson(data) {
      this.$utils.copyClipboard(JSON.stringify(data, null, '    '))
    },
    openJsonDialog(datas) {
      this.showJson = !this.showJson
      if (datas.hasOwnProperty('res') && datas.res) {
        this.resJson = datas.res
      }
      if (datas.hasOwnProperty('req')) {
        this.reqJson = datas.req ? JSON.parse(datas.req) : {}
      }
    },
    closeRightTest() {
      this.$store.dispatch('studioSkill/setRightTestOpen', false)
    },
    setSlotValue(slot, e) {
      this.slotInfo = {
        slotMark: slot.name,
        slotValue: slot.value,
      }
      const rect = e.target.getBoundingClientRect()
      this.variablePopover = {
        show: true,
        rect: rect,
      }
    },
    setIflyosSlotValue(slot, e) {
      this.slotInfo = {
        slotMark: slot.name,
        slotValue: slot.value,
      }
      const rect = e.target.getBoundingClientRect()
      this.variablePopover = {
        show: true,
        rect: rect,
      }
    },
    toPostprocess() {
      let self = this
      let name = this.$route.path.match(/\/sub/g)
        ? 'sub-skill-post-process'
        : 'skill-post-process'
      let routeData = this.$router.resolve({
        name: name,
        params: { skillId: self.skill.id },
      })
      window.open(routeData.href, '_blank')
    },
  },
  components: { EntityPopover },
}
</script>

<style lang="scss" scoped>
@import './debug.scss';
</style>
