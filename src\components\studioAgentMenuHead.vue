<template>
  <div class="studio-skill-menu-head">
    <div class="studio-skill-menu-head-return" @click="returnCb">
      <svg-icon
        iconClass="secondary-return"
        :customStyle="{
          width: '13px',
          height: '9px',
          marginRight: '5px',
          transform: 'translateY(1px)',
        }"
      />
      <span>返回列表</span>
    </div>
    <!-- <div
      class="studio-skill-menu-head-skill-name"
      @click.stop="openSelectAgent($event)"
    >
      <span :title="agentName || '-'">{{ agentName || '-' }}</span>
    </div>
    <SelectAgentPopover ref="SelectAgentPopover"></SelectAgentPopover> -->
  </div>
</template>

<script>
import SelectAgentPopover from '@P/studio/handlePlatform/agent/selectAgent.vue'

export default {
  name: 'studioAgentMenuHead',
  data() {
    return {
      agentName: '',
    }
  },

  created() {
    // this.getAgentInfo()
  },

  methods: {
    returnCb() {
      this.$router.push({ name: 'studio-handle-platform-agent' })
    },
    openSelectAgent(event) {
      // this.$store.dispatch('studioSkill/setSkillPopover', {
      //   show: true,
      //   rect: event.target.getBoundingClientRect(),
      // })
      event.preventDefault()
      event.stopPropagation()
      this.$refs.SelectAgentPopover.show(event.target.getBoundingClientRect())
    },

    getAgentInfo() {
      const params = {
        agentId: this.$route.params.agentId,
      }
      this.$utils.httpPost(
        this.$config.api.AGENT_DETAIL_OLD,
        JSON.stringify(params),
        {
          config: {
            headers: {
              'Content-Type': 'application/json;charset=UTF-8',
            },
          },
          success: (res) => {
            if (res.code == '0') {
              this.agentName = res.data.agentName
            }
          },
          error: (err) => {
            this.$message.error(err.desc)
          },
        }
      )
    },
  },
  components: {
    SelectAgentPopover,
  },
}
</script>

<style lang="scss">
.studio-skill-menu-head {
  padding: 16px 22px 0;
  height: 96px;
  &-return {
    display: inline-flex;
    cursor: pointer;
    margin-bottom: 10px;
    font-size: 14px;
    color: $grey4;
    align-items: center;
    i {
      color: $grey4;
    }
  }
  &-skill-name {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    cursor: pointer;
    span {
      font-size: 24px;
      // font-weight: 600;
    }
  }
}
</style>
