<template>
  <div
    v-if="entityPopover.show && showType === entityPopover.showType"
    v-clickoutside="closePopover"
    v-scrollcb="handleScroll"
    ref="selectEntityPopover"
    class="el-popover el-popper el-popover--plain editor-select-popover"
    :style="popperStyle"
    :x-placement="entityPopover.showTop ? 'top-start' : 'bottom'">
    <div>
      <div class="editor-select-popover__head">
        <span class="lump">
          <span
            :style="entityPopover.style"
            :title="entityPopover.data.entityName">{{useInReplyDialog ? '#' : ''}}{{entityPopover.data.entityName}}</span>
        </span>
        <el-button v-if="!useInReplyDialog" size="mini" style="min-width:72px;" @click="changeSlotType">{{type === 0 ? '实体' : '辅助词'}}<i class="ic-r-exchange el-icon--right"></i></el-button>
        <el-button v-if="!useInReplyDialog" type="text" size="mini" icon="ic-r-plus" style="min-width:46px;" @click="toCreate">创建</el-button>
      </div>
      <div class="editor-select-popover__body">
        <div class="editor-select-popover-search">
          <el-input
            class="search-area"
            size="medium"
            :placeholder="type === 0 ? '搜索实体' : '搜索辅助词'"
            v-model="searchName">
          </el-input>
        </div>
        <div class="editor-select-popover-list" v-loading="loading">
          <div class="editor-select-popover-item"
            v-for="(item, key) in entityList"
            @click="selectItem(item)">
            <span :class="['txt-ellipsis-nowrap', {'bold': item.type == 2 || item.type == 3 || item.type == 5 || item.type == 6}]" :title="item.name">{{item.name}}</span>
            <span class="txt-ellipsis-nowrap" style="float: right; width: 80px;" :title="item.value">{{item.value}}</span>
          </div>
          <div class="el-table__empty-block" v-if="!entityList.length">
            <span class="el-table__empty-text">暂无数据</span>
          </div>
        </div>
      </div>
      <div class="popover-bottom" v-if="useInReplyDialog" @click="toCreate">
        <el-button type="text" size="mini" icon="ic-r-plus" style="min-width:46px;" @click="toCreate">创建辅助词</el-button>
      </div>
    </div>
    <div x-arrow="" class="popper__arrow" style="left: 20px;"></div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  props: {
    useInReplyDialog: {
      type: Boolean,
      default: null
    },
    showType: {
      required: true,
      type: String
    }
  },
  data () {
    return {
      loading: true,
      rect: {
        top: 0,
        left: 0,
        width: 0
      },
      baseRectTop: 0,
      type: 0,
      searchName: '',
      entityList: [],
      debounced: null
    }
  },
  computed: {
    ...mapGetters({
      intention: 'studioSkill/intention',
      entityPopover: 'studioSkill/entityPopover',
      subAccountSkillAuths: 'studioSkill/subAccountSkillAuths',
      businessId: 'studioSkill/id'
    }),
    popperStyle () {
      let top = 0,
          left = 0
      if (this.rect) {
        if (this.entityPopover.showTop) {
          top = this.rect.top - 340
        } else {
          top = this.rect.top + 20
        }
        left = this.rect.left - (56 - this.rect.width) / 2
        return {
          'top': `${top}px`,
          'left': `${left}px`
        }
      } else {
        return {
          'display': `none`
        }
      }
    },
    subAccountEditable(){
      return this.subAccountSkillAuths[this.intention.businessId] == 2 ? false : true
    }
  },
  watch: {
    'entityPopover.show': function (val, oldVal) {
      if (val) {
        this.searchName = ''
        this.initData()
      }
    },
    'entityPopover.rect': function (val, oldVal) {
      this.rect = JSON.parse(JSON.stringify(this.entityPopover.rect))
      this.baseRectTop = JSON.parse(JSON.stringify(this.entityPopover.rect)).top
    },
    'searchName': function (val, oldVal) {
      this.debounced()
    }
  },
  created () {

  },
  mounted () {
    this.setDebounce()
  },
  beforeDestroy(){
    this.debounced = null
  },
  methods: {
    setDebounce(){
      this.debounced = this.$utils.debounce(() => { this.getData(1) }, 500, true)
    },
    initData () {
      if(this.entityPopover.showType == 'modifierSlot') {
        //自定义修饰语 slot
        this.type = this.entityPopover.data.entityType === 2 ? 1 : 0
      } else {
        this.type = this.$deepClone(this.entityPopover.data.slotType)
      }
      this.getData()
    },
    getData () {
      let self = this
      this.loading = true
      self.type = self.type === 2 ? 1 : self.type  //回复语的type 为2，不过传参时要传 1
      this.$utils.httpGet(this.$config.api.STUDIO_SKILL_ALL_ENTITYS, {
        type: this.type,
        businessId: this.intention.businessId,
        search: this.searchName
      }, {
        success: (res) => {
          let arr = []
          if (this.type === 0) {
            arr = [...res.data.used, ...res.data.private, ...res.data.personal, ...res.data.public]
          } else {
            arr = [...res.data.used, ...res.data.auxiliary]
          }
          self.entityList = arr
          self.loading = false
        },
        error: (err) => {
          console.log('page=>>');
          console.log(err);
        }
      })
    },
    changeSlotType () {
      this.type = this.type === 0 ? 1 : 0
      this.getData()
    },
    toCreate () {
      
      let routeNamePrefix = this.$route.path.includes('/sub/') ? 'sub-' : ''
      let routeNameSuffix = this.type === 0 ? 'studio-handle-platform-entities' : 'studio-handle-platform-auxiliaries'
      let routeData = routeNamePrefix + routeNameSuffix
      routeData = this.$router.resolve({name: routeData})
      localStorage.setItem('pageHandle', 'create')
      window.open(routeData.href, '_blank')
    },
    handleScroll (e, top) {
      this.rect.top = this.baseRectTop - top
    },
    closePopover () {
      this.$store.dispatch('studioSkill/setEntityPopover', {
        show: false
      })
    },
    selectItem (item) {
      let self = this
      if(this.entityPopover.showType == 'modifierSlot') {
        return self.setModifierSlot(item)
      }
      if(!this.subAccountEditable) {
        return self.closePopover()
      }
      let entityIds = ''
      if (this.entityPopover.data.entityId) {
        entityIds = `${item.id},${this.entityPopover.data.entityId}`
      } else {
        entityIds = item.id
      }
      let api
      let data = {
        businessId: this.intention.businessId,
        intentId: this.intention.id,
        entityIds: entityIds
      }
      if(this.entityPopover.data.source) {
        api = this.$config.api.STUDIO_EXTEND_DEPLOY_SOURCE_ENTITY
        data.parentSlotId = this.entityPopover.data.parentSlotId
      } else {
        api = this.$config.api.STUDIO_SLOT_DEPLOY_ENTITY
        data.id = this.entityPopover.data.id
        data.name = this.entityPopover.data.slotName
      }
      this.$utils.httpPost(api, data, {
        success: (res) => {
          if (self.type === 0) {
            self.$message.success('设置实体成功')
          } else {
            self.$message.success('设置辅助词成功')
          }
          self.closePopover()
          self.$emit('change')

        },
        error: (err) => {
          console.log('page=>>');
          console.log(err);
        }
      })
    },
    setModifierSlot(item){
      let self = this
      let data = {
        id: this.entityPopover.data.id,
        businessId: this.businessId,
        modifierId: this.entityPopover.data.modifierId,
        entityId: item.id
      }
      this.$utils.httpPost(this.$config.api.STUDIO_MODIFIER_SLOT_BINDS, data, {
        success: (res) => {
          if (self.type === 0) {
            self.$message.success('设置实体成功')
          } else {
            self.$message.success('设置辅助词成功')
          }
          self.closePopover()
          self.$emit('change')

        },
        error: (err) => {
          console.log('page=>>');
          console.log(err);
        }
      })

    }
  },
  components: {

  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="scss">
.editor-select-popover {
  position: fixed;
  width: 318px;
  transform-origin: center top 0px;
  z-index: 2000;
  padding: 0;
  padding-top: 18px;
}

.editor-select-popover__head {
  display: flex;
  align-items: center;
  padding: 0 20px;
}
.editor-select-popover__body {
  margin-top: 10px;
}
.editor-select-popover-search {
  border-top: 1px solid $grey2;
  border-bottom: 1px solid $grey2;
}
.editor-select-popover-search input {
  border: 0;
}
.editor-select-popover-list {
  width: 100%;
  height: 236px;
  overflow-y: scroll;
}
.editor-select-popover-item {
  height: 36px;
  line-height: 36px;
  padding: 0 16px;
  cursor: pointer;
  display: flex;
  &:hover {
    background: $primary-light-12;
  }
  span:first-child {
    flex: auto;
  }
  .bold {
    font-weight: 600;
  }
  span:last-child {
    width: 64px;
    text-align: right;
    color: $grey4;
  }
}
.popover-bottom {
  padding-left: 16px;
  height: 40px;
  line-height: 40px;
  cursor: pointer;
}
</style>
