<template>
  <div class="os-scroll os_scroll">
    <div class="entity-page" v-loading="tableData.loading">
      <div class="entity-page-head">
        <!-- <i class="ic-r-angle-l-line entity-page-head-back" @click="back" /> -->
        <div class="entity_page_head_left">
          <back-icon @click="back" style="margin-right: 10px"></back-icon>
          <el-popover placement="bottom" width="264" trigger="click">
            <select-auxiliary :subAccount="subAccount" />
            <div slot="reference" class="entity-page-head-title">
              <span
                style="max-width: 250px"
                class="txt-ellipsis-nowrap"
                :title="oldEntity.zhName"
                >{{ oldEntity.zhName }}</span
              >
              <i class="ic-r-triangle-down" />
            </div>
          </el-popover>
        </div>

        <div class="header-right">
          <div v-if="entity.operator">
            <p class="header-save-time">
              最近由<span class="text-blod" style="color: #262626">{{
                entity.operator
              }}</span>
            </p>
            <p v-if="entity.updateTime" class="header-save-time">
              保存于{{ entity.updateTime | time }}
            </p>
          </div>
          <!-- <span class="header-save-time" v-if="entity.updateTime"
            >最近保存 {{ entity.updateTime | time }}</span
          > -->
          <span class="header-qa">
            <el-tooltip
              class="item"
              effect="dark"
              content="辅助词构建后方可生效"
              placement="bottom"
            >
              <i class="el-icon-question" />
            </el-tooltip>
          </span>
          <el-button
            size="small"
            type="primary"
            @click="structure"
            :loading="structureLoading"
          >
            {{ structureLoading ? '构建中...' : '构建辅助词' }}</el-button
          >
        </div>
      </div>
      <!-- <os-divider class="mgb56" /> -->
      <div class="os_scroll_content">
        <div class="main">
          <div class="mgb24">
            <os-page-label label="基本信息" class="mgb12" />
            <el-form
              :model="entity"
              ref="entityForm"
              label-width="104px"
              :rules="rules"
              inline
              label-position="left"
            >
              <el-form-item
                label="辅助词名称"
                prop="zhName"
                class="entity-page-form-item"
                style="width: 380px"
              >
                <template v-if="!edit">
                  <span class="entity-name" :title="entity.zhName">{{
                    entity.zhName || '-'
                  }}</span>
                  <div class="ib entity-edit-btn" @click="toEdit">
                    <i class="ic-r-edit" />
                    <span>编辑</span>
                  </div>
                </template>
                <div v-else @keyup.enter="editEntityBlur">
                  <el-input
                    v-model="entity.zhName"
                    ref="entityNameInput"
                    class="entity-page-form-input"
                    placeholder="请输入辅助词名称"
                    @blur="editEntity"
                  />
                  <input type="text" style="display: none" />
                  <i
                    class="entity-page-form-save el-icon-check"
                    @click="$refs.entityForm.validate()"
                  />
                  <i
                    class="entity-page-form-cancel el-icon-close"
                    @mousedown="cancelEdit"
                  />
                </div>
              </el-form-item>

              <el-form-item
                label="辅助词标识"
                prop="name"
                class="entity-page-form-item ib"
              >
                <span>{{ entity.name }}</span>
              </el-form-item>
            </el-form>
          </div>
          <div>
            <os-page-label label="编辑词条" class="mgb24">
              <span style="flex: auto"></span>
              <div class="fr" @keyup.enter="searchEntry">
                <el-input
                  class="entry-search-area"
                  size="medium"
                  placeholder="搜索词条"
                  v-model="entrySearchName"
                >
                  <i
                    slot="suffix"
                    class="el-input__icon el-icon-search search-area-btn"
                    @click="searchEntry"
                  />
                </el-input>
              </div>
            </os-page-label>
            <div class="mgb16">
              <el-button
                class="mgr16"
                icon="ic-r-plus"
                size="small"
                type="primary"
                style="vertical-align: bottom"
                @click="addRow"
              >
                添加词条
              </el-button>
              <el-dropdown
                trigger="click"
                @command="handleCommand"
                placement="bottom-start"
              >
                <el-button size="small">
                  批量操作
                  <i class="ic-r-triangle-down el-icon--right" />
                </el-button>
                <el-dropdown-menu style="width: 120px" slot="dropdown">
                  <el-dropdown-item>
                    <div @click="coverShow = true">批量覆盖</div>
                  </el-dropdown-item>
                  <el-dropdown-item
                    v-if="tableData.total >= entityLimitCount"
                    style="padding: 0"
                  >
                    <div class="import-disabled">批量追加</div>
                  </el-dropdown-item>
                  <el-dropdown-item v-else>
                    <upload
                      :dictId="dictId"
                      :options="addOnly"
                      :limitCount="limitCount"
                      :subAccount="subAccount"
                      @setErrInfo="setErrInfo"
                      @setLoad="setLoad"
                      @getEntryList="getEntryList(1)"
                    ></upload>
                  </el-dropdown-item>
                  <el-dropdown-item command="export">
                    导出辅助词
                  </el-dropdown-item>
                  <el-dropdown-item command="download">
                    下载模版
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
            <os-table
              ref="entityTable"
              :border="true"
              class="entry-table gutter-table-style secondary-table"
              :height="'calc(100vh - 342.67px)'"
              :tableData="tableData"
              @change="getEntryList"
              @del="delEntry"
            >
              <el-table-column prop="value" label="词条">
                <template slot-scope="scope">
                  <el-input
                    :ref="'entityValueInput' + scope.$index"
                    class="entry-value"
                    size="small"
                    placeholder="输入词条，回车添加"
                    v-model="scope.row.value"
                    @keyup.enter.native="editEntryBlur"
                    @blur="editEntry(scope.row)"
                  >
                  </el-input>
                </template>
              </el-table-column>
            </os-table>
            <!-- <os-table
          :border="true"
          class="entry-table"
          :tableData="tableData"
          @del="toDel">
          <el-table-column
            prop="value"
            label="词条">
            <template slot-scope="scope">
              <el-input
                class="entry-value"
                size="small"
                placeholder="输入词条，回车添加"
                v-model="scope.row.value"
                @keyup.enter.native="editEntryBlur"
                @blur="editEntry(scope.row)">
              </el-input>
            </template>
          </el-table-column>
        </os-table> -->
          </div>
        </div>
      </div>
    </div>
    <!-- 子账号登录后首次进入提示 -->
    <cooperate-warn-dialog
      :dialog="cooperateDialog"
      type="firtEnterAuxiliary"
    ></cooperate-warn-dialog>
    <el-dialog
      class="upload-cover-dialog"
      width="480px"
      :visible.sync="coverShow"
    >
      <div slot="title" class="dialog-title">
        <i class="ic-r-exclamation"></i>确定批量导入覆盖吗？
      </div>
      <span>一旦导入成功，技能现有信息将被完全覆盖！</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="coverShow = false">取 消</el-button>
        <el-button @click="coverShow = false">
          <upload
            :dictId="dictId"
            :name="entity.name"
            :options="cover"
            :limitCount="limitCount"
            :subAccount="subAccount"
            @setLoad="setLoad"
            @setErrInfo="setErrInfo"
            @getEntryList="getEntryList(1)"
          ></upload>
        </el-button>
      </span>
    </el-dialog>
    <!-- 批量操作错误提示 -->
    <el-dialog title="错误提示" :visible.sync="showErrDialog" width="50%">
      <div style="margin-bottom: 20px">
        <p
          style="line-height: 22px"
          v-for="(text, index) in errList"
          :key="index"
        >
          {{ text }}
        </p>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="showErrDialog = false"
          >确 定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import SelectAuxiliary from './selectAuxiliary.vue'
import CooperateWarnDialog from '@C/cooperatWarnDialog'
import { mapGetters } from 'vuex'
import Upload from './uploadFile'

export default {
  name: 'auxiliary',
  data() {
    return {
      entityId: '',
      entity: {},
      oldEntity: {},
      rules: {
        zhName: [
          this.$rules.required('辅助词名称不能为空'),
          this.$rules.lengthLimit(1, 32, '辅助词名称长度不能超过32个字符'),
          this.$rules.baseRegLimit(),
        ],
      },
      edit: false,
      entrySearchName: '',
      tableData: {
        loading: false,
        total: 0,
        page: 1,
        size: 10,
        handles: ['del'],
        handleColumnText: '',
        list: [],
      },
      structureLoading: false,
      checkCount: 0,
      cooperateDialog: {
        show: false,
      },
      cover: {
        type: 1,
        text: '覆 盖',
      },
      addOnly: {
        type: 0,
        text: '批量追加',
      },
      coverShow: false,
      showErrDialog: false,
      errList: [],
      dictId: '',
    }
  },
  computed: {
    ...mapGetters({
      limitCount: 'aiuiApp/limitCount',
      subAccount: 'user/subAccount',
    }),
    isCharNoLimit() {
      return this.limitCount['char_no_limit_language'] > 0 // 0：限制；>0不限制
    },
    entityLimitCount() {
      return this.limitCount['entity_entry_count'] || '20000'
    },
  },
  created() {
    if (this.$route.params.entityId) {
      this.entityId = this.$route.params.entityId
      this.getEntityDetail()
      this.getEntryList()
    } else {
      this.$router.push({ name: 'studio-handle-platform-auxiliaries' })
    }
    if (this.subAccount && localStorage.getItem('firstEnterAuxiliary')) {
      this.cooperateDialog.show = true
    }
  },
  methods: {
    back() {
      // this.subAccount
      //   ? this.$router.push({ name: 'sub-studio-handle-platform-auxiliaries' })
      //   : this.$router.push({ name: 'studio-handle-platform-auxiliaries' })
      if (this.subAccount) {
        this.$router.push({ name: 'sub-studio-handle-platform-auxiliaries' })
      } else {
        this.$router.push({
          name: 'studio-skill',
          query: { type: 'auxiliary' },
        })
      }
    },
    getEntityDetail() {
      let self = this
      this.$utils.httpGet(
        this.$config.api.STUDIO_ENTITY_DETAIL,
        {
          entityId: this.entityId,
        },
        {
          success: (res) => {
            self.entity = res.data
            self.oldEntity = JSON.parse(JSON.stringify(res.data))
            self.dictId = res.data.dictList[0].dictId || ''
          },
          error: (err) => {
            self.$router.push({ name: 'studio-handle-platform-auxiliaries' })
          },
        }
      )
    },
    toEdit() {
      this.edit = true
      this.$nextTick(function () {
        this.$refs['entityNameInput'] && this.$refs['entityNameInput'].focus()
      })
    },
    cancelEdit() {
      this.edit = false
      this.entity.zhName = this.oldEntity.zhName
      this.$refs.entityForm && this.$refs.entityForm.clearValidate()
    },
    editEntityBlur(e) {
      e.target.blur()
    },
    editEntity() {
      if (this.entity.zhName === this.oldEntity.zhName) {
        this.edit = false
        return
      }
      if (!this.edit) {
        return
      }
      this.$refs.entityForm.validate((valid) => {
        if (valid) {
          this.$utils.httpPost(
            this.$config.api.STUDIO_ENTITY_EDIT,
            {
              entityId: this.entityId,
              value: this.entity.zhName,
            },
            {
              success: (res) => {
                this.$message.success('修改成功')
                this.oldEntity.zhName = this.entity.zhName
                this.edit = false
                this.getEntityDetail()
              },
              error: (err) => {},
            }
          )
        }
      })
    },
    getEntryList(page) {
      let self = this
      this.tableData.loading = true
      this.$utils.httpGet(
        this.$config.api.STUDIO_ENTITY_ENTRY_LIST,
        {
          entityId: this.entityId,
          pageIndex: page || this.tableData.page,
          pageSize: this.tableData.size,
          search: this.entrySearchName,
        },
        {
          success: (res) => {
            this.tableData.list = res.data.results
            this.tableData.total = res.data.count
            this.tableData.page = res.data.pageIndex
            this.tableData.size = res.data.pageSize
            this.tableData.loading = false
          },
          error: (err) => {},
        }
      )
    },
    searchEntry() {
      this.getEntryList(1)
    },
    addRow() {
      let self = this
      let entry = this.tableData.list[0] || {}
      if (entry.id || this.tableData.list.length <= 0) {
        this.tableData.list.unshift({})
        if (this.tableData.total % 10 === 0) {
          this.tableData.size = 11
        } else {
          this.tableData.size = 10
        }
        this.tableData.total += 1
      }
      this.$nextTick(function () {
        self.$refs['entityValueInput0'] &&
          self.$refs['entityValueInput0'].focus()
      })
    },
    addEntry(data) {
      let self = this
      let reg =
        /^[\u4e00-\u9faf\uff00-\uffef\u30a0-\u30ff\u3000-\u303f\u3040-\u309F\u0400-\u052f\u00a0-\u00ff\u0100-\u017f\u0180-\u024f0-9\u4e00-\u9fffa-zA-Z=' ]+$/
      if (!data.value) {
        return
      }
      if (data.value.length > 32) {
        return self.$message.warning('词条名不能超过32个字符')
      }
      if (!this.isCharNoLimit) {
        if (!reg.test(data.value)) {
          return self.$message.warning('词条名仅支持汉字/数字/字母/等号')
        }
      }
      this.$utils.httpPost(
        this.$config.api.STUDIO_ENTITY_ENTRY_ADD,
        {
          entityId: this.entityId,
          value: data.value,
        },
        {
          success: (res) => {
            let entry = self.tableData.list[0]
            entry.id = res.data.id
            self.addRow()
          },
          error: (err) => {},
        }
      )
    },
    editEntryBlur(event) {
      event.target.blur()
    },
    editEntry(data) {
      let self = this
      let reg =
        /^[\u4e00-\u9faf\uff00-\uffef\u30a0-\u30ff\u3000-\u303f\u3040-\u309F\u0400-\u052f\u00a0-\u00ff\u0100-\u017f\u0180-\u024f0-9\u4e00-\u9fffa-zA-Z=' ]+$/
      if (data.id) {
        if (!data.value) {
          return this.$message.warning('词条不能为空')
        }
        if (data.value.length > 32) {
          return self.$message.warning('词条名不能超过32个字符')
        }
        if (!this.isCharNoLimit) {
          if (!reg.test(data.value)) {
            return self.$message.warning('词条名仅支持汉字/数字/字母/等号')
          }
        }
        this.$utils.httpPost(
          this.$config.api.STUDIO_ENTITY_ENTRY_EDIT,
          {
            entityId: this.entityId,
            id: data.id,
            value: data.value,
          },
          {
            success: (res) => {
              // self.$message.success('修改成功')
            },
            error: (err) => {},
          }
        )
      } else {
        this.addEntry(data)
      }
    },
    toDel(data) {
      let self = this
      this.$confirm(
        '词条删除后不可恢复，请谨慎操作。',
        `确定删除词条 - ${data.value}`,
        {
          confirmButtonText: '确定删除',
          cancelButtonText: '取消',
          confirmButtonClass: 'el-button--danger',
          type: 'warning',
          showClose: false,
        }
      )
        .then(() => {
          self.delEntry(data)
        })
        .catch(() => {})
    },
    delEntry(data, index) {
      let self = this
      if (!data.id) {
        this.tableData.list.splice(index, 1)
        this.tableData.total -= 1
        return
      }
      this.tableData.loading = true
      this.$utils.httpPost(
        this.$config.api.STUDIO_ENTITY_ENTRY_DEL,
        {
          entityId: this.entityId,
          ids: data.id,
        },
        {
          success: (res) => {
            self.$message.success('删除成功')
            if (self.tableData.list.length === 1 && self.tableData.page > 1) {
              self.tableData.page -= 1
            }
            self.getEntryList()
          },
          error: (err) => {},
        }
      )
    },
    // 构建
    structure() {
      let self = this
      if (this.tableData.list.length <= 0) {
        return this.$message.warning('请添加至少一个词条再进行构建')
      }
      this.structureLoading = true
      this.$utils.httpPost(
        this.$config.api.STUDIO_ENTITY_COMPILE,
        {
          entityId: this.entityId,
        },
        {
          success: (res) => {
            this.$message.success('提交成功，正在构建...')
            this.checkStatus()
          },
          error: (err) => {
            self.structureLoading = false
          },
        }
      )
    },
    checkStatus() {
      let self = this
      this.checkCount += 1
      this.$utils.httpGet(
        this.$config.api.STUDIO_ENTITY_CHECK_COMPILE_STATUS,
        {
          entityId: this.entityId,
        },
        {
          success: (res) => {
            if (res.data === 4) {
              if (self.structureLoading) {
                self.$message.error('构建失败')
              }
              self.structureLoading = false
              self.checkCount = 0
            } else if (res.data === 1) {
              if (self.structureLoading) {
                self.$message.success('构建成功')
              }
              self.structureLoading = false
              self.checkCount = 0
            } else {
              if (self.checkCount < 300) {
                setTimeout(function () {
                  self.checkStatus()
                }, 2000)
              } else {
                if (self.structureLoading) {
                  self.$message.error('构建失败')
                }
                self.structureLoading = false
                self.checkCount = 0
              }
            }
          },
          error: (err) => {},
        }
      )
    },
    handleCommand(command) {
      let self = this
      switch (command) {
        case 'export':
          self.exportExcel()
          break
        case 'download':
          self.downloadExcel()
          break
        default:
          break
      }
    },
    exportExcel() {
      this.$utils.postopen(this.$config.api.STUDIO_ENTITY_EXPORT_EXCEL, {
        name: this.entity.name,
        dictId: this.dictId,
        isAuxiliary: 1,
      })
    },
    downloadExcel() {
      window.open(
        'https://aiui-file.cn-bj.ufileos.com/DemoAuxiliary.xlsx',
        '_self'
      )
    },
    setLoad(val) {
      this.tableData.loading = val
    },
    setErrInfo(data, type) {
      this.errList = JSON.parse(data)
      this.showErrDialog = type
    },
  },
  components: {
    SelectAuxiliary,
    CooperateWarnDialog,
    Upload,
  },
}
</script>

<style scoped lang="scss">
.os_scroll {
  overflow-y: hidden !important;
  .entity-page {
    background-color: $secondary-bgc;
    .entity-page-head {
      padding: 0 24px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      background-color: #fff;
      .entity_page_head_left {
        display: flex;
        align-items: center;

        .txt-ellipsis-nowrap {
          font-weight: 600;
          font-size: 20px;
          color: #000000;
        }
      }
    }
  }

  .os_scroll_content {
    padding: 15px 20px;
    height: calc(100vh - 64px);
    overflow-y: auto;
    .main {
      min-height: 100%;
      padding: 20px 20px;
      border-radius: 16px;
      background-color: #fff;
      :deep(.os-page-label) {
        height: 24px;
        i {
          font-size: 14px;
        }
        .title {
          font-size: 18px;
          font-weight: 700;
        }
      }
    }
  }
}
</style>

<style lang="scss">
.entity-page {
  // max-width: 1200px;
  // padding-top: 24px;
  padding-bottom: 70px;
  margin: auto;
  &-head {
    height: 63px;
    display: flex;
    align-items: center;
    position: relative;
    border-bottom: 1px solid #e7e7e7;
    &-back {
      cursor: pointer;
      margin-right: 16px;
      color: $grey4;
    }
    &-title {
      flex: auto;
      display: flex;
      align-items: center;
      cursor: pointer;
      i {
        color: $grey5;
        margin-left: 4px;
      }
    }
    &-right {
      position: absolute;
      right: 0;
    }
  }
}
.entity-name {
  display: inline-block;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.entity-edit-btn {
  vertical-align: top;
  margin-left: 9px;
  color: $primary;
  cursor: pointer;
}

.entity-page-form-item {
  vertical-align: top;
  width: 30%;
  margin-bottom: 0;
}
.entity-page-form-input {
  width: 188px;
}
.entity-page-form-save,
.entity-page-form-cancel {
  margin-left: 16px;
  cursor: pointer;
  &:hover {
    color: $primary;
  }
}
.entry-search-area {
  width: 240px;
}
.entry-value {
  input {
    height: 54px !important;
    line-height: 54px !important;
    border: 0 !important;
    padding-left: 0;
    color: $semi-black;
    font-weight: 600;
  }
}

.entry-table {
  .el-table__body-wrapper {
    border-bottom: 1px solid #d8e0ed;
  }
  .el-table--border {
    border: none;
  }
  .el-table__row td {
    padding: 0;
    height: 56px;
  }
  .el-table__body tr:hover > td {
    background-color: #fff;
  }
  td .cell {
    padding-left: 0;
  }
}
.upload-cover-dialog {
  .el-button {
    padding: 0;
    width: 130px;
    height: 44px;
    line-height: 44px;
    overflow: hidden;
  }
  .el-upload .el-button {
    width: 130px;
    height: 44px;
    font-size: 16px;
    color: $dangerous;
  }
  .dialog-title {
    font-size: 20px;
  }
  .ic-r-exclamation {
    margin-right: 16px;
    vertical-align: -1px;
    color: $warning;
  }
  .el-dialog__header {
    padding: 32px 32px 12px;
  }
  .el-dialog__body {
    padding-top: 0;
    padding-bottom: 23px;
    font-size: 16px;
    color: $semi-black;
    padding-left: 70px;
  }
  .el-dialog__footer {
    padding-bottom: 32px;
  }
}
</style>
