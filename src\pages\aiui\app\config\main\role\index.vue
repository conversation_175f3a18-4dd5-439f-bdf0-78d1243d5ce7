<template>
  <card>
    <template #title> 回复角色配置 </template>
    <div v-if="sceneRoleId">
      <div class="role-card">
        <img :src="roleDetail?.info?.image" />
        <ul>
          <li>
            <label>姓&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;名：</label>
            {{ roleDetail?.info?.name }}
          </li>
          <li>
            <label>性格特征：</label>
            {{ roleCharacter }}
          </li>
          <li>
            <label>角色任务：</label>
            {{ roleTask }}
          </li>
          <li>
            <a @click="openDetail()" v-show="!roleDetail?.info?.official"
              >查看更多 &gt;</a
            >
          </li>
        </ul>
      </div>

      <div>
        <div class="conf-title item-title" style="margin-top: 0">
          <div style="margin-right: 10px">角色发音人</div>
          <el-switch
            v-model="appTtsConfig.enable"
            class="mgr16"
            :disabled="!subAccountEditable"
            @change="(val) => changeMethod('enable', val)"
            :active-value="1"
            :inactive-value="0"
          ></el-switch>
        </div>
        <p class="item-desc">
          发音人关闭后只下发文本信息，你可以使用主动合成的方式调用合成
        </p>
        <el-form
          ref="form"
          class="custom_form"
          :disabled="!subAccountEditable"
          label-width="90px"
          label-position="right"
          style="width: 70%"
        >
          <el-form-item label="音色：">
            <div class="tone-card">
              <img :src="appTtsConfig.url" v-show="appTtsConfig.url" />
              <span class="text" :title="appTtsConfig.name">
                {{ appTtsConfig.name || '--' }}
              </span>
              <a @click="changeMethod('tone')">更换</a>
            </div>
          </el-form-item>
          <el-form-item label="音量：">
            <el-slider
              class="slider"
              v-model="appTtsConfig.volume"
              @change="(val) => changeMethod('volume', val)"
              show-input
            ></el-slider>
          </el-form-item>
          <el-form-item label="语速：">
            <el-slider
              class="slider"
              v-model="appTtsConfig.speed"
              @change="(val) => changeMethod('speed', val)"
              show-input
            ></el-slider>
          </el-form-item>
          <el-form-item label="试听文本：">
            <el-input
              type="textarea"
              v-model="appTtsConfig.text"
              :autosize="{ minRows: 4 }"
              :placeholder="`请输入试听文本`"
              :maxlength="300"
              show-word-limit
            />
            <el-button
              v-if="testStatus === 2"
              type="primary"
              size="small"
              @click="stop"
              >停止试听</el-button
            >
            <el-button
              v-else
              style="margin-top: 10px"
              type="primary"
              size="small"
              :loading="testStatus === 1"
              @click="synthetic"
              >试听</el-button
            >
          </el-form-item>
        </el-form>
      </div>
    </div>
    <tone-select
      :dialog="ttsDialog"
      :roleTtsData="appTtsConfig"
      :fromApp="true"
      :currentScene="currentScene"
      @change="(val) => changeMethod('tts', val)"
    />
  </card>
</template>
<script>
import card from '../components/card'
import { mapGetters } from 'vuex'
import { fetchEventSource } from '@microsoft/fetch-event-source'
import toneSelect from '@P/studio/role/dialog/toneSelect.vue'
import TTSRecorder from '@P/studio/role/tts/TTSRecorder'

const abortController = new AbortController()
export default {
  data() {
    return {
      roleDetail: {},
      roleTtsConfig: {}, //角色配置的发音人信息
      appTtsConfig: {}, //应用配置的发音人信息
      ttsDialog: {
        show: false,
      },
      defaultVcn: '',
      testStatus: 0, //试听状态 0：初始状态，1：正在合成，2：播放中
      ttsRecorder: null,
      ttsRecorderStatus: 'init',
      uid: this.$utils.experienceUid(),
    }
  },
  computed: {
    ...mapGetters({
      currentScene: 'aiuiApp/currentScene',
      appInfo: 'aiuiApp/app',
      sceneRoleId: 'aiuiApp/sceneRoleId',
      subAccountEditable: 'aiuiApp/subAccountEditable',
    }),
    roleCharacter() {
      let character = this.roleDetail?.property?.character
      if (character) {
        let cObj = character.find((c) => c.name === '性格')
        if (cObj) return cObj.value || '--'
      }
      return '--'
    },
    roleTask() {
      let task = this.roleDetail?.property?.task
      if (task) {
        let tObj = task.find((t) => t.name === '任务描述')
        if (tObj) return tObj.value || '--'
      }
      return '--'
    },
  },
  components: {
    card,
    toneSelect,
  },
  async created() {
    if (this.sceneRoleId) {
      await this.getRoleDetail()
    }
    this.ttsRecorder = new TTSRecorder(this.ttsRecorderStatusCb, true)
    this.getDefaultVcn()
    this.getTtsConfig()
  },
  beforeDestroy() {
    this.ttsRecorder && this.ttsRecorder.audioStop()
  },
  watch: {
    currentScene(scene) {},
    async sceneRoleId(roleId, oldRoleId) {
      if (roleId && oldRoleId) {
        // console.log('更换角色情形')
        await this.getRoleDetail()
        this.getTtsConfig()
      } else if (roleId && !oldRoleId) {
        await this.getRoleDetail()
        this.getTtsConfig()
      }
    },
    ttsRecorderStatus(val) {
      if (val === 'endPlay') {
        this.testStatus = 0
      }
    },
  },
  methods: {
    getRoleDetail() {
      return new Promise((resolve, reject) => {
        this.$utils.httpGet(
          this.$config.api.AIUI_BOT_ROLE_DETAIL,
          {
            roleId: this.sceneRoleId,
          },
          {
            success: (res) => {
              this.roleDetail = res.data
              this.roleTtsConfig = res.data.tts
              return resolve()
            },
            error: (err) => {
              reject()
            },
          }
        )
      })
    },
    getTtsConfig() {
      this.$utils.httpGet(
        this.$config.api.AIUI_BOT_ROLE_TTS_GET,
        {
          appid: this.$route.params.appId,
          sceneName: this.currentScene.sceneBoxName,
        },
        {
          success: async (res) => {
            if (res.data) {
              this.appTtsConfig = {
                ...res.data,
              }
            } else if (this.roleTtsConfig) {
              //之前没有配置的情况下（没有res.data）展示角色的配置
              this.appTtsConfig = this.roleTtsConfig
            } else {
              //角色和应用都没有配置的情况下
              this.appTtsConfig = {}
            }
          },
          error: (err) => {},
        }
      )
    },
    openDetail() {
      window.open(`/studio/role/${this.sceneRoleId}`, '_blank')
    },
    getDefaultVcn() {
      this.$utils.httpGet(
        this.$config.api.AIUI_BOT_ROLE_TTS_VCN,
        {
          appid: this.$route.params.appId,
          vcn: 'x4_lingxiaoqi_oral', //这里的vcn值是为了获取默认发音人，随便给的
        },
        {
          success: (res) => {
            this.defaultVcn = res.data.default
          },
          error: (err) => {},
        }
      )
    },
    checkAuth() {
      // 判断发音人是否有权限
      if (this.appTtsConfig && this.appTtsConfig.vcn) {
        if (this.appTtsConfig.resId) return true //复刻的声音
        return new Promise((resolve, reject) => {
          this.$utils.httpGet(
            this.$config.api.AIUI_BOT_ROLE_TTS_VCN,
            {
              appid: this.$route.params.appId,
              vcn: this.appTtsConfig.vcn,
            },
            {
              success: (res) => {
                this.defaultVcn = res.data.default
                if (!res.data.auth) {
                  // 没有权限
                  this.$message.warning(
                    '当前发音人没有授权，已更换为默认发音人'
                  )
                }
                return resolve(res.data.auth)
              },
              error: (err) => {
                return resolve(false)
              },
            }
          )
        })
      } else {
        // 角色未配置发音人
        this.$message.warning('当前角色未配置发音人，已更换为默认发音人')
        return false
      }
    },
    async changeMethod(type, val) {
      const formData = new FormData()
      formData.append('appid', this.$route.params.appId)
      formData.append('sceneName', this.currentScene.sceneBoxName)
      switch (type) {
        case 'enable':
          //先校验是否有权限，再给发音人相关参数
          formData.append('enable', val)
          if (val) {
            // 开启发音色需要检查授权
            let checkRes = await this.checkAuth()

            formData.append(
              'vcn',
              checkRes ? this.appTtsConfig.vcn : this.defaultVcn
            )
            formData.append('volume', this.appTtsConfig.volume || 50)
            formData.append('speed', this.appTtsConfig.speed || 50)
            if (this.appTtsConfig.resId) {
              formData.append('resId', this.appTtsConfig.resId)
            }
          }
          this.saveTtsConfig(formData)
          break
        case 'tone':
          this.ttsDialog.show = true
          break
        case 'tts':
          formData.append('enable', this.appTtsConfig.enable || 0)
          formData.append('volume', this.appTtsConfig.volume || 50)
          formData.append('speed', this.appTtsConfig.speed || 50)
          if (val.vcn) {
            formData.append('vcn', val.vcn)
          }
          if (val.resId) {
            formData.append('resId', val.resId)
            formData.append('vcn', 'x5_clone')
          }
          this.saveTtsConfig(formData)
          break
        case 'volume':
        case 'speed':
          if (!this.appTtsConfig.vcn) return
          formData.append('enable', this.appTtsConfig.enable || 0)
          formData.append('vcn', this.appTtsConfig.vcn)
          formData.append('volume', this.appTtsConfig.volume)
          formData.append('speed', this.appTtsConfig.speed)
          if (this.appTtsConfig.resId)
            formData.append('resId', this.appTtsConfig.resId)
          this.saveTtsConfig(formData)
          break
        default:
          break
      }
    },
    saveTtsConfig(formData) {
      this.$utils.httpPost(this.$config.api.AIUI_BOT_ROLE_TTS_SAVE, formData, {
        config: {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        },
        success: (res) => {
          this.getTtsConfig()
        },
        error: (err) => {
          this.$message.error(err.data)
          this.getTtsConfig()
          console.error('上传错误', err)
        },
      })
    },
    synthetic() {
      if (this.appTtsConfig.vcn === '' || !this.appTtsConfig.vcn) {
        this.$message.error('请先选择发音人')
        return
      }
      if (!this.appTtsConfig.text || this.appTtsConfig.text === '') {
        this.$message.error('试听文本不能为空')
        return
      }
      setTimeout(() => {
        this.testStatus = 1
      })
      let that = this
      this.ttsRecorder && this.ttsRecorder.resetAudio()
      const baseUrl = '/aiui/web/user/chat'
      let formData = new FormData()
      formData.append('version', 'vtts')
      formData.append('expUid', this.uid)
      formData.append('query', this.appTtsConfig.text)
      formData.append('speed', this.appTtsConfig.speed)
      formData.append('volume', this.appTtsConfig.volume)
      formData.append('vcn', this.appTtsConfig.vcn)
      if (this.appTtsConfig.vcn === 'x5_clone') {
        formData.append('resId', this.appTtsConfig.resId)
        formData.append('ttsType', 3)
      } else {
        formData.append('ttsType', this.appTtsConfig.ttsType)
      }
      try {
        fetchEventSource(baseUrl, {
          method: 'POST',
          openWhenHidden: true,
          body: formData,
          signal: abortController.signal,
          async onopen(response) {
            if (response.ok) {
              console.log('连接了')
              setTimeout(() => {
                that.testStatus = 2
              }, 1000)
            } else {
            }
          },

          onmessage(event) {
            try {
              const result = JSON.parse(event.data || '{}')
              // console.log('result----------------', result)
              // 处理每一条信息
              if (result.code == '300001') {
                that.$message.error('请先登录')
                setTimeout(() => {
                  window.location.href = '/user/login'
                }, 2000)
              } else if (result.code == '0') {
                const data = result.data
                that.handleMessage(data)
              } else {
                that.$message.error(result.desc || '未知错误')
                that.stop()
              }
            } catch (e) {
              console.log(e)
            }
          },
          onclose() {
            console.info('断开了')
          },
          onerror(err) {
            console.log('报错了', err)
            that.stop()
            throw new Error(err)
          },
        })
      } catch (e) {
        console.log('fetchEventSource e', e)
      }
    },
    handleMessage(data) {
      if (data.type === 'tts') {
        if (data.audio) {
          this.ttsRecorder.result(data.audio)
        }
      }
    },
    stop() {
      setTimeout(() => {
        this.ttsRecorder && this.ttsRecorder.audioStop()
        // this.ttsRecorder && this.ttsRecorder.resetAudio()
      }, 0)
      abortController.abort()
      this.testStatus = 0
    },
    ttsRecorderStatusCb(status) {
      // console.log('ttsRecorderStatusCb', status);
      this.ttsRecorderStatus = status
    },
  },
}
</script>
<style lang="scss" scoped>
@import '../common.scss';
.role-card {
  width: 100%;
  padding: 10px;
  background: #eff3f9;
  border-radius: 12px;
  display: flex;
  gap: 30px;
  margin-bottom: 30px;
  > img {
    height: 120px;
    border-radius: 6px;
  }
  ul {
    li {
      line-height: 30px;
      color: #555454;
      > label {
        display: inline-block;
        margin-right: 4px;
      }
    }
  }
}
.conf-title {
  display: flex;
  align-items: center;
}
.custom_form {
  :deep(.el-form-item__label) {
    font-weight: 400;
    color: #555454;
  }
}
.tone-card {
  width: 210px;
  height: 60px;
  padding: 6px 7px;
  display: inline-flex;
  align-items: center;
  border: 1px solid #ededed;
  border-radius: 6px;
  background: #ffffff;
  > img {
    width: 48px;
    height: 48px;
    border-radius: 6px;
  }
  > a {
    margin-left: auto;
  }
  .text {
    margin-left: 8px;
    display: inline-block;
    max-width: 105px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

:deep(.el-textarea) {
  position: relative;
  .el-input__count {
    color: #909399;
    position: absolute;
    font-size: 12px;
    bottom: -32px;
    right: 0px;
  }
}
</style>
