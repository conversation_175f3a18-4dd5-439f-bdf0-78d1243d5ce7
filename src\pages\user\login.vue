<template>
  <div class="container">
    <div class="ib container-left">
      <img :src="BannerImg" alt="login-register.png" />
    </div>
    <div class="ib container-right" @keyup.enter="submit">
      <div class="flex" style="margin-bottom: 96px">
        <a href="/" target="_self" class="container-right-logo fx1">
          <img class="vt-middle" alt="AIUI" :src="IconLogo" />
        </a>
        <router-link
          :to="{ name: 'user-register' }"
          class="fx1 text-black fs18 txt-al-r"
          style="line-height: 32px"
        >
          注册
        </router-link>
      </div>
      <p class="fs25 text-black ff-medium mgb12">登录</p>
      <p class="fs14 text-grey6 mgb40">讯飞云平台用户可直接登录</p>
      <el-tabs v-model="activeName">
        <el-tab-pane label="手机快捷登录" name="second"></el-tab-pane>
        <el-tab-pane label="账号密码登录" name="first"></el-tab-pane>
      </el-tabs>
      <template v-if="activeName === 'first'">
        <el-form
          :model="loginForm"
          label-position="top"
          :rules="rules"
          ref="loginForm"
          label-width="100px"
          class="login-reg-form mgb48"
        >
          <el-form-item label="账号" prop="username">
            <el-input
              type="input"
              v-model="loginForm.username"
              placeholder="请输入手机号/邮箱/用户名"
              auto-complete="off"
              tabindex="1"
            ></el-input>
          </el-form-item>
          <el-form-item label="密码" prop="password">
            <a
              href="https://passport.xfyun.cn/forget"
              target="_blank"
              class="input-topright-btn"
              >忘记密码?</a
            >
            <el-input
              type="password"
              v-model="loginForm.password"
              placeholder="请输入密码"
              auto-complete="off"
              tabindex="2"
            ></el-input>
          </el-form-item>
        </el-form>
        <div class="mgb24">
          <el-button
            class="login-btn"
            type="primary"
            :loading="submitLoading"
            @click="submit"
            tabindex="3"
          >
            {{ submitLoading ? '登录中...' : '登录' }}
          </el-button>
        </div>
        <p>
          没有账号，<router-link :to="{ name: 'user-register' }"
            >注册新用户</router-link
          >
        </p>
      </template>
      <template v-else>
        <el-form
          :model="quickLoginForm"
          label-position="top"
          :rules="rules"
          ref="quickLoginForm"
          label-width="100px"
          class="login-reg-form mgb48"
        >
          <!-- 解决浏览器自动填充 start -->
          <input
            type="text"
            name="clear"
            style="position: fixed; bottom: -9999px"
          />
          <input
            type="password"
            name="clear"
            style="position: fixed; bottom: -9999px"
          />
          <!-- 解决浏览器自动填充 end -->
          <el-form-item
            label="手机号"
            prop="mobile"
            :rules="[
              { required: true, message: '手机号不能为空', trigger: 'blur' },
              { validator: checkMobile, trigger: 'blur' },
            ]"
          >
            <el-input
              placeholder="手机号"
              v-model="quickLoginForm.mobile"
              class="input-with-select"
              autocomplete="off"
            >
              <el-select v-model="areaCode" slot="prepend" placeholder="请选择">
                <el-option
                  v-for="(area, index) in areas"
                  :key="index"
                  :label="'+' + area.key"
                  :value="area.key"
                ></el-option>
              </el-select>
            </el-input>
          </el-form-item>
          <el-form-item
            label="短信验证码"
            prop="code"
            :rules="[
              { required: true, message: '验证码不能为空', trigger: 'blur' },
              { validator: checkCode, trigger: 'blur' },
            ]"
          >
            <el-input
              placeholder="短信验证码"
              v-model="quickLoginForm.code"
              autocomplete="new-password"
            >
              <div
                slot="append"
                class="send-code-btn"
                :class="{ 'send-code-btn-primary': !second }"
                @click="sendCode"
              >
                {{ second ? second + 's后重发' : '发送验证码' }}
              </div>
            </el-input>
          </el-form-item>
        </el-form>
        <div>
          <el-checkbox v-model="checked" class="mgb16"
            ><div class="text-black">
              <span>未注册的手机号将自动注册。勾选即代表您同意并接受</span>
              <div>
                <a :href="`${$config.docs}doc-85/`" target="_blank"
                  >《AIUI开放平台服务协议》</a
                ><a :href="`${$config.docs}doc-86/`" target="_blank"
                  >《AIUI开放平台隐私政策》</a
                >
              </div>
            </div></el-checkbox
          >
        </div>

        <div class="mgb24">
          <el-button class="login-btn" type="primary" @click="quickSubmit"
            >登录</el-button
          >
        </div>
      </template>
    </div>
  </div>
</template>

<script>
import IconLogo from '@A/images/aiui/logo_aiui_black.png'
import BannerImg from '@A/images/banner-login-signup.png'
import areacode from '@M/areacode'
import SSO from 'sso/sso.js'
import api from '@U/api.js'

export default {
  name: 'user-login',
  data() {
    return {
      pageFrom: '',
      submitLoading: false,
      IconLogo: IconLogo,
      BannerImg: BannerImg,
      loginForm: {
        username: '',
        password: '',
      },
      rules: {
        username: [
          { required: true, message: '账号不能为空', trigger: 'blur' },
        ],
        password: [
          { required: true, message: '密码不能为空', trigger: 'blur' },
          { min: 6, message: '请至少输入 6 个字符', trigger: 'blur' },
        ],
        // code: [{ required: true, message: '验证码不能为空', trigger: 'blur' }],
        // code: [{ validator: checkCode, trigger: 'blur' }],
      },
      loginTimeout: null,
      activeName: 'second',
      quickLoginForm: {
        mobile: '',
        code: '',
      },
      second: 0,
      areas: areacode.areaCodeList.sort(function (arr1, arr2) {
        return parseInt(arr1.key) - parseInt(arr2.key)
      }),
      areaCode: '86',
      checked: false,
    }
  },
  created() {
    if (this.$route.query.pageFrom) {
      this.pageFrom = this.$route.query.pageFrom
    }
  },
  methods: {
    checkCode(rule, val, callback) {
      const regex = /^\d{6}$/
      const result = regex.test(val)
      if (!result) {
        callback(new Error('请输入6位数字组成的验证码'))
      } else {
        callback()
      }
    },
    checkMobile(rule, val, callback) {
      let self = this
      if (this.areaCode === '86' && !this.$utils.regTest(val, 'phone')) {
        return callback(new Error('请输入正确的手机号'))
      } else {
        callback()
      }
    },
    submit() {
      let self = this
      this.$utils.clearCookie()
      SSO.logout(function () {
        self.login()
      })
    },
    login() {
      let self = this
      let jump = this.$utils.toPage('/', 'aiui', 'none')
      /*if (this.pageFrom) {
        jump = this.pageFrom
      }*/
      /*if(window.location.port){
        //开发环境不跳线上地址
        jump = `${window.location.origin}/`
      }*/
      jump = `${window.location.origin}/`
      if (this.pageFrom) {
        jump = this.pageFrom
      }
      this.$refs.loginForm.validate((valid) => {
        if (valid) {
          self.submitLoading = true
          self.loginTimeout = setTimeout(function () {
            self.$message.error('登录超时，请重新登录')
            self.submitLoading = false
          }, 6000)
          SSO.login(
            {
              accountName: self.loginForm.username,
              accountPwd: self.loginForm.password,
              jump: jump,
              isAct: false,
            },
            (err) => {
              if (err.desc) {
                clearTimeout(self.loginTimeout)
                self.$message.error(err.desc)
                self.submitLoading = false
              }
            },
            (res, cb) => {
              clearTimeout(self.loginTimeout)
              let msg = self.$message.success('登录成功，正在跳转')
              api
                .userInfo()
                .then((res) => {
                  if (res.status === 200 && res.data.flag) {
                    setTimeout(() => {
                      cb && cb()
                    }, 500)
                  } else {
                  }
                })
                .catch(() => {})
            }
          )
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
    },

    quickSubmit() {
      let self = this
      this.$utils.clearCookie()
      SSO.logout(function () {
        self.quickLogin()
      })
    },
    quickLogin() {
      let self = this

      let jump = this.$utils.toPage('/', 'aiui', 'none')

      jump = `${window.location.origin}/`
      if (this.pageFrom) {
        jump = this.pageFrom
      }

      this.$refs.quickLoginForm.validate((valid) => {
        if (valid) {
          if (!self.checked) {
            return self.$message.warning(
              '请阅读并接受《AIUI开放平台用户服务协议》《AIUI开放平台隐私政策》'
            )
          }
          SSO.mobileLogin(
            {
              countryCode: self.areaCode,
              mobile: this.quickLoginForm.mobile,
              verifyCode: this.quickLoginForm.code,
              jump,
            },
            (err) => {
              if (err.desc) {
                clearTimeout(self.loginTimeout)
                self.$message.error(err.desc)
                self.submitLoading = false
              }
            },
            (res, cb) => {
              console.log('mobileLogin', res, cb)
              clearTimeout(self.loginTimeout)
              let msg = self.$message.success('登录成功，正在跳转')
              api
                .userInfo()
                .then((res) => {
                  if (res.status === 200 && res.data.flag) {
                    setTimeout(() => {
                      cb && cb()
                    }, 500)
                  } else {
                  }
                })
                .catch(() => {})
            }
          )
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },

    sendCode() {
      if (this.second > 0) {
        return
      }
      this.$refs.quickLoginForm.validateField('mobile', (valid) => {
        if (!valid) {
          this.sendMsg()
        }
      })
    },
    sendMsg() {
      let self = this
      let data = {
        countryCode: self.areaCode,
        mobile: self.quickLoginForm.mobile,
      }
      SSO.getRegisterMobileCode(data, (result) => {
        let res = typeof result === 'string' ? JSON.parse(result) : resut
        if (res.code === 0) {
          self.$message.success('验证码已发送')
          let time = 60
          var timer = null
          self.second = time
          self.sendCodeLock = true
          timer = setInterval(function () {
            self.second = --time
            if (time <= 0) {
              self.sendCodeLock = false
              clearInterval(timer)
            }
          }, 1000)
        } else {
          self.$message.warning(res.desc)
        }
      })
    },
  },
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="scss" scoped>
.send-code-btn {
  width: 70px;
  background: #fff;
  text-align: center;
  &-primary {
    color: #1784e9;
    cursor: pointer;
  }
}
.container {
  width: 100%;
  height: 100%;
  &-left {
    width: 100%;
    height: 100%;
    padding-right: 520px;
    background-color: #0d2a96;
    display: flex;
    align-items: center;
    justify-content: center;
    & img {
      width: 582px;
      height: 582px;
    }
  }
  &-right {
    position: fixed;
    width: 520px;
    height: 100%;
    padding: 40px 80px;
    top: 0;
    right: 0;
    background-color: #fff;
    overflow-y: scroll;
    &-logo {
      align-items: center;
      display: flex;
      & img {
        width: 154px;
      }
    }
  }
  .input-topright-btn {
    position: absolute;
    right: 0;
    top: -26px;
    line-height: 22px;
  }
  .login-btn {
    width: 100%;
    height: 52px;
    font-size: 16px;
    :deep(span) {
      height: 100%;
      display: inline-block;
      line-height: 26px;
    }
  }
}
@media screen and (max-width: 519px) {
  .container {
    width: 100%;
    height: 100%;
    &-left {
      display: none;
    }
    &-right {
      width: 100%;
      height: 100%;
      padding: 20px 24px;
      background-color: #fff;
    }
  }
}
</style>
