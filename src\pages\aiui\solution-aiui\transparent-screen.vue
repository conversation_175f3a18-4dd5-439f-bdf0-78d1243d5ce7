<template>
  <div class="main-content">
    <section class="main-content-banner">
      <div class="banner-text">
        <h2><span>多语种AI透明屏</span></h2>
        <p class="banner-text-content">
          解决公共服务窗口跨语言沟通难题<br />「 透明无界 沟通无限 」
        </p>

        <div class="banner-text-button" @click="toConsole">合作咨询</div>
      </div>
    </section>
    <div class="section-0-wrap">
      <section class="section section-0">
        <div class="section-title">
          <span class="section-title-bold">解决服务窗口跨语言沟通难题</span>
        </div>
        <div class="video-container" @click="isPlaying = true">
          <div v-show="isPlaying" class="case_video_player">
            <!-- <div class="case_video_player_inner">
          <video :src="videoSrc" autoplay preload controls></video>
        </div> -->
            <video ref="videoPlayer" :src="videoSrc" preload controls></video>
          </div>
        </div>
      </section>
    </div>

    <div class="section-10-wrap">
      <section class="section section-10">
        <div class="section-title">
          <span class="section-title-bold">实物图片</span>
        </div>
        <div class="image-container">
          <div class="matter matter-a"></div>
          <div class="matter matter-b"></div>
        </div>
      </section>
    </div>

    <section class="section section-1">
      <div class="section-title">
        <span class="section-title-bold">应用场景</span>
      </div>
      <div class="section-desc">适用于以下场景的服务窗口</div>
      <ul class="interact-list">
        <li
          v-for="(item, index) in interactList"
          :key="index"
          :class="item.klass"
        >
          <h1>{{ item.name }}</h1>
        </li>
      </ul>
    </section>

    <div class="section-2-wrap">
      <section class="section section-2">
        <div class="section-title">
          <span class="section-title-bold">方案介绍</span>
        </div>
        <ul class="advantage">
          <li>
            <div class="advantage-text">
              <p class="advantage-tag-wrap">
                <span class="advantage-tag-text"
                  >实时多语种识别翻译，跨越语言障碍</span
                >
              </p>
              <ul>
                <li>
                  <i class="tick"></i>
                  <span class="tick-text">集成多语种识别与翻译功能</span>
                </li>
                <li>
                  <i class="tick"></i>
                  <span class="tick-text"
                    >支持中文、英语、法语、阿拉伯语等多语言即时互译</span
                  >
                </li>
              </ul>
            </div>
            <div class="advantage-image"></div>
          </li>
          <li>
            <div class="advantage-image"></div>
            <div class="advantage-text">
              <p class="advantage-tag-wrap">
                <span class="advantage-tag-text"
                  >数字人客服模式，提供全天候问答服务</span
                >
              </p>
              <ul>
                <li>
                  <i class="tick"></i>
                  <span class="tick-text"
                    >24小时不间断服务，基于星火大模型提供快速响应的智能问答</span
                  >
                </li>
                <li>
                  <i class="tick"></i>
                  <span class="tick-text"
                    >支持热门问题快速配置，实时更新回复内容</span
                  >
                </li>
                <li>
                  <i class="tick"></i>
                  <span class="tick-text"
                    >智能问答知识库可按需定制，提供精准服务</span
                  >
                </li>

                <li>
                  <i class="tick"></i>
                  <span class="tick-text">虚拟人形象与发音可定制</span>
                </li>
              </ul>
            </div>
          </li>
        </ul>
      </section>
    </div>

    <div class="section-3-wrap">
      <section class="section section-3">
        <div class="section-title">
          <span class="section-title-bold">方案优势</span>
        </div>
        <ul class="advantage">
          <li>
            <div class="advantage-text">
              <p class="advantage-tag-wrap">
                <span class="advantage-tag-text"
                  >多模态降噪技术，适应复杂环境</span
                >
              </p>
              <ul>
                <li>
                  <span class="tick-text">支持人脸识别、唇形和声纹识别</span>
                </li>
                <li>
                  <span class="tick-text"
                    >在嘈杂环境中有效降低背景噪音，提高语音识别准确率</span
                  >
                </li>
              </ul>
            </div>
            <div class="advantage-image"></div>
          </li>
          <li>
            <div class="advantage-image"></div>
            <div class="advantage-text">
              <p class="advantage-tag-wrap">
                <span class="advantage-tag-text"
                  >双面透明显示，打造自然互动体验</span
                >
              </p>
              <ul>
                <li>
                  <span class="tick-text">采用双面透明屏设计</span>
                </li>
                <li>
                  <span class="tick-text">实现无障碍的自然交互</span>
                </li>
                <li>
                  <span class="tick-text">适用于各类窗口服务场景</span>
                </li>
              </ul>
            </div>
          </li>
          <li>
            <div class="advantage-text">
              <p class="advantage-tag-wrap">
                <span class="advantage-tag-text"
                  >个性化配置，多业务场景兼容</span
                >
              </p>
              <ul>
                <li>
                  <span class="tick-text">支持定制虚拟人形象</span>
                </li>
                <li>
                  <span class="tick-text">定制智能问答知识库</span>
                </li>
              </ul>
            </div>
            <div class="advantage-image"></div>
          </li>
        </ul>
      </section>
    </div>

    <section class="section section-4">
      <div class="section-title">
        <span class="section-title-bold">硬件参数</span>
      </div>
      <div class="section-desc">多语种AI透明屏规格参数</div>
      <ul class="params">
        <li v-for="(item, index) in params" :key="index">
          <div :class="item.klass"></div>
          <p>{{ item.line1 }}</p>
          <p>{{ item.line2 }}</p>
        </li>
      </ul>
    </section>

    <!-- <corp @jump="toConsole">
      <template> 免费咨询专属顾问 为您量身定制产品推荐方案</template>
    </corp> -->
    <!-- <div class="corp-button-wrap">
      <div class="corp-button" @click="toConsole">合作咨询</div>
    </div> -->
    <corp @jump="toConsole">
      <template>提交信息，我们会尽快与您联系</template>
    </corp>
  </div>
</template>

<script>
import corp from '@P/aiui/solution-aiui/components/corp3.vue'
import videoPlayer from '@C/videoPlayer/index'

export default {
  name: 'multimodal-interaction',
  data() {
    return {
      videoSrc:
        'https://aiui-file.cn-bj.ufileos.com/video/transparent_screen.mp4',
      interactList: [
        {
          name: '酒店',
          desc: '',
          klass: 'img_full_duplex',
        },
        {
          name: '交通轨道',
          desc: '',
          klass: 'img_free_wake_click',
        },
        {
          name: '机场',
          desc: '',
          klass: 'img_multimodal_click',
        },
        {
          name: '旅游景区',
          desc: '',
          klass: 'img_offlineinteraction_click',
        },
      ],
      isPlaying: false,

      params: [
        {
          klass: 'param1',
          line1: '显示尺寸',
          line2: '20.8寸',
        },
        {
          klass: 'param2',
          line1: '显示分辨率',
          line2: '1280*720',
        },
        {
          klass: 'param3',
          line1: '亮度',
          line2: '300cd/㎡',
        },
        {
          klass: 'param4',
          line1: '电源输入',
          line2: 'DC 20V',
        },
        {
          klass: 'param5',
          line1: '外形尺寸',
          line2: '564*315*180(mm)',
        },
        {
          klass: 'param6',
          line1: '整机功耗',
          line2: '< 40W',
        },
        {
          klass: 'param7',
          line1: '工作温度',
          line2: '0℃~40℃',
        },
      ],
    }
  },

  watch: {
    isPlaying(newVal) {
      if (newVal && this.$refs.videoPlayer) {
        // 当isPlaying变为true时，手动播放视频
        this.$nextTick(() => {
          this.$refs.videoPlayer.play().catch((err) => {
            console.error('自动播放失败:', err)
            // 一些浏览器可能会阻止自动播放，这里可以添加处理逻辑
          })
        })
      }
    },
  },
  mounted() {},
  methods: {
    toConsole() {
      // 判断是否有百度SEM营销参数，有的话，提交表单页需要带上
      const search = window.location.search
      if (search) {
        window.open(`/solution/apply/35${search}`)
      } else {
        window.open('/solution/apply/35')
      }
    },
    getWindowHeight() {
      return 'innerHeight' in window
        ? window.innerHeight
        : document.documentElement.offsetHeight
    },

    playVideo() {
      // let height = Math.min(this.getWindowHeight() * 0.9, 562)
      // let width = parseInt((1920 * height) / 1080)
      // videoPlayer({
      //   width,
      //   height,
      //   videoSrc: this.videoSrc,
      //   videoStyle: {
      //     width: `${width}px`,
      //     height: `${height}px`,
      //     'box-sizing': 'border-box',
      //     'margin-left': `-${width * 0.5}px`,
      //     'margin-top': `-${height * 0.5}px`,
      //   },
      // })
      this.isPlaying = true
    },
  },
  components: { corp },
}
</script>

<style lang="scss" scoped>
.main-content {
  &-banner {
    background: url(~@A/images/solution/transparent-screen/banner.png) center
      no-repeat;
    background-size: cover;
    height: 576px;
    overflow: hidden;
    width: 100%;
    .banner-text {
      position: relative;
      max-width: 1200px;
      height: 100%;
      margin: auto;

      &-button {
        font-size: 20px;
        text-align: center;
        font-weight: 400;
        line-height: 60px;
        color: #fff;
        cursor: pointer;
        width: 160px;
        height: 60px;
        background: linear-gradient(112deg, #5594ff 23%, #2274ff 75%);
        border-radius: 4px;
      }

      h2 {
        padding-top: 148px;
        margin-bottom: 20px;

        // font-size: 44px;
        // font-weight: 600;
        text-align: left;
        // line-height: 54px;
        span {
          font-size: 48px;
          font-weight: 600;
          text-align: left;
          line-height: 54px;
          color: #000;
        }

        // background-image: linear-gradient(270deg,#1f6efd,#1ebce8);
      }

      .banner-text-content {
        font-size: 16px;
        font-weight: 400;
        text-align: left;
        color: #000;
        line-height: 31px;
        margin-bottom: 100px;
      }
    }
  }

  .section {
    p,
    ul {
      margin-bottom: 0;
    }
    max-width: 1200px;
    overflow: hidden;
    margin: 0 auto;
    .section-title {
      text-align: center;
      font-size: 34px;
      font-family: SourceHanSansSC-Medium, SourceHanSansSC;
      font-weight: bold;
      color: #333;
      line-height: 34px;
      position: relative;
      // width: 200px;
      margin: 0 auto;
      .arrow {
        width: 160px;
        height: 8px;
        display: inline-block;
        background-repeat: no-repeat;
        background-size: contain;
        position: absolute;
        z-index: 1;
      }
      .arrow-left {
        background-position: left;
        top: 50%;
        left: -160px;
      }
      .arrow-right {
        background-position: right;
        top: 50%;
        right: -160px;
      }
    }
    .section-desc {
      text-align: center;
      margin-top: 30px;
      font-size: 16px;
      font-weight: 400;
      color: #666666;
      line-height: 25px;
    }
    .section-title-bold {
      font-size: 40px;
      font-weight: 500;
      color: #333;
      line-height: 44px;
    }

    .section-tabs {
      margin-top: 70px;
      ul {
        display: flex;
        justify-content: center;
      }
      li {
        font-size: 20px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #666;
        line-height: 30px;
        position: relative;
        cursor: pointer;
        &:hover {
          color: #1f90fe;
        }
        &.active {
          color: #1f90fe;
          &::before {
            position: absolute;
            content: ' ';
            z-index: 1;
            width: 180px;
            height: 4px;
            background: #1f90fe;
            border-radius: 2px;
            bottom: -32px;
            left: -48px;
          }
          &::after {
            position: absolute;
            content: ' ';
            display: inline-block;
            width: 0;
            height: 0;
            bottom: -100px;
            left: 50%;
            transform: translateX(-50%);
            border: 46px solid;
            border-color: transparent transparent #f4f7f9;
          }
        }
      }
      li + li {
        margin-left: 165px;
      }
    }
    .section-sub-title {
      text-align: center;
      font-size: 18px;
      font-weight: 400;
      color: #777777;
      line-height: 25px;
      margin-top: 42px;
    }
    .section-sub-title-spec {
      font-size: 17px;
      font-weight: 400;
      color: #999999;
      line-height: 28px;
      margin-top: 18px;
    }
  }
  .section-0-wrap {
    background: url(~@A/images/solution/transparent-screen/bg1.png) center/cover
      no-repeat;
    padding-bottom: 41px;
  }

  .section-0 {
    .section-title {
      padding-top: 115px;
    }

    .video-container {
      width: 1028px;
      height: 611px;
      background: url(~@A/images/solution/transparent-screen/<EMAIL>)
        center/100% no-repeat;
      margin: 68px auto 0;
      cursor: pointer;
      position: relative;
    }
    .case_video_player {
      width: 961px;
      height: 540px;
      position: absolute;
      top: 49%;
      left: 50.8%;
      transform: translate(-50%, -50%);
      video {
        width: 100%;
        height: 100%;
      }
      .case_video_player_inner {
      }
    }
  }

  .section-10-wrap {
    background: url(~@A/images/solution/transparent-screen/bg10.png)
      center/cover no-repeat;
    padding-bottom: 74px;
  }

  .section-10 {
    .section-title {
      padding-top: 90px;
    }
    .image-container {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 54px;
      .matter {
        width: 503px;
        height: 350px;
      }
      .matter-a {
        background: url(~@A/images/solution/transparent-screen/<EMAIL>)
          center/100% no-repeat;
      }
      .matter-b {
        background: url(~@A/images/solution/transparent-screen/<EMAIL>)
          center/100% no-repeat;
      }
    }
  }

  .section-2-wrap {
    padding: 80px 0 82px 0;
    background: url(~@A/images/solution/transparent-screen/bg2.png) center/cover
      no-repeat;
    margin-top: 140px;
  }
  .section-2 {
    p {
      margin-bottom: 0;
    }

    .advantage {
      margin-top: 60px;
      > li {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      > li:nth-child(1) {
        background: linear-gradient(180deg, #f3f4f9, #fbfbfb 55%);
        border: 3px solid #ffffff;
        box-shadow: 0px 1px 16px 5px rgba(175, 175, 175, 0.13);
        padding: 17px 52px 0 52px;
        .advantage-image {
          width: 520px;
          height: 351px;
          background: url(~@A/images/solution/transparent-screen/<EMAIL>)
            center/contain no-repeat;
        }
        .advantage-text {
          // margin-right: 250px;
        }
      }
      > li:nth-child(2) {
        padding: 14px 20px 0 20px;
        margin-top: 50px;
        background: linear-gradient(180deg, #f2f4f8, #ffffff 50%);
        border: 3px solid #ffffff;
        box-shadow: 0px 1px 16px 5px rgba(175, 175, 175, 0.13);
        .advantage-image {
          width: 529px;
          height: 354px;
          background: url(~@A/images/solution/transparent-screen/<EMAIL>)
            center/contain no-repeat;
        }
        .advantage-text {
          // padding-top: 91px;
          // margin-left: 302px;
        }
      }
    }
    .advantage-text {
      .tick {
        width: 16px;
        height: 16px;
        display: inline-block;
        background: url(~@A/images/solution/transparent-screen/<EMAIL>)
          center/contain no-repeat;
        margin-right: 17px;
      }

      width: 570px;
      p {
        font-size: 30px;
        font-weight: 600;
        color: #000;
        height: 30px;
        min-width: 200px;
        position: relative;
      }

      ul {
        margin-top: 40px;
        li {
          font-size: 16px;
          font-weight: 400;
          color: #262b4f;
          line-height: 40px;
          white-space: nowrap;
          display: flex;
          align-items: center;
        }
      }
    }
  }

  .section-3-wrap {
    padding: 80px 0 82px 0;
    background: #eff7ff;
    background: url(~@A/images/solution/transparent-screen/bg3.png) center/cover
      no-repeat;
  }
  .section-3 {
    p {
      margin-bottom: 0;
    }
    // margin-top: 50px;

    .advantage {
      margin-top: 60px;
      > li {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      > li:nth-child(1) {
        .advantage-image {
          width: 590px;
          height: 342px;
          background: url(~@A/images/solution/transparent-screen/<EMAIL>)
            center/contain no-repeat;
        }
        .advantage-text {
          // margin-right: 250px;
        }
      }
      > li:nth-child(2) {
        margin-top: 50px;
        .advantage-image {
          width: 593px;
          height: 341px;
          background: url(~@A/images/solution/transparent-screen/<EMAIL>)
            center/contain no-repeat;
        }
        .advantage-text {
          // padding-top: 91px;
          // margin-left: 302px;
        }
      }
      > li:nth-child(3) {
        margin-top: 8px;

        .advantage-image {
          width: 696px;
          height: 436px;
          background: url(~@A/images/solution/transparent-screen/<EMAIL>)
            center/contain no-repeat;
        }
        .advantage-text {
          // margin-right: 250px;
        }
      }
    }
    .advantage-text {
      .tick {
        width: 30px;
        height: 30px;
        display: inline-block;
        background: url(~@A/images/solution/multimodal-interaction/tik.png)
          center/contain no-repeat;
        margin-right: 17px;
      }

      width: 472px;
      p {
        font-size: 30px;
        font-weight: 600;
        color: #000;
        height: 30px;
        min-width: 200px;
        position: relative;
      }

      ul {
        margin-top: 40px;
        li {
          font-size: 20px;
          font-weight: 400;
          color: #262b4f;
          line-height: 40px;
          white-space: nowrap;
          display: flex;
          align-items: center;
        }
      }
    }
  }

  .section-1 {
    padding-top: 80px;
    .interact-list {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin: 50px auto 0;

      li {
        position: relative;
        text-align: center;
        width: 286px;
        height: 371px;

        h1 {
          text-align: center;
          font-size: 18px;
          font-weight: 400;
          color: #fff;
          line-height: 21px;
          margin: 0 auto;
          position: relative;
          padding-top: 53px;
        }

        &.img_full_duplex {
          background: url(~@A/images/solution/transparent-screen/<EMAIL>)
            center/100% no-repeat;
        }
        &.img_free_wake_click {
          background: url(~@A/images/solution/transparent-screen/<EMAIL>)
            center/100% no-repeat;
        }
        &.img_multimodal_click {
          background: url(~@A/images/solution/transparent-screen/<EMAIL>)
            center/100% no-repeat;
        }
        &.img_offlineinteraction_click {
          background: url(~@A/images/solution/transparent-screen/<EMAIL>)
            center/100% no-repeat;
        }
      }
    }
  }

  .section-4 {
    padding-top: 60px;
    .params {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      grid-template-rows: auto auto;
      gap: 60px; /* 可根据需求调整间距 */
      padding: 78px 0 112px 0;
      li {
        text-align: center;
        div {
          width: 96px;
          height: 96px;
          margin: 0 auto 12px;
          &.param1 {
            background: url(~@A/images/solution/transparent-screen/<EMAIL>)
              center/100% no-repeat;
          }
          &.param2 {
            background: url(~@A/images/solution/transparent-screen/<EMAIL>)
              center/100% no-repeat;
          }
          &.param3 {
            background: url(~@A/images/solution/transparent-screen/<EMAIL>)
              center/100% no-repeat;
          }
          &.param4 {
            background: url(~@A/images/solution/transparent-screen/<EMAIL>)
              center/100% no-repeat;
          }
          &.param5 {
            background: url(~@A/images/solution/transparent-screen/<EMAIL>)
              center/100% no-repeat;
          }
          &.param6 {
            background: url(~@A/images/solution/transparent-screen/<EMAIL>)
              center/100% no-repeat;
          }
          &.param7 {
            background: url(~@A/images/solution/transparent-screen/<EMAIL>)
              center/100% no-repeat;
          }
        }
        p {
          margin-bottom: 0;
          line-height: 30px;
          font-size: 20px;
          color: #000;
        }
      }
    }
  }
}

.advantage-tag {
  width: 140px;
  height: 12px;
  background: linear-gradient(270deg, rgba(37, 185, 245, 0) 0%, #2bb6d7 100%);
  position: absolute;
  z-index: 1;
  left: 0;
  bottom: 0;
}
.advantage-tag-text {
  position: absolute;
  z-index: 2;
  left: 0;
  bottom: 0;
}

.corp-button-wrap {
  padding: 84px 0;
  background: #f4f7ff;
  margin: 0 auto;
}
.corp-button {
  width: 163px;
  height: 60px;
  background: linear-gradient(90deg, #26bff5 0%, #1a7af6 100%);
  border-radius: 60px 60px 60px 60px;
  line-height: 60px;
  font-weight: 600;
  font-size: 20px;
  color: #ffffff;
  text-align: center;
  margin: 0 auto;
  cursor: pointer;
}
</style>
