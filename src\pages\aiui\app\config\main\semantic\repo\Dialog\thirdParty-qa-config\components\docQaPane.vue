<template>
  <CommonDialogBody
    abbBtnText="创建问答库"
    tagText="文档问答"
    jumpUrl="/studio/qaBank"
    :scrollHeight="316"
    switchKey="selected"
    :ragData="ragData"
    :ragDataCopy="ragDataCopy"
    :loading="loading"
    @selectchange="onSelectchange"
    @toDocCard="toDocCard"
  >
    <template #search>
      <el-input
        size="medium"
        class="search-area"
        placeholder="搜索"
        v-model.trim="searchVal"
        @keyup.enter.native="searchAppConfig"
        style="width: 267px"
      >
        <i
          @click.stop.prevent="searchAppConfig"
          slot="suffix"
          class="el-input__icon el-icon-search search-area-btn"
        />
      </el-input>
    </template>
    <template #config>
      <search-config
        :form="configForm"
        @change="onConfigChange"
      ></search-config>
    </template>
  </CommonDialogBody>
</template>

<script>
import { mapGetters } from 'vuex'
import searchConfig from './searchConfig.vue'
import CommonDialogBody from '../../commonDialogBody/index.vue'

export default {
  name: 'SentenceQaPane',
  components: { searchConfig, CommonDialogBody },
  props: {
    appId: '',
    currentScene: Object,
  },
  data() {
    return {
      // 添加防抖保存方法
      debouncedSave: null,
      searchVal: '',
      ragData: [],
      ragDataCopy: [],
      originData: [],
      loading: false,
      channel: '',
      configForm: {
        channel: 2,
        threshold: 0.1,
      },
      hasChanged: false,
    }
  },

  created() {
    this.getAppRagConfig()
    this.debouncedSave = this.$utils.debounce(() => this.saveData(), 500)
  },
  computed: {
    ...mapGetters({
      skillIconBgColors: 'studioSkill/skillIconBgColors',
    }),
  },
  methods: {
    toDocCard(item) {
      console.log('toDocCard', item)
      window.open(`/studio/ragqa/${item.id}/localDoc`, '_blank')
    },

    onConfigChange(type, val) {
      this.configForm[type] = val
      // 无感保存检索方法和相关性阈值
      // this.debouncedSave() // 先不要无感保存

      // 直接标记有变化并通知父组件
      this.hasChanged = true
      this.$emit('dataChanged', true)
    },

    getAppRagConfig() {
      let that = this
      this.loading = true

      this.$utils.httpGet(
        this.$config.api.AIUI_BOT_CONFIG_GET_BOTRAGREPOS,
        {
          botId: this.currentScene.botBoxId,
          pageIndex: 1,
          pageSize: 1000,
        },
        {
          success: (res) => {
            console.log(res, '这个是知识库的res')
            that.loading = false
            let newRepos = (res.data.repos || []).map(({ repoid, ...rest }) => {
              return {
                ...rest,
                repoId: repoid,
              }
            })

            const colors = this.skillIconBgColors
            newRepos.forEach((item, index) => {
              item.color = colors[index % colors.length]
            })

            that.originData = JSON.parse(JSON.stringify(newRepos))
            that.ragDataCopy = JSON.parse(JSON.stringify(newRepos))
            that.configForm.channel = res.data.channel
            that.configForm.threshold = Number(res.data.threshold)
            that.ragData = newRepos || []
          },
          error: (res) => {},
        }
      )
    },
    searchAppConfig() {
      this.ragData = this.ragDataCopy.filter((it) =>
        it.name.includes(this.searchVal)
      )
    },
    saveData() {
      let that = this
      const addRepos = []
      const delRepos = []
      const updateRepos = []

      // 创建一个原始数据的映射，方便查找
      const originMap = {}
      this.originData.forEach((item) => {
        originMap[item.id] = item
      })

      // 遍历当前表格数据，找出变更
      this.ragData.forEach((currentItem) => {
        const originItem = originMap[currentItem.id]

        if (!originItem) {
          // 如果是新增的数据（如果有这种情况）
          addRepos.push(currentItem)
          return
        }

        // 检查selected字段的变化
        if (currentItem.selected !== originItem.selected) {
          if (currentItem.selected) {
            // 从未选中变为选中 -> 添加到addRepos
            addRepos.push(currentItem)
          } else {
            // 从选中变为未选中 -> 添加到delRepos
            delRepos.push(currentItem)
          }
        } else {
          // selected没有变化，检查isTop是否有变化
          if (currentItem.threshold !== originItem.threshold) {
            // 只有isTop变化 -> 添加到updateRepos
            updateRepos.push(currentItem)
          }
        }
      })

      const params = {
        botId: this.currentScene.botBoxId,
        channel: this.configForm.channel,
        threshold: this.configForm.threshold,
        addRepos,
        updateRepos,
        delRepos,
      }

      this.$utils.httpPost(
        this.$config.api.AIUI_BOT_CONFIG_SAVE_RAGREOPCONFIG,
        JSON.stringify(params),
        {
          config: {
            headers: {
              'Content-Type': 'application/json',
            },
          },
          success: (res) => {
            // this.getAppRagConfig() // 先不要无感保存
            that.$emit('saveSuccess')
            that.$message.success('保存成功')
          },
          error: (err) => {
            console.log('err', err)
            // reject(err.desc)
            that.$message.error(err)
          },
        }
      )
    },

    onSelectchange(item, isSelected) {
      const id = item.id
      this.ragData = this.ragData.map((item) => {
        if (item.id === id) {
          return {
            ...item,
            selected: isSelected,
          }
        } else {
          return { ...item }
        }
      })
      this.ragDataCopy = this.ragDataCopy.map((item) => {
        if (item.id === id) {
          return {
            ...item,
            selected: isSelected,
          }
        } else {
          return { ...item }
        }
      })

      // 直接标记有变化并通知父组件
      this.hasChanged = true
      this.$emit('dataChanged', true)
    },
  },
}
</script>

<style lang="scss" scoped></style>
