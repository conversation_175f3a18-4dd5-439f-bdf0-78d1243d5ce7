<template lang="html">
  <div class="aiui">
		<div class="banner-wrap">
			<div class="content-wrap">
				<div class="banner-content">
					<p class="banner-title">讯飞魔飞智能麦克风 MORFEI 2.0</p>
					<p class="banner-desc">功能全面升级，性能高效优化，语音全链路开放，打造快如闪电的成品级开发平台。</p>
				</div>
				<a class="buy-btn" :href="buyUrl" target="_blank">立即购买</a>
				<a class="buy-btn download-doc" href="https://aiui-file.cn-bj.ufileos.com/morfei2.zip" target="_self">资料下载</a>
				<!-- <ul class="tab">
					<li><a href="javascript:void(0);" class="active">讯飞魔飞智能麦克风 MORFEI 2.0</a></li>
					<li><a href="/solutions/hardware">AIUI 评估板</a></li>
				</ul> -->
			</div>
		</div>
		<div class="type-wrap">
			<div class="content-wrap">
				<div class="common-title"><span>｛</span>成品级开发套件<span>｝</span></div>
				<div class="product-wrap">
					<ul class="product-advantages">
						<li class="product-advantages-wrap">
							<div class="advantages-hide">
								<p>双环八麦 立体拾音</p>
								<p>业界首款双环立体麦克风，可以拾取<br/>直径10米球型范围内的有效声源。</p>
							</div>
							<div class="advantages-block">
								<i class="product-icons product-advantages-icon1"></i>
								<p>双环八麦 立体拾音</p>
								<ul>
									<li>立体八麦克风阵列构型</li>
									<li>10米球型场景拾音</li>
								</ul>
							</div>
						</li><li class="product-advantages-wrap">
							<div class="advantages-hide">
								<p>高效降噪 精准识别</p>
								<p>具备回声消除能力，可去除设备自身<br/>播放对语音交互的影响，实现语音实<br/>时交互和打断，可支持最低信噪比为<br/>-25DB，高识别率。</p>
							</div>
							<div class="advantages-block">
								<i class="product-icons product-advantages-icon2"></i>
								<p>高效降噪 精准识别</p>
								<ul>
									<li>支持信噪比-25dB</li>
									<li>高识别率</li>
								</ul>
							</div>
						</li><li class="product-advantages-wrap">
							<div class="advantages-hide">
								<p>支持AIUI 解析语义</p>
								<p>后台对接AIUI开放平台，支持上百种<br/>不同技能。开发者可根据实际场景需<br/>求使用或自定义各类技能，准确理解<br/>用户语音命令。</p>
							</div>
							<div class="advantages-block">
								<i class="product-icons product-advantages-icon3"></i>
								<p>支持 AIUI 解析语义</p>
								<ul>
									<li>可配置多种技能</li>
									<li>准确理解不同场景下的语义</li>
								</ul>
							</div>
						</li><li class="product-advantages-wrap">
							<div class="advantages-hide">
								<p>平台配置 快速开发</p>
								<p>高度成熟的软硬件一体方案，仅需在<br/>平台简单配置，完成设备调试，数小<br/>时内即可完成Demo。</p>
							</div>
							<div class="advantages-block">
								<i class="product-icons product-advantages-icon4"></i>
								<p>平台配置 快速开发</p>
								<ul>
									<li>成品级套件</li>
									<li>30min平台快速配置 迅捷开发</li>
								</ul>
							</div>
						</li>
					</ul>
				</div>
			</div>
		</div>
		<div class="advantage-wrap1">
			<div class="content-wrap">
				<div class="common-title"><span>｛</span>新能力 支持云函数<span>｝</span></div>
				<div class="advantage-wrap1-desc">
					<p><i></i>提供FaaS级云服务，无需服务器即可在云端编写业务逻辑。</p>
					<p><i></i>实现多轮对话，可基于上下文内容进行语义理解，使交互更加自然。</p>
					<p><i></i>进一步缩短开发周期，快速搭建各类业务场景。</p>
				</div>
			</div>
		</div>
		<div class="advantage-wrap2">
			<div class="content-wrap">
				<div class="common-title"><span>｛</span>新版本 全链路接口开放<span>｝</span></div>
				<ul class="interfaces">
					<li class="new-interface interfaces-audio">
						<i></i>
						<p>音频接口</p>
						<p>降噪音频接口开放<br/>提供纯净音频</p>
					</li><li class="new-interface interfaces-word">
						<i></i>
						<p>文本接口</p>
						<p>转写文本接口开放<br/>实时输出语音文本</p>
					</li><li class="new-interface interfaces-semantic">
						<i></i>
						<p>语义接口</p>
						<p>语义接口开放<br/>局域网内同步语义结果</p>
					</li><li class="new-interface interfaces-compose">
						<i></i>
						<p>合成接口</p>
						<p>合成接口开放<br/>云+端分布式合成内容</p>
					</li>
				</ul>
			</div>
		</div>
		<div class="advantage-wrap3">
			<div class="content-wrap">
				<div class="common-title"><span>｛</span>新特性 声纹唤醒 + 能量跟随<span>｝</span></div>
				<div class="new-voice">
					<p>声纹唤醒  让魔飞闻声识人</p>
					<p>唤醒设备后，设备将迅速识别已注册的用<br/>户身份信息开放声纹接口，开发者可根据<br/>声纹信息，推送个性化服务。</p>
				</div>
				<div class="new-power">
					<p>能量跟随  使魔飞如影随形</p>
					<p>唤醒设备后，设备的拾音波束将自动跟随<br/>用户所在位置，准确收集语音命令。</p>
				</div>
			</div>
		</div>
		<div class="qa">
			<ul class="qa_content">
				<li class="li_q" >购买事宜</li>
				<li class="li_a li_sort">
					<ol>
						<li>点击购买按钮后，即可直接购买评估版，购买时需要创建应用。评估版最多支持100套的购买。当需要购买超过100套的评估版，或者购买量产版时，请联系商务。</li>
						<li>商务合作电话：19955108393、19955108395。</li>
						<li>付款成功后会在七个工作日内发货（节假日顺延）。</li>
					</ol>
				</li>
				<li class="li_q">评估版与量产版的区别</li>
				<li class="li_a li_sort" style="border-bottom: none;">
					<ol>
						<li>
							使用区别<br>
							(1)&nbsp;购买评估版后，用户获得创建应用、配置技能、调试后处理等权限。在所有配置完成后，用户获取独立的APPID，用于购买量产版。<br>
							(2)&nbsp;购买量产版前，用户需将APPID、AppKey和情景参数同步给讯飞，讯飞将直接发货已配置完成的量产版。<br>
							(3)&nbsp;购买量产版后，用户直接配网即可使用讯飞魔飞智能麦克风。
						</li>
						<li>
							功能区别<br>
							(1)&nbsp;评估版是为了快速体验和用户应用调试。在经过评估调试后，确定采用此方案能否满足实际产品需求。<b style="color:#22a4e7; ">评估版属于电子产品，除非质量问题，不能退换货。</b><br>
							(2)&nbsp;量产版是为了实现产品快速量产，用户需在购买评估版获取APPID后，才可购买量产版。
						</li>
						<li>
							服务区别<br>
							(1)&nbsp;针对评估版客户，我们提供周全的服务，包括完善的售前售后支持、完备的包装配件等。<br>
							(2)&nbsp;针对量产版客户，我们支持100台量产起购，提供完备的开发支持和效果评估。
						</li>
						<li>
							价格区别<br>
							评估版12999元/片，包含平台开发权限、配件以及相应的支持服务费用。量产版价格低于评估版，价格和购买事宜请联系商务。
						</li>
					</ol>
				</li>
			</ul>
		</div>
  </div>
</template>

<script>
import utils from '../../../assets/lib/utils.js'

export default {
  layout: 'aiuiHome',
  data () {
    return {
			userinfo: null,
			buyUrl: 'http://www.aifuwus.com/onstage/cmddetail?id=878'
    }
	},
	mounted() {
    let self = this
    this.selectTab = this.$route.name
    utils.httpRequest('/api/graphql', 'post', {
      data: {
        query: `
          {
            userDetailInfo {
              certificatedName
              email
              isCertificated
              mobile
              type
            }
          }
        `
      },
      success: (res) => {
        self.userinfo = res.data.userDetailInfo
      }
    })
  },
  methods: {
		canBuy() {
			if(this.$store.state.auth.loggedIn) {
					window.location.href
				} else {
          location.href = ''
				}
		}
  }
}
</script>
<style lang="scss" scoped>
	.banner-wrap {
		height: 459px;
		padding-top: 60px;
		min-width: 1200px;
		box-sizing: border-box;
		background: url("../../../assets/images/solutions/morfei/morfei2.0-bg.jpg") #1a294d no-repeat center bottom;
	}

	.content-wrap {
		width: 1200px;
		height: 399px;
		margin: 0 auto;
		color: #999;
		position: relative;
	}

	.banner-content {
		padding: 0 15px;
		box-sizing: border-box;

		.banner-title {
			top: 50px;
			color: #fff;
			font-size: 45px;
			position: absolute;
		}

		.banner-desc {
			position: absolute;
			top: 130px;
			color: #fff;
			line-height: 25px;
			font-size: 16px;
			letter-spacing: 5px;
		}
	}

	.buy-btn {
		position: absolute;
		top: 220px;
		left: 15px;
		width: 150px;
		height: 40px;
		color: #fff;
		line-height: 40px;
		text-align: center;
		display: inline-block;
		border: 1px solid #fff;
		border-radius: 5px;
		font-size: 16px;
	}

	.download-doc {
		left: 190px;
	}

	.tab {
		width: 1200px;
		position: absolute;
		bottom: 0;

		a {
			width: 600px;
			height: 48px;
			float: left;
			text-align: center;
			line-height: 44px;
			color: #fff;
			font-size: 16px;
			background: #304681;
			border-color: #304681;
			box-sizing: border-box;
		}

		.active {
			color: #3482ec;
			background: #fff;
			border-top: 4px solid #79b2ff;
		}
	}

	.type-wrap {
		min-width: 1200px;
		height: 580px;
		margin: 0 auto;
		padding-top: 75px;
		box-sizing: border-box;
		.product-wrap {
			height: 362px;
			margin-top: 45px;
			background: url("../../../assets/images/solutions/morfei/producted.jpg") no-repeat center;
		}
	}

	.common-title {
		font-size: 20px;
		text-align: center;
		color: #24374e;
		span {
			color: #2d9bf3;
		}
	}

	.product-advantages-wrap {
		width: 300px;
		height: 362px;
		display: inline-block;
		text-align: center;
		overflow: hidden;
		>div {
			width: 300px;
			overflow: hidden;
			transition: all .5s;
		}
		&:hover .advantages-block {
			height: 0;
		}
		&:hover .advantages-hide {
			height: 362px;
		}
	}
	.advantages-block {
		height: 362px;
		.product-icons {
			display: inline-block;
			width: 70px;
			height: 50px;
			margin-top: 100px;
		}
		p {
			margin-top: 30px;
			font-size: 16px;
			color: #ffffff;
		}
		ul {
			margin-top: 25px;
			text-align: center;
			li {
				color: #b9babb;
			}
		}
	}

	.advantages-hide {
		height: 0;
		background: rgba(64,165,214,0.6);
		p:first-child{
			color: #fff;
			margin-top: 125px;
			font-size: 16px;
			font-weight: bold;
		}
		p:nth-child(2){
			width: 235px;
			color: #fff;
			margin: 20px auto 0;
			text-align: left;
		}
	}

	.product-advantages-icon1 {
		background: url("../../../assets/images/solutions/morfei/icons-producted.png") no-repeat 16px 0;
	}

	.product-advantages-icon2 {
		background: url("../../../assets/images/solutions/morfei/icons-producted.png") no-repeat -278px 0;
	}

	.product-advantages-icon3 {
		background: url("../../../assets/images/solutions/morfei/icons-producted.png") no-repeat -580px 0;
	}

	.product-advantages-icon4 {
		background: url("../../../assets/images/solutions/morfei/icons-producted.png") no-repeat -878px 0;
	}

	.advantage-wrap1 {
		min-width: 1200px;
		height: 560px;
		padding-top: 75px;
		box-sizing: border-box;
		background: url("../../../assets/images/solutions/morfei/ability-bg.jpg") no-repeat center;
	}

	.advantage-wrap1-desc {
		width: 565px;
		position: absolute;
		top: 120px;
		right: 0;
		p {
			font-size: 16px;
			color: #415266;
			line-height: 60px;
			i {
				display: inline-block;
				width: 16px;
				height: 14px;
				margin-right: 15px;
				vertical-align: middle;
				background: url("../../../assets/images/solutions/morfei/ability-icon.png") no-repeat;
			}
		}
	}

	.advantage-wrap2 {
		min-width: 1200px;
		height: 465px;
		padding-top: 75px;
		box-sizing: border-box;
		background: #fff;
		.content-wrap {
			height: 390px;
		}
	}

	.interfaces {
		margin-top: 40px;
		.new-interface {
			display: inline-block;
			width: 240px;
			height: 240px;
			margin-left: 80px;
			text-align: center;
			border-radius: 3px;
			i {
				display: inline-block;
				width: 110px;
				height: 93px;
				margin-top: 30px;
			}
			p:nth-child(2) {
				margin: 10px 0 15px;
				font-size: 14px;
				font-weight: bold;
				color: #24384f;
			}
			p:nth-child(3) {
				font-size: 13px;
			}
			&:hover {
				box-shadow: 0.00px 0.00px 15.36px 0.64px rgba(235, 240, 245, 1);
			}
		}
		.interfaces-audio {
			margin-left: 0;
		}
	}
	.interfaces-audio i {
		background: url("../../../assets/images/solutions/morfei/icons-version.png") no-repeat 0px 0px;
	}
	.interfaces-word i {
		background: url("../../../assets/images/solutions/morfei/icons-version.png") no-repeat -311px 0px;
	}
	.interfaces-semantic i {
		background: url("../../../assets/images/solutions/morfei/icons-version.png") no-repeat -627px 0px;
	}
	.interfaces-compose i {
		background: url("../../../assets/images/solutions/morfei/icons-version.png") no-repeat -950px 0px;
	}

	.advantage-wrap3 {
		min-width: 1200px;
		height: 548px;
		padding-top: 75px;
		box-sizing: border-box;
		background: url("../../../assets/images/solutions/morfei/character-bg.jpg") no-repeat center;
	}

	.new-voice, .new-power {
		position: absolute;
		padding: 25px 20px;
		border-radius: 5px;
		box-sizing: border-box;
		p:first-child {
			font-size: 16px;
			font-weight: bold;
			margin-bottom: 10px;
		}
	}

	.new-voice {
		width: 307px;
		height: 135px;
		top: 55px;
		left: 0;
		p:first-child {
			color: #2d9bf3;
		}
	}

	.new-power {
		width: 306px;
		height: 116px;
		top: 55px;
		right: 2px;
		p:first-child {
			color: #5870de;
		}
	}

	.qa {
		min-width: 1200px;
		background: #f6f7fa;
		padding: 1px 0;
		box-sizing: border-box;

		.qa_content {
			width: 1200px;
			margin: 0 auto;
		}
		.qa_content .li_q {
			margin-top: 30px;
			padding-left: 60px;
			line-height: 45px;
			color: #333;
			font-size: 16px;
			text-align: left;
		}
		.qa_content .li_sort {
			padding-left: 78px;
			border-bottom: 1px dashed #dddddd;
		}

		.qa_content p{
			color: #999;
			padding: 0 63px;
		}

		.qa_content .li_a {
			padding: 10px 81px 30px;
			font-size: 14px;
			line-height: 24px;
			color: #666;
		}
		.qa_content ol li {
			list-style-type: decimal;
			list-style-position: outside;
			text-align: left;
		}

		b {
			font-weight: bold;
		}
	}
</style>
