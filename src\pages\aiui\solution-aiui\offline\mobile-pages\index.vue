<template>
  <div class="main-content">
    <section class="main-content-banner">
      <div class="banner-text">
        <h2>离线语音交互解决方案</h2>
        <p class="banner-text-content">
          没有网络也可以使用语音服务，<br />
          实现离线语音控制交互。
        </p>

        <div class="banner-text-button" @click="toConsole">合作咨询</div>
      </div>
    </section>
    <section class="section section-1">
      <div class="section-title">
        <i class="arrow arrow-left"></i
        ><span class="section-title-bold">应用场景</span
        ><i class="arrow arrow-right"></i>
      </div>
      <div class="section-desc">应用在无网弱网场景，可以保护用户隐私</div>
      <ul class="interact-list">
        <li
          v-for="(item, index) in interactList"
          :key="index"
          :class="item.klass"
        >
          <h1>{{ item.name }}</h1>
          <div class="desc-wrap">
            <h2>{{ item.name }}</h2>
            <p>
              {{ item.desc }}
            </p>
          </div>
          <div class="overlay"></div>
        </li>
      </ul>
    </section>
    <section class="section section-2">
      <div class="section-title">
        <i class="arrow arrow-left"></i
        ><span class="section-title-bold">方案介绍</span
        ><i class="arrow arrow-right"></i>
      </div>

      <div class="section-tabs">
        <ul>
          <li :class="{ active: activeName == '0' }" @click="toggleActive('0')">
            语种支持
          </li>
          <li :class="{ active: activeName == '1' }" @click="toggleActive('1')">
            音频要求
          </li>
          <li :class="{ active: activeName == '2' }" @click="toggleActive('2')">
            交互说明
          </li>
          <li :class="{ active: activeName == '3' }" @click="toggleActive('3')">
            更新方式
          </li>
          <li :class="{ active: activeName == '4' }" @click="toggleActive('4')">
            算力要求
          </li>
        </ul>
      </div>
      <div class="section-swiper" v-swiper:swiper="swiperOption">
        <div class="swiper-wrapper">
          <div class="swiper-slide" key="1">
            <information :info="cells1" label="1" />
          </div>
          <div class="swiper-slide" key="2">
            <information :info="cells2" label="2" />
          </div>
          <div class="swiper-slide" key="3">
            <information :info="cells3" label="3" />
          </div>
          <div class="swiper-slide" key="4">
            <information :info="cells4" label="4" />
          </div>
          <div class="swiper-slide" key="5">
            <information :info="cells5" label="5" />
          </div>
        </div>
      </div>
    </section>
    <section class="section section-3">
      <div class="section-title">
        <i class="arrow arrow-left"></i
        ><span class="section-title-bold">方案优势</span
        ><i class="arrow arrow-right"></i>
      </div>
      <ul class="advantage">
        <li>
          <div class="advantage-text">
            <p>交互不受网络影响，实时响应</p>
            <ul>
              <li>可纯离线使用，实时出字</li>
              <li>300ms给出语音识别结果和理解信息</li>
              <li>让设备操控更加稳定可靠</li>
            </ul>
          </div>
          <div class="advantage-image"></div>
        </li>
        <li>
          <div class="advantage-image"></div>
          <div class="advantage-text">
            <p>语音技能可定制，更新简单</p>
            <ul>
              <li>离线命令词和语音交互技能可定制</li>
              <li>支持不同场景下命令词和词条动态更新</li>
              <li>200ms完成构建</li>
              <li>实现离线的所见即可说</li>
            </ul>
          </div>
        </li>
        <li>
          <div class="advantage-text">
            <p>数据不联网，隐私有保障</p>
            <ul>
              <li>家庭、酒店私密场所，医疗、政务内网环境</li>
              <li>对话交互纯离线</li>
              <li>数据不联网，安全有保障</li>
            </ul>
          </div>
          <div class="advantage-image"></div>
        </li>
      </ul>
    </section>
    <section class="section section-4">
      <div class="section-title">
        <p class="section-title-bold">合作咨询</p>
        <p>提交信息，我们会尽快与你联系</p>
      </div>
      <div class="section-item">
        <aiui-button hasTop @click.native="toConsole">申请合作</aiui-button>
      </div>
    </section>
  </div>
</template>

<script>
import '../../../../../../static/vue-awesome-swiper'
import information from '../information.vue'

export default {
  name: 'offline',
  data() {
    return {
      interactList: [
        {
          name: '家居',
          desc: '',
          klass: 'img_full_duplex',
        },
        {
          name: '户外',
          desc: '',
          klass: 'img_free_wake_click',
        },
        {
          name: '车机',
          desc: '',
          klass: 'img_multimodal_click',
        },
        {
          name: '工业控制',
          desc: '',
          klass: 'img_offlineinteraction_click',
        },
      ],
      cells1: {
        title: '离线支持多语种识别',
        desc: '支持普通话、日语、俄语、韩语、法语、西班牙语、阿拉伯语、德语、越南语、泰语、印地语、意大利语、葡萄牙语',
        imageName: 'img_language_support',
      },
      cells2: {
        title: '适用于离线人机对话交互',
        desc: '用于人机对话交互场景<br/>每次对话音频长度不超过20秒<br/>能有效地保障人机交互效果',
        imageName: 'img_audio_requirements',
      },
      cells3: {
        title: '离线支持用户自由说',
        desc: '支持语音技能定制<br/>30000个词条，说法不限制<br/>可满足垂直场景高频说法全部覆盖',
        imageName: 'img_interactive_instructions',
      },
      cells4: {
        title: '支持动态覆盖更新',
        desc: '语音技能词条资源支持动态构建<br/>以500个词条为例，可以200ms完成覆盖<br/>更新不影响用户使用',
        imageName: 'img_update_mode',
      },
      cells5: {
        title: '系统算力要求',
        desc: '支持安卓、ios、linux(arm)系统<br/>端上1核，1000mips，内存100M可满足用户随意说<br/>仅使用200个离线命令词，算力可降50%',
        imageName: 'img_calculate_power_demand',
      },
      activeName: '0',
      swiperOption: {
        loop: true,
        autoplay: {
          delay: 4000,
          // disableOnInteraction: false,
        },
      },
    }
  },
  mounted() {
    this.swiper.on('slideChange', () => {
      this.activeName = this.swiper.realIndex + ''
    })
  },
  methods: {
    toConsole() {
      // 判断是否有百度SEM营销参数，有的话，提交表单页需要带上
      const search = window.location.search
      if (search) {
        window.open(`/solution/apply/9${search}`)
      } else {
        window.open('/solution/apply/9')
      }
    },
    toggleActive(val) {
      // this.activeName = val
      this.swiper.slideToLoop(Number(val))
    },
  },
  components: { information },
}
</script>

<style lang="scss" scoped>
.main-content {
  &-banner {
    background: url(~@A/images/solution/offline/img_bg.png) center no-repeat;
    background-size: cover;
    height: 500px;
    overflow: hidden;
    width: 100%;
    .banner-text {
      max-width: 1200px;
      color: #fff;
      height: 100%;
      margin: auto;
      &-button {
        font-size: 16px;
        text-align: center;
        font-weight: 400;
        width: 140px;
        height: 40px;
        line-height: 40px;
        border: 1px solid #fff;
        border-radius: 40px;
        color: #fff;
        cursor: pointer;
        transition: 0.6s;
        // &:hover {
        //   color: #002985;
        //   background: #fff;
        //   transition: 0.3s;
        // }
      }
      h2 {
        color: #fff;
        padding-top: 148px;
        margin-bottom: 29px;
        font-size: 48px;
        font-weight: 500;
        line-height: 48px;
      }
      p {
        font-size: 18px;
        margin-bottom: 74px;
      }

      .banner-text-content {
        width: 570px;
        font-size: 16px;
        font-family: SourceHanSansSC-Regular, SourceHanSansSC;
        font-weight: 400;
        color: rgba(255, 255, 255, 0.86);
        line-height: 30px;
      }
    }
  }

  .section {
    p,
    ul {
      margin-bottom: 0;
    }
    max-width: 1200px;
    overflow: hidden;
    margin: 0 auto;
    .section-title {
      text-align: center;
      font-size: 34px;
      font-family: SourceHanSansSC-Medium, SourceHanSansSC;
      font-weight: bold;
      color: #333;
      line-height: 34px;
      position: relative;
      width: 200px;
      margin: 0 auto;
      .arrow {
        width: 160px;
        height: 8px;
        display: inline-block;
        background-repeat: no-repeat;
        background-size: contain;
        position: absolute;
        z-index: 1;
      }
      .arrow-left {
        background-position: left;
        // background-image: url(~@A/images/solution/smart-hardware/arrow-left.png);
        background-image: url(~@A/images/solution/offline/img_title_01.png);
        top: 50%;
        left: -160px;
      }
      .arrow-right {
        background-position: right;
        // background-image: url(~@A/images/solution/smart-hardware/arrow-right.png);
        background-image: url(~@A/images/solution/offline/img_title_02.png);
        top: 50%;
        right: -160px;
      }
    }
    .section-desc {
      text-align: center;
      margin-top: 40px;
      font-size: 16px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #999999;
    }
    .section-title-bold {
      font-size: 34px;
      font-family: Microsoft YaHei;
      font-weight: bold;
      color: #333333;
    }
    .section-title-spec {
      font-size: 30px;
      font-weight: 500;
      color: #656565;
      line-height: 42px;
    }
    .section-tabs {
      margin-top: 70px;
      ul {
        display: flex;
        justify-content: center;
      }
      li {
        font-size: 20px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #666;
        line-height: 30px;
        position: relative;
        cursor: pointer;
        &:hover {
          color: #1f90fe;
        }
        &.active {
          color: #1f90fe;
          &::before {
            position: absolute;
            content: ' ';
            z-index: 1;
            width: 180px;
            height: 4px;
            background: #1f90fe;
            border-radius: 2px;
            bottom: -32px;
            left: -48px;
          }
          &::after {
            position: absolute;
            content: ' ';
            display: inline-block;
            width: 0;
            height: 0;
            bottom: -100px;
            left: 50%;
            transform: translateX(-50%);
            border: 46px solid;
            border-color: transparent transparent #f4f7f9;
          }
        }
      }
      li + li {
        margin-left: 165px;
      }
    }
    .section-sub-title {
      text-align: center;
      font-size: 18px;
      font-weight: 400;
      color: #777777;
      line-height: 25px;
      margin-top: 42px;
    }
    .section-sub-title-spec {
      font-size: 17px;
      font-weight: 400;
      color: #999999;
      line-height: 28px;
      margin-top: 18px;
    }
  }

  .section-3 {
    p {
      margin-bottom: 0;
    }
    margin-top: 50px;
    .advantage {
      margin-top: 84px;
      > li {
        display: flex;
        justify-content: center;
        align-items: center;
      }
      > li:nth-child(1) {
        .advantage-image {
          background: url(~@A/images/solution/offline/img_voice_response.png)
            center/100% no-repeat;
        }
        .advantage-text {
          // padding-top: 96px;
          // padding-left: 93px;
          margin-right: 250px;
        }
      }
      > li:nth-child(2) {
        margin-top: 148px;
        .advantage-image {
          background: url(~@A/images/solution/offline/img_custom_word.png)
            center/100% no-repeat;
        }
        .advantage-text {
          // padding-top: 91px;
          margin-left: 302px;
        }
      }
      > li:nth-child(3) {
        margin-top: 148px;

        .advantage-image {
          background: url(~@A/images/solution/offline/img_Secret.png)
            center/100% no-repeat;
        }
        .advantage-text {
          // padding-top: 96px;
          // padding-left: 93px;
          margin-right: 250px;
        }
      }
    }
    .advantage-text {
      width: 442px;
      p {
        font-size: 34px;
        font-weight: 400;
        color: #666;
        line-height: 34px;
      }
      // p:nth-child(2) {
      //   font-size: 18px;
      //   color: #777777;
      //   line-height: 27px;
      //   font-weight: 400;
      //   margin-top: 12px;
      // }
      ul {
        margin-top: 42px;
        li {
          font-size: 16px;
          font-weight: 400;
          color: #999999;
          line-height: 30px;
          white-space: nowrap;
          // &::before {
          //   display: inline-block;
          //   content: ' ';
          //   width: 9px;
          //   height: 9px;
          //   border-radius: 100%;
          //   background: #3a91ff;
          //   margin-right: 4px;
          // }
        }
        // li + li {
        //   margin-top: 14px;
        // }
      }
    }
    .advantage-image {
      width: 280px;
      height: 244px;
    }
  }

  .section-1 {
    margin-top: 50px;
  }

  .section-2 {
    max-width: 2560px;
    margin-top: 85px;
  }
  .section-3 {
    margin-top: 81px;
  }

  // .section-2 {
  //   p {
  //     margin-bottom: 0;
  //   }
  //   margin-top: 160px;
  //   .advantage {
  //     margin-top: 84px;
  //     li {
  //       display: flex;
  //       justify-content: center;
  //     }
  //     li:nth-child(1) {
  //       margin-top: 65px;
  //       .advantage-image {
  //         margin-left: 93px;
  //         background-image: url(~@A/images/solution/assistant/adv.png) center
  //           no-repeat;
  //       }
  //       .advantage-text {
  //         padding-top: 94px;
  //       }
  //     }
  //   }
  //   .advantage-text {
  //     width: 373px;
  //     p:first-child {
  //       font-size: 30px;
  //       font-weight: 500;
  //       color: #656565;
  //       line-height: 42px;
  //     }
  //     p:last-child {
  //       font-size: 17px;
  //       font-weight: 400;
  //       color: #999999;
  //       line-height: 28px;
  //       margin-top: 18px;
  //     }
  //   }
  //   .advantage-image {
  //     width: 398px;
  //     height: 313px;
  //     background-repeat: no-repeat;
  //   }
  // }

  .section-4 {
    margin-top: 109px;
    padding-bottom: 129px;
    text-align: center;
    p {
      margin-bottom: 0;
    }
    .section-title {
      width: 300px;
      p:first-child {
        font-size: 34px;
        font-family: Microsoft YaHei;
        font-weight: bold;
        color: #333333;
      }
      p:last-child {
        font-size: 16px;
        font-weight: 400;
        color: #666;
        line-height: 22px;
        // margin-top: 18px;
        margin-top: 43px;
      }
    }
    .section-item {
      margin-top: 49px;
      .section-button {
        color: #fff;
        background: #1784e9;
        width: 195px;
        height: 50px;
        line-height: 50px;
        text-align: center;
        margin: 0 auto;
        cursor: pointer;
      }
    }
  }

  .interact-list {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 50px auto 0;

    li {
      position: relative;
      text-align: center;
      cursor: pointer;
      width: 287px;
      height: 410px;

      .desc-wrap {
        top: 50%;
        transform: translateY(-50%);
        position: absolute;
        left: 0;
        width: 100%;
        z-index: 2;
      }
      .overlay {
        display: none;
        width: 100%;
        height: 100%;
        // background: rgba(0, 0, 0, 0.3);
        background-image: linear-gradient(
          0deg,
          rgb(0, 54, 255) 0%,
          rgb(39, 12, 73) 100%
        );
        opacity: 0.502;
        position: absolute;
        top: 0;
        left: 0;
        z-index: 1;
      }

      // &:hover {
      //   p,
      //   h2 {
      //     display: block;
      //   }
      //   h1 {
      //     display: none;
      //   }
      //   .overlay {
      //     display: block;
      //   }
      // }
      h1 {
        text-align: left;
        max-width: 25px;
        font-size: 24px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #ffffff;
        line-height: 30px;
        margin: 0 auto;
        position: relative;
        top: 50%;
        transform: translateY(-50%);
      }
      h2 {
        display: none;
        text-align: left;
        // padding-top: 178px;
        padding-left: 35px;
        font-size: 34px;
        font-weight: bold;
        color: #ffffff;
        line-height: 40px;
      }
      p {
        display: none;
        margin-top: 32px;
        width: 232px;
        font-size: 16px;
        font-weight: 400;
        color: #ffffff;
        line-height: 32px;
        padding-left: 35px;
        text-align: left;
      }

      &.img_full_duplex {
        background: url(~@A/images/solution/offline/img_household.png)
          center/100% no-repeat;
      }
      &.img_free_wake_click {
        background: url(~@A/images/solution/offline/img_outdoor.png) center/100%
          no-repeat;
      }
      &.img_multimodal_click {
        background: url(~@A/images/solution/offline/img_car_machine.png)
          center/100% no-repeat;
      }
      &.img_offlineinteraction_click {
        background: url(~@A/images/solution/offline/img_industrial_control.png)
          center/100% no-repeat;
      }
    }

    li + li {
      margin-left: 16px;
    }
  }
}
.section-swiper {
  margin-top: 81px;
  background: #f4f7f9;
  .swiper-wrapper {
    max-width: 1200px;
  }
}
</style>
