<template>
  <div class="main-content">
    <section class="main-content-banner">
      <div class="banner-text">
        <h2><span>多模态交互解决方案</span></h2>
        <p class="banner-text-content">
          从语音交互拓展到音视频流的实时多模态交互，<br />
          新增超拟人虚拟人输出和个性化能力，引领人机交互新体验。
        </p>
        <div class="banner-side-image"></div>

        <div class="banner-text-button" @click="toConsole">合作咨询</div>
      </div>
    </section>
    <section class="section section-1">
      <div class="section-title">
        <span class="section-title-bold">应用场景</span>
      </div>
      <div class="section-desc">适用于带有摄像头的智能硬件</div>
      <ul class="interact-list">
        <li
          v-for="(item, index) in interactList"
          :key="index"
          :class="item.klass"
        >
          <h1>{{ item.name }}</h1>
        </li>
      </ul>
    </section>

    <div class="section-3-wrap">
      <section class="section section-3">
        <div class="section-title">
          <span class="section-title-bold">方案优势</span>
        </div>
        <!-- <div class="section-desc">
          提供公共环境下多模态录音降噪和星火大模型交互整套软硬件解决方案
        </div> -->
        <ul class="advantage">
          <li>
            <div class="advantage-text">
              <p class="advantage-tag-wrap">
                <span class="advantage-tag"></span
                ><span class="advantage-tag-text">主动对话、自由交互</span>
              </p>
              <ul>
                <li>
                  <i class="tick"></i
                  ><span class="tick-text">搭配多模态降噪，无惧高噪环境</span>
                </li>
                <li>
                  <i class="tick"></i
                  ><span class="tick-text">人脸识别，主动对话</span>
                </li>
                <li>
                  <i class="tick"></i
                  ><span class="tick-text">唇动监测，免唤醒交互</span>
                </li>
              </ul>
            </div>
            <div class="advantage-image"></div>
          </li>
          <li>
            <div class="advantage-image"></div>
            <div class="advantage-text">
              <p class="advantage-tag-wrap">
                <span class="advantage-tag"></span
                ><span class="advantage-tag-text">多模情境感知、极速响应</span>
              </p>
              <ul>
                <li>
                  <i class="tick"></i
                  ><span class="tick-text">视频输入，自由打断</span>
                </li>
                <li>
                  <i class="tick"></i
                  ><span class="tick-text">表达方式随心可控</span>
                </li>
                <li>
                  <i class="tick"></i
                  ><span class="tick-text">情绪感知共鸣</span>
                </li>
              </ul>
            </div>
          </li>
          <li>
            <div class="advantage-text">
              <p class="advantage-tag-wrap">
                <span class="advantage-tag"></span
                ><span class="advantage-tag-text"
                  >支持业务配置、产品生而智能</span
                >
              </p>
              <ul>
                <li>
                  <i class="tick"></i
                  ><span class="tick-text">支持配置官方技能及信源</span>
                </li>
                <li>
                  <i class="tick"></i
                  ><span class="tick-text">支持大模型文档问答、知识定制</span>
                </li>
              </ul>
            </div>
            <div
              class="advantage-image"
              style="width: 564px; height: 428px"
            ></div>
          </li>
          <li>
            <div class="advantage-image" @click="playVideo" v-if="!isPlaying">
              <div class="play-icon"></div>
            </div>
            <div v-else>
              <video
                :src="videoSrc"
                style="width: 586px; height: 340px"
                autoplay
                preload
                controls
              ></video>
            </div>

            <div class="advantage-text">
              <p class="advantage-tag-wrap">
                <span class="advantage-tag"></span
                ><span class="advantage-tag-text">超拟人数字人、自然交互</span>
              </p>
              <ul>
                <li>
                  <i class="tick"></i
                  ><span class="tick-text">表情、动作、情感语义贯穿</span>
                </li>
                <li>
                  <i class="tick"></i
                  ><span class="tick-text">动作流畅、表情自然、实时响应</span>
                </li>
              </ul>
            </div>
          </li>
        </ul>
      </section>
    </div>

    <!-- <corp @jump="toConsole">
      <template> 免费咨询专属顾问 为您量身定制产品推荐方案</template>
    </corp> -->
    <div class="corp-button-wrap">
      <div class="corp-button" @click="toConsole">合作咨询</div>
    </div>
  </div>
</template>

<script>
// import corp from '@P/aiui/solution-aiui/components/corp2.vue'
import videoPlayer from '@C/videoPlayer/index'

export default {
  name: 'multimodal-interaction',
  data() {
    return {
      videoSrc: 'https://aiui-file.cn-bj.ufileos.com/video/hyper_digital.mp4',
      interactList: [
        {
          name: '手机助手',
          desc: '',
          klass: 'img_full_duplex',
        },
        {
          name: '儿童陪伴机',
          desc: '',
          klass: 'img_free_wake_click',
        },
        {
          name: '虚拟人一体机',
          desc: '',
          klass: 'img_multimodal_click',
        },
        {
          name: '机器人',
          desc: '',
          klass: 'img_offlineinteraction_click',
        },
        {
          name: '自助终端',
          desc: '',
          klass: 'img_self_terminal',
        },
      ],
      isPlaying: false,
    }
  },
  mounted() {},
  methods: {
    toConsole() {
      // 判断是否有百度SEM营销参数，有的话，提交表单页需要带上
      const search = window.location.search
      if (search) {
        window.open(`/solution/apply/34${search}`)
      } else {
        window.open('/solution/apply/34')
      }
    },
    getWindowHeight() {
      return 'innerHeight' in window
        ? window.innerHeight
        : document.documentElement.offsetHeight
    },

    playVideo() {
      // let height = Math.min(this.getWindowHeight() * 0.9, 562)
      // let width = parseInt((1920 * height) / 1080)
      // videoPlayer({
      //   width,
      //   height,
      //   videoSrc: this.videoSrc,
      //   videoStyle: {
      //     width: `${width}px`,
      //     height: `${height}px`,
      //     'box-sizing': 'border-box',
      //     'margin-left': `-${width * 0.5}px`,
      //     'margin-top': `-${height * 0.5}px`,
      //   },
      // })
      this.isPlaying = true
    },
  },
  // components: { corp },
}
</script>

<style lang="scss" scoped>
.main-content {
  &-banner {
    background: url(~@A/images/solution/multimodal-interaction/banner.png)
      center no-repeat;
    background-size: cover;
    height: 576px;
    overflow: hidden;
    width: 100%;
    .banner-text {
      position: relative;
      max-width: 1200px;
      height: 100%;
      margin: auto;

      &-button {
        font-size: 20px;
        text-align: center;
        font-weight: 600;
        line-height: 60px;
        color: #080808;
        cursor: pointer;
        width: 163px;
        height: 60px;
        background: linear-gradient(90deg, #e9f2ff 0%, #bbd8fe 100%);
        border-radius: 60px 60px 60px 60px;
      }

      h2 {
        padding-top: 148px;
        margin-bottom: 20px;

        // font-size: 44px;
        // font-weight: 600;
        text-align: left;
        // line-height: 54px;
        span {
          font-size: 48px;
          font-weight: 600;
          text-align: left;
          line-height: 54px;
          color: #fff;
        }

        // background-image: linear-gradient(270deg,#1f6efd,#1ebce8);
      }

      .banner-text-content {
        font-size: 16px;
        font-weight: 400;
        text-align: left;
        color: #fff;
        line-height: 31px;
        margin-bottom: 100px;
      }

      .banner-side-image {
        position: absolute;
        z-index: 1;
        right: 0;
        top: 42px;
        width: 662px;
        height: 504px;
        background: url(~@A/images/solution/multimodal-interaction/<EMAIL>)
          center/contain no-repeat;
      }
    }
  }

  .section {
    p,
    ul {
      margin-bottom: 0;
    }
    max-width: 1200px;
    overflow: hidden;
    margin: 0 auto;
    .section-title {
      text-align: center;
      font-size: 34px;
      font-family: SourceHanSansSC-Medium, SourceHanSansSC;
      font-weight: bold;
      color: #333;
      line-height: 34px;
      position: relative;
      width: 200px;
      margin: 0 auto;
      .arrow {
        width: 160px;
        height: 8px;
        display: inline-block;
        background-repeat: no-repeat;
        background-size: contain;
        position: absolute;
        z-index: 1;
      }
      .arrow-left {
        background-position: left;
        top: 50%;
        left: -160px;
      }
      .arrow-right {
        background-position: right;
        top: 50%;
        right: -160px;
      }
    }
    .section-desc {
      text-align: center;
      margin-top: 30px;
      font-size: 16px;
      font-weight: 400;
      color: #666666;
      line-height: 25px;
    }
    .section-title-bold {
      font-size: 40px;
      font-weight: 500;
      color: #333;
      line-height: 44px;
    }

    .section-tabs {
      margin-top: 70px;
      ul {
        display: flex;
        justify-content: center;
      }
      li {
        font-size: 20px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #666;
        line-height: 30px;
        position: relative;
        cursor: pointer;
        &:hover {
          color: #1f90fe;
        }
        &.active {
          color: #1f90fe;
          &::before {
            position: absolute;
            content: ' ';
            z-index: 1;
            width: 180px;
            height: 4px;
            background: #1f90fe;
            border-radius: 2px;
            bottom: -32px;
            left: -48px;
          }
          &::after {
            position: absolute;
            content: ' ';
            display: inline-block;
            width: 0;
            height: 0;
            bottom: -100px;
            left: 50%;
            transform: translateX(-50%);
            border: 46px solid;
            border-color: transparent transparent #f4f7f9;
          }
        }
      }
      li + li {
        margin-left: 165px;
      }
    }
    .section-sub-title {
      text-align: center;
      font-size: 18px;
      font-weight: 400;
      color: #777777;
      line-height: 25px;
      margin-top: 42px;
    }
    .section-sub-title-spec {
      font-size: 17px;
      font-weight: 400;
      color: #999999;
      line-height: 28px;
      margin-top: 18px;
    }
  }

  .section-3-wrap {
    padding: 80px 0 82px 0;
    background: #eff7ff;
    margin-top: 55px;
  }
  .section-3 {
    p {
      margin-bottom: 0;
    }
    // margin-top: 50px;

    .advantage {
      margin-top: 60px;
      > li {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      > li:nth-child(1) {
        .advantage-image {
          width: 656px;
          height: 450px;
          background: url(~@A/images/solution/multimodal-interaction/<EMAIL>)
            center/contain no-repeat;
        }
      }
      > li:nth-child(2) {
        margin-top: 123px;
        .advantage-image {
          width: 614px;
          height: 624px;
          background: url(~@A/images/solution/multimodal-interaction/<EMAIL>)
            center/contain no-repeat;
        }
      }
      > li:nth-child(3) {
        // margin-top: 75px;

        .advantage-image {
          width: 614px;
          height: 354px;
          background: url(~@A/images/solution/multimodal-interaction/<EMAIL>)
            center/contain no-repeat;
        }
      }
      > li:nth-child(4) {
        margin-top: 86px;
        background: #fff;
        border-radius: 20px;
        padding: 29px;
        .advantage-image {
          position: relative;
          cursor: pointer;
          width: 586px;
          height: 340px;
          background: url(~@A/images/solution/multimodal-interaction/<EMAIL>)
            center/contain no-repeat;
          .play-icon {
            position: absolute;
            width: 100px;
            height: 100px;
            z-index: 1;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: url(~@A/images/solution/multimodal-interaction/<EMAIL>)
              center/contain no-repeat;
          }
        }
      }
    }
    .advantage-text {
      .tick {
        width: 30px;
        height: 30px;
        display: inline-block;
        background: url(~@A/images/solution/multimodal-interaction/tik.png)
          center/contain no-repeat;
        margin-right: 17px;
      }

      width: 442px;
      p {
        font-size: 30px;
        font-weight: 600;
        color: #000;
        height: 30px;
        min-width: 200px;
        position: relative;
      }

      ul {
        margin-top: 40px;
        li {
          font-size: 20px;
          font-weight: 400;
          color: #36485d;
          line-height: 40px;
          white-space: nowrap;
          display: flex;
          align-items: center;
        }
      }
    }
  }

  .section-1 {
    padding-top: 80px;
    .interact-list {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin: 50px auto 0;

      li {
        position: relative;
        text-align: center;
        width: 215px;
        height: 371px;

        h1 {
          text-align: center;
          font-size: 18px;
          font-weight: 400;
          color: #000;
          line-height: 21px;
          margin: 0 auto;
          position: relative;
          padding-top: 53px;
        }

        &.img_full_duplex {
          background: url(~@A/images/solution/multimodal-interaction/<EMAIL>)
            center/100% no-repeat;
        }
        &.img_free_wake_click {
          background: url(~@A/images/solution/multimodal-interaction/<EMAIL>)
            center/100% no-repeat;
        }
        &.img_multimodal_click {
          background: url(~@A/images/solution/multimodal-interaction/<EMAIL>)
            center/100% no-repeat;
        }
        &.img_offlineinteraction_click {
          background: url(~@A/images/solution/multimodal-interaction/<EMAIL>)
            center/100% no-repeat;
        }
        &.img_self_terminal {
          background: url(~@A/images/solution/multimodal-interaction/<EMAIL>)
            center/100% no-repeat;
        }
      }
    }
  }

  .section-2 {
    max-width: 2560px;
    margin-top: 110px;
  }
}

.advantage-tag {
  width: 140px;
  height: 12px;
  background: linear-gradient(270deg, rgba(37, 185, 245, 0) 0%, #2bb6d7 100%);
  position: absolute;
  z-index: 1;
  left: 0;
  bottom: 0;
}
.advantage-tag-text {
  position: absolute;
  z-index: 2;
  left: 0;
  bottom: 0;
}

.corp-button-wrap {
  padding: 84px 0;
  background: #f4f7ff;
  margin: 0 auto;
}
.corp-button {
  width: 163px;
  height: 60px;
  background: linear-gradient(90deg, #26bff5 0%, #1a7af6 100%);
  border-radius: 60px 60px 60px 60px;
  line-height: 60px;
  font-weight: 600;
  font-size: 20px;
  color: #ffffff;
  text-align: center;
  margin: 0 auto;
  cursor: pointer;
}
</style>
