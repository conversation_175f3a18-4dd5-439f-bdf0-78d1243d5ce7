<template>
  <div class="main-content">
    <section class="main-content-banner">
      <div class="banner-text">
        <h2><span>虚拟人交互解决方案</span></h2>
        <p class="banner-text-content">
          为带屏硬件打造主动交互、言行灵动、知识专业的虚拟人对话体验
        </p>

        <div class="banner-text-button" @click="toConsole">合作咨询</div>
      </div>
    </section>
    <section class="section section-1">
      <div class="section-title">
        <span class="section-title-bold">应用场景</span>
      </div>
      <div class="section-desc">
        适用于业务知识的讲解问答、业务办理、内容播报和信息导览等交互场景
      </div>
      <div class="application-scenes">
        <ul class="left">
          <li
            v-for="(item, index) in applicationScenes"
            :key="index"
            :class="{ active: index === activeIndex }"
            @click="onItemClick(index)"
          >
            {{ item.name }}
          </li>
        </ul>
        <div class="right">
          <div id="application-scenes-swiper" style="width: 100%; height: 100%">
            <div class="swiper-wrapper">
              <div
                class="swiper-slide"
                v-for="(item, index) in applicationScenes"
                :key="index"
                :style="{
                  backgroundImage:
                    'url(' +
                    require(`@A/images/solution/meta-human/${item.bg}.png`) +
                    ')',
                }"
              ></div>
            </div>
          </div>
          <div class="information">
            {{ applicationScenes[activeIndex].desc }}
          </div>
        </div>
      </div>
    </section>

    <div class="section-3-wrap">
      <section class="section section-3">
        <div class="section-title">
          <span class="section-title-bold">方案优势</span>
        </div>
        <div class="section-desc">
          提供公共环境下多模态录音降噪和星火大模型交互整套软硬件解决方案
        </div>
        <ul class="advantage">
          <li>
            <div class="advantage-text">
              <p>
                <span class="advantage-tag">优势一</span
                ><span>多模态收音，嘈杂环境智能降噪</span>
              </p>
              <ul>
                <li>
                  结合唇形的多模态降噪算法，解决公共高噪场景交互<br />难题，人声嘈杂时虚拟人也能听得清。
                </li>
              </ul>
            </div>
            <div class="advantage-image"></div>
          </li>
          <li>
            <div class="advantage-image"></div>
            <div class="advantage-text">
              <p>
                <span class="advantage-tag">优势二</span
                ><span>无需唤醒，主动交互</span>
              </p>
              <ul>
                <li>
                  <i class="tick"></i>多模感知，主动和人打招呼，拉近距离。
                </li>
                <li><i class="tick"></i>不用唤醒，直接自然对话交流。</li>
              </ul>
            </div>
          </li>
          <li>
            <div class="advantage-text">
              <p>
                <span class="advantage-tag">优势三</span
                ><span>本地虚拟人实时交互</span>
              </p>
              <ul>
                <li>
                  <i class="tick"></i>交互动作跟随实时语义，灵动自然宛如真人。
                </li>
                <li><i class="tick"></i>响应更快，节省成本。</li>
                <li><i class="tick"></i>支持Android和Windows系统。</li>
              </ul>
            </div>
            <div
              class="advantage-image"
              style="width: 564px; height: 428px"
            ></div>
          </li>
        </ul>
      </section>
    </div>
    <div class="section-4-wrap">
      <section class="section section-4">
        <div class="section-title">
          <span class="advantage-tag">优势四</span
          ><span>虚拟人形象丰富，1138个形象可选</span>
        </div>

        <meta-human-swiper></meta-human-swiper>
      </section>
    </div>
    <div class="section-5-wrap">
      <section class="section section-5">
        <div class="section-title">
          <span class="advantage-tag">优势五</span
          ><span>人物形象定制 真人克隆</span>
        </div>
        <div class="section-title-side">
          支持真人照片和视频训练，可实现根据语义插入肢体动作互动
        </div>
        <div class="content"></div>
      </section>
    </div>
    <div class="section-6-wrap">
      <section class="section section-6">
        <div class="section-title">
          <span class="advantage-tag">优势六</span
          ><span>大模型交互，打造虚拟人智慧大脑</span>
        </div>
        <div class="content"></div>
      </section>
    </div>
    <div class="section-7-wrap">
      <section class="section section-7">
        <div class="section-title">
          <span class="section-title-bold">产品形态</span>
        </div>
        <div class="content"></div>
      </section>
    </div>

    <corp @jump="toConsole">
      <template> 免费咨询专属顾问 为您量身定制产品推荐方案</template>
    </corp>
  </div>
</template>

<script>
import Swiper, { Autoplay } from 'swiper'
Swiper.use([Autoplay])

import corp from '@P/aiui/solution-aiui/components/corp2.vue'
import metaHumanSwiper from './meta-human-swiper.vue'

export default {
  name: 'offline',
  data() {
    return {
      applicationScenes: [
        {
          name: '政务服务大厅',
          desc: '增设虚拟人用作政务服务新渠道，为市民提供信息查询、业务办理、政策讲解和规章制度宣传教育等服务。用全天候、标准化服务，打造好办事和高效率的政务服务大厅形象。',
          bg: 's1',
        },
        {
          name: '银行数字员工',
          desc: '虚拟人数字员工在银行大堂接待用户，提供窗口叫号、产品推介、业务引导和金融咨询等服务。面对面主动交互与自动化流程让每一位客户都被贴心关照，助力打造轻量智能、客户满意的银行网点。',
          bg: 's2',
        },
        {
          name: '党建内容播报',
          desc: '借助虚拟人播报，高效制作低成本党建宣传视频。通过一对一交谈的互动形式传播党建知识，指导党员学习党的基本方针政策、基础理论、历史沿革与精神，提高学习趣味性与针对性。',
          bg: 's3',
        },
        {
          name: '智慧文旅',
          desc: '塑造活灵活现的虚拟人文化IP，作为虚拟导游为游客介绍景区，推荐交通、住宿与美食，实时回复游客提问，讲解景观与展品。一人服务千万人，降低人工导览成本。',
          bg: 's4',
        },
      ],
      activeIndex: 0,
      swiper: null,
    }
  },
  mounted() {
    this.swiper = new Swiper('#application-scenes-swiper', {
      spaceBetween: 30,
      // effect: 'fade',
      // 改变swiper样式时，自动初始化swiper
      observer: true,
      // 监测swiper父元素，如果有变化则初始化swiper
      observeParents: true,
      loop: true,
      centeredSlides: true,
      direction: 'vertical',
      autoplay: {
        delay: 3000,
        disableOnInteraction: true,
      },
    })

    this.swiper.on('slideChange', () => {
      this.activeIndex = this.swiper.realIndex
    })
  },
  methods: {
    toConsole() {
      // 判断是否有百度SEM营销参数，有的话，提交表单页需要带上
      const search = window.location.search
      if (search) {
        window.open(`/solution/apply/33${search}`)
      } else {
        window.open('/solution/apply/33')
      }
    },

    onItemClick(index) {
      this.swiper && this.swiper.slideToLoop(index)
    },
  },
  components: { corp, metaHumanSwiper },
}
</script>

<style lang="scss" scoped>
.main-content {
  &-banner {
    background: url(~@A/images/solution/meta-human/banner.png) center no-repeat;
    background-size: cover;
    height: 576px;
    overflow: hidden;
    width: 100%;
    .banner-text {
      max-width: 1200px;
      height: 100%;
      margin: auto;

      &-button {
        font-size: 16px;
        text-align: center;
        font-weight: 400;
        width: 140px;
        height: 40px;
        line-height: 40px;
        // border: 1px solid #fff;
        // border-radius: 40px;
        color: #fff;
        cursor: pointer;
        background: $primary;
        border-radius: 4px;
        // background: linear-gradient(0deg, #00b4ff 0%, #2371ff 100%);
        // transition: 0.6s;
      }

      h2 {
        padding-top: 148px;
        margin-bottom: 20px;

        // font-size: 44px;
        // font-weight: 600;
        text-align: left;
        // line-height: 54px;
        span {
          font-size: 48px;
          font-weight: 600;
          text-align: left;
          line-height: 54px;
          color: #0065f9;
          -webkit-text-fill-color: transparent;
          -webkit-background-clip: text;
          background-clip: text;
          background-image: linear-gradient(
            270deg,
            #006cf2 40%,
            #00a9f9 56%,
            #00d7ff 98%
          );
        }

        // background-image: linear-gradient(270deg,#1f6efd,#1ebce8);
      }

      .banner-text-content {
        font-size: 16px;
        font-weight: 400;
        text-align: left;
        color: #282a47;
        line-height: 31px;
        margin-bottom: 100px;
      }
    }
  }

  .section {
    p,
    ul {
      margin-bottom: 0;
    }
    max-width: 1200px;
    overflow: hidden;
    margin: 0 auto;
    .section-title {
      text-align: center;
      font-size: 34px;
      font-family: SourceHanSansSC-Medium, SourceHanSansSC;
      font-weight: bold;
      color: #333;
      line-height: 34px;
      position: relative;
      width: 200px;
      margin: 0 auto;
      .arrow {
        width: 160px;
        height: 8px;
        display: inline-block;
        background-repeat: no-repeat;
        background-size: contain;
        position: absolute;
        z-index: 1;
      }
      .arrow-left {
        background-position: left;
        background-image: url(~@A/images/solution/offline/img_title_01.png);
        top: 50%;
        left: -160px;
      }
      .arrow-right {
        background-position: right;
        background-image: url(~@A/images/solution/offline/img_title_02.png);
        top: 50%;
        right: -160px;
      }
    }
    .section-desc {
      text-align: center;
      margin-top: 30px;
      font-size: 16px;
      font-weight: 400;
      color: #666666;
      line-height: 25px;
    }
    .section-title-bold {
      font-size: 40px;
      font-weight: 500;
      color: #333;
      line-height: 44px;
    }

    .section-tabs {
      margin-top: 70px;
      ul {
        display: flex;
        justify-content: center;
      }
      li {
        font-size: 20px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #666;
        line-height: 30px;
        position: relative;
        cursor: pointer;
        &:hover {
          color: #1f90fe;
        }
        &.active {
          color: #1f90fe;
          &::before {
            position: absolute;
            content: ' ';
            z-index: 1;
            width: 180px;
            height: 4px;
            background: #1f90fe;
            border-radius: 2px;
            bottom: -32px;
            left: -48px;
          }
          &::after {
            position: absolute;
            content: ' ';
            display: inline-block;
            width: 0;
            height: 0;
            bottom: -100px;
            left: 50%;
            transform: translateX(-50%);
            border: 46px solid;
            border-color: transparent transparent #f4f7f9;
          }
        }
      }
      li + li {
        margin-left: 165px;
      }
    }
    .section-sub-title {
      text-align: center;
      font-size: 18px;
      font-weight: 400;
      color: #777777;
      line-height: 25px;
      margin-top: 42px;
    }
    .section-sub-title-spec {
      font-size: 17px;
      font-weight: 400;
      color: #999999;
      line-height: 28px;
      margin-top: 18px;
    }
  }

  .section-3-wrap {
    padding: 80px 0 82px 0;
    background: url(~@A/images/solution/meta-human/adv.png) center/cover
      no-repeat;
    margin-top: 55px;
  }
  .section-3 {
    p {
      margin-bottom: 0;
    }
    // margin-top: 50px;

    .advantage {
      margin-top: 84px;
      > li {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      > li:nth-child(1) {
        .advantage-image {
          background: url(~@A/images/solution/meta-human/<EMAIL>)
            center/contain no-repeat;
        }
        .advantage-text {
          // margin-right: 250px;
        }
      }
      > li:nth-child(2) {
        margin-top: 26px;
        .advantage-image {
          background: url(~@A/images/solution/meta-human/<EMAIL>)
            center/contain no-repeat;
        }
        .advantage-text {
          // padding-top: 91px;
          // margin-left: 302px;
        }
      }
      > li:nth-child(3) {
        margin-top: 75px;

        .advantage-image {
          background: url(~@A/images/solution/meta-human/<EMAIL>)
            center/contain no-repeat;
        }
        .advantage-text {
          // margin-right: 250px;
        }
      }
    }
    .advantage-text {
      .tick {
        width: 25px;
        height: 9px;
        display: inline-block;
        background: url(~@A/images/solution/meta-human/tik.png) left/contain
          no-repeat;
      }

      width: 442px;
      p {
        font-size: 24px;
        font-weight: 600;
        color: #1b2337;
        line-height: 33px;
        display: flex;
        align-items: center;
      }

      ul {
        margin-top: 12px;
        padding-left: 93px;
        li {
          font-size: 16px;
          font-weight: 400;
          color: #7f8894;
          line-height: 30px;
          white-space: nowrap;
        }
      }
    }
    .advantage-image {
      width: 638px;
      height: 361px;
    }
  }

  .section-1 {
    // margin-top: 110px;
  }

  .section-2 {
    max-width: 2560px;
    margin-top: 110px;
  }
  .section-3 {
    // margin-top: 110px;
  }

  .section-4-wrap {
    height: 635px;
    background: #ebf6fd;
    .swiper-wrapper {
      // transition-timing-function: linear !important;
    }
  }

  .section-4 {
    text-align: center;
    p {
      margin-bottom: 0;
    }
    .section-title {
      font-size: 24px;
      font-weight: 600;
      color: #1b2337;
      line-height: 33px;
      width: unset;
      display: flex;
      align-items: center;
      justify-content: center;
      padding-top: 67px;
      margin-bottom: 65px;
    }
  }

  .section-5-wrap {
    height: 603px;
    background: #fff;
  }

  .section-5 {
    text-align: center;
    p {
      margin-bottom: 0;
    }
    .section-title {
      font-size: 24px;
      font-weight: 600;
      color: #1b2337;
      line-height: 33px;
      width: unset;
      display: flex;
      align-items: center;
      justify-content: center;
      padding-top: 67px;
    }
    .section-title-side {
      font-size: 16px;
      font-weight: 400;
      color: #666;
      line-height: 25px;
      margin-top: 12px;
    }
    .content {
      width: 1200px;
      height: 332px;
      margin-top: 64px;
      background: url(~@A/images/solution/meta-human/<EMAIL>) center/contain
        no-repeat;
    }
  }

  .section-6-wrap {
    height: 620px;
    background: #f1f9ff;
  }

  .section-6 {
    text-align: center;
    p {
      margin-bottom: 0;
    }
    .section-title {
      font-size: 24px;
      font-weight: 600;
      color: #1b2337;
      line-height: 33px;
      width: unset;
      display: flex;
      align-items: center;
      justify-content: center;
      padding-top: 67px;
    }
    .content {
      width: 1200px;
      height: 437px;
      background: url(~@A/images/solution/meta-human/<EMAIL>) center/contain
        no-repeat;
      margin-top: 41px;
    }
  }

  .section-7-wrap {
    padding-top: 66px;
    padding-bottom: 63px;
  }
  .section-7 {
    .content {
      width: 1200px;
      height: 412px;
      background: url(~@A/images/solution/meta-human/<EMAIL>)
        center/contain no-repeat;
      margin-top: 44px;
    }
  }

  .application-scenes {
    width: 1200px;
    margin: 32px auto 0;
    height: 426px;
    background: #eff6ff;
    border-radius: 24px;
    padding: 36px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .left {
      width: 260px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      height: 100%;

      > li {
        cursor: pointer;
        width: 100%;
        height: 74px;
        background: #fdfeff;
        border-radius: 12px;
        color: #001225;
        font-size: 16px;
        text-align: center;
        line-height: 74px;
        &:hover,
        &.active {
          color: #fff;
          background: linear-gradient(270deg, #0192fd, #15e6f5);
          box-shadow: 0px 1px 3px 0px rgba(255, 255, 255, 0.5) inset;
          backdrop-filter: blur(7px);
        }
      }
    }
    .right {
      width: 832px;
      height: 100%;
      position: relative;
      overflow: hidden;
      .swiper-slide {
        background-position: center;
        background-size: cover;
      }
      .information {
        position: absolute;
        padding: 41px 36px 0 41px;
        z-index: 1;
        width: 380px;
        height: 186px;
        top: 87px;
        right: 53px;
        background: rgba(255, 255, 255, 0.56);

        border-image: linear-gradient(180deg, #ffffff, rgba(255, 255, 255, 0)) 1
          1;
        border-radius: 30px 6px 30px 6px;
        backdrop-filter: blur(7px);
        color: #001225;
        font-size: 14px;
        line-height: 26px;
      }
    }
  }
}
.section-swiper {
  margin-top: 81px;
  background: #f4f7f9;
  .swiper-wrapper {
    max-width: 1200px;
    .swiper-slide {
      position: relative;
    }
  }
}
.advantage-tag {
  width: 82px;
  height: 26px;
  background: #dbedff;
  border-radius: 4px;
  color: #3083f2;
  line-height: 26px;
  text-align: center;
  display: inline-block;
  font-size: 14px;
  margin-right: 11px;
}
</style>
