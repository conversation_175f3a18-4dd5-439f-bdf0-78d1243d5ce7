<template>
  <div class="os-scroll">
    <div class="keywords-page">
      <repo-header :backToParams="backToParams"></repo-header>
      <div class="mgt28 mgb16 top-edit-wrap">
        <el-button
          class="mgr16"
          icon="ic-r-plus"
          size="small"
          type="primary"
          style="vertical-align: bottom;"
          @click="addRow">
          &nbsp;添加问法关键字
        </el-button>
        <batch
          api="importEntity"
          exportText="导出问法关键字"
          :repoId="repoId"
          @setLoad="setLoad"
          @getData="getList(1)"></batch>
        <div class="fr" @keyup.enter="getList(1)">
          <el-input
            class="search-area"
            placeholder="搜索问法关键字及别名"
            size="medium"
            style="width: 480px;"
            v-model="searchVal">
            <i slot="suffix"
              class="el-input__icon el-icon-search search-area-btn"
              @click="getList(1)" />
          </el-input>
        </div>
      </div>
      <os-table
        class="keyword-reply-table"
        :border="true"
        :tableData="tableData"
        @change="getList"
        @del="toDel"
      >
        <el-table-column
          width="260"
          prop="word"
          label="问法关键字">
          <template slot-scope="scope">
            <el-input
              :ref="'keywordInput'+scope.$index"
              class="keyword-value highlight-wrap"
              size="small"
              placeholder="输入问法关键字，回车添加"
              v-model="scope.row.word"
              :title="scope.row.word"
              @keyup.enter.native="editKeywordBlur"
              @blur="editKeyword(scope.row, scope.$index)">
            </el-input>
          </template>
        </el-table-column>
        <el-table-column
          width="420"
          label="问法关键字别名"
          prop="alias">
          <template slot-scope="scope">
            <div class="alias-edit-wrap highlight-wrap" @click="toEditAlias(scope.row, scope.$index)">
              <template v-if="scope.$index === aliasOnEditIndex || scope.row && !scope.row.id">
                <el-tag
                  v-for="(tag, index) in scope.row.aliasArr"
                  :key="index"
                  closable
                  size="medium"
                  :disable-transitions="false"
                  @close="delTmpAlias(scope.row, index, $event)"
                >
                  <span class="alias-tag" :title="tag">{{tag}}</span>
                </el-tag>
                <el-input
                  class="alias-add"
                  ref="aliasInput"
                  size="small"
                  v-if="!scope.row.id || scope.row.aliasArr && scope.row.aliasArr.length < 200"
                  v-model.trim="scope.row.newAlias"
                  placeholder="输入问法关键字别名，回车添加"
                  @keyup.enter.native="$event.target.blur()"
                  @blur="addAlias(scope.row, $event, scope.$index)"
                ></el-input>
              </template>
              <span v-else >{{scope.row.alias}}</span>
            </div>
          </template>
        </el-table-column>
      </os-table>
    </div>
  </div>
</template>

<script>
import RepoHeader from './repoHeader'
import Batch from './batch'
  export default {
    name: 'qa-relation-keywords',
    data() {
      return {
        searchVal: '',
        tableData: {
          loading: false,
          total: 0,
          page: 1,
          size: 10,
          handles: ['del'],
          handleColumnText: '',
          list: []
        },
        limitCount: 20000,
        initList: [],
        editKeywordEnter: false,
        aliasOnEditIndex: null,
        reg: /^[\u4e00-\u9fffa-zA-Z0-9 \.']{0,}$/,
        cover: {
          isCover: 1,
          text: '批量覆盖',
          api: 'importEntity'
        },
        addOnly: {
          isCover: 2,
          text: '批量追加',
          api: 'importEntity'
        },
      }
    },
    computed: {
      repoId() {
        return this.$route.params.repoId || ''
      },
      backToParams() {
        let tmp = 'qa-relations'
        return tmp
      }
    },
    created(){
      this.getList()
    },
    methods: {
      getList(page) {
        let self = this
        this.tableData.loading = true
        this.aliasOnEditIndex = null
        this.$utils.httpGet(this.$config.api.STUDIO_REPO_KEYWORD_LIST, {
          repositoryId: this.repoId,
          search: this.searchVal,
          pageSize: this.tableData.size,
          pageIndex: page || this.tableData.page
        }, {
          success: (res) => {
          this.tableData.loading = false
            if(!res.data) {
              return self.$message.error(res.desc || '数据获取失败')
            }
            let resList = res.data.list
            self.tableData.total = res.data.count
            self.tableData.list = Array.prototype.map.call(resList, item => {
              item.aliasArr = item.alias ? item.alias.split('|') : []
              return item
            })
            self.initList = res.data.count ? JSON.parse(JSON.stringify(resList)) : []
          },
          error: (err) => {
            this.tableData.loading = false
            self.$router.push({'name': 'studio-handle-platform-qabanks'})
          }
        })
      },
      addRow (type) {
        let self = this
        if(this.tableData.list[0] && !this.tableData.list[0].id) return
        this.tableData.list.unshift({
          word: '',
          newAlias: ''
        })
        if (this.tableData.total % 10 === 0) {
          this.tableData.size = 11
        } else {
          this.tableData.size = 10
        }
        this.tableData.total += 1
        this.editKeywordEnter = false
        self.aliasOnEditIndex = 0
        this.$nextTick(function () {
          self.$refs['keywordInput0'] && self.$refs['keywordInput0'].focus()
        })
      },
      editKeyword (data, index) {
        let self = this
        let len = this.tableData.list.length || 0
        data.word = data.word && data.word.trim()
        if (data.word && data.word.length > 40) {
          return self.$message.warning('关键字不能超过40个字符')
        }
        if(!self.reg.test(data.word)) {
          return self.$message.warning('只支持中文、英文、数字、空格、英文单引号、英文句号')
        }
        let tmp = {
          repositoryId: this.repoId,
          word: data.word,
          alias: data.alias || '',
        }
        if (data.id) {
          tmp.id = data.id
          if (!data.word) {
            return this.$message.warning('关键字不能为空')
          }
          if (self.initList[index].word === data.word) return
        } else {
          if (!data.word) return
        }
        this.$utils.httpPost(this.$config.api.STUDIO_REPO_KEYWORD_ADD_EDIT, tmp, {
          success: (res) => {
            if(!tmp.hasOwnProperty('id')) {
              let firstItem = self.tableData.list[0]
              firstItem.id = res.data.id
              firstItem.alias = res.data.alias
              firstItem.aliasArr = []
              if(self.editKeywordEnter) {
                self.addRow()
              }
            }
            self.initList = JSON.parse(JSON.stringify(self.tableData.list))
          },
          error: (err) => {}
        })
      },
      editKeywordBlur(event){
        this.editKeywordEnter = true
        event.target.blur()
      },
      toDel (data) {
        let self = this
        if(this.aliasOnEditIndex >= 0) {
          this.aliasOnEditIndex = null
        }
        if(!data.id) {
          this.tableData.list.splice(0, 1)
          return
        }
        this.$confirm('删除后所有和该关键字相关的问法均不再支持。',
          `确定删除问法关键字 -  ${data.word}？`, {
          confirmButtonText: '确定删除',
          cancelButtonText: '取消',
          confirmButtonClass: 'el-button--danger',
          type: 'warning',
          showClose: false
        }).then(() => {
          self.delKA(data)
        }).catch(() => {

        })
      },
      delKA(data){
        let self = this
        this.$utils.httpPost(this.$config.api.STUDIO_REPO_KEYWORD_DEL, {
          repositoryId: this.repoId,
          keywordId: data.id
        }, {
          success: (res) => {
            self.$message.success('删除成功')
            self.getList()
          },
          error: (err) => {
          }
        })
      },
      toEditAlias(data, index,e){
        let self = this
        this.aliasOnEditIndex = index
        this.$nextTick(function () {
          self.$refs.aliasInput && self.$refs.aliasInput.focus()
        })
      },
      delTmpAlias (data, index, e) {
        let self = this
        let aliasArr = JSON.parse(JSON.stringify(data.aliasArr))
        let alias = ''
        aliasArr.splice(index, 1)
        if (aliasArr.length) {
          alias = aliasArr.join('|')
        }
        let tmp = {
          repositoryId: this.repoId,
          word: data.word,
          alias: alias,
          id: data.id
        }
        this.tableData.loading = true
        this.$utils.httpPost(this.$config.api.STUDIO_REPO_KEYWORD_ADD_EDIT, tmp, {
          success: (res) => {
            this.tableData.loading = false
            data.aliasArr.splice(index, 1)
            data.alias = data.aliasArr.join('|')
            this.$forceUpdate()
            self.initList = JSON.parse(JSON.stringify(self.tableData.list))
            e.target.focus()
          },
          error: (err) => {
            this.tableData.loading = false
          }
        })
      },
      addAlias(data, e, index) {
        let self = this
        if(!data.id) {
          return self.$message.warning("请先编辑问法关键字")
        }
        if (!data.newAlias) return
        if( data.newAlias == self.initList[index].alias ) return
        if (data.newAlias && data.newAlias.length > 40) {
          return self.$message.warning('关键字别名不能超过40个字符')
        }
        if(!self.reg.test(data.newAlias)) {
          return self.$message.warning('只支持中文、英文、数字、空格、英文单引号、英文句号')
        }
        let alias = data.aliasArr.length ? `${data.alias}|${data.newAlias}` : data.newAlias
        let tmp = {
          repositoryId: this.repoId,
          word: data.word,
          alias: alias,
          id: data.id
        }
        this.$utils.httpPost(this.$config.api.STUDIO_REPO_KEYWORD_ADD_EDIT, tmp, {
          success: (res) => {
            data.aliasArr.push(data.newAlias)
            data.alias = data.aliasArr.join('|')
            data.newAlias = ''
            self.initList = JSON.parse(JSON.stringify(self.tableData.list))
            e.target.focus()
          },
          error: (err) => {
          }
        })
      },
      // 批量操作
      setLoad(val) {
        this.tableData.loading = val
      }
    },
    components: { RepoHeader, Batch }
  }
</script>

<style lang="scss" scoped>
.keywords-page {
  max-width: 1200px;
  margin: 24px auto 56px;
}
.alias-edit-wrap {
  display: flex;
  align-items: center;
  min-height: 56px;
  padding: 6px 12px;
  display: inline-flex;
  flex-flow: wrap;
  height: auto;
  width: 100%;
  box-sizing: border-box;
  border: 1px solid transparent;
  &:hover {
    border: 1px solid $primary;
  }
  .el-tag {
    display: flex;
    align-items: baseline;
    margin-top: 3px;
    margin-right: 3px;
  }
  .alias-tag {
    display: inline-block;
    max-width: 160px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .el-tag + .el-input {
    flex: 1;
    min-width: 100px;
  }
}
</style>
<style lang="scss">
.keywords-page {
  .highlight-wrap {
    min-height: 56px;
    padding: 6px 12px;
    height: auto;
    width: 100%;
    box-sizing: border-box;
    border: 1px solid transparent;
    &:hover {
      border: 1px solid $primary;
    }
  }
  .keyword-value {
    input {
      height: 40px !important;
      line-height: 40px !important;
      border: 0;
      padding-left: 0;
      color: $semi-black;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  .keyword-reply-table {
    .el-table__row td {
      padding: 0;
      height: 56px;
    }
    .el-table__body tr:hover > td {
      background-color: #fff;
    }
  }
  .alias-add {
    margin-left: 4px;
    input {
      height: 22px !important;
      line-height: 22px !important;
      padding-left: 0;
      border: 0;
      color: $semi-black;
    }
  }
}
</style>
