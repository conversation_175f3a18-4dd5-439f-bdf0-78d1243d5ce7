<template>
  <div>
    <el-form :inline="true" class="search-form">
      <el-form-item label="查询设备：">
        <el-input
          class="search-input"
          v-model="search.search"
          placeholder="请输入设备码查询"
          size="medium"
          @keyup.native.enter="getSearchResult"
          style="width: 200px"
        >
          <i
            slot="suffix"
            class="el-input__icon el-icon-search search-area-btn"
            @click="getSearchResult"
          />
        </el-input>
      </el-form-item>
      <el-form-item label="时间：">
        <date-range @setTime="setTime" :clearable="false"></date-range>
      </el-form-item>
      <el-form-item label="设备类型：">
        <el-select
          placeholder="请选择"
          size="medium"
          v-model="search.os"
          style="width: 150px"
        >
          <el-option label="全部" value="" key="all"> </el-option>
          <el-option label="Android SDK" value="1" key="1"> </el-option>
          <el-option label="Linux SDK" value="4" key="4"> </el-option>
          <el-option label="Windows SDK" value="3" key="3"> </el-option>
          <el-option label="iOS SDK" value="2" key="2"> </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" size="medium" @click="getSearchResult"
          >查询</el-button
        >
        <el-button plain @click="exportSnList"
          ><i class="el-icon-download"></i>导出</el-button
        >
      </el-form-item>
    </el-form>
    <div style="color: #ff6300">共搜索到 {{ tableData.sum }} 个设备信息</div>
    <div style="margin: 5px 0 20px 0">
      <el-button
        type="primary"
        size="small"
        @click="importSelectedFail"
        :disabled="multipleSelection.length <= 0"
        >导入设备ID({{ multipleSelection.length }})</el-button
      >
      <el-button
        type="text"
        size="small"
        @click="deleteSelected"
        style="color: #ff5a5a; min-width: 50px"
        :disabled="multipleSelection.length <= 0"
        >删除({{ multipleSelection.length }})</el-button
      >
      <!-- <el-button
        type="text"
        size="small"
        @click="selectAll"
        style="min-width: 80px; margin-left: 0"
        >选择全部</el-button
      > -->
      <span>已选择 {{ multipleSelection.length }} 条</span>
    </div>
    <os-table
      class="app-list-table"
      :tableData="tableData"
      style="margin-bottom: 56px"
      @change="getAppList"
      @selection-change="handleSelectionChange"
      ref="multipleTable"
    >
      <el-table-column type="selection" width="55"> </el-table-column>
      <el-table-column prop="device_id" label="设备信息"> </el-table-column>
      <el-table-column label="注册时间">
        <template slot-scope="scope">
          <!-- {{ $utils.dateFormat(scope.row.createTime) }} -->
          {{ scope.row.time | timeFilter }}
        </template>
      </el-table-column>
    </os-table>
  </div>
</template>
<script>
import dateRange from './dateRange'

export default {
  data() {
    return {
      tableData: {
        loading: true,
        total: 0,
        page: 1,
        size: 80,
        list: [],
        sum: 0,
      },
      search: {
        search: '',
        os: '',
        startDate: '',
        endDate: '',
        timeChanged: false,
      },

      multipleSelection: [],
    }
  },
  components: { dateRange },
  watch: {
    'search.timeChanged'(val) {
      if (val) {
        this.getSnList()
      }
    },
  },

  methods: {
    setTime(start, end) {
      this.search.startDate = start
      this.search.endDate = end
      this.search.timeChanged = true
    },
    getSearchResult() {
      this.getAppList(1)
    },
    getAppList(page) {
      this.tableData.page = page
      this.getSnList()
    },
    exportSnList() {
      let param = this.getSearchParam()
      this.$utils.postopen(this.$config.api.APP_SN_EXPORT_FAIL_DETAIL, param)
    },

    getSearchParam() {
      let param = {
        appid: this.$route.params.appId,
        pageIndex: this.tableData.page,
        pageSize: this.tableData.size,
        search: this.search.search.trim(),
        os: this.search.os,
        startDate: this.search.startDate
          ? `${this.search.startDate} 00:00:00`
          : '',
        endDate: this.search.endDate ? `${this.search.endDate} 23:59:59` : '',
      }

      return param
    },
    getSnList() {
      this.tableData.loading = true
      // 搜索规则：当用户搜索了设备码的时候，无视时间和设备类型，直接展示全部时间段+全部设备类型的内容。
      // 当用户没有输入设备码，按用户选择时间和设备类型来搜索。
      let param = this.getSearchParam()
      this.$utils.httpGet(this.$config.api.APP_SN_SEARCH_FAIL_LIST, param, {
        success: (res) => {
          this.tableData.loading = false
          this.tableData.total = res.data.max * 16
          this.tableData.list = res.data.list
          this.tableData.sum = res.data.count
          this.$nextTick(() => {
            let dom = document.getElementsByClassName('el-pagination__total')[0]
            if (dom) {
              dom.innerHTML = `共 ${res.data.count} 条`
            }
          })
        },
        error: (err) => {
          this.tableData.loading = false
        },
      })
    },

    handleSelectionChange(val) {
      this.multipleSelection = val
      console.log('handleSelectionChange', this.multipleSelection)
    },
    selectAll() {
      console.log(this.$refs.multipleTable)
      this.tableData.list.forEach((row) => {
        this.$refs.multipleTable.toggleRowSelection(row, true)
      })
    },
    deleteSelected() {
      let data = {
        appid: this.$route.params.appId,
        ids: JSON.stringify(
          this.multipleSelection.map((item) => item.device_id)
        ),
      }
      this.$utils.httpPost(this.$config.api.APP_SN_DELETE_FAIL, data, {
        success: (res) => {
          if (res.flag) {
            this.$message.success('删除成功')
            this.$emit('deleted')
            this.getSearchResult()
          }
        },
        error: (err) => {},
      })
    },
    importSelectedFail() {
      let data = {
        appid: this.$route.params.appId,
        ids: JSON.stringify(
          this.multipleSelection.map((item) => item.device_id)
        ),
      }
      this.$utils.httpPost(this.$config.api.APP_SN_IMPORT_FAIL, data, {
        success: (res) => {
          if (res.flag) {
            this.$message.success(res.desc)
            this.$emit('imported')
            this.getSearchResult()
          }
        },
        error: (err) => {},
      })
    },
  },

  filters: {
    timeFilter(val) {
      if (val) {
        let time = val.split('.')
        return time[0]
      } else {
        return '-'
      }
    },
  },
}
</script>
<style lang="scss" scoped>
.search-form {
  margin: 10px 0 20px 0;
  .el-form-item {
    margin-bottom: 0;
    margin-right: 15px;
  }
  :deep(.el-form-item__label) {
    font-weight: normal;
    padding: 0;
  }
  :deep(.el-date-editor .el-range-separator) {
    padding: 0 !important;
  }
}
.online-device-header {
  display: flex;
  margin: 20px 0;
  .online-device-header-item + .online-device-header-item {
    margin-left: 15px;
  }
  :deep(.el-date-editor .el-range-separator) {
    padding: 0 !important;
  }
}
</style>
