<template>
  <div class="aiui-button" :style="{ marginTop: hasTop ? '32px' : 0 }">
    <slot></slot>
  </div>
</template>

<script>
export default {
  name: 'aiui-button',
  props: {
    hasTop: Boolean,
  },
}
</script>

<style lang="scss">
.aiui-button {
  color: #fff;
  background: $primary;
  font-size: 24px;
  width: 280px;
  height: 70px;
  line-height: 70px;
  text-align: center;
  margin: 0 auto;
  cursor: pointer;
  border-radius: 4px;
  &:hover {
    color: $primary;
    border: 1px solid $primary;
    background: #fff;
    transition: 0.3s;
    a {
      color: $primary;
    }
  }
  a {
    color: #fff;
    display: inline-block;
    width: 100%;
    height: 100%;
  }
}
</style>
