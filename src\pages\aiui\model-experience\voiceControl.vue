<template>
  <div>
    <div class="voice" @click="start" v-show="status === 'end'">
      <!-- <img :src="require('@A/images/model-exeperience/v2/<EMAIL>')" /> -->
    </div>
    <div class="voice-active" @click="stop" v-show="status !== 'end'">
      <div class="voice-wave" ref="voiceActive"></div>
      <div class="voice-icon">
        <div class="voice-rect"></div>
      </div>
    </div>
  </div>
</template>
<script>
import lottie from 'lottie-web'
import { animationData } from './data.js'
import Worker from '@C/serviceExperience/transcode.worker.js'

const transWorker = new Worker()
let timer
export default {
  data() {
    return {
      // 状态 end表示停止收音状态 ， ing表示正在录音状态
      status: 'end',
      audioData: new ArrayBuffer(),
    }
  },
  created() {
    let self = this
    transWorker.onmessage = function (event) {
      // self.audioData.push(...event.data)
      self.audioData = self.appendBuffer(self.audioData, event.data)
    }
  },
  mounted() {
    lottie.loadAnimation({
      container: this.$refs['voiceActive'],
      renderer: 'svg',
      loop: true,
      autoplay: true,
      animationData: animationData,
    })
  },

  methods: {
    /**
     * Creates a new Uint8Array based on two different ArrayBuffers
     *
     * @private
     * @param {ArrayBuffers} buffer1 The first buffer.
     * @param {ArrayBuffers} buffer2 The second buffer.
     * @return {ArrayBuffers} The new ArrayBuffer created out of the two.
     */
    appendBuffer(buffer1, buffer2) {
      var tmp = new Uint8Array(buffer1.byteLength + buffer2.byteLength)
      tmp.set(new Uint8Array(buffer1), 0)
      tmp.set(new Uint8Array(buffer2), buffer1.byteLength)
      return tmp.buffer
    },

    start() {
      this.recorderStart()
      this.toggleStatus('ing')
      this.countDown(10, this.stop)
    },
    stop() {
      clearInterval(timer)
      this.recorderStop()
    },
    toggleStatus(status) {
      this.status = status
    },
    recorderStart() {
      if (!this.audioContext) {
        this.recorderInit()
      } else {
        this.audioContext.resume()
      }
    },

    // 暂停录音
    recorderStop() {
      // safari下suspend后再次resume录音内容将是空白，设置safari下不做suspend
      if (
        !(
          /Safari/.test(navigator.userAgent) &&
          !/Chrome/.test(navigator.userAgent)
        )
      ) {
        console.log('------recorderStop---------')
        this.audioContext && this.audioContext.suspend()
      }
      this.toggleStatus('end')
      // this.downloadPCM(this.audioData)
      this.$emit('end', this.audioData)
      this.audioData = new ArrayBuffer()
    },

    downloadPCM(audioData) {
      let urlObject = window.URL || window.webkitURL || window

      let url = urlObject.createObjectURL(
        new Blob([audioData], { type: 'audio/pcm' })
      )
      let a = document.createElement('a')
      a.style.display = 'none'
      a.href = url
      a.download = 'test.pcm'
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)
    },

    // 初始化浏览器录音
    recorderInit() {
      navigator.getUserMedia =
        navigator.getUserMedia ||
        navigator.webkitGetUserMedia ||
        navigator.mozGetUserMedia ||
        navigator.msGetUserMedia

      // 创建音频环境
      try {
        this.audioContext = new (window.AudioContext ||
          window.webkitAudioContext)()
        this.audioContext.resume()
        if (!this.audioContext) {
          alert('浏览器不支持webAudioApi相关接口')
          return
        }
      } catch (e) {
        if (!this.audioContext) {
          alert('浏览器不支持webAudioApi相关接口')
          return
        }
      }

      // 获取浏览器录音权限
      if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
        navigator.mediaDevices
          .getUserMedia({
            audio: true,
            video: false,
          })
          .then((stream) => {
            getMediaSuccess(stream)
          })
          .catch((e) => {
            getMediaFail(e)
          })
      } else if (navigator.getUserMedia) {
        navigator.getUserMedia(
          {
            audio: true,
            video: false,
          },
          (stream) => {
            getMediaSuccess(stream)
          },
          function (e) {
            getMediaFail(e)
          }
        )
      } else {
        if (
          navigator.userAgent.toLowerCase().match(/chrome/) &&
          location.origin.indexOf('https://') < 0
        ) {
          alert(
            'chrome下获取浏览器录音功能，因为安全性问题，需要在localhost或127.0.0.1或https下才能获取权限'
          )
        } else {
          alert('无法获取浏览器录音功能，请升级浏览器或使用chrome')
        }
        this.audioContext && this.audioContext.close()
        return
      }
      // 获取浏览器录音权限成功的回调
      let getMediaSuccess = (stream) => {
        console.log('getMediaSuccess')
        // 创建一个用于通过JavaScript直接处理音频
        this.scriptProcessor = this.audioContext.createScriptProcessor(0, 1, 1)
        this.scriptProcessor.onaudioprocess = (e) => {
          // 去处理音频数据
          if (this.status === 'ing') {
            transWorker.postMessage(e.inputBuffer.getChannelData(0))
          }
        }
        // 创建一个新的MediaStreamAudioSourceNode 对象，使来自MediaStream的音频可以被播放和操作
        this.mediaSource = this.audioContext.createMediaStreamSource(stream)
        // 连接
        this.mediaSource.connect(this.scriptProcessor)
        this.scriptProcessor.connect(this.audioContext.destination)
      }

      let getMediaFail = (e) => {
        alert('请求麦克风失败')
        console.log(e)
        this.audioContext && this.audioContext.close()
        this.audioContext = undefined
      }
    },

    countDown(time, callback) {
      if (time <= 0) {
        return
      }
      timer = setInterval(() => {
        time--
        if (time <= 0) {
          clearInterval(timer)
          callback && callback()
        } else {
        }
      }, 1000)
    },
  },
}
</script>
<style scoped lang="scss">
.voice {
  cursor: pointer;
  width: 24px;
  height: 32px;
  background: url(~@A/images/model-exeperience/v2/<EMAIL>)
    center/contain no-repeat;
}

.voice-active {
  display: flex;
  align-items: center;
  cursor: pointer;
  height: 32px;
  .voice-wave {
    // width: 34px;
    // height: 12px;
    // background: url(~@A/images/model-exeperience/v2/<EMAIL>) center/contain
    //   no-repeat;
  }
  .voice-icon {
    width: 32px;
    height: 32px;
    background: url(~@A/images/model-exeperience/v2/<EMAIL>) center/contain
      no-repeat;
  }
}
</style>
