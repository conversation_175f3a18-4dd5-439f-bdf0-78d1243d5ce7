<template>
  <div>
    <ul class="buy-section-3-header">
      <li>场景</li>
      <li>发音人</li>
      <li>音色</li>
      <li>试听</li>
      <li>价格</li>
      <li>购买</li>
    </ul>
    <el-tabs
      type="card"
      tab-position="left"
      v-model="search.tab"
      :class="search.people == 1 ? 'people' : ''"
    >
      <el-tab-pane
        v-for="item in tabs"
        :label="item.label"
        :key="item.key"
        :name="item.key"
        :disabled="search.people == 1 && item.key != 1"
        v-loading="loading"
      >
        <div class="tab-content" v-if="voicePeopleData.length > 0">
          <div
            v-for="(voice, index) in voicePeopleData"
            :key="voice.name"
            class="tab-content-list"
          >
            <div>{{ voice.name }}</div>
            <div>{{ voice.timbre }}</div>
            <div>
              <span class="san-circle" @click="playAudio(voice)">
                <span
                  class="san"
                  :class="voice.isPlay ? 'play' : 'pause'"
                ></span>
                <span class="er" :class="voice.isPlay ? 'play' : 'pause'">
                  <span></span>
                  <span></span>
                </span>
              </span>
            </div>
            <div>{{ voice.price }}</div>
            <div>
              <el-button
                type="text"
                size="small"
                @click="buyControl(voice, wareData['发音人'])"
                :disabled="voice.status === 1"
                >立即购买</el-button
              >
            </div>
          </div>
        </div>
        <div
          v-else
          class="tab-content"
          style="text-align: center; padding-top: 50px; color: #959595"
        >
          暂无数据
        </div>
      </el-tab-pane>
    </el-tabs>
    <audio ref="audio"></audio>
    <add-to-app :dialog="add2AppDialog"></add-to-app>
  </div>
</template>

<script>
import AddToApp from '../pages/aiui/buy-service/addToApp.vue'
export default {
  name: 'anchorBuy',
  components: {
    AddToApp,
  },
  data() {
    return {
      add2AppDialog: {
        show: false,
      },
      wareData: {},
      voiceData: [
        {
          value: '0',
          label: '所有音色',
        },
        {
          value: '1',
          label: '温柔女声',
        },
        {
          value: '2',
          label: '成年男声',
        },
        {
          value: '3',
          label: '可爱女童',
        },
        {
          value: '4',
          label: '可爱男童',
        },
      ],
      tabs: [
        {
          label: '通用场景',
          key: '1',
        },
        {
          label: '阅读场景',
          key: '2',
        },
        {
          label: '客服场景',
          key: '3',
        },
        {
          label: '交互场景',
          key: '4',
        },
        {
          label: '新闻场景',
          key: '5',
        },
        {
          label: '小说场景',
          key: '6',
        },
        {
          label: '故事场景',
          key: '7',
        },
        {
          label: '多语种场景',
          key: '8',
        },
        {
          label: '方言场景',
          key: '9',
        },
        {
          label: '民族语场景',
          key: '10',
        },
      ],
      loading: false,
      voicePeopleData: [],
      search: {
        people: '2',
        voice: 0,
        tab: '1',
      },
    }
  },
  watch: {
    search: {
      handler(val) {
        this.getInformants()
      },
      deep: true,
    },
  },
  created() {
    this.search.voice = this.voiceData[0].label
    this.getWare()
    this.getInformants()
  },
  methods: {
    getWare() {
      this.$utils.httpGet(
        this.$config.api.GET_APP_WARE,
        {},
        {
          success: (res) => {
            if (res.flag) {
              this.wareData = res.data
            }
          },
          error: (err) => {},
        }
      )
    },
    playAudio(row) {
      if (row.isPlay) {
        row.isPlay = false
        this.$refs.audio.pause()
      } else {
        this.voicePeopleData.forEach((item) => {
          item.isPlay = false
        })
        this.$refs.audio.pause()
        this.$refs.audio.src = row.audio
        this.$refs.audio.play()
        row.isPlay = true
        this.$refs.audio.onended = () => {
          row.isPlay = false
        }
      }
    },

    getInformants() {
      this.loading = true
      let find = this.tabs.find((item) => item.key === this.search.tab),
        timbre = this.voiceData.find((item) => item.value === this.search.voice)
      this.$utils.httpGet(
        this.$config.api.GET_INFORMANTS,
        {
          scene: find.label,
          timbre: timbre && timbre.value !== '0' ? timbre.label : null,
          // status: this.search.people,
        },
        {
          success: (res) => {
            res.data.informants.forEach((item) => {
              item.isPlay = false
              item.price = item.status == 1 ? '免费' : '2万元/年'
            })

            this.voicePeopleData = res.data.informants.slice()
            console.log(this.voicePeopleData)
            this.loading = false
          },
          error: (err) => {},
        }
      )
    },

    buyControl(row, type) {
      if (type == 1701) {
        this.add2AppDialog = {
          show: true,
          wareId: type,
          packageId: type + row.id,
        }
      } else if (type == 7002 || type == 1202) {
        this.add2AppDialog = {
          show: true,
          wareId: type,
          packageId: row.packageId,
        }
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.buy-section-3-header {
  display: flex;
  justify-content: space-around;
  align-items: center;
  height: 56px;
  background: #f2f5f7;
  border: 1px solid #e4e7ed;
  border-bottom: 0;
  li {
    flex: 1;
    text-align: center;
    height: 56px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    color: #677183;
  }
  :deep(.el-input__inner) {
    border: 0;
    width: 120px;
    padding-right: 30px;
    background: #f2f5f7;
  }
}
:deep(.el-tabs--card > .el-tabs__header) {
  border-bottom: 0;
}

:deep(.el-tabs--left .el-tabs__header.is-left) {
  margin-right: 0;
}
:deep(.el-tabs.el-tabs--card.el-tabs--left) {
  .el-tabs__header.is-left {
    width: 16.66%;
  }
  .el-tabs__item {
    height: 56px;
    line-height: 56px;
    text-align: center;
    padding: 0;
    border-left: 5px solid transparent;
  }
  .el-tabs__item.is-active {
    border-left: 5px solid #1784e9;
  }
  .el-tabs__content {
  }
}
:deep(.el-tabs.el-tabs--card.el-tabs--left.people) {
  .el-tabs__header.is-left {
    width: 20%;
  }
}
.tab-content {
  font-size: 14px;
  border: 1px solid #e4e7ed;
  border-left: 0;
  height: 562px;
  overflow-y: auto;
  .tab-content-list {
    display: flex;
    height: 56px;
    justify-content: space-around;
    align-items: center;
    > div {
      flex: 1;
      text-align: center;
      .san-circle {
        width: 30px;
        height: 30px;
        margin: 0 auto;
        border-radius: 50%;
        background: #1784e9;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        .san.play {
          display: none;
        }
        .span.pause {
          display: inline-block;
        }
        .er.play {
          display: inline-block;
        }
        .er.pause {
          display: none;
        }
        .san {
          width: 0;
          height: 0;
          border-left: 10px solid transparent;
          border-right: 10px solid transparent;
          border-top: 10px solid transparent;
          border-bottom: 10px solid #fff;
          border-radius: 3px;
          display: inline-block;
          transform: rotate(90deg);
          position: relative;
          left: 7px;
        }
        .er {
          span {
            display: inline-block;
            width: 4px;
            height: 15px;
            background: #fff;
            margin-top: 5px;
          }
        }
      }
    }
  }
}
</style>
