/**
 * 需求背景：从搜索引擎过来aiui页面，可能携带了相关营销参数，比如 'a=xxxxxx' 这种，后续跳转时，需要保持，最终
 * 在解决方案提交页面提交表单后持久化进后端
 * @url 待处理的url
 * @searchObj 新的search参数对象
 */
import qs from 'qs'
export const transJumpPageUrl = function (url, searchObj = {}) {
  const search = window.location.search || ''
  let originObj = {}
  if (search && search.split('?')[1]) {
    originObj = qs.parse(search.split('?')[1])
  }
  let newSearchObj = Object.assign(originObj, searchObj)
  let newUrl = url.split('?')[0]
  return `${newUrl}?${qs.stringify(newSearchObj)}`
}
