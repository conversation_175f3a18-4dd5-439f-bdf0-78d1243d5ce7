<template>
  <el-dialog :visible.sync="dialog.show" width="680px">
    <div slot="title">
      <p class="el-dialog__title">创建对话示例</p>
      <span style="font-size: 13px"
        >对话示例将用于稳定语言风格，最多添加5轮对话示例</span
      >
    </div>
    <div class="conv-content" ref="scrollContainer">
      <div v-for="(item, index) in convList" :key="index" class="conv-item">
        <div class="item-text">
          <div class="item-conv">
            <div class="user-icon">用户</div>
            <el-input type="textarea" v-model="item.userText" :rows="3" />
          </div>
          <div class="item-conv">
            <img class="role-icon" :src="roleInfo.image" />
            <el-input type="textarea" v-model="item.roleText" :rows="3" />
          </div>
        </div>
        <i class="el-icon-delete del" @click="deleteConv(index)" />
      </div>
      <el-button
        @click="addConv()"
        class="add-btn"
        v-show="convList.length < 10"
      >
        +&nbsp;新增一段对话
      </el-button>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="dialog.show = false"> 取消 </el-button>
      <el-button class="dialog-btn" type="primary" @click="save">
        确定
      </el-button>
    </span>
  </el-dialog>
</template>
<script>
export default {
  name: 'conv-dialog',
  props: {
    roleInfo: {
      type: Object,
      default: () => ({}),
    },
    languageData: {
      type: Array,
      default: () => [],
    },
    dialog: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      convList: [
        // 数据样式
        // {
        //   userText: 'hello',
        //   roleText: '不hello',
        // },
      ],
    }
  },
  methods: {
    addConv() {
      if(this.convList.length >=5){
        this.$message.warning('最多添加5轮对话示例')
      }
      this.convList.push({
        userText: '',
        roleText: '',
      })
      this.$nextTick(() => {
        const element = this.$refs.scrollContainer
        element.scrollTop = element.scrollHeight
      })
    },
    deleteConv(index) {
      this.convList.splice(index, 1)
    },
    save() {
      // 校验总轮数
      if (this.convList.length > 5) {
        this.$message.warning('最多只能保存5轮对话')
        return
      }

      let totalLength = 0
      for (let i = 0; i < this.convList.length; i++) {
        const conv = this.convList[i]
        const userText = conv.userText?.trim() || ''
        const roleText = conv.roleText?.trim() || ''
        const combinedLength = userText.length + roleText.length

        // 校验是否为空
        if (!userText || !roleText) {
          this.$message.warning(`第 ${i + 1} 轮对话中用户或助手内容为空`)
          return
        }

        // 校验单轮长度
        if (combinedLength > 200) {
          this.$message.warning(`第 ${i + 1} 轮对话总长度超过200个字`)
          return
        }

        totalLength += combinedLength
      }

      // 校验总长度
      if (totalLength > 1000) {
        this.$message.warning('所有对话内容总长度不能超过1000个字')
        return
      }

      this.languageData.forEach((item) => {
        if (item.name === '对话示例') {
          item.value = JSON.stringify(this.convList)
        }
      })
      this.$emit('change')
      this.dialog.show = false
    },
  },
  watch: {
    'dialog.show': function (val, oldVal) {
      let self = this
      if (val) {
        let chatData = self.languageData.find(
          (item) => item.name === '对话示例'
        )
        // console.log('chatData', chatData);
        this.convList = chatData.value ? JSON.parse(chatData.value) : []
      }
    },
  },
}
</script>
<style lang="scss" scoped>
.conv-content {
  padding-bottom: 20px;
  max-height: 50vh;
  overflow: auto;
  position: relative;
  .add-btn {
    width: 490px;
    border-radius: 10px;
    margin-left: 86px;
  }
  .conv-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    .item-text {
      .item-conv {
        display: flex;
        gap: 11px;
        margin-bottom: 14px;
      }
      .user-icon {
        width: 75px;
        min-width: 75px;
        height: 75px;
        line-height: 75px;
        text-align: center;
        border: 1px solid #eaeaea;
        border-radius: 50%;
      }
      .role-icon {
        width: 75px;
        height: 75px;
        border-radius: 50%;
      }
      .el-textarea {
        width: 490px;
      }
    }
    .del {
      font-size: 20px;
      cursor: pointer;
      display: none;
    }
    &:hover{
      .del{
        display: inline-block;
      }
    }
  }
}
</style>
