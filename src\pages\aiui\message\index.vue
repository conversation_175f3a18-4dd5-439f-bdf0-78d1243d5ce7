<template>
  <div class="os-scroll">
    <p class="page-title">消息中心</p>
    <div class="page-content">
      <div class="mgb24">
        <el-button size="small" type="primary" @click="markReadAll">全部已读</el-button>
        <el-button size="small" type="primary" :disabled="multipleSelection.length === 0" @click="markRead">所选标记为已读</el-button>
        <el-button size="small" type="primary" :disabled="multipleSelection.length === 0" @click="deleteMessage">删除</el-button>
      </div>
      <el-table
        class="message-table"
        :data="data"
        row-key="id"
        :expand-row-keys="expands"
        @row-click="rowClick"
        @expand-change="handleExpandChange"
        @selection-change="handleSelectionChange">
        <el-table-column type="expand">
          <template slot-scope="props">
            <div class="message-content" v-html="props.row.content"></div>
          </template>
        </el-table-column>
        <el-table-column
          type="selection"
          width="55">
        </el-table-column>
        <el-table-column
          label="消息标题"
          prop="title">
          <template slot-scope="scope">
            <span :class="{'un-read': scope.row.isRead == 0}">{{ scope.row.title }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="时间"
          prop="createTime">
        </el-table-column>
        <el-table-column
          label="类型"
          prop="type">
          <template slot-scope="scope">
            <span>{{ messageType[scope.row.type] }}</span>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        class="message-pagination"
        @current-change="handleCurrentChange"
        :current-page.sync="currentPage"
        :page-size="pageSize"
        :total="count"
        layout="total, prev, pager, next, jumper">
      </el-pagination>
    </div>
  </div>
</template>

<script>
  export default {
    name: '',
    data() {
      return {
        messageType: ['', '产品消息', '运维消息', '用量消息'],
        currentPage: 1,
        pageSize: 10,
        count: 0,
        data: [],
        multipleSelection: [],
        expands: [], // 要展开的行，数值的元素是row的key值
      }
    },
    methods: {
      getMessageList() {
        let self = this
        this.expands.splice(0, this.expands.length)
        this.$utils.httpGet(this.$config.api.USER_MESSAGE_LIST, {
          pageIndex: this.currentPage,
          pageSize: this.pageSize
        }, {
          success: (res) => {
            self.count = res.data.count
            self.data = res.data.list
          },
          error: (err) => {
            console.log('page=>>');
            console.log(err);
          }
        })
      },
      getMessageDetail(row) {
        let self = this
        let data = {
          textId: row.textId
        }
        row.appid ? data.appid = row.appid : ''
        this.$utils.httpGet(this.$config.api.USER_MESSAGE_DETAIL, data, {
          success: (res) => {
            if(row.isRead == 0) {
              self.$store.dispatch('user/setMessageUnReadCount')
            }
            self.data.forEach((item, index) => {
              if(item.id === row.id) {
                self.data[index].content = res.data.content
                self.data[index].isRead = 1
              }
            })
            self.expands.push(row.id);
          },
          error: (err) => {
            console.log('page=>>');
            console.log(err);
          }
        })
      },
      rowClick(row) {
        if(this.expands[0] === row.id) {
          this.expands.pop()
        } else {
          this.expands.pop()
          if(!row.content) {
            this.getMessageDetail(row)
          } else {
            this.expands.push(row.id)
          }
        }
      },
      handleExpandChange(row, expandedRows) {
        this.rowClick(row)
      },
      handleSelectionChange(val) {
        this.multipleSelection = val;
      },
      calcCheckedId() {
        let ids = []
        for(let i = 0; i < this.multipleSelection.length; i++) {
          ids.push(this.multipleSelection[i].id)
        }

        return ids.join(',')
      },
      markReadAll() {
        let self = this
        this.$confirm('您确定要标记全部消息为已读吗？', '全部已读', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$utils.httpPost(this.$config.api.USER_MESSAGE_READ_ALL, {}, {
            success: (res) => {
              self.getMessageList()
              self.$store.dispatch('user/setMessageUnReadCount')
            },
            error: (err) => {
              console.log('page=>>');
              console.log(err);
            }
          })
        }).catch(() => {})
      },
      markRead() {
        let self = this
        this.$utils.httpPost(this.$config.api.USER_MESSAGE_READ, {
          ids: this.calcCheckedId()
        }, {
          success: (res) => {
            self.getMessageList()
            self.$store.dispatch('user/setMessageUnReadCount')
          },
          error: (err) => {
            console.log('page=>>');
            console.log(err);
          }
        })
      },
      deleteMessage() {
        let self = this
        this.$confirm('删除后消息将无法恢复，您确定要删除吗？', '删除消息', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$utils.httpPost(this.$config.api.USER_MESSAGE_DELETE, {
            ids: self.calcCheckedId()
          }, {
            success: (res) => {
              self.getMessageList()
              self.$store.dispatch('user/setMessageUnReadCount')
            },
            error: (err) => {
              console.log('page=>>');
              console.log(err);
            }
          })
        }).catch(() => {})
      },
      handleCurrentChange(val) {
        this.currentPage = val
        this.getMessageList()
      }
    },
    created() {
      this.getMessageList()
    }
  }
</script>

<style lang="scss" scoped>
  .page-title {
    font-size: 36px;
    font-weight: 500;
    text-align: center;
    margin: 64px auto 48px auto;
  }
  .page-content {
    max-width: 1200px;
    margin: auto;
  }
  .message-table {
    color: $grey5;
    margin-bottom: 40px;
  }
  .message-pagination {
    text-align: center;
  }
  .un-read {
    color: $semi-black;
  }
  .message-content {
    padding: 20px 100px;
  }
</style>
