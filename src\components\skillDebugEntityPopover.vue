<template>
  <div 
    class="skill-debug-entity-popover el-popover el-popper el-popover--plain"
    v-if="variablePopover.show"
    v-clickoutside="closePopover"
    :style="popperStyle"
    visible-arrow="false"
    x-placement="bottom-start">
      <p class="entity-item"><span class="tag">槽位标识</span>{{slotInfo.slotMark}}</p>
      <p v-if="entityInIntent && Object.keys(entityInIntent).length && entityInIntent[slotInfo.slotMark]" class="entity-item">
        <span class="tag">对应实体</span>@{{entityInIntent[slotInfo.slotMark]}}</p>
      <p class="entity-item"><span class="tag">对应短语</span>{{slotInfo.slotValue}}</p>
    <div x-arrow="" class="popper__arrow" style="left: 13.5px;"></div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
export default {
  name: 'skill-debug-entity-popover',
  props: {
    slotInfo: {
      type: Object,
      default: {}
    },
    variablePopover: {
      type: Object,
      default: () => ({ 
        show: false
      })
    }
  },
  data() {
    return {
      rect: {
        top: 0,
        left: 0,
        width: 0
      }
    }
  },
  computed: {
    popperStyle() {
      if (this.rect) {
        return {
          top: `${this.rect.top - 40 }px`,
          right: '15px'
        }
      } else {
        return {
          display: `none`
        }
      }
    },
    ...mapGetters({
      entityInIntent: 'studioSkill/entityInIntent'
    })
  },
  watch: {
    'variablePopover.rect': function() {
      this.rect = JSON.parse(JSON.stringify(this.variablePopover.rect))
    }
  },
  methods: {
    closePopover(){
      this.variablePopover.show = false
    }
  }
}
</script>
<style lang="scss" scoped>
.skill-debug-entity-popover-popover {
  position: fixed;
  transform-origin: center top 0px;
  z-index: 2000;
  padding: 8px 0;
}
.entity-item {
  margin-bottom: 8px;
}
.tag {
  display: inline-block;
  margin-right: 12px;
  width:56px;
  font-weight:600;
  color: $grey5;
}

</style>