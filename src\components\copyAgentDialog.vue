<template>
  <el-dialog
    title="复刻智能体"
    :visible.sync="dialogVisible"
    width="550px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      :model="form"
      :rules="rules"
      ref="form"
      label-width="55px"
      label-position="left"
    >
      <el-form-item label="名称：" prop="pluginName">
        <el-input
          v-model.trim="form.pluginName"
          placeholder="支持中英文/数字/小数点/短横线/下划线,不超过32个字符"
          maxlength="32"
        ></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="handleConfirm">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: 'CopyAgentDialog',
  data() {
    return {
      dialogVisible: false,
      form: {
        pluginName: '',
        pluginId: '',
      },
      rules: {
        pluginName: [
          { required: true, message: '请输入智能体名称', trigger: 'blur' },
        ],
      },
    }
  },
  methods: {
    show(item) {
      this.dialogVisible = true
      this.form.pluginId = item.pluginId
      this.form.pluginName = item.pluginName
    },
    handleClose() {
      this.dialogVisible = false
      this.$refs.form.resetFields()
    },
    handleConfirm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.$emit('confirm', this.form)
          this.handleClose()
        }
      })
    },
  },
}
</script>
<style scoped lang="scss">
:deep(.el-form) {
  .el-form-item {
    .el-form-item__label {
      padding: 0;
      &::before {
        float: none;
        position: relative;
      }
    }
  }
}
</style>
