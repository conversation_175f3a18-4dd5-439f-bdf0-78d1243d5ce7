import Axios from 'axios'
import SSO from 'sso/sso.js'
import $config from '../config'

let Server = null
let Root = null
let Connect = null
let BaseURI = null

export const utils = {
  output() {
    return {
      init: this.init,
      BaseURI: this.BaseURI,
      httpGet: this.httpGet,
      publishQaPack: this.publishQaPack,
      qaPublishCheck: this.qaPublishCheck,
      httpPost: this.httpPost,
      experienceUid: this.experienceUid,
      copyClipboard: this.copyClipboard,
      dateFormat: this.dateFormat,
      logout: this.logout,
      setCookie: this.setCookie,
      getCookie: this.getCookie,
      clearCookie: this.clearCookie,
      trimSpace: this.trimSpace,
      strictTrimSpace: this.strictTrimSpace,
      postopen: this.postopen,
      toPage: this.toPage,
      isEqual: this.isEqual,
      orderBy: this.orderBy,
      inArray: this.inArray,
      isEng: this.isEng,
      debounce: this.debounce,
      arraysDiff: this.arraysDiff,
      hasDocument: this.hasDocument,
      scrollIntoView: this.scrollIntoView,
      moneyformat: this.moneyformat,
      getCursortPosition: this.getCursortPosition,
      setCursortPosition: this.setCursortPosition,
      filterUtterSpace: this.filterUtterSpace,
      regTest: this.regTest,
    }
  },
  init(rootVm) {
    let that = this
    Root = rootVm
    Server = Root.$config.server
    const baseUrl = rootVm?.$store?.state?.user?.baseUrl || '/aiui/web' // 0829
    this.BaseURI = `${Root.$config.server}${baseUrl}`
    Axios.defaults.validateStatus = function (status) {
      return status >= 200 && status < 500 // 默认
    }
    let header = {}

    Connect = Axios.create({
      baseURL: `${Root.$config.server}${baseUrl}`,
      // timeout: 5000
      headers: header,
      transformRequest: [
        function (data) {
          if (
            Object.prototype.toString
              .call(data)
              .replace(/\[object|\s|\]/g, '') === 'FormData'
          ) {
            return data
          }
          if (
            Object.prototype.toString
              .call(data)
              .replace(/\[object|\s|\]/g, '') === 'String'
          ) {
            return data
          }
          let ret = ''
          for (let it in data) {
            ret +=
              encodeURIComponent(it) + '=' + encodeURIComponent(data[it]) + '&'
          }
          return ret
        },
      ],
      validateStatus: function (status) {
        return status >= 200 && status < 500 // 默认
      },
      withCredentials: true,
    })
    Connect.interceptors.request.use(
      function (config) {
        // 在发送请求之前做某事
        if (config.params) {
          config.params.ts = new Date().getTime()
        }

        if (localStorage.getItem('AIUI_GLOBAL_VARIABLE')) {
          config.headers['X-Csrf-Token'] = localStorage.getItem(
            'AIUI_GLOBAL_VARIABLE'
          )
        }
        return config
      },
      function (error) {
        // 请求错误时做些事
        return Promise.reject(error)
      }
    )

    // Root.$store.dispatch('user/setUserInfo')

    utils._StringTrim()
  },

  _StringTrim() {
    // 对String的trim方法进行改写
    // String.prototype.trim = function () {
    // return this.replace(/^\s\s*/, '').replace(/\s\s*$/, '')
    // replace(/<[^>]+>/g,"").replace(/&nbsp;/g,"").replace(/\s+([\u4e00-\u9fa5])/ig,'$1').replace(/([\u4e00-\u9fa5])\s+/ig,'$1')
    // }
  },

  /**
   * Get 请求
   */
  httpGet(url, payload, options) {
    Connect.get(url, { params: payload }).then(
      (response) => {
        utils.afterRequest(response, options)
      },
      () => {
        if (options.error) {
          options.error()
        } else {
          return Root.$message.error('服务器繁忙')
        }
      }
    )
  },

  /**
   * 发布问答库
   */
  publishQaPack(url, data) {
    return Connect.post(url, data)
  },

  /**
   * 查询问答库发布状态
   */
  qaPublishCheck(url, data) {
    return Connect.post(url, data)
  },

  /**
   * POST 请求
   */
  httpPost(url, payload, options) {
    Connect.post(url, payload, (options && options.config) || {}).then(
      (response) => {
        utils.afterRequest(response, options)
      },
      () => {
        if (options.error) {
          options.error()
        } else {
          return Root.$message.error('服务器繁忙')
        }
      }
    )
  },

  afterRequest(response, options) {
    if (!response) {
      options.error && options.error()
      return Root.$message.error('服务器繁忙')
    }
    if (response.status === 200 || response.status === 204) {
      if (
        response.data &&
        (response.data.code == '0' || response.data.code == '300002')
      ) {
        // '300002' 用户注册，手机号已注册时返回 300002
        options.success(response.data)
      } else if (
        !!response.headers['content-disposition'] ||
        (response.headers['content-type'] &&
          response.headers['content-type'].indexOf('text/json;charset=UTF-8') >=
            0)
      ) {
        options.success(response)
      } else if (
        response.data &&
        response.data.code &&
        response.data.code == '102019'
      ) {
        /*
         * 避免两个弹窗提示的情景
         * 条件:因修饰语有问题技能编译失败时，flag: false
         */
        options.error(response.data)
      } else {
        // 判断 response.data 类型，
        if (response.data instanceof Blob) {
          // 如果是一个blob对象，放到具体处理逻辑中处理
          options.success(response)
        } else {
          
            !options.noErrorMessage &&
            Root.$message.error(response.data?.desc || '未知错误') // 避免response.data = ''时，报错
          options.error && options.error(response.data)
        }
      }
    } else if (response.status === 401) {
      utils.clearCookie()
      localStorage.removeItem('AIUI_GLOBAL_VARIABLE')
      !options.noMessage && Root.$message.error('登录已过期，请重新登录')
      if (!options.noLogin) {
        setTimeout(function () {
          localStorage.setItem('pagefrom', location.pathname)
          if (Root.$config.env != 'development') {
            utils.toPage(`/user/login?pageFrom=${location.href}`)
          } else {
            Root.$router.push({ name: 'user-login' })
          }
        }, 2000)
      }
    } else {
      options.error && options.error()
      return Root.$message.error('服务器繁忙')
    }
  },

  /**
   * 生成快速体验 uid
   */
  experienceUid() {
    let randomStr = ''
    for (let i = 0; i < 6; i++) {
      let randomNum = Math.ceil(Math.random() * 25)
      randomStr += String.fromCharCode(97 + randomNum)
    }
    return new Date().getTime() + randomStr
  },

  /**
   * 复制到剪切板
   */
  copyClipboard(value) {
    let input = document.createElement('textarea')
    document.body.appendChild(input)
    input.value = value
    input.select()
    document.execCommand('copy')
    document.body.removeChild(input)
    Root.$message.success('复制成功')
  },
  dateFormat(date, format) {
    if (!date) return '-'
    if (date == '-' || date == '--') return date
    var date = new Date(date)
    var paddNum = function (num) {
      num += ''
      return num.replace(/^(\d)$/, '0$1')
    }
    // 指定格式字符
    var cfg = {
      yyyy: date.getFullYear(), // 年 : 4位
      yy: date.getFullYear().toString().substring(2), // 年 : 2位
      M: date.getMonth() + 1, // 月 : 如果1位的时候不补0
      MM: paddNum(date.getMonth() + 1), // 月 : 如果1位的时候补0
      d: date.getDate(), // 日 : 如果1位的时候不补0=
      dd: paddNum(date.getDate()), // 日 : 如果1位的时候补0
      hh: paddNum(date.getHours()), // 时
      mm: paddNum(date.getMinutes()), // 分
      ss: paddNum(date.getSeconds()), // 秒
    }
    format || (format = 'yyyy-MM-dd hh:mm:ss')
    return format.replace(/([a-z])(\1)*/gi, function (m) {
      return cfg[m]
    })
  },

  // 退出登录
  logout(cb) {
    SSO.logout(function () {
      utils.clearCookie()
      Root.$message.success('已退出登录')
      // cb && cb()
      setTimeout(function () {
        utils.toPage('/user/login')
      }, 1000)
    })
  },

  setCookie(name, value) {
    var Days = 30
    var exp = new Date()
    exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000)
    document.cookie = `${name}=${escape(
      value
    )};expires=${exp.toGMTString()};path=/`
  },

  getCookie(name) {
    var arr
    var reg = new RegExp('(^| )' + name + '=([^;]*)(;|$)')
    if ((arr = document.cookie.match(reg))) {
      return unescape(arr[2])
    } else {
      return null
    }
  },

  clearCookie() {
    var keys = document.cookie.match(/[^ =;]+(?=\=)/g)
    // 主账号退出时，避免清楚子账号的 cookies
    if (keys) {
      keys = keys.filter(
        (item) => !['sub_account_id', 'subSessionId'].includes(item)
      )
      for (var i = keys.length; i--; ) {
        document.cookie = keys[i] + '=0;expires=' + new Date(0).toUTCString()
        document.cookie =
          keys[i] + '=0;expires=' + new Date(0).toUTCString() + ';path=/;'
      }
    }
  },

  trimSpace(str) {
    return str
      .replace(/&nbsp;/g, '')
      .replace(/\s+([\u4e00-\u9fa5])/gi, '$1')
      .replace(/([\u4e00-\u9fa5])\s+/gi, '$1')
      .trim()
  },

  strictTrimSpace(str) {
    return str
      .replace(/<[^>]+>/g, '')
      .replace(/&nbsp;/g, '')
      .replace(/\s+([\u4e00-\u9fa5])/gi, '$1')
      .replace(/([\u4e00-\u9fa5])\s+/gi, '$1')
      .trim()
  },

  postopen(URL, PARAMS, isSubAccount) {
    return new Promise((resolve, reject) => {
      try {
        this.httpPost(URL, PARAMS, {
          config: {
            headers: {},
            responseType: 'blob',
          },
          success: (res) => {
            if (
              res.headers['content-type'].indexOf('text/json;charset=UTF-8') >=
                0 ||
              res.headers['content-type'].indexOf(
                'application/json;charset=UTF-8'
              ) >= 0
            ) {
              const reader = new FileReader()
              if (!!res.data) {
                reader.readAsText(res.data, 'utf-8')
                reader.onload = function () {
                  let data = JSON.parse(reader.result)
                  Root.$message.error(data.data || data.desc)
                  return
                }
              }
              return
            }
            let urlObject = window.URL || window.webkitURL || window
            let contentDisposition = res.headers['content-disposition']
            let fileName
            if (contentDisposition) {
              fileName = window.decodeURI(
                res.headers['content-disposition'].split('=')[1],
                'UTF-8'
              )
            }
            let url = urlObject.createObjectURL(
              new Blob([res.data], { type: 'application/octet-stream' })
            )
            let a = document.createElement('a')
            a.style.display = 'none'
            a.href = url
            a.download = fileName
            document.body.appendChild(a)
            a.click()
            window.URL.revokeObjectURL(url)
            document.body.removeChild(a)
            resolve()
          },
          error: (err) => {
            console.log(err)
            Root.$message.error('下载失败，请稍后再试')
            reject()
          },
        })
      } catch (e) {
        console.log(e)
      } finally {
      }
    })
  },

  /*postopen (URL, PARAMS, isSubAccount) {
    try {
      var temp_form = document.createElement('form')
      temp_form.action = `${Root.$config.server}/aiui/${isSubAccount ? 'sub' : ''}web${URL}`
      temp_form.target = '_blank'
      temp_form.method = 'post'
      temp_form.style.display = 'none'
      for (var x in PARAMS) {
        var opt = document.createElement('textarea')
        opt.name = x
        opt.value = PARAMS[x]
        temp_form.appendChild(opt)
      }
      document.body.appendChild(temp_form)
      temp_form.submit()
      jquery(temp_form).ajaxForm(function (message) {
        var messageJson = JSON.parse(message);
        if (!messageJson.flag) {
          alert(messageJson.desc);
        } else {
          alert("保存失败，请联系管理员！" + message);
        }
      });
    } catch (e) {
      console.log(e);
    } finally {
      temp_form.remove()
    }
  },*/

  toPage(path, tohost, target = '_self') {
    let baseHost = ''
    if (Root.$config.env === 'integration') {
      baseHost = 'integration-'
    }
    if (Root.$config.env === 'staging') {
      baseHost = 'staging-'
    }
    const host = {
      www: (baseHost) => `${baseHost}www.iflyos.cn`,
      os: (baseHost) => `${baseHost}open.iflyos.cn`,
      aiui: (baseHost) => `${baseHost}aiui.xfyun.cn`,
      studio: (baseHost) => `${baseHost}studio.iflyos.cn`,
      device: (baseHost) => `${baseHost}device.iflyos.cn`,
      service: (baseHost) => `${baseHost}service.iflyos.cn`,
    }

    let href = ''
    switch (path) {
      // aiui
      case '':
      case '/':
        href = '/'
        break
      //case '/sub/apps':
      //  href = `https://${host.aiui(baseHost)}`
      //  break
      // studio.
      case '/index-studio':
      //case '/cooperation':
      //case '/sub/skills':
      //case '/sub/login':
      //case '/sub/user-info':
      case '/orders':
        href = `https://${host.studio(baseHost)}${path}`
        break
      // device.
      case '/device':
        href = `https://${host.studio(baseHost)}${path}`
        break
      case '/products':
        href = `https://${host.device(baseHost)}${path}`
        break
      // service.
      case '/service':
      case '/services':
        href = `https://${host.service(baseHost)}${path}`
        break
      case '/user/login':
      case '/user/register':
        href = `${path}`
        break
      case '/skills':
        href = `https://${
          tohost ? host[tohost](baseHost) : host.www(baseHost)
        }${path}`
        break
      default:
        href = `${path}`
        break
    }
    if (target === '_self') {
      window.location.href = href
    } else if (target === 'none') {
      return href
    } else {
      window.open(href)
    }
  },

  isEqual(value, other) {
    var type = Object.prototype.toString.call(value)
    if (type !== Object.prototype.toString.call(other)) {
      return false
    }
    if (['[object Array]', '[object Object]'].indexOf(type) < 0) {
      return false
    }
    var valueLen =
      type === '[object Array]' ? value.length : Object.keys(value).length
    var otherLen =
      type === '[object Array]' ? other.length : Object.keys(other).length
    if (valueLen !== otherLen) {
      return false
    }
    var compare = function (item1, item2) {
      var itemType = Object.prototype.toString.call(item1)
      if (['[object Array]', '[object Object]'].indexOf(itemType) >= 0) {
        if (!utils.isEqual(item1, item2)) {
          return false
        }
      } else {
        if (itemType !== Object.prototype.toString.call(item2)) {
          return false
        }
        if (itemType === '[object Function]') {
          if (item1.toString() !== item2.toString()) {
            return false
          }
        } else {
          if (item1 !== item2) {
            return false
          }
        }
      }
    }
    if (type === '[object Array]') {
      for (var i = 0; i < valueLen; i++) {
        if (compare(value[i], other[i]) === false) {
          return false
        }
      }
    } else {
      for (var key in value) {
        if (value.hasOwnProperty(key)) {
          if (compare(value[key], other[key]) === false) {
            return false
          }
        }
      }
    }
    // If nothing failed, return true
    return true
  },
  /**
   * @param arr 待排序的数组
   * @param props 排序条件
   * @param orders 排序规则：可选 默认降序 desc
   */
  orderBy(arr, props, orders) {
    return [...arr].sort((a, b) =>
      props.reduce((acc, prop, i) => {
        if (acc === 0) {
          const [p1, p2] =
            orders && orders[i] === 'desc'
              ? [b[prop], a[prop]]
              : [a[prop], b[prop]]
          acc = p1 > p2 ? 1 : p1 < p2 ? -1 : 0
        }
        return acc
      }, 0)
    )
  },

  /**
   * @param array 目标数组
   * @param val 目标value
   */
  inArray: function (array, val) {
    var s = String.fromCharCode(2)
    var r = new RegExp(s + val + s)
    return r.test(s + array.join(s) + s)
  },

  isEng: function (str) {
    let pattern = new RegExp('[A-Za-z]+')
    return pattern.test(str)
  },
  debounce(fn, wait = 500, immediate) {
    let timer = null
    return function (...args) {
      if (timer) {
        clearTimeout(timer)
      }
      // immediate 为 true时, 表示第一次触发时也执行
      // timer 为空时，表示首次触发
      if (immediate && !timer) {
        fn.apply(this, args)
      }
      timer = setTimeout(() => {
        fn.apply(this, args)
      }, wait)
    }
  },
  /**
   * 提取两个 array 的差异元素
   */
  arraysDiff(a, b) {
    const s = new Set(b)
    return a.filter((x) => !s.has(x))
  },

  hasDocument(str) {
    var el = document.createElement('div')
    el.innerHTML = str
    return el.childElementCount
  },
  scrollIntoView: function (container, selected) {
    if (!selected) {
      container.scrollTop = 0
      return
    }
    const offsetParents = []
    let pointer = selected.offsetParent
    while (pointer && container !== pointer && container.contains(pointer)) {
      offsetParents.push(pointer)
      pointer = pointer.offsetParent
    }
    const top =
      selected.offsetTop +
      offsetParents.reduce((prev, curr) => prev + curr.offsetTop, 0)
    const bottom = top + selected.offsetHeight
    const viewRectTop = container.scrollTop
    const viewRectBottom = viewRectTop + container.clientHeight

    if (top < viewRectTop) {
      container.scrollTop = top
    } else if (bottom > viewRectBottom) {
      container.scrollTop = bottom - container.clientHeight
    }
  },
  moneyformat(num) {
    if (!num) return 0
    num += 0
    return parseFloat(num)
      .toFixed(2)
      .replace(/\B(?=(\d{3})+\b)/g, ',')
      .replace(/^/, '￥')
  },
  // 获取光标位置
  getCursortPosition(element) {
    var caretOffset = 0
    // var element = this.$refs[`content${this.utterance.id}`]
    var doc = element.ownerDocument || element.document
    var win = doc.defaultView || doc.parentWindow
    var sel
    if (typeof win.getSelection !== 'undefined') {
      // 谷歌、火狐
      sel = win.getSelection()
      if (sel.rangeCount > 0) {
        // 选中的区域
        var range = win.getSelection().getRangeAt(0)
        var preCaretRange = range.cloneRange() // 克隆一个选中区域
        preCaretRange.selectNodeContents(element) // 设置选中区域的节点内容为当前节点
        preCaretRange.setEnd(range.endContainer, range.endOffset) // 重置选中区域的结束位置
        caretOffset = preCaretRange.toString().length
      }
    } else if ((sel = doc.selection) && sel.type != 'Control') {
      // IE
      var textRange = sel.createRange()
      var preCaretTextRange = doc.body.createTextRange()
      preCaretTextRange.moveToElementText(element)
      preCaretTextRange.setEndPoint('EndToEnd', textRange)
      caretOffset = preCaretTextRange.text.length
    }
    return caretOffset
  },
  // 设置光标位置
  setCursortPosition(pos, element) {
    var range, selection
    // var element = this.$refs[`content${this.utterance.id}`]
    if (document.createRange) {
      // Firefox, Chrome, Opera, Safari, IE 9+
      range = document.createRange() // 创建一个选中区域
      range.selectNodeContents(element) // 选中节点的内容
      var text = ''
      if (!element.childNodes[pos]) {
        text = document.createTextNode('')
        element.appendChild(text)
        if (element.childNodes.length === pos) {
          pos -= 1
        }
      } else if (element.childNodes[pos].nodeName === 'SPAN') {
        text = document.createTextNode('')
        element.insertBefore(text, element.childNodes[pos])
      }
      range.setStart(element.childNodes[pos], 0) // 设置光标起始为指定位置
      range.collapse(true) // 设置选中区域为一个点
      selection = window.getSelection() // 获取当前选中区域
      selection.removeAllRanges() // 移出所有的选中范围
      selection.addRange(range) // 添加新建的范围
    } else if (document.selection) {
      // IE 8 and lower
      range = document.body.createTextRange() // Create a range (a range is a like the selection but invisible)
      range.moveToElementText(element) // Select the entire contents of the element with the range
      range.collapse(false) // collapse the range to the end point. false means collapse to end rather than the start
      range.select() // Select the range (make it the visible selection
    }
  },
  filterUtterSpace(val) {
    let reg1 = /\s*{.*?}\s*/g
    let reg2 = /\s*\[.*?\]\s*/g
    let reg3 = /\s*\(.*?\)\s*/g
    let res = val.replace(reg1, function (word) {
      return word.trim().replace(/\s{2,}/g, ' ')
    })
    res = res.replace(reg2, function (word) {
      return word.trim().replace(/\s{2,}/g, ' ')
    })
    res = res.replace(reg3, function (word) {
      return word.trim().replace(/\s{2,}/g, ' ')
    })
    return res
  },
  regTest(target, type) {
    if (!type) {
      return false
    }
    let types = {
      phone: /^1[3456789]\d{9}$/,
    }
    return types[type].test(target)
  },
}

export default {
  install(Vue) {
    Vue.prototype.$utils = utils.output()
  },
}
