/**
 * Created by lycheng on 2018/8/6.
 */

 const AUTH_SITES = {
 	development: '',
  integration: 'https://sso.xfyun.cn',
  staging: 'https://sso.xfyun.cn',
  // stagingHF: 'http://sso.server.com',
  stagingHF: 'https://ssodev.xfyun.cn',
   production: 'https://sso.xfyun.cn',
   devops: 'https://sso.xfyun.cn'
 };

 const getAuthSite = (env) => {
  if(env === 'development') {
    return ''
  } else {
    if(location.host === 'teststudio.iflyos.cn' || location.host === 'dev-aiui.xfyun.cn' || location.host === 'test-aiui.xfyun.cn') {
      return AUTH_SITES['stagingHF']
    }else {
      return AUTH_SITES['production']
    }
  }
 }

export default {
  AUTH_SITE: getAuthSite(process.env.NODE_ENV),
  PASSPORT: 'xxx'
}
