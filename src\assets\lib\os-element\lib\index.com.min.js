"use strict";
Object.defineProperty(exports, "__esModule", {value: !0});
var elementUi = require("element-ui"), Main$1 = {
    render: function () {
      var e = this, t = e.$createElement, i = e._self._c || t;
      return i("el-table-column", e._g(e._b({
        scopedSlots: e._u([{
          key: "header", fn: function (t) {
            return [e._v("\n    " + e._s(e.$attrs.label) + "\n    "), e.$attrs.tip ? i("el-tooltip", {
              attrs: {
                effect: "dark",
                content: e.$attrs.tip,
                placement: "right"
              }
            }, [i("i", {class: e.$attrs.icon})]) : e._e()]
          }
        }])
      }, "el-table-column", e.$attrs, !1), e.$listeners), [e._v(" "), e._t("default")], 2)
    }, staticRenderFns: [], name: "OsTableColumn", props: {search: ""}, computed: {}, mounted: function () {
    }, methods: {}
  }, Main$2 = {
    render: function () {
      var e = this.$createElement, t = this._self._c || e;
      return t("span", [this._v("\n  " + this._s(this.$attrs.label) + "\n  "), this.$attrs.tip ? t("el-tooltip", {
        attrs: {
          effect: "dark",
          content: this.$attrs.tip,
          placement: "right"
        }
      }, [t("i", {staticClass: "el-icon-question"})]) : this._e()], 1)
    }, staticRenderFns: [], name: "OsTableQahead", props: {search: ""}, computed: {}, mounted: function () {
    }, methods: {}
  }, Main$3 = {
    render: function () {
      var e = this, t = e.$createElement, i = e._self._c || t;
      return i("div", {staticClass: "os-icon-radios", style: e.styles.radios}, e._l(e.data, function (t, a) {
        return i("div", {
          staticClass: "os-icon-radios-col",
          style: e.styles.radiosCol
        }, [i("div", {
          staticClass: "os-icon-radio",
          class: {"os-icon-radio-active": e.value == t.name},
          on: {
            click: function (i) {
              return e.select(t.name)
            }
          }
        }, [i("i", {class: t.icon}), e._v(" "), i("p", [e._v(e._s(t.name))])])])
      }), 0)
    },
    staticRenderFns: [],
    name: "OsIconRadio",
    model: {prop: "value", event: "change"},
    props: {value: {}, data: Array, gutter: {type: Number, default: 20}},
    data: function () {
      return {}
    },
    computed: {
      styles: function () {
        return {
          radios: {"margin-left": "-" + this.gutter / 2 + "px", "margin-right": "-" + this.gutter / 2 + "px"},
          radiosCol: {"padding-left": this.gutter / 2 + "px", "padding-right": this.gutter / 2 + "px"}
        }
      }
    },
    mounted: function () {
    },
    methods: {
      select: function (e) {
        this.$emit("change", e)
      }
    }
  }, Main$4 = {
    render: function () {
      var e = this, t = e.$createElement, i = e._self._c || t;
      return i("div", {
        staticClass: "os-text-adder",
        class: {"os-text-adder-disabled": e.disabled},
        style: {width: e.width}
      }, [i("ul", [e._l(e.data, function (t, a) {
        return i("li", {
          key: a,
          staticClass: "os-text-adder-row"
        }, [i("label", {staticClass: "os-text-adder-row-index"}, [e._v(e._s(a + 1))]), e._v(" "), i("p", {
          staticClass: "os-text-adder-row-text",
          on: {
            keyup: function (i) {
              return !i.type.indexOf("key") && e._k(i.keyCode, "enter", 13, i.key, "Enter") ? null : e.inputBlur(t, a)
            }
          }
        }, [e.dataKey ? [i("el-input", {
          staticClass: "os-text-adder-row-input dataKey",
          attrs: {size: "medium", title: t[e.dataKey] ? t[e.dataKey] : e.placeholder, placeholder: e.placeholder, disabled: e.disabled},
          on: {
            focus: e.focusHandle, blur: function (i) {
              return e.editt(t, a, arguments)
            }, input: function (i) {
              return e.changet(!1, t, a)
            }
          },
          model: {
            value: t[e.dataKey], callback: function (i) {
              e.$set(t, e.dataKey, i)
            }, expression: "item[dataKey]"
          }
        })] : [i("el-input", {
          staticClass: "os-text-adder-row-input nodatakey",
          attrs: {
            size: "medium",
            placeholder: e.placeholder,
            title: t[e.dataKey] ? t[e.dataKey] : e.placeholder,
            readonly: e.readonly,
            disabled: e.disabled
          },
          on: {
            blur: function (t) {
              return e.editt(e.data[a], a, arguments)
            }, input: function (t) {
              return e.changet(!1, e.data[a], a)
            }, focus: e.focusHandle
          },
          model: {
            value: e.data[a], callback: function (t) {
              e.$set(e.data, a, t)
            }, expression: "data[index]"
          }
        })], e._v(" "), e._t("default", null, {item: t}), e._v(" "), e.disabled ? e._e() : i("i", {
          staticClass: "os-text-adder-row-text-del el-icon-delete",
          on: {
            click: function (i) {
              return e.del(t, a)
            }
          }
        })], 2)])
      }), e._v(" "), e.data.length < e.max ? i("li", {staticClass: "os-text-adder-row 12 input-text"}, [i("label", {staticClass: "os-text-adder-row-index"}, [e._v(e._s(e.data.length + 1))]), e._v(" "), i("p", {
        staticClass: "os-text-adder-row-text",
        on: {
          keyup: function (t) {
            return !t.type.indexOf("key") && e._k(t.keyCode, "enter", 13, t.key, "Enter") ? null : e.add(t)
          }
        }
      }, [i("el-input", {
        staticClass: "os-text-adder-row-input",
        attrs: {size: "medium", placeholder: e.placeholder, title: t[e.dataKey] ? t[e.dataKey] : e.placeholder, disabled: e.disabled},
        on: {
          focus: e.focusHandle, blur: e.add, input: function (t) {
            return e.change(!0, arguments)
          }
        },
        model: {
          value: e.text, callback: function (t) {
            e.text = t
          }, expression: "text"
        }
      }), e._v(" "), i("input", {staticStyle: {display: "none"}, attrs: {type: "text"}})], 1)]) : e._e()], 2)])
    },
    staticRenderFns: [],
    name: "OsTextAdder",
    props: {
      data: Array,
      dataKey: {type: String, default: ""},
      width: {type: String, default: "100%"},
      placeholder: {type: String, default: ""},
      reg: {default: ""},
      warning: {type: String, default: "输入有误"},
      max: {type: Number, default: 999},
      readonly: {type: Boolean, default: !1},
      disabled: {type: Boolean, default: !1}
    },
    data: function () {
      return {text: ""}
    },
    computed: {},
    methods: {
      add: function () {
        this.removeHover(event.target.parentNode.parentNode.parentNode);
        var e = this, t = !0;
        if (this.text = this.text.trim(), this.text) {
          if (this.data.forEach(function (i, a) {
            if ((e.dataKey ? i[e.dataKey] : i) === e.text) return t = !1, e.$message.warning("请勿重复添加")
          }), this.reg && !this.reg.test(this.text)) return t = !1, this.$message.warning(this.warning);
          t && (this.$emit("add", this.text), this.text = "", document.getElementsByClassName("input-text")[0].className += " hover")
        }
      }, inputBlur: function (e, t) {
        e || this.$emit("del", e, t), event.target.blur()
      }, edit: function (e, t) {
        t.target.value = t.target.value.trim(), this.$emit("edit", t.target.value, e)
      }, editt: function (e, t, i) {
        this.removeHover(i[0].srcElement.parentNode.parentNode.parentNode);
        var a = "";
        if (a = this.dataKey ? e[this.dataKey] : e, this.reg && !this.reg.test(a)) return this.$message.warning(this.warning);
        this.$emit("edit", e, t)
      }, del: function (e, t) {
        this.$emit("del", e, t)
      }, delText: function () {
        this.text = ""
      }, change: function (e, t) {
        this.$emit("change", e, event.target.value)
      }, changet: function (e, t, i) {
        var a = "";
        a = this.dataKey ? t[this.dataKey] : t, this.reg && !this.reg.test(a) || (this.$emit("edit", t, i), this.$emit("change", e, event.target.value, i))
      }, addHover: function (e) {
        -1 === e.className.indexOf("hover") && (e.className += " hover")
      }, removeHover: function (e) {
        -1 !== e.className.indexOf("hover") && (e.className = e.className.replace("hover", ""), e.className = e.className.replace(/\s*$/g, ""))
      }, changeInputValue: function (e) {
        this.text = e
      }, focusHandle: function (e) {
        this.addHover(e.srcElement.parentNode.parentNode.parentNode)
      }
    }
  }, Main$5 = {
    render: function () {
      var e = this, t = e.$createElement, i = e._self._c || t;
      return i("div", {
        staticClass: "os-text-star-adder",
        style: {width: e.width}
      }, [i("ul", [e._l(e.data, function (t, a) {
        return i("li", {
          key: a,
          staticClass: "os-text-star-adder-row"
        }, [i("label", {staticClass: "os-text-star-adder-row-index"}, [e._v(e._s(a + 1))]), e._v(" "), i("div", {
          staticClass: "os-text-star-adder-row-text",
          on: {
            keyup: function (t) {
              return !t.type.indexOf("key") && e._k(t.keyCode, "enter", 13, t.key, "Enter") ? null : e.inputBlur(t)
            }
          }
        }, [i("el-input", {
          staticClass: "os-text-star-adder-row-input",
          attrs: {size: "big", placeholder: e.placeholder, value: t.text, title: t.text, readonly: e.readonly},
          on: {
            blur: function (t) {
              return e.edit(a, t)
            }
          },
          model: {
            value: t.text, callback: function (i) {
              e.$set(t, "text", i)
            }, expression: "item.text"
          }
        }), e._v(" "), e._t("default", null, {item: t}), e._v(" "), t && t.score > 0 ? i("div", [i("p", {staticClass: "star-wrap"}, e._l(t.score, function (e) {
          return i("i", {
            key: e,
            class: ["os-text-star-adder-row-text-star el-icon-star-on", {"star-success": t.score && t.score > 3}]
          })
        }), 0), e._v(" "), i("p", {class: ["star-desc", {"star-desc-success": t.score && t.score > 3}]}, [e._v(e._s(t.desc))]), e._v(" "), i("i", {
          staticClass: "os-text-star-adder-row-del el-icon-delete",
          on: {
            click: function (i) {
              return e.del(t, a)
            }
          }
        })]) : i("div", {
          directives: [{
            name: "loading",
            rawName: "v-loading",
            value: e.starLoading,
            expression: "starLoading"
          }]
        })], 2)])
      }), e._v(" "), e.data.length < e.max ? i("li", {staticClass: "os-text-star-adder-row"}, [i("label", {staticClass: "os-text-star-adder-row-index"}, [e._v(e._s(e.data.length + 1))]), e._v(" "), i("p", {
        staticClass: "os-text-star-adder-row-text",
        on: {
          keyup: function (t) {
            return !t.type.indexOf("key") && e._k(t.keyCode, "enter", 13, t.key, "Enter") ? null : e.add(t)
          }
        }
      }, [i("el-input", {
        staticClass: "os-text-star-adder-row-input",
        attrs: {size: "big", placeholder: e.placeholder},
        on: {blur: e.add},
        model: {
          value: e.text, callback: function (t) {
            e.text = "string" == typeof t ? t.trim() : t
          }, expression: "text"
        }
      }), e._v(" "), i("input", {staticStyle: {display: "none"}, attrs: {type: "text"}})], 1)]) : e._e()], 2)])
    },
    staticRenderFns: [],
    name: "OsTextStarAdder",
    props: {
      data: Array,
      width: {type: String, default: "100%"},
      placeholder: {type: String, default: ""},
      reg: {default: ""},
      warning: {type: String, default: "输入有误"},
      max: {type: Number, default: 999},
      readonly: {type: Boolean, default: !1}
    },
    data: function () {
      return {text: "", starLoading: !0}
    },
    methods: {
      add: function () {
        var e = this, t = !0;
        if (this.text) {
          if (e.data.forEach(function (i, a) {
            if (i.text === e.text) return t = !1, e.$message.warning("请勿重复添加")
          }), this.reg && !this.reg.test(this.text)) return t = !1, this.$message.warning(this.warning);
          t && (this.$emit("add", this.text), this.text = "")
        }
      }, inputBlur: function (e) {
        e.target.blur()
      }, edit: function (e, t) {
        t.target.value ? this.$emit("edit", t.target.value, e) : this.del("", e)
      }, editt: function (e, t) {
        this.$emit("edit", e, t)
      }, del: function (e, t) {
        this.$emit("del", e, t)
      }, delText: function () {
        this.text = ""
      }
    }
  },
  BlueFileSvg = "data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMjRweCIgaGVpZ2h0PSIyNHB4IiB2aWV3Qm94PSIwIDAgMjQgMjQiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8IS0tIEdlbmVyYXRvcjogU2tldGNoIDUyLjYgKDY3NDkxKSAtIGh0dHA6Ly93d3cuYm9oZW1pYW5jb2RpbmcuY29tL3NrZXRjaCAtLT4KICAgIDx0aXRsZT5faW1hZ2VzIC8gVGludGVkIEljb25zIC8gaWMtZmlsZS1ibHVlPC90aXRsZT4KICAgIDxkZXNjPkNyZWF0ZWQgd2l0aCBTa2V0Y2guPC9kZXNjPgogICAgPGcgaWQ9Il9pbWFnZXMtLy1UaW50ZWQtSWNvbnMtLy1pYy1maWxlLWJsdWUiIHN0cm9rZT0ibm9uZSIgc3Ryb2tlLXdpZHRoPSIxIiBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPgogICAgICAgIDxwYXRoIGQ9Ik00LjUsMiBMMTQsMiBMMjAuMDUzNTM5OSw2LjA1MzI0ODQxIEwyMC4wNTM1Mzk5LDIxLjUgQzIwLjA1MzUzOTksMjEuNzc2MTQyNCAxOS44Mjk2ODIzLDIyIDE5LjU1MzUzOTksMjIgTDQuNSwyMiBDNC4yMjM4NTc2MywyMiA0LDIxLjc3NjE0MjQgNCwyMS41IEw0LDIuNSBDNCwyLjIyMzg1NzYzIDQuMjIzODU3NjMsMiA0LjUsMiBaIiBpZD0iUmVjdGFuZ2xlIiBmaWxsLW9wYWNpdHk9IjAuMTIiIGZpbGw9IiMxNzg0RTkiPjwvcGF0aD4KICAgICAgICA8cGF0aCBkPSJNNC4zMzMzMzMzMywxLjAwMTg2MTU3IEwxNC4yMTg5NjQxLDEuMDAxODYxNTcgQzE0LjQ0MTMzOTksMS4wMDE4NjE1NyAxNC42NTczNzE4LDEuMDc1OTgzNzggMTQuODMyOTA0NywxLjIxMjUwOTM1IEwyMC42MTM5NDA2LDUuNzA4ODcwNjIgQzIwLjg1NzUyNzIsNS44OTgzMjY4MiAyMSw2LjE4OTYzMjQxIDIxLDYuNDk4MjIyODQgTDIxLDIxLjY1MTk1MTYgQzIxLDIyLjM4ODMzMTMgMjAuNDAzMDQ2MywyMyAxOS42NjY2NjY3LDIzIEw0LjMzMzMzMzMzLDIzIEMzLjU5Njk1MzY3LDIzIDMsMjIuMzg4MzMxMyAzLDIxLjY1MTk1MTYgTDMsMi4zMzUxOTQ5MSBDMywxLjU5ODgxNTI0IDMuNTk2OTUzNjcsMS4wMDE4NjE1NyA0LjMzMzMzMzMzLDEuMDAxODYxNTcgWiBNMTQuMzMzMzMzMyw2LjMzNTE5NDkxIEwxOS4yNDc0NTIsNi4zMzUxOTQ5MSBMMTQuMzMzMzMzMywyLjUxMzEwMjYzIEwxNC4zMzMzMzMzLDYuMzM1MTk0OTEgWiBNMTMsMi4zMzUxOTQ5MSBMNC4zMzMzMzMzMywyLjMzNTE5NDkxIEw0LjMzMzMzMzMzLDIxLjY1MTk1MTYgTDE5LjY2NjY2NjcsMjEuNjUxOTUxNiBMMTkuNjY2NjY2Nyw3LjY2ODUyODI0IEwxNC4zMzMzMzMzLDcuNjY4NTI4MjQgQzEzLjU5Njk1MzcsNy42Njg1MjgyNCAxMyw3LjA3MTU3NDU3IDEzLDYuMzM1MTk0OTEgTDEzLDIuMzM1MTk0OTEgWiIgaWQ9IlNoYXBlIiBmaWxsPSIjMTc4NEU5IiBmaWxsLXJ1bGU9Im5vbnplcm8iPjwvcGF0aD4KICAgIDwvZz4KPC9zdmc+",
  RedFileSvg = "data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMjRweCIgaGVpZ2h0PSIyNHB4IiB2aWV3Qm94PSIwIDAgMjQgMjQiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8IS0tIEdlbmVyYXRvcjogU2tldGNoIDUyLjYgKDY3NDkxKSAtIGh0dHA6Ly93d3cuYm9oZW1pYW5jb2RpbmcuY29tL3NrZXRjaCAtLT4KICAgIDx0aXRsZT5faW1hZ2VzIC8gVGludGVkIEljb25zIC8gaWMtZmlsZS1yZWQ8L3RpdGxlPgogICAgPGRlc2M+Q3JlYXRlZCB3aXRoIFNrZXRjaC48L2Rlc2M+CiAgICA8ZyBpZD0iX2ltYWdlcy0vLVRpbnRlZC1JY29ucy0vLWljLWZpbGUtcmVkIiBzdHJva2U9Im5vbmUiIHN0cm9rZS13aWR0aD0iMSIgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj4KICAgICAgICA8cGF0aCBkPSJNNC41LDIgTDE0LDIgTDIwLjA1MzUzOTksNi4wNTMyNDg0MSBMMjAuMDUzNTM5OSwyMS41IEMyMC4wNTM1Mzk5LDIxLjc3NjE0MjQgMTkuODI5NjgyMywyMiAxOS41NTM1Mzk5LDIyIEw0LjUsMjIgQzQuMjIzODU3NjMsMjIgNCwyMS43NzYxNDI0IDQsMjEuNSBMNCwyLjUgQzQsMi4yMjM4NTc2MyA0LjIyMzg1NzYzLDIgNC41LDIgWiIgaWQ9IlJlY3RhbmdsZSIgZmlsbC1vcGFjaXR5PSIwLjEyIiBmaWxsPSIjRkYzODM4Ij48L3BhdGg+CiAgICAgICAgPHBhdGggZD0iTTQuMzMzMzMzMzMsMS4wMDE4NjE1NyBMMTQuMjE4OTY0MSwxLjAwMTg2MTU3IEMxNC40NDEzMzk5LDEuMDAxODYxNTcgMTQuNjU3MzcxOCwxLjA3NTk4Mzc4IDE0LjgzMjkwNDcsMS4yMTI1MDkzNSBMMjAuNjEzOTQwNiw1LjcwODg3MDYyIEMyMC44NTc1MjcyLDUuODk4MzI2ODIgMjEsNi4xODk2MzI0MSAyMSw2LjQ5ODIyMjg0IEwyMSwyMS42NTE5NTE2IEMyMSwyMi4zODgzMzEzIDIwLjQwMzA0NjMsMjMgMTkuNjY2NjY2NywyMyBMNC4zMzMzMzMzMywyMyBDMy41OTY5NTM2NywyMyAzLDIyLjM4ODMzMTMgMywyMS42NTE5NTE2IEwzLDIuMzM1MTk0OTEgQzMsMS41OTg4MTUyNCAzLjU5Njk1MzY3LDEuMDAxODYxNTcgNC4zMzMzMzMzMywxLjAwMTg2MTU3IFogTTE0LjMzMzMzMzMsNi4zMzUxOTQ5MSBMMTkuMjQ3NDUyLDYuMzM1MTk0OTEgTDE0LjMzMzMzMzMsMi41MTMxMDI2MyBMMTQuMzMzMzMzMyw2LjMzNTE5NDkxIFogTTEzLDIuMzM1MTk0OTEgTDQuMzMzMzMzMzMsMi4zMzUxOTQ5MSBMNC4zMzMzMzMzMywyMS42NTE5NTE2IEwxOS42NjY2NjY3LDIxLjY1MTk1MTYgTDE5LjY2NjY2NjcsNy42Njg1MjgyNCBMMTQuMzMzMzMzMyw3LjY2ODUyODI0IEMxMy41OTY5NTM3LDcuNjY4NTI4MjQgMTMsNy4wNzE1NzQ1NyAxMyw2LjMzNTE5NDkxIEwxMywyLjMzNTE5NDkxIFoiIGlkPSJTaGFwZSIgZmlsbD0iI0ZGMzgzOCIgZmlsbC1ydWxlPSJub256ZXJvIj48L3BhdGg+CiAgICA8L2c+Cjwvc3ZnPg==",
  UploadingItem = {
    render: function () {
      var e = this, t = e.$createElement, i = e._self._c || t;
      return i("li", {staticClass: "os-upload__file"}, [i("embed", {
        staticClass: "os-upload__file__ic-file",
        attrs: {src: e.BlueFileSvg, width: "16", height: "18", type: "image/svg+xml"}
      }), e._v(" "), i("span", {staticClass: "os-upload__file__name"}, [e._v(e._s(e.uploadStatus.name))]), e._v(" "), e.uploadStatus.loading ? i("span", {staticClass: "os-upload__file__percent"}, [e._v(e._s(e.percent))]) : [e.uploadStatus.success ? i("i", {staticClass: "os-upload__file__ic-success ic-r-tick-oval"}) : e._e()], e._v(" "), i("div", {staticClass: "os-upload__file__bar"}, [i("div", {
        staticClass: "os-upload__file__bar-inner",
        style: e.barPercent
      })])], 2)
    }, staticRenderFns: [], props: {uploadStatus: Object}, data: function () {
      return {BlueFileSvg: BlueFileSvg}
    }, computed: {
      percent: function () {
        return parseInt(this.uploadStatus.loaded / this.uploadStatus.total * 100) + "%"
      }, barPercent: function () {
        return {width: this.uploadStatus.loaded / this.uploadStatus.total * 100 + "%"}
      }
    }, mounted: function () {
    }, methods: {}
  }, UploadedItem = {
    render: function () {
      var e = this, t = e.$createElement, i = e._self._c || t;
      return i("li", {
        staticClass: "os-upload__file os-upload__file-uploaded",
        class: {"os-upload__file__active": e.visible}
      }, [i("embed", {
        staticClass: "os-upload__file__ic-file",
        attrs: {src: e.BlueFileSvg, width: "16", height: "18", type: "image/svg+xml"}
      }), e._v(" "), i("span", {staticClass: "os-upload__file__name"}, [e._v(e._s(e.file.name))]), e._v(" "), e.success ? i("i", {staticClass: "os-upload__file__ic-success ic-r-tick-oval"}) : [i("i", {
        staticClass: "os-upload__file__ic-download ic-r-download",
        on: {
          click: function (t) {
            return e.download(e.file)
          }
        }
      }), e._v(" "), i("el-popover", {
        attrs: {placement: "bottom-end", width: "240", trigger: "click"},
        model: {
          value: e.visible, callback: function (t) {
            e.visible = t
          }, expression: "visible"
        }
      }, [i("div", {staticClass: "give-up-save-title"}, [i("i", {staticClass: "ic-r-exclamation"}), e._v(" "), i("span", [e._v("确定删除吗？")])]), e._v(" "), i("p", {staticClass: "give-up-save-content"}, [e._v("删除后不可恢复")]), e._v(" "), i("div", {
        staticStyle: {
          "text-align": "right",
          margin: "0"
        }
      }, [i("el-button", {
        staticStyle: {"min-width": "64px"}, attrs: {size: "mini"}, on: {
          click: function (t) {
            e.visible = !1
          }
        }
      }, [e._v("取消")]), e._v(" "), i("el-button", {
        staticStyle: {"min-width": "64px"},
        attrs: {type: "danger", size: "mini"},
        on: {click: e.remove}
      }, [e._v("确定")])], 1), e._v(" "), i("i", {
        staticClass: "os-upload__file__ic-del ic-r-delete",
        attrs: {slot: "reference"},
        slot: "reference"
      })])]], 2)
    }, staticRenderFns: [], props: {file: Object}, data: function () {
      return {BlueFileSvg: BlueFileSvg, success: !1, visible: !1}
    }, computed: {}, mounted: function () {
      var e = this;
      this.file && this.file.loadingEnd && (this.success = !0, setTimeout(function () {
        e.success = !1
      }, 2e3))
    }, watch: {}, methods: {
      download: function (e) {
        if (/(http|https):\/\/([\w.]+\/?)\S*/.test(e.url)) {
          var t = e.url;
          if (!e.isBlobFile) return void window.open(t);
          var i = e.name || t.substring(t.lastIndexOf("/") + 1).split("?")[0], a = new XMLHttpRequest;
          a.responseType = "blob", a.onload = function () {
            var e = document.createElement("a");
            e.href = window.URL.createObjectURL(a.response), e.download = i, e.style.display = "none", document.body.appendChild(e), e.click()
          }, a.open("GET", t), a.send()
        } else this.$emit("downLoadFromOss", e.url)
      }, remove: function () {
        this.$parent.remove(this.file), this.visible = !1
      }
    }
  }, crypt = require("crypto"), FileTypes = {
    image: "image/*",
    png: "image/png, .png",
    jpg: "image/jpeg, .jpg, .jpeg",
    doc: ".doc,.docx,.xml,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    apk: "application/vnd.android.package-archive",
    zip: "application/zip",
    mp3: ".mp3, audio/mp3",
    m3u: ".m3u, .m3u8, audio/m3u, audio/mpegurl, application/x-mpegurl, audio/x-mpegurl",
    aac: ".aac, audio/aac, audio/vnd.dlna.adts",
    excel: ".csv, .xlsx, .xls, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel, text/csv",
    js: "text/javascript, application/javascript",
    mp4: ".mp4, audio/mp4, video/mp4",
    m4a: ".m4a, audio/x-m4a, video/x-m4a, audio/m4a, video/m4a",
    wav: ".wav, audio/wav, video/wav",
    pcm: ".pcm, audio/pcm, video/pcm",
    json: ".json",
    pdf: ".pdf"
  }, Main$6 = {
    render: function () {
      var e = this, t = e.$createElement, i = e._self._c || t;
      return i("div", {
        staticClass: "os-upload",
        class: {
          "os-upload__list-right": e.options.listRight,
          "os-upload__list-right-middle": 1 === e.options.numLimit && "button" === e.type
        }
      }, [i("el-upload", {
        ref: "upload",
        class: {"os-drap-upload": "drag" === e.type, "os-btn-upload": "button" === e.type},
        attrs: {
          drag: "drag" === e.type,
          action: e.options.action || "",
          data: e.options.data,
          multiple: !0,
          "on-exceed": e.exceed,
          limit: e.options.numLimit,
          "show-file-list": !1,
          "file-list": e.fileList,
          "on-change": e.change,
          "auto-upload": void 0 === e.options.autoUpload || e.options.autoUpload,
          disabled: void 0 !== e.options.disabled && e.options.disabled,
          "before-upload": e.beforeUpload,
          "http-request": e.upload,
          accept: e.accept
        }
      }, ["drag" === e.type ? [i("i", {staticClass: "ic-r-upload"}), e._v(" "), i("p", {staticClass: "el-upload__text"}, [e._v(e._s(e.options.mainText))]), e._v(" "), i("p", {staticClass: "el-upload__tips"}, [e._v(e._s(e.options.mainTips))]), e._v(" "), i("div", {
        staticClass: "el-upload__tip",
        attrs: {slot: "tip"},
        slot: "tip"
      }, [e._v(e._s(e.options.tips))])] : e._e(), e._v(" "), "button" === e.type ? [i("el-button", {
        class: e.options.btnClass || "",
        attrs: {size: e.btnSize || "small", type: e.options.btnType || "primary"}
      }, [e.options.btnNoIcon ? e._e() : i("i", {staticClass: "el-icon-upload"}), e._v("\n        " + e._s(e.options.btnText || "点击上传") + "\n      ")]), e._v(" "), e.options.mainText ? i("p", {
        staticStyle: {"margin-left": "16px"},
        domProps: {innerHTML: e._s(e.options.mainText)}
      }) : e._e()] : e._e()], 2), e._v(" "), i("ul", {staticClass: "os-upload__file-list"}, [e._l(e.uploadFileList, function (e, t) {
        return i("uploading-item", {key: "index" + t, attrs: {uploadStatus: e}})
      }), e._v(" "), e._l(e.fileList, function (t, a) {
        return i("uploaded-item", {key: a, attrs: {file: t}, on: {downLoadFromOss: e.downLoadFromOss}})
      })], 2)], 1)
    },
    staticRenderFns: [],
    name: "OsUpload",
    props: {type: {type: String, default: "drag"}, options: Object, fileList: Array},
    data: function () {
      return {
        btnSize: "",
        FileTypes: FileTypes,
        BlueFileSvg: BlueFileSvg,
        RedFileSvg: RedFileSvg,
        uploadFileList: [],
        visible: !1
      }
    },
    computed: {
      sizeLimit: function () {
        for (var e = this.options.sizeLimit / 1, t = 0; e > 1024;) e /= 1024, t += 1;
        return "" + e + {0: "b", 1: "kb", 2: "M", 3: "G"}[t]
      }, accept: function () {
        var e = "";
        if (this.options.fileType && this.options.fileType.length) {
          e = "";
          for (var t = 0; t <= this.options.fileType.length - 1; t++) e ? e += "," + FileTypes[this.options.fileType[t]] : e = FileTypes[this.options.fileType[t]]
        } else this.options.fileTypeStr && (e = this.options.fileTypeStr);
        return e
      }
    },
    mounted: function () {
    },
    methods: {
      change: function (e, t) {
        var i = this.$refs.upload.$el.getElementsByClassName("el-upload__input")[0].files;
        this.options.beforeUpload && this.options.beforeUpload(i).then(function (e) {
          e && (t.splice(0, t.length), console.log("清空filelist", t))
        })
      }, exceed: function () {
        this.fileList && this.fileList.length >= this.options.numLimit && this.$message.error("超过上传数量")
      }, beforeUpload: function (e) {
        var t = e.size < this.options.sizeLimit;
        if (!t) return this.$message.error("文件大小不能超过" + this.sizeLimit), !1;
        var i = !1;
        if (this.options.fileType && this.options.fileType.length) {
          for (var a = 0; a <= this.options.fileType.length - 1 && !(i = e.type === this.options.fileType[a] || FileTypes[this.options.fileType[a]].indexOf(e.type) >= 0 || e.type.indexOf(this.options.fileType[a]) >= 0); a++) ;
          i || this.$message.error("不支持该文件类型")
        } else if (this.options.fileTypeStr) {
          if (console.log(e), e.type) i = this.options.fileTypeStr.indexOf(e.type) >= 0 || e.type.indexOf(this.options.fileTypeStr) >= 0; else {
            var s = e.name && e.name.split("."), n = s[s.length - 1];
            i = this.options.fileTypeStr.indexOf(n) >= 0 || n.indexOf(this.options.fileTypeStr) >= 0
          }
          i || this.$message.error("不支持该文件类型")
        } else i = !0;
        var l = !0;
        return this.options.fileNameRule && !new RegExp(this.options.fileNameRule).test(e.name) && (this.$message.error(this.options.fileNameTips || "文件名格式有误"), l = !1), t && i && l
      }, upload: function (e) {
        var t = this, i = crypt.createHash("md5"), a = e.file.name + e.file.lastModified + e.file.size,
          s = e.file.name.split("."), n = "", l = "";
        s.length > 1 ? (n = "." + s[s.length - 1], s.pop(), l = s.join(".")) : l = s[0];
        var o = void 0;
        if (console.log(this.options.md5), i.update(a), o = (this.options.setPath && this.options.setPath(e.file) || this.options.stsPath) + "/" + (this.options.noFileName ? "" : l + ".") + i.digest("hex") + n, !1 === this.options.md5 && (o = (this.options.setPath && this.options.setPath(e.file) || this.options.stsPath) + "/" + (this.options.noFileName ? "" : l) + n), console.log(o), this.uploadFileList.unshift({
          name: e.file.name,
          uploadName: o,
          total: 100,
          loaded: 0,
          loading: !0,
          success: !1
        }), this.options.ossService) this.options.ossService.putToOss(this, o, e.file, {
          progress: this.uploadProgress,
          complete: this.uploadComplete,
          fail: this.uploadFail
        }); else {
          var r = new FormData;
          r.append("file", e.file), r.append("type", e.file.type);
          var d = [];
          for (var c in this.options.data || {}) d.push(c);
          Array.prototype.forEach.call(d, function (e) {
            r.append(e, t.options.data[e])
          }), console.log(r.values()), this.$utils && this.$utils.httpPost ? this.$utils.httpPost(e.action, r, {
            config: {onUploadProgress: this.uploadProgress},
            noErrMsg: !0,
            success: this.uploadComplete,
            error: this.uploadFail
          }) : this.uploadFail()
        }
      }, uploadProgress: function (e, t) {
        t ? this.uploadFileList = Array.prototype.map.call(this.uploadFileList, function (i, a) {
          return i.uploadName === t && (i.total = e.total, i.loaded = e.loaded), i
        }) : (this.uploadFileList[0].total = e.total, this.uploadFileList[0].loaded = e.loaded)
      }, uploadComplete: function (e, t) {
        var i = "";
        if (t) this.uploadFileList = Array.prototype.map.call(this.uploadFileList, function (e, a) {
          return e.uploadName === t && (i = e.name), e
        }), this.uploadFileList = Array.prototype.filter.call(this.uploadFileList, function (e, i) {
          return e.uploadName !== t
        }), this.$emit("completecb", {name: i, url: t, loadingEnd: !0}); else {
          var a = this;
          this.uploadFileList = Array.prototype.map.call(this.uploadFileList, function (e, t) {
            return e.loading = !1, e.success = !0, e.total = 100, e.loaded = 100, e
          }), setTimeout(function () {
            a.uploadFileList = [], a.$emit("completecb", e)
          }, 2e3)
        }
      }, uploadFail: function (e, t) {
        this.$message.error("上传失败，请重试"), this.uploadFileList = t ? Array.prototype.filter.call(this.uploadFileList, function (e, i) {
          return e.uploadName !== t
        }) : [], this.$emit("failcb", e)
      }, remove: function (e) {
        this.$emit("remove", e)
      }, downLoadFromOss: function (e) {
        this.options.ossService && this.options.ossService.getFromOss(e)
      }
    },
    components: {UploadingItem: UploadingItem, UploadedItem: UploadedItem}
  }, Main$7 = {
    render: function () {
      var e = this, t = e.$createElement, i = e._self._c || t;
      return i("div", {staticClass: "el-pagination el-pagination--small el-pagination--simple"}, [i("button", {
        staticClass: "btn-prev",
        attrs: {type: "button", disabled: !e.page || 1 === e.page},
        on: {click: e.prePage}
      }, [i("i", {staticClass: "el-icon el-icon-arrow-left"})]), e._v(" "), i("span", {
        staticClass: "el-pagination__jump",
        on: {
          keyup: function (t) {
            return !t.type.indexOf("key") && e._k(t.keyCode, "enter", 13, t.key, "Enter") ? null : e.handleEnter(t)
          }
        }
      }, [i("el-input", {
        ref: "pageInput",
        staticClass: "el-pagination__editor is-in-pagination",
        attrs: {type: "number", autocomplete: "off", min: "1", max: e.totalPage},
        on: {
          blur: function (t) {
            return e.$emit("change", e.page)
          }
        },
        model: {
          value: e.page, callback: function (t) {
            e.page = t
          }, expression: "page"
        }
      }), e._v(" "), e._v("\n    / " + e._s(e.totalPage) + "\n  ")], 1), e._v(" "), i("button", {
        staticClass: "btn-next",
        attrs: {type: "button", disabled: !e.page || e.page === e.totalPage},
        on: {click: e.nextPage}
      }, [i("i", {staticClass: "el-icon el-icon-arrow-right"})])])
    },
    staticRenderFns: [],
    name: "OsPagination",
    model: {prop: "value", event: "change"},
    props: {value: {}, total: {type: Number, default: 0}, size: {type: Number, default: 1}},
    data: function () {
      return {page: 1}
    },
    watch: {
      value: function (e) {
        this.page = e
      }, page: function (e) {
        (e = parseInt(e)) <= 0 ? this.page = 1 : e > this.totalPage && (this.page = this.totalPage)
      }
    },
    computed: {
      totalPage: function () {
        return Math.ceil(this.total / this.size) || 1
      }
    },
    mounted: function () {
    },
    methods: {
      nextPage: function () {
        this.page += 1, this.$emit("change", this.page)
      }, prePage: function () {
        this.page -= 1, this.$emit("change", this.page)
      }, handleEnter: function () {
        this.$refs.pageInput.blur()
      }
    }
  }, Main$8 = {
    render: function () {
      var e = this, t = e.$createElement, i = e._self._c || t;
      return i("div", {
        staticClass: "os-collapse",
        class: {"os-collapse-close": !e.open, "os-collapse-large": "large" === e.size}
      }, [i("div", {
        staticClass: "os-collapse-title",
        
      }, [i("i", {staticClass: "ic-r-angle-d",on: {click: e.change}}), e._v(" "), e.title ? [e._v(e._s(e.title))] : e._t("title")], 2), e._v(" "), i("el-collapse-transition", [i("div", {
        directives: [{
          name: "show",
          rawName: "v-show",
          value: e.open,
          expression: "open"
        }], ref: "collapseContent", staticClass: "os-collapse-content"
      }, [e._t("default")], 2)])], 1)
    }, staticRenderFns: [], name: "OsCollapse", props: {
      size: {
        type: String, default: function () {
          return ""
        }
      }, title: {
        type: String, default: function () {
          return ""
        }
      }, default: {
        type: Boolean, default: function () {
          return !0
        }
      }, value: {type: Boolean}
    }, data: function () {
      return {open: this.value}
    }, watch: {
      default: function (e) {
        this.open = e
      }, value: function (e) {
        this.open = e
      }, open: function (e) {
        this.$emit("input", e)
      }
    }, created: function () {
      this.open = !!this.default
    }, methods: {
      change: function () {
        this.open = !this.open
      }
    }
  }, Main$9 = {
    render: function () {
      var e = this.$createElement, t = this._self._c || e;
      return t("div", {staticClass: "os-alert"}, [t("el-alert", this._b({}, "el-alert", this.$attrs, !1), [this._t("default")], 2)], 1)
    }, staticRenderFns: [], name: "OsAlert", mounted: function () {
    }, methods: {}
  }, Main$10 = {
  render: function () {
    var e = this, t = e.$createElement, i = e._self._c || t;
    return i("div", {
      staticClass: "os-collapse-recommend",
      class: {"os-collapse-close": !e.open, "os-collapse-large": "large" === e.size}
    }, [i("div", {
        staticClass: "os-collapse-title",
        style: e.styles.pointer,
        on: {click: e.change}
      },
      [
        e.edit ?
          [i("el-input", {
            staticClass: "entity-page-form-input",
            attrs: {
              size: "medium",
              placeholder: e.placeholder,
              title: t[e.title] ? t[e.title] : e.placeholder,
              readonly: e.readonly,
              disabled: e.disabled
            },
            /*model: {
              value: e.currentValue, callback: function (t) {
                e.currentValue = t
              }, expression: "currentValue"
            },*/
            on: {
              blur: function (t) {
                //this.title = t.target.value;
                return e.editt(e.title, t, e.category.id)
              }
            },
            nativeOn: {
              click: function (t) {
                if(t.stopPropagation) {
                  t.stopPropagation();
                } else {
                  t.cancelBubble = true;
                }
              },
              keyup: function (t) {
                return !t.type.indexOf("key") && e._k(t.keyCode, "enter", 13, t.key, "Enter") ? null : e.saveTitle()
              },
              blur: function (t) {
                return e.editt(e.title, t, e.category.id)
              }, input: function (t) {
                return e.changet(!1, t, e.category.id)
              }
            },
            model: {
              value:  e.title ? e._s(e.title) : e._t(e.title), callback: function (t) {
                e.title = t
                //e.$set(e.category, "contentName", t)
              }, expression: "e.title"
            }
          }),
            i("i",{staticClass: "entity-page-form-save el-icon-check", on: {click: e.saveTitle}}),
            i("i",{staticClass: "entity-page-form-cancel el-icon-close", on: {click: e.editTitle}}),
          ]
          :
          [i("i",{class: { "ic-r-angle-d": e.category.childrenNum > 0 } }), e._v(" "), e.category.contentName ? e._v(e._s(e.category.contentName)) : e._t(e.category.contentName),
            i("i",{staticClass: "tab-content-category-edit ic-r-edit", on: {click: e.editTitle}, attrs:{
                title: "编辑",
              }})],
        [
          i('div', {staticClass: 'ib basic-info-edit-btn-inner', directives: [{ name: "show",
                rawName: "v-show",
                value: e.category.type > 0 && e.category.type < 2,
                expression: "addChild"}], on: {click: e.addContentTypes}},
            [i('span', [e._v("添加子类")], {staticClass: 'basic-info-edit-btn-span'})]
          )
        ],
        [
          i('div', {staticClass: 'ib basic-info-edit-btn-inner', on: {click: e.editAlbum}},
            [i('span', [e._v("新增专辑")], {staticClass: 'basic-info-edit-btn-span'})]
          )
        ],
        [
          i('div', {staticClass: 'ib basic-info-edit-btn-inner', on: {click: e.confirmDeleteCategory}},
            [i('span', [e._v("删除")], {staticClass: 'basic-info-edit-btn-span'})]
          )
        ]
      ],
      2), e._v(" "),
      i("el-collapse-transition", [i("div", {
          directives: [{
          name: "show",
          rawName: "v-show",
          value: e.open,
          expression: "open"
        }],
          attrs: {slot: "inner"},
          slot: "inner",
        ref: "collapseContent", staticClass: "os-collapse-content"
      }, [e._t("default")], 2)]
      )], 1)
  }, staticRenderFns: [], name: "OsCollapseRecommend", props: {
    category: {
      type: Object,
      default: {
        'childrenNum': 0,
        'contentName': '',
        'type': 0,
        'id': null
      }
    },
    index_1: Number,
    index_2: Number,
    size: {
      type: String, default: function () {
        return ""
      }
    }
  }, data: function () {
    return {open: false,edit: false, title: {
        type: String, default: function () {
          return ""
        }
      }}
  }, watch: {
  }, computed: {
      styles: function () {
        return {
          pointer: {"cursor": "pointer"}
        }
      }
  }, created: function () {
    //this.open = !!this.default
  }, methods: {
    change: function (event, index, index_i) {
      /*if (event.stopPropagation) {
        event.stopPropagation();
      } else {
        event.cancelBubble = true;
      }*/
      this.edit = false
      if (isNaN(event)) {
        this.open = !this.open
        this.$children.forEach(child => {
          if (child.$refs) {
            child.open = false
          }
        })
      } else {
        this.open = true
        this.$children.forEach((child, index_i) => {
          if (child.$refs) {
            if (child.change && child.open === true) {
              child.change(0, index, index_i)
            }
          }
        })
      }

      if (!!this.open && this.category.type > -2) {
        this.$emit('getAlbumOrSongById', this.category, index !== undefined ? index : this.index_1, index_i !== undefined ? index_i : this.index_2)
      }
    },
    editTitle: function (event) {
      if (event.stopPropagation) {
        event.stopPropagation();
      } else {
        event.cancelBubble = true;
      }
      this.title = this.category.contentName
      this.edit = !this.edit
    },
    changet: function (e, t, i) {
      this.title = t.target.value;
      //this.category.contentName = t.target.value;
      //e.$emit("editTitle", e, t.target.value, i)
    },
    editt: function (e, t, i) {
      //return this.$emit('checkTitleLengthAndType', e)
    },
    saveTitle: function (e) {
      if (event.stopPropagation) {
        event.stopPropagation();
      } else {
        event.cancelBubble = true;
      }
      this.$emit("editTitle", e, this.title, this.category, this.index_1, this.index_2, () => this.edit = false)
    },
    addContentTypes: function (e) {
      if (event.stopPropagation) {
        event.stopPropagation();
      } else {
        event.cancelBubble = true;
      }
      this.$emit('addContentTypes', 2, this.category.id)
    },
    editAlbum: function (e) {
      if (event.stopPropagation) {
        event.stopPropagation();
      } else {
        event.cancelBubble = true;
      }
      this.$emit('editAlbum', 1, null, this.category, this.index_1, this.index_2)
    },
    confirmDeleteCategory: function (e) {
      if (event.stopPropagation) {
        event.stopPropagation();
      } else {
        event.cancelBubble = true;
      }
      this.$emit('confirmDeleteCategory', this.category, this.index_1, this.index_2)
    }
  }
},components = [/*Main,*/ Main$1, Main$3, Main$2, Main$4, Main$6, Main$5, Main$7, Main$8, Main$9, Main$10];

export const osELement = function osELement(e, t) {
  e.use(elementUi.Autocomplete), e.use(elementUi.Button), e.use(elementUi.ButtonGroup), e.use(elementUi.Input), e.use(elementUi.InputNumber), e.use(elementUi.Table), e.use(elementUi.TableColumn), e.use(elementUi.Pagination), e.use(elementUi.Loading.directive), e.use(elementUi.Tooltip), e.use(elementUi.Radio), e.use(elementUi.RadioGroup), e.use(elementUi.RadioButton), e.use(elementUi.Checkbox), e.use(elementUi.CheckboxGroup), e.use(elementUi.Switch), e.use(elementUi.Slider), e.use(elementUi.Menu), e.use(elementUi.MenuItem), e.use(elementUi.Submenu), e.use(elementUi.MenuItemGroup), e.use(elementUi.Select), e.use(elementUi.Option), e.use(elementUi.DatePicker), e.use(elementUi.Form), e.use(elementUi.FormItem), e.use(elementUi.Dialog), e.use(elementUi.Dropdown), e.use(elementUi.DropdownMenu), e.use(elementUi.DropdownItem), e.use(elementUi.Popover), e.use(elementUi.Progress), e.use(elementUi.Upload), e.use(elementUi.Collapse), e.use(elementUi.CollapseItem), e.use(elementUi.Tag), e.use(elementUi.Alert), e.use(elementUi.Tabs), e.use(elementUi.TabPane), e.use(elementUi.Badge), e.use(elementUi.Scrollbar), e.use(elementUi.Container), e.use(elementUi.Aside), e.use(elementUi.Main), e.use(elementUi.Row), e.use(elementUi.Col), e.use(elementUi.Card), e.use(elementUi.Breadcrumb), e.use(elementUi.BreadcrumbItem), e.use(elementUi.Cascader), e.use(elementUi.CascaderPanel), e.use(elementUi.OptionGroup), e.use(elementUi.Step), e.use(elementUi.Steps), e.use(elementUi.Carousel), e.use(elementUi.CarouselItem), e.component(elementUi.CollapseTransition.name, elementUi.CollapseTransition), e.prototype.$message = elementUi.Message, e.prototype.$alert = elementUi.MessageBox.alert, e.prototype.$confirm = elementUi.MessageBox.confirm, components.forEach(function (t) {
    e.component(t.name, t)
  })
}
