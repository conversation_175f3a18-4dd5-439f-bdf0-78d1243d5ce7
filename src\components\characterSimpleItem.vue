<template>
  <div class="os-character-simple-item">
    <img
      v-if="url"
      :src="`http://cdn.iflyos.cn/public/character/${url}.jpg`"
      class="icon-image"
    />
    <div class="os-character-simple-item-thumb" v-else>
      <span>{{ name.slice(0, 1) }}</span>
    </div>
    <p class="os-character-simple-item-name" :title="name">{{ name }}</p>
  </div>
</template>

<script>
export default {
  name: 'OsCharacterSimpleItem',
  props: {
    url: {
      type: String,
      default: '',
    },
    name: {
      type: String,
      default: '',
    },
  },
  data() {
    return {}
  },
  computed: {},
  mounted() {},
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="scss" scoped>
.os-character-simple-item {
  display: inline-flex;
  align-items: center;
  .icon-image {
    width: 40px;
    min-width: 40px;
    height: 40px;
    line-height: 40px;
    margin: 10px 0;
    margin-right: 12px;
    border-radius: 24px;
  }
  &-thumb {
    margin: 16px 0;
    width: 40px;
    min-width: 40px;
    height: 40px;
    border-radius: 24px;
    box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.05);
    border: 1px solid $grey2;
    font-size: 24px;
    text-align: center;
    line-height: 40px;
    color: $semi-black;
    background: $white;
    margin-right: 24px;
  }
  &-name {
    color: $semi-black;
    font-size: 14px;
    font-weight: 500;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}
</style>
