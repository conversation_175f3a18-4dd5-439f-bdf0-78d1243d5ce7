<template>
  <div>
    <div class="num-card-wrap">
      <div class="num-card">
        <div class="num_card_left">
          <img src="@A/images/aiui/used-times.jpg" alt="" />
        </div>
        <div class="num_card_right">
          <div class="num_card_right_top">
            <div class="title">已使用（次数）</div>
            <el-tooltip class="item" effect="dark" placement="bottom">
              <i class="el-icon-more" />
              <div slot="content">
                <p class="num-tip-title">使用次数（包含免费调用的使用次数）</p>
                <p v-for="(item, key) in statisticUsedInfo" :key="key">
                  <span v-if="key != 'all'"
                    >{{ key | statisticUsedInfo }}&nbsp;&nbsp;{{ item }}次
                  </span>
                </p>
              </div>
            </el-tooltip>
          </div>
          <div class="num_card_right_bottom">
            <div class="statistic-tip">
              <span class="txt">AIUI普通版&nbsp;</span
              ><span class="num">{{ statisticUsedInfo['all'] }}</span>
            </div>
            <div class="statistic-tip">
              <span class="txt">AIUI大模型版&nbsp;</span
              ><span class="num">{{ statisticUsedInfo['model'] }}</span>
            </div>
          </div>
        </div>
      </div>

      <div class="num-card">
        <div class="num_card_left">
          <img src="@A/images/aiui/remaining-times.jpg" alt="" />
        </div>
        <div class="num_card_right">
          <div class="num_card_right_top">
            <div style="display: flex; align-items: center">
              <div class="title">剩余（次数）</div>
              <a
                v-if="!subAccount"
                :href="`${$config.xfyunConsole}sale/buy?platform=aiui&wareId=1701&appId=${appId}&appName=${appName}&serviceName=AIUI服务`"
                target="_blank"
                >提升交互次数</a
              >
            </div>
            <el-tooltip class="item" effect="dark" placement="bottom">
              <i class="el-icon-more" />
              <div slot="content">
                <p class="num-tip-title">剩余次数</p>
                <p>
                  <span class="num-tip-type">AIUI普通版</span>
                  {{ appSurplusCount }} 次
                </p>
                <p>
                  <span class="num-tip-type">AIUI大模型版</span>
                  {{ appSurplusCountModel }} 次
                </p>
              </div>
            </el-tooltip>
          </div>
          <div class="num_card_right_bottom">
            <div class="statistic-tip">
              <span class="txt">AIUI普通版&nbsp;</span
              ><span class="num">{{
                !!businessType && businessType === 1 ? '不限' : appSurplusCount
              }}</span>
            </div>
            <div class="statistic-tip">
              <span class="txt">AIUI大模型版&nbsp;</span
              ><span class="num">{{ appSurplusCountModel }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="os_table_wrapper">
      <os-table :tableData="tableData">
        <el-table-column label="类型" width="150">
          <template slot-scope="scope">
            <span class="table-cell-type" :title="scope.row.servicename">{{
              scope.row.servicename
            }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="startTime" label="开始时间" width="150">
          <template slot-scope="scope">
            {{ scope.row.startTime | date('yyyy-MM-dd') }}
          </template>
        </el-table-column>
        <el-table-column prop="endTime" label="结束时间" width="150">
          <template slot-scope="scope">
            {{ scope.row.endTime | date('yyyy-MM-dd') }}
          </template>
        </el-table-column>
        <el-table-column label="使用量" width="150">
          <template slot-scope="scope">
            <span v-if="scope.row.usedCount >= 0">
              {{ scope.row.usedCount }}次</span
            >
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="剩余量" width="150">
          <template slot-scope="scope">
            <span v-if="scope.row.allCount >= 0">
              {{ scope.row.allCount - scope.row.usedCount }}次</span
            >
            <span v-else>-</span>
          </template>
        </el-table-column>
      </os-table>
    </div>
  </div>
</template>
<script>
import { mapGetters } from 'vuex'

export default {
  data() {
    return {
      statisticUsedInfo: {
        all: 0,
      },

      //大模型调用量
      // modelAll: 0,

      businessType: null,
      tableData: {
        loading: false,
        total: '',
        page: 1,
        size: 10,
        needHandle: true,
        list: [],
      },

      appUsedCount: 0,
      appSurplusCount: 0,

      appUsedCountModel: 0,
      appSurplusCountModel: 0,
    }
  },
  computed: {
    appId() {
      return this.$route.params.appId
    },
    appName() {
      return this.$store.state.aiuiApp.app.appName
    },
    platform() {
      return this.$store.state.aiuiApp.app.platform || ''
    },
    ...mapGetters({
      subAccount: 'user/subAccount',
    }),
  },
  watch: {
    platform() {
      this.init()
    },
  },
  created() {
    if (this.platform) {
      this.init()
      this.getAppBusiness()
    }
  },
  methods: {
    getAppBusiness() {
      this.$utils.httpGet(
        this.$config.api.SERVICE_STATISTICS_GETAPPBUSINESS,
        {
          appid: this.appId,
        },
        {
          success: (res) => {
            if (res.flag) {
              if (res.data) {
                this.businessType = res.data.businessType
              }
            } else {
              this.$message.error(res.desc)
            }
          },
          error: (err) => {},
        }
      )
    },
    init() {
      this.tableData.list.splice(0)
      this.getAppUseInfo()
      this.getAppUseInfoModel()
      this.getAllUsed()
      this.getModelAllUsed()
    },
    getAppUseInfo() {
      let self = this
      this.$utils.httpGet(
        this.$config.api.AIUI_STATISTIC_WEBAPI_TABEL,
        {
          appid: this.appId,
        },
        {
          success: (res) => {
            self.appUsedCount = 0
            self.appSurplusCount = 0
            if (!res.data || (res.data && !res.data.length)) {
              return
            }
            self.tableData.list = self.tableData.list.concat(
              (res.data || []).map((item) => {
                return {
                  ...item,
                  servicename:
                    item.servicename === '免费包'
                      ? 'AIUI普通版免费包'
                      : item.servicename,
                }
              })
            )

            let totalAllCount = 0
            for (let i = 0; i < res.data.length; i++) {
              self.appUsedCount += res.data[i].usedCount
              totalAllCount += res.data[i].allCount
            }
            self.appSurplusCount = totalAllCount - self.appUsedCount
          },
          error: (err) => {},
        }
      )
    },

    getAppUseInfoModel() {
      let self = this
      this.$utils.httpGet(
        this.$config.api.AIUI_APP_SERVICE_PACKAGE,
        {
          appid: this.appId,
        },
        {
          success: (res) => {
            self.appUsedCountModel = 0
            self.appSurplusCountModel = 0

            if (res.data && res.data.packages) {
              self.tableData.list = self.tableData.list.concat(
                res.data.packages || []
              )
            }

            let totalAllCount = 0
            for (let i = 0; i < res.data.packages.length; i++) {
              self.appUsedCountModel += Number(res.data.packages[i].usedCount)
              totalAllCount += Number(res.data.packages[i].allCount)
            }
            self.appSurplusCountModel = totalAllCount - self.appUsedCountModel
          },
          error: (err) => {},
        }
      )
    },
    getAllUsed() {
      let self = this
      this.$utils.httpGet(
        this.$config.api.AIUI_STATISTIC_ALL_USED,
        {
          appid: this.appId,
        },
        {
          success: (res) => {
            // {all: 0, SDK: 0, source: 0}
            // self.statisticUsedInfo = res.data || {}
            Object.keys(res.data || {}).forEach((k) => {
              self.$set(self.statisticUsedInfo, k, res.data[k])
            })
          },
          error: (err) => {},
        }
      )
    },

    getModelAllUsed() {
      let self = this
      this.$utils.httpGet(
        this.$config.api.AIUI_APP_STATISTICS_ALL_COUNT,
        {
          appid: this.appId,
        },
        {
          success: (res) => {
            self.$set(self.statisticUsedInfo, 'model', res.data.model || 0)
          },
          error: (err) => {},
        }
      )
    },
  },
}
</script>

<style lang="scss" scoped>
.num-card-wrap {
  display: flex;
  margin-bottom: 20px;
}
.os_table_wrapper {
  border-radius: 12px;
  overflow: hidden;
  :deep(.os-table) {
    .el-table .el-table__header-wrapper tr th,
    .el-table--fit .el-table__header-wrapper tr th {
      background-color: #f5f9fd;
      padding: 15px 0;
    }
    .el-table .el-table__header-wrapper,
    .el-table--fit .el-table__header-wrapper {
      height: 54px !important;
    }
  }
}

.num-card-left {
  background: #fff;
  // box-shadow: 0px 4px 16px 0px rgba(161, 196, 253, 0.4);
  // background: url(~@A/images/aiui5/static-left.png) right/cover no-repeat;
}
.num-card-right {
  padding: 16px 24px;
  // background: #8fd3f4;
  // box-shadow: 0px 4px 16px 0px rgba(143, 211, 244, 0.4);
  background: url(~@A/images/aiui5/static-right.png) right/cover no-repeat;
  .title {
    margin-bottom: 0;
  }
}
.num-card {
  flex: 1;
  position: relative;
  margin-right: 16px;
  padding: 20px;
  background: #fff;
  border-radius: 12px;
  display: flex;
  &:last-child {
    margin-right: 0;
  }
  .ic-r-more {
    position: absolute;
    top: 8px;
    right: 12px;
    z-index: 2;
    cursor: pointer;
    &::before {
      color: #fff;
      font-weight: bold;
      font-size: 20px;
    }
    &:hover::before {
      color: $primary;
    }
  }

  .num_card_left {
    padding-right: 25px;
    img {
      width: 50px;
    }
  }
  .num_card_right {
    flex: 1;
    .num_card_right_top {
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid #e8ecf2;
      margin-bottom: 20px;
      padding-bottom: 12px;
      i {
        color: #9e9fa0;
        cursor: pointer;
        &:hover {
          color: #bebfc8;
        }
      }
    }
    .num_card_right_bottom {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .statistic-tip {
        flex: 1;

        &:first-child {
          border-right: 1px solid #e8ecf2;
        }
        &:last-child {
          padding-left: 20px;
        }
        .txt {
          font-size: 14px;
          color: #8d8d99;
        }
      }
    }
  }
}
.title {
  font-size: 16px;
  color: #262626;
  font-weight: 600;
}
.num {
  font-size: 34px;
  font-weight: bold;
  color: #222222;
}
.num-tip-title {
  margin-bottom: 10px;
}
.num-tip-type {
  margin-right: 10px;
}
.table-cell-type {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.decoration {
  width: 241px;
  height: 160px;
  position: absolute;
  z-index: 1;
  right: 0;
  top: 0;
}
.decoration-left {
  background: url(~@A/images/aiui5/static-left-corner.png) center/contain
    no-repeat;
}
.decoration-right {
  background: url(~@A/images/aiui5/static-right-corner.png) center/contain
    no-repeat;
}

.app-use-info-wrap {
  :deep(.el-table__body-wrapper) {
    max-height: 280px;
    overflow: auto;
  }
}

.statistic-wrap {
  display: flex;
  align-items: center;
  position: absolute;
  z-index: 10;
}
</style>
