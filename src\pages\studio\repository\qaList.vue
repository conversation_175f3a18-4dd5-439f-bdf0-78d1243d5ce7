<template>
  <div class="qa-list">
    <p class="list-wrap-title">{{listTitle}}</p>
    <div class="fr" @keyup.enter="handleSearch">
      <el-input
        class="search-area"
        :placeholder="searchPlacholder"
        size="medium"
        style="width: 360px;"
        v-model="searchVal">
        <i slot="suffix"
          class="el-input__icon el-icon-search search-area-btn"
          @click="handleSearch" />
      </el-input>
    </div>
    <div class="add-input">
      <i class="ic-r-plus"></i>
      <qa-template
        class="qa-template-wrap"
        :ref="'addInput'"
        :data="newVal"
        :listType="type"
        inputFun="addInput"
        :valMaxLength="valMaxLength"
        :placeholder="placeholder"
        @addOrEdit="addOrEdit"
        @setRealTimeAddInput="setRealTimeAddInput"
      />
       <el-button slot="suffix" type="text"
        class="qa-add-btn"
        :disabled="onCreatePage"
        size="small"
        @click="addTemplate">
        添加
      </el-button>
    </div>
    <p class="qa-example">{{example}}</p>
    <template v-if="list && list.length">
      <el-scrollbar wrapStyle="max-height:345px; border-top: 1px solid #d5d8de; border-bottom: 1px solid #d5d8de;"
        :noresize="false" tag="section">
        <div class="list-wrap" v-loading="qaLoading">
          <div class="list-item-wrap" v-for="(item, index) in list" :key="index">
            <div class="list-index">{{index + 1}}</div>
            <qa-template
              class="qa-template-wrap"
              :ref="'qaTemplate' + item.id"
              :data="item"
              :selected="item.id"
              :selectedIndex="index"
              :listType="type"
              :valMaxLength="valMaxLength"
              @addOrEdit="addOrEdit"
            />
            <i class="ic-r-delete" @click="deleteItem(item, index)"></i>
          </div>
        </div>

      </el-scrollbar>
    </template>
    <div v-if="noSearchResult && !list.length">无搜索结果</div>
  </div>
</template>

<script>
import QaTemplate from './qaTemplate'
  export default {
    name: 'qa-list',
    props: {
      type: String,
      listTitle: String,
      searchPlacholder: String,
      placeholder: String,
      initList: {
        type: Array,
        default: () => {
          return []
        }
      },
      questionExample: String,
      keyword: String,
      replyExample: String,
    },
    data() {
      return {
        searchVal: '',
        list: [],
        newVal: {
          id: '',
          template: ''
        },
        noSearchResult: false,
        qaLoading: false,
        onAddItem: false,
        example: '',
        reg1: /{问法关键字}/g,
        reg2: /{关系关键字}/g,
        reg3: /{回复语}/g
      }
    },
    watch: {
      'initList.length': function(val) {
        this.list = val ? JSON.parse(JSON.stringify(this.initList)) : ''
      }
    },
    mounted(){
      this.list = JSON.parse(JSON.stringify(this.initList))
    },
    computed: {
      repoId() {
        return this.$route.params.repoId || ''
      },
      themeId() {
        return this.$route.params.themeId || ''
      },
      onCreatePage() {
        if(this.$route.path.match('/create')) {
          return true
        }
        return false
      },
      valMaxLength() {
        if(self.type == 'question') {
          return 128
        } else {
          return 1000
        }
      }
    },
    methods: {
      handleSearch(){
        if(this.onCreatePage) return
        let val = this.searchVal.trim()
        if(!val) {
          this.list = JSON.parse(JSON.stringify(this.initList))
          this.noSearchResult = false
          return
        }
        let tmpList = []
        tmpList = Array.prototype.filter.call( this.initList, item => {
          let tmp = item.template
          if(tmp && tmp.match(val)) {
            return item
          }
        })
        this.list = tmpList
        this.noSearchResult = tmpList.length ? false : true
      },
      addOrEdit(node, index){
        let self = this
        let val, id
        if(typeof node == 'object' ) {
          val = node.template.trim()
          id = node.id
        } else {
          val = node.trim()
        }
        if(!val) return
        if(val.length > self.valMaxLength) {
          return self.$message.warning(`长度不能超过${self.valMaxLength}个字`)
        }
        if(id && val == self.initList[index].template) return

        if(val.replace(/[^\{\}]/g, '').replace(/(\{\})/g, '')) {
          return self.$message.warning('花括号必须成对出现')
        }
        let flowerBrackets = val.match(/\{(.*?)\}/g) || []
        for (let i = 0; i < flowerBrackets.length; i++) {
          let item = flowerBrackets[i].substring(1, flowerBrackets[i].length - 1)
          if (item == "") {
            return self.$message.warning('花括号内容不能为空')
          }
        }

        let reg = /^[\u4e00-\u9fffa-zA-Z0-9 \.'{}]{0,}$/
        if(!reg.test(val)) {
          return self.$message.warning('只支持中文、英文、数字、空格、英文单引号、英文句号、花括号')
        }

        if(self.initList.find( item => {
          return item.template == val
        })){
          self.$message.warning('已存在，请勿重复添加')
          id && setTimeout(function(){
            self.list[index].template = self.initList[index].template
          }, 1000)
          return
        }

        let data = {
          repositoryId: self.repoId,
          themeId: self.themeId,
          template: val,
          id: id || ''
        }
        let api = self.type == 'question' ? this.$config.api.STUDIO_REPO_QUESTION_ADD_EDIT :
          this.$config.api.STUDIO_REPO_ANSWER_ADD_EDIT
        this.qaLoading = true
        self.$utils.httpPost(api, data, {
          success: (res) => {
            this.qaLoading = false
            self.newVal = {
              id: '',
              template: ''
            }
            if(index) {
              this.list[index].template = val
              return
            }
            if(id) {
              this.list[index] = res.data
              this.initList[index] = JSON.parse(JSON.stringify(res.data))
              return
            } else {
              self.$refs.qaTemplate && self.$refs.qaTemplate.setCursortPosition(0)
            }
            this.$emit('getInfo')
          },
          error: (err) => {
            this.qaLoading = false
          }
        })
      },
      deleteItem(item, index) {
        let self = this
        let data = {
          repositoryId: this.repoId,
          themeId: this.themeId,
          templateId: item.id,
        }
        let api = self.type == 'question' ?
          this.$config.api.STUDIO_REPO_QUESTION_DEL :
          this.$config.api.STUDIO_REPO_ANSWER_DEL
        self.$utils.httpPost(api, data, {
          success: (res) => {
            self.$message.success('删除成功')
            self.list.splice(index, 1)
            self.initList.splice(index, 1)

          },
          error: (err) => {}
        })
      },
      setRealTimeAddInput(val) {
        let self = this
        if(!self.questionExample) return
        val = val.replace(self.reg1, ` ${this.questionExample} `)
        val = val.replace(self.reg2, ` ${this.keyword} `)
        val = val.replace(self.reg3, ` ${this.replyExample} `)
        self.example = val
      },
      addTemplate(){
        this.$refs.addInput.handleBlur()
      }
    },
    components: {QaTemplate}
  }
</script>

<style lang="scss" scoped>

.list-wrap-title {
  display: inline-block;
  margin: 5px 0 17px;
  font-size: 16px;
  font-weight: 600;
}
.list-wrap-title.answer {
  margin-top: 33px;
}
.qa-add-btn {
  min-width: 28px;
  margin-right: 8px;
}
.list-wrap {
  border: 1px solid #d5d8de;
  border-bottom-color: transparent;
  border-top-color: transparent;
}
.list-item-wrap {
  display: flex;
  font-size: 0;
  height: 36px;
  line-height: 36px;
  border-top: 1px solid #d5d8de;
  &:first-child {
    border-top: none;
  }
  &:hover {
    .ic-r-delete {
      display: inline-block;
    }
  }
}
.list-index {
  display: inline-block;
  vertical-align: bottom;
  margin-right: 24px;
  width: 40px;
  line-height: 36px;
  font-size: 12px;
  color: #d5d8de;
  text-align: center
}
.ic-r-delete {
  display: none;
  margin-right: 15px;
  font-size: 14px;
  cursor: pointer;
  color: #d5d8de;
}
.qa-template-wrap {
  display: inline-block;
  width: calc( 100% - 100px );
}

.add-input {
  border: 1px solid #d5d8de;
  border-radius: 2px;
  height: 36px;
  .ic-r-plus {
    line-height: 36px;
    display: inline-block;
    vertical-align: top;
    width: 50px;
    text-align: center;
    color: #d5d8de;
  }
  button {
    vertical-align: top;
  }
}
.qa-example {
  margin: 2px 0 6px;
  padding-left: 54px;
  height: 20px;
  font-size: 12px;
  color: $grey5;
}
</style>
<style lang="scss">
.add-input.el-input--prefix .el-input__inner {
    padding-left: 52px;
  }
.add-input .el-input__prefix {
    width: 43px;
  }
.list-item-wrap {
  .el-input {
    &:hover {
      .ic-r-delete {
        display: block;
      }
    }
  }
  .el-input__inner {
    padding-left: 64px;
    border-radius: 0;
    border-color: transparent;
    border-bottom-color: #d5d8de;
  }
  &:last-child {
    .el-input__inner {
      border-bottom-color: transparent;
    }
  }
}
</style>
