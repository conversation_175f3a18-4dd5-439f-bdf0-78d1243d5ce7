<template>
  <div class="form-row-container">
    <el-row :gutter="16" class="form-row">
      <el-col :span="area === 'toolRequestInput' ? 4 : 7">
        <el-form-item
          :prop="fieldProp('name')"
          :rules="nameRules"
          :style="{ paddingLeft: `${nestingLevel * 12}px` }"
          label-width="0px"
        >
          <el-input
            v-model="localData.name"
            placeholder="参数名称"
            :disabled="fatherType === 'array' || disabled"
            @input="handleChange"
          />
        </el-form-item>
      </el-col>

      <el-col :span="area === 'toolRequestInput' ? 2 : 3">
        <el-form-item
          :prop="fieldProp('type')"
          :rules="[
            { required: true, message: '请选择参数类型', trigger: 'change' },
          ]"
          label-width="0px"
        >
          <el-select
            v-model="localData.type"
            placeholder="参数类型"
            :disabled="disabled"
            @change="handleTypeChange"
          >
            <el-option label="string" value="string" />
            <el-option label="number" value="number" />
            <el-option label="integer" value="integer" />
            <el-option label="boolean" value="boolean" />
            <el-option
              v-if="fatherType !== 'array'"
              label="array"
              value="array"
            />
            <el-option label="object" value="object" />
          </el-select>
        </el-form-item>
      </el-col>

      <el-col :span="10" v-if="area === 'toolRequestOutput'">
        <el-form-item
          :prop="fieldProp('description')"
          :rules="[
            { required: false, message: '请输入参数描述', trigger: 'blur' },
          ]"
          label-width="0px"
        >
          <el-input
            v-model="localData.description"
            placeholder="参数描述"
            :disabled="disabled"
            @input="handleChange"
          />
        </el-form-item>
      </el-col>

      <template v-if="area === 'toolRequestInput'">
        <el-col :span="2" class="fixed-col">
          <el-form-item
            v-show="
              nestingLevel === 0 &&
              fatherType !== 'object' &&
              !localData.arraySon
            "
            :prop="fieldProp('location')"
            label-width="0px"
            :rules="locationRules"
          >
            <el-select
              v-model="localData.location"
              placeholder="传入方法"
              :disabled="disabled"
              @change="handleChange"
            >
              <el-option label="query" value="query" />
              <el-option label="body" value="body" />
              <el-option label="path" value="path" />
              <el-option label="header" value="header" />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="7">
          <el-form-item
            :prop="fieldProp('description')"
            :rules="[
              { required: false, message: '请输入参数描述', trigger: 'blur' },
            ]"
            label-width="0px"
          >
            <el-input
              v-model="localData.description"
              placeholder="参数描述"
              :disabled="disabled"
              @input="handleChange"
            />
          </el-form-item>
        </el-col>

        <el-col :span="3" class="fixed-col">
          <template>
            <el-form-item
              v-if="
                localData.type !== 'object' &&
                localData.type !== 'array' &&
                fatherType !== 'array' &&
                !localData.arraySon
              "
              :prop="fieldProp('defaultValue')"
              :rules="defaultValueRules"
              :style="{
                visibility: localData.arraySon ? 'hidden' : 'visible',
              }"
              label-width="0px"
            >
              <el-input
                v-model="localData.defaultValue"
                placeholder="默认值"
                :disabled="onlyDefaultValueEnabled ? false : disabled"
                @input="handleChange"
              />
            </el-form-item>
          </template>

          <template>
            <el-form-item
              v-if="isRootArray"
              :prop="fieldProp('defaultValue')"
              label-width="0px"
            >
              <el-button
                @click="handleArrayEdit"
                :disabled="onlyDefaultValueEnabled ? false : disabled"
                style="width: 100px; padding: left 0px"
              >
                编辑数组参数
              </el-button>
            </el-form-item>
          </template>
        </el-col>

        <el-col :span="2" class="fixed-col">
          <el-form-item
            v-show="fatherType !== 'array'"
            :prop="fieldProp('required')"
            label-width="0px"
          >
            <el-checkbox
              v-model="localData.required"
              :disabled="disabled"
              @change="handleChange"
            />
          </el-form-item>
        </el-col>
      </template>

      <el-col :span="2">
        <el-form-item :prop="fieldProp('open')" label-width="0px">
          <el-switch
            v-model="localData.open"
            :disabled="disabled"
            @change="handleChange"
          />
        </el-form-item>
      </el-col>

      <el-col :span="2" class="fixed-col">
        <el-form-item label-width="0px">
          <div class="form-actions">
            <i
              v-show="localData.type === 'object'"
              class="el-icon-circle-plus primary_icon"
              @click="handleAddChild('object')"
              :style="{
                cursor: disabled ? 'not-allowed' : 'pointer',
                opacity: disabled ? 0.5 : 1,
              }"
            />
            <i
              v-show="fatherType !== 'array'"
              class="el-icon-remove primary_icon"
              @click="handleRemove"
              :style="{
                cursor: disabled ? 'not-allowed' : 'pointer',
                opacity: disabled ? 0.5 : 1,
              }"
            />
          </div>
        </el-form-item>
      </el-col>
    </el-row>

    <template v-if="hasChildren && !collapsed">
      <el-form-item
        :prop="fieldProp('children')"
        label-width="0px"
        style="margin-bottom: 0px"
      >
        <div v-for="(child, index) in localData.children" :key="child.id">
          <template v-if="!(localData.type === 'array' && index !== 0)">
            <form-row
              :ref="`child-${index}`"
              :form-data="child"
              :path="[...path, 'children', index]"
              :nesting-level="nestingLevel + 1"
              :area="area"
              :father-type="localData.type"
              :disabled="disabled"
              :only-default-value-enabled="onlyDefaultValueEnabled"
              :all-names="allNames"
              @change="handleChildChange(index, $event)"
              @remove-child="handleRemoveChild(index)"
              @edit-array="$emit('edit-array', $event)"
            />
          </template>
        </div>
      </el-form-item>
    </template>
  </div>
</template>

<script>
import { v4 as uuidv4 } from 'uuid'
export default {
  name: 'FormRow',
  props: {
    formData: {
      type: Object,
      required: true,
    },
    path: {
      type: Array,
      default: () => [],
    },
    nestingLevel: {
      type: Number,
      default: 0,
    },
    area: {
      type: String,
      required: true,
      default: 'toolRequestInput',
    },
    fatherType: {
      type: String,
      default: '',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    onlyDefaultValueEnabled: {
      type: Boolean,
      default: false,
    },
    allNames: {
      type: Function,
      default: () => [],
    },
  },
  data() {
    const getParentPath = () => {
      // 如果是顶层元素，返回null
      if (this.nestingLevel === 0) {
        return null
      }

      // 对于嵌套元素，需要找到当前元素所在的父元素路径
      if (this.path && this.path.length >= 2) {
        // 例如，当前路径为 ['inputForm', 0, 'children', 1]
        // 我们需要找到当前元素所在的children数组
        const pathCopy = [...this.path]

        // 查找最后一个 'children' 的位置
        const lastChildrenIndex = pathCopy.lastIndexOf('children')

        if (lastChildrenIndex !== -1) {
          // 返回到 'children' 的路径
          const parentPath = pathCopy.slice(0, lastChildrenIndex + 1)
          return parentPath
        }
      }

      return null
    }

    const validateName = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入参数名称'))
        return
      }

      if (value.length > 20) {
        callback(new Error('只能输入20个以内的字符'))
        return
      }

      if (this.fatherType === 'array') {
        callback()
        return
      }

      if (!/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(value)) {
        callback(new Error('包含字母、数字或下划线，并且以字母或下划线开头'))
        return
      }

      // 检查同层级参数名称是否重复
      if (value && this.allNames) {
        try {
          // 获取父路径
          const parentPath = getParentPath()

          // 使用父路径获取同层级的其他参数名称
          const otherNames = this.allNames(this.formData.id, parentPath)

          if (otherNames && otherNames.includes(value)) {
            callback(new Error('同层级参数名称不可重复'))
            return
          }
        } catch (error) {
          console.error('验证参数名称出错:', error)
          // 如果出错，不阻止表单提交
          callback()
          return
        }
      }

      callback()
    }

    return {
      collapsed: false,
      localData: JSON.parse(JSON.stringify(this.formData)),
      nameRules: [
        {
          required: true,
          validator: validateName,
          trigger: ['blur', 'change'],
        },
      ],
      // defaultValueRules: [
      //   {
      //     required: this.formData.required,
      //     message: '请输入默认值',
      //     trigger: 'blur',
      //   },
      // ],
    }
  },
  computed: {
    isRootArray() {
      // 1. 当前必须是 array 类型
      if (this.localData.type !== 'array') return false

      // 2. 不能是 arraySon（数组子项）
      if (this.localData.arraySon) return false

      let parent = this.$parent
      while (parent) {
        if (parent.localData?.type === 'array') {
          return false // 发现上层有 array，当前不是第一个
        }
        parent = parent.$parent
      }

      return true // 没有发现上层的 array，当前是第一
    },
    isCollapsible() {
      return this.localData.type === 'object' || this.localData.type === 'array'
    },
    hasChildren() {
      return (
        (this.localData.type === 'object' || this.localData.type === 'array') &&
        this.localData.children &&
        this.localData.children.length > 0
      )
    },

    locationRules() {
      // 如果是嵌套子项或者是数组子项，不需要校验location
      if (
        this.nestingLevel > 0 ||
        this.fatherType === 'array' ||
        this.localData.arraySon
      ) {
        return []
      }

      return [
        {
          required: true,
          message: '请选择传入方法',
          trigger: 'change',
        },
      ]
    },

    defaultValueRules() {
      // 如果是输出参数(output)或者数组子项，不需要校验默认值
      if (this.area === 'toolRequestOutput' || this.localData.arraySon) {
        return []
      }

      return [
        {
          // required: this.formData.required,
          required: false,
          message: '请输入默认值',
          trigger: 'blur',
        },
      ]
    },
  },
  watch: {
    formData: {
      deep: true,
      handler(newVal) {
        this.localData = JSON.parse(JSON.stringify(newVal))
      },
    },
  },
  methods: {
    // 其他方法...

    fieldProp(fieldName) {
      return this.path.join('.') + '.' + fieldName
    },

    handleChange() {
      this.$emit('change', this.localData)
    },

    handleTypeChange(val) {
      this.localData.children = undefined
      if (val === 'object') {
        // 对象类型初始化带空children数组
        this.localData.children = []
        this.handleAddChild('object')
      } else if (val === 'array') {
        // 数组类型初始化不带children
        this.handleAddChild('array')
      }

      this.handleChange()
    },

    handleAddChild(type) {
      if (this.disabled) return

      const baseFields = {
        id: uuidv4(),
        open: true,
        startDisabled: false,
        nameErrMsg: '', // 确保每个对象都有
        descriptionErrMsg: '', // 确保每个对象都有
        isUserAdded: true, // 标记为用户添加的项
      }

      if (type === 'object') {
        const childType = this.localData.children?.[0]?.type || 'string'

        const newChild = {
          ...baseFields,
          name: '',
          description: '',
          type: childType,
          required: true,
          fatherType: 'object',
          arraySon: false,
        }

        // 只有子项是object类型时才初始化children
        if (childType === 'object') {
          newChild.children = []
        }

        if (!this.localData.children) {
          this.localData.children = []
        }
        this.localData.children.push(newChild)
      } else if (type === 'array') {
        // 数组子项永远不带children
        const newChild = {
          ...baseFields,
          name: '[Array Item]',
          type: 'string',
          required: false,
          fatherType: 'array',
          arraySon: true,
        }
        if (!this.localData.children) {
          this.localData.children = []
        }
        this.localData.children.push(newChild)
      }

      this.handleChange()
    },

    handleRemove() {
      if (this.disabled) return
      this.$emit('remove-child', this.path)
    },

    handleRemoveChild(index) {
      this.localData.children.splice(index, 1)
      this.handleChange()
    },

    handleChildChange(index, newChildData) {
      this.$set(this.localData.children, index, newChildData)
      this.handleChange()
    },

    checkArray() {
      if (this.localData.arraySon) {
        // 实现清空父节点children数据的逻辑
      }
    },

    handleArrayEdit() {
      if (!this.localData.children || this.localData.children.length === 0) {
        // 初始化数组子项
        this.localData.children = [
          {
            id: uuidv4(),
            name: '[Array Item]',
            type: 'string',
            defaultValue: '',
            fatherType: 'array',
            arraySon: true,
            nameErrMsg: '', // 保持一致
            descriptionErrMsg: '', // 保持一致
            isUserAdded: true, // 标记为用户添加的项
          },
        ]
      }
      this.$emit('edit-array', {
        path: this.path,
        data: this.localData,
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.form-row-container {
  .form-row {
    .el-col .el-form-item {
      margin-bottom: 20px;
    }
  }

  // 设置所有输入框的背景颜色
  :deep(.el-input__inner) {
    background-color: #f5f6f8;
  }

  // 禁用输入框隐藏边框
  :deep(.el-input.is-disabled .el-input__inner) {
    border: none;
    box-shadow: none;
  }
}
.fixed-col {
  min-height: 40px; // 保持高度一致
  position: relative; // 为绝对定位占位元素做准备
}
.primary_icon {
  color: $primary; // Element UI 默认主题蓝色
}
</style>
