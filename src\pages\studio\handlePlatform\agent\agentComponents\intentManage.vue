<template>
  <div class="intent_manage_wrapper">
    <div class="intent_manage_header">
      <div class="intent_manage_header_left">
        <el-input
          class="search-area"
          placeholder="搜索意图"
          size="medium"
          v-model="searchVal"
          clearable
          @clear="searchIntent(1)"
          @keyup.enter.native="searchIntent(1)"
          :maxlength="250"
        >
          <i
            slot="suffix"
            class="el-input__icon el-icon-search search-area-btn"
            @click="searchIntent(1)"
          />
        </el-input>
      </div>
      <div class="intent_manage_header_right">
        <el-button @click="quoteIntent">引用官方意图</el-button>
        <el-button @click="addIntent"
          ><i class="el-icon-plus" style="margin-right: 5px"></i
          >创建意图</el-button
        >
      </div>
    </div>
    <el-scrollbar>
      <div class="intent_list">
        <IntentItem
          v-for="item in intentList"
          :key="item.intentId"
          :intent-data="item"
          :reduce-padding="true"
          @click="handleIntentItem"
          @command="handleDropdownItem"
        />
      </div>
    </el-scrollbar>
    <el-pagination
      ref="pagination"
      :current-page="page"
      :page-size="size"
      :total="total"
      :layout="pageLayout"
      @current-change="pageChange"
      class="txt-al-c"
    ></el-pagination>

    <IntentDialog ref="IntentDialog" @refresh="refresh" />
    <IntentLibDialog ref="IntentLibDialog" @refresh="refresh" />
  </div>
</template>

<script>
import IntentDialog from '../intentDialog.vue'
import IntentItem from './intentItem.vue'
import IntentLibDialog from '../intentLibDialog.vue'

export default {
  name: 'IntentManage',
  components: {
    IntentDialog,
    IntentItem,
    IntentLibDialog,
  },
  data() {
    return {
      searchVal: '',
      total: 0,
      page: 1,
      size: 10,
      intentList: [],
    }
  },
  created() {
    this.searchIntent()
  },
  computed: {
    pageLayout() {
      return this.total > 10
        ? 'prev, pager, next, jumper, total'
        : 'prev, pager, next'
    },
  },
  methods: {
    searchIntent() {
      const params = {
        pluginId: this.$route.params.agentId,
        // pluginId: 'web_third_681dd3d16bc623f674fb4e97',
        pageIndex: this.page,
        pageSize: this.size,
        searchKey: this.searchVal,
      }
      this.$utils.httpPost(
        this.$config.api.AGENT_INTENT_TABLE_LIST,
        JSON.stringify(params),
        {
          config: {
            headers: {
              'Content-Type': 'application/json;charset=UTF-8',
            },
          },
          success: (res) => {
            console.log(res, '模板智能体的intentList')
            this.intentList = res.data.data
          },
        }
      )
    },
    pageChange(e) {
      console.log('pageChange=>', e)
      this.page = e
      this.searchIntent()
    },

    quoteIntent() {
      this.$refs.IntentLibDialog.show()
    },
    addIntent() {
      this.$refs.IntentDialog.show()
    },
    refresh() {
      this.searchIntent()
    },
    handleIntentItem(item) {
      console.log('点击某一项意图=>', item)
    },
    handleDropdownItem(command, item) {
      switch (command) {
        case 'edit':
          this.toEditIntent(item)
          break
        case 'del':
          this.toDelIntent(item)
          break
      }
    },
    toDelIntent(item) {
      console.log('点击删除 item=>', item)

      let self = this
      this.$confirm('意图删除后不可恢复，请谨慎操作。', `确定删除该意图?`, {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        confirmButtonClass: 'el-button--danger',
        type: 'warning',
        showClose: false,
      })
        .then(() => {
          self.delAgent(item)
        })
        .catch(() => {})
    },
    delAgent(data) {
      const params = {
        intentId: data.intentId,
        pluginId: this.$route.params.agentId,
      }
      this.$utils.httpPost(
        this.$config.api.AGENT_INDENT_DELETE,
        JSON.stringify(params),
        {
          config: {
            headers: {
              'Content-Type': 'application/json;charset=UTF-8',
            },
          },
          success: (res) => {
            this.$message.success('删除成功')
            this.searchIntent()
          },
          error: (err) => {
            this.$message.error(err.desc)
          },
        }
      )
    },

    toEditIntent(item) {
      this.$refs.IntentDialog.show(this.$route.params.agentId, item)
    },
  },
}
</script>

<style scoped lang="scss">
.intent_manage_wrapper {
  height: 100%;
  .intent_manage_header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 10px;
    padding-left: 1px;
    margin-bottom: 20px;
    .intent_manage_header_left {
      flex: 1;
    }
    .intent_manage_header_right {
      display: flex;
      align-items: center;
    }
  }
  .el-scrollbar {
    height: calc(100% - 116px);
    .intent_list {
      display: grid;
      grid-template-columns: repeat(1, 1fr);
      gap: 10px;
    }
  }
  .el-pagination {
    margin-top: 12px;
  }
}
</style>
