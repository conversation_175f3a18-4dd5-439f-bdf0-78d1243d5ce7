<template>
  <div>
    <div class="num-card-wrap">
      <div class="num-card num-card-left">
        <p class="title">已使用（次数）</p>
        <p class="num" v-if="statisticUsedInfo">
          {{ statisticUsedInfo['all'] }}
        </p>
        <el-tooltip class="item" effect="dark" placement="bottom">
          <i class="ic-r-more" />
          <div slot="content">
            <p class="num-tip-title">使用次数（包含免费调用的使用次数）</p>
            <p v-for="(item, key) in statisticUsedInfo" :key="key">
              <span v-if="key != 'all'"
                >{{ key | statisticUsedInfo }}&nbsp;&nbsp;{{ item }}次
              </span>
            </p>
          </div>
        </el-tooltip>
        <div class="decoration decoration-left"></div>
      </div>
      <div class="num-card num-card-right">
        <p class="title">剩余（次数）</p>
        <p class="num">
          {{ !!businessType && businessType === 1 ? '不限' : appSurplusCount }}
        </p>
        <el-tooltip class="item" effect="dark" placement="bottom">
          <i class="ic-r-more" />
          <div slot="content">
            <p class="num-tip-title">剩余次数</p>
            <p>
              <span class="num-tip-type">AIUI服务</span>
              {{ appSurplusCount }} 次
            </p>
          </div>
        </el-tooltip>
        <a
          v-if="!subAccount"
          :href="`${$config.xfyunConsole}sale/buy?platform=aiui&wareId=1701&appId=${appId}&appName=${appName}&serviceName=AIUI服务`"
          target="_blank"
          class="left-tip"
          >提升交互次数</a
        >
        <div class="decoration decoration-right"></div>
      </div>
    </div>
    <os-table :tableData="tableData">
      <el-table-column label="类型" width="150">
        <template slot-scope="scope">
          <span class="table-cell-type" :title="scope.row.servicename">{{
            scope.row.servicename
          }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="startTime" label="开始时间" width="150">
        <template slot-scope="scope">
          {{ scope.row.startTime | date('yyyy-MM-dd') }}
        </template>
      </el-table-column>
      <el-table-column prop="endTime" label="结束时间" width="150">
        <template slot-scope="scope">
          {{ scope.row.endTime | date('yyyy-MM-dd') }}
        </template>
      </el-table-column>
      <el-table-column label="使用量" width="150">
        <template slot-scope="scope">
          <span v-if="scope.row.usedCount >= 0">
            {{ scope.row.usedCount }}次</span
          >
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="剩余量" width="150">
        <template slot-scope="scope">
          <span v-if="scope.row.allCount >= 0">
            {{ scope.row.allCount - scope.row.usedCount }}次</span
          >
          <span v-else>-</span>
        </template>
      </el-table-column>
    </os-table>
  </div>
</template>
<script>
import { mapGetters } from 'vuex'

export default {
  data() {
    return {
      statisticUsedInfo: {
        all: 0,
      },
      businessType: null,
      tableData: {
        loading: false,
        total: '',
        page: 1,
        size: 10,
        needHandle: true,
        list: [],
      },
      appSurplusCount: 0,
    }
  },
  computed: {
    appId() {
      return this.$route.params.appId
    },
    appName() {
      return this.$store.state.aiuiApp.app.appName
    },
    platform() {
      return this.$store.state.aiuiApp.app.platform || ''
    },
    ...mapGetters({
      subAccount: 'user/subAccount',
    }),
  },
  watch: {
    platform() {
      this.init()
    },
  },
  created() {
    if (this.platform) {
      this.init()
      this.getAppBusiness()
    }
  },
  methods: {
    getAppBusiness() {
      this.$utils.httpGet(
        this.$config.api.SERVICE_STATISTICS_GETAPPBUSINESS,
        {
          appid: this.appId,
        },
        {
          success: (res) => {
            if (res.flag) {
              if (res.data) {
                this.businessType = res.data.businessType
              }
            } else {
              this.$message.error(res.desc)
            }
          },
          error: (err) => {},
        }
      )
    },
    init() {
      this.tableData.list.splice(0)
      this.getAppUseInfo()
      // this.getSourceUseInfo()
      this.getAllUsed()
    },
    getAppUseInfo() {
      let self = this
      this.$utils.httpGet(
        this.$config.api.AIUI_STATISTIC_WEBAPI_TABEL,
        {
          appid: this.appId,
        },
        {
          success: (res) => {
            self.appUsedCount = 0
            self.appSurplusCount = 0
            if (!res.data || (res.data && !res.data.length)) {
              return
            }
            self.tableData.list.push(...res.data)

            let totalAllCount = 0
            for (let i = 0; i < res.data.length; i++) {
              this.appUsedCount += res.data[i].usedCount
              totalAllCount += res.data[i].allCount
            }
            self.appSurplusCount = totalAllCount - self.appUsedCount
          },
          error: (err) => {},
        }
      )
    },
    getAllUsed() {
      let self = this
      this.$utils.httpGet(
        this.$config.api.AIUI_STATISTIC_ALL_USED,
        {
          appid: this.appId,
        },
        {
          success: (res) => {
            // {all: 0, SDK: 0, source: 0}
            self.statisticUsedInfo = res.data || {}
          },
          error: (err) => {},
        }
      )
    },
  },
}
</script>

<style lang="scss" scoped>
.num-card-wrap {
  display: flex;
  margin: 40px 0 32px;
}
.num-card-left {
  // background: #a1c4fd;
  // box-shadow: 0px 4px 16px 0px rgba(161, 196, 253, 0.4);
  background: url(~@A/images/aiui5/static-left.png) right/cover no-repeat;
}
.num-card-right {
  padding: 16px 24px;
  // background: #8fd3f4;
  // box-shadow: 0px 4px 16px 0px rgba(143, 211, 244, 0.4);
  background: url(~@A/images/aiui5/static-right.png) right/cover no-repeat;
  .title {
    margin-bottom: 0;
  }
}
.num-card {
  flex: 1;
  position: relative;
  margin-right: 16px;
  padding: 20px 24px;
  height: 127px;
  // border-radius: 15px;
  // border: 1px solid $grey2;
  &:last-child {
    margin-right: 0;
  }
  .ic-r-more {
    position: absolute;
    top: 8px;
    right: 12px;
    z-index: 2;
    cursor: pointer;
    &::before {
      color: #fff;
      font-weight: bold;
      font-size: 20px;
    }
    &:hover::before {
      color: $primary;
    }
  }

  .left-tip {
    position: absolute;
    bottom: 12px;
    left: 23px;
    height: 22px;
    background: #2bceff;
    border-radius: 4px;
    display: inline-block;
    line-height: 22px;
    padding: 0 7px;
    color: #fff;
  }
}
.title {
  margin-bottom: 6px;
  font-size: 16px;
  color: #262626;
}
.num {
  font-size: 30px;
  font-weight: bold;
  color: $primary;
}
.num-tip-title {
  margin-bottom: 10px;
}
.num-tip-type {
  margin-right: 10px;
}
.table-cell-type {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.decoration {
  width: 241px;
  height: 126px;
  position: absolute;
  z-index: 1;
  right: 0;
  top: 0;
}
.decoration-left {
  background: url(~@A/images/aiui5/static-left-corner.png) center/cover
    no-repeat;
}
.decoration-right {
  background: url(~@A/images/aiui5/static-right-corner.png) center/cover
    no-repeat;
}

.app-use-info-wrap {
  :deep(.el-table__body-wrapper) {
    max-height: 280px;
    overflow: auto;
  }
}
</style>
