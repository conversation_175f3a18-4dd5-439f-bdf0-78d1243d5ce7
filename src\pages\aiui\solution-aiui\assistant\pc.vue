<template>
  <div class="main-content">
    <section class="main-content-banner">
      <div class="banner-text">
        <h2>APP语音助手</h2>
        <p class="banner-text-content">
          让APP能听会说，带来智慧新体验<br />
          支持免唤醒语音交互，提供丰富技能和海量内容
        </p>

        <div class="banner-text-button" @click="toConsole">合作咨询</div>
      </div>
    </section>
    <section class="section section-1">
      <div class="section-title">
        <!-- <i class="arrow arrow-left"></i> -->
        <span>应用场景</span>
        <!-- <i class="arrow arrow-right"></i> -->
      </div>
      <ul class="advantage">
        <li>
          <div class="advantage-image"></div>
          <div class="advantage-text">
            <p>语音搜索</p>
            <p>想搜什么说什么，简单更高效</p>
            <ul>
              <li>功能直达，无障碍交互</li>
              <li>内容查找，一语中的</li>
            </ul>
          </div>
        </li>
        <li>
          <div class="advantage-text">
            <p>语音操控</p>
            <p>一语即控，让操控更轻松</p>
            <ul>
              <li>毫秒级响应</li>
              <li>多重校验，防止误触发</li>
            </ul>
          </div>
          <div class="advantage-image"></div>
        </li>
        <li>
          <div class="advantage-image"></div>
          <div class="advantage-text">
            <p>语音客服</p>
            <p>降低客服成本，提升咨询体验</p>
            <ul>
              <li>24H在线智能回复，问题解决率达85%</li>
              <li>支持多轮对话，招呼闲聊</li>
            </ul>
          </div>
        </li>
        <li>
          <div class="advantage-text">
            <p>语音录入</p>
            <p>即时语音、即时输入</p>
            <ul>
              <li>一分钟400字，准确率98%</li>
              <li>支持24种方言及35个语种</li>
            </ul>
          </div>
          <div class="advantage-image"></div>
        </li>
        <li>
          <div class="advantage-image"></div>
          <div class="advantage-text">
            <p>语音播报</p>
            <p>文字转语音，让APP更有温度</p>
            <ul>
              <li>100+发音人，男女老少，风格随心选</li>
              <li>消息主动播报，关怀弱势群体</li>
            </ul>
          </div>
        </li>
      </ul>
    </section>
    <section class="section section-2">
      <div class="section-title">
        <!-- <i class="arrow arrow-left"></i> -->
        <span>方案亮点</span>
        <!-- <i class="arrow arrow-right"></i> -->
      </div>
      <ul class="advantage">
        <li>
          <div class="advantage-text">
            <p>无需唤醒，直接交互</p>
            <p>
              直接说出语音指令，无需语音唤醒。基于用户意图甄别，有效防止误触发
            </p>
          </div>
          <div class="advantage-image"></div>
        </li>
      </ul>
    </section>
    <section class="section section-3">
      <div class="section-title section-title-spec">
        <!-- <i class="arrow arrow-left"></i> -->
        <span>提供丰富技能和海量内容</span>
        <!-- <i class="arrow arrow-right"></i> -->
      </div>
      <p class="section-sub-title section-sub-title-spec">
        200+常用技能，涵盖生活、娱乐、游戏、办公、搜索导航、
        AIOT控制；内容资源覆盖1500万首曲库，10余个视频品牌，有声内容1200万+小时
      </p>
      <div class="section-content">
        <!-- <hr /> -->
        <div class="content-container">
          <p class="content-title">200+常用技能</p>
          <ul class="li-wrap">
            <li class="">
              <i class="content-icon icon-clock"></i>
              <p>闹钟</p>
            </li>
            <li class="">
              <i class="content-icon icon-dictionary"></i>
              <p>成语词典</p>
            </li>
            <li class="">
              <i class="content-icon icon-qa"></i>
              <p>百科问答</p>
            </li>
            <li class="">
              <i class="content-icon icon-poetry"></i>
              <p>古诗词</p>
            </li>
            <li class="">
              <i class="content-icon icon-chat"></i>
              <p>闲聊</p>
            </li>
            <li class="btn-more-wrap">
              <i class="btn-more"></i>
            </li>
          </ul>
        </div>
        <div class="content-container">
          <p class="content-title">海量内容</p>
          <ul class="li-wrap">
            <li class="">
              <i class="content-icon icon-xmly"></i>
              <p>喜马拉雅听说</p>
            </li>
            <li class="">
              <i class="content-icon icon-kwyy"></i>
              <p>酷我音乐</p>
            </li>
            <li class="">
              <i class="content-icon icon-xlxw"></i>
              <p>新浪新闻</p>
            </li>
            <li class="">
              <i class="content-icon icon-aqy"></i>
              <p>爱奇艺</p>
            </li>
            <li class="">
              <i class="content-icon icon-bilibili"></i>
              <p>哔哩哔哩</p>
            </li>
            <li class="btn-more-wrap">
              <i class="btn-more"></i>
            </li>
          </ul>
        </div>
      </div>
    </section>
    <corp @jump="toConsole">
      <template> 提交信息，我们会尽快与您联系</template>
    </corp>
    <!-- <section class="section section-4">
      <div class="section-title">
        <p>合作咨询</p>
        <p>提交信息，我们会尽快与您联系</p>
      </div>
      <div class="section-item">
        <aiui-button hasTop @click.native="toConsole">申请合作</aiui-button>
      </div>
    </section> -->
  </div>
</template>

<script>
import corp from '@P/aiui/solution-aiui/components/corp.vue'

export default {
  name: 'smart-hardware',
  data() {
    return {}
  },
  methods: {
    toConsole() {
      // 判断是否有百度SEM营销参数，有的话，提交表单页需要带上
      const search = window.location.search
      if (search) {
        window.open(`/solution/apply/9${search}`)
      } else {
        window.open('/solution/apply/9')
      }
    },
  },
  components: { corp },
}
</script>

<style lang="scss" scoped>
.main-content {
  &-banner {
    background: url(~@A/images/solution/assistant/img_voiceassistant_bg_banner.png)
      center no-repeat;
    background-size: cover;
    height: 500px;
    overflow: hidden;
    width: 100%;
    .banner-text {
      max-width: 1200px;
      color: #fff;
      height: 100%;
      margin: auto;
      &-button {
        font-size: 16px;
        text-align: center;
        font-weight: 400;
        width: 140px;
        height: 40px;
        line-height: 40px;
        border: 1px solid #fff;
        border-radius: 40px;
        color: #fff;
        cursor: pointer;
        transition: 0.6s;
      }
      h2 {
        color: #fff;
        padding-top: 148px;
        margin-bottom: 29px;
        font-size: 48px;
        font-weight: 500;
        line-height: 48px;
      }
      p {
        font-size: 18px;
        margin-bottom: 74px;
      }

      .banner-text-content {
        width: 570px;
        font-size: 16px;
        font-family: SourceHanSansSC-Regular, SourceHanSansSC;
        font-weight: 400;
        color: rgba(255, 255, 255, 0.86);
        line-height: 30px;
      }
    }
  }

  .section {
    p,
    ul {
      margin-bottom: 0;
    }
    max-width: 1200px;
    overflow: hidden;
    margin: 0 auto;
    .section-title {
      text-align: center;
      font-size: 34px;
      font-family: SourceHanSansSC-Medium, SourceHanSansSC;
      font-weight: bold;
      color: #333;
      line-height: 53px;
      > span {
        font-size: 34px;
        font-family: SourceHanSansSC-Medium, SourceHanSansSC;
        font-weight: bold;
        color: #333;
      }
      .arrow {
        width: 25px;
        height: 23px;
        display: inline-block;
        background-repeat: no-repeat;
        background-size: contain;
      }
      .arrow-left {
        background-position: left;
        background-image: url(~@A/images/solution/smart-hardware/arrow-left.png);
      }
      .arrow-right {
        background-position: right;
        background-image: url(~@A/images/solution/smart-hardware/arrow-right.png);
      }
    }
    .section-title-spec {
      font-size: 30px;
      font-weight: 500;
      color: #656565;
      line-height: 42px;
      > span {
        font-size: 34px;
        font-weight: 400;
        color: #666;
      }
    }
    .section-tabs {
      margin-top: 70px;
    }
    .section-sub-title {
      text-align: center;
      font-size: 18px;
      font-weight: 400;
      color: #777777;
      line-height: 25px;
      margin-top: 42px;
    }
    .section-sub-title-spec {
      font-size: 17px;
      font-weight: 400;
      color: #999999;
      line-height: 28px;
      margin-top: 18px;
    }
  }

  .section-1 {
    p {
      margin-bottom: 0;
    }
    margin-top: 50px;
    .advantage {
      margin-top: 84px;
      > li {
        display: flex;
        justify-content: center;
      }
      > li:nth-child(1) {
        .advantage-image {
          margin-right: 93px;
          background: url(~@A/images/solution/assistant/sce_1.png) center
            no-repeat;
        }
        .advantage-text {
          padding-top: 96px;
          padding-left: 93px;
        }
      }
      > li:nth-child(2) {
        margin-top: 141px;
        .advantage-image {
          margin-left: 93px;
          background: url(~@A/images/solution/assistant/sce_2.png);
        }
        .advantage-text {
          padding-top: 91px;
        }
      }
      > li:nth-child(3) {
        margin-top: 141px;

        .advantage-image {
          margin-right: 93px;
          background: url(~@A/images/solution/assistant/sce_3.png) center
            no-repeat;
        }
        .advantage-text {
          padding-top: 96px;
          padding-left: 93px;
          // width: 392px;
        }
      }
      > li:nth-child(4) {
        margin-top: 124px;
        .advantage-image {
          margin-left: 93px;
          background: url(~@A/images/solution/assistant/sce_4.png) center
            no-repeat;
        }
        .advantage-text {
          padding-top: 91px;
        }
      }
      > li:nth-child(5) {
        margin-top: 141px;
        .advantage-image {
          margin-right: 93px;
          background: url(~@A/images/solution/assistant/sce_5.png) center
            no-repeat;
        }
        .advantage-text {
          padding-top: 96px;
          padding-left: 93px;
        }
      }
    }
    .advantage-text {
      width: 373px;
      p:first-child {
        font-size: 30px;
        font-weight: 500;
        color: #333;
        line-height: 42px;
      }
      p:nth-child(2) {
        font-size: 18px;
        color: #666;
        line-height: 27px;
        font-weight: 400;
        margin-top: 12px;
      }
      ul {
        margin-top: 32px;
        li {
          font-size: 17px;
          font-weight: 400;
          color: #999999;
          line-height: 17px;
          white-space: nowrap;
          &::before {
            display: inline-block;
            content: ' ';
            width: 9px;
            height: 9px;
            border-radius: 100%;
            background: #3a91ff;
            margin-right: 4px;
          }
        }
        li + li {
          margin-top: 14px;
        }
      }
    }
    .advantage-image {
      width: 373px;
      height: 352px;
    }
  }

  .section-2 {
    p {
      margin-bottom: 0;
    }
    margin-top: 160px;
    .advantage {
      margin-top: 84px;
      li {
        display: flex;
        justify-content: center;
      }
      li:nth-child(1) {
        margin-top: 65px;
        .advantage-image {
          margin-left: 93px;
          background-image: url(~@A/images/solution/assistant/adv.png);
        }
        .advantage-text {
          padding-top: 94px;
        }
      }
    }
    .advantage-text {
      width: 373px;
      p:first-child {
        font-size: 30px;
        font-weight: 500;
        color: #656565;
        line-height: 42px;
      }
      p:last-child {
        font-size: 17px;
        font-weight: 400;
        color: #999999;
        line-height: 28px;
        margin-top: 18px;
      }
    }
    .advantage-image {
      width: 398px;
      height: 313px;
      background-repeat: no-repeat;
    }
  }

  .section-3 {
    margin-top: 201px;
    max-width: 2000px;
    .content-icon {
      display: inline-block;
      width: 115px;
      height: 115px;
      background-repeat: no-repeat;
      background-size: 100%;
    }
    .icon-xmly {
      background-image: url(~@A/images/solution/assistant/logos/s_1.png);
    }

    .icon-kwyy {
      background-image: url(~@A/images/solution/assistant/logos/s_2.png);
    }

    .icon-xlxw {
      background-image: url(~@A/images/solution/assistant/logos/s_3.png);
    }

    .icon-aqy {
      background-image: url(~@A/images/solution/assistant/logos/s_4.png);
    }

    .icon-bilibili {
      background-image: url(~@A/images/solution/assistant/logos/s_5.png);
    }

    .icon-clock {
      background-image: url(~@A/images/solution/assistant/logos/t_1.png);
    }

    .icon-dictionary {
      background-image: url(~@A/images/solution/assistant/logos/t_2.png);
    }

    .icon-qa {
      background-image: url(~@A/images/solution/assistant/logos/t_3.png);
    }

    .icon-poetry {
      background-image: url(~@A/images/solution/assistant/logos/t_4.png);
    }

    .icon-chat {
      background-image: url(~@A/images/solution/assistant/logos/t_5.png);
    }

    .btn-more-wrap {
      padding-top: 47px;
    }

    .btn-more {
      display: inline-block;
      width: 52px;
      height: 10px;
      background-image: url(~@A/images/solution/assistant/logos/btn_more.png);
    }

    .li-wrap {
      display: flex;
      justify-content: center;
      margin-bottom: 0;
      li {
        text-align: center;
        p {
          margin-top: 15px;
          font-size: 18px;
          font-weight: 400;
          color: #999;
          line-height: 25px;
        }
      }
      li + li {
        margin-left: 109px;
      }
      li:last-child {
        margin-left: 46px;
      }
    }

    .content-title {
      max-width: 1200px;
      font-size: 20px;
      font-weight: 400;
      color: #333;
      line-height: 28px;
      padding-left: 36px;
      margin: 0 auto 44px;
    }

    .section-content {
      padding-top: 47px;
      background: rgba(171, 212, 255, 0.08);
      // hr {
      //   width: 100%;
      //   height: 1px;
      //   background: #d1e8ff;
      // }

      .content-container:first-child {
        border-bottom: 1px solid #d1e8ff;
        margin-bottom: 57px;
        padding-bottom: 57px;
      }
      .content-container:last-child {
        padding-bottom: 86px;
      }
    }
  }

  .section-4 {
    margin-top: 109px;
    padding-bottom: 129px;
    text-align: center;
    p {
      margin-bottom: 0;
    }
    .section-title {
      p:first-child {
        font-size: 34px;
        font-weight: bold;
        color: #333;
        line-height: 40px;
      }
      p:last-child {
        font-size: 16px;
        font-weight: 400;
        color: #666;
        line-height: 22px;
        margin-top: 18px;
      }
    }
    .section-item {
      margin-top: 49px;
      .section-button {
        color: #fff;
        background: #1784e9;
        width: 195px;
        height: 50px;
        line-height: 50px;
        text-align: center;
        margin: 0 auto;
        cursor: pointer;
      }
    }
  }
}
</style>
