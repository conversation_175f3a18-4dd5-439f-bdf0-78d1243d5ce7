<template>
  <div class="handle-platform-top console" style="margin-bottom: 20px">
    <h1 class="txt-al-c mgb24 console-text" style="text-align: center">
      自定义业务控制台
    </h1>
    <div class="handle-platform-navs">
      <router-link
        :to="{ name: 'studio-handle-platform-qabanks' }"
        v-if="!subAccount"
      >
        <div
          class="handle-platform-nav"
          :class="{ 'handle-platform-nav-active': nav === '/studio/qaBank' }"
        >
          问答库
        </div>
      </router-link>

      <router-link
        :to="{ name: 'studio-handle-platform-agent' }"
        v-if="!subAccount"
      >
        <div
          class="handle-platform-nav"
          :class="{ 'handle-platform-nav-active': nav === '/studio/agent' }"
        >
          智能体
        </div>
      </router-link>

      <router-link
        :to="{
          name: subAccount
            ? 'sub-studio-handle-platform-skills'
            : 'studio-handle-platform-skills',
        }"
      >
        <div
          class="handle-platform-nav"
          :class="{
            'handle-platform-nav-active':
              nav === '/studio/skill' || nav === '/sub/skills',
          }"
        >
          技能
        </div>
      </router-link>
      <!-- <a>
        <div class="handle-platform-nav" style="cursor: not-allowed">
          智能体
        </div></a
      > -->

      <router-link
        v-if="subAccount"
        :to="{
          name: subAccount
            ? 'sub-studio-handle-platform-entities'
            : 'studio-handle-platform-entities',
        }"
      >
        <div
          class="handle-platform-nav"
          :class="{
            'handle-platform-nav-active':
              nav === '/studio/entity' || nav === '/sub/entities',
          }"
        >
          我的实体
        </div>
      </router-link>
      <router-link
        v-if="subAccount"
        :to="{
          name: subAccount
            ? 'sub-studio-handle-platform-auxiliaries'
            : 'studio-handle-platform-auxiliaries',
        }"
      >
        <div
          class="handle-platform-nav"
          :class="{
            'handle-platform-nav-active':
              nav === '/studio/auxiliary' || nav === '/sub/auxiliaries',
          }"
        >
          我的辅助词
        </div>
      </router-link>
      <!-- <router-link
        :to="{
          name: subAccount
            ? 'sub-studio-handle-platform-labels'
            : 'studio-handle-platform-labels',
        }"
      >
        <div
          class="handle-platform-nav"
          :class="{
            'handle-platform-nav-active':
              nav === '/studio/label' || nav === '/sub/labels',
          }"
        >
          我的交互标签
        </div>
      </router-link> -->
      <router-link
        :to="{ name: 'studio-handle-platform-characters' }"
        v-if="!subAccount"
      >
        <div
          class="handle-platform-nav"
          :class="{ 'handle-platform-nav-active': nav === '/studio/character' }"
        >
          设备人设
        </div>
      </router-link>

      <!-- <router-link
        :to="{ name: 'studio-handle-platform-qadocs' }"
        v-if="!subAccount"
      >
        <div
          class="handle-platform-nav"
          :class="{ 'handle-platform-nav-active': nav === '/studio/qaDoc' }"
        >
          文档问答
        </div>
      </router-link> -->
    </div>
  </div>
</template>

<script>
import CreateSkillDialog from './dialog/createSkill.vue'
import { mapGetters } from 'vuex'

export default {
  data() {
    return {
      nav: '',
    }
  },
  computed: {
    ...mapGetters({
      subAccount: 'user/subAccount',
    }),
  },
  methods: {
    init() {
      // 去除子账号地址栏中的sub_account_id/subSessionId
      window.history.replaceState(
        {},
        '',
        SSO.utils.setQueryString({
          sub_account_id: '',
          subSessionId: '',
        })
      )
    },
  },
  created() {
    this.nav = this.$route.path
    if (this.subAccount) {
      this.init()
    }
  },
}
</script>

<style lang="scss" scoped>
.console {
  background: url(~@A/images/console.png) center/cover no-repeat;
  padding-top: 50px;
  height: 169px;
}
.console-text {
  color: #000;
  font-size: 36px;
  font-weight: 600;
}
</style>
