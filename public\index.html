<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <meta http-equiv="pragma" content="no-cache" />
    <meta http-equiv="cache-control" content="no-cache" />
    <meta http-equiv="expires" content="0" />
    <title>AIUI开放平台_以讯飞星火大模型为底座的人机交互开发平台</title>
    <meta
      data-vue-meta="1"
      data-vmid="keywords"
      name="keywords"
      content="AIUI,讯飞语音交互,讯飞语义理解,AIUI人机交互,全链路语音交互,全链路人机交互,讯飞人机交互,讯飞自然语言理解,全双工交互,语音唤醒,语音助手开发,语音交互接入,接入语音交互,语音交互控制,语音交互设计,讯飞人工智能,讯飞AIUI开放平台,讯飞云平台,讯飞星火大模型,星火交互大模型,多模态唤醒,虚拟人互动,数字人互动,多语种识别,超拟人合成,声音复刻,硬件模组,讯飞星火,人机交互,智能硬件,消费电子语音交互,消费电子人机交互,手机语音交互,电视语音交互,机器人语音交互,地铁火车轨道交通语音交互,PC语音助手,语音软硬件,语音交互控制"
    />
    <meta
      data-vue-meta="1"
      data-vmid="description"
      name="description"
      content="AIUI是以讯飞星火大模型为核心的人机交互开发平台，具备多模态唤醒、虚拟人驱动、多语种识别、超拟人合成、声音复刻等特性。广泛应用于手机、电视、机器人扫读笔、语音购票等智能硬件设备上。提供SDK、Websocket、硬件模组等，接入集成简单，开箱即用。"
    />
    <link rel="icon" href="<%= BASE_URL %>favicon.svg" type=image/x-icon/>

    <link rel="dns-prefetch" href="//at.alicdn.com" />
    <link rel="dns-prefetch" href="//aiui.xfyun.cn" />
    <link rel="dns-prefetch" href="//aiui-file.cn-bj.ufileos.com" />

    <% for (var i in htmlWebpackPlugin.options.cdnConfig &&
    htmlWebpackPlugin.options.cdnConfig.css) { %>
    <!-- 使用CDN的CSS文件 -->
    <link
      href="<%= htmlWebpackPlugin.options.cdnConfig.css[i] %>"
      rel="external nofollow preload"
      as="style"
    />
    <link
      href="<%= htmlWebpackPlugin.options.cdnConfig.css[i] %>"
      rel="external nofollow stylesheet"
    />
    <% } %> <% for (var i in htmlWebpackPlugin.options.cdnConfig &&
    htmlWebpackPlugin.options.cdnConfig.js) { %>
    <!-- 使用CDN的JS文件 -->
    <link
      href="<%= htmlWebpackPlugin.options.cdnConfig.js[i] %>"
      rel="external nofollow preload"
      as="script"
    />
    <% } %>
    <link
      href="//at.alicdn.com/t/font_1268607_u4atyioi98g.css"
      rel="stylesheet"
    />
    <link
      href="//at.alicdn.com/t/font_2906246_fvjrsw0xgwt.css"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdn.bootcdn.net/ajax/libs/KaTeX/0.6.0/katex.min.css"
      media="none"
      onload="this.media='all'"
    />
  </head>

  <body>
    <noscript>
      <strong>
        很抱歉，如果没有 JavaScript 支持，网页 将不能正常工作。请启用浏览器的
        JavaScript 然后继续。
      </strong>
    </noscript>
    <div id="app"></div>
    <script type="text/javascript">
      if (/sub/.test(location.href)) {
        document.write(
          '<script defer src="' +
            location.origin +
            '/aiui/subweb/user/sub/login/getcookies?ts=' +
            new Date().getTime() +
            '"><\/script>'
        )
      } else {
        if (
          location.host === 'teststudio.iflyos.cn' ||
          location.host === 'dev-aiui.xfyun.cn' ||
          location.host === 'test-aiui.xfyun.cn'
        ) {
          document.write(
            '<script defer src="https://ssodev.xfyun.cn/SSOService/login/getcookies?ts=' +
              new Date().getTime() +
              '"><\/script>'
          )
        } else {
          document.write(
            '<script defer src="https://sso.xfyun.cn/SSOService/login/getcookies?ts=' +
              new Date().getTime() +
              '"><\/script>'
          )
        }
      }
    </script>

    <% for (var i in htmlWebpackPlugin.options.cdnConfig &&
    htmlWebpackPlugin.options.cdnConfig.js) { %>
    <script src="<%= htmlWebpackPlugin.options.cdnConfig.js[i] %>"></script>
    <% } %>

    <script>
      var _hmt = _hmt || []
      ;(function () {
        var hm = document.createElement('script')
        hm.defer = 'defer'
        hm.src = 'https://hm.baidu.com/hm.js?20c4e7babb832770d94d02120c87fe7f'
        var s = document.getElementsByTagName('script')[0]
        s.parentNode.insertBefore(hm, s)
      })()
    </script>
    <script>
      ;(function (w, d, t, r, u) {
        var f, n, i
        ;(w[u] = w[u] || []),
          (f = function () {
            var o = { ti: '187122004', enableAutoSpaTracking: true }
            ;(o.q = w[u]), (w[u] = new UET(o)), w[u].push('pageLoad')
          }),
          (n = d.createElement(t)),
          (n.src = r),
          (n.async = 1),
          (n.onload = n.onreadystatechange =
            function () {
              var s = this.readyState
              ;(s && s !== 'loaded' && s !== 'complete') ||
                (f(), (n.onload = n.onreadystatechange = null))
            }),
          (i = d.getElementsByTagName(t)[0]),
          i.parentNode.insertBefore(n, i)
      })(window, document, 'script', '//bat.bing.com/bat.js', 'uetq')
    </script>
    <script>
      window.uetq = window.uetq || []
      window.uetq.push('set', {
        pid: {
          em: '',
          ph: '',
        },
      })
    </script>
  </body>
</html>
