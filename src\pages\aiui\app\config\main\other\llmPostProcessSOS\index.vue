<template>
  <div>
    <div class="conf-title item-title" style="margin-top: 0">
      <div style="margin-right: 10px">应用后处理</div>
      <el-switch
        v-model="form.swtich"
        class="mgr16"
        :disabled="!subAccountEditable"
        @change="emitChange"
        :active-value="1"
        :inactive-value="0"
      ></el-switch>
    </div>
    <p class="item-desc">
      对语音识别和语义理解的结果进行自定义处理。<a
        :href="`${$config.docs}doc-5/`"
        target="_blank"
        >了解后处理</a
      >
    </p>
    <div class="form-wrap">
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        :disabled="!subAccountEditable"
        label-width="110px"
        label-position="left"
        style="min-width: 745px"
      >
        <el-form-item
          :label="type == 2 ? '前处理链接' : '后处理链接'"
          prop="url"
          class="inline-form-item-01"
        >
          <el-input
            ref="url"
            class="config-input"
            v-model.trim="form.url"
            placeholder="请填写"
            @blur="emitChange"
          ></el-input>
        </el-form-item>
        <el-form-item label="尝试次数" class="inline-form-item-02">
          <el-select
            class="config-select"
            v-model="form.retries"
            @change="changeReties"
          >
            <el-option
              v-for="(item, index) in repeatTimeList"
              :key="index"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select> </el-form-item
        ><br />
        <el-form-item
          label="备用链接"
          prop="backupUrl"
          class="inline-form-item-01"
        >
          <el-input
            class="config-input"
            v-model.trim="form.backupUrl"
            @change="backupUrlChange"
            placeholder="请填写"
            @input="emitChange"
          ></el-input>
        </el-form-item>
        <el-form-item label="尝试次数" class="inline-form-item-02">
          <el-select
            class="config-select"
            v-model="form.backupReties"
            @change="changeReties"
          >
            <el-option
              v-for="(item, index) in repeatTimeList"
              :key="index"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="超时时间" prop="timeout">
          <el-input
            class="config-input"
            ref="timeout"
            v-model.trim="form.timeout"
            :min="200"
            @blur="emitChange"
            :placeholder="`尝试次数为${form.retries}次时，超时时间可设范围为100-${timeOutLimit}ms`"
          ></el-input>
          ms
        </el-form-item>
        <el-form-item label="校验token">
          <span class="mgr24">{{ form.token }}</span>
          <a class="mgr24" @click="copy(form['token'])">复制</a>
          <a v-if="subAccountEditable" @click="changeToken('token')"
            >重新生成</a
          >
        </el-form-item>
        <el-form-item label="消息是否加密">
          <el-switch
            v-model="form.isEncrypt"
            @change="emitChange"
            :active-value="true"
            :inactive-value="false"
          ></el-switch>
        </el-form-item>
        <el-form-item label="加密AES KEY">
          <span class="mgr24">{{ form.aesKey }}</span>
          <a class="mgr24" @click="copy(form['aesKey'])">复制</a>
          <a v-if="subAccountEditable" @click="changeToken('aesKey')"
            >重新生成</a
          >
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
import md5 from 'blueimp-md5'
import { mapGetters } from 'vuex'

export default {
  name: 'llmPostProcess',
  props: {
    type: {
      type: Number,
      default: 1,
    },
    text: {
      type: Object,
      default: () => {
        return {
          name: '后处理',
          desc: '对语音识别和语义理解的结果进行自定义处理。',
        }
      },
    },
  },

  data() {
    const checkUrl = (rule, value, callback) => {
      if (!value) return callback()
      if (this.type === 1) {
        if (
          /^([hH][tT]{2}[pP]:\/\/|[hH][tT]{2}[pP][sS]:\/\/|[wW][sS]{1,2}:\/\/)((192\.168|172\.([1][6-9]|[2]\d|3[01]))(\.([2][0-4]\d|[2][5][0-5]|[01]?\d?\d)){2}|10(\.([2][0-4]\d|[2][5][0-5]|[01]?\d?\d)){3})/.test(
            value
          )
        ) {
          callback(new Error('不支持内网IP'))
          return
        }
      }

      if (this.type === 1) {
        this.$utils.httpPost(
          this.$config.api.AIUI_APP_CALLBACK_CHECKURL,
          {
            appid: this.appId,
            url: value,
            h_token: this.form.token,
          },
          {
            success: (res) => {
              if (!res.flag) {
                callback(new Error(res.desc))
              } else {
                callback()
              }
            },
            error: (err) => {
              callback(new Error(err?.desc))
            },
          }
        )
      } else {
        callback()
      }
    }
    const checkTimeout = (rule, value, callback) => {
      if (value < 100 || value > this.timeOutLimit) {
        // callback(new Error(`超时时间可设范围为100-${this.timeOutLimit}ms`))
        if (this.backupUrlValid && this.form.backupUrl) {
          callback(
            new Error(
              `尝试次数为${
                parseInt(this.form.retries) + parseInt(this.form.backupReties)
              }次时，超时时间可设范围为100-${this.timeOutLimit}ms`
            )
          )
        } else {
          callback(
            new Error(
              `尝试次数为${this.form.retries}次时，超时时间可设范围为100-${this.timeOutLimit}ms`
            )
          )
        }
      }
      callback()
    }
    const isUrlRepeat = (rule, value, callback) => {
      if (value && value == this.form.backupUrl) {
        callback(new Error('链接不能重复'))
      }
      callback()
    }
    const checkUrlRepeat = (rule, value, callback) => {
      if (value && value == this.form.url) {
        callback(new Error('链接不能重复'))
      }
      callback()
    }
    const nullMessage =
      this.type == 2 ? '前处理URL不能为空' : '后处理URL不能为空'

    return {
      form: {
        url: '',
        isEncrypt: false,
        token: this.getToken(),
        aesKey: this.getToken(),
        platform: 'other',
        timeout: '3000',
        retries: 1,
        backupUrl: '',
        backupReties: 1,
        swtich: 0,
      },
      rules: {
        url: [
          {
            required: true,
            message: nullMessage,
            trigger: 'blur',
          },
          {
            pattern:
              /^([hH][tT]{2}[pP]:\/\/|[hH][tT]{2}[pP][sS]:\/\/|[wW][sS]{1,2}:\/\/)(([A-Za-z0-9-~]+).)+([A-Za-z0-9-~\/])+$/,
            message: 'URL格式不正确',
            trigger: 'blur',
          },
          { validator: checkUrl, trigger: 'blur' },
          { validator: isUrlRepeat, trigger: 'blur' },
        ],
        timeout: [
          {
            required: true,
            message: '超时时间不能为空',
            trigger: 'blur',
          },
          {
            pattern: /^[0-9]+$/,
            message: '仅支持数字',
            trigger: 'blur',
          },
          { validator: checkTimeout, trigger: 'blur' },
        ],
        backupUrl: [
          {
            pattern:
              /^([hH][tT]{2}[pP]:\/\/|[hH][tT]{2}[pP][sS]:\/\/|[wW][sS]{1,2}:\/\/)(([A-Za-z0-9-~]+).)+([A-Za-z0-9-~\/])+$/,
            message: 'URL格式不正确',
            trigger: 'blur',
          },
          { validator: checkUrlRepeat, trigger: 'blur' },
          { validator: checkUrl, trigger: 'blur' },
        ],
      },
      repeatTimeList: [
        {
          label: '1次',
          value: 1,
        },
        {
          label: '2次',
          value: 2,
        },
        {
          label: '3次',
          value: 3,
        },
      ],
      isBackupUrlValid: true,
    }
  },
  computed: {
    ...mapGetters({
      currentScene: 'aiuiApp/currentScene',
      appInfo: 'aiuiApp/app',
      limitCount: 'aiuiApp/limitCount',
      subAccount: 'user/subAccount',
      subAccountEditable: 'aiuiApp/subAccountEditable',
    }),
    timeOutLimit() {
      let num = 3000
      let tmp = parseInt(this.form.retries) + parseInt(this.form.backupReties)
      if (this.isBackupUrlValid && this.form.backupUrl) {
        num = 9000 / tmp
        return num
      } else {
        num =
          this.form.retries == 3 ? 3000 : this.form.retries == 2 ? 4500 : 9000
        return num
      }
    },
    appId() {
      return this.$route.params.appId
    },
  },
  created() {
    if (this.currentScene && this.currentScene.sceneBoxId) {
      this.getLLMConf() // 获取配置列表
    }
  },
  methods: {
    backupUrlValid() {
      // debugger
      this.$refs.form &&
        this.$refs.form.validateField(['backupUrl'], (err) => {
          if (!err) {
            this.isBackupUrlValid = true
          } else {
            this.isBackupUrlValid = false
          }
        })
    },
    emitChange() {
      console.log('---------emitChange--------------------')
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.change = true
          this.$emit('change')
        } else {
          this.$emit('validFail')
        }
      })
    },
    changeReties() {
      this.$refs.form.validateField(['timeout', 'backupUrl'])
      this.emitChange()
    },
    backupUrlChange() {
      // debugger
      this.$refs.form.validateField(['timeout', 'url'])
      // this.emitChange()
    },

    getToken() {
      let rand = parseInt(Math.random() * 15)
      let timeStamp = new Date().getTime()
      let res = timeStamp + rand
      res = md5(res)
      return res.substring(rand, rand + 16)
    },
    changeToken(id) {
      let self = this
      this.form[id] = this.getToken()
      self.emitChange()
    },
    copy(value) {
      this.$utils.copyClipboard(value)
      this.$message.success('已复制到剪切板')
    },

    getLLMConf() {
      let self = this
      this.$utils.httpGet(
        this.$config.api.AIUI_BOT_CONFIG_HANDLE_GET,
        {
          botId: this.currentScene.botBoxId,
        },
        {
          success: (res) => {
            if (res.flag) {
              // 如果配置中存在URL 或者是MORFEI 平台则可直接打开

              if (!res.data.token) {
                // 防止魔飞应用第一次打开token 为空
                res.data.token = self.getToken()
                res.data.aesKey = self.getToken()
              }
              let tempData = {
                ...res.data,
                retries: parseInt(res.data.retries) || 1,
                isEncrypt: Number(res.data.isEncrypt) === 1,
                swtich: res.data.swtich ?? 0,
              }
              Object.keys(self.form).forEach((k) => {
                self.$set(self.form, k, tempData[k])
              })

              self.$set(self.form, 'backupUrl', '')
              self.$set(self.form, 'backupReties', 1)
              let tmp = res.data.moreConfig && JSON.parse(res.data.moreConfig)
              if (Array.isArray(tmp)) {
                self.form.backupUrl = tmp[0]['url']
                self.form.backupReties = tmp[0].retries || 1
              }
            } else {
              self.$message.error(res.desc)
            }
          },
        }
      )
    },

    emitChange() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.saveConf()
        } else {
          this.$emit('validFail')
        }
      })
    },

    saveConf() {
      let self = this
      let data = {
        botId: this.currentScene.botBoxId,
        url: this.form.url, //魔飞使用IoT模式时不需要URL，这里后端限制了必传。所以传个默认值
        token: this.form.token || this.getToken(),
        aesKey: this.form.aesKey || this.getToken(),
        isEncrypt: this.form.isEncrypt ? 1 : 0,
        timeout: this.form.timeout,
        retries: this.form.retries,
        // platform: this.form.platform,
        swtich: this.form.swtich,
        type: this.type,
      }
      if (this.form.backupUrl) {
        data.moreConfig = JSON.stringify([
          {
            url: this.form.backupUrl,
            retries: this.form.backupReties,
          },
        ])
      } else {
        this.form.backupReties = 1
        data.moreConfig = ''
      }
      self.$utils.httpPost(
        this.$config.api.AIUI_BOT_CONFIG_HANDLE_SAVE,
        JSON.stringify(data),
        {
          config: {
            headers: {
              'Content-Type': 'application/json;charset=UTF-8',
            },
          },
          success: (res) => {
            if (res.flag) {
              self.change = false
              self.$emit('saveSuccess')
            } else {
              self.$message.error(res.desc)
              self.$emit('saveFail')
            }
          },
          error: (err) => {
            self.$emit('saveFail')
          },
        }
      )
    },
  },
  watch: {
    currentScene(scene) {
      if (scene && scene.sceneBoxId) {
        this.getLLMConf()
      }
    },
  },
}
</script>

<style lang="scss" scoped>
@import '../../common.scss';
@import '../style.scss';
</style>
