<template>
  <div 
    class="os-modifier-select 
      el-popover el-popper el-popover--plain  editor-select-popover"
    v-if="variablePopover.show"
    v-clickoutside="closePopover"
    :style="popperStyle"
    visible-arrow="false"
    x-placement="bottom-start">
    <div class="editor-select-popover__body"
      @keyup.up="handleUp"
      @keyup.down="handleDown"
      @keyup.enter="handleEnter">
      <input
        class="focus-input"
        ref="focusInput"
        style="height: 0; overflow:hidden"
      />
      <div class="editor-select-popover-list" ref="popper" v-loading="loading">
        <template v-show="modifiers.length > 0 && !loading">
          <div class="editor-select-popover-item"
            :ref="'selectItem' + index"
            :class="{'highlight-item': index === selectIndex} "
            v-for="(item, index) in modifiers" :key="index"
            @click="selectItem(item)">
            {{item}}
          </div>
        </template>
        <div class="el-table__empty-block" v-if="!modifiers.length">
          <span class="el-table__empty-text">暂无数据</span>
        </div>
      </div>
    </div>
    <div x-arrow="" class="popper__arrow" style="left: 13.5px;"></div>
  </div>
</template>

<script>
export default {
  name: 'OsModifierSelect',
  props: {
    variablePopover: {
      type: Object,
      default: () => ({ 
        show: false
      })
    },
    inAddInput: false
  },
  data() {
    return {
      loading: false,
      rect: {
        top: 0,
        left: 0,
        width: 0
      },
      selectIndex: -1,
      modifiers: []
    }
  },
  computed: {
    popperStyle() {
      if (this.rect) {
        if(!this.inAddInput) {
          return {
            top: `${this.rect.top + 15 }px`,
            left: `${this.rect.left }px`
          } 
        }
        return {
          top:  `${ this.rect.top + 30 }px`,
          left: `${ this.rect.left + 20 + this.variablePopover.cursorPos * 8}px`
        }
      } else {
        return {
          display: `none`
        }
      }
    }
  },
  watch: {
    'variablePopover.rect': function() {
      this.rect = JSON.parse(JSON.stringify(this.variablePopover.rect))
    },
    'variablePopover.show': function(val) {
      if(val) {
        this.selectIndex = 0
        this.getData()
        this.$nextTick(function() {
          this.$refs.focusInput.focus()
        })
      } else {
        this.$emit('setModifier')
      }
    }
  },
  methods: {
    getData(){
      let data = {
        businessId: this.$store.state.studioSkill.id
      }
      let api = this.$config.api.STUDIO_MODIFIER_OF_SKILL
      if(this.variablePopover.type === 'usedInModifier'){
        //当前修饰语中获取所有可引用的修饰语
        data.modifierId = this.variablePopover.modifierId
        api = this.$config.api.STUDIO_MODIFIER_GET_MODIFIERS
      }
      if(!this.$store.state.studioSkill.id) return
      this.$utils.httpPost( api, data, {
        success: (res) => {
          if(res.data) {
            this.modifiers = this.filterNames(res.data)
          }
        },
        error: (err) => {
        }
      })
    }, 
    filterNames(list = []){
      let len = list.length, res = []
      if(!len) return []
      for(let i=0; i <len; i++) {
        res.push(list[i].name)
      }
      return res
    },
    closePopover(){
      this.variablePopover.show = false
    },
    selectItem(item) {
      this.$emit('setModifier', item)
      this.variablePopover.show = false
    },
    handleDown() {
      let self = this
      window.getSelection().removeAllRanges()
      this.selectIndex++
      if (this.selectIndex === this.modifiers.length ) {
        this.selectIndex = 0
      }
    },
    handleUp() {
      this.selectIndex--
      if (this.selectIndex < 0 ) {
        this.selectIndex = this.modifiers.length - 1
      }
    },
    handleEnter() {
      this.selectItem(this.modifiers[this.selectIndex])
    }
  }
}
</script>
<style lang="scss">
.os-modifier-select  {
  .editor-select-popover-list {
    padding-bottom:16px;
    height: auto;
  }
  .highlight-item {
    background: #e3f0fc;
  }
  .focus-input {
    height: 0;
    display: inherit;
    border: none;
    width: 100%;
  }
}
</style>