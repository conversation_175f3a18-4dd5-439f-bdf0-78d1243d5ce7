<template>
  <div class="container userinfo-page">
    <template v-if="userInfo">
      <div class="userinfo-page-detail mgb24">
        <div class="mgb16">
          <i class="ic-r-email vt-middle fs24 text-mid-grey mgr24" />
          <span class="vt-middle fs16 text-black">{{userInfo.email}}</span>
        </div>
        <div class="mgb16">
          <i class="ic-r-phone vt-middle fs24 text-mid-grey mgr24" />
          <span class="vt-middle fs16 text-black">{{userInfo.mobile}}</span>
        </div>
        <div class="lh15">
          <a class="fs14" v-on:click="dialog.show=!dialog.show">修改密码</a>
        </div>
      </div>
      <div v-if="!userInfo.isCertificated" class="userinfo-page-auth">
        <div class="userinfo-page-auth-icon"><i class="fs24 ic-r-exclamation text-unknown-yellow" /></div>
        <p class="fs18 text-black lh15 mgb16">您尚未实名认证！</p>
        <p class="fs14 text-black lh15 mgb8">实名认证后，您可获得更全面的语音服务、更多的云端免费交互次数。</p>
        <a
          class="fs14 text-underline lh15"
          href="https://console.xfyun.cn/user/realnameauth"
          target="_blank"
        >
          立即实名认证
        </a>
      </div>
      <div v-else class="userinfo-page-auth">
        <div class="userinfo-page-auth-icon"><i class="fs24 ic-r-success text-primary" /></div>
        <p class="fs18 text-black lh15 mgb16">您已通过讯飞云实名认证</p>
        <p class="fs14 lh15 mgb8"><span class="text-grey mgr16">认证身份：</span>{{this.userType[userInfo.type]}}</p>
        <p class="fs14 lh15"><span class="text-grey mgr16">公司名称：</span>{{userInfo.certificatedName}}</p>
      </div>
    </template>

    <reset-psw-dialog :dialog="dialog" />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import ResetPswDialog from './dialog/resetPswDialog'
export default {
  name: 'userInfo',
  data () {
    return {
      dialog: {
        show: false
      },
      userType: {
        1: '个人',
        2: '企业',
        3: '创业团队',
      }
    }
  },
  computed: {
    ...mapGetters({
      userInfo: 'user/userInfo'
    })
  },
  methods: {

  },
  components: {
    ResetPswDialog
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="scss" scoped>
.userinfo-page {
  width: 700px;
  margin: auto;
  padding-top: 48px;
  &-detail {
    width: 100%;
    height: 184px;
    border: 1px solid #e5e5e5;
    box-shadow: 0px 12px 24px 0px rgba(0,0,0, .08);
    padding: 36px 48px;
    border-radius: 12px;
  }
  &-auth {
    position: relative;
    width: 100%;
    height: 176px;
    background: #f8f8f8;
    border-radius: 12px;
    padding: 28px 104px;
    &-icon {
      width: 24px;
      height: 24px;
      position: absolute;
      left: 48px;
      top: 30px;
      margin: auto;
    }
  }
}

</style>
