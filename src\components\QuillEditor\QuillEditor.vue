<template>
  <div>
    <div class="quill-editor" :class="disabled ? '' : 'onedit'">
      <!-- <slot name="toolbar"></slot> -->
      <div ref="editor" class="box-test" @blur="handleBlur()"></div>
      <div :id="`cus-toolbar-${this.value.id}`" v-show="!disabled">
        <button class="ql-image"></button>
        <button
          :id="`ql-save-${this.value.id}`"
          value="save"
          style="float: right"
        >
          <i class="el-icon-circle-check custom-btn"></i>
        </button>
        <button
          :id="`ql-cancel-${this.value.id}`"
          value="cancel"
          style="float: right"
        >
          <i class="el-icon-circle-close custom-btn"></i>
        </button>
      </div>
    </div>
    <el-dialog
      title="查看"
      :visible.sync="dialogVisible"
      width="800px"
      class="create-dialog"
    >
      <quill-editor :disabled="true" :value="quillValue"></quill-editor>
    </el-dialog>
  </div>
</template>

<script>
import 'quill/dist/quill.core.css'
import 'quill/dist/quill.snow.css'
import 'quill/dist/quill.bubble.css'
// require sources
import Quill from 'quill'
// import ImageResize from 'quill-image-resize-module'
import {
  container,
  ImageExtend,
  QuillWatch,
} from './modules/quill-image-extend-module'
import Viewer from 'viewerjs'
import 'viewerjs/dist/viewer.css'

import TableThumbnailBlot from './modules/table/table'
import TableDetailBlot from './modules/table/tableDetail'

import PreviewModule from './modules/PreviewModule'

// Quill.register('modules/imageResize', ImageResize)
Quill.register('modules/ImageExtend', ImageExtend)

Quill.register(TableThumbnailBlot)
Quill.register(TableDetailBlot)
Quill.register('modules/preview', PreviewModule)

const handleCustomMatcher = (node, delta) => {
  // 添加换行符过滤
  delta.ops = delta.ops.filter((op) => op.insert !== '\n')
  delta.ops = delta.ops.map((op) => {
    let type = typeof op.insert
    if (type === 'string') {
      return {
        insert: op.insert.replace('\n', ''),
      }
    } else {
      return {
        insert: op.insert,
      }
    }
  })
  return delta
}

const defaultOptions = {
  theme: 'snow',
  boundary: document.body,
  modules: {
    toolbar: [
      ['bold', 'italic', 'underline', 'strike'],
      ['blockquote', 'code-block'],
      [{ header: 1 }, { header: 2 }],
      [{ list: 'ordered' }, { list: 'bullet' }],
      [{ script: 'sub' }, { script: 'super' }],
      [{ indent: '-1' }, { indent: '+1' }],
      [{ direction: 'rtl' }],
      [{ size: ['small', false, 'large', 'huge'] }],
      [{ header: [1, 2, 3, 4, 5, 6, false] }],
      [{ color: [] }, { background: [] }],
      [{ font: [] }],
      [{ align: [] }],
      ['clean'],
      ['link', 'image', 'video'],
    ],
  },
  placeholder: '请输入...',
  readOnly: false,
}

// pollfill
if (typeof Object.assign != 'function') {
  Object.defineProperty(Object, 'assign', {
    value(target, varArgs) {
      if (target == null) {
        throw new TypeError('Cannot convert undefined or null to object')
      }
      const to = Object(target)
      for (let index = 1; index < arguments.length; index++) {
        const nextSource = arguments[index]
        if (nextSource != null) {
          for (const nextKey in nextSource) {
            if (Object.prototype.hasOwnProperty.call(nextSource, nextKey)) {
              to[nextKey] = nextSource[nextKey]
            }
          }
        }
      }
      return to
    },
    writable: true,
    configurable: true,
  })
}

// export
export default {
  name: 'quill-editor',
  data() {
    let self = this
    console.log('this in data', this)
    return {
      _options: {},
      _content: '', //编辑器中的值
      _data: null,
      defaultOptions,
      dataGet: this.data,
      imageResizeModule: null,

      dialogVisible: false,
      quillValue: {
        knowledge: '',
        reference: {},
      },
      options: {
        modules: {
          toolbar: {
            container: `#cus-toolbar-${this.value.id}`,
            handlers: {
              image: function () {
                QuillWatch.emit(this.quill.id)
              },
            },
          },
          // toolbar: [ ['image']],
          ImageExtend: {
            name: 'file',
            loading: true,
            size: 10,
            action: '/aiui/web/knowledge/file/uploadFile',
            response: (res) => {
              console.log('res')
              return res.data[0]
            },
            sizeError: () => {
              this.$message.error('图片大小超过10M')
            },
          },
          clipboard: {
            // 粘贴板，处理粘贴时候的自带样式
            matchers: [[Node.ELEMENT_NODE, handleCustomMatcher]],
          },
          keyboard: {
            bindings: {
              // 阻止回车换行
              handleEnter: {
                key: 'Enter',
                handler: function (range, context) {
                  if (context.event) {
                    context.event.preventDefault()
                  }
                },
              },
            },
          },

          preview: {
            handlers: {
              detail(content) {
                // let cont = JSON.stringify({"title": [], "header": [[{"text": "设备/设施/系统名称", "reference": {}, "spanStyle": "Normal"}, {"text": "", "reference": {}, "spanStyle": "Normal"}, {"text": "型号/序列号", "reference": {}, "spanStyle": "Normal"}, {"text": "", "reference": {}, "spanStyle": "Normal"}]], "rowNum": 8, "colNum": 4, "cells": [[{"text": "位置", "reference": {}, "spanStyle": "Normal"}, {"text": "", "reference": {}, "spanStyle": "Normal"}, {"text": "子分部/系统名称", "reference": {}, "spanStyle": "Normal"}, {"text": "", "reference": {}, "spanStyle": "Normal"}], [{"text": "维护团队（公司）名称", "reference": {}, "spanStyle": "Normal"}, {"text": "", "reference": {}, "spanStyle": "colspan=3"}], [{"text": "故障现象描述", "reference": {}, "spanStyle": "Normal"}, {"text": "", "reference": {}, "spanStyle": "colspan=3"}], [{"text": "故障原因分析", "reference": {}, "spanStyle": "Normal"}, {"text": "", "reference": {}, "spanStyle": "colspan=3"}], [{"text": "维修步骤", "reference": {}, "spanStyle": "Normal"}, {"text": "", "reference": {}, "spanStyle": "colspan=3"}], [{"text": "维修结果及 反馈意见", "reference": {}, "spanStyle": "Normal"}, {"text": "", "reference": {}, "spanStyle": "colspan=3"}], [{"text": "客户评价", "reference": {}, "spanStyle": "Normal"}, {"text": "", "reference": {}, "spanStyle": "colspan=3"}], [{"text": "维修人", "reference": {}, "spanStyle": "Normal"}, {"text": "签字：年 月曰", "reference": {}, "spanStyle": "Normal"}, {"text": "运行负责人", "reference": {}, "spanStyle": "Normal"}, {"text": "签字：年 月曰", "reference": {}, "spanStyle": "Normal"}]]})
                // content = cont
                // console.log('detail contetn!!!!!', content)

                self.quillValue = {
                  knowledge: '<unused0>',
                  reference: {
                    unused0: {
                      link: '',
                      content,
                      format: 'table-detail',
                      suffix: 'xlsx',
                    },
                  },
                }
                self.dialogVisible = true
              },
            },
          },
        },
      },
    }
  },
  props: {
    value: {
      //传入的初始值
      type: Object,
      default: () => ({
        knowledge: '',
        reference: {},
      }),
    },
    disabled: {
      type: Boolean,
      default: true,
    },
    isAdd: {
      type: Boolean,
      default: false,
    },
  },
  mounted() {
    this.initialize()
    this._content = this.value
    let self = this
    this.$refs.editor.addEventListener('keyup', function (event) {
      // 添加回车键保存
      if (event.key === 'Enter') {
        // console.log('初始数据',self.value)
        self.$emit('save', self._content)
      }
    })
  },
  beforeDestroy() {
    this.quill = null
    // this.viewer.destroy()
    delete this.quill
  },
  methods: {
    // Init Quill instance
    initialize() {
      if (this.$el) {
        // Options
        this._options = Object.assign({}, this.defaultOptions, this.options)
        // console.log("this.defaultOptions", this._options);

        // Instance
        this.quill = new Quill(this.$refs.editor, this._options)

        this.quill.enable(false)

        // Set editor content
        if (this.value || this.data) {
          this.render(this.value || this.data)
        }

        // Disabled editor
        if (!this.disabled) {
          this.quill.enable(true)
        }
        // Mark model as touched if editor lost focus
        // this.quill.on("selection-change", (range) => {
        //   console.log(range);
        //   if (!range) {
        //     // this.$emit("blur", this.quill);
        //     this.$emit("blur", this._content);
        //   } else {
        //     this.$emit("focus", this.quill);
        //   }
        // });

        // Update model if text changes
        this.quill.on('text-change', (delta, oldDelta, source) => {
          const quill = this.quill
          const deltas = quill.getContents()
          console.log('text-change,quill.getContents()', deltas)
          if (this.viewer) {
            this.viewer.update()
          }
          // 将deltas转成接口协议格式
          let data = {
            newKnowledge: '',
            newReference: {},
          }
          let index = 1
          deltas.forEach((dt) => {
            const type = typeof dt.insert
            if (type === 'string') {
              data.newKnowledge = data.newKnowledge + dt.insert
            }
            if (type === 'object') {
              if (dt.insert.image) {
                if (dt.insert.image.startsWith('data:image/')) {
                  data.newKnowledge = data.newKnowledge + `<unused${index}>`
                  const suffix = dt.insert.image.split(';')[0].split('/')[1]
                  data.newReference[`unused${index}`] = {
                    suffix,
                    format: 'img',
                    content: this.extractBase64Data(dt.insert.image),
                    link: '',
                  }
                  index++
                }
                if (dt.insert.image.startsWith('http')) {
                  data.newKnowledge = data.newKnowledge + `<unused${index}>`
                  let lastIndex = dt.insert.image.lastIndexOf('.')
                  let suffix = '' //文件类型
                  if (lastIndex !== -1) {
                    suffix = dt.insert.image.substring(lastIndex + 1)
                  }
                  data.newReference[`unused${index}`] = {
                    suffix,
                    format: 'img',
                    content: '',
                    link: dt.insert.image,
                  }
                  index++
                }
              }
              if (dt.insert['table']) {
                if (dt.insert['table'].content) {
                  data.newKnowledge = data.newKnowledge + `<unused${index}>`
                  const suffix = 'xlsx'
                  data.newReference[`unused${index}`] = {
                    suffix,
                    format: 'table',
                    content: dt.insert['table'].content,
                    link: '',
                  }
                  index++
                }
              }
              if (dt.insert['table-detail']) {
                if (dt.insert['table-detail'].content) {
                  data.newKnowledge = data.newKnowledge + `<unused${index}>`
                  const suffix = 'xlsx'
                  data.newReference[`unused${index}`] = {
                    suffix,
                    format: 'table-detail',
                    content: dt.insert['table-detail'].content,
                    link: '',
                  }
                  index++
                }
              }
            }
          })
          // console.log("处理后data", data);
          this._content = Object.assign({}, this.value, data)
          // this.$emit("input", this._data);
          // this.$emit("change", { data, quill });
        })

        // Emit ready event
        // this.$emit("ready", this.quill);

        // 添加取消和保存功能
        let self = this
        let cancelButton = document.querySelector(`#ql-cancel-${this.value.id}`)
        cancelButton.addEventListener('click', function () {
          self.$emit('cancel', self._content)
        })

        let saveButton = document.querySelector(`#ql-save-${this.value.id}`)
        saveButton.addEventListener('click', function () {
          self.$emit('save', self._content)
        })

        this.viewer = new Viewer(this.$refs.editor, {
          rotatable: false,
          loop: false,
          toolbar: {
            zoomIn: true,
            zoomOut: true,
          },
        })
      }
    },

    /**
     * 词法分析器
     * @param input 字符串
     * @returns token列表
     */

    tokenizer(input) {
      // 输入字符串处理的索引
      let current = 0

      // token列表
      let tokens = []

      // 遍历字符串，解析token
      while (current < input.length) {
        let char = input[current]

        // 匹配如下字符串，以<>包裹
        //   (hello <unused1> <unused2>)
        //           ^^^^^^^   ^^^^^^^
        if (char === '<') {
          let value = ''

          // 跳过<
          char = input[++current]

          // 获取双引号之间的所有字符串

          while (char !== '>') {
            value += char
            char = input[++current]
          }

          // 跳过右双引号

          char = input[++current]

          // type 为 'string'，value 为字符串参数

          tokens.push({
            type: 'tag',
            value,
          })

          continue
        }

        if (char !== '<' && typeof char !== 'undefined') {
          let value = ''
          // 获取连续字符
          while (char !== '<' && typeof char !== 'undefined') {
            value += char
            char = input[++current]
          }

          // type 为 'string'，value 为普通字符串
          tokens.push({
            type: 'string',
            value,
          })

          continue
        }

        // 无法识别的字符，抛出错误提示
        throw new Error(`I dont know what this character is: ${char}`)
      }

      // 返回词法分析器token列表
      return tokens
    },

    render(val) {
      const tokens = this.tokenizer(val.knowledge)
      // console.log('--------tokens-----------', tokens)
      let delta = []
      tokens.forEach((token) => {
        if (token.type === 'string') {
          delta.push({ insert: token.value })
        }
        if (token.type === 'tag') {
          if (val.reference[token.value]) {
            let type = val.reference[token.value].format
            if (type === 'img') {
              if (val.reference[token.value].content) {
                delta.push({
                  insert: {
                    image:
                      'data:image/png;base64,' +
                      val.reference[token.value].content,
                  },
                })
              } else if (val.reference[token.value].link) {
                delta.push({
                  insert: {
                    image: val.reference[token.value].link,
                  },
                })
              }
            }
            if (type === 'table') {
              if (val.reference[token.value].content) {
                delta.push({
                  insert: {
                    table: {
                      content: val.reference[token.value].content,
                    },
                  },
                })
              }
            }
            if (type === 'table-detail') {
              if (val.reference[token.value].content) {
                delta.push({
                  insert: {
                    ['table-detail']: {
                      content: val.reference[token.value].content,
                    },
                  },
                })
              }
            }
          } else {
            // 没有找到映射，原样输出
            delta.push({ insert: `<${token.value}>` })
          }
        }
      })
      this.quill.setContents(delta)
    },
    extractBase64Data(base64String) {
      const parts = base64String.split(',')
      if (parts.length === 2) {
        return parts[1]
      } else {
        return base64String
      }
    },
    saveContent() {
      this.$emit('save', self._content)
    },
    handleBlur() {
      console.log('失焦 in')
    },
  },
  watch: {
    // Watch data change
    data(newVal, oldVal) {
      if (this.quill) {
        if (newVal && newVal !== this._data) {
          this._data = newVal
          this.render(newVal)
        } else if (!newVal) {
          this.quill.setText('')
        }
      }
    },

    value(newVal, oldVal) {
      if (this.quill) {
        if (newVal && newVal !== this._data) {
          this._data = newVal
          this.render(newVal)
        } else if (!newVal) {
          this.quill.setText('')
        }
      }
      if (this.viewer) {
        this.viewer.update()
      }
    },
    // Watch disabled change
    disabled(newVal, oldVal) {
      this._content = this.value
      if (this.quill) {
        this.quill.enable(!newVal)
        if (newVal) {
          // this.quill.options.modules.imageResize = false
          this.quill.update()
        } else {
          this.quill.focus()
        }
      }
    },

    _content(val) {
      console.log('contnent变化', val)
    },
  },
}
</script>
<style scoped lang="scss">
.ql-container.ql-snow {
  border: 0px solid #ccc;
}
.onedit {
  border: 1px solid #3d6fff;
}
.ql-editor {
  .ql-placeholder {
    font-size: 20px;
    color: red;
  }
}
.custom-btn {
  font-size: 18px;
  color: #909399;
}
.box-test {
  padding-bottom: 18px;
  :deep(img) {
    cursor: pointer;
    height: 25px;
  }
}
</style>

<style lang="scss">
.table-thumbnail {
  display: inline-block;
  width: 30px;
  height: 30px;
  background: url('./modules/table/table.png') center/100% no-repeat;
  border: none;
}
.quill-table-detail {
  border: 1px solid #ddd;
  border-collapse: collapse;
  width: 100%;
  line-height: 30px;

  tr {
    padding: 2px;
    text-align: left;
    border: 1px solid #ddd;
  }
  tr td {
    padding: 10px;
    text-align: left;
    border: 1px solid #ddd;
  }
}
</style>
