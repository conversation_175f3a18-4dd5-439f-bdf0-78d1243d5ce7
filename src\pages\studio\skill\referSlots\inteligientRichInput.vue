<template>
  <div
    class="relative input-container"
    @keyup.219="flowerBracketHandle"
    ref="inputContainerRef"
  >
    <div
      class="content-container"
      :style="{
        width: showSwitch ? 'calc(100% - 136px)' : '100%',
      }"
    >
      <!-- <el-input
        class=""
        type="textarea"
        :rows="1"
        ref="richContentRef"
        :placeholder="placeholder"
        :disabled="false"
        @blur="onBlur"
        @keyup.native.up.stop.prevent="handleUp"
        @keyup.native.down.stop.prevent="handleDown"
        :value="value"
        @input="onInputChange"
        @keyup.native.enter.stop.prevent="onEnterUp"
      >
      </el-input> -->
      <div class="rich-content-container">
        <div
          ref="richContentRef"
          :contenteditable="!disabled"
          :editindex="editIndex"
          class="rich-content"
          v-html="dangerouslyInnerHTML"
          @keyup.up.stop.prevent="handleUp"
          @keyup.down.stop.prevent="handleDown"
          @keyup.enter.stop.prevent="onEnterKeyUp"
          @keydown.enter.stop.prevent
          @input="onInputChange"
          :placeholder="placeholder"
          @paste="onPaste"
          @copy="onCopy"
          @blur.stop.prevent="onBlur"
        ></div>
        <div
          class="button-save"
          v-if="edit && value.changed && !saveOnBlur"
          @click="handleEdit()"
        >
          <i class="el-icon el-icon-success"></i>
        </div>
      </div>
    </div>

    <el-button
      v-if="showAdd"
      slot="suffix"
      type="text"
      class="utterance-add-area-addbtn"
      size="small"
      @click="doAdd"
      :disabled="disabled"
    >
      添加
    </el-button>
    <!-- <div v-if="showSwitch" class="style-container">
      <span>回复风格：</span>
      <a-switch
        checked-children="儿童"
        un-checked-children="成人"
        v-model="checkedValue"
      />
    </div> -->

    <entity-auxiliary-popover
      ref="entityAuxiliaryPopover"
      :variablePopover="variablePopover"
      @setSlot="setSlot"
    />
  </div>
</template>
<script>
import EntityAuxiliaryPopover from './replyEntityAuxiliaryPopover.vue'
import { debounce } from 'lodash-es'
import Switch from 'ant-design-vue/lib/switch'
import 'ant-design-vue/lib/switch/style/css'

const RICHCONTENT = 'rich-content'
const compare = function (prop) {
  return function (obj1, obj2) {
    var val1 = obj1[prop]
    var val2 = obj2[prop]
    if (val1 < val2) {
      return -1
    } else if (val1 > val2) {
      return 1
    } else {
      return 0
    }
  }
}
let EDITORRANGE = null
// 作为一个全局变量
let USEDLABELS = []

// 区别于属性的editIndex，由于document.onselectionchange事件必须挂载在document上，
// 事件触发了 可能不是在当前组件实例中触发的，不能取得预期的editIndex
let activeIndex = -1

export default {
  name: 'inteligient-rich-input',
  props: {
    placeholder: String,
    value: {
      type: Object,
      default: {
        text: '',
        labels: [],
        type: '1',
        changed: false,
      },
    },
    disabled: {
      type: Boolean,
      default: false,
    },

    showAdd: { type: Boolean, default: true },
    showSwitch: { type: Boolean, default: false },
    hasSlot: {
      type: Boolean,
      default: true,
    },

    // 用于 edit模式
    edit: {
      type: Boolean,
      default: false,
    },
    // 在编辑的组件实例中可取得
    editIndex: {
      type: Number,
      default: -1,
    },
    // 同一个划词处是否运行添加多个标签
    multipleMark: {
      type: Boolean,
      default: true,
    },
    // 是否渲染标签
    renderMark: {
      type: Boolean,
      default: true,
    },
    // 是否自动保存，还是点击勾选保存数据
    saveOnBlur: {
      type: Boolean,
      default: false,
    },
    // disabled 为true时支持搜索
    searchVal: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      dangerouslyInnerHTML: '',
      // 原来 inteligient-input 功能
      cursorPos: -1,
      variablePopover: {
        show: false,
        rect: null,
        style: null,
      },
      checkedValue: false,
    }
  },
  watch: {
    checkedValue(val) {
      this.$emit('checkedChange', val)
    },
    renderMark(val) {
      this.init()
    },
    searchVal(val) {
      // 将关键词高亮标识
      if (this.disabled) {
        if (val) {
          let { labels, text } = this.value
          let str = this.handleStr(text, this.renderMark ? labels : [])
          let searchedStr = str.replace(
            new RegExp(val.trim(), 'im'),
            "<span class='qabank-page-hight-light'>" + val.trim() + '</span>'
          )
          this.dangerouslyInnerHTML = searchedStr
        } else if (val === '') {
          this.init()
        }
      }
    },
  },
  computed: {},
  mounted() {
    // let originText = '{today}各位领导大家好欢迎来到{合肥}科大讯飞'
    // let markInfo = [
    //   { start: 8, end: 10, type: '1' },
    //   { start: 5, end: 8, type: '1' },
    // ]

    this.init()
  },
  methods: {
    init() {
      let { labels, text } = this.value
      let str = this.handleStr(text, this.renderMark ? labels : [])
      this.dangerouslyInnerHTML = str
      this.bindEvents()
    },
    isSelectionEmpty(range) {
      if (range && range.startContainer) {
        if (range.startContainer === range.endContainer) {
          if (range.startOffset === range.endOffset) {
            return true
          }
        }
      }
      return false
    },
    bindEvents() {
      if (this.disabled) {
        return
      }
      let that = this
      document.onselectionchange = () => {
        // console.log(that.$el)
        let selection = window.getSelection()
        let anchorNode = selection.anchorNode
        // console.log('onselectionchange', anchorNode)
        const isContentEditable =
          anchorNode &&
          anchorNode.className === RICHCONTENT &&
          anchorNode.getAttribute('contenteditable') === 'true'

        const isContentEditableSub =
          anchorNode &&
          anchorNode.parentNode &&
          anchorNode.parentNode.className === RICHCONTENT &&
          anchorNode.parentNode.getAttribute('contenteditable') === 'true'

        // 点击了删除节点
        const isDelete =
          anchorNode &&
          anchorNode.parentNode &&
          anchorNode.parentNode.parentNode &&
          anchorNode.parentNode.parentNode.parentNode &&
          anchorNode.parentNode.parentNode.parentNode.getAttribute(
            'contenteditable'
          ) === 'true'
        if (isDelete) {
          activeIndex = Number(
            anchorNode.parentNode.parentNode.parentNode.getAttribute(
              'editindex'
            ) || -1
          )
        }

        if (isContentEditable) {
          activeIndex = Number(anchorNode.getAttribute('editindex') || -1)
        }
        if (isContentEditableSub) {
          activeIndex = Number(
            anchorNode.parentNode.getAttribute('editindex') || -1
          )
        }
        if (isContentEditable || isContentEditableSub) {
          EDITORRANGE = that.saveSelection()
          // console.log('document.getSelection', document.getSelection())
          // console.log('onselectionchange saveSelection', EDITORRANGE)
        }
      }

      this.$nextTick(() => {
        // console.log(
        //   'this.$refs.richContentRef.querySelector',
        //   this.$refs.richContentRef.querySelector('.delete')
        // )
        this.bindLabelEvents()
      })
    },

    bindLabelEvents() {
      const iNodes = this.$refs.richContentRef.querySelectorAll('.delete')
      if (iNodes && Array.from(iNodes).length > 0) {
        iNodes.forEach((iNode) => {
          iNode.addEventListener('click', () => {
            let range = new Range()
            range.selectNode(iNode.parentNode)
            window.getSelection().addRange(range)
            range.deleteContents()
            window.getSelection().removeAllRanges()
            this.normalizeEditor()
            this.$emit('change')
          })
        })
      }

      // 点击hightlight元素，聚焦
      const highlightNodes =
        this.$refs.richContentRef.querySelectorAll('.highlight')
      if (highlightNodes && Array.from(iNodes).length > 0) {
        highlightNodes.forEach((highlightNode) => {
          highlightNode.addEventListener('click', () => {
            let highlightRange = new Range()
            highlightRange.selectNode(highlightNode)
            EDITORRANGE = this.saveSelection(highlightRange)
            window.getSelection().removeAllRanges()
            window.getSelection().addRange(highlightRange)
          })
        })
      }
    },
    // 保存光标位置
    saveSelection() {
      if (window.getSelection) {
        const sel = window.getSelection()
        if (sel.getRangeAt && sel.rangeCount) {
          return sel.getRangeAt(0)
        }
      } else if (document.selection && document.selection.createRange) {
        return document.selection.createRange()
      }
      return null
    },
    // 重置光标位置
    restoreSelection(range) {
      if (range) {
        if (window.getSelection) {
          const sel = window.getSelection()
          sel.removeAllRanges()
          sel.addRange(range)
        } else if (document.selection && range.select) {
          range.select()
        }
      }
    },

    insertLabel(label) {
      if (this.disabled) {
        return
      }
      // 获取选中区域，如果有，（参考文档），将该标签插入选中区域的后面
      if (EDITORRANGE && EDITORRANGE.toString()) {
        this.restoreSelection(EDITORRANGE)
      } else {
        return this.$message.warning('请先选中待插入标签的文本')
      }
      let range = EDITORRANGE
      console.log('insertLabel+++++++++++++EDITORRANGE', EDITORRANGE)
      // let empty = this.isSelectionEmpty(range)
      // if (empty) {
      //   return this.$message.warning('请先选中待插入标签的文本11')
      // }
      USEDLABELS.push(label)
      //  1、 根据range类型判断是何种插入类型
      // console.log('insertLabel-----------EDITORRANGE', EDITORRANGE)

      const { startContainer, endContainer } = range
      // let range = EDITORRANGE

      const isSameNode = startContainer === endContainer
      const isTextNode = startContainer.nodeType === 3
      const isSpanNode =
        startContainer.nodeType === 1 && startContainer.className === 'whole'
      const isRichContentDirectChild =
        startContainer.parentNode.className === RICHCONTENT
      const canInsertNormal =
        isSameNode && isRichContentDirectChild && isTextNode
      const canInsert = isSameNode && isRichContentDirectChild && isSpanNode

      if (canInsertNormal) {
        // 第一种，常规类型
        let empty = this.isSelectionEmpty(range)
        if (empty) {
          return this.$message.warning('请先选中待插入标签的文本')
        }
        let txtContent = range.toString()
        let regrex = /\{(.+?)\}/g
        //  判断另外一种情况，即为在{}中取值的情况，这种情况也是不允许
        if (
          (txtContent.includes('{') || txtContent.includes('}')) &&
          !regrex.test(txtContent)
        ) {
          // 包含了一个花括号的情况
          return this.$message.warning('选区必须完整包含槽位')
        }

        let { startOffset, endOffset } = range
        let totalText = startContainer.textContent
        // console.log('startOffset, endOffset', startOffset, endOffset)
        // console.log('totalText', totalText)
        // console.log(
        //   'totalText[startOffset-1], totalText[endOffset]',
        //   totalText[startOffset - 1],
        //   totalText[endOffset]
        // )
        let bolLeft, bolRight

        for (let i = startOffset - 1; i >= 0; i--) {
          if (totalText[i] === '}') {
            break
          } else if (totalText[i] === '{') {
            bolLeft = true
            break
          }
        }
        for (let j = endOffset; j < totalText.length; j++) {
          if (totalText[j] === '}') {
            bolRight = true
            break
          } else if (totalText[j] === '{') {
            break
          }
        }
        if (bolLeft && bolRight) {
          // 选了花括号中间的情况
          return this.$message.warning('选区必须完整包含槽位')
        }
        this.insertLabelNormal(label, range)
      } else if (canInsert) {
        if (!this.multipleMark) {
          // 不支持插入多个标签时，直接返回
          return this.$message.warning('只支持插入一个标签')
        }
        // 第二种，
        // 判断待插入的标签判重
        let dom = startContainer

        let newImgNode = document.createElement('img')
        newImgNode.classList.add('label')
        newImgNode.src =
          label.picture ||
          'https://aiui-file.cn-bj.ufileos.com/avatar/default.png'
        newImgNode.title = label.zhName
        newImgNode.setAttribute('data-label-id', label.id)
        let originLabelId = dom.firstChild.getAttribute('data-label-id')
        if (originLabelId.split(',').includes(String(label.id))) {
          return this.$message.warning('不得重复插入标签')
        }
        dom.firstChild.setAttribute(
          'data-label-id',
          `${originLabelId},${label.id}`
        )
        dom.appendChild(newImgNode)
        this.$parent.onChange(activeIndex)
        window.getSelection().removeAllRanges()
        EDITORRANGE = null
      } else {
        return this.$message.warning('不支持包含已经插入标签的文本')
      }
    },

    insertLabelNormal(label, range) {
      let newNode = document.createElement('span')
      newNode.classList.add('highlight')
      // 绑定点击事件，点击后即聚焦
      newNode.addEventListener('click', () => {
        let highlightRange = new Range()
        // highlightRange.setStart(newNode.childNodes[0], 0)
        // highlightRange.setEnd(newNode.childNodes[0], newNode.childNodes[0].textContent.length)
        highlightRange.selectNode(newNode)
        EDITORRANGE = this.saveSelection(highlightRange)
        // console.log('hightlightRange', EDITORRANGE)
        window.getSelection().removeAllRanges()
        window.getSelection().addRange(highlightRange)
      })
      newNode.setAttribute('data-label-id', label.id)
      try {
        range.surroundContents(newNode)
        range.setStartAfter(newNode)
        range.collapse(true)

        let iNode = document.createElement('i')
        iNode.classList.add('delete')
        iNode.innerHTML = '×'
        iNode.addEventListener('click', (e) => {
          range.selectNode(iNode.parentNode)
          range.deleteContents()
          window.getSelection().removeAllRanges()
          this.normalizeEditor()

          this.$parent.onChange(activeIndex)
        })
        range.insertNode(iNode)
        range.setStartAfter(iNode)

        let imgNode = document.createElement('img')
        imgNode.classList.add('label')
        imgNode.src =
          label.picture ||
          'https://aiui-file.cn-bj.ufileos.com/avatar/default.png'
        imgNode.setAttribute('data-label-id', label.id)
        imgNode.setAttribute('title', label.zhName)
        imgNode.setAttribute('draggable', 'false')
        range.insertNode(imgNode)

        //再次调整range,包含span和img， 外面包含span
        range.setStartBefore(newNode)
        range.setEndAfter(imgNode)
        // collapse(toStart) if toStart=true set end=start, otherwise set start=end, thus collapsing the range
        // range.collapse(true)
        let spanNode = document.createElement('span')
        spanNode.classList.add('whole')
        spanNode.setAttribute('contenteditable', 'false')
        range.surroundContents(spanNode)
        range.collapse(true)
        this.$parent.onChange(activeIndex)
        window.getSelection().removeAllRanges()
        EDITORRANGE = null
      } catch (e) {
        console.log(e)
      }
    },
    insertSlot(val) {
      // 在花括号后插入槽值字符串
      if (EDITORRANGE) {
        this.restoreSelection(EDITORRANGE)
      }
      let textNode
      let tempRange = EDITORRANGE.cloneRange()
      tempRange.setStart(
        EDITORRANGE.startContainer,
        EDITORRANGE.startOffset - 1
      )
      // console.log('tempRange', tempRange.toString())
      let char = tempRange.toString()
      if (char === '{') {
        textNode = document.createTextNode(val + '}')
        EDITORRANGE.insertNode(textNode)
        EDITORRANGE.collapse(false)
        this.normalizeEditor()
        // hack修复插入后变成两个}的问题，是搜狗输入法引入，如果后面又紧跟着}，则将}删除
        let nextRange = EDITORRANGE.cloneRange()
        try {
          nextRange.setEnd(EDITORRANGE.endContainer, EDITORRANGE.endOffset + 1)
        } catch (e) {
          console.log(e)
        }
        const nextChar = nextRange.toString()
        // console.log('nextChar is', nextChar)
        if (nextChar === '}') {
          nextRange.deleteContents()
          window.getSelection().addRange(nextRange)
        }
      } else if (char === '}') {
        EDITORRANGE.setStart(
          EDITORRANGE.startContainer,
          EDITORRANGE.startOffset - 1
        )
        EDITORRANGE.collapse(true)
        textNode = document.createTextNode(val)
        EDITORRANGE.insertNode(textNode)
        EDITORRANGE.collapse(false)
        EDITORRANGE.setStart(
          EDITORRANGE.startContainer,
          EDITORRANGE.startOffset + 1
        )
        EDITORRANGE.collapse(true)
      } else {
      }
    },
    // deSerializeByRange(range, label) {
    //   let newNode = document.createElement('span')
    //   newNode.classList.add('highlight')
    //   newNode.setAttribute('data-label-id', label.id)
    //   try {
    //     range.surroundContents(newNode)
    //     range.setStartAfter(newNode)
    //     range.collapse(true)

    //     let imgNode = document.createElement('img')
    //     imgNode.classList.add('label')
    //     imgNode.src = label.picture

    //     // let iNode = document.createElement('i')
    //     range.insertNode(imgNode)
    //     range.setStartAfter(imgNode)

    //     let iNode = document.createElement('i')
    //     iNode.classList.add('delete')
    //     iNode.innerHTML = '×'
    //     iNode.addEventListener('click', (e) => {
    //       //
    //       range.selectNode(iNode.parentNode)
    //       range.deleteContents()
    //       window.getSelection().removeAllRanges()
    //     })
    //     range.insertNode(iNode)

    //     //再次调整range,包含span和img， 外面包含span
    //     range.setStartBefore(newNode)
    //     range.setEndAfter(iNode)
    //     console.log('printRange', range)
    //     // collapse(toStart) if toStart=true set end=start, otherwise set start=end, thus collapsing the range
    //     // range.collapse(true)
    //     let spanNode = document.createElement('span')
    //     spanNode.classList.add('whole')
    //     spanNode.setAttribute('contenteditable', 'false')
    //     range.surroundContents(spanNode)
    //     range.collapse(true)
    //     window.getSelection().removeAllRanges()
    //   } catch (e) {
    //     console.log(e)
    //   }
    // },
    // 将后端给的startOffset， endOffset转为前端需要的，在后端，将字符串中的{today}算作一个字符处理
    ajustOffset(text = '', markInfo = []) {
      // 将原始text转为数组，数组对应的下标为服务端的偏移offset
      let textArr = []
      let i = 0
      do {
        if (text[i] === '{') {
          let start = i
          while (text[i] !== '}' && i < text.length) {
            // 防止没有结束}造成的数组越界
            i++
            if (text[i + 1] && text[i + 1] === '{') {
              // 预防缺少结束}造成的切割错误
              break
            }
          }
          textArr.push(text.substring(start, i + 1))
        } else {
          textArr.push(text[i])
        }
        i++
      } while (i < text.length)
      // step 2: 新建一个map，key是textArr的索引，value是修正过的索引
      // console.log('textArr', textArr)
      let ajustMap = {}
      let offset = 0
      for (let j = 0, len = textArr.length; j < len; j++) {
        if (textArr[j].length > 1) {
          offset = offset + textArr[j].length - 1
        } else {
          // offset = offset + 1
        }
        ajustMap[j + 1] = j + 1 + offset
      }
      ajustMap[0] = 0
      ajustMap[textArr.length] = textArr.length + offset
      return markInfo.map((item) => {
        return {
          ...item,
          startOffset: ajustMap[item.startOffset],
          endOffset: ajustMap[item.endOffset],
        }
      })
    },
    convertMarkInfo(markInfo = []) {
      let newMarkInfo = []
      for (let i = 0, len = markInfo.length; i < len; i++) {
        let item = markInfo[i]
        let index = newMarkInfo.findIndex(
          (im) =>
            im.startOffset === item.startOffset &&
            im.endOffset === item.endOffset
        )
        if (index === -1) {
          newMarkInfo.push({
            startOffset: markInfo[i].startOffset,
            endOffset: markInfo[i].endOffset,
            labels: [
              {
                labelId: markInfo[i].labelId,
                value: markInfo[i].value,
                label: markInfo[i].label,
                picture: markInfo[i].picture,
                zhName: markInfo[i].zhName,
              },
            ],
          })
        } else {
          newMarkInfo[index].labels.push({
            labelId: markInfo[i].labelId,
            value: markInfo[i].value,
            label: markInfo[i].label,
            picture: markInfo[i].picture,
            zhName: markInfo[i].zhName,
          })
        }
      }
      return newMarkInfo
    },
    handleStr(originText, mark = []) {
      if (!originText) {
        return ''
      }
      if (mark.length <= 0) {
        return originText
      }
      USEDLABELS = USEDLABELS.concat(
        mark.map((im) => {
          return {
            ...im,
            id: im.labelId,
          }
        })
      )
      console.log('this.usedlabels', USEDLABELS)
      let markInfo = this.ajustOffset(originText, mark)
      // 确保start从小到大排列
      markInfo = markInfo.sort(compare('startOffset'))
      // 转换markInfo的格式，将相同的startOffset,endOffset项目合并
      // [{startOffset: 1, endOffset: 2, labels:[]}]
      markInfo = this.convertMarkInfo(markInfo)
      // 最终要的html字符串
      let totalStr = ''
      // 当前处理
      let presentStr = ''
      // 历史处理之和
      let historyStr = ''
      // 将要处理
      let nextStr = originText
      let self = this

      function replaceStr(markIndex) {
        if (markIndex >= markInfo.length) {
          // TODO:
          totalStr += nextStr
          return
        }
        // 核减历史偏移量
        let startOffset = markInfo[markIndex].startOffset - historyStr.length
        let endOffset = markInfo[markIndex].endOffset - historyStr.length

        presentStr = nextStr.substring(0, endOffset)

        const labelText = presentStr.substring(startOffset, endOffset)
        const imageStrs = markInfo[markIndex].labels
          .map((im) => {
            return `<img class="label" src="${
              im.picture ||
              'https://aiui-file.cn-bj.ufileos.com/avatar/default.png'
            }" data-label-id="${im.labelId}" title="${im.zhName || im.value}"/>`
          })
          .join('')
        const labelIds = markInfo[markIndex].labels
          .map((im) => im.labelId)
          .join(',')
        const deleteStr = self.disabled ? '' : `<i class="delete">×</i>`
        const str = `<span class="whole" contenteditable="false"><span class="highlight" data-label-id="${labelIds}">${labelText}</span>${deleteStr}${imageStrs}</span>`

        // totalStr += presentStr.replace(labelText, str)
        // let targetStr = ''
        // for (let i = 0; i < presentStr.length; i++) {
        //   if (i < startOffset) {
        //     targetStr += presentStr[i]
        //     continue
        //   }
        //   if (i >= startOffset && i < endOffset - 1) {
        //     continue
        //   }
        //   if (i === endOffset - 1) {
        //     targetStr += str
        //     continue
        //   }
        //   if (i >= endOffset) {
        //     targetStr += presentStr[i]
        //   }
        // }
        let headPart = presentStr.substring(0, startOffset)
        let tailPart = presentStr.substring(endOffset, presentStr.length)
        let targetStr = headPart + str + tailPart
        totalStr += targetStr

        nextStr = nextStr.substring(endOffset, nextStr.length)
        historyStr += presentStr

        replaceStr(markIndex + 1)
      }
      replaceStr(0)
      return totalStr
    },
    traverseNode(node) {
      const nodeStack = [node]
      let curNode = null
      let offset = 0
      let labels = []
      let text = ''
      while ((curNode = nodeStack.pop())) {
        const children = curNode.childNodes
        for (let i = children.length - 1; i >= 0; i--) {
          nodeStack.push(children[i])
        }
        if (
          curNode.nodeType === 3 &&
          curNode.parentNode.className === 'highlight'
        ) {
          // 这里不含有 {} 插槽
          // console.log('offset', offset, curNode.textContent)
          let labelIds = curNode.parentNode
            .getAttribute('data-label-id')
            .split(',')
          labelIds.forEach((id) => {
            let labelObj = USEDLABELS.find((item) => item.id == id)
            labels.push({
              startOffset: offset,
              endOffset:
                offset + curNode.textContent.replace(/\{(.+?)\}/g, '*').length,
              labelId: id,
              value: labelObj.value,
              label: labelObj.label,
              picture: labelObj.picture,
              zhName: labelObj.zhName,
            })
          })

          text += curNode.textContent
          let tmpText = curNode.textContent.replace(/\{(.+?)\}/g, '*') // *为占位
          offset += tmpText.length
        } else if (
          curNode.nodeType === 3 &&
          curNode.parentNode.className !== 'delete'
        ) {
          text += curNode.textContent
          // 如果存在插槽{}部分
          let tempText = curNode.textContent.replace(/\{(.+?)\}/g, '*') // *为占位
          offset += tempText.length
        }
      }
      // console.log('labels', { text, labels })
      return { text, labels }
    },

    getNewValue(node) {
      const result = this.traverseNode(node)
      this.$emit('input', result)
    },

    // 原来 inteligient-input 中的业务代码
    resetCheckedValue() {
      this.checkedValue = false
    },
    clearHTML() {
      this.$refs.richContentRef.innerHTML = ''
    },

    // 富文本 input 实时输入事件
    onInputChange() {
      if (this.disabled) {
        return
      }
      this.$emit('change')
      // 计算值
      EDITORRANGE = this.saveSelection()
    },

    // onInputChange: debounce(function () {
    //   if (this.disabled) {
    //     return
    //   }
    //   this.$emit('change')
    //   // 计算值
    //   EDITORRANGE = this.saveSelection()
    // }, 100),

    flowerBracketHandle(e) {
      // hasSlot 针对的是某些不需要插槽的业务
      if (this.disabled || !this.hasSlot) {
        return
      }
      let self = this
      const cursor = self.getCursortPosition()
      const value = e.target.textContent
      const rect = window.getSelection().getRangeAt(0).getBoundingClientRect()
      const rectInput = this.$refs.inputContainerRef.getBoundingClientRect()
      // console.log('光标:', rect)
      // console.log('输入框:', rectInput)
      const left = rect.left - rectInput.left
      const top = rect.top - rectInput.top + 40
      let styleObj = {
        position: 'absolute',
        left: `${left}px`,
        top: `${top}px`,
      }
      // 318 是弹出框的宽度
      if (left + 318 > rectInput.width) {
        styleObj = { ...styleObj, right: 0 }
        delete styleObj.left
      }

      // if (value[cursor - 1] === '{') {
      //   self.cursorPos = cursor
      // }
      // if (value.substring(cursor - 2, cursor) == '{}') {
      //   self.cursorPos = cursor - 1
      // }
      // if (self.cursorPos > cursor) {
      //   return (self.variablePopover.show = false)
      // }
      // if (self.cursorPos !== -1) {
      //   setTimeout(function () {
      //     self.variablePopover = {
      //       show: true,
      //       rect: rect,
      //       cursorPos: cursor,
      //       searchVal: value.substring(self.cursorPos).replace(/}+/g, ''),
      //       style: styleObj,
      //     }
      //     self.$nextTick(() => {
      //       self.$refs.entityAuxiliaryPopover.inputFocus()
      //     })
      //   }, 0)
      // }
      setTimeout(function () {
        self.variablePopover = {
          show: true,
          rect: rect,
          cursorPos: cursor,
          searchVal: '',
          style: styleObj,
        }
        self.$nextTick(() => {
          self.$refs.entityAuxiliaryPopover.inputFocus()
        })
      }, 0)
    },
    // 获取光标位置
    getCursortPosition() {
      var caretOffset = 0
      var element = this.$refs.richContentRef
      var doc = element.ownerDocument || element.document
      var win = doc.defaultView || doc.parentWindow
      var sel
      if (typeof win.getSelection != 'undefined') {
        //谷歌、火狐
        sel = win.getSelection()
        if (sel.rangeCount > 0) {
          //选中的区域
          var range = win.getSelection().getRangeAt(0)
          var preCaretRange = range.cloneRange() //克隆一个选中区域
          preCaretRange.selectNodeContents(element) //设置选中区域的节点内容为当前节点
          preCaretRange.setEnd(range.endContainer, range.endOffset) //重置选中区域的结束位置
          caretOffset = preCaretRange.toString().length
        }
      } else if ((sel = doc.selection) && sel.type != 'Control') {
        //IE
        var textRange = sel.createRange()
        var preCaretTextRange = doc.body.createTextRange()
        preCaretTextRange.moveToElementText(element)
        preCaretTextRange.setEndPoint('EndToEnd', textRange)
        caretOffset = preCaretTextRange.text.length
      }
      return caretOffset
    },
    setSlot(item) {
      if (!item) return (this.cursorPos = -1)
      let regL = /{+/g
      let regR = /}+/g
      let searchValLen = this.variablePopover.searchVal
        ? this.variablePopover.searchVal.length
        : 0
      let val = this.value.text

      val =
        val.substring(0, this.cursorPos) +
        item +
        '}' +
        val.substring(this.cursorPos + searchValLen)
      val = val.replace(regL, '{').replace(regR, '}')

      console.log('setSlot', item)
      // this.$emit('input', val)
      // 处理插入选中的槽位字符串
      // this.$emit('onSlotInsert', val)
      this.insertSlot(item)
      this.normalizeEditor()
      // this.getNewValue(this.$refs.richContentRef)
      // let currentCursorPos = this.cursorPos + item.length + 1
      //  处理鼠标光标位置
      // let input = this.$refs.richContentRef
      // input.focus()
      this.cursorPos = -1
    },
    normalizeEditor() {
      this.$refs.richContentRef.normalize()
    },

    handleAdd(event) {
      if (this.disabled) {
        return
      }
      let node
      if (event) {
        event.preventDefault && event.preventDefault()
        node = event.target
      } else {
        node = this.$refs.richContentRef
      }
      const result = this.traverseNode(node)
      const trimText = result.text.trim()
      if (trimText !== result.text) {
        // 表示收尾有空格，这种情况下不允许添加
        return this.$message.warning('文本首尾不允许有空格')
      }
      // if (/(<([^>]+)>)/i.test(result.text)) {
      //   return this.$message.warning('不能包含标签和闭合标签')
      // }

      this.$emit('onAdd', result)
    },

    handleEdit(event) {
      if (this.disabled) {
        return
      }
      let node
      if (event) {
        event.preventDefault && event.preventDefault()
        node = event.target
      } else {
        node = this.$refs.richContentRef
      }
      const result = this.traverseNode(node)
      const trimText = result.text.trim()
      if (trimText !== result.text) {
        // 表示收尾有空格，这种情况下不允许添加
        return this.$message.warning('文本首尾不允许有空格')
      }
      // if (/(<([^>]+)>)/i.test(result.text)) {
      //   return this.$message.warning('不能包含标签和闭合标签')
      // }
      this.$emit('onEdit', result, this.editIndex)
    },

    onEnterKeyUp(event) {
      if (this.edit) {
        event.target.blur()
      } else {
        this.handleAdd(event)
      }
    },
    doAdd() {
      this.handleAdd()
    },
    handleUp() {
      if (!this.variablePopover.show) return
      this.$refs.entityAuxiliaryPopover.handleUp()
    },
    handleDown() {
      if (!this.variablePopover.show) return
      this.$refs.entityAuxiliaryPopover.handleDown()
    },
    onCopy(e) {
      // console.log('on copy e', e.clipboardData.getData('Text'))
      e.preventDefault()

      let fragment = window.getSelection().getRangeAt(0).cloneContents()
      console.log('fragment', fragment)
      console.log('fragment.children', fragment.children)
      let canCopy = true
      Array.from(fragment.children).forEach((dom) => {
        if (dom.className !== 'whole') {
          canCopy = false
        }
      })
      if (!canCopy) {
        this.$message.warning('复制内容须包含完整标签')
        return false
      } else {
        let testDom = document.createElement('div')
        testDom.appendChild(fragment)
        console.log(testDom.innerHTML)
        let copyObj = {
          content: testDom.innerHTML,
        }
        e.clipboardData.setData('Text', JSON.stringify(copyObj))
      }
    },
    onPaste(e) {
      e.preventDefault()
      activeIndex = Number(e.target.getAttribute('editindex') || -1)
      let range = window.getSelection().getRangeAt(0)
      let txt = e.clipboardData.getData('Text')

      // if (window.clipboardContents) {
      //   window.getSelection().deleteFromDocument()
      //   let fragment = document.createDocumentFragment()
      //   fragment.appendChild(window.clipboardContents)
      //   range.insertNode(fragment)
      //   range.collapse(false)
      //   this.normalizeEditor()
      //   this.$parent.onChange(activeIndex)
      // } else
      try {
        let textObj = JSON.parse(txt)
        if (textObj.content) {
          txt = textObj.content
        }
        let divDom = document.createElement('div')
        divDom.innerHTML = txt
        window.getSelection().deleteFromDocument()
        let fragment = document.createDocumentFragment()
        Array.from(divDom.childNodes).forEach((node) => {
          fragment.appendChild(node)
        })
        range.insertNode(fragment)
        range.collapse(false)
        this.normalizeEditor()
        this.$nextTick(() => {
          this.bindLabelEvents()
        })
        this.$parent.onChange(activeIndex)
      } catch (err) {
        console.log(err)
        if (txt) {
          window.getSelection().deleteFromDocument()
          let txtNode = document.createTextNode(txt)
          range.insertNode(txtNode)
          range.collapse(false)
          this.normalizeEditor()
          this.$parent.onChange(activeIndex)
        }
      }

      return true
    },
    onBlur(e) {
      console.log('onBlur changed')
      if (this.edit) {
        if (this.variablePopover.show) {
          return
        }
        if (!this.value.changed) {
          return
        }
        if (this.saveOnBlur) {
          this.handleEdit(e)
        }
      } else {
      }
    },
    // onPaste(e) {
    //   e.preventDefault()
    //   const txt = e.clipboardData.getData('Text')
    //   if (txt) {
    //     if (EDITORRANGE) {
    //       this.restoreSelection(EDITORRANGE)
    //     }
    //     let range = EDITORRANGE
    //     window.getSelection().deleteFromDocument()
    //     let txtNode = document.createTextNode(txt)
    //     range.insertNode(txtNode)
    //     range.collapse(false)
    //     this.normalizeEditor()
    //   }
    //   return true
    // },
  },
  components: {
    EntityAuxiliaryPopover,
    [Switch.name]: Switch,
  },
}
</script>
<style lang="scss" scoped>
p {
  margin-bottom: 0;
}
.rich-content-container {
  .rich-content {
    &:focus {
      border: 1px solid #8bc1f4;
      border-radius: 3px;
    }
  }
}
.rich-content-container {
  display: inline-block;
  display: flex;
  align-items: center;
  position: relative;
  :deep(.highlight) {
    // border-bottom: 1px solid red;
    padding-bottom: 4px;
  }
  :deep(.label) {
    width: 20px;
    height: 20px;
    margin-bottom: 4px;
    margin-left: 4px;
  }
  :deep(.whole) {
    position: relative;
    background: #edf5ff;
    padding: 0px 20px 0 8px;
    border: 1px solid $primary;
    border-radius: 4px;
    display: inline-block;
    margin: 1px;
  }
  :deep(.delete) {
    position: absolute;
    z-index: 1;
    top: 50%;
    transform: translateY(-50%);
    right: 1px;
    display: inline-block;
    cursor: pointer;
    font-style: normal;
    color: $primary;
    font-size: 18px;
  }
}
.button-save {
  position: absolute;
  right: -50px;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
  i {
    display: inline-block;
    font-size: 20px;
    color: $primary;
    // width: 50px;
    // height: 50px;
  }
}

.total-wrap {
  width: 100%;
}
.input-container {
  width: calc(100% - 40px);
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.style-container {
  min-width: 136px;
  margin-left: 5px;
}
// .content-container {
//   width: calc(100% - 136px);
// }

.rich-content {
  white-space: normal;
  overflow: hidden;
  line-height: 30px;
  display: inline-block;
  padding: 5px;
  width: 100%;
}
// .rich-content[contenteditable] {
//   white-space: nowrap;
// }
.rich-content[contenteditable]:empty:before {
  content: attr(placeholder);
  color: #cccccc;
  cursor: text;
}
.rich-content[contenteditable]:focus {
  content: none;
  // border: 1px solid #8bc1f4;
  // border-radius: 3px;
}
</style>
<style lang="scss">
.relative {
  position: relative;
}
.input-container {
  width: 100%;
  display: flex;
  .el-input__inner {
    padding: 0;
  }
}
.content-container {
  textarea {
    border: none;
    padding-left: 0;
    padding-right: 0;
  }
}
</style>
