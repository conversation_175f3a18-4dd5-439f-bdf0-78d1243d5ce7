<template>
  <card background>
    <template #title> 基础配置 </template>
    <div>
      <recogSimple />
      <semanticSimple
        v-if="currentScene && currentScene.sos === true"
        style="margin-top: 16px"
      />
      <roleSimple
        v-if="currentScene && currentScene.sos === true"
        style="margin-top: 16px"
      />
    </div>
  </card>
</template>
<script>
import card from '../components/card'
import recogSimple from './recog'
import semanticSimple from './semanticModelSimple.vue'
import roleSimple from './roleSimple'
import { mapGetters } from 'vuex'

export default {
  data() {
    return {}
  },

  computed: {
    ...mapGetters({
      currentScene: 'aiuiApp/currentScene',
      appInfo: 'aiuiApp/app',
    }),
    appId() {
      return this.$route.params.appId
    },
  },

  components: {
    card,
    recogSimple,
    semanticSimple,
    roleSimple,
  },
}
</script>
<style lang="scss" scoped></style>
