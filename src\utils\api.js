import Axios from 'axios'
// import { utils } from '@U'
import { api } from '@/config'
import Store from '../store'

Axios.defaults.validateStatus = function (status) {
  return status >= 200 && status < 500 // 默认
}
function userInfo() {
  return new Promise((resolve, reject) => {
    let baseUrl, service
    if (Store && Store.state) {
      baseUrl = Store.state.user.baseUrl
      service = Store.state.user.subAccount
        ? api.SUB_USER_ACCOUNT_INFO
        : api.USER_AUTH_USERINFO
    } else {
      baseUrl = '/aiui/web'
      service = api.USER_AUTH_USERINFO
    }
    Axios.get(`${baseUrl}${service}`)
      .then(function (response) {
        if (response.headers['x-csrf-token']) {
          localStorage.setItem(
            'AIUI_GLOBAL_VARIABLE',
            response.headers['x-csrf-token']
          )
        }
        resolve(response)
      })
      .catch(function (error) {
        resolve(error)
      })
  })
}

export default {
  userInfo,
}
