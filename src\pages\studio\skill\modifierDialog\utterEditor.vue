<template>
  <div class="modifier-utterance-editor-area"
    @mousemove="moveMark" @mousedown="clearSelection" 
    @mouseup="handleBrowserSelect">
    <i class="utterance-area-template ic-brace" />
    <div
      contenteditable="true"
      id="content"
      ref="content"
      @dblclick="handleBrowserSelect"
      @blur="handleBlur"
      @keydown="handleKeyDown"
      @keyup="angleBracketHandle"
      @paste="handlePaste"
    >
      <template v-if="utterance.mark && utterance.mark.length > 0">
        <template
          v-for="(mark, index) in utterance.mark"
        >
          <span
            v-if="mark.slotName"
            class="selection"
            :style="utteranceColor[mark.slotName]"
            :key="index"
            >{{mark.text ? mark.text : '{'+mark.slotName+'}'}}</span
          ><span contenteditable="false" class="text-blue" 
            v-else-if="mark.modifierName" :key="index">{{'<' + mark.modifierName +'>'}}</span
          ><span
            v-if="mark.symbol === '(' || mark.symbol === ')' || mark.symbol === '[' || mark.symbol === ']' || mark.symbol === '|'"
            :data-markid="mark.markId"
            class="text-blod"
            :class="{
              'mglr2': mark.symbol === '|',
              'mgl3mgr1': mark.symbol === '(' || mark.symbol === '[',
              'mgl1mgr3': mark.symbol === ')' || mark.symbol === ']',
              'text-red': mark.symbol === '(' || mark.symbol === ')',
              'text-grey': mark.symbol === '[' || mark.symbol === ']'
            }"
            :key="index"
          >{{ mark.text }}</span
          ><template v-else>{{ mark.text }}</template>
        </template>
      </template>
      <span
        v-else
      >
        {{utterance.fragment}}
      </span>
    </div>
    <i class="utterance-area-del" @click="del"><i class="ic-r-delete"/></i>
    <os-modifier-select ref="modifierSelectPopover" :variablePopover="modifierSelectPopover"
      @setModifier="setModifier" />
  </div>
</template>

<script>

export default {
  name: 'OsUtterance',
  props: {
    defaultUtterance: {
      type: Object,
      default: {}
    },
    defaultIndex: {
      type: Number,
      default: 0
    },
    businessId: String,
    modifierId: [Number, String]
  },
  data () {
    return {
      utterance: {},
      contentDom: '',
      range: '',
      rangeText: '',
      rect: '',

      edited: '',
      parentRectX: '', // 整个区域的x
      editRectX: '', // 被移入选中的x
      editRectW: '', // 被移入选中的w
      droping: '', // 抓住
      clickTimer: null, //单击timer
      cursorPos: -1,
      modifierSelectPopover: {
        show: false,
        rect: null
      }
    }
  },
  computed: {
    utteranceColor () {
      return this.$store.state.studioSkill.utteranceColor
    }
  },
  watch: {
    'defaultUtterance': function (val, oldVal) {
      this.utterance = JSON.parse(JSON.stringify(val))
    },
    'utterance.mark': function (val, oldVal) {
      let self = this
      let editNode = ''
      val.forEach((item, index) => {
        if (item.edit) {
          this.edited = index
          editNode = index
        }
      })
      this.contentDom.childNodes.forEach((node, index) => {
        if (node.dataset && parseInt(node.dataset.index) === editNode) {
          this.$nextTick(() => {
            self.editRectX = node.getBoundingClientRect().x
            self.editRectW = node.getBoundingClientRect().width
            self.resizing = false
          })
        }
      })
    }
  },
  created () {
    let self = this
    this.utterance = JSON.parse(JSON.stringify(this.defaultUtterance))
    if (this.utterance && this.utterance.mark) {
      this.utterance.mark.forEach(function (item, index) {
        if (item.slotName) {
          self.$store.dispatch('studioSkill/setUtteranceColor', item.slotName)
        }
      })
    }
  },
  mounted () {
    this.contentDom = this.$refs['content']
  },
  methods: {
    clearSelection (event) {
      if (document.selection) {
        document.selection.empty()
      } else if (window.getSelection()) {
        window.getSelection().removeAllRanges()
      }
    },
    //双击选词
    handleBrowserSelect (event) {
      let self = this
      // 鼠标松开，松手
      this.droping = ''
      this.rangeText = ''
      if (document.selection) {
        // ie浏览器
        this.range = document.selection.createRange()
        this.rangeText = this.range.text
        if (!this.rangeText) {
          this.range = ''
        }
      } else if (window.getSelection().anchorNode) {
        // 标准浏览器
        this.range = window.getSelection().getRangeAt(0)
        this.rangeText = window.getSelection().toString()
        if (!this.rangeText) {
          this.range = ''
        }
      }
      if (!this.range) {
        return
      }
      this.rect = this.range.getClientRects()[0]
    },
    resizeMark (markTag, to) {
      let mark = this.utterance.mark
      let editedLeft = this.edited - 1 // 左边文本index
      let leftText = '' // 左边文本
      let leftSpliceText = '' // 左边割出的文本
      let editedRight = this.edited + 1 // 右边文本index
      let rightText = '' // 右边文本
      let rightSpliceText = '' // 右边割出的文本
      let editedText = '' // 当前文本
      let editedSpliceText = '' // 当前割出的文本
      switch (to) {
        // 光标向左
        case 'left':
          if (markTag === 'one' && this.edited > 0) {
            if (mark[editedLeft].slotType >= 0) {
              // 如果左边是有type的，不可左移
              return
            } else {
              leftText = mark[editedLeft].text
              if (!leftText) {
                return
              }
              leftSpliceText = leftText[leftText.length - 1]
              leftText = leftText.substring(0, leftText.length - 1)
            }
            mark[this.edited].text = leftSpliceText + mark[this.edited].text
            mark[editedLeft].text = leftText
          } else if (markTag === 'two') {
            if (mark[this.edited].text.length === 0) {
              // 已经没有可选择文本了，不可左移
              return
            } else {
              editedText = mark[this.edited].text
              editedSpliceText = editedText[editedText.length - 1]
              editedText = editedText.substring(0, editedText.length - 1)
            }
            mark[this.edited].text = editedText
            if (editedRight <= mark.length - 1 && !mark[editedRight].slotType) {
              mark[editedRight].text = editedSpliceText + mark[editedRight].text
            } else {
              mark.splice(editedRight, 0, {
                text: editedSpliceText
              })
            }
          }
          break
        case 'right':
          if (markTag === 'one' && this.edited < mark.length - 1) {
            if (mark[this.edited].text.length === 0) {
              // 右边已经没有可选择文本了，不可右移
              return
            } else {
              editedText = mark[this.edited].text
              editedSpliceText = editedText[0]
              editedText = editedText.substring(1, editedText.length)
            }
            mark[this.edited].text = editedText

            if (editedLeft >= 0 && !(mark[editedLeft].slotType >= 0)) {
              mark[editedLeft].text = mark[editedLeft].text + editedSpliceText
            } else {
              mark.splice(this.edited, 0, {
                text: editedSpliceText
              })
            }
          } else if (markTag === 'two' && this.edited < mark.length - 1) {
            if (mark[editedRight].slotType >= 0) {
              // 如果右边是有type的，不可右移
              return
            } else {
              rightText = mark[editedRight].text
              if (!rightText) {
                return
              }
              rightSpliceText = rightText[0]
              rightText = rightText.substring(1, rightText.length)
            }
            mark[this.edited].text = mark[this.edited].text + rightSpliceText
            mark[editedRight].text = rightText
          }
          break
        default:
          break
      }
      this.utterance.mark = []
      this.utterance.mark = mark
    },
    moveMark (event) {
      if (this.droping && !this.resizing) {
        let dist = 0
        switch (this.droping) {
          case 'one':
            dist = event.clientX - this.editRectX
            break
          case 'two':
            dist = event.clientX - (this.editRectX + this.editRectW)
            break
          default:
            break
        }
        if (dist >= 8) {
          this.resizing = true
          this.resizeMark(this.droping, 'right')
        } else if (dist <= -8) {
          this.resizing = true
          this.resizeMark(this.droping, 'left')
        }
      }
    },
    // 删除语料
    del () {
      this.$emit('del', this.defaultUtterance, this.defaultIndex)
      this.$emit('reloadModifier')
    },
    handleKeyDown (event) {
      var keycode = window.event ? event.keyCode : event.which;
      var evt = event || window.event;
      if (keycode == 13 && !(evt.ctrlKey)) {
        // 发送消息的代码
        event.target.blur()
        event.preventDefault();
        return false;
      }
    },
    handlePaste (e) {
      e.preventDefault();
      var text;
      var clp = (e.originalEvent || e).clipboardData;
      if (clp === undefined || clp === null) {
        text = window.clipboardData.getData("text") || "";
        if (text !== "") {
          if (window.getSelection) {
            var newNode = document.createElement("span");
            newNode.innerHTML = text;
            window.getSelection().getRangeAt(0).insertNode(newNode);
          } else {
            document.selection.createRange().pasteHTML(text);
          }
        }
      } else {
        text = clp.getData('text/plain') || "";
        if (text !== "") {
          document.execCommand('insertText', false, text);
        }
      }
    },
    // 失去焦点
    regFun(val){
      return this.$utils.trimSpace(val).trim()
    },
    handleBlur () {
      let self = this
      if(this.cursorPos > -1) return
      let markNodes = this.$refs.content.childNodes
      let marks = []
      let utterance = ''
      let showUtterance = ''
      for (let i = 0; i < markNodes.length; i++) {
        if (markNodes[i].dataset) {
          utterance += this.regFun(markNodes[i].textContent).trim()
          showUtterance += markNodes[i].textContent
        } else if (this.regFun(markNodes[i].nodeValue).trim()) {
          utterance += this.regFun(markNodes[i].nodeValue).trim()
          showUtterance += markNodes[i].nodeValue
        }
      }

      if (showUtterance.trim() === this.utterance.fragment) {
        return
      }

      if (!utterance.trim()) {
        return self.$message.warning('修饰语内容不能为空')
      }
      let nameValid = this.$rules.judgeUtteranceParams(utterance.trim(), 100, '内容', 3)
      if (!nameValid.valid) {
        return self.$message.warning(nameValid.data.message)
      }
      let api = this.$config.api.STUDIO_MODIFIER_FRAGMENT_ADD_OR_EDIT
      let data = {
        id: this.utterance.id,
        businessId: this.businessId,
        modifierId: this.modifierId,
        fragment: utterance.trim()
      }
      this.$utils.httpPost(api, data, {
        success: (res) => {
          self.$emit('change')
          self.$emit('reloadModifier')
        },
        error: (err) => {
          console.log('page=>>');
          console.log(err);
        }
      })
    },
    // 自定义修饰语
    angleBracketHandle(e){
      let self = this
      let rightAngleBracket = 190, 
        angleCode= [188, 190]
      if(e.keyCode === 38 || e.keyCode === 40) return  //键盘上下方向键
      if(!angleCode.includes(e.keyCode)) return
      if(e.keyCode === rightAngleBracket && self.cursorPos !== -1) {
        return self.modifierSelectPopover.show = false
      }
      const cursor = self.$utils.getCursortPosition(self.$refs['content'])
      const value = self.$refs['content'].textContent
      if (value[cursor - 1] === '<'){
        self.cursorPos = cursor
      } else {
        return
      }
      const rect = window
        .getSelection()
        .getRangeAt(0)
        .getBoundingClientRect()
      if(self.cursorPos > cursor) {
        self.modifierSelectPopover.show = false
      }
      setTimeout(function() {
        self.modifierSelectPopover= {
          show: true,
          rect: rect,
          searchVal: value.substring(self.cursorPos, cursor),
          type: 'usedInModifier',
          businessId: self.businessId,
          modifierId: self.modifierId
        }
      }, 0)
    },
    setModifier(item, e){
      let self = this
      let utter = self.$refs['content'].textContent
      if(!item) return this.cursorPos = -1
      let searchValLen = this.modifierSelectPopover.searchVal 
        ? this.modifierSelectPopover.searchVal.length : 0
      utter = utter.substring(0, this.cursorPos) + item + '>'
        + utter.substring(this.cursorPos + searchValLen)
      let currentCursorPos = this.cursorPos + item.length + 1
      self.$refs['content'].textContent = utter
      this.cursorPos = -1
      this.handleBlur()
    },
  }
}
</script>

<style lang="scss">
.modifier-utterance-editor-area {
  padding: 11px 27px 11px 11px;
  height: auto;
  min-height: 44px;
  background-color: #fff;
  z-index: 1;
  display: flex;
  align-items: center;
  &-template {
    color: $grey3;
    margin-right: 20px;
  }
  &-del {
    line-height: 22px;
    display: none;
    cursor: pointer;
    color: $grey4;
    position: absolute;
    right: 8px;
  }
  &:hover {
    .utterance-area-del {
      display: block;
    }
  }
  #content {
    max-width: 98%;
    outline: none;
    position: relative;
    display: inline-block;
    font-size: 14px;
    span {
      font-size: 14px;
    }
  }
}
</style>
