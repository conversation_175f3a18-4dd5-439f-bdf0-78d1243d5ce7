<template>
  <el-dialog
    title="配置应用需要的技能"
    :visible.sync="dialog.show"
    width="1000px"
    top="5vh"
    :show-close="false"
    @closed="closeSkillDialog"
  >
    <div class="skill_header">
      <el-input
        size="small"
        class="search-area"
        placeholder="搜索"
        v-model.trim="searchVal"
        @keyup.enter.native="searchAppConfig"
        style="width: 258px"
      >
        <i
          @click.stop.prevent="searchAppConfig"
          slot="suffix"
          class="el-input__icon el-icon-search search-area-btn"
        />
      </el-input>
      <el-button
        icon="ic-r-plus"
        plain
        size="medium"
        @click="jump"
        style="margin-left: 10px"
        >&nbsp;创建技能</el-button
      >
    </div>
    <div class="config-content">
      <div class="skill_container" v-if="dialog.show">
        <store-skill
          :currentScene="currentScene"
          :appId="appId"
          :skillConfig="skillConfig"
          :sourceConfig="sourceConfig"
          :searchVal="searchVal"
          @change="emitChange"
          ref="storeSkill"
        >
          <custom
            :currentScene="currentScene"
            :appId="appId"
            :skillConfig="skillConfig"
            :qaConfig="qaConfig"
            :docConfig="docConfig"
            :ubotQaConfig="ubotQaConfig"
            :skillThresholdConfig="skillThresholdConfig"
            :qaThresholdConfig="qaThresholdConfig"
            :globalThresholdChange="thresholdChange"
            :searchVal="searchVal"
            :clickSearchVal="clickSearchVal"
            :sourceConfig="sourceConfig"
            :skillLoading="skillLoading"
            @change="emitChange"
            ref="custom"
          ></custom>
        </store-skill>
        <p
          class="empty_skill_tips"
          v-if="
            clickSearchVal !== '' &&
            !(
              $refs.storeSkill.addSkillList.length +
              $refs.custom.addSkillList.length
            )
          "
        >
          暂无搜索结果
        </p>
      </div>
    </div>

    <div slot="footer" class="dialog_footer">
      <div class="dialog_footer_left"></div>
      <div class="dialog_footer_right">
        <el-button size="small" @click="dialog.show = false">取消</el-button>
        <el-button
          size="small"
          type="primary"
          :disabled="!changeState"
          @click="saveChangeData"
          :loading="saveLoading"
        >
          保存配置
        </el-button>
      </div>
    </div>
  </el-dialog>
</template>
<script>
import StoreSkill from './semanticSkill/storeSkill.vue'
import Custom from './semanticSkill/custom.vue'

export default {
  props: {
    dialog: Object,
    appId: '',
    currentScene: Object,
  },
  data() {
    return {
      saveLoading: false,

      clickSearchVal: '',
      searchVal: '',

      loading: false,

      switchHasChange: false,

      changeState: false,

      skillLoading: false,

      threshold: 0.82,
      thresholdChange: false,

      skillConfig: {},
      qaConfig: {},
      docConfig: {},
      ubotQaConfig: {}, // 关键词问答变更数据结构
      skillThresholdConfig: {},
      qaThresholdConfig: {},
      sourceConfig: {},
    }
  },
  methods: {
    // 关闭弹窗
    closeSkillDialog() {
      this.changeState = false
      // 搜索框清空
      this.clickSearchVal = ''
      this.searchVal = ''
    },
    emitChange() {
      this.changeState = true
    },
    jump() {
      window.open('/studio/skill', '_blank')
    },
    searchAppConfig() {
      this.clickSearchVal = this.searchVal
      // 商店技能
      this.$refs.storeSkill.filterList()
      // 用户自定义
      this.$refs.custom.filterList()
    },

    getAppConfig() {
      let that = this
      this.skillLoading = true
      this.$utils.httpGet(
        this.$config.api.AIUI_APP_BUSINESS_CAN_USE,
        {
          appid: this.appId,
          sceneId: this.currentScene.sceneBoxId,
          type: 0,
          search: '',
          point:
            this.currentScene.point.split(',').indexOf('13') >= 0 ? '13' : '2',
        },
        {
          success: (res) => {
            if (this.$refs.storeSkill) {
              that.$refs.storeSkill.getList(res)
            }
            if (this.$refs.custom) {
              that.$refs.custom.getList(res)
            }
            that.skillLoading = false
          },
          error: (res) => {
            that.skillLoading = false
          },
        }
      )
    },

    saveChangeData() {
      // TODO: 保存配置数据
      this.saveLoading = true
      this.saveChangeSemanticSkill()
      this.changeState = false
    },

    globalThresholdChange() {
      this.thresholdChange = true
      this.threshold = parseFloat(this.threshold.toFixed(2))
      this.skillThresholdConfig = {}
      this.qaThresholdConfig = {}
    },
    // 保存技能卡片弹窗数据
    saveChangeSemanticSkill() {
      let self = this
      let activeName
      let charater = {} //人设
      let data = {
        appid: this.appId,
        sceneId: this.currentScene.sceneBoxId,
        sceneName: this.currentScene.sceneBoxName,
      }
      let flag = false // 记录上面data数据是否变化
      if (this.thresholdChange) {
        data.threshold = this.threshold
        flag = true
      }
      if (JSON.stringify(this.skillConfig) !== '{}') {
        let arr = []
        for (let i in this.skillConfig) {
          let tmp = {
            skillId: this.skillConfig[i].id,
            name: this.skillConfig[i].name,
            operation: this.skillConfig[i].operation,
          }
          if (this.skillConfig[i].hasOwnProperty('mallId')) {
            tmp.mallId = this.skillConfig[i].mallId
          }
          arr.push(tmp)
        }
        data.skillConfig = JSON.stringify(arr)
        flag = true
      }
      if (JSON.stringify(this.qaConfig) !== '{}') {
        if (
          activeName === 5 ||
          this.qaConfig[Object.keys(this.qaConfig)[0]].skillListType ===
            'deviceRepositoryList'
        ) {
          // 设备人设
          for (let i in this.qaConfig) {
            charater.repositoryId = this.qaConfig[i].repositoryId
            charater.operation = this.qaConfig[i].operation
            charater.type = 3
          }
          let arr = []
          arr.push(charater)
          data.qaConfig = JSON.stringify(arr)
        } else {
          // 自定义问答
          let arr = []
          for (let i in this.qaConfig) {
            let tmp = {
              repositoryId: this.qaConfig[i].repositoryId,
              operation: this.qaConfig[i].operation,
              type: this.qaConfig[i].type,
            }
            if (this.qaConfig[i].verId) {
              tmp.verId = this.qaConfig[i].verId
            }
            arr.push(tmp)
          }

          data.qaConfig = JSON.stringify(arr)
        }
        flag = true
      }
      if (JSON.stringify(this.skillThresholdConfig) !== '{}') {
        data.skillThresholdConfig = JSON.stringify(
          this.objectToArray(this.skillThresholdConfig)
        )
        flag = true
      }
      if (JSON.stringify(this.qaThresholdConfig) !== '{}') {
        data.qaThresholdConfig = JSON.stringify(
          this.objectToArray(this.qaThresholdConfig)
        )
        flag = true
      }
      if (JSON.stringify(this.sourceConfig) !== '{}') {
        data.sourceConfig = JSON.stringify(
          this.objectToArray(this.sourceConfig)
        )
        flag = true
      }
      if (JSON.stringify(this.ubotQaConfig) !== '{}') {
        //关键词问答
        let arr = []
        for (let i in this.ubotQaConfig) {
          let tmp = {
            qaId: this.ubotQaConfig[i].qaId,
            operation: this.ubotQaConfig[i].operation,
          }
          if (this.ubotQaConfig[i].verId) {
            tmp.verId = this.ubotQaConfig[i].verId
          }
          arr.push(tmp)
        }
        data.ubotQaConfig = JSON.stringify(arr)
        flag = true
      }
      // 改写成promise

      let allPromises = []
      if (flag) {
        allPromises.push({ type: 'skill', fn: this.saveChangeSemantic4(data) })
      }

      // 保存有问必答
      this.$refs.custom.saveMustAnswer()

      // 保存文档问答
      if (JSON.stringify(this.docConfig) !== '{}') {
        // /app/pluginStudio/saveConfig
        let arr = []
        for (let i in this.docConfig) {
          let tmp = {
            repoId: this.docConfig[i].repoId,
            operation: this.docConfig[i].operation,
          }
          arr.push(tmp)
        }
        let param = {
          appid: this.appId,
          // sceneId: this.currentScene.sceneBoxId,
          chainId: this.currentScene.chainId || 'cbm_v45',
          sceneName: this.currentScene.sceneBoxName,
          knowledgeConfig: JSON.stringify(arr),
        }

        allPromises.push({ type: 'doc', fn: this.saveChangeKnowl45(param) })
      }

      // 发起所有promise请求
      Promise.all(allPromises.map((it) => it.fn)).then((ret) => {
        self.skillConfig = {}
        self.qaConfig = {}
        self.skillThresholdConfig = {}
        self.qaThresholdConfig = {}
        self.sourceConfig = {}
        self.ubotQaConfig = {}
        self.docConfig = {}
        self.$emit('saveSuccess')

        self.saveLoading = false
        self.dialog.show = false
      })
    },

    objectToArray(obj) {
      let arr = []
      for (let i in obj) {
        arr.push(obj[i])
      }
      return arr
    },

    // save 技能卡片的promise
    saveChangeSemantic4(data) {
      let self = this

      return new Promise((resolve, reject) => {
        this.$utils.httpPost(this.$config.api.AIUI_APP_SAVE_SEMANTIC, data, {
          success: (res) => {
            if (res.flag) {
              resolve()
            } else {
              // self.$emit('saveFail')
              reject()
            }
          },
          error: (res) => {
            // self.$emit('saveFail')
            reject()
          },
        })
      })
    },

    // 4.5 保存 知识库卡片
    saveChangeKnowl45(param) {
      return new Promise((resolve, reject) => {
        this.$utils.httpPost(
          this.$config.api.AIUI_APP_PLUGINSTUDIO_SAVECONFIG,
          param,
          {
            success: (res) => {
              // self.qaConfig = {}
              // self.updateDocConfig()
              resolve()
            },
          }
        )
      })
    },
  },

  watch: {
    'dialog.show'(val) {
      if (val) {
        this.getAppConfig()
      }
    },
  },
  components: { StoreSkill, Custom },
}
</script>
<style lang="scss" scoped>
.skill_header {
  position: absolute;
  top: 80px;
  left: 165px;
}

.el-tabs {
  margin-left: 20px;
}

.empty_skill_tips {
  text-align: center;
  position: absolute;
  top: 50%;
  left: 57%;
  transform: translate(-50%, -50%);
}
:deep(.el-dialog) {
  .el-dialog__header {
    border-bottom: 1px solid #e1e1e1;
  }
  .el-dialog__body {
    padding-top: 0;
    padding: 0px;
  }
  .el-dialog__footer {
    padding: 0 !important;
    .dialog_footer {
      display: flex;
      justify-content: space-between;

      .dialog_footer_left {
        width: 150px;
        padding: 14px 32px 14px 0;
        border-right: 1px solid #e1e1e1;
      }
      .dialog_footer_right {
        flex: 1;
        padding: 14px 32px 14px 0;
      }
    }
  }
}
</style>
