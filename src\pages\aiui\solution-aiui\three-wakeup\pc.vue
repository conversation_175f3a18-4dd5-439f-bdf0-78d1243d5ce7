<template>
  <div class="main-content">
    <banner
      :src="'solution/three-wakeup/img_Low_power_consumption_banner.png'"
      @jump="toConsole"
    >
      <template v-slot:title> 低功耗三级唤醒解决方案 </template
      ><template>
        为手机、手表等私人智能硬件定制设计，<br />
        唤醒效果好、功耗低、支持本人声纹验证；
        <br />保障设备续航并解决个人数据隐私问题。
      </template></banner
    >
    <section class="section section-1">
      <div class="section-title">
        <i class="arrow arrow-left"></i
        ><span class="section-title-bold">应用场景</span
        ><i class="arrow arrow-right"></i>
      </div>
      <div class="section-desc" style="text-align: center">
        适用于对电池续航有要求或个人隐私敏感的智能硬件
      </div>
      <ul class="product-list">
        <li
          v-for="(item, index) in productList"
          :key="index"
          :class="item.klass"
        >
          <!-- <h1>{{ item.name }}</h1> -->
          <div class="desc-wrap">
            <h2>{{ item.name }}</h2>
            <p>
              {{ item.desc }}
            </p>
          </div>
          <!-- <div class="overlay"></div> -->
        </li>
      </ul>
    </section>
    <section class="section section-7">
      <div class="section-title">
        <i class="arrow arrow-left"></i
        ><span class="section-title-bold">方案介绍</span
        ><i class="arrow arrow-right"></i>
      </div>
      <div class="section-item">
        <ul>
          <li class="image-1"></li>
          <li class="image-arrow"></li>
          <li class="cell">
            <div class="head-image image-dsp"></div>
            <div class="cell-desc">
              <p>DSP层唤醒</p>
              <p>解决待机时功耗问题</p>
            </div>
          </li>
          <li class="image-arrow"></li>
          <li class="cell">
            <div class="head-image image-ap"></div>
            <div class="cell-desc">
              <p>AP层唤醒</p>
              <p>保证最好的唤醒效果</p>
            </div>
          </li>
          <li class="image-arrow"></li>
          <li class="cell">
            <div class="head-image image-dsp"></div>
            <div class="cell-desc">
              <p>声纹验证</p>
              <p>最大可能保证用户隐私安全</p>
            </div>
          </li>
          <li class="image-arrow"></li>
          <li class="image-2"></li>
        </ul>
      </div>
    </section>
    <section class="section section-3">
      <div>
        <div class="section-title">
          <i class="arrow arrow-left"></i
          ><span class="section-title-bold">方案优势</span
          ><i class="arrow arrow-right"></i>
        </div>
        <ul class="advantage">
          <li>
            <div>
              <div class="advantage-text" style="padding-left: 218px">
                <p>功耗低</p>
                <ul>
                  <li>DSP层低功耗降噪+唤醒</li>
                  <li>每小时功耗低于3mAh</li>
                  <li>解决待机时功耗问题</li>
                </ul>
              </div>
              <div class="advantage-image"></div>
            </div>
          </li>
          <li>
            <div>
              <div class="advantage-image"></div>
              <div class="advantage-text">
                <p>唤醒效果好</p>
                <ul>
                  <li>环境误唤醒48小时一次</li>
                  <li>声纹冒认率小于2%</li>
                  <li>使用大模型兜底，保证最好的唤醒效果</li>
                </ul>
              </div>
            </div>
          </li>
          <li>
            <div>
              <div class="advantage-text">
                <p>个性化定制、保障隐私安全</p>
                <ul>
                  <li>支持声纹注册验证，</li>
                  <li>确保只能本人唤醒，满足个性化要求，</li>
                  <li>最大可能保证用户隐私安全</li>
                </ul>
              </div>
              <div class="advantage-image"></div>
            </div>
          </li>
        </ul>
      </div>
    </section>
    <corp @jump="toConsole">
      <template> 提交信息，我们会尽快与您联系</template>
    </corp>
  </div>
</template>

<script>
import banner from '@P/aiui/solution-aiui/components/banner.vue'
import corp from '@P/aiui/solution-aiui/components/corp.vue'

export default {
  layout: 'aiuiHome',
  data() {
    return {
      productList: [
        {
          name: '手机',
          desc: '',
          klass: 'img_domestic_cellphone',
        },
        {
          name: '智能手表',
          desc: '',
          klass: 'img_smartwatch',
        },
        {
          name: '智能手环',
          desc: '',
          klass: 'img_instrument_smar_bracelet',
        },
        {
          name: '运动相机',
          desc: '',
          klass: 'img_sports_camera',
        },
        {
          name: '智能头盔',
          desc: '',
          klass: 'img_smart_helmet',
        },
      ],

      access: [
        {
          title: '纯软接入、无需改造硬件',
          title2:
            '提供SDK,可以直接端集成；说法量身定制，端<br />上适配即可使用',
          img: require('@A/images/solution/wakeup/img_pure_soft.png'),
        },
        {
          title: '麦克风阵列+声学算法',
          title2:
            '针对没有录音装置的设备；提供2/4/6麦USB<br />语音模组和CAE算法；帮你解决硬件收音问题',
          img: require('@A/images/solution/wakeup/img_microphone_array.png'),
        },
      ],
    }
  },
  methods: {
    toConsole() {
      // 判断是否有百度SEM营销参数，有的话，提交表单页需要带上
      const search = window.location.search
      if (search) {
        window.open(`/solution/apply/17${search}`)
      } else {
        window.open('/solution/apply/17')
      }
    },
  },
  components: {
    banner,
    corp,
  },
}
</script>

<style lang="scss" scoped>
.main-content {
  .section {
    p,
    ul {
      margin-bottom: 0;
    }
    max-width: 1200px;
    overflow: hidden;
    margin: 0 auto;
    .section-title {
      text-align: center;
      font-size: 34px;
      font-family: SourceHanSansSC-Medium, SourceHanSansSC;
      font-weight: bold;
      color: #333;
      line-height: 34px;
      position: relative;
      width: 200px;
      margin: 0 auto;
      .arrow {
        width: 160px;
        height: 8px;
        display: inline-block;
        background-repeat: no-repeat;
        background-size: contain;
        position: absolute;
        z-index: 1;
      }
      .arrow-left {
        background-position: left;
        background-image: url(~@A/images/solution/offline/img_title_01.png);
        top: 50%;
        left: -160px;
      }
      .arrow-right {
        background-position: right;
        background-image: url(~@A/images/solution/offline/img_title_02.png);
        top: 50%;
        right: -160px;
      }
      .arrow-left1 {
        background-position: left;
        background-image: url(~@A/images/solution/wakeup/img_access_title_01.png);
        top: 50%;
        left: -160px;
      }
      .arrow-right1 {
        background-position: right;
        background-image: url(~@A/images/solution/wakeup/img_access_title_02.png);
        top: 50%;
        right: -160px;
      }
    }
    .section-desc {
      text-align: left;
      margin-top: 40px;
      font-size: 16px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #999999;
    }
    .section-title-bold {
      font-size: 34px;
      font-family: Microsoft YaHei;
      font-weight: bold;
      color: #333333;
    }
    .section-title-spec {
      font-size: 30px;
      font-weight: 500;
      color: #656565;
      line-height: 42px;
    }
    .section-tabs {
      margin-top: 70px;
      ul {
        display: flex;
        justify-content: center;
      }
      li {
        font-size: 20px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #666;
        line-height: 30px;
        position: relative;
        cursor: pointer;
        &:hover {
          color: #1f90fe;
        }
        &.active {
          color: #1f90fe;
          &::before {
            position: absolute;
            content: ' ';
            z-index: 1;
            width: 180px;
            height: 4px;
            background: #1f90fe;
            border-radius: 2px;
            bottom: -32px;
            left: -48px;
          }
          &::after {
            position: absolute;
            content: ' ';
            display: inline-block;
            width: 0;
            height: 0;
            bottom: -100px;
            left: 50%;
            transform: translateX(-50%);
            border: 46px solid;
            border-color: transparent transparent #f4f7f9;
          }
        }
      }
      li + li {
        margin-left: 165px;
      }
    }
    .section-sub-title {
      text-align: center;
      font-size: 18px;
      font-weight: 400;
      color: #777777;
      line-height: 25px;
      margin-top: 42px;
    }
    .section-sub-title-spec {
      font-size: 17px;
      font-weight: 400;
      color: #999999;
      line-height: 28px;
      margin-top: 18px;
    }

    .product-list {
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 50px auto 0;

      li {
        position: relative;
        text-align: center;
        width: 230px;
        height: 328px;

        .desc-wrap {
          padding-top: 39px;
        }
        .overlay {
          display: none;
          width: 100%;
          height: 100%;
          // background: rgba(0, 0, 0, 0.3);
          background-image: linear-gradient(
            0deg,
            rgb(0, 54, 255) 0%,
            rgb(39, 12, 73) 100%
          );
          opacity: 0.502;
          position: absolute;
          top: 0;
          left: 0;
          z-index: 1;
        }

        h1 {
          display: none;
          text-align: left;
          max-width: 25px;
          font-size: 24px;
          font-family: Microsoft YaHei;
          font-weight: 400;
          color: #ffffff;
          line-height: 30px;
          margin: 0 auto;
          position: relative;
          top: 50%;
          transform: translateY(-50%);
        }
        h2 {
          // display: none;
          text-align: center;
          // padding-top: 178px;
          // padding-left: 35px;
          font-size: 21px;
          // font-weight: bold;
          color: #ffffff;
          line-height: 21px;
        }
        p {
          // display: none;
          margin-top: 32px;
          width: 232px;
          font-size: 16px;
          font-weight: 400;
          color: #ffffff;
          line-height: 32px;
          padding-left: 35px;
          text-align: left;
        }

        &.img_domestic_cellphone {
          background: url(~@A/images/solution/three-wakeup/img_domestic_cellphone.png)
            center/100% no-repeat;
        }
        &.img_smartwatch {
          background: url(~@A/images/solution/three-wakeup/img_smartwatch.png)
            center/100% no-repeat;
        }
        &.img_instrument_smar_bracelet {
          background: url(~@A/images/solution/three-wakeup/img_instrument_smar_bracelet.png)
            center/100% no-repeat;
        }
        &.img_sports_camera {
          background: url(~@A/images/solution/three-wakeup/img_sports_camera.png)
            center/100% no-repeat;
        }
        &.img_smart_helmet {
          background: url(~@A/images/solution/three-wakeup/img_smart_helmet.png)
            center/100% no-repeat;
        }
      }

      li + li {
        margin-left: 16px;
      }
    }
  }

  .section-1 {
    margin-top: 110px;
  }
  .section-2 {
    .section-title-2 {
      font-size: 18px;
      color: #8c8c8c;
      text-align: center;
      margin-top: 15px;
    }
    .section-item {
      > ul {
        margin-bottom: 80px;
        li {
          width: 150px;
          img {
            width: 100%;
          }
          .app-text {
            position: static;
            text-align: center;
            font-size: 18px;
            color: #202020;
            margin-top: 15px;
            transform: none;
          }
        }
      }
    }
  }
  .section-3 {
    max-width: unset;
    padding: 110px 0 110px 0;
    > div {
      margin: 0 auto;
    }

    p {
      margin-bottom: 0;
    }
    .advantage {
      margin-top: 84px;
      > li {
        > div {
          max-width: 1200px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin: 0 auto;
        }
      }
      > li:not(:first-child) {
        margin-top: 40px;
      }

      > li:nth-child(1) {
        .advantage-image {
          width: 580px;
          height: 326px;
          background: url(~@A/images/solution/three-wakeup/img_low_power_dissipation.png)
            center/100% no-repeat;
        }
        .advantage-text {
        }
      }
      > li:nth-child(2) {
        background: #f4f7f9;
        padding: 40px 0;
        .advantage-image {
          width: 580px;
          height: 326px;
          background: url(~@A/images/solution/three-wakeup/img_wake_effect.png)
            center/100% no-repeat;
        }
        .advantage-text {
        }
      }
      > li:nth-child(3) {
        .advantage-image {
          width: 580px;
          height: 326px;
          background: url(~@A/images/solution/three-wakeup/img_autobiography.png)
            center/100% no-repeat;
        }
        .advantage-text {
        }
      }
    }
    .advantage-text {
      width: 411px;
      p {
        font-size: 34px;
        font-weight: 400;
        color: #666;
        line-height: 34px;
      }

      ul {
        margin-top: 45px;
        li {
          font-size: 16px;
          font-weight: 400;
          color: #999999;
          line-height: 30px;
          white-space: nowrap;
        }
      }
    }
  }

  .section-7 {
    width: 100%;
    max-width: 100%;
    margin-top: 100px;
    // background: #262626;
    background: url(~@A/images/solution/three-wakeup/img_plan_introduce.png)
      center/cover no-repeat;
    padding: 110px 0 109px 0;
    .section-title {
      color: #fff;
    }
    .section-item {
      margin-top: 70px;
      .title-desc {
        position: absolute;
        z-index: 1;
        left: 128px;
        top: 40px;
        h1,
        h2 {
          color: #fff;
          text-align: left;
        }
        h1 {
          font-size: 30px;
        }
        h2 {
          font-size: 16px;
          margin-top: 26px;
        }
      }

      > ul {
        display: flex;
        align-items: center;
        justify-content: center;
        li {
          position: relative;
        }
        li + li {
          margin-left: 1px;
        }
        .image-1 {
          width: 168px;
          height: 133px;
          background: url(~@A/images/solution/three-wakeup/img_voice_intercom.png);
        }
        .image-2 {
          width: 167px;
          height: 133px;
          background: url(~@A/images/solution/three-wakeup/img_mobile_phone_wake.png);
        }
        .image-arrow {
          width: 35px;
          height: 31px;
          background: url(~@A/images/solution/three-wakeup/img_arrow.png);
        }
      }

      .cell {
        width: 238px;
        .head-image {
          width: 100%;
          height: 176px;
        }
        .image-dsp {
          background: url(~@A/images/solution/three-wakeup/img_dsp.png)
            center/100% no-repeat;
        }
        .image-ap {
          background: url(~@A/images/solution/three-wakeup/img_ap.png)
            center/100% no-repeat;
        }
        .image-voice {
          background: url(~@A/images/solution/three-wakeup/img_voiceprint_authentication.png)
            center/100% no-repeat;
        }
        .cell-desc {
          padding: 20px;
          background: #fff;
          p {
            margin-bottom: 0;
            &:first-child {
              font-size: 16px;
              font-family: Microsoft YaHei;
              font-weight: bold;
              color: #333333;
              line-height: 16px;
            }
            &:last-child {
              font-size: 14px;
              font-family: Microsoft YaHei;
              color: #999;
              line-height: 14px;
              margin-top: 9px;
            }
          }
        }
      }
    }
  }
}
</style>
