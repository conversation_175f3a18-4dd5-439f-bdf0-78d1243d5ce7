<template>
  <el-dialog
    title="配置应用所需的智能体"
    :visible.sync="dialog.show"
    width="1000px"
    top="5vh"
    @closed="closeAgentDialog"
  >
    <div class="skill_header">
      <el-input
        size="small"
        class="search-area"
        placeholder="搜索"
        v-model.trim="searchVal"
        @focus="searchFocus"
        @keyup.enter.native="searchAgentConfig"
        style="width: 258px"
      >
        <i
          @click.stop.prevent="searchAgentConfig"
          slot="suffix"
          class="el-input__icon el-icon-search search-area-btn"
        />
      </el-input>

      <el-button
        icon="ic-r-plus"
        plain
        size="medium"
        @click="jump"
        style="margin-left: 10px"
        >&nbsp;创建智能体</el-button
      >
    </div>

    <div v-if="dialog.show">
      <agent-list
        :agentData="agentData"
        :agentDataCopy="agentDataCopy"
        :loading="loading"
        @selectchange="onSelectchange"
      ></agent-list>
    </div>

    <div slot="footer" class="dialog_footer">
      <div class="dialog_footer_left"></div>
      <div class="dialog_footer_right">
        <el-button size="small" @click="dialog.show = false">取消</el-button>
        <el-button
          size="small"
          type="primary"
          @click="saveChangeData"
          :loading="saveLoading"
          :disabled="!switchHasChange"
        >
          保存配置
        </el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { mapGetters } from 'vuex'
import agentList from './agentList.vue'
export default {
  name: 'AiuiWebSparkAgentDialog',
  components: { agentList },

  watch: {
    'dialog.show'(val) {
      if (val) {
        this.getAppAgentConfig()
      }
    },
  },

  props: {
    dialog: Object,
    appId: '',
    currentScene: Object,
  },

  data() {
    return {
      searchVal: '',

      changeState: false,
      saveLoading: false,
      switchHasChange: false,

      loading: false,

      agentData: [],
      agentDataCopy: [],
      originAgentData: [],
    }
  },
  computed: {
    ...mapGetters({
      skillIconBgColors: 'studioSkill/skillIconBgColors',
    }),
  },
  mounted() {
    // this.getAppAgentConfig()
  },

  methods: {
    closeAgentDialog() {
      this.changeState = false
      // 搜索框清空
      this.clickSearchVal = ''
      this.searchVal = ''
    },

    searchFocus() {},

    searchAgentConfig() {
      this.clickSearchVal = this.searchVal
      this.agentData = this.agentDataCopy.filter((it) =>
        it.pluginName.includes(this.clickSearchVal)
      )
    },

    onSelectchange(agent, val) {
      console.log('onSelectchange的agent和val和official', agent, val)
      let pluginId = agent.pluginId
      let official = agent.official
      this.agentData = this.agentData.map((item) => {
        if (item.pluginId === pluginId) {
          return {
            ...item,
            selected: val,
          }
        } else {
          return { ...item }
        }
      })
      this.agentDataCopy = this.agentDataCopy.map((item) => {
        if (item.pluginId === pluginId) {
          return {
            ...item,
            selected: val,
          }
        } else {
          return { ...item }
        }
      })
      this.switchHasChange = true
    },

    saveChangeData() {
      let that = this
      let addPlugins = []
      let delPlugins = []
      let updatePlugins = []

      this.agentData.forEach((f) => {
        const originF = this.originAgentData.find((it) => it.id === f.id)
        if (f.selected && !originF.selected) {
          addPlugins.push({
            pluginId: f.pluginId,
            pluginName: f.name,
            classifyType: 2,
            domain: '',
            intents: f.intents,
          })
        }
        if (!f.selected && originF.selected) {
          delPlugins.push({
            configId: f.configId,
            pluginId: f.pluginId,
            pluginName: f.name,
            classifyType: 2,
            domain: '',
            intents: f.intents,
          })
        }
        if (f.selected && originF.selected && f.changed) {
          updatePlugins.push({
            configId: f.configId,
            pluginId: f.pluginId,
            pluginName: f.name,
            classifyType: 2,
            domain: '',
            intents: f.intents,
          })
        }
      })

      if (
        addPlugins.length === 0 &&
        delPlugins.length === 0 &&
        updatePlugins.length === 0
      ) {
        return this.$message.warning('无配置更新')
      }

      let params = {
        botId: this.currentScene.botBoxId,
        addPlugins,
        delPlugins,
        updatePlugins,
      }

      this.saveLoading = true

      this.$utils.httpPost(
        this.$config.api.AIUI_BOT_CONFIG_SAVE_AgentPluginConfig,
        JSON.stringify(params),
        {
          config: {
            headers: {
              'Content-Type': 'application/json',
            },
          },
          success: (res) => {
            that.saveLoading = false
            that.switchHasChange = false
            that.$emit('saveSuccess')
            that.$message.success('保存成功')
            that.dialog.show = false
          },
          error: (err) => {
            console.log('err', err)
            that.saveLoading = false
            that.$message.error(err.desc)
          },
        }
      )
    },

    getAppAgentConfig() {
      let that = this
      this.loading = true
      this.$utils.httpGet(
        this.$config.api.AIUI_BOT_CONFIG_GET_BotAgentPlugins,
        {
          botId: this.currentScene.botBoxId,
        },
        {
          success: (res) => {
            that.loading = false

            const agentArr = JSON.parse(JSON.stringify(res.data || []))
            const colors = this.skillIconBgColors
            agentArr.forEach((item, index) => {
              item.color = colors[index % colors.length]
            })

            that.originAgentData = agentArr
            that.agentDataCopy = agentArr
            that.agentData =
              (agentArr || []).map((item) => {
                return {
                  ...item,
                  isShow: item.selected,
                }
              }) || []
          },
          error: (res) => {},
        }
      )
    },
    jump() {
      console.log('jump')
      window.open('/studio/agent', '_blank')
    },
  },
}
</script>

<style lang="scss" scoped>
.skill_header {
  position: absolute;
  top: 80px;
  left: 165px;
}
.tab-container {
  display: flex;
  position: relative;
  &::before {
    position: absolute;
    content: ' ';
    width: 100%;
    height: 1px;
    background: #e7e9ed;
    bottom: 0;
  }
}

.skill-type {
  margin-top: 1%;
  margin-bottom: 1%;
}
.add-skill-tab {
  a {
    display: inline-block;
    width: 108px;
    height: 40px;
    line-height: 40px;
    cursor: pointer;
    color: unset;
    text-align: center;
  }
  .active {
    position: relative;
    color: $primary;
    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      display: inline-block;
      width: 88px;
      height: 2px;
      background-color: #1f90fe;
      border-radius: 2px;
      transform: translateX(-50%);
    }
  }
}

.el-tabs {
  margin-left: 20px;
}

:deep(.el-dialog) {
  .el-dialog__header {
    border-bottom: 1px solid #e1e1e1;
  }
  .el-dialog__body {
    padding-top: 0;
    padding: 0px;
  }
  .el-dialog__footer {
    padding: 0 !important;
    .dialog_footer {
      display: flex;
      justify-content: space-between;

      .dialog_footer_left {
        width: 150px;
        padding: 14px 32px 14px 0;
        border-right: 1px solid #e1e1e1;
      }
      .dialog_footer_right {
        flex: 1;
        padding: 14px 32px 14px 0;
      }
    }
  }
}
</style>
