<template>
  <div
    class="notice-tip"
    :style="{ display: messages.length > 0 ? 'inline-flex' : 'block' }"
  >
    <template v-if="messages.length > 0">
      <div class="notice-title">
        <i class="circle"></i>
        <span class="title">技能公告<span class="plus">+</span></span>
      </div>
      <div class="icon-wrap"><i class="icon icon-new"></i></div>
      <div class="notice-desc" style="height: 100%">
        <div class="carousel-wrap" style="height: 100%">
          <el-carousel
            height="40px"
            direction="vertical"
            :autoplay="true"
            indicator-position="none"
          >
            <el-carousel-item v-for="(item, index) in messages" :key="index">
              <span @click="goMessage" class="message" :title="item.name">
                {{ item.name }}
              </span>
            </el-carousel-item>
          </el-carousel>
        </div>
      </div>
    </template>
  </div>
</template>
<script>
export default {
  data() {
    return {
      messages: [
        // { name: '111111' }, { name: '测试的大幅度发到付的发' }
      ],
    }
  },
  created() {
    this.getMessages()
  },
  methods: {
    getMessages() {
      let self = this
      this.$utils.httpGet(
        this.$config.api.AIUI_STORE_SKILL_NOTICES,
        {},
        {
          success: (res) => {
            self.messages = res.data.message || []
          },
          error: (err) => {},
        }
      )
    },
    goMessage() {
      let routeUrl = this.$router.resolve({
        name: 'message',
      })
      window.open(routeUrl.href, '_blank')
    },
  },
}
</script>
<style lang="scss" scoped>
.icon-wrap {
  line-height: 1;
}
.icon {
  display: inline-block;
  margin-left: 10px;
  background-repeat: no-repeat;
  background-size: 100%;
}
.icon-new {
  width: 31px;
  height: 25px;
  background-image: url(~@A/images/aiui/main-page/icon_new.png);
}
.notice-tip {
  padding-left: 232px;
  height: 36px;
  line-height: 36px;
  background: #f9f9f9;
  display: inline-flex;
  align-items: center;
  width: 100%;

  .notice-title {
    position: relative;
    .circle {
      width: 8px;
      height: 8px;
      border: 2px solid #1ed3ff;
      border-radius: 50%;
      display: inline-block;
    }
    .title {
      font-size: 20px;
      font-family: Microsoft YaHei;
      font-weight: bold;
      color: #1f90fe;
      position: relative;
    }
    .plus {
      position: absolute;
      z-index: 1;
      top: 0px;
      right: -6px;
      color: #1ed3ff;
      font-size: 12px;
      display: inline-block;
      line-height: 0;
    }
  }
  .notice-desc {
    margin-left: 10px;
    height: 17px;
    font-family: Microsoft YaHei;
    font-weight: bold;
    color: #999999;
    display: inline-flex;
    align-items: center;
    width: calc(100% - 150px);
  }
  .carousel-wrap {
    width: calc(100% - 150px);
  }
}
.message {
  cursor: pointer;
  font-size: 16px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: inline-block;
  max-width: calc(100% - 150px);
}
// 技能商店适配不同屏幕
@media screen and (max-height: 801px) {
  .notice-tip {
    padding-left: 156px;
    height: 32px;
    line-height: 32px;
    .notice-title {
      .circle {
        width: 5px;
        height: 5px;
      }
      .title {
        font-size: 13px;
      }
      .plus {
        position: absolute;
        z-index: 1;
        top: -2px;
        right: -4px;
        color: #1ed3ff;
        font-size: 12px;
        display: inline-block;
        line-height: 0;
      }
    }
    .notice-desc {
      margin-left: 10px;
      font-size: 12px;
    }
  }
  .message {
    font-size: 12px;
  }
}
</style>
