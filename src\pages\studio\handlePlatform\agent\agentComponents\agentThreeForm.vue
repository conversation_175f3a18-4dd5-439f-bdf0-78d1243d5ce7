<template>
  <div class="third_agent_detail">
    <div class="agent_config_wrap">
      <div class="config_header">
        <div class="config_header_left">
          <span class="sub-title">三方智能体配置</span>
          <el-tooltip content="使用公告" placement="top">
            <i
              class="iconfont icon-notice custom_icon"
              @click="announcementDialog.show = true"
            ></i>
          </el-tooltip>
        </div>
        <div class="config_header_right">
          <!-- <el-button type="primary" size="mini" @click="doDebugging"
            >调试</el-button
          > -->
          <el-button
            plain
            size="mini"
            v-show="isEditing"
            :loading="saving"
            @click="handleSave"
            >保存</el-button
          >
          <el-button
            type="text"
            size="mini"
            v-show="isEditing"
            @click="handleCancel"
            >取消</el-button
          >
          <el-button
            type="text"
            size="mini"
            v-show="!isEditing"
            @click="toggleCollapse"
            >{{ isCollapsed ? '展开' : '收起' }}</el-button
          >
          <el-button
            type="text"
            size="mini"
            style="margin-left: 0px"
            v-show="!isEditing"
            @click="handleEdit"
            >编辑</el-button
          >
          <!-- <el-dropdown v-show="!isEditing">
            <span class="el-dropdown-link">
              <i class="el-icon-more"></i>
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="clear">清空</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown> -->
        </div>
      </div>
      <div class="config_main">
        <el-form
          class="baseInfo_form"
          ref="baseInfoForm"
          :rules="rules"
          :inline="false"
          :model="schemaForm"
          label-position="top"
        >
          <el-row :gutter="15">
            <el-col :span="4">
              <el-form-item label="平台" prop="platform">
                <el-select v-model="schemaForm.platform" :disabled="!isEditing">
                  <el-option
                    v-for="item in platformOptions"
                    :key="item.code"
                    :value="item.code"
                    :label="item.name"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="4">
              <el-form-item label="智能体类型" prop="resourceType">
                <el-select
                  v-model="schemaForm.resourceType"
                  :disabled="!isEditing"
                >
                  <el-option
                    v-for="item in resourceTypeOptions"
                    :key="item.resourceCode"
                    :value="item.resourceCode"
                    :label="item.resourceName"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="4">
              <el-form-item label="请求方式" prop="method">
                <el-select
                  v-model="schemaForm.method"
                  placeholder="请选择"
                  :disabled="true || !isEditing"
                >
                  <el-option label="GET" value="GET"></el-option>
                  <el-option label="POST" value="POST"></el-option>
                  <el-option label="PUT" value="PUT"></el-option>
                  <el-option label="DELETE" value="DELETE"></el-option>
                  <el-option label="PATCH" value="PATCH"></el-option>
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="API链接" prop="endPoint">
                <el-input
                  placeholder="请输入智能体API"
                  v-model="schemaForm.endPoint"
                  :disabled="true || !isEditing"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>

        <el-collapse class="extend_entitie_collapse" v-model="activeNames">
          <el-collapse-item name="1">
            <el-scrollbar>
              <el-form
                ref="schemaForm"
                :model="schemaForm"
                label-width="130px"
                size="medium"
                :hide-required-asterisk="true"
                v-loading="baseInfoLoading"
              >
                <div class="item_title" style="margin-top: 15px">请求参数</div>
                <div class="param_header">
                  <el-row style="margin-bottom: 15px">
                    <el-col :span="4">参数名称</el-col>
                    <el-col :span="2" style="padding-left: 1px"
                      >参数类型</el-col
                    >
                    <el-col :span="2" style="padding-left: 2px"
                      >传入方法</el-col
                    >
                    <el-col :span="7" style="padding-left: 3px"
                      >参数描述</el-col
                    >
                    <el-col :span="3" style="padding-left: 10px">默认值</el-col>
                    <el-col :span="2" style="padding-left: 7px"
                      >是否必填</el-col
                    >
                    <el-col :span="2" style="padding-left: 10px">开启</el-col>
                    <el-col :span="2" style="padding-left: 10px">操作</el-col>
                  </el-row>
                </div>
                <div
                  v-for="(item, index) in schemaForm.inputForm"
                  :key="item.id"
                >
                  <form-row
                    :ref="`row-${index}`"
                    :form-data="item"
                    :path="['inputForm', index]"
                    area="toolRequestInput"
                    @change="
                      (newData) => handleItemChange('input', index, newData)
                    "
                    @remove-child="() => handleRemoveItem('input', index)"
                    @edit-array="(data) => handleEditArray('input', data)"
                    :nesting-level="0"
                    :disabled="
                      !isEditing ||
                      (isEditing && schemaForm.resourceType === 'bot') ||
                      (isEditing && !item.isUserAdded)
                    "
                    :only-default-value-enabled="
                      isEditing &&
                      (schemaForm.resourceType === 'bot' ||
                        schemaForm.resourceType === 'workflow')
                    "
                    :all-names="allInputNames"
                  />
                </div>

                <div
                  v-show="!hideInputAddButton"
                  class="add_btn_wrap"
                  ref="inputAddBtnWrap"
                >
                  <el-button
                    plain
                    icon="el-icon-plus"
                    @click="addRootItem('input')"
                    class="add-root-btn"
                  >
                    新增参数
                  </el-button>
                </div>

                <!-- <div v-if="showOutputParams">
                  <div class="item_title">输出参数</div>

                  <div class="param_header">
                    <el-row>
                      <el-col :span="7" class="text-left">参数名称</el-col>
                      <el-col :span="3" style="padding-left: 2px"
                        >参数类型</el-col
                      >
                      <el-col :span="10" style="padding-left: 4px"
                        >参数描述</el-col
                      >
                      <el-col :span="2" style="padding-left: 10px">开启</el-col>
                      <el-col :span="2" style="padding-left: 10px">操作</el-col>
                    </el-row>
                  </div>

                  <div
                    v-for="(item, index) in schemaForm.outputForm"
                    :key="item.id"
                  >
                    <form-row
                      :ref="`row-${index}`"
                      :form-data="item"
                      :path="['outputForm', index]"
                      area="toolRequestOutput"
                      @change="
                        (newData) => handleItemChange('output', index, newData)
                      "
                      @remove-child="() => handleRemoveItem('output', index)"
                      @edit-array="(data) => handleEditArray('output', data)"
                      :nesting-level="0"
                      :disabled="
                        !isEditing ||
                        (isEditing && schemaForm.resourceType === 'bot') ||
                        (isEditing && !item.isUserAdded)
                      "
                      :only-default-value-enabled="
                        isEditing &&
                        (schemaForm.resourceType === 'bot' ||
                          schemaForm.resourceType === 'workflow')
                      "
                      :all-names="allOutputNames"
                    />
                  </div>

                  <div
                    v-show="!isFormDisabled"
                    class="add_btn_wrap"
                    ref="outputAddBtnWrap"
                  >
                    <el-button
                      plain
                      icon="el-icon-plus"
                      @click="addRootItem('output')"
                      class="add-root-btn"
                    >
                      新增参数
                    </el-button>
                  </div>
                </div> -->
              </el-form>
            </el-scrollbar>
          </el-collapse-item>
        </el-collapse>
      </div>
    </div>

    <div class="card_table">
      <div class="card-wrap">
        <div class="sub-title">意图</div>
        <div class="card-btns">
          <el-button @click="quoteIntent">引用官方意图</el-button>
          <el-button @click="addIntent"
            ><i class="el-icon-plus" style="margin-right: 5px"></i
            >创建意图</el-button
          >
        </div>
      </div>
      <el-scrollbar v-loading="tableData.loading">
        <div v-if="tableData.list.length > 0" class="intent_list">
          <IntentItem
            v-for="item in tableData.list"
            :key="item.intentId"
            :intent-data="item"
            @command="handleDropdownItem"
          />
        </div>
        <EmptyTip emptyText="尚无意图，请添加" v-else />
      </el-scrollbar>
      <el-pagination
        v-if="tableData.list.length > 0"
        ref="pagination"
        :current-page="tableData.page"
        :page-size="tableData.size"
        :total="tableData.total"
        :layout="pageLayout"
        @current-change="pageChange"
        class="txt-al-c"
      ></el-pagination>
    </div>

    <IntentDialog ref="IntentDialog" @refresh="refresh" />
    <IntentLibDialog ref="IntentLibDialog" @refresh="refresh" />
    <array-modal
      v-model="arrayModalVisible"
      :array-data="currentArrayData"
      @submit="handleArraySave"
    />
    <debug-modal
      ref="DebugModal"
      :input-params="schemaForm.inputForm"
      :openApiForm="schemaForm"
      :authApiType="authApiType"
      :authApiData="authApiData"
    />
    <usage-announcement-dialog :dialog="announcementDialog" />
  </div>
</template>

<script>
import IntentDialog from '../intentDialog.vue'
import IntentLibDialog from '../intentLibDialog.vue'
import IntentItem from './intentItem.vue'
import FormRow from '../../source/components/formRow.vue'
import ArrayModal from '../../source/components/arrayModal.vue'
import DebugModal from '../../source/components/debugModal.vue'
import { v4 as uuidv4 } from 'uuid'
import EmptyTip from '@/pages/studio/role/empty.vue'
import UsageAnnouncementDialog from '@C/usageAnnouncementDialog.vue'

export default {
  name: 'AgentThreeForm',
  components: {
    IntentDialog,
    IntentLibDialog,
    IntentItem,
    FormRow,
    ArrayModal,
    DebugModal,
    EmptyTip,
    UsageAnnouncementDialog,
  },
  data() {
    return {
      announcementDialog: {
        show: false,
      },
      authApiType: -1,
      authApiData: '',
      activeNames: ['1'],
      isCollapsed: false,
      isEditing: false,
      originalEndPoint: null,
      schemaForm: {
        inputForm: [],
        outputForm: [],
        method: 'POST',
        platform: null,
        resourceType: null,
        endPoint: null,
      },

      arrayModalVisible: false,
      currentArrayData: null,
      currentArrayIndex: null,
      currentArea: null,
      baseInfoLoading: false,
      agentId: null,
      overflowItems: {}, // 用于存储哪些item需要显示tooltip
      platformOptions: [],
      isFormDataInitialized: false, // 添加标志位，用于标记表单数据是否已经初始化
      saving: false,
      rules: {
        platform: [
          { required: true, message: '请选择平台', trigger: 'change' },
        ],
        resourceType: [
          { required: true, message: '请选择类型', trigger: 'change' },
        ],
        method: [
          { required: true, message: '请选择请求方式', trigger: 'change' },
        ],
        endPoint: [
          { required: true, message: '智能体API不可为空', trigger: 'blur' },
          {
            pattern:
              /^(http|https):\/\/[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+(:[0-9]{1,5})?(\/[-a-zA-Z0-9%_\.~#]*)*(\?[a-zA-Z0-9%_\.~#=-]*)?(#[-a-zA-Z0-9_]*)?$/,
            message: 'API链接必须是合法的HTTP/HTTPS URL格式',
            trigger: 'blur',
          },
        ],
      },
      tableData: {
        loading: false,
        total: 0,
        page: 1,
        size: 10,
        list: [],
      },
    }
  },
  computed: {
    pageLayout() {
      return this.tableData.total > 10
        ? 'prev, pager, next, jumper, total'
        : 'prev, pager, next'
    },
    selectedPlatformObj() {
      return (
        this.platformOptions.find(
          (item) => item.code === this.schemaForm.platform
        ) || {}
      )
    },
    resourceTypeOptions() {
      // 对selectedPlatform.resourceList进行去重，去除resourceName重复的数据
      if (this.selectedPlatformObj && this.selectedPlatformObj.resourceList) {
        // 使用Set来存储已经见过的resourceName
        const seenResourceNames = new Set()
        // 使用filter方法过滤出resourceName唯一的元素
        const uniqueResourceList = this.selectedPlatformObj.resourceList.filter(
          (item) => {
            if (!seenResourceNames.has(item.resourceName)) {
              seenResourceNames.add(item.resourceName)
              return true
            }
            return false
          }
        )

        return uniqueResourceList
      }
      return []
    },
    showOutputParams() {
      return this.schemaForm.resourceType !== 'bot'
    },
    isFormDisabled() {
      return (
        !this.isEditing ||
        (this.isEditing && this.schemaForm.resourceType === 'bot')
      )
    },

    // 判断是否隐藏添加输入参数按钮
    // 当resourceType为workflow且输入参数数量已达到3个时隐藏
    hideInputAddButton() {
      return (
        this.isFormDisabled ||
        (this.schemaForm.resourceType === 'workflow' &&
          this.schemaForm.inputForm.length >= 3)
      )
    },

    // 获取所有输入参数名称（用于检查重名，不包括当前正在编辑的项）
    allInputNames() {
      // 返回一个函数，接收当前正在编辑的项的ID和父节点路径作为参数
      return (currentItemId, parentPath = null) => {
        try {
          console.log('allInputNames 被调用:', currentItemId, parentPath)

          // 如果没有提供父路径，则检查顶层参数
          if (!parentPath) {
            const names = this.schemaForm.inputForm
              .filter((item) => item.id !== currentItemId) // 排除当前正在编辑的项
              .map((item) => item.name)
            console.log('顶层参数名称:', names)
            return names
          }

          // 如果提供了父路径，则查找该父节点下的所有子参数
          const parent = this.findItemByPath(null, parentPath)
          console.log('找到的父节点:', parent)

          if (parent && Array.isArray(parent)) {
            // 如果返回的是数组（例如children数组）
            const names = parent
              .filter((item) => item.id !== currentItemId)
              .map((item) => item.name)
            console.log('子参数名称(数组):', names)
            return names
          } else if (parent && parent.children) {
            // 如果返回的是对象，且有children属性
            const names = parent.children
              .filter((item) => item.id !== currentItemId)
              .map((item) => item.name)
            console.log('子参数名称(对象.children):', names)
            return names
          }

          console.log('没有找到子参数')
          return []
        } catch (error) {
          console.error('获取输入参数名称出错:', error)
          return []
        }
      }
    },
    // 获取所有输出参数名称（用于检查重名，不包括当前正在编辑的项）
    allOutputNames() {
      // 返回一个函数，接收当前正在编辑的项的ID和父节点路径作为参数
      return (currentItemId, parentPath = null) => {
        try {
          console.log('allOutputNames 被调用:', currentItemId, parentPath)

          // 如果没有提供父路径，则检查顶层参数
          if (!parentPath) {
            const names = this.schemaForm.outputForm
              .filter((item) => item.id !== currentItemId) // 排除当前正在编辑的项
              .map((item) => item.name)
            console.log('顶层参数名称:', names)
            return names
          }

          // 如果提供了父路径，则查找该父节点下的所有子参数
          const parent = this.findItemByPath(null, parentPath)
          console.log('找到的父节点:', parent)

          if (parent && Array.isArray(parent)) {
            // 如果返回的是数组（例如children数组）
            const names = parent
              .filter((item) => item.id !== currentItemId)
              .map((item) => item.name)
            console.log('子参数名称(数组):', names)
            return names
          } else if (parent && parent.children) {
            // 如果返回的是对象，且有children属性
            const names = parent.children
              .filter((item) => item.id !== currentItemId)
              .map((item) => item.name)
            console.log('子参数名称(对象.children):', names)
            return names
          }

          console.log('没有找到子参数')
          return []
        } catch (error) {
          console.error('获取输出参数名称出错:', error)
          return []
        }
      }
    },
  },
  watch: {
    'schemaForm.platform': {
      handler(newVal, oldVal) {
        if (oldVal !== undefined) {
          // 只在非初始化时执行
          this.schemaForm.resourceType = null
          this.schemaForm.endPoint = null
          // 清空表单数据
          this.schemaForm.inputForm = []
          this.schemaForm.outputForm = []
          // 重置表单初始化标志位
          this.isFormDataInitialized = false
        }
      },
      immediate: true,
    },
    'schemaForm.resourceType': {
      handler(newVal) {
        // 当智能体类型变化时，获取当前选中的智能体类型对象
        const selectedPlatform = this.platformOptions.find(
          (item) => item.code === this.schemaForm.platform
        )
        if (selectedPlatform && selectedPlatform.resourceList) {
          const selectedResourceType = selectedPlatform.resourceList.find(
            (item) => item.resourceCode === newVal
          )
          if (selectedResourceType) {
            // 设置endPoint为当前选中项的resourceUrl
            this.schemaForm.endPoint = selectedResourceType.resourceUrl
            // 同时更新originalEndPoint
            this.originalEndPoint = selectedResourceType.resourceUrl
            // 设置method为当前选中项的httpMethod
            this.schemaForm.method = selectedResourceType.httpMethod

            // 保存当前platform和resourceType到Vuex，供其他组件使用
            this.$store.dispatch('studioSkill/setAgentPlatformInfo', {
              platform: this.schemaForm.platform,
              resourceType: this.schemaForm.resourceType,
              queryKey: 'query',
            })

            // 只有在未初始化过表单数据时，才进行初始化
            if (!this.isFormDataInitialized) {
              console.log('初始化表单数据')
              this.initFormData(selectedResourceType.webSchema)
            } else {
              // 如果是用户手动切换了resourceType，则需要重新初始化表单
              // 通过检查是否为编辑状态来判断是否为用户手动操作
              if (this.isEditing) {
                console.log('重新初始化表单数据')
                this.initFormData(selectedResourceType.webSchema)
              }
            }
          }
        }
      },
    },
  },
  created() {
    this.agentId = this.$route.params.agentId
    this.getIntentList(1)
    this.baseInfoLoading = true
    // 同时发起两个请求,获取基础信息
    this.getPluginInfo()
  },
  methods: {
    // 初始化表单数据（输入 输出）
    initFormData(webSchema) {
      const webSchemaObj = JSON.parse(webSchema)

      // 获取用户添加的字段名称列表（如果存在的话）
      const userAddedInputFields = webSchemaObj.userAddedFields?.input || []
      const userAddedOutputFields = webSchemaObj.userAddedFields?.output || []

      if (webSchemaObj.toolRequestInput) {
        // 为接口返回的数据项标记isUserAdded为false
        const inputFormData = JSON.parse(
          JSON.stringify(webSchemaObj.toolRequestInput)
        )
        // 为每个表单项标记用户添加标识
        inputFormData.forEach((item) => {
          // 如果字段名称在用户添加的列表中，则标记为用户添加
          item.isUserAdded = userAddedInputFields.includes(item.name)

          // 递归处理嵌套的children
          const markChildren = (obj) => {
            if (obj.children && obj.children.length > 0) {
              obj.children.forEach((child) => {
                child.isUserAdded = userAddedInputFields.includes(child.name)
                markChildren(child)
              })
            }
          }
          markChildren(item)
        })
        this.schemaForm.inputForm = inputFormData
      }
      if (webSchemaObj.toolRequestOutput) {
        // 为接口返回的数据项标记isUserAdded为false
        const outputFormData = JSON.parse(
          JSON.stringify(webSchemaObj.toolRequestOutput)
        )
        // 为每个表单项标记用户添加标识
        outputFormData.forEach((item) => {
          // 如果字段名称在用户添加的列表中，则标记为用户添加
          item.isUserAdded = userAddedOutputFields.includes(item.name)

          // 递归处理嵌套的children
          const markChildren = (obj) => {
            if (obj.children && obj.children.length > 0) {
              obj.children.forEach((child) => {
                child.isUserAdded = userAddedOutputFields.includes(child.name)
                markChildren(child)
              })
            }
          }
          markChildren(item)
        })
        this.schemaForm.outputForm = outputFormData
      }
      // 设置标志位，表示已初始化
      this.isFormDataInitialized = true
    },
    getPluginInfo() {
      Promise.all([this.getThirdPartyPromise(), this.getIntentDetailPromise()])
        .then(([thirdPartyRes, intentDetailRes]) => {
          this.baseInfoLoading = false
          if (thirdPartyRes.code == 0) {
            this.platformOptions = thirdPartyRes.data

            const defaultPlatform = this.platformOptions.find(
              (item) => item.code === 'coze'
            )
            // 如果/plugin/detail返回了thirdPartyInfo，则回显
            if (intentDetailRes.data.thirdPartyInfo) {
              console.log('回显')
              const info = intentDetailRes.data.thirdPartyInfo
              // 设置标志位，避免watch中重复初始化
              this.isFormDataInitialized = true
              this.schemaForm.platform = info.platform
              this.$nextTick(() => {
                this.schemaForm.resourceType = info.resourceType
                this.schemaForm.endPoint = info.resourceUrl
                this.schemaForm.method = info.httpMethod
                this.initFormData(info.webSchema)
              })
            } else {
              console.log('默认')
              // 设置默认值：平台为coze，智能体类型为bot
              this.schemaForm.platform = 'coze'
              // 等待platform值更新后，再设置resourceType
              this.$nextTick(() => {
                const defaultResourceType = defaultPlatform.resourceList?.find(
                  (item) => item.resourceCode === 'bot'
                )
                if (defaultResourceType) {
                  this.schemaForm.resourceType = 'bot'
                  this.schemaForm.endPoint = defaultResourceType.resourceUrl
                  this.schemaForm.method = defaultResourceType.httpMethod
                }
              })
            }
          }
        })
        .catch((error) => {
          this.baseInfoLoading = false
          this.$message.error('获取数据失败')
        })
    },
    // 将getThirdParty改造成Promise形式
    getThirdPartyPromise() {
      return new Promise((resolve, reject) => {
        this.$utils.httpGet(
          this.$config.api.AGENT_THIRD_PARTY,
          {},
          {
            success: (res) => {
              resolve(res)
            },
            error: (err) => {
              reject(err)
            },
          }
        )
      })
    },

    // 将getIntentDetail改造成Promise形式
    getIntentDetailPromise() {
      return new Promise((resolve, reject) => {
        this.$utils.httpPost(
          this.$config.api.AGENT_DETAIL,
          JSON.stringify({ pluginId: this.agentId }),
          {
            config: {
              headers: {
                'Content-Type': 'application/json;charset=UTF-8',
              },
            },
            success: (res) => {
              resolve(res)
            },
            error: (err) => {
              reject(err)
            },
          }
        )
      })
    },

    // 检查文本是否溢出
    checkOverflow(item) {
      this.$nextTick(() => {
        const el = this.$refs.cardMid[this.tableData.list.indexOf(item)]
        if (el) {
          const isOverflow = el.scrollWidth > el.clientWidth
          this.$set(this.overflowItems, item.id, isOverflow)
        }
      })
    },

    // 判断是否显示tooltip
    isOverflow(item) {
      return this.overflowItems[item.id] || false
    },
    refresh() {
      this.getIntentList()
    },
    async doDebugging() {
      try {
        // 1. 先校验主表单
        await this.$refs.schemaForm.validate()
        // 2. 校验所有动态表单项（包括嵌套的表单项）
        const validateAll = await Promise.all([
          this.validateFormItems('inputForm'),
          this.validateFormItems('outputForm'),
        ])
        if (validateAll.every((valid) => valid)) {
          // 所有校验通过，执行提交逻辑
          // this.submitForm()
          console.log(this.schemaForm, '调试的·························')
          this.$refs.DebugModal.show()
        } else {
          this.$message.error('请检查表单填写是否正确')
        }
      } catch (error) {
        this.$message.error('请检查表单填写是否正确')
      }
    },
    async validateFormItems(formName) {
      try {
        // 获取所有 form-row 组件的引用
        const formItems = this.$refs[`row-${formName}`] || []

        // 扁平化处理嵌套的 form-row
        const allFormItems = []
        const flattenFormItems = (items) => {
          items.forEach((item) => {
            allFormItems.push(item)
            if (item.$refs) {
              Object.values(item.$refs).forEach((ref) => {
                if (Array.isArray(ref)) {
                  flattenFormItems(ref)
                }
              })
            }
          })
        }

        flattenFormItems(formItems)

        // 校验每个表单项
        const results = await Promise.all(
          allFormItems.map((item) => {
            if (item.$refs && item.$refs.formItem) {
              return item.$refs.formItem.validate()
            }
            return Promise.resolve(true)
          })
        )

        return results.every((valid) => valid)
      } catch (error) {
        console.error(`${formName} 验证失败:`, error)
        return false
      }
    },
    // 保存数组编辑结果
    handleArraySave(formData) {
      if (this.currentArea === 'input') {
        // 更新输入参数的数组
        const [_, index] = this.currentArrayPath // 解构路径 ['inputForm', index]
        this.$set(this.schemaForm.inputForm, index, formData)
      } else {
        // 更新输出参数的数组
        const [_, index] = this.currentArrayPath // 解构路径 ['outForm', index]
        this.$set(this.schemaForm.outputForm, index, formData)
      }
      this.arrayModalVisible = false
    },
    handleEditArray(area, { path, data }) {
      this.currentArea = area
      this.currentArrayPath = path
      this.currentArrayData = JSON.parse(JSON.stringify(data))
      this.arrayModalVisible = true
    },
    handleRemoveItem(type, index) {
      if (type === 'input') {
        this.schemaForm.inputForm.splice(index, 1)
        // 当平台为coze且智能体类型为workflow时
        if (
          this.schemaForm.platform === 'coze' &&
          this.schemaForm.resourceType === 'workflow' &&
          index === 2
        ) {
          this.$store.dispatch('studioSkill/setAgentPlatformInfo', {
            platform: this.schemaForm.platform,
            resourceType: this.schemaForm.resourceType,
            queryKey: 'query',
          })
        }
      } else {
        this.schemaForm.outputForm.splice(index, 1)
      }
    },
    handleItemChange(type, index, newData) {
      // 当平台为coze且智能体类型为workflow时
      if (
        type === 'input' &&
        this.schemaForm.platform === 'coze' &&
        this.schemaForm.resourceType === 'workflow'
      ) {
        this.$store.dispatch('studioSkill/setAgentPlatformInfo', {
          platform: this.schemaForm.platform,
          resourceType: this.schemaForm.resourceType,
          queryKey: newData.name || 'query',
        })
      }

      if (type === 'input') {
        this.$set(this.schemaForm.inputForm, index, newData)
      } else {
        this.$set(this.schemaForm.outputForm, index, newData)
      }
    },
    getNewItemTemplate(type) {
      const base = {
        id: uuidv4(),
        name: '',
        description: '',
        type: 'string',
        open: true,
        children: [],
      }

      return type === 'input'
        ? {
            ...base,
            location: 'query',
            required: false,
            defaultValue: '',
          }
        : base
    },

    addRootItem(area) {
      const newItem = {
        id: uuidv4(),
        type: 'string',
        location: 'body',
        required: true,
        from: 2,
        open: true,
        startDisabled: false,
        nameErrMsg: '',
        descriptionErrMsg: '',
        name: '',
        description: '',
        defaultValue: '',
        isUserAdded: true, // 标记这是用户新添加的项
        // 不包含 fatherType 和 arraySon
      }
      if (area === 'input') {
        this.schemaForm.inputForm.push(newItem)
        // 添加后等待DOM更新，然后滚动到添加按钮位置
        this.$nextTick(() => {
          const inputAddBtnWrap = this.$refs.inputAddBtnWrap
          if (inputAddBtnWrap) {
            inputAddBtnWrap.scrollIntoView({ behavior: 'smooth', block: 'end' })
          }
        })
      } else {
        this.schemaForm.outputForm.push(newItem)
        // 添加后等待DOM更新，然后滚动到添加按钮位置
        this.$nextTick(() => {
          const outputAddBtnWrap = this.$refs.outputAddBtnWrap
          if (outputAddBtnWrap) {
            outputAddBtnWrap.scrollIntoView({
              behavior: 'smooth',
              block: 'end',
            })
          }
        })
      }
    },
    getIntentList(page) {
      this.tableData.loading = true
      const data = {
        pluginId: this.agentId,
        pageIndex: page || this.tableData.page,
        pageSize: this.tableData.size,
      }
      this.$utils.httpPost(
        this.$config.api.AGENT_INTENT_TABLE_LIST,
        JSON.stringify(data),
        {
          config: {
            headers: {
              'Content-Type': 'application/json;charset=UTF-8',
            },
          },
          success: (res) => {
            if (res.code == '0') {
              this.tableData.list = res.data.data
              this.tableData.total = res.data.totalSize
              this.tableData.page = res.data.pageIndex
              this.tableData.size = res.data.pageSize
              this.tableData.loading = false
            }
          },
          error: (err) => {
            this.tableData.loading = false
            this.$message.error(err?.desc)
          },
        }
      )
    },
    pageChange(e) {
      console.log('pageChange=>', e)
      this.tableData.page = e
      this.getIntentList()
    },
    quoteIntent() {
      this.$refs.IntentLibDialog.show()
    },
    addIntent() {
      this.$refs.IntentDialog.show(this.agentId)
    },
    handleDropdownItem(command, item) {
      switch (command) {
        case 'edit':
          this.toEditIntent(item)
          break
        case 'del':
          if (item.official) {
            this.toDereference(item) // 取消引用
          } else {
            this.toDelIntent(item) // 删除
          }
          break
      }
    },
    toEditIntent(item) {
      this.$refs.IntentDialog.show(this.agentId, item)
    },
    toDelIntent(item) {
      let self = this
      this.$confirm('意图删除后不可恢复，请谨慎操作。', `确定删除该意图?`, {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        confirmButtonClass: 'el-button--danger',
        type: 'warning',
        showClose: false,
      })
        .then(() => {
          const params = {
            pluginId: this.agentId,
            intentId: item.intentId,
          }
          self.delIndent(params)
        })
        .catch(() => {})
    },
    toDereference(item) {
      const params = {
        pluginId: this.agentId,
        intentId: item.intentId,
        intentVersion: item.version,
        quoteFlag: false,
      }
      this.$utils.httpPost(
        this.$config.api.AGENT_OFFICAL_INTENT_QUOTE,
        JSON.stringify(params),
        {
          config: {
            headers: {
              'Content-Type': 'application/json;charset=UTF-8',
            },
          },
          success: (res) => {
            if (res.code == '0') {
              this.$message.success('操作成功')
              this.getIntentList()
              this.$refs.IntentLibDialog.getOfficialIntentList()
            }
          },
          error: (err) => {
            this.$message.error(err?.desc)
          },
        }
      )
    },
    delIndent(params) {
      this.$utils.httpPost(
        this.$config.api.AGENT_INDENT_DELETE,
        JSON.stringify(params),
        {
          config: {
            headers: {
              'Content-Type': 'application/json;charset=UTF-8',
            },
          },
          success: (res) => {
            if (res.code == '0') {
              this.$message.success('操作成功')
              if (this.tableData.list.length === 1 && this.tableData.page > 1) {
                this.tableData.page -= 1
              }
              this.getIntentList()
            }
          },
          error: (err) => {
            this.$message.error(err.desc)
          },
        }
      )
    },
    toggleCollapse() {
      this.isCollapsed = !this.isCollapsed
      this.activeNames = this.isCollapsed ? [] : ['1']
    },
    handleEdit() {
      this.isEditing = true
      this.isCollapsed = false
      this.activeNames = ['1']
      this.originalEndPoint = this.schemaForm.endPoint
    },
    handleCancel() {
      this.isEditing = false
      this.activeNames = ['1']
      this.schemaForm.endPoint = this.originalEndPoint
      this.$refs.baseInfoForm.clearValidate()
      // 清除主表单的验证信息
      this.$refs.schemaForm.clearValidate()
      // 清除动态表单项的验证信息
      this.clearFormItemsValidate('inputForm')
      this.clearFormItemsValidate('outputForm')
    },

    // 清除动态表单项的验证信息
    clearFormItemsValidate(formName) {
      // 获取所有 form-row 组件的引用
      const formItems = this.$refs[`row-${formName}`] || []

      // 扁平化处理嵌套的 form-row
      const allFormItems = []
      const flattenFormItems = (items) => {
        items.forEach((item) => {
          allFormItems.push(item)
          if (item.$refs) {
            Object.values(item.$refs).forEach((ref) => {
              if (Array.isArray(ref)) {
                flattenFormItems(ref)
              }
            })
          }
        })
      }

      flattenFormItems(formItems)

      // 清除每个表单项的验证信息
      allFormItems.forEach((item) => {
        if (item.$refs && item.$refs.formItem) {
          item.$refs.formItem.clearValidate()
        }
      })
    },

    async handleSave() {
      this.$refs.baseInfoForm.validate(async (valid) => {
        if (valid) {
          try {
            // 1. 先校验主表单
            await this.$refs.schemaForm.validate()

            // 2. 校验所有动态表单项（包括嵌套的表单项）
            let inputFormValid = true
            let outputFormValid = true

            try {
              inputFormValid = await this.validateFormItems('inputForm')
            } catch (inputError) {
              inputFormValid = false
            }

            try {
              outputFormValid = await this.validateFormItems('outputForm')
            } catch (outputError) {
              outputFormValid = false
            }

            if (inputFormValid && outputFormValid) {
              this.saving = true

              const params = {
                pluginId: this.$route.params.agentId,
                thirdPartyInfo: {
                  platform: this.schemaForm.platform, //第三方平台标识
                  resourceType: this.schemaForm.resourceType, //资源类型标识，bot：智能体，workflow：工作流
                  httpMethod: this.schemaForm.method, //请求方法
                  resourceUrl: this.schemaForm.endPoint, //请求url
                  webSchema: JSON.stringify({
                    toolRequestInput: this.schemaForm.inputForm,
                    toolRequestOutput: this.schemaForm.outputForm,
                    // 添加自定义属性标记哪些字段是用户添加的
                    userAddedFields: {
                      input: this.schemaForm.inputForm
                        .filter((item) => item.isUserAdded)
                        .map((item) => item.name),
                      output: this.schemaForm.outputForm
                        .filter((item) => item.isUserAdded)
                        .map((item) => item.name),
                    },
                  }), //输入输出参数schema
                },
              }

              // 调用保存接口
              this.$utils.httpPost(
                this.$config.api.AGENT_THIRD_INFO_SAVE,
                JSON.stringify(params),
                {
                  config: {
                    headers: {
                      'Content-Type': 'application/json;charset=UTF-8',
                    },
                  },
                  success: (res) => {
                    console.log('保存成功，返回结果:', res)
                    if (res.code === '0') {
                      this.$message.success('保存成功')
                      this.isEditing = false // 保存成功后退出编辑模式
                    } else {
                      this.$message.error(
                        res.desc || '保存失败，服务器返回错误'
                      )
                    }
                    this.saving = false
                  },
                  error: (err) => {
                    console.error('保存请求失败:', err)
                    this.saving = false
                  },
                }
              )
            } else {
              this.$message.error('请检查表单填写是否正确')
            }
          } catch (error) {
            console.error('error', error)
          }
        }
      })
    },

    findItemByPath(items, path) {
      if (!path || !path.length) return null

      // 如果路径是数组索引的形式，例如 ['inputForm', 0, 'children', 1]
      if (Array.isArray(path)) {
        // 确定起始集合
        let current
        let startIndex = 0

        if (path[0] === 'inputForm') {
          current = this.schemaForm.inputForm
          startIndex = 1 // 跳过 'inputForm'
        } else if (path[0] === 'outputForm') {
          current = this.schemaForm.outputForm
          startIndex = 1 // 跳过 'outputForm'
        } else {
          // 如果路径不是以 'inputForm' 或 'outputForm' 开头，
          // 则尝试从当前项开始
          current = items || this.schemaForm.inputForm
        }

        // 遍历路径
        for (let i = startIndex; i < path.length; i++) {
          if (!current) return null

          const key = path[i]

          if (key === 'children') {
            // 如果是 'children' 属性，但是没有下一个索引，直接返回children数组
            if (i === path.length - 1) {
              return current.children || []
            }

            // 否则继续向下遍历
            if (current.children) {
              current = current.children
            } else {
              return null
            }
          } else if (typeof key === 'number' || !isNaN(parseInt(key))) {
            // 如果是数字索引
            const index = parseInt(key)
            if (Array.isArray(current) && index < current.length) {
              current = current[index]
            } else {
              return null // 索引超出范围
            }
          } else {
            // 其他属性
            if (current[key] !== undefined) {
              current = current[key]
            } else {
              return null // 属性不存在
            }
          }
        }

        return current
      }

      // 如果路径是对象ID形式
      return this.findItemById(items || this.schemaForm.inputForm, path)
    },

    // 通过ID查找项
    findItemById(items, id) {
      if (!items || !Array.isArray(items)) return null

      // 直接在当前层级查找
      const found = items.find((item) => item.id === id)
      if (found) return found

      // 递归查找子项
      for (const item of items) {
        if (item.children && Array.isArray(item.children)) {
          const result = this.findItemById(item.children, id)
          if (result) return result
        }
      }

      return null
    },
  },
}
</script>

<style lang="scss">
.third_agent_detail {
  .el-scrollbar__bar.is-horizontal {
    display: none;
  }
  width: 100%;
  .agent_config_wrap {
    padding: 20px 20px 0;
    background-color: #fff;
    border-radius: 8px;
    margin-bottom: 20px;
    .config_header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid #e4e7ed;
      padding: 10px 15px;
      .config_header_left {
        display: flex;
        align-items: center;
        .custom_icon {
          cursor: pointer;
          margin-left: 10px;
          color: $primary;
        }
      }

      .config_header_right {
        display: flex;
        align-items: center;
        gap: 10px;
        .el-button {
          height: auto;
          min-width: 80px;
        }
        .el-button--text {
          min-width: 70px;
        }
        .el-dropdown {
          cursor: pointer;
          margin-left: 10px;
        }
      }
    }
    .config_main {
      padding: 15px;

      // 添加的新样式，让表单控件填满它们所在的列
      .baseInfo_form {
        .el-form-item {
          margin-bottom: 12px;
          .el-select,
          .el-input {
            width: 100%;
          }
        }
      }

      .extend_entitie_collapse {
        border: none;

        .el-collapse-item__header {
          display: none;
        }

        .el-collapse-item__header,
        .el-collapse-item__wrap {
          border: none;
        }

        .el-collapse-item__content {
          padding-bottom: 0;
          .el-scrollbar {
            height: 350px;
          }
        }
      }
      .input_with_select {
        .el-select .el-input {
          width: 130px;
        }
        .el-input__inner {
          border-top-left-radius: 0;
          border-bottom-left-radius: 0;
        }
        .el-input-group__prepend {
          background-color: #fff;
        }
      }

      .item_title {
        margin-bottom: 15px;
        font-size: 16px;
        font-weight: 600;
      }
      .param_header {
        margin-bottom: 10px;
        .el-col {
          padding-left: 12px;
        }
      }
      .add_btn_wrap {
        padding: 15px 0 25px 0px;
        border-top: 1px solid #d8d8d8;
        button {
          border: none;
          color: #656769;
          background-color: #f5f6f8;
        }
      }
    }
  }

  .sub-title {
    padding-left: 14px;
    font-size: 16px;
    font-weight: 600;
    position: relative;
    color: #000000;
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-45%);
      width: 4px;
      height: 18px;
      background-color: $primary;
    }
  }
  .card_table {
    width: 100%;
    background: #f0f3f6;
    border-radius: 8px;
    padding: 20px;
    overflow-y: scroll;
    .card-wrap {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      .card-btns {
        margin-left: auto;
      }
    }
    .el-scrollbar {
      height: 300px;
      .intent_list {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        row-gap: 16px;
        column-gap: 10px;
      }
    }
    .el-pagination {
      margin-top: 24px;
    }
  }
}
</style>
