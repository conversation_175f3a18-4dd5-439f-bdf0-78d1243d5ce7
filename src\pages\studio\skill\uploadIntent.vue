<template>
  <div class="intent-batch-wrap">
    <el-dropdown
      trigger="click"
      @command="handleCommand"
      placement="bottom-start"
    >
      <el-button size="small" :disabled="!subAccountEditable">
        批量操作
        <i class="ic-r-triangle-down el-icon--right" />
      </el-button>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item command="upload"> 批量覆盖 </el-dropdown-item>
        <el-dropdown-item command="download">导出技能</el-dropdown-item>
        <el-dropdown-item command="template">下载模版</el-dropdown-item>
        <el-dropdown-item command="toDocs" style="width: 138px"
          >了解导入格式<i class="ic-r-link"></i
        ></el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>

    <!-- 批量覆盖提示 -->
    <el-dialog class="upload-tip-wrap" :visible.sync="visible" width="480px">
      <div class="upload-tip-title" slot="title">
        <i class="ic-r-exclamation" />
        <span>确定批量覆盖吗？</span>
      </div>
      <p class="give-up-save-content">
        一旦上传成功，技能现有信息将被完全覆盖！
      </p>
      <div slot="footer">
        <el-button style="min-width: 104px" @click="visible = false"
          >取消</el-button
        >
        <el-upload
          class="intent__upload-file"
          :action="
            `/aiui/${
              subAccount ? 'sub' : ''
            }web/intent/import?businessId=${businessId}&timestamp=` + timestamp
          "
          :show-file-list="false"
          :before-upload="beforeUpload"
          :on-success="success"
          :on-error="uploadError"
        >
          <el-button type="danger" style="min-width: 116px">确定覆盖</el-button>
        </el-upload>
      </div>
    </el-dialog>
    <el-dialog
      title="批量覆盖失败提示"
      :visible.sync="uploadFailed"
      width="500px"
    >
      <div v-for="(item, index) in failedReason" :key="index">
        <span>{{ item.file }} </span><span>{{ item.desc }}</span>
      </div>
      <div slot="footer" style="margin-top: 20px">
        <el-button type="primary" @click="uploadFailed = !uploadFailed"
          >确定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>
<script>
export default {
  name: 'upload-intent',
  props: {
    businessId: '',
    businessName: '',
    limitCount: Object,
    subAccountEditable: Boolean,
    skillType: '',
    subAccount: Boolean,
  },
  data() {
    return {
      baseUrl: this.$config.server,
      timestamp: new Date().getTime(),
      uploadFailed: false,
      failedReason: [],
      visible: false,
    }
  },
  methods: {
    // 批量操作
    handleCommand(command) {
      let self = this
      switch (command) {
        case 'download':
          this.$utils.postopen(
            this.$config.api.STUDIO_EXPORT_INTENT,
            {
              name: this.businessName,
              businessId: this.businessId,
            },
            this.subAccount
          )
          break
        case 'upload':
          this.visible = true
          break
        case 'template':
          window.open(
            `https://aiui-file.cn-bj.ufileos.com/${
              self.skillType == '3' ? 'DemoExtendWeather' : 'DemoWeather'
            }_2.1.zip`,
            '_self'
          )
          break
        case 'toDocs':
          if (self.skillType == '3') {
            window.open('http://cdn.iflyos.cn/docs/custom_import.pdf', '_blank')
          } else {
            window.open(`${self.$config.docs}doc-59/`, '_blank')
          }
          break
        default:
          break
      }
    },
    beforeUpload(file) {
      this.visible = false
      let reg = /\.zip?$/i
      let type = reg.test(file.name)
      let limit = this.limitCount['intent_file_size'] || 5
      let unExceed = file.size < 1024 * 1024 * limit
      if (!type) {
        this.$message.error('仅支持zip包文件')
      }
      if (!unExceed) {
        this.$message.error(`文件不能超过${limit}M`)
      }
      this.$emit('setLoad', type && unExceed)
      return type && unExceed
    },
    success(data) {
      if (data.flag) {
        this.uploadFailed = false
        this.$emit('setLoad', false)
        this.$emit('reloadAfterUpload')
        this.$message.success('批量覆盖成功')
      } else {
        this.$emit('setLoad', false)
        if (data.data && data.data.length) {
          this.uploadFailed = true
          this.failedReason = data.data
        } else {
          this.$message.error(data.desc || '批量覆盖失败')
        }
      }
    },
    uploadError() {
      this.$emit('setLoad', false)
    },
  },
}
</script>
<style lang="scss" scoped>
.intent-batch-wrap {
  display: inline-block;
  vertical-align: top;
  position: relative;
  .el-icon-question {
    color: $grey4;
  }
}
.upload-tip-title {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  i {
    font-size: 20px;
    color: $warning;
  }
  span {
    margin-left: 8px;
    font-size: 20px;
    font-weight: 500;
  }
}
.tip-wrap {
  position: absolute;
  right: 3px;
  z-index: 10;
  top: 11px;
  font-size: 14px;
}
.ic-r-link {
  position: absolute;
  right: 12px;
}
</style>

<style lang="scss">
.upload-tip-wrap {
  .el-dialog__body {
    padding: 0 32px 32px 60px;
  }
}
.intent__upload-file {
  display: inline-block;
  margin-left: 16px;
}
</style>
