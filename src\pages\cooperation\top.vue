<template>
  <div class="handle-platform-top">
    <h1 class=" mgb24" style="text-align: center">协同操作</h1>
    <div class="handle-platform-navs" style="width: 288px;" v-if="!subAccount">
      <router-link :to="{ name: 'authorities-manage' }">
        <div class="handle-platform-nav"
          :class="{'handle-platform-nav-active': nav === 'cooperation/authorities'}">
          子帐号管理
        </div>
      </router-link>
      <router-link :to="{ name: 'operate-logs' }">
        <div class="handle-platform-nav"
          :class="{'handle-platform-nav-active': nav === 'cooperation/logs'}">
          操作日志
        </div>
      </router-link>
    </div>
    <div v-else style="height: 32px;"></div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
export default {
  data () {
    return {
      nav: ''
    }
  },
  computed: {
    ...mapGetters({
      subAccount: 'user/subAccount'
    })
  },
  created() {
    this.nav = this.$route.path.replace('/', '')
  }
}
</script>

<style lang="scss">

</style>
