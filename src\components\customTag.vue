<template>
  <div>
    <el-tag
      :key="tag"
      v-for="tag in value"
      closable
      :disable-transitions="false"
      @close="handleClose(tag)"
    >
      {{ tag }}
    </el-tag>
    <el-input
      class="input-new-tag"
      v-if="inputVisible"
      v-model="inputValue"
      ref="saveTagInput"
      size="small"
      @keyup.enter.native="handleInputConfirm"
      @blur="handleInputConfirm"
    >
    </el-input>
    <el-button v-else class="button-new-tag" size="small" @click="showInput"
      >+ 标签</el-button
    >
  </div>
</template>
<script>
export default {
  props: {
    value: {
      type: Array,
      default: [],
    },
  },
  data() {
    return {
      inputVisible: false,
      inputValue: '',
    }
  },
  methods: {
    handleClose(tag) {
      let dynamicTags = this.value.slice()
      dynamicTags.splice(dynamicTags.indexOf(tag), 1)
      this.$emit('input', dynamicTags)
    },

    showInput() {
      this.inputVisible = true
      this.$nextTick((_) => {
        this.$refs.saveTagInput.$refs.input.focus()
      })
    },

    handleInputConfirm() {
      let dynamicTags = this.value.slice()
      let inputValue = this.inputValue
      if (inputValue) {
        dynamicTags.push(inputValue)
      }
      this.inputVisible = false
      this.inputValue = ''
      this.$emit('input', dynamicTags)
    },
  },
}
</script>
<style lang="scss" scoped>
.el-tag + .el-tag {
  margin-left: 10px;
}
.button-new-tag {
  margin-left: 10px;
  //   height: 32px;
  //   line-height: 30px;
  //   padding-top: 0;
  //   padding-bottom: 0;
}
.input-new-tag {
  width: 90px;
  margin-left: 10px;
  vertical-align: bottom;
}
</style>
