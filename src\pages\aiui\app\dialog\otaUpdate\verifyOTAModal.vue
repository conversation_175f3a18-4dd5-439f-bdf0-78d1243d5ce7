<template>
  <el-dialog class="dg-body" title="添加设备" :visible.sync="modalParam.show">
    <p>以下设备将在重启设备后收到固件更新提醒，最多支持添加10个设备ID</p>
    <el-select
      v-model="value"
      class="verify-select"
      multiple
      filterable
      allow-create
      :multiple-limit="10"
      placeholder="选择或新增设备ID（按回车添加）"
      default-first-option
      :loading="selectLoading"
    >
      <el-option
        v-for="item in options"
        :key="item.deviceId"
        :label="item.deviceId"
        :value="item.deviceId"
      >
      </el-option>
    </el-select>
    <div class="modal-btn-container">
      <el-button size="small" @click="closeModal">取消</el-button>
      <el-button size="small" type="primary" @click="verifySubmit"
        >确定</el-button
      >
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'verifyOTAModal',
  props: {
    modalParam: {
      type: Object,
      default: {
        show: Boolean,
        item: Object,
      },
    },
  },

  data() {
    return {
      options: [],
      value: [],
      selectLoading: false,
    }
  },

  methods: {
    closeModal() {
      this.modalParam.show = false
    },
    verifySubmit() {
      if (this.value.length === 0) {
        this.$message.error('验证设备id不能为空')
        return
      }

      if (this.value.some((v) => v.length > 64)) {
        this.$message.error('设备id长度不可超过64个字符')
        return
      }

      this.$utils.httpPost(
        this.$config.api.OTA_VERIFY_ADD,
        {
          appid: this.appId,
          version: this.modalParam.item.version,
          deviceIds: JSON.stringify([...this.value]),
        },
        {
          success: (res) => {
            if (res.flag) {
              this.modalParam.show = false
              this.$message.success('固件验证成功')
              this.$emit('getList')
            } else {
              this.$message.error(res.desc)
            }
          },
        }
      )
    },
  },

  computed: {
    appId() {
      return this.$route.params.appId
    },
  },

  watch: {
    'modalParam.show': function (val) {
      if (val) {
        this.selectLoading = true
        this.$utils.httpGet(
          this.$config.api.OTA_VERIFY_LIST,
          {
            appid: this.appId,
            version: this.modalParam.item.version,
          },
          {
            success: (res) => {
              this.selectLoading = false
              if (res.flag) {
                this.options = [...res.data]
                res.data.forEach((el) => {
                  this.value.push(el.deviceId)
                })
              } else {
                this.$message.error(res.desc)
              }
            },
          }
        )
        this.value = []
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.verify-select {
  margin-top: 10px;
  width: 100%;
}

.modal-btn-container {
  display: flex;
  justify-content: flex-end;
  padding: 30px 0;
  width: 100%;
}
</style>
<style lang="scss">
.verify-select {
  width: 100%;
  .el-tag {
    max-width: 100%;
    display: flex;
    align-items: center;
  }
  .el-select__tags-text {
    display: inline-block;
    max-width: 100%;
    text-overflow: ellipsis;
    overflow: hidden;
  }
  .el-icon-close {
    flex-shrink: 0;
    height: 16px;
    width: 16px;
  }
}
</style>
