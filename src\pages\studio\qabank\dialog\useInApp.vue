<template>
  <el-dialog
    title="在应用中生效"
    :visible.sync="dialog.show"
    width="640px"
    :close-on-click-modal="!loading"
    :close-on-press-escape="!loading"
    :before-close="beforeClose"
  >
    <p>该问答库已被以下应用引用，是否在以下应用的【生产环境】中生效新版本？</p>
    <p class="top-tip mgt12">
      注意： 1.
      该操作将在应用的【生产环境】生效，请确保本次操作不会造成用户的不良体验。
      2. 若此处未选择批量生效，你可以在以下应用的配置中选择问答库版本。
    </p>
    <div v-loading="loading" element-loading-text="正在生效中，请稍候">
      <el-checkbox v-model="isCheckedBatchAll" style="margin-bottom: 10px"
        >全部生效</el-checkbox
      >
      <template v-if="!isCheckedBatchAll">
        <div @keyup.enter="getAllApps">
          <el-input
            class="search-area"
            size="medium"
            placeholder="请输入应用名或APPID搜索"
            v-model="searchVal"
          >
            <i
              slot="suffix"
              class="el-input__icon el-icon-search search-area-btn"
              @click="getAllApps"
            />
          </el-input>
        </div>

        <div class="table-wrapper">
          <div class="checkbox-wrapper">
            <el-checkbox
              v-model="isCheckedAll"
              :indeterminate="isIndeterminate"
              @change="onCheckedAllChange"
              :disabled="checkedAllDisabled"
            ></el-checkbox>
          </div>
          <os-table
            class="mgb12 use-in-app"
            :tableData="tableData"
            @change="getApp"
          >
            <el-table-column prop="appName" label="应用名称" width="130">
            </el-table-column>
            <el-table-column prop="appid" label="APPID" width="130">
            </el-table-column>
            <el-table-column prop="sceneName" label="情景模式" width="130">
            </el-table-column>
            <el-table-column prop="outNumber" label="当前版本" width="130">
            </el-table-column>
            <el-table-column width="50">
              <template slot-scope="scope">
                <el-checkbox
                  v-model="scope.row.checked"
                  :disabled="!checkSelectable(scope.row)"
                  @change="(val) => onItemCheckedChange(val, scope.row)"
                ></el-checkbox>
              </template>
            </el-table-column>
          </os-table>
        </div>
      </template>
    </div>

    <span slot="footer" class="dialog-footer">
      <el-button class="dialog-btn" @click="cancel()"> 取消 </el-button>
      <el-button class="dialog-btn" type="primary" @click="save">
        {{ isCheckedBatchAll ? '全部生效' : '批量生效' }}
      </el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  props: {
    dialog: {
      type: Object,
      default: {},
    },
    businessId: '',
    selectNumber: '',
    selectMallId: '',
    onlineFlag: false,
  },
  data() {
    return {
      tableData: {
        loading: true,
        total: 0,
        page: 1,
        size: 5,
        list: [],
      },
      canSave: true,
      searchVal: '',
      isCheckedBatchAll: false,
      isCheckedAll: true,
      isIndeterminate: false,
      loading: false,
      apps: [],
    }
  },

  watch: {
    'dialog.show': function (val, oldVal) {
      if (val) {
        this.apps = []
        this.searchVal = ''

        this.getAllApps()
      } else {
        this.$set(this.tableData, 'page', 1)
        this.isCheckedBatchAll = false
      }
    },
  },
  computed: {
    checkedAllDisabled() {
      return this.apps.every((item) => !this.checkSelectable(item))
    },
  },
  methods: {
    getApp(page) {
      let self = this
      this.tableData.page = page || 1
      self.tableData.loading = true
      let pageIndex = page || self.tableData.page
      let from = (pageIndex - 1) * this.tableData.size
      let to = from + this.tableData.size
      self.tableData.list = self.apps.slice(from, to)
      self.tableData.loading = false
    },

    onCheckedAllChange(val) {
      this.isIndeterminate = false
      if (val) {
        this.tableData.list = this.tableData.list.map((item) => ({
          ...item,
          checked: true,
        }))
        this.apps = this.apps.map((item) => ({
          ...item,
          checked: true,
        }))
      } else {
        this.tableData.list = this.tableData.list.map((item) => {
          if (this.checkSelectable(item)) {
            return {
              ...item,
              checked: false,
            }
          } else {
            return {
              ...item,
            }
          }
        })
        this.apps = this.apps.map((item) => {
          if (this.checkSelectable(item)) {
            return {
              ...item,
              checked: false,
            }
          } else {
            return {
              ...item,
            }
          }
        })
      }
    },

    onItemCheckedChange(val, row) {
      this.apps = this.apps.map((item) => {
        if (item.sceneName === row.sceneName && item.appid === row.appid) {
          return {
            ...item,
            checked: val,
          }
        } else {
          return item
        }
      })

      const allCheck = this.apps.every((item) => item.checked)
      console.log('allCheck, this.apps', allCheck, this.apps)
      const allNoCheck = this.apps.every((item) => !item.checked)
      if (allCheck) {
        this.isIndeterminate = false
        this.isCheckedAll = true
      }
      if (allNoCheck) {
        this.isIndeterminate = false
        this.isCheckedAll = false
      }
      if (!allCheck && !allNoCheck) {
        this.isIndeterminate = true
      }
    },

    getAllApps() {
      let self = this

      self.tableData.loading = true
      this.tableData.page = 1
      this.$utils.httpGet(
        this.$config.api.STUDIO_QA_USED_LIST,
        {
          repoId: this.businessId,
          pageIndex: 1,
          pageSize: 1000,
          search: this.searchVal,
        },
        {
          success: (res) => {
            self.isCheckedAll = true
            self.tableData.loading = false
            self.apps = (res.data.apps || []).map((item) => ({
              ...item,
              checked: true,
            }))
            self.tableData.total = (res.data.apps || []).length
            self.tableData.list = []
            this.getApp(1)
          },
          error: (err) => {},
        }
      )
    },

    checkSelectable(row) {
      if (row && !row.hasOwnProperty('outNumber')) {
        return true
      }
      let selectNumberArr = this.selectNumber.split('.')
      let rowNumberArr = row.outNumber.split('.')
      if (
        rowNumberArr[0] == selectNumberArr[0] &&
        rowNumberArr[1] == selectNumberArr[1] &&
        rowNumberArr[2] == selectNumberArr[2]
      ) {
        return false
      } else {
        return true
      }
    },
    beforeClose(done) {
      if (this.loading) {
        return
      } else {
        done && done()
      }
    },
    save() {
      let self = this
      if (this.loading) {
        return
      }
      let data = {
        repoId: this.businessId,
        selectAll: false,
      }
      if (!this.onlineFlag) {
        data.verId = this.selectMallId
      }

      if (this.isCheckedBatchAll) {
        data.selectAll = true
      } else {
        const configs = this.apps
          .filter((item) => this.checkSelectable(item))
          .map(({ appid, sceneName }) => ({ appid, sceneName }))
        if (configs.length === 0) {
          return this.$message.warning('无待批量生效的应用')
        }
        data.configs = JSON.stringify(configs)
      }
      this.loading = true
      this.$utils.httpPost(this.$config.api.STUDIO_QA_USE_IN_APP, data, {
        success: (res) => {
          self.$message({
            message: res.data.desc || '操作成功',
            type: 'success',
          })
          self.dialog.show = false
          self.loading = false
        },
        error: (err) => {
          self.$message({
            message: res.data.desc || '操作失败',
            type: 'error',
          })
          self.loading = false
        },
      })
    },

    cancel(rows) {
      if (this.loading) {
        return
      }
      this.dialog.show = false
    },
  },
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="scss" scoped>
.top-tip {
  margin-bottom: 24px;
  padding: 11px 17px;
  color: $warning;
  border-radius: 4px;
  border: 1px solid $warning;
}
.table-wrapper {
  position: relative;
}
.checkbox-wrapper {
  position: absolute;
  z-index: 1;
  top: 18px;
  right: 26px;
}
</style>
<style lang="scss">
.unchecked-all {
  th .el-checkbox__inner {
    background-color: #409eff;
    border-color: #409eff;
    &:before {
      content: '';
      position: absolute;
      display: block;
      background-color: #fff;
      height: 2px;
      transform: scale(0.5);
      left: 0;
      right: 0;
      top: 5px;
    }
    &:after {
      border-color: transparent;
    }
  }
}
.use-in-app {
  .is-disabled .el-checkbox__inner::after {
    box-sizing: content-box;
    content: '';
    border: 1px solid #c0c4cc;
    border-left: 0;
    border-top: 0;
    height: 7px;
    left: 4px;
    position: absolute;
    top: 1px;
    width: 3px;
    transition: transform 0.15s ease-in 0.05s;
    transform-origin: center;
    transform: rotate(45deg) scaleY(1);
  }
}
</style>
