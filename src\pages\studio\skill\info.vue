<template>
  <os-page :options="pageOptions">
    <studio-skill-header-right slot="btn" />
    <div class="info-page">
      <el-form
        class="mgt48 mgb56"
        ref="skillForm"
        :rules="rules"
        :model="skill"
        label-width="118px"
        label-position="left"
        :disabled="!subAccountEditable"
      >
        <el-form-item label="技能类型">
          <span v-if="skill.privateSkill">私有技能</span>
          <span v-else>开放技能</span>
        </el-form-item>
        <!-- <el-form-item label="二级分类">
          <span v-if="skill.secondType === 2">APP控制技能</span>
          <span v-else>自定义语音技能</span>
        </el-form-item> -->
        <!-- <el-form-item label="服务平台">
          <span v-if="skill.type === '2'">AIUI</span>
          <span v-else>iFLYOS</span>
        </el-form-item> -->
        <el-form-item label="技能协议" class="item-skill-privacy">
          <span class="skill-privacy">v{{ skill.protocolVersion }}</span>
          <a
            style="font-size: 14px; margin-right: 20px"
            :href="DocsInUrl"
            target="_blank"
            >查看开发文档</a
          >
          <el-tooltip
            class="item"
            effect="dark"
            placement="top"
            v-if="skill.privateSkill && Number(skill.protocolVersion) < 2.1"
          >
            <div slot="content">
              1、更加标准化的协议，降低接入门槛；<br />2、提供更加丰富的API，降低技能后处理开发门槛；<br />3、提供更多类型的指令，满足更复杂的业务需求；<br />
              4、更加稳定的服务；2.0技能协议后续不再维护，尽快升级体验
            </div>
            <el-button size="small" @click="upgrade">升级v2.1</el-button>
          </el-tooltip>
        </el-form-item>
        <el-form-item
          :label="theme + '名称'"
          prop="zhName"
          :placeholder="'请输入' + theme + '名称'"
        >
          <el-input v-model="skill.zhName"></el-input>
        </el-form-item>
        <el-form-item v-if="!skill.privateSkill">
          <span style="font-weight: 600" slot="label"
            >{{ theme + '别名' }}
            <el-tooltip
              effect="dark"
              :content="
                '用户可通过' + theme + '名称和' + theme + '别名来访问你的技能'
              "
            >
              <i class="el-icon-question" />
            </el-tooltip>
          </span>
          <skill-alias
            :zhName="skill.zhName"
            :initAlias="initAlias"
            :onCheck="false"
            :subAccountEditable="subAccountEditable"
            @setAlias="setAlias"
          ></skill-alias>
        </el-form-item>

        <el-form-item label="英文标识">
          <span>{{ skill.identify }}</span>
        </el-form-item>
        <!-- <el-form-item v-if="!skill.privateSkill"
          label="调用名称" prop="callName" placeholder="请输入调用名称">
          <el-input v-model="skill.callName"></el-input>
        </el-form-item> -->
        <el-form-item label="技能分类">
          <el-select v-model="skill.businesstype" placeholder="请选择">
            <el-option
              v-for="(val, key, index) in skillTypeList"
              :key="index"
              :label="val"
              :value="key"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <!-- 1226-关联包名 本次更新 -->
        <template v-if="skill.secondType == 2">
          <el-form-item
            class="skill-name-input"
            v-if="!skill.privateSkill"
            prop="appPackage"
          >
            <span style="font-weight: 600" slot="label"
              >关联包名
              <el-tooltip
                effect="dark"
                content="技能和APP一对一关联。请确保你开发技能的行为不会对APP产生侵权。"
              >
                <i class="el-icon-question" />
              </el-tooltip>
            </span>
            <el-input
              ref="nameInput"
              v-model.trim="skill.appPackage"
              placeholder="请输入技能关联应用的完整package_name"
            ></el-input>
          </el-form-item>
          <el-form-item
            class="skill-name-input"
            v-if="!skill.privateSkill"
            prop="appDownloadUrl"
          >
            <span style="font-weight: 600" slot="label"
              >APP下载地址
              <el-tooltip
                effect="dark"
                content="请确保该下载地址可以一直下载到最新的APP。"
              >
                <i class="el-icon-question" />
              </el-tooltip>
            </span>
            <el-input
              ref="nameInput"
              v-model.trim="skill.appDownloadUrl"
              placeholder="请输入APP的下载地址"
            ></el-input>
          </el-form-item>
        </template>

        <el-form-item
          v-if="!skill.privateSkill"
          :label="skill.secondType === 2 ? 'APP图标' : '技能图标'"
        >
          <upload-icon
            :skillId="skill.id"
            :image="image"
            @setIconInfo="setIconInfo"
          ></upload-icon>
        </el-form-item>
        <el-form-item v-if="!skill.privateSkill">
          <span style="font-weight: 600" slot="label"
            >示例说法
            <el-tooltip
              effect="dark"
              v-if="theme === 'APP'"
              content="请告诉用户可以通过语言怎样和你的技能交互"
            >
              <i class="el-icon-question" />
            </el-tooltip>
          </span>
          <span>回车添加示例说法：</span>
          <example-utterance
            ref="egUtterance"
            :list="initEgUtterances"
            :subAccountEditable="subAccountEditable"
            @setExampleUtterance="setExampleUtterance"
          ></example-utterance>
        </el-form-item>

        <el-form-item v-if="!skill.privateSkill" label="欢迎语示例说法">
          <span>回车添加示例说法：</span>

          <!-- <example-utterance
            style="margin-bottom: 8px"
            ref="egUtterance"
            :list="initWelcomePhrase"
            :subAccountEditable="subAccountEditable"
            @setWelcomePhrase="setWelcomePhrase"
          ></example-utterance> -->
          <welcome-words
            :list="initWelcomePhrase.data"
            :subAccountEditable="subAccountEditable"
            @setWelcomePhrase="setWelcomePhrase"
          ></welcome-words>
          <p class="welcom-words-tip">
            温馨提示：该欢迎语与AIUI技能自定义回复语一起使用，使用技能后处理写技能回复，则在技能后处理里添加欢迎语。
          </p>
        </el-form-item>

        <el-form-item
          v-if="!skill.privateSkill"
          prop="briefIntroduction"
          label="技能简介"
        >
          <el-input
            type="textarea"
            resize="none"
            :rows="3"
            style="font-family: Arial"
            placeholder="请输入技能简介，最多1000个字符"
            v-model="skill.briefIntroduction"
          >
          </el-input>
        </el-form-item>
        <el-form-item v-if="!skill.privateSkill" prop="provider" label="开发者">
          <span>{{ skill.provider || '-' }}</span>
        </el-form-item>
      </el-form>

      <div style="margin-top: 40px" v-if="subAccountEditable">
        <el-button
          type="primary"
          @click="onSubmit(2)"
          :loading="saving"
          :disabled="!changed"
        >
          {{ saving ? '保存中...' : '保存' }}
        </el-button>
        <os-give-up-save :edited="changed" @noSave="noSave" />
      </div>
    </div>
    <page-leave-tips
      :dialog="leaveDialog"
      @save="onSubmit"
      @noSave="noSave"
      @noJump="noJump"
    />
  </os-page>
</template>

<script>
import { mapGetters } from 'vuex'
import SkillAlias from './storeSkill/skillAlias'
import UploadIcon from './storeSkill/uploadIcon'
import ExampleUtterance from './storeSkill/exampleUtterance'
import welcomeWords from './welcomeWords.vue'

export default {
  name: 'skill-info',
  data() {
    return {
      pageOptions: {
        title: '基本信息',
        loading: false,
      },
      rules: {
        // without blur, blur校验单独处理，防止点击页面上的“构建”触发blur校验，形成误导
        zhName: this.$rules.skillZhName(),
        // zhName: this.$rules.skillZhNameWithoutBlur(),
        name: this.$rules.skillZhName(),
        briefIntroduction: [
          this.$rules.lengthLimit(1, 1000, '技能简介长度不能超过1000个字符'),
        ],
        appPackage: [
          this.$rules.required('关联包名不能为空'),
          this.$rules.onlyNumAndEnglishChar(),
        ],
        appDownloadUrl: [
          {
            pattern:
              /^([hH][tT]{2}[pP]:\/\/|[hH][tT]{2}[pP][sS]:\/\/)(([A-Za-z0-9-~]+).)+([A-Za-z0-9-~\/])+$/,
            message: 'APP下载地址格式不正确',
            trigger: 'blur',
          },
        ],
      },
      skill: {},
      skillTypeList: [],
      saving: false,
      leaveDialog: {
        show: false,
      },
      routeTo: {},
      isAliasValid: null,
      initAlias: {
        data: [],
      },
      initEgUtterances: {
        data: [],
        type: 'setExampleUtterance',
        tip: '例如：打开小飞天文百科,回车新增',
      },
      initWelcomePhrase: {
        data: [],
        type: 'setWelcomePhrase',
        tip: '例如：欢迎来到小飞天文百科,回车新增',
      },
      image: {
        url: '',
      },
      initDataChanged: false, //记录computed检测不到的数据是否有改变(包括：别名、示例说法、iconUrl)
    }
  },
  beforeRouteLeave: function (to, from, next) {
    if (this.changed) {
      this.leaveDialog.show = true
      this.routeTo = to
      next(false)
    } else {
      next()
    }
  },
  computed: {
    ...mapGetters({
      originalSkill: 'studioSkill/skill',
      subAccountSkillAuths: 'studioSkill/subAccountSkillAuths',
    }),
    edited() {
      let self = this
      return (
        self.skill.zhName !== self.originalSkill.zhName ||
        self.skill.name !== self.originalSkill.name ||
        self.skill.businesstype !== self.originalSkill.businesstype ||
        self.skill.briefIntroduction !== self.originalSkill.briefIntroduction ||
        self.skill.appPackage !== self.originalSkill.appPackage ||
        self.skill.appDownloadUrl != self.originalSkill.appDownloadUrl
      )
    },
    changed() {
      return this.edited || this.initDataChanged
    },
    DocsInUrl() {
      // 新文档下线，暂时不动
      if (this.skill.protocolVersion && this.skill.protocolVersion == '2.0') {
        return `https://aiui.xfyun.cn/doc/aiui/4_skill_develop/8_agreement/protocal/request.html`
      }
      return `${this.$config.docs}doc-73/`
    },
    subAccountEditable() {
      return this.subAccountSkillAuths[this.skill.id] == 2 ? false : true
    },
    theme() {
      return this.skill.secondType === 2 ? 'APP' : '技能'
    },
  },
  watch: {
    originalSkill: function (val, oldVal) {
      this.skill = this.$deepClone(val)
      this.returnInitData()
      if (this.routeTo.name) {
        this.$router.push({
          name: this.routeTo.name,
          params: this.routeTo.params,
        })
      }
    },
  },
  created() {
    if (this.$store.state.studioSkill.skill.id) {
      this.skill = this.$deepClone(this.$store.state.studioSkill.skill)
      this.returnInitData()
    }
    this.getSkillType()
  },
  methods: {
    onZhNameBlur(e) {
      this.$refs.skillForm.validateField('zhName')
    },
    returnInitData() {
      if (this.skill.privateSkill) return
      this.initAlias.data = this.skill.aliasName
        ? JSON.parse(this.skill.aliasName)
        : []
      this.initEgUtterances.data = this.skill.examplePhrase
        ? JSON.parse(this.skill.examplePhrase)
        : []
      // this.initWelcomePhrase.data = this.skill.welcomePhrase
      //   ? JSON.parse(this.skill.welcomePhrase)
      //   : []
      this.initWelcomePhrase.data = this.skill.welcomePhrase
        ? this.skill.welcomePhrase
        : []

      this.image.url = this.skill.iconUrl
    },
    checkInitDataStatus() {
      let self = this
      if (
        self.skill.aliasName != self.originalSkill.aliasName ||
        self.skill.examplePhrase !== self.originalSkill.examplePhrase ||
        self.skill.welcomePhrase !== self.originalSkill.welcomePhrase ||
        self.skill.iconUrl !== self.originalSkill.iconUrl
      ) {
        self.initDataChanged = true
      } else {
        self.initDataChanged = false
      }
    },
    getSkillType() {
      this.$utils.httpGet(
        this.$config.api.STUDIO_SKILL_SKILL_TYPES,
        {},
        {
          success: (res) => {
            this.skillTypeList = res.data
          },
          error: (err) => {
            console.log('page=>>')
            console.log(err)
          },
        }
      )
    },
    setAlias(val, valid) {
      this.skill.aliasName = val
      this.isAliasValid = valid
      this.checkInitDataStatus()
    },
    setExampleUtterance(val) {
      this.skill.examplePhrase = val
      this.checkInitDataStatus()
    },
    setWelcomePhrase(val) {
      this.skill.welcomePhrase = val
      this.checkInitDataStatus()
    },
    setIconInfo(val) {
      this.skill.iconUrl = val
      this.checkInitDataStatus()
    },
    upgrade() {
      // 私有技能创建
      // (data.zhname = this.skill.zhName),
      //       (data.name = this.skill.name),
      //       (data.businesstype = this.skill.businesstype || '')
      //     data.type = this.skill.type

      let self = this
      this.$confirm('确认将技能升级为v2.1?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        showCancelButton: false,
        type: 'warning',
      })
        .then(() => {
          this.doUpgrade()
        })
        .catch(() => {})
    },
    doUpgrade() {
      let self = this
      let api = this.$config.api.STUDIO_ADD_EDIT_PRIVATESKILL
      let data = {
        id: this.skill.id,
        protocolVersion: '2.1',
        zhname: this.skill.zhName,
        name: this.skill.name,
        // businesstype: this.skill.businesstype,
        // type: this.skill.type,
        isPublish: 0,
        // secondType: this.skill.secondType,
      }
      this.$utils.httpPost(api, data, {
        success: (res) => {
          self.saving = false
          // self.$message.success('升级成功')
          self.$refs.skillForm && self.$refs.skillForm.clearValidate()
          self.$store.dispatch('studioSkill/setSkill', this.skill.id)
          this.$alert(
            '升级成功，去技能后处理修改云函数，可参考<a target="_blank" href="https://iflydocs.com/h/s/doc/hTSnLE7WR6sE3bsG">升级指南</a>',
            '提示',
            {
              dangerouslyUseHTMLString: true,
              type: 'info',
            }
          )
        },
        error: (err) => {
          this.saving = false
          console.log('page=>>')
          console.log(err)
        },
      })
    },
    onSubmit() {
      let self = this
      if (this.saving) {
        return
      }
      if (!this.isAliasValid && this.isAliasValid != null) {
        this.$message.warning('技能别名不合法')
        return false
      }
      // if(this.skill.welcomePhrase && this.skill.welcomePhrase.length <3) {
      //   this.$message.warning('欢迎词不能为空')
      //   return false
      // }
      this.$refs.skillForm.validate((valid) => {
        if (valid) {
          this.saving = true
          this.$refs.egUtterance && this.$refs.egUtterance.delText()
          let data = {
            isPublish: 0,
            secondType: this.skill.secondType,
          }
          let api = ''
          if (this.skill.privateSkill) {
            // 私有技能创建
            api = this.$config.api.STUDIO_ADD_EDIT_PRIVATESKILL
            ;(data.id = this.skill.id),
              (data.zhname = this.skill.zhName),
              (data.name = this.skill.name),
              (data.businesstype = this.skill.businesstype || '')
            data.type = this.skill.type
          } else {
            api = this.$config.api.STUDIO_ADD_EDIT_SKILL
            data.skillId = this.skill.id
            ;(data.zhName = this.skill.zhName),
              (data.businesstype = this.skill.businesstype || '')
            data.aliasName = this.skill.aliasName || ''
            data.examplePhrase = this.skill.examplePhrase || ''
            // data.welcomePhrase = this.skill.welcomePhrase || ''
            data.welcomePhrase = this.skill.welcomePhrase
              ? JSON.stringify(this.skill.welcomePhrase)
              : ''
            data.iconUrl = this.skill.iconUrl || ''
            data.briefIntroduction = this.skill.briefIntroduction || ''
            data.provider = this.skill.provider || ''
            if (this.skill.secondType == 2) {
              data.appPackage = this.skill.appPackage || ''
              data.appDownloadUrl = this.skill.appDownloadUrl || ''
            }
          }
          this.$utils.httpPost(api, data, {
            success: (res) => {
              self.saving = false
              self.$message.success('保存成功')
              self.$refs.skillForm && self.$refs.skillForm.clearValidate()
              self.$store.dispatch('studioSkill/setSkill', this.skill.id)
              self.initDataChanged = false
            },
            error: (err) => {
              this.saving = false
              console.log('page=>>')
              console.log(err)
            },
          })
        }
      })
    },
    noSave() {
      this.$refs.skillForm.clearValidate()
      this.initDataChanged = false
      this.skill = this.$deepClone(this.originalSkill)
      this.returnInitData()
      if (this.routeTo.name) {
        this.$router.push({
          name: this.routeTo.name,
          params: this.routeTo.params,
        })
      }
    },
    noJump() {
      this.routeTo = {}
    },
  },
  components: {
    SkillAlias,
    UploadIcon,
    ExampleUtterance,
    welcomeWords,
  },
}
</script>

<style lang="scss" scoped>
.item-skill-privacy {
  font-size: 0;
}
.skill-privacy {
  display: inline-block;
  margin-right: 16px;
  width: 52px;
  height: 24px;
  line-height: 24px;
  text-align: center;
  color: $success;
  border-radius: 12px;
  background-color: $success-light-12;
}
.welcom-words-tip {
  padding-left: 16px;
  height: 40px;
  color: $grey5;
  background: $grey4-15;
  border-radius: 2px;
  margin-top: 5px;
}
</style>
