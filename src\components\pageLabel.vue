<template>
  <div class="os-page-label">
    <i v-if="showAngle" @click="toggle" :class="['ic-r-angle-r', {'angle-down': angleDown}]"></i>
    <span v-if="label" @click="toggle" v-html="label" :class="['title', {'title-cursor': showAngle}]"></span>
    <slot v-else @click="toggle" name="title"></slot>
    <slot></slot>
  </div>
</template>

<script>
export default {
  name: 'OsPageLabel',
  props: {
    showAngle: {
      type: Boolean,
      default: false
    },
    angleDown: {
      type: Boolean,
      default: false
    },
    label: {
      type: String,
      default: ''
    }
  },
  data () {
    return {

    }
  },
  computed: {

  },
  mounted() {

  },
  methods: {
    toggle() {
      if(this.showAngle)
        this.$emit('toggle')
    }
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="scss" scoped>
.os-page-label {
  height: 40px;
  display: flex;
  align-items: center;
  i {
    font-size: 20px;
    margin-right: 7px;
    cursor: pointer;
    transition: all .3s;
  }
  .angle-down {
    transform: rotate(90deg);
  }
  & span.title {
    font-size: 20px;
  }
  .title-cursor{
    cursor: pointer;
  }
}
</style>
