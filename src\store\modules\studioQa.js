import { utils } from '@U'
import { api } from '@/config'
import Router from '../../router'

export default {
  namespaced: true,
  state: {
    id: '',
    qa: {},
  },

  actions: {
    setId({ commit }, qaId) {
      commit('setId', qaId)
    },

    setQa({ state, commit, rootState }, qaId) {
      commit('setId', qaId)
      commit('setQa', {})
      if (!qaId) {
        return false
      }
      utils.httpPost(
        api.STUDIO_QA_INFO,
        {
          repoId: qaId,
        },
        {
          success: (res) => {
            if (!res.data.latestNumber) {
              res.data.latestNumber = '0.0.0'
            }
            res.data.latestVersionArr = res.data.latestNumber.split('.')
            res.data.latestVersionArr[res.data.latestVersionArr.length - 1] =
              parseInt(
                res.data.latestVersionArr[res.data.latestVersionArr.length - 1]
              ) + 1

            let obj = {
              ...res.data,
              qaType: res.data.qaType ? res.data.qaType : '0',
            }
            commit('setQa', obj)
            // 临时
            utils.httpGet(
              api.STUDIO_QA_VERSIONS,
              {
                repoId: qaId,
              },
              {
                success: (res) => {
                  if ((res.data.private || []).length > 0) {
                    const onlineVersion = res.data.private[0].outNumber
                    commit('setQa', {
                      ...obj,
                      onlineVersion,
                      // 两个版本相同，证明上次是刚发布的，这时界面上updateLog应该清空
                      updateLog:
                        onlineVersion === obj.latestNumber ? '' : obj.updateLog,
                    })
                  } else {
                    commit('setQa', { ...obj, onlineVersion: '0.0.0' })
                  }
                },
              }
            )
          },
          error: (err) => {
            Router.push({ name: 'studio-handle-platform-qabanks' })
          },
        }
      )
    },
    setKeyQa({ state, commit, rootState }, qaId) {
      commit('setId', qaId)
      commit('setQa', {})
      utils.httpGet(
        api.STUDIO_QA_INFO,
        {
          repoId: qaId,
        },
        {
          success: (res) => {
            if (res.data.latestNumber) {
              res.data.latestVersionArr = res.data.latestNumber.split('.')
              res.data.latestVersionArr[res.data.latestVersionArr.length - 1] =
                parseInt(
                  res.data.latestVersionArr[
                    res.data.latestVersionArr.length - 1
                  ]
                ) + 1
            } else if (res.data.onlineVersion) {
              res.data.latestVersionArr = res.data.onlineVersion.split('.')
              res.data.latestVersionArr[res.data.latestVersionArr.length - 1] =
                parseInt(
                  res.data.latestVersionArr[
                    res.data.latestVersionArr.length - 1
                  ]
                ) + 1
            } else {
              res.data.latestVersionArr = [0, 0, 1]
            }
            let obj = {
              ...res.data,
              qaType: res.data.qaType ? res.data.qaType : '0',
            }
            commit('setQa', res.data)
            // 临时
            utils.httpGet(
              api.STUDIO_KEY_QA_VERSIONS,
              {
                qaId: res.data.qaId,
              },
              {
                success: (res) => {
                  if ((res.data.list || []).length > 0) {
                    const onlineVersion = res.data.list[0].outNumber
                    commit('setQa', {
                      ...obj,
                      onlineVersion,
                      // 两个版本相同，证明上次是刚发布的，这时界面上updateLog应该清空
                      updateLog:
                        onlineVersion === obj.latestNumber ? '' : obj.updateLog,
                    })
                  } else {
                    commit('setQa', { ...obj, onlineVersion: '0.0.0' })
                  }
                },
              }
            )
          },
          error: (err) => {
            Router.push({ name: 'studio-handle-platform-qabanks' })
          },
        }
      )
    },
  },

  mutations: {
    setId(state, qaId) {
      state.id = qaId
    },
    setQa(state, qa) {
      state.qa = qa
    },
  },

  getters: {
    id(state, getters) {
      return state.id
    },
    qa(state, getters) {
      return state.qa
    },
  },
}
