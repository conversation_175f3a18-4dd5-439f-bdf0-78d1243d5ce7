<template>
  <div>
    <p class="mgb24 section-sub-title">创建应用
      <el-checkbox style="margin-left: 25px;" v-model="createApp"
        :disabled="saving">创建新应用权限</el-checkbox>
      <el-input size="medium" style="margin-left: 114px; width: 330px;"
        placeholder="根据APPID/应用名称搜索" v-model="searchVal"  @keyup.enter.native="searchApp">
        <i slot="suffix" class="el-input__icon el-icon-search search-area-btn"
          @click="searchApp" />
      </el-input>
    </p>
    <p class="section-sub-title">已有应用</p>
    <div class="btn-wrap">
        <el-select v-model="flag" class="mgr8"
          placeholder="请选择" size="medium" style="width: 112px;"
          :disabled="saving">
          <el-option
            v-for="item in filterTypes"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
        <el-button size="medium"
          @click="batchOptionType = 2" :disabled="saving">设为查看</el-button>
        <el-button size="medium"
          @click="batchOptionType = 3" :disabled="saving">设为编辑</el-button>
        <el-button size="medium"
          @click="batchOptionType = 4" :disabled="saving">设为发布</el-button>
        <el-button size="medium"
          :disabled="flag === 2 || saving"
          @click="batchOptionType = 0">关闭权限</el-button>
      </div>
      <os-table
        :tableData="tableData"
        size="small"
        style="min-height: 200px;"
        @change="changePages">
        <el-table-column
          width="114" label="权限开关">
          <template slot-scope="scope">
            <el-switch v-model="scope.row.checked" @change="setSelection(scope.row)"></el-switch>
          </template>
        </el-table-column>
        <el-table-column
          width="240"
          label="应用">
          <template slot-scope="scope">
            <div class="text-blod" :title="scope.row.appName || '-'">{{scope.row.appName }}</div>
          </template>
        </el-table-column>
        <el-table-column
          width="300"
          label="权限">
          <template slot-scope="scope">
            <el-radio-group v-model="scope.row.subResourceId">
              <el-radio :label="2" :disabled="!scope.row.checked">查看</el-radio>
              <el-radio :label="3" :disabled="!scope.row.checked">编辑</el-radio>
              <el-radio :label="4" :disabled="!scope.row.checked">发布</el-radio>
            </el-radio-group>
          </template>
        </el-table-column>
      </os-table>
  </div>
</template>

<script>
  export default {
    name: 'app-table',
    props: {
      account: {
        type: Object,
        default: {}
      },
      saving: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        flag: 0,
        filterTypes: [
          {
            value: 0,
            label: '全部'
          },{
            value: 1,
            label: '有权限'
          },{
            value: 2,
            label: '无权限'
          }
        ],
        batchOptionType: null,
        searchVal: '',
        createApp: false,
        initCreateApp: false,
        tableData: {
          loading: true,
          total: 0,
          page: 1,
          size: 10,
          list: []
        },
        appIdsToSave: [],
        appsToSave: [],
        pagesList: {}, //每页的数据
        initAppIds: [], //每页有权限的appid，用于计算delete的技能
        initAuthApps: [] //每页有权限的app，用于判断数据有没有改动
      }
    },
    watch: {
      'account.subUid': function(val) {
        this.getAuthority(1)
      },
      flag(val) {
        this.init()
        this.getAuthority(1)
      },
      batchOptionType(val, oldVal) {
        let self = this
        if(val !== oldVal) {
          let keys = Object.keys(self.pagesList)
          keys.forEach(key => {
            self.pagesList[key].forEach( item => {
              self.$set(item, 'subResourceId', val)
              item.checked = val > 0 ? true : false
              self.$forceUpdate()
            })
          })
        }
      },
      saving(val) {
        this.tableData.loading = val
      }
    },
    created() {
      this.flag = 0
      this.init()
      this.getAuthority(1)
    },
    methods: {
      init(){
        this.batchOptionType = null
        this.tableData.total = 0
        this.tableData.list = []
        this.pagesList = {}
        this.initAppIds = []
        this.initAuthApps = []
        this.appIdsToSave = []
        this.appsToSave = []
      },
      changePages(page) {
        let self = this
        if(self.pagesList.hasOwnProperty(page)){
          self.tableData.list = self.pagesList[page]
        } else {
          self.getAuthority(page)
        }
      },
      searchApp () {
        this.getAuthority(1)
      },
      getAuthority(page){
        let self = this
        let data = {
          subUid: self.account.subUid,
          pageIndex: page || self.tableData.page,
          pageSize: self.tableData.size,
          search: self.searchVal,
          flag: self.flag,
          ability: 2
        }
        if (self.searchVal != '') {
          this.pagesList = {}
          this.initAppIds = []
          this.initAuthApps = []
        }
        self.tableData.loading = true
        this.$utils.httpGet(this.$config.api.COOP_GET_AUTHORITY, data, {
          success: (res) => {
            self.tableData.loading = false
            self.createApp = res.data.createapp
            self.initCreateApp = res.data.createapp
            self.tableData.total = res.data.edit.count
            self.tableData.page = res.data.edit.pageIndex
            self.tableData.size = res.data.edit.pageSize
            if(res.data && res.data.edit) {
              if(!res.data.edit.count) res.data.edit.count = 0
              if(!res.data.edit.apps) res.data.edit.apps = []
              let tmp = []
              res.data.edit.apps.forEach(item => {
                //已开权限的标志：有subResourceId属性
                if(!self.batchOptionType && self.batchOptionType !== 0 ) {
                  if(item.hasOwnProperty('subResourceId') && item.subResourceId) {
                    item.checked = true
                    self.initAppIds.push(item.appid)
                    tmp.push({
                      appid: item.appid,
                      subResourceId: item.subResourceId
                    })
                  } else {
                    if(self.flag === 2) {
                      self.initAppIds.push(item.appid)
                    }
                    item.checked = false
                  }
                } else {
                  item.subResourceId = self.batchOptionType
                  item.checked = self.batchOptionType ? true : false
                }
              })
              self.pagesList[page] = res.data.edit.apps
              self.tableData.list = res.data.edit.apps
              self.initAuthApps.push(...JSON.parse(JSON.stringify(tmp)))
            }
          },
          error: (err) => {
            self.tableData.loading = false
          }
        })
      },
      beforeSaveApps(){
        let self = this
        let keys = Object.keys(self.pagesList)
        keys.forEach(key => {
          self.pagesList[key].forEach( item => {
            if(item.checked && item.subResourceId){
              self.appsToSave.push({
                appid: item.appid,
                subResourceId: item.subResourceId
              })
              self.appIdsToSave.push(item.appid)
            }
          })
        })
      },
      async saveApp(){
        let self = this
        if(self.batchOptionType >= 0) {
          await self.saveBatchOption()
        }
        self.beforeSaveApps()
        if(JSON.stringify(self.appsToSave) === JSON.stringify(self.initAuthApps) && self.initCreateApp === self.createApp){
          return Promise.resolve('noChange')
        }
        let tmp = {
          createapp: this.createApp,
          editapp: self.appsToSave,
          'deleteapp': this.$utils.arraysDiff(this.initAppIds, this.appIdsToSave)
        }
        let data = {
          subUid: self.account.subUid,
          authority: JSON.stringify(tmp)
        }
        return new Promise((resolve, reject) => {
          self.$utils.httpPost(this.$config.api.COOP_SAVE_AUTHORITY, data, {
            success: (res) => {
              resolve('saveApp success')
            },
            error: (err) => {
              reject('saveApp')
            }
          })
        })
      },
      setSelection(val) {
        let self = this
        if(!val.hasOwnProperty('oldSubResourceId')) {
          self.$set(val, 'oldSubResourceId', '')
        }
        if(val.checked) {
          if(!val.hasOwnProperty('subResourceId') || val.subResourceId === 0) {
            self.$set(val, 'subResourceId', 2)
            val.oldSubResourceId = ''
          } else {
            val.subResourceId = val.oldSubResourceId
            val.oldSubResourceId = ''
          }
        } else {
          val.oldSubResourceId = val.subResourceId
          val.subResourceId = ''
        }
      },
      async saveBatchOption() {
        let self = this
        if(typeof self.batchOptionType !== 'number') {
          return Promise.resolve('noChange')
        }
        let data = {
          subUid: self.account.subUid,
          flag: self.flag,
          type: self.batchOptionType,
          ability: 2,
          search: self.searchVal
        }
        return new Promise((resolve, reject) => {
          self.$utils.httpPost(this.$config.api.COOP_BATCH_SAVE_AUTHORITY, data, {
            success: (res) => {
              resolve('saveApp success')
            },
            error: (err) => {
              reject('saveApp')
            }
          })
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
@import "./authTable.scss";
</style>