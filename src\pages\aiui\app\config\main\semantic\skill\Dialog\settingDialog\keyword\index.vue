<template>
  <div>
    <p class="item-title">关键词过滤</p>
    <p class="item-desc">
      用户表述中可能包含唤醒词，此时会影响到语义理解的结果，我们建议配置过滤。
    </p>
    <div class="config-content mgb40">
      <el-tag
        class="keyword"
        :key="index"
        v-for="(keyword, index) in keywords"
        :closable="subAccountEditable"
        :disable-transitions="false"
        @close="handleKeywordClose(index)"
        >{{ keyword }}</el-tag
      >
      <el-input
        size="medium"
        class="new-keyword-input"
        v-if="newKeywordVisible"
        v-model.trim="newKeyword"
        ref="saveTagInput"
        placeholder="2-30个字符，回车添加"
        @blur="newKeywordConfirm"
        @keyup.enter.native="newKeywordConfirm"
      ></el-input>
      <div
        v-if="keywords.length < 10 && !newKeywordVisible"
        :class="[
          'button-new-keyword ib',
          { 'not-allowed': !subAccountEditable },
        ]"
        @click="showNewKeyword"
      >
        +
      </div>
    </div>
  </div>
</template>
<script>
import { mapGetters } from 'vuex'

export default {
  props: {
    show: Boolean,
  },
  data() {
    return {
      keywords: [],
      newKeywordVisible: false,
      newKeyword: '',
    }
  },
  computed: {
    ...mapGetters({
      currentScene: 'aiuiApp/currentScene',
      appInfo: 'aiuiApp/app',
      subAccountEditable: 'aiuiApp/subAccountEditable',
    }),
    appId() {
      return this.$route.params.appId
    },
  },

  watch: {
    show(val) {
      if (val) {
        this.getKeywodList() // 获取配置列表
      }
    },
  },
  methods: {
    getKeywodList() {
      let self = this
      this.$utils.httpGet(
        this.$config.api.AIUI_APP_KEYWORD_FILTER,
        {
          appid: this.appId,
          sceneId: this.currentScene.sceneBoxId,
        },
        {
          success: (res) => {
            if (res.flag) {
              if (res.data) {
                self.keywords = JSON.parse(res.data)
              }
            } else {
              self.$message.error(res.desc)
            }
          },
        }
      )
    },
    handleKeywordClose(index) {
      this.keywords.splice(index, 1)
      this.saveKeyword()
    },
    showNewKeyword() {
      if (!this.subAccountEditable) return
      this.newKeywordVisible = true
      this.$nextTick((_) => {
        this.$refs.saveTagInput.$refs.input.focus()
      })
    },
    newKeywordConfirm() {
      let newKeyword = this.newKeyword
      if (!newKeyword) {
        this.newKeywordVisible = false
        return
      }
      if (newKeyword.length < 2 || newKeyword.length > 30) {
        this.$message.warning('关键词长度为 2-30 个字符')
        return
      }

      if (!/^[a-zA-Z \u4e00-\u9fa5]+$/.test(newKeyword)) {
        this.$message.warning('关键词仅支持字母、汉字和空格')
        return
      }

      if (this.keywords.indexOf(newKeyword) > -1) {
        this.$message.warning('该关键词已存在')
        return
      }

      this.keywords.push(newKeyword.toLowerCase())
      this.newKeywordVisible = false
      this.newKeyword = ''
      this.saveKeyword()
    },

    saveKeyword() {
      let data = {
        appid: this.appId,
        sceneId: this.currentScene.sceneBoxId,
        sceneName: this.currentScene.sceneBoxName,
        keywords: JSON.stringify(this.keywords),
      }

      this.$utils.httpPost(this.$config.api.AIUI_APP_SAVE_SEMANTIC, data, {
        success: (res) => {
          if (res.flag) {
          } else {
            // self.$emit('saveFail')
          }
        },
        error: (res) => {
          //   self.$emit('saveFail')
        },
      })
    },
  },
}
</script>
<style lang="scss" scoped>
@import '../../common.scss';
.keyword {
  margin: 0 8px 8px 0;
}
.new-keyword-input {
  width: 200px;
}
.button-new-keyword {
  width: 36px;
  height: 36px;
  font-size: 24px;
  line-height: 30px;
  cursor: pointer;
  color: $primary;
  text-align: center;
  vertical-align: top;
  border: 1px solid $grey3;
  border-radius: 2px;
}
</style>
