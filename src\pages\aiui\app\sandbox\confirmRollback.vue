<template>
  <el-dialog :title="title" :visible.sync="dialog.show" width="600px">
    <div>
      <template v-if="!sceneCompare.change">
        <p class="rollback-tip mgb12">
          回滚后线上版本将被更新，请确定你要回滚至该版本：
        </p>
        <div class="version mgb36">
          <p class="version-number version-default">{{ appVersion.number }}</p>
          <p class="version-default create-time">
            {{ appVersion.createTime | date }}
          </p>
          <p
            class="version-default update-log"
            :title="appVersion.updateLog"
            v-if="appVersion.updateLog"
          >
            {{ appVersion.updateLog }}
          </p>
          <p class="version-default update-log" v-else>-</p>
        </div>
      </template>
      <template v-else>
        <div class="scene-del mgb36">
          <p>{{ messsage }}</p>
          <p>确认回滚版本吗?</p>
        </div>
      </template>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="dialog.show = false">取 消</el-button>
      <el-button class="rollback-default" @click="rollback()" :loading="saving"
        >确定回滚</el-button
      >
    </span>
  </el-dialog>
</template>

<script>
export default {
  props: {
    dialog: {
      type: Object,
      default: {},
    },
    sceneCompare: {},
    appVersion: {},
    appId: {},
  },
  data() {
    return {
      title: '回滚版本',
      messsage: '',
      saving: false,
    }
  },
  computed: {},
  watch: {
    'dialog.show': function (val, oldVal) {},
    'sceneCompare.change': function (val) {
      if (val) {
        this.title = '情景模式将丢失'
        this.getMessage()
      }
    },
  },
  mounted() {},
  methods: {
    rollback() {
      this.saving = true
      this.$utils.httpGet(
        this.$config.api.AIUI_APP_VERSION_ROLLBACK,
        {
          appid: this.appId,
          appVersionId: this.appVersion.appVersionId,
        },
        {
          success: (res) => {
            this.saving = false
            this.$message_pro_success(
              '回滚成功',
              '请在设备正式环境中验证回滚是否生效。'
            )
            this.$emit('getVersion')
            this.dialog.show = false
          },
          error: (err) => {
            this.saving = false
          },
        }
      )
    },
    getMessage() {
      let addScenes = this.sceneCompare.addScenes
      let delScenes = this.sceneCompare.delScenes
      for (let i = 0; i < addScenes.length; i++) {
        addScenes[i] = '{' + addScenes[i] + '}'
      }
      for (let i = 0; i < delScenes.length; i++) {
        delScenes[i] = '{' + delScenes[i] + '}'
      }
      if (addScenes.length > 0) {
        if (delScenes.length > 0) {
          this.messsage =
            '当前版本存在新增的情景模式' +
            addScenes.join('、') +
            '，选中版本的情景模式' +
            delScenes.join('、') +
            '已被删除；回滚版本新增情景模式将会删除，被删除情景模式将不会恢复。'
        } else {
          this.messsage =
            '当前版本存在新增的情景模式' +
            addScenes.join('、') +
            '，回滚后新增情景模式将被删除，且不可恢复。'
        }
      } else if (delScenes.length > 0) {
        this.messsage =
          '选中版本的情景模式' +
          delScenes.join('、') +
          '已被删除，回滚版本被删除情景模式将不会恢复。'
      }
    },
  },
  components: {},
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="scss" scoped>
.rollback-default {
  background-color: $primary;
  color: $white;
}
.rollback-tip {
  color: $grey6;
  height: 24px;
  font-size: 16px;
}
.version {
  font-size: 0;
}
.version-default {
  display: inline-block;
  padding: 17px 12px;
  line-height: 22px;
  font-size: 14px;
  background: $grey1;
}
.version-number {
  width: 70px;
  line-height: 22px;
  font-weight: 600;
}
.create-time {
  width: 170px;
}
.update-log {
  width: 294px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  vertical-align: top;
}
.scene-del {
  line-height: 22px;
  font-size: 14px;
}
</style>
