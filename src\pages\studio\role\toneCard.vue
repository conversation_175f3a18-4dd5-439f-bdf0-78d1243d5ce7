<template>
  <div class="tone-card" :class="cardActiveClass" @click="onCardClick">
    <div class="tone-card-header">
      <img class="tone-icon" :src="cardData.url" />
      <div class="tone-info">
        <p class="tone-name" :title="cardData.name">
          <span :class="fromApp && !cardData.id ? 'app-name' : ''">{{
            cardData.name
          }}</span>
          <span
            v-show="fromApp && !cardData.resId"
            :class="cardData.auth ? 'auth' : 'unauth'"
            >授</span
          >
          <el-tooltip
            placement="top"
            :content="cardData.resId? `resId:${cardData.resId}`:`vcn:${cardData.vcn}`"
          >
            <i
              class="ic-r-copy"
              style="color: #BEBFC8;"
              @click.stop="copyVcn"
            ></i>
          </el-tooltip>
        </p>
        <p class="tone-desc" v-if="cardData.vcn">
          <span class="tone-desc-label">{{ cardData.language }}</span>
          <span class="tone-desc-label" :title="cardData.scene">{{
            cardData.scene
          }}</span>
        </p>
      </div>

      <el-popover
        placement="right"
        trigger="hover"
        popper-class="role-card-popover"
        v-if="cardData.id"
      >
        <ul>
          <li @click.stop.prevent="delVoice">
            <a style="color: #ff4242">删除</a>
          </li>
        </ul>
        <i class="el-icon-more" slot="reference" @click.stop></i>
      </el-popover>
    </div>
    <div class="tone-card-footer">
      <img
        :src="
          cardActiveClass === 'card-active'
            ? require(`@A/images/studio-handle-platform/wave-active.png`)
            : require(`@A/images/studio-handle-platform/wave.png`)
        "
        class="wave"
      />
      <img
        :src="
          cardData.isPlay
            ? require(`@A/images/studio-handle-platform/pause.png`)
            : require(`@A/images/studio-handle-platform/play.png`)
        "
        class="audio"
        @click.stop="toggleAudio"
      />
    </div>
  </div>
</template>
<script>
export default {
  name: 'role-card',
  props: {
    cardData: {
      type: Object,
      default: () => ({
        image: '',
      }),
    },
    roleTtsData: {
      type: Object,
      default: () => ({}),
    },
    fromApp: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    cardActiveClass() {
      return (this.cardData.id &&
        this.roleTtsData.resId == this.cardData.resId) ||
        (this.cardData.vcn && this.roleTtsData.vcn == this.cardData.vcn)
        ? 'card-active'
        : ''
    },
  },
  data() {
    return {
      bindApps: [],
      bindRoles: [],
      defaultRole: {},
    }
  },
  mounted() {},
  methods: {
    toggleAudio() {
      this.$emit('playVoice')
    },
    onCardClick() {
      this.$emit('selectRole', this.cardData)
    },
    copyVcn(){
      let value = this.cardData.vcn || this.cardData.resId
      this.$utils.copyClipboard(value)
      this.$message.success('复制成功')
    },
    async delVoice() {
      const delMethod = () => {
        this.$utils.httpGet(
          this.$config.api.AIUI_ROLE_VOICE_CLONE_DELETE,
          {
            voiceCloneId: this.cardData.id,
          },
          {
            success: (res) => {
              this.$emit('getList')
              if (this.roleTtsData.resId == this.cardData.resId) {
                // 当前绑定的复刻音色被删除，刷新外面的发音人信息
                console.log('当前绑定的复刻音色被删除，刷新外面的发音人信息')
                this.$emit('refresh')
              }
            },
            error: (err) => {},
          }
        )
      }
      const checkBind = () => {
        //分为三种情况，无引用1，被应用引用2，被角色引用3
        return new Promise((resolve, reject) => {
          this.$utils.httpGet(
            this.$config.api.AIUI_ROLE_VOICE_CLONE_CHECK,
            {
              voiceCloneId: this.cardData.id,
            },
            {
              success: (res) => {
                if (!res.flag) {
                  this.$message.error(res.desc || '操作失败')
                  return reject()
                }
                this.defaultRole = res.data.default
                if (res.data.apps && res.data.apps.length > 0) {
                  this.bindApps = res.data.apps
                  return resolve(2)
                } else {
                  this.bindApps = []
                }
                if (res.data.roles && res.data.roles.length > 0) {
                  this.bindRoles = res.data.roles
                  return resolve(3)
                } else {
                  this.bindRoles = []
                }
                return resolve(1)
              },
              error: (err) => {
                reject()
              },
            }
          )
        })
      }
      let checkRes = await checkBind()
      // console.log('checkRes', checkRes);

      switch (checkRes) {
        case 1:
          //无引用
          this.$confirm(
            `<span style="color: #FF4242">删除发音人后将无法恢复。</span>是否继续删除`,
            `温馨提醒`,
            {
              confirmButtonText: '关闭',
              cancelButtonText: '继续删除',
              confirmButtonClass: 'el-button--danger',
              type: 'warning',
              showClose: false,
              dangerouslyUseHTMLString: true,
            }
          )
            .then(() => {
              //关闭
            })
            .catch(() => {
              //执行删除
              delMethod()
            })
          break
        case 2:
          //被应用引用
          this.$confirm(
            `发音人“${
              this.cardData.name
            }”被以下情景模式引用，无法删除。请您前往“我的应用”取消引用再尝试操作
              <table style="width: 100%; border-collapse: collapse; margin-top: 10px; border: 1px solid #ebeef5;font-size:13px">
                <thead>
                  <tr style="background-color: #f5f7fa;">
                    <th style="padding: 8px 12px; text-align: left; border-bottom: 1px solid #ebeef5;">应用名称</th>
                    <th style="padding: 8px 12px; text-align: left; border-bottom: 1px solid #ebeef5;">appid</th>
                    <th style="padding: 8px 12px; text-align: left; border-bottom: 1px solid #ebeef5;">引用场景</th>
                  </tr>
                </thead>
                <tbody>
                  ${this.bindApps
                    .map(
                      (app) => `
                    <tr>
                      <td style="padding: 8px 12px; border-bottom: 1px solid #ebeef5;">${
                        app.appName || '-'
                      }</td>
                      <td style="padding: 8px 12px; border-bottom: 1px solid #ebeef5;">${
                        app.appid || '-'
                      }</td>
                      <td style="padding: 8px 12px; border-bottom: 1px solid #ebeef5;">${
                        app.sceneName || '-'
                      }</td>
                    </tr>
                  `
                    )
                    .join('')}
                </tbody>
              </table>
            `,
            `温馨提醒`,
            {
              confirmButtonText: '关闭',
              showCancelButton: false,
              confirmButtonClass: 'el-button--danger',
              type: 'warning',
              showClose: false,
              dangerouslyUseHTMLString: true,
            }
          )
            .then(() => {
              //关闭
            })
            .catch(() => {})
          break
        case 3:
          //被角色引用
          this.$confirm(
            `发音人“${
              this.cardData.name
            }”已被角色<span style="color: #32C9FB">${this.bindRoles
              .map((r) => r.name)
              .join(
                ','
              )}</span>引用。删除发音人后将无法恢复，建议您自行取消引用后再删除
            <span style="color: #F18A56">如果继续删除，上述角色的发音人将更换为默认发音人“${
              this.defaultRole.name
            }”</span>
            `,
            `温馨提醒`,
            {
              confirmButtonText: '关闭',
              cancelButtonText: '我已知晓，继续删除',
              confirmButtonClass: 'el-button--danger',
              type: 'warning',
              showClose: false,
              dangerouslyUseHTMLString: true,
              closeOnClickModal: false,
              closeOnPressEscape: false,
            }
          )
            .then(() => {
              //关闭
            })
            .catch(() => {
              //执行删除
              delMethod()
            })
          break
      }
    },
  },
  watch: {
    cardData: {
      handler(newVal) {
        // console.log('cardData newVal', newVal);
      },
      deee: true,
    },
  },
}
</script>
<style lang="scss" scoped>
.tone-card {
  border: 1px solid #ebebeb;
  border-radius: 8px;
  padding: 16px 14px 11px 14px;
  min-width: 200px;
  cursor: pointer;
  &:hover{
    border: 1px solid #1890ff;
  }
  .tone-card-header {
    display: flex;
    gap: 10px;
    margin-bottom: 16px;
    width: 100%;
    .tone-icon {
      width: 56px;
      height: 56px;
      border-radius: 50%;
    }
    .tone-info {
      flex: 1;
      min-width: 0;
    }
    .tone-name {
      font-size: 16px;
      font-family: PingFang SC, PingFang SC-500;
      font-weight: 500;
      color: #000000;
      line-height: 20px;
      margin-bottom: 8px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      width: 100%;
      display: flex;
      align-items: center;
      gap: 4px;
      .auth {
        padding: 0 3px;
        background: #e5f2ff;
        border-radius: 3px;
        color: #1890ff;
        font-size: 12px;
      }
      .unauth {
        padding: 0 3px;
        background: #ffdebe;
        border-radius: 3px;
        color: #ff7d00;
        font-size: 12px;
      }
    }
    .app-name {
      display: inline-block;
      max-width: calc(100% - 30px);
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .tone-desc-label {
      display: inline-block;
      padding: 4px;
      color: #7a7a7a;
      background: #f5f5f5;
      border-radius: 3px;
      font-size: 12px;
      max-width: 80px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  .tone-card-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .wave {
      width: 182px;
      height: 21px;
      margin-right: 4px;
    }
    .audio {
      width: 23px;
      height: 22px;
      cursor: pointer;
    }
  }
}
.card-active {
  border: none !important;
  background-image: url(~@A/images/studio-handle-platform/tone-back.png);
  background-size: 100% 100%; /* 完全拉伸（可能变形） */
  background-repeat: no-repeat;
}
</style>

<style lang="scss">
.role-card-popover {
  padding: 9px !important;
  border-radius: 10px !important;
  li {
    width: 97px;
    height: 36px;
    line-height: 36px;
    padding: 0 8px;
    border-radius: 10px;
    cursor: pointer;
    &:hover {
      background: #f6f8f9;
    }
  }
}
</style>
