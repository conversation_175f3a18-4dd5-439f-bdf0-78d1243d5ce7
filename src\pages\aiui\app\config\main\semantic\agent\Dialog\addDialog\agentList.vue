<template>
  <div class="dialog_body">
    <!-- 隐藏左侧tab -->
    <!-- <div class="tab_container">
      <a
        style="cursor: pointer"
        @click="onCickTab('custom')"
        :class="{ active: activeTab === 'custom' }"
      >
        自定义
      </a>
      <a
        style="cursor: pointer"
        @click="onCickTab('official')"
        :class="{ active: activeTab === 'official' }"
      >
        官方
      </a>
    </div> -->
    <div class="agent_list_wrapper agent-list-wrapper">
      <div
        class="scroll_wrap"
        v-loading="loading"
        ref="scrollWrap"
        @scroll="handleScroll"
      >
        <!-- 隐藏左侧tab开始 -->
        <div class="agent_list agent-list">
          <div
            v-for="(item, index) in filteredAgentData"
            class="skill"
            @click="toAgentCard(item)"
            :key="index"
          >
            <div :class="['content_wrap']">
              <div class="skill_info_wrap">
                <i
                  class="skill-icon"
                  :style="{ backgroundColor: item.color }"
                  >{{ item.name && item.name.substr(0, 1) }}</i
                >
                <div class="skill-info">
                  <p class="skill-title" :title="item.name">
                    {{ item.name }}
                  </p>
                  <p class="agent-type-title">
                    {{ item.official === 1 ? '官方智能体' : '自定义' }}
                  </p>
                </div>
              </div>

              <div @click.stop class="switch-wrap switch_wrap">
                <el-switch
                  size="small"
                  :value="item.used"
                  @change="(val) => onSwitchChange(val, item)"
                >
                </el-switch>
              </div>
            </div>
          </div>
        </div>
        <!-- 隐藏左侧tab结束 -->
        <!-- 通用可复用的agent_list_container组件 -->
        <!-- <div
          class="agent_list_container"
          v-for="(agentGroup, groupIndex) in agentGroups"
          :key="groupIndex"
          :id="`agent_group_${groupIndex === 0 ? 'custom' : 'official'}`"
        >
          <div class="agent_type_title">{{ agentGroup.title }}</div>
          <div class="agent_list">
            <div
              v-for="(item, index) in agentGroup.data"
              class="skill"
              @click="toAgentCard(item)"
              :key="index"
              v-if="showAgent(item)"
            >
              <div :class="['content_wrap']">
                <div class="skill_info_wrap">
                  <i
                    class="skill-icon"
                    :style="{ backgroundColor: item.color }"
                    >{{ item.name && item.name.substr(0, 1) }}</i
                  >
                  <div class="skill-info">
                    <p class="skill-title" :title="item.name">
                      {{ item.name }}
                    </p>
                  </div>
                </div>

                <div @click.stop class="switch-wrap">
                  <el-switch
                    size="small"
                    :value="item.used"
                    @change="(val) => onSwitchChange(val, item)"
                  >
                  </el-switch>
                </div>
              </div>
            </div>
          </div>
        </div> -->
      </div>
      <!-- <p class="empty-skill-tips" v-if="clickSearchVal !== ''">暂无搜索结果</p> -->
    </div>
  </div>
</template>

<script>
export default {
  name: 'AiuiWebAgentList',

  props: {
    agentData: {
      type: Array,
      default() {
        return []
      },
    },
    agentDataCopy: {
      type: Array,
      default() {
        return []
      },
    },
    loading: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      activeTab: 'custom', // 默认激活自定义tab
      scrollDebounceTimer: null, // 滚动防抖定时器
      isManualTabClick: false, // 标记是否由手动点击触发的tab切换
      manualTabLockTimer: null, // 手动点击锁定计时器
    }
  },

  mounted() {
    // 初始化时执行一次滚动检测
    this.$nextTick(() => {
      // 先根据初始滚动位置设置正确的激活标签
      this.initActiveTab()

      // 然后执行一次滚动检测
      this.handleScroll()
    })
  },
  computed: {
    customAgentData() {
      return this.agentData.filter((item) => item.official === 0)
    },
    officialAgentData() {
      return this.agentData.filter((item) => item.official === 1)
    },
    // 将所有agent分组数据聚合到一个数组中，便于循环渲染
    agentGroups() {
      return [
        {
          title: '自定义',
          data: this.customAgentData,
        },
        {
          title: '官方',
          data: this.officialAgentData,
        },
      ]
    },
    // 添加新的计算属性，用于过滤需要显示的智能体
    filteredAgentData() {
      return this.agentData.filter((item) => this.showAgent(item))
    },
  },
  methods: {
    showAgent(item) {
      if (item.official === 1) {
        return item.isShow ? true : false
      } else {
        return true
      }
    },
    toAgentCard(item) {
      window.open(`/agent/legacy/${item.agentId}/info`, '_blank')
    },
    onSwitchChange(val, item) {
      console.log(val, item.agentId, '智能体的onSwitchChange')
      this.$emit('selectchange', item, val)
    },
    // 初始化激活标签状态
    initActiveTab() {
      // 获取滚动容器的当前滚动位置
      const scrollTop = this.$refs.scrollWrap
        ? this.$refs.scrollWrap.scrollTop
        : 0

      // 如果滚动位置为0或接近顶部，激活自定义标签；否则检测当前视图
      if (scrollTop < 20) {
        this.activeTab = 'custom'
      } else {
        // 执行一次完整的滚动位置检测
        this.updateActiveTabByScroll(true)
      }
    },
    // 点击tab时滚动到对应区域
    onCickTab(tabKey) {
      // 立即更新激活状态，避免视觉延迟
      this.activeTab = tabKey

      // 设置手动点击标志，防止滚动事件立即覆盖点击设置的状态
      this.isManualTabClick = true

      // 清除之前的锁定计时器
      if (this.manualTabLockTimer) {
        clearTimeout(this.manualTabLockTimer)
      }

      const targetElement = document.getElementById(`agent_group_${tabKey}`)
      if (targetElement) {
        // 修改滚动逻辑，添加适当的偏移量确保标题完全可见
        // 考虑顶部padding和其他可能的元素高度
        const scrollOffset = 140 // 调整为更合适的值，确保标题区域完全可见
        this.$refs.scrollWrap.scrollTo({
          top: targetElement.offsetTop - scrollOffset,
          behavior: 'smooth',
        })

        // 锁定一段时间，防止滚动事件立即覆盖点击设置的状态
        // 平滑滚动大约需要500ms，所以设置为700ms的锁定时间
        this.manualTabLockTimer = setTimeout(() => {
          this.isManualTabClick = false
        }, 700)
      }
    },
    // 监听滚动事件，动态更新activeTab
    handleScroll() {
      return
      // 如果是由手动点击触发的，则不执行滚动更新
      if (this.isManualTabClick) return

      // 添加防抖处理，避免频繁触发
      if (this.scrollDebounceTimer) {
        clearTimeout(this.scrollDebounceTimer)
      }

      this.scrollDebounceTimer = setTimeout(() => {
        this.updateActiveTabByScroll()
      }, 50) // 50ms的防抖延迟
    },
    // 辅助函数：计算元素在滚动容器中的可见程度
    calculateVisibility(element, scrollTop, containerHeight) {
      if (!element) return 0

      const rect = element.getBoundingClientRect()
      const scrollRect = this.$refs.scrollWrap.getBoundingClientRect()

      // 元素顶部相对于滚动容器的位置
      const elementTop = element.offsetTop - scrollTop
      const elementHeight = element.offsetHeight

      // 计算元素在视窗内的部分
      let visibleHeight = 0

      if (elementTop < 0) {
        // 元素顶部在视窗上方
        visibleHeight = Math.max(0, elementHeight + elementTop)
      } else if (elementTop < containerHeight) {
        // 元素顶部在视窗内
        visibleHeight = Math.min(containerHeight - elementTop, elementHeight)
      }

      // 返回可见百分比
      return visibleHeight / elementHeight
    },
    // 根据滚动位置更新激活的标签
    updateActiveTabByScroll(isInitCall = false) {
      // 如果是由手动点击触发的，则不执行滚动更新
      if (this.isManualTabClick && !isInitCall) return

      // 获取滚动容器的当前滚动位置和大小
      const scrollTop = this.$refs.scrollWrap.scrollTop
      const scrollHeight = this.$refs.scrollWrap.clientHeight
      const scrollContainerHeight = this.$refs.scrollWrap.scrollHeight

      // 检测是否已滚动到顶部或接近顶部(更精确的判断)
      const isAtTop = scrollTop === 0
      const isNearTop = scrollTop <= 20

      // 检测是否已滚动到底部或接近底部
      // 使用更宽松的判断条件，当距离底部50px内时就认为是接近底部
      const isAtBottom = scrollTop + scrollHeight >= scrollContainerHeight - 1
      const isNearBottom =
        scrollTop + scrollHeight >= scrollContainerHeight - 50

      // 获取各个分组的位置
      const officialGroup = document.getElementById('agent_group_official')
      const customGroup = document.getElementById('agent_group_custom')

      if (!officialGroup || !customGroup) return

      // 计算各组的可见度
      const officialVisibility = this.calculateVisibility(
        officialGroup,
        scrollTop,
        scrollHeight
      )
      const customVisibility = this.calculateVisibility(
        customGroup,
        scrollTop,
        scrollHeight
      )

      // 获取各个组标题在视口中的位置
      const officialTitle = officialGroup.querySelector('.agent_type_title')
      const customTitle = customGroup.querySelector('.agent_type_title')

      const officialTitleVisible = officialTitle
        ? this.isElementVisibleInViewport(officialTitle, this.$refs.scrollWrap)
        : false

      const customTitleVisible = customTitle
        ? this.isElementVisibleInViewport(customTitle, this.$refs.scrollWrap)
        : false

      // 检查是否可以看到官方组的内容
      const canSeeOfficialContent = officialVisibility > 0

      // 最顶部特殊处理：如果在最顶部，直接激活自定义标签
      if (isAtTop) {
        if (this.activeTab !== 'custom') {
          this.activeTab = 'custom'
        }
        return
      }

      // 最底部特殊处理：如果在最底部或接近底部，且能看到官方内容，直接激活官方标签
      if ((isAtBottom || isNearBottom) && canSeeOfficialContent) {
        if (this.activeTab !== 'official') {
          this.activeTab = 'official'
        }
        return
      }

      // 自定义标签激活条件:
      // 1. 已滚动到顶部或接近顶部
      // 2. 自定义分组可见度大于官方分组且超过阈值
      // 3. 自定义标题可见且官方标题不可见
      if (
        isNearTop ||
        (customVisibility > officialVisibility && customVisibility > 0.3) ||
        (customTitleVisible && !officialTitleVisible)
      ) {
        if (this.activeTab !== 'custom') {
          this.activeTab = 'custom'
        }
      }
      // 官方标签激活条件:
      // 1. 已滚动到底部或接近底部
      // 2. 官方分组可见度大于自定义分组且超过阈值
      // 3. 官方组标题在视口中可见，且自定义标题不可见
      // 4. 可以看到任何官方内容，且已经滚过自定义内容的大部分
      else if (
        isNearBottom ||
        (officialVisibility > customVisibility && officialVisibility > 0.3) ||
        (customVisibility < 0.2 && officialVisibility > 0) ||
        (officialTitleVisible && !customTitleVisible) ||
        (canSeeOfficialContent && customVisibility < 0.5)
      ) {
        if (this.activeTab !== 'official') {
          this.activeTab = 'official'
        }
      }
    },
    // 判断元素是否在滚动容器的可视区域内
    isElementVisibleInViewport(element, container) {
      if (!element || !container) return false

      const containerRect = container.getBoundingClientRect()
      const elementRect = element.getBoundingClientRect()

      // 元素顶部在容器可视区域内
      const topVisible =
        elementRect.top >= containerRect.top &&
        elementRect.top < containerRect.bottom

      // 元素底部在容器可视区域内
      const bottomVisible =
        elementRect.bottom > containerRect.top &&
        elementRect.bottom <= containerRect.bottom

      // 元素完全包含容器可视区域
      const elementContainsViewport =
        elementRect.top <= containerRect.top &&
        elementRect.bottom >= containerRect.bottom

      return topVisible || bottomVisible || elementContainsViewport
    },
  },
}
</script>

<style lang="scss" scoped>
@import '../common.scss';

.skill-info {
  width: 50%;
  .agent-type-title {
    font-size: 12px;
    padding-top: 5px;
    color: #bebfc8;
  }
}

.empty-skill-tips {
  color: $grey5;
  margin-top: 80px;
  width: 100%;
  text-align: center;
}

// 隐藏左侧tab
.agent-list-wrapper {
  padding: 70px 32px 0 32px !important;
  .agent_list {
    display: grid;
    grid-template-columns: repeat(3, 1fr); /* 将容器分为3列，每列宽度平均 */
    gap: 18px; /* 设置格子之间的间距 */
    .content_wrap {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .skill_info_wrap {
        flex: 1;
        display: flex;
        align-items: center;
      }
      .switch_wrap {
        top: 14px;
      }
    }
  }
}
</style>
