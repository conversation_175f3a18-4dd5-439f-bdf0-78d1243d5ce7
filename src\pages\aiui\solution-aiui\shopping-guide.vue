<template lang="html">
  <div class="aiui">
    <div class="banner-wrap">
      <div class="banner-content">
        <p class="title">商场导购解决方案</p>
        <p class="desc">构建完善的商超语音交互场景，让你的智能设备<br>能听会说，更懂用户</p>
        <a class="btn" href="/store/skill/143416?type=2" target="_blank">立即体验</a>
        <p class="contect-info">更多咨询，请联系商务************************</p>
      </div>
    </div>
    <div class="product-section">
      <p class="section-title">产品介绍</p>
      <div class="product-list">
        <div class="product-item">
          <div class="item-icon icon-1"></div>
          <div class="item-title">快速接入</div>
          <div class="item-desc">用户在AIUI平台创建应用、绑定<br>商场导购技能即可体验并使用技能</div>
        </div>
        <div class="product-item">
          <div class="item-icon icon-2"></div>
          <div class="item-title">精准匹配用户意图</div>
          <div class="item-desc">支持用户输入语音文本，匹配用<br>户意图，请求合适的语义理解，<br>返回给用户图文并茂的内容</div>
        </div>
        <div class="product-item">
          <div class="item-icon icon-3"></div>
          <div class="item-title">海量技能说法</div>
          <div class="item-desc">提供海量商场导购说法，包含品牌、<br>品类、单品咨询、商铺导航、展示地<br>图、活动咨询等。支持额外定制意<br>图，新增未覆盖说法 </div>
        </div>
        <div class="product-item">
          <div class="item-icon icon-4"></div>
          <div class="item-title">精准识别品牌词</div>
          <div class="item-desc">精准识别商超类品牌词汇，实现<br>语音文字间转化，支持定制化识别<br>引擎。提供额外定制识别效果<br>服务</div>
        </div>
      </div>
    </div>
    <div class="use-case-section">
      <p class="section-title">应用场景</p>
      <div style=" margin: 0 auto; width: 1200px">
        <div class="use-case-item case-1">
          <p class="item-title">智能大屏</p>
          <p class="item-desc">接入AIUI平台即可通过智能大屏提供商场地图导视、<br>消费导购、信息查询、品牌宣传、语音交互等功能 </p>
        </div>
        <div class="use-case-item case-2">
          <p class="item-title">机器人</p>
          <p class="item-desc">机器人接入AIUI平台即可实现商场咨询导购信息查<br>询，品类、品牌咨询，查看地图，导航带路等功能 </p>
        </div>
        <div class="use-case-item case-3">
          <p class="item-title">手机端</p>
          <p class="item-desc">手机APP、小程序、公众号接入AIUI平台，进行商场<br>导购，品牌查询，查找优惠活动，查看地图等 </p>
        </div>
      </div>
    </div>
    <div class="cooperation-case-section">
      <p class="section-title" style="color: #fff;">合作案例</p>
      <div class="cooperation-case-list">
        <div class="case-item">
          家居Mall场景的智能导购机器人，全双工语音交互，提升导购的便捷度，实现快速搜索商场活动、咨询导购、家居类品牌优惠、查看地图、导航带路等功能，支持多轮交互理解用户需求、搜索结果语音播报等类人化交流。
        </div>
        <div class="case-item case-2">
          Shopping Mall场景的智能导购机器人，为顾客提供商场活动查询，单品查询、查看地图等功能，此外与顾客进行多轮交互，闲聊，互动娱乐等功能。
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  layout: 'aiuiHome',
}
</script>
<style lang="scss" scoped>
  .banner-wrap {
    height: 530px;
    background: linear-gradient(106deg, #409dec, #162a92);

  }
  .banner-content {
    margin: 0 auto;
    padding: 166px 120px 0;
    width: 1200px;
    height: 100%;
    background: url('../../../assets/images/solutions/shopping-guide/banner.png') 375px bottom no-repeat;
  }
  .title {
    margin-bottom: 21px;
    letter-spacing: 1px;
    font-size: 46px;
    color: #fff;
  }
  .desc {
    line-height: 32px;
    font-size: 18px;
    color: #fff;
  }
  .btn {
    display: block;
    margin: 23px 0 28px;
    width: 125px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    color: #3e90e4;
    border-radius: 5px;
    background: #fff;
  }
  .contect-info {
    font-size: 12px;
    color: #fff;
  }

  .product-section {
    height: 555px;
    .product-list {
      margin: 0 auto;
      width: 1200px;
    }
    .product-item {
      display: inline-block;
      vertical-align: top;
      width: 24%;
      height: 350px;
      text-align: center;
    }
    .item-icon {
      margin: 0 auto 30px;
      width: 120px;
      height: 120px;
      border-radius: 120px;
    }
    .item-title {
      margin-bottom: 25px;
      font-size: 22px;
      color: #333;
    }
    .item-desc {
      line-height: 1.5;
      color: #888;
    }
    .icon-1 {
      background: url('../../../assets/images/solutions/shopping-guide/product-1.png') center no-repeat #f6f6f6;
    }
    .icon-2 {
      background: url('../../../assets/images/solutions/shopping-guide/product-2.png') center no-repeat #f6f6f6;
    }
    .icon-3 {
      background: url('../../../assets/images/solutions/shopping-guide/product-3.png') center no-repeat #f6f6f6;
    }
    .icon-4 {
      background: url('../../../assets/images/solutions/shopping-guide/product-4.png') center no-repeat #f6f6f6;
    }
  }
  .section-title {
    padding: 60px 0;
    text-align: center;
    font-size: 34px;
    letter-spacing: 3px;
    color: #393939;
  }

  .use-case-section {
    height: 617px;
    font-size: 0;
    background: #f5f6f7;
    .use-case-item {
      display: inline-block;
      margin-right: 15px;
      width: 390px;
      height: 300px;
      background: #fff;
      &:last-child {
        margin-right: 0;
      }
    }
    .case-1 {
      background: url('../../../assets/images/solutions/shopping-guide/use-case-1.png') top no-repeat #fff;
    }
    .case-2 {
      background: url('../../../assets/images/solutions/shopping-guide/use-case-2.png') top no-repeat #fff;
    }
    .case-3 {
      background: url('../../../assets/images/solutions/shopping-guide/use-case-3.png') top no-repeat #fff;
    }
    .item-title {
      margin: 116px 0 70px;
      text-align: center;
      font-size: 22px;
      color: #fff;
    }
    .item-desc {
      padding-left: 34px;
      line-height: 1.5;
      font-size: 14px;
      color: #666;
    }
  }

  .cooperation-case-section {
    height: 555px;
    background: url('../../../assets/images/solutions/shopping-guide/cooperation-case-bg.png') center no-repeat;
    .cooperation-case-list {
      margin: 0 auto;
      padding: 60px 45px 60px 68px;
      // padding: 60px 68px;
      width: 1140px;
      height: 330px;
      border-radius: 5px;
      background: #fff;
    }
    .case-item {
      margin-bottom: 70px;
      padding-top: 14px;
      padding-left: 240px;
      height: 65px;
      line-height: 1.5;
      color: #666;
      background: url('../../../assets/images/solutions/shopping-guide/cooperation-1.png') left top no-repeat;
    }
    .case-2 {
      margin-bottom: 0;
      background: url('../../../assets/images/solutions/shopping-guide/cooperation-2.png') -12px top no-repeat;
    }
  }
</style>
