<template>
  <div class="container">
    <ul
      v-if="data && data.length > 0"
      class="confirm-list"
      :style="{ paddingBottom: data.length < max ? '0px' : '15px' }"
    >
      <li v-for="(item, index) in data" :key="item.answer">
        <span class="number-label">{{ index + 1 }}</span>
        <!-- <span>{{ item }}</span> -->
        <inteligient-rich-input
          :placeholder="editPlaceholder"
          :value="{
            text: item.answer,
            labels: item.labels,
            changed: item.changed,
          }"
          :showAdd="false"
          :showSwitch="false"
          :hasSlot="false"
          :disabled="disabled"
          :edit="true"
          :editIndex="index"
          @onEdit="onEdit"
          @change="onChange(index)"
          :saveOnBlur="true"
        >
        </inteligient-rich-input>
        <i
          class="delete ic-r-delete"
          @click="onDelClick(item)"
          v-if="!disabled"
        ></i>
      </li>
    </ul>
    <div class="confirm-adder" v-show="data.length < max">
      <span class="number-label">{{ data.length + 1 }}</span>
      <!-- <el-input
        v-model="text"
        @change="onChange"
        placeholder=" 使用“{”符号引用槽位，最多添加5条，每条不超过50字，回车新增。例：你是不是要问{city}的天气"
      ></el-input> -->
      <!-- <inteligient-input
        v-model="text"
        @onAdd="onAdd"
        :placeholder="placeholder"
      ></inteligient-input> -->
      <inteligient-rich-input
        v-model="textObj"
        :showAdd="true"
        :showSwitch="false"
        @onAdd="onAdd"
        :placeholder="placeholder"
        ref="intelInput"
        :hasSlot="false"
        :disabled="disabled"
      >
      </inteligient-rich-input>
    </div>
  </div>
</template>
<script>
import InteligientRichInput from '../referSlots/inteligientRichInput.vue'

export default {
  name: 'qa-text-adder',
  components: { InteligientRichInput },

  data() {
    return {
      textObj: { text: '', labels: [] },
    }
  },
  props: {
    data: Array,
    disabled: Boolean,
    reg: {
      default: '',
    },
    warning: {
      type: String,
      default: '输入有误',
    },
    editPlaceholder: String,
    placeholder: String,
    max: {
      type: Number,
      default: 5,
    },
  },

  methods: {
    onDelClick(text) {
      this.$emit('del', text)
    },
    // onChange() {
    //   this.$emit('add', this.text)
    //   this.text = ''
    // },
    onAdd(textObj) {
      const val = textObj.text.trim()
      if (!val) {
        return this.$message.warning('输入不能为空')
      }
      const idx = this.data.findIndex((item) => item.answer === val)
      if (idx > -1) {
        return this.$message.warning('不能重复添加')
      }
      if (this.reg && !this.reg.test(val)) {
        return this.$message.warning(this.warning)
      }

      this.$emit('add', { answer: textObj.text, labels: textObj.labels })
      // this.$refs.intelInput.clearHTML()
      // this.textObj = { text: '', labels: [] }
    },
    resetInitialStatus() {
      this.$refs.intelInput.clearHTML()
      this.textObj = { text: '', labels: [] }
    },
    onEdit(textObj, index) {
      const val = textObj.text.trim()
      if (!val) {
        return this.$message.warning('输入不能为空')
      }
      // const idx = this.data.findIndex((item) => item.answer === val)
      // if (idx > -1) {
      //   return this.$message.warning('不能重复添加')
      // }
      if (this.reg && !this.reg.test(val)) {
        return this.$message.warning(this.warning)
      }

      this.$emit(
        'edit',
        { answer: textObj.text, labels: textObj.labels },
        index
      )
    },
    onChange(index) {
      console.log('receive onChange', index)
      if (index !== -1) {
        this.$emit('change', index)
      }
    },
  },
}
</script>
<style lang="scss" scoped>
.container {
  width: 100%;
  border: 1px solid #d5d8de;
  padding: 0 16px;
  .confirm-adder {
    display: flex;
    align-items: center;
    margin-top: 18px;
  }
  .confirm-list {
    padding-top: 15px;
    > li {
      display: flex;
      align-items: center;
      position: relative;
      padding-right: 20px;
      &:hover {
        .delete {
          display: block;
        }
      }
      .delete {
        position: absolute;
        right: 0;
        color: #b8babf;
        cursor: pointer;
        display: none;
        color: #1784e9;
        font-size: 20px;
        // &:hover {
        //   color: #1784e9;
        // }
      }
    }
    li + li {
      margin-top: 18px;
    }
    margin-bottom: 0;
  }
  .number-label {
    margin-right: 20px;
    color: #b8babf;
  }
}
</style>
