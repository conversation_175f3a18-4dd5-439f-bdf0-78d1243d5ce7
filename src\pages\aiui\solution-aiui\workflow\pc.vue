<template>
  <div class="main-content">
    <banner
      :src="'solution/workflow/img_intelligent_workflow_banner.png'"
      @jump="toConsole"
    >
      <template v-slot:title> 企业智能工作流解决方案 </template
      ><template>
        拖拽式图形化工作流设计器，灵活高可配，快速搭建企业内外部多样化协同<br />
        流程，助力企业数字化升级。
      </template></banner
    >
    <section class="section section-1">
      <div class="section-title">
        <i class="arrow arrow-left"></i
        ><span class="section-title-bold">应用场景</span
        ><i class="arrow arrow-right"></i>
      </div>
      <!-- <div class="section-desc" style="text-align: center">
        基于婴儿啼哭检测能力，配合周边设备震动，闪烁，短信等，第一时间提醒父母
      </div> -->
      <ul class="product-list">
        <li
          v-for="(item, index) in productList"
          :key="index"
          :class="item.klass"
        >
          <h1>{{ item.name }}</h1>
          <!-- <div class="desc-wrap">
            <h2>{{ item.name }}</h2>
            <p>
              {{ item.desc }}
            </p>
          </div> -->
          <!-- <div class="overlay"></div> -->
        </li>
      </ul>
    </section>
    <section class="section section-7">
      <div class="section-title">
        <i class="arrow arrow-left"></i
        ><span class="section-title-bold">方案构成</span
        ><i class="arrow arrow-right"></i>
      </div>

      <div class="section-item">
        <ul>
          <li class="cell">
            <div class="head-image image-1"></div>
            <div class="cell-desc">
              <p>拖拽式表单设计器</p>
              <p>为企业提供多元、高效的业务流<br />程表单配置平台</p>
            </div>
          </li>
          <li class="cell">
            <div class="head-image image-2"></div>
            <div class="cell-desc">
              <p>图形化业务流程配置引擎</p>
              <p>敏捷响应业务更迭，提升企业流<br />程管理水平</p>
            </div>
          </li>
          <li class="cell">
            <div class="head-image image-3"></div>
            <div class="cell-desc">
              <p>跨组织业务协同</p>
              <p>整合企业内组织及上下游供应<br />商，实现业务全流程管理</p>
            </div>
          </li>
          <li class="cell">
            <div class="head-image image-4"></div>
            <div class="cell-desc">
              <p>多角色任务视图</p>
              <p>
                灵活设置领导、供应商等多种角<br />色视图，任务处理更快捷直观
              </p>
            </div>
          </li>
          <li class="cell">
            <div class="head-image image-5"></div>
            <div class="cell-desc">
              <p>多维数据分析</p>
              <p>直观的业务办理效率、完成率及<br />供应商绩效等数据展现</p>
            </div>
          </li>
          <li class="cell">
            <div class="head-image image-6"></div>
            <div class="cell-desc">
              <p>多终端访问</p>
              <p>支持PC端、微信小程序等多种<br />方式访问系统，操作更便捷</p>
            </div>
          </li>
        </ul>
      </div>
    </section>
    <section class="section section-3">
      <div>
        <div class="section-title">
          <i class="arrow arrow-left"></i
          ><span class="section-title-bold">方案优势</span
          ><i class="arrow arrow-right"></i>
        </div>
        <ul class="advantage">
          <li>
            <div class="ad ad1"></div>
            <p>灵活可扩展</p>
            <p>丰富的拖曳式元件，可广泛应用于<br />各种业务场景</p>
          </li>
          <li>
            <div class="ad ad2"></div>
            <p>多系统打通、数据闭环</p>
            <p>支持企业原有系统的数据导入，消<br />除跨系统的数据孤岛</p>
          </li>
          <li>
            <div class="ad ad3"></div>
            <p>工作流+RPA数字员工双拳组合</p>
            <p>联合定制的RPA数字员工服务，完<br />成业务的自动化操作</p>
          </li>
          <li>
            <div class="ad ad4"></div>
            <p>部署方式多样</p>
            <p>提供云服务、私有化部署等多种方式</p>
          </li>
        </ul>
      </div>
    </section>
    <section class="section section-8">
      <div class="section-title">
        <i class="arrow arrow-left"></i
        ><span class="section-title-bold">合作案例</span
        ><i class="arrow arrow-right"></i>
      </div>

      <div class="section-item">
        <div class="left-part"></div>
        <div class="right-part">
          <p>车险协赔平台</p>
          <p>
            险企内部员工通过车险协赔平台联合外部协赔员或第三方服务单位，实现外修询价、残值询价、配件询
            价、协作估损、协作查勘、律师外聘等业务协同办理。实现派工、资料收集、案件跟踪、审核、评价一
            体化理赔辅助平台。对第三方服务单位，支持集中询价，并根据智能竞价策略，自动匹配最优服务商，
            并对服务商进行跟踪、监控和评价，服务商业绩表现多维度呈现。后台审核端，对上述外部渠道来源的
            信息和服务进行审核，并最终确定理赔方案，完成理赔资料及流程，与理赔核心系统对接，实现无纸化、
            线上化理赔服务，提升理赔运营效率。
          </p>
        </div>
      </div>
    </section>
    <corp @jump="toConsole">
      <template> 提交信息，我们会尽快与您联系</template>
    </corp>
  </div>
</template>

<script>
import banner from '@P/aiui/solution-aiui/components/banner.vue'
import corp from '@P/aiui/solution-aiui/components/corp.vue'

export default {
  layout: 'aiuiHome',
  data() {
    return {
      productList: [
        {
          name: '金融',
          desc: '',
          klass: 'img_domestic_cellphone',
        },
        {
          name: '交通',
          desc: '',
          klass: 'img_smartwatch',
        },
        {
          name: '制造业',
          desc: '',
          klass: 'img_instrument_smar_bracelet',
        },
        {
          name: '建筑业',
          desc: '',
          klass: 'img_sports_camera',
        },
        {
          name: '政务',
          desc: '',
          klass: 'img_smart_helmet',
        },
      ],
    }
  },
  methods: {
    toConsole() {
      // 判断是否有百度SEM营销参数，有的话，提交表单页需要带上
      const search = window.location.search
      if (search) {
        window.open(`/solution/apply/20${search}`)
      } else {
        window.open('/solution/apply/20')
      }
    },
  },
  components: {
    banner,
    corp,
  },
}
</script>

<style lang="scss" scoped>
.main-content {
  .section {
    p,
    ul {
      margin-bottom: 0;
    }
    max-width: 1200px;
    overflow: hidden;
    margin: 0 auto;
    .section-title {
      text-align: center;
      font-size: 34px;
      font-family: SourceHanSansSC-Medium, SourceHanSansSC;
      font-weight: bold;
      color: #333;
      line-height: 34px;
      position: relative;
      width: 200px;
      margin: 0 auto;
      .arrow {
        width: 160px;
        height: 8px;
        display: inline-block;
        background-repeat: no-repeat;
        background-size: contain;
        position: absolute;
        z-index: 1;
      }
      .arrow-left {
        background-position: left;
        background-image: url(~@A/images/solution/offline/img_title_01.png);
        top: 50%;
        left: -160px;
      }
      .arrow-right {
        background-position: right;
        background-image: url(~@A/images/solution/offline/img_title_02.png);
        top: 50%;
        right: -160px;
      }
      .arrow-left1 {
        background-position: left;
        background-image: url(~@A/images/solution/wakeup/img_access_title_01.png);
        top: 50%;
        left: -160px;
      }
      .arrow-right1 {
        background-position: right;
        background-image: url(~@A/images/solution/wakeup/img_access_title_02.png);
        top: 50%;
        right: -160px;
      }
    }
    .section-desc {
      text-align: left;
      margin-top: 40px;
      font-size: 16px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #999999;
    }
    .section-title-bold {
      font-size: 34px;
      font-family: Microsoft YaHei;
      font-weight: bold;
      color: #333333;
    }
    .section-title-spec {
      font-size: 30px;
      font-weight: 500;
      color: #656565;
      line-height: 42px;
    }
    .section-tabs {
      margin-top: 70px;
      ul {
        display: flex;
        justify-content: center;
      }
      li {
        font-size: 20px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #666;
        line-height: 30px;
        position: relative;
        cursor: pointer;
        &:hover {
          color: #1f90fe;
        }
        &.active {
          color: #1f90fe;
          &::before {
            position: absolute;
            content: ' ';
            z-index: 1;
            width: 180px;
            height: 4px;
            background: #1f90fe;
            border-radius: 2px;
            bottom: -32px;
            left: -48px;
          }
          &::after {
            position: absolute;
            content: ' ';
            display: inline-block;
            width: 0;
            height: 0;
            bottom: -100px;
            left: 50%;
            transform: translateX(-50%);
            border: 46px solid;
            border-color: transparent transparent #f4f7f9;
          }
        }
      }
      li + li {
        margin-left: 165px;
      }
    }
    .section-sub-title {
      text-align: center;
      font-size: 18px;
      font-weight: 400;
      color: #777777;
      line-height: 25px;
      margin-top: 42px;
    }
    .section-sub-title-spec {
      font-size: 17px;
      font-weight: 400;
      color: #999999;
      line-height: 28px;
      margin-top: 18px;
    }

    .product-list {
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 50px auto 0;

      li {
        position: relative;
        text-align: center;
        width: 230px;
        height: 328px;

        .desc-wrap {
          padding-top: 39px;
        }
        .overlay {
          display: none;
          width: 100%;
          height: 100%;
          // background: rgba(0, 0, 0, 0.3);
          background-image: linear-gradient(
            0deg,
            rgb(0, 54, 255) 0%,
            rgb(39, 12, 73) 100%
          );
          opacity: 0.502;
          position: absolute;
          top: 0;
          left: 0;
          z-index: 1;
        }

        h1 {
          // display: none;
          text-align: left;
          max-width: 25px;
          font-size: 24px;
          font-family: Microsoft YaHei;
          font-weight: 400;
          color: #ffffff;
          line-height: 30px;
          margin: 0 auto;
          position: relative;
          top: 50%;
          transform: translateY(-50%);
        }
        h2 {
          // display: none;
          text-align: center;
          // padding-top: 178px;
          // padding-left: 35px;
          font-size: 21px;
          // font-weight: bold;
          color: #ffffff;
          line-height: 21px;
        }
        p {
          // display: none;
          margin-top: 32px;
          width: 232px;
          font-size: 16px;
          font-weight: 400;
          color: #ffffff;
          line-height: 32px;
          padding-left: 35px;
          text-align: left;
        }

        &.img_domestic_cellphone {
          background: url(~@A/images/solution/workflow/img_financial.png)
            center/100% no-repeat;
        }
        &.img_smartwatch {
          background: url(~@A/images/solution/workflow/img_traffic.png)
            center/100% no-repeat;
        }
        &.img_instrument_smar_bracelet {
          background: url(~@A/images/solution/workflow/img_manufacturing.png)
            center/100% no-repeat;
        }
        &.img_sports_camera {
          background: url(~@A/images/solution/workflow/img_building.png)
            center/100% no-repeat;
        }
        &.img_smart_helmet {
          background: url(~@A/images/solution/workflow/img_government.png)
            center/100% no-repeat;
        }
      }

      li + li {
        margin-left: 16px;
      }
    }
  }

  .section-1 {
    margin-top: 110px;
  }

  .section-3 {
    max-width: unset;
    padding: 110px 0 5px 0;
    > div {
      margin: 0 auto;
    }

    p {
      margin-bottom: 0;
    }
    .advantage {
      margin-top: 84px;
      display: flex;
      justify-content: center;

      > li {
        text-align: center;
        width: 260px;
        > div {
          margin: 0 auto;
        }
        p {
          margin-bottom: 0;
          &:first-of-type {
            font-size: 18px;
            font-family: Microsoft YaHei;
            font-weight: bold;
            color: #333333;
            margin-top: 40px;
          }
          &:last-of-type {
            font-size: 16px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            color: #999999;
            line-height: 30px;
            margin-top: 22px;
          }
        }
        .ad {
          width: 107px;
          height: 106px;
        }
        .ad1 {
          background: url(~@A/images/solution/workflow/icon_expand.png) bottom
            center/100% no-repeat;
        }
        .ad2 {
          background: url(~@A/images/solution/workflow/icon_closed_loop.png)
            bottom center/100% no-repeat;
        }
        .ad3 {
          background: url(~@A/images/solution/workflow/icon_pra.png) bottom
            center/100% no-repeat;
        }
        .ad4 {
          background: url(~@A/images/solution/workflow/icon_cloud_deployment.png)
            bottom center/100% no-repeat;
        }
      }

      li + li {
        margin-left: 23px;
      }
    }
  }

  .section-7 {
    width: 100%;
    max-width: 100%;
    margin-top: 100px;
    background: #f4f7f9;

    padding: 110px 0 109px 0;
    .section-title {
      color: #fff;
    }
    .section-item {
      margin-top: 70px;

      > ul {
        display: flex;
        align-items: center;
        justify-content: center;
        max-width: 1200px;
        flex-wrap: wrap;
        margin: 0 auto;
        li {
          position: relative;
        }
        li + li {
          // margin-left: 30px;
        }
        li:nth-child(n + 4) {
          margin-top: 50px;
        }
        li:nth-child(3n + 3) {
          margin-left: 30px;
        }
        li:nth-child(3n + 2) {
          margin-left: 30px;
        }
        .cell {
          width: 375px;
          border-radius: 15px;
          .head-image {
            width: 100%;
            height: 220px;
          }
          .image-1 {
            background: url(~@A/images/solution/workflow/img_form_design.png)
              center/100% no-repeat;
          }
          .image-2 {
            background: url(~@A/images/solution/workflow/img_configuration_engine.png)
              center/100% no-repeat;
          }
          .image-3 {
            background: url(~@A/images/solution/workflow/img_coordination.png)
              center/100% no-repeat;
          }
          .image-4 {
            background: url(~@A/images/solution/workflow/img_task_view.png)
              center/100% no-repeat;
          }
          .image-5 {
            background: url(~@A/images/solution/workflow/img_data_analysis.png)
              center/100% no-repeat;
          }
          .image-6 {
            background: url(~@A/images/solution/workflow/img_multi_terminal.png)
              center/100% no-repeat;
          }
          .cell-desc {
            padding: 20px;
            background: #fff;
            border-bottom-right-radius: 15px;
            border-bottom-left-radius: 15px;
            border-right: 1px solid #ddd;
            border-left: 1px solid #ddd;
            border-bottom: 1px solid #ddd;
            text-align: center;
            p {
              margin-bottom: 0;
              &:first-child {
                font-size: 16px;
                font-family: Microsoft YaHei;
                font-weight: bold;
                color: #333333;
                line-height: 16px;
              }
              &:last-child {
                font-size: 14px;
                font-family: Microsoft YaHei;
                color: #999;
                line-height: 30px;
                margin-top: 22px;
                text-align: left;
                padding-left: 70px;
              }
            }
          }
          border: 1px solid transparent;
          &:hover {
            border: 1px solid #1f90fe;
            box-shadow: 0px 0px 5px 0px rgba(45, 153, 255, 0.8);
          }
        }
      }
    }
  }

  .section-8 {
    width: 100%;
    max-width: 100%;
    margin-top: 100px;
    background: #f4f7f9;

    padding: 110px 0 109px 0;
    .section-title {
      color: #fff;
    }
    .section-item {
      width: 1200px;
      margin: 0 auto;
      display: flex;
      height: 275px;
      background: #ffffff;
      box-shadow: 0px 5px 20px 0px rgba(165, 165, 165, 0.3);
      border-radius: 15px;
      margin-top: 98px;
      .left-part {
        background: url(~@A/images/solution/workflow/img_case.png) center/100%
          no-repeat;
        width: 390px;
        height: 100%;
      }
      .right-part {
        flex: 1;
        padding: 18px 40px 0 40px;
        p:first-child {
          font-size: 20px;
          font-family: Microsoft YaHei;
          font-weight: bold;
          color: $primary;
          line-height: 30px;
        }
        p:last-child {
          font-size: 16px;
          font-family: Microsoft YaHei;
          font-weight: 400;
          color: #999999;
          line-height: 32px;
          margin-top: 14px;
        }
      }
    }
  }
}
</style>
