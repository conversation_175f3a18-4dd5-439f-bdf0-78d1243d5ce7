<template>
  <el-dialog
    :title="`创建文档问答库`"
    :visible.sync="dialog.show"
    width="480px"
  >
    <el-form :model="form" :rules="rules" ref="qaForm" label-position="top">
      <el-form-item
        label="知识构建策略"
        style="margin-bottom: 10px"
        prop="type"
      >
        <el-radio-group v-model="form.type">
          <el-radio-button label="aiuiRepoModel">图文知识</el-radio-button>
          <el-radio-button label="aiuiTableRepoModel">表格知识</el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-form-item
        label=""
        style="margin-bottom: 0"
        prop="currentModel"
        v-if="form.type === 'aiuiRepoModel'"
      >
        <el-radio-group v-model="form.currentModel" class="radio-wrapper">
          <el-radio
            :label="item.id"
            v-for="item in models"
            :key="item.name"
            :disabled="!item.status"
            >{{ item.name }}</el-radio
          >
        </el-radio-group>
      </el-form-item>

      <p class="qa-tip">
        {{
          (models.find((item) => item.id === form.currentModel) || {}).remark ||
          ''
        }}
      </p>
      <el-form-item :label="`文档问答库名称`" prop="name">
        <el-input
          v-model.trim="form.name"
          :placeholder="`支持中文/英文/数字/下划线格式，不超过32个字符`"
          ref="nameInput"
          @keyup.enter.native="save"
        />
        <input type="text" style="display: none" />
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button
        class="dialog-btn"
        type="primary"
        style="min-width: 104px"
        @click="save"
        :loading="saving"
      >
        {{ saving ? '创建中...' : '创建' }}
      </el-button>
    </span>
  </el-dialog>
</template>

<script>
import dicts from '@M/dicts'
import IconAdd from '@A/images/plugin/add.png'
import { mapGetters } from 'vuex'
export default {
  props: {
    dialog: {
      type: Object,
      default: {},
    },
  },
  data() {
    return {
      IconAdd,

      saving: false,
      form: {
        currentModel: '',
        name: '',
        type: 'aiuiRepoModel',
      },

      rules: {
        name: [
          this.$rules.required('名称不能为空', 'none'),
          this.$rules.lengthLimit(1, 32, '名称长度不能超过32个字符'),
          this.$rules.baseRegLimit(),
        ],

        addName: [
          {
            required: true,
            message: '请输入新增知识库名称',
            trigger: 'blur',
          },
          {
            max: 40,
            message: '内容长度不超过40个字符',
            trigger: 'change',
          },
          {
            validator: (rule, value, callback) => {
              const regExp = /^[\u4e00-\u9fa5a-zA-Z]+$/ // 正则表达式匹配中英文
              if (!regExp.test(value)) {
                callback(new Error('知识库名称只能包含中英文'))
              } else {
                callback()
              }
            },
            trigger: 'change',
          },
        ],
      },

      // 策略模型
      models: [],
    }
  },
  watch: {
    'dialog.show': function (val, oldVal) {
      let self = this
      if (val) {
        // 模态框弹出
        this.getStrategyModels()
        this.form = {
          currentModel: 100,
          name: '',
          type: 'aiuiRepoModel',
        }
        this.$nextTick(function () {
          self.$refs.nameInput && self.$refs.nameInput.focus()
        })
      } else {
        this.$refs.qaForm && this.$refs.qaForm.resetFields()
      }
    },
    'form.type'() {
      this.getStrategyModels()
    },
  },
  methods: {
    // 获取策略模型
    getStrategyModels() {
      let that = this
      this.$utils.httpGet(
        this.$config.api.AIUI_KNOWLEDGE_GET_MODELS,
        {
          modelType: this.form.type,
        },
        {
          success: (res) => {
            if (res.code == '0') {
              that.models = res.data || []
              if (that.models.length > 0) {
                that.form.currentModel = that.models[0].id
              }
            }
          },
          error: (err) => {},
        }
      )
    },

    clearValidateForm(e) {
      this.$refs.qaForm && this.$refs.qaForm.clearValidate()
    },
    save() {
      let self = this
      if (this.saving) {
        return
      }
      this.$refs.qaForm.validate((valid) => {
        if (valid) {
          this.saving = true
          console.log(this.form)
          const modelInfo =
            this.models.find((item) => item.id === this.form.currentModel)
              ?.modelInfo || '{}'
          const newModelInfo = {
            ...JSON.parse(modelInfo),
            id: this.form.currentModel,
            modelType: this.form.type,
          }
          let data = {
            name: this.form.name,
            repoConfig: JSON.stringify(newModelInfo),
            fromSource: 'aiui',
          }

          let api = this.$config.api.AIUI_KNOWLEDGE_REPO_CREATE
          this.$utils.httpPost(api, data, {
            success: (res) => {
              self.saving = false
              if (res.flag) {
                if (res.code == 0) {
                  self.$message.success('创建成功')
                  setTimeout(() => {
                    window.location.href = `/studio/ragqa/${res.data.id}/localDoc`
                  }, 50)
                }
              }
            },
            error: (err) => {
              self.saving = false
              console.log('page=>>')
              console.log(err)
            },
          })
        }
      })
    },
  },
  computed: {
    ...mapGetters({
      limitCount: 'aiuiApp/limitCount',
    }),
  },
  components: {},
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="scss" scoped>
.qa-tip {
  margin-top: 10px;
  margin-bottom: 10px;
  color: $warning;

  &-hidden {
    visibility: hidden;
    display: none;
  }
}

.kl-add {
  width: 24px;
  height: 24px;
  margin-left: 12px;
  cursor: pointer;
}
.base {
  :deep(.el-form-item__content) {
    display: flex;
    align-items: center;
  }
}

.radio-wrapper {
  margin-top: 5px;
  :deep(.el-radio + .el-radio) {
    margin-left: 10px;
  }
}
</style>
