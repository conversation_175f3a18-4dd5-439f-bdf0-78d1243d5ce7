<template>
  <div
    class="os-text-adder"
    :class="{ 'os-text-adder-disabled': disabled }"
    :style="{ width }"
  >
    <ul class="text-content">
      <li
        v-if="data.filter((d) => !d.ai).length < max"
        class="os-text-adder-row input-text"
      >
        <!-- <label class="ib serial-number" style="visibility: hidden">{{
          data.length + 1
        }}</label> -->
        <p
          class="os-text-adder-row-text"
          @keyup.enter="addText"
          style="padding-right: 0"
        >
          <el-input
            class="os-text-adder-row-input"
            style="padding: 0 10px"
            size="medium"
            :placeholder="placeholder"
            @focus="focusHandle"
            @input="handleInputChange"
            v-model="text"
          ></el-input>
        </p>
      </li>
      <li
        v-for="(item, index) in pageData"
        :key="item.questionId || item.questionKey"
        class="os-text-adder-row"
      >
        <!-- <label class="os-text-adder-row-index">{{ index + 1 }}</label> -->
        <div class="ib serial-number" style="width: 24px">
          {{ (current - 1) * pageSize + index + 1 }}
        </div>
        <p class="os-text-adder-row-text" @keyup.enter="inputBlur(item, index)">
          <el-input
            v-if="dataKey"
            class="os-text-adder-row-input dataKey"
            size="medium"
            :title="item[dataKey] || placeholder"
            :placeholder="placeholder"
            :disabled="disabled"
            @focus="focusHandle"
            @blur="() => handleEdit(item, index)"
            @input="() => handleChange(false, item, index)"
            v-model="item[dataKey]"
          ></el-input>
          <el-input
            v-else
            class="os-text-adder-row-input nodatakey"
            size="medium"
            :placeholder="placeholder"
            :title="item || placeholder"
            :readonly="readonly"
            :disabled="disabled"
            @focus="focusHandle"
            @blur="() => handleEdit(data[index], index)"
            @input="() => handleChange(false, data[index], index)"
            v-model="data[index]"
          ></el-input>
          <slot :item="item"></slot>
          <span class="icon-wrap">
            <i class="ai-extend-icon" v-if="!!item.ai"></i>
            <i
              v-if="!disabled"
              class="el-icon-delete delete"
              @click="handleDelete(item, index)"
            ></i>
          </span>
        </p>
      </li>
    </ul>
    <div class="pagination" v-if="data.length > pageSize">
      <el-pagination
        small
        layout="prev, pager, next"
        :total="data.length"
        :page-size="pageSize"
        @current-change="currentChange"
        :current-page="current"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AiuiTextAdder',
  props: {
    data: Array,
    dataKey: { type: String, default: '' },
    width: { type: String, default: '100%' },
    placeholder: { type: String, default: '' },
    reg: { type: RegExp, default: null },
    warning: { type: String, default: '输入有误' },
    max: { type: Number, default: 999 },
    readonly: { type: Boolean, default: false },
    disabled: { type: Boolean, default: false },
  },
  data() {
    return {
      text: '',
      pageSize: 10,
      pageData: [],
      current: 1,
    }
  },
  created() {
    this.getPageData(1)
  },
  methods: {
    addText() {
      if (this.text.trim()) {
        if (this.isDuplicate(this.text)) {
          this.$message.warning('请勿重复添加')
          return
        }
        if (this.reg && !this.reg.test(this.text)) {
          this.$message.warning(this.warning)
          return
        }
        this.$emit('add', this.text)
        this.text = ''
      }
    },
    isDuplicate(newText) {
      return this.data.some(
        (item) => (this.dataKey ? item[this.dataKey] : item) === newText
      )
    },
    inputBlur(item, index) {
      if (!item) this.$emit('del', item, index)
    },
    handleEdit(item, index) {
      let totalIndex = index + (this.current - 1) * this.pageSize
      this.$emit('edit', item, totalIndex)
    },
    handleDelete(item, index) {
      // 须计算在index在整体数组中的索引
      let totalIndex = index + (this.current - 1) * this.pageSize
      this.$emit('del', item, totalIndex)
    },
    handleChange(isNew, item, index) {
      // Handle input change
    },
    handleInputChange(event) {
      //   this.text = event?.target?.value || ''
    },
    focusHandle() {
      // Handle focus event
    },

    getPageData(page) {
      this.pageData = this.data.slice(
        (page - 1) * this.pageSize,
        (page - 1) * this.pageSize + this.pageSize
      )
      this.current = page || 1
      console.log('this.pageData', this.pageData)
    },
    currentChange(val) {
      console.log('currentChange', val)
      this.getPageData(val)
    },
  },

  watch: {
    data(val, oldVal) {
      if (val && oldVal) {
        // 新增场景
        if (val.length > oldVal.length) {
          this.getPageData(1)
        } else if (val.length < oldVal.length) {
          // 删除了项目，判断现在总共有几页
          let totalPage = Math.ceil(parseFloat(val.length / this.pageSize))
          this.getPageData(Math.min(totalPage, this.current))
        } else {
        }
      }
    },
  },
}
</script>

<style scoped lang="scss">
.os-text-adder {
  /* Add relevant styling */
  background: #fff;
  border: none !important;
  height: 100%;
}
.os-text-adder-disabled {
  opacity: 0.5;
  pointer-events: none;
}
.os-text-adder-row {
  display: flex;
  align-items: center;
  border-radius: 4px;
  &:not(.input-text):hover {
    background: #f3f8ff;
    :deep(.el-input__inner) {
      background: #f3f8ff;
    }
    .delete {
      display: block;
    }
  }
}
.os-text-adder-row-index {
  margin-right: 8px;
}
.os-text-adder-row-text {
  flex: 1;
  margin-bottom: 0;
}
.os-text-adder-row-input {
  width: 100%;
  padding-right: 26px;
}
.os-text-adder-row-text-del {
  cursor: pointer;
  margin-left: 8px;
  color: #bfbfbf;
}

.serial-number {
  width: 26px;
  min-width: 26px;
  height: 17px;
  background: #ffffff;
  border-radius: 2px;
  box-shadow: 0px 2px 10px 0px rgba(154, 164, 179, 0.27);

  font-size: 12px;
  color: #b1b1b1;
  line-height: 17px;
  text-align: center;
  margin: 0 16px 0 12px;
}

.os-text-adder ul li {
  padding: 0 16px 0 0;
}
.input-text {
  padding: 12px 0 0 0 !important;
  // padding-top: 8px !important;
  :deep(input) {
    border: 1px solid #e7e9ed !important;
    padding-left: 14px;
    margin-bottom: 10px;
  }
}

.text-content {
  padding: 0 8px 8px 8px;
}

.pagination {
  text-align: right;
  padding: 10px;
}

.icon-wrap {
  position: absolute;
  right: 0px;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  .ai-extend-icon {
    width: 24px;
    height: 16px;
    background: url(~@A/images/aiui/<EMAIL>) center/contain no-repeat;
  }
  .delete {
    color: #bfbfbf;
    cursor: pointer;
    display: none;
    margin-left: 5px;
  }
}
</style>
