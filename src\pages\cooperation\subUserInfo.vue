<template>
  <div class="container userinfo-page">
    <div class="userinfo-page-detail mgb24">
      <div class="mgb16">
        <i class="ic-r-email text-mid-grey"></i> 
        <span class="fs16 text-black" v-if="subAccountInfo">{{subAccountInfo.login}}</span>
      </div>
      <p style="color: #1784E9; cursor: pointer" @click="dialog.show = true">修改密码</p>
    </div>
    <pwd-form :dialog="dialog"></pwd-form>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import PwdForm from './pwdForm'
export default {
  data() {
    return {
      dialog: {
        show: false
      }
    }
  },
  computed: {
    ...mapGetters({
      subAccountInfo: 'user/subAccountInfo'
    })
  },
  components: { PwdForm }
}
</script>

<style lang="scss" scoped>
.userinfo-page {
  width: 600px;
  margin: auto;
  padding-top: 48px;
}
.userinfo-page-detail{
  width: 100%;
  height: 144px;
  border: 1px solid #e5e5e5;
  box-shadow: 0px 12px 24px 0px rgba(0, 0, 0, 0.08);
  padding: 36px 48px;
  border-radius: 12px;
}
.text-mid-grey {
  margin-right: 24px;
  font-size: 24px;
  color: #8c8c8c;
}
.text-black {
  color: $semi-black;
  font-size: 16px;
}
</style>