<template>
  <div
    class="assistant-container"
    :class="{ 'assistant-container-hide': !show }"
  >
    <div class="assistant-title">AIUI平台助手</div>
    <div class="assistant-bottom">
      <div class="left-part">
        <div class="rightChatboxContainer">
          <div class="rightChatbox" ref="rightChatbox">
            <div class="ai chatbox">
              <div class="con con-special">
                <div class="icon-ai"></div>
                <div class="welcome-word">
                  尊敬的开发者，您好。我是科大讯飞AIUI平台助手，您可以通过语音或者文本同我交流。
                </div>
                <div class="question-title">
                  <span>热门问题</span>
                  <div style="cursor: pointer" @click="toggleQuestions">
                    <i class="icon-switch"></i><span>换一批</span>
                  </div>
                </div>
                <ul class="questions">
                  <li
                    v-for="(item, index) in questions"
                    :key="index"
                    @click="chooseQuestion(item)"
                  >
                    <a :title="item">{{ item }}</a>
                  </li>
                </ul>
              </div>
            </div>
            <div
              v-for="(item, index) in chat"
              :key="index"
              :class="item.people === 'me' ? 'me' : 'ai'"
              class="chatbox"
            >
              <div class="con" style="position: relative">
                <span>{{ item.con }}</span>
                <div class="icon-me" v-if="item.people === 'me'"></div>
                <div class="icon-ai" v-if="item.people === 'ai'"></div>
              </div>
            </div>
            <div id="line" style="height: 25px; width: 1px"></div>
          </div>
        </div>
        <div class="inputContainer">
          <template v-if="mode === 'voice' && show">
            <voice-control @end="onVoiceEnd"></voice-control>
            <div
              class="icon-display icon-keyboard"
              @click="toggleMode('input')"
            >
              <i></i>
            </div>
          </template>
          <template v-if="mode === 'input'">
            <input
              placeholder="请输入您想咨询的问题～"
              class="speak-control input"
              v-model="inputText"
            />
            <div class="icon-display icon-send" @click="sendText"><i></i></div>
            <div class="icon-display icon-voice" @click="toggleMode('voice')">
              <i></i>
            </div>
          </template>
        </div>
      </div>
      <div class="right-part">
        <div class="contact">
          <div class="title"><i class="divider"></i><span>联系我们</span></div>
          <!-- <div class="email">
            邮箱：
            <a href="mailto:<EMAIL>"
              ><EMAIL></a
            >
          </div> -->

          <ul class="contact-wrap">
            <li>
              <a href="mailto:<EMAIL>">
                <div class="block mail"></div>
                <div class="contact-content">
                  <h2>邮箱</h2>
                  <p><EMAIL></p>
                </div>
              </a>
            </li>
            <li>
              <a href="/ask" target="_blank">
                <div class="block feedback"></div>
                <div class="contact-content">
                  <h2>意见反馈</h2>
                  <p>您的每一条建议，我们都认真对待</p>
                </div>
              </a>
            </li>
            <li>
              <a href="https://www.wjx.cn/vj/tWwBZcc.aspx" target="_blank">
                <div class="block satisfied"></div>
                <div class="contact-content">
                  <h2>满意度调查</h2>
                  <p>为提高质量和服务，诚邀您参与调研</p>
                </div>
              </a>
            </li>
          </ul>
        </div>
        <div class="product">
          <div class="title"><i class="divider"></i><span>产品动态</span></div>
          <ul>
            <li
              v-for="(item, index) in products"
              :key="index"
              :title="item.titleAsTitle"
              @click="jumpUpdates"
              v-html="item.title"
            ></li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
// import voiceControl from './voiceControl.vue'
export default {
  props: {
    show: false,
  },
  data() {
    return {
      chat: [
        // { people: 'me', con: '逃跑计划的海鸥听过吗' },
        // {
        //   people: 'ai',
        //   con: '逃跑计划的海鸥是一首由逃跑计划演唱的歌曲,非常动人,值得一听。',
        // },
      ],
      mode: 'voice',
      authId: '',
      questions: [],
      inputText: '',
      isFirst: true,
      products: [
        // 'AIUI平台5.0升级，敬请期待',
        // 'AIUI平台开放交互大模型的体验及接入',
        // 'AIUI平台远近场识别引擎已升级至ed版本',
        // 'AIUI平台新推出了扫地机交互解决方案',
      ],
    }
  },
  created() {
    this.authId = this.genRandomAuthId()
    this.getUpdates()
  },
  watch: {
    show(val) {
      if (val && this.isFirst) {
        this.getQuestions()
        this.isFirst = false
      }
    },
  },
  methods: {
    getAsTitle(content) {
      let div = document.createElement('div')
      div.innerHTML = content
      return div.innerText || ''
    },
    jumpUpdates() {
      window.open('/updates', '_blank')
    },
    getUpdates() {
      let that = this
      this.$utils.httpGet(
        this.$config.api.RESOURCE_DYNAMICS_LIST,
        {
          // pageIndex: page || this.tableData.page,
          // pageSize: this.tableData.size,
        },
        {
          success: (res) => {
            this.products = (res.data.dynamics || [])
              .map((item) => {
                return {
                  title: item.title,
                  titleAsTitle: this.getAsTitle(item.title),
                }
              })
              .slice(0, 4)
          },
          error: (err) => {},
        }
      )
    },
    toggleMode(val) {
      this.mode = val
    },
    genRandomAuthId() {
      const len = 32
      const chars = 'abcdef123456789'
      const maxPos = chars.length
      let character = ''
      for (let i = 0; i < len; i++) {
        character += chars.charAt(Math.floor(Math.random() * maxPos))
      }
      return character
    },

    addMessage(val) {
      this.chat.push(val)
      this.scrollMessage()
    },

    scrollMessage() {
      let container = document.getElementById('line')
      if (container) {
        this.$nextTick(() => {
          setTimeout(() => {
            container.scrollIntoView({
              behavior: 'smooth',
              block: 'end',
              inline: 'nearest',
            })
          }, 0)
        })
      }
    },
    onVoiceEnd(data) {
      // console.log('--------data------------', data)
      this.sendData('audio', data)
    },
    sendData(type, data) {
      let that = this
      if (type === 'text') {
        this.addMessage({ people: 'me', con: data })
      }
      const xhr = new XMLHttpRequest()
      xhr.withCredentials = true
      xhr.open(
        'POST',
        `/aiui/web/${this.$config.api.USER_ASSISTANT_QUERY}?authId=${this.authId}&dataType=${type}&aue=raw&sampleRate=16000`,
        true
      )

      // Send the proper header information along with the request
      xhr.setRequestHeader('Content-Type', 'application/octet-stream')

      if (localStorage.getItem('AIUI_GLOBAL_VARIABLE')) {
        xhr.setRequestHeader(
          'X-Csrf-Token',
          localStorage.getItem('AIUI_GLOBAL_VARIABLE')
        )
      }

      xhr.onreadystatechange = () => {
        // Call a function when the state changes.
        if (xhr.readyState === XMLHttpRequest.DONE && xhr.status === 200) {
          // Request finished. Do processing here.
          let result = JSON.parse(xhr.responseText)
          if (result.code === '0' && result.flag) {
            let res = JSON.parse(result.data.result)
            console.log('------------res---------------', res)

            let nlp = (res.data || []).find((item) => item.sub === 'nlp')
            if (type === 'audio') {
              // let iat = (res.data || []).find((item) => item.sub === 'iat')
              // if (iat && iat.text) {
              //   that.addMessage({ people: 'me', con: iat.text })
              // }

              if (nlp && nlp.intent && nlp.intent.text) {
                that.addMessage({ people: 'me', con: nlp.intent.text })
              } else {
                this.$message.warning('未能识别结果，请重试')
              }
            }

            if (
              nlp &&
              nlp.intent &&
              nlp.intent.answer &&
              nlp.intent.answer.text
            ) {
              that.addMessage({ people: 'ai', con: nlp.intent.answer.text })
            }
          }
        }
      }
      xhr.send(data)
    },
    getQuestions() {
      this.$utils.httpGet(
        this.$config.api.USER_ASSISTANT_QUESTIONS,
        {},
        {
          success: (res) => {
            if (res.flag) {
              console.log(res.data)
              this.questions = res.data.questions || []
            }
          },
          error: (err) => {},
        }
      )
    },
    toggleQuestions() {
      this.getQuestions()
    },
    chooseQuestion(item) {
      this.sendData('text', item)
    },
    sendText() {
      if (this.inputText.length <= 0) {
        return this.$message.warning('输入文本不能为空')
      }
      if (this.inputText.length > 100) {
        return this.$message.warning('不超过100字符')
      }
      this.sendData('text', this.inputText)
      this.inputText = ''
    },
  },
  components: { voiceControl: () => import('./voiceControl.vue') },
}
</script>
<style lang="scss" scoped>
p,
h2 {
  margin-bottom: 0;
}
.assistant-container {
  width: 900px;
  height: 520px;
  position: fixed;
  right: 120px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1000;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0px 0px 49px 0px rgba(41, 41, 84, 0.23);
  opacity: 1;
  transition: opacity 0.2s linear, transform 0.25s linear;
}

.assistant-container-hide {
  top: 100%;
  opacity: 0;
  visibility: hidden !important;
  z-index: -1 !important;
  transform: translateY(calc(-50% + 40px));
}

.assistant-title {
  height: 60px;
  line-height: 60px;
  text-align: left;
  font-size: 18px;
  font-weight: 500;
  padding-left: 25px;
  color: #fff;
  background: url(~@A/images/aiui/service/head-bg.png) center/contain no-repeat;
}

.assistant-bottom {
  height: calc(100% - 60px);
  display: flex;
}

.left-part {
  width: 576px;
}
.right-part {
  width: 322px;
  border-left: 1px solid #c7cedd;
  padding: 16px 20px;
  background: #f3f4f6;
}

.title {
  font-size: 16px;
  font-weight: 500;
  text-align: left;
  color: #0c0e3b;
  line-height: 28px;
  margin-bottom: 4px;
  display: flex;
  align-items: center;
  i {
    margin-right: 10px;
    width: 2px;
    height: 18px;
    background: #bec1cc;
    display: inline-block;
  }
}
.email {
  a {
    color: #262626;
  }

  &:hover {
    a {
      color: $primary;
    }
  }
}

.block {
  width: 119px;
  height: 69px;
}
.contact-wrap {
  li {
    width: 100%;
    height: 64px;
    background: #ffffff;
    border-radius: 4px;
    position: relative;

    .contact-content {
      position: absolute;
      z-index: 2;
      top: 10px;
      left: 14px;
    }
    .block {
      position: absolute;
      z-index: 1;
      top: 6px;
      right: 10px;
      width: 68px;
      height: 56px;
    }
    h2 {
      font-size: 14px;
      color: #313745;
    }
    p {
      font-size: 14px;
      color: #919bb4;
    }
    &:hover {
      background-color: #4b8efe;
      h2,
      p {
        color: #fff;
      }
    }
  }
  li + li {
    margin-top: 10px;
  }
}
.feedback {
  background: url(~@A/images/aiui/service/feedback.png) center/100% no-repeat;
}
.satisfied {
  background: url(~@A/images/aiui/service/satisfied.png) center/100% no-repeat;
}
.mail {
  background: url(~@A/images/aiui/service/mail.png) center/100% no-repeat;
}

.product {
  margin-top: 16px;
  ul {
    li + li {
      margin-top: 5px;
    }
    li {
      height: 32px;
      line-height: 32px;
      color: #313745;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      cursor: pointer;
      &:hover {
        color: $primary;
      }
    }
    li:not(:last-child) {
      border-bottom: 1px solid #e7e9ed;
    }
  }
}

.rightChatboxContainer {
  width: 100%;
  height: calc(100% - 80px);
  position: relative;
  overflow: auto;
}
.inputContainer {
  height: 80px;
  display: flex;
  align-items: center;
  padding: 0 21px;
  position: relative;
  border-top: 1px solid #e7e9ed;
}
.icon-display {
  width: 34px;
  height: 34px;
  cursor: pointer;
  border-radius: 100%;
  margin-left: 16px;
}
.icon-keyboard {
  border: 2px solid #1676ff;
  position: relative;
  i {
    width: 18px;
    height: 16px;
    display: inline-block;
    background: url(~@A/images/aiui/service/keyboard.png) center/100% no-repeat;
    position: absolute;
    z-index: 1;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}
.icon-voice {
  border: 2px solid #1676ff;
  position: relative;
  i {
    width: 19px;
    height: 14px;
    display: inline-block;
    background: url(~@A/images/aiui/service/voice.png) center/100% no-repeat;
    position: absolute;
    z-index: 1;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}

.icon-send {
  border: 2px solid #1676ff;
  background-color: #1676ff;
  position: relative;
  i {
    width: 19px;
    height: 19px;
    display: inline-block;
    background: url(~@A/images/aiui/service/send.png) center/100% no-repeat;
    position: absolute;
    z-index: 1;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}
.speak-control {
  flex: 1;
  line-height: 32px;
  text-align: center;
  color: #0c0e3b;
  font-size: 14px;
  font-weight: 400;
  height: 32px;
  background: rgba(231, 233, 237, 0.5);
  border-radius: 4px;
  border: 1px solid transparent;

  &.active {
    background: rgba(225, 235, 255, 0.5);
    border: 1px solid #3778ff;
    border-radius: 16px;
    box-shadow: 0px 0px 5px 0px rgba(55, 119, 255, 0.5) inset;
  }
  .voice {
    display: inline-block;
    width: 90px;
    height: 15px;
    background: url(~@A/images/aiui/service/wave.png) center/contain no-repeat;
  }
  &.input {
    text-align: left;
    padding: 0 20px;
    line-height: 25px;
  }
  &.input::-webkit-input-placeholder {
    // 针对 谷歌 内核
    color: #313745;
  }
  &.input:-moz-placeholder {
    // 火狐
    color: #313745;
  }
}
.rightChatbox {
  width: 100%;
  height: 100%;
  overflow: auto;
  box-sizing: border-box;
  padding: 27px 79px 0 79px;
  &::-webkit-scrollbar-track {
    background-color: rgba(255, 255, 255, 0.3);
  }
  &::-webkit-scrollbar-thumb {
    background: #bacff2;
  }
}

.chatbox {
  margin-bottom: 16px;
}
.me {
  display: flex;
  justify-content: flex-start;
  flex-direction: row-reverse;

  .icon-me {
    width: 42px;
    height: 42px;
    position: absolute;
    z-index: 1;
    top: 0;
    right: -60px;
    background: url(~@A/images/aiui/service/user.png) center/contain no-repeat;
  }

  .con {
    display: flex;
    align-items: center;
    max-width: 90%;
    min-height: 39px;
    min-width: 100px;
    font-size: 14px;
    line-height: 24px;
    word-break: break-word;
    color: #fff;
    background: #4b8efe;
    border-radius: 4px;
    padding: 10px 17px;
    &::before {
      position: absolute;
      content: ' ';
      top: 15px;
      right: -11px;
      width: 0;
      height: 0;
      transform: rotate(-90deg);
      border-color: #4b8efe transparent transparent;
      border-style: solid;
      border-width: 9px 7px 0;
    }
  }
}
.ai {
  .icon-ai {
    width: 42px;
    height: 42px;
    position: absolute;
    z-index: 1;
    top: 0px;
    left: -60px;
    background: url(~@A/images/aiui/service/robot.png) center/contain no-repeat;
  }
  .con {
    color: #fff;
    box-sizing: border-box;
    position: relative;
    width: fit-content;
    min-width: 100px;
    min-height: 40px;
    padding: 25px;
    background: #fff;
    color: #313745;
    font-size: 14px;
    line-height: 24px;
    word-break: break-all;
    max-width: 400px;
    background: #f3f4f6;
    border: 1px solid #e7e9ed;
    border-radius: 4px;

    &:before {
      position: absolute;
      content: ' ';
      top: 20px;
      left: -11px;
      width: 0;
      height: 0;
      transform: rotate(90deg);
      border-color: #f3f4f6 transparent transparent;
      border-style: solid;
      border-width: 9px 7px 0;
    }
  }
  .con-special {
    width: 400px;
    padding: 0;
  }
  .question-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 12px;
    height: 40px;
    color: #919bb4;
  }

  .questions {
    padding: 0px 12px 12px 12px;
    font-size: 14px;
    line-height: 28px;
    li {
      a {
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        max-width: 100%;
        display: block;
      }
      cursor: pointer;
    }
  }
  .icon-switch {
    display: inline-block;
    width: 10px;
    height: 10px;
    background: url(~@A/images/aiui/service/switch.png) center/contain no-repeat;
    margin-right: 5px;
  }
}

.send-button {
  color: #fff;
  width: 52px;
  height: 23px;
  background: linear-gradient(270deg, #4164fb 40%, #70c0fd 100%);
  border-radius: 12px;
  text-align: center;
  line-height: 23px;
  font-size: 14px;
  position: absolute;
  z-index: 1;
  right: 34px;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
}

.welcome-word {
  padding: 22px 12px;
  border-bottom: 1px solid #e7e9ed;
  font-size: 14px;
  line-height: 25px;
  color: #313745;
}
</style>
