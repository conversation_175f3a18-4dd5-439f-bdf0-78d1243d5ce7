@font-face {
  font-family: 'iFLYOS-Iconfont';
  src:  url('../font/iFLYOS-Iconfont.eot?ugnasn');
  src:  url('../font/iFLYOS-Iconfont.eot?ugnasn#iefix') format('embedded-opentype'),
    url('../font/iFLYOS-Iconfont.ttf?ugnasn') format('truetype'),
    url('../font/iFLYOS-Iconfont.woff?ugnasn') format('woff'),
    url("../font/iFLYOS-Iconfont.eot") format("embedded-opentype"),
    url('../font/iFLYOS-Iconfont.svg?ugnasn#iFLYOS-Iconfont') format('svg');
  font-weight: normal;
  font-style: normal;
}

[class^="ic-"],
[class*=" ic-"] {
  font-family: "iFLYOS-Iconfont" !important;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -webkit-text-stroke-width: 0.2px;
  -moz-osx-font-smoothing: grayscale;
  display: inline-block;
  vertical-align: middle;
}

.ic-r-node:before {
  content: "\e900";
}
.ic-r-subflow:before {
  content: "\e901";
}
.ic-flow-arrow:before {
  content: "\e902";
}
.ic-r-link:before {
  content: "\e903";
}
.ic-r-plus:before {
  content: "\e904";
}
.ic-r-plus-thin:before {
  content: "\e905";
}
.ic-r-tick:before {
  content: "\e906";
}
.ic-r-tick-thin:before {
  content: "\e907";
}
.ic-r-cross:before {
  content: "\e908";
}
.ic-r-cross-thin:before {
  content: "\e909";
}
.ic-r-enlarge:before {
  content: "\e90a";
}
.ic-r-shrink:before {
  content: "\e90b";
}
.ic-r-angle-d:before {
  content: "\e90c";
}
.ic-r-angle-u:before {
  content: "\e90d";
}
.ic-r-angle-l:before {
  content: "\e90e";
}
.ic-r-angle-r:before {
  content: "\e90f";
}
.ic-r-angle-d-line:before {
  content: "\e910";
}
.ic-r-angle-u-line:before {
  content: "\e911";
}
.ic-r-angle-l-line:before {
  content: "\e912";
}
.ic-r-angle-r-line:before {
  content: "\e913";
}
.ic-r-angle-d-oval:before {
  content: "\e914";
}
.ic-r-angle-u-oval:before {
  content: "\e915";
}
.ic-r-angle-l-oval:before {
  content: "\e916";
}
.ic-r-angle-r-oval:before {
  content: "\e917";
}
.ic-r-triangle-down:before {
  content: "\e918";
}
.ic-r-triangle-up:before {
  content: "\e919";
}
.ic-r-num-decrease:before {
  content: "\e91a";
}
.ic-r-num-increase:before {
  content: "\e91b";
}
.ic-r-tick-oval:before {
  content: "\e91c";
}
.ic-r-cross-oval:before {
  content: "\e91d";
}
.ic-r-exclamation:before {
  content: "\e91e";
}
.ic-r-tip:before {
  content: "\e91f";
}
.ic-r-info:before {
  content: "\e920";
}
.ic-r-play:before {
  content: "\e921";
}
.ic-r-pause:before {
  content: "\e922";
}
.ic-r-upload:before {
  content: "\e923";
}
.ic-r-download:before {
  content: "\e924";
}
.ic-r-edit:before {
  content: "\e925";
}
.ic-r-delete:before {
  content: "\e926";
}
.ic-r-data:before {
  content: "\e927";
}
.ic-r-file:before {
  content: "\e928";
}
.ic-r-list:before {
  content: "\e929";
}
.ic-r-setting:before {
  content: "\e92a";
}
.ic-r-search:before {
  content: "\e92b";
}
.ic-r-sound:before {
  content: "\e92c";
}
.ic-r-see:before {
  content: "\e92d";
}
.ic-r-calendar:before {
  content: "\e92e";
}
.ic-r-copy:before {
  content: "\e92f";
}
.ic-r-move:before {
  content: "\e930";
}
.ic-r-extend:before {
  content: "\e931";
}
.ic-r-email:before {
  content: "\e932";
}
.ic-r-phone:before {
  content: "\e933";
}
.ic-r-select:before {
  content: "\e934";
}
.ic-r-menu:before {
  content: "\e935";
}
.ic-r-more:before {
  content: "\e936";
}
.ic-r-minus:before {
  content: "\e937";
}
.ic-r-equal:before {
  content: "\e938";
}
.ic-r-exchange:before {
  content: "\e939";
}
.ic-r-filter:before {
  content: "\e93a";
}
.ic-r-loading:before {
  content: "\e93b";
}
.ic-r-seq-handle:before {
  content: "\e93c";
}
.ic-r-wave:before {
  content: "\e93d";
}
.ic-mn-link:before {
  content: "\e93e";
}
.ic-mn-download:before {
  content: "\e93f";
}
.ic-mn-reg-rec:before {
  content: "\e940";
}
.ic-mn-basic-info:before {
  content: "\e941";
}
.ic-mn-device-skill:before {
  content: "\e942";
}
.ic-mn-cli-capcity:before {
  content: "\e943";
}
.ic-mn-sys-char:before {
  content: "\e944";
}
.ic-mn-device-auth:before {
  content: "\e945";
}
.ic-mn-integ-test:before {
  content: "\e946";
}
.ic-mn-update:before {
  content: "\e947";
}
.ic-mn-custom:before {
  content: "\e948";
}
.ic-mn-version:before {
  content: "\e949";
}
.ic-mn-interact:before {
  content: "\e94a";
}
.ic-mn-skills-ex:before {
  content: "\e94b";
}
.ic-mn-debug:before {
  content: "\e94c";
}
.ic-mn-launch:before {
  content: "\e94d";
}
.ic-mn-code:before {
  content: "\e94e";
}
.ic-mn-data:before {
  content: "\e94f";
}
.ic-mn-data-user:before {
  content: "\e950";
}
.ic-mn-optimize:before {
  content: "\e951";
}
.ic-mn-edit:before {
  content: "\e952";
}
.ic-mn-ip-list:before {
  content: "\e953";
}
.ic-mn-flow:before {
  content: "\e954";
}
.ic-mn-dict:before {
  content: "\e955";
}
.ic-mn-chat:before {
  content: "\e956";
}
.ic-mn-device-id:before {
  content: "\e957";
}
.ic-logo-eye:before {
  content: "\e958";
}
.ic-brace:before {
  content: "\e959";
}
.ic-big-success:before {
  content: "\e95a";
}
.ic-big-failed:before {
  content: "\e95b";
}
.ic-sys-android:before {
  content: "\e95c";
}
.ic-sys-linux:before {
  content: "\e95d";
}
.ic-sys-windows:before {
  content: "\e95e";
}
.ic-sys-ios:before {
  content: "\e95f";
}
.ic-sys-webapi:before {
  content: "\e960";
}
.ic-sys-wechat:before {
  content: "\e961";
}
.ic-sys-rtos:before {
  content: "\e962";
}
.ic-sys-morfei:before {
  content: "\e963";
}
.ic-sys-other:before {
  content: "\e964";
}
.ic-sc-toy:before {
  content: "\e965";
}
.ic-sc-soundbox:before {
  content: "\e966";
}
.ic-sc-refri:before {
  content: "\e967";
}
.ic-sc-tv:before {
  content: "\e968";
}
.ic-sc-screen:before {
  content: "\e969";
}
.ic-sc-robot:before {
  content: "\e96a";
}
.ic-sc-usual:before {
  content: "\e96b";
}
.ic-sc-inte-appli:before {
  content: "\e96c";
}
.ic-sc-vehicle:before {
  content: "\e96d";
}
.ic-sc-other:before {
  content: "\e96e";
}
.ic-ser-smart-home:before {
  content: "\e96f";
}
.ic-ser-content:before {
  content: "\e970";
}
.ic-ser-3rd-party:before {
  content: "\e971";
}
.ic-sys-mf:before {
  content: "\e972";
}
.ic-sys-kf:before {
  content: "\e973";
}
