<template>
  <div class="ai_c_body">
    <div class="top-banner-title">
      <div class="top-banner-title-info">
        <img
          class="ai_c_logo"
          src="https://aiui-file.cn-bj.ufileos.com/aicalendar/logo.png"
        />
        <div class="top-banner-title-sec">
          <p class="top-banner-title-main">智能台历 APP</p>
          <p class="top-banner-title-desc">开启你的智慧生活</p>
        </div>
      </div>

      <div class="ai_c_download">
        <!-- <div class="indevice-btn-pc ios">
            <a :href="downloadAddressIOS" target='_blank'>
              <div class="indevice-btn">iOS 下载</div>
            </a>
            <p class="version-content">支持 iOS 10.1 及以上系统</p>
          </div> -->
        <div class="indevice-btn-pc android">
          <a :href="downloadAddressAndroid">
            <div class="indevice-btn">Android APK 下载</div>
          </a>
          <p class="version-content">支持 Android 5.1 及以上系统</p>
        </div>
      </div>

      <div class="ai_c_code_out">
        <img
          class="ai_c_code"
          src="https://aiui-file.cn-bj.ufileos.com/aicalendar/QRcode.png"
        />
      </div>
    </div>

    <div class="pc-footer-container-row footer-bottom">
      <div class="pc-footer-copyright">
        © 科大讯飞股份有限公司 皖ICP备05001217号
      </div>
      <div class="pc-footer-link">
        <a href="http://www.iflytek.com" target="_blank">科大讯飞</a>
        <span class="pc-footer-sep">|</span>
        <a href="https://www.xfyun.cn/" target="_blank">讯飞开放平台</a>
        <span class="pc-footer-sep">|</span>
        <a href="http://www.voiceads.cn/" target="_blank">广告平台</a>
        <span class="pc-footer-sep">|</span>
        <a href="http://www.toycloud.com/" target="_blank">讯飞淘云</a>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'aiCalendar',
  components: {},
  data: function () {
    return {
      screenWidth: null,
      downloadAddressIOS: 'https://apps.apple.com/cn/app/id1531290014',
      downloadAddressAndroid:
        'https://oss-beijing-m8.openstorage.cn/aios/aicalendar/aicalendar.apk',
    }
  },
  computed: {
    /*shrink: function () {
          if(this.screenWidth !== null && this.screenWidth < 800) {
            return true;
          } else {
            return false
          }
        }*/
  },
  mounted() {
    const that = this
    window.onresize = () => {
      return (() => {
        window.screenWidth = document.body.clientWidth
        that.screenWidth = window.screenWidth
      })()
    }
    ;(function () {
      var currClientWidth, fontValue, originWidth
      originWidth = 750 //ui设计稿的宽度，一般750或640
      __resize()

      window.addEventListener('resize', __resize, false)

      function __resize() {
        currClientWidth = document.documentElement.clientWidth
        if (currClientWidth > 768) {
          currClientWidth = 768
        }
        if (currClientWidth < 320) {
          currClientWidth = 320
        }
        fontValue = ((625 * currClientWidth) / originWidth).toFixed(2)
        document.documentElement.style.fontSize = fontValue + '%'
      }
    })()
  },
}
</script>

<style scoped lang="scss">
.ai_c_body {
  background: linear-gradient(286deg, #66c3ff, #496be6);
  background-size: cover;
  background-position: bottom;
  background-repeat: no-repeat;
  margin-bottom: 100px;
  width: 100%;
  height: 100%;
  margin: 0 auto;
  align-items: center;
  background-position: center center;
  background-attachment: fixed;
  background-size: cover;
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #fff;
  position: fixed;
  white-space: nowrap;
  overflow: auto;
  background-size: 100%;
  white-space: nowrap;

  .pc-footer {
    width: 100%;
    &-container {
      width: 100%;
      max-width: 1200px;
      margin: auto;
      padding-top: 88px;
      &-row {
        width: 100%;
        overflow: hidden;
        position: fixed;
        -webkit-box-sizing: border-box;
        box-sizing: border-box;
        margin: 0 auto;
        bottom: 0.4rem;
        width: 90%;
      }
    }
    .footer-bottom {
      display: flex;
      justify-content: space-between;
      padding-top: 24px;
      border-top: 1px solid #fff;
    }
    &-copyright {
      font-size: 0.15rem;
      color: #fff;
      float: left;
      position: relative;
      left: 0.3rem;
    }
    &-link {
      font-size: 0.15rem;
      float: right;
      right: 0.3rem;
      position: relative;
      a,
      span {
        color: #fff;
      }
    }
    &-sep {
      color: #fff;
      margin: 0 0.02rem;
    }
  }
  .top-banner-title {
    max-width: 1200px;
    margin: 0 auto;
    margin-left: 10%;
    margin-top: 7%;
    letter-spacing: 0.02rem;
    font-size: 0.5rem;
    font-weight: 500;
    .ai_c_code_out {
      float: right;
      position: relative;
      display: inline-block;
      margin-right: -7.7rem;
      margin-top: -0.9rem;
      width: 100%;
      &-shrink {
        display: none;
      }
    }
    .ai_c_code {
      width: 2.7rem;
    }
    .ai_c_download {
      position: absolute;
      .indevice-btn-pc.ios {
        float: left;
        right: 0.3rem;
      }
      .indevice-btn-pc.android {
      }
      .indevice-btn-pc {
        position: relative;
        display: grid;
        margin-top: 0.9rem;
        a:hover {
          text-decoration: none;
          color: #1784e9;
          cursor: pointer;
        }
        .version-content {
          font-size: 0.12rem;
          text-align: center;
          margin-top: 0.1rem;
          letter-spacing: 0;
          width: 3rem;
          height: 0.6rem;
          line-height: 0.55rem;
        }
        .indevice-btn {
          font-size: 0.3rem;
          text-align: center;
          font-weight: 500;
          width: 3rem;
          height: 0.6rem;
          line-height: 0.55rem;
          letter-spacing: 0.003rem;
          border: 1px solid #fff;
          border-radius: 1px;
          color: #fff;
          cursor: pointer;
          -webkit-transition: 0.6s;
          transition: 0.6s;
          display: inline-block;
        }
        .indevice-btn:hover {
          -webkit-transition: 0.6s;
          transition: 0.6s;
          color: #1784e9;
          background-color: #fff;
        }
      }
    }
    .top-banner-title-info {
      display: block;
      margin-top: 5%;
      .ai_c_logo {
        float: left;
        width: 1.5rem;
      }
      .top-banner-title-sec {
        .top-banner-title-main {
          color: #fff;
          //padding-top: 0.2rem;
          position: relative;
          left: 0.3rem;
        }
        .top-banner-title-desc {
          color: #fff;
          font-size: 0.2rem;
          //margin-top: 0.01rem;
          position: relative;
          left: 0.3rem;
          bottom: 0.3rem;
        }
      }
    }
  }

  @media screen and (max-width: 50rem) {
    .top-banner-title {
      margin-top: 2rem !important;
    }
    .pc-footer-container {
      &-row {
        width: 100%;
        justify-content: center;
        display: grid;
      }
    }
    .pc-footer-copyright {
      width: 100%;
      margin: 0 auto;
      justify-content: center;
      left: unset;
      right: unset;
    }
    .pc-footer-link {
      width: 100%;
      margin: 0 auto;
      justify-content: center;
      left: unset;
      right: unset;
    }

    .ai_c_code_out {
      display: none !important;
    }
  }

  @media screen and (max-height: 34rem) {
    .top-banner-title {
      margin-top: 1rem !important;
    }
  }
}
</style>
