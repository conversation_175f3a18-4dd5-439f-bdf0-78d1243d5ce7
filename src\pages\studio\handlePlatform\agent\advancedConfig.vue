<template>
  <div class="container">
    <div class="advanced-header">
      <back-icon @click="goback"></back-icon>
      <span> {{ intentName }}</span>

      <div class="right-btns">
        <!-- 
        <el-button type="primary" @click="buildAgent" size="middle"
          >构建智能体</el-button
        > -->

        <studio-agent-header-right
          @updatePublishStatus="
            (val) => {
              isPublished = val
            }
          "
        >
          <el-button @click="advancedModel" v-loading="saveLoading"
            >高级模式</el-button
          >
        </studio-agent-header-right>
      </div>
    </div>
    <div class="content">
      <div class="main">
        <div style="margin-bottom: 20px">
          <span class="dialog-config">对话配置</span>
          <span class="dialog-config-title">
            使用大模型分析用户对话，抽取关键信息，关键信息可用于信源查询或下发给端侧处理</span
          >
        </div>

        <div class="sub-title">
          <div>
            <span class="sub-title-ctx"> 关键信息抽槽 </span>
            <el-popover
              placement="top-start"
              title=""
              width="200"
              trigger="hover"
              content='关键信息一般是用户指令的重要参数。例如"音量调节到80"，关键信息"音量"是用户指令，"调节音量"的重要参数'
            >
              <i class="el-icon-question" slot="reference"></i>
            </el-popover>
          </div>

          <el-button
            type="primary"
            plain
            @click="addInfo"
            icon="ic-r-plus"
            size="medium"
            >&nbsp; 添加信息</el-button
          >
        </div>

        <os-table
          class="gutter-table-style secondary-table"
          :tableData="tableData"
          @change="getInfoList"
          @edit="editTable"
          @del="delTable"
        >
          <el-table-column
            label="查询关键信息"
            prop="keyDataName"
          ></el-table-column>
          <el-table-column
            label="英文标识"
            prop="keyDataNameEn"
          ></el-table-column>
          <el-table-column
            label="关键信息描述"
            prop="keyDataDesc"
            min-width="300px"
          ></el-table-column>

          <el-table-column label="多值项" prop="multivalued">
            <template slot-scope="scope">
              <span v-if="scope.row.multivalued === 1">是</span>
              <span v-if="scope.row.multivalued === 0">否</span>
            </template>
          </el-table-column>
          <el-table-column label="必须项" prop="required">
            <template slot-scope="scope">
              <span v-if="scope.row.required === 1">是</span>
              <span v-if="scope.row.required === 0">否</span>
            </template>
          </el-table-column>
          <!-- <el-table-column label="操作"></el-table-column> -->
        </os-table>

        <div class="sub-title">
          <span class="sub-title-ctx">信源配置</span>
        </div>

        <div>
          <el-form
            :model="form"
            ref="form"
            label-position="right"
            label-width="100px"
            :rules="rules"
          >
            <el-form-item label="信源:">
              <el-card style="width: 700px; overflow: visible" shadow="hover">
                <div
                  slot="header"
                  style="position: relative; overflow: visible"
                >
                  <div
                    class="cascader-label"
                    @click="showCascaderPane = !showCascaderPane"
                  >
                    <span>{{ sourceLabel }}</span>
                    <span>{{ toolLabel }}</span>
                    <i
                      :class="{
                        'el-icon-arrow-up': showCascaderPane,
                        'el-icon-arrow-down': !showCascaderPane,
                      }"
                    ></i>
                    <i
                      class="el-icon-circle-close clear"
                      @click.stop="doClear"
                      v-show="form.tool"
                    ></i>
                  </div>

                  <el-cascader-panel
                    @mouseleave="showCascaderPane = false"
                    v-show="showCascaderPane"
                    v-model="form.tool"
                    :options="cascaderOptions"
                    :props="cascaderProps"
                    @change="changeTool"
                    ref="cascader"
                    clearable
                    class="custom-cascader"
                    @focus="focusCascader"
                    separator=""
                    style="
                      position: absolute;
                      z-index: 99999;
                      top: 100%;
                      left: 0;
                      max-height: 300px; /* 限制最大高度 */
                      overflow-y: auto; /* 超出时显示滚动条 */
                      margin-top: 5px; /* 与触发按钮的间距 */
                      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
                      border-radius: 4px;
                      background-color: #fff;
                    "
                  >
                  </el-cascader-panel>
                </div>

                <div
                  class="card-content"
                  v-if="form.tool && toolDetail?.official !== 1"
                >
                  <div class="method">
                    {{ toolDetail?.method.toUpperCase() }}
                  </div>

                  <div class="card-url">
                    <el-tooltip
                      :disabled="!isOverflow"
                      :content="toolDetail?.endPoint"
                      placement="top"
                      popper-class="url_tooltip"
                    >
                      <div class="url" ref="url" @mouseenter="checkOverflow">
                        {{ toolDetail?.endPoint }}
                      </div>
                    </el-tooltip>
                  </div>
                  <el-button class="btn" type="text" @click="gotoToolDetail"
                    >查看详情</el-button
                  >
                </div>
              </el-card>
            </el-form-item>

            <el-form-item label="请求参数:" v-if="form.tool" prop="paramsList">
              <div class="request-params">
                <el-row :gutter="20" class="request-params-header">
                  <el-col :span="4">参数名称</el-col>
                  <el-col :span="4">数据类型</el-col>
                  <el-col :span="4">必填项</el-col>
                  <el-col :span="6">参数赋值</el-col>
                </el-row>
                <el-row
                  :gutter="20"
                  v-for="item in form.paramsList"
                  :key="item.index"
                  style="margin-left: 5px"
                  :popp="`paramsList.${item.index}.keydataId`"
                >
                  <el-col :span="4">{{ item.name }}</el-col>
                  <el-col :span="4"> {{ item.type }}</el-col>
                  <el-col :span="4">
                    {{ item.required ? '是' : '否' }}
                  </el-col>
                  <el-col :span="6">
                    <el-select
                      size="mini"
                      v-model="item.keyDataId"
                      @change="(val) => changeKeyData(val, item)"
                      :placeholder="item.required ? '请选上参数赋值' : '请选择'"
                      clearable
                    >
                      <el-option
                        v-for="single in tableData.list"
                        :key="single.keydataId"
                        :label="single.keyDataName"
                        :value="single.keydataId"
                      ></el-option>
                    </el-select>
                  </el-col>
                </el-row>
              </div>
            </el-form-item>

            <el-form-item label="默认回复prompt:" v-if="form.tool">
              <my-input
                v-model="form.defaultPrompt"
                :variables="variables"
                placeholder="信源返回结果时，可使用的回复语润色prompt。可以{}插入系统变量和信源输出参数"
                @blur="handleBlur"
                @input="debouncedSavePrompt"
                :popoverSouceList="popoverSouceList"
              >
              </my-input>
            </el-form-item>

            <el-form-item label="兜底回复prompt:" v-if="form.tool">
              <my-input
                v-model="form.safeguardPrompt"
                :variables="variables"
                placeholder="信源返回结果为空时，可使用的回复语润色prompt。可以{}插入系统变量"
                @blur="handleBlur"
                @input="debouncedSavePrompt"
                :popoverSouceList="popoverSouceList"
              ></my-input>
            </el-form-item>
            <el-form-item v-if="form.tool">
              <!-- <el-button @click="savePrompt" type="primary">保存</el-button> -->
            </el-form-item>
          </el-form>
        </div>
      </div>

      <key-data-modal ref="KeyDataModal" @refresh="refresh" />
    </div>
  </div>
</template>

<script>
import StudioAgentHeaderRight from '@C/studioAgentHeaderRight.vue'
import KeyDataModal from './agentComponents/keyInfoModal.vue'
import MyInput from './agentComponents/MyInput.vue'
export default {
  name: 'AgentAdvancedConfig',

  components: { KeyDataModal, MyInput, StudioAgentHeaderRight },

  created() {},

  mounted() {
    this.checkOverflow()
    this.intentName = localStorage.getItem('intentName')
    this.getInfoList()
    this.getPromptInfo()
    this.$nextTick(() => {
      this.getSourceCascaderTree()
    })

    this.debouncedSavePrompt = this.$utils.debounce(
      () => this.savePrompt(),
      1000
    )

    document.addEventListener('click', this.handleClickOutside)
  },

  beforeDestroy() {
    document.removeEventListener('click', this.handleClickOutside)
  },
  data() {
    return {
      apiType: 1,
      apiId: null,
      apiSelectList: [],
      // selectOpenApiTemplate: null,
      isPublished: false,
      intentName: '',

      saveLoading: false,

      tableData: {
        loading: false,
        handles: ['edit', 'del'],
        handleColumnText: '操作',
        list: [],
      },

      form: {
        tool: null,
        defaultPrompt: '',
        safeguardPrompt: '',
        paramsList: [],
      },
      cascaderOptions: [],

      rules: {},

      previousToolValue: null,
      quoteFlag: true,

      isOverflow: false,

      toolDetail: null,
      variables: [
        // {
        //   id: 'history',
        //   name: 'history（对话历史）',
        //   type: 'string',
        // },
        { id: 'query', name: 'query（用户提问）', type: 'string' },
        {
          id: 'intent',
          name: 'intent（用户意图）',
          type: 'string',
        },
        {
          id: 'knowledge',
          name: 'knowledge（信源结果）',
          type: 'string',
        },
      ],

      cascaderProps: {
        value: 'sourceId',
        label: 'sourceName',
        children: 'apis',
        emitPath: false, // 只 emit 最后一级的值
        checkStrictly: false, // 必须选到最后一级
      },

      sourceLabel: '',
      toolLabel: '',

      showCascaderPane: false,

      popoverSouceList: [],

      // 添加防抖保存方法
      debouncedSavePrompt: null,
    }
  },
  watch: {
    'form.tool'(newVal, oldVal) {
      console.log(newVal, oldVal, '监听的新值和旧值')
      if (!newVal && oldVal) {
        this.previousToolValue = oldVal // 监听变化并保存旧值
        this.saveSource()
      }
    },
  },
  methods: {
    goback() {
      this.$router.go(-1)
    },
    handleClickOutside(event) {
      const cascaderPanel = this.$refs.cascader?.$el
      const cascaderLabel = this.$el.querySelector('.cascader-label')

      // 如果点击的不是 cascader 或其子元素，并且当前是展开状态
      if (
        this.showCascaderPane &&
        !cascaderPanel?.contains(event.target) &&
        !cascaderLabel?.contains(event.target)
      ) {
        this.showCascaderPane = false
      }
    },

    checkOverflow() {
      this.$nextTick(() => {
        const el = this.$refs.url
        if (el) {
          this.isOverflow = el.scrollWidth > el.clientWidth
        }
      })
    },

    doClear() {
      this.changeTool()
      this.sourceLabel = ''
      this.toolLabel = ''
    },

    getSourceCascaderTree() {
      let pluginId = this.$route.params.agentId
      let intentId = this.$route.params.intentId
      this.$utils.httpGet(
        `/aiui-agent/openPlatform/source/tree?pluginId=${pluginId}&intentId=${intentId}`,
        {},
        {
          config: {
            headers: {
              'Content-Type': 'application/json;charset=UTF-8',
            },
          },
          success: (res) => {
            if (res.code === '0') {
              this.cascaderOptions = res.data.sourceTree.map((item) => {
                if (item.apis) {
                  item.apis = item.apis.map((a) => {
                    return {
                      sourceName: a.toolName,
                      sourceId: a.toolId,
                    }
                  })
                }
                return item
              })
              // this.cascaderOptions = res.data.sourceTree
              this.form.tool = res.data.toolIntentPlugin?.toolId
              if (res.data.toolIntentPlugin?.toolId) {
                this.getRequestParams()

                this.$nextTick(() => {
                  const cascader = this.$refs.cascader
                  const nodes = cascader.getCheckedNodes()
                  console.log(nodes, 'nodes======>')
                  this.sourceLabel = nodes[0]?.pathLabels[0]
                  this.toolLabel = nodes[0]?.pathLabels[1]
                })
              }
            }
          },
          error: (err) => {},
        }
      )
    },

    focusCascader() {},

    getLastTreeByToolId(id) {
      this.$utils.httpGet(
        `/aiui-agent/openPlatform/source/personalTree?toolId=${id}`,
        {},
        {
          success: (res) => {
            if (res.code === '0') {
              this.cascaderOptions = res.data.map((item) => {
                if (item.apis) {
                  item.apis = item.apis.map((a) => {
                    return {
                      sourceName: a.toolName,
                      sourceId: a.toolId,
                    }
                  })
                }
                return item
              })
            }
          },
          error: (err) => {},
        }
      )
    },

    getPromptInfo() {
      let intentId = this.$route.params.intentId
      this.$utils.httpGet(
        `/aiui-agent/intent/prompt/detail?intentId=${intentId}`,
        {},
        {
          config: {
            headers: {
              'Content-Type': 'application/json;charset=UTF-8',
            },
          },
          success: (res) => {
            if (res.code === '0') {
            }
            this.form.defaultPrompt = res.data?.defaultPrompt
            this.form.safeguardPrompt = res.data?.safeguardPrompt
          },
          error: (err) => {},
        }
      )
    },

    getRequestParams() {
      const params = {
        toolId: this.form.tool,
        pluginId: this.$route.params.agentId,
        intentId: this.$route.params.intentId,
      }
      this.$utils.httpPost(
        `/aiui-agent/openPlatform/openapiDetail`,
        JSON.stringify(params),
        {
          config: {
            headers: {
              'Content-Type': 'application/json;charset=UTF-8',
            },
          },
          success: (res) => {
            console.log(res, 'getRequestParams的res')
            if (res.code === '0') {
              this.form.paramsList = res.data?.paramList
              this.toolDetail = res.data?.toolDetail

              this.popoverSouceList = res.data.outParamList.map((item) => {
                return {
                  name: item.name,
                  id: item.id,
                  type: item.type,
                  children: item?.children || [],
                }
              })
            }
          },
          error: (err) => {},
        }
      )
    },
    handleClear(val) {},

    changeTool(val) {
      if (val) {
        const cascader = this.$refs.cascader
        const nodes = cascader.getCheckedNodes()
        console.log(nodes, 'nodes======>')
        this.sourceLabel = nodes[0]?.pathLabels[0]
        this.toolLabel = nodes[0]?.pathLabels[1]

        this.quoteFlag = true
        this.getRequestParams()
        this.saveSource()
      } else {
        this.quoteFlag = false
        this.toolDetail = null
        this.form.tool = null
      }
    },
    gotoToolDetail() {
      this.$router.push({
        name: 'studio-source-detail',
        params: {
          sourceId: this.toolDetail?.sourceId,
        },
      })
    },

    saveSource() {
      const params = {
        intentId: this.$route.params.intentId,
        pluginId: this.$route.params.agentId,
        toolId: this.quoteFlag ? this.form.tool : this.previousToolValue, // 取消引用传上一个id
        quoteFlag: this.quoteFlag,
      }
      this.$utils.httpPost(
        `/aiui-agent/openPlatform/openapiSave`,
        JSON.stringify(params),
        {
          config: {
            headers: {
              'Content-Type': 'application/json;charset=UTF-8',
            },
          },
          success: (res) => {
            if (res.code === '0') {
              this.$message.success(
                `成功${this.quoteFlag ? '关联' : '解绑'}信源`
              )
              this.getPromptInfo()
              this.showCascaderPane = false
            }
          },
          error: (err) => {},
        }
      )
    },

    advancedModel() {
      this.$confirm(
        `切换高级模式后，会自动切换成工作流类型(回不到模版)，是否继续？`,
        {
          confirmButtonClass: '确定切换',
          cancelButtonText: '取消',
          confirmButtonClass: 'el-button-danger',
          type: 'warning',
          showClose: false,
        }
      )
        .then(() => {
          this.saveLoading = true
          let id = this.$route.params.agentId
          this.$utils.httpPost(
            `/aiui-agent/plugin/switchWorkflow?pluginId=${id}`,
            {},
            {
              config: {
                headers: {
                  'Content-Type': 'application/json;charset=UTF-8',
                },
              },
              success: async (res) => {
                if (res.code === '0') {
                  this.saveLoading = false
                  this.$message.success('操作成功')
                  this.workflowPluginInfo = res.data
                  // await this.getAgentList()
                  // 更新路由参数
                  this.$router.replace({
                    name: 'studio-handle-platform-agent-detail',
                    params: {
                      agentId: this.$route.params.agentId,
                      agentType: 22,
                      boardId: res.data.boardId,
                    },
                  })
                } else {
                  this.$message.error('切换失败')
                }
              },
              error: (err) => {
                this.saveLoading = false
                this.$message.error(err?.desc || '切换失败')
              },
            }
          )
        })
        .catch(() => {})
    },

    buildAgent() {},

    addInfo() {
      this.$refs.KeyDataModal.show()
    },

    changeKeyData(val, item) {
      console.log(val, item)
      const params = {
        toolId: this.form.tool,
        paramName: item.name,
        keyDataId: val,
        pluginId: this.$route.params.agentId,
        intentId: this.$route.params.intentId,
      }
      if (!val && item.required) {
        this.$message.warning(`${item.name}参数必须赋值`)
        return
      } else {
        this.$utils.httpPost(
          `/aiui-agent/openPlatform/toolParamKeyData/save`,
          JSON.stringify(params),
          {
            config: {
              headers: {
                'Content-Type': 'application/json;charset=UTF-8',
              },
            },
            success: (res) => {
              if (res.code === '0') {
                this.$message.success('保存成功')
                this.getRequestParams()
              }
            },
            error: (err) => {},
          }
        )
      }
      // this.$refs.form.validate((valid) => {
      //   if (valid) {
      //   }
      // })
    },

    // changeDefaultPrompt: debounce(function (content) {
    //   this.savePrompt() // 调用接口
    // }, 1000), // 500ms 防抖延迟

    // changeSafeguardPrompt: debounce(function (content) {
    //   this.savePrompt() // 调用接口
    // }, 1000), // 500ms 防抖延迟

    savePrompt() {
      const params = {
        defaultPrompt: this.form.defaultPrompt,
        safeguardPrompt: this.form.safeguardPrompt,
        intentId: this.$route.params.intentId,
        pluginId: this.$route.params.agentId,
      }
      console.log(this.form, 'this.form')
      this.$utils.httpPost(
        `/aiui-agent/intent/prompt/save`,
        JSON.stringify(params),
        {
          config: {
            headers: {
              'Content-Type': 'application/json;charset=UTF-8',
            },
          },
          success: (res) => {
            if (res.code === '0') {
              // this.$message.success('保存prompt成功')
              this.getPromptInfo()
            }
          },
          error: (err) => {},
        }
      )
    },

    refresh() {
      this.getInfoList()
    },

    editTable(data) {
      console.log(data, '表格的data')
      this.$refs.KeyDataModal.show(data)
    },

    delTable(data) {
      let pluginId = this.$route.params.agentId
      this.$confirm(`确定删除该条关键信息吗？`, {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        confirmButtonClass: 'el-button-danger',
        type: 'warning',
        showClose: false,
      })
        .then(() => {
          this.$utils.httpPost(
            `/aiui-agent/openPlatform/keyData/delete?keyDataId=${data.keydataId}&pluginId=${pluginId}`,
            {},
            {
              config: {
                headers: {
                  'Content-Type': 'application/json;charset=UTF-8',
                },
              },
              success: (res) => {
                if (res.code === '0') {
                  this.$message.success('操作成功')
                  this.getInfoList()
                }
              },
              error: (err) => {},
            }
          )
        })
        .catch(() => {})
    },

    getInfoList() {
      this.$utils.httpGet(
        '/aiui-agent/openPlatform/keyData',
        {
          intentId: this.$route.params.intentId,
        },
        {
          success: (res) => {
            if (res.code === '0') {
              this.tableData.list = res.data
            }
          },
          error: (err) => {},
        }
      )
    },
    handleBlur(val) {
      console.log('编辑器内容已更新:', val)
    },
  },
}
</script>

<style lang="scss" scoped>
.container {
  background-color: $secondary-bgc;
  height: 100%;
  .advanced-header {
    span {
      margin-left: 10px;
      font-size: 20px;
      color: #000000;
      font-weight: 600;
    }
    background-color: #fff;
    border: 1px solid $grey007;
    border-top: 0px;
    border-left: 0px;
    display: flex;
    min-height: 63px;
    align-items: center;
    padding: 0px 24px;
    .right-btns {
      margin-left: auto;
    }
  }
  .content {
    padding: 15px 20px;
    height: calc(100% - 64px);
    overflow-y: auto;
    .main {
      // display: flex;
      // flex-direction: column;
      min-height: 100%;
      padding: 20px 20px;
      border-radius: 16px;
      background-color: #fff;

      .dialog-config {
        font-size: 18px;
        font-weight: 700;
        margin-right: 20px;
        &-title {
          font-size: 12px;
        }
      }
      .el-scrollbar {
        flex: 1;
      }
      .sub-title {
        margin: 10px 0px;
        margin-top: 25px;
        color: #000;
        font-size: 14px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-left: 10px;
        &-ctx {
          line-height: 18px;
          font-weight: 500;
          border-left: #1990ff 4px solid;
          padding-left: 10px;
        }
      }
      .query-operate {
        display: flex;
      }
      .checkbox {
        padding: 10px;
        width: 256px;
        border: 1px solid #f0f0f0;
        border-radius: 6px;
        background: #ffffff;
      }
      .api-select {
        margin-bottom: 10px;
        width: 300px;
      }
      .form-item {
        width: 540px;
        border: 1px solid #d7d7d7;
        border-radius: 6px;
      }
      :deep(.el-form-item__label) {
        color: #555454 !important;
      }
      .card-content {
        width: 100%;
        overflow: hidden;
        display: flex;
        align-content: center;
        justify-content: space-between;
        padding-left: 20px;
        .card-url {
          max-width: 400px;
          .url {
            overflow: hidden;
          }
        }
        .method {
          color: #1990ff;
          padding: 0 3px;
          border-radius: 1px;
          font-size: 20px;
          margin-right: 20px;
        }
        .btn {
          margin-left: auto;
        }
      }

      .inputTool {
        font-weight: 400;
        text-align: LEFT;
        color: #000000;
        line-height: 20px;
        margin-bottom: 10px;
      }
      .outTool {
        margin: 10px 0px;
      }
      .param-header {
        margin-bottom: 10px;
        .text-left {
          text-align: left;
          padding-left: 12px;
        }
      }
      .highlight {
        background-color: #fffacd;
        color: #d2691e;
        padding: 0 2px;
        border-radius: 2px;
      }

      // 添加自动保存提示样式
      .auto-save-hint {
        margin-left: 10px;
        color: #909399;
        font-size: 12px;
        font-style: italic;
      }
    }
    ::v-deep .el-card {
      border-radius: 12px;
    }

    ::v-deep .el-card__header {
      padding: 10px 20px;
      padding-left: 20px;
    }

    ::v-deep .el-card__body {
      padding: 0px;
    }

    .custom-cascader {
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      border-radius: 4px;
      margin-top: 5px;
    }
    ::v-deep .custom-cascader .el-input__inner {
      width: 100%;
      border: none;
      color: #353535;
      font-size: 20px;
      display: flex !important;
      font-weight: bold;
      align-items: center !important;
    }

    .request-params {
      box-sizing: border-box;
      width: 700px;
      border-radius: 6px;
      border: 1px solid #d7d7d7;
      padding-bottom: 10px;
      overflow: hidden;
      &-header {
        padding: 0 10px;
        background-color: #eff3f9;
      }
    }
  }

  .parent {
    font-size: 20px;
  }
  .child {
    color: red;
  }
  .cascader-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    span:nth-child(1) {
      height: 26px;
      font-size: 18px;
      font-family: PingFang SC, PingFang SC-400;
      font-weight: normal;
      text-align: LEFT;
      color: #353535;
      line-height: 26px;
      margin-left: 10px;
      margin-right: 25px;
    }
    span:nth-child(2) {
      color: #6a6a6a;
      font-size: 14px;
      margin-right: auto;
      height: 26px;
      line-height: 26px;
    }
    i:nth-child(1) {
      margin-right: 20px;
      cursor: pointer;
    }
    .clear {
      margin-left: 20px;
    }
  }
}
</style>
