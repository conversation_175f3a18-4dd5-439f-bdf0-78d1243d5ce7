const { readEnv, getConditionalLoader, resolve } = require('./utils')
const config = readEnv('./.env.production')
const { DefinePlugin, ProgressPlugin } = require('webpack')
const { VueLoaderPlugin } = require('vue-loader')

const path = require('path')

//读取环境变量
module.exports = {
  mode: 'production',
  devtool: false,
  entry: './src/components/feedBackHover.js',
  output: {
    path: resolve('dist/js'),
    filename: 'feedback-hover.js',
    libraryTarget: 'umd', //用到的模块定义规范
    library: 'feedBackHover', //库的名字
  },
  module: {
    rules: [
      {
        test: /\.js$/,
        exclude: [resolve('node_modules')],
        use: ['babel-loader', getConditionalLoader()],
      },
      {
        test: /\.mjs$/,
        use: ['babel-loader', getConditionalLoader()],
        include: [resolve('node_modules')],
        type: 'javascript/auto',
      },
      {
        test: /\.worker\.js$/,
        loader: 'worker-loader',
      },

      {
        test: /\.(png|gif|jpe?g|svg)$/,
        type: 'asset', // webpack5使用内置静态资源模块，且不指定具体，根据以下规则使用
        generator: {
          filename: 'img/[name]_[hash][ext]', // ext本身会附带点，放入img目录下
        },
        parser: {
          dataUrlCondition: {
            maxSize: 10 * 1024, // 超过10kb的进行复制，不超过则直接使用base64
          },
        },
        exclude: [resolve('src/assets/svg')],
      },
      {
        test: /\.svg$/,
        include: [resolve('src/assets/svg')],
        use: {
          loader: 'svg-sprite-loader',
          options: {
            symbolId: 'icon-[name]',
          },
        },
      },
      {
        test: /\.(ttf|woff2?|eot|otf)$/,
        type: 'asset/resource', // 指定静态资源类复制
        generator: {
          filename: 'font/[name][ext]', // 放入font目录下
        },
      },
      {
        test: /\.(mp4|webm|ogg|mp3|wav|flac|aac)(\?.*)?$/,
        type: 'asset/resource', // 指定静态资源类复制
        generator: {
          filename: 'media/[name][ext]', // 放入meida目录下
        },
      },
      {
        test: /\.vue$/,
        use: ['vue-loader', getConditionalLoader()],
      },
      {
        test: /\.css$/i,
        use: ['style-loader', 'css-loader'],
      },
      {
        test: /\.s[ac]ss$/i,
        use: [
          'style-loader',
          'css-loader',
          {
            loader: 'postcss-loader',
            options: {
              sourceMap: false,
            },
          },
          {
            loader: 'sass-loader',
            options: {
              sourceMap: false,
              additionalData: `@import "@/assets/scss/variable.scss";`,
            },
          },
          getConditionalLoader(),
        ],
      },
    ],
  },
  plugins: [
    new VueLoaderPlugin(),
    new DefinePlugin({
      BASE_URL: JSON.stringify('/'),
      'process.env': config,
    }),

    new ProgressPlugin(),
  ],

  resolve: {
    symlinks: false,
    extensions: ['.vue', '.js', '.json', '.mjs'],
    alias: {
      vue$: 'vue/dist/vue.esm.js',
      '@': resolve('src'),
      '@A': resolve('src/assets'),
      '@C': resolve('src/components'),
      '@M': resolve('src/model'),
      '@L': resolve('src/layouts'),
      '@P': resolve('src/pages'),
      '@U': resolve('src/utils'),
      'os-element': resolve('src/assets/lib/os-element'),
      sso: resolve('src/assets/lib/sso'),
      '@static': resolve('static'),
    },
    fallback: {
      crypto: require.resolve('crypto-browserify'),
      buffer: require.resolve('buffer/'),
      stream: require.resolve('stream-browserify'),
      vm: require.resolve('vm-browserify'),
      process: require.resolve('process/browser'),
    },
  },
}
