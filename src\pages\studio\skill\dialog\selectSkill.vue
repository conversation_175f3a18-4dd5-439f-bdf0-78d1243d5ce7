<template>
  <div
    v-if="skillPopover.show"
    v-clickoutside="closePopover"
    class="el-popover el-popper el-popover--plain skill-select-popover"
    :style="popperStyle"
    x-placement="bottom">
    <div>
      <div class="skill-select-popover__head">
        <div class="skill-select-popover-search">
          <el-input
            class="search-area"
            placeholder="搜索技能"
            v-model="searchName">
          </el-input>
        </div>
      </div>
      <div class="skill-select-popover__body">
        <div class="skill-select-popover-list" v-loadmore="scrollLoad" v-loading="loading">
          <div class="skill-select-popover-item"
            v-for="(item, key) in skillData.list"
            @click="selectItem(item)">
            <span :title="item.zhName">{{item.zhName}}</span>
          </div>
          <div class="el-table__empty-block" v-if="!skillData.list.length">
            <span class="el-table__empty-text">暂无数据</span>
          </div>
        </div>
        <os-divider/>
        <div class="skill-select-popover-add">
          <i class="ic-r-plus" />
          <a @click="addSkill">创建新技能</a>
        </div>
      </div>
    </div>
    <div x-arrow="" class="popper__arrow" style="left: 20px;"></div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  props: {
    subAccount: Boolean
  },
  data () {
    return {
      rect: {
        top: 0,
        left: 0,
        width: 0
      },
      searchName: '',
      loading: false,
      skillData: {
        loading: true,
        loadend: false,
        total: 0,
        page: 1,
        size: 20,
        list: []
      },
      debounced: null
    }
  },
  computed: {
    ...mapGetters({
      skillPopover: 'studioSkill/skillPopover'
    }),
    popperStyle () {
      if (this.rect) {
        return {
          'top': `${this.rect.top + 20}px`,
          'left': 0
        }
      } else {
        return {
          'display': `none`
        }
      }
    },
  },
  watch: {
    'skillPopover.show': function (val, oldVal) {
      if (val) {
        this.searchName = ''
        this.initData(1)
      }
    },
    'skillPopover.rect': function (val, oldVal) {
      this.rect = JSON.parse(JSON.stringify(this.skillPopover.rect))
    },
    'searchName': function (val, oldVal) {
      this.debounced()
    }
  },
  mounted() {
    this.initData(1)
    this.setDebounce()
  },
  beforeDestroy(){
    this.debounced = null
  },
  methods: {
    setDebounce(){
      this.debounced = this.$utils.debounce(() => { this.initData(1) }, 500, true)
    },
    initData (page) {
      let self = this
      this.skillData.loading = true
      this.loading = true
      this.$utils.httpGet(this.$config.api.STUDIO_USER_SKILLS, {
        pageIndex: page || this.skillData.page,
        pageSize: this.skillData.size,
        search: this.searchName
      }, {
        success: (res) => {
          if (res.data.pageIndex === 1) {
            self.skillData.list = res.data.skills
          } else {
            self.skillData.list = self.skillData.list.concat(res.data.skills)
          }
          self.skillData.total = res.data.count
          self.skillData.page = res.data.pageIndex
          self.skillData.size = res.data.pageSize
          self.skillData.loading = false
          self.loading = false
          self.skillData.loadend = res.data.pageSize * res.data.pageIndex >= res.data.count
        },
        error: (err) => {
          self.loading = false
        }
      })
    },
    closePopover () {
      this.$store.dispatch('studioSkill/setSkillPopover', {
        show: false
      })
    },
    selectItem (item) {
      let routeData, routeName = this.$route.name
      let routePrefix = item.type == '3' ? 'extend-' : ''
      let namePrefix = routeName.substr(0, 6)
      let last15str = routeName.substr(-15)
      let nextRouteName = ''
      if(namePrefix !== 'extend') {
        // 从普通技能跳转
        if (last15str == 'skill-intention') {
          nextRouteName =  `${routePrefix}${routeName}s`
        } else {
          nextRouteName =  `${routePrefix}${routeName}`
        }
      } else {
        //从业务定制技能跳转
        if(!routePrefix) {
          //跳转到普通技能
          if (last15str == 'skill-intention') {
            nextRouteName =  `${routeName.substring(7)}s`
          } else {
            nextRouteName =  `${routeName.substring(7)}`
          }
        } else {
          //跳转到业务定制技能
          if (last15str == 'skill-intention') {
            nextRouteName =  `${routeName}s`
          } else {
            nextRouteName =  `${routeName}`
          }
        }
      }
      routeData = this.$router.resolve({ name: `${nextRouteName}`, params: {skillId: item.id}})
      location.href = routeData.href
    },
    scrollLoad () {
      if (this.skillData.loading || this.skillData.loadend) {
        return
      }
      this.initData(this.skillData.page + 1)
    },
    addSkill () {
      this.closePopover()
      localStorage.setItem('addSkill', true)
      this.$router.push({ name: 'studio-handle-platform-skills' })
    }
  },
  components: {

  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="scss">
.skill-select-popover {
  position: fixed;
  width: 256px;
  transform-origin: center top 0px;
  z-index: 2000;
  padding: 0;
}

.skill-select-popover__head {
  display: flex;
  align-items: center;
}
.skill-select-popover__body {
  // margin-top: 10px;
}
.skill-select-popover-search {
  width: 100%;
  border-bottom: 1px solid $grey2;
}
.skill-select-popover-search input {
  border: 0;
  border-radius: 8px;
}
.skill-select-popover-list {
  width: 100%;
  height: 236px;
  overflow-y: scroll;
}
.skill-select-popover-item {
  height: 36px;
  line-height: 36px;
  padding: 0 16px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: pointer;
  &:hover {
    background: $primary-light-12;
  }
}
.skill-select-popover-add {
  height: 48px;
  line-height: 48px;
  padding: 0 16px;
  display: flex;
  i {
    color: $grey4;
  }
  a {
    font-weight: 600;
    padding-left: 6px;
  }
}
</style>
