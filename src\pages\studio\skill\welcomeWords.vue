<template>
  <div>
    <label-selector
      @select="onLabelSelect"
      title="欢迎语示例说法"
    ></label-selector>

    <welcome-words-text-adder
      :data="welcomeWords || []"
      :disabled="!subAccountEditable"
      @add="add"
      @del="del"
      @edit="edit"
      @change="onInputChange"
      :reg="textReg"
      :warning="warning"
      :max="10"
      editPlaceholder="例如，欢迎来到小飞天文百科，回车或点击右边按钮保存"
      placeholder="例如，欢迎来到小飞天文百科，回车新增"
      ref="welcomeRef"
    >
    </welcome-words-text-adder>
    <span slot="footer" class="dialog-footer"> </span>
  </div>
</template>

<script>
import welcomeWordsTextAdder from './welcomeWordsTextAdder.vue'
import labelSelector from './labelSelector.vue'
export default {
  components: { welcomeWordsTextAdder, labelSelector },
  props: {
    // dialog: {
    //   type: Object,
    //   default: {},
    // },
    subAccountEditable: Boolean,
    list: [],
  },
  data() {
    return {
      saving: false,
      // textReg: /^[\u4e00-\u9fffa-zA-Z0-9° ]{1,50}$/,
      welcomeWords: [],
      textReg: /^[\u4e00-\u9fffa-zA-Z0-9 {}_?？°]{1,40}$/,
      warning: '仅支持汉字/字母/数字/空格/{}/_/?/°，且每条不超过40字',
    }
  },
  computed: {
    intentId() {
      return this.$store.state.studioSkill.intention.id
    },
  },
  // watch: {
  //   'list.data'(val, oldName) {
  //     if (val) {
  //       this.welcomeWords = val || []
  //     } else {
  //     }
  //   },
  // },
  mounted() {
    this.welcomeWords = (this.list.slice() || []).map((item) => {
      return {
        ...item,
        changed: false,
      }
    })
  },
  methods: {
    onInputChange(index) {
      this.welcomeWords = (this.welcomeWords || []).map((item, i) => {
        if (index === i) {
          return {
            ...item,
            changed: true,
          }
        } else {
          return {
            ...item,
          }
        }
      })
    },
    onLabelSelect(label) {
      this.$refs.welcomeRef.$refs.intelInput.insertLabel(label)
    },

    add(text) {
      let welcomeWords = JSON.parse(JSON.stringify(this.welcomeWords))
      welcomeWords[welcomeWords.length] = text
      this.save(welcomeWords, 'add')
    },
    del(text, index) {
      if (this.saving) {
        return
      }
      this.saving = true
      let welcomeWords = (this.welcomeWords = Array.prototype.filter.call(
        this.welcomeWords,
        function (item, i) {
          return index !== i
        }
      ))
      this.save(welcomeWords, 'sub')
    },
    edit(text, index) {
      const otherItems = (this.welcomeWords || []).filter(
        (_, idx) => idx !== index
      )
      const find = otherItems.findIndex((itm) => itm.answer === text.answer)
      if (find !== -1) {
        return this.$message.error('不得与其他条目重复')
      }
      let welcomeWords = (this.welcomeWords || []).map((item, i) => {
        if (index === i) {
          return {
            ...item,
            ...text,
          }
        } else {
          return { ...item }
        }
      })
      this.save(welcomeWords, 'edit', index)
    },
    save(list, mode, index) {
      let self = this
      if (mode === 'edit') {
        self.welcomeWords = (list || []).map((it, i) => {
          if (index === i) {
            return {
              ...it,
              changed: false,
            }
          } else {
            return { ...it }
          }
        })
      } else {
        self.welcomeWords = list || []
      }
      let resultList = (list || []).map((item) => {
        return {
          answer: item.answer,
          labels: (item.labels || []).map(({ picture, ...rest }) => {
            return { ...rest }
          }),
        }
      })
      // this.$emit('setWelcomePhrase', JSON.stringify(resultList))
      this.$emit('setWelcomePhrase', resultList)
      this.saving = false
    },
  },
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="scss" scoped>
.dialog-official-intents {
  margin-bottom: 15px;
}
.dialog-official-intent-selectnum {
  color: $grey4;
  margin-right: 24px;
}

.container {
  width: 100%;
  border: 1px solid #d5d8de;
  padding: 0 16px;
  .confirm-adder {
    display: flex;
    align-items: center;
  }
  .confirm-list {
    padding-top: 15px;
    > li {
      display: flex;
      align-items: center;
      position: relative;
      padding-right: 20px;
      &:hover {
        .delete {
          display: block;
        }
      }
      .delete {
        position: absolute;
        right: 0;
        color: #b8babf;
        cursor: pointer;
        display: none;
        color: #1784e9;
        // &:hover {
        //   color: #1784e9;
        // }
      }
    }
    li + li {
      margin-top: 10px;
    }
    margin-bottom: 0;
  }
  .number-label {
    margin-right: 20px;
    color: #b8babf;
  }
}
</style>
