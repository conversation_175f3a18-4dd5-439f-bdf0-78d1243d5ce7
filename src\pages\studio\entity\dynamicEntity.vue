<template>
  <div class="dynamic-entity">
    <os-page-label label="编辑资源" class="mgb24"> </os-page-label>
    <div class="mgb16 btns-wrap">
      <el-button
        icon="ic-r-plus"
        size="small"
        type="primary"
        @click="addPersonal"
      >
        添加资源
      </el-button>
      <el-button icon="ic-r-download" size="small" @click="downloadTemplate"
        >下载资源模版</el-button
      >
      <el-button size="small" @click="openSecretKey" style="margin-right: 24px">
        动态实体密钥
      </el-button>
      <div class="fr">
        <el-button
          type="primary"
          size="small"
          @click="onSubmit"
          :loading="saving"
          :disabled="!edited"
        >
          {{ saving ? '保存中...' : '保存' }}
        </el-button>
      </div>
    </div>
    <os-table
      :border="true"
      class="dynamic-entity__personal-table secondary-table-thead"
      :tableData="tableData"
      style="margin-bottom: 56px"
      @del="toDel"
      @cell-mouse-enter="handleCellMouseEnter"
      @cell-mouse-leave="handleCellMouseLeave"
      @cell-click="handleCellClick"
      v-clickoutside="handleClickOutSide"
      :cell-class-name="cellClassName"
    >
      <el-table-column type="expand">
        <template slot-scope="props">
          <el-form
            label-position="left"
            label-width="200px"
            class="personal-table-expand"
          >
            <el-form-item label="预处理 (非必填)">
              <el-tooltip
                :content="tips.preprocess"
                style="position: absolute; left: -98px; top: 15px"
                placement="right"
              >
                <i class="el-icon-question"></i>
              </el-tooltip>
              <el-checkbox
                v-model="props.row.preprocess[0]"
                true-label="1"
                false-label="0"
                >统一转小写</el-checkbox
              >
              <el-checkbox
                v-model="props.row.preprocess[1]"
                true-label="1"
                false-label="0"
                >统一转半角</el-checkbox
              >
              <el-checkbox
                v-model="props.row.preprocess[2]"
                true-label="1"
                false-label="0"
                >统一转中文简体</el-checkbox
              >
            </el-form-item>
            <el-form-item label="实体扩展">
              <el-tooltip
                :content="tips.extend"
                style="position: absolute; left: -140px; top: 15px"
                placement="right"
              >
                <i class="el-icon-question"></i>
              </el-tooltip>
              <el-radio-group
                class="personal-table-expand-radio"
                v-model="props.row.extend"
                style="padding-top: 16px"
              >
                <el-radio label="none" style="width: 97px">不扩展</el-radio>
                <el-radio label="location" style="width: 98px"
                  >地名扩展</el-radio
                >
                <el-radio label="name">人名扩展</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-form>
        </template>
      </el-table-column>
      <el-table-column width="200">
        <template slot="header" slot-scope="scope">
          <os-table-qahead label="资源名称" :tip="tips.resname" />
        </template>
        <template slot-scope="scope">
          <span :title="scope.row.name">{{ scope.row.name }}</span>
        </template>
      </el-table-column>
      <el-table-column width="280">
        <template slot="header" slot-scope="scope">
          <os-table-qahead label="资源维度" :tip="tips.title" />
        </template>
        <template slot-scope="scope">
          <span v-if="scope.row.type === 3">
            <span v-if="osResourceTitle.includes(scope.row.title)">{{
              scope.row.title | personalEntityType
            }}</span>
            <span v-else
              >{{ scope.row.type | personalEntityType }}：{{
                scope.row.title
              }}</span
            >
          </span>
          <span v-else>{{ scope.row.type | personalEntityType }}</span>
        </template>
      </el-table-column>
      <el-table-column width="200" label="词条名字段">
        <template slot="header" slot-scope="scope">
          <!-- <os-table-qahead label="词条名字段" :tip="tips.fileds" /> -->
          <os-table-qahead label="词条名字段" />
        </template>
        <template slot-scope="scope">
          <el-form
            :model="scope.row"
            :ref="`mainFieldForm` + scope.$index"
            label-width="0px"
            class="one-input-form"
          >
            <el-form-item
              prop="mainField"
              :rules="[
                $rules.required('主字段不能为空'),
                $rules.lengthLimit(1, 20, '主字段长度不能超过20个字符'),
                { validator: checkMainField, trigger: 'blur' },
                { validator: checkJson, trigger: 'blur' },
              ]"
            >
              <el-input
                :ref="'dictName' + scope.row.name"
                class="personal-value"
                size="small"
                placeholder="仅支持一个主字段"
                :title="scope.row.mainField"
                v-model="scope.row.mainField"
              />
              <input type="text" style="display: none" />
            </el-form-item>
          </el-form>
        </template>
      </el-table-column>
      <el-table-column label="词条别名字段 (非必填)">
        <template slot-scope="scope">
          <el-form
            :model="scope.row"
            :ref="`slaveFieldForm` + scope.$index"
            label-width="0px"
            class="one-input-form"
          >
            <el-form-item
              prop="slaveField"
              :rules="[
                { validator: checkSlaveField, trigger: 'blur' },
                { validator: checkJson, trigger: 'blur' },
              ]"
            >
              <el-input
                :ref="'alias' + scope.row.name"
                class="personal-value"
                size="small"
                placeholder="最多支持10个从字段，英文逗号隔开"
                :title="scope.row.slaveField"
                v-model="scope.row.slaveField"
              />
              <input type="text" style="display: none" />
            </el-form-item>
          </el-form>
        </template>
      </el-table-column>
    </os-table>
    <secret-key-dialog :dialog="secretKeyDialog" />
    <add-resource
      :dialog="addResourceDialog"
      :oldList="oldList"
      @getData="getData"
    />
  </div>
</template>

<script>
import dicts from '@M/dicts'
import SecretKeyDialog from '../handlePlatform/dialog/secretKey.vue'
import AddResource from './dialog/addResource'
export default {
  props: ['entityId'],
  data() {
    return {
      saving: false,
      tableData: {
        loading: true,
        total: 0,
        page: 1,
        size: 10,
        handles: ['del'],
        handleColumnText: '',
        list: [],
      },
      oldList: [],
      type: dicts.getDictArr('personalEntityType'),
      changed: false,
      tips: {},
      fieldValidate: true,
      secretKeyDialog: {
        show: false,
      },
      selectedRowId: -1,
      selectedColumnId: -1,
      addResourceDialog: {
        show: false,
      },
      osResourceTitle: ['os_client_id', 'os_device_id', 'os_user_id'],
    }
  },
  computed: {
    edited() {
      return JSON.stringify(this.oldList) != JSON.stringify(this.tableData.list)
    },
  },
  created() {
    this.getData()
  },
  watch: {
    edited(val) {
      if (val) {
        this.$emit('setDynamicChanged', true)
      }
    },
  },
  methods: {
    openSecretKey() {
      this.secretKeyDialog = {
        show: true,
      }
    },
    getData() {
      let self = this
      this.tableData.loading = true
      this.$utils.httpGet(
        this.$config.api.STUDIO_ENTITY_PERSONAL_DETAIL,
        {
          entityId: this.entityId,
        },
        {
          success: (res) => {
            let list = []
            if (res.data.personals && res.data.personals.length) {
              list = Array.prototype.map.call(
                res.data.personals,
                (item, index) => {
                  item.fields = item.fields
                    ? JSON.parse(item.fields)
                    : {
                        'col_name:s': [],
                        'col_name:m': '',
                      }
                  let newItem = {
                    personalId: item.pId || -1,
                    name: item.pName || '',
                    type: item.type,
                    title: item.title || '',
                    mainField: item.fields['col_name:m'],
                    slaveField: item.fields['col_name:s'].join(','),
                    preprocess: item.preprocess
                      ? item.preprocess.split(',')
                      : ['0', '0', '0'],
                    extend: item.extend ? item.extend : '',
                    disabled: false,
                  }
                  return newItem
                }
              )
            }
            this.tips = res.data.tips
            this.oldList = JSON.parse(JSON.stringify(list))
            this.tableData.list = list
            this.tableData.loading = false
          },
          error: (err) => {},
        }
      )
    },
    checkJson(rule, val, callback) {
      if (/\/$/.test(val)) {
        return callback(new Error('Json Pointer格式不正确'))
      }
      let keyword = 'none'
      if (val.toLowerCase() == keyword) {
        return callback(new Error('不能有none字段'))
      }
      let arr = val.split('/')
      if (arr && arr.length > 4) {
        return callback(new Error('最多支持4层'))
      }
      for (let i = 0; i < arr.length; i++) {
        if (arr[i].toLowerCase() == keyword) {
          return callback(new Error('不能有none字段'))
        }
      }
      callback()
    },
    checkMainField(rule, val, callback) {
      if (!val) {
        callback()
        return
      }
      let reg = /^[a-zA-Z0-9_/]{0,}$/
      if (!reg.test(val)) {
        return callback(new Error('仅支持字母/数字/下划线'))
      }
      callback()
    },
    checkSlaveField(rule, val, callback) {
      if (!val) {
        callback()
        return
      }
      if (val.split(',').length > 10) {
        callback(new Error('最多支持10个从字段'))
        return
      }
      let reg = /^[a-zA-Z0-9_,/]{0,}$/
      if (!reg.test(val)) {
        return callback(new Error('仅支持字母/数字/下划线/逗号'))
      }
      callback()
    },
    addPersonal() {
      if (this.tableData.list.length >= 5) {
        return this.$message.warning('资源数量不能超过5个')
      }
      this.addResourceDialog = {
        show: true,
      }
    },
    checkForm() {
      let len = this.tableData.list.length
      for (let i = 0; i < len; i++) {
        this.$refs['mainFieldForm' + i].validate((valid) => {
          if (!valid) {
            this.fieldValidate = false
            return
          }
        })
        this.$refs['slaveFieldForm' + i].validate((valid) => {
          if (!valid) {
            this.fieldValidate = false
            return
          }
        })
      }
    },
    onSubmit(optionType) {
      let self = this
      let data = Array.prototype.map.call(
        this.tableData.list,
        function (item, index) {
          let newItem = self.$deepClone(item)
          newItem.preprocess = newItem.preprocess.join(',')
          return newItem
        }
      )

      this.fieldValidate = true
      this.checkForm()
      if (!this.fieldValidate) {
        return
      }
      this.$utils.httpPost(
        this.$config.api.STUDIO_ENTITY_PERSONAL_SAVE,
        {
          entityId: this.entityId,
          dynamicJson: JSON.stringify(data),
        },
        {
          success: (res) => {
            let message = optionType === 'del' ? '删除成功' : '保存成功'
            self.$message.success(message)
            self.getData()
            self.$emit('change')
            this.$emit('setDynamicChanged', false)
          },
          error: (err) => {},
        }
      )
    },
    noSave() {
      this.tableData.list = this.$deepClone(this.oldList)
    },
    toDel(item, index) {
      let self = this
      if (this.tableData.list.length <= 1) {
        return this.$message.warning('需保留至少一个资源')
      }
      this.$confirm('删除后不可恢复，需要重新填写。', '确定删除改资源设置？', {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        confirmButtonClass: 'el-button--danger',
        type: 'warning',
        showClose: false,
      })
        .then(() => {
          self.tableData.list.splice(index, 1)
          self.oldList.splice(index, 1)
          self.onSubmit('del')
        })
        .catch(() => {})
    },
    handleCellMouseEnter(row, column, cell) {
      if (column.label && column.label.match(/^词条/g)) {
        cell.classList.add('entity-table__border-hover')
      }
    },
    handleCellMouseLeave(row, column, cell) {
      cell.classList.remove('entity-table__border-hover')
    },
    handleCellClick(row, column) {
      let self = this
      if (column.label && column.label.match(/^词条/g)) {
        this.selectedRowId = row.name
        this.selectedColumnId = column.label
        if (column.label === '词条名字段') {
          setTimeout(function () {
            self.$refs[`dictName${row.name}`] &&
              self.$refs[`dictName${row.name}`].$refs.input.focus()
          }, 4)
        } else {
          setTimeout(function () {
            self.$refs[`alias${row.name}`] &&
              self.$refs[`alias${row.name}`].$refs.input.focus()
          }, 4)
        }
      } else {
        this.selectedRowId = -1
        this.selectedColumnId = -1
      }
    },
    cellClassName({ row, column }) {
      if (
        this.selectedRowId === row.name &&
        this.selectedColumnId === column.label
      ) {
        return 'entity-table__border'
      } else {
        return ''
      }
    },
    downloadTemplate() {
      window.open(
        `${this.$config.server}/aiui/web/download?url=https://aiui-file.cn-bj.ufileos.com/entity_upload.txt&fileName=entity_upload.txt&code=UTF-8`,
        '_self'
      )
    },
    handleClickOutSide(e) {
      this.selectedRowId = -1
      this.selectedColumnId = -1
    },
  },
  components: {
    SecretKeyDialog,
    AddResource,
  },
}
</script>

<style lang="scss">
.dynamic-entity {
  .btns-wrap {
    font-size: 0;
    :deep(.el-button) {
      margin-left: 0;
      &:nth-child(2) {
        margin-left: 8px;
        vertical-align: bottom;
        border-right: 1px solid $grey2;
      }
    }
  }
}
.dynamic-entity__personal-table {
  margin-bottom: 100px;
  .el-table__row td {
    padding: 0;
    height: 56px;
  }
  .el-table__body tr:hover > td {
    background-color: #fff;
  }
  &-expand {
    padding: 12px 10px;
    .el-form-item__label,
    .el-form-item__content {
      line-height: 36px;
    }
    .el-form-item:last-child {
      margin-bottom: 0;
    }
    &-radio {
      height: 36px;
      display: flex;
      align-items: center;
    }
  }
  .el-icon-question {
    color: $grey3;
  }
}
.personal-select {
  width: 80px;
  input {
    border: 0;
    padding-left: 0;
  }
}
.personal-value {
  input {
    height: 54px !important;
    line-height: 54px !important;
    border: 0;
    padding-left: 0;
    color: $semi-black;
    font-weight: 600;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
.personal-input {
  width: 160px;
  input {
    height: 54px !important;
    line-height: 54px !important;
    border: 0;
    padding-left: 0;
    color: $semi-black;
    font-weight: 600;
  }
}

.el-tooltip__popper {
  max-width: 500px;
  line-height: 180%;
}
.entity-table__border,
.entity-table__border-hover {
  border: 1px solid #8bc2f4 !important;
  z-index: 10;
}
</style>
