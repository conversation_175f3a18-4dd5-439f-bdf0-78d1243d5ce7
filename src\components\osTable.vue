<template>
  <div class="os-table">
    <el-table
      v-loading="data.loading"
      :data="data.list"
      ref="table"
      :class="{ 'os-border-table': $attrs.border, mgb24: showPagination }"
      @handleSwitch="handleSwitch"
      v-bind="$attrs"
      v-on="$listeners"
    >
      <slot></slot>
      <template v-if="handleOptions.handleColumn">
        <el-table-column
          :width="handleOptions.width"
          :label="tableData.handleColumnText"
        >
          <template v-slot="scope">
            <div class="cell-handle">
              <!-- Edit -->
              <!-- <el-tooltip
                v-if="handleOptions.edit && !scope.row.noEdit"
                effect="dark"
                content="编辑"
                placement="top"
              >
                <i
                  class="ic-r-edit cell-handle-ic"
                  @click.stop="edit(scope.row, scope.$index)"
                ></i>
              </el-tooltip> -->
              <span
                v-if="handleOptions.edit && !scope.row.noEdit"
                @click.stop="edit(scope.row, scope.$index)"
                >编辑</span
              >
              <!-- Share -->
              <i
                v-if="handleOptions.share && !scope.row.noShare"
                class="ic-r-data cell-handle-ic"
                @click.stop="$emit('share', scope.row, scope.$index)"
              ></i>
              <!-- Copy -->
              <!-- <i
                v-if="handleOptions.copy && !scope.row.noCopy"
                class="ic-r-copy cell-handle-ic"
                @click.stop="$emit('copy', scope.row, scope.$index)"
              ></i> -->
              <span
                v-if="handleOptions.copy && !scope.row.noCopy"
                @click.stop="$emit('copy', scope.row, scope.$index)"
                >复刻</span
              >
              <!-- Play -->
              <i
                v-if="handleOptions.play && !scope.row.noPlay"
                :class="[
                  'ic-r-play cell-handle-ic',
                  { 'ic-r-pause': onPlayIndex === scope.$index },
                ]"
                @click.stop="play(scope.row, scope.$index)"
              ></i>
              <!-- Delete -->
              <!-- <i
                v-if="handleOptions.del && !scope.row.noDel"
                class="cell-handle-hovershow ic-r-delete cell-handle-ic"
                @click.stop="$emit('del', scope.row, scope.$index)"
              ></i> -->
              <span
                v-if="handleOptions.del && !scope.row.noDel"
                @click.stop="$emit('del', scope.row, scope.$index)"
                >删除</span
              >
              <!-- Switch -->
              <el-switch
                v-if="handleOptions.switchBtn && !scope.row.noSwitchBtn"
                class="cell-handle-hovershow"
                :active-value="data.switchOptions.active"
                :inactive-value="data.switchOptions.inactive"
                :active-text="
                  scope.row[data.switchOptions.column] ===
                  data.switchOptions.active
                    ? data.switchOptions.activeText
                    : data.switchOptions.inactiveText
                "
                v-model="scope.row[data.switchOptions.column]"
                @change="handleSwitch(scope.row, scope.$index)"
              ></el-switch>
              <!-- Tips -->
              <el-tooltip
                v-if="handleOptions.tips && !scope.row.noTips"
                class="cell-handle-hovershow"
                effect="dark"
                :content="scope.row.tip || data.tips.content"
                placement="bottom"
              >
                <i :class="data.tips.icon"></i>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
      </template>
    </el-table>

    <!-- Pagination -->
    <template v-if="showPagination">
      <template v-if="data.simplePagination">
        <os-pagination
          :total="data.total"
          :size="data.size"
          v-model="data.page"
          @input="pageChange"
          :class="{ 'txt-al-c': !tableData.pageLeft }"
        ></os-pagination>
      </template>
      <template v-else>
        <el-pagination
          ref="pagination"
          :current-page="data.page"
          :page-size="data.size"
          :total="data.total"
          :layout="pageLayout"
          @current-change="pageChange"
          :class="{ 'txt-al-c': !tableData.pageLeft }"
        ></el-pagination>
      </template>
    </template>
  </div>
</template>

<script>
export default {
  name: 'OsTable',
  props: {
    tableData: Object,
  },
  data() {
    return {
      oldList: [],
      data: this.tableData,
      onPlayIndex: null,
      change: true,
      currentChange: false,
    }
  },
  computed: {
    pageLayout() {
      return this.data.total > 10
        ? 'prev, pager, next, jumper, total'
        : 'prev, pager, next'
    },
    showPagination() {
      return (
        this.data.total > this.data.size || this.tableData.showPaginationForever
      )
    },
    handleOptions() {
      console.log('this.tableData=>', this.tableData)
      const e = this.tableData.handles || []
      const t = {
        handleColumn: e.length,
        width: 60 * e.length,
        edit: this._inArray(this.tableData.handles, 'edit'),
        copy: this._inArray(this.tableData.handles, 'copy'),
        share: this._inArray(this.tableData.handles, 'share'),
        play: this._inArray(this.tableData.handles, 'play'),
        del: this._inArray(this.tableData.handles, 'del'),
        switchBtn: this._inArray(this.tableData.handles, 'switch'),
        tips: this._inArray(this.tableData.handles, 'tips'),
      }
      if (t.switchBtn) {
        t.width += 50
      }
      console.log('t=>', t)
      return t
    },
  },
  watch: {
    'tableData.list'(e, t) {
      this.oldList = this._cloneArr(e)
    },
    'data.page'(e) {
      if (this.change && !this.currentChange) {
        this.$emit('change', e)
      } else {
        this.currentChange = false
      }
    },
  },
  methods: {
    doLayout() {
      const t = this
      const dataCopy = this.data
      this.change = false
      this.data = {}
      this.$nextTick(() => {
        t.data = dataCopy
        t.$emit('doLayoutCb')
        t.$nextTick(() => {
          t.change = true
        })
      })
    },
    pageChange(e) {
      this.currentChange = true
      this.$emit('change', e)
    },
    edit(e) {
      e._actived = !!this.data.editable
      this.$emit('edit', e)
      this.doLayout()
    },
    // cancel(e) {
    //   e._actived = !e._actived
    //   this.$emit('cancel', e)
    //   this.doLayout()
    // },
    del(e) {
      this.$emit('del', e)
    },
    toggleRowSelection(e, selected) {
      this.$refs.table.toggleRowSelection(e, selected)
    },
    handleSwitch(e, t) {
      if (
        this.oldList[t] &&
        e[this.data.switchOptions.column] !==
          this.oldList[t][this.data.switchOptions.column]
      ) {
        this.$emit('switch', e)
        this.oldList = this._cloneArr(this.data.list)
      }
    },
    _inArray(e, t) {
      if (!e) return false
      const i = String.fromCharCode(2)
      return new RegExp(i + t + i).test(i + e.join(i) + i)
    },
    _cloneArr(e) {
      const t = []
      for (let i = 0; i < e.length; i++) {
        t.push(this._cloneObj(e[i]))
      }
      return t
    },
    _cloneObj(e) {
      const t = {}
      for (const i in e) {
        t[i] = e[i]
      }
      return t
    },
    play(e, t) {
      this.onPlayIndex = this.onPlayIndex === t ? null : t
      this.$emit('play', e, t)
    },
    pauseAll() {
      this.onPlayIndex = null
    },
  },
}
</script>
<style lang="scss" scoped>
.os-table {
  :deep(.el-table .cell-handle-ic) {
    line-height: 1;
  }
  :deep(.el-table) {
    .el-table__header-wrapper tr th div {
      color: #8d8d99;
    }
  }
  :deep(.el-table::before) {
    height: 0 !important;
    display: none;
  }
  .cell-handle {
    display: flex;
    gap: 20px;
    > span {
      cursor: pointer;
      color: $primary;
      &:hover {
        color: $hover;
      }
    }
  }
}
</style>
