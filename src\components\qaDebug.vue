<template>
  <div class="debug-wrap">
    <div class="debug-header">
      <div class="collapse-btn" @click="closeRightTest">
        <i class="iconfont icon-zhankai"></i>
        <span>问答库体验</span>
      </div>
    </div>
    <div class="clear" @click="cleanHistory">
      <i class="iconfont icon-shanchu"></i>
    </div>

    <div class="debug-dialog" ref="list">
      <div
        :class="['dialog dialog-' + item.type]"
        v-for="(item, index) in dialogList"
        :key="index"
      >
        <template v-if="item.type === 'answer'">
          <div class="msg-answer-item">
            <i class="robot-avatar"></i>
            <div class="ib message">
              <template v-if="item.data.answer">
                <div class="msg-item">
                  <span>{{ item.data[item.type] }}</span>
                </div>

                <div class="view-button-group">
                  <a
                    class="view-button"
                    v-if="!!item.data.res"
                    @click="openJsonDialog(item.data)"
                    ><i class="iconfont icon-chakan"></i
                    ><span style="white-space: nowrap">&nbsp;查看JSON</span></a
                  >
                </div>
              </template>
            </div>
          </div>
        </template>
        <div class="ib message" v-else>
          {{ item.data[item.type] }}
        </div>
      </div>
    </div>

    <div class="send-wrap">
      <el-input
        class="debug-input"
        :maxlength="120"
        v-model="question"
        size="medium"
        @keyup.enter.native="experience"
        @keyup.up.native="preQuestion"
        @keyup.down.native="nextQuestion"
        placeholder="输入文本，回车体验"
      ></el-input>
      <div
        :class="['debug-send', { 'debug-send-active': question }]"
        @click="experience"
      >
        <svg-icon iconClass="send" />&nbsp;发送
      </div>
    </div>

    <el-dialog
      class="debug-json-dialog"
      title="JSON"
      :visible.sync="showJson"
      width="50%"
    >
      <div class="request-json">
        <template>
          <i class="ic-r-copy" title="复制代码" @click="copyJson(resJson)"></i>
          <json-view class="json-wrap" :data="resJson"></json-view>
        </template>
      </div>
      <div class="dialog-bottom"></div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'qa-debug',
  props: ['debugType'],
  data() {
    return {
      submitting: false,
      rc4Answer: [
        '啊哦~~这个问题太难了，换个问题吧！',
        '不好意思，我好像没听懂。。。',
      ],
      rc4Index: 0,
      question: '',
      questionList: [],
      questionIndex: 0, //记录按上下键问题开始位置
      uid: this.$utils.experienceUid(),
      dialogList: [
        {
          type: 'answer',
          data: {
            answer:
              '你好，我是智能的小飞～你可以点击构建，然后测试问答库效果(^_-)～',
          },
        },
      ],
      showJson: false,
      resJson: {},
    }
  },
  computed: {
    ...mapGetters({
      qa: 'studioQa/qa',
    }),
  },
  created() {},
  updated() {
    if (this.$refs.list) {
      this.$refs.list.scrollTop = 100000
    }
  },
  methods: {
    preQuestion() {
      if (this.questionIndex > 0) {
        this.questionIndex--
        this.question = this.questionList[this.questionIndex]
      } else {
        this.question = ''
        this.questionIndex = -1
      }
    },
    nextQuestion() {
      if (this.questionIndex < this.questionList.length) {
        this.questionIndex++
        this.question = this.questionList[this.questionIndex]
      } else {
        this.question = ''
        this.questionIndex = this.questionList.length
      }
    },
    addDialog(type, data) {
      this.dialogList.push({
        type: type,
        data: data,
        httpCode: null,
      })
    },

    experience() {
      if (this.submitting) return
      if (!this.question) return

      let self = this
      let api =
        this.debugType === 'qa'
          ? this.$config.api.STUDIO_QA_EXPERIENCE
          : this.$config.api.STUDIO_KEY_QA_EXPERIENCE
      let data =
        this.debugType === 'qa'
          ? {
              repoId: this.$store.state.studioQa.id,
              text: this.question.trim(),
              tyuid: this.uid,
            }
          : {
              qaId: this.$route.params.qaId,
              text: this.question.trim(),
            }

      this.submitting = true
      this.$utils.httpPost(api, data, {
        success: (res) => {
          this.submitting = false
          if (res.flag) {
            self.addDialog('question', {
              question: self.question.trim(),
            })
            self.questionList.push(self.question)
            self.questionIndex = self.questionList.length
            self.question = ''
            if (this.debugType == 'keyqa') {
              let result = JSON.parse(res.data.result || '{}')
              if (result.header.code == 0) {
                let text = JSON.parse(result.payload.UBotQA.text)
                console.log('text------', text)
                let obj = {
                  rc: text.rc,
                }
                res.data.answer = text.answer.text
                res.data.res = obj
              } else {
                let message = JSON.parse(result.header.message)
                console.log('message------', message)
                if (message.code == 4) {
                  res.data.res = { rc: 4 }
                }
              }
            } else {
              res.data.res = res.data.res
                ? JSON.parse(res.data.res)
                : res.data.res
            }

            if (res.data.res && res.data.res.rc == 4) {
              res.data.answer = self.rc4Answer[self.rc4Index]
              self.rc4Index = 1
            }

            // 判断是否构建
            if (!res.data.isStructure) {
              res.data.answer += '(您的问答库尚未构建)'
            }
            self.addDialog('answer', res.data)
          }
        },
        error: (err) => {
          this.submitting = false
        },
      })
    },

    copyJson(data) {
      this.$utils.copyClipboard(JSON.stringify(data, null, '    '))
    },
    openJsonDialog(datas) {
      this.showJson = !this.showJson
      if (this.debugType === 'qa') {
        if (datas.hasOwnProperty('res') && datas.res) {
          this.resJson = datas.res
        }
      } else {
        console.log('openJsonDialog', JSON.parse(datas.result))
        this.resJson = JSON.parse(datas.result)
      }
    },
    closeRightTest() {
      this.$store.dispatch('studioSkill/setRightTestOpen', false)
    },
    cleanHistory() {
      this.dialogList = [
        {
          type: 'answer',
          data: {
            answer: '你好，我是智能的小飞~',
          },
        },
      ]
    },
  },
}
</script>

<style lang="scss" scoped>
@import './debug.scss';
</style>
