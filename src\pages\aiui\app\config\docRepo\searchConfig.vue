<template>
  <el-form
    ref="form"
    :model="form"
    label-width="120px"
    style="padding-top: 10px"
  >
    <el-form-item label="检索方法">
      <el-col :span="8">
        <el-select
          :value="form.channel"
          placeholder="请选择检索方式"
          @change="onSelectChange"
          style="width: 170px"
        >
          <el-option label="单向量召回" :value="1"></el-option>
          <el-option
            label="多路召回"
            :value="2"
          ></el-option> </el-select></el-col
      ><el-col :span="8">
        {{
          form.channel === 2
            ? '混合多种方式召回知识点'
            : '使用语义向量召回知识点'
        }}
      </el-col>
    </el-form-item>

    <el-form-item label="相关性阈值">
      <el-col :span="8">
        <el-input-number
          style="width: 170px"
          :value="form.threshold"
          :min="0"
          :max="1"
          :precision="2"
          :step="0.01"
          :step-strictly="true"
          @change="onInputChange"
        ></el-input-number>
      </el-col>

      <el-col :span="8"> 取值为0-1 </el-col>
    </el-form-item>
  </el-form>
</template>
<script>
export default {
  props: {
    form: {
      type: Object,
      default() {
        return {
          channel: 2,
          threshold: 0.1,
        }
      },
    },

    showSearchConfig: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {}
  },
  methods: {
    show() {},
    getConfig() {},
    onInputChange(val) {
      console.log('onInputChange', val)
      this.$emit('change', 'threshold', val)
    },
    onSelectChange(val) {
      this.$emit('change', 'channel', val)
    },

    cancel() {
      this.$emit('searchConfigCancel')
    },
  },
}
</script>
<style lang="scss" scoped></style>
