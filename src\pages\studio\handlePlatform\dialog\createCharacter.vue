<template>
  <el-dialog
    :title="dialog.type === 'create' ? '创建人设' : '保存为新的人设'"
    :visible.sync="dialog.show"
    width="480px"
  >
    <p class="skill-introduction" style="margin-bottom: 5px">
      另存成功后将复制一个当前人设，支持单独编辑使用。
    </p>
    <el-form
      :model="form"
      :rules="rules"
      ref="characterForm"
      label-position="top"
    >
      <div v-if="dialog.type === 'save'">
        <el-form-item label="名称" prop="name" ref="characterFormItem">
          <el-input
            v-model.trim="form.name"
            placeholder="支持汉字/字母/数字，不超过32字符"
            ref="nameInput"
            @keyup.enter.native="save"
          />
          <input type="text" style="display: none" />
        </el-form-item>
      </div>
      <div v-else>
        <p class="note-msg" v-if="showMess">
          副本的名称超过字数限制，请重新命名。
        </p>
        <el-form-item label="名称" prop="name">
          <el-input
            v-model.trim="form.name"
            placeholder="支持汉字/字母/数字，不超过32字符"
            ref="nameInput"
            @keyup.enter.native="create"
          />
          <input type="text" style="display: none" />
        </el-form-item>
      </div>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button
        class="dialog-btn"
        type="primary"
        style="min-width: 104px"
        @click="save"
        :loading="saving"
        v-if="dialog.type === 'save'"
        >{{ saving ? '保存中...' : '确定' }}</el-button
      >
      <el-button
        class="dialog-btn"
        type="primary"
        style="min-width: 104px"
        @click="create"
        :loading="saving"
        v-else
        >{{ saving ? '创建中...' : '创建' }}</el-button
      >
    </span>
  </el-dialog>
</template>

<script>
import dicts from '@M/dicts'

export default {
  props: {
    dialog: {
      type: Object,
      default: {},
    },
  },
  data() {
    let reg = /^[a-zA-Z0-9\u4e00-\u9fff]+$/
    let validateName = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入名称'))
      } else if (value.length > 32) {
        callback(new Error('人设名称长度不能超过32个字符'))
      } else if (!reg.test(value)) {
        callback(new Error('人设名称仅支持汉字、字母、数字'))
      } else {
        callback()
      }
    }
    return {
      saving: false,
      form: {
        name: '',
      },
      rules: {
        // name: [
        //   this.$rules.required("设备人设名称不能为空"),
        //   this.$rules.lengthLimit(1, 32, "设备人设名称长度不能超过32个字符"),
        //   this.$rules.baseRegLimit()
        // ]
        name: [{ validator: validateName, trigger: 'blur' }],
      },
    }
  },
  computed: {
    showMess() {
      return this.dialog.type === 'copy' && this.dialog.name.length > 16
    },
  },
  watch: {
    'dialog.show': function (val, oldVal) {
      let self = this
      if (val) {
        ;(this.form = {
          name: '',
        }),
          this.$refs.characterForm && this.$refs.characterForm.resetFields()
        this.$nextTick(function () {
          self.$refs.nameInput && self.$refs.nameInput.focus()
        })
      } else {
        this.saving = false
      }
    },
  },
  mounted() {},
  methods: {
    //创建人设
    create() {
      let self = this
      if (this.saving) {
        return
      }
      let data
      this.$refs.characterForm.validate((valid) => {
        if (valid) {
          this.saving = true
          if (this.dialog.type === 'copy') {
            data = {
              fromId: this.dialog.id,
              name: this.form.name,
            }
          } else {
            data = {
              name: this.form.name,
            }
          }

          let api = this.$config.api.STUDIO_CHARACTER_ADD
          this.$utils.httpPost(api, data, {
            success: (res) => {
              this.saving = false
              self.$message.success('创建成功')
              self.$router.push({
                name: 'character',
                params: { characterId: res.data.id },
              })
            },
            error: (err) => {
              this.saving = false
            },
          })
        }
      })
    },
    //保存人设副本
    save() {
      let self = this
      if (this.saving) {
        return
      }
      this.$refs.characterForm.validate((valid) => {
        if (valid) {
          this.saving = true
          let data = {
            name: this.form.name,
          }
          this.$emit('saveAsHandle', 'copy', this.form.name)
        }
      })
    },
    close() {
      this.saving = false
    },
  },
}
</script>
<style lang="scss" scoped>
.note-msg {
  width: 416px;
  height: 40px;
  padding-left: 20px;
  margin-bottom: 20px;
  background: #fff8ed;
  border-radius: 2px;
  height: 22px;
  font-size: 14px;
  font-weight: 400;
  color: rgba(38, 38, 38, 1);
  line-height: 22px;
  // display: none;
  // &.active{
  //   display: block;
  // }
}
</style>
