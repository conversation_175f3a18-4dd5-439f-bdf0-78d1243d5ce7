<template>
  <div>
    <div class="chain-wrapper">
      <div class="chain-list swiper" id="product-swiper">
        <div class="swiper-wrapper">
          <div
            :class="{
              'chain-unit ability-unit': true,
              'swiper-slide': true,
              active: currentProduct.productId === item.productId,
              disabled: item.disabled,
            }"
            v-for="item in products"
            :key="item.productName"
            @click="setActive(item)"
          >
            <div class="chain-title">{{ item.productName }}</div>
            <div
              class="chain-desc"
              :title="item.desc.replace('<br/>', '\n')"
              v-if="item.desc"
              v-html="item.desc"
            ></div>

            <!-- <div
              class="chain-icon2"
              :style="{
                backgroundImage: 'url(' + item.icon + ')',
              }"
            ></div> -->
          </div>
        </div>
      </div>

      <div
        class="indicator indicator-pre"
        v-if="currentPage > 1"
        @click="prePage"
      >
        <i class="el-icon-arrow-left"></i>
      </div>
      <div
        class="indicator indicator-next"
        v-if="currentPage < pageNums"
        @click="nextPage"
      >
        <i class="el-icon-arrow-right"></i>
      </div>
    </div>

    <div class="chain-divider">
      <div class="chain-divider-line"></div>
      <span class="chain-divider-text">产品品类选择</span>
    </div>
  </div>
</template>
<script>
import Swiper from 'swiper'

const PAGESIZE = 4

export default {
  data() {
    return {
      products: [],
      currentPage: 1,
      swiper: null,
    }
  },
  computed: {
    pageNums() {
      return Math.ceil(this.products.length / 4)
    },
  },
  props: {
    currentProduct: Object,
  },
  created() {
    this.getProducts()
  },

  methods: {
    setCurrentPage(index) {
      this.currentPage = index + 1
      this.swiper && this.swiper.slideTo(index * PAGESIZE)
    },
    nextPage() {
      if (this.currentPage < this.pageNums) {
        this.currentPage++
        this.swiper && this.swiper.slideNext()
      }
    },
    prePage() {
      if (this.currentPage > 1) {
        this.currentPage--
        this.swiper && this.swiper.slidePrev()
      }
    },
    getProducts() {
      this.products = [
        {
          productId: 1,
          productName: '泛屏品类',
          desc: `面向电视、投影仪、闺蜜机等设备<br/>打造大模型影视顾问，支持剧情点播`,
        },
        {
          productId: 2,
          productName: '儿童教育品类',
          desc: `面向故事机、学习机等设备<br/>打造儿童鼓励式共情闲聊、更简短的知识问答`,
        },
      ]
      this.$emit('selected', this.products[0])
      this.$nextTick(() => {
        this.swiper = new Swiper('#product-swiper', {
          slidesPerView: PAGESIZE,
          spaceBetween: 10,
          slidesPerGroup: PAGESIZE,
        })
      })
    },
    setActive(item) {
      if (!item.disabled) {
        this.$emit('selected', item)
      }
    },
  },
  watch: {},
}
</script>
<style lang="scss" scoped>
@import './common.scss';
</style>
