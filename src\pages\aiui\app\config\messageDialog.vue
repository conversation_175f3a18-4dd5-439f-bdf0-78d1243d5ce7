<template>
  <el-dialog
    title="提示"
    :visible.sync="dialog.show"
    width="480px"
  >
  <p v-if="subAccount" style="line-height:22px" class="mgb24">
    多人协同操作可能会出现冲突，导致你的配置无法生效，建议与其他成员沟通后再操作。
  </p>
  <os-divider class="mgb24" v-if="subAccount && dialog.isCertificated" />
  <p v-if="dialog.isCertificated" style="line-height:22px" class="mgb24">
    AIUI应用支持测试环境了，已审核上线的应用，在应用配置页修改配置后，设备端只需要SDK传参时在情景模式后加“_box”即可体验配置效果；新的配置效果确认无误，更新发布后生产环境的应用配置将会更新。
  </p>
  <span slot="footer" class="dialog-footer">
    <el-button type="primary" @click="close">知道了</el-button>
  </span>
  </el-dialog>
</template>

<script>
import dicts from "@M/dicts"

export default {
  props: {
    dialog: {
      type: Object,
      default: {}
    },
    subAccount: Boolean
  },
  data () {
    return {
      show: false
    }
  },
  computed: {
    // dialog:{
    //   get: function () {
    //     console.log(this.dialog)
    //     return {
    //       show: this.$store.state.aiuiApp.app.check || false
    //     }
    //   },
    //   set: function (val) {
    //     this.dialog.show = false
    //   }
    // }
  },
  methods: {
    close() {
      let self = this
      self.dialog.show = false
    }
  },
  components: {

  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="scss" scoped>

</style>
