<template>
  <el-dialog
    :visible="docVisible"
    v-if="docVisible"
    @close="handleClose(false)"
    :title="title"
  >
    <div id="docx-content" ref="file" v-loading="loading"></div>
    <!-- <div id="txt-content"></div> -->
  </el-dialog>
</template>

<script>
import axios from 'axios'
import { renderAsync } from 'docx-preview'

export default {
  name: 'knowledge-docx-preview',
  props: {
    docVisible: {
      type: Boolean,
      default: false,
    },
    fileUrl: {
      type: String,
      default: '',
    },
    paragraphId: {
      type: Array,
      default: [],
    },
    title: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      loading: false,
    }
  },
  mounted() {
    ;(this.title == this.title) == ''
      ? this.fileUrl
        ? this.fileUrl.split('/').pop()
        : ''
      : this.title
  },
  computed: {
    fileName: function () {
      return this.fileUrl ? this.fileUrl.split('/').pop() : ''
    },
    fileType: function () {
      return this.fileUrl ? this.fileUrl.split('.').pop() : ''
    },
  },
  methods: {
    previewFile() {
      switch (this.fileType) {
        case 'docx':
          axios({
            method: 'get',
            responseType: 'blob',
            url: `/aiui/web/knowledge/file/download?fileUrl=${this.fileUrl}`,
          }).then(({ data }) => {
            try {
              renderAsync(data, this.$refs.file).then((res) => {
                this.loading = false
                this.handleTrace()
              })
            } catch (e) {
              this.$message.warning('当前知识点暂不支持溯源功能')
            }
          })
          break
        case 'txt':
          axios({
            method: 'get',
            responseType: 'blob',
            url: `/aiui/web/knowledge/file/download?fileUrl=${this.fileUrl}`,
          }).then(({ data }) => {
            this.loading = false
            let self = this
            console.log(this.paragraphId)
            let reader = new FileReader()
            reader.onload = function () {
              // 获取文本内容
              let textContent = reader.result
              console.log(textContent)
              let divElement = document.createElement('article')
              let lines = textContent.split('\n')
              lines.forEach(function (line) {
                let p = document.createElement('p')
                p.textContent = line
                divElement.appendChild(p)
              })
              let contentContainer = document.getElementById('docx-content')
              contentContainer.appendChild(divElement)
              self.handleTrace()
            }
            reader.readAsText(data)
          })
          break
        default:
          this.loading = false
      }
    },
    handleClose(val) {
      this.$emit('changeVisible', false)
    },
    handleTrace() {
      console.log('===this.paragraphId==', this.paragraphId)
      let articleAll = document.querySelectorAll('#docx-content article')
      let paragraphs = []
      articleAll.forEach(function (item) {
        item.childNodes.forEach(function (itemChildNodes) {
          paragraphs.push(itemChildNodes)
        })
      })
      // console.log("===articleAll==",articleAll);
      // console.log("===paragraphs==",paragraphs)
      this.paragraphId.forEach(function (index) {
        if (paragraphs[index]) {
          paragraphs[index].style.backgroundColor = '#EEE8AA' // 设置背景色
        }
      })
      // 将窗口滚动到第一个高亮标签的位置
      if (this.paragraphId.length > 0 && paragraphs[this.paragraphId[0]]) {
        paragraphs[this.paragraphId[0]].scrollIntoView({
          behavior: 'smooth',
          block: 'start',
        })
      }
    },
  },
  watch: {
    docVisible: function (val) {
      this.loading = true
      if (val) {
        this.previewFile()
      }
    },
  },
}
</script>
<style lang="scss" scoped>
#docx-content {
  min-height: 200px;
  max-height: 600px;
  overflow-y: scroll;
  overflow-x: hidden;
  padding: 10px;
}

:deep(.docx-wrapper) {
  background-color: #fff;
  padding: 0;
}

:deep(.docx-wrapper > section.docx) {
  width: 100% !important;
  padding: 0rem !important;
  min-height: auto !important;
  box-shadow: none;
  margin-bottom: 0;
}
</style>
