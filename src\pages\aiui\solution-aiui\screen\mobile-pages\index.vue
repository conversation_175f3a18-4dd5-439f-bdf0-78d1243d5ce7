<template>
  <div class="main-content">
    <MyHeader> </MyHeader>

    <section class="main-content-banner">
      <div class="banner-text">
        <h2>语音大屏调度解决方案</h2>

        <p class="banner-text-content">
          面向政务、交通、医疗、商显等大屏交互场景，语音交互赋能大屏一
          句话直达深层页面，让大屏更懂用户，提高业务办理和演示效率。
        </p>

        <div class="hor-btn">
          <el-button
            class="banner-text-button"
            @click="toConsole"
            type="primary"
            round
            >合作咨询</el-button
          >
        </div>
      </div>
    </section>

    <section class="section-nav">
      <ul class="nav-ul" id="nav-ul">
        <li
          v-for="item in nav_list"
          :key="item.id"
          @click="gotoSelection(item.id)"
        >
          {{ item.name }}
        </li>
      </ul>

      <i
        v-if="nav_list.length > 4"
        :class="[
          'nav-btn',
          nav_flag ? 'el-icon-caret-bottom' : 'el-icon-caret-top',
        ]"
        id="nav-btn"
        @click="handleNavExpand"
      ></i>
    </section>

    <section class="section section1">
      <h2>应用场景</h2>
      <p class="desc-title">
        应用于指挥调度、监控中心、多功能展厅等场景下，实现语音操控，打造智能化交互体验
      </p>

      <ul>
        <li v-for="(item, index) in app_scenario" :key="index" class="app">
          <img :src="item.src" :alt="item.alt" />
          <p class="app-text">{{ item.alt }}</p>
        </li>
      </ul>
    </section>

    <section class="section section2">
      <div class="bg">
        <h2>方案架构</h2>
        <div class="section-hor">
          <p>拾音设备</p>
          <p>语音语义服务</p>
          <p>业务后处理服务</p>
          <p>大屏系统</p>
        </div>
        <div class="section-ver">
          <p>内容播报</p>
          <p>调度控制</p>
          <p>知识问答</p>
        </div>
      </div>
    </section>

    <section class="section section3">
      <h2>方案优势</h2>

      <ul>
        <li v-for="item in pics" :key="item.index">
          <h3>{{ item.title }}</h3>
          <p>{{ item.sub_title }}</p>

          <img :src="item.src" alt="" v-if="item.visible" />

          <i
            class="el-icon-arrow-up my-up-btn"
            v-if="item.visible"
            @click="item.visible = !item.visible"
          ></i>
          <i
            class="el-icon-arrow-down my-down-btn"
            v-else
            @click="item.visible = !item.visible"
          ></i>
        </li>
      </ul>
    </section>

    <section class="section section4">
      <h2>合作案例</h2>
      <img
        src="../../../../../assets/images/solution/digital-screen-lamp/section-4-1.png"
        alt=""
      />
      <p class="title">能源电力语音调度</p>
      <p class="sub-title">
        某能源电力集团在数据大屏上集成语音调度解决方案，实现语音打开页面、控制风机、查询发电量、电力知识问答等功能。同时，方案提供声纹认证等能力，用于风机控制时的身份确认，替代传统鼠标和键盘繁杂操作的同时，提高语音操作的安全性。
      </p>
    </section>

    <section class="section section5">
      <h2>合作咨询</h2>

      <h3>提交信息，我们会尽快与您联系</h3>

      <div class="cooperation-btn" @click="toConsole">申请合作</div>
    </section>

    <section class="section section-footer">
      <div class="footer-title">
        联系我们
        <span class="arrow" @click="clickFooterArrow">▼</span>
      </div>
      <div class="content">
        <p>联系电话：19955108393</p>
        <p>开发者交流群：617903641</p>
        <p>商务技术支持：<EMAIL></p>
      </div>

      <div class="footer-title">
        友情链接
        <span class="arrow" @click="clickFooterArrow">▼</span>
      </div>
      <div class="content">
        <p>科大讯飞</p>
        <p>讯飞开放平台</p>
      </div>

      <div class="corporation-info">
        ©科大讯飞股份有限公司 皖ICP备05001217号
      </div>
    </section>
  </div>
</template>

<script>
import MyHeader from '@P/aiui/solution-aiui/mobile-header.vue'
export default {
  name: 'AiuiWebIndex',

  components: {
    MyHeader,
  },

  data() {
    return {
      nav_flag: true,

      expandFlag: true,
      nav_list: [
        { name: '应用场景', id: 1 },
        { name: '方案架构', id: 2 },
        { name: '方案优势', id: 3 },
        { name: '合作案例', id: 4 },
      ],

      app_scenario: [
        {
          alt: '指挥调度系统',
          src: require('../../../../../assets/images/solution/digital-screen-lamp/digital-section-1.png'),
        },
        {
          alt: '监控中心',
          src: require('../../../../../assets/images/solution/digital-screen-lamp/digital-section-2.png'),
        },
        {
          alt: '多功能展厅',
          src: require('../../../../../assets/images/solution/digital-screen-lamp/digital-section-3.png'),
        },
      ],

      pics: [
        {
          title: '大屏语音助手',
          sub_title: '提供配套的语音助手软件，启动即用，识别结果实时上屏',

          src: require('../../../../../assets/images/solution/digital-screen-lamp/digital-section-5.png'),
          visible: true,
        },
        {
          title: '近场交互准确度高',
          sub_title:
            '近场通过演示器收音，识别准确性高，同时演示器自带激光飞鼠键，可辅助演示',

          src: require('../../../../../assets/images/solution/digital-screen-lamp/digital-section-6.png'),
          visible: false,
        },
        {
          title: '远场交互抗噪性强',
          sub_title:
            '人脸、唇形、语音等多模技术融合，实现人声噪音抑制，5米内可走动交互',

          src: require('../../../../../assets/images/solution/digital-screen-lamp/digital-section-7.png'),
          visible: false,
        },
        {
          title: '企业专属的语义模型',
          sub_title: '根据业务场景训练专属的语义模型，并提供语义配置平台',
          src: require('../../../../../assets/images/solution/digital-screen-lamp/digital-section-8.png'),
          visible: false,
        },
        {
          title: '支持私有化部署',
          sub_title: '语音语义全链路服务支持私有化，业务数据不出外网更安全',
          src: require('../../../../../assets/images/solution/digital-screen-lamp/digital-section-9.png'),
          visible: false,
        },
      ],
    }
  },

  mounted() {},

  methods: {
    gotoSelection(id) {
      const section = document.getElementsByClassName(`section` + id)[0]
      if (section) {
        section.scrollIntoView({ behavior: 'smooth' })
      }
    },

    handleNavExpand() {
      const nav_ul = document.getElementById('nav-ul')
      const navIcon = document.getElementById('nav-btn')

      navIcon.addEventListener('click', () => {
        if (this.nav_flag) {
          nav_ul.classList.add('expanded')
        } else {
          nav_ul.classList.remove('expanded')
        }
        this.nav_flag = !this.nav_flag
      })
    },

    clickFooterArrow() {
      const titles = document.querySelectorAll('.footer-title')
      titles.forEach((title) => {
        title.addEventListener('click', () => {
          const content = title.nextElementSibling
          const arrow = title.querySelector('.arrow')

          if (content.style.display === 'none' || !content.style.display) {
            content.style.display = 'block'
            arrow.classList.add('up')
          } else {
            content.style.display = 'none'
            arrow.classList.remove('up')
          }
        })
      })
    },

    toConsole() {
      const search = window.location.search
      if (search) {
        window.open(`/solution/apply/3${search}`)
      } else {
        window.open('/solution/apply/3')
      }
    },

    gotoSDK() {
      window.open('https://aiui-doc.xf-yun.com/project-1/doc-418/')
    },

    gotoClick() {
      window.open('https://www.aifuwus.com/onstage/cmddetail?id=4434')
    },
  },
}
</script>

<style lang="scss" scoped>
.main-content {
  background-color: #f6f7f9;
  width: 750px;
  overflow: hidden;
  &-banner {
    background: url(~@A/images/solution/smart-hardware/mobile/banner2.jpg)
      center no-repeat;
    background-size: cover;
    height: 375px;
    overflow: hidden;
    width: 100%;
  }

  .banner-text {
    margin-top: 64px;
    padding-left: 44px;
    h2 {
      font-size: 48px;
      font-weight: 600;
      color: #000000;
      line-height: 68px;
    }
    p {
      width: 648px;
      font-size: 24px;
      font-weight: 300;
      color: #000000;
      line-height: 40px;
      text-align: left;
    }
    .hor-btn {
      display: flex;
      flex-direction: row;
      justify-content: flex-start;
      margin-top: 40px;
      div:nth-child(2) {
        margin-left: 30px;
      }
    }
  }

  .section {
    h2 {
      font-size: 48px;
      font-weight: 600;
      color: #000000;
      line-height: 68px;
      text-align: center;
      margin: 0 auto;
      margin-top: 50px;
      margin-bottom: 30px;
    }
    p,
    ul {
      margin-bottom: 0;
    }
    max-width: 750px;
    overflow: hidden;
    margin: 0 auto;
    .section-title {
      text-align: center;
      font-size: 36px;
      font-weight: 500;
      color: #444444;
      line-height: 54px;
      .arrow {
        width: 26px;
        height: 24px;
        display: inline-block;
        background-repeat: no-repeat;
        background-size: contain;
      }
      .arrow-left {
        background-position: left;
        background-image: url(~@A/images/solution/smart-hardware/arrow-left.png);
      }
      .arrow-right {
        background-position: right;
        background-image: url(~@A/images/solution/smart-hardware/arrow-right.png);
      }
    }
    .section-tabs {
      margin-top: 60px;
    }
  }

  .section-nav {
    background-color: #ffffff;
    position: relative;
    width: 100%;
    border: 1px solid #ddd;
    padding: 0 30px;
    .nav-ul {
      display: flex;
      justify-content: space-evenly;
      flex-wrap: wrap;
      overflow: hidden; /* 默认隐藏超出部分 */
      max-height: 68px; /* 折叠状态时，只显示一行 */
      transition: max-height 0.3s ease; /* 动画过渡效果 */
    }
    .nav-ul.expanded {
      max-height: 200px; /* 展开状态：允许显示多行 */
    }
    .nav-ul li {
      flex: 0 0 auto;
      margin-right: 30px;
      white-space: nowrap;
      cursor: pointer;
      font-size: 24px;
      font-weight: 400;
      text-align: center;
      line-height: 68px;
      color: #7a7a7a;
    }
    .nav-btn {
      position: absolute;
      height: 68px;
      width: 60px;
      right: 0px;
      top: 60%;
      transform: translateY(-50%);
      font-size: 48px;
      // background-color: red;
    }
  }

  .section1 {
    padding: 0 29px;

    .desc-title {
      font-size: 24px;
      text-align: center;
      color: #7a7a7a;
      margin-bottom: 20px;
    }

    > ul {
      li {
        width: 100%;
        // height: 400px;
        height: auto;
        margin-bottom: 60px;

        img {
          width: 100%;
          // height: 100%;
          margin-bottom: 10px;
        }

        p {
          height: 38px;
          text-align: center;
          margin: 0 auto;
          font-size: 24px;
          line-height: 38px;
          margin-bottom: 20px;
        }
      }
    }
  }

  .section2 {
    background: url(~@A/images/solution/digital-screen-lamp/bg2-section-3-1.png)
      center no-repeat;
    background-size: cover;
    width: 100%;
    position: relative;
    .bg {
      background: url(~@A/images/solution/digital-screen-lamp/bg-section-3-1.png)
        center no-repeat;
      background-size: cover;
      height: 400px;
      overflow: hidden;
      padding: 0;
      width: 100%;
      h2 {
        font-size: 48px;
        font-weight: 600;
        color: #000000;
        line-height: 68px;
        text-align: center;
      }
      .section-hor {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        position: absolute;
        bottom: 21%;
        left: 26%;
        p:nth-child(1) {
          font-size: 16px;
          font-weight: 600;
          text-align: center;
          color: #262626;
        }
        p:nth-child(2) {
          font-size: 16px;
          font-weight: 600;
          text-align: center;
          color: #262626;
          margin-left: 42px;
        }
        p:nth-child(3) {
          font-size: 16px;
          font-weight: 600;
          text-align: center;
          color: #262626;
          margin-left: 64px;
        }
        p:nth-child(4) {
          font-size: 16px;
          font-weight: 600;
          text-align: center;
          color: #262626;
          margin-left: 80px;
        }
      }
      .section-ver {
        position: absolute;
        right: 20px;
        top: 32%;
        display: flex;
        height: 154px;
        flex-direction: column;
        justify-content: space-between;
        p {
          font-size: 14px;
          font-weight: 400;
          text-align: left;
          color: #666666;
        }
      }
    }
  }

  .section3 {
    padding: 0 29px;
    ul {
      li {
        min-height: 133px;
        padding: 20px;
        padding-bottom: 30px;
        background: linear-gradient(180deg, #ffffff, #ffffff);
        border-radius: 30px;
        box-shadow: 0px -8px 10px 0px rgba(173, 173, 173, 0.11);
        margin-bottom: 20px;
        position: relative;
        h3 {
          font-size: 32px;
          height: 42px;
          color: #000000;
          line-height: 42px;
          font-weight: 500;
          text-align: left;
          margin-bottom: 15px;
        }
        p {
          font-size: 24px;
          font-weight: 400;
          text-align: left;
          color: #666666;
          line-height: 38px;
        }
        img {
          width: 650px;
          height: 470px;
        }
        video {
          margin-top: 20px;
          width: 100%;
        }

        .my-up-btn {
          font-size: 32px;
          position: absolute;
          bottom: 5px;
          left: 50%;
          transform: translateX(-50%);
        }
        .my-down-btn {
          font-size: 32px;
          position: absolute;
          right: 20px;
          top: 66px;
          transform: translateY(-50%);
        }
      }
    }
  }
  .section4 {
    padding: 0 29px;
    img {
      width: 100%;
      margin-bottom: 20px;
    }
    .title {
      font-size: 24px;
      font-weight: 600;
      text-align: left;
      color: #262626;
      margin-bottom: 10px;
    }
    .sub-title {
      font-size: 18px;
      font-weight: 400;
      line-height: 30px;
      text-align: left;
      color: #666666;
    }
  }

  .section5 {
    padding: 0 29px;
    text-align: center;
    margin-bottom: 20px;
    h3 {
      font-size: 24px;
      color: #7a7a7a;
      margin: 30px auto;
      margin-bottom: 20px;
    }
    .cooperation-btn {
      width: 222px;
      height: 81px;
      background: linear-gradient(90deg, #26bcf6, #1b7bf7 98%);
      border-radius: 52px;
      text-align: center;
      margin: 0 auto;
      font-weight: 600;
      line-height: 81px;
      font-size: 32px;
      color: #f0f0f0;
      margin-top: 60px;
    }
  }

  .section-footer {
    width: 750px;
    background-color: #061930;
    padding: 30px 20px;
    .footer-title {
      color: #c1c1c1;
      display: flex;
      justify-content: space-between;
      align-items: center;
      cursor: pointer;
      font-size: 28px;
      font-weight: bold;
      margin-bottom: 20px;
    }
    .content {
      margin-top: 10px;
      display: none; /* 默认隐藏内容 */
      font-size: 24px;
      color: #7b7e93;
    }

    .content p {
      margin: 5px 0;
      font-size: 24px;
      line-height: 49px;
    }

    .arrow {
      font-size: 26px;
      transition: transform 0.5s ease;
    }

    .arrow.up {
      transform: rotate(180deg);
    }

    .corporation-info {
      margin-top: 30px;
      color: #7b7e93;
      font-size: 24px;
      text-align: left;
      line-height: 33px;
    }
  }
}
</style>
