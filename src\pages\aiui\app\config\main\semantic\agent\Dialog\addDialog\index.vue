<template>
  <el-dialog
    title="配置应用所需的智能体"
    :visible.sync="dialog.show"
    width="880px"
    top="5vh"
    @closed="closeAgentDialog"
  >
    <div class="skill_header">
      <el-input
        size="small"
        class="search-area"
        placeholder="搜索"
        v-model.trim="searchVal"
        @focus="searchFocus"
        @keyup.enter.native="searchAgentConfig"
        style="width: 258px"
      >
        <i
          @click.stop.prevent="searchAgentConfig"
          slot="suffix"
          class="el-input__icon el-icon-search search-area-btn"
        />
      </el-input>

      <el-button
        icon="ic-r-plus"
        plain
        size="medium"
        @click="jump"
        style="margin-left: 10px"
        >&nbsp;创建智能体</el-button
      >
    </div>

    <div v-if="dialog.show">
      <agent-list
        :agentData="agentData"
        :agentDataCopy="agentDataCopy"
        :loading="loading"
        @selectchange="onSelectchange"
      ></agent-list>
    </div>

    <div slot="footer" class="dialog_footer">
      <div class="dialog_footer_left"></div>
      <div class="dialog_footer_right">
        <el-button size="small" @click="dialog.show = false">取消</el-button>
        <el-button
          size="small"
          type="primary"
          @click="saveChangeData"
          :loading="saveLoading"
          :disabled="!switchHasChange"
        >
          保存配置
        </el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { mapGetters } from 'vuex'
import agentList from './agentList.vue'

export default {
  name: 'AiuiWebSparkAgentDialog',
  components: { agentList },

  watch: {
    'dialog.show'(val) {
      if (val) {
        this.getAppAgentConfig()
      }
    },
  },

  props: {
    dialog: Object,
    appId: '',
    currentScene: Object,
  },

  data() {
    return {
      searchVal: '',

      changeState: false,
      saveLoading: false,
      switchHasChange: false,

      loading: false,

      agentData: [],
      agentDataCopy: [],
      originAgentData: [],
    }
  },
  computed: {
    ...mapGetters({
      skillIconBgColors: 'studioSkill/skillIconBgColors',
    }),
  },
  mounted() {
    // this.getAppAgentConfig()
  },

  methods: {
    jump() {
      console.log('jump')
      window.open('/studio/agent', '_blank')
    },
    closeAgentDialog() {
      this.changeState = false
      // 搜索框清空
      this.clickSearchVal = ''
      this.searchVal = ''
    },

    searchFocus() {},

    searchAgentConfig() {
      this.clickSearchVal = this.searchVal
      this.agentData = this.agentDataCopy.filter((it) =>
        it.name.includes(this.clickSearchVal)
      )
    },

    onSelectchange(agent, val) {
      console.log('onSelectchange的agent和val和official', agent, val)
      let agentId = agent.agentId
      let official = agent.official
      this.agentData = this.agentData.map((item) => {
        if (item.agentId === agentId) {
          return {
            ...item,
            used: val,
          }
        } else {
          return { ...item }
        }
      })
      this.agentDataCopy = this.agentDataCopy.map((item) => {
        if (item.agentId === agentId) {
          return {
            ...item,
            used: val,
          }
        } else {
          return { ...item }
        }
      })
      if (this.computedAgentDelta().length > 0) {
        this.switchHasChange = true
      } else {
        this.switchHasChange = false
      }
    },

    saveChangeData() {
      let deltaAgent = this.computedAgentDelta()
      let param = {
        appid: this.appId,
        chainId: this.currentScene.chainId || 'cbm_v45',
        sceneName: this.currentScene.sceneBoxName,
      }
      if (deltaAgent.length > 0) {
        param.agentCfg = JSON.stringify({
          cbm_intent_domain_agent_default: deltaAgent,
        })
      }

      let allPromises = [this.saveChangeKnowl45(param)]

      if (param.agentCfg) {
        this.saveLoading = true
        Promise.all(allPromises)
          .then(() => {
            this.saveLoading = false
            this.switchHasChange = false
            this.$emit('saveSuccess')
            this.$message.success('保存成功')
            this.dialog.show = false
          })
          .catch((err) => {
            this.saveLoading = false
            this.$message.error(err)
          })
      }
    },

    saveChangeKnowl45(param) {
      return new Promise((resolve, reject) => {
        this.$utils.httpPost(
          this.$config.api.AIUI_APP_PLUGINSTUDIO_SAVECONFIG,
          param,
          {
            success: (res) => {
              resolve()
            },
            error: (err) => {
              console.log('err', err)
              reject(err.desc)
            },
          }
        )
      })
    },

    computedAgentDelta() {
      let delta = []
      this.agentDataCopy.forEach((data) => {
        const originData = this.originAgentData.find(
          (item) => item.agentId === data.agentId
        )
        if (data.used !== originData.used) {
          if (data.used) {
            delta.push({
              agentId: data.agentId,
              operation: 'open',
            })
          } else {
            // 关闭的情况
            delta.push({
              agentId: data.agentId,
              operation: 'close',
            })
          }
        }
      })

      return delta
    },

    getAppAgentConfig() {
      let that = this
      this.loading = true
      this.$utils.httpGet(
        this.$config.api.AIUI_APP_PLUGINSTUDIO_USABLELIST,
        {
          appid: this.appId,
          sceneName: this.currentScene.sceneBoxName,
          type: '7',
          fromSource: 'cbm_v45',
          abilityId: 'cbm_intent_domain_agent_default',
        },
        {
          success: (res) => {
            that.loading = false

            const agentArr = JSON.parse(JSON.stringify(res.data.agents || []))
            const colors = this.skillIconBgColors
            agentArr.forEach((item, index) => {
              item.color = colors[index % colors.length]
            })

            that.originAgentData = agentArr
            that.agentDataCopy = agentArr
            that.agentData =
              agentArr.map((item) => {
                return {
                  ...item,
                  isShow: item.used,
                }
              }) || []
          },
          error: (res) => {},
        }
      )
    },
  },
}
</script>

<style scoped lang="scss">
.skill_header {
  position: absolute;
  top: 80px;
  // left: 165px;
  left: 32px; //隐藏左侧tab
}

.config_content {
  display: flex;
  .tab_container {
    padding: 20px 12px 0 12px;
    display: flex;
    flex-direction: column;
    gap: 10px;
    width: 150px;
    border-right: 1px solid #e1e1e1;
    a {
      display: inline-block;
      width: 100%;
      height: 40px;
      line-height: 40px;
      border-radius: 6px;
      cursor: pointer;
      color: #17171e;
      padding: 0 12px;
      &:hover {
        background-color: #eff3f9;
      }
      &.active {
        position: relative;
        font-weight: 600;
        background-color: #eff3f9;
      }
    }
  }
}
.tab-container {
  display: flex;
  position: relative;
  &::before {
    position: absolute;
    content: ' ';
    width: 100%;
    height: 1px;
    background: #e7e9ed;
    bottom: 0;
  }
}

.skill-type {
  margin-top: 1%;
  margin-bottom: 1%;
}
.add-skill-tab {
  a {
    display: inline-block;
    width: 108px;
    height: 40px;
    line-height: 40px;
    cursor: pointer;
    color: unset;
    text-align: center;
  }
  .active {
    position: relative;
    color: $primary;
    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      display: inline-block;
      width: 88px;
      height: 2px;
      background-color: #1f90fe;
      border-radius: 2px;
      transform: translateX(-50%);
    }
  }
}

.el-tabs {
  margin-left: 20px;
}

:deep(.el-dialog) {
  .el-dialog__header {
    border-bottom: 1px solid #e1e1e1;
  }
  .el-dialog__body {
    padding-top: 0;
    padding: 0px;
  }
  .el-dialog__footer {
    padding: 0 !important;
    .dialog_footer {
      display: flex;
      justify-content: space-between;

      .dialog_footer_left {
        display: none;
        width: 150px;
        padding: 14px 32px 14px 0;
        border-right: 1px solid #e1e1e1;
      }
      .dialog_footer_right {
        flex: 1;
        padding: 14px 32px 14px 0;
      }
    }
  }
}
</style>
