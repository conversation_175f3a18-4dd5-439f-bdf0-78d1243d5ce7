<template>
  <div class="extend-entity-add-dict">
    <div class="mgb24" style="font-size: 0">
      <el-button
        class="mgr16"
        icon="ic-r-plus"
        type="primary"
        size="small"
        :disabled="tableData.total >= entityLimitCount || !subAccountEditable"
        @click="addRow"
        >添加词条</el-button
      >
      <el-dropdown
        trigger="click"
        @command="handleCommand"
        placement="bottom-start"
      >
        <el-button size="small" :disabled="!subAccountEditable">
          批量操作
          <i class="ic-r-triangle-down el-icon--right" />
        </el-button>
        <el-dropdown-menu style="width: 120px" slot="dropdown">
          <el-dropdown-item>
            <div @click="dialog.show = true">批量覆盖</div>
          </el-dropdown-item>
          <el-dropdown-item
            v-if="tableData.total >= entityLimitCount"
            style="padding: 0"
          >
            <div class="import-disabled">批量追加</div>
          </el-dropdown-item>
          <el-dropdown-item v-else>
            <upload
              :dictId="dictId"
              :options="addOnly"
              :limitCount="limitCount"
              :subAccount="subAccount"
              @setLoad="setLoad"
              @setErrInfo="setErrInfo"
              @getEntryList="getDictList(1)"
            ></upload>
          </el-dropdown-item>
          <el-dropdown-item command="export"> 导出实体 </el-dropdown-item>
          <el-dropdown-item command="download"> 下载模版 </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <div class="fr" @keyup.enter="getDictList(1)">
        <el-input
          class="search-area"
          placeholder="搜索词条"
          size="medium"
          v-model="searchVal"
        >
          <i
            slot="suffix"
            class="el-input__icon el-icon-search search-area-btn"
            @click="getDictList(1)"
          />
        </el-input>
      </div>
    </div>
    <os-table
      ref="entityTable"
      :border="true"
      class="entry-table"
      :tableData="tableData"
      style="margin-bottom: 56px"
      @change="getDictList"
      @del="delEntry"
    >
      <el-table-column prop="value" width="200" label="词条">
        <template slot-scope="scope">
          <span v-if="!subAccountEditable">{{ scope.row.value }}</span>
          <el-input
            v-else
            :ref="'entityValueInput' + scope.$index"
            class="entry-value"
            size="small"
            placeholder="输入词条，回车添加"
            v-model="scope.row.value"
            :title="scope.row.value"
            @keyup.enter.native="editEntryBlur"
            @blur="editEntry(scope.row, scope.$index)"
          >
          </el-input>
        </template>
      </el-table-column>
      <el-table-column prop="lemma" label="别名">
        <template slot-scope="scope">
          <div class="entry-lemma-area">
            <el-tag
              v-for="(tag, index) in scope.row.lemmaArr"
              :key="index"
              :closable="subAccountEditable"
              :disable-transitions="false"
              @close="delLemma(index, scope.row)"
            >
              <span class="entry-lemma-area-tag" :title="tag">{{ tag }}</span>
            </el-tag>
            <el-input
              v-if="subAccountEditable"
              class="entry-lemma-add"
              size="small"
              v-model="scope.row.addLemmaName"
              :placeholder="
                (scope.$index === 0 && tableData.page === 1) || !scope.row.id
                  ? '输入别名，回车添加'
                  : ''
              "
              ref="saveTagInput"
              @keyup.enter.native="$event.target.blur()"
              @blur="addLemma(scope.row, scope.$index, $event)"
            ></el-input>
          </div>
        </template>
      </el-table-column>
    </os-table>
    <cover
      :dialog="dialog"
      :dictId="dictId"
      :limitCount="limitCount"
      :subAccount="subAccount"
      @setLoad="setLoad"
      @setErrInfo="setErrInfo"
      @getDictList="getDictList(1)"
    >
    </cover>
    <!-- 批量操作错误提示 -->
    <el-dialog title="错误提示" :visible.sync="showErrDialog" width="50%">
      <div style="margin-bottom: 20px">
        <p
          style="line-height: 22px"
          v-for="(text, index) in errList"
          :key="index"
        >
          {{ text }}
        </p>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="showErrDialog = false"
          >确 定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { mapGetters } from 'vuex'
import Upload from './uploadExtendEntity'
import Cover from './dialog/uploadCoverDialog'

export default {
  name: 'extend-entity-add-dict',
  data() {
    return {
      searchVal: '',
      tableData: {
        loading: false,
        total: 0,
        page: 1,
        size: 10,
        handles: ['del'],
        handleColumnText: '',
        list: [],
      },
      oldTableDataList: [],
      dictName: '',
      editEntryEnterBlur: false,
      addOnly: {
        type: 2,
        text: '批量追加',
      },
      dialog: {
        show: false,
      },
      showErrDialog: false,
      errList: [],
    }
  },
  computed: {
    skillId() {
      return this.$route.params.skillId
    },
    dictId() {
      return this.$route.params.addDictId
    },
    ...mapGetters({
      limitCount: 'aiuiApp/limitCount',
      subAccountSkillAuths: 'studioSkill/subAccountSkillAuths',
      subAccount: 'user/subAccount',
    }),
    isCharNoLimit() {
      return this.limitCount['char_no_limit_language'] > 0 // 0：限制；>0不限制
    },
    entityLimitCount() {
      return this.limitCount['entity_entry_count'] || '20000'
    },
    subAccountEditable() {
      if (this.subAccountSkillAuths[this.skillId] == 2) {
        this.tableData.handles = []
        return false
      }
      return true
    },
  },
  created() {
    this.getDictList()
  },
  methods: {
    setLoad(val) {
      this.tableData.loading = val
    },
    setErrInfo(data, type) {
      this.errList = JSON.parse(data)
      this.showErrDialog = type
    },
    getDictList(page) {
      let self = this
      this.tableData.loading = true
      this.$utils.httpGet(
        this.$config.api.STUDIO_EXTEND_ENTITY_SEARCH,
        {
          dictId: self.dictId,
          pageIndex: page || self.tableData.page,
          pageSize: self.tableData.size,
          search: self.searchVal,
        },
        {
          success: (res) => {
            self.tableData.loading = false
            self.dictName = res.data.dict.name
            let list = res.data.dictDataList
            self.tableData.list = Array.prototype.map.call(
              list,
              (item, index) => {
                item.lemmaArr = item.lemma ? item.lemma.split('|') : []
                return item
              }
            )
            self.oldTableDataList = JSON.parse(
              JSON.stringify(res.data.dictDataList)
            )
            self.tableData.total = res.data.size
          },
          error: (err) => {
            self.tableData.loading = false
          },
        }
      )
    },
    handleCommand(command) {
      let self = this
      switch (command) {
        case 'export':
          self.exportExcel()
          break
        case 'download':
          self.downloadExcel()
          break
        default:
          break
      }
    },
    exportExcel() {
      this.$utils.postopen(this.$config.api.STUDIO_EXTEND_ENTITY_EXPORT_EXCEL, {
        name: this.dictName,
        dictId: this.dictId,
      })
    },
    downloadExcel() {
      window.open(
        'https://aiui-file.cn-bj.ufileos.com/DemoEntity.xlsx',
        '_self'
      )
    },
    addRow() {
      let self = this
      let entry = this.tableData.list[0] || {}
      if (entry.id || this.tableData.list.length <= 0) {
        this.tableData.list.unshift({})
        if (this.tableData.total % 10 === 0) {
          this.tableData.size = 11
        } else {
          this.tableData.size = 10
        }
        this.tableData.total += 1
      }
      this.editEntryEnterBlur = false
      this.$nextTick(function () {
        self.$refs['entityValueInput0'] &&
          self.$refs['entityValueInput0'].focus()
      })
    },
    // 编辑词条
    editEntryBlur(event) {
      this.editEntryEnterBlur = true
      event.target.blur()
    },
    editEntry(data, index) {
      let self = this
      let reg =
        /^[\u4e00-\u9faf\uff00-\uffef\u30a0-\u30ff\u3000-\u303f\u3040-\u309F\u0400-\u052f\u00a0-\u00ff\u0100-\u017f\u0180-\u024f\u4e00-\u9fffa-zA-Z0-9\(\)\+\.\*`%'_ -]+$/
      let tmp = {
        skillId: self.skillId,
        extendDictId: self.dictId,
        value: data.value,
        lemma: data.lemma || '',
      }

      if (!data.value) {
        return this.$message.warning('词条不能为空')
      }
      if (data.value.length > 128) {
        return self.$message.warning('词条名不能超过128个字符')
      }
      if (!this.isCharNoLimit) {
        if (!reg.test(data.value)) {
          return self.$message.warning(
            "词条名仅支持中英文/数字/空格和._-'%`()*+"
          )
        }
      }
      if (data.id) {
        if (!self.tableData.list[0].id) {
          index -= 1
        }
        if (self.oldTableDataList[index]?.value === data.value) {
          return
        }
        tmp.id = data.id
      }
      self.$utils.httpPost(this.$config.api.STUDIO_EXTEND_ENTITY_ADDDICT, tmp, {
        success: (res) => {
          if (!tmp.hasOwnProperty('id')) {
            let entry = self.tableData.list[0]
            entry.id = res.data.id
            entry.lemma = res.data.lemma
            entry.lemmaArr = []
            if (self.editEntryEnterBlur) {
              self.addRow()
            }
          }
        },
        error: (err) => {},
      })
    },
    delEntry(data, index) {
      let self = this
      if (!data.id) {
        self.tableData.list.splice(index, 1)
        self.tableData.total -= 1
        return
      }
      self.tableData.loading = true
      self.$utils.httpPost(
        self.$config.api.STUDIO_EXTEND_ENTITY_DEL,
        {
          dictId: self.dictId,
          id: data.id,
          skillId: self.skillId,
        },
        {
          success: (res) => {
            self.tableData.loading = false
            self.$message.success('删除成功')
            if (self.tableData.list.length === 1 && self.tableData.page > 1) {
              self.tableData.page -= 1
            }
            self.getDictList()
          },
          error: (err) => {
            this.tableData.loading = false
          },
        }
      )
    },
    addLemma(data, index, event) {
      let self = this
      let reg =
        /^[\u4e00-\u9faf\uff00-\uffef\u30a0-\u30ff\u3000-\u303f\u3040-\u309F\u0400-\u052f\u00a0-\u00ff\u0100-\u017f\u0180-\u024f\u4e00-\u9fffa-zA-Z0-9\(\)\+\.\*'_ -=]+$/
      if (!data.addLemmaName) {
        return
      }
      if (!data.id) {
        return self.$message.warning('请先填写词条名创建词条')
      }
      if (data.lemmaArr && data.lemmaArr.indexOf(data.addLemmaName) !== -1) {
        return self.$message.warning('该别名已存在，请勿重复')
      }
      if (data.addLemmaName) {
        if (!data.value) {
          return this.$message.warning('词条不能为空')
        }
        if (data.addLemmaName.length > 128) {
          return self.$message.warning('词条别名不能超过128个字符')
        }
        if (!this.isCharNoLimit) {
          if (!reg.test(data.addLemmaName)) {
            return self.$message.warning(
              "词条别名仅支持中英文/数字/空格和()._-'%*+="
            )
          }
        }
        let lemma = data.lemmaArr.length
          ? `${data.lemma}|${data.addLemmaName}`
          : data.addLemmaName
        let tmp = {
          skillId: self.skillId,
          extendDictId: self.dictId,
          id: data.id,
          value: data.value,
          lemma: lemma,
        }
        self.$utils.httpPost(
          this.$config.api.STUDIO_EXTEND_ENTITY_ADDDICT,
          tmp,
          {
            success: (res) => {
              data.lemmaArr.push(data.addLemmaName)
              data.lemma = data.lemmaArr.join('|')
              data.addLemmaName = ''
              event.target.focus()
            },
            error: (err) => {},
          }
        )
      }
    },
    delLemma(index, data) {
      let self = this
      let lemmaArr = JSON.parse(JSON.stringify(data.lemmaArr))
      let lemma = ''
      lemmaArr.splice(index, 1)
      if (lemmaArr.length) {
        lemma = lemmaArr.join('|')
      }
      let tmp = {
        skillId: self.skillId,
        extendDictId: self.dictId,
        id: data.id,
        value: data.value,
        lemma: lemma,
      }
      this.tableData.loading = true
      self.$utils.httpPost(this.$config.api.STUDIO_EXTEND_ENTITY_ADDDICT, tmp, {
        success: (res) => {
          data.lemmaArr.splice(index, 1)
          data.lemma = data.lemmaArr.join('|')
          self.tableData.loading = false
        },
        error: (err) => {},
      })
    },
  },
  components: {
    Upload,
    Cover,
  },
}
</script>
<style lang="scss">
.entity-page {
  max-width: 1200px;
  padding-top: 24px;
  margin: auto;
  &-head {
    font-size: 24px;
    margin-bottom: 21px;
    display: flex;
    align-items: center;
    position: relative;
    &-back {
      cursor: pointer;
      margin-right: 16px;
      color: $grey4;
    }
    &-title {
      flex: auto;
      display: flex;
      align-items: center;
      cursor: pointer;
      i {
        color: $grey5;
        margin-left: 4px;
      }
    }
    &-right {
      position: absolute;
      right: 0;
    }
  }
}
.entity-name {
  display: inline-block;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.entity-edit-btn {
  vertical-align: top;
  margin-left: 9px;
  color: $primary;
  cursor: pointer;
}

.entity-page-form-item {
  width: 30%;
  margin-bottom: 0;
}
.entity-page-form-input {
  width: 188px;
}
.entity-page-form-save,
.entity-page-form-cancel {
  margin-left: 16px;
  cursor: pointer;
  &:hover {
    color: $primary;
  }
}

.entry-value {
  input {
    height: 54px !important;
    line-height: 54px !important;
    border: 0 !important;
    padding-left: 0;
    color: $semi-black;
    font-weight: 600;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.entry-table {
  margin-bottom: 100px;
  .el-table__row td {
    padding: 0;
    height: 56px;
  }
  .el-table__body tr:hover > td {
    background-color: #fff;
  }
  td .cell {
    padding-left: 0;
  }
}

.entry-lemma-area {
  display: flex;
  align-items: center;
  min-height: 54px;
  padding: 3px 0 3px 8px;
  display: inline-flex;
  flex-flow: wrap;
  height: auto;
  width: 100%;
  box-sizing: border-box;
  border: 1px solid transparent;
  &:hover,
  &:focus-within {
    border: 1px solid $primary;
  }
  .el-tag {
    display: flex;
    align-items: baseline;
    margin: 3px 8px 3px 0;
  }
  &-tag {
    display: inline-block;
    max-width: 160px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  // .el-tag + .el-tag {
  //   margin-right: 8px;
  // }
  .el-tag + .el-input {
    flex: 1;
    min-width: 100px;
  }
}

.entry-lemma-add {
  margin-left: 4px;
  input {
    height: 36px !important;
    line-height: 36px !important;
    padding-left: 0px;
    border: 0 !important;
    color: $semi-black;
    font-weight: 600;
  }
}
</style>
