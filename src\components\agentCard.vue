<template>
  <div class="card" @click="$emit('click', item)">
    <div class="card_top">
      <img :src="item.pluginIconUrl" alt="" style="object-fit: cover" />
      <div class="card_top_right">
        <div class="card_name">{{ item.pluginName }}</div>
        <el-tag
          size="mini"
          class="card_tag"
          :style="getTagStyle(item.pluginName)"
          >{{ item.pluginType == 20 ? '三方' : item.templateTag }}</el-tag
        >
      </div>
    </div>

    <el-tooltip
      :disabled="!isOverflow"
      :content="item.pluginDesc"
      placement="top"
      popper-class="card_tooltip"
    >
      <div class="card_mid" ref="cardMid" @mouseenter="checkOverflow">
        {{ item.pluginDesc }}
      </div>
    </el-tooltip>

    <div class="card_footer">
      <div class="id_wrap">ID:{{ item.pluginId }}</div>
      <el-dropdown @click.native.stop @command="handleDropdown">
        <span class="el-dropdown-link">
          <i class="el-icon-more"></i>
        </span>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item command="edit">编辑</el-dropdown-item>
          <el-dropdown-item command="copy">创建副本</el-dropdown-item>
          <el-dropdown-item command="del">删除</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AgentCard',
  props: {
    item: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      isOverflow: false,
    }
  },
  computed: {
    getTagStyle() {
      return (tag) => {
        if (tag?.includes('查询')) {
          return {
            backgroundColor: '#e5e9ff',
            color: '#544aff',
          }
        } else if (tag?.includes('预定')) {
          return {
            backgroundColor: '#fcf7e4',
            color: '#D1931A',
          }
        }
        return {}
      }
    },
  },
  methods: {
    checkOverflow() {
      this.$nextTick(() => {
        const el = this.$refs.cardMid
        if (el) {
          this.isOverflow = el.scrollHeight > el.clientHeight
        }
      })
    },
    handleDropdown(command) {
      this.$emit('dropdown', { command, item: this.item })
    },
  },
}
</script>

<style lang="scss" scoped>
.card {
  cursor: pointer;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  overflow: hidden;
  border: 1.5px solid #dedede;
  border-radius: 5px;
  padding: 14px 16px;
  box-sizing: border-box;
  transition: border-color 0.3s ease;
  background-color: #fff;
  &:hover {
    border-color: $primary;
  }

  .card_top {
    display: flex;
    img {
      width: 58px;
      height: 60px;
      border-radius: 7px;
    }
    .card_top_right {
      flex: 1;
      margin-left: 10px;
      padding: 5px 0;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      width: 0;
      .card_name {
        margin-bottom: 6px;
        font-size: 18px;
        font-weight: 600;
        color: #000000;
        width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .card_tag {
        height: 25px;
        width: fit-content;
        padding: 0 8px;
        line-height: 25px;
        font-size: 12px;
        color: $primary;
        border: none;
      }
    }
  }
  .card_mid {
    display: -webkit-box;
    -webkit-line-clamp: 2; /* 限制显示行数 */
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    color: #696871;
    margin: 5px 0 12px 0;
    height: 36px;
  }

  .card_footer {
    display: flex;
    justify-content: space-between;
    gap: 20px;
    .id_wrap {
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      color: #8a909b;
    }
  }
}
</style>
