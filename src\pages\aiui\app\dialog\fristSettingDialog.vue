<template>
  <el-dialog
    custom-class="my-app-fritst-setting"
    :visible.sync="visible"
    width="620px"
    :show-close="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <template slot="title">
      <span class="main-title">选择设备需要AI能力</span>
      <p class="sub-title">
        选择后，系统会为应用自动配置基础服务。后续可以在应用配置里修改。
      </p>
    </template>
    <div class="block-item">
      <div class="block-item-top">
        <el-switch value disabled class="btn-switch"></el-switch>
        <span class="block-item-top-title">语音识别</span>
        <span class="block-item-top-sub">（将语音转成文字）</span>
      </div>
      <div class="block-item-bottom">
        <p>
          -
          识别服务包含：识别<span>语种领域、流式识别、所见即可说、识别优化</span>。
        </p>
        <p>- 更多高级能力、在应用配置种设置。</p>
      </div>
    </div>

    <div class="block-item">
      <div class="block-item-top">
        <el-switch
          v-model="semanticsUnderstanding"
          class="btn-switch"
        ></el-switch>
        <span class="block-item-top-title">语义理解</span>
        <span class="block-item-top-sub">（理解自然语言）</span>
      </div>
      <div class="block-item-bottom">
        <p>- 开启后自动配置AIUI语音技能包 和 讯飞闲聊服务</p>
        <p>
          - <span>敏关键词、敏感词过滤，语义VAD</span>，更多技能在应用配置里开启
        </p>
      </div>
    </div>

    <div class="block-item">
      <div class="block-item-top">
        <el-switch v-model="speechSynthesis" class="btn-switch"></el-switch>
        <span class="block-item-top-title">语音合成</span>
        <span class="block-item-top-sub">（文字合成语音）</span>
      </div>
      <div class="block-item-bottom">
        <p>- 默认配置小娟发音人</p>
        <p>- 更多发音人合成、数字虚拟人合成可在应用配置里继续设置</p>
      </div>
    </div>

    <div class="btm-btn-group">
      <el-button type="primary" @click="onOk">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'FristSettingDialog',
  props: {
    visible: Boolean,
  },
  data() {
    return {
      semanticsUnderstanding: true,
      speechSynthesis: false,
    }
  },
  methods: {
    onOk() {
      this.$emit(
        'toSaveAiPower',
        this.semanticsUnderstanding,
        this.speechSynthesis
      )
    },
  },
}
</script>

<style lang="scss" scoped>
.my-app-fritst-setting {
  .main-title {
    display: block;
    margin-bottom: 10px;
    color: #333;
    font-size: 20px;
    font-weight: bold;
  }
  .sub-title {
    margin-bottom: 0;
    color: #666;
    font-size: 16px;
  }

  .block-item {
    padding: 30px;
    border-top: 1px solid #eff1f1;

    &-top {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
      color: #333;
      &-title {
        font-size: 20px;
      }
      &-sub {
        font-size: 18px;
      }
    }
    &-bottom {
      color: #a5a5b8;
      p {
        margin-bottom: 0;
      }
      p + p {
        margin-top: 5px;
      }
      span {
        color: #ff9b00;
      }
    }
  }
  .btm-btn-group {
    display: flex;
    justify-content: flex-end;
    padding: 0 40px;
  }
}
</style>
<style lang="scss">
.my-app-fritst-setting {
  margin-top: 8vh !important;
  margin-bottom: 0;
  .el-dialog__body {
    padding: 0 0 30px;
  }

  .block-item {
    .btn-switch {
      margin-right: 10px;
      .el-switch__core {
        width: 55px !important;
        height: 24px;
        &::after {
          top: -1px;
          width: 26px;
          height: 26px;
        }
      }
    }
    .el-switch.is-checked {
      .el-switch__core {
        &::after {
          margin-left: -27px;
        }
      }
    }
  }
}
</style>
