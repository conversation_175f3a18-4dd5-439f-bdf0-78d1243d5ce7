<template>
  <os-page :options="pageOptions">
    <studio-skill-header-right slot="btn" />
    <div class="post-process-page">
      <div class="mgb56">
        <p class="page-title">服务部署</p>
        <el-tooltip
          effect="dark"
          content="还原为源技能的服务部署"
          placement="top"
        >
          <span
            class="revert-btn"
            v-if="skill.type == '3' && skill.againExtend"
            @click="revertToSourceProcess"
            >还原</span
          >
        </el-tooltip>
        <p class="desc">
          利用技能后处理，您可以通过编写代码的方式，为您的技能配置多轮对话，以及调用外部信源，为您的技能组合丰富多彩的回答话术。
          <a
            v-if="skill.type == '3'"
            href="http://cdn.iflyos.cn/docs/custom_post_process.pdf"
            target="_blank"
            >阅读技能后处理 API 文档</a
          >
          <a v-else :href="`${$config.docs}doc-62/`" target="_blank"
            >阅读技能后处理 API 文档</a
          >
        </p>
        <el-form
          class="mgt24"
          size="medium"
          label-width="90px"
          label-position="left"
          v-loading="loading"
        >
          <el-form-item
            label="部署方式"
            v-if="skill.protocolVersion != '2.0'"
            style="border-bottom: 1px dashed #e4e7ed; padding-bottom: 24px"
          >
            <el-radio-group
              class="process-methods-wrap"
              size="small"
              v-model="processingType"
            >
              <el-radio-button label="0">使用技能云函数</el-radio-button>
              <el-radio-button label="1">使用Webhook</el-radio-button>
            </el-radio-group>
          </el-form-item>

          <!-- 云函数 start -->
          <el-form-item v-if="processingType != 1" label="文件格式">
            <el-radio-group class="radio-wrap" v-model="radio">
              <el-radio :label="1">单文件</el-radio>
              <el-radio :label="2">多文件</el-radio>
            </el-radio-group>
            <os-upload
              v-if="radio == 1 && subAccountEditable"
              :fileList="fileList"
              type="button"
              :options="btnUploadOptions"
              @completecb="uploaded"
              @remove="remove"
              @failcb="handleUploadFailed"
            >
            </os-upload>
            <editor
              v-if="radio == 1"
              :businessId="businessId"
              :editorInfo="editorInfo"
              :subAccountEditable="subAccountEditable"
              @showLog="showLog = true"
              @getInfo="getInfo"
            ></editor>
            <!-- 打包上传 start -->
            <template v-if="radio == 2">
              <a
                class="download-template"
                v-if="skill.type == '3'"
                href="https://aiui-file.cn-bj.ufileos.com/extendJsDemo.zip"
              >
                下载模板</a
              >
              <a
                class="download-template"
                v-else
                :href="`https://aiui-file.cn-bj.ufileos.com/ACF_DEMO${
                  skill.protocolVersion == '2.1' ? '_2.1' : ''
                }.zip`"
              >
                下载{{ skill.protocolVersion }}协议模板
              </a>
            </template>
            <upload-zip
              v-if="radio == 2"
              :businessId="businessId"
              :fileList="zipList"
              :JSType="JSType"
              :subAccount="subAccount"
              :subAccountEditable="subAccountEditable"
              :limitSize="jsSize"
              @getInfo="getInfo"
              @setShowLog="setShowLog"
            ></upload-zip>
          </el-form-item>
          <!-- 云函数 end -->

          <webhook
            v-if="processingType == 1 && skill.protocolVersion != '2.0'"
            :businessId="businessId"
            :subAccountEditable="subAccountEditable"
          ></webhook>

          <!-- 实时日志 -业务定制不支持实时日志-->
          <el-form-item
            label="实时日志"
            v-if="
              skill.type != '3' &&
              processingType != 1 &&
              (showLog || (debugLog && debugLog.length))
            "
          >
            <div class="log-wrap">
              <div class="os-scroll">
                <p
                  class="log-details"
                  v-for="(log, index) in debugLog"
                  :key="index"
                >
                  <span>[{{ log.dataTime }}]</span>
                  <span>[{{ log.level }}]</span>
                  {{ log.data }}
                </p>
              </div>
            </div>
            <a v-if="subAccountEditable" @click="cleanDebugLog">清空日志</a>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </os-page>
</template>
<script>
import Editor from './codeEditor'
import UploadZip from './uploadZip'
import Webhook from './webhook'
import { mapGetters } from 'vuex'

export default {
  name: 'skill-post-process',
  props: {
    subAccount: Boolean,
  },
  data() {
    return {
      pageOptions: {
        title: '技能后处理',
        loading: false,
      },
      loading: false,
      processingType: '0',
      radio: 1, //1 单文件; 2 打包上传
      zipRadioTipShow: false, //点击多文件上传radio时的提示
      jsRadioTipShow: false,
      editorInfo: '',
      showLog: false,
      fileList: [],
      zipList: [],
      debugLog: [],
      JSType: '',
      btnUploadOptions: {
        btnText: '上传JS文件',
        tips: '只能上传js文件',
        sizeLimit: 1024 * 1024 * 100,
        fileType: ['js'],
        numLimit: 1,
        action: '',
      },
    }
  },
  computed: {
    ...mapGetters({
      limitCount: 'aiuiApp/limitCount',
    }),
    jsSize() {
      return (
        (this.limitCount &&
          this.limitCount.js_size &&
          Number(this.limitCount.js_size)) ||
        100
      )
    },
    businessId() {
      return this.$store.state.studioSkill.id
    },
    skill() {
      return this.$store.state.studioSkill.skill
    },
    subAccountEditable() {
      let auths = this.$store.state.studioSkill.subAccountSkillAuths
      return auths[this.businessId] == 2 ? false : true
    },
  },
  watch: {
    '$store.state.studioSkill.debugConsole': function (val) {
      if (val && val.length) {
        this.debugLog.push(...val)
      }
    },
  },
  created() {
    this.radio = 1
    this.getInfo()
  },
  methods: {
    getInfo() {
      let self = this
      self.pageOptions.loading = true
      self.fileList.splice(0)
      self.zipList.splice(0)
      self.loading = true
      self.btnUploadOptions.action = `/cloudFunction/uploadJS?skillId=${self.businessId}`
      self.$utils.httpGet(
        self.$config.api.STUDIO_PROCESS_INFO,
        {
          skillId: self.businessId,
        },
        {
          success: (res) => {
            self.pageOptions.loading = false
            self.JSType = res.data.JSType
            self.editorInfo = {
              JSType: res.data.JSType,
              code: res.data.aladdinJS,
              uploadTime: res.data.uploadTime,
            }
            if (res.data.JSType == 'demo') {
              self.radio = 1
              self.showLog = false
            }
            if (res.data.JSType == 'js') {
              self.radio = 1
              self.showLog = true
              self.fileList.push({
                name: res.data.fileName,
                uploadTime: res.data.uploadTime,
                url: `${location.origin}/aiui/${
                  self.subAccount ? 'sub' : ''
                }web/cloudFunction/downloadFile?skillId=${this.businessId}`,
                isBlobFile: false,
              })
            }
            if (res.data.JSType == 'zip') {
              self.radio = 2
              self.showLog = true
              self.zipList.push({
                name: res.data.fileName,
                uploadTime: res.data.uploadTime,
              })
            }
            if (res.data.business.hasOwnProperty('hasaladdin')) {
              self.processingType = res.data.business.hasaladdin
            }
            self.loading = false
          },
          error: (err) => {
            self.loading = false
          },
        }
      )
    },
    setShowLog(val) {
      this.showLog = val
    },
    cleanDebugLog() {
      if (!this.debugLog.length) {
        return
      }
      this.debugLog.splice(0, this.debugLog.length)
      this.$message.success('日志已清空')
    },
    uploaded(file) {
      this.fileList.unshift(file)
      this.setShowLog(true)
      this.getInfo()
      this.$message.success('上传成功')
    },
    remove(file) {
      this.$utils.httpPost(
        this.$config.api.STUDIO_PROCESS_DELETE_FILE,
        {
          skillId: this.businessId,
        },
        {
          success: (res) => {
            this.setShowLog(false)
            this.getInfo()
            this.$message.success('删除成功')
          },
        }
      )
    },
    handleUploadFailed(data) {
      this.fileList = []
    },
    revertToSourceProcess() {
      this.$utils.httpPost(
        this.$config.api.STUDIO_EXTEND_REVERT_TO_SOURCE_PROCESS,
        {
          skillId: this.businessId,
        },
        {
          success: (res) => {
            this.$message.success('还原成功')
            this.getInfo()
          },
        }
      )
    },
  },
  components: {
    Editor,
    UploadZip,
    Webhook,
  },
}
</script>
<style lang="scss" scoped>
.post-process-page {
  padding-bottom: 70px;
}
.page-title {
  display: inline-block;
  margin: 33px 0 5px;
  font-size: 20px;
  line-height: 26px;
}
.revert-btn {
  margin-left: 12px;
  font-size: 14px;
  cursor: pointer;
  color: $primary;
}
.desc {
  line-height: 22px;
  color: $grey5;
}
.process-methods-wrap {
  width: 260px;
}
.radio-wrap {
  margin: 10px 0 24px;
}

.download-template {
  display: inline-block;
  margin-bottom: 16px;
  width: 100%;
  height: 44px;
  line-height: 44px;
  text-align: center;
  color: $primary;
  border-radius: 12px;
  background: $grey1;
}

// 实时日志
.log-wrap {
  padding: 5px 24px;
  overflow: hidden;
  height: 300px;
  border-radius: 2px;
  border: 1px solid $grey1;
  background-color: $grey1;
}
.log-details {
  margin: 10px 0 0;
  line-height: 22px;
  span {
    display: inline-block;
    margin-right: 20px;
  }
}
</style>
