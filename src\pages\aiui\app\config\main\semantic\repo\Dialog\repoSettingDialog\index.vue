<template>
  <el-dialog
    title="问答设置"
    :visible.sync="dialog.show"
    width="600px"
    top="5vh"
    :show-close="true"
    @closed="closeSkillDialog"
  >
    <search-config :form="configForm" @change="onConfigChange"></search-config>

    <span slot="footer" class="dialog-footer">
      <el-button size="small" @click="dialog.show = false">取消</el-button>
      <el-button
        size="small"
        type="primary"
        @click="saveChangeData"
        :loading="saveLoading"
        :disabled="!cofigHasChange"
      >
        保存配置
      </el-button>
    </span>
  </el-dialog>
</template>
<script>
import searchConfig from './searchConfig.vue'

export default {
  props: {
    dialog: Object,
    appId: '',
    currentScene: Object,
  },
  data() {
    return {
      saveLoading: false,

      configForm: {
        channel: 2,
        threshold: 0.1,
      },
      cofigHasChange: false,
    }
  },
  methods: {
    saveChangeData() {
      let param = {
        appid: this.appId,
        chainId: this.currentScene.chainId || 'cbm_v45',
        sceneName: this.currentScene.sceneBoxName,
      }

      if (this.cofigHasChange) {
        param.knowledgeSearch = JSON.stringify(this.configForm)
      }
      let allPromises = [this.saveChangeKnowl45(param)]

      if (param.knowledgeSearch) {
        this.saveLoading = true
        Promise.all(allPromises)
          .then(() => {
            this.saveLoading = false
            this.cofigHasChange = false
            this.$emit('saveSuccess')
            this.$message.success('保存成功')
            this.dialog.show = false
          })
          .catch((err) => {
            this.saveLoading = false
            this.$message.error(err)
          })
      }
    },

    saveChangeKnowl45(param) {
      return new Promise((resolve, reject) => {
        this.$utils.httpPost(
          this.$config.api.AIUI_APP_PLUGINSTUDIO_SAVECONFIG,
          param,
          {
            success: (res) => {
              resolve()
            },
            error: (err) => {
              console.log('err', err)
              reject(err.desc)
            },
          }
        )
      })
    },

    // 关闭弹窗
    closeSkillDialog() {},

    getChainInfo() {
      let that = this
      this.$utils.httpGet(
        this.$config.api.AIUI_APP_CHAIN_INFO,
        {
          appid: this.appId || this.$route.params.appId,
          sceneName: this.currentScene.sceneBoxName,
          chainId: this.currentScene.chainId,
        },
        {
          success: (res) => {
            console.log('ressss', res)
            that.configForm.channel =
              res.data.knowledgeSearchConfig?.channel ?? 2
            that.configForm.threshold =
              Number(res.data.knowledgeSearchConfig?.threshold) ?? 0.1
          },
        }
      )
    },

    onConfigChange(type, val) {
      this.cofigHasChange = true
      this.configForm[type] = val
    },
  },
  watch: {
    'dialog.show'(val) {
      if (val) {
        this.getChainInfo()
      }
    },
  },
  components: { searchConfig },
}
</script>
<style lang="scss" scoped>
.skill-header {
  position: absolute;
  top: 12px;
  right: 20px;
}
.tab-container {
  display: flex;
  position: relative;
  &::before {
    position: absolute;
    content: ' ';
    width: 100%;
    height: 1px;
    background: #e7e9ed;
    bottom: 0;
  }
}

.skill-type {
  margin-top: 1%;
  margin-bottom: 1%;
}
.add-skill-tab {
  a {
    display: inline-block;
    width: 108px;
    height: 40px;
    line-height: 40px;
    cursor: pointer;
    color: unset;
    text-align: center;
  }
  .active {
    position: relative;
    color: $primary;
    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      display: inline-block;
      width: 88px;
      height: 2px;
      background-color: #1f90fe;
      border-radius: 2px;
      transform: translateX(-50%);
    }
  }
}
// .check-config {
//   margin-left: 15px;
//   margin-top: 10px;
//   span {
//     color: #1f90fe;
//     cursor: pointer;
//   }
// }
.el-tabs {
  margin-left: 20px;
}
</style>
