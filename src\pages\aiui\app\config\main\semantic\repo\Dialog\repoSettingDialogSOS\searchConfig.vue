<template>
  <el-form
    ref="form"
    :model="form"
    label-width="120px"
    style="padding-top: 10px"
  >
    <el-form-item label="检索方法">
      <el-col :span="8">
        <el-select
          :value="form.channel"
          placeholder="请选择检索方式"
          @change="onSelectChange"
          size="small"
          :disabled="!subAccountEditable"
        >
          <el-option label="单向量召回" :value="1"></el-option>
          <el-option
            label="多路召回"
            :value="2"
          ></el-option> </el-select></el-col
      ><el-col :span="12" style="margin-left: 10px">
        {{
          form.channel === 2
            ? '混合多种方式召回知识点'
            : '使用语义向量召回知识点'
        }}
      </el-col>
    </el-form-item>

    <el-form-item label="相关性阈值">
      <el-col :span="8">
        <el-input-number
          size="small"
          :value="form.threshold"
          :min="0"
          :max="1"
          :precision="2"
          :step="0.01"
          :step-strictly="true"
          @change="onInputChange"
          style="width: 100%"
          :disabled="!subAccountEditable"
        ></el-input-number>
      </el-col>

      <el-col :span="10" style="margin-left: 10px"> 取值为0-1 </el-col>
    </el-form-item>
  </el-form>
</template>
<script>
import { mapGetters } from 'vuex'

export default {
  props: {
    form: {
      type: Object,
      default() {
        return {
          channel: 2,
          threshold: 0.1,
        }
      },
    },
  },
  data() {
    return {}
  },
  computed: {
    ...mapGetters({
      subAccountEditable: 'aiuiApp/subAccountEditable',
    }),
  },
  methods: {
    onInputChange(val) {
      console.log('onInputChange', val)
      this.$emit('change', 'threshold', val)
    },
    onSelectChange(val) {
      this.$emit('change', 'channel', val)
    },

    cancel() {
      this.$emit('searchConfigCancel')
    },
  },
}
</script>
<style lang="scss" scoped></style>
