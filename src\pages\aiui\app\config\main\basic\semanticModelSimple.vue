<template>
  <div>
    <el-form
      ref="form"
      class="recognition-form"
      :model="form"
      label-width="90px"
      label-position="left"
      :inline="true"
      style="width: 100%"
    >
      <el-form-item class="form-item" label="选择模型：">
        <el-select
          class="config-select"
          v-model="form.modelConf"
          @change="changeOption"
          size="small"
          style="width: 400px"
          :disabled="!subAccountEditable"
        >
          <el-option
            v-for="(item, index) in modelConfList"
            :key="index"
            :label="item.name"
            :value="item.domain"
          >
            <div class="modelWrap">
              <img :src="item.icon" />
              <span>{{ item.name }}</span>
            </div>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item class="form-item" style="margin-left: 10px">
        <a v-if="form.modelConf === 'third'" @click="dialogVisible = true"
          >模型配置</a
        >
      </el-form-item>
    </el-form>

    <!-- 第三方模型配置弹窗 -->
    <thirdPartyModelDialog
      v-if="dialogVisible"
      :visible.sync="dialogVisible"
      :modelInfo="thirdModelInfo"
      @confirm="handleDialogConfirm"
    />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import thirdPartyModelDialog from './thirdPartyModelDialog.vue'

export default {
  components: {
    thirdPartyModelDialog,
  },
  data() {
    return {
      modelConfList: [],
      form: {
        modelConf: '',
      },
      dialogVisible: false,
      thirdModelInfo: null,
    }
  },
  created() {
    if (this.currentScene && this.currentScene.sceneBoxId) {
      this.getModelList() // 获取配置列表
    }
  },
  watch: {
    currentScene(scene) {
      if (scene && scene.sceneBoxId) {
        this.getModelList() // 获取配置列表
      }
    },
    'form.modelConf': {
      handler(newVal) {
        // 当选择第三方模型时，获取模型信息
        if (newVal === 'third') {
          this.getThirdModelInfo()
        }
      },
    },
  },
  computed: {
    ...mapGetters({
      currentScene: 'aiuiApp/currentScene',
      appInfo: 'aiuiApp/app',
      subAccountEditable: 'aiuiApp/subAccountEditable',
    }),
    appId() {
      return this.$route.params.appId
    },
  },
  methods: {
    changeOption(val) {
      let data = {
        botId: this.currentScene.botBoxId,
        domain: val,
      }
      this.$utils.httpPost(
        this.$config.api.AIUI_BOT_CONFIG_SAVE_MODEL,
        JSON.stringify(data),
        {
          config: {
            headers: {
              'Content-Type': 'application/json;charset=UTF-8',
            },
          },
          success: (res) => {
            if (res.flag) {
            } else {
              this.$message.error(res.desc)
            }
          },
          error: (err) => {},
        }
      )
    },

    getModelList() {
      this.$utils.httpGet(
        this.$config.api.AIUI_BOT_CONFIG_GET_MODELS,
        {
          botId: this.currentScene.botBoxId,
        },
        {
          success: (res) => {
            this.modelConfList = res.data.model || []
            console.log('modelConfList=>', this.modelConfList)
            const selectedModel = (res.data.model || []).find(
              (data) => data.selected
            )
            this.form.modelConf = selectedModel?.domain
          },
          error: (err) => {},
        }
      )
    },

    // 获取第三方模型配置信息
    getThirdModelInfo() {
      this.$utils.httpGet(
        this.$config.api.AIUI_BOT_CONFIG_GET_MODEL_INFO,
        {
          botId: this.currentScene.botBoxId,
        },
        {
          success: (res) => {
            if (res.flag) {
              this.thirdModelInfo = res.data || null
            } else {
              this.$message.error(res.desc || '获取第三方模型配置失败')
            }
          },
          error: (err) => {
            this.$message.error('获取第三方模型配置失败')
          },
        }
      )
    },

    handleDialogConfirm(formData) {
      const data = {
        botId: this.currentScene.botBoxId,
        domain: 'third',
        url: formData.url,
        token: formData.token,
        modelName: formData.modelName,
      }
      this.$utils.httpPost(
        this.$config.api.AIUI_BOT_CONFIG_SAVE_MODEL,
        JSON.stringify(data),
        {
          config: {
            headers: {
              'Content-Type': 'application/json;charset=UTF-8',
            },
          },
          success: (res) => {
            if (res.flag) {
              this.$message.success(res.desc)
              this.dialogVisible = false
              // 配置保存成功后更新本地模型信息
              this.getThirdModelInfo()
            } else {
              this.$message.error(res.desc || '第三方模型配置保存失败')
            }
          },
          error: (err) => {
            this.$message.error('第三方模型配置保存失败')
          },
        }
      )
    },
  },
}
</script>

<style lang="scss" scoped>
@import './common.scss';
.modelWrap {
  display: flex;
  align-items: center;
  img {
    width: 20px;
    height: 20px;
    object-fit: contain;
  }
  span {
    margin-left: 10px;
  }
}
</style>
