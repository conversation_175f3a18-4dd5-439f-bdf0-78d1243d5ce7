<template>
  <el-dialog
    :title="title"
    :visible.sync="dialog.show"
    width="480px"
  >
    <div class="os-scroll skill-quote">
      <template v-if="dialog.entityId">
        <os-skill-simple-item
          v-for="(skill, index) in skills"
          :key="index"
          class="skill-quote-item"
          :url="skill.url"
          :name="skill.zhName || '-'"
        />
      </template>
      <template v-else>
        <os-skill-simple-item
          v-for="(skill, index) in skills"
          :key="index"
          class="skill-quote-item"
          :url="skill.url"
          :name="skill.appName+'('+skill.sceneName+')' || '-'"
        />
      </template>
    </div>

  </el-dialog>
</template>

<script>
import dicts from "@M/dicts"

export default {
  props: {
    dialog: {
      type: Object,
      default: {}
    }
  },
  data () {
    return {
      title: '0个引用',
      skills: []
    }
  },
  watch: {
    'dialog.show': function(val, oldVal) {
      if (val) {
        this.getSkills()
      } else {

      }
    }
  },
  mounted() {

  },
  methods: {
    getSkills () {
      let self = this
      this.skills = []
      if (this.dialog.entityId) {
        this.$utils.httpGet(this.$config.api.STUDIO_ENTITY_CHECKSKILL, {
          entityId: this.dialog.entityId
        }, {
          success: (res) => {
            self.skills = res.data
            self.title = `${res.data.length}个引用`
          },
          error: (err) => {

          }
        })
      } else if (this.dialog.repoId) {
        this.$utils.httpGet(this.$config.api.STUDIO_QA_SCENES_LIST, {
          repoId: this.dialog.repoId
        }, {
          success: (res) => {
            self.skills = res.data
            self.title = `${res.data.length}个引用`
          },
          error: (err) => {

          }
        })
      }

    }
  },
  components: {

  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="scss" scoped>
.skill-quote {
  max-height: 560px;
  margin: 0 -16px;
  &-item {
    width: 192px;
    margin: 0 16px 32px;
  }
}
</style>
