<template>
  <div class="swiper-container" id="application-advantage-swiper">
    <div class="swiper-wrapper l-banner__swiper-wrapper">
      <div
        class="swiper-slide"
        v-for="(item, index) in applicationMetas"
        :key="index"
      >
        <div
          class="meta-beauty"
          :style="{
            backgroundImage:
              'url(' +
              require(`@A/images/solution/meta-human/${
                item.image || item.name
              }.png`) +
              ')',
          }"
        ></div>
        <div class="meta-tag" :style="{ background: item.bg }">
          {{ item.tag }}
        </div>
        <div class="meta-name">{{ item.name }}</div>
      </div>
    </div>
  </div>
</template>
<script>
import swiper, { Autoplay } from 'swiper'
swiper.use([Autoplay])

export default {
  data() {
    return {
      applicationMetas: [
        {
          tag: '通用',
          name: '沐沐',
          bg: 'linear-gradient(270deg,#00d290 1%, #00e2ff)',
        },
        {
          tag: '文旅',
          name: '晓依',
          bg: 'linear-gradient(270deg, #3083f2, #00d9ff)',
        },
        {
          tag: '党建',
          name: '小晴',
          bg: 'linear-gradient(270deg,#a059ff, #6abbff)',
        },
        {
          tag: '金融',
          name: '晓依',
          image: '晓依2',
          bg: 'linear-gradient(270deg,#ff7614, #ffc060)',
        },

        {
          tag: '政务',
          name: '小颜',
          bg: 'linear-gradient(270deg,#f04614, #f09060)',
        },
        {
          tag: '通用',
          name: '小满',
          bg: 'linear-gradient(270deg,#00d290 1%, #00e2ff)',
        },

        {
          tag: '文旅',
          name: '悦悦',
          bg: 'linear-gradient(270deg, #3083f2, #00d9ff)',
        },
        {
          tag: '通用',
          name: '晓雯',
          bg: 'linear-gradient(270deg,#00d290 1%, #00e2ff)',
        },
        {
          tag: '通用',
          name: '明轩',
          bg: 'linear-gradient(270deg,#00d290 1%, #00e2ff)',
        },

        {
          tag: '政务',
          name: '爱加',
          bg: 'linear-gradient(270deg,#f04614, #f09060)',
        },
        {
          tag: '通用',
          name: '晓姿',
          bg: 'linear-gradient(270deg,#00d290 1%, #00e2ff)',
        },
        {
          tag: '文旅',
          name: '嫣然',
          bg: 'linear-gradient(270deg, #3083f2, #00d9ff)',
        },

        {
          tag: '政务',
          name: '伊凡',
          bg: 'linear-gradient(270deg,#f04614, #f09060)',
        },
        {
          tag: '通用',
          name: '温然',
          bg: 'linear-gradient(270deg,#00d290 1%, #00e2ff)',
        },

        {
          tag: '通用',
          name: '爱聆',
          bg: 'linear-gradient(270deg,#00d290 1%, #00e2ff)',
        },
      ],
      swiper2: null,

      duration: 0,
      distanceRatio: 0,
      startTimer: null,

      isPlaying: true,
      clickable: true,
    }
  },
  mounted() {
    let swiper2 = (this.swiper2 = new swiper('#application-advantage-swiper', {
      spaceBetween: 13,
      slidesPerView: 5,
      // longSwipesRatio: 0.1,

      loop: true,
      //   centeredSlides: true,
      autoplay: {
        delay: 0,
        stopOnLastSlide: false,
        disableOnInteraction: false,
      },
      speed: 3000,
      freeMode: false,
    }))

    this.swiper2.el.onmouseenter = (e) => {
      console.log('onmouseenter trigged')
      e && e.stopPropagation()
      this.handleTogglePlay()
    }
    this.swiper2.el.onmouseleave = (e) => {
      console.log('onmouseleave trigged')
      e && e.stopPropagation()
      this.handleTogglePlay()
    }
  },
  methods: {
    // stop
    stopAutoplay() {
      if (this.startTimer) clearTimeout(this.startTimer)

      // Stop slide at current translate.
      this.swiper2.setTranslate(this.swiper2.getTranslate())

      // Calculating the distance between current slide and next slide.
      // 0.3 is equal to 30% distance to the next slide.
      // this.distanceRatio = Math.abs((this.swiper2.width * this.swiper2.activeIndex + this.swiper2.getTranslate()) / this.swiper2.width);

      // currentSlideWidth for slidesPerView > 1
      const currentSlideWidth =
        this.swiper2.slides[this.swiper2.activeIndex].offsetWidth
      this.distanceRatio = Math.abs(
        (currentSlideWidth * this.swiper2.activeIndex +
          this.swiper2.getTranslate()) /
          currentSlideWidth
      )

      // The duration that playing to the next slide
      this.duration = this.swiper2.params.speed * this.distanceRatio
      this.swiper2.autoplay.stop()
    },

    setEasing(easing) {
      const swiperWrapperEl = document.querySelector(
        '.l-banner__swiper-wrapper'
      )
      swiperWrapperEl.style.setProperty('--easing', easing)
    },

    startAutoplay(delay) {
      this.startTimer = setTimeout(() => {
        this.setEasing('linear')
        this.swiper2.autoplay.start()
      }, delay || this.duration)
    },

    handleTogglePlay() {
      if (!this.clickable) return
      this.clickable = false

      if (this.isPlaying) this.stopAutoplay()
      else {
        const distance =
          this.swiper2.width * this.swiper2.activeIndex +
          this.swiper2.getTranslate()

        // Avoid distance that is exactly 0
        this.duration = distance !== 0 ? this.duration : 0
        this.swiper2.slideTo(this.swiper2.activeIndex, this.duration)
        this.startAutoplay()
      }
      this.isPlaying = !this.isPlaying

      setTimeout(() => {
        this.clickable = true
      }, 200)
    },
  },
}
</script>
<style lang="scss" scoped>
.swiper-slide {
  width: 240px !important;
  height: 380px;
  background: rgba(191, 224, 255, 0.31);

  border-image: linear-gradient(180deg, #ffffff, rgba(255, 255, 255, 0.52)) 1 1;
  border-radius: 16px;
  padding: 10px;
  position: relative;
}
.meta-beauty {
  width: 100%;
  height: 100%;
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
}
.meta-tag {
  width: 60px;
  height: 24px;
  background: linear-gradient(270deg, #3083f2, #00d9ff);
  border-radius: 0px 8px 0px 8px;
  top: 10px;
  position: absolute;
  right: 10px;
  text-align: center;
  line-height: 24px;
  color: #fff;
}
.meta-name {
  bottom: 0;
  left: 0;
  width: 100%;
  position: absolute;
  z-index: 1;
  text-align: center;
  color: #fff;
  font-size: 16px;
  line-height: 34px;

  width: 240px;
  height: 34px;
  background: rgba(58, 177, 255, 0.51);
  border-image: linear-gradient(180deg, #ffffff, rgba(255, 255, 255, 0)) 0.5 0.5;
  border-radius: 0px 0px 8px 8px;
  backdrop-filter: blur(7px);
}
.swiper-wrapper {
  transition-timing-function: linear !important;
}
</style>
