<template>
  <os-page :options="pageOptions">
    <studio-qaBank-header-right slot="btn" />
    <div class="skill-publish-page">
      <template>
        <os-collapse :default="true" size="large" title="基本信息">
          <el-form
            ref="skillForm"
            :rules="rules"
            :model="qa"
            label-width="118px"
            label-position="left"
            :disabled="qa.isCheck"
          >
            <el-form-item :label="'问答类型'">
              {{ qa.type == 0 ? '语句问答' : qa.type == 3 ? '关键词问答' : '' }}
            </el-form-item>
            <el-form-item
              :label="'问答名称'"
              prop="name"
              :placeholder="'请输入问答名称'"
            >
              <el-input v-model="qa.name"></el-input>
            </el-form-item>
            <el-form-item :label="'问答ID'">
              {{ qa.repositoryId }}
            </el-form-item>
            <el-form-item :label="'问答分类'">
              <el-select v-model="qa.qaType">
                <el-option
                  v-for="(item, index) in qaTypes"
                  :key="index"
                  :value="item.value"
                  :label="item.label"
                  >{{ item.label }}</el-option
                >
              </el-select>
            </el-form-item>
          </el-form>
        </os-collapse>
        <os-divider />
        <os-collapse :default="true" size="large">
          <template slot="title"> 发布信息 </template>
          <el-form
            ref="releaseForm"
            :model="qa"
            label-width="98px"
            :rules="rules"
            label-position="left"
            :disabled="qa.isCheck"
          >
            <el-form-item
              label="版本号"
              class="is-required"
              prop="latestVersionArr"
              v-if="qa.latestVersionArr"
            >
              <el-input
                class="release-form-version-input"
                v-model.number="qa.latestVersionArr[0]"
                type="number"
              ></el-input>
              <span>.</span>
              <el-input
                class="release-form-version-input"
                v-model.number="qa.latestVersionArr[1]"
                type="number"
              ></el-input>
              <span>.</span>
              <el-input
                class="release-form-version-input mgr16"
                v-model.number="qa.latestVersionArr[2]"
                type="number"
              ></el-input>
              <span v-if="qa.onlineVersion && qa.onlineVersion != '0.0.0'"
                >最近发布的版本{{ qa.onlineVersion }}</span
              >
            </el-form-item>
            <el-form-item label="更新说明">
              <el-input
                type="textarea"
                resize="none"
                style="font-family: Arial"
                :autosize="{ minRows: 4, maxRows: 4 }"
                placeholder="请简要描述你的问答功能，若为版本更新请说明更新点。"
                v-model="qa.updateLog"
              >
              </el-input>
            </el-form-item>
          </el-form>
        </os-collapse>

        <div class="mgb56" style="margin-top: 40px; text-align: right">
          <os-give-up-save :edited="changed" @noSave="noSave" />
          <el-button
            @click="onSubmit(2)"
            :loading="saving"
            :disabled="!changed"
          >
            {{ saving ? '保存中...' : '仅保存' }}
          </el-button>

          <el-button
            type="primary"
            @click="onSubmit(1)"
            :loading="publishing"
            :disabled="!canPulish"
          >
            {{ publishing ? '发布中...' : '发布上线' }}
          </el-button>
        </div>
      </template>
    </div>
    <page-leave-tips
      :dialog="leaveDialog"
      @save="onSubmit(2)"
      @noSave="noSave"
      @noJump="noJump"
    />
  </os-page>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'qa-publish',
  data() {
    return {
      pageOptions: {
        title: '发布',
        loading: false,
      },
      rules: {
        name: this.$rules.skillZhName(),

        latestVersionArr: [{ validator: this.checkVersion, trigger: ['blur'] }],
      },
      qaTypes: [],
      publishing: false,
      saving: false,
      leaveDialog: {
        show: false,
      },
      routeTo: {},
      versionUnValid: false,

      initDataChanged: false, //记录computed检测不到的数据是否有改变(包括：别名、示例说法、iconUrl)
      qa: {},
    }
  },
  beforeRouteLeave: function (to, from, next) {
    if (this.changed) {
      this.leaveDialog.show = true
      this.routeTo = to
      next(false)
    } else {
      next()
    }
  },
  computed: {
    ...mapGetters({
      originalQa: 'studioQa/qa',
      userInfo: 'user/userInfo',
      subAccount: 'user/subAccount',
    }),
    edited() {
      let self = this
      console.log('self.qa, self.originalQa', self.qa, self.originalQa)
      return (
        self.qa.name !== self.originalQa.name ||
        self.qa.updateLog !== self.originalQa.updateLog ||
        self.qa.qaType !== self.originalQa.qaType
      )
    },
    changed() {
      console.log('this.edited', this.edited)
      return this.edited || this.initDataChanged
    },

    canPulish() {
      return !this.qa.isCheck
    },
  },
  watch: {
    originalQa: function (val, oldVal) {
      this.qa = {
        ...this.$deepClone(val),
        qaType: val.qaType ? val.qaType : '0',
      }
      if (this.routeTo.name) {
        this.$router.push({
          name: this.routeTo.name,
          params: this.routeTo.params,
        })
      }
    },
  },
  created() {
    if (this.$store.state.studioQa.id) {
      let qa = this.$deepClone(this.$store.state.studioQa.qa)
      this.qa = {
        ...qa,
        qaType: qa.qaType ? qa.qaType : '0',
      }
    }
    // getQaType
    this.getQaType()
  },

  methods: {
    checkVersion(rule, value, callback) {
      let self = this
      let first = parseInt(value[0]),
        second = parseInt(value[1]),
        third = parseInt(value[2])
      if (
        !Number.isInteger(first) ||
        !Number.isInteger(second) ||
        !Number.isInteger(third)
      ) {
        callback(new Error('版本号必须为数字值'))
      }
      if (
        self.qa.onlineVersion &&
        (value.join('.') == self.qa.onlineVersion ||
          first < self.qa.onlineVersion.split('.')[0])
      ) {
        callback(new Error('当前版本号需大于最近发布的版本号'))
      }
      if (
        self.qa.onlineVersion &&
        first == self.qa.onlineVersion.split('.')[0]
      ) {
        if (second < self.qa.onlineVersion.split('.')[1]) {
          callback(new Error('当前版本号需大于最近发布的版本号'))
        }
        if (
          second == self.qa.onlineVersion.split('.')[1] &&
          third <= self.qa.onlineVersion.split('.')[2] - 1
        ) {
          callback(new Error('当前版本号需大于最近发布的版本号'))
        }
      }
      callback()
    },

    checkInitDataStatus() {
      let self = this
      if (self.qa.name != self.originalQa.name) {
        self.initDataChanged = true
      } else {
        self.initDataChanged = false
      }
    },

    checkStoreSkill() {
      return true
    },
    onSubmit(isPublish) {
      let self = this
      if (this.saving || this.publishing) {
        return
      }

      let canSubmit = true
      this.$refs.releaseForm.validate((valid) => {
        if (!valid) {
          canSubmit = false
        }
      })

      if (!canSubmit) return
      let data = {
        isPublish: isPublish,
        updateLog: this.qa.updateLog || '',
        name: this.qa.name,
        type: this.qa.type,
        repoId: this.qa.repositoryId,
        qaType: this.qa.qaType,
      }
      data.latestNumber = this.qa.latestVersionArr.join('.')
      if (data.latestNumber === '..') {
        data.latestNumber = ''
      }
      self.pageOptions.loading = true
      let api = this.$config.api.STUDIO_QA_CREATE_EDIT

      if (isPublish === 1) {
        this.publishing = true
      } else {
        this.saving = true
      }
      this.$utils.httpPost(api, data, {
        success: (res) => {
          self.pageOptions.loading = false
          if (isPublish === 1) {
            this.publishing = false
            self.$message.success('发布成功')
          } else {
            this.saving = false
            self.$message.success('保存成功')
          }
          self.$store.dispatch('studioQa/setQa', this.qa.repositoryId)
          self.initDataChanged = false
        },
        error: (err) => {
          self.pageOptions.loading = false
          if (isPublish === 1) {
            this.publishing = false
          } else {
            this.saving = false
          }
          console.log('page=>>')
          console.log(err)
        },
      })
    },

    noSave() {
      this.$refs.skillForm.clearValidate()
      this.initDataChanged = false
      this.qa = this.$deepClone(this.originalQa)

      if (this.routeTo.name) {
        this.$router.push({
          name: this.routeTo.name,
          params: this.routeTo.params,
        })
      }
    },
    noJump() {
      this.routeTo = {}
    },

    getQaType() {
      this.$utils.httpGet(
        this.$config.api.STUDIO_QA_TYPE,
        {},
        {
          success: (res) => {
            let initTypes = [{ label: '全部', value: '0' }]
            let realTypes = Object.keys(res.data || {}).map((item) => {
              return {
                value: item,
                label: (res.data || {})[item],
              }
            })
            this.qaTypes = initTypes.concat(realTypes)
            console.log('this.qaTypes', this.qaTypes)
          },
          error: (err) => {},
        }
      )
    },
  },
  components: {},
}
</script>

<style lang="scss" scoped>
.skill-publish-page {
  padding-bottom: 70px;
}
.release-form-price-select {
  width: 92px;
}
.release-form-price-input {
  .el-input__inner {
    width: 160px;
  }
}
.release-form-version-input {
  width: 80px;
}
.release-form-phrase-label {
  color: $grey5;
  padding-right: 24px;
}
.item-skill-privacy {
  font-size: 0;
}
.skill-privacy {
  display: inline-block;
  margin-right: 16px;
  width: 52px;
  height: 24px;
  line-height: 24px;
  text-align: center;
  color: $success;
  border-radius: 12px;
  background-color: $success-light-12;
}
.contact-form {
  .el-form-item {
    width: 43%;
    display: inline-block;
  }
  .el-form-item:nth-child(2n + 1) {
    margin-right: 13%;
  }
}
//实名认证
.certificate-wrap {
  padding: 40px 48px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 1);
  border: 1px solid rgba(242, 245, 247, 1);
  box-shadow: 0px 6px 18px 0px rgba(0, 0, 0, 0.05);
  font-size: 16px;
  .title {
    margin-bottom: 12px;
    line-height: 1.23;
    font-size: 20px;
    font-weight: 500;
  }
  .ic-r-exclamation {
    margin-right: 16px;
    vertical-align: -1px;
    color: $warning;
  }
}
.welcom-words-tip {
  padding-left: 16px;
  height: 40px;
  color: $grey5;
  background: $grey4-15;
  border-radius: 2px;
}

.param-container {
  margin: 0 auto;
  // width: 80%;

  li,
  .protocol-container {
    display: flex;
    //  justify-content: space-between;
    list-style-type: none;
    > div {
      border-left: 1px solid #eee;
      border-top: 1px solid #eee;
      border-bottom: 1px solid #eee;
      text-align: center;
      padding: 10px;
    }
    > div:last-child {
      border-right: 1px solid #eee;
    }
    > div:nth-child(1) {
      width: 20%;
    }
    > div:nth-child(2) {
      width: 20%;
    }
    > div:nth-child(3) {
      width: 20%;
    }
    > div:nth-child(4) {
      width: 20%;
    }
    > div:nth-child(5) {
      width: 20%;
    }
    ::v-deep .el-form-item__content {
      margin-left: 0 !important;
    }
  }

  .protocol-container {
    width: 100%;
  }
}
</style>
<style lang="scss">
.skill-publish-page {
  .os-collapse-title {
    margin: 28px 0;
    .ic-r-angle-d {
      color: $grey5;
    }
    a {
      font-size: 14px;
    }
  }
}
</style>
