<template>
  <div class="main-content">
    <section class="main-content-banner">
      <div class="banner-text">
        <h2
          v-html="'RK3328 AIUI 评估板开发套件'"
          style="line-height: 60px"
        ></h2>
        <p class="pc-show banner-text-content">
          搭载 AIUI
          全链路语音交互能力，快速完成语音项目方案验证，适用于教育教培，服务机器人等场景。
        </p>
        <div class="hor-btn">
          <div class="banner-text-button" @click="toConsole">合作咨询</div>
        </div>
      </div>
    </section>
    <section class="section section-2">
      <div class="section-title">应用场景</div>
      <div class="section-item">
        <ul>
          <li v-for="(item, index) in app_scenario" :key="index" class="app">
            <img :src="item.src" :alt="item.alt" />
            <p class="app-text">{{ item.alt }}</p>
          </li>
        </ul>
      </div>
    </section>
    <section class="section section-3">
      <div class="section-title">产品功能</div>
      <div class="section-item">
        <div>
          <p>录音</p>
          <p>降噪</p>
          <p>回声<br />消除</p>
          <p>声源<br />定位</p>
          <p>唤醒</p>
          <p>在线<br />语音</p>

          <p>基于麦克风阵列<br />录制原始音频</p>
          <p>基于前端声学算法<br />可以将外部噪音进行过滤</p>
          <p>可以接入设备本身的参考信号<br />屏蔽设备本身音频噪音</p>
          <p>可以实现定向拾音<br />过滤波束外噪音</p>
          <p>集成唤醒SDK<br />让设备开始交互第一步</p>
          <p>内部集成AIUI SDK<br />可体验全链路在线语音能力</p>
        </div>
      </div>
    </section>
    <section class="section section-4">
      <div class="section-title">实物图片</div>
      <div class="section-item">
        <div></div>
      </div>
    </section>
    <section class="section section-5">
      <div class="section-title">主板接口说明</div>
      <div class="section-item"></div>
    </section>
    <section class="section section-6">
      <div class="section-title">接线示意图</div>
      <div class="section-item">
        <div>
          <p>录制原始音频给主板</p>
          <p>语音处理结果<br />传给上位机</p>
          <p>回采信号<br />输入到主板</p>
          <p>麦克风阵列</p>
          <p>评估板主板</p>
          <p>上位机</p>
        </div>
      </div>
    </section>
    <section class="section section-7">
      <div class="section-title">硬件参数</div>
      <div class="section-item">
        <ul>
          <li v-for="(item, index) in hard_list" :key="index" class="item">
            <div class="left">
              <img :src="item.src" />
            </div>
            <div class="right">
              <div class="title">{{ item.name }}</div>
              <span>{{ item.content }}</span>
              <span v-show="item.sub_content" class="sub-content">{{
                item.sub_content
              }}</span>
            </div>
          </li>
        </ul>
      </div>
    </section>
    <section class="section section-8">
      <div class="section-title">产品清单</div>
      <div class="section-item">
        <div v-for="item in product_list" :key="item.index" class="item">
          <div class="title">{{ item.title }}</div>
          <p v-html="item.sub_title"></p>
        </div>
      </div>
    </section>
    <section class="section section-9">
      <div class="section-title">开发材料</div>
      <div class="section-item">
        <div class="item">
          <a
            v-for="item in develop_doc"
            :key="item.index"
            @click="toDoc(item.link)"
            style="color: #666666"
            >{{ item.name }}</a
          >
        </div>
      </div>
    </section>
    <!-- <div class="corp">
      <div class="corp-section-wrap">
        <div class="corp-section-title">
          <p class="corp-section-title-contact">立即联系您的专属顾问</p>
          <p class="corp-section-desc2">免费咨询专属顾问 为您量身定制产品推荐方案</p>
        </div>

        <div class="corp-section-item" style="padding-top: 39px; text-align: left">
          <div class="corp-section-button" @click="toConsole">合作咨询</div>
        </div>
      </div>
    </div> -->
  </div>
</template>

<script>
import corp from '@P/aiui/solution-aiui/components/corp.vue'

export default {
  layout: 'aiuiHome',
  data() {
    return {
      app_scenario: [
        {
          alt: '人工智能教培教具',
          src: require('../../../../assets/images/solution/soft-hardware/3328s/yy1.png'),
        },
        {
          alt: '家庭智能服务机器人',
          src: require('../../../../assets/images/solution/soft-hardware/3328s/yy2.png'),
        },
        {
          alt: '地铁自助服务终端',
          src: require('../../../../assets/images/solution/soft-hardware/3328s/yy3.png'),
        },
        {
          alt: '智慧会议室',
          src: require('../../../../assets/images/solution/soft-hardware/3328s/yy4.png'),
        },
      ],
      product_list: [
        {
          title: '硬件',
          sub_title:
            ' • 评估板套件 麦克风板 麦克风排线<br/> • 回采线 USB线 电源适配器',
        },
        {
          title: '软件',
          sub_title:
            ' • 唤醒SDK，集成前端声学算法 AIUI SDK<br/> • 集成云端交互能力AIUI SDK',
        },
        {
          title: '服务',
          sub_title: ' • 为期两个月的VIP技术支持<br/> • 浅定制资源定制支持',
        },
      ],
      hard_list: [
        {
          name: 'CPU',
          src: require('../../../../assets/images/solution/soft-hardware/3328s/yj1.png'),
          content: '四核Cortex-A53',
        },
        {
          name: '主频',
          src: require('../../../../assets/images/solution/soft-hardware/3328s/yj2.png'),
          content: '1.5GHz',
        },
        {
          name: '操作系统',
          src: require('../../../../assets/images/solution/soft-hardware/3328s/yj3.png'),
          content: 'Andriod',
        },
        {
          name: '电压',
          src: require('../../../../assets/images/solution/soft-hardware/3328s/yj4.png'),
          content: 'DC12V',
        },
        {
          name: '电流',
          src: require('../../../../assets/images/solution/soft-hardware/3328s/yj5.png'),
          content: '典型值0.13A,max0.24A',
        },
        {
          name: '扬声器功率',
          src: require('../../../../assets/images/solution/soft-hardware/3328s/yj6.png'),
          content: '双10W 4Ω',
        },
        {
          name: '以太网',
          src: require('../../../../assets/images/solution/soft-hardware/3328s/yj7.png'),
          content: '10/100Mbps',
        },
        {
          name: 'WIFI',
          src: require('../../../../assets/images/solution/soft-hardware/3328s/yj8.png'),
          content: '2.4G/5G',
        },
        {
          name: '麦克风',
          src: require('../../../../assets/images/solution/soft-hardware/3328s/yj9.png'),
          content: '默认模拟硅麦',
          sub_content: '(驻极体麦可联系商务提供)',
        },
        {
          name: '灵敏度',
          src: require('../../../../assets/images/solution/soft-hardware/3328s/yj10.png'),
          content: '-32dB',
        },
        {
          name: '信噪比',
          src: require('../../../../assets/images/solution/soft-hardware/3328s/yj11.png'),
          content: '驻极体麦74dB,模拟硅麦65dB',
        },
        {
          name: '主板尺寸',
          src: require('../../../../assets/images/solution/soft-hardware/3328s/yj12.png'),
          content: '126mm*81.4mm',
        },
        {
          name: '工作环境',
          src: require('../../../../assets/images/solution/soft-hardware/3328s/yj13.png'),
          content: '10~75°,相对湿度≤80%',
        },
      ],
      develop_doc: [
        {
          name: ' • 《RK3328 AIUI评估板开发套件产品白皮书.pdf》',
          link: 'https://aiui-doc.xf-yun.com/project-1/doc-184/',
        },
        {
          name: ' • 《RK3328 AIUI评估板开发套件产品规格书.pdf》',
          link: 'https://aiui-doc.xf-yun.com/project-1/doc-186/',
        },
        {
          name: ' • 《RK3328 AIUI评估板开发套件产品使用手册.pdf》',
          link: 'https://aiui-doc.xf-yun.com/project-1/doc-185/',
        },
        {
          name: ' • 《RK3328 AIUI评估板开发套件开发手册.pdf》',
          link: 'https://aiui-doc.xf-yun.com/project-1/doc-187/',
        },
      ],
    }
  },
  methods: {
    toConsole() {
      // 判断是否有百度SEM营销参数，有的话，提交表单页需要带上
      const search = window.location.search
      if (search) {
        window.open(`/solution/apply/12${search}`)
      } else {
        window.open('/solution/apply/12')
      }
    },
    toBuy() {
      window.open('https://www.aifuwus.com/onstage/cmddetail?product_type=4436')
    },
    toDoc(link) {
      window.open(link)
    },
  },
  components: { corp },
}
</script>

<style lang="scss" scoped>
@import '../../../../assets/scss/screen-and-lamp.scss';

@media screen and (min-width: 751px) {
  .main-content {
    &-banner {
      background: url(~@A/images/solution/soft-hardware/3328s/banner_bg.png)
        center no-repeat;
      background-size: cover;
      height: 500px;
      overflow: hidden;
      width: 100%;

      .banner-text {
        max-width: 1200px;
        color: #fff;
        height: 100%;
        margin: auto;

        .hor-btn {
          display: flex;
          flex-direction: row;
          justify-content: flex-start;

          div:nth-child(2) {
            margin-left: 30px;
          }
        }

        &-button {
          font-size: 16px;
          text-align: center;
          font-weight: 400;
          width: 180px;
          height: 50px;
          line-height: 50px;
          border-radius: 4px;
          color: #fff;
          cursor: pointer;
          transition: 0.6s;
          background: $primary;
          border: none;
        }

        h2 {
          color: #000;
          padding-top: 148px;
          margin-bottom: 25px;
          font-size: 44px;
          font-weight: 600;
          line-height: 44px;
          font-family: PingFang SC, PingFang SC-Semibold;
        }

        p {
          font-size: 18px;
          margin-bottom: 25px;
        }

        .banner-text-content {
          width: 570px;
          font-size: 16px;
          font-weight: 400;
          color: #181818;
          line-height: 30px;
          font-family: PingFang SC, PingFang SC-Regular;
        }
      }
    }
  }

  .section-title {
    font-size: 34px;
    font-weight: bold;
    color: #333;
    margin-top: 0 !important;
    margin-bottom: 30px !important;
  }

  .section-sub-title {
    font-size: 16px;
    font-weight: 400;
    text-align: center;
    color: #666666;
    margin-bottom: 60px !important;
  }

  .app-text {
    color: #666;
  }

  .corp {
    padding-top: 76px;
    height: 320px;
    background: url(~@A/images/solution/acoustics/corp.png) center/cover
      no-repeat;

    .corp-section-wrap {
      width: 1200px;
      margin: 0 auto;
    }

    .corp-section-title-contact {
      font-size: 36px !important;
      font-weight: 400;
      color: #000000 !important;
      text-align: left !important;
      margin-top: 0 !important;
    }

    .corp-section-desc2 {
      text-align: left;
      margin-top: 20px !important;
      font-size: 18px;
      line-height: 30px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #262626 !important;
      display: block;
    }

    .corp-section-button {
      font-size: 16px;
      text-align: center;
      line-height: 50px;
      border: none;
      color: #fff;
      cursor: pointer;
      width: 180px;
      height: 50px;
      background: $primary;
      border-radius: 4px;
    }
  }

  .section-2 {
    .section-item {
      margin-top: 50px !important;

      > ul {
        display: flex;
        justify-content: space-between !important;

        li {
          width: 285px !important;
          position: relative;

          img {
            width: 100%;
          }

          .app-text {
            width: 100%;
            position: absolute;
            top: 0;
            left: 50%;
            transform: translate(-50%, 0);
            margin-top: 20px !important;
            font-weight: 600;
          }
        }
      }
    }
  }

  .section-3 {
    background: url('../../../../assets/images/solution/soft-hardware/3328s/banner_bg3.png')
      center no-repeat !important;
    background-size: cover !important;
    height: 550px;
    width: 100%;

    .section-item {
      height: 507px;
      margin-top: 50px !important;

      div {
        background: url('../../../../assets/images/solution/soft-hardware/3328s/cp.png')
          center no-repeat !important;
        background-size: cover !important;
        width: 100%;
        height: 267px;
        position: relative;

        p {
          &:nth-child(1),
          &:nth-child(2),
          &:nth-child(3),
          &:nth-child(4),
          &:nth-child(5),
          &:nth-child(6) {
            position: absolute;
            top: 124px;
            font-size: 18px;
            font-family: PingFang SC, PingFang SC-Semibold;
            font-weight: 600;
            text-align: left;
            color: #ffffff;
            line-height: 30px;
          }

          &:nth-child(7),
          &:nth-child(8),
          &:nth-child(9),
          &:nth-child(10),
          &:nth-child(11),
          &:nth-child(12) {
            position: absolute;
            bottom: -70px;
            font-size: 14px;
            font-family: PingFang SC, PingFang SC-Regular;
            font-weight: 400;
            text-align: center;
            color: #262626;
            line-height: 24px;
          }

          &:nth-child(1) {
            left: 106px;
            top: 124px;
          }

          &:nth-child(2) {
            left: 312px;
          }

          &:nth-child(3) {
            left: 520px;
            top: 104px;
          }

          &:nth-child(4) {
            left: 728px;
            top: 104px;
          }

          &:nth-child(5) {
            left: 934px;
          }

          &:nth-child(6) {
            left: 1142px;
            top: 104px;
          }

          &:nth-child(7) {
            left: 30px;
          }

          &:nth-child(8) {
            left: 210px;
          }

          &:nth-child(9) {
            left: 400px;
          }

          &:nth-child(10) {
            left: 650px;
          }

          &:nth-child(11) {
            left: 846px;
          }

          &:nth-child(12) {
            right: 0;
          }
        }
      }
    }

    .section-title {
      margin-bottom: 0 !important;
    }
  }

  .section-4 {
    background: white !important;
    height: 700px !important;
    width: 100%;
    background: url('../../../../assets/images/solution/soft-hardware/3328s/banner_bg1.png')
      center no-repeat !important;
    background-size: cover !important;

    .section-title {
      margin-bottom: 0 !important;
      color: #fff;
    }

    .section-item {
      margin-top: 0 !important;
      padding: 0 !important;
      background: url('../../../../assets/images/solution/soft-hardware/3328s/photo.png')
        center no-repeat !important;
      background-size: cover !important;
      height: 550px;
      position: relative;

      div {
        position: absolute;
        bottom: 0;
        left: 0;
        display: flex;
        width: 100%;
        flex-direction: row;
        justify-content: flex-start;

        p {
          font-size: 14px;
          font-weight: 400;
          text-align: center;
          color: #262626;
        }

        p:nth-child(1) {
          margin-left: 220px;
        }

        p:nth-child(2) {
          margin-left: 230px;
        }

        p:nth-child(3) {
          margin-left: 65px;
        }

        p:nth-child(4) {
          margin-left: 25px;
        }
      }
    }
  }

  .section-5 {
    width: 100%;
    height: 728px;
    max-width: 100% !important;
    position: relative;

    .section-item {
      max-width: 1200px;
      margin: 0 auto !important;
      background: url('../../../../assets/images/solution/soft-hardware/3328s/explain.png')
        center no-repeat !important;
      background-size: cover !important;
      width: 1200px;
      height: 628px;
    }

    .section-title {
      position: absolute;
      left: 50%;
      top: 70px;
      transform: translate(-50%, 0);
    }
  }

  .section-6 {
    background: url('../../../../assets/images/solution/soft-hardware/3328s/banner_bg2.png')
      center no-repeat !important;
    background-size: cover !important;
    width: 100%;
    max-width: 100% !important;
    position: relative;

    .section-item {
      margin-top: 50px;
      height: 385px;
      width: 100%;
      max-width: 1200px;
      margin: 0 auto;
      position: relative;

      div {
        p {
          &:nth-child(1),
          &:nth-child(2),
          &:nth-child(3) {
            font-size: 14px;
            font-family: PingFang SC, PingFang SC-Regular;
            font-weight: 400;
            text-align: center;
            color: #262626;
            line-height: 21px;
            position: absolute;
            text-align: center;
          }

          &:nth-child(4),
          &:nth-child(5),
          &:nth-child(6) {
            font-size: 18px;
            font-family: PingFang SC, PingFang SC-Regular;
            font-weight: 400;
            text-align: justifyLeft;
            color: #212122;
            line-height: 30px;
            position: absolute;
            bottom: 0;
          }

          &:nth-child(1) {
            left: 140px;
            top: 186px;
          }

          &:nth-child(2) {
            left: 800px;
            top: 100px;
          }

          &:nth-child(3) {
            left: 806px;
            top: 240px;
          }

          &:nth-child(5) {
            left: 468px;
          }

          &:nth-child(6) {
            right: 30px;
          }
        }
      }
    }
  }

  .section-7 {
    background-color: #fff !important;
    width: 100%;
    max-width: 100% !important;
    height: 600px;
    position: relative;

    .section-title {
      margin-bottom: 40px !important;
    }

    .section-item {
      margin-top: 50px;
      height: 115px;
      width: 100%;
      max-width: 1200px;
      margin: 0 auto;

      ul {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
      }

      .item {
        width: 25%;
        display: flex;
        align-items: center;
        margin-top: 36px;

        .left {
          width: 73px;
        }

        .right {
          margin-left: 16px;

          .title {
            font-size: 18px;
            font-family: PingFang SC, PingFang SC-Semibold;
            font-weight: 600;
            text-align: left;
            color: #262626;
            line-height: 30px;
          }

          .content {
            font-size: 14px;
            font-family: PingFang SC, PingFang SC-Regular;
            font-weight: 400;
            text-align: justifyLeft;
            color: #666666;
            line-height: 42px;
          }

          .sub-content {
            display: inline-block;
            font-size: 14px;
            font-family: PingFang SC, PingFang SC-Regular;
            font-weight: 400;
            text-align: justifyLeft;
            color: rgb(167, 167, 167);
          }
        }
      }
    }
  }

  .section-8 {
    .section-item {
      display: flex;
      flex-direction: row;
      justify-content: space-around;
      align-items: center;

      .item {
        text-align: center;
        height: 151px;
        width: 490px;
        background: url('../../../../assets/images/solution/soft-hardware/3328s/qd1.png')
          center no-repeat !important;
        background-size: contain !important;

        &:nth-child(2) {
          background: url('../../../../assets/images/solution/soft-hardware/3328s/qd2.png')
            center no-repeat !important;
          background-size: contain !important;
        }

        &:nth-child(3) {
          background: url('../../../../assets/images/solution/soft-hardware/3328s/qd3.png')
            center no-repeat !important;
          background-size: contain !important;
        }

        .title {
          font-size: 18px;
          font-family: PingFang SC, PingFang SC-Semibold;
          font-weight: 600;
          text-align: left;
          color: #262626;
          line-height: 30px;
          text-align: center;
          margin-top: 24px;
        }

        p {
          font-size: 14px;
          font-family: PingFang SC, PingFang SC-Regular;
          font-weight: 400;
          text-align: left;
          color: #666666;
          margin-top: 20px;
          margin-left: 62px;
          line-height: 30px;
        }
      }
    }
  }

  .section-9 {
    width: 100%;
    max-width: 100% !important;
    position: relative;

    .section-item {
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      background: url('../../../../assets/images/solution/soft-hardware/3328s/kfcl.png')
        center no-repeat !important;
      background-size: contain !important;
      height: 224px;

      .item {
        padding-top: 50px;
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        grid-gap: 10px;
      }
    }
  }
}

@media screen and (max-width: 750px) {
  .main-content {
    &-banner {
      background: url('../../../../assets/images/solution/soft-hardware/3328/banner_bg.png')
        center no-repeat;
      background-size: cover;
    }
  }
}

.contact-wrap {
  // padding-top: 100px;
  height: 400px;
  text-align: center;

  .title {
    margin-bottom: 16px;
    font-size: 34px;
    color: #333;
    font-weight: bold;
  }

  .desc {
    font-size: 16px;
    color: #666;
    margin-bottom: 50px;
  }

  .apply-btn {
    margin: 60px auto 0;
    background: #1784e9;

    &:hover {
      color: #fff;
    }
  }
}
</style>
