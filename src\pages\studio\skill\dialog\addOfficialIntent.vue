<template>
  <el-dialog
    title="引用官方意图"
    :visible.sync="dialog.show"
    width="640px"
  >
    <div class="mgb24" @keyup.enter="searchIntention">
      <el-input
        class="search-area"
        placeholder="搜索官方意图"
        v-model="intentionSearchName">
        <i slot="suffix" class="el-input__icon el-icon-search search-area-btn" @click="searchIntention"></i>
      </el-input>
    </div>

    <os-table
      ref="multipleTable"
      :tableData="tableData"
      class="dialog-official-intents"
      tooltip-effect="dark"
      style="width: 100%"
      height="250"
      @selection-change="handleSelectionChange">
      <el-table-column
        type="selection"
        width="55">
      </el-table-column>
      <el-table-column
        label="意图名称"
        width="120">
        <template slot-scope="scope">{{ scope.row.zhName }}</template>
      </el-table-column>
      <el-table-column
        prop="name"
        label="英文标识"
        width="160">
      </el-table-column>
      <el-table-column
        prop="descrition"
        label="意图概述"
        show-overflow-tooltip>
      </el-table-column>
    </os-table>

    <span slot="footer" class="dialog-footer">
      <span class="dialog-official-intent-selectnum">已选（{{multipleSelection.length}}）</span>
      <el-button class="dialog-btn" type="primary" style="min-width: 104px;" @click="save">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>

export default {
  props: {
    dialog: {
      type: Object,
      default: {}
    }
  },
  data () {
    return {
      intentionSearchName: '',
      tableData: {
        loading: true,
        total: 0,
        page: 1,
        size: 10,
        list: []
      },
      multipleSelection: []
    }
  },
  computed: {
    businessId () {
      return this.$store.state.studioSkill.id
    }
  },
  watch: {
    'dialog.show': function(val, oldVal) {
      if (val) {
        this.searchIntention()
      } else {

      }
    }
  },
  mounted() {

  },
  methods: {
    searchIntention () {
      let self = this
      this.tableData.loading = true
      this.$utils.httpGet(this.$config.api.STUDIO_BULITIN_INTENTS, {
        businessId: this.businessId,
        search: this.intentionSearchName
      }, {
        success: (res) => {
          this.tableData.list = res.data
          this.tableData.loading = false
          self.toggleSelection(this.tableData.list)
        },
        error: (err) => {
          console.log('page=>>');
          console.log(err);
        }
      })
    },
    save () {
      let self = this
      let ids = Array.prototype.map.call(this.multipleSelection, function (item, itemIndex) {
        return item.id
      })
      this.$utils.httpPost(this.$config.api.STUDIO_SWITCH_BULITIN_INTENTS, {
        businessId: this.businessId,
        ids: ids.toString()
      }, {
        success: (res) => {
          self.$message.success('保存成功')
          self.$emit('change', 1)
          self.dialog.show = false
        },
        error: (err) => {
          console.log('page=>>');
          console.log(err);
        }
      })
    },
    toggleSelection(rows) {
      let self = this
      if (rows) {
        rows.forEach(row => {
          if (row.checked) {
            this.$nextTick(() => {
              self.$refs.multipleTable.toggleRowSelection(row)
            })
          }
        });
      } else {
        this.$refs.multipleTable.clearSelection();
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
      console.log(val);
    }
  },
  components: {

  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="scss" scoped>
.dialog-official-intents {
  margin-bottom: 15px;
}
.dialog-official-intent-selectnum {
  color: $grey4;
  margin-right: 24px;
}
</style>
