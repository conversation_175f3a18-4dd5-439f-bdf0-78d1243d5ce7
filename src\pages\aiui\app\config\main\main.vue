<template>
  <div class="content-container">
    <div class="content-container-content" ref="scrollContent">
      <div
        class="form-menu"
        v-if="
          currentScene.point && currentScene.point.split(',').indexOf('4') < 0
        "
      >
        <ul>
          <li
            v-for="(item, index) in menuList"
            :key="item.name"
            :class="{ hd2: true, active: item.name === activeModule }"
            @click="jumpTo(item.name)"
          >
            {{ item.value }}
          </li>
        </ul>
      </div>
      <div ref="recognition">
        <recognition></recognition>
      </div>
      <div class="gutter" ref="semantic">
        <semantic></semantic>
      </div>
      <div class="gutter" ref="spark">
        <spark></spark>
      </div>
      <!-- currentScene.sos === true 表示新的智能体模式场景应用-->

      <div class="gutter" ref="synthesis">
        <synthesis></synthesis>
      </div>
      <div
        class="gutter"
        v-if="
          currentScene.point && currentScene.point.split(',').indexOf('4') >= 0
        "
      >
        <translation></translation>
      </div>
      <div
        class="gutter"
        v-if="
          !(
            currentScene &&
            currentScene.chainId === 'sos_app' &&
            currentScene.point === '1,13'
          )
        "
        ref="other"
      >
        <other></other>
      </div>
    </div>
  </div>
</template>
<script>
import { mapGetters } from 'vuex'
import basic from './basic'
import recognition from './recognition/recognition'
import semantic from './semantic/semantic'
import role from './role'
import synthesis from './synthesis'
import translation from './translation'
import spark from './semantic/spark'

import other from './other'

export default {
  data() {
    return {
      activeModule: 'recognition',
    }
  },
  computed: {
    ...mapGetters({
      currentScene: 'aiuiApp/currentScene',
      app: 'aiuiApp/app',
      subAccountEditable: 'aiuiApp/subAccountEditable',
    }),
    appId() {
      return this.$route.params.appId
    },

    menuList() {
      return [
        {
          name: 'recognition',
          value: '语音识别配置',
        },
        {
          name: 'semantic',
          value: '结构化语义配置',
        },
        {
          name: 'spark',
          value: '星火大模型配置',
        },
        {
          name: 'synthesis',
          value: '语音合成配置',
        },

        {
          name: 'other',
          value: '其他配置',
        },
      ].filter(Boolean)
    },
  },
  // watch: {
  //   activeModule(val) {
  //     this.$nextTick(() => {
  //       let scrollDom = this.$refs['scrollContent']
  //       if (val === 'recognition') {
  //         scrollDom.scrollTo({
  //           top: 0,
  //           behavior: 'smooth',
  //         })
  //       } else if (val === 'semantic') {
  //         scrollDom.scrollTo({
  //           top: this.$refs['semantic'].offsetTop,
  //           behavior: 'smooth',
  //         })
  //       } else if (val === 'spark') {
  //         scrollDom.scrollTo({
  //           top: this.$refs['spark'].offsetTop,
  //           behavior: 'smooth',
  //         })
  //       } else if (val === 'synthesis') {
  //         scrollDom.scrollTo({
  //           top: this.$refs['synthesis'].offsetTop,
  //           behavior: 'smooth',
  //         })
  //       } else if (val === 'other') {
  //         scrollDom.scrollTo({
  //           top: this.$refs['other'].offsetTop,
  //           behavior: 'smooth',
  //         })
  //       }
  //     })
  //   },
  // },

  mounted() {
    const dom = this.$refs['scrollContent']
    if (dom) {
      // dom.addEventListener(
      //   'scroll',
      //   throttle(() => {
      //     this.handleScroll()
      //   }, 10)
      // )
      dom.addEventListener('scroll', this.handleScroll)
    }
  },

  destroyed() {
    const dom = this.$refs['scrollContent']
    if (dom) {
      dom.removeEventListener('scroll', this.handleScroll)
    }
  },
  methods: {
    jumpTo(module) {
      let el = this.$refs[module]

      if (el) {
        // el.scrollIntoView({
        //   behavior: 'smooth',
        //   block: 'start',
        //   inline: 'nearest',
        // })
        this.$refs['scrollContent'].scrollTo({
          top: el.offsetTop,
          behavior: 'smooth',
        })
      }
    },

    handleScroll() {
      let scrollDom = this.$refs['scrollContent']

      if (
        this.$refs['other'] &&
        scrollDom.scrollTop > this.$refs['other'].offsetTop - 1
      ) {
        this.activeModule = 'other'
        return
      }
      if (
        this.$refs['synthesis'] &&
        scrollDom.scrollTop > this.$refs['synthesis'].offsetTop - 1
      ) {
        this.activeModule = 'synthesis'
        return
      }

      if (
        this.$refs['spark'] &&
        scrollDom.scrollTop > this.$refs['spark'].offsetTop - 1
      ) {
        this.activeModule = 'spark'
        return
      }
      if (
        this.$refs['semantic'] &&
        scrollDom.scrollTop > this.$refs['semantic'].offsetTop - 1
      ) {
        this.activeModule = 'semantic'
        return
      }
      this.activeModule = 'recognition'
    },
  },
  components: {
    basic,
    recognition,
    semantic,
    spark,
    role,
    synthesis,
    other,
    translation,
  },
}
</script>
<style lang="scss" scoped>
@import './common.scss';
.gutter {
  margin-top: 10px;
}
.content-container {
  background: $secondary-bgc;
  position: relative;
}

.content-container-content {
  padding: 14px 16px;
  height: calc(100vh - 128px);
  overflow: auto;
}
</style>
