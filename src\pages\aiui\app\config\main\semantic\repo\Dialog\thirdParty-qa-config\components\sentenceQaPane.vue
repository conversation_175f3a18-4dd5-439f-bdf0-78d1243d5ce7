<template>
  <div class="sentence_qa_wrapper">
    <header class="qa_header">
      <!-- <el-button plain icon="el-icon-plus" @click="jump">创建问答库</el-button> -->
      <el-input
        size="medium"
        class="search-area"
        placeholder="搜索"
        v-model.trim="searchVal"
        @keyup.enter.native="searchAppConfig"
        style="width: 267px"
      >
        <i
          @click.stop.prevent="searchAppConfig"
          slot="suffix"
          class="el-input__icon el-icon-search search-area-btn"
        />
      </el-input>
    </header>
    <main class="qa_main" v-loading="loading">
      <qaCard
        :data="ragData"
        :dataCopy="ragDataCopy"
        @selectchange="onSelectchange"
      ></qaCard>
    </main>
  </div>
</template>

<script>
import qaCard from './qaCard.vue'

export default {
  name: 'SentenceQaPane',
  components: { qaCard },
  props: {
    appId: '',
    currentScene: Object,
  },
  data() {
    return {
      searchVal: '',
      ragData: [],
      ragDataCopy: [],
      switchHasChange: false,
      originData: [],
      loading: false,
      channel: '',
      configForm: {
        channel: 2,
        threshold: 0.1,
      },
      mockData: [
        {
          capacity: 0,
          configId: 152,
          createTime: '2024-12-27 16:06:26',
          fromSource: 'aiui',
          groupId: 'group_56c0eb6986b522386919196b',
          id: 45,
          isDeleted: 0,
          isTop: 0,
          name: '去玩儿',
          repoId: 'insight_spark_201024_ki9u8',
          repoType: 1,
          selected: true,
          status: 1,
          threshold: '',
          uid: **********,
        },
        {
          capacity: 0,
          configId: 166,
          createTime: '2025-02-14 16:50:31',
          fromSource: 'aiui',
          groupId: 'group_e46a8ba23a2f60d0fd9e0046',
          id: 84,
          isDeleted: 0,
          isTop: 0,
          name: '标签库',
          repoId: 'insight_spark_201024_8ev8f',
          repoType: 1,
          selected: true,
          status: 1,
          threshold: '',
          uid: **********,
          updateTime: '2025-03-03 15:43:48',
        },
      ],
    }
  },
  created() {
    this.getCardList()
  },
  methods: {
    validate() {
      return Promise.resolve(true)
    },
    jump() {
      window.open('/studio/qaBank', '_blank')
    },
    onConfigChange(type, val) {
      this.configForm[type] = val
    },
    saveChangeData() {
      let that = this
      let param = {
        botId: this.currentScene.botBoxId,
        channel: this.configForm.channel,
        threshold: this.configForm.threshold,
        addRepos: [],
        updateRepos: [],
        delRepos: [],
      }

      this.$utils.httpPost(
        this.$config.api.AIUI_BOT_CONFIG_SAVE_RAGREOPCONFIG,

        JSON.stringify(param),
        {
          config: {
            headers: {
              'Content-Type': 'application/json',
            },
          },
          success: (res) => {
            that.$message.success('保存成功')
          },
          error: (err) => {
            that.$message.error(err.desc)
          },
        }
      )
    },
    getCardList() {
      let that = this
      this.loading = true

      setTimeout(() => {
        that.loading = false
        that.originData = JSON.parse(JSON.stringify(this.mockData))
        that.ragDataCopy = JSON.parse(JSON.stringify(this.mockData))
        that.ragData = [
          ...this.mockData,
          ...this.mockData,
          ...this.mockData,
          ...this.mockData,
        ]
      }, 500)

      return
      this.$utils.httpGet(
        this.$config.api.AIUI_BOT_CONFIG_GET_BOTRAGREPOS,
        {
          botId: this.currentScene.botBoxId,
          pageIndex: 1,
          pageSize: 1000,
        },
        {
          success: (res) => {
            console.log(res, '这个是知识库的res')
            that.loading = false
            let newRepos = (res.data.repos || []).map(({ repoid, ...rest }) => {
              return {
                ...rest,
                repoId: repoid,
              }
            })
            that.originData = JSON.parse(JSON.stringify(newRepos))
            that.ragDataCopy = JSON.parse(JSON.stringify(newRepos))
            that.channel = res.data.channel
            that.ragData = newRepos || []
          },
          error: (res) => {},
        }
      )
    },
    searchAppConfig() {
      this.ragData = this.ragDataCopy.filter((it) =>
        it.name.includes(this.searchVal)
      )
    },
    onSelectchange(id, isSelected) {
      this.switchHasChange = true
      this.ragData = this.ragData.map((item) => {
        if (item.id === id) {
          return {
            ...item,
            selected: isSelected,
          }
        } else {
          return { ...item }
        }
      })
      this.ragDataCopy = this.ragDataCopy.map((item) => {
        if (item.id === id) {
          return {
            ...item,
            selected: isSelected,
          }
        } else {
          return { ...item }
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.sentence_qa_wrapper {
  height: 100%;
  .qa_header {
    margin-bottom: 20px;
  }
  .qa_main {
    height: 344px;
  }
}
</style>
