<template>
  <os-page :options="pageOptions" @returnCb="goback">
    <div class="os-scroll container">
      <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
        <el-tab-pane
          v-for="item in classifies"
          :label="item.name"
          :name="item.id"
          :key="item.id"
        >
          <!-- <official-intention :type="item.id"></official-intention> -->
        </el-tab-pane>
      </el-tabs>
      <official-intention :type="activeName"></official-intention>
    </div>
  </os-page>
</template>
<script>
import officialIntention from './officialIntensions/index.vue'
import { mapGetters } from 'vuex'

export default {
  name: 'refer-offical-intentions',
  data() {
    return {
      pageOptions: {
        title: '引用官方意图',
        returnBtn: true,
        loading: false,
      },
      activeName: '1',
      classifies: [],
    }
  },

  created() {
    // 获取引用的官方意图分类
    this.getIntentClassifies()
  },
  computed: {
    ...mapGetters({
      subAccount: 'user/subAccount',
    }),
  },

  methods: {
    handleClick(tab, event) {
      console.log(tab, event)
    },
    getIntentClassifies() {
      this.$utils.httpGet(
        this.$config.api.STUDIO_INTENT_CLASSIFIERS,
        {},
        {
          success: (res) => {
            if (res && res.data) {
              const { classifies = [] } = res.data
              this.classifies = classifies.map((item) => {
                return {
                  ...item,
                  id: String(item.id),
                }
              })
              if (classifies.length > 0) {
                this.activeName = String(classifies[0].id)
              }
              return
            }
          },
          error: (err) => {
            console.log(err)
          },
        }
      )
    },
    goback() {
      if (this.subAccount) {
        this.$router.push({ name: 'sub-skill-intentions' })
      } else {
        this.$router.push({ name: 'skill-intentions' })
      }
    },
  },
  components: {
    officialIntention,
  },
}
</script>

<style lang="scss" scoped>
.container {
  padding-top: 12px;
}
</style>
