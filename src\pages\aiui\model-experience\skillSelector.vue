<template>
  <div style="position: relative; min-width: 300px; margin-right: 10px">
    <div class="control-btns control-btns-left">
      <div class="selector-title-wrap">
        <i class="selector-icon skill-icon"></i>
        <span>技能选择</span>
        <div class="selector-divider"></div>
      </div>
      <ul class="type-wrapper">
        <li
          @click.stop="showSelector(item)"
          v-for="item in skillTypeList"
          :key="item.key"
        >
          {{ item.value }}
        </li>
      </ul>
    </div>

    <div class="abilities" v-show="visible" v-clickoutside="closeSelector">
      <!-- <div class="close-btn" @click="closeSelector">×</div> -->
      <div
        class="ability-tip"
        v-if="skills.filter((item) => item.checked).length > 0"
      >
        <div class="ability-tip__left">
          当前已选择&nbsp;<strong class="ability-number">{{
            skills.filter((item) => item.checked).length
          }}</strong
          >&nbsp;个技能:
        </div>
        <ul class="ability-tip__right">
          <li
            v-for="(it, idx) in skills.filter((item) => item.checked)"
            :key="idx"
          >
            {{ it.zhName }}
          </li>
        </ul>
      </div>
      <ul class="ability-wrapper">
        <li v-for="item in skillTypeList" :key="item.key">
          <div class="ability-type-wrapper" :id="item.key">
            {{ item.value }}
          </div>
          <div class="ability-container">
            <div
              class="ability-unit"
              v-for="it in skills.filter(
                (s) => String(s.businessType) === String(item.key)
              )"
              :key="it.id"
            >
              <div class="ability-top">
                <div class="ability-icon" v-if="it.url">
                  <img :src="it.url" />
                </div>
                <div class="ability-icontext" v-else>
                  {{ it.zhName.substr(0, 1) }}
                </div>
                <div class="ability-right">
                  <div class="ability-name" :title="it.zhName">
                    {{ it.zhName }}
                  </div>
                  <div class="ability-desc" :title="it.briefIntroduction">
                    {{ it.briefIntroduction }}
                  </div>
                </div>
                <div class="switch-wrpper">
                  <el-switch
                    size="small"
                    :value="it.checked"
                    @input="onSwitchInput(it)"
                  ></el-switch>
                </div>
              </div>
            </div>
          </div>
        </li>
      </ul>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      visible: false,
      skillTypeList: [],
      skills: [],
    }
  },
  created() {
    this.getSkillType()
    this.getTotalAllSkills()
  },
  beforeDestroy() {
    this.$emit('selected', '')
  },
  methods: {
    showSelector(item) {
      this.clickedKey = item.key
      this.visible = true
      this.$nextTick(() => {
        let container = document.getElementById(item.key)
        if (container) {
          container.scrollIntoView({
            behavior: 'smooth',
            block: 'start',
            inline: 'nearest',
          })
        }
      })
    },
    closeSelector() {
      this.visible = false
    },

    getTotalAllSkills() {
      Promise.all([this.getAllSkills(), this.getDefaultSelectedSkills()]).then(
        (data) => {
          // console.log('getTotalAllSkills', data)
          let skills = data[0] || []
          let tookSkillsDefaultSelected = data[1]
          this.skills = skills.map((item) => {
            return {
              ...item,
              checked: tookSkillsDefaultSelected.includes(String(item.id)),
            }
          })
          this.$emit(
            'selected',
            this.skills
              .filter((item) => item.checked)
              .map((item) => item.name)
              .join('|')
          )
        }
      )
    },

    getSkillType() {
      this.$utils.httpGet(
        this.$config.api.STUDIO_SKILL_SKILL_TYPES,
        {},
        {
          success: (res) => {
            this.skillTypeList = Object.keys(res.data).map((k) => {
              return {
                key: k,
                value: res.data[k],
              }
            })
          },
          error: (err) => {
            console.log('page=>>')
            console.log(err)
          },
        }
      )
    },

    getDefaultSelectedSkills() {
      return new Promise((resolve, reject) => {
        this.$utils.httpGet(
          this.$config.api.RESOURCE_AIHUB_GETSELECTED_SKILLSLIST,
          { aiui: 1 },
          {
            success: (res) => {
              let skills = res.data.skills || []
              resolve(skills)
            },
            error: (err) => {
              console.log('page=>>')
              console.log(err)
            },
          }
        )
      })
    },
    // getAllSkills() {
    //   this.$utils.httpGet(
    //     this.$config.api.RESOURCE_AIHUB_GETSKILLSLIST,
    //     { pageIndex: 1, pageSize: 1000, experience: 1 },
    //     {
    //       success: (res) => {
    //         let skills = res.data.skills || []
    //         let tookSkillsDefaultSelected = skills
    //           .filter((s) => String(s.businessType) === String('10100'))
    //           .filter((_, index) => index < 10)
    //           .map((s) => s.id)
    //         this.skills = skills.map((item) => {
    //           return {
    //             ...item,
    //             checked: tookSkillsDefaultSelected.includes(item.id),
    //           }
    //         })
    //         this.$emit(
    //           'selected',
    //           this.skills
    //             .filter((item) => item.checked)
    //             .map((item) => item.name)
    //             .join('|')
    //         )
    //       },
    //       error: (err) => {
    //         console.log('page=>>')
    //         console.log(err)
    //       },
    //     }
    //   )
    // },
    getAllSkills() {
      return new Promise((resolve, reject) => {
        this.$utils.httpGet(
          this.$config.api.RESOURCE_AIHUB_GETSKILLSLIST,
          { pageIndex: 1, pageSize: 1000, experience: 1 },
          {
            success: (res) => {
              let skills = res.data.skills || []
              resolve(skills)
            },
            error: (err) => {
              console.log('page=>>')
              console.log(err)
            },
          }
        )
      })
    },

    onSwitchInput(item) {
      console.log('onSwitchInput', item)
      const hasCheckedLen = this.skills.filter((it) => it.checked).length
      if (hasCheckedLen >= 10 && item.checked === false) {
        return this.$message.warning('最多只能配置体验10个技能')
      }
      if (hasCheckedLen === 1 && item.checked === true) {
        return this.$message.warning('至少配置体验1个技能')
      }
      this.skills = this.skills.map((it) => {
        return {
          ...it,
          checked: it.id === item.id ? !it.checked : it.checked,
        }
      })
      this.$emit(
        'selected',
        this.skills
          .filter((item) => item.checked)
          .map((item) => item.name)
          .join('|')
      )
    },
  },
}
</script>
<style lang="scss" scoped>
@import './selector.scss';
</style>
