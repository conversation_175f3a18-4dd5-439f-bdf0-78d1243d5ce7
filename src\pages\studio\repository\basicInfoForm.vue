<template>
  <div class="basic-info-form">
    <os-collapse size="large" title="基本信息">
      <div v-if="!edit" class="ib basic-info-edit-btn" @click.stop="toEdit">
        <i class="ic-r-edit"/>
        <span>编辑</span>
      </div>
      <el-form :model="form" :rules="rules"
        ref="form" label-width="104px" label-position="left">
        <el-form-item prop="themeName">
          <label slot="label">
            <span class="intention-form-label">关系名称
              <el-tooltip effect="dark">
                <div slot="content">如问：张山的老婆是谁？答：李四。则有三个元素组成：<br/>
                  1、张山（问法关键字）；<br/>2、老婆（关系关键字）；<br/>3、李四（回复语）。</div>
                <i class="el-icon-question"/>
              </el-tooltip>
            </span>
          </label>
          <span v-if="!edit">{{form.themeName}}</span>
          <el-input v-else ref="nameInput" v-model="form.themeName" placeholder="请输入问答关系名称" size="medium"/>
        </el-form-item>
        <el-form-item prop="keyword">
          <label slot="label">
            <span class="intention-form-label">关系关键字
              <el-tooltip effect="dark">
                <div slot="content">是指想要问的问题问法关键字和回复语之间的关系。例如：<br/>张山的老婆是谁，则关系关键字是“老婆“。</div>
                <i class="el-icon-question"/>
              </el-tooltip>
            </span>
          </label>
          <span v-if="!edit">{{form.keyword}}</span>
          <el-input v-else v-model="form.keyword" placeholder="请输入问答关系关键字" size="medium"/>
        </el-form-item>
        <el-form-item prop="newAlias" class="form-item-alias">
          <label slot="label">
            <span class="intention-form-label">关系别名
              <el-tooltip effect="dark">
                <div slot="content">问“老婆”，则别名会有“妻子，夫人，太太,，爱人，女人、<br/>内人...... ”，穷举关键信息。</div>
                <i class="el-icon-question"/>
              </el-tooltip>
            </span>
          </label>
          <template v-if="edit">
            <el-tag
              v-for="(tag, index) in form.aliasArr"
              :key="index"
              :closable="edit"
              :disable-transitions="false"
              @close="delTmpAlias(index)"
            >
              <span :title="tag">{{tag }}</span>
            </el-tag>
            <template v-if="(form.aliasArr && form.aliasArr.length) < aliasLimit">
              <i v-if="!onEditAlias" class="ic-r-plus"
                @click="editAlias" ></i>
              <el-input
                class="basic-info-alias-add"
                ref="aliasInput"
                size="small"
                v-else
                v-model="form.newAlias"
                placeholder="输入问法关键字别名，回车添加"
                @keyup.enter.native="addTmpAlias($event)"
                @blur="addOrEditAlias($event)"
              ></el-input>
            </template>

          </template>
          <p class="alias-static" v-else>{{form.alias || '-'}}</p>
        </el-form-item>
        <el-form-item v-if="edit">
          <el-button type="primary" size="small"
            style="min-width: 80px;"
            @click="editSubmit('form')"
            :loading="editSaveLoading">
            {{ editSaveLoading ? '保存中...' : '保存' }}
          </el-button>
          <el-button size="small"
            style="min-width: 80px;"
            v-on:click="cancel">
            取消
          </el-button>
        </el-form-item>
      </el-form>
    </os-collapse>
  </div>
</template>

<script>
  export default {
    name: 'basic-info-form',
    props: {
      themeInfo: {},
    },
    data() {
      return {
        edit: false,
        form: {
          themeName: '',
          keyword: '',
          newAlias: ''
        },
        rules: {
          'themeName': [this.$rules.required('问答关系名称不能为空'),
            this.$rules.lengthLimit(1, 100, '问答关系名称长度不能超过100个字符'),
            { validator: this.checkEmpty, trigger: 'blur'}],
          'keyword': [this.$rules.required('问答关系关键字不能为空'),
            this.$rules.lengthLimit(1, 20, '问答关系关键字长度不能超过20个字符'),
            this.$rules.repositoryCommonReg(),
            { validator: this.checkEmpty, trigger: 'blur'}],
          'newAlias': [this.$rules.lengthLimit(1, 20, '关系别名长度不能超过20个字符'),
            this.$rules.repositoryCommonReg(),
            { validator: this.checkRepeate, trigger: 'blur'}]
        },
        editSaveLoading: false,
        onEditAlias: false,
        aliasLimit: 200
      }
    },
    watch: {
      'themeInfo.id': function(val) {
        this.formFormat()
      }
    },
    computed: {
      repoId() {
        return this.$route.params.repoId || ''
      }
    },
    created() {
      this.isCreatePage()
      this.formFormat()
    },
    methods: {
      checkEmpty(rule, val, callback){
        val = val ? val.trim() : val
        if(!val.trim()){
          callback(new Error('不能为空'))
        }
        callback()
      },
      checkRepeate(rule, val, callback){
        val = val ? val.trim() : val
        if(this.form && this.form.aliasArr.indexOf(val) != -1 ){
          callback(new Error('不能重复'))
        }
        callback()
      },
      formFormat() {
        this.form = JSON.parse(JSON.stringify(this.themeInfo))
        let tmp = this.form.alias && this.form.alias.split('|') || []
        this.form.aliasArr = tmp.filter(item => item)
      },
      isCreatePage() {
        let self = this
        if(this.$route.name == 'create-qa-relation-page') {
          this.edit = true
          this.$nextTick(function () {
            self.$refs.nameInput && self.$refs.nameInput.focus()
          })
          return
        }
        this.edit = false
      },
      toEdit() {
        let self = this
        this.edit = true
        this.$nextTick(function () {
          self.$refs.nameInput && self.$refs.nameInput.focus()
        })
      },
      editAlias() {
        let self = this
        if(!self.edit) return
        this.onEditAlias = true
        this.$nextTick(function () {
          self.$refs['aliasInput'] && self.$refs['aliasInput'].focus()
        })
      },
      addTmpAlias(e, afterBlur){
        let val = e.target.value.trim()
        if(!val) return
        let self = this
        this.$refs.form.validateField('newAlias', (errMesg) => {
          if(!errMesg) {
            self.form.aliasArr.push(val)
            self.form.newAlias = ''
            self.onEditAlias = afterBlur ? false : true
          }
        })
      },
      delTmpAlias(index) {
        this.form.aliasArr.splice(index, 1)
        this.$forceUpdate()
      },
      addOrEditAlias(e){
        this.addTmpAlias(e, true)
      },
      // 编辑保存基本信息
      editSubmit (formName) {
        let self = this
        self.form.alias = self.form.aliasArr && self.form.aliasArr.join('|') || ''
        self.$refs[formName].validate(valid => {
          if(valid){
            self.editSaveLoading = true
            self.$utils.httpPost(self.$config.api.STUDIO_REPO_REL_CREATE_EDIT, {
              themeName: self.form.themeName,
              repositoryId: self.repoId,
              keyword: self.form.keyword,
              alias: self.form.alias,
              id: self.form.id || ''
            }, {
              success: (res) => {
                if(res.flag) {
                  self.$message.success('保存成功')
                  self.editSaveLoading = false
                  self.edit = false
                  if(!self.form.id) {
                    self.$router.replace({path:`/studio/repo/${self.repoId}/${res.data.id}`})
                  } else {
                    this.$emit('getInfo')
                  }
                } else {
                  self.$message.warning('保存失败')
                }
              },
              error: (err) => {
                self.editSaveLoading = false
                self.edit = false
              }
            })
          }
        })
      },
      cancel() {
        this.edit=!this.edit
        this.onEditAlias = false
        this.$refs.form && this.$refs.form.resetFields()
        this.formFormat()
      }
    }
  }
</script>

<style lang="scss" scoped>
.basic-info-form {
  position: relative;
  .ic-r-plus {
    width: 36px;
    height: 36px;
    text-align: center;
    line-height: 36px;
    color: $grey6;
    cursor: pointer;
    background: $grey4-15;
  }
  .el-tag {
    margin-top: 3px;
    margin-right: 3px;
    background: #f4f5f5;
    border: none;
    color: $semi-black;
  }
}
.basic-info-alias-add {
  width: 220px;
}
.basic-info-edit-btn {
  position: absolute;
  top: 8px;
  left: 120px;
  color: $primary;
  cursor: pointer;
}
.el-icon-question {
  color: $grey3;
}
.alias-static {
  word-break:break-word;
}
</style>
<style lang="scss">
.basic-info-form {
  .el-form-item__content {
    width: 500px;
  }
  .form-item-alias {
    .el-form-item__content {
      width: calc(100% - 110px);
    }
  }
}
</style>

