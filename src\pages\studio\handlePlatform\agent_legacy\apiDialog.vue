<template>
  <el-form
    :model="form"
    label-position="left"
    label-width="80px"
    ref="apiForm"
    :rules="rules"
  >
    <el-form-item label="URL:" prop="url">
      <el-input v-model="form.url" style="width: 500px"></el-input>
    </el-form-item>

    <el-form-item label="Method:" prop="method">
      <el-select v-model="form.method" style="width: 500px">
        <el-option value="POST" label="POST"></el-option>
      </el-select>
    </el-form-item>

    <el-form-item label="type:" prop="type">
      <el-select v-model="form.type" style="width: 500px">
        <el-option value="bearer" label="bearer"></el-option>
      </el-select>
    </el-form-item>

    <el-form-item label="key:" prop="key">
      <div>
        <span>{{ form.key }}</span>
        <el-button type="text" @click="getCopy">复制</el-button>
        <div><el-button @click="refreshKey" plain>重新生成</el-button></div>
      </div>
    </el-form-item>

    <el-form-item>
      <el-button @click="cancel">取消</el-button>
      <el-button type="primary" @click="submit">确定</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
export default {
  name: 'IflyAIuiWebApiDialog',
  props: {
    extraParam: {
      required: false,
      type: Object,
    },
  },

  mounted() {
    this.refreshKey()
  },

  data() {
    return {
      form: {
        url: null,
        method: 'POST',
        type: 'bearer',
        key: null,
      },
      rules: {
        url: [
          { required: true, message: '请填写url', trigger: 'blur' },
          // { validator: this.validateHttpUrl, message: '请填写符合http协议的url', trigger: 'blur' },
          {
            pattern:
              /^(https?:\/\/)?((([a-zA-Z0-9-]+\.)+[a-zA-Z0-9-]+)|(\d{1,3}\.){3}\d{1,3})(:\d+)?(\/[a-zA-Z0-9-_]*)*(\?[a-zA-Z0-9-_&=]*)?$/,
            message: '请输入符合的HTTP协议的URL',
            trigger: 'blur',
          },
        ],
        method: [{ required: true, message: '请选择method', trigger: 'blur' }],
        type: [{ required: true, message: '请选择type', trigger: 'blur' }],
        key: [{ required: true, message: '请填写key', trigger: 'blur' }],
      },
    }
  },

  mounted() {
    if (this.extraParam) {
      this.form = {
        url: this.extraParam.url,
        method: this.extraParam.method,
        type: this.extraParam.type,
        key: this.extraParam.key,
      }
    }
  },

  methods: {
    submit() {
      this.$refs.apiForm.validate((valid) => {
        if (valid) {
          const params = {
            agentId: this.$route.params.agentId,
            extraParam: {
              url: this.form.url,
              method: this.form.method,
              type: this.form.type,
              key: this.form.key,
            },
          }
          this.$utils.httpPost(
            this.$config.api.AGENT_POSTHANDLE_EDIT_OLD,
            JSON.stringify(params),
            {
              config: {
                headers: {
                  'Content-Type': 'application/json;charset=UTF-8',
                },
              },
              success: (res) => {
                this.$message.success('操作成功')
                this.$emit('refresh')
              },
              error: (err) => {},
            }
          )
        }
      })
    },
    getValues(extraParam) {
      this.form.url = extraParam.url
      this.form.method = extraParam.method
      this.form.type = extraParam.type
      this.form.key = extraParam.key
    },
    cancel() {
      // this.$refs.apiForm.resetFields()
      this.getDetail()
      this.$refs.apiForm.clearValidate()
    },

    validateHttpUrl(rule, value, callback) {
      const httpRegex = /^https?:\/\/[a-zA-Z0-9.-]+$/
      if (!value) {
        callback(new Error('请输入URL'))
      } else if (!httpRegex.test(value)) {
        callback(new Error('请输入有效的HTTP URL'))
      } else {
        callback()
      }
    },
    refreshKey() {
      this.form.key = this.generateRandomString()
    },

    getCopy() {
      // 使用现代的剪贴板 API
      navigator.clipboard
        .writeText(this.form.key)
        .then(() => {
          this.$message.success('复制成功')
        })
        .catch((err) => {
          this.$message.error('复制失败')
        })
    },
    generateRandomString() {
      const characters =
        'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()_'
      let result = ''
      const length = 30
      for (let i = 0; i < length; i++) {
        const randomIndex = Math.floor(Math.random() * characters.length)
        result += characters[randomIndex]
      }
      return result
    },

    getDetail() {
      const agentId = this.$route.params.agentId
      this.$utils.httpPost(
        this.$config.api.AGENT_DETAIL_OLD,
        JSON.stringify({ agentId: agentId }),
        {
          config: {
            headers: {
              'Content-Type': 'application/json;charset=UTF-8',
            },
          },
          success: (res) => {
            console.log(res, '详情的res')
            this.agent_info = res.data
            if (res.data.extraParam !== '') {
              const expraRes = JSON.parse(res.data.extraParam)
              this.form.url = expraRes.url
              this.form.method = expraRes.method
              this.form.type = expraRes.type
              this.form.key = expraRes.key
            }
            // this.$refs.ApiForm.getValues(JSON.parse(res.data.extraParam))
          },
          error: (err) => {},
        }
      )
    },
  },
}
</script>

<style lang="scss" scoped></style>
