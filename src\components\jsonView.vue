<template>
  <el-scrollbar style="height: 432px">
    <vue-json-pretty
      :data="data"
      :path="path"
      :deep="deep"
      :showLength="showLength"
    >
    </vue-json-pretty>
  </el-scrollbar>
</template>

<script>
import VueJsonPretty from 'vue-json-pretty'
// require('vue-json-pretty/lib/styles.css')

export default {
  name: 'json-view',
  props: {
    data: {
      type: Object,
      required: true,
    },
    path: {
      type: String, // 定义最顶层数据层级
      default: 'root',
    },
    deep: {
      type: Number, // 数据深度, 大于该深度的数据将不被展开
    },
    showLength: {
      type: Boolean, // 是否在数据线闭合的时候展示长度
    },
  },
  data() {
    return {}
  },
  components: {
    VueJsonPretty,
  },
  methods: {},
  created() {},
}
</script>

<style lang="scss" scoped></style>
