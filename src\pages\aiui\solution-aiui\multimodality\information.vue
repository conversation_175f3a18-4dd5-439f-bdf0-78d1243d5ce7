<template>
  <div class="info-container">
    <div
      class="left-part"
      :style="{
        backgroundImage:
          'url(' +
          require(`../../../../assets/images/solution/offline/${info.imageName}.png`) +
          ')',
      }"
    ></div>
    <div class="right-part">
      <p class="title">{{ info.title }}</p>
      <div class="desc" v-html="info.desc"></div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    info: {
      required: true,
      type: Object,
      default: {},
    },
    label: {
      required: true,
      type: String,
      default: '',
    },
  },
}
</script>
<style lang="scss" scoped>
.info-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 440px;
  .left-part {
    width: 537px;
    height: 294px;
    background-repeat: no-repeat;
    background-size: contain;
    background-position: center;
  }
  .right-part {
    width: 380px;
    margin-left: 193px;
    .title {
      font-size: 34px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #333333;
      line-height: 30px;
      text-align: left;
    }
    .desc {
      margin-top: 43px;
      font-size: 16px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #999999;
      line-height: 30px;
      text-align: left;
    }
  }
}
</style>
