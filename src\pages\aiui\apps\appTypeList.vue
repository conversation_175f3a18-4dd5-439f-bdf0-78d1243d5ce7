<template>
  <el-popover
    placement="bottom-start"
    v-model="popoverShow"
    :visible-arrow="false"
    trigger="click"
  >
    <div class="os-scroll app-type-wrap" :style="{ width: bottomContentWidth }">
      <ul class="top-tab">
        <li
          class="tab"
          :class="{ active: tabIndex === 0 }"
          @click.stop="tabIndex = 0"
        >
          应用
        </li>
        <li
          class="tab"
          :class="{ active: tabIndex === 1 }"
          @click.stop="tabIndex = 1"
        >
          游戏
        </li>
        <li
          class="tab"
          :class="{ active: tabIndex === 2 }"
          @click.stop="tabIndex = 2"
        >
          智能硬件
        </li>
      </ul>
      <el-scrollbar style="height: 263px" v-show="tabIndex === 0">
        <ul class="bottom-content">
          <li
            class="type-wrap"
            v-for="(aditem, adkey) in list.admap"
            :key="aditem"
          >
            <div class="type-title">{{ aditem }}：</div>
            <ul class="type-list">
              <li
                :class="['type-item', { active: appType == axkey }]"
                v-for="(axitem, axkey) in list.axmap"
                :key="axitem"
                v-if="adkey.substring(0, 4) == axkey.substring(0, 4)"
                @click="getType(`应用 - ${aditem} - ${axitem}`, axkey)"
              >
                {{ axitem }}
              </li>
            </ul>
          </li>
        </ul>
      </el-scrollbar>
      <ul class="bottom-content game" v-show="tabIndex === 1">
        <template v-for="(gditem, gdkey, index) in list.gdmap">
          <li
            :class="['type-item no-small-type', { active: appType == gdkey }]"
            @click="getType(`游戏 - ${gditem}`, gdkey)"
            :key="gditem"
          >
            {{ gditem }}
          </li>
          <!-- <li class="line-for-game-type" v-if="(index + 1) % 7 == 0" :key="index"></li> -->
        </template>
      </ul>
      <el-scrollbar style="height: 263px" v-show="tabIndex === 2">
        <ul class="bottom-content" v-show="tabIndex === 2">
          <li
            class="type-wrap"
            v-for="(wditem, wdkey) in list.wdmap"
            :key="wditem"
            v-if="
              wdkey.substring(2, 4) == '01' ||
              wdkey.substring(2, 4) == '02' ||
              wdkey.substring(2, 4) == '03'
            "
          >
            <div class="type-title">{{ wditem }}：</div>
            <ul class="type-list">
              <li
                :class="['type-item', { active: appType == wxkey }]"
                v-for="(wxitem, wxkey) in list.wxmap"
                :key="wxitem"
                v-if="wdkey.substring(0, 4) == wxkey.substring(0, 4)"
                @click="getType(`智能硬件 - ${wditem} - ${wxitem}`, wxkey)"
              >
                {{ wxitem }}
              </li>
            </ul>
          </li>
          <ul class="single-item-wrap">
            <li
              class="single-item"
              style="margin: 10px 0"
              v-for="(wditem, wdkey) in list.wdmap"
              :key="wditem"
              v-if="
                wdkey.substring(0, 4) !== '0301' &&
                wdkey.substring(0, 4) !== '0302' &&
                wdkey.substring(0, 4) !== '0303'
              "
              @click="getType(`智能硬件 - ${wditem}`, wdkey)"
            >
              <div :class="['type-item', { active: appType == wdkey }]">
                {{ wditem }}
              </div>
            </li>
          </ul>
        </ul>
      </el-scrollbar>
    </div>
    <el-input
      slot="reference"
      class="type-input"
      ref="input"
      v-model="appTypeName"
      readonly
      suffix-icon="ic-r-select"
      placeholder="请选择分类"
    ></el-input>
  </el-popover>
</template>
<script>
export default {
  name: 'app-type-list',
  data() {
    return {
      list: {},
      tabIndex: 0,
      appType: '',
      appTypeName: '',
      popoverShow: false,
      bottomContentWidth: '640px',
    }
  },
  props: {
    initAppTypeName: {
      type: String,
      default: '',
    },
  },
  computed: {
    appInfo() {
      return this.$store.state.aiuiApp.app
    },
  },
  watch: {
    appInfo(val) {
      if (val) {
        this.appType = val.appType
        this.appTypeName = val.appTypeName
      }
    },
    popoverShow(val) {
      this.$emit('setTypeListStatu', val)
    },

    initAppTypeName(val) {
      if (val) {
        this.getInitTabIndex(val)
      }
    },
  },
  created() {
    if (this.$store.state.aiuiApp.id && this.$route.name !== 'apps-add') {
      this.appType = this.$store.state.aiuiApp.app.appType
      this.appTypeName = this.$store.state.aiuiApp.app.appTypeName
    }
    this.getlist()
  },
  methods: {
    getInitTabIndex(initAppTypeName) {
      let typeName = initAppTypeName.split('-')[0]
      if (typeName) {
        if (typeName === '游戏') {
          this.tabIndex = 1
        } else if (typeName === '智能硬件') {
          this.tabIndex = 2
        } else {
          this.tabIndex = 0
        }
      }
    },
    getlist() {
      this.$utils.httpGet(
        this.$config.api.AIUI_APP_TYPE_List,
        {},
        {
          success: (res) => {
            this.list = res.data
          },
        }
      )
    },
    getType(appTypeName, appType) {
      this.appTypeName = appTypeName
      this.appType = appType
      this.popoverShow = false
      this.$emit('setType', this.appType, this.appTypeName)
    },
  },
  mounted() {
    this.bottomContentWidth = `${
      this.$refs.input.$el.querySelector('input').clientWidth + 2
    }px`
  },
}
</script>
<style lang="scss" scoped>
.app-type-wrap {
  padding: 4px 0 2px;
  max-height: 310px;
  overflow: hidden;
  box-sizing: border-box;
  // border: 1px solid #dcdfe6;
  border-radius: 1px;
  // box-shadow: 0 0 2px 0 rgba(0, 0, 0, 0.04), 0 0 4px 0 rgba(0, 0, 0, 0.04),
  //   0 0 8px 0 rgba(0, 0, 0, 0.04), 0 0 16px 0 rgba(0, 0, 0, 0.04);
}
.top-tab {
  display: flex;
}
.tab {
  flex: 1;
  text-align: center;
  color: $grey6;
  background: #f5f5f6; //无色值名，临时用
  width: 212px;
  height: 39px;
  line-height: 39px;
  margin: 0 1px;
  cursor: pointer;
  &.active {
    margin: 0;
    background: $white;
    font-weight: 600;
  }
}
//底部的应用类型
.bottom-content {
  display: flex;
  // width: 640px;
  flex-wrap: wrap;
  justify-content: space-around;
}
.type-wrap {
  padding: 10px 0 10px 31px;
  width: 100%;
  border-bottom: 1px dashed $grey2;
  display: flex;
}
.type-title {
  display: inline-block;
  flex: 0 0 70px;
  vertical-align: top;
  margin-top: 4px;
  margin-right: 32px;
  width: 70px;
  line-height: 27px;
  text-align: right;
  color: $grey6;
  font-weight: 600;
  cursor: default;
}
.type-list {
  display: inline-block;
}
.type-item {
  display: inline-block;
  margin: 4px 0;
  width: 88px;
  height: 27px;
  line-height: 27px;
  text-align: center;
  cursor: pointer;
  &:hover,
  &.active {
    color: $white;
    background: $primary;
  }
}
.game {
  padding: 10px 0;
  height: 263px;
  justify-content: flex-start;
  align-content: flex-start;
}
.type-item.no-small-type {
  flex: 0 0 88px;
  margin: 14px 0;
}
.line-for-game-type {
  display: block;
  width: 100%;
  border-bottom: 1px dashed $grey2;
}
.single-item-wrap {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  padding-left: 15px;
}
</style>
<style lang="scss">
.el-popper {
  padding: 0;
  border: none;
  border-radius: 0;
}
.el-popper[x-placement^='top'] {
  margin-bottom: 8px;
}
</style>
