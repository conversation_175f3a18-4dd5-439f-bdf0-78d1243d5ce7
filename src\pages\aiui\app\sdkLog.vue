<template>
  <div>
    <!-- <p class="sdk-title">{{ this.platform }} SDK</p> -->
    <ul class="sdk-log-wrap">
      <li v-for="(item, index) in sdkLog" :key="index">
        <p class="log-title">
          <span>版本：{{ item.sdkVersion }}</span
          ><span>{{ item.registrationDate }}</span
          ><a
            :href="item.href1"
            style="margin-right: 40px; font-weight: normal"
            target="_blank"
            >个人信息处理规则</a
          ><a :href="item.href2" style="font-weight: normal" target="_blank"
            >合规使用说明</a
          >
        </p>
        <pre class="log-content" v-html="item.note"></pre>
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  name: 'sdk-log',
  props: {
    platform: String,
  },
  data() {
    return {
      sdkLog: [],
    }
  },
  created() {
    this.getSdkLog()
  },
  methods: {
    getSdkLog() {
      if (!this.platform) return
      let self = this
      this.$utils.httpGet(
        this.$config.api.AIUI_APP_SDKVERSION,
        {
          platform: this.platform,
          appid: this.$route.params.appId,
        },
        {
          success: (res) => {
            if (res.flag) {
              // self.sdkLog = res.data
              self.sdkLog = (res.data || []).map((item) => {
                return {
                  ...item,
                  href1: 'https://aiui-doc.xf-yun.com/project-1/doc-191/',
                  href2: 'https://aiui-doc.xf-yun.com/project-1/doc-192/',
                }
              })
            } else {
              this.$message.error(res.desc)
            }
          },
        }
      )
    },
  },
}
</script>

<style lang="scss" scoped>
.sdk-title {
  font-size: 16px;
  font-weight: 600;
  line-height: 22px;
}
.sdk-log-wrap {
  max-height: 500px;
  overflow-y: auto;
  li {
    margin-bottom: 40px;
  }
}
.log-title {
  font-size: 16px;
  font-weight: 600;
  color: $semi-black;
  margin-bottom: 12px;

  span {
    font-weight: 600;
    margin-right: 40px;
  }
}
.log-content {
  color: $grey4;
  white-space: pre-wrap;
}
</style>
