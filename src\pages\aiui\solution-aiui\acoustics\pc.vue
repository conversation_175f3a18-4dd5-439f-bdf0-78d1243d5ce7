<template>
  <div class="main-content">
    <section
      class="main-content-banner"
      :style="{
        backgroundImage:
          'url(' +
          require(`@A/images/solution/acoustics/acoustics-back.png`) +
          ')',
      }"
    >
      <div class="banner-text">
        <h2>前端声学解决方案</h2>
        <p class="banner-text-content">为复杂环境下的语音交互保驾护航</p>
        <div class="banner-text-button" @click="toConsole">合作咨询</div>
      </div>
    </section>

    <section class="section section-1">
      <div class="section-title">我们明白您的困扰</div>
      <ul class="product-list">
        <li
          v-for="(item, index) in productList"
          :key="index"
          :class="item.klass"
        >
          <div class="desc-wrap">
            <p v-html="item.desc"></p>
          </div>
        </li>
      </ul>
    </section>
    <section class="section section-7">
      <div class="section-title">我们将全流程参与您的产品开发</div>
      <div class="section-desc" style="text-align: center">
        专业的声学硬件结构指导，为语音交互效果保驾护航
      </div>

      <div class="flow-container">
        <div class="flow-graph">
          <div class="tip tip1">需求确认</div>
          <div class="tip tip2">声学评估</div>
          <div class="tip tip3">结构设计指导</div>
          <div class="tip tip4">声学集成</div>
          <div class="tip tip5">产线测试</div>
          <div class="tip tip6">需求验收</div>
        </div>
      </div>
    </section>
    <section class="section section-8">
      <div class="section-title">可保障高噪场景的交互效果</div>
      <div class="section-desc" style="text-align: center; color: #fff">
        核心技术打磨，不断克服不可用的场景难题
      </div>

      <div class="effect-container">
        <div class="effect-rect rect1">
          <div class="effect-cell">
            <p>多模语音增强</p>
            <p>高噪人声场景识别准确率可达95%</p>
            <i class="icon-left icon-1"></i>
          </div>
          <div class="effect-cell">
            <p>高回声消除</p>
            <p>回声消除可达40db</p>
            <i class="icon-right icon-2"></i>
          </div>
        </div>
        <div class="effect-rect rect2">
          <div class="effect-cell">
            <p>混响长时抑制</p>
            <p>抑制时间可达0.6s</p>
            <i class="icon-left icon-3"></i>
          </div>
          <div class="effect-cell">
            <p>超指向波束</p>
            <p>波束范围可达±15°</p>
            <i class="icon-right icon-4"></i>
          </div>
        </div>
      </div>
    </section>
    <section class="section section-9">
      <div class="section-title">支持多种麦克风构型</div>
      <div class="section-desc" style="text-align: center">
        常见的麦克风构型可快速适配
      </div>
      <div class="scene-container">
        <ul>
          <li>
            <p>单麦</p>
            <div class="scene-mask mask-a" v-show="showMaskA">
              <div class="scene-introduction">
                <div class="scene-title">单麦</div>
                <div class="scene-divider"></div>
                <p class="scene-text">
                  拾音距离：2m <br />
                  声源定位：0° <br />
                  噪音抑制：-10db <br />
                  回声消除：-20db
                </p>
              </div>
            </div>
          </li>
          <li @mouseenter="handleMask" @mouseleave="handleMaskVisible">
            <p>双麦</p>
            <div class="scene-mask mask-b">
              <div class="scene-introduction">
                <div class="scene-title">双麦</div>
                <div class="scene-divider"></div>
                <p class="scene-text">
                  拾音距离：3m<br />
                  声源定位：180°±30ﾟ<br />
                  噪音抑制：-15db<br />
                  回声消除：-30db
                </p>
              </div>
            </div>
          </li>
          <li @mouseenter="handleMask" @mouseleave="handleMaskVisible">
            <p>线性四麦</p>
            <div class="scene-mask mask-c">
              <div class="scene-introduction">
                <div class="scene-title">线性四麦</div>
                <div class="scene-divider"></div>
                <p class="scene-text">
                  拾音距离：5m<br />
                  声源定位：180°±10ﾟ<br />
                  噪音抑制：-15db<br />
                  回声消除：-30db
                </p>
              </div>
            </div>
          </li>
          <li @mouseenter="handleMask" @mouseleave="handleMaskVisible">
            <p>环形四麦</p>
            <div class="scene-mask mask-d">
              <div class="scene-introduction">
                <div class="scene-title">环形四麦</div>
                <div class="scene-divider"></div>
                <p class="scene-text">
                  拾音距离：5m<br />
                  声源定位：360°±20ﾟ<br />
                  噪音抑制：-20db<br />
                  回声消除：-30db
                </p>
              </div>
            </div>
          </li>
          <li></li>
          <li @mouseenter="handleMask" @mouseleave="handleMaskVisible">
            <p>线性六麦</p>
            <div class="scene-mask mask-f">
              <div class="scene-introduction">
                <div class="scene-title">线性六麦</div>
                <div class="scene-divider"></div>
                <p class="scene-text">
                  拾音距离：5m及以上<br />
                  声源定位：360°±10ﾟ<br />
                  噪音抑制：-25db<br />
                  回声消除：-40db
                </p>
              </div>
            </div>
          </li>
          <li @mouseenter="handleMask" @mouseleave="handleMaskVisible">
            <p>环形六麦</p>
            <div class="scene-mask mask-g">
              <div class="scene-introduction">
                <div class="scene-title">环形六麦</div>
                <div class="scene-divider"></div>
                <p class="scene-text">
                  拾音距离：5m及以上<br />
                  声源定位：360°±20ﾟ<br />
                  噪音抑制：-20db<br />
                  回声消除：-30db
                </p>
              </div>
            </div>
          </li>
          <li @mouseenter="handleMask" @mouseleave="handleMaskVisible">
            <p>线性八麦</p>
            <div class="scene-mask mask-h">
              <div class="scene-introduction">
                <div class="scene-title">线性八麦</div>
                <div class="scene-divider"></div>
                <p class="scene-text">
                  拾音距离：5m及以上<br />
                  声源定位：360°±10ﾟ<br />
                  噪音抑制：-30db<br />
                  回声消除：-40db
                </p>
              </div>
            </div>
          </li>
        </ul>
      </div>
    </section>
    <section class="section section-10">
      <div class="section-title">支持软核及硬件集成</div>
      <div class="section-desc" style="text-align: center">
        软硬件一体助您快速接入、验证
      </div>
      <div class="integration-container">
        <ul>
          <li>
            <div class="left-part left-usb"></div>
            <div class="right-part">
              <h3>USB声卡</h3>
              <ul>
                <li>USB口输出原始音频</li>
                <li>支持两路回采</li>
                <li>支持模拟硅麦及驻极体麦</li>
              </ul>
              <div class="detail" @click="jumpDetail('usb')">
                <a>了解详情</a>
                <a><i class="arrow-go"></i></a>
              </div>
            </div>
          </li>
          <li>
            <div class="left-part left-aiui"></div>
            <div class="right-part">
              <h3>AIUI降噪板</h3>
              <ul>
                <li>内置降噪、唤醒、声纹算法</li>
                <li>USB口/3.5mm耳机口输出降噪音频</li>
                <li>支持双声道立体声输出</li>
              </ul>
              <div class="detail" @click="jumpDetail('aiui')">
                <a>了解详情</a>
                <a><i class="arrow-go"></i></a>
              </div>
            </div>
          </li>
        </ul>
      </div>
    </section>
    <section class="section section-11">
      <div class="section-title">合作案例</div>
      <div class="section-tabs">
        <ul>
          <li :class="{ active: activeName == '0' }" @click="toggleActive('0')">
            机器人
          </li>
          <li :class="{ active: activeName == '1' }" @click="toggleActive('1')">
            电视
          </li>
          <!-- <li :class="{ active: activeName == '2' }" @click="toggleActive('2')">
            投影
          </li> -->
          <li :class="{ active: activeName == '2' }" @click="toggleActive('2')">
            智能音箱
          </li>
        </ul>
        <div class="bottom-line"></div>
      </div>
      <div class="section-swiper" v-swiper:swiper="swiperOption">
        <div class="swiper-wrapper">
          <div class="swiper-slide" key="0">
            <div class="case-container tv">
              <div class="tv-box">
                <div class="tv-left">
                  <h1>机器人</h1>
                  <p>
                    采用环形六麦方案，支持机器人在公共高噪复杂场景下的窄波束拾音需求。最大拾音距离可达5m，拾音角度最窄可收±15°，多模态语音增强方案可保证信噪比-5db人声干扰情况下唤醒率98%（人脸唤醒）、识别字准率97%。
                  </p>
                </div>
                <div class="tv-right">
                  <div class="robot-image"></div>
                </div>
              </div>
            </div>
          </div>
          <div class="swiper-slide" key="1">
            <div class="case-container tv">
              <div class="tv-box">
                <div class="tv-left">
                  <h1>电视</h1>
                  <p>
                    采用标准的线性四麦方案，满足智能电视远场语音交互的收音需求。最大拾音距离可达5m，定制家庭场景多点噪声降噪模型，信噪比0db情况下唤醒率98%、识别字准率97%。
                  </p>
                </div>
                <div class="tv-right">
                  <div class="tv-image"></div>
                </div>
              </div>
            </div>
          </div>
          <div class="swiper-slide" key="2">
            <div class="case-container tv">
              <div class="tv-box">
                <div class="tv-left">
                  <h1>智能音箱</h1>
                  <p>
                    采用标准的双麦方案，性价比高。最大拾音距离可达3m，支持通话降噪，信噪比0db情况下唤醒率90%、识别字准率95%。
                  </p>
                </div>
                <div class="tv-right">
                  <div class="box-image"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    <!-- <corp @jump="toConsole">
      <template> 提交信息，我们会尽快与您联系</template>
    </corp> -->
    <div class="corp">
      <div class="section-wrap">
        <div class="section-title">
          <p class="section-title-contact">立即联系您的专属顾问</p>
          <p class="section-desc2">免费咨询专属顾问 为您量身定制产品推荐方案</p>
        </div>

        <div class="section-item" style="padding-top: 39px; text-align: left">
          <div class="section-button" @click="toConsole">合作咨询</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import banner from '@P/aiui/solution-aiui/components/banner.vue'
import corp from '@P/aiui/solution-aiui/components/corp.vue'
import '../../../../../static/vue-awesome-swiper'

export default {
  layout: 'aiuiHome',
  data() {
    return {
      productList: [
        {
          desc: '不知道怎样的<br />硬件构型适合收音',
          klass: 'img_domestic_cellphone',
        },
        {
          desc: '鸡尾酒场景下<br />前端声学效果受限',
          klass: 'img_smartwatch',
        },
        {
          desc: '纯软核算法<br />接入适配困难',
          klass: 'img_instrument_smar_bracelet',
        },
      ],

      access: [
        {
          title: '纯软接入、无需改造硬件',
          title2:
            '提供SDK,可以直接端集成；说法量身定制，端<br />上适配即可使用',
          img: require('@A/images/solution/wakeup/img_pure_soft.png'),
        },
        {
          title: '麦克风阵列+声学算法',
          title2:
            '针对没有录音装置的设备；提供2/4/6麦USB<br />语音模组和CAE算法；帮你解决硬件收音问题',
          img: require('@A/images/solution/wakeup/img_microphone_array.png'),
        },
      ],

      showMaskA: true,
      activeName: '0',
      swiperOption: {
        loop: true,
        autoplay: {
          delay: 4000,
          // disableOnInteraction: false,
        },
      },
    }
  },
  mounted() {
    this.swiper.on('slideChange', () => {
      this.activeName = this.swiper.realIndex + ''
    })
  },
  methods: {
    toConsole() {
      // 判断是否有百度SEM营销参数，有的话，提交表单页需要带上
      const search = window.location.search
      if (search) {
        window.open(`/solution/apply/25${search}`)
      } else {
        window.open('/solution/apply/25')
      }
    },
    handleMask() {
      this.showMaskA = false
    },
    handleMaskVisible() {
      // this.showMaskA = true
    },
    toggleActive(val) {
      this.swiper.slideToLoop(Number(val))
    },
    jumpDetail(type) {
      let url = ''
      if (type === 'usb') {
        url = '/solution/soft-hardware?ch=aiui&way=menu#product_page1'
      }
      if (type === 'aiui') {
        url = '/solution/soft-hardware?ch=aiui&way=menu#product_page2'
      }
      window.open(url, '_blank')
    },
  },
  components: {
    banner,
    corp,
  },
}
</script>

<style lang="scss" scoped>
.main-content {
  .section {
    p,
    ul {
      margin-bottom: 0;
    }
    max-width: 1200px;
    overflow: hidden;
    margin: 0 auto;
    .section-title {
      text-align: center;
      font-size: 32px;
      font-family: SourceHanSansSC-Medium, SourceHanSansSC;
      font-weight: 600;
      color: #262626;
      line-height: 34px;
      position: relative;
      width: 600px;
      margin: 0 auto;
      .arrow {
        width: 160px;
        height: 8px;
        display: inline-block;
        background-repeat: no-repeat;
        background-size: contain;
        position: absolute;
        z-index: 1;
      }
      .arrow-left {
        background-position: left;
        background-image: url(~@A/images/solution/offline/img_title_01.png);
        top: 50%;
        left: -160px;
      }
      .arrow-right {
        background-position: right;
        background-image: url(~@A/images/solution/offline/img_title_02.png);
        top: 50%;
        right: -160px;
      }
      .arrow-left1 {
        background-position: left;
        background-image: url(~@A/images/solution/wakeup/img_access_title_01.png);
        top: 50%;
        left: -160px;
      }
      .arrow-right1 {
        background-position: right;
        background-image: url(~@A/images/solution/wakeup/img_access_title_02.png);
        top: 50%;
        right: -160px;
      }
    }
    .section-desc {
      text-align: left;
      margin-top: 40px;
      font-size: 16px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #999999;
    }
    .section-title-bold {
      font-size: 36px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #000;
    }
    .section-title-spec {
      font-size: 30px;
      font-weight: 500;
      color: #656565;
      line-height: 42px;
    }

    .section-sub-title {
      text-align: center;
      font-size: 18px;
      font-weight: 400;
      color: #4b4c4d;
      line-height: 25px;
      margin-top: 42px;
    }
    .section-sub-title-spec {
      font-size: 17px;
      font-weight: 400;
      color: #999999;
      line-height: 28px;
      margin-top: 18px;
    }

    .product-list {
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 50px auto 0;

      li {
        position: relative;
        text-align: center;
        width: 385px;
        height: 232px;

        .desc-wrap {
          padding-top: 39px;
        }

        p {
          // display: none;
          margin-top: 54px;
          width: 232px;
          font-size: 16px;
          font-weight: 400;
          color: #262626;
          line-height: 32px;
          padding-left: 35px;
          text-align: left;
        }

        &.img_domestic_cellphone {
          background: url(~@A/images/solution/acoustics/<EMAIL>) center/100%
            no-repeat;
        }
        &.img_smartwatch {
          background: url(~@A/images/solution/acoustics/<EMAIL>) center/100%
            no-repeat;
        }
        &.img_instrument_smar_bracelet {
          background: url(~@A/images/solution/acoustics/<EMAIL>) center/100%
            no-repeat;
        }
      }

      li + li {
        margin-left: 16px;
      }
    }
  }

  .section-1 {
    margin-top: 110px;
  }
  .section-2 {
    .section-title-2 {
      font-size: 18px;
      color: #8c8c8c;
      text-align: center;
      margin-top: 15px;
    }
    .section-item {
      > ul {
        margin-bottom: 80px;
        li {
          width: 150px;
          img {
            width: 100%;
          }
          .app-text {
            position: static;
            text-align: center;
            font-size: 18px;
            color: #202020;
            margin-top: 15px;
            transform: none;
          }
        }
      }
    }
  }
  .section-3 {
    max-width: unset;
    padding: 110px 0 110px 0;
    > div {
      margin: 0 auto;
    }

    p {
      margin-bottom: 0;
    }
    .advantage {
      margin-top: 84px;
      > li {
        > div {
          max-width: 1200px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin: 0 auto;
        }
      }
      > li:not(:first-child) {
        margin-top: 40px;
      }

      > li:nth-child(1) {
        .advantage-image {
          width: 580px;
          height: 326px;
          background: url(~@A/images/solution/three-wakeup/img_low_power_dissipation.png)
            center/100% no-repeat;
        }
        .advantage-text {
        }
      }
      > li:nth-child(2) {
        background: #f4f7f9;
        padding: 40px 0;
        .advantage-image {
          width: 580px;
          height: 326px;
          background: url(~@A/images/solution/three-wakeup/img_wake_effect.png)
            center/100% no-repeat;
        }
        .advantage-text {
        }
      }
      > li:nth-child(3) {
        .advantage-image {
          width: 580px;
          height: 326px;
          background: url(~@A/images/solution/three-wakeup/img_autobiography.png)
            center/100% no-repeat;
        }
        .advantage-text {
        }
      }
    }
    .advantage-text {
      width: 411px;
      p {
        font-size: 34px;
        font-weight: 400;
        color: #666;
        line-height: 34px;
      }

      ul {
        margin-top: 45px;
        li {
          font-size: 16px;
          font-weight: 400;
          color: #999999;
          line-height: 30px;
          white-space: nowrap;
        }
      }
    }
  }

  .section-7 {
    width: 100%;
    max-width: 100%;
    margin-top: 100px;
    padding-top: 80px;
    // background: #262626;
    background: url(~@A/images/solution/acoustics/<EMAIL>) center/cover
      no-repeat;
    height: 560px;
    .section-title {
      color: #fff;
    }
    .flow-graph {
      width: 1200px;
      height: 80px;
      background: url(~@A/images/solution/acoustics/flow.png) center/contain
        no-repeat;
      margin: 128px auto 0;
      position: relative;
      .tip {
        position: absolute;
        z-index: 1;
        top: 85px;
        color: #000;
        font-size: 18px;
        font-weight: 500;
      }
      .tip1 {
        left: 26px;
      }
      .tip2 {
        left: 243px;
      }
      .tip3 {
        left: 450px;
      }
      .tip4 {
        left: 682px;
      }
      .tip5 {
        left: 890px;
      }
      .tip6 {
        right: 36px;
      }
    }
  }

  .section-8 {
    width: 100%;
    max-width: 100%;
    padding-top: 80px;
    background: url(~@A/images/solution/acoustics/effect-back.png) center/cover
      no-repeat;
    height: 783px;
    .section-title {
      color: #fff;
    }
    .effect-container {
      width: 1200px;
      height: 504px;
      margin: 0 auto;
      margin-top: 70px;
      position: relative;
      background: url(~@A/images/solution/acoustics/circle.png) center/contain
        no-repeat;

      .effect-rect {
        position: relative;
        margin: 0 auto;
        width: 1200px;
        height: 180px;
        background: transparent;
        padding: 94px 78px 0;

        display: flex;
        justify-content: space-between;
      }
      .effect-cell {
        p:first-of-type {
          font-size: 22px;
          font-weight: 600;
          color: #262626;
        }
        p:nth-of-type(2) {
          font-size: 16px;
          font-weight: 400;
          color: #666666;
          margin-top: 18px;
        }
        .icon-left {
          display: inline-block;
          z-index: 1;
          position: absolute;
          left: 29px;
          top: 92px;
        }
        .icon-right {
          display: inline-block;
          z-index: 1;
          position: absolute;
          right: 29px;
          top: 92px;
        }
        .icon-1 {
          width: 34px;
          height: 35px;
          background: url(~@A/images/solution/acoustics/<EMAIL>)
            center/contain no-repeat;
        }
        .icon-2 {
          width: 35px;
          height: 35px;
          background: url(~@A/images/solution/acoustics/<EMAIL>)
            center/contain no-repeat;
        }
        .icon-3 {
          width: 34px;
          height: 34px;
          background: url(~@A/images/solution/acoustics/<EMAIL>)
            center/contain no-repeat;
        }
        .icon-4 {
          width: 34px;
          height: 35px;
          background: url(~@A/images/solution/acoustics/<EMAIL>)
            center/contain no-repeat;
        }
      }
      .effect-rect + .effect-rect {
        margin-top: 76px;
      }
    }
  }

  .section-9 {
    width: 100%;
    max-width: 100%;
    background: url(~@A/images/solution/acoustics/bg.png) center/cover no-repeat;
    height: 602px;
    padding-top: 80px;
    .scene-container {
      > ul {
        width: 1200px;
        margin: 70px auto 0;

        display: grid;
        grid-template-columns: repeat(
          4,
          1fr
        ); /* 将容器分为四列，每列宽度平均 */
        gap: 20px; /* 设置格子之间的间距为20px */

        > li {
          width: 286px;
          /* 设置长方形元素的样式 */
          width: 100%; /* 或者其他具体宽度值，根据需要调整 */
          height: 135px; /* 或者其他具体高度值，根据需要调整 */
          position: relative;

          > p {
            font-size: 18px;
            font-weight: 600;
            color: #262626;
            padding-left: 34px;
            padding-top: 60px;
          }

          .scene-mask {
            position: absolute;
            z-index: 1;
            top: 0;
            width: 100%;
            height: 327px;
            display: none;
            padding: 98px 0 0 36px;

            .scene-title {
              font-size: 18px;
              font-weight: 600;
              color: #fff;
            }
            .scene-divider {
              width: 14px;
              height: 2px;
              background: #82feff;
              margin: 20px 0;
            }
            .scene-text {
              font-size: 14px;
              font-weight: 400;
              text-align: left;
              color: #fff;
              line-height: 24px;
            }
          }

          &:hover {
            .scene-mask {
              display: block !important;
            }
          }

          .mask-a {
            display: block;
            left: 0;
            top: -37px;
            background: url(~@A/images/solution/acoustics/rabit-hover.png)
              center/cover no-repeat;
          }
          .mask-b {
            top: -37px;
            background: url(~@A/images/solution/acoustics/clock-hover.png)
              center/cover no-repeat;
          }
          .mask-c {
            top: -37px;
            background: url(~@A/images/solution/acoustics/tv-hover.png)
              center/cover no-repeat;
          }
          .mask-d {
            top: -37px;
            background: url(~@A/images/solution/acoustics/movie-hover.png)
              center/cover no-repeat;
          }
          .mask-f {
            top: unset;
            bottom: 0;
            background: url(~@A/images/solution/acoustics/pad-hover.png)
              center/cover no-repeat;
          }
          .mask-g {
            top: unset;
            bottom: 0;
            background: url(~@A/images/solution/acoustics/robot-hover.png)
              center/cover no-repeat;
          }
          .mask-h {
            top: unset;
            bottom: 0;
            background: url(~@A/images/solution/acoustics/ticket-hover.png)
              center/cover no-repeat;
          }
        }

        li:nth-child(1) {
          background-color: #fff; /* 或者其他背景颜色，根据需要调整 */
          background: url(~@A/images/solution/acoustics/rabit.png) center/cover
            no-repeat;
        }
        li:nth-child(2) {
          background-color: #fff; /* 或者其他背景颜色，根据需要调整 */
          background: url(~@A/images/solution/acoustics/clock.png) center/cover
            no-repeat;
        }
        li:nth-child(3) {
          background-color: #fff; /* 或者其他背景颜色，根据需要调整 */
          background: url(~@A/images/solution/acoustics/tv.png) center/cover
            no-repeat;
        }
        li:nth-child(4) {
          background-color: #fff; /* 或者其他背景颜色，根据需要调整 */
          background: url(~@A/images/solution/acoustics/movie.png) center/cover
            no-repeat;
        }
        li:not(:nth-child(5)) {
          background-color: #fff; /* 或者其他背景颜色，根据需要调整 */
        }
        li:nth-child(6) {
          background-color: #fff; /* 或者其他背景颜色，根据需要调整 */
          background: url(~@A/images/solution/acoustics/pad.png) center/cover
            no-repeat;
        }
        li:nth-child(7) {
          background-color: #fff; /* 或者其他背景颜色，根据需要调整 */
          background: url(~@A/images/solution/acoustics/robot.png) center/cover
            no-repeat;
        }
        li:nth-child(8) {
          background-color: #fff; /* 或者其他背景颜色，根据需要调整 */
          background: url(~@A/images/solution/acoustics/ticket.png) center/cover
            no-repeat;
        }
      }
    }
  }

  .section-10 {
    width: 100%;
    max-width: 100%;
    height: 602px;
    padding-top: 80px;
    .integration-container {
      > ul {
        width: 1200px;
        margin: 50px auto 0;

        display: grid;
        grid-template-columns: repeat(
          2,
          1fr
        ); /* 将容器分为四列，每列宽度平均 */
        gap: 20px; /* 设置格子之间的间距为20px */

        > li {
          /* 设置长方形元素的样式 */
          width: 100%; /* 或者其他具体宽度值，根据需要调整 */
          height: 320px; /* 或者其他具体高度值，根据需要调整 */
          position: relative;
          display: flex;
          align-items: center;
          border: 1px solid #f1f1f1;
          box-shadow: -4px 0px 14px 4px rgba(188, 198, 216, 0.3);
          &:hover {
            border: 1px solid #2373ff;
          }
          &:first-child {
            padding-left: 69px;
          }
          &:last-child {
            padding-left: 30px;
          }
        }
      }

      .right-part {
        h3 {
          font-size: 18px;
          font-weight: 600;
          color: #262626;
        }
        ul {
          margin-top: 30px;
          li {
            font-size: 14px;
            font-weight: 400;
            color: #666666;
            line-height: 33px;
            position: relative;
            &::before {
              position: absolute;
              content: ' ';
              z-index: 1;
              width: 4px;
              height: 4px;
              border-radius: 100%;
              background: #666;
              top: 14px;
              left: -16px;
            }
          }
        }
        .detail {
          display: flex;
          align-items: center;
          margin-top: 30px;
        }

        .arrow-go {
          display: inline-block;
          margin-left: 23px;
          width: 25px;
          height: 15px;
          background: url(~@A/images/solution/acoustics/arrow.png)
            center/contain no-repeat;
        }
      }

      .left-usb {
        width: 214px;
        height: 206px;
        margin-right: 72px;
        background: url(~@A/images/solution/acoustics/usb.png) center/cover
          no-repeat;
      }
      .left-aiui {
        width: 270px;
        height: 192px;
        margin-right: 27px;
        background: url(~@A/images/solution/acoustics/aiui.png) center/cover
          no-repeat;
      }
    }
  }

  .section-11 {
    max-width: unset;
    .section-tabs {
      margin-top: 50px;
      position: relative;
      ul {
        display: flex;
        justify-content: center;
      }
      li {
        font-size: 20px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #666;
        line-height: 30px;
        position: relative;
        cursor: pointer;
        &:hover {
          color: #326dff;
        }
        &.active {
          color: #326dff;
          &::before {
            position: absolute;
            content: ' ';
            z-index: 1;
            width: 173px;
            height: 4px;
            background: #326dff;
            left: 50%;
            transform: translateX(-50%);
            bottom: -20px;
          }
        }
      }
      li + li {
        margin-left: 215px;
      }
      .bottom-line {
        width: 1200px;
        height: 1px;
        background: #dde1ea;
        position: absolute;
        z-index: 1;
        left: 50%;
        bottom: -20px;
        transform: translateX(-50%);
      }
    }
    .section-swiper {
      background: #f4f7f9;
      margin-top: 70px;
    }
    .case-container {
    }
    .tv {
      height: 431px;
      background: url(~@A/images/solution/acoustics/tv-bg.png) center/cover
        no-repeat;
      padding-top: 57px;
    }
    .tv-box {
      margin: 0 auto;
      width: 1200px;
      height: 323px;
      background: linear-gradient(187deg, #f4f6f8 0%, #ffffff 100%);
      border: 2px solid #ffffff;
      border-radius: 4px;
      box-shadow: -4px 0px 14px 4px rgba(188, 198, 216, 0.3);

      display: flex;
      .tv-left {
        padding: 80px 12px 0 60px;
        h1 {
          font-size: 20px;
          font-weight: 600;
          color: #262626;
        }
        p {
          margin-top: 26px;
          font-size: 14px;
          font-weight: 400;
          color: #666666;
          line-height: 42px;
          max-width: 580px;
        }
      }

      .tv-right {
        padding: 18px 18px 0 75px;
        .tv-image {
          width: 486px;
          height: 285px;
          background: url(~@A/images/solution/acoustics/television.png)
            center/100% no-repeat;
          margin-left: 75px;
        }
        .robot-image {
          width: 486px;
          height: 285px;
          background: url(~@A/images/solution/acoustics/2.jpg) center/100%
            no-repeat;
          margin-left: 75px;
        }
        .box-image {
          width: 486px;
          height: 285px;
          background: url(~@A/images/solution/acoustics/1.jpg) center/100%
            no-repeat;
          margin-left: 75px;
        }
      }
    }
  }
}

.main-content-banner {
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
  height: 420px;
  overflow: hidden;
  width: 100%;
  .banner-text {
    max-width: 1200px;
    color: #fff;
    height: 100%;
    margin: auto;
    &-button {
      font-size: 16px;
      text-align: center;
      font-weight: 400;
      line-height: 50px;
      color: #fff;
      cursor: pointer;
      transition: 0.6s;
      width: 180px;
      height: 50px;
      background: $primary;
      border-radius: 4px;
    }
    h2 {
      color: #181818;
      padding-top: 122px;
      margin-bottom: 25px;
      font-size: 44px;
      font-weight: 600;
      line-height: 48px;
    }
    p {
      font-size: 18px;
      margin-bottom: 48px;
    }

    .banner-text-content {
      width: 570px;
      font-size: 16px;
      font-family: SourceHanSansSC-Regular, SourceHanSansSC;
      font-weight: 400;
      color: #181818;
      line-height: 30px;
    }
  }
}

.corp {
  // padding: 80px 0 100px 0;
  padding-top: 76px;
  height: 320px;
  background: url(~@A/images/solution/acoustics/corp.png) center/cover no-repeat;
}
.section-wrap {
  width: 1200px;
  margin: 0 auto;
  .section-title-bold {
    text-align: left;
  }
}

.section-desc2 {
  text-align: left;
  margin-top: 20px;
  font-size: 18px;
  line-height: 30px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #262626;
  display: block;
}
.section-title-bold {
  font-size: 34px;
  font-family: Microsoft YaHei;
  font-weight: bold;
  color: #333333;
  text-align: center;
}

.section-button {
  font-size: 16px;
  text-align: center;
  line-height: 50px;
  border: none;
  color: #fff;
  cursor: pointer;

  width: 180px;
  height: 50px;
  background: $primary;
  border-radius: 4px;
}

.section-title-contact {
  font-size: 36px;
  font-weight: 400;
  color: #000000;
}
</style>
