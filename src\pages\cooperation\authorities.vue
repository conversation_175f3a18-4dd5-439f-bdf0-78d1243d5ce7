<template>
  <div class="os-scroll">
    <handle-platform-top></handle-platform-top>
    <div class="handle-platform-content">
      <div class="mgb24" style="font-size: 0">
        <el-button icon="ic-r-plus" type="primary" @click="openCreateAccount">
          新增子账号
        </el-button>
        <a
          class="btn-copy"
          v-if="tableData.total"
          :href="loginLink"
          target="_blank"
        >
          子帐号登录页</a
        >
        <i
          class="ic-r-copy"
          title="复制"
          @click="$utils.copyClipboard(loginLink)"
        ></i>
        <div class="fr" @keyup.enter="getAccounts(1)">
          <el-input
            class="search-area"
            placeholder="搜索子帐号"
            v-model="searchVal"
          >
            <i
              slot="suffix"
              class="el-input__icon el-icon-search search-area-btn"
              @click="getAccounts(1)"
            />
          </el-input>
        </div>
      </div>
      <os-table
        :tableData="tableData"
        style="margin-bottom: 56px"
        class="accounts-table"
        @change="getAccounts"
        @edit="openEditAccount"
        @del="del"
      >
        <el-table-column prop="login" width="550" label="帐号">
          <template slot-scope="scope">
            <div
              class="text-blod cp authorities-accout-name"
              :title="scope.row.login || '-'"
              @click="openEditAccount(scope.row)"
            >
              {{ scope.row.login }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="remark" width="500" label="备注">
          <template slot-scope="scope">
            <div class="remark" :title="scope.row.remark">
              {{ scope.row.remark }}
            </div>
          </template>
        </el-table-column>
      </os-table>
    </div>
    <create-account
      :dialog="createAccountDialog"
      @getAccounts="getAccounts"
    ></create-account>
    <edit-account
      :dialog="editAccountDialog"
      :account="currentAccount"
      @getAccounts="getAccounts"
    ></edit-account>
    <el-dialog :visible.sync="limitTIpDialog.show" width="440px">
      <i class="ic-r-exclamation"></i>
      <p
        style="display: inline-block; vertical-align: text-top; font-size: 18px"
      >
        你的子帐号数已达上限<br />请联系 +86 *********** 增加
      </p>
      <span slot="footer" class="dialog-footer">
        <el-button
          class="dialog-btn"
          type="primary"
          size="small"
          style="min-width: 80px"
          @click="limitTIpDialog.show = false"
        >
          知道了
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import HandlePlatformTop from './top.vue'
import CreateAccount from './dialog/createAccount.vue'
import EditAccount from './dialog/accountInfo.vue'
import { mapGetters } from 'vuex'

export default {
  name: 'cooperation-authorities',
  data() {
    return {
      searchVal: '',
      createAccountDialog: {
        show: false,
      },
      editAccountDialog: {
        show: false,
      },
      tableData: {
        loading: true,
        total: 0,
        page: 1,
        size: 10,
        handles: ['edit', 'del'],
        handleColumnText: '操作',
        list: [],
      },
      currentAccount: {
        subUid: '',
        account: '',
        remark: '',
      },
      limitTIpDialog: {
        show: false,
      },
    }
  },
  computed: {
    ...mapGetters({
      limitCount: 'aiuiApp/limitCount',
    }),
    loginLink() {
      return window.location.href.replace(
        'cooperation/authorities',
        'sub/login'
      )
    },
  },
  created() {
    this.getAccounts(1)
  },
  watch: {
    'limitCount.skill_count': function (val) {
      let self = this
      if (
        !self.limitCount.hasOwnProperty('sub_account') ||
        self.limitCount['sub_account'] == '0'
      ) {
        self.$message.warning('您尚未开通协同操作权限')
        self.$router.replace({ name: 'studio-handle-platform-skills' })
      }
    },
  },
  methods: {
    getAccounts(page) {
      let self = this
      this.tableData.loading = true
      this.$utils.httpGet(
        this.$config.api.COOP_ACCOUNTS,
        {
          pageIndex: page || this.tableData.page,
          pageSize: this.tableData.size,
          search: this.searchVal,
        },
        {
          success: (res) => {
            self.tableData.list = res.data.accounts
            self.tableData.total = res.data.count
            self.tableData.page = res.data.pageIndex
            self.tableData.size = res.data.pageSize
            self.tableData.loading = false
          },
          error: (err) => {
            self.tableData.loading = false
          },
        }
      )
    },
    openCreateAccount() {
      let lim = this.limitCount['sub_account_count'] || 50
      if (this.tableData.total >= lim) {
        this.limitTIpDialog.show = true
      } else {
        this.createAccountDialog.show = true
      }
    },
    openEditAccount(data) {
      this.currentAccount = {
        account: data.login,
        subUid: data.subUid,
        remark: data.remark,
      }
      this.editAccountDialog.show = true
    },
    del(data) {
      let self = this
      this.$confirm(
        '子账号删除后将无法恢复，请谨慎操作',
        `确定删除子账号 - ${data.login}`,
        {
          confirmButtonText: '确定删除',
          cancelButtonText: '取消',
          confirmButtonClass: 'el-button--danger',
          type: 'warning',
          showClose: false,
        }
      )
        .then(() => {
          self.delAccounts(data)
        })
        .catch(() => {})
    },
    delAccounts(data) {
      let self = this
      this.tableData.loading = true
      this.$utils.httpPost(
        this.$config.api.COOP_DEL_ACCOUNTS,
        {
          subUid: data.subUid,
        },
        {
          success: (res) => {
            self.$message.success('删除成功')
            if (self.tableData.list.length === 1 && self.tableData.page > 1) {
              self.tableData.page -= 1
            }
            self.getAccounts()
          },
          error: (err) => {},
        }
      )
    },
  },
  components: { HandlePlatformTop, CreateAccount, EditAccount },
}
</script>

<style lang="scss" scoped>
.handle-platform-content {
  max-width: 1200px;
  margin: auto;
}
.search-area {
  width: 480px;
}
.btn-copy {
  font-size: 14px;
  margin-left: 32px;
}
.ic-r-exclamation {
  font-size: 20px;
  margin-right: 16px;
  color: $warning;
}

.ic-r-copy {
  vertical-align: baseline;
  margin-left: 4px;
  font-size: 14px;
  color: $primary;
  cursor: pointer;
}
</style>
<style lang="scss">
.el-table--enable-row-hover .el-table__body tr:hover > td {
  .authorities-accout-name {
    color: $primary;
  }
}
.el-table .ic-r-edit {
  color: $primary;
}
</style>
