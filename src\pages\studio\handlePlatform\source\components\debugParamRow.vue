<template>
  <div class="debug-param-row">
    <el-row :gutter="16" class="param_row">
      <el-col :span="8">
        <div :style="{ paddingLeft: `${nestingLevel * 20}px` }">
          <span
            v-if="hasChildren"
            @click="toggleCollapse"
            class="collapse-icon"
          >
            <i
              :class="collapsed ? 'el-icon-arrow-right' : 'el-icon-arrow-down'"
            ></i>
          </span>
          {{ param.name }}
        </div>
      </el-col>

      <el-col :span="6">
        {{ param.type }}
      </el-col>

      <el-col :span="4">
        {{ param.required ? '是' : '否' }}
      </el-col>

      <el-col :span="6">
        <el-form-item
          v-if="param.type !== 'object' && param.type !== 'array'"
          :prop="fieldProp('defaultValue')"
          :rules="defaultValueRules"
        >
          <el-input
            v-model="localParam.defaultValue"
            placeholder="请输入参数值"
            @input="handleChange"
          />
        </el-form-item>

        <span v-else>-</span>
      </el-col>
    </el-row>

    <template v-if="hasChildren && !collapsed">
      <debug-param-row
        ref="children"
        v-for="(child, index) in localParam.children"
        :key="child.id"
        :param="child"
        :nesting-level="nestingLevel + 1"
        @change="handleChildChange(index, $event)"
        :field-path="`${fieldPath}.children[${index}]`"
      />
    </template>
  </div>
</template>

<script>
export default {
  name: 'DebugParamRow',
  props: {
    param: {
      type: Object,
      required: true,
    },
    nestingLevel: {
      type: Number,
      default: 0,
    },

    fieldPath: {
      // 新增 prop，用于表单校验路径
      type: String,
      default: '',
    },
  },
  data() {
    return {
      localParam: JSON.parse(JSON.stringify(this.param)),
      collapsed: false,
    }
  },
  computed: {
    hasChildren() {
      return this.localParam.children && this.localParam.children.length > 0
    },

    defaultValueRules() {
      if (['object', 'array'].includes(this.localParam.type)) {
        return [] // 返回空规则
      }
      return [
        {
          required:
            this.localParam.required &&
            !['object', 'array'].includes(this.localParam.type),
          message: '请输入默认值',
          trigger: 'blur',
        },
        {
          validator: (rule, value, callback) => {
            if (
              !this.localParam.required ||
              ['object', 'array'].includes(this.localParam.type)
            ) {
              callback()
              return
            }

            if (this.localParam.type === 'number' && isNaN(Number(value))) {
              callback(new Error('必须是数字'))
            }
            if (
              this.localParam.type === 'integer' &&
              !/^-?\d+$/.test(String(value))
            ) {
              callback(new Error('参数必须是integer类型'))
            }
            if (
              this.localParam.type === 'boolean' &&
              String(value).toLowerCase() !== 'true' &&
              String(value).toLowerCase() !== 'false'
            ) {
              callback(new Error('参数必须是boolean类型'))
            } else {
              callback()
            }
          },
          trigger: 'blur',
        },
      ]
    },
  },
  watch: {
    param: {
      deep: true,
      handler(newVal) {
        this.localParam = JSON.parse(JSON.stringify(newVal))
      },
    },
  },
  methods: {
    toggleCollapse() {
      this.collapsed = !this.collapsed
    },

    fieldProp(fieldName) {
      // 处理嵌套路径
      return `${this.fieldPath}.${fieldName}`
    },

    handleChange() {
      const changedParam = {
        ...this.localParam,
        // 清除 object/array 的 defaultValue
        defaultValue: ['object', 'array'].includes(this.localParam.type)
          ? undefined
          : this.localParam.defaultValue,
      }
      this.$emit('change', changedParam)
    },

    handleChildChange(index, newChild) {
      this.$set(this.localParam.children, index, newChild)
      this.handleChange()
    },

    validate() {
      const errors = []
      const { type, required, defaultValue, name } = this.localParam

      // 必填校验（跳过 object/array）
      if (
        required &&
        !['object', 'array'].includes(type) &&
        !defaultValue &&
        defaultValue !== 0
      ) {
        errors.push(`${name} 是必填的`)
      }

      // 类型校验（非 object/array）
      if (
        defaultValue !== undefined &&
        defaultValue !== '' &&
        !['object', 'array'].includes(type)
      ) {
        switch (type) {
          case 'number':
            if (isNaN(Number(defaultValue))) errors.push(`${name} 必须是数字`)
            break
          case 'boolean':
            if (!['true', 'false', true, false].includes(defaultValue)) {
              errors.push(`${name} 必须是布尔值(true/false)`)
            }
            break
        }
      }

      // 递归校验子项
      if (this.$refs.children) {
        this.$refs.children.forEach((child) => {
          errors.push(...child.validate())
        })
      }
      return errors
    },
  },
}
</script>

<style scoped>
:deep(.el-input__inner) {
  background-color: #f5f6f8;
}
.param_row {
  padding: 21px 10px;
  display: flex;
  align-items: center;
  margin-left: 0px !important;
  margin-right: 0px !important;
  /* border-bottom: 1px solid #f0f0f0; */
}

.collapse-icon {
  cursor: pointer;
  margin-right: 5px;
  color: #409eff;
}

.el-input {
  width: 100%;
}
.el-form-item {
  margin-bottom: 0;
}
</style>
