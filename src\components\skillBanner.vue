<template>
  <div class="top-banner-wrap">
    <div class="top-banner">
      <div
        class="bannerSwiperContainer"
        v-swiper:bannerSwiper="bannerSwiperOption"
        style="height: 100%"
      >
        <div class="swiper-wrapper">
          <div
            :class="[
              'swiper-slide',
              {
                'swiper-slide-pointer': !!item.link,
              },
            ]"
            v-for="item in banner"
            :key="item.title"
            @click="onBannerClick(item)"
          >
            <div
              class="top-banner-img"
              :style="{ backgroundImage: 'url(' + item.bg + ')' }"
            ></div>
            <div class="text-area">
              <h1>{{ item.title }}</h1>
              <p
                v-html="item.desc"
                :class="{
                  gutter: item.type === 'multiLang',
                }"
              ></p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="banner-pagination swiper-pagination-clickable swiper-pagination-bullets"
    >
      <span
        v-for="(item, index) in banner"
        :key="item.title"
        @click="toggleBanner(index)"
        :class="[
          'swiper-pagination-bullet',
          {
            'swiper-pagination-bullet-active': activeBannerIndex === index,
          },
        ]"
        :tabindex="index"
        role="button"
      ></span>
    </div>
  </div>
</template>
<script>
import '@static/vue-awesome-swiper'
import multiLangSkillBanner from '@A/images/aiui/skill-banner/03.png'
import dialectSkillBanner from '@A/images/aiui/skill-banner/01.png'
import closedSkillBanner from '@A/images/aiui/skill-banner/02.png'
export default {
  data() {
    return {
      activeBannerIndex: 0,
      bannerSwiperOption: {
        loop: true,
        autoplay: {
          delay: 4000,
          // disableOnInteraction: false,
        },
        pagination: {
          el: '.top-banner .banner-pagination',
          clickable: true,
        },
      },
      // banner: [
      //   {
      //     bg: multiLangSkillBanner,
      //     bgSize: 'cover',
      //     title: '多语种技能上线',
      //     desc: 'AIUI语义现已支持英、韩、日、俄、法、西、德、阿、<br/>泰、越南、印地、意、葡13个多语种技能。如有需要，<br/>欢迎联系我们。',
      //     link: 'https://jinshuju.net/f/H7Sxrq',
      //     type: 'multiLang',
      //   },
      //   {
      //     bg: dialectSkillBanner,
      //     bgSize: 'cover',
      //     title: '方言技能上线',
      //     desc: '天气、音乐等常用技能支持中/粤/川交互',
      //   },
      //   {
      //     bg: closedSkillBanner,
      //     bgSize: 'cover',
      //     title: '封闭技能上线',
      //     desc: '成语接龙、口算挑战等游戏类技能更新为封闭版本',
      //   },
      // ],
      banner: [],
    }
  },
  created() {
    this.getBanner()
  },
  mounted() {
    this.bannerSwiper.on('slideChange', () => {
      this.$nextTick(() => {
        this.activeBannerIndex = this.bannerSwiper.realIndex
      })
    })
  },
  methods: {
    getBanner() {
      this.$utils.httpGet(
        `/resource/banner/list?type=3`,
        {},
        {
          noLogin: true,
          noMessage: true,
          success: (res) => {
            this.banner = (res.data.storeBanner || [])
              .sort((obj1, obj2) => {
                if (obj1.number < obj2.number) {
                  return -1
                } else if (obj1.number > obj2.number) {
                  return 1
                } else {
                  return 0
                }
              })
              .map((item) => {
                return {
                  bg: item.imgUrl,
                  bgSize: 'cover',
                  desc: item.description,
                  title: item.title,
                  link: item.jumpUrls.length === 1 ? item.jumpUrls[0].url : '',
                }
              })
          },
          error: (err) => {},
        }
      )
    },
    toggleBanner(index) {
      this.bannerSwiper.slideToLoop(index)
    },
    onBannerClick(item) {
      if (!!item.link) {
        window.open(item.link, '_blank')
      }
    },
  },
}
</script>
<style lang="scss" scoped>
p,
h1 {
  margin-bottom: 0;
}

.top-banner-wrap {
  position: relative;
}
.top-banner {
  width: 100%;
  height: 240px;
  position: relative;
  &-img {
    display: block;
    width: 100%;
    min-height: 100%;
    background-position: center;
    background-repeat: no-repeat;
    background-size: auto 100%;
  }

  .text-area {
    width: 100%;
    padding-left: 234px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    margin: 0 auto;
    h1 {
      font-size: 36px;
      line-height: 36px;
      font-weight: 400;
      color: #ffffff;
    }
    p {
      font-size: 20px;
      font-weight: 400;
      color: #ffffff;
      line-height: 30px;
      margin-top: 30px;
      max-width: 520px;
    }
  }

  // 技能商店适配不同屏幕
  @media screen and (max-height: 801px) {
    .text-area {
      padding-left: 156px;
      h1 {
        font-size: 24px;
      }
      p {
        font-size: 14px;
        margin-top: 20px;
        line-height: 20px;
      }
      .gutter {
        margin-top: 10px;
      }
    }
    &-img {
      background-size: auto 110%;
    }
  }
}

.banner-pagination {
  position: absolute;
  bottom: 30px;
  width: 100%;
  height: 12px;
  line-height: 12px;
  text-align: center;
  z-index: 1;
  left: 50%;
  transform: translatex(-50%);
  :deep(.swiper-pagination-bullet) {
    width: 54px;
    height: 5px;
    border-radius: 0px;
    background: rgba(255, 255, 255, 0.3);
    margin: 0 7px;
    outline: none;
  }

  :deep(.swiper-pagination-bullet-active) {
    position: relative;
    &::before {
      content: ' ';
      position: absolute;
      border-radius: 0px;
      z-index: 100;
      width: 54px;
      height: 5px;
      background: #fff;
      top: 0;
      left: 0;
      animation: ani-step 4s linear 0s 1;
    }
  }
}

@keyframes ani-step {
  from {
    width: 0px;
  }
  to {
    width: 100%;
  }
}
@media screen and (max-height: 801px) {
  .top-banner {
    height: 136px;
  }
}

.swiper-slide-pointer {
  cursor: pointer;
}
</style>
