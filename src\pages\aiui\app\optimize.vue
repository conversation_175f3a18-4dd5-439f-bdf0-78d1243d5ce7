<template>
  <os-page :options="pageOptions">
    <div class="app-statistic-optimize-page" style="margin-bottom: 100px;">
      <p class="tip">以下是你的应用未被引擎识别出的用户请求（RC=4）</p>
      <div class="operation-wrap">
        <el-select v-model="sceneIndex" size="medium" placeholder="请选择"
          @change="changeScene">
          <el-option
            v-for="(val, key, index) in sceneList"
            :key="index"
            :label="val.sceneName"
            :value="key">
          </el-option>
        </el-select>
        <date-range @setTime="setTime"></date-range>
        <el-button class="btn-export"
          type="primary" size="small" @click="exportRCLog">导出</el-button>
      </div>
      <os-table
        :tableData="tableData"
        style="margin-bottom: 56px;"
        @change="getRCLog">
        <el-table-column
          prop="scene"
          label="情景模式"
          width="120">
        </el-table-column>
        <el-table-column
          prop="text"
          label="用户语料"
          width="300">
        </el-table-column>
        <el-table-column
          prop="count"
          label="出现次数"
          width="96">
        </el-table-column>
        <el-table-column
          prop="date"
          label="最近出现日期"
          width="126">
          <template slot-scope="scope">
            {{ scope.row.date.substr(0, 10) }}
          </template>
        </el-table-column>
      </os-table>
      <p class="no-data-tip" v-if="!tableData.total">升级中，敬请期待</p>
    </div>
  </os-page>
</template>
<script>
  import fileDownload from 'js-file-download'
  import dateRange from './dateRange'

  export default {
    name: 'app-optimize',
    data(){
      return {
        pageOptions: {
          title: '在线优化',
          loading: false
        },
        sceneIndex: 0,
        sceneList: [],
        startDate: '',
        endDate: '',
        tableData: {
          loading: false,
          total: 0,
          page: 1,
          size: 10,
          list: []
        },
        timeChanged: false
      }
    },
    computed: {
      appId() {
        return this.$route.params.appId
      }
    },
    watch: {
      async timeChanged(val) {
        if(val){
          await this.getAppSceneList()
          this.getRCLog()
        }
      }
    },
    methods: {
      setTime(start, end) {
        this.startDate = start
        this.endDate = end
        this.timeChanged = true
      },
      getAppSceneList() {
        this.$utils.httpGet(this.$config.api.AIUI_SCENE_LIST, {
          appid: this.appId
        },{
          success: res => {
            this.sceneList = res.data
            this.sceneList.unshift({
              sceneName: 'all'
            })
            this.timeChanged = false
          },
          error: err => {
            this.timeChanged = false
          }
        })
      },
      getRCLog(page) {
        this.tableData.loading = true
        this.$utils.httpGet(this.$config.api.AIUI_STATISTIC_RCLOG, {
          appid: this.appId,
          sceneName: this.sceneIndex ? this.sceneList[this.sceneIndex].sceneBoxName : 'all',
          beginTime: this.startDate,
          endTime: this.endDate,
          pageIndex: page || this.tableData.page,
          pageSize: this.tableData.size
        },{
          success: res => {
            this.tableData.loading = false
            this.tableData.list.splice(0)
            this.tableData.list.push(...res.data.datas)
            this.tableData.total = parseInt(res.data.count)
          },
          error: err => {
            this.tableData.loading = false
          }
        })
      },
      changeScene(val){
        this.getRCLog()
      },
      exportRCLog() {
        this.$utils.httpPost(this.$config.api.AIUI_EXPORT_RCLOG, {
          appid: this.appId,
          sceneName: this.sceneIndex ? this.sceneList[this.sceneIndex].sceneBoxName : 'all',
          beginTime: this.startDate,
          endTime: this.endDate
        },{
          success: res => {
            if(res.headers['content-type'].indexOf('application/octet-stream') >= 0) {
              fileDownload(res.data, decodeURI(res.headers['content-disposition'].split('filename=')[1]))
            }
          },
          error: err => {
          }
        })
      }
    },
    components: {
      dateRange
    }
  }
</script>
<style lang="scss" scoped>
  .app-statistic-optimize-page {
    position: relative;
  }
  .tip {
    margin: 40px 0 48px;
  }
  .operation-wrap {
    position: relative;
    margin-bottom: 20px;
    font-size: 0;
  }
  .date-range-wrap {
    margin-left: 8px;
  }
  .btn-export {
    position: absolute;
    right: 0;
  }
  .no-data-tip {
    position: absolute;
    top: 185px;
    text-align: center;
    width: 100%;
    background: #fff;
    line-height: 50px;
    color: $grey5;
  }
</style>
