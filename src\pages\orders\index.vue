<template>
  <div class="os-page">
    <div class="os-content os-scroll">
      <div class="orders-page" style="margin-bottom: 250px;" v-loading="loading">
      <div class="page-title">我的订单</div>
        <template v-if="total">
          <ul class="order mgt24" v-for="(item, index) of list" :key="index">
            <li class="order-item divider name-wrap">
              <p class="package-name">{{ item.goodsName }}</p>
              <p class="package-num">￥{{ item.price }} x {{item.number}}</p>
            </li>
            <li class="order-item divider order-number">
              <p>下单时间<span :title="item.orderDate">{{ item.orderDate ? item.orderDate.replace('.0', '') :  '-' }}</span></p>
              <p>订单号&nbsp;&nbsp;&nbsp;<span :title="item.tradeNo">{{ item.tradeNo || '-' }}</span></p>
              <p>支付流水<span :title="item.turnoverNo">{{ item.turnoverNo || '-' }}</span></p>
            </li>
            <li class="order-item divider small-wrap">
              <span class="price">{{ $utils.moneyformat(item.totalFee) }}</span>
            </li>
            <li class="order-item divider small-wrap" :class="{'cancel-status': item.orderStatus == 0}">
              <i :class="'status status-' + item.orderStatus"></i>
              {{ item.orderStatus | orderStatusList }}
            </li>
            <li class="order-item">
              <div class="wait-payment-wrap" v-if="item.orderStatus == 1">
                <el-button
                  class="btn-to-pay"
                  type="primary"
                  size="mini"
                  @click="toPay(item)">立即付款</el-button>
                <a class="btn-order-detail" @click="toDetail(item)">订单详情</a>
                <a class="btn-cancel-order"
                  @click="beforeCancelOrder(item.tradeNo)">取消订单</a>
              </div>
              <a v-else class="btn-order-detail" @click="toDetail(item)">订单详情</a>
              <i v-if="item.orderStatus == 0" class="ic-r-delete del-icon" @click="beforeDelOrder(item.tradeNo)"></i>
            </li>
          </ul>
          <el-pagination
            layout="prev, pager, next"
            :total="total"
            :page-size="5"
            :current-page.sync="pageIndex"
            @current-change="getOrderList"
          >
          </el-pagination>
        </template>
        <p v-else class="tip-no-data">暂无订单数据</p>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'orders',
  data() {
    return {
      loading: false,
      total: 0,
      pageIndex: 1,
      size: 5,
      list: [],
      statusOnTraining: [3, 4, 5],
      waitingForDeliveryStatus: [3, 8, 9],
      dealFinishedStatus: [5, 6, 11],
      cancelStatuss: ['0', '10'],
      xfyunHardwareIds: ['3002001', '6012003']
    }
  },
  created() {
    this.getOrderList()
    sessionStorage.removeItem('tradeNo')
    sessionStorage.removeItem('xfyunGoodsId')
  },
  methods: {
    getOrderList() {
      let self = this
      self.loading = true
      let data = {
        search: '',
        pageIndex: this.pageIndex,
        pageSize: this.size
      }
      this.$utils.httpGet(self.$config.api.ORDER_LIST, data, {
        success: res => {
          self.loading = false
          if (res.data) {
            self.list = res.data.list
            self.total = res.data.count
          } else {
            self.list = []
            self.total = 0
          }
          for( let item of self.list) {
            if(item.orderFrom != 3 && !this.xfyunHardwareIds.includes(item.goodsId) && item.orderStatus == 2 ) {
              item.orderStatus = 6
            }
          }
        },
        error: () => {
          self.loading = false
        }
      })
    }, 
    toPay(item) {
      let url
      if(item.orderFrom == 3){
        url = `${this.$config.aifuwus}user/payway?orderNumber=${item.tradeNo}&goodsId=${item.goodsId}`
      } else {
        url = `${this.$config.xfyunConsole}sale/payway?tradeNo=${item.tradeNo}`
      }
      window.open(url, '_blank')
    },
    beforeDelOrder(tradeNo){
      let api = this.$config.api.ORDER_DELETE,
        desc="删除",
        subDesc = '订单删除后不可恢复，请谨慎操作。'
      this.cancelOrDeleteOrderPopover(api, tradeNo, desc, subDesc)
    },
    beforeCancelOrder(tradeNo){
      let api = this.$config.api.ORDER_CANCEL,
        desc="取消",
        subDesc = '取消后需重新下单'
      this.cancelOrDeleteOrderPopover(api, tradeNo, desc, subDesc)
    },
    cancelOrDeleteOrderPopover(api, tradeNo, desc, subDesc){
      let self = this
      this.$confirm(subDesc,
        `确定${desc}订单吗？`, {
        confirmButtonText: `${desc == '删除' ? '删除' : '取消订单'}`,
        cancelButtonText: `${desc == '删除' ? '取消' : '关闭'}`,
        confirmButtonClass: 'el-button--danger',
        type: 'warning',
        showClose: false
      }).then(() => {
        self.cancelOrDeleteHandle(api, tradeNo, desc)
      }).catch(() => {})
    },
    cancelOrDeleteHandle(api, tradeNo, desc) {
      this.$utils.httpGet(
        api, { tradeNo: tradeNo },{
        success: res => {
          this.$message({
            message: res.desc || `${desc}成功`,
            type: 'success'
          })
          this.getOrderList()
        },
        error: err => {
          this.$message({
            message: err && err.desc || `${desc}失败`,
            type: 'error'
          })
        }}
      )
    },
    toDetail(item) {
      sessionStorage.setItem('tradeNo', item.tradeNo)
      if(item.orderFrom != 3 && this.xfyunHardwareIds.includes(item.goodsId)) {
        sessionStorage.setItem('xfyunGoodsId', item.goodsId)
      }
      this.$router.push({ name: 'orderPage' })
    }
  }
}
</script>
<style lang="scss" scoped>
.orders-page {
  margin: 0 auto;
  width: 1080px
}
.page-title {
  margin-top: 48px;
  font-size: 30px;
}
.order {
  position: relative;
  min-width: 960px;
  height: 144px;
  border-radius: 12px;
  border: 1px solid $grey2;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow:0px 12px 36px 0px rgba(0,0,0,0.05);
  &:hover {
    .del-icon {
      display: block;
    }
  }

  .order-item {
    height: 100%;
    flex: 1;
    text-align: center;
    line-height: 142px;
  }

  .divider {
    position: relative;
    &::after {
      position: absolute;
      top: 44px;
      right: 0;
      content: ' ';
      width: 1px;
      height: 56px;
      background: $grey2;
    }
  }
}
.name-wrap {
  padding: 46px 48px;
  min-width: 235px;
  line-height:22px!important;
  text-align: left!important;
}
.package-name {
  margin-bottom: 8px;
  font-size: 16px;
  font-weight: 600;
}
.order-number {
  padding-top: 31px ;
  min-width: 300px;
  p {
    padding: 0 32px;
    margin-bottom: 8px;
    text-align: left;
    color: $grey5;
    line-height: 22px;
  }
  span {
    margin-left: 24px;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    display: inline-block;
    width: calc( 100% - 80px );
    color: $semi-black;
    vertical-align: bottom;
  }
}
.small-wrap {
  line-height: 96px;
  width: calc(100% - 900px);
  min-width: 150px;
}
.price {
  font-size: 20px;
  font-weight: 500;
}
.cancel-status {
  color: $grey4;
}
.status {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  border: solid 2px;
  display: inline-block;
  border-color: $primary; 
  &-0, &-10 {
    border-color: $grey4; //待付款
  }
  &-1 {
    border-color: $warning; //已支付
  }
  &-3, &-8 {
    border-color: $success; //已支付
  }
}

.btn-order-detail {
}
.order-detail {
  margin: 16px 0 8px;
  line-height: 1;
}
.btn-cancel-order {
  color: $semi-black;
}
.el-pagination {
  margin-top: 28px;
  text-align: center;
}

.tip-no-data {
  line-height: 200px;
  text-align: center;
}

.wait-payment-wrap {
  .btn-to-pay {
    min-width: 84px;
    display: block;
    margin: 24px auto 16px;
  }
  a {
    display: block;
    line-height: 22px;
    margin-bottom: 8px;
  }
}
.del-icon {
  display: none;
  position: absolute;
  right: 16px;
  bottom: 16px;
  line-height: 1;
  font-size:20px;
  color: $grey5;
  cursor: pointer;
}
</style>
