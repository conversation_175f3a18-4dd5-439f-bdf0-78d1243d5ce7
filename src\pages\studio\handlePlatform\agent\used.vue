<template>
  <os-page :options="pageOptions">
    <studio-agent-header-right slot="btn" />
    <div class="os-scroll">
      <div class="mgb24" @keyup.enter="getReferedIndentList(1)">
        <el-input
          class="search-area"
          placeholder="搜索意图"
          v-model="intentionSearchName"
        >
          <i
            slot="suffix"
            class="el-input__icon el-icon-search search-area-btn"
            @click="getReferedIndentList(1)"
          />
        </el-input>
      </div>

      <os-table
        class="intentions-table"
        :tableData="tableData"
        style="margin-bottom: 56px"
        @del="del"
        @change="getReferedIndentList"
      >
        <!-- <el-table-column type="index" width="50">
          <template slot-scope="scope">
            {{ (tableData.page - 1) * tableData.size + scope.$index + 1 }}
          </template>
        </el-table-column> -->

        <el-table-column prop="intentName" label="意图名称" min-width="100px">
          <template slot-scope="scope">
            <div class="intent-zhname ib" @click="toEdit(scope.row)">
              {{ scope.row.intentName }}
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="intentNameEn" label="英文标识" min-width="100px">
          <template slot-scope="scope">
            <div class="intent-name" @click="toEdit(scope.row)">
              {{ scope.row.intentNameEn }}
            </div>
          </template>
        </el-table-column>

        <el-table-column
          label="意图描述"
          prop="intentDesc"
          min-width="300px"
        ></el-table-column>
      </os-table>
    </div>
  </os-page>
</template>

<script>
export default {
  name: 'IflyAIuiWebIndentUsed',

  data() {
    return {
      pageOptions: {
        title: '引用的意图',
        loading: false,
      },
      tableData: {
        loading: true,
        total: 0,
        page: 1,
        size: 10,
        handles: ['del'],
        switchOptions: {
          column: 'type',
          active: 2,
          inactive: 3,
          activeText: '入口',
          inactiveText: '对话',
        },
        handleColumnText: '操作',
        list: [],
      },
      intentionSearchName: null,
    }
  },

  mounted() {
    this.getReferedIndentList(1)
  },

  methods: {
    getReferedIndentList(page) {
      const params = {
        pageIndex: page || this.tableData.page,
        pageSize: this.tableData.size,
        agentId: this.$route.params.agentId,
        intentName: this.intentionSearchName,
      }

      this.$utils.httpPost(
        this.$config.api.AGENT_USED_QUOTE_LIST,
        JSON.stringify(params),
        {
          config: {
            headers: {
              'Content-Type': 'application/json;charset=UTF-8',
            },
          },
          success: (res) => {
            if (res.code == '0') {
              this.tableData.list = res.data.data
              this.tableData.page = res.data.pageIndex
              this.tableData.size = res.data.pageSize
              this.tableData.total = res.data.totalSize
              this.tableData.loading = false
            }
            // else if (res.code == '328002') {
            //   this.tableData.loading = false
            //   this.$message.error(res.desc)
            // }
          },
          error: (err) => {
            this.$message.error(err.desc)
            this.tableData.loading = false
          },
        }
      )
    },
    del(data) {
      this.$confirm('意图删除后不可恢复，请谨慎操作。', `确定删除该意图吗?`, {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        confirmButtonClass: 'el-button--danger',
        type: 'warning',
        showClose: false,
      })
        .then(() => {
          const params = {
            agentId: this.$route.params.agentId,
            intentId: data.intentId,
            quoteFlag: false, //  这里的删除,是引用关系,
          }
          this.$utils.httpPost(
            this.$config.api.AGENT_OFFICAL_INTENT_QUOTE,
            JSON.stringify(params),
            {
              config: {
                headers: {
                  'Content-Type': 'application/json;charset=UTF-8',
                },
              },
              success: (res) => {
                if (res.code == '0') {
                  this.$message.success('操作成功')
                  this.getReferedIndentList(1)
                }
              },
              error: (err) => {
                this.$message.error(err.desc)
              },
            }
          )
        })
        .catch(() => {})
    },
  },
}
</script>

<style lang="scss" scoped>
.mgb24 {
  margin-top: 10px;
}
</style>
