<template>
  <section
    class="aiui-section section-interact"
    v-lazy:background-image="
      require('@A/images/aiui/main-page/img_interact_bg.png')
    "
  >
    <div class="aiui-section-wrap">
      <div class="aiui-section-title"><span>交互方式</span></div>
      <ul class="interact-list">
        <li
          v-for="(item, index) in interactList"
          :key="index"
          :class="item.klass"
          v-lazy:background-image="
            require(`@A/images/aiui/main-page/${item.klass}.jpg`)
          "
        >
          <h1>{{ item.name }}</h1>
          <div class="desc-wrap">
            <h2>{{ item.name }}</h2>
            <p>
              {{ item.desc }}
            </p>
          </div>
          <div class="overlay"></div>
        </li>
      </ul>
    </div>
  </section>
</template>
<script>
function getWindowHeight() {
  return 'innerHeight' in window
    ? window.innerHeight
    : document.documentElement.offsetHeight
}
export default {
  data() {
    return {
      interactList: [
        {
          name: '全双工',
          desc: '通过讯飞麦克风的回声消除能力，可以一次唤醒，多次交互，实现与设备持续的对话。',
          // image: 'img_full_duplex.png',
          klass: 'img_full_duplex',
        },
        {
          name: '免唤醒',
          desc: '通过定制200个入口说法，实现用户不用唤醒，直接和设备进行语音交互。',
          // image: 'img_free_wake_click.png',
          klass: 'img_free_wake_click',
        },
        {
          name: '多模态',
          desc: '融合声纹识别，手势识别、唇形检测、虚拟人交互等AI技术，让人机交互方式更丰富。',
          // image: 'img_multimodal_click.png',
          klass: 'img_multimodal_click',
        },
        {
          name: '离线交互',
          desc: '通过离线的控制命令和语音技能，让设备不需要网络也可以语音控制交互。',
          // image: 'img_offlineinteraction_click.png',
          klass: 'img_offlineinteraction_click',
        },
      ],
    }
  },
  mounted() {
    // this.adjustContent()
  },
  methods: {
    adjustContent() {
      // 设置高度
      let height = Math.max(getWindowHeight(), 500)

      Array.from(document.getElementsByClassName('section-interact')).forEach(
        (item, index) => {
          item.style.height = `${height}px`
        }
      )
    },
  },
}
</script>
<style lang="scss" scoped>
@import './common.scss';
.section-interact {
  min-height: 587px;
  background: center/100% repeat-y;
}
.interact-list {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 70px auto 0;

  li {
    position: relative;
    text-align: center;
    cursor: pointer;
    width: 240px;
    height: 400px;
    // background: rgba(0, 0, 0, 0.5);

    .desc-wrap {
      top: 50%;
      transform: translateY(-50%);
      position: absolute;
      left: 0;
      width: 100%;
      z-index: 2;
    }
    .overlay {
      display: none;
      width: 100%;
      height: 100%;
      // background: rgba(0, 0, 0, 0.3);
      background-image: linear-gradient(
        0deg,
        rgb(0, 54, 255) 0%,
        rgb(39, 12, 73) 100%
      );
      opacity: 0.502;
      position: absolute;
      top: 0;
      left: 0;
      z-index: 1;
    }

    &:hover {
      p,
      h2 {
        display: block;
      }
      h1 {
        display: none;
      }
      .overlay {
        display: block;
      }
    }
    h1 {
      text-align: left;
      max-width: 25px;
      height: 84px;
      font-size: 24px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #ffffff;
      line-height: 30px;
      margin: 0 auto;
      position: relative;
      top: 50%;
      transform: translateY(-50%);
    }
    h2 {
      display: none;
      text-align: left;
      // padding-top: 178px;
      padding-left: 35px;
      font-size: 34px;
      font-weight: bold;
      color: #ffffff;
      line-height: 40px;
    }
    p {
      display: none;
      margin-top: 32px;
      width: 232px;
      font-size: 16px;
      font-weight: 400;
      color: #ffffff;
      line-height: 32px;
      padding-left: 35px;
      text-align: left;
    }

    &.img_full_duplex,
    &.img_free_wake_click,
    &.img_multimodal_click,
    &.img_offlineinteraction_click {
      background-position: center;
      background-size: 100%;
      background-repeat: no-repeat;
    }

    // &.img_full_duplex {
    //   background: url(~@A/images/aiui/main-page/img_full_duplex.jpg) center/100%
    //     no-repeat;
    // }
    // &.img_free_wake_click {
    //   background: url(~@A/images/aiui/main-page/img_free_wake_click.jpg)
    //     center/100% no-repeat;
    // }
    // &.img_multimodal_click {
    //   background: url(~@A/images/aiui/main-page/img_multimodal_click.jpg)
    //     center/100% no-repeat;
    // }
    // &.img_offlineinteraction_click {
    //   background: url(~@A/images/aiui/main-page/img_offlineinteraction_click.jpg)
    //     center/100% no-repeat;
    // }
  }
}
@media screen and (min-width: 1500px) {
  .interact-list {
    li {
      width: 300px;
      height: 500px;
    }
  }
}
</style>
