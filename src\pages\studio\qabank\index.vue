<template>
  <div class="os-scroll os_scroll" ref="scrollDiv">
    <div class="qabank-page" v-loading="tableData.loading">
      <div class="qabank-page-head">
        <div class="qabank-page-head-title">
          <!-- <span style="max-width: 250px" class="txt-ellipsis-nowrap"
            >问答编辑</span
          > -->
        </div>

        <div class="header-right">
          <span class="header-save-time" v-if="baseQaInfo.updateTime"
            >最近保存 {{ baseQaInfo.updateTime | time }}</span
          >
          <span class="header-qa">
            <el-tooltip
              class="item"
              effect="dark"
              content="问答库修改后需要重新构建"
              placement="bottom"
            >
              <i class="el-icon-question" />
            </el-tooltip>
          </span>
          <el-button
            size="small"
            type="primary"
            @mousedown.native.prevent="structure"
            :loading="structureLoading"
            style="padding: 11px 0px"
          >
            {{ structureLoading ? '构建中...' : '构建问答库' }}</el-button
          >
        </div>
      </div>
      <div
        v-loading="structureLoading"
        style="padding: 26px 30px 16px 30px"
        element-loading-text="
          正在构建中，请稍候
        "
      >
        <div class="qabank-page-handle-bar">
          <div>
            <el-button
              class="mgr16"
              icon="ic-r-plus"
              type="primary"
              size="small"
              style="min-width: 112px"
              @click="openCreateTheme"
            >
              创建主题
            </el-button>

            <el-dropdown
              trigger="click"
              @command="handleCommand"
              placement="bottom-start"
            >
              <el-button size="small">
                批量操作
                <i class="ic-r-triangle-down el-icon--right" />
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="cover">
                  <upload
                    :qaId="qaId"
                    :options="cover"
                    :limitCount="limitCount"
                    @setLoad="setLoad"
                    @getQaPair="getQaPair(1)"
                  ></upload>
                </el-dropdown-item>
                <el-dropdown-item command="questioning">
                  <upload
                    :qaId="qaId"
                    :options="addOnly"
                    :limitCount="limitCount"
                    @setLoad="setLoad"
                    @getQaPair="getQaPair(1)"
                  ></upload>
                </el-dropdown-item>
                <el-dropdown-item command="export">导出问答</el-dropdown-item>
                <el-dropdown-item command="down">下载模版</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>

            <!-- <el-switch
              v-model="avatar"
              active-text="虚拟人驱动"
              inactive-text=""
              @change="onAvatarSwitchChange"
              style="margin-left: 20px"
            >
            </el-switch> -->
          </div>

          <div
            class="search-block"
            style="font-size: 0"
            @keyup.enter="getQaPair(1)"
          >
            <el-input
              placeholder="搜索主题、问题、答案"
              v-model="searchVal"
              size="medium"
              class="search-area"
            >
              <el-select
                v-model="type"
                slot="prepend"
                style="width: 120px"
                placeholder="请选择"
              >
                <el-option label="全部" value=""></el-option>
                <el-option label="主题" value="topicName"></el-option>
                <el-option label="问题" value="question"></el-option>
                <el-option label="答案" value="answer"></el-option>
              </el-select>
              <i
                slot="suffix"
                class="el-input__icon el-icon-search search-area-btn"
                @click="getQaPair(1)"
              />
            </el-input>
            <el-button size="small" @click="resetSearch"> 重置 </el-button>
          </div>
        </div>

        <div class="qabank-page-theme-list">
          <div
            :class="{
              'qabank-page-theme': true,
              'theme-editting': editingTheme.topic === theme.topic,
            }"
            v-for="(theme, index) in tableData.list"
            :key="theme.topic || index"
          >
            <template v-if="editingTheme.topic !== theme.topic">
              <div class="qabank-page-theme-head">
                <div class="theme-head-wrap">
                  <span
                    class="qabank-page-theme-head-name txt-ellipsis-nowrap"
                    style="cursor: pointer"
                    @click="toEdit(theme)"
                    :title="theme.topicName"
                    v-html="
                      theme.topicName.replace(
                        new RegExp(searchVal.trim(), 'im'),
                        '<span class=\'qabank-page-hight-light\'>' +
                          searchVal.trim() +
                          '</span>'
                      )
                    "
                  >
                  </span>
                  <span
                    class="status-tag status-warning"
                    v-if="theme.status === 1"
                    >待构建</span
                  >
                  <span
                    class="status-tag status-publishing"
                    v-else-if="theme.status === 2"
                    >待发布</span
                  >
                  <span
                    class="status-tag status-success"
                    v-else-if="theme.status === 3"
                    >已发布</span
                  >
                </div>

                <ul class="theme-edit-btns">
                  <li v-if="!theme.more" @click="showMore(theme, index)">
                    <i class="el-icon-arrow-down"></i><span>展开</span>
                  </li>
                  <li v-else @click="showMore(theme, index)">
                    <i class="el-icon-arrow-up"></i><span>收起</span>
                  </li>
                  <li @click="toEdit(theme)">
                    <i class="el-icon-edit-outline"></i><span>编辑</span>
                  </li>

                  <li v-if="theme.topic">
                    <el-popconfirm
                      title="确定删除该主题吗？"
                      @confirm="delTheme(theme)"
                    >
                      <span slot="reference"
                        ><i class="el-icon-delete"></i><span>删除</span></span
                      >
                    </el-popconfirm>
                  </li>
                </ul>
              </div>
              <div class="qabank-page-theme-content">
                <ul class="qabank-page-theme-content-ul">
                  <div class="question-title-wrap">
                    <label>问题</label>
                    <!-- <span class="question-ai-item">AI扩写15条&nbsp;&gt;</span> -->
                  </div>

                  <li class="question-wrap">
                    <div
                      v-for="(question, quesIndex) in theme.questionList"
                      :key="quesIndex"
                      v-if="theme.more || quesIndex === 0"
                    >
                      <div
                        class="ib serial-number"
                        style="
                          width: 24px;
                          min-width: 24px;
                          align-self: flex-start;
                          margin-top: 12px;
                        "
                      >
                        {{ quesIndex + 1 }}
                      </div>
                      <span
                        v-html="
                          question.question.replace(
                            new RegExp(searchVal.trim(), 'im'),
                            '<span class=\'qabank-page-hight-light\'>' +
                              searchVal.trim() +
                              '</span>'
                          )
                        "
                      ></span>
                    </div>
                    <div></div>
                  </li>
                </ul>
                <ul class="qabank-page-theme-content-ul">
                  <label>答案</label>
                  <li class="answer-outer-wrap">
                    <div
                      v-for="(answer, ansIndex) in theme.answerList"
                      :key="ansIndex"
                      v-if="theme.more || ansIndex === 0"
                      class="answer-wrap"
                    >
                      <div class="ib serial-number" style="width: 24px">
                        {{ ansIndex + 1 }}
                      </div>

                      <inteligient-rich-input
                        :key="`${theme.topic}_${ansIndex}_${
                          answer.answer
                        }_${JSON.stringify(answer.labels || [])}`"
                        placeholder=""
                        :value="{
                          text: answer.answer,
                          labels: answer.labels || [],
                          changed: false,
                        }"
                        :showAdd="false"
                        :showSwitch="false"
                        :disabled="true"
                        :edit="true"
                        :editIndex="index"
                        :hasSlot="false"
                        :renderMark="avatar"
                        :searchVal="searchVal"
                      >
                      </inteligient-rich-input>
                      <span class="answer-tag"
                        >({{ answer.emotion | answerEmotion }})</span
                      >
                      <span
                        class="answer-tag"
                        v-if="isNativeAccount && answer.style"
                        >({{ answer.style | answerStyleRender }})</span
                      >
                    </div>
                  </li>
                </ul>
              </div>
            </template>
            <div v-else>
              <div
                class="qabank-page-theme-head"
                style="margin-top: 4px; margin-bottom: 24px"
              >
                <el-input
                  :ref="'qaValueInput' + index"
                  size="small"
                  class="qabank-page-theme-head-input"
                  v-model="theme.topicName"
                  placeholder="请输入主题名称"
                  maxlength="128"
                />
              </div>
              <div class="qabank-page-theme-content">
                <ul class="qabank-page-theme-content-ul">
                  <label>添加问题</label>
                  <li
                    class="edit-question-wrap"
                    v-loading="aiLoading"
                    element-loading-text="正在扩写中，请稍候"
                  >
                    <aiui-text-adder
                      :data="theme.questionList"
                      :max="40"
                      dataKey="question"
                      @add="addQues"
                      @edit="editQues"
                      @del="delQues"
                      :reg="quesTextReg"
                      warning="问题不能超过128个字符"
                      placeholder="输入问题，回车添加"
                    />
                    <!-- 调用AI扩写，生成更多问题语料！ -->
                    <el-tooltip
                      content="调用AI扩写，生成更多问题语料！"
                      placement="top"
                    >
                      <div
                        v-if="
                          theme.questionList.filter((it) => !it.ai).length < 40
                        "
                        :class="{
                          'magic-icon': true,
                          active: theme.questionList.length > 0,
                        }"
                        @click="onAiMagicClick(theme)"
                      >
                        <svg-icon iconClass="magic" />
                      </div>
                    </el-tooltip>
                  </li>
                </ul>
                <ul class="qabank-page-theme-content-ul">
                  <label>添加答案</label>
                  <li>
                    <qa-answers
                      :list="theme.answerList"
                      @setQaAnswers="setQaAnswers"
                      qaType="sentence"
                      :showStyle="isNativeAccount && isOpenRepo"
                      :avatar="avatar"
                    ></qa-answers>
                  </li>
                </ul>
              </div>
              <div class="qabank-page-theme-footer">
                <el-button
                  size="small"
                  style="min-width: 80px"
                  @click="cancelEditQa(index)"
                >
                  取消
                </el-button>
                <el-button
                  size="small"
                  type="primary"
                  style="min-width: 80px"
                  @click="saveQa"
                >
                  保存
                </el-button>
              </div>
            </div>
          </div>
          <div
            class="qabank-page-theme"
            v-if="!tableData.list.length"
            style="text-align: center; padding: 20px"
          >
            暂无数据
          </div>

          <!-- 创建问答提示 -->
          <!-- <div class="qabank-page-empty" v-if="tableData.list.length <= 0">
          <div class="qabank-page-empty-thumb"></div>
          <div class="qabank-page-empty-main">
            <span>你还没有创建任何主题，</span><a @click="openCreateTheme">点击创建</a>
          </div>
          <div class="qabank-page-empty-desc">
            技能，类似于手机 APP 的概念，一个语音技能用于解决一类用户需求，不同于<br/>手机 App 的地方在于，语音技能使用语音作为交互的入口。
          </div>
          <a>查看文档</a>
        </div> -->
        </div>
        <div class="mgt24">
          <el-pagination
            ref="pagination"
            v-if="showPagination"
            class="txt-al-c"
            @current-change="getQaPair"
            :current-page="tableData.page"
            :page-size="tableData.size"
            :total="tableData.total"
            :layout="pageLayout"
          >
          </el-pagination>
        </div>
      </div>
    </div>
    <!-- <page-leave-tips :dialog="leaveDialog" /> -->
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import dicts from '@M/dicts'
import Upload from './uploadFile'
import SelectQa from './selectQa.vue'
import qaAnswers from './qaAnswers.vue'
import InteligientRichInput from '../skill/referSlots/inteligientRichInput.vue'

let controller

export default {
  name: 'qabank',
  data() {
    return {
      qaId: '',
      baseQaInfo: {},
      qaInfo: {},
      isNativeAccount: false,
      isOpenRepo: false,
      editingName: false,
      type: '',
      searchVal: '',
      show: false, // test
      editingTheme: {},
      selectedArray: [],
      selectMultiStyle: [],
      //checked: false,
      emojiType: dicts.getDictArr('answerEmotion'),
      styleType: dicts.getDictArr('answerStyle'),
      answerStyleRender: dicts.getDictArr('answerStyleRender'),
      tableData: {
        loading: true,
        total: 0,
        page: 1,
        size: 10,
        list: [],
      },
      oldList: [],
      leaveDialog: {
        show: false,
      },
      cover: {
        isCover: true,
        text: '批量覆盖',
      },
      addOnly: {
        isCover: false,
        text: '批量追加',
      },
      quesTextReg: /^\s*[\s\S]{0,128}\s*$/,
      answerTextReg: /^\s*[\s\S]{0,1000}\s*$/,
      structureLoading: false,
      checkCount: 0,
      // 是否开启虚拟人开关
      avatar: true,
      aiLoading: false,
      // 控制header_right元素的显示
    }
  },
  computed: {
    showPagination() {
      return this.tableData.total > this.tableData.size
    },
    pageLayout() {
      if (this.tableData.total / this.tableData.size > 7) {
        return 'prev, pager, next, jumper'
      }
      return 'prev, pager, next'
    },
    ...mapGetters({
      userInfo: 'user/userInfo',
      limitCount: 'aiuiApp/limitCount',
    }),
  },
  created() {
    if (this.$route.params.qaId) {
      this.qaId = this.$route.params.qaId
      this.getData()
      this.getQaPair()
    } else {
      this.$router.push({
        name: 'studio-handle-platform-qabanks',
        query: { type: 'sentence' },
      })
    }
  },
  methods: {
    onAiMagicClick(theme) {
      console.log('theme---', theme)
      controller = new AbortController()
      let self = this
      if (self.editingTheme.questionList.length === 0) {
        return this.$message.warning('请先添加问题')
      }
      let param = {
        repoId: this.qaInfo.repositoryId,
        questions: theme.questionList.map((item) => item.question),
      }
      if (theme.topic) {
        param.topic = theme.topic
      }

      this.aiLoading = true

      this.$utils.httpPost(
        this.$config.api.STUDIO_QA_REWRITE,
        JSON.stringify(param),
        {
          config: {
            headers: {
              'Content-Type': 'application/json',
            },
            signal: controller.signal,
          },
          success: (res) => {
            // self.$message.success('扩写成功')
            if (res.flag) {
              const aiQuestions = (res.data.questions || []).map((item) => {
                return {
                  question: item,
                  ai: true,
                  questionKey: this.$utils.experienceUid(),
                }
              })
              self.editingTheme.questionList = [
                ...aiQuestions,
                ...self.editingTheme.questionList,
              ]
              const len = aiQuestions.length
              self.$message.success(
                len > 0
                  ? `为您扩写了${len}条问题`
                  : `扩写完成，没有生成新的问题`
              )
              self.aiLoading = false
            } else {
              self.$message.error(res.desc)
              self.aiLoading = false
            }
            console.log(res)
          },
          error: (err) => {
            self.aiLoading = false
          },
        }
      )
    },
    onAvatarSwitchChange(val) {
      let self = this
      if (!this.qaInfo.name) {
        return self.$message.warning('问答库名称不能为空')
      }

      if (this.qaInfo.name.length > 32) {
        return self.$message.warning('问答库名称长度不能超过32个字符')
      }
      let reg = /^[a-zA-Z0-9_\u4e00-\u9fff]+$/
      if (!reg.test(this.qaInfo.name)) {
        return self.$message.warning('问答库名称仅支持汉字/字母/数字/下划线')
      }

      this.$utils.httpPost(
        this.$config.api.STUDIO_QA_CREATE_EDIT,
        {
          repoId: this.qaInfo.repositoryId,
          name: this.qaInfo.name,
          avatar: val ? 1 : 0,
          type: 0, //type: 自定义问答 0, 开放问答 1, 知识库 2
        },
        {
          success: (res) => {
            self.$message.success('修改成功')
            self.getData()
          },
          error: (err) => {},
        }
      )
    },
    selectAll(item) {
      item.styles = []
      item.style = 0
      if (item.checked) {
        for (let i = 0; i < this.styleType.length; i++) {
          item.style += this.styleType[i].key
          item.styles.push(this.styleType[i].key)
        }
      } else {
        item.styles = []
      }
      this.$forceUpdate() // 强制页面局部刷新，避免数据已更新而页面不刷新。
    },
    changeSelect(item) {
      item.style = 0
      for (let i = 0; i < item.styles.length; i++) {
        item.style += item.styles[i]
      }
      if (item.styles.length === this.styleType.length || item.style === 7) {
        item.checked = true
      } else {
        item.checked = false
      }
      this.$forceUpdate() // 强制页面局部刷新，避免数据已更新而页面不刷新。
    },
    setLoad(val) {
      this.tableData.loading = val
    },
    // 校验当前用户是否是本地用户，即是否渲染多风格下拉选项列表
    chechIsNativeAccount() {
      let self = this
      this.$utils.httpGet(
        this.$config.api.STUDIO_QA_CHECK_IS_NATIVE_ACCOUNT,
        {
          repoId: this.qaId,
        },
        {
          success: (res) => {
            self.isNativeAccount = res.data.isNativeAccount
            self.isOpenRepo = res.data.isOpenRepo
          },
          error: (err) => {},
        }
      )
    },
    getData() {
      this.chechIsNativeAccount()
      let self = this
      // 先隐藏header_right元素
      this.$utils.httpGet(
        this.$config.api.STUDIO_QA_INFO,
        {
          repoId: this.qaId,
        },
        {
          success: (res) => {
            self.qaInfo = res.data
            self.avatar = res.data.avatar == 1
            self.baseQaInfo = JSON.parse(JSON.stringify(res.data))
          },
          error: (err) => {
            self.$router.push({
              name: 'studio-handle-platform-qabanks',
              query: { type: 'sentence' },
            })
          },
        }
      )
    },
    getQaPair(page, topic) {
      let self = this
      this.tableData.loading = true
      // 先隐藏header_right元素
      this.$utils.httpGet(
        this.$config.api.STUDIO_QA_PAIR_GET,
        {
          repoId: this.qaId,
          query: this.searchVal,
          queryRange: this.type,
          pageIndex: page || this.tableData.page,
          pageSize: this.tableData.size,
        },
        {
          success: (res) => {
            self.oldList = JSON.parse(JSON.stringify(res.data.topicList))
            if (topic) {
              // 展开topic
              self.tableData.list = (res.data.topicList || []).map((item) => {
                return {
                  ...item,
                  more: item.topic === topic,
                }
              })
            } else {
              self.tableData.list = res.data.topicList || []
            }

            self.tableData.total = res.data.count
            self.tableData.page = res.data.pageIndex
            self.tableData.size = res.data.pageSize
            self.tableData.loading = false

            //  确保，以新的数据渲染多选框
            for (let j = 0; j < self.tableData.list.length; j++) {
              if (self.tableData.list[j].answerList) {
                for (
                  let i = 0;
                  i < self.tableData.list[j].answerList.length;
                  i++
                ) {
                  var style = self.tableData.list[j].answerList[i].style
                  let arr = []
                  if (style) {
                    let byteStr = style.toString(2) // 十进制转二进制
                    let n = 0
                    for (let k = byteStr.length; k > 0; k--) {
                      let s = byteStr.substring(k - 1, k)
                      if (s !== '0') {
                        arr.push(parseInt(s) << n)
                      }
                      n++
                    }
                    self.tableData.list[j].answerList[i].styles = arr
                  }
                  if (arr.length === 3) {
                    self.tableData.list[j].answerList[i].checked = true
                  } else {
                    self.tableData.list[j].answerList[i].checked = false
                  }
                }
              }
            }

            console.log(JSON.stringify(self.tableData))

            self.$nextTick(() => {
              self.$refs.scrollDiv.scrollTop = 0
            })
          },
          error: (err) => {},
        }
      )
    },
    // 返回
    back() {
      this.$router.push({
        name: 'studio-handle-platform-qabanks',
        query: { type: 'sentence' },
      })
      // if (this.$utils.isEqual(this.tableData.list, this.oldList)) {
      //
      // } else {
      //   this.leaveDialog.show = true
      // }
    },
    toEditName() {
      this.editingName = true
      this.$nextTick(function () {
        this.$refs['qaNameInput'] && this.$refs['qaNameInput'].focus()
      })
    },
    // 编辑问答库名
    editQabankName() {
      let self = this
      if (!this.qaInfo.name) {
        return self.$message.warning('问答库名称不能为空')
      }
      if (this.qaInfo.name === this.baseQaInfo.name) {
        this.editingName = false
        return
      }
      if (!this.editingName) {
        return
      }
      if (this.qaInfo.name.length > 32) {
        return self.$message.warning('问答库名称长度不能超过32个字符')
      }
      let reg = /^[a-zA-Z0-9_\u4e00-\u9fff]+$/
      if (!reg.test(this.qaInfo.name)) {
        return self.$message.warning('问答库名称仅支持汉字/字母/数字/下划线')
      }
      this.$utils.httpPost(
        this.$config.api.STUDIO_QA_CREATE_EDIT,
        {
          repoId: this.qaInfo.repositoryId,
          name: this.qaInfo.name,
          type: 0, //type: 自定义问答 0, 开放问答 1, 知识库 2
        },
        {
          success: (res) => {
            self.$message.success('修改成功')
            self.getData()
            self.editingName = false
          },
          error: (err) => {},
        }
      )
    },
    // 取消编辑问答库名
    cancelEditQabankName() {
      this.editingName = false
      this.qaInfo = this.$deepClone(this.baseQaInfo)
    },
    // 批量操作
    handleCommand(command) {
      let self = this
      switch (command) {
        case 'cover':
          break
        case 'questioning':
          break
        case 'export':
          this.$utils.postopen(this.$config.api.STUDIO_QA_EXCEL_EXPORT, {
            repoId: this.qaId,
          })
          break
        case 'down':
          window.open(
            this.userInfo && this.userInfo.uid === **********
              ? 'https://aiui-file.cn-bj.ufileos.com/qa_export_inner.xlsx'
              : 'https://aiui-file.cn-bj.ufileos.com/qa_export.xlsx',
            '_self'
          )
          break
        default:
          break
      }
    },
    // 重置
    resetSearch() {
      this.searchVal = ''
      this.type = ''
      this.getQaPair(1)
    },
    // 打开创建主题
    openCreateTheme() {
      let self = this
      if (this.aiLoading) {
        controller.abort()
        this.aiLoading = false
      }
      let newTheme = {
        topicName: '',
        answerList: [],
        questionList: [],
      }
      if (this.tableData.list[0]) {
        if (this.tableData.list[0].topic) {
          this.tableData.list.unshift(newTheme)
        }
      } else {
        this.tableData.list.unshift(newTheme)
      }
      this.editingTheme = this.tableData.list[0]
      this.$nextTick(function () {
        self.$refs['qaValueInput0'] && self.$refs['qaValueInput0'][0].focus()
      })
    },
    // 删除主题
    delTheme(theme) {
      let self = this
      this.tableData.loading = true
      this.$utils.httpPost(
        this.$config.api.STUDIO_QA_PAIR_DEL,
        {
          repoId: this.qaId,
          topic: theme.topic,
        },
        {
          success: (res) => {
            self.$message.success('删除成功')
            if (self.tableData.list.length === 1 && self.tableData.page > 1) {
              self.tableData.page -= 1
            }
            self.getData()
            self.getQaPair()
          },
          error: (err) => {},
        }
      )
    },
    // 展开
    showMore(theme, index) {
      theme.more = !theme.more
      this.$set(this.tableData.list, index, theme)
    },
    // 编辑界面
    toEdit(theme) {
      if (this.aiLoading) {
        controller.abort()
        this.aiLoading = false
      }
      this.editingTheme = theme
      for (let i = 0; i < theme.answerList.length; i++) {
        var style = theme.answerList[i].style
        if (!style) continue
        let byteStr = style.toString(2) // 十进制转二进制
        let arr = []
        let n = 0
        for (let j = byteStr.length; j > 0; j--) {
          let s = byteStr.substring(j - 1, j)
          if (s !== '0') {
            arr.push(parseInt(s) << n)
          }
          n++
        }
        theme.answerList[i].styles = arr
      }
      //TODO 尚未设置多风格的开放式问答，在点击编辑时是否默认选中"正式"风格
      /*let answerList = this.editingTheme.answerList
        for (let i = 0; i < answerList.length; i++) {
          if(!answerList[i].style) answerList[i].style = 1;
        }*/
    },
    // 添加问题
    addQues(item) {
      // 数组前插
      this.editingTheme.questionList = [
        { question: item, questionKey: this.$utils.experienceUid() },
        ...this.editingTheme.questionList,
      ]
    },
    editQues(item, index) {
      item.question = item.question.trim()
    },
    delQues(item, index) {
      this.editingTheme.questionList = this.editingTheme.questionList.filter(
        (ques, quesIndex) => quesIndex != index
      )
    },
    // 添加答案
    // addAnswer(item) {
    //   let answerList = this.editingTheme.answerList
    //   answerList.push({
    //     answer: item,
    //     emotion: 'default',
    //     //style: 0,
    //     styles: [],
    //   })
    // },
    // editAnswer(item, index) {
    //   item.answer = item.answer.trim()
    // },
    // delAnswer(item, index) {
    //   let answerList = Array.prototype.filter.call(
    //     this.editingTheme.answerList,
    //     function (answer, answerIndex) {
    //       return answerIndex != index
    //     }
    //   )
    //   this.editingTheme.answerList = answerList
    // },
    cancelEditQa(index) {
      if (this.aiLoading) {
        controller.abort()
        this.aiLoading = false
      }
      if (this.tableData.list[index].topic) {
        let originTopicData = this.oldList.find(
          (item) => item.topic === this.tableData.list[index].topic
        )
        this.$set(this.tableData.list, index, this.$deepClone(originTopicData))
        if (this.tableData.list[0].topic) {
          this.editingTheme = {}
        } else {
          this.editingTheme = this.tableData.list[0]
        }
      } else {
        this.tableData.list.shift()
        this.editingTheme = {}
      }
    },
    saveQa() {
      let self = this

      let data = {}
      let questionList = this.editingTheme.questionList.map((it) => {
        let item = { ...it }
        delete item.questionKey
        delete item.ai
        return item
      })
      if (this.editingTheme.topic) {
        data = {
          topic: this.editingTheme.topic,
          topicName: this.editingTheme.topicName,
          answerList: this.editingTheme.answerList,
          questionList,
        }
      } else {
        data = {
          topicName: this.editingTheme.topicName,
          answerList: this.editingTheme.answerList,
          questionList,
        }
      }
      if (data.questionList && data.questionList.length <= 0) {
        return this.$message.warning('请添加至少一个问题')
      }
      if (data.answerList && data.answerList.length <= 0) {
        return this.$message.warning('请添加至少一个答案')
      }

      if (this.aiLoading) {
        return this.$message.warning('小主请留步，AI扩写马上就好！')
      }

      for (let i = 0; i <= data.questionList.length - 1; i++) {
        data.questionList[i].question = data.questionList[i].question.replace(
          /<(i|b|h|a|d|\/)[^>]*>/gi,
          ''
        )
        if (data.questionList[i].question.length > 128) {
          return this.$message.warning('问题不能有超过128个字符')
        }
      }

      for (let i = 0; i <= data.answerList.length - 1; i++) {
        // if (this.$utils.hasDocument(data.answerList[i].answer)) {
        //   return this.$message.warning(`第${i+1}条答案中不能包含html元素标签`)
        // }
        data.answerList[i].answer = data.answerList[i].answer.replace(
          /<(i|b|h|a|d|\/)[^>]*>/gi,
          ''
        )
        if (data.answerList[i].answer.length > 1000) {
          return this.$message.warning('答案不能有超过1000个字符')
        }
      }
      data.answerList = (data.answerList || []).map((item) => {
        return {
          ...item,
          labels: (item.labels || []).map(({ picture, zhName, ...rest }) => {
            return { ...rest }
          }),
        }
      })
      this.tableData.loading = true
      this.$utils.httpPost(
        this.$config.api.STUDIO_QA_PAIR_ADD_UPDATE,
        {
          repoId: this.qaId,
          param: JSON.stringify(data),
        },
        {
          success: (res) => {
            self.$message.success('保存成功')
            const topic = self.editingTheme.topic
            self.editingTheme = {}
            self.getData()
            self.getQaPair(1, topic)
          },
          error: (err) => {
            self.tableData.loading = false
          },
        }
      )
    },

    setQaAnswers(val) {
      this.editingTheme.answerList = val
    },
    // 构建
    structure() {
      let that = this
      this.structureLoading = true
      // 调用接口构建
      this.$utils.httpPost(
        this.$config.api.STUDIO_QA_STRUCT_OR_PUBLISH,
        {
          repositoryId: this.qaId,
        },
        {
          success: (res) => {
            // 构建接口调用后，还需要轮询检查状态
            that.$message.success('提交成功，正在构建...')
            that.checkStatus(res.data.id)
          },
          error: (err) => {
            that.structureLoading = false
          },
        }
      )
    },

    // 轮询检查状态
    checkStatus(id) {
      let self = this
      this.checkCount += 1
      this.$utils.httpGet(
        this.$config.api.STUDIO_QA_STRUCT_OR_PUBLISH_STATUS,
        {
          id,
        },
        {
          success: (res) => {
            if (String(res.data.ok) === '1') {
              if (self.structureLoading) {
                self.$message.success('构建成功')
              }
              self.structureLoading = false
              self.checkCount = 0

              // 构建成功刷新下列表接口，便于前端展示最新构建状态
              self.getQaPair(1)
            } else if (String(res.data.ok) === '-1') {
              if (self.structureLoading) {
                self.$message.error('构建失败')
              }
              self.structureLoading = false
              self.checkCount = 0
            } else {
              if (self.checkCount < 300) {
                setTimeout(function () {
                  self.checkStatus(id)
                }, 2000)
              } else {
                if (self.structureLoading) {
                  self.$message.error('构建失败')
                }
                self.structureLoading = false
                self.checkCount = 0
              }
            }
          },
          error: (err) => {},
        }
      )
    },
  },
  components: {
    Upload,
    SelectQa,
    qaAnswers,
    InteligientRichInput,
  },
}
</script>

<style lang="scss">
// .el-checkbox {
//   text-align: right;
//   width: 100%;
//   padding-right: 10px;
// }
.qabank-page {
  // max-width: 1200px;
  // padding-top: 14px;
  margin: auto;
  height: 100%;
  .ic-r-edit {
    color: $primary;
  }
  &-form-input {
    width: 220px;
  }
  &-form-save,
  &-form-cancel {
    font-size: 18px;
    margin-left: 8px;
    cursor: pointer;
    &:hover {
      color: $primary;
    }
  }
  &-head {
    padding: 0 30px;
    font-size: 18px;
    height: 63px;
    // margin-bottom: 14px;
    // background: #f7f8fa;
    display: flex;
    align-items: center;
    color: #545556;
    &-back {
      cursor: pointer;
      margin-right: 16px;
      color: $grey4;
    }
    &-title {
      flex: auto;
      display: flex;
      align-items: center;
    }
    &-title-select {
      display: flex;
      align-items: center;
      cursor: pointer;
      margin-right: 16px;
      i {
        padding-left: 8px;
        color: $grey5;
      }
    }
  }
  &-handle-bar {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
    gap: 8px;
    margin-bottom: 16px;
    .search-area {
      width: 450px;
      margin-right: 8px;
      font-size: 14px;
    }
    .el-select {
      width: 90px;
    }
  }
  &-empty {
    text-align: center;
    margin-top: 92px;
    &-thumb {
      width: 120px;
      height: 120px;
      border-radius: 100%;
      background-color: $grey1;
      margin: auto;
      margin-bottom: 24px;
    }
    &-main {
      margin-bottom: 24px;
      span,
      a {
        font-size: 16px;
        font-weight: 600;
      }
    }
    &-desc {
      margin-bottom: 8px;
    }
  }

  &-theme {
    position: relative;
    border: 1px solid $grey2;
    border-radius: 4px;
    padding: 20px;

    &:hover &-del {
      display: block;
    }
    &-list {
      // margin-bottom: 56px;
      max-height: calc(100vh - 227px);
      overflow-y: auto;
      background-color: #fff;
    }
    &-angle {
      position: absolute;
      color: $grey4;
      left: 24px;
      top: 24px;
      &-active {
        transform: rotate(180deg);
      }
    }
    &-del {
      position: absolute;
      color: $grey4;
      right: 48px;
      top: 24px;
      font-size: 20px;
      cursor: pointer;
      display: none;
    }
    &-head {
      // width: 85%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 16px;
      color: #262626;
      i {
        margin-left: 4px;
      }
    }
    &-head-name {
      font-size: 16px;
      font-weight: 500;
      display: inline-block;
      max-width: 400px;
      margin-right: 8px;
    }
    &-head-input {
      width: 496px;
    }
    &-content {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      &-ul {
        display: flex;
        flex-direction: column;
        // align-items: center;
        min-width: 500px;
        margin-bottom: 10px;
        flex: 1;
        // display: inline-flex;
        // padding-right: 48px;
        > li {
          flex: 1;
          margin-top: 5px;
          span {
            word-break: break-all;
          }
        }
      }
      label {
        color: #545556;
        font-size: 14px;
        font-weight: 500;
        padding-right: 24px;
        flex: none;
      }
      &-emotion {
        width: 70px;
        input {
          border: 0;
          padding: 0 !important;
        }
        .el-input__suffix {
          right: 0;
        }
      }
    }
    &-footer {
      margin-top: 24px;
      text-align: right;
    }
  }
  &-theme + &-theme {
    margin-top: 16px;
  }

  &-hight-light {
    background-color: $success-light-30;
  }
}
</style>

<style lang="scss" scoped>
.os_scroll {
  overflow-y: hidden !important;
  background-color: $white-grey !important;
}
ul,
p {
  margin-bottom: 0;
}
.search-block {
  float: right;
}
@media screen and (max-width: 1801px) {
  .search-block {
    float: unset;
    // margin-top: 10px;
  }
}
.answer-outer-wrap {
  background: #f7f8fa;
  border-radius: 4px;
  padding-right: 20px;
}
.answer-wrap {
  display: flex;
  align-items: center;
  .serial-number {
    align-self: flex-start;
    margin-top: 12px;
  }
}
.answer-tag {
  white-space: nowrap;
}

.question-wrap {
  background: #f7f8fa;
  border-radius: 4px;
  > div {
    line-height: 40px;
    display: flex;
    align-items: center;
  }
}
.edit-question-wrap {
  position: relative;
  .magic-icon {
    position: absolute;
    cursor: pointer;
    z-index: 1;
    right: 30px;
    top: 21px;
    color: #797979;
    cursor: default;
    &.active {
      color: $primary;
      cursor: pointer;
    }
    :deep(.svg-icon) {
      width: 19px;
      height: 19px;
    }
  }
}
// .qabank-page-head {
//   .el-icon-question {
//     &::before {
//       color: $primary;
//     }
//   }
// }
.theme-edit-btns {
  display: flex;
  align-items: center;

  li {
    display: flex;
    align-items: center;
    padding: 0 13px;
    cursor: pointer;
    i {
      margin-right: 7px;
      font-size: 16px;
      &::before {
        color: #b1b1b1;
      }
    }
    span {
      color: #262626;
    }
  }

  li:not(:last-child) {
    border-right: 1px solid #c2c2c2;
  }
}

.serial-number {
  width: 26px;
  height: 17px;
  background: #ffffff;
  border-radius: 2px;
  box-shadow: 0px 2px 10px 0px rgba(154, 164, 179, 0.27);

  font-size: 12px;
  color: #b1b1b1;
  line-height: 17px;
  text-align: center;
  margin: 0 16px 0 20px;
}

.status-tag {
  height: 22px;
  line-height: 22px;
  border-radius: 4px;
  text-align: center;
  font-size: 12px;
  padding: 0 6px;
  &.status-warning {
    color: #eb7527;
    background: #ffece1;
  }
  &.status-publishing {
    color: #255cdb;
    background: #e4ecff;
  }
  &.status-success {
    color: #318325;
    background: #e2f4e0;
  }
}

.question-title-wrap {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .question-ai-item {
    font-size: 12px;
    font-weight: 500;
    color: #bfbfbf;
    line-height: 17px;
  }
}

.theme-editting {
  background: #f7f8fa;
}

.theme-head-wrap {
  display: flex;
  align-items: center;
}
</style>
