<template>
  <os-page :options="pageOptions" @returnCb="returnCb">
    <div class="s-content">
      <div class="div-content">
        <div>
          <ul>
            <li></li>
            <li>
              <ul class="ul-border">
                <li>
                  {{ orderData.statuDesc }}
                </li>
              </ul>
              <ul v-if="orderData.statu === 0">
                <li>
                  {{ '提问处理中，讯飞工程师会在3个工作日内回复您' }}
                </li>
              </ul>
              <ul class="ul-or-ti">
                <li>
                  <p v-html="`${'工单号：' + orderData.code}`"></p>
                </li>
                <li>
                  <p v-html="`${'提交时间：' + orderData.createtime}`"></p>
                </li>
              </ul>
            </li>
          </ul>
        </div>
        <div>
          <ul>
            <li>
              <img
                class="ic-r-tip-ask"
                src="../../../assets/images/aiui/question.png"
              />
            </li>
            <li>
              <ul>
                <li>
                  {{ '您' }}
                </li>
              </ul>
              <ul>
                <li>
                  {{ orderData.title }}
                </li>
              </ul>
              <ul>
                <li>
                  {{ orderData.detail }}
                </li>
              </ul>
              <ul
                v-for="(item, index_a) in orderDataAttachments"
                :key="index_a"
              >
                <li>
                  <a
                    :href="item.url"
                    target="_blank"
                    :download="item.name"
                    :title="'文件大小字节：' + item.size"
                  >
                    {{ item.name }}
                  </a>
                </li>
              </ul>
              <ul>
                <li>
                  <p v-html="orderData.createtime"></p>
                </li>
              </ul>
            </li>
          </ul>
        </div>
        <div v-for="(message, index) in orderData.message" :key="index">
          <ul>
            <li>
              <img
                class="ic-r-tip-ask"
                v-if="message.opt === 1"
                src="../../../assets/images/aiui/question.png"
              />
              <os-skill-simple-item
                v-else
                :showName="false"
                class="skills-page-skill-zh-name"
                :name="message.optName || '-'"
              />
            </li>
            <li>
              <ul>
                <li>
                  {{ message.optName ? message.optName : '您' }}
                </li>
              </ul>
              <ul>
                <li>
                  <p v-html="message.content"></p>
                  <!--<vue-ueditor-wrap v-model="message.content" :config="myConfig"></vue-ueditor-wrap>-->
                </li>
              </ul>
              <ul v-for="(item, index_i) in message.attachments" :key="index_i">
                <li>
                  <a
                    :href="item.url"
                    target="_blank"
                    :download="item.name"
                    :title="'文件大小字节：' + item.size"
                  >
                    {{ item.name }}
                  </a>
                </li>
              </ul>
              <ul>
                <li>
                  <p v-html="message.createtime"></p>
                </li>
              </ul>
            </li>
          </ul>
        </div>
      </div>
      <a-form
        :form="form"
        :label-col="{ span: 5 }"
        :wrapper-col="{ span: 12 }"
        @submit="handleSubmit"
      >
        <div
          class="div-answer"
          v-if="showReply || orderData.statu === 0 || orderData.statu === 1"
        >
          <a-form-item :wrapper-col="{ span: 24, offset: 0 }">
            <a-textarea
              class="fb_content_main"
              style="width: 100%; height: 100%"
              placeholder="请具体描述您遇到的问题，我们将全力为您解答！限定200字。"
              :rows="4"
              :maxLength="200"
              v-decorator="[
                'content',
                { rules: [{ required: true, message: '请输入需求内容' }] },
              ]"
            />
            <div class="div-upload">
              <a-upload
                :action="`${
                  this.$utils.BaseURI +
                  this.$config.api.WORKER_ORDER_UPLOAD_POST
                }`"
                :file-list="defaultFileList"
                :beforeUpload="beforeUpload"
                :remove="handleRemove"
                @change="handleChange"
                :headers="{ 'X-Csrf-Token': token }"
              >
                <a-button> <a-icon type="upload" />文件上传</a-button>
                <div class="el-upload__tip">
                  上传附件支持jpg、png、txt、zip、rar、doc、xls格式，最大不超过5M
                </div>
              </a-upload>
            </div>
          </a-form-item>
        </div>
        <div class="div-submit">
          <a-form-item
            :wrapper-col="{ span: 24, offset: 0 }"
            class="div-submit-form"
          >
            <ul>
              <li
                v-if="
                  showReply || orderData.statu === 0 || orderData.statu === 1
                "
              >
                <a-button type="primary" html-type="submit">
                  提交追问
                </a-button>
              </li>
              <li v-else-if="orderData.statu === 2">
                <div>
                  <a-button type="primary" html-type="submit">
                    确认结单
                  </a-button>
                  <span style="left: 2%; position: relative">
                    如未解决问题，请<a @click="switchReply">继续留言</a>
                  </span>
                </div>
              </li>
            </ul>
          </a-form-item>
        </div>
      </a-form>
    </div>
  </os-page>
</template>

<script>
import Form from 'ant-design-vue/lib/form'
import Input from 'ant-design-vue/lib/input'
import Button from 'ant-design-vue/lib/button'
import Icon from 'ant-design-vue/lib/icon'
import Upload from 'ant-design-vue/lib/upload'
import 'ant-design-vue/lib/icon/style/css'
import 'ant-design-vue/lib/form/style/css'
import 'ant-design-vue/lib/input/style/css'
import 'ant-design-vue/lib/button/style/css'
import 'ant-design-vue/lib/upload/style/css'
export default {
  name: 'detail',
  data() {
    return {
      pageOptions: {
        title: '问题详情',
        loading: false,
        returnBtn: true,
        screen: true,
      },
      list: {
        total: 1,
        pageIndex: 1,
        pageSize: 10,
        list: [],
      },
      fileList: [],
      orderData: {},
      defaultFileList: [],
      attachment_names: [],
      attachment_sizes: [],
      attachment_urls: [],
      form: this.$form.createForm(this, { name: 'detail' }),
      showReply: false,
      token: localStorage.getItem('AIUI_GLOBAL_VARIABLE'),
    }
  },
  computed: {
    id() {
      return this.$route.params.id
    },
    orderDataAttachments() {
      return this.orderData.attachments
    },
  },
  created() {
    this.getOrderDetail()
  },
  methods: {
    downloadSave(name, href) {
      var xhr = false
      if (window.XMLHttpRequest) xhr = new XMLHttpRequest()
      else if (window.ActiveXObject) {
        xhr = new ActiveXObject('Microsoft.XMLHttp')
      }
      xhr.open('GET', href, true)
      if (localStorage.getItem('AIUI_GLOBAL_VARIABLE')) {
        xhr.setRequestHeader(
          'X-Csrf-Token',
          localStorage.getItem('AIUI_GLOBAL_VARIABLE')
        )
      }
      xhr.responseType = 'blob'
      xhr.onload = function (e) {
        //会创建一个 DOMString，其中包含一个表示参数中给出的对象的URL。这个 URL 的生命周期和创建它的窗口中的 document 绑定。这个新的URL 对象表示指定的 File 对象或 Blob 对象。
        var url = window.URL.createObjectURL(xhr.response)
        var a = document.createElement('a')
        a.href = url
        a.download = name
        a.click()
      }
      xhr.send()
    },
    switchReply() {
      this.showReply = true
    },
    getOrderDetail() {
      this.$utils.httpGet(
        this.$config.api.WORKER_ORDER_DETAIL_GET,
        {
          id: this.id,
        },
        {
          success: (res) => {
            if (res.code == 0 && res.flag) {
              res.data = {
                ...res.data,
                message: res.data.message.reverse(),
              }
              this.orderData = res.data
            } else {
              this.$message.error(res.desc)
            }
          },
          error: (err) => {},
        }
      )
    },
    handleSubmit(e) {
      e.preventDefault()
      this.form.validateFields((err, values) => {
        if (!err) {
          delete values.typeId
          this.$utils.httpPost(
            this.$config.api.WORKER_ORDER_REPLY_POST,
            {
              id: this.id,
              opt: this.orderData.statu === 2 ? 1 : 0,
              attachment_names: this.attachment_names.join(','),
              attachment_sizes: this.attachment_sizes.join(','),
              attachment_urls: this.attachment_urls.join(','),
              content: this.orderData.statu === 2 ? '确认结单' : null,
              ...values,
            },
            {
              success: (res) => {
                if (res.code == 0 && res.flag) {
                  this.$message.success('提交成功')
                  this.form.resetFields()
                  this.defaultFileList = []
                  this.attachment_names = []
                  this.attachment_sizes = []
                  this.attachment_urls = []
                  if (this.showReply || this.orderData.statu !== 2) {
                    this.getOrderDetail()
                  } else {
                    this.$router.push({ name: 'ask' })
                  }
                } else {
                  this.$message.success
                }
              },
              error: (err) => {},
            }
          )
        } else {
          var a = document.getElementsByClassName('s-content')
          a.length &&
            a[0].scrollIntoView({
              behavior: 'smooth',
              block: 'bottom',
              inline: 'center',
            })
        }
      })
    },
    returnCb() {
      this.$router.push({ name: 'ask' })
      // window.location.href = '/ask'
    },
    handleChange({ file, fileList, event }) {
      let thiz = this
      if (file.status === 'done') {
        if (file.response.flag) {
          thiz.attachment_names.push(file.name)
          thiz.attachment_sizes.push(file.size)
          thiz.attachment_urls.push(file.response.data.url)
        }
      }
    },
    handleRemove(file) {
      const index = this.defaultFileList.indexOf(file)
      const newFileList = this.defaultFileList.slice()
      newFileList.splice(index, 1)
      this.defaultFileList = newFileList

      const attachment_names_ = this.attachment_names.slice()
      attachment_names_.splice(index, 1)
      this.attachment_names = attachment_names_

      const attachment_sizes_ = this.attachment_sizes.slice()
      attachment_sizes_.splice(index, 1)
      this.attachment_sizes = attachment_sizes_

      const attachment_urls_ = this.attachment_urls.slice()
      attachment_urls_.splice(index, 1)
      this.attachment_urls = attachment_urls_
    },
    beforeUpload(file) {
      const isImage =
        file.type === 'image/jpeg' ||
        file.type === 'image/png' ||
        file.type === 'text/plain' ||
        file.type ===
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
        file.type ===
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||
        file.type === 'application/vnd.ms-excel' ||
        file.type === 'application/msword' ||
        //|| file.type === 'application/x-rar-compressed,application/zip,application/x-zip-compressed'
        file.type === 'application/zip' ||
        (file.type === '' &&
          file.name.substr(file.name.lastIndexOf('.')).toLowerCase() ===
            '.zip') ||
        (file.type === '' &&
          file.name.substr(file.name.lastIndexOf('.')).toLowerCase() === '.rar')
      if (!isImage) {
        this.$message.error('文件格式错误')
        return
      }

      const sizeLt = file.size < 1024 * 1024 * 5
      // / 1024 / 1024 < 2;
      if (!sizeLt) {
        this.$message.error('文件大小不能超过5MB')
        return
      }
      if (!(sizeLt && isImage)) {
        const index = this.defaultFileList.indexOf(file)
        const newFileList = this.defaultFileList.slice()
        if (index > -1) {
          newFileList.splice(index, 1)
        }
        this.defaultFileList = newFileList
      } else {
        file = {
          ...file,
          name: file.name,
          size: file.size,
          type: file.type,
        }
        this.defaultFileList = [...this.defaultFileList, file]
      }

      return sizeLt && isImage
    },
  },
  components: {
    [Form.name]: Form,
    [Form.Item.name]: Form.Item,
    [Input.name]: Input,
    [Button.name]: Button,
    [Input.TextArea.name]: Input.TextArea,
    [Upload.name]: Upload,
    [Icon.name]: Icon,
  },
}
</script>

<style scoped lang="scss">
.ic-r-tip-ask {
  position: relative;
  margin-top: 15% !important;
}
.div-upload {
  border: 1px solid $grey3;
  border-bottom-right-radius: 5px;
  border-bottom-left-radius: 5px;
  background-color: $grey1;
  padding: 2%;
}
.div-submit {
  margin-top: 2%;
}
.div-answer {
  position: relative;
  margin-top: 2%;
  width: 100%;
  display: flex;
  :deep(> *) {
    flex: 1;
    width: 100% !important;
    position: relative;
  }
}
.div-content {
  position: relative;
  width: 100%;
  margin-top: 2% !important;
  border: 1px solid $grey3;
  > div:nth-child(1) > ul > li:nth-child(2) > ul:first-child > li:nth-child(1) {
    border-left: 4px solid #2989ee;
    position: relative;
    text-indent: 3%;
    line-height: 20px;
  }
  > div:nth-child(2) > ul > li:nth-child(2) > ul:nth-child(4) > li {
    position: relative;
    margin-top: 1%;
  }
  > div {
    border-bottom: 1px solid $grey3;
    .ul-or-ti {
      display: inline-flex;
      width: 100%;
      > li:nth-child(2) {
        position: relative;
        left: 6%;
      }
    }
    > ul {
      padding-left: 3%;
      display: flex;
      margin-bottom: 0 !important;
      > li {
        justify-content: center;
        align-self: center;
        vertical-align: center;
      }
      > li:first-child {
        align-self: flex-start;
      }
      > li:nth-child(2) {
        position: relative;
        width: 100%;
        padding-left: 3%;
        > ul {
          position: relative;
          margin-top: 1%;
        }
        > ul:last-of-type {
          /*border-bottom: 1px solid $grey3;*/
        }
        > ul:nth-child(1) {
          > li {
            font-size: 1.2em;
            font-weight: 700;
          }
        }
        > ul:nth-child(2) {
          > li {
            /*font-size: 15px;*/
            color: #454545;
            word-break: break-all;
          }
        }
        > ul:nth-child(3) {
          > li {
            /*display: inline-block;*/
          }
        }
      }
    }
  }
  > div:last-of-type {
    border-bottom: none;
  }
}
</style>
