<template>
  <div style="margin-top: 16px">
    <el-card
      class="box-card"
      shadow="never"
      :body-style="{ padding: '10 20', background: '#F9F9F9' }"
    >
      <div slot="header" class="clearfix">
        <span class="header-title">技能</span>
        <div style="float: right">
          <a @click="add" style="margin-right: 14px"
            ><i class="el-icon-circle-plus-outline"></i>&nbsp;添加</a
          ><a @click="setting"><i class="el-icon-setting"></i>&nbsp;设置</a>
        </div>
      </div>
      <div class="text">
        <div class="description">
          <ul class="btm-skill-tips">
            <li v-if="skillNameList.removes.length > 0">
              <span>已下架技能：</span>
              <span v-if="skillNameList.removes.length > 3">
                <span v-for="(v, i) in Array(3)" :key="i" class="skill-cell">
                  {{ skillNameList.removes[i] }}<span v-if="i < 3 - 1">、</span>
                </span>
                等
                {{ skillNameList.removes.length }}
                个技能已下线;
              </span>
              <span v-else>
                <span
                  v-for="(v, i) in skillNameList.removes"
                  :key="i"
                  class="skill-cell"
                >
                  {{ v
                  }}<span v-if="i < skillNameList.removes.length - 1">、</span>
                </span>
              </span>
            </li>
            <li v-if="skillNameList.updates.length > 0">
              <span>已更新技能：</span>
              <span v-if="skillNameList.updates.length > 3">
                <span v-for="(v, i) in Array(3)" :key="i" class="skill-cell">
                  {{ skillNameList.updates[i]
                  }}<span v-if="i < skillNameList.updates.length - 1">、</span>
                </span>
                等
                {{ skillNameList.updates.length }}
                个技能有更新;
              </span>
              <span v-else>
                <span
                  v-for="(v, i) in skillNameList.updates"
                  :key="i"
                  class="skill-cell"
                >
                  {{ v }}
                </span>
              </span>
            </li>
            <li v-if="skillNameList.selects.length > 0">
              <span>已选择技能：</span>
              <span v-if="skillNameList.selects.length > 20">
                <span v-for="(v, i) in Array(20)" :key="i" class="skill-cell">
                  {{ skillNameList.selects[i]
                  }}<span v-if="i < 20 - 1">、</span>
                </span>
                等
                {{ skillNameList.selects.length }}
                个技能已选择;
              </span>
              <span v-else>
                <span
                  v-for="(v, i) in skillNameList.selects"
                  :key="i"
                  class="skill-cell"
                >
                  {{ v
                  }}<span v-if="i < skillNameList.selects.length - 1">、</span>
                </span>
              </span>
            </li>
            <div class="description" v-else>暂未配置技能</div>
          </ul>
        </div>
      </div>
    </el-card>
    <settingDialog
      :dialog="dialogSet"
      :appId="appId"
      :currentScene="currentScene"
      @saveSuccess="onSettingSaveSuccess"
    ></settingDialog>
    <addDialog
      :dialog="dialogAdd"
      :appId="appId"
      :currentScene="currentScene"
      @saveSuccess="onAddSaveSuccess"
    ></addDialog>
  </div>
</template>
<script>
import { mapGetters } from 'vuex'
import addDialog from './Dialog/addDialog/index.vue'
import settingDialog from './Dialog/settingDialog/index.vue'

export default {
  data() {
    return {
      dialogAdd: {
        show: false,
      },
      dialogSet: {
        show: false,
      },

      skillNameList: {
        count: 0,
        removes: [],
        selects: [],
        updates: [],
      },
    }
  },
  computed: {
    ...mapGetters({
      currentScene: 'aiuiApp/currentScene',
      appInfo: 'aiuiApp/app',
    }),
    appId() {
      return this.$route.params.appId
    },
  },
  created() {
    if (this.currentScene && this.currentScene.sceneBoxId) {
      this.getSkillConfig() // 获取配置列表
    }
  },
  watch: {
    currentScene(scene) {
      if (scene && scene.sceneBoxId) {
        this.getSkillConfig() // 获取配置列表
      }
    },
  },

  methods: {
    // 获取已配置的文档问答信息
    getSkillConfig() {
      let that = this
      this.$utils.httpGet(
        this.$config.api.AIUI_APP_SKILL_CONFIG,
        {
          appid: this.appId,
          sceneId: this.currentScene.sceneBoxId,
          point:
            this.currentScene.point.split(',').indexOf('13') >= 0 ? '13' : '2',
        },
        {
          success: (res) => {
            if (res.flag) {
              that.skillNameList = res.data
            }
          },
        }
      )
    },

    setting() {
      this.dialogSet.show = true
    },
    add() {
      this.dialogAdd.show = true
    },
    onSettingSaveSuccess() {},
    onAddSaveSuccess() {
      this.getSkillConfig()
    },
  },
  components: { addDialog, settingDialog },
}
</script>
<style lang="scss" scoped>
@import '../card.scss';
</style>
