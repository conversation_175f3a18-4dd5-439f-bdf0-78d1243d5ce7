<template>
  <div class="store-skill">
    <div
      class="skill-type-title"
      style="margin: 10px 0 5px"
      v-if="!(clickSearchVal && addSkillList.length == 0)"
    >
      <div class="empty-card title">自定义</div>
      <div class="empty-card" v-for="i in Array(8)" :key="i"></div>
    </div>

    <div
      class="flex-center"
      v-if="showMustAnswer && !(clickSearchVal && addSkillList.length == 0)"
    >
      <div
        class="must-answer-wrap empty-card"
        style="margin: 10px 0 0; padding-left: 12px"
      >
        有问必答
        <el-tooltip class="item" effect="dark" placement="right">
          <div slot="content">
            你配置了自定义问答，开启有问必答后，我们将返回得分最高的问答结果，即<br />
            使这个结果不是非常准确。如果你配置了商店技能，我们建议关闭有问必答。
          </div>
          <i class="el-icon-question" />
        </el-tooltip>
        <el-switch
          v-model="mustAnswer"
          @change="mustAnswerHandle"
          :disabled="!subAccountEditable"
        ></el-switch>
      </div>
      <!-- 用于布局样式占位 -->
      <div class="empty-card" v-for="i in Array(8)" :key="i"></div>
    </div>

    <div class="new-skill-wrapper">
      <div
        :class="[
          'skill',
          'skill-to-add',
          { 'not-allowed': !subAccountEditable },
        ]"
        @click="jump"
        v-if="!(clickSearchVal && addSkillList.length == 0) && !subAccount"
      >
        <a>
          <i
            class="AIUI-myapp-iconfont ai-myapp-add"
            style="font-size: 20px"
          ></i>
          <p style="margin-top: 10px">去创建自定义技能</p>
        </a>
      </div>
      <template v-for="(item, index) in addSkillList">
        <!-- 自定义文档问答 -4.5才有 -->
        <doc-cell
          v-if="item.skillListType === 'docKnowledge'"
          :key="index"
          :item="item"
          :currentScene="currentScene"
          :appId="appId"
          :docConfig="docConfig"
          @change="$emit('change')"
        ></doc-cell>

        <!-- 自定义技能 -->
        <sy-cell
          v-if="item.skillListType === 'sbusinessList'"
          :key="index"
          :item="item"
          :sourceConfig="sourceConfig"
          :currentScene="currentScene"
          :skillThresholdConfig="skillThresholdConfig"
          :qaThresholdConfig="qaThresholdConfig"
          :skillConfig="skillConfig"
          :appId="appId"
          :globalThresholdChange="globalThresholdChange"
          @change="$emit('change')"
        ></sy-cell>
        <!-- 自定义问答 -->
        <zs-cell
          v-if="item.skillListType === 'repositoryList'"
          :key="index"
          :item="item"
          :qaConfig="qaConfig"
          :currentScene="currentScene"
          :skillThresholdConfig="skillThresholdConfig"
          :qaThresholdConfig="qaThresholdConfig"
          :skillConfig="skillConfig"
          :appId="appId"
          :globalThresholdChange="globalThresholdChange"
          @change="$emit('change')"
          :mustAnswer="mustAnswer"
        ></zs-cell>
        <!-- 设备人设 -->
        <device-repository-cell
          v-if="item.skillListType === 'deviceRepositoryList'"
          :key="index"
          :item="item"
          :qaConfig="qaConfig"
          :hasDeviceCheckedBefore="hasDeviceCheckedBefore"
          @change="$emit('change')"
          @justCheck="handleJustCheck"
        ></device-repository-cell>

        <!-- 开放问答 -->
        <kzs-cell
          v-if="item.skillListType === 'grepositoryList'"
          :key="index"
          :item="item"
          :qaConfig="qaConfig"
          :currentScene="currentScene"
          :skillThresholdConfig="skillThresholdConfig"
          :qaThresholdConfig="qaThresholdConfig"
          :skillConfig="skillConfig"
          :appId="appId"
          :ubotQaConfig="ubotQaConfig"
          :globalThresholdChange="globalThresholdChange"
          @change="$emit('change')"
        ></kzs-cell>

        <!-- 关键词问答 -->
        <ubot-cell
          v-if="item.skillListType === 'ubotQaList'"
          :key="index"
          :item="item"
          :ubotQaConfig="ubotQaConfig"
          :currentScene="currentScene"
          :appId="appId"
          @change="$emit('change')"
        ></ubot-cell>
      </template>
      <!-- 用于布局样式占位 -->
      <!-- <div class="empty-card" v-for="i in Array(8)" :key="i"></div> -->
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import zsCell from './custom/zsCell.vue'
import syCell from './custom/syCell.vue'
import deviceRepositoryCell from './custom/deviceRepositoryCell.vue'
import ubotCell from './custom/ubotCell.vue'
import kzsCell from './custom/kzsCell.vue'

import docCell from './custom/docCell.vue'

import { cloneDeep } from 'lodash-es'

export default {
  name: 'storeSkill',
  props: {
    skillConfig: Object,
    qaConfig: Object,
    ubotQaConfig: Object,
    currentScene: Object,
    appId: String,
    sourceConfig: Object,
    skillThresholdConfig: Object,
    qaThresholdConfig: Object,
    globalThresholdChange: Boolean,
    searchVal: String,
    clickSearchVal: String,
    docConfig: Object,
  },
  computed: {
    ...mapGetters({
      rightTestOpen: 'studioSkill/rightTestOpen',
      subAccount: 'user/subAccount',
      subAccountAppAuths: 'aiuiApp/subAccountAppAuths',
    }),
    subAccountEditable() {
      return this.subAccountAppAuths[this.$route.params.appId] == 2
        ? false
        : true
    },
    subAccountHasCreateAuth() {
      return this.$store.state.aiuiApp.subAccountHasCreateAuth
    },

    showMustAnswer() {
      return (
        this.initMustAnswer == 1 &&
        this.addSkillListCopy.filter(
          (item) => item.skillListType === 'repositoryList'
        ).length > 0
      )
    },
  },
  data() {
    return {
      addSkillList: [],
      hasDeviceCheckedBefore: false,

      mustAnswer: null,
      initMustAnswer: null,
      addSkillListCopy: [],

      // 暂时先放在这里
      addSkillInfo: {
        0: {
          title: '添加技能',
          desc: '',
          key: '',
          resultKey: '',
          emptyTip: '暂无技能',
        },
        1: {
          title: '添加自定义技能',
          desc: '可以添加你在技能工作室中开发的技能',
          key: 'sbusinessList',
          resultKey: 'sy',
          emptyTip: '你还没有自定义技能',
        },
        2: {
          title: '添加自定义问答',
          desc: '可以添加你在技能工作室中开发的问答',
          key: 'repositoryList',
          resultKey: 'zs',
          emptyTip: '你还没有自定义问答',
        },
        3: {
          title: '添加商店技能',
          desc: '可以添加AIUI技能商店中的技能',
          key: 'storeSkillList',
          resultKey: 'storeSkill',
          emptyTip: '暂无商店技能',
        },
        4: {
          title: '添加开放问答',
          desc: '添加开放问答',
          key: 'grepositoryList',
          resultKey: 'kzs',
          emptyTip: '你还没有开放问答',
        },
        5: {
          title: '添加设备人设',
          desc: '添加设备人设',
          key: 'deviceRepositoryList',
          resultKey: 'deviceRepository',
          emptyTip: '你还没有设备人设',
        },
        6: {
          title: '添加关键词问答',
          desc: '添加关键词问答',
          key: 'ubotQaList',
          resultKey: 'ubotQa',
          emptyTip: '你还没有关键词问答',
        },
      },
    }
  },
  created() {
    // 商店技能
    // this.getList()
    this.getMustAnswer()
  },

  filters: {
    customType(item) {
      let keyMap = {
        deviceRepositoryList: '设备人设',
        repositoryList: '语句问答',
        ubotQaList: '关键词问答',
      }
      let skillTypeMap = {
        2: '私有技能',
        9: '开放技能',
      }
      if (keyMap[item.skillListType]) {
        return keyMap[item.skillListType]
      } else if (
        item.skillListType === 'sbusinessList' ||
        item.skillListType === 'storeSkillList'
      ) {
        return skillTypeMap[item.type]
      }
    },
  },
  methods: {
    mustAnswerHandle(val) {
      if (!val) {
        this.$confirm('有问必答即将下线，关闭后无法再次重新打开。', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(() => {
            this.$emit('change')
          })
          .catch(() => {
            this.mustAnswer = true
          })
      }
    },
    getMustAnswer() {
      let self = this
      this.$utils.httpPost(
        this.$config.api.AIUI_MUST_ANSWER,
        {
          appid: self.appId,
          sceneName: self.currentScene.sceneBoxName,
        },
        {
          success: (res) => {
            self.isOldApp = res.data.isOld ? true : false
            if (res.data.isOld) {
              self.mustAnswer = res.data.mustAnswer ? true : false
            } else {
              self.mustAnswer = res.data.mustAnswer ? true : false || false
            }
            self.initMustAnswer = self.mustAnswer
          },
          error: (err) => {
            console.log(err)
          },
        }
      )
    },
    saveMustAnswer() {
      let self = this
      if (self.initMustAnswer === self.mustAnswer) return
      this.$utils.httpPost(
        this.$config.api.AIUI_SAVE_MUST_ANSWER,
        {
          appid: self.appId,
          sceneName: self.currentScene.sceneBoxName,
          mustAnswer: self.mustAnswer ? 1 : 0,
        },
        {
          success: (res) => {
            self.getMustAnswer()
          },
          error: (err) => {
            console.error(err)
          },
        }
      )
    },
    jump() {
      window.open('/studio/skill', '_blank')
    },
    handleJustCheck(id) {
      this.addSkillList = this.addSkillList.map((item) => {
        if (id !== item.id) {
          if (item.type == '3') {
            return {
              ...item,
              used: false,
            }
          } else {
            return {
              ...item,
            }
          }
        } else {
          return {
            ...item,
            used: true,
          }
        }
      })
    },
    filterList() {
      // 根据searchVal 过滤item.name 以及 item.zhName 中包含searchVal的项目
      // 同时兼顾  过滤出相应的skillTypeKey
      let list = this.addSkillListCopy

      if (this.searchVal) {
        let val = this.searchVal.toLowerCase()
        list = list.filter(
          (item) =>
            item.name.toLowerCase().includes(val) ||
            (item.zhName && item.zhName.toLowerCase().includes(val))
        )
      }
      this.addSkillList = list
    },
    getList(res, knowledgesRes) {
      const type = 3
      let self = this
      if (type !== undefined) {
        self.type = type
      }
      this.addSkillList = []

      //   自定义技能>自定义语句问答>设备人设> (开放问答)>商店技能>自定义关键词问答
      // delete res.data.kzs // 清除开放技能，即不可添加开放技能
      // 重新组装, 将item.used为true 放在前面

      // 4.5链路增加 自定义文档问答
      // let docKnowledge = (
      //   knowledgesRes && knowledgesRes.data
      //     ? knowledgesRes.data.knowledges || []
      //     : []
      // ).map((item) => {
      //   return {
      //     ...item,
      //     skillListType: 'docKnowledge',
      //   }
      // })

      // let docKnowledgeUsed = docKnowledge.filter((item) => item.used)
      // let docKnowledgeUnused = docKnowledge.filter((item) => !item.used)

      // 自定义技能
      let sy = (res.data.sy || []).map((item) => {
        return {
          ...item,
          skillListType: 'sbusinessList',
        }
      })
      let syUsed = sy.filter((item) => item.used)
      let syUnused = sy.filter((item) => !item.used)

      // 自定义语句问答
      let zs = (res.data.zs || []).map((item) => {
        return {
          ...item,
          skillListType: 'repositoryList',
        }
      })
      let zsUsed = zs.filter((item) => item.used)
      let zsUnused = zs.filter((item) => !item.used)

      // 设备人设
      let deviceRepository = (res.data.deviceRepository || []).map((item) => {
        return {
          ...item,
          skillListType: 'deviceRepositoryList',
        }
      })
      let deviceRepositoryUsed = deviceRepository.filter((item) => item.used)
      let deviceRepositoryUnused = deviceRepository.filter((item) => !item.used)

      // 开放问答
      let kzs = (res.data.kzs || []).map((item) => {
        return {
          ...item,
          skillListType: 'grepositoryList',
        }
      })
      let kzsUsed = kzs.filter((item) => item.used)
      let kzsUnused = kzs.filter((item) => !item.used)

      // 自定义关键词问答
      let ubotQa = (res.data.ubotQa || []).map((item) => {
        return {
          ...item,
          skillListType: 'ubotQaList',
        }
      })
      let ubotQaUsed = ubotQa.filter((item) => item.used)
      let ubotQaUnused = ubotQa.filter((item) => !item.used)
      this.hasDeviceCheckedBefore = deviceRepositoryUsed.length > 0
      let result = [
        // ...docKnowledgeUsed,
        ...syUsed,
        ...zsUsed,
        ...deviceRepositoryUsed,
        ...kzsUsed,
        ...ubotQaUsed,

        // ...docKnowledgeUnused,
        ...syUnused,
        ...zsUnused,
        ...deviceRepositoryUnused,
        // ...kzsUnused,
        ...ubotQaUnused,
      ]
      let result2 = result.slice()

      if (this.searchVal) {
        let val = this.searchVal.toLowerCase()
        result = result.filter(
          (item) =>
            item.name.toLowerCase().includes(val) ||
            (item.zhName && item.zhName.toLowerCase().includes(val))
        )
      }

      self.addSkillList = result
      self.addSkillListCopy = cloneDeep(result2)
    },
    toStoreSkill(item, $event) {
      let self = this
      if (self.subAccount) return
      window.open(`/store/skill/${item.id}?type=${item.type}`, '_blank')
    },
    onSwitchChange(val, item) {
      console.log('onSwitchChange', val, item)
      this.$emit('change')
      let data
      let type = item.skillListType
      let operation = val ? 'open' : 'close'
      if (type === 'sbusinessList' || type === 'storeSkillList') {
        data = {
          id: item.id,
          name: item.name,
          operation,
        }
        this.skillConfig[item.id] = data
      } else if (type === 'repositoryList') {
        // 自定义问答
        data = {
          repositoryId: item.id,
          type: '2',
          operation,
        }
        this.qaConfig[item.id] = data
      } else if (type === 'ubotQaList') {
        data = {
          qaId: item.id,
          operation,
        }
        this.ubotQaConfig[item.id] = data
      } else if (type === 'deviceRepositoryList') {
        data = {
          repositoryId: item.id,
          type: '3',
          operation,
        }
        this.qaConfig[item.id] = data
      }
    },
  },
  components: {
    zsCell,
    syCell,
    deviceRepositoryCell,
    ubotCell,
    kzsCell,
    docCell,
  },
}
</script>

<style lang="scss" scoped>
@import './style.scss';
.empty-skill-tips-new {
  position: absolute;
  top: 49%;
  right: -140px;
}
// .skill {
//   height: 117px;
// }

.label-wrap {
  padding: 10px 20px 0 20px;
  margin: 0 auto;
  text-align: center;
  border-top: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: center;
  p {
    padding: 0 10px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    color: #1784e9;
    background-color: rgba(227, 240, 252, 1);
    margin: 0 auto;
    border-radius: 40px;
    top: 50%;
  }
}

.flex-center {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  padding-bottom: 15px;
}

.new-skill-wrapper {
  display: grid;
  grid-template-columns: repeat(3, 1fr); /* 将容器分为3列，每列宽度平均 */
  gap: 18px; /* 设置格子之间的间距 */
}

.skill-to-add {
  padding-top: 35px;
  text-align: center;
  position: relative;
  background: #fff;
  border: 1px solid #e7e9ed;
  border-radius: 4px;
}
</style>
