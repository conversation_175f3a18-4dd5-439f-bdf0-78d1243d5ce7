<template>
  <div class="container">
    <div class="content">
      <left-menu @setCurrent="setCurrent" :current="current" />
      <right-chat :current="current" />
    </div>
  </div>
</template>
<script>
import LeftMenu from './model-experience/LeftMenu.vue'
import rightChat from './model-experience/rightChat.vue'

export default {
  data() {
    return {
      current: 'chain',
    }
  },
  methods: {
    setCurrent(val) {
      this.current = val
    },
  },
  components: { LeftMenu, rightChat },
}
</script>
<style lang="scss" scoped>
.title {
  text-align: center;
  font-size: 22px;
  font-weight: 700;
  text-align: center;
  color: #262626;
  line-height: 42px;
}

.content {
  display: flex;
  height: 100vh;
}

.content-left {
  width: 288px;
  z-index: 100;
  background: linear-gradient(180deg, #f6f9ff, #dce8ff 24%, #cee0ff);
  border: 1px solid #bfc7d6;
}

.foot {
  font-size: 14px;
  font-weight: 400;
  text-align: center;
  color: #959595;
  line-height: 42px;
}

// @media screen and (max-width: 1601px) {
//   .title {
//     font-size: 14px;
//   }

//   .content {
//     height: calc(100vh - 40px);
//   }

//   .foot {
//     font-size: 12px;
//     line-height: 30px;
//   }
// }
</style>
