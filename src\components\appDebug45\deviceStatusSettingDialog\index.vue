<template>
  <!-- 查看知识库溯源引用 -->
  <div>
    <el-dialog
      title="配置设备状态"
      :visible.sync="dialog.show"
      width="800px"
      custom-class="linkDialog"
    >
      <div>
        <json-form-editor
          ref="jsonEditor"
          :fields="formFields"
          @update="handleUpdate"
        ></json-form-editor>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialog.show = false">取消</el-button>
        <el-button
          size="small"
          type="primary"
          @click="handleSubmit"
          :loading="false"
          :disabled="false"
        >
          保存
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import JsonFormEditor from './JsonFormEditor.vue'
import { saveDeviceStatus, getDeviceStatus } from '@/utils/deviceStatusStorage'

export default {
  props: {
    dialog: Object,
    fields: {
      type: Array,
      default() {
        return []
      },
    },
    userId: {
      type: Number,
      default: 0,
    },
    appId: {
      type: String,
      default: '',
    },
    scene: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      formFields: [],
    }
  },
  watch: {
    'dialog.show'(val) {
      if (val) {
        // 弹窗打开时，优先从本地存储加载数据
        this.loadFormFieldsFromStorage()
      }
    },
  },
  computed: {
    // 获取所有输入参数名称（用于检查重名，不包括当前正在编辑的项）
  },
  methods: {
    // 从本地存储加载表单字段数据
    loadFormFieldsFromStorage() {
      const storedFields = getDeviceStatus(this.userId, this.appId, this.scene)
      if (storedFields && storedFields.length > 0) {
        this.formFields = storedFields
      } else {
        // 如果本地存储没有数据，使用父组件传入的默认数据
        this.formFields = this.fields.slice()
      }
    },

    handleUpdate(fields) {
      console.log(
        '---------------------------handleUpdate-------------------',
        fields
      )
      this.formFields = fields
    },
    handleSubmit() {
      this.$refs.jsonEditor.validate().then((valid) => {
        if (!valid) {
          this.$message.warning('请填写正确字段名')
          return
        }

        console.log('生成的 JSON：', JSON.stringify(this.formFields, null, 2))

        // 保存到本地存储
        const success = saveDeviceStatus(
          this.userId,
          this.appId,
          this.scene,
          this.formFields
        )
        if (success) {
          this.$message.success('已保存到本地')
        } else {
          this.$message.error('保存失败')
        }

        this.$emit('updateFormFields', this.formFields)
        this.dialog.show = false
      })
    },
  },
  components: { JsonFormEditor },
}
</script>
<style lang="scss">
.linkDialog {
  .el-dialog__body {
    padding: 0 32px 0 32px;
  }
}
</style>
<style lang="scss" scoped>
.item_title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10px;
}
</style>
