<template>
  <div class="code-editor">
    <div
      v-loading="loading"
      :element-loading-text="loadingText"
      style="min-height: 600px"
    >
      <p class="edit-tip">请使用NodeJS在编辑器中编写代码</p>
      <!-- 在线编辑遮罩 start -->
      <div class="editor-shadow" v-if="showShadow">
        <el-popover
          v-if="hasZipFile"
          placement="bottom-start"
          width="360"
          trigger="hover"
        >
          <p>您已上传了多文件zip包，开启在线编辑zip包将会被清空！</p>
          <el-button
            slot="reference"
            class="btn-open-editor"
            type="primary"
            @click="openEditor"
            :disabled="!subAccountEditable"
            >开启在线编辑</el-button
          >
        </el-popover>
        <el-button
          v-else
          class="btn-open-editor"
          type="primary"
          :disabled="!subAccountEditable"
          @click="openEditor"
          >开启在线编辑</el-button
        >
      </div>
      <!-- 在线编辑遮罩 end -->
      <template v-else>
        <div
          class="editor-mask"
          :class="{ 'editor-mask-show': !editorShow }"
        ></div>
        <monaco-editor
          v-if="editorShow"
          class="editor-wrap"
          height="480"
          language="javascript"
          theme="vs-dark"
          :code="code"
          :editorOptions="options"
          @mounted="onMounted"
          @codeChange="onCodeChange"
        >
        </monaco-editor>
        <template v-if="subAccountEditable">
          <el-button
            size="small"
            type="primary"
            :disabled="!edited"
            @click="save"
            >保存</el-button
          >
          <el-button type="text" :disabled="!edited" @click="cancel"
            >放弃修改</el-button
          >
        </template>
      </template>
    </div>
  </div>
</template>
<script>
import MonacoEditor from 'vue-monaco-editor'

export default {
  props: {
    businessId: '',
    editorInfo: {
      JSType: '',
      code: '',
      uploadTime: '',
    },
    subAccountEditable: Boolean,
  },
  data() {
    return {
      options: {
        selectOnLineNumbers: false,
        roundedSelection: false,
        readOnly: false,
        cursorStyle: 'line',
        automaticLayout: false,
        glyphMargin: true,
      },
      loading: false,
      loadingText: '保存中...',
      code: '',
      editorShow: false,
      showShadow: true,
      hasZipFile: false,
      visible: false,
      tips: {
        type: Object,
        title: '确定开启吗？',
        optionDesc:
          '后处理仅保存最后一次上次的文件。您已上传了多文件zip包，开启在线编辑后，已上传的zip包将被自动删除',
        confirmText: '确定',
        btn: '开启在线编辑',
      },
      edited: false,
    }
  },
  watch: {
    editorInfo: {
      handler(val, oldVal) {
        this.editorShow = false
        let self = this
        setTimeout(function () {
          self.editorShow = true
        }, 0)
        self.code = self.editorInfo.code
        if (self.editorInfo.JSType == 'zip') {
          self.showShadow = true
          self.hasZipFile = true
          return
        }
        if (self.editorInfo.JSType == 'js') {
          self.showShadow = false
          self.hasZipFile = false
          return
        }
        if (self.editorInfo.JSType == 'demo') {
          self.showShadow = true
          self.hasZipFile = false
        }
      },
      immediate: true,
    },
  },
  methods: {
    onMounted(editor) {
      this.editor = editor
    },
    onCodeChange(editor) {
      this.code = this.editor.getValue()
      this.edited = true
    },
    save() {
      let self = this
      self.loading = true
      self.loadingText = '保存中...'
      self.editorShow = false
      self.$utils.httpPost(
        self.$config.api.STUDIO_PROCESS_SAVE_SCRIPT,
        {
          skillId: self.businessId,
          singleJs: self.code,
        },
        {
          success: (res) => {
            setTimeout(function () {
              self.editorShow = true
              self.loading = false
              self.edited = false
              self.$message.success('保存成功')
            }, 500)
            self.$emit('showLog')
            self.$emit('getInfo')
          },
          error: (err) => {
            setTimeout(function () {
              self.editorShow = true
              self.loading = false
              self.edited = false
              self.$message.error('保存失败')
            }, 500)
          },
        }
      )
    },
    cancel() {
      let self = this
      self.code = this.editorInfo.code
      self.editorShow = false
      self.loading = true
      self.loadingText = '放弃修改中...'
      self.edited = false
      setTimeout(function () {
        self.editorShow = true
        self.loading = false
      }, 500)
    },
    openEditor() {
      this.showShadow = false
      this.save()
    },
  },
  components: {
    MonacoEditor,
  },
}
</script>
<style lang="scss" scoped>
.code-editor {
  position: relative;
  overflow-x: hidden;
  padding-bottom: 10px;
}
.editor-wrap {
  margin: 8px 0 16px;
}
.cancel {
  display: inline-block;
  margin-left: 28px;
  color: $primary;
}

.editor-shadow {
  position: relative;
  width: 100%;
  height: 550px;
  z-index: 10;
  background: $white;
  border: 1px solid $grey3;
  border-radius: 2px;
}
.btn-open-editor {
  position: absolute;
  top: 50%;
  left: 50%;
  margin-top: -18px;
  margin-left: -58px;
}
.editor-mask {
  height: 480px;
  margin: 8px 0 16px;
  position: absolute;
  top: 52px;
  z-index: 99;
  right: 0;
  width: 100%;
  background: #1e1e1e;
  opacity: 0.3;
  transition: 0.5s ease-in;
  transform: rotateX(90deg);
}
.editor-mask-show {
  position: relative;
  transform: rotateX(0);
}
</style>
