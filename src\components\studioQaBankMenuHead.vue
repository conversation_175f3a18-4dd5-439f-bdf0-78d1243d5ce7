<template>
  <div class="studio-skill-menu-head">
    <div class="studio-skill-menu-head-return" @click="returnCb">
      <svg-icon
        iconClass="secondary-return"
        :customStyle="{
          width: '13px',
          height: '9px',
          marginRight: '5px',
          transform: 'translateY(1px)',
        }"
      />
      <span>返回列表</span>
    </div>

    <!-- <el-popover placement="bottom" width="264" trigger="click">
      <select-qa :qaType="type" />
      <div slot="reference" class="studio-skill-menu-head-skill-name">
        <span :title="qa.name || '-'">{{ qa.name || '-' }}</span>
      </div>
    </el-popover> -->
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
// import SelectSkillPopover from '@P/studio/skill/dialog/selectSkill.vue'
// import SelectCharacter from '@P/studio/character/selectCharacter.vue'
import SelectQa from '@P/studio/qabank/selectQa.vue'

export default {
  name: 'studioQaBankMenuHead',
  data() {
    return {}
  },
  props: {
    type: Number,
  },
  computed: {
    ...mapGetters({
      qa: 'studioQa/qa',
      subAccount: 'user/subAccount',
    }),
  },
  created() {},

  methods: {
    returnCb() {
      this.$router.push({
        name: 'studio-handle-platform-qabanks',
        query: {
          type: this.type === 0 ? 'sentence' : this.type === 3 ? 'keyword' : '',
        },
      })
    },
  },
  components: {
    // SelectSkillPopover,
    SelectQa,
  },
}
</script>

<style lang="scss">
.studio-skill-menu-head {
  padding: 0 15px;
  display: flex;
  align-items: center;
  height: 44px;
  background-color: #fff;
  &-return {
    display: inline-flex;
    cursor: pointer;
    font-size: 14px;
    color: $grey4;
    align-items: center;
    margin-bottom: 0px;
    margin-right: 10px;
    i {
      color: $grey4;
    }
  }
  &-skill-name {
    padding: 10px 0 0 24px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    cursor: pointer;
    span {
      font-size: 20px;
      font-weight: 600;
      color: #000000;
    }
  }
}
</style>
