<template>
  <pc v-if="userAgent === 'pc'"></pc>
  <mobile v-else-if="userAgent === 'mobile'"></mobile>
  <span v-else>加载中...</span>
</template>

<script>
import pc from './multimodal-interaction/pc.vue'
import mobile from './multimodal-interaction/mobile-pages/index.vue'
import utils from '@A/lib/utils.js'

// import pc from '@P/aiui/solution-aiui/multimodal-interaction/pc.vue'

// import pc from './RK3588/pc.vue'
// import mobile from './RK3588/mobile-pages/index.vue'
// import utils from '@A/lib/utils.js'

export default {
  name: 'multimodal-interaction',
  data() {
    return {
      userAgent: '',
    }
  },
  created() {
    // 获取是否是h5
    const isMobile = utils.isMobile()
    if (isMobile) {
      this.userAgent = 'mobile'
    } else {
      this.userAgent = 'pc'
    }
  },
  components: { pc, mobile },
}
</script>

<style lang="scss"></style>
