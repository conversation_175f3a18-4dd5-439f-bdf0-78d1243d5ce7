<template>
  <os-text-adder
    ref="textAdder"
    class="mgb24"
    :data="list.data"
    :reg="textReg"
    :warning="warning"
    @add="addUtterance"
    @del="delUtterance"
    :readonly="true"
    :max="10"
    :placeholder="list.tip"
    :disabled="!editable"
  />
</template>
<script>
export default {
  name: 'expample-utterance',
  props: {
    list: {
      data: '',
      isCheck: false,
      type: '',
      tip: '',
    },
    subAccountEditable: Boolean,
  },
  data() {
    return {
      // textReg: /^[\u4e00-\u9fffa-zA-Z_\{\}\[\]\(\),\.!\?%\|\+×'\*÷\/-]{1,40}$/,
      // 中英文数字标点符号
      // textReg:
      //   /^[\u4e00-\u9fa5_a-zA-Z0-9\s\·\~\！\@\#\￥\%\……\&\*\（\）\——\-\+\=\【\】\{\}\、\|\；\‘\’\：\“\”\《\》\？\，\。\、\`\~\!\#\$\%\^\&\*\(\)\_\[\]{\}\\\|\;\'\'\:\"\"\,\.\/\<\>\?]{1,40}$/,
      // warning: '仅支持中英文、数字和标点符号,且每条不超过40字'
      textReg: /^[\u4e00-\u9fffa-zA-Z0-9 {}_?？°]{1,40}$/,
      warning: '仅支持汉字/字母/数字/空格/{}/_/?/°，且每条不超过40字',
    }
  },
  computed: {
    editable() {
      return !this.list.isCheck && this.subAccountEditable
    },
  },
  methods: {
    addUtterance(val) {
      this.list.data.push(val)
      this.$emit(this.list.type, JSON.stringify(this.list.data))
    },
    delUtterance(val) {
      this.list.data = Array.prototype.filter.call(
        this.list.data,
        function (item, index) {
          return item != val
        }
      )
      this.$emit(this.list.type, JSON.stringify(this.list.data))
    },
    delText() {
      this.$refs.textAdder.delText()
    },
  },
}
</script>
