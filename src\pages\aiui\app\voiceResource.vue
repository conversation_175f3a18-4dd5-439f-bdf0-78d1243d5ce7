<template>
  <div>
    <p class="item-title">语音合成资源</p>
    <p class="item-desc">语音合成资源仅用于评估板体验，只限于在评估板使用。</p>
    <el-table
      :data="tableData"
      style="width: 100%">
      <el-table-column
        label="人名"
        width="180">
        <template slot-scope="scope">
          <img :src="scope.row.icon" class="icon">
          {{ scope.row.name }}
        </template>
      </el-table-column>
      <el-table-column
        prop="timbre"
        label="音色"
        width="180">
      </el-table-column>
      <el-table-column
        prop="params"
        label="音库参数">
      </el-table-column>
      <el-table-column
        prop="url"
        label="试听">
        <template slot-scope="scope">
          <i
            @click="play(scope.row, scope.$index)"
            :class="['voice-icon', scope.row.playStatus ? 'ic-r-pause' : 'ic-r-play']"></i>
        </template>
      </el-table-column>
      <el-table-column
        prop="address"
        label="下载">
        <template slot-scope="scope">
          <i @click="downloadResource(scope.row.params)" class="voice-icon ic-r-download"></i>
        </template>
      </el-table-column>
    </el-table>
    <audio ref="audio" type="audio/ogg"></audio>

    <p class="buy-tips" v-if="!awakenForm.hasBuy">
      请先<a :href="`${$config.xfyunConsole}sale/buy?packageId=3002001&wareId=3002&appId=${appId}&appName=${appName}&serviceName=AIUI评估板`" target="_blank">购买</a>此AIUI应用的评估板，再生成资源包！
    </p>
    <div
      v-else
      class="mgt32"
      v-loading="packing"
      element-loading-text="唤醒词制作中…">
      <p class="item-title">唤醒词制作</p>
      <p class="item-desc">唤醒词制作仅用于评估板体验，只限于在评估板使用，且最多只能制作10次，请谨慎使用。</p>
      <el-form ref="awakenForm" class="mgt48" label-width="120px" label-position="left">
        <el-form-item label="唤醒词">
          <os-text-adder
            class="mgb24"
            style="max-width: 640px;"
            :data="awakenForm.awakenWords"
            :reg="textReg"
            warning="唤醒词不符合规范，仅支持中英文和空格"
            @add="add"
            @del="del"
            @edit="edit"
            :max="8"
            placeholder="回车新增"
          />
        </el-form-item>
        <el-form-item label="">
          <template v-if="awakenForm.modifyTimes < 10">
            <el-button v-if="awakenForm.zipUri" type="primary" @click="awakenPackage">重新制作</el-button>
            <el-button v-else type="primary" @click="awakenPackage">生成唤醒包</el-button>
          </template>
          <el-button v-if="awakenForm.zipUri" type="primary" @click="awakenDownload">下载资源包</el-button>
        </el-form-item>
      </el-form>
      <p class="awaken-word-tips">
        注意：<br>
        1、此处制作的唤醒词为浅定制，与评估板预装的 “叮咚叮咚” 效果不同；<br>
        2、唤醒词填写规则：仅支持汉字和英文。汉字间不能有空格，英文单词间用空格隔开且必须为字典中存在的单词；<br>
        3、当前唤醒词版本为6.0版本，唤醒效果有较大提升，建议所有用户升级，并配合5.5.1041.0000以后版本 SDK 使用。</p>
    </div>
  </div>
</template>

<script>
  import icon1 from '@A/images/app/tool/xiaoyan.png'
  import icon2 from '@A/images/app/tool/xiaofeng.png'
  import icon3 from '@A/images/app/tool/mengmeng.png'
  import { Base64 } from 'js-base64'

  export default {
    name: 'app-voice-resource',
    data() {
      return {
        pageOptions: {
          title: '语音资源',
          loading: false,
          returnBtn: false
        },
        tableData: [{
          icon: icon1,
          name: '小燕',
          params: 'xiaoyan',
          timbre: '青年女生',
          url: 'https://aiui-file.cn-bj.ufileos.com/xiaoyan.mp3',
          playStatus: false
        }, {
          icon: icon2,
          name: '晓峰',
          params: 'xiaofeng',
          timbre: '青年男生',
          url: 'https://aiui-file.cn-bj.ufileos.com/xiaoyu.mp3',
          playStatus: false
        }, {
          icon: icon3,
          name: '萌萌',
          params: 'mengmeng',
          timbre: '女童',
          url: 'https://aiui-file.cn-bj.ufileos.com/mengmeng.mp3',
          playStatus: false
        }],
        playIndex: -1,
        isResourceDownloading: false,
        textReg: /^[a-zA-Z \u4e00-\u9fa5]{1,}$/,
        awakenForm: {
          hasBuy: false,
          zipUri: '',
          modifyTimes: 0,
          awakenWords: []
        },
        saving: false, // 唤醒词保存
        packing: false // 唤醒词打包
      }
    },
    methods: {
      play(params, index) {
        if(params.playStatus) {
          this.$refs.audio.pause()
        } else {
          if(index !== this.playIndex && this.playIndex !== -1) {
            this.tableData[this.playIndex].playStatus = false
          }
          this.$refs.audio.src = params.url
          this.$refs.audio.play()
          let self = this
          this.$refs.audio.onended = function() {
            self.tableData[index].playStatus = false
          }
        }
        this.playIndex = index
        this.tableData[index].playStatus = !this.tableData[index].playStatus
      },
      downloadResource(name) {
        let self = this;
        if (!this.isResourceDownloading) {
          this.isResourceDownloading = true

          if(name == 'xiaofeng') {
            name = 'xiaoyu' // 晓峰的下载参数是xiaoyu 。。
          }
          window.open("https://www.xfyun.cn/aiui/manage/getResourcePath"+"?resourceName="+name+"&appid="+Base64.encode(this.appId), '_self')
          setTimeout(function() {
            self.isResourceDownloading = false
          }, 5000);
        } else {
          this.$message.warning('操作过快，请稍后再试')
        }
      },
      getAwakenInfo() {
        let self = this
        this.$utils.httpGet(this.$config.api.AIUI_APP_AWAKEN_INFO, {
          appid: this.appId
        }, {
          success: (res) => {
            if(res.data.awakenWords) {
              res.data.awakenWords = res.data.awakenWords.split(';')
            } else {
              res.data.awakenWords = []
            }
            self.awakenForm = res.data
          }
        })
      },
      //判断字符串是否为2个单词
      checkAllEnglish(str) {
        var reg = /^[a-zA-Z]+$/;
        var str1 = str.replace(/\s/g, "");
        if (!reg.test(str1)) {
          return false;
        }
        str = str.replace(/([\s]){2,}/g, " ");
        var length = str.split(" ").length;
        if (length >= 2 && length <= 4) return true;
        return false;
      },
      //判断字符串是否为3到6个中文
      checkChineseNum(str) {
        var reg = /^[\u4E00-\u9FA5]{3,6}$/;
        if (!reg.test(str)) {
          return false;
        }
        return true;
      },
      checkAwaken(text){
        if(this.checkAllEnglish(text) || this.checkChineseNum(text)) {
          return true
        } else {
          this.$message.warning('唤醒词不符合规范，英文唤醒词需为2-4个单词，中文唤醒词需为3-6个字符')
          return false
        }
      },
      add(text) {
        if(!this.checkAwaken(text)) return
        let awakenWords = JSON.parse(JSON.stringify(this.awakenForm.awakenWords))
        awakenWords[awakenWords.length] = text
        this.save(awakenWords)
      },
      edit(text, index) {
        if(!this.checkAwaken(text)) return
        let awakenWords = JSON.parse(JSON.stringify(this.awakenForm.awakenWords))
        if(awakenWords[index] === text) return
        awakenWords[index] = text
        this.save(awakenWords)
      },
      del(text) {
        let awakenWords = Array.prototype.filter.call(this.awakenForm.awakenWords, function(item, index) {
          return item != text
        })
        this.save(awakenWords)
      },
      save (list) {
        if (this.saving) {
          return
        }
        this.saving = true
        let self = this
        this.$utils.httpPost(this.$config.api.AIUI_APP_AWAKEN_SAVE, {
          appid: this.appId,
          awakens: list.join(';')
        }, {
          success: (res) => {
            self.saving = false
            self.$message.success('保存成功')
            self.awakenForm.awakenWords = list
          },
          error: (err) => {
            self.saving = false
          }
        })
      },
      awakenPackage() {
        let self = this
        this.packing = true
        this.$utils.httpPost(this.$config.api.AIUI_APP_AWAKEN_PACK, {
          appid: this.appId
        }, {
          success: (res) => {
            self.$message.success('提交成功，唤醒词制作中，请30s后操作')
            setTimeout(function() {
              self.packing = false
            }, 1000 * 30)
            self.getAwakenInfo()
          },
          error: (err) => {
            self.packing = false
          }
        })
      },
      awakenDownload() {
        window.open(this.awakenForm.zipUri, "_self")
      }
    },
    computed: {
      appId() {
        return this.$route.params.appId
      },
      appName() {
        return this.$store.state.aiuiApp.app.appName
      }
    },
    created() {
        this.getAwakenInfo()
    }
  }
</script>

<style lang="scss" scoped>
  .item-title {
    color: $semi-black;
    font-size: 20px;
    margin-bottom: 8px;
  }
  .item-desc {
    color: $grey5;
    margin-bottom: 24px;
  }
  .voice-icon {
    color: $grey4;
    font-size: 20px;
    cursor: pointer;
  }
  .buy-tips {
    font-size: 16px;
    margin: 30px 0;
  }
  .icon {
    width: 32px;
    height: 32px;
    vertical-align: middle;
    margin-right: 8px;
  }
  .awaken-word-tips {
    font-size: 12px;
    color: $grey5;
    line-height: 2;
    margin-bottom: 30px;
  }
</style>
