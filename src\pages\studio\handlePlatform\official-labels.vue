<template>
  <div class="os-scroll">
    <div class="handle-platform-top">
      <h1 class="txt-al-c mgb24">官方交互标签</h1>
      <div
        class="handle-platform-top-search"
        @keyup.enter="getOfficiallLabels(1)"
      >
        <el-input
          class="search-area"
          placeholder="输入要查询的内容，如：打招呼"
          v-model="searchVal"
        >
          <i
            slot="suffix"
            class="el-input__icon el-icon-search search-area-btn"
            @click="getOfficiallLabels(1)"
          />
        </el-input>
      </div>
    </div>
    <div class="handle-platform-content">
      <os-table
        :tableData="tableData"
        style="margin-bottom: 56px"
        @change="getOfficiallLabels"
      >
        <el-table-column prop="value" min-width="120" label="交互标签名称">
          <template slot-scope="scope">
            <div class="text-blod">{{ scope.row.zhName || '-' }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="value" min-width="200" label="英文标识">
        </el-table-column>
        <el-table-column prop="description" min-width="200" label="描述">
          <template slot-scope="scope">
            <div>{{ scope.row.description || '-' }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="label" min-width="100" label="类型">
        </el-table-column>
        <el-table-column prop="picture" min-width="100" label="示意">
          <template slot-scope="scope">
            <div>
              <img
                :src="
                  scope.row.picture ||
                  'https://aiui-file.cn-bj.ufileos.com/avatar/default.png'
                "
                class="label-picture"
              />
            </div>
          </template>
        </el-table-column>
      </os-table>
    </div>
  </div>
</template>

<script>
export default {
  name: 'studio-official-labels',
  data() {
    return {
      nav: 'labels',
      searchVal: '',
      tableData: {
        loading: true,
        total: 0,
        page: 1,
        size: 10,
        list: [],
      },
      dialog: {
        show: false,
      },
      countDialog: {
        show: false,
        entityId: '',
      },
    }
  },
  created() {
    this.getOfficiallLabels(1)
  },
  computed: {},
  methods: {
    getOfficiallLabels(page) {
      let self = this
      this.tableData.loading = true
      this.$utils.httpGet(
        this.$config.api.STUDIO_OFFICIAL_LABEL,
        {
          pageIndex: page || this.tableData.page,
          pageSize: this.tableData.size,
          search: this.searchVal,
        },
        {
          success: (res) => {
            self.tableData.list = res.data.labels
            self.tableData.total = res.data.count
            self.tableData.page = res.data.pageIndex
            self.tableData.size = res.data.pageSize
            self.tableData.loading = false
          },
          error: (err) => {},
        }
      )
    },
  },
  components: {},
}
</script>

<style lang="scss" scoped>
.handle-platform-content {
  max-width: 1200px;
  margin: auto;
}
.label-picture {
  width: 20px;
  height: 20px;
}
</style>
