<template>
  <os-page :options="pageOptions">
    <studio-skill-header-right slot="btn" />
    <div class="statistic-page">
      <div class="mgt32 statistic-item">
        <div class="item-header">
          <p class="item-title">用户统计</p>
          <el-date-picker
            class="date-picker"
            v-model="date1"
            type="daterange"
            align="right"
            unlink-panels
            range-separator="~"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :picker-options="pickerOptions1">
          </el-date-picker>
          <el-button type="default">重置</el-button>
          <span class="toggle">隐藏</span>
        </div>
        <div class="item-content">
          <div class="user-data-wrap">
            <div class="ib user-data-item">
              <p class="count dangerous">2,143</p>
              <p class="title">昨天活跃用户</p>
              <el-tooltip class="tips" effect="dark" content="Bottom Right 提示文字" placement="bottom">
                <i class="el-icon-question"/>
              </el-tooltip>
            </div>
            <div class="ib user-data-item">
              <p class="count">189</p>
              <p class="title">昨天新增用户</p>
              <el-tooltip class="tips" effect="dark" content="Bottom Right 提示文字" placement="bottom">
                <i class="el-icon-question"/>
              </el-tooltip>
            </div>
            <div class="ib user-data-item">
              <p class="count warning">10,143</p>
              <p class="title">累计用户</p>
              <el-tooltip class="tips" effect="dark" content="Bottom Right 提示文字" placement="bottom">
                <i class="el-icon-question"/>
              </el-tooltip>
            </div>
          </div>
          <div class="data-chart">
            <ve-line class="mgt32" :data="chartData" :settings="chartSettings"></ve-line>
          </div>
        </div>
      </div>
      <div class="mgt32 statistic-item">
        <div class="item-header">
          <p class="item-title">会话统计</p>
          <el-date-picker
            class="date-picker"
            v-model="date1"
            type="daterange"
            align="right"
            unlink-panels
            range-separator="~"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :picker-options="pickerOptions1">
          </el-date-picker>
          <el-button type="default">重置</el-button>
          <span class="toggle">隐藏</span>
        </div>
        <div class="item-content">
          <div class="user-data-wrap">
            <div class="ib user-data-item">
              <p class="count success">2,143</p>
              <p class="title">昨天请求次数</p>
              <el-tooltip class="tips" effect="dark" content="Bottom Right 提示文字" placement="bottom">
                <i class="el-icon-question"/>
              </el-tooltip>
            </div>
            <div class="ib user-data-item">
              <p class="count">89 <span class="unit">%</span></p>
              <p class="title">昨天请求成功率</p>
              <el-tooltip class="tips" effect="dark" content="Bottom Right 提示文字" placement="bottom">
                <i class="el-icon-question"/>
              </el-tooltip>
            </div>
            <div class="ib user-data-item">
              <p class="count">125 <span class="unit">ms</span></p>
              <p class="title">平均响应时间</p>
              <el-tooltip class="tips" effect="dark" content="Bottom Right 提示文字" placement="bottom">
                <i class="el-icon-question"/>
              </el-tooltip>
            </div>
          </div>
          <div class="data-chart">
            <ve-line class="mgt32" :data="chartData" :settings="chartSettings"></ve-line>
          </div>
        </div>
      </div>
      <div class="mgt32 statistic-item">
        <div class="item-header">
          <p class="item-title">会话分布</p>
          <el-date-picker
            class="date-picker"
            v-model="date1"
            type="daterange"
            align="right"
            unlink-panels
            range-separator="~"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :picker-options="pickerOptions1">
          </el-date-picker>
          <el-button type="default">重置</el-button>
          <span class="toggle">隐藏</span>
        </div>
        <div class="item-content">
          <os-table
            :tableData="tableData">
            <el-table-column
              prop="intent"
              label="意图名">
            </el-table-column>
            <el-table-column
              prop="count"
              label="会话数">
            </el-table-column>
            <el-table-column
              prop="count"
              label="请求次数">
            </el-table-column>
            <el-table-column
              prop="count"
              label="退出率">
            </el-table-column>
            <el-table-column
              prop="count"
              label="响应时间(ms)">
            </el-table-column>
          </os-table>
        </div>
      </div>
    </div>
  </os-page>
</template>

<script>
  export default {
    name: '',
    data() {
      this.chartSettings = {
        stack: { '用户': ['用户数', '活跃用户'] },
        area: true
      }
      return {
        pageOptions: {
          title: '数据统计',
          loading: false,
          returnBtn: false
        },
        pickerOptions1: {
          shortcuts: [{
            text: '最近一周',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '最近一个月',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit('pick', [start, end]);
            }
          }]
        },
        date1: '',
        chartData: {
          columns: ['日期', '用户数', '活跃用户'],
          rows: [
            { '日期': '1/1', '用户数': 1393, '活跃用户': 1093 },
            { '日期': '1/2', '用户数': 3530, '活跃用户': 3230 },
            { '日期': '1/3', '用户数': 2923, '活跃用户': 2623 },
            { '日期': '1/4', '用户数': 1723, '活跃用户': 1423 },
            { '日期': '1/5', '用户数': 3792, '活跃用户': 3492 },
            { '日期': '1/6', '用户数': 4593, '活跃用户': 4293 }
          ]
        },
        tableData: {
          loading: false,
          total: 38,
          page: 1,
          size: 5,
          list: [{
            intent: 'default',
            count: '50'
          },{
            intent: 'default',
            count: '50'
          },{
            intent: 'default',
            count: '50'
          },{
            intent: 'default',
            count: '50'
          }]
        }
      }
    },
    methods: {},
    created() {
    }
  }
</script>

<style lang="scss" scoped>
  .item-header {
    display: flex;
    align-items: baseline;
    margin-bottom: 24px;
  }
  .item-title {
    font-size: 20px;
    color: $semi-black;
    flex: auto;
  }
  .date-picker {
    margin-right: 8px;
  }
  .toggle {
    margin-left: 24px;
  }
  .user-data-wrap {
    margin-left: -16px;
  }
  .user-data-item {
    position: relative;
    width: calc(33% - 16px);
    height: 144px;
    padding: 34px 24px;
    text-align: center;
    border-radius: 12px;
    border: 1px solid $grey4;
    margin-left: 16px;

    .count {
      height: 46px;
      font-size: 36px;
    }
    .title {
      height: 22px;
      color: $semi-black;
    }
    .tips {
      position: absolute;
      right: 12px;
      bottom: 12px;
      color: $grey5;
    }
  }
  .dangerous {
    color: $dangerous;
  }
  .warning {
    color: $warning;
  }
  .success {
    color: $success;
  }
  .unit {
    color: $grey4;
    font-size: 14px;
  }
</style>
