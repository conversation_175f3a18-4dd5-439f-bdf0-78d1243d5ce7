@mixin scroll() {
  // 定义滚动条高宽及背景 高宽分别对应横竖滚动条的尺寸
  -ms-overflow-style:none;
  -webkit-overflow-scrolling:touch;
  overflow:-moz-scrollbars-none;
  &::-webkit-scrollbar {
    // width: 4px;
    width: 0px;
  }

  // 定义滚动条轨道 内阴影+圆角
  &::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 2px rgba(0, 0, 0, 0.2);
    border-radius: 4px;
    background-color: #FFFFFF;
  }
  // 定义滑块 内阴影+圆角
  &::-webkit-scrollbar-thumb {
    border-radius: 2px;
    background-color: #CCC;
  }
  //
  &::-webkit-scrollbar-thumb:hover {
    border-radius: 2px;
    background-color: #999;
  }

}
