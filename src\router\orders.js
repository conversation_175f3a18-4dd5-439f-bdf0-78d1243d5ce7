import aiuiHome from '@L/aiuiHome'
import aiuiDefaultLayout from '@L/aiuiDefaultLayout'

export default [
  {
    path: "/user",
    component: aiuiHome,
    children: [
      {
        path: 'order',
        name: 'orders',
        component: () => import("@P/orders/index")
      }
    ]
  },
  {
    path: "/ask",
    component: aiuiDefaultLayout,
    children: [
      {
        path: '/',
        name: 'ask',
        component: () => import("@P/user/ask")
      },
      {
        path: 'detail/:id',
        name: 'askDetail',
        component: () => import("@P/user/ask/detail")
      },
      {
        path: 'submit',
        name: 'submitAsk',
        component: () => import("@P/user/ask/submitAsk")
      }
    ]
  },
  {
    path: "/order",
    component: aiuiHome,
    children: [
      {
        path: '/',
        name: 'orderPage',
        redirect: {
          name: 'order-detail'
        }
      },
      {
        path: 'detail',
        name: 'order-detail',
        component: () => import("@P/orders/detail")
      }
    ]
  }
]
