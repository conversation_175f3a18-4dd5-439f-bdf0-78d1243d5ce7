<template>
  <el-dialog
    title="技能设置"
    :visible.sync="dialog.show"
    width="600px"
    top="5vh"
    :show-close="true"
    @closed="closeSkillDialog"
    @opened="openDialog"
  >
    <div style="padding-bottom: 40px">
      <template
        v-if="
          !(
            currentScene &&
            currentScene.chainId === 'sos_app' &&
            currentScene.point === '1,13'
          )
        "
      >
        <qc
          ref="qcConfig"
          v-if="qcAuth && currentScene.sos !== true"
          :show="show"
          :appId="appId"
          :currentScene="currentScene"
          :subAccountEditable="subAccountEditable"
        ></qc>

        <keyword :show="show" />
        <div style="margin-bottom: 40px">
          <recogSensitiveWord
            key="semantic"
            :show="show"
            ref="refSensitiveWord"
            :type="1"
            :changeType="1"
            :appId="appId"
            :limitCount="limitCount"
            :currentScene="currentScene"
            :subAccount="subAccount"
            :subAccountEditable="subAccountEditable"
          />
        </div>
      </template>

      <simpleProtocol :show="show" />
      <backstop
        v-if="showBackstop"
        :show="show"
        :currentScene="currentScene"
        :subAccountEditable="subAccountEditable"
      />
    </div>

    <!-- <span slot="footer" class="dialog-footer">
      <el-button size="small" @click="dialog.show = false">取消</el-button>
      <el-button
        size="small"
        type="primary"
        @click="saveChangeData"
        :loading="saveLoading"
      >
        保存配置
      </el-button>
    </span> -->
  </el-dialog>
</template>
<script>
import { mapGetters } from 'vuex'
import simpleProtocol from './simpleProtocol'
import keyword from './keyword'
import qc from './qc'
import backstop from './backstop'

import recogSensitiveWord from '../../../../sensitiveWord/recogSensitiveWord'
import RECOGNITION_LLM_SEMANTIC_State from '@U/AIUIState/RECOGNITION_LLM_SEMANTIC_State'
import RECOGNITION_LLM_SEMANTIC_SYNTHESIS_State from '@U/AIUIState/RECOGNITION_LLM_SEMANTIC_SYNTHESIS_State'

import RECOGNITION_TRANSLATE_SYNTHESIS_State from '@U/AIUIState/RECOGNITION_TRANSLATE_SYNTHESIS_State'
import RECOGNITION_TRANSLATE_State from '@U/AIUIState/RECOGNITION_TRANSLATE_State'
export default {
  props: {
    dialog: Object,
    appId: '',
    currentScene: Object,
  },
  data() {
    return {
      saveLoading: false,

      show: false,
    }
  },
  computed: {
    ...mapGetters({
      appInfo: 'aiuiApp/app',
      limitCount: 'aiuiApp/limitCount',
      subAccount: 'user/subAccount',
      qcAuth: 'aiuiApp/qcAuth',
      subAccountEditable: 'aiuiApp/subAccountEditable',
      context: 'aiuiApp/context',
    }),

    showBackstop() {
      if (
        this.currentScene &&
        this.currentScene.sceneBoxId &&
        this.currentScene.sos !== true &&
        this.context
      ) {
        const point = this.currentScene.point
        const context = this.context
        if (
          !context.isCurrentState(RECOGNITION_LLM_SEMANTIC_State) &&
          !context.isCurrentState(RECOGNITION_LLM_SEMANTIC_SYNTHESIS_State)
        ) {
          return true
        }
      }
      return false
    },
  },
  methods: {
    // 关闭弹窗
    closeSkillDialog() {
      this.show = false
    },
    openDialog() {
      // this.show = true
    },

    getChainInfo() {},
  },
  watch: {
    'dialog.show'(val) {
      if (val) {
        this.$nextTick(() => {
          this.show = true
        })
      }
    },
  },
  components: { simpleProtocol, keyword, recogSensitiveWord, qc, backstop },
}
</script>
<style lang="scss" scoped></style>
