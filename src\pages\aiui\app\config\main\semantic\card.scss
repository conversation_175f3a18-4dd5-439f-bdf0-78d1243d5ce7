.text {
  font-size: 14px;
}

.item {
  margin-bottom: 18px;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: '';
}
.clearfix:after {
  clear: both;
}

.box-card {
  width: 70%;
  min-width: 342px;

  :deep(.el-card__header) {
    padding: 14px 16px;
    background: #f7faff;
  }

  .header-title {
    font-size: 16px;
    color: #17171e;
    font-weight: 500;
  }

  .description {
    color: #666666;
    font-size: 14px;
    line-height: 30px;
  }
  .opt-link {
    line-height: 30px;
    margin-top: 4px;
    font-size: 14px;
  }
}
