<template>
  <el-dialog class="dg-body" title="固件详情" :visible.sync="modalParam.show">
    <el-form
      style="padding: 0 30px"
      ref="form"
      :model="modalParam.item"
      label-width="120px"
      label-position="left"
    >
      <el-form-item label="固件名称" prop="name">
        <div class="item-text">
          {{ modalParam.item.name }}
        </div>
      </el-form-item>

      <el-form-item label="固件版本" prop="version">
        <div class="item-text">
          {{ modalParam.item.version }}
        </div>
      </el-form-item>

      <el-form-item label="版本描述" prop="description">
        <div class="item-text">
          {{ modalParam.item.description }}
        </div>
      </el-form-item>

      <el-form-item label="上传文件" prop="fileId">
        <el-button size="small" type="primary" @click="downloadFile"
          >文件下载</el-button
        >
        <div class="file-info">
          <i class="el-icon-document"></i>
          <span>{{ modalParam.item.fileName }}</span>
        </div>
      </el-form-item>
    </el-form>
    <div class="modal-btn-container">
      <el-button size="small" type="primary" @click="closeModal"
        >关闭</el-button
      >
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'detailOTAModal',
  props: {
    modalParam: {
      type: Object,
      default: {
        show: Boolean,
        item: Object,
      },
    },
  },

  data() {
    return {}
  },

  methods: {
    closeModal() {
      this.modalParam.show = false
    },
    downloadFile() {
      window.open(
        this.modalParam.item.downloadUrl
          ? this.modalParam.item.downloadUrl
          : `/aiui/web${this.$config.api.OTA_DOWNLOAD}?appid=${this.appId}&fileId=${this.modalParam.item.fileId}`
      )
    },
  },

  computed: {
    appId() {
      return this.$route.params.appId
    },
  },

  watch: {
    'modalParam.show': function (val) {
      if (val) {
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.modal-btn-container {
  display: flex;
  justify-content: flex-end;
  padding: 30px 0;
  width: 100%;
}
.file-info {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  & > i {
    margin-right: 5px;
  }
  & > span {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }
}
.item-text {
  padding-top: 10px;
  line-height: 24px;
}
</style>
