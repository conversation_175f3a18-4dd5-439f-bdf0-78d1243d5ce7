import { utils } from '@U'
import { api } from '@/config'
import Router from '../../router'

export default {
  namespaced: true,
  state: {
    id: '',
    character: {},
  },

  actions: {
    setId({ commit }, characterId) {
      commit('setId', characterId)
    },

    set<PERSON><PERSON>cter({ state, commit, rootState }, characterId) {
      commit('setId', characterId)
      commit('setCharacter', {})
      utils.httpPost(
        api.STUDIO_CHARACTER_ATTRIBUTES,
        {
          repositoryId: characterId,
          category: 1,
          pageIndex: 1,
          pageSize: 999,
        },
        {
          success: (res) => {
            commit('setCharacter', res.data.context)
          },
          error: (err) => {
            Router.push({ name: 'studio-handle-platform-skills' })
          },
        }
      )
    },
  },

  mutations: {
    setId(state, characterId) {
      state.id = characterId
    },
    setCharacter(state, character) {
      state.character = character
    },
  },

  getters: {
    id(state, getters) {
      return state.id
    },
    character(state, getters) {
      return state.character
    },
  },
}
