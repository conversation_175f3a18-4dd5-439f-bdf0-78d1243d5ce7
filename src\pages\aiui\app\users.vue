<template>
  <div class="content-container">
    <!-- <os-page-label label="AIUI在线交互" class="mgb24"> </os-page-label> -->
    <div class="top-content">
      <div class="top-item">
        <div class="item_left">
          <div class="item_left_top">
            <span class="title">日新增设备</span>
            <el-tooltip
              class="item"
              effect="dark"
              content="每天新增加的UID数量。默认显示前一天的数据。"
              placement="bottom"
            >
              <i class="el-icon-question" />
            </el-tooltip>
          </div>
          <div class="item_left_bottom num">
            {{ addNum }}
          </div>
        </div>
        <div class="item_right">
          <img src="@A/images/aiui/newly-added.jpg" alt="" />
        </div>
      </div>
      <div class="top-item">
        <div class="item_left">
          <div class="item_left_top">
            <span class="title">日活跃设备</span>
            <el-tooltip
              class="item"
              effect="dark"
              content=""
              placement="bottom"
            >
              <div slot="content">
                每天活跃的设备数，按UID统计，计算当日会话UID<br />
                重后的总和。默认显示前一天的数据。
              </div>
              <i class="el-icon-question" />
            </el-tooltip>
          </div>
          <div class="item_left_bottom num">
            {{ dailyUser }}
          </div>
        </div>
        <div class="item_right">
          <img src="@A/images/aiui/active.jpg" alt="" />
        </div>
      </div>
      <div class="top-item">
        <div class="item_left">
          <div class="item_left_top">
            <span class="title">累计设备</span>
            <el-tooltip
              class="item"
              effect="dark"
              content="该应用下有过会话的UID去重后总和。"
              placement="bottom"
            >
              <i class="el-icon-question" />
            </el-tooltip>
          </div>
          <div class="item_left_bottom num">
            {{ allUser }}
          </div>
        </div>
        <div class="item_right">
          <img src="@A/images/aiui/active.jpg" alt="" />
        </div>
      </div>
      <div class="top-item">
        <div class="item_left">
          <div class="item_left_top">
            <span class="title">剩余台数</span>
            <el-tooltip
              class="item"
              effect="dark"
              content="已购买还未使用的授权装机量总和"
              placement="bottom"
            >
              <i class="el-icon-question" />
            </el-tooltip>
          </div>
          <div class="item_left_bottom num">
            {{ surplusUser === '不限制' ? '不限' : surplusUser }}
            <el-tooltip
              class="item"
              effect="dark"
              v-if="surplusUser !== '不限制' && surplusUser"
              placement="bottom"
            >
              <span slot="content">
                装机量授权请邮件联系商务<br />
                <EMAIL><br />
                <span
                  class="cp"
                  @click="$utils.copyClipboard('<EMAIL>')"
                  >复制邮箱</span
                >
              </span>
              <span class="left-tip" style="font-size: 14px">提升授权量</span>
            </el-tooltip>
          </div>
        </div>
        <div class="item_right">
          <img src="@A/images/aiui/remaining.jpg" alt="" />
        </div>
      </div>
    </div>
    <div class="bottom-content">
      <div>
        <os-page-label
          label="设备增长趋势"
          class="mgb24 user-statistic__date-wrap"
        >
          <date-range @setTime="setTime"></date-range>
        </os-page-label>
        <os-chart-line ref="chartLine"></os-chart-line>
      </div>
    </div>
    <div class="bottom-content offline_equipment">
      <template v-if="caeData">
        <os-page-label label="离线唤醒降噪（VTN）" class="mgb12">
        </os-page-label>
        <div class="top-content" style="width: 100%">
          <div class="top-item">
            <div class="top_item_left">
              <img src="@A/images/aiui/offline-total.jpg" alt="" />
              <div class="item_title">累计设备：</div>
              <div class="num">{{ caeData.liccNum }}</div>
            </div>
            <el-tooltip
              class="item"
              effect="dark"
              content="该应用下有过会话的UID去重后总和。"
              placement="bottom"
            >
              <i class="el-icon-question" />
            </el-tooltip>
          </div>
          <div class="top-item">
            <div class="top_item_left">
              <img src="@A/images/aiui/offline-remaining.jpg" alt="" />
              <div class="item_title">剩余台数：</div>
              <div class="num" style="margin-right: 20px">
                {{ caeData.liccSurplus }}
              </div>
              <el-tooltip
                class="item"
                effect="dark"
                v-if="surplusUser"
                placement="bottom"
              >
                <div slot="content">
                  装机量授权请邮件联系商务<br />
                  <EMAIL><br />
                  <span
                    class="cp"
                    @click="$utils.copyClipboard('<EMAIL>')"
                    >复制邮箱</span
                  >
                </div>
                <span class="left-tip">提升授权量</span>
              </el-tooltip>
            </div>

            <el-tooltip
              class="item"
              effect="dark"
              content="已购买还未使用的授权装机量总和"
              placement="bottom"
            >
              <i class="el-icon-question" />
            </el-tooltip>
          </div>
        </div>
      </template>
      <template v-if="ttsData">
        <os-page-label label="离线合成" class="mgb12"> </os-page-label>
        <div class="top-content" style="width: 100%">
          <div class="top-item">
            <div class="top_item_left">
              <img src="@A/images/aiui/offline-total.jpg" alt="" />
              <div class="item_title">累计设备：</div>
              <div class="num">{{ ttsData.liccNum }}</div>
            </div>
            <el-tooltip
              class="item"
              effect="dark"
              content="该应用下有过会话的UID去重后总和。"
              placement="bottom"
            >
              <i class="el-icon-question" />
            </el-tooltip>
          </div>
          <div class="top-item">
            <div class="top_item_left">
              <img src="@A/images/aiui/offline-remaining.jpg" alt="" />
              <div class="item_title">剩余台数：</div>
              <div class="num" style="margin-right: 20px">
                {{ ttsData.liccSurplus }}
              </div>
              <el-tooltip
                class="item"
                effect="dark"
                v-if="surplusUser"
                placement="bottom"
              >
                <div slot="content">
                  装机量授权请邮件联系商务<br />
                  <EMAIL><br />
                  <span
                    class="cp"
                    @click="$utils.copyClipboard('<EMAIL>')"
                    >复制邮箱</span
                  >
                </div>
                <span class="left-tip">提升授权量</span>
              </el-tooltip>
            </div>
            <el-tooltip
              class="item"
              effect="dark"
              content="已购买还未使用的授权装机量总和"
              placement="bottom"
            >
              <i class="el-icon-question" />
            </el-tooltip>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>
<script>
import dateRange from './dateRange'
import dicts from '@M/dicts'

let option = JSON.parse(JSON.stringify(dicts.lineOption))
// let option = dicts.lineOption
option.color = ['#8DDAD5', '#EE9CA7', '#A1C4FD']
option.legend.data = ['日新增设备', '日活跃设备', '累计设备']
option.series = [
  {
    name: '日新增设备',
    type: 'line',
    areaStyle: {},
    data: [],
  },
  {
    name: '日活跃设备',
    type: 'line',
    areaStyle: {},
    data: [],
  },
  {
    name: '累计设备',
    type: 'line',
    areaStyle: {},
    data: [],
  },
]

export default {
  name: 'app-users',
  data() {
    return {
      pageOptions: {
        title: '设备统计',
        loading: false,
      },
      addNum: 0,
      dailyUser: 0,
      allUser: 0,
      surplusUser: null,
      startDate: '',
      endDate: '',
      timeChanged: false,
      caeData: null,
      ttsData: null,
    }
  },
  computed: {
    appId() {
      return this.$route.params.appId
    },
  },
  watch: {
    timeChanged(val) {
      if (val) {
        this.getUserData()
      }
    },
  },
  created() {
    this.getUserDataCount()
    this.getAbilityDataCount()
  },
  methods: {
    getAbilityDataCount() {
      this.$utils.httpGet(
        this.$config.api.APP_SN_GET_ABILITY_LIST,
        {
          appid: this.appId,
        },
        {
          success: (res) => {
            console.log('res---', res)
            this.caeData = res.data && res.data.cae
            this.ttsData = res.data && res.data.tts
          },
          error: (err) => {},
        }
      )
    },

    getUserDataCount() {
      this.$utils.httpGet(
        this.$config.api.AIUI_STATISTIC_USER_COUNT,
        {
          appid: this.appId,
        },
        {
          success: (res) => {
            this.addNum = res.data.addUser
            this.dailyUser = res.data.dailyUser
            this.allUser = res.data.allUser
            this.surplusUser = res.data.surplusUser
          },
          error: (err) => {},
        }
      )
    },
    getUserData() {
      option.xAxis[0].data = []
      option.series[0].data = []
      option.series[1].data = []
      option.series[2].data = []
      this.$utils.httpGet(
        this.$config.api.AIUI_STATISTIC_USER,
        {
          appid: this.appId,
          startDate: this.startDate,
          endDate: this.endDate,
        },
        {
          success: (res) => {
            for (let i = 0; i < res.data.addUser.xAxis.length; i++) {
              option.xAxis[0].data.push([res.data.addUser.xAxis[i]])
              option.series[0].data.push(res.data.addUser.yAxis[i])
              option.series[1].data.push(res.data.dailyUser.yAxis[i])
              option.series[2].data.push(res.data.allUser.yAxis[i])
            }
            this.timeChanged = false
            this.setChart()
          },
          error: (err) => {
            this.timeChanged = false
          },
        }
      )
    },
    setTime(start, end) {
      this.startDate = start
      this.endDate = end
      this.timeChanged = true
    },
    setChart() {
      let self = this
      this.$refs.chartLine && this.$refs.chartLine.setOption(option)
    },
  },
  components: {
    dateRange,
  },
}
</script>
<style lang="scss" scoped>
@import './config/main/common.scss';

.app-statistic-users-page {
  padding-top: 20px;
}
.top-content {
  display: flex;
  background-color: #fff;
  padding: 32px 20px;
  border-radius: 10px;
}
.bottom-content {
  margin-top: 20px;
  padding: 20px;
  background-color: #fff;
  border-radius: 10px;
  :deep(.os-page-label) {
    .title {
      position: relative;
      font-size: 16px;
      color: #000000;
      padding-left: 10px;
      &:before {
        width: 2px;
        height: 16px;
        background-color: $primary;
        content: ' ';
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        margin: auto;
      }
    }
  }
  &.offline_equipment {
    .top-content {
      gap: 20px;
      padding: 0;
      .top-item {
        background: #f3f8ff;
        border-radius: 12px;
        padding: 15px 30px;
        i {
          cursor: pointer;
          font-size: 16px;
        }
        .top_item_left {
          display: flex;
          align-items: center;
          img {
            width: 40px;
            margin-right: 10px;
            border-radius: 50%;
          }
          .item_title {
            font-size: 14px;
            color: #757a8c;
          }
          .num {
            font-size: 24px;
          }
        }
      }
    }
  }
}
.el-icon-question {
  color: $grey4;
}
.top-item {
  flex: 1;
  padding: 0 40px;
  display: flex;
  align-items: center;
  justify-content: space-between;

  &:last-child {
    margin-right: 0;
  }
  .title {
    margin-bottom: 10px;
    color: #343434;
    font-size: 16px;
    font-weight: 600;
  }
  .num {
    font-size: 24px;
    font-weight: bold;
    color: #222222;
  }

  .left-tip {
    top: unset;
    bottom: 15px;
    left: 32px;
    width: 80px;
    color: $primary;
  }
  .item_left {
    flex: 1;
    .item_left_top {
      margin-bottom: 5px;
    }
    i {
      font-size: 16px;
      cursor: pointer;
    }
  }
  .item_right {
    img {
      width: 50px;
    }
  }
}

.user-statistic__date-wrap {
  position: relative;
}
.date-range-wrap {
  position: absolute;
  right: 0;
}

.content-container {
  padding: 14px;
  max-height: calc(100vh - 128px);
  overflow: auto;
  background: $secondary-bgc;
}
</style>
