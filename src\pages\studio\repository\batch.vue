<template>
  <div style="display: inline-block;">
    <el-dropdown
      trigger="click"
      @command="handleCommand"
      placement="bottom-start"
      >
      <el-button size="small" style="height: 36px;">
        批量操作
        <i class="ic-r-triangle-down el-icon--right" />
      </el-button>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item command="cover">
          <upload
            :repoId="repoId"
            :options="cover"
            :themeId="themeId"
            :api="api"
            @setLoad="setLoad"
            @getData="getData"
          ></upload>
        </el-dropdown-item>
        <el-dropdown-item command="addOnly">
          <upload
            :repoId="repoId"
            :options="addOnly"
            :themeId="themeId"
            :api="api"
            @setLoad="setLoad"
            @getData="getData"
          ></upload>
        </el-dropdown-item>
        <el-dropdown-item command="export">{{exportText}}</el-dropdown-item>
        <el-dropdown-item command="down">下载模版</el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
    <reason-for-upload-failed
      :dialog="uploadFailedDialg"
      :uploadFailedTip="uploadFailedTip"></reason-for-upload-failed>
  </div>
</template>

<script>
import Upload from './uploadFile'
import ReasonForUploadFailed from './dailog/reasonForUploadFailed'
export default {
  name: 'repo-batch',
  props: {
    repoId:'',
    themeId: [String, Number],
    api: String,
    exportText: String
  },
  data() {
    return {
      cover: {
        isCover: 2,
        text: '批量覆盖',
      },
      addOnly: {
        isCover: 1,
        text: '批量追加',
      },
      uploadFailedTip: [],
      uploadFailedDialg: {
        show: false
      }
    }
  },

  methods: {
    setLoad(val, failed) {
      if(failed) {
        this.uploadFailedDialg.show = true
        this.uploadFailedTip = JSON.parse(failed)
      }
      this.$emit('setLoad', val)
    },
    getData() {
      this.$emit('getData')
    },
    handleCommand (command) {
      let self = this
      switch (command) {
        case 'cover':
          break
        case 'addOnly':
          break
        case 'export':
          this.$utils.postopen(this.$config.api.STUDIO_REPO_EXCEL_EXPORT, {
            repositoryId: this.repoId
          })
          break
        case 'down':
          window.open('https://aiui-file.cn-bj.ufileos.com/kbqa_demo.xlsx', '_self')
          break
        default:
          break
      }
    },
  },
  components: { Upload, ReasonForUploadFailed }
}
</script>

<style lang="scss" scoped>
</style>
