<template>
  <div class="lib-solution main-content">
    <section class="main-content-banner">
      <div class="banner-text">
        <h2>儿童教育硬件解决方案</h2>
        <p class="banner-text-content">
          丰富的故事、儿歌、知识内容、配置要求低、接入方便快捷<br />
        </p>

        <div class="banner-text-button" @click="toConsole">合作咨询</div>
      </div>
    </section>

    <div class="section section1">
      <div class="section-title">应用领域</div>
      <p class="section-sub-title">适用于儿童教育陪伴相关智能硬件</p>
      <div class="section-content">
        <div class="content-item">
          故事机
          <div class="word">
            <p>
              针对0~6岁儿童,AIUI赋能儿童故事内容。色彩绚丽、形象卡通、耐摔耐磨,帮助婴幼儿启蒙发声和情绪安抚作用。
            </p>
          </div>
        </div>
        <div class="content-item">
          儿童陪伴机器人
          <div class="word">
            <p>
              适用于0-12岁儿童,AIUI赋能人机互动、闲聊陪伴功能,满足儿童的陪伴需求。
            </p>
          </div>
        </div>
        <div class="content-item">
          早教机
          <div class="word">
            <p>
              适用于0-12岁儿童,AIUI赋能人机互动、趣味游戏、益智学习等功能,辅助儿童成长。
            </p>
          </div>
        </div>
        <div class="content-item">
          扫读笔
          <div class="word">
            <p>
              适用于三年级以上学生群体使用,AIUI赋能支持OCR拼接、单词词典翻译等功能,减轻儿童英语学习负担。
            </p>
          </div>
        </div>
        <div class="content-item">
          学习机
          <div class="word">
            <p>
              适用于一年级以上学生群体使用,AIUI赋能人机交互、口语评测等功能,辅助学生全科学习。
            </p>
          </div>
        </div>
      </div>
    </div>

    <div class="section section2">
      <div class="section-title">丰富的儿童内容 同步课堂教材内容</div>
      <p class="section-sub-title">
        海量内容，满足儿童的求知欲和探究兴趣，促进儿童智力、语言各项能力发展。
      </p>
      <div class="section-content2">
        <div class="content-item2">
          <div class="content-pic"></div>
          <p>儿童歌谣</p>
        </div>
        <div class="content-item2">
          <div class="content-pic"></div>
          <p>童话故事</p>
        </div>
        <div class="content-item2">
          <div class="content-pic"></div>
          <p>教育百科</p>
        </div>
        <div class="content-item2">
          <div class="content-pic"></div>
          <p>安全教育</p>
        </div>
        <div class="content-item2">
          <div class="content-pic"></div>
          <p>国学启蒙</p>
        </div>
        <div class="content-item2">
          <div class="content-pic"></div>
          <p>儿童英语</p>
        </div>
        <div style="width: 100%"></div>
        <div class="content-item2">
          <div class="content-pic"></div>
          <p>学科教育</p>
        </div>
        <div class="content-item2">
          <div class="content-pic"></div>
          <p>绘本故事</p>
        </div>
        <div class="content-item2">
          <div class="content-pic"></div>
          <p>艺术培养</p>
        </div>
        <div class="content-item2">
          <div class="content-pic"></div>
          <p>科学内容</p>
        </div>
        <div class="content-item2">
          <div class="content-pic"></div>
          <p>文明礼仪</p>
        </div>
        <div class="content-item2">
          <div class="content-pic"></div>
          <p>卡通视频</p>
        </div>
      </div>
    </div>

    <div class="section section3">
      <div class="hidden">
        <h2>多款儿童专属技能</h2>
        <p class="skill-title">
          根据不同年龄段的儿童成长特点,打造各个年龄段所需技能
        </p>

        <div class="skill-content">
          <div class="special">
            <p>儿童</p>
            <p>专属技能</p>
          </div>
          <p class="child">儿童闲聊</p>
          <p class="book">有声书</p>
          <p class="question">趣味问答</p>
          <p class="tongfan">同反义词</p>
          <p class="baike">百科问答</p>
          <p class="translate">中英翻译</p>
          <p class="math">数学计算</p>
          <p class="kousuan">口算挑战</p>
        </div>

        <div class="skill-describe">
          <p class="xiqu">戏曲相声</p>
          <p class="zanghua">脏话引导</p>
          <p class="zaoju">造句</p>
          <p class="reason">十万个为什么</p>
          <p class="family">家族关系</p>
          <p class="chengyu">成语接龙</p>
          <p class="jisuan">数学计算</p>
          <p class="huansuan">单位换算</p>
          <p class="chengfabiao">九九乘法表</p>
        </div>
      </div>
    </div>

    <div class="section section4">
      <div class="methods">
        <h2>方案优势</h2>
        <p>儿童领域专属AI能力,赋能儿童教育硬件</p>
      </div>

      <div class="methods-content">
        <div class="methods-item">
          <h3>坐姿检测</h3>
          <p>提供坐姿检测、行为检测服务帮助儿童养成好的行为习惯。</p>
          <div class="pic"></div>
        </div>
        <div class="methods-item">
          <h3>中英文口语评测</h3>
          <p>
            提供业界领先的讯飞口语评测技术，满足中、英文的评测场景，辅助提升孩子的口
            语能力。
          </p>
          <div class="pic"></div>
        </div>
        <div style="width: 100%"></div>
        <div class="methods-item">
          <h3>留声模式</h3>
          <p>
            全球领先的个性化语音合成技术，能在极短的时间内为普通个人用户定制自己的专属音库。
          </p>
          <div class="pic"></div>
        </div>
        <div class="methods-item">
          <h3>OCR+机器翻译</h3>
          <p>
            汉字/英文多体识别,中英文翻译效果达8级水平,支持离线翻译帮助孩子学习英语。
          </p>
          <div class="pic"></div>
        </div>
      </div>
    </div>

    <div class="section section5">
      <h2>接入方式</h2>
      <p>接入灵活,提供纯软接入和RTOS硬件模组及小程序源码,打通云端内容服务</p>
      <div class="text">
        <div class="text1">
          <p>SDK / Web API</p>
        </div>
        <div class="text2">
          <p>7911 RTOS模组小程序源码</p>
        </div>
      </div>
    </div>

    <div class="last">
      <div class="banner-text">
        <h2>立即联系您的专属顾问</h2>
        <p class="banner-text-content">
          免费咨询专属顾问 为您量身定制产品推荐方案<br />
        </p>
        <div class="banner-text-button" @click="toConsole">合作咨询</div>
      </div>
    </div>
  </div>
</template>

<script>
import utils from '../../../assets/lib/utils.js'
import bus from '../../../assets/lib/bus.js'
import corp from '@P/aiui/solution-aiui/components/corp.vue'

export default {
  layout: 'aiuiHome',
  methods: {
    // toConsole() {
    //   if (utils.isMobile()) {
    //     bus.$emit('osMessageShow')
    //   } else {
    //     location.href = '/console'
    //   }
    // },
    toConsole() {
      // 判断是否有百度SEM营销参数，有的话，提交表单页需要带上
      const search = window.location.search
      if (search) {
        window.open(`/solution/apply/26${search}`)
      } else {
        window.open('/solution/apply/26')
      }
    },
  },
  components: { corp },
}
</script>

<style lang="scss" scoped>
.main-content {
  &-banner {
    background: url(~@A/images/solution/child-education/img_child_education_bg_banner1.png)
      center no-repeat;
    background-size: cover;
    height: 500px;
    overflow: hidden;
    width: 100%;
    .banner-text {
      max-width: 1200px;
      color: #fff;
      height: 100%;
      margin: auto;
      &-button {
        font-size: 16px;
        text-align: center;
        font-weight: 400;
        width: 140px;
        height: 40px;
        line-height: 40px;
        background: $primary;
        border-radius: 4px;
        // border: 1px solid #fff;
        // border-radius: 40px;
        color: #fff;
        cursor: pointer;
        // background: linear-gradient(0deg, #00b4ff 0%, #2371ff 100%);
        // transition: 0.6s;
      }
      h2 {
        color: #181818;
        padding-top: 148px;
        margin-bottom: 29px;
        font-size: 48px;
        font-weight: 600;
        font-family: PingFang SC, PingFang SC-Semibold;
        line-height: 48px;
      }
      p {
        font-size: 18px;
        margin-bottom: 74px;
      }

      .banner-text-content {
        width: 570px;
        font-size: 16px;
        font-family: SourceHanSansSC-Regular, SourceHanSansSC;
        font-weight: 400;
        // color: rgba(255, 255, 255, 0.86);
        color: #444444;
        line-height: 30px;
      }
    }
  }
  .section-title {
    text-align: center;
    font-size: 34px;
    font-weight: bold;
    color: #333;
    margin-top: 0 !important;
    margin-bottom: 30px !important;
  }
  .section-sub-title {
    font-size: 16px;
    font-weight: 400;
    text-align: center;
    color: #666666;
    margin-bottom: 60px !important;
  }
  .app-text {
    color: #666;
  }

  .section {
    padding: 30px 0 84px;
    text-align: center;
    .section-title {
      margin-bottom: 40px;
      font-size: 40px;
      font-weight: 500;
    }
    .section-content {
      margin: 50px auto 0;
      width: 1200px;
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
    }
    .content-item {
      flex: 0 0 auto;
    }
  }
  .content-item {
    padding: 20px;
    position: relative;
    flex: 0 0 auto;
    padding-top: 30px;
    width: 215px;
    height: 290px;
    text-align: center;
    font-size: 18px;
    color: #666;
    background: url('../../../assets/images/solution/child-education/img_bot1.png')
      center no-repeat;
    background-size: cover;
    overflow: hidden;
    .word {
      margin-top: 20px;
      visibility: hidden;
      font-size: 14px;
      line-height: 24px;
      text-align: left;
    }
    &:hover {
      background: url('../../../assets/images/solution/child-education/img_maskb.png')
        center no-repeat;
      color: #fff;
      .word {
        visibility: visible;
      }
    }
    &:first-child {
      background: url('../../../assets/images/solution/child-education/img_story1.png')
        center no-repeat;
      background-size: cover;
      &:hover {
        background: url('../../../assets/images/solution/child-education/img_maska.png')
          center no-repeat;
      }
    }
    &:last-child {
      background: url('../../../assets/images/solution/child-education/img_study1.png')
        center no-repeat;
      background-size: cover;
      &:hover {
        background: url('../../../assets/images/solution/child-education/img_maske.png')
          center no-repeat;
      }
    }
    &:nth-child(3) {
      background: url('../../../assets/images/solution/child-education/img_zaojiaoji1.png')
        center no-repeat;
      background-size: cover;
      &:hover {
        background: url('../../../assets/images/solution/child-education/img_maskc.png')
          center no-repeat;
      }
    }
    &:nth-child(4) {
      background: url('../../../assets/images/solution/child-education/img_pen1.png')
        center no-repeat;
      background-size: cover;
      &:hover {
        background: url('../../../assets/images/solution/child-education/img_maskd.png')
          center no-repeat;
      }
    }
  }

  .section-content2 {
    margin: 50px auto 0;
    // margin-left: 180px;
    width: 1200px;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
  }
  .content-item2 {
    position: relative;
    flex: 0 0 auto;
    padding-top: 30px;
    width: 110px;
    height: 180px;
    text-align: center;
    margin-bottom: 50px;
    font-size: 18px;
    color: #666;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    p {
      margin-right: 15px;
    }
    .content-pic {
      // margin-right: 0;
      margin-bottom: 20px;
      height: 95px;
      width: 100px;
      background: url('../../../assets/images/solution/child-education/img_9.png')
        center no-repeat;
      background-size: cover;
      overflow: hidden;
    }
    &:nth-child(2) {
      .content-pic {
        margin-bottom: 20px;
        height: 78px;
        width: 78px;
        background: url('../../../assets/images/solution/child-education/img_8.png')
          center no-repeat;
        background-size: cover;
        overflow: hidden;
      }
    }
    &:nth-child(3) {
      .content-pic {
        margin-bottom: 20px;
        height: 76px;
        width: 76px;
        background: url('../../../assets/images/solution/child-education/img_5.png')
          center no-repeat;
        background-size: cover;
        overflow: hidden;
      }
    }
    &:nth-child(4) {
      .content-pic {
        margin-bottom: 20px;
        height: 76px;
        width: 86px;
        background: url('../../../assets/images/solution/child-education/img_1.png')
          center no-repeat;
        background-size: cover;
        overflow: hidden;
      }
    }
    &:nth-child(5) {
      .content-pic {
        margin-bottom: 20px;
        height: 78px;
        width: 84px;
        background: url('../../../assets/images/solution/child-education/img_3.png')
          center no-repeat;
        background-size: cover;
        overflow: hidden;
      }
    }
    &:nth-child(6) {
      .content-pic {
        margin-bottom: 20px;
        height: 75px;
        width: 100px;
        background: url('../../../assets/images/solution/child-education/img_2.png')
          center no-repeat;
        background-size: cover;
        overflow: hidden;
      }
    }
    &:nth-child(7) {
      .content-pic {
        margin-bottom: 20px;
        height: 81px;
        width: 87px;
        background: url('../../../assets/images/solution/child-education/img_11.png')
          center no-repeat;
        background-size: cover;
        overflow: hidden;
      }
    }
    &:nth-child(8) {
      .content-pic {
        margin-bottom: 20px;
        height: 81px;
        width: 87px;
        background: url('../../../assets/images/solution/child-education/img_11.png')
          center no-repeat;
        background-size: cover;
        overflow: hidden;
      }
    }
    &:nth-child(9) {
      .content-pic {
        margin-bottom: 20px;
        height: 73px;
        width: 74px;
        background: url('../../../assets/images/solution/child-education/img_4.png')
          center no-repeat;
        background-size: cover;
        overflow: hidden;
      }
    }
    &:nth-child(10) {
      .content-pic {
        margin-bottom: 20px;
        height: 75px;
        width: 77px;
        background: url('../../../assets/images/solution/child-education/img_12.png')
          center no-repeat;
        background-size: cover;
        overflow: hidden;
      }
    }
    &:nth-child(11) {
      .content-pic {
        margin-bottom: 20px;
        height: 73px;
        width: 80px;
        background: url('../../../assets/images/solution/child-education/img_7.png')
          center no-repeat;
        background-size: cover;
        overflow: hidden;
      }
    }
    &:nth-child(12) {
      .content-pic {
        margin-bottom: 20px;
        height: 76px;
        width: 71px;
        background: url('../../../assets/images/solution/child-education/img_10.png')
          center no-repeat;
        background-size: cover;
        overflow: hidden;
      }
    }
    &:last-child {
      .content-pic {
        margin-bottom: 20px;
        height: 82px;
        width: 85px;
        background: url('../../../assets/images/solution/child-education/img_6.png')
          center no-repeat;
        background-size: cover;
        overflow: hidden;
      }
    }
  }

  .section3 {
    margin: 0, auto;
    // position: relative;
    background: url(~@A/images/solution/child-education/img_cricle.png) center
      no-repeat;
    background-size: cover;
    height: 750px;
    overflow: hidden;
    // width: 2560px;

    .hidden {
      position: relative;
      width: 1200px;
      margin: auto;
      // visibility: hidden;
      h2 {
        font-size: 32px;
        line-height: 60px;
        color: #fff;
      }
      .skill-title {
        color: #fff;
        font-size: 16px;
        line-height: 40px;
      }
      .skill-content {
        .special {
          position: absolute;
          z-index: 999;
          top: 340px;
          left: 540px;
          font-size: 28px;
          p {
            color: #fff;
            font-size: 30px;
          }
        }
        p {
          font-size: 16px;
          color: #000;
        }
        .child {
          position: absolute;
          top: 290px;
          left: 43px;
        }
        .book {
          position: absolute;
          top: 260px;
          left: 385px;
        }
        .question {
          position: absolute;
          top: 270px;
          left: 803px;
        }
        .baike {
          position: absolute;
          top: 250px;
          left: 1100px;
        }
        .tongfan {
          position: absolute;
          top: 410px;
          left: 1000px;
        }
        .translate {
          position: absolute;
          top: 510px;
          left: 135px;
        }
        .math {
          position: absolute;
          top: 555px;
          left: 390px;
        }
        .kousuan {
          position: absolute;
          top: 580px;
          left: 740px;
        }
      }
      .skill-describe {
        p {
          font-size: 20px;
          opacity: 0.6;
          color: #fff;
        }
        .xiqu {
          position: absolute;
          top: 250px;
          left: 200px;
        }
        .zanghua {
          position: absolute;
          top: 200px;
          left: 520px;
        }
        .zaoju {
          position: absolute;
          top: 215px;
          left: 950px;
          font-size: 30px;
        }
        .reason {
          position: absolute;
          top: 385px;
          left: 100px;
        }
        .family {
          position: absolute;
          top: 555px;
          left: 260px;
          font-size: 16px;
        }
        .chengyu {
          position: absolute;
          top: 370px;
          left: 830px;
        }
        .jisuan {
          position: absolute;
          top: 615px;
          left: 570px;
          font-size: 16px;
        }
        .huansuan {
          position: absolute;
          top: 574px;
          left: 900px;
          font-size: 16px;
        }
        .chengfabiao {
          position: absolute;
          top: 526px;
          left: 1080px;
          font-size: 24px;
        }
      }
    }
  }

  .section4 {
    padding-bottom: 0;
    .methods {
      h2 {
        font-weight: 600;
        font-size: 32px;
        color: #262626;
        line-height: 60px;
      }
      p {
        font-weight: 400;
        font-size: 16px;
        line-height: 40px;
      }
    }
    .methods-content {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      margin: 50px auto 0;
      // margin-left: 160px;
      width: 1200px;
      text-align: left;
      .methods-item {
        background: linear-gradient(187deg, #f5f7f9 0%, #ffffff 78%);
        border: 2px solid #ffffff;
        box-shadow: -7px 0px 27.55px 0.35px rgba(188, 198, 216, 0.3);
        position: relative;
        flex: 0 0 auto;
        width: 580px;
        height: 170px;
        padding-left: 20px;
        padding-top: 30px;
        margin-bottom: 40px;
        h3 {
          font-weight: 500;
          font-size: 18px;
          line-height: 56px;
          color: #262626;
        }
        p {
          width: 450px;
          font-size: 12px;
          color: #666666;
        }
        .pic {
          position: absolute;
          top: -30px;
          left: 400px;
          width: 100px;
          height: 100px;
          background: url('../../../assets/images/solution/child-education/3.png')
            center no-repeat;
          background-size: cover;
        }
        &:first-child {
          .pic {
            background: url('../../../assets/images/solution/child-education/1.png')
              center no-repeat;
            background-size: cover;
          }
        }
        &:nth-child(2) {
          .pic {
            background: url('../../../assets/images/solution/child-education/2.png')
              center no-repeat;
            background-size: cover;
          }
        }
        &:nth-child(5) {
          .pic {
            background: url('../../../assets/images/solution/child-education/4.png')
              center no-repeat;
            background-size: cover;
          }
        }
      }
    }
  }

  .section5 {
    position: relative;
    background: url(~@A/images/solution/child-education/img_jieruBG1.png) center
      no-repeat;
    background-size: cover;
    height: 305px;
    margin-top: 0;
    overflow: hidden;
    width: 100%;
    h2 {
      font-size: 30px;
      line-height: 60px;
      color: #262626;
    }
    p {
      font-size: 16px;
      line-height: 40px;
      color: #444444;
    }
    .text {
      margin: 50px auto 0;
      display: flex;
      justify-content: center;
    }
    .text1 {
      // position: absolute;
      // top: 160px;
      // left: 480px;
      width: 280px;
      margin-right: 20px;
      background: url(~@A/images/solution/child-education/img_white.png) center
        no-repeat;
    }
    .text2 {
      // position: absolute;
      // top: 160px;
      // left: 780px;
      width: 280px;
      margin-left: 20px;
      background: url(~@A/images/solution/child-education/img_white.png) center
        no-repeat;
    }
  }
  .last {
    background: url(~@A/images/solution/child-education/img_guwenBG.png) center
      no-repeat;
    background-size: cover;
    height: 300px;
    overflow: hidden;
    width: 100%;
    .banner-text {
      max-width: 1200px;
      color: #fff;
      height: 100%;
      margin: auto;
      // margin-left: 35px;
      &-button {
        font-size: 16px;
        text-align: center;
        font-weight: 400;
        width: 140px;
        height: 40px;
        line-height: 40px;
        // border: 1px solid #fff;
        // border-radius: 40px;
        background: $primary;
        border-radius: 4px;
        color: #fff;
        cursor: pointer;
        // background: linear-gradient(0deg, #00b4ff 0%, #2371ff 100%);
        // transition: 0.6s;
      }
      h2 {
        color: #181818;
        padding-top: 50px;
        margin-bottom: 20px;
        font-size: 36px;
        font-weight: 400;
        line-height: 48px;
      }
      p {
        font-size: 18px;
        margin-bottom: 50px;
      }

      .banner-text-content {
        width: 570px;
        font-size: 16px;
        font-family: SourceHanSansSC-Regular, SourceHanSansSC;
        font-weight: 400;
        // color: rgba(255, 255, 255, 0.86);
        color: #444444;
        line-height: 30px;
      }
    }
  }
}
</style>
