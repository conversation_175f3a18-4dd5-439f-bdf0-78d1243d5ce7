<template>
  <div
    :class="['skill', { 'skill-active': item.used }]"
    @click="toPage(item.id)"
  >
    <!-- <div class="content-wrap">
      <img class="skill-icon-new" v-if="item.url" :src="item.url" />
      <i class="skill-icon-new" v-else-if="item.zhName">{{
        item.zhName.substr(0, 1)
      }}</i>
      <i class="skill-icon-new" v-else-if="item.name">{{
        item.name.substr(0, 1)
      }}</i>
      <div class="skill-info">
        <p class="skill-title ib" :title="item.zhName || item.name">
          {{ item.zhName || item.name }}
        </p>
        <a
          class="skill-source ib"
          v-if="item.sourceCount && item.sourceCount > 0"
          >{{ item.sourceCount }}个信源</a
        >
        <p
          class="private-skill-number"
          v-if="item.used && item.outNumber"
          :title="item.outNumber"
        >
          {{ item.outNumber }}
        </p>
        <p
          class="private-skill-number"
          v-if="!item.used && item.newestNumber"
          :title="item.newestNumber"
        >
          {{ item.newestNumber }}
        </p>
        <p class="skill-desc" :title="item.briefIntroduction">
          {{ item.briefIntroduction }}
        </p>
      </div>
    </div> -->

    <div :class="['content-wrap']">
      <img
        v-if="item && item.image"
        :src="`http://cdn.iflyos.cn/public/character/${item.image}.jpg`"
        class="skill-icon"
      />
      <span
        class="skill-icon-new"
        :style="{ backgroundColor: item.color }"
        v-else
      >
        {{ item.name && item.name.substr(0, 1) }}
      </span>
      <div class="skill-info">
        <p class="skill-title" :title="item.name">
          {{ item.name }}
        </p>

        <!-- 标题下按钮 -->
        <div class="title-btm-btn-group">
          <p class="ability-tag">设备人设</p>
        </div>
      </div>
    </div>

    <div @click.stop class="switch-wrap">
      <el-switch
        :disabled="!subAccountEditable"
        size="small"
        v-model="item.used"
        @change="(val) => onSwitchChange(val, item)"
      >
      </el-switch>
    </div>
    <!-- 下面的label area -->
    <!-- <div class="label-wrap"></div> -->
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  data() {
    return {}
  },
  props: {
    item: Object,
    qaConfig: Object,
    hasDeviceCheckedBefore: Boolean,
  },
  computed: {
    ...mapGetters({
      subAccount: 'user/subAccount',
      subAccountAppAuths: 'aiuiApp/subAccountAppAuths',
      subAccountSkillAuths: 'aiuiApp/subAccountSkillAuths',
    }),
    subAccountEditable() {
      return this.subAccountAppAuths[this.$route.params.appId] == 2
        ? false
        : true
    },
  },
  methods: {
    toPage(id) {
      if (this.subAccount) return
      window.open(`/studio/character/${id}`, '_blank')
    },
    doRealChange(val, item) {
      console.log('onSwitchChange', val, item)
      this.$emit('change')
      let data
      let operation = val ? 'open' : 'close'
      data = {
        repositoryId: item.id,
        type: '3',
        operation,
      }
      this.qaConfig[item.id] = data
    },
    onSwitchChange(val, item) {
      let that = this
      if (val) {
        // 要判断是否其他的均为关闭
        let qaConfigArr = Object.keys(this.qaConfig).map((k) => ({
          key: k,
          value: this.qaConfig[k],
        }))
        let hasJustCheck =
          qaConfigArr.findIndex((item) => item.value.operation === 'open') !==
          -1

        if (this.hasDeviceCheckedBefore || hasJustCheck) {
          this.$confirm(
            '设备人设只允许打开一个，是否确定关闭先前设备人设打开当前选中的设备人设。',
            '提示',
            {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning',
            }
          )
            .then(() => {
              // 1、 要将qaConfig中其他项目关闭,
              console.log('this.qaConfig', this.qaConfig)

              Object.keys(this.qaConfig).forEach((k) => {
                if (k !== item.id && this.qaConfig[k].type == '3') {
                  this.qaConfig[k].operation = 'close'
                }
              })
              // 2、 外面父级收到此事件后进行处理， 界面上将其他设备人设切为关闭,
              this.$emit('justCheck', item.id)
              that.doRealChange(val, item)
            })
            .catch(() => {
              item.used = false
            })
        } else {
          that.doRealChange(val, item)
        }
      } else {
        that.doRealChange(val, item)
      }
    },
  },
}
</script>
<style lang="scss" scoped>
@import '../style.scss';
// .skill {
//   height: 140px;
// }

// .label-wrap {
//   padding: 10px 20px 0 20px;
//   margin: 0 auto;
//   text-align: center;
//   border-top: 1px solid #e4e7ed;
//   display: flex;
//   align-items: center;
//   justify-content: center;
//   p {
//     padding: 0 10px;
//     height: 30px;
//     line-height: 30px;
//     text-align: center;
//     color: #1784e9;
//     background-color: rgba(227, 240, 252, 1);
//     margin: 0 auto;
//     border-radius: 40px;
//     top: 50%;
//   }
// }

.icon-image {
  width: 30px;
  height: 30px;
  border-radius: 15px;
}
</style>
