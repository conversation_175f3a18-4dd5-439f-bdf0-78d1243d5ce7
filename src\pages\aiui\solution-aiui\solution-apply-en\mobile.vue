<template>
  <div class="s-body">
    <div class="s-body-inner">
      <div class="aiui-logo"></div>
      <div class="form-header__title">
        iFLYTEK Product Solutions Consultation Application
      </div>

      <p class="field__description">
        After submission, we will contact you within one business day to provide
        full technical support.
      </p>
      <div class="s-content">
        <a-form
          :form="form"
          :label-col="{ span: 8 }"
          :wrapper-col="{ span: 16 }"
          @submit="handleSubmit"
        >
          <a-form-item label="Company Name">
            <a-input
              v-decorator="[
                'companyName',
                {
                  rules: [
                    {
                      required: true,
                      message: 'Please enter the company name.',
                    },
                  ],
                },
              ]"
            />
          </a-form-item>
          <a-form-item label="Full Name">
            <a-input
              v-decorator="[
                'linkMan',
                {
                  rules: [
                    {
                      required: true,
                      message: 'Please enter the full name.',
                    },
                  ],
                },
              ]"
            />
          </a-form-item>
          <a-form-item label="Email Address">
            <a-input
              v-decorator="[
                'email',
                {
                  rules: [
                    { validator: checkEmail },
                    {
                      required: true,
                      message: 'Please enter the email address.',
                    },
                  ],
                },
              ]"
            />
          </a-form-item>
          <a-form-item label="Contact Person Job Title">
            <a-input
              v-decorator="[
                'position',
                {
                  rules: [
                    {
                      required: false,
                      message: 'Please enter the contact person job title.',
                    },
                  ],
                },
              ]"
            />
          </a-form-item>
          <a-form-item label="City of Residence">
            <a-input
              v-decorator="[
                'city',
                {
                  rules: [
                    { required: true, message: 'Please enter the city.' },
                  ],
                },
              ]"
            />
          </a-form-item>
          <a-form-item label="Description of requirements">
            <a-textarea
              class="fb_content_main"
              style="width: 100%; height: 100%"
              placeholder="Please briefly describe your requirements so that we can provide better service."
              :rows="4"
              :maxLength="500"
              v-decorator="[
                'description',
                {
                  rules: [
                    {
                      required: true,
                      message: 'Please describe your requirements.',
                    },
                  ],
                },
              ]"
            />
          </a-form-item>
          <a-form-item :wrapper-col="{ span: 12, offset: 10 }">
            <a-button type="primary" html-type="submit">
              Submit Request
            </a-button>
          </a-form-item>
        </a-form>
      </div>
    </div>
  </div>
</template>

<script>
import Form from 'ant-design-vue/lib/form'
import Input from 'ant-design-vue/lib/input'
import Button from 'ant-design-vue/lib/button'
import 'ant-design-vue/lib/form/style/css'
import 'ant-design-vue/lib/input/style/css'
import 'ant-design-vue/lib/button/style/css'

export default {
  name: 'solutionApply',
  data: function () {
    return {
      solutionId: this.$route.params.solutionId,
      title: '',
      formLayout: 'horizontal',
      form: this.$form.createForm(this, { name: 'coordinated' }),
    }
  },
  methods: {
    checkTel(rule, value, callbackFn) {
      if (!value) {
        callbackFn()
        return
      }
      const reg = /^[1][3,4,5,6,7,8,9][0-9]{9}$/
      if (!reg.test(value)) {
        callbackFn('请检查手机号码格式')
        return
      }
      callbackFn()
    },
    checkEmail(rule, value, callbackFn) {
      if (!value) {
        callbackFn()
        return
      }
      const reg = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!reg.test(value)) {
        callbackFn('Please check the email format.')
        return
      }
      callbackFn()
    },
    onChange(value) {
      console.log(value)
    },
    handleSubmit(e) {
      e.preventDefault()
      this.form.validateFields((err, values) => {
        if (!err) {
          console.log('Received values of form: ', values)
          let paramObj = {
            ...values,
            solutionId: this.solutionId,
          }
          if (window.location.search) {
            paramObj.otherParam = window.location.href
          }
          this.$utils.httpGet(this.$config.api.SOLUTION_APPLY, paramObj, {
            success: (res) => {
              if (res.code == 0 && res.flag) {
                this.$message.success('Information submitted successfully.')
                this.form.resetFields()
              } else {
                this.$message.error('failed')
              }
            },
            error: (err) => {},
          })
        } else {
          var a = document.getElementsByClassName('s-body')
          a.length &&
            a[0].scrollIntoView({
              behavior: 'smooth',
              block: 'center',
              inline: 'center',
            })
        }
      })
    },
    handleSelectChange(value) {
      console.log(value)
      this.form.setFieldsValue({
        note: `Hi, ${value === 'male' ? 'man' : 'lady'}!`,
      })
    },
  },
  mounted() {
    const that = this
    window.onresize = () => {
      return (() => {
        window.screenWidth = document.body.clientWidth
        that.screenWidth = window.screenWidth
      })()
    }
    ;(function () {
      var currClientWidth, fontValue, originWidth
      originWidth = 750 //ui设计稿的宽度，一般750或640
      __resize()

      window.addEventListener('resize', __resize, false)

      function __resize() {
        currClientWidth = document.documentElement.clientWidth
        if (currClientWidth > 768) {
          currClientWidth = 768
        }
        if (currClientWidth < 320) {
          currClientWidth = 320
        }
        fontValue = ((625 * currClientWidth) / originWidth).toFixed(2)
        document.documentElement.style.fontSize = fontValue + '%'
      }
    })()
  },
  created() {
    let self = this
    this.$utils.httpGet(
      this.$config.api.SOLUTION_APPLY_LIST,
      {},
      {
        success: (res) => {
          if (res.data.solutions && res.data.solutions.length) {
            /*self.title = res.data.solutions.filter(solution => {
              return solution.id == self.solutionId
            })[0].name*/
            for (let i = 0; i < res.data.solutions.length; i++) {
              if (res.data.solutions[i].id == self.solutionId) {
                self.title = res.data.solutions[i].name
              }
            }
          }
        },
        error: (err) => {},
      }
    )
  },
  components: {
    [Form.name]: Form,
    [Form.Item.name]: Form.Item,
    [Input.name]: Input,
    [Button.name]: Button,
    [Input.TextArea.name]: Input.TextArea,
  },
}
</script>

<style scoped lang="scss" scoped>
.aiui-logo {
  width: 109px;
  height: 65px;
  background: url(~@A/images/solutions/apply/aiui_small.png) center/contain
    no-repeat;
}
.s-body {
  p {
    margin-bottom: 0;
  }
  background: white;
  background-image: url('../../../../assets/images/solutions/apply/solution_apply.jpg');
  background-size: cover;
  background-position: bottom;
  background-repeat: no-repeat;
  margin-bottom: 100px;
  width: 100%;
  height: 100%;
  margin: 0 auto;
  align-items: center;
  background-position: center center;
  background-attachment: fixed;
  background-size: cover;
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #fff;
  position: relative;
  white-space: nowrap;
  overflow-y: auto;
  overflow-x: hidden;
  background-size: 100%;
  white-space: nowrap;

  position: fixed;
  background-size: cover;

  .s-body-inner {
    background: white;
    border: 1px solid #e7e7e7;
    height: auto;
    width: 7rem;
    margin: 0.3rem;
    padding: 0.2rem;

    .field__description {
      // color: rgb(16, 16, 16);
      color: #ff8c00;
      font-size: 14px;
      font-weight: normal;
      background-color: rgb(245, 245, 245);
      padding: 8px;
      //   width: 80%;
      margin: 10px auto;
      word-wrap: break-word;
      white-space: normal;
    }
  }
}
.form-header__title {
  margin-top: 0.2rem;
  //   margin-left: 0.7rem;
  text-align: center;
  white-space: normal;
  font-family: inherit;
  font-size: 15px;
  font-weight: normal;
  color: rgb(34, 34, 34);
}
.s-body {
  :deep(.ant-form-item) {
    display: flex !important;
    align-items: center !important;
  }
}
</style>
<style scoped lang="scss"></style>
