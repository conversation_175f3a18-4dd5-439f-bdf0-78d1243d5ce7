<template>
  <div 
    class="select-list-popover el-popover el-popper el-popover--plain"
    v-if="variablePopover.show"
    v-clickoutside="closePopover"
    :style="popperStyle"
    visible-arrow="false"
    x-placement="bottom-start">
      <p :class="{'active': filterType.type === 'currentPage'}" @click="filterHandle('currentPage')">选择当前页</p>
      <p :class="{'active': filterType.type === 'all'}" @click="filterHandle('all')">选择所有</p>
    <div x-arrow="" class="popper__arrow" style="left: 13.5px;"></div>
  </div>
</template>

<script>
export default {
  name: 'select-list-popover',
  props: {
    variablePopover: {
      type: Object,
      default: () => ({ 
        show: false
      })
    },
    filterType: {
      type: Object,
      default: () => ({ 
        type: ''
      })
    }
  },
  data() {
    return {
      rect: {
        top: 0,
        left: 0,
        width: 0
      }
    }
  },
  computed: {
    popperStyle() {
      if (this.rect) {
        return {
          top: `${this.rect.top + 20}px`,
          left: `${this.rect.left - 10}px`
        }
      } else {
        return {
          display: `none`
        }
      }
    }
  },
  watch: {
    'variablePopover.rect': function() {
      this.rect = JSON.parse(JSON.stringify(this.variablePopover.rect))
    }
  },
  methods: {
    closePopover(){
      this.variablePopover.show = false
    },
    filterHandle(val) {
      if(val === this.filterType.type) return
      this.$emit('filterHandle', val)
      this.variablePopover.show = false
    }
  }
}
</script>
<style lang="scss" scoped>
.select-list-popover {
  padding: 8px 0;
  width: 144px;
  p {
    padding: 0 16px;
    height: 36px;
    line-height: 36px;
    cursor: pointer;
    &:hover {
      background: #e3f0fc;
    } 
  }
  .active {
    background: #e3f0fc;
  }
}

</style>/