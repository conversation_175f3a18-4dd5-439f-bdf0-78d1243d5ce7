<template>
  <div class="skill-icon-uploader">
    <el-upload
      v-loading="loading"
      :action="`${$config.server}${this.$store.state.user.baseUrl}/skill/public/uploadLogo?businessId=${skillId}`"
      :show-file-list="false"
      :on-success="uploadSuccess"
      :before-upload="beforeUpload"
    >
      <img v-if="image.url" :src="image.url" class="avatar" />
      <i v-else class="ic-r-plus avatar-uploader-icon"></i>
    </el-upload>
    <p class="upload-tip">
      支持 .png .jpg .jpeg 格式文件，<br />
      图片大小不超过 {{ limitSize }}M，<br />
      尺寸 {{ limitLength }} × {{ limitLength }} px<br />
      <a :href="`${this.$config.docs}doc-55/`" target="_blank">具体参考</a>
    </p>
  </div>
</template>
<script>
export default {
  name: 'upload-icon',
  props: {
    skillId: '',
    image: {
      url: '',
    },
  },
  data() {
    return {
      limitSize: '5', // 单位/M
      limitLength: '480',
      loading: false,
      imgBeforeUpload: '',
    }
  },
  methods: {
    beforeUpload(file) {
      let isPNGOrJPG =
        file.type === 'image/png' ||
        file.type === 'image/jpg' ||
        file.type === 'image/jpeg'
      let unExceed = file.size < 1024 * 1024 * this.limitSize
      if (!isPNGOrJPG) {
        this.$message.error('仅支持.png .jpg .jpeg格式文件')
        return false
      }
      if (!unExceed) {
        this.$message.error(`文件不能超过${this.limitSize}M`)
        return false
      }
      let self = this
      self.loading = true
      return new Promise(function (resolve, reject) {
        let reader = new FileReader()
        reader.readAsDataURL(file)
        reader.onload = function (theFile) {
          let img = new Image()
          img.src = theFile.target.result
          img.onload = function () {
            if (
              img.height != self.limitLength &&
              img.width != self.limitLength
            ) {
              self.$message.error(
                `图片尺寸需为${self.limitLength}*${self.limitLength}像素`
              )
              self.loading = false
              reject('图片尺寸不对')
            } else {
              self.imgBeforeUpload = img.src
              resolve(file)
            }
          }
        }
      })
    },
    uploadSuccess(res) {
      if (res.data) {
        this.loading = false
        this.image.url = this.imgBeforeUpload
        this.$message.success('上传成功')
        this.$emit('setIconInfo', res.data)
      } else {
        this.loading = false
        this.$message.error(res.desc)
        this.$emit('setIconInfo', '')
      }
    },
  },
}
</script>
<style lang="scss" scoped>
.skill-icon-uploader {
  display: flex;
  .avatar-uploader-icon {
    width: 160px;
    height: 160px;
    line-height: 160px;
  }
  .ic-r-plus {
    font-size: 32px;
    color: $grey3;
  }
  .upload-tip {
    padding-top: 7px;
    padding-left: 16px;
    line-height: 22px;
  }
}
</style>
<style lang="scss">
.skill-icon-uploader {
  .el-upload {
    width: 160px;
    height: 160px;
    border-radius: 2px;
    border: 1px dashed #d9d9d9;
  }
  .avatar {
    width: 100%;
    height: 100%;
  }
}
</style>
