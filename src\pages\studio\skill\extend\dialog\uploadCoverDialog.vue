<template>
  <el-dialog
    class="upload-cover-dialog"
    width="480px"
    :visible.sync="dialog.show"
  >
    <div slot="title" class="dialog-title">
      <i class="ic-r-exclamation"></i>确定批量导入覆盖吗？
    </div>
    <span>一旦导入成功，技能现有信息将被完全覆盖！</span>
    <span slot="footer" class="dialog-footer">
      <el-button @click="dialog.show = false">取 消</el-button>
      <el-button @click="setUpload">
        <upload
          :dictId="dictId"
          :options="cover"
          :limitCount="limitCount"
          :subAccount="subAccount"
          @setErrInfo="setErrInfo"
          @setLoad="setLoad"
          @getEntryList="getDictList"
        ></upload>
      </el-button>
    </span>
  </el-dialog>
</template>
<script>
import Upload from './../uploadExtendEntity'
export default {
  name: 'upload-cover-dialog',
  props: {
    dialog: {
      type: Object,
      default: {},
    },
    dictId: '',
    limitCount: Object,
    subAccount: Boolean,
  },
  data() {
    return {
      cover: {
        type: 1,
        text: '覆 盖',
      },
    }
  },
  methods: {
    setUpload() {
      this.dialog.show = false
    },
    setLoad(val) {
      this.$emit('setLoad', val)
    },
    getDictList() {
      this.$emit('getDictList', 1)
    },
    setErrInfo(data, type) {
      this.$emit('setErrInfo', data, type)
    },
  },
  components: {
    Upload,
  },
}
</script>

<style lang="scss">
.upload-cover-dialog {
  .el-button {
    padding: 0;
    width: 130px;
    height: 44px;
    line-height: 44px;
    overflow: hidden;
  }
  .el-upload .el-button {
    width: 130px;
    height: 44px;
    font-size: 16px;
    color: $dangerous;
  }
  .dialog-title {
    font-size: 20px;
  }
  .ic-r-exclamation {
    margin-right: 16px;
    vertical-align: -1px;
    color: $warning;
  }
  .el-dialog__header {
    padding: 32px 32px 12px;
  }
  .el-dialog__body {
    padding-top: 0;
    padding-bottom: 23px;
    font-size: 16px;
    color: $semi-black;
    padding-left: 70px;
  }
  .el-dialog__footer {
    padding-bottom: 32px;
  }
}
</style>
