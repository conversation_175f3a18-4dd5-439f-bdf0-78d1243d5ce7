<template>
  <div class="relative input-container" @keyup="flowerBracketHandle">
    <div
      class="content-container"
      :style="{ width: showSwitch ? 'calc(100% - 136px)' : '100%' }"
    >
      <el-input
        class=""
        type="textarea"
        :rows="1"
        ref="addUtter"
        :placeholder="placeholder"
        :disabled="false"
        @blur="onBlur"
        @keyup.native.up.stop.prevent="handleUp"
        @keyup.native.down.stop.prevent="handleDown"
        :value="value"
        @input="onInputChange"
        @keyup.native.enter.stop.prevent="onEnterUp"
      >
      </el-input>
    </div>

    <el-button
      v-if="showAdd"
      slot="suffix"
      type="text"
      class="utterance-add-area-addbtn"
      size="small"
      @click="onIconAdd"
    >
      添加
    </el-button>
    <div v-if="showSwitch" class="style-container">
      <span>回复风格：</span>
      <a-switch
        checked-children="儿童"
        un-checked-children="成人"
        v-model="checkedValue"
      />
    </div>

    <entity-auxiliary-popover
      ref="entityAuxiliaryPopover"
      :variablePopover="variablePopover"
      @setSlot="setSlot"
    />
  </div>
</template>
<script>
import EntityAuxiliaryPopover from './replyEntityAuxiliaryPopover.vue'
import getTextBoundingRect from '@U/getTextBoundingRect.js'
import Switch from 'ant-design-vue/lib/switch'
import 'ant-design-vue/lib/switch/style/css'

export default {
  name: 'inteligient-input',
  props: {
    placeholder: String,
    value: String,
    showAdd: { type: Boolean, default: true },
    showSwitch: { type: Boolean, default: false },
  },
  data() {
    return {
      // value: '',
      cursorPos: -1,
      variablePopover: {
        show: false,
        rect: null,
        style: null,
      },
      checkedValue: false,
    }
  },
  watch: {
    checkedValue(val) {
      this.$emit('checkedChange', val)
    },
  },
  methods: {
    resetCheckedValue() {
      this.checkedValue = false
    },
    onInputChange(val) {
      this.$emit('input', val)
    },
    flowerBracketHandle(e) {
      let self = this
      let flowerKeyCode = [219, 37] // 37 是中文的 }, 搜狗输入法中文状态时输入 { 时会自动补全 {}
      // 上下箭头
      if (e.keyCode === 38 || e.keyCode === 40) return
      if (!flowerKeyCode.includes(e.keyCode) && self.cursorPos === -1) return
      // const cursor = this.$refs.addUtter.$refs.input.selectionStart //input 里的鼠标当前位置
      const cursor = this.$refs.addUtter.$refs.textarea.selectionStart //input 里的鼠标当前位置
      const value = e.target.value
      // const rect = e.target.getBoundingClientRect()
      // const input = this.$refs.addUtter.$refs.input
      const input = this.$refs.addUtter.$refs.textarea
      const { selectionStart, selectionEnd } = input
      const rect = getTextBoundingRect(
        input,
        selectionStart,
        selectionEnd,
        false
      )
      const rectInput = input.getBoundingClientRect()
      const left = rect.left - rectInput.left
      let styleObj = {
        position: 'absolute',
        left: `${left}px`,
        top: '44px',
      }
      // 318 是弹出框的宽度
      if (left + 318 > rectInput.width) {
        styleObj = { ...styleObj, right: 0 }
        delete styleObj.left
      }

      if (value[cursor - 1] === '{') {
        self.cursorPos = cursor
      }
      if (value.substring(cursor - 2, cursor) == '{}') {
        self.cursorPos = cursor - 1
      }
      if (self.cursorPos > cursor) {
        return (self.variablePopover.show = false)
      }
      self.cursorPos !== -1 &&
        setTimeout(function () {
          self.variablePopover = {
            show: true,
            rect: rect,
            cursorPos: cursor,
            searchVal: value.substring(self.cursorPos).replace(/}+/g, ''),
            style: styleObj,
          }
          self.$nextTick(() => {
            self.$refs.entityAuxiliaryPopover.inputFocus()
          })
        }, 0)
    },
    setSlot(item) {
      if (!item) return (this.cursorPos = -1)
      let regL = /{+/g
      let regR = /}+/g
      let searchValLen = this.variablePopover.searchVal
        ? this.variablePopover.searchVal.length
        : 0
      let val = this.value

      val =
        val.substring(0, this.cursorPos) +
        item +
        '}' +
        val.substring(this.cursorPos + searchValLen)
      val = val.replace(regL, '{').replace(regR, '}')

      this.$emit('input', val)

      let currentCursorPos = this.cursorPos + item.length + 1
      // let input = this.$refs.addUtter.$refs.input
      let input = this.$refs.addUtter.$refs.textarea
      input.setSelectionRange(this.cursorPos, currentCursorPos)
      input.focus()
      this.cursorPos = -1
    },
    onEnterUp() {
      this.$emit('onAdd', this.value)
      // this.value = ''
    },
    onBlur() {
      // this.$emit('onAdd', this.value)
    },
    onIconAdd() {
      this.$emit('onAdd', this.value)
      // this.value = ''
    },
    handleUp() {
      if (!this.variablePopover.show) return
      this.$refs.entityAuxiliaryPopover.handleUp()
    },
    handleDown() {
      if (!this.variablePopover.show) return
      this.$refs.entityAuxiliaryPopover.handleDown()
    },
  },
  components: {
    EntityAuxiliaryPopover,
    [Switch.name]: Switch,
  },
}
</script>
<style lang="scss" scoped>
.total-wrap {
  width: 100%;
}
.input-container {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.style-container {
  min-width: 136px;
  margin-left: 5px;
}
.content-container {
  width: calc(100% - 136px);
}
</style>
<style lang="scss">
.relative {
  position: relative;
}
.input-container {
  width: 100%;
  display: flex;
  .el-input__inner {
    padding: 0;
  }
}
.content-container {
  textarea {
    border: none;
    padding-left: 0;
    padding-right: 0;
  }
}
</style>
