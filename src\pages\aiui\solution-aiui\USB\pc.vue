<template>
  <div class="main-content">
    <section class="main-content-banner">
      <div class="banner-text">
        <h2 v-html="'AIUI USB声卡开发套件'" style="line-height: 60px"></h2>
        <p class="pc-show banner-text-content">
          多麦克风阵列构型录音声卡，集成便捷，适用于智能机器人，智慧大屏等设备开发评估。
        </p>
        <div class="hor-btn">
          <div class="banner-text-button" @click="toConsole">合作咨询</div>
          <div class="banner-text-buy" @click="toBuy">立即购买</div>
        </div>
      </div>
    </section>
    <section class="section section-2">
      <div class="section-title">应用场景</div>
      <div class="section-item">
        <ul>
          <li v-for="(item, index) in app_scenario" :key="index" class="app">
            <img :src="item.src" :alt="item.alt" />
            <p class="app-text">{{ item.alt }}</p>
          </li>
        </ul>
      </div>
    </section>
    <section class="section section-3">
      <div class="section-title">产品特点</div>
      <div class="section-item">
        <ul>
          <li v-for="(item, index) in point_list" :key="index" class="app">
            <img :src="item.src" :alt="item.alt" />
            <div class="title">{{ item.title }}</div>
            <div class="sub_title">{{ item.sub_title }}</div>
          </li>
        </ul>
      </div>
    </section>
    <section class="section section-4">
      <div class="section-title">接线示意图</div>
      <div class="section-item">
        <div>
          <p>支持线性四麦<br />线性六麦和环形六麦</p>
          <p>原始音频<br />上传给上位机</p>
          <p>上位机回采信号<br />接入声卡主板</p>
          <p>上位机系统支持<br />windows/Andriod/linux</p>
        </div>
      </div>
    </section>
    <section class="section section-5">
      <div class="section-title">硬件接口说明</div>
      <div class="section-item">
        <div>
          <p>调试串口接口</p>
          <p>参考信号接口</p>
          <p>wafer USB接口</p>
          <p>micro USB接口</p>
          <p>麦克风信号接口</p>
          <p>麦克风信号接口</p>
          <p>独立电源接口</p>
        </div>
      </div>
    </section>
    <section class="section section-8">
      <div class="section-title">产品清单</div>
      <div class="section-item">
        <div v-for="item in product_list" :key="item.index" class="item">
          <div class="title">{{ item.title }}</div>
          <p v-html="item.sub_title"></p>
        </div>
      </div>
    </section>
    <section class="section section-9">
      <div class="section-title">开发材料</div>
      <div class="section-item">
        <div class="item">
          <a
            v-for="item in develop_doc"
            :key="item.index"
            @click="toDoc(item.link)"
            style="color: #666666"
            >{{ item.name }}</a
          >
        </div>
      </div>
    </section>
    <!-- <div class="corp">
      <div class="corp-section-wrap">
        <div class="corp-section-title">
          <p class="corp-section-title-contact">立即联系您的专属顾问</p>
          <p class="corp-section-desc2">免费咨询专属顾问 为您量身定制产品推荐方案</p>
        </div>

        <div class="corp-section-item" style="padding-top: 39px; text-align: left">
          <div class="corp-section-button" @click="toConsole">合作咨询</div>
        </div>
      </div>
    </div> -->
  </div>
</template>

<script>
import corp from '@P/aiui/solution-aiui/components/corp.vue'

export default {
  layout: 'aiuiHome',
  data() {
    return {
      app_scenario: [
        {
          alt: '智能机器人',
          src: require('../../../../assets/images/solution/soft-hardware/USB/znjqr.png'),
        },
        {
          alt: '一体机',
          src: require('../../../../assets/images/solution/soft-hardware/USB/ytj.png'),
        },
        {
          alt: '智能屏',
          src: require('../../../../assets/images/solution/soft-hardware/USB/znp.png'),
        },
        {
          alt: '电视机',
          src: require('../../../../assets/images/solution/soft-hardware/USB/dsj.png'),
        },
      ],
      product_list: [
        {
          title: '硬件',
          sub_title:
            '• USB声卡主板    • 麦克风板  • 麦克风排线 <br/>• 回采线 • USB线',
        },
        {
          title: '软件',
          sub_title:
            ' • 唤醒SDK，集成前端声学算法（上位机集成）<br/> • AIUI SDK，集成云端交互能力（上位机集成）',
        },
        {
          title: '服务',
          sub_title: ' • 为期两个月的VIP技术支持<br/> • 浅定制资源定制支持',
        },
      ],
      develop_doc: [
        {
          name: ' • 《AIUI USB声卡开发套件产品白皮书.pdf》',
          link: 'https://aiui-doc.xf-yun.com/project-1/doc-164/',
        },
        {
          name: ' • 《AIUI USB声卡开发套件使用指南.pdf》',
          link: 'https://aiui-doc.xf-yun.com/project-1/doc-165/',
        },
      ],
      point_list: [
        {
          src: require('../../../../assets/images/solution/soft-hardware/USB/td1.png'),
          title: '兼容多类型麦克风阵列',
          sub_title: '支持线性四麦/线性六麦/环形六麦 阵列构型',
        },
        {
          src: require('../../../../assets/images/solution/soft-hardware/USB/td2.png'),
          title: '180°/360°定向拾音',
          sub_title: '支持声源定位 ，拾音距离可达5米',
        },
        {
          src: require('../../../../assets/images/solution/soft-hardware/USB/td3.png'),
          title: '尺寸小巧易集成',
          sub_title: '89mm*38mm',
        },
        {
          src: require('../../../../assets/images/solution/soft-hardware/USB/td4.png'),
          title: '扩展能力丰富',
          sub_title: '对接上位机可实现前端声学，语音 交互等功能',
        },
      ],
    }
  },
  methods: {
    toConsole() {
      // 判断是否有百度SEM营销参数，有的话，提交表单页需要带上
      const search = window.location.search
      if (search) {
        window.open(`/solution/apply/10${search}`)
      } else {
        window.open('/solution/apply/10')
      }
    },
    toBuy() {
      window.open(`https://www.aifuwus.com/onstage/cmddetail?id=3062`)
    },
    toDoc(link) {
      window.open(link)
    },
  },
  components: { corp },
}
</script>

<style lang="scss" scoped>
@import '../../../../assets/scss/screen-and-lamp.scss';

@media screen and (min-width: 751px) {
  .main-content {
    &-banner {
      background: url(~@A/images/solution/soft-hardware/USB/banner_bg.png)
        center no-repeat;
      background-size: cover;
      height: 500px;
      overflow: hidden;
      width: 100%;

      .banner-text {
        max-width: 1200px;
        color: #fff;
        height: 100%;
        margin: auto;

        .hor-btn {
          display: flex;
          flex-direction: row;
          justify-content: flex-start;

          div:nth-child(2) {
            margin-left: 30px;
          }
        }

        &-button {
          font-size: 16px;
          text-align: center;
          font-weight: 400;
          width: 180px;
          height: 50px;
          line-height: 50px;
          border-radius: 4px;
          color: #fff;
          cursor: pointer;
          transition: 0.6s;
          background: $primary;
          border: none;
        }

        &-buy {
          font-size: 16px;
          text-align: center;
          font-weight: 400;
          width: 180px;
          height: 50px;
          line-height: 50px;
          cursor: pointer;
          transition: 0.6s;
          background: #ffffff;
          border: 1px solid $primary;
          border-radius: 4px;
          color: $primary;
        }

        h2 {
          color: #000;
          padding-top: 148px;
          margin-bottom: 25px;
          font-size: 44px;
          font-weight: 600;
          line-height: 44px;
          font-family: PingFang SC, PingFang SC-Semibold;
        }

        p {
          font-size: 18px;
          margin-bottom: 25px;
        }

        .banner-text-content {
          width: 570px;
          font-size: 16px;
          font-weight: 400;
          color: #181818;
          line-height: 30px;
          font-family: PingFang SC, PingFang SC-Regular;
        }
      }
    }
  }

  .section-title {
    font-size: 34px;
    font-weight: bold;
    color: #333;
    margin-top: 0 !important;
    margin-bottom: 30px !important;
  }

  .section-sub-title {
    font-size: 16px;
    font-weight: 400;
    text-align: center;
    color: #666666;
    margin-bottom: 60px !important;
  }

  .app-text {
    color: #666;
  }

  .corp {
    padding-top: 76px;
    height: 320px;
    background: url(~@A/images/solution/acoustics/corp.png) center/cover
      no-repeat;

    .corp-section-wrap {
      width: 1200px;
      margin: 0 auto;
    }

    .corp-section-title-contact {
      font-size: 36px !important;
      font-weight: 400;
      color: #000000 !important;
      text-align: left !important;
      margin-top: 0 !important;
    }

    .corp-section-desc2 {
      text-align: left;
      margin-top: 20px !important;
      font-size: 18px;
      line-height: 30px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #262626 !important;
      display: block;
    }

    .corp-section-button {
      font-size: 16px;
      text-align: center;
      line-height: 50px;
      border: none;
      color: #fff;
      cursor: pointer;
      width: 180px;
      height: 50px;
      background: $primary;
      border-radius: 4px;
    }
  }

  .section-2 {
    .section-item {
      margin-top: 50px !important;

      > ul {
        display: flex;
        justify-content: space-between !important;

        li {
          width: 285px !important;
          position: relative;

          img {
            width: 100%;
          }

          .app-text {
            position: absolute;
            top: 0;
            left: 50%;
            transform: translate(-50%, 0);
            margin-top: 30px !important;
          }
        }
      }
    }
  }

  .section-3 {
    height: 400px;
    width: 100%;
    background-color: #fff !important;

    .section-item {
      ul {
        display: flex;
        margin-top: 65px;

        li {
          width: 285px;
          height: 220px;
          background: linear-gradient(187deg, #f5f7f9 0%, #ffffff 78%);
          border: 2px solid #ffffff;
          box-shadow: -7px 0px 27.55px 0.35px rgba(188, 198, 216, 0.3);
          margin: 0 10px;
          padding: 72px 32px 50px 32px;
          position: relative;
          display: inline-block !important;

          img {
            position: absolute;
            top: -45px;
            left: 50%;
            transform: translate(-50%, 0);
          }

          .title {
            text-align: center;
            font-size: 16px;
            font-family: PingFang SC, PingFang SC-Medium;
            font-weight: 500;
            color: #262626;
          }

          .sub_title {
            margin-top: 20px;
            font-size: 14px;
            font-family: PingFang SC, PingFang SC-Regular;
            color: #666666;
            line-height: 30px;
          }

          &:nth-child(3) {
            .sub_title {
              text-align: center;
            }
          }
        }
      }
    }

    .section-title {
      margin-bottom: 0 !important;
    }
  }

  .section-4 {
    height: 500px !important;
    width: 100%;
    background: url('../../../../assets/images/solution/soft-hardware/USB/banner_bg1.png')
      center no-repeat !important;
    background-size: cover !important;

    .section-title {
      margin-bottom: 0 !important;
    }

    .section-item {
      margin-top: 0 !important;
      padding: 0 !important;
      background: transparent !important;
      max-width: 1200px !important;
      height: 100%;
      margin: 0 auto;
      position: relative;

      div {
        p {
          &:nth-child(1),
          &:nth-child(2),
          &:nth-child(3),
          &:nth-child(4) {
            font-size: 14px;
            font-family: PingFang SC, PingFang SC-Regular;
            font-weight: 400;
            text-align: center;
            color: #262626;
            line-height: 21px;
            position: absolute;
            text-align: center;
          }

          &:nth-child(1) {
            left: 140px;
            top: 150px;
          }

          &:nth-child(2) {
            left: 770px;
            top: 100px;
          }

          &:nth-child(3) {
            left: 776px;
            top: 240px;
          }

          &:nth-child(4) {
            right: 42px;
            top: 40px;
          }
        }
      }
    }
  }

  .section-5 {
    background: url('../../../../assets/images/solution/soft-hardware/USB/yj.png')
      center no-repeat !important;
    background-size: cover !important;
    width: 100%;
    height: 620px;
    max-width: 100% !important;
    position: relative;

    .section-title {
      color: #fff;
    }

    .section-item {
      margin: 0 auto;
      max-width: 1200px;
      height: 100%;
      position: relative;

      div:nth-child(1) {
        p {
          font-size: 14px;
          font-family: PingFang SC, PingFang SC-Regular;
          font-weight: 400;
          color: #ffffff;
          position: absolute;
        }

        p:nth-child(1) {
          left: 630px;
          top: -10px;
        }

        p:nth-child(2) {
          left: 80px;
          top: 196px;
        }

        p:nth-child(3) {
          top: 170px;
          right: 130px;
        }

        p:nth-child(4) {
          top: 246px;
          right: 130px;
        }

        p:nth-child(5) {
          bottom: 70px;
          left: 400px;
        }

        p:nth-child(6) {
          bottom: 70px;
          left: 558px;
        }

        p:nth-child(7) {
          bottom: 70px;
          left: 720px;
        }
      }
    }
  }

  .section-6 {
    .section-item {
      margin-top: 50px;
      background: url('../../../../assets/images/solution/soft-hardware/3328/section-6.png')
        center no-repeat !important;
      background-size: contain !important;
      height: 385px;
      width: 100%;
      max-width: 1200px;
      position: relative;
    }
  }

  .section-8 {
    .section-item {
      display: flex;
      flex-direction: row;
      justify-content: space-around;
      align-items: center;

      .item {
        text-align: center;
        height: 151px;
        width: 380px;
        background: url('../../../../assets/images/solution/soft-hardware/USB/qd1.png')
          center no-repeat !important;
        background-size: contain !important;

        &:nth-child(2) {
          background: url('../../../../assets/images/solution/soft-hardware/USB/qd2.png')
            center no-repeat !important;
          background-size: contain !important;
        }

        &:nth-child(3) {
          background: url('../../../../assets/images/solution/soft-hardware/USB/qd3.png')
            center no-repeat !important;
          background-size: contain !important;
        }

        .title {
          font-size: 18px;
          font-family: PingFang SC, PingFang SC-Semibold;
          font-weight: 600;
          text-align: left;
          color: #262626;
          line-height: 30px;
          text-align: center;
          margin-top: 20px;
        }

        p {
          font-size: 14px;
          font-family: PingFang SC, PingFang SC-Regular;
          font-weight: 400;
          margin-left: 52px;
          text-align: left;
          color: #666666;
          line-height: 30px;
          margin-top: 28px;
        }
      }
    }
  }

  .section-9 {
    width: 100%;
    max-width: 100% !important;
    position: relative;

    .section-item {
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      background: url('../../../../assets/images/solution/soft-hardware/USB/cl.png')
        center no-repeat !important;
      background-size: contain !important;
      height: 224px;

      .item {
        padding-top: 50px;
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        grid-gap: 10px;
      }
    }
  }
}

@media screen and (max-width: 750px) {
  .main-content {
    &-banner {
      background: url('../../../../assets/images/solution/soft-hardware/3328/banner_bg.png')
        center no-repeat;
      background-size: cover;
    }
  }
}

.contact-wrap {
  // padding-top: 100px;
  height: 400px;
  text-align: center;

  .title {
    margin-bottom: 16px;
    font-size: 34px;
    color: #333;
    font-weight: bold;
  }

  .desc {
    font-size: 16px;
    color: #666;
    margin-bottom: 50px;
  }

  .apply-btn {
    margin: 60px auto 0;
    background: #1784e9;

    &:hover {
      color: #fff;
    }
  }
}
</style>
