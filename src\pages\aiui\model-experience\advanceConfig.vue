<template>
  <div>
    <div style="position: relative; min-width: 300px">
      <div class="control-btns control-btns-left">
        <div class="selector-title-wrap">
          <i class="selector-icon advance-icon"></i>
          <span>高级设置</span>
          <div class="selector-divider"></div>
        </div>
        <ul class="type-wrapper">
          <advance-checkbox
            :value="personaChecked"
            @click.native="onPersonaClick"
            >设备人设</advance-checkbox
          >
          <advance-checkbox
            v-if="
              !(current === 'product' && Number(currentProduct.productId) === 2)
            "
            :value="styleChecked"
            @click.native="onReplyStyleClick"
            >回复风格</advance-checkbox
          >
          <advance-checkbox :value="streamTTSChecked" @click.native="onVcnClick"
            >流式合成</advance-checkbox
          >
        </ul>
      </div>
    </div>
    <device-dialog
      :dialog="deviceDialog"
      :personaConfig="personaConfig"
      @setPersonaConfig="savePersonaConfigToLocalStorage"
    ></device-dialog>
    <reply-style-dialog
      :dialog="replyStyleDialog"
      :replyStyleConfig="replyStyleConfig"
      @setReplyStyleConfig="saveReplyStyleConfigToLocalStorage"
    ></reply-style-dialog>
    <tts-dialog
      :dialog="ttsDialog"
      :vcnConfig="vcnConfig"
      :speakers="speakers"
      :currentChain="currentChain"
      :current="current"
      @setVcn="saveVcnToLocalStorage"
    ></tts-dialog>
  </div>
</template>
<script>
import advanceCheckbox from './advanceCheckbox.vue'
import deviceDialog from './deviceDialog.vue'
import replyStyleDialog from './replyStyleDialog.vue'
import ttsDialog from './ttsDialog.vue'
import { bus } from '@U/bus'

export default {
  components: { advanceCheckbox, deviceDialog, replyStyleDialog, ttsDialog },
  props: {
    vcnConfig: Object,
    replyStyleConfig: Object,
    personaConfig: Object,
    speakers: Array,
    currentChain: String,
    current: String,
    currentProduct: Object,
  },
  data() {
    return {
      personaChecked: false,
      styleChecked: false,
      streamTTSChecked: false,
      deviceDialog: {
        show: false,
      },
      replyStyleDialog: {
        show: false,
      },
      ttsDialog: {
        show: false,
      },
    }
  },
  created() {
    // 读取本地发音人localStorage配置
    this.readVcnFromLocalStorage()

    // 读取回复风格配置
    this.readReplyStyleConfigFromLocalStorage()

    // 读取设备人设配置
    this.readPersonaConfigFromLocalStorage()

    bus.$on('REMOVE_VCN_CONFIG', () => {
      // console.log('REMOVE_VCN_CONFIG receive')
      this.removeVcnConfig()
    })
  },
  beforeDestroy() {
    //
  },
  methods: {
    onPersonaClick() {
      if (this.personaChecked) {
        this.turnOffPersonaConfig()
      } else {
        this.deviceDialog.show = true
      }
    },
    onReplyStyleClick() {
      if (this.styleChecked) {
        this.turnOffReplyStyleConfig()
      } else {
        this.replyStyleDialog.show = true
      }
    },
    onVcnClick() {
      if (this.streamTTSChecked) {
        this.turnOffVcnConfig()
      } else {
        this.ttsDialog.show = true
      }
    },
    turnOffPersonaConfig() {
      const rsConfig = localStorage.getItem('AIUI_PERSONA_CONFIG')
      if (rsConfig) {
        try {
          const config = JSON.parse(rsConfig)
          const newCfg = { ...config, isOn: '0' }
          localStorage.setItem('AIUI_PERSONA_CONFIG', JSON.stringify(newCfg))
          this.$emit('setPersonaConfig', newCfg)
          this.personaChecked = false
        } catch (e) {}
      }
    },
    turnOffReplyStyleConfig() {
      const rsConfig = localStorage.getItem('AIUI_REPLY_STYLE_CONFIG')
      if (rsConfig) {
        try {
          const config = JSON.parse(rsConfig)
          const newCfg = { ...config, isOn: '0' }
          localStorage.setItem(
            'AIUI_REPLY_STYLE_CONFIG',
            JSON.stringify(newCfg)
          )
          this.$emit('setReplyStyleConfig', newCfg)
          this.styleChecked = false
        } catch (e) {}
      }
    },
    turnOffVcnConfig() {
      const vcnConfig = localStorage.getItem('AIUI_VCN_CONFIG')
      if (vcnConfig) {
        try {
          const config = JSON.parse(vcnConfig)
          const newCfg = { ...config, isOn: '0' }
          localStorage.setItem('AIUI_VCN_CONFIG', JSON.stringify(newCfg))
          this.$emit('setVcnConfig', newCfg)
          this.streamTTSChecked = false
        } catch (e) {}
      }
    },

    removeVcnConfig() {
      localStorage.removeItem('AIUI_VCN_CONFIG')
      this.$emit('setVcnConfig', null)
      this.streamTTSChecked = false
    },

    readPersonaConfigFromLocalStorage() {
      const rsConfig = localStorage.getItem('AIUI_PERSONA_CONFIG')
      if (rsConfig) {
        let config = JSON.parse(rsConfig)
        if (config.personaName && config.personaFather) {
          this.$emit('setPersonaConfig', config)
          if (config.isOn === '1') {
            this.personaChecked = true
          } else {
            this.personaChecked = false
          }
        } else {
          this.$emit('setPersonaConfig', null)
          this.personaChecked = false
        }
      } else {
        this.$emit('setPersonaConfig', null)
        this.personaChecked = false
      }
    },

    readReplyStyleConfigFromLocalStorage() {
      const rsConfig = localStorage.getItem('AIUI_REPLY_STYLE_CONFIG')
      if (rsConfig) {
        let config = JSON.parse(rsConfig)
        if (config.guideType && config.guideValues) {
          this.$emit('setReplyStyleConfig', config)
          if (config.isOn === '1') {
            this.styleChecked = true
          } else {
            this.styleChecked = false
          }
        } else {
          this.$emit('setReplyStyleConfig', null)
          this.styleChecked = false
        }
      } else {
        this.$emit('setReplyStyleConfig', null)
        this.styleChecked = false
      }
    },
    readVcnFromLocalStorage() {
      const vcnConfig = localStorage.getItem('AIUI_VCN_CONFIG')
      if (vcnConfig) {
        let config = JSON.parse(vcnConfig)
        if (config.vcn) {
          this.$emit('setVcnConfig', config)
          if (config.isOn === '1') {
            this.streamTTSChecked = true
          } else {
            this.streamTTSChecked = false
          }
        } else {
          this.$emit('setVcnConfig', null)
          this.streamTTSChecked = false
        }
      } else {
        this.$emit('setVcnConfig', null)
        this.streamTTSChecked = false
      }
    },

    savePersonaConfigToLocalStorage(config = {}) {
      let newCfg = { ...config, isOn: '1' }
      localStorage.setItem('AIUI_PERSONA_CONFIG', JSON.stringify(newCfg))
      this.$emit('setPersonaConfig', newCfg)
      this.personaChecked = true
    },

    saveReplyStyleConfigToLocalStorage(config = {}) {
      let newCfg = { ...config, isOn: '1' }
      localStorage.setItem('AIUI_REPLY_STYLE_CONFIG', JSON.stringify(newCfg))
      this.$emit('setReplyStyleConfig', newCfg)
      this.styleChecked = true
    },
    saveVcnToLocalStorage(vcnCfg = {}) {
      localStorage.setItem(
        'AIUI_VCN_CONFIG',
        JSON.stringify({ ...vcnCfg, isOn: '1' })
      )
      this.$emit('setVcnConfig', { ...vcnCfg, isOn: '1' })
      this.streamTTSChecked = true
    },
  },
}
</script>
<style lang="scss" scoped>
@import './selector.scss';
</style>
