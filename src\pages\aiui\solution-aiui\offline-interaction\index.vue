<template>
  <div class="main_content">
    <section class="main_content_banner">
      <div class="banner_text_wrapper">
        <div class="banner_text"></div>
        <div class="banner_text_button_wrap">
          <div class="button primary_button" @click="toConsole">合作咨询</div>
          <!-- <div class="button text_button" plain @click="toBuy">立即购买</div> -->
        </div>
      </div>
    </section>

    <div class="section section1">
      <div class="section_title">适用人群</div>
      <div class="section_content">
        <div
          class="content_item"
          v-for="(item, index) in applicableAudiences"
          :key="index"
        >
          <div class="item_img">
            <img :src="item.imgSrc" :alt="item.title" />
          </div>
          <div class="item_icon">
            <img :src="item.iconSrc" :alt="item.title" />
          </div>
          <div class="item_title">{{ item.title }}</div>
        </div>
      </div>
    </div>

    <div class="product_function_wrap">
      <img
        src="@/assets/images/solution/offline-interaction/product_function_content_txt.png"
      />
    </div>

    <div class="product_features_wrap">
      <h2>产品特点</h2>
      <p class="product_features_subTitle">内置端侧开源大模型</p>
      <div class="product_features_desc">
        <div class="product_features__desc_item">
          <i class="icon"></i>
          <span>提供2种端侧大模型，支持本地高效推理</span>
        </div>
        <div class="product_features__desc_item">
          <i class="icon"></i>
          <span>支持模型微调，灵活适配业务场景</span>
        </div>
      </div>
      <div class="card_wrapper">
        <div class="model_card deepseek">
          <div class="logo_area">
            <img
              src="@/assets/images/solution/offline-interaction/deepseek_logo.png"
              alt="Deepseek"
            />
          </div>
          <h3>Deepseek-R1-Distill-Qwen-1.5B</h3>
          <ul>
            <li>• 数学推理模型，适合教育、计算任务</li>
            <li>• 低资源占用，推理速度快</li>
          </ul>
        </div>
        <div class="model_card qwen">
          <div class="logo_area">
            <img
              src="@/assets/images/solution/offline-interaction/qwen_logo.png"
              alt="Qwen"
            />
          </div>
          <h3>Qwen2.5-1.5B</h3>
          <ul>
            <li>• 通用模型，适用多种场景</li>
            <li>• 128K超长上下文处理</li>
          </ul>
        </div>
      </div>
    </div>

    <div class="all_Links">
      <div class="all_Links_title">全链路离线交互</div>
      <div class="all_Links_content"></div>
    </div>

    <!-- 语音增强 -->
    <div class="big_card_wrapper" style="margin: 70px auto 80px">
      <p class="subTitle">视听融合多模态语音增强</p>
      <div class="desc">
        <div class="product_features__desc_item">
          <i class="icon"></i>
          <span>看唇形识人声，过滤背景杂音</span>
        </div>
        <div class="product_features__desc_item">
          <i class="icon"></i>
          <span>遮挡自适应，戴口罩也能清晰拾音</span>
        </div>
      </div>
      <div class="content"></div>
    </div>

    <!-- 灵活麦克风阵列方案 -->
    <div class="big_card_wrapper" style="margin: 0 auto">
      <p class="subTitle">灵活麦克风阵列方案</p>
      <div class="desc">
        <div class="product_features__desc_item">
          <i class="icon"></i>
          <span>适配各类产品形态</span>
        </div>
        <div class="product_features__desc_item">
          <i class="icon"></i>
          <span>无需结构改造，快速集成</span>
        </div>
      </div>
      <div class="microphone_card_wrapper">
        <div
          class="microphone_card"
          v-for="(item, index) in microphoneTypes"
          :key="index"
        >
          <img :src="item.imgSrc" :alt="item.title" />
          <h3>{{ item.title }}</h3>
          <div class="microphone_desc">
            <p v-for="(desc, descIndex) in item.descriptions" :key="descIndex">
              {{ desc }}
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- 硬件接口展示 -->
    <div class="hardware_interface_wrap">
      <h2>硬件接口展示</h2>
      <div class="interface_cards">
        <div class="interface_card">
          <div class="card_label">正面接口</div>
          <img
            style="width: 881px; height: 604px"
            src="@/assets/images/solution/offline-interaction/front-interface.jpg"
            alt="正面接口布局"
          />
        </div>
        <div class="interface_card" style="padding-top: 50px">
          <div class="card_label">背面接口</div>
          <img
            style="width: 941px; height: 486px"
            src="@/assets/images/solution/offline-interaction/back-interface.jpg"
            alt="背面接口布局"
          />
        </div>
      </div>
    </div>

    <!-- 接线示意图 -->
    <div class="wiring_diagram_wrap" style="margin-top: 100px">
      <h2>接线示意图</h2>
      <div class="wiring_content">
        <img
          src="@/assets/images/solution/offline-interaction/wiring.png"
          alt="接线示意图"
        />
      </div>
    </div>

    <!-- 硬件参数 -->
    <div class="hardware_specs_wrap">
      <div class="hardware_specs_card">
        <h2>硬件参数</h2>
        <div class="specs_table_container">
          <table class="specs_table">
            <tr v-for="(item, index) in hardwareSpecs" :key="index">
              <td class="specs_label">{{ item.label }}</td>
              <td class="specs_value">{{ item.value }}</td>
            </tr>
          </table>
        </div>
      </div>
    </div>

    <!-- 合作咨询 -->
    <div class="cooperation_consulting_wrapper">
      <div class="banner-text">
        <h2>立即联系您的专属顾问</h2>
        <p class="banner_text_content">
          免费咨询专属顾问 为您量身定制产品推荐方案<br />
        </p>
        <div class="banner-text-button" @click="toConsole">合作咨询</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AiuiWebChildToys',

  data() {
    return {
      applicableAudiences: [
        {
          title: '个人开发者',
          imgSrc: require('@/assets/images/solution/offline-interaction/applicable_audience1.png'),
          iconSrc: require('@/assets/images/solution/offline-interaction/applicable_audience1_icon.png'),
        },
        {
          title: '高校科研机构',
          imgSrc: require('@/assets/images/solution/offline-interaction/applicable_audience2.png'),
          iconSrc: require('@/assets/images/solution/offline-interaction/applicable_audience2_icon.png'),
        },
        {
          title: '学生',
          imgSrc: require('@/assets/images/solution/offline-interaction/applicable_audience3.png'),
          iconSrc: require('@/assets/images/solution/offline-interaction/applicable_audience3_icon.png'),
        },
        {
          title: '创客',
          imgSrc: require('@/assets/images/solution/offline-interaction/applicable_audience4.png'),
          iconSrc: require('@/assets/images/solution/offline-interaction/applicable_audience4_icon.png'),
        },
        {
          title: '企业开发者',
          imgSrc: require('@/assets/images/solution/offline-interaction/applicable_audience5.png'),
          iconSrc: require('@/assets/images/solution/offline-interaction/applicable_audience5_icon.png'),
        },
      ],
      microphoneTypes: [
        {
          title: '线性2麦',
          imgSrc: require('@/assets/images/solution/offline-interaction/microphone-type1.png'),
          descriptions: ['专为近场交互优化', '适用于桌面级设备'],
        },
        {
          title: '线性4麦',
          imgSrc: require('@/assets/images/solution/offline-interaction/microphone-type2.png'),
          descriptions: ['前向增强拾音', '适配高度>150cm设备'],
        },
        {
          title: '环形6麦',
          imgSrc: require('@/assets/images/solution/offline-interaction/microphone-type3.png'),
          descriptions: ['360°全向收音', '适配高度<150cm设备'],
        },
      ],
      hardwareSpecs: [
        { label: '电源接口', value: 'Type-C5V@4A直流输入' },
        {
          label: '主芯片',
          value: 'RK3588S(四核A76+四核A55、Mali-G610、6T算力)',
        },
        { label: '内存', value: 'LPDDR4X-4/8GB' },
        { label: '存储', value: 'eMMC-32/64/128GB' },
        { label: '以太网', value: '10/100/1000M自适应以太口' },
        { label: 'USB2.0', value: 'Type-A接口x3(HOST)' },
        { label: 'USB3.0', value: 'Type-A接口x1(HOST)' },
        {
          label: 'USB3.0',
          value:
            'Type-C接口x1(OTG)，同伴烧录接口，DP显示(支持与其他屏幕进行多屏异显)',
        },
        { label: 'Debug串口', value: '一路Debug串口，默认参数1500000-8-N-1' },
        {
          label: '按键',
          value: 'ON/OFF(开关机键)、MR(MaskRom)、REC(Recovery)',
        },
        { label: '音频接口', value: '耳机输出+麦克风输入2合1接口' },
        {
          label: '40Pin接口',
          value: '兼容树莓派40Pin接口，支持PWM,GPIO,I2C,SPI,UART功能',
        },
        {
          label: 'MINI-PCIE接口',
          value:
            '可配合全高或半高的WIFI网卡、4G模块或其他MINI-PCIE接口模块使用',
        },
        { label: 'TF卡座', value: '支持TF卡启动系统，最高支持512GB' },
        { label: 'SIM卡接口', value: '需要搭配4G模块使用' },
        { label: '红外接收', value: '支持红外遥控' },
        { label: 'HDMI2.1', value: '显示器接口,支持与其他屏幕进行多屏异显' },
        { label: 'RTC电池接口', value: '支持RTC功能' },
        {
          label: 'MIPI-DSI',
          value: '2xMIPI屏幕接口,支持与其他屏幕进行多屏异显',
        },
        { label: '风扇接口', value: '支持安装风扇散热' },
        { label: 'MIPI-CSI', value: '3x摄像头接口' },
      ],
    }
  },

  mounted() {},

  methods: {
    toConsole() {
      // 判断是否有百度SEM营销参数，有的话，提交表单页需要带上
      const search = window.location.search
      if (search) {
        window.open(`/solution/apply/43${search}`)
      } else {
        window.open('/solution/apply/43')
      }
    },

    toBuy() {
      window.open('https://www.aifuwus.com/onstage/cmddetail?id=4434')
    },
  },
}
</script>

<style lang="scss" scoped>
.main_content {
  background-color: #f5f5f5;
  .main_content_banner {
    background: url(~@A/images/solution/offline-interaction/banner.jpg) center
      no-repeat;
    background-size: cover;
    height: 500px;
    overflow: hidden;
    width: 100%;
  }

  .banner_text_wrapper {
    max-width: 1200px;
    color: #fff;
    height: 100%;
    margin: auto;

    .banner_text {
      width: 592px;
      height: 152px;
      background-image: url('~@A/images/solution/offline-interaction/banner-text.png');
      background-size: contain;
      background-repeat: no-repeat;
      margin: 100px 0 75px 0;
    }
    .banner_text_button_wrap {
      display: flex;
      gap: 30px;
      .button {
        font-size: 18px;
        text-align: center;
        font-weight: 400;
        width: 183px;
        height: 60px;
        line-height: 60px;
        border-radius: 8px;

        cursor: pointer;
        letter-spacing: 1px;
        &.primary_button {
          background: #1d69ff;
          color: #fff;
        }
        &.text_button {
          border: 1.5px solid #1d69ff;
          color: #1d69ff;
          font-weight: 600;
        }
      }
    }
  }

  .section {
    padding: 60px 0 90px;
    text-align: center;
    .section_title {
      margin-bottom: 40px;
      font-size: 34px;
      color: #000000;
    }
    .section_content {
      display: flex;
      justify-content: center;
      gap: 20px;
      flex-wrap: wrap;
    }
    .content_item {
      background-color: #fff;
      padding-bottom: 25px;
      border-radius: 12px 12px 0px 0px;
      .item_img {
        img {
          width: 225px;
          height: 265px;
          object-fit: cover;
          display: block;
        }
      }

      .item_icon {
        padding: 15px 0 10px 20px;
        text-align: left;
        img {
          width: 22px;
          height: 27px;
        }
      }

      &:nth-child(2) {
        .item_icon {
          img {
            width: 23px;
            height: 23px;
          }
        }
      }

      &:nth-child(3) {
        .item_icon {
          img {
            width: 21px;
            height: 27px;
          }
        }
      }

      &:nth-child(4) {
        .item_icon {
          img {
            width: 32px;
            height: 27px;
          }
        }
      }

      &:nth-child(5) {
        .item_icon {
          img {
            width: 24px;
            height: 27px;
          }
        }
      }

      .item_title {
        padding-left: 20px;
        font-size: 18px;
        text-align: left;
      }
    }
  }

  .product_function_wrap {
    height: 747px;
    background: url(~@A/images/solution/offline-interaction/product_function_banner.jpg)
      center no-repeat;
    background-size: cover;
    text-align: center;
    img {
      height: 100%;
    }
  }

  .product_features_wrap {
    text-align: center;
    h2 {
      font-size: 52px;
      color: #0c9afa;
      padding: 90px 0 70px;
    }
    .product_features_subTitle {
      font-size: 34px;
      color: #233a53;
    }
    .product_features_desc {
      margin: 30px 0 60px;
      display: flex;
      justify-content: center;
      gap: 40px;
      .product_features__desc_item {
        display: flex;
        align-items: center;
        i.icon {
          display: inline-block;
          width: 12px;
          height: 12px;
          border-radius: 50%;
          border: 3px solid #00a2ff;
          margin-right: 12px;
        }
        span {
          font-size: 22px;
          font-family: PingFang SC, PingFang SC-300;
          color: #0d223e;
        }
      }
    }
    .card_wrapper {
      display: flex;
      justify-content: center;
      gap: 16px;
      padding-bottom: 106px;
      .model_card {
        padding: 33px 43px;
        border-radius: 20px;
        width: 592px;
        height: 324px;
        text-align: left;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);

        &.deepseek {
          background-color: #dbecff;
        }

        &.qwen {
          background-color: #e1e8ff;
        }

        .logo_area {
          display: inline-block;
          margin-bottom: 20px;

          img {
            height: 46px;
            object-fit: contain;
          }
        }

        h3 {
          font-weight: 600;
          margin-bottom: 30px;

          font-size: 27px;
          font-family: Alibaba PuHuiTi, Alibaba PuHuiTi-500;

          color: #030428;
        }

        ul {
          li {
            padding-left: 10px;
            font-size: 24px;
            color: #030428;
            margin-bottom: 8px;
          }
        }
      }
    }
  }
  .all_Links {
    background: url(~@A/images/solution/offline-interaction/all-links-bg.jpg)
      center no-repeat;
    background-size: cover;
    height: 661px;
    .all_Links_title {
      font-size: 34px;
      color: #fff;
      padding: 90px 0 75px 0;
      text-align: center;
    }
    .all_Links_content {
      width: 1112px;
      height: 327px;
      background: url(~@A/images/solution/offline-interaction/all-links-content.png)
        center no-repeat;
      background-size: cover;
      margin: auto;
    }
  }

  .big_card_wrapper {
    max-width: 1200px;
    border-radius: 12px;
    background-color: #fff;
    padding: 60px 0 50px 0;

    .microphone_card_wrapper {
      display: flex;
      justify-content: center;
      gap: 30px;

      .microphone_card {
        width: 331px;
        height: 395px;
        background-image: url('~@A/images/solution/offline-interaction/microphone-type-bg.png');
        background-size: cover;
        text-align: center;
        display: flex;
        flex-direction: column;
        align-items: center;

        img {
          width: 241px;
          height: 241px;
          margin-top: 15px;
          object-fit: contain;
        }

        h3 {
          font-size: 22px;
          margin-top: 28px;
          color: #030428;
        }
        .microphone_desc {
          margin-top: 18px;
          p {
            font-size: 15px;
            color: rgba(3, 4, 40, 0.6);
            &:last-child {
              margin-top: 10px;
            }
          }
        }
      }
    }
    .subTitle {
      font-size: 34px;
      color: #030428;
      text-align: center;
    }
    .desc {
      display: flex;
      justify-content: center;
      gap: 40px;
      margin: 30px 0 60px 0;
      .product_features__desc_item {
        display: flex;
        align-items: center;
        i.icon {
          display: inline-block;
          width: 12px;
          height: 12px;
          border-radius: 50%;
          border: 3px solid #00a2ff;
          margin-right: 12px;
        }
        span {
          font-size: 22px;
          font-family: PingFang SC, PingFang SC-300;
          color: #0d223e;
        }
      }
    }
    .content {
      width: 1099px;
      height: 486px;
      background: url(~@A/images/solution/offline-interaction/voice-enhancement-content.png)
        center no-repeat;
      background-size: cover;
      margin: auto;
    }
  }

  .hardware_interface_wrap {
    text-align: center;

    h2 {
      font-size: 52px;
      color: #0c9afa;
      margin: 100px 0 60px 0;
    }

    .interface_cards {
      display: flex;
      flex-direction: column;
      gap: 40px;
      align-items: center;
    }

    .interface_card {
      position: relative;
      width: 1200px;
      background: #fff;
      border-radius: 20px;
      border: 1px solid rgba(255, 255, 255, 0.14);
      padding: 30px;

      .card_label {
        position: absolute;
        top: 0;
        left: 0;
        width: 152px;
        height: 50px;
        background: linear-gradient(261deg, #9fdff2 0%, #0c9afa 100%);
        border-radius: 20px 0px 20px 0px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 18px;
      }

      img {
        display: block;
        margin: 0 auto;
      }
    }
  }
  .wiring_diagram_wrap {
    height: 661px;
    background: linear-gradient(180deg, #ffffff, #e0efff 100%);
    text-align: center;
    padding-top: 100px;

    h2 {
      font-size: 34px;
      color: #030428;
      margin-bottom: 84px;
    }

    .wiring_content {
      display: flex;
      justify-content: center;

      img {
        height: 370px;
        object-fit: contain;
      }
    }
  }

  .hardware_specs_wrap {
    padding: 60px 0 80px 0;
    display: flex;
    justify-content: center;

    .hardware_specs_card {
      width: 1200px;
      background: #fff;
      border-radius: 20px;
      padding: 35px 50px 50px 50px;

      h2 {
        font-size: 34px;
        color: #030428;
        text-align: center;
        margin-bottom: 40px;
      }

      .specs_table_container {
        overflow: hidden;
        border-radius: 20px;
        border: 1px solid #cde3fb;
      }

      .specs_table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
        background: #f6faff;
        overflow: hidden;

        tr:first-child {
          td:first-child {
            border-top-left-radius: 20px;
          }
          td:last-child {
            border-top-right-radius: 20px;
          }
        }

        tr:last-child {
          td:first-child {
            border-bottom-left-radius: 20px;
          }
          td:last-child {
            border-bottom-right-radius: 20px;
          }
        }

        tr {
          height: 50px;

          &:not(:last-child) {
            td {
              border-bottom: 1px solid #cde3fb;
            }
          }
        }

        td {
          padding: 0 20px;
          font-size: 18px;
          color: #030428;

          &.specs_label {
            width: 20%;
            border-right: 1px solid #cde3fb;
            text-align: center;
          }

          &.specs_value {
            width: 80%;
            padding-left: 30px;
          }
        }
      }
    }
  }

  .cooperation_consulting_wrapper {
    background: url(~@A/images/solution/child-education/img_guwenBG.png) center
      no-repeat;
    background-size: cover;
    height: 300px;
    overflow: hidden;
    width: 100%;
    .banner-text {
      max-width: 1200px;
      color: #fff;
      height: 100%;
      margin: auto;
      &-button {
        font-size: 16px;
        text-align: center;
        font-weight: 400;
        width: 140px;
        height: 40px;
        line-height: 40px;
        background: #1d69ff;
        border-radius: 4px;
        color: #fff;
        cursor: pointer;
      }
      h2 {
        color: #181818;
        padding-top: 50px;
        margin-bottom: 20px;
        font-size: 36px;
        font-weight: 400;
        line-height: 48px;
      }
      p {
        font-size: 18px;
        margin-bottom: 50px;
      }

      .banner_text_content {
        width: 570px;
        font-size: 16px;
        font-family: SourceHanSansSC-Regular, SourceHanSansSC;
        font-weight: 400;
        color: #444444;
        line-height: 30px;
      }
    }
  }
}
</style>
