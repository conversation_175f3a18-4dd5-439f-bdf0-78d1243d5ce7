import aiuiDefaultLayout from '@L/aiuiDefaultLayout'
import AiuiAppLayout from '@P/aiui/app/aiuiApplicationLayout'
import defaultLayout from '@L/defaultLayout'

export default [
  {
    path: '/sub/apps',
    component: aiuiDefaultLayout,
    children: [
      {
        path: '/',
        name: 'sub-apps', // 我的应用页
        meta: {
          title: 'AIUI开放平台',
        },
        component: () =>
          import(/* webpackChunkName: "apps" */ '@P/aiui/apps/index'),
      },
      {
        path: 'add',
        name: 'sub-apps-add', // 新增应用页
        meta: {
          title: 'AIUI开放平台',
        },
        component: () =>
          import(/* webpackChunkName: "apps-add" */ '@P/aiui/apps/add'),
      },
    ],
  },
  {
    path: '/sub/app/:appId',
    component: AiuiAppLayout,

    children: [
      {
        path: '/',
        name: 'sub-app', // 应用信息页
        redirect: { name: 'sub-app-config' },
      },
      {
        path: 'info',
        name: 'sub-app-info', // 应用信息页
        meta: {
          title: 'AIUI开放平台',
        },
        component: () =>
          import(/* webpackChunkName: "app-info" */ '@P/aiui/app/info'),
      },
      {
        path: 'config',
        name: 'sub-app-config', // 应用配置页
        meta: {
          title: 'AIUI开放平台',
        },
        component: () =>
          import(
            /* webpackChunkName: "app-configs" */ '@P/aiui/app/config/main'
          ),
      },
      {
        path: 'tool',
        name: 'sub-app-tool', // 开发工具
        meta: {
          title: 'AIUI开放平台',
        },
        component: () =>
          import(/* webpackChunkName: "app-tool" */ '@P/aiui/app/tool'),
      },
      {
        path: 'resource',
        name: 'sub-make-resource', // 离在线资源制作
        meta: {
          title: 'AIUI开放平台',
        },
        component: () =>
          import(
            /* webpackChunkName: "make-resource" */ '@P/aiui/app/resource'
          ),
      },
      {
        path: 'online',
        name: 'sub-app-online-device', // 激活设备明细
        meta: {
          title: 'AIUI开放平台',
        },
        component: () =>
          import(
            /* webpackChunkName: "app-online-device" */ '@P/aiui/app/onlineDevice/index'
          ),
      },
      {
        path: 'interact',
        name: 'sub-interact-record', // 用户交易记录
        meta: {
          title: 'AIUI开放平台',
        },
        component: () =>
          import(
            /* webpackChunkName: "app-online-device" */ '@P/aiui/app/interactRecord'
          ),
      },
      {
        path: 'app-sources',
        name: 'sub-app-sources', // 信源授权统计
        component: () =>
          import(
            /* webpackChunkName: "app-statistic-user" */ '@P/aiui/app/source'
          ),
      },
      {
        path: 'whitelist',
        name: 'sub-app-whitelist', // 应用IP白名单页
        meta: {
          title: 'AIUI开放平台',
        },
        component: () =>
          import(
            /* webpackChunkName: "app-white-list" */ '@P/aiui/app/whitelist'
          ),
      },
      {
        path: 'audit',
        name: 'sub-app-audit', // 应用审核页
        meta: {
          title: 'AIUI开放平台',
        },
        component: () =>
          import(/* webpackChunkName: "app-audit" */ '@P/aiui/app/audit'),
      },
      {
        path: 'otaUpdate',
        name: 'sub-app-ota-update', // 固件升级页
        meta: {
          title: 'AIUI开放平台',
        },
        component: () =>
          import(
            /* webpackChunkName: "app-ota-update" */ '@P/aiui/app/otaUpdate'
          ),
      },
      {
        path: 'otaUpdateForm',
        name: 'sub-app-ota-update-form', // 固件上传页
        meta: {
          title: 'AIUI开放平台',
        },
        component: () =>
          import(
            /* webpackChunkName: "app-ota-update-form" */ '@P/aiui/app/otaUpdateForm'
          ),
      },
      {
        path: 'publish',
        name: 'sub-app-publish', // 应用更新发布页
        meta: {
          title: 'AIUI开放平台',
        },
        component: () =>
          import(
            /* webpackChunkName: "app-publish" */ '@P/aiui/app/sandbox/publish'
          ),
      },
      {
        path: 'version',
        name: 'sub-app-version', // 应用版本管理页
        meta: {
          title: 'AIUI开放平台',
        },
        component: () =>
          import(
            /* webpackChunkName: "app-version" */ '@P/aiui/app/sandbox/version'
          ),
      },

      {
        path: 'users',
        name: 'sub-app-users', // 用户统计
        component: () =>
          import(
            /* webpackChunkName: "app-statistic-user" */ '@P/aiui/app/users'
          ),
      },
      {
        path: 'services',
        name: 'sub-app-statistic-service-index', // 服务统计
        meta: {
          title: 'AIUI开放平台',
        },
        component: () =>
          import(
            /* webpackChunkName: "app-statistic-service-index" */ '@P/aiui/app/statisticServiceIndex'
          ),
      },
      {
        path: 'optimize',
        name: 'sub-app-optimize', // 在线优化
        meta: {
          title: 'AIUI开放平台',
        },
        component: () =>
          import(
            /* webpackChunkName: "app-statistic-optimize" */ '@P/aiui/app/optimize'
          ),
      },
    ],
  },
  {
    path: '/sub/app/:appId/versionDiff',
    component: defaultLayout,
    children: [
      {
        path: '/',
        name: 'sub-app-version-diff', // 应用版本对比页
        component: () =>
          import(
            /* webpackChunkName: "app-version-diff" */ '@P/aiui/app/sandbox/versionDiff'
          ),
      },
    ],
  },
]
