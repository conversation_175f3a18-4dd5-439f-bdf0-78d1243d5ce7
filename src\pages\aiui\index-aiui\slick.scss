/* Slider */
.hm-banner {
  .slick-slider {
    position: relative;
    display: block;
    box-sizing: border-box;

    // 禁止文本元素被选中
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;

    // 禁止文本元素被拖动
    -webkit-touch-callout: none;
    -khtml-user-select: none;
    -ms-touch-action: pan-y;
    touch-action: pan-y;
    -webkit-tap-highlight-color: transparent;
  }

  .slick-slider {
    height: 416px;
  }

  .slick-list {
    position: relative;

    display: block;
    overflow: hidden;
  }

  .slick-list:focus {
    outline: none;
  }

  .slick-list.dragging {
    cursor: pointer;
    cursor: hand;
  }

  .slick-slider .slick-track,
  .slick-slider .slick-list {
    -webkit-transform: translate3d(0, 0, 0);
    -moz-transform: translate3d(0, 0, 0);
    -ms-transform: translate3d(0, 0, 0);
    -o-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }

  .slick-track {
    position: relative;
    top: 0;
    left: 0;
    display: block;
    margin-left: auto;
    margin-right: auto;
  }

  .slick-track:before,
  .slick-track:after {
    display: table;

    content: '';
  }

  .slick-track:after {
    clear: both;
  }

  .slick-loading .slick-track {
    visibility: hidden;
  }

  .slick-slide {
    display: none;
    float: left;

    height: 100%;
    min-height: 1px;
  }

  [dir='rtl'] .slick-slide {
    float: right;
  }

  .slick-slide img {
    display: block;
  }

  .slick-slide.slick-loading img {
    display: none;
  }

  .slick-slide.dragging img {
    pointer-events: none;
  }

  .slick-initialized .slick-slide {
    display: block;
  }

  .slick-loading .slick-slide {
    visibility: hidden;
  }

  .slick-vertical .slick-slide {
    display: block;
    height: auto;
    border: 1px solid transparent;
  }

  .slick-arrow.slick-hidden {
    display: none;
  }

  /* Slider */
  // .slick-loading .slick-list {
  //   background: #fff url('./ajax-loader.gif') center center no-repeat;
  // }

  /* Icons */
  // @font-face {
  //   font-weight: normal;
  //   font-style: normal;

  //   src: url('./fonts/slick.eot');
  //   src: url('./fonts/slick.eot?#iefix') format('embedded-opentype'), url('./fonts/slick.woff') format('woff'), url('./fonts/slick.ttf') format('truetype'), url('./fonts/slick.svg#slick') format('svg');
  // }

  /* Arrows */
  .slick-prev,
  .slick-next {
    font-size: 0;
    line-height: 0;
    position: absolute;
    top: 50%;
    display: block;
    width: 20px;
    height: 20px;
    padding: 0;
    -webkit-transform: translate(0, -50%);
    -ms-transform: translate(0, -50%);
    transform: translate(0, -50%);

    cursor: pointer;

    color: transparent;
    border: none;
    outline: none;
    background: transparent;
  }

  .slick-prev:hover,
  .slick-prev:focus,
  .slick-next:hover,
  .slick-next:focus {
    color: transparent;
    outline: none;
    background: transparent;
  }

  .slick-prev:hover:before,
  .slick-prev:focus:before,
  .slick-next:hover:before,
  .slick-next:focus:before {
    opacity: 1;
  }

  .slick-prev.slick-disabled:before,
  .slick-next.slick-disabled:before {
    opacity: 0.25;
  }

  .slick-prev:before,
  .slick-next:before {
    font-size: 20px;
    line-height: 1;
    opacity: 0.75;
    color: #ffffff;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  .slick-prev {
    left: -25px;
  }

  [dir='rtl'] .slick-prev {
    right: -25px;
    left: auto;
  }

  .slick-prev:before {
    content: '←';
  }

  [dir='rtl'] .slick-prev:before {
    content: '→';
  }

  .slick-next {
    right: -25px;
  }

  [dir='rtl'] .slick-next {
    right: auto;
    left: -25px;
  }

  .slick-next:before {
    content: '→';
  }

  [dir='rtl'] .slick-next:before {
    content: '←';
  }

  /* Dots */
  .slick-dots {
    position: absolute;
    bottom: 48px !important;
    display: block;
    width: 100%;
    list-style: none;
    text-align: left;
    padding-bottom: 15px;

    //dots 位置
    ul {
      box-sizing: border-box;
      width: 1200px;
      margin: 0 auto !important;
      padding-left: 10px;
    }
  }

  .slick-dots ul li {
    position: relative;
    display: inline-block;
    width: 118px;
    margin-right: 12px;
  }

  .hm-banner-dot {
    .hm-dot-title {
      font-size: 12px;
      color: #ffffff;
      opacity: 0.7;
      line-height: 22px;
      width: 100%;
      text-align: center;
    }

    .hm-dot-progress {
      display: block;
      width: 118px;
      cursor: pointer;
      opacity: 0.7;
      height: 18px;
      background-image: linear-gradient(
        to bottom,
        transparent 0,
        transparent 7px,
        rgba(255, 255, 255, 0.42) 7px,
        rgba(255, 255, 255, 0.42) 10px,
        transparent 10px,
        transparent 18px
      );
      white-space: nowrap;
      position: relative;
      overflow: hidden;

      .icon-bug {
        width: 14px;
        height: 12px;
        position: absolute;
        top: 50%;
        left: 0;
        display: none;
        opacity: 0;
        transform: translate3d(-2px, -6.5px, 0);
      }

      &::after {
        content: '';
        display: inline-block;
        position: absolute;
        top: 7px;
        left: 0px;
        width: 0px;
        height: 3px;
        text-align: center;
        background: #ffffff;
        opacity: 0.75;
      }

      &:hover {
        opacity: 1;
      }
    }
  }

  li.slick-active {
    .hm-dot-title {
      font-weight: 600;
      opacity: 1;
    }

    .hm-dot-progress {
      opacity: 1;
      position: relative;

      &::after {
        opacity: 1;
        width: 118px;
        animation: borderAni 3s linear;
        transform-origin: left center;
      }

      .icon-bug {
        z-index: 2;
        opacity: 0;
        display: block;
        animation: bugAni 3s linear;
        animation-fill-mode: forwards;
      }
    }
  }
}

@keyframes borderAni {
  from {
    transform: scaleX(0);
  }

  to {
    transform: scaleX(1);
  }
}

@keyframes bugAni {
  from {
    opacity: 1;
    transform: translate3d(0, -7px, 0);
  }

  to {
    opacity: 1;
    transform: translate3d(104px, -7px, 0);
  }
}
