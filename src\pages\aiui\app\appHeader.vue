<template>
  <div>
    <div class="application-header header1">
      <div class="application-header__left">
        <div @click="backToApp" class="back-icon">
          <back-icon></back-icon>
        </div>

        <div class="config-title">应用配置</div>

        <div class="appname">{{ form.appName }}</div>
        <i class="el-icon-edit" @click="editApp" v-if="form.appName"></i>
      </div>

      <AppHeaderTab v-if="!subAccount"> </AppHeaderTab>

      <div class="application-header__right">
        <div class="appid">{{ $route.params.appId }}</div>
        <el-tooltip
          :content="form.appkey"
          placement="bottom"
          v-if="form.appkey"
        >
          <div class="appkey" @click="copyAppKey(form.appkey)">
            APPKEY
          </div></el-tooltip
        >

        <el-tooltip
          :content="form.apiSecret"
          placement="bottom"
          v-if="form.apiSecret"
        >
          <div class="appkey" @click="copyAppKey(form.apiSecret)">
            APPSECRET
          </div>
        </el-tooltip>
      </div>
    </div>
    <appInfoDialog :dialog="editAppDialog" />
  </div>
</template>

<script>
// :subAccount="subAccount"
//     :subAccountEditable="subAccountEditable"
// import sceneImport from './config/sceneImport'
import { mapGetters } from 'vuex'

// import PublishDialog from './sandbox/publish.vue'
// import VersionDialog from './sandbox/version.vue'
// import AuditDialog from './audit.vue'
import AppHeaderTab from './appHeaderTab.vue'
import appInfoDialog from './dialog/appInfoDialog.vue'

export default {
  data() {
    return {
      form: {
        appName: '',
        appid: '',
        platformNum: '',
        appType: '',
        appTypeName: '',
        appDescription: '',
        appkey: '',
        apiSecret: '',
        deviceType: [],
      },
      editAppDialog: {
        show: false,
      },
    }
  },
  created() {
    // 首次弹窗

    //
    this.appData()
  },
  computed: {
    ...mapGetters({
      app: 'aiuiApp/app',
      currentScene: 'aiuiApp/currentScene',
      subAccount: 'user/subAccount',
      subAccountEditable: 'aiuiApp/subAccountEditable',
    }),
    appId() {
      return this.$route.params.appId
    },
  },
  watch: {
    app: {
      handler(newVal) {
        if (newVal) {
          const formData = this.$deepClone(newVal)
          const deviceType = []
          for (let i = 4; i >= 1; i /= 2) {
            if (formData.deviceType - i >= 0) {
              deviceType.push(i)
              formData.deviceType -= i
            }
          }
          this.form = {
            ...formData,
            deviceType,
          }
        }
      },
      deep: true,
    },
  },
  methods: {
    copyAppKey(value) {
      this.$utils.copyClipboard(value)
      this.$message.success('已复制到剪切板')
    },

    backToApp() {
      this.$router.push({ name: this.subAccount ? 'sub-apps' : 'apps' })
    },

    appData() {
      let self = this
      this.$utils.httpGet(
        this.$config.api.AIUI_APPINFO,
        {
          appid: this.$route.params.appId,
        },
        {
          success: (res) => {
            if (res.flag) {
              const formData = self.$deepClone(res.data)
              const deviceType = []
              for (let i = 4; i >= 1; i /= 2) {
                if (formData.deviceType - i >= 0) {
                  deviceType.push(i)
                  formData.deviceType -= i
                }
              }
              self.form = {
                ...formData,
                deviceType,
              }

              self.$store.dispatch('aiuiApp/setAppInfo', res.data)
            } else {
              self.$router.push({ name: self.subAccount ? 'sub-apps' : 'apps' })
            }
          },
          error: (err) => {
            self.$router.push({ name: self.subAccount ? 'sub-apps' : 'apps' })
          },
        }
      )
    },

    editApp() {
      this.editAppDialog.show = true
    },
  },
  components: {
    // sceneImport,
    // PublishDialog,
    // VersionDialog,
    // AuditDialog,
    AppHeaderTab,
    appInfoDialog,
  },
}
</script>
<style lang="scss" scoped>
.application-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 35px;
  padding: 0 20px;
  &__left {
    display: flex;
    align-items: center;
  }
  &__right {
    display: flex;
    align-items: center;
  }
  .back-icon {
    margin-right: 10px;
    display: flex;
  }

  .config-title {
    font-size: 20px;
    font-weight: 600;
    color: #000000;
    line-height: 20px;
    margin-right: 14px;
  }

  .appid {
    font-size: 14px;
    font-weight: 400;
    color: #8d8d99;
    line-height: 20px;
  }

  .appkey {
    font-size: 14px;
    font-weight: 400;
    color: $primary;
    line-height: 20px;
    margin-left: 12px;
    cursor: pointer;
  }

  .appname {
    font-size: 16px;
    font-weight: 600;
    color: #17171e;
    margin: 0 12px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    max-width: 120px;
  }
}
.header1 {
  height: 63px;
  border-bottom: 1px solid #f4f4f4;
}
.header2 {
  // border-top: 1px solid #f4f4f4;
  height: 64px;
}

.el-icon-edit {
  cursor: pointer;

  &:hover {
    color: #595959;
  }
}
</style>
