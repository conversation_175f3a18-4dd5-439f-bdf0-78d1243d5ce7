<template>
  <card>
    <template #title> 语音翻译配置 </template>
    <div>
      <div class="conf-title item-title" style="margin-top: 0">
        <p style="margin-right: 10px">语音翻译</p>
      </div>
      <p class="item-desc">
        识别参数不影响翻译效果。SDK中目前仅支持语音翻译，不支持文本翻译
      </p>
      <el-form
        class="config-content"
        ref="form"
        :model="form"
        :disabled="!subAccountEditable"
        label-width="80px"
        label-position="left"
        :inline="true"
      >
        <el-form-item label="源语言" class="form-item-left">
          <el-select
            :disabled="ver === '1.0'"
            class="translate-select"
            v-model="source"
            @change="tranConfChange"
            placeholder="请选择源语言"
          >
            <el-option
              v-for="(value, key) in sourceTargetList.source"
              :key="key"
              :label="value"
              :value="key"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="目标语言">
          <el-select
            class="translate-select"
            v-model="target"
            :disabled="ver === '1.0' || !canCreateTranslateScene"
            @change="targetChange"
            placeholder="请选择目标语言"
          >
            <el-option
              v-for="(item, index) in targetList"
              :key="index"
              :label="item.label"
              :value="item.value"
              :disabled="item.disabled"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <span v-if="!canCreateTranslateScene" class="trans-tips"
        >请联系商务(<EMAIL>)开启翻译模式！</span
      >
      <p class="item-title">识别设置</p>
      <p class="item-desc">以下选项仅对中文生效，不影响语义理解效果。</p>
      <div class="config-content mgb24">
        <p class="advanced-setting">
          <el-checkbox
            v-model="form.ptt"
            :disabled="!subAccountEditable"
            @change="aacConfChange"
            true-label="1"
            false-label="0"
            >识别结果添加标点</el-checkbox
          >
        </p>
        <p class="advanced-setting">
          <el-checkbox
            v-model="form.nunum"
            :disabled="!subAccountEditable"
            @change="aacConfChange"
            true-label="1"
            false-label="0"
            >识别结果优先阿拉伯数字</el-checkbox
          >
          <el-tooltip
            content="例如：系统会更倾向识别出“我今年12岁”，而不是“我今年十二岁”"
            placement="right"
          >
            <i class="ic-r-tip" />
          </el-tooltip>
        </p>
        <p class="advanced-setting">
          <el-checkbox
            v-model="form.dwa"
            :disabled="!subAccountEditable"
            @change="aacConfChange"
            true-label="1"
            false-label="0"
            >progressive 流式识别</el-checkbox
          >
          <el-tooltip
            content="边说边返回识别结果，不断修正，有屏设备语音输入体验更佳。"
            placement="right"
          >
            <i class="ic-r-tip" />
          </el-tooltip>
        </p>
      </div>
    </div>
  </card>
</template>
<script>
import card from '../components/card'
import { mapGetters } from 'vuex'
export default {
  name: 'translation',
  components: {
    card,
  },
  computed: {
    ...mapGetters({
      currentScene: 'aiuiApp/currentScene',
      app: 'aiuiApp/app',
      limitCount: 'aiuiApp/limitCount',
      subAccountEditable: 'aiuiApp/subAccountEditable',
    }),
    appId() {
      return this.$route.params.appId
    },
  },
  data() {
    return {
      form: {
        language: 'zh-cn',
        accent: 'mandarin',
        domain: 'sms',
        isFar: '1',
        ptt: '0',
        nunum: '0',
        dwa: '0',
        isHot: '0',
      },
      ver: '1.0',
      source: 'cn-0-mandarin',
      target: 'en',
      targetList: [],
      sourceTargetList: {},
      getDataEnd: 0,
      canCreateTranslateScene: false,
    }
  },
  created() {
    this.getSourceTarget()
    this.getAacConf()
    this.getAiuiTtransConf()
  },
  methods: {
    getAacConf() {
      let self = this
      this.$utils.httpGet(
        this.$config.api.AIUI_DIST_AACCONF,
        {
          appid: this.appId,
          sceneId: this.currentScene.sceneBoxId,
        },
        {
          success: (res) => {
            if (res.flag) {
              self.form = res.data
              if (
                res.data.config &&
                res.data.config.hasOwnProperty('translateScene')
              ) {
                // self.$emit('setTranslateScene', res.data.config.translateScene)
                this.canCreateTranslateScene =
                  res.data.config.translateScene == 1 ? true : false
              }
            } else {
              this.$message.error(res.desc)
            }
          },
        }
      )
    },
    getSourceTarget() {
      let self = this
      this.$utils.httpGet(
        this.$config.api.AIUI_APP_TRANSLATION_SOURCE_TARGET,
        {
          appid: this.appId,
        },
        {
          success: (res) => {
            if (res.flag) {
              self.sourceTargetList = res.data
              self.getDataEnd = self.getDataEnd + 1
            }
          },
        }
      )
    },
    getAiuiTtransConf() {
      let self = this
      this.$utils.httpGet(
        this.$config.api.AIUI_APP_TRANSLATION_TRANSCONF,
        {
          appid: this.appId,
          sceneId: this.currentScene.sceneBoxId,
        },
        {
          success: (res) => {
            self.ver = res.data.ver
            self.source = res.data.source
            self.target = res.data.target
            self.getDataEnd = self.getDataEnd + 1
            self.$store.dispatch('aiuiApp/setTranslateConfig', res.data)
          },
        }
      )
    },
    renderTargetList() {
      let targetList = this.sourceTargetList.target
      this.targetList = []
      for (let item in targetList) {
        let data = {
          value: item,
          label: targetList[item],
        }
        if (item === this.source.split('-')[0]) {
          data.disabled = true
        } else if (this.form.language === 'en-us' && item !== 'cn') {
          data.disabled = true
          this.target = 'cn'
        } else {
          data.disabled = false
        }
        this.targetList.push(data)
      }
    },
    tranConfChange() {
      let params = this.source.split('-')

      // 如果源语言和目标语言语种相同，则改变目标语言类型
      if (params[0] === this.target) {
        this.target = this.target === 'en' ? 'cn' : 'en'
      }

      // 改变识别引擎参数
      if (params[0] === 'cn') {
        this.form.language = 'zh-cn'
      } else {
        this.form.language = 'en-us'
      }
      this.form.isFar = params[1]
      this.form.accent = params[2]
      // this.tranChange = true
      this.renderTargetList()

      // this.emitChange()
      // 实际代码
      this.saveTranConfig()
    },
    targetChange() {
      this.saveTranConfig()
    },
    saveTranConfig() {
      let self = this
      this.$utils.httpPost(
        this.$config.api.AIUI_APP_TRANSLATION_SAVE_CONF,
        {
          appid: this.appId,
          sceneId: this.currentScene.sceneBoxId,
          source: this.source,
          target: this.target,
        },
        {
          success: (res) => {
            if (res.flag) {
              self.$store.dispatch('aiuiApp/setTranslateConfig', res.data)
            } else {
              this.$message.error(res.desc)
              this.getAiuiTtransConf()
            }
          },
          error: (err) => {
            this.getAiuiTtransConf()
          },
        }
      )
    },
    aacConfChange() {
      let self = this
      this.$utils.httpPost(
        this.$config.api.AIUI_DIST_SAVEAACCONF,
        {
          appid: this.appId,
          sceneId: this.currentScene.sceneBoxId,
          platform: this.app.platform,
          language: this.form.language,
          isFar: this.form.isFar,
          accent: this.form.accent,
          domain: this.form.domain,
          ptt: this.form.ptt,
          nunum: this.form.nunum,
          dwa: this.form.dwa,
        },
        {
          success: (res) => {},
          error: (err) => {
            self.getAacConf()
          },
        }
      )
    },
  },
}
</script>
<style lang="scss" scoped>
@import '../common.scss';
.advanced-setting {
  margin-bottom: 12px;
}
.ic-r-tip {
  color: $grey4;
  margin-left: 10px;
}
.form-item-left {
  width: auto;
  min-width: 350px;
}
.trans-tips {
  padding-left: 80px;
  color: #ffa400;
}
</style>
