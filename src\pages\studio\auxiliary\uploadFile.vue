<template>
  <el-upload
    class="qabank__upload-file"
    :action="`${baseUrl}/aiui/${
      subAccount ? 'sub' : ''
    }web/${postApi}?dictId=${dictId}&type=${options.type}&isAuxiliary=1`"
    :show-file-list="false"
    :before-upload="beforeUpload"
    :on-success="success"
    :on-error="uploadError"
  >
    <el-button size="small"> {{ options.text }}</el-button>
  </el-upload>
</template>
<script>
export default {
  props: {
    dictId: '',
    name: '',
    options: {
      isCover: '',
      text: '',
    },
    limitCount: Object,
    subAccount: Boolean,
  },
  data() {
    return {
      baseUrl: this.$config.server,
      postApi: '',
    }
  },
  methods: {
    beforeUpload(file) {
      let reg = /\.(txt|xls|xlsx)(\?.*)?$/
      let type = reg.test(file.name)
      let unExceed =
        file.size < 1024 * 1024 * this.limitCount['entity_file_size']
      let self = this
      this.postApi = ''
      if (!type) {
        this.$message.error('仅支持Excel和txt文件')
      }
      if (!unExceed) {
        this.$message.error(
          `文件不能超过${this.limitCount['entity_file_size']}M`
        )
      }
      return new Promise((resolve, reject) => {
        if (!type || !unExceed) {
          reject()
        }
        if (/\.(xls|xlsx)(\?.*)?$/.test(file.name)) {
          self.$nextTick(() => {
            self.postApi = 'entity/doExcelImport'
            self.$emit('setLoad', type && unExceed)
            resolve()
          })
        } else {
          self.$nextTick(() => {
            self.postApi = 'entity/dict/importByDictId'
            self.$emit('setLoad', type && unExceed)
            resolve()
          })
        }
      })
    },
    success(data) {
      if (data.flag) {
        this.$emit('setLoad', false)
        this.$emit('getEntryList')
        this.$message.success('上传成功')
      } else {
        this.$emit('setLoad', false)
        if (data.code == '106010') {
          this.$emit('setErrInfo', data.desc, true)
        } else {
          this.$message.error(data.desc || '上传失败')
        }
      }
    },
    uploadError() {
      this.$emit('setLoad', false)
    },
  },
}
</script>
<style lang="scss">
.qabank__upload-file {
  .el-button {
    padding: 0 !important;
    min-width: unset;
    text-align: left;
    color: $grey6;
    border: none;
    background: transparent;
    height: unset;
    &:hover {
      background-color: #e8f3fd;
      color: #459ded;
    }
  }
  .el-upload {
    text-align: left;
  }
}
</style>
