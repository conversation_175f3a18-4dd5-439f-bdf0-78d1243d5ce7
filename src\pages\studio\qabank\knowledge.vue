<template>
  <!-- <div class="os-scroll"> -->
  <os-page
    :options="pageOptions"
    @returnCb="$router.push({ name: 'knowledge-list' })"
  >
    <div slot="btn">
      <el-button
        size="small"
        type="primary"
        class="fr"
        style="margin-top: 10px"
        v-show="id"
        :loading="buildLoading"
        @click="handleKnowledgeBuild"
      >
        构建发布
      </el-button>
    </div>
    <div class="form-area">
      <el-form
        ref="form"
        :model="ruleForm"
        label-width="118px"
        label-position="left"
        :rules="rules"
      >
        <!-- <el-form-item label="知识库类型" prop="repoId">
          <el-select
            v-model="ruleForm.repoId"
            placeholder="请勾选知识库类型"
            :style="{ width: '430px' }"
            :disabled="true"
          >
            <el-option
              v-for="item in optionList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
          <img :src="IconAdd" class="kl-add" @click="handleKnowAdd" />
        </el-form-item> -->
        <el-form-item label="文档库名称" prop="name">
          <!-- 40个字符限制 -->
          <el-input
            v-model="ruleForm.name"
            :style="{ width: '430px' }"
            :disabled="detailDisabled"
          ></el-input>
        </el-form-item>
        <el-form-item label="" prop="fileList">
          <el-upload
            class="custom-upload"
            name="file"
            drag
            ref="upload"
            :action="'/aiui/web/knowledge/doc/create/'"
            :multiple="true"
            :accept="'.docx,.txt,.pdf,.xlsx,.xls,.csv,.md'"
            :auto-upload="false"
            :http-request="handleFileUpload"
            :on-error="handleFileError"
            :on-change="handleFileChange"
            :on-remove="handleFileRemove"
            :show-file-list="false"
            :disabled="detailDisabled"
          >
            <i class="el-icon-upload"></i>
            <p class="upload-title">上传文件</p>
            <div class="el-upload__text">
              支持docx、txt、pdf、xlsx、md格式，大小不超过10M，数量不超过10篇
            </div>
            <div class="el-upload__tip" slot="tip">
              当前支持文本+图片内容，敬请期待后续功能升级
              <a @click="handleModelDownload">下载模板</a>
            </div>
          </el-upload>
          <ul v-if="fileList.length > 0" style="width: 700px; margin-top: 10px">
            <li
              v-for="(file, index) in fileList"
              :key="index"
              class="file-whole"
            >
              <div :title="file.name" class="file-history">
                <span class="file-name ellipsis">{{ file.name }}</span>
                <i
                  class="el-icon-close file-del"
                  @click="handleFileCurrentDel(index)"
                ></i>
              </div>
              <!-- <span class="file-status-success">已上传未解析</span> -->
            </li>
          </ul>

          <ul
            v-if="beforeList.length > 0"
            style="width: 700px; margin-top: 10px"
          >
            <li
              v-for="(file, index) in beforeList"
              :key="index"
              class="file-whole"
            >
              <div :title="file.docName" class="file-history">
                <a
                  class="file-name ellipsis"
                  target="_blank"
                  :href="file.filePath"
                  >{{ file.docName }}</a
                >
                <i
                  class="el-icon-close file-del"
                  @click="handleFileHistoryDel(file)"
                  v-show="!detailDisabled"
                ></i>
              </div>
              <!-- <span
                v-if="file.displayStatus"
                :class="{
                  'file-status-success':
                    file.displayStatus === '101' ||
                    file.displayStatus === '201',
                  'file-status-processing':
                    file.displayStatus === '100' ||
                    file.displayStatus === '102' ||
                    file.displayStatus === '202',
                  'file-status-error':
                    file.displayStatus === '103' ||
                    file.displayStatus === '203',
                }"
                >{{ file.displayStatusName }}</span
              > -->
              <span
                v-if="file.displayStatus"
                :class="{
                  'file-status-success':
                    file.displayStatus === '101' ||
                    file.displayStatus === '201' ||
                    file.displayStatus === '100' ||
                    file.displayStatus === '102' ||
                    file.displayStatus === '202',
                  'file-status-error':
                    file.displayStatus === '103' ||
                    file.displayStatus === '203',
                }"
                >{{ file.displayStatusName }}</span
              >
            </li>
          </ul>
        </el-form-item>
        <el-form-item label="文档描述" prop="description">
          <el-input
            v-model="ruleForm.description"
            type="textarea"
            :rows="3"
            :disabled="detailDisabled"
            :style="{ width: '430px' }"
            :maxlength="200"
            placeholder="不超过200字符"
          ></el-input>
        </el-form-item>
        <el-form-item>
          <div class="btn-group">
            <el-button
              type="primary"
              @click="submitForm"
              :disabled="saveDisabled"
              :loading="saveLoading"
              v-show="!detailDisabled"
              size="small"
              >保存文档</el-button
            >
            <el-button @click="cancelCreate" size="small">取消</el-button>
          </div>
        </el-form-item>
      </el-form>
    </div>
    <el-dialog
      title="新增自定义库"
      :visible.sync="dialogVisible"
      :width="'530px'"
      class="create-dialog"
    >
      <el-form
        ref="addForm"
        :model="addForm"
        label-width="70px"
        :rules="rules"
        :hide-required-asterisk="true"
      >
        <el-form-item label="库名称：" prop="addName">
          <el-input
            v-model="addForm.addName"
            :style="{ width: '400px' }"
            placeholder="输入自定义库名称，中文/英文/数字/下划线，不超过40个字符"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          size="medium"
          style="min-width: 90px"
          @click="submitKnowAdd"
          :loading="addLoading"
          >保存</el-button
        >
      </div>
    </el-dialog>
  </os-page>
  <!-- </div> -->
</template>

<script>
import IconAdd from '@A/images/plugin/add.png'
import axios from 'axios'
import { mapGetters } from 'vuex'
export default {
  name: 'qaBank-knowledge-create',
  data() {
    return {
      IconAdd,
      pageOptions: {
        title: '知识基本信息',
        loading: false,
        returnBtn: false,
      },
      dialogVisible: false,
      repoId: null,
      id: null,
      addLoading: false,
      buildLoading: false,
      saveLoading: false,
      saveDisabled: false,
      detailDisabled: false,
      fileSize: 0,
      ruleForm: {
        repoId: '',
        name: '',
        description: '',
      },
      fileList: [],
      beforeList: [],
      optionList: [],
      modelUrl: [],
      addForm: {
        addName: '',
      },
      rules: {
        repoId: [
          { required: true, message: '请选择知识库类型', trigger: 'change' },
        ],
        name: [
          { required: true, message: '请输入文档名称', trigger: 'blur' },
          { max: 40, message: '文档名称不得超过40个字符', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              const regExp = /^[a-zA-Z0-9_\u4e00-\u9fff]+$/ // 正则表达式匹配中英文、数字，下划线

              if (!regExp.test(value)) {
                callback(new Error('文档名称只能包含中文/英文/数字/下划线'))
              } else {
                callback()
              }
            },
            trigger: 'change',
          },
        ],
        addName: [
          {
            required: true,
            message: '请输入新增知识库名称',
            trigger: 'blur',
          },
          {
            max: 40,
            message: '内容长度不超过40个字符',
            trigger: 'change',
          },
          {
            validator: (rule, value, callback) => {
              const regExp = /^[a-zA-Z0-9_\u4e00-\u9fff]+$/ // 正则表达式匹配中英文、数字，下划线
              if (!regExp.test(value)) {
                callback(new Error('知识库名称只能包含中英文/数字/下划线'))
              } else {
                callback()
              }
            },
            trigger: 'change',
          },
        ],
        description: [
          { max: 200, message: '文档描述不得超过200个字符', trigger: 'change' },
        ],
      },
      originName: '',

      timer: null,

      canToast: false,
    }
  },
  computed: {
    // ...mapGetters({
    //   name: "pluginKnowledge/name",
    // }),
  },
  methods: {
    handleKnowAdd() {
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs.addForm.resetFields()
      })
    },
    getOptionList() {
      let self = this
      this.$utils.httpGet(
        this.$config.api.KNOWLEDGE_TYPE_LIST,
        {},
        {
          success: (res) => {
            self.optionList = res.data || []
          },
          error: (err) => {},
        }
      )
    },
    getModel() {
      this.$utils.httpGet(
        this.$config.api.KNOWLEDGE_STORE_MODEL,
        {},
        {
          success: (res) => {
            if (res.code === '0') {
              this.modelUrl = res.data
            }
          },
          error: (err) => {},
        }
      )
    },
    downloadFile(file) {},
    handleModelDownload() {
      function downloadFile(url, index) {
        setTimeout(function () {
          //设置setTimeout是防止出现多个下载只生效一次的情形
          const frame = document.createElement('iframe')
          frame.setAttribute('style', 'display: none')
          frame.setAttribute('src', url)
          frame.setAttribute('id', 'iframeName')
          document.body.appendChild(frame)
          setTimeout(function () {
            const node = document.getElementById('iframeName')
            node.parentNode.removeChild(node)
          }, 1000)
        }, index * 100)
      }
      this.modelUrl.map(function (url, index) {
        let fileName = url.substring(url.lastIndexOf('/') + 1)
        let newUrl = url.replace('http:', 'https:')
        downloadFile(newUrl, index)
      })
    },
    submitKnowAdd() {
      let self = this
      this.$refs['addForm'].validate((valid) => {
        if (valid) {
          self.addLoading = true
          let api = this.$config.api.KNOWLEDGE_TYPE_CREATE
          let data = {
            name: self.addForm.addName,
          }
          this.$utils.httpPost(api, data, {
            success: (res) => {
              if (res) {
                self.addLoading = false
                self.$message.success('知识库创建成功')
                this.dialogVisible = false
                // self.getOptionList()
              }
            },
            error: (err) => {
              self.addLoading = false
            },
          })
        } else {
          return false
        }
      })
    },
    async handleFileUpload() {
      if (this.fileList.length === 0 && this.beforeList.length === 0) {
        return this.$message.error('请选择上传文件')
      }
      this.saveLoading = true
      const formData = new FormData()
      this.fileList.forEach((file) => {
        formData.append('file', file.raw)
      })
      formData.append('repoId', this.ruleForm.repoId)
      formData.append('name', this.ruleForm.name)
      formData.append(
        'description',
        this.ruleForm.description ? this.ruleForm.description : ''
      )
      if (this.id) {
        // 知识编辑
        formData.append('id', this.id)
        try {
          const response = await axios.post(
            '/aiui/web/knowledge/doc/edit/',
            formData,
            {
              headers: {
                'Content-Type': 'multipart/form-data',
              },
            }
          )
          let res = response.data
          if (res.code === '0') {
            this.$message.success('保存文档成功')
            // if (this.fileList.length === 0) {
            //   this.$message.success('保存成功')
            // } else {
            //   this.$message.success('您的文件正在解析中，请耐心等待')
            // }

            // this.$message.warning(
            //   '保存文档提交成功，稍候构建成功后，点击发布文档库可生效'
            // )
            // 保存成功，清空fileList
            this.fileList = []
            this.getRepoDocInfo()
            this.saveLoading = false
          } else {
            this.$message.error(res.desc)
            this.saveLoading = false
          }
        } catch (error) {
          this.saveLoading = false
        }
      } else {
        try {
          // 知识创建
          const response = await axios.post(
            '/aiui/web/knowledge/doc/create/',
            formData,
            {
              headers: {
                'Content-Type': 'multipart/form-data',
              },
            }
          )
          this.saveLoading = false
          let res = response.data
          if (res.code === '0') {
            this.$message.success('您的文件正在解析中，请耐心等待')
            // this.$message.warning(
            //   '保存文档提交成功，稍候构建成功后，点击发布文档库可生效'
            // )
            // this.$message.success('知识创建成功')
            // this.$router.push({
            //   name: 'qaBank',
            // })
            // 保存成功，清空fileList
            this.fileList = []
            this.getRepoDocInfo()
          } else {
            this.$message.error(res.desc)
          }
        } catch (error) {
          this.saveLoading = false
        }
      }
    },
    handleFileChange(file) {
      let fileList = this.fileList.concat(file)
      console.log(
        '-----------------handleFileChange---------',
        file,
        fileList,
        this.fileList
      )

      this.$message.success(
        '您的文件已经添加成功，请点击保存文档按钮来上传文件'
      )
      // ********
      let resSize = this.fileSize
      fileList.map((item) => {
        resSize += item.size
      })
      if (resSize > ********) {
        this.$message.error('上传文件大小不能超过 10MB!')
        this.saveDisabled = true
      } else {
        this.saveDisabled = false
      }
      this.fileList = fileList
    },
    handleFileRemove(file, fileList) {
      console.log('-----------------handleFileRemove---------')
      this.fileList = fileList
      let resSize = this.fileSize
      fileList.map((item) => {
        resSize += item.size
      })
      if (resSize > ********) {
        this.$message.error('上传文件大小不能超过 10MB!')
        this.saveDisabled = true
      } else {
        this.saveDisabled = false
      }
    },
    handleFileError(err, file, fileList) {
      this.$message.error('文件上传失败')
    },
    handleFileCurrentDel(index) {
      this.fileList.splice(index, 1)
    },
    handleFileHistoryDel(file) {
      let data = {
        id: this.id,
        docId: file.id,
      }
      this.$utils.httpGet(this.$config.api.KNOWLEDGE_STORE_FILE_DELETE, data, {
        success: (res) => {
          this.$message.success('删除文档成功')
          // console.log('删除文档后，打印fileList', this.fileList)
          this.getRepoDocInfo()
        },
        error: (err) => {},
      })
    },
    doHandleKnowledgeBuild() {
      this.buildLoading = true
      let data = {
        id: this.id,
      }
      let self = this
      this.$utils.httpGet(this.$config.api.KNOWLEDGE_STORE_BUILD, data, {
        success: (res) => {
          self.buildLoading = false
          if (res.code === '0') {
            this.$message.success(
              '知识构建发布需要一定时间，请等待文档显示发布成功后再使用'
            )
            // 再次启动定时器轮询
            if (self.timer) {
              clearTimeout(self.timer)
            }
            self.getRepoDocInfo()
          } else {
            this.$message.error(`您的知识构建失败,${res.desc}`)
          }
        },
        error: (err) => {
          self.buildLoading = false
        },
      })
    },
    handleKnowledgeBuild() {
      if (this.fileList.length > 0) {
        this.$confirm('您的文档修改尚未保存，是否确认发布?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(() => {
            // 清空fileList
            // this.fileList = []
            this.doHandleKnowledgeBuild()
          })
          .catch(() => {})
      } else {
        this.doHandleKnowledgeBuild()
      }
    },
    cancelCreate() {
      // this.$router.go(-1)
      this.$router.push({ name: 'studio-handle-platform-qabanks' })
    },
    submitForm() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          if (this.originName !== this.ruleForm.name) {
            this.updateRepoInfo()
          } else {
            this.handleFileUpload()
          }

          // this.handleFileUpload()
        }
      })
    },

    updateRepoInfo() {
      let self = this
      let data = {
        repoId: this.$route.params.repoId,
        name: this.ruleForm.name,
        type: '5',
      }

      const api = this.$config.api.STUDIO_QA_CREATE_EDIT
      this.$utils.httpPost(api, data, {
        success: (res) => {
          // self.saving = false
          // self.$message.success('保存成功')
          // self.$refs.skillForm && self.$refs.skillForm.clearValidate()
          // self.$store.dispatch('studioQa/setQa', this.qa.repositoryId)
          // self.initDataChanged = false
          self.$store.dispatch('studioQa/setQa', this.$route.params.repoId)
          self.handleFileUpload()
        },
        error: (err) => {
          console.log('page=>>')
          console.log(err)
        },
      })
    },
    // getFileList() {
    //   if (this.id && this.repoId) {
    //     let data = {
    //       repoId: this.repoId,
    //       id: this.id,
    //     }
    //     let self = this
    //     this.$utils.httpGet(this.$config.api.KNOWLEDGE_STORE_FILE_LIST, data, {
    //       success: (res) => {
    //         self.beforeList = res.data || []
    //       },
    //       error: (err) => {},
    //     })
    //   }
    // },
    getDocDisplayStatus(data, docItem) {
      /**
       * data.extractStatus==3  解析失败
          data.extractStatus==2  解析中  
        需要去查看docList 的 文件 status  (1:解析完成， 2：解析中，3：解析失败)

        data.extractStatus==1  解析成功 所有文件统一查看并展示 依据：data.buildStatus  (1:已构建， 2：构建中，3：构建失败)
       */
      if (data.extractStatus === 1) {
        if (data.buildStatus === 0) {
          return {
            displayStatus: '100',
            displayStatusName: '已解析未发布',
          }
        } else if (data.buildStatus === 1) {
          return {
            displayStatus: '101',
            displayStatusName: '发布成功',
          }
        } else if (data.buildStatus === 2) {
          return {
            displayStatus: '102',
            displayStatusName: '发布中',
          }
        } else if (data.buildStatus === 3) {
          return {
            displayStatus: '103',
            displayStatusName: '发布失败',
          }
        } else {
          return {
            displayStatus: '',
            displayStatusName: '',
          }
        }
      } else if (data.extractStatus === 2 || data.extractStatus === 3) {
        if (docItem.status === 1) {
          return {
            displayStatus: '201',
            displayStatusName: '已解析未发布',
          }
        } else if (docItem.status === 2) {
          return {
            displayStatus: '202',
            displayStatusName: '解析中',
          }
        } else if (docItem.status === 3) {
          return {
            displayStatus: '203',
            displayStatusName: '解析失败',
          }
        } else {
          return {
            displayStatus: '',
            displayStatusName: '',
          }
        }
      } else {
        return {
          displayStatus: '',
          displayStatusName: '',
        }
      }
    },

    getRepoInfo() {
      let self = this
      this.$utils.httpGet(
        this.$config.api.STUDIO_QA_INFO,
        {
          repoId: this.$route.params.repoId,
        },
        {
          success: (res) => {
            self.originName = res.data.name
            self.$set(self.ruleForm, 'name', res.data.name)
          },
          error: (err) => {},
        }
      )
    },

    getRepoDocInfo() {
      let params = this.$route.params
      let data = {
        repoId: params.repoId,
      }
      if (!params.repoId) {
        return
      }
      const self = this
      this.$utils.httpGet(this.$config.api.QA_GETREPODOCINFO, data, {
        success: (res) => {
          if (res.data) {
            // self.ruleForm = {
            //   ...res.data,
            // }
            // 去这里的字段，但是name不从这个接口取
            let keys = Object.keys(res.data).filter((it) => it !== 'name')
            keys.forEach((k) => {
              self.$set(self.ruleForm, k, res.data[k])
            })

            self.repoId = res.data.repoId || ''
            self.id = res.data.id || ''
            if (res.data.id) {
              self.$store.dispatch('pluginKnowledge/setKnowIdSync', res.data.id)
            }
            self.beforeList = (res.data.docList || []).map((it) => {
              return {
                ...it,
                ...self.getDocDisplayStatus(res.data, it),
              }
            })

            self.$store.dispatch(
              'pluginKnowledge/setRepoIds',
              res.data.repoId || ''
            )
            // 发布成功状态
            if (res.data.extractStatus === 1) {
              if (res.data.buildStatus === 1) {
                if (this.canToast) {
                  this.$message.success(
                    '您的知识文档构建发布成功，请在右侧进行知识体验。'
                  )
                  this.canToast = false
                }
              }
            }

            if (
              // 解析中
              // 解析成功，构建中
              res.data.extractStatus === 2 ||
              (res.data.extractStatus === 1 && res.data.buildStatus === 2)
            ) {
              self.timer = setTimeout(() => {
                self.getRepoDocInfo()
              }, 2000)
            } else if (
              // 待构建状态(待发布)
              res.data.extractStatus === 1 &&
              res.data.buildStatus === 0
            ) {
              self.canToast = true
              self.timer = setTimeout(() => {
                self.getRepoDocInfo()
              }, 3000)
            }
          }
        },
        error: (err) => {},
      })
    },
  },
  created() {
    const repoId = this.$route.params.repoId
    const id = this.$route.params.id
    this.repoId = repoId
    this.id = id

    this.detailDisabled = this.$route.params.disabled
    console.log('路由中params', this.$route.params)

    // this.getOptionList()
    this.getRepoInfo()
    this.getModel()
    this.getRepoDocInfo()
  },

  destroyed() {
    if (this.timer) {
      clearTimeout(this.timer)
    }
  },

  watch: {
    beforeList: function (val) {
      let res = 0
      val.map((file) => {
        res += file.fileSize
      })
      this.fileSize = res
    },
    fileList(val) {
      let fileList = val
      let resSize = this.fileSize
      fileList.map((item) => {
        resSize += item.size
      })
      if (this.beforeList.length + val.length > 10) {
        this.$message.error('上传文件数量不超过10篇')
        this.saveDisabled = true
        return
      }
      if (resSize > ********) {
        this.$message.error('上传文件大小不能超过 10MB!')
        this.saveDisabled = true
      } else {
        this.saveDisabled = false
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.form-area {
  padding: 20px 24px 70px 0px;
  white-space: nowrap;

  .kl-add {
    width: 24px;
    height: 24px;
    margin-left: 23px;
    cursor: pointer;
  }

  .custom-upload {
    :deep(.el-upload) {
      width: 430px;
    }

    :deep(.el-upload-dragger) {
      width: 430px;
      height: 170px;
    }

    :deep(.el-upload-list) {
      width: 400px;
    }

    .el-icon-upload {
      margin: 26px 0 0px;
    }

    .upload-title {
      font-size: 14px;
      font-family: PingFangSC, PingFangSC-Regular;
      font-weight: 400;
      color: #303133;
      line-height: 22px;
      margin-bottom: 10px;
    }

    .el-upload__text {
      font-size: 12px;
      font-family: PingFangSC, PingFangSC-Regular;
      font-weight: 400;
      color: #9ea5bd;
    }

    .el-upload__tip {
      width: 430px;

      > a {
        float: right;
      }
    }
  }

  .btn-group {
    width: 770px;
  }

  .file-whole {
    display: flex;
    align-items: center;
  }
  .file-status-success {
    color: #18da00;
  }
  .file-status-error {
    color: #ff3838;
  }
  .file-status-processing {
    color: #ffa400;
  }

  .file-history {
    width: 330px;
    height: 41px;
    background: #fafcfe;
    border: 1px solid #dcdfe6;
    border-radius: 2px;
    padding: 0 12px;
    margin: 10px 10px 10px 0;

    // display: inline-block;
    &:hover {
      background: #ebeef5;
    }

    .file-name {
      width: 280px;
      display: inline-block;
      line-height: 41px;
    }

    .file-del {
      font-size: 15px;
      position: relative;
      bottom: 15px;
      cursor: pointer;
    }
  }
}
</style>
