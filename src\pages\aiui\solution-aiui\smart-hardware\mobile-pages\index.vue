<template>
  <div class="main-content2">
    <section class="main-content2-banner">
      <div class="banner-text">
        <h2>智能硬件</h2>
        <p class="banner-text-content">
          当前智能硬件在生产和生活中全面渗透，通过集成讯飞多种AI能力，智能硬件厂家可快速实现产品技术突破及应用场景创新，在激烈竞争中拔得头筹，占领市场先机
        </p>
      </div>
    </section>
    <section class="section section-1">
      <div class="section-title">
        <i class="arrow arrow-left"></i>
        <span>AI赋能</span>
        <i class="arrow arrow-right"></i>
      </div>

      <div class="section-tabs">
        <el-tabs v-model="activeName" @tab-click="handleClick">
          <el-tab-pane label="耳聪" name="0"> </el-tab-pane>
          <el-tab-pane label="目明" name="1"> </el-tab-pane>
          <el-tab-pane label="善言" name="2"> </el-tab-pane>
          <el-tab-pane label="博学" name="3"> </el-tab-pane>
          <el-tab-pane label="多才" name="4"> </el-tab-pane>
        </el-tabs>
      </div>
      <div class="section-swiper" v-swiper:swiper="swiperOption">
        <div class="swiper-wrapper">
          <div class="swiper-slide" key="1">
            <cells :cells="rcCells" label="rc" />
          </div>
          <div class="swiper-slide" key="2">
            <cells :cells="mmCells" label="mm" />
          </div>
          <div class="swiper-slide" key="3">
            <cells :cells="syCells" label="sy" />
          </div>
          <div class="swiper-slide" key="4">
            <div class="content-container">
              <p class="content-sub-title">
                1500万首曲库，10余个视频品牌，有声内容1200万+小时，100余家内容提供方
              </p>
              <ul class="li-wrap">
                <li class="">
                  <i class="content-icon icon-xmly"></i>
                  <p>喜马拉雅听说</p>
                </li>
                <li class="">
                  <i class="content-icon icon-kwyy"></i>
                  <p>酷我音乐</p>
                </li>
                <li class="">
                  <i class="content-icon icon-xlxw"></i>
                  <p>新浪新闻</p>
                </li>
                <li class="">
                  <i class="content-icon icon-aqy"></i>
                  <p>爱奇艺</p>
                </li>
                <li class="">
                  <i class="content-icon icon-bilibili"></i>
                  <p>哔哩哔哩</p>
                </li>
                <li class="">
                  <i class="content-icon icon-more"></i>
                  <p>更多内容</p>
                </li>
              </ul>
            </div>
          </div>
          <div class="swiper-slide" key="5">
            <div class="content-container">
              <p class="content-sub-title">
                语音技能涵盖生活、娱乐、游戏、办公、搜索导航、
                AIOT控制，一键配置，让你的产品生而智能
              </p>
              <ul class="li-wrap">
                <li class="">
                  <i class="content-icon icon-clock"></i>
                  <p>闹钟</p>
                </li>
                <li class="">
                  <i class="content-icon icon-dictionary"></i>
                  <p>成语词典</p>
                </li>
                <li class="">
                  <i class="content-icon icon-qa"></i>
                  <p>百科问答</p>
                </li>
                <li class="">
                  <i class="content-icon icon-poetry"></i>
                  <p>古诗词</p>
                </li>
                <li class="">
                  <i class="content-icon icon-chat"></i>
                  <p>闲聊</p>
                </li>
                <li class="">
                  <i class="content-icon icon-more"></i>
                  <p>更多内容</p>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </section>
    <section class="section section-2">
      <div class="section-title">
        <i class="arrow arrow-left"></i>
        <span>解决方案</span>
        <i class="arrow arrow-right"></i>
      </div>
      <ul class="solutions">
        <li class="solution-child">
          <p>儿童教育产品解决方案</p>
          <p>
            利用讯飞的语音技术、手指检测、绘本检测引擎、口语评测等多项AI能力，让机器人快速实现语音交互及教学能力
          </p>
        </li>
        <li class="solution-switch">
          <p>穿戴式设备解决方案</p>
          <p>
            提供系统级的语音助手，支持免唤醒交互，让产品轻松实现语音打电话，语音导航，语音查天气等多场景语音交互
          </p>
        </li>
        <li>
          <p>服务机器人解决方案</p>
          <p>
            为机器人厂家提供从前端拾音、语音交互，图像识别全链路、全场景软硬件一体化AI能力，基于高效降噪算法及多模态技术，让机器人在公共场所下轻松完成人机交互
          </p>
        </li>
        <li>
          <p>健身按摩设备解决方案</p>
          <p>
            提供离线命令词识别服务，支持200条语法，零流量实时响应，满足不同终端快速稳定的本地化语音服务
          </p>
        </li>
      </ul>
    </section>
    <section class="section section-3">
      <div class="section-title">
        <i class="arrow arrow-left"></i>
        <span>我们的优势</span>
        <i class="arrow arrow-right"></i>
      </div>
      <ul class="advantage">
        <li>
          <div class="advantage-image"></div>
          <div class="advantage-text">
            <p>免唤醒交互</p>
            <p>无需说出唤醒词，直接交互打造自然流畅的语音交互体验</p>
          </div>
        </li>
        <li>
          <div class="advantage-image"></div>
          <div class="advantage-text">
            <p>多模态融合</p>
            <p>
              支持图像、手势、声纹等多模态技术与语音的融合，满足不同终端、不同场景应用需求
            </p>
          </div>
        </li>
        <li>
          <div class="advantage-image"></div>
          <div class="advantage-text">
            <p>多种接入方式</p>
            <p>
              提供Android、iOS、Linux、WebAPI、软硬件一体化产品等接入方式，助力开发者以最小的成本快速接入AI能力
            </p>
          </div>
        </li>
        <li>
          <div class="advantage-image"></div>
          <div class="advantage-text">
            <p>一对一指导</p>
            <p>
              针对智能硬件前端声学设计、语音交互设计、系统能力集成、技能开发等提供专业的技术支持，一对一贴身指导
            </p>
          </div>
        </li>
      </ul>
    </section>
    <section class="btn-wrap">
      <div class="section-btn" @click="toConsole">合作咨询</div>
    </section>
  </div>
</template>

<script>
import Cells from './mobileCells.vue'
import '../../../../../../static/vue-awesome-swiper'

export default {
  // name: "",
  data() {
    return {
      activeName: '0',
      rcCells: [
        {
          title: '免唤醒',
          introduction: '提供低功耗唤醒，多模态唤醒及免唤醒交互',
        },
        {
          title: '远距离拾音',
          introduction: '支持1-3-5米远距离拾音',
        },
        {
          title: '定向拾音',
          introduction: '支持全向或指定方向拾音',
        },
        {
          title: '声纹识别',
          introduction: '支持成人、儿童、老人音质辨别',
        },
        {
          title: '高效降噪',
          introduction: '提供多领域、多场景降噪引擎',
        },
      ],
      mmCells: [
        {
          title: '人脸识别',
          introduction: '提供人脸检测、活体检测、人脸跟踪等能力',
        },
        {
          title: '手势识别',
          introduction: '支持20+常用动态手势动作识别',
        },
        {
          title: '唇形识别',
          introduction: '支持唇形检测、唇形识别',
        },
        {
          title: 'OCR',
          introduction: '用与各种场景图像文字识别，支持多个语种',
        },
        {
          title: '手指检测',
          introduction: '检测出手指在图像上的位置坐标',
        },
      ],
      syCells: [
        {
          title: '多发音人',
          introduction: '100+发音人，男女老少，风格随心选',
        },
        {
          title: '方言语种',
          introduction: '支持19个语种，11种方言，中英混合自然合成',
        },
        {
          title: '动态调参',
          introduction: '随心调节语调/语速/音量等参数，满足复杂场景需求',
        },
        {
          title: '定制音库',
          introduction: '赋予产品声音形象，定制发音人，为产品量身打造专属音库',
        },
        {
          title: '个性化变声',
          introduction: '将源发音人的声音转换为目标发音人音色',
        },
      ],
      swiperOption: {
        loop: true,
        autoplay: {
          delay: 4000,
          // disableOnInteraction: false,
        },
      },
    }
  },
  methods: {
    toConsole() {
      // 判断是否有百度SEM营销参数，有的话，提交表单页需要带上
      const search = window.location.search
      if (search) {
        window.open(`/solution/apply/8${search}`)
      } else {
        window.open(`/solution/apply/8`)
      }
    },
    handleClick() {
      this.swiper.slideToLoop(Number(this.activeName))
    },
  },
  mounted() {
    this.swiper.on('slideChange', () => {
      this.activeName = this.swiper.realIndex + ''
    })
  },
  components: { Cells },
}
</script>

<style lang="scss" scoped>
.main-content2 {
  max-width: 750px;
  overflow: hidden;
  &-banner {
    background: url(~@A/images/solution/smart-hardware/mobile/banner.png) center
      no-repeat;
    background-size: cover;
    height: 1052px;
    overflow: hidden;
    width: 100%;
  }
  .banner-text {
    padding-top: 774px;
    text-align: center;
    h2 {
      font-size: 58px;
      font-weight: 500;
      color: #ffffff;
      line-height: 88px;
    }
    p {
      width: 654px;
      font-size: 24px;
      font-weight: 400;
      color: rgba(255, 255, 255, 0.8);
      line-height: 34px;
      margin: 30px auto 0;
    }
  }

  .section {
    p,
    ul {
      margin-bottom: 0;
    }
    max-width: 750px;
    overflow: hidden;
    margin: 0 auto;
    .section-title {
      text-align: center;
      font-size: 36px;
      font-weight: 500;
      color: #444444;
      line-height: 54px;
      .arrow {
        width: 26px;
        height: 24px;
        display: inline-block;
        background-repeat: no-repeat;
        background-size: contain;
      }
      .arrow-left {
        background-position: left;
        background-image: url(~@A/images/solution/smart-hardware/arrow-left.png);
      }
      .arrow-right {
        background-position: right;
        background-image: url(~@A/images/solution/smart-hardware/arrow-right.png);
      }
    }
    .section-tabs {
      margin-top: 60px;
    }
  }
  .section-1 {
    padding-top: 100px;
    max-width: 750px;
  }
  .section-2 {
    max-width: 750px;
    margin-top: 100px;
    .solutions {
      display: flex;
      justify-content: center;
      flex-wrap: wrap;
      margin-top: 52px;
      > li {
        width: 666px;
        height: 366px;
        text-align: center;
        background-size: cover !important;
        padding-top: 100px;

        p {
          margin: 0 auto;
        }

        p:nth-child(1) {
          color: #fff;
          line-height: 33px;
          margin-top: 29px;
          width: 552px;
          font-size: 32px;
          font-weight: 500;
          line-height: 44px;
        }
        p:nth-child(2) {
          margin-top: 25px;
          width: 552px;
          font-size: 22px;
          font-weight: 400;
          color: #ffffff;
          line-height: 34px;
        }
        &:first-child {
          background: url(~@A/images/solution/smart-hardware/solution-tips/child.png)
            center no-repeat;
          i {
            background: url(~@A/images/solution/smart-hardware/solution-tips/child_2.png)
              center no-repeat;
          }
        }
        &:nth-child(2) {
          margin-top: 18px;
          background: url(~@A/images/solution/smart-hardware/solution-tips/switch.png)
            center no-repeat;
          i {
            background: url(~@A/images/solution/smart-hardware/solution-tips/switch_2.png)
              center no-repeat;
          }
        }
        &:nth-child(3) {
          margin-top: 18px;
          background: url(~@A/images/solution/smart-hardware/solution-tips/robot.png)
            center no-repeat;
          i {
            background: url(~@A/images/solution/smart-hardware/solution-tips/robot_2.png)
              center no-repeat;
          }
        }
        &:nth-child(4) {
          margin-top: 18px;
          background: url(~@A/images/solution/smart-hardware/solution-tips/rest.png)
            center no-repeat;
          i {
            background: url(~@A/images/solution/smart-hardware/solution-tips/rest_2.png)
              center no-repeat;
          }
        }
      }
    }
  }

  .section-3 {
    max-width: 750px;
    margin-top: 80px;
    padding-bottom: 384px;
    .advantage {
      margin-top: 82px;
      li {
        text-align: center;
        .advantage-image,
        .advantage-text {
          margin: 0 auto;
        }
      }
      li:nth-child(1) {
        .advantage-image {
          width: 364px;
          height: 342px;
          background: url(~@A/images/solution/smart-hardware/adv_1.png) center
            no-repeat;
          background-size: cover;
        }
      }
      li:nth-child(2) {
        .advantage-image {
          width: 362px;
          height: 384px;
          background: url(~@A/images/solution/smart-hardware/adv_2.png);
          background-size: cover;
        }
        .advantage-text {
          width: 428px;
        }
      }
      li:nth-child(3) {
        .advantage-image {
          width: 362px;
          height: 384px;
          background: url(~@A/images/solution/smart-hardware/adv_3.png) center
            no-repeat;
          background-size: cover;
        }
        .advantage-text {
        }
      }
      li:nth-child(4) {
        .advantage-image {
          width: 362px;
          height: 384px;
          background: url(~@A/images/solution/smart-hardware/adv_4.png) center
            no-repeat;
          background-size: cover;
        }
        .advantage-text {
        }
      }

      li + li {
        margin-top: 66px;
      }
    }
    .advantage-text {
      margin-top: 38px !important;
      width: 428px;
      p:first-child {
        font-size: 34px;
        font-weight: 500;
        color: #656565;
        line-height: 48px;
      }
      p:last-child {
        font-size: 24px;
        font-weight: 400;
        color: #999999;
        line-height: 42px;
        margin-top: 14px;
      }
    }
  }

  .btn-wrap {
    position: fixed;
    bottom: 148px;
    width: 100%;
    z-index: 100;
    .section-btn {
      width: 622px;
      height: 100px;
      text-align: center;
      color: #fff;
      line-height: 100px;
      font-size: 34px;
      background: #348cff;
      margin: 0 auto;
    }
  }

  .content-container {
    max-width: 750px;
    overflow: hidden;
    padding-top: 61px;
    padding-bottom: 75px;
    background: rgba(171, 212, 255, 0.08);
    .content-sub-title {
      text-align: center;
      margin: 0 auto 72px;
      width: 670px;
      font-size: 24px;
      font-weight: 400;
      color: #999999;
      line-height: 34px;
    }
    .content-icon {
      display: inline-block;
      width: 69px;
      height: 69px;
      background-repeat: no-repeat;
      background-size: contain;
    }

    .icon-xmly {
      background-image: url(~@A/images/solution/assistant/logos/s_1.png);
    }

    .icon-kwyy {
      background-image: url(~@A/images/solution/assistant/logos/s_2.png);
    }

    .icon-xlxw {
      background-image: url(~@A/images/solution/assistant/logos/s_3.png);
    }

    .icon-aqy {
      background-image: url(~@A/images/solution/assistant/logos/s_4.png);
    }

    .icon-bilibili {
      background-image: url(~@A/images/solution/assistant/logos/s_5.png);
    }

    .icon-clock {
      background-image: url(~@A/images/solution/assistant/logos/t_1.png);
    }

    .icon-dictionary {
      background-image: url(~@A/images/solution/assistant/logos/t_2.png);
    }

    .icon-qa {
      background-image: url(~@A/images/solution/assistant/logos/t_3.png);
    }

    .icon-poetry {
      background-image: url(~@A/images/solution/assistant/logos/t_4.png);
    }

    .icon-chat {
      background-image: url(~@A/images/solution/assistant/logos/t_5.png);
    }
    .icon-more {
      background-image: url(~@A/images/solution/assistant/logos/more.png);
    }

    .li-wrap {
      display: flex;
      flex-wrap: wrap;
      // background: rgba(171, 212, 255, 0.08);
      padding-top: 61px;
      padding-bottom: 69px;
      padding-left: 64px;
      max-width: 750px;
      margin: 0 auto;
      > li {
        width: 284px;
        text-align: center;
        .content-icon {
          width: 80px;
          height: 80px;
          background-repeat: no-repeat;
          background-size: contain;
          margin: 0 auto;
        }
        p {
          font-size: 36px;
          font-weight: 400;
          color: #444444;
          line-height: 50px;
          margin-top: 26px;
        }
      }
      > li:nth-child(even) {
        margin-left: 54px;
      }
      > li:not(:last-child) {
        margin-bottom: 90px;
      }
    }
  }
}
</style>
<style lang="scss">
.main-content2 {
  .section {
    .el-tabs__nav-scroll {
      margin: 0 auto;
    }

    .el-tabs__nav-wrap::after {
      display: none;
    }

    .el-tabs__item {
      padding: 0 42px;
      color: #999999;
      &:hover {
        color: #348cff;
      }
      &.is-active {
        color: #348cff;
      }
    }
    .el-tabs__header {
      padding: 0 56px;
      max-width: 750px;
      margin: 0 auto;
    }
    .el-tabs__content {
      // margin-top: 46px;
      // padding-top:61px;
    }
  }
}
</style>
