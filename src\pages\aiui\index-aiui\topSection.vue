<template>
  <section class="top-section">
    <div class="hm-banner">
      <Slick
        ref="slick"
        class="banner"
        :options="slickOptions"
        v-if="banner.length > 0"
      >
        <a v-for="(item, index) in banner" :key="index" class="top-banner">
          <!-- <div
            class="top-banner-img"
            :style="{
              backgroundImage: `url(${item.bg})`,
            }"
          ></div> -->
          <img class="top-banner-img" :data-lazy="item.bg" />
          <div class="text-area">
            <h1 v-html="item.title"></h1>
            <p v-html="item.desc"></p>
            <div class="know-button">
              <a
                v-for="(it, idx) in item.jumpUrls"
                :key="idx"
                :class="
                  idx % 2 === 0
                    ? 'learn-button learn-operate'
                    : 'learn-button learn-more'
                "
                :href="`${transJumpPageUrl(it.url, {
                  chan: 'AIUI',
                  way: 'banner',
                })}`"
                target="_blank"
                >{{ it.name }}</a
              >
            </div>
          </div>
        </a>
      </Slick>
      <div class="shadow"></div>
    </div>
    <section
      v-if="userInfo.mobile || userInfo.email"
      class="hm-user-card-container-login"
    >
      <div class="hm-user-card">
        <div class="hm-user-card-info">
          <div class="hm-user-card-top">
            <div class="user-info">
              <div class="img-wrap">
                <img v-lazy="avatar" alt="" />
              </div>
              <div class="user-info-right">
                <div class="name">
                  {{ userInfo.mobile || encryptedmailbox(userInfo.email) }}
                  <i class="arrow"></i>
                  <div class="logout-panel" @click="dologOut">退出登录</div>
                </div>
                <a
                  v-if="
                    limitCount &&
                    limitCount.hasOwnProperty('sub_account') &&
                    limitCount['sub_account'] != '0'
                  "
                >
                  <span style="cursor: pointer" @click="toCooperation"
                    >协同操作</span
                  >
                </a>
              </div>
            </div>
            <a class="create-center" target="_blank" @click="toPage('/app')">
              我的应用
              <!-- {msgCount !== 0 ? <span class="msg-count">{msgCount}</span> : ''} -->
            </a>
          </div>
          <div class="guide-wrap">
            <div v-for="(item, index) in menu" class="user-guide" :key="index">
              <div :class="['guide-icon', `guide-icon-${index}`]">
                <el-badge
                  :value="messageUnRead"
                  :hidden="messageUnRead === 0"
                  :max="99"
                  v-if="item.icon == 'message'"
                >
                  <img
                    v-lazy="require(`@A/images/home/<USER>/${item.icon}.png`)"
                    style="margin-top: 5px"
                    alt=""
                  />
                </el-badge>
                <img
                  v-else
                  v-lazy="require(`@A/images/home/<USER>/${item.icon}.png`)"
                  alt=""
                />
              </div>
              <a class="guide-tip" target="_blank" :href="item.targetUrl">
                <div class="guide-title">{{ item.title }}</div>
              </a>
            </div>
          </div>
        </div>
        <div class="hm-user-card-relavant">
          <div class="user-relavant">
            <div class="ut-tabs">
              <div
                v-for="(item, index) in news"
                :class="[
                  'ut-tab-item',
                  { 'ut-tab-item__active': index === cardActiveKey },
                ]"
                :key="index"
                @mouseenter="cardActiveKey = index"
              >
                {{ item.title }}
              </div>
            </div>
            <div class="relavant-lists">
              <div v-if="cardActiveKey == 0">
                <a
                  v-for="(relavant, rIndex) in news[0].list"
                  :key="`relavant${rIndex}`"
                  class="relavant-list relavant-list-ability"
                  :href="relavant.targetUrl"
                  target="_blank"
                >
                  <span class="label">{{ relavant.title }}</span>
                  <span
                    class="content"
                    style="cursor: pointer; color: $primary"
                    >{{ relavant.desc }}</span
                  >
                </a>
              </div>
              <div v-else>
                <a
                  v-for="(relavant, rIndex) in news[1].list"
                  :key="`relavant${rIndex}`"
                  class="relavant-list relavant-list-calendar"
                  :href="relavant.targetUrl"
                >
                  <div class="content">
                    <span class="dot" />
                    <div class="desc" :title="relavant.desc">
                      {{ relavant.desc }}
                    </div>
                  </div>
                  <span class="time">{{ relavant.time }}</span>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    <section v-else class="hm-user-card-container-no-login">
      <div class="hm-user-card">
        <div class="card-title">Hi，您好！</div>
        <div class="card-subtitle">欢迎来到AIUI 开放平台</div>
        <div class="card-tip">立即登录探索人工智能世界</div>
        <div class="hm-card-button" @click="toPage('/user/login')">
          登录注册
        </div>
      </div>
    </section>
    <section class="hm-guide-container">
      <div class="hm-guide">
        <a
          class="hm-guide-item"
          v-for="(item, index) in nav_bottom"
          :key="index"
          :href="item.src"
          target="_blank"
          rel="noreferrer"
        >
          <div class="img-wrap">
            <img v-lazy="item.imgUrl" alt="" />
          </div>
          <div>
            <div class="hm-guide-item-title">{{ item.title }}</div>
            <div class="hm-guide-item-subtitle">{{ item.text }}</div>
          </div>
        </a>
      </div>
    </section>
  </section>
</template>
<script>
import avatar from '@A/images/home/<USER>/avatar.png'
import { mapGetters } from 'vuex'
import Slick from 'vue-slick'
import { transJumpPageUrl } from '@U/transJumpPageUrl.js'
import { encryptedmailbox } from '@U/utils.js'

export default {
  data() {
    return {
      avatar,
      slickOptions: {
        autoplay: true,
        autoplaySpeed: 3000,
        dots: true,
        infinite: true,
        speed: 1000,
        arrows: false,
        lazyLoad: 'ondemand',
        slidesToShow: 1,
        slidesToScroll: 1,
        pauseOnDotsHover: true,
        pauseOnHover: true,
        // 定制的dots css代码在slick.scss中
        customPaging: (i) => (
          <div class="hm-banner-dot">
            <div class="hm-dot-progress">
              <img
                src="https://aixfyun-cn-bj.xfyun.cn/aixfyun/1682230091000/bug.svg"
                alt=""
                class="icon-bug"
              />
            </div>
          </div>
        ),
      },
      panelShow: 'login',
      banner: [],
      nav_bottom: [],
      menu: [
        {
          targetUrl: '/message',
          icon: 'message',
          title: '消息通知',
        },
        {
          targetUrl: '/user/info',
          icon: 'info',
          title: '个人信息',
        },
      ],
      cardActiveKey: 0,
      news: [
        {
          title: '能力推荐',
          list: [
            {
              targetUrl: '/modelExperience',
              title: '热门推荐',
              desc: 'AIUI大模型交互',
            },
            {
              targetUrl: '/solution/multimodality?chan=AIUI',
              title: '热门推荐',
              desc: '多模态降噪',
            },
            {
              targetUrl: '/solution/offline?chan=AIUI',
              title: '热门推荐',
              desc: '离线语音交互',
            },
            {
              targetUrl: '/solution/multimodal-interaction?chan=AIUI',
              title: '热门推荐',
              desc: '多模态交互',
            },
          ],
        },
        {
          title: '活动日历',
          list: [
            {
              targetUrl: 'javascript:;',
              time: '2024-5-17',
              desc: '中国智慧轨道交通展',
            },
          ],
        },
      ],
    }
  },
  components: { Slick },
  computed: {
    ...mapGetters({
      userInfo: 'user/userInfo',
      messageUnRead: 'user/messageUnRead',
      subAccountInfo: 'user/subAccountInfo',
      subAccount: 'user/subAccount',
      limitCount: 'aiuiApp/limitCount',
      subAccountHasCreateSkillAuth: 'aiuiApp/subAccountHasCreateSkillAuth',
      subAccountSkillAuths: 'aiuiApp/subAccountSkillAuths',
    }),
  },
  created() {
    // 获取首页banner和钻石展位信息
    this.getBannerAndBooth()
  },
  methods: {
    transJumpPageUrl,
    encryptedmailbox,
    toCooperation() {
      let routeData = this.$router.resolve({ path: '/cooperation' })
      window.open(routeData.href, '_blank')
    },
    onGuideClick(path) {
      this.toPage(path)
    },
    toPage(path) {
      this.$router.push({ path })
    },
    toBlankPage(path) {
      const routeData = this.$router.resolve({ path })
      window.open(routeData.href, '_blank')
    },
    getBannerAndBooth() {
      let that = this
      this.$utils.httpGet(
        `/resource/banner/list`,
        {},
        {
          noLogin: true,
          noMessage: true,
          success: (res) => {
            that.banner = (res.data.homeBanner || [])

              .sort((obj1, obj2) => {
                if (obj1.number < obj2.number) {
                  return -1
                } else if (obj1.number > obj2.number) {
                  return 1
                } else {
                  return 0
                }
              })
              .map((item, index) => {
                return {
                  bg: item.imgUrl,
                  bgSize: 'cover',
                  desc: item.description,
                  title: item.title,
                  jumpUrls: item.jumpUrls,
                }
              })
            const length = (res.data.homeBooth || []).length
            that.nav_bottom = (res.data.homeBooth || [])
              .sort((obj1, obj2) => {
                if (obj1.number < obj2.number) {
                  return -1
                } else if (obj1.number > obj2.number) {
                  return 1
                } else {
                  return 0
                }
              })
              .map((item, idx) => {
                return {
                  title: item.title,
                  text: item.description,
                  hot: idx >= length / 2,
                  new: idx < length / 2,
                  src:
                    item.jumpUrls.length === 1
                      ? transJumpPageUrl(item.jumpUrls[0].url, {
                          chan: 'AIUI',
                          way: 'diamond',
                        })
                      : '',
                  imgUrl: item.imgUrl,
                }
              })

            if (res.data.homeGray) {
              if (
                document.getElementsByTagName('html') &&
                document.getElementsByTagName('html')[0]
              ) {
                document.getElementsByTagName('html')[0].style.filter =
                  'grayscale(1)'
              }
            }
          },
          error: (err) => {},
        }
      )
    },

    dologOut() {
      localStorage.removeItem('AIUI_GLOBAL_VARIABLE')
      setTimeout(() => {
        this.toPage('/user/logout', 'aiui')
      }, 50)
    },
  },
}
</script>
<style lang="scss" scoped>
@import './common.scss';

.top-section {
  min-width: 1200px;
  position: relative;
  height: 546px;

  .hm-banner {
    position: relative;

    .banner {
      height: 448px;
    }

    .shadow {
      width: 100%;
      height: 70px;
      position: absolute;
      left: 0;
      bottom: 0;
      background: linear-gradient(0deg, #99cafd, rgba(161, 198, 255, 0));
    }
  }

  .top-banner {
    position: relative;
    height: 448px;

    &-img {
      display: block;
      width: 100%;
      min-height: 100%;
      background-position: center;
      background-repeat: no-repeat;
      object-fit: cover;
      object-position: center;
    }

    .text-area {
      width: 1200px;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      margin: 0 auto;
      height: 282px;

      h1 {
        font-size: 40px;
        line-height: 40px;
        font-family: PingFangSC, PingFangSC-Semibold, Microsoft YaHei;
        font-weight: 600;
        color: #00062d;
        max-width: 457px;
      }

      p {
        font-size: 18px;
        font-weight: 400;
        color: #333657;
        font-family: PingFangSC, PingFangSC-Regular, Microsoft YaHei;
        line-height: 26px;
        margin-top: 20px;
        max-width: 457px;
      }

      .know-button {
        position: absolute;
        left: 0px;
        bottom: 0;
        z-index: 1;

        .learn-button + .learn-button {
          margin-left: 20px;
        }
      }

      .learn-more {
        display: inline-block;
        width: 144px;
        height: 36px;
        // background: rgba(255, 255, 255, 0.5);
        // border: 1px solid $primary;
        font-size: 14px;
        font-family: PingFangSC, PingFangSC-Regular, Microsoft YaHei;
        font-weight: 400;
        line-height: 36px;
        text-align: center;
        color: $primary;
        // border: 1px solid $primary;

        &.special {
          left: 210px;
        }
      }

      .learn-operate {
        display: inline-block;
        text-align: center;
        width: 144px;
        height: 36px;
        background: $primary;
        font-size: 14px;
        font-family: PingFangSC, PingFangSC-Regular;
        font-weight: 400;
        color: #ffffff;
        line-height: 36px;
      }
    }
  }

  .hm-user-card-container-login {
    position: absolute;
    z-index: 1;
    right: 0;
    top: 58px;
    margin-right: calc(50% - 600px);

    .hm-user-card {
      width: 321px;
      height: 338px;
      background: url(~@A/images/home/<USER>/card.png) center center / 100% 100%
        no-repeat;
      box-sizing: border-box;
      padding: 29px 22px 0 21px;
    }

    .hm-user-card-info {
      .hm-user-card-top {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 22px;
        padding-left: 3px;

        .user-info {
          display: flex;
          align-items: center;

          .img-wrap {
            width: 38px;
            height: 38px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            overflow: hidden;
            margin-right: 8px;
            margin-top: 3px;

            > img {
              max-width: 100%;
              height: 100%;
            }
          }

          .name {
            font-size: 14px;
            font-weight: 600;
            color: #4b5b76;
            display: flex;
            align-items: center;
            cursor: pointer;
            position: relative;

            .arrow {
              display: inline-block;
              width: 10px;
              height: 6px;
              background: url(~@A/images/home/<USER>/arrow.svg) center center /
                100% 100% no-repeat;
              transition: all 0.1s ease-out;
              margin-left: 10px;
            }

            .logout-panel {
              display: none;
              position: absolute;
              top: 18px;
              left: 38px;
              width: 80px;
              height: 25px;
              line-height: 25px;
              background: #ffffff;
              color: #4b5b76;
              text-align: center;
              font-weight: 400;
              font-size: 13px;
              box-shadow: 0px 2px 1px 0px rgba(0, 0, 0, 0.12);
              border: 1px solid;
              border-image: linear-gradient(
                  180deg,
                  rgba(218, 232, 243, 1),
                  rgba(184, 216, 255, 1)
                )
                1 1;
            }

            &:hover {
              .arrow {
                transform: rotate(180deg);
              }

              .logout-panel {
                display: block;
              }
            }
          }

          .level {
            display: block;
            height: 20px;
            width: 54px;
            background: url(~@A/images/home/<USER>/name_bg.png) center center /
              100% 100% no-repeat;

            &-text {
              line-height: 20px;
              display: block;
              color: #ec7f11;
              font-family: RZGF;
              font-weight: 600;
              font-size: 12px;
              padding-left: 24px;
            }
          }
        }

        .create-center {
          display: inline-block;
          text-decoration: none;
          width: 87px;
          height: 36px;
          line-height: 36px;
          text-align: center;
          background: linear-gradient(180deg, #ffffff 0%, #f9fbff 100%);
          color: #1975ff;
          border-radius: 4px;
          border: 1px solid rgba(139, 180, 241, 0.82);
          font-size: 16px;
          font-weight: 600;
          cursor: pointer;
          position: relative;
          box-shadow: 0px 4px 10px rgba(25, 117, 255, 0.17);

          .msg-count {
            position: absolute;
            top: -4px;
            left: 72px;
            font-size: 12px;
            height: 14px;
            line-height: 14px;
            font-weight: 600;
            padding: 0px 8px;
            background: linear-gradient(180deg, #ff997b 0%, #ff805a 100%);
            border-radius: 7px;
            color: #ffffff;
          }

          &:hover {
            box-shadow: 0px 4px 10px rgba(25, 117, 255, 0.37);
          }
        }
      }

      .guide-wrap {
        display: flex;
        justify-content: space-between;
        padding: 0 6px;

        .user-guide,
        .user-survey {
          display: flex;
          cursor: pointer;

          .guide-icon {
            width: 38px;
            height: 38px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid #c3d6ef;
            margin-right: 7px;
            background: linear-gradient(
              204deg,
              rgba(211, 229, 255, 0.4) 0%,
              rgba(175, 211, 255, 0.4) 100%
            );
            border-radius: 50%;

            img {
              max-width: 75%;
              max-height: 75%;
            }

            &-0 {
              img {
                margin-left: 4px;
                max-width: 67%;
                max-height: 67%;
              }
            }
          }

          .guide-tip {
            text-decoration: none;

            .guide-title {
              font-size: 16px;
              font-family: PingFangSC, PingFangSC-Semibold;
              font-weight: 600;
              color: #4b5b76;
              line-height: 38px;
            }

            &:hover {
              .guide-title {
                color: #1975ff;
              }
            }
          }
        }
      }
    }

    .hm-user-card-relavant {
      margin-top: 17px;

      .user-relavant {
        .ut-tabs {
          height: 30px;
          display: flex;
          border-bottom: 1px solid #ebebeb;

          .ut-tab-item {
            height: 30px;
            color: #4b5b76;
            font-size: 14px;
            cursor: pointer;
            padding: 2px 8px;
            position: relative;
            margin-right: 10px;

            &:last-child {
              margin-right: 0;
            }

            &::after {
              content: '';
              width: 90%;
              height: 0;
              border-bottom: 2px solid $primary;
              position: absolute;
              bottom: 0;
              left: 50%;
              transform: translateX(-50%);
              display: none;
            }

            &__active::after {
              display: block;
            }

            &__active {
              color: $primary;
              font-family: PingFangSC, PingFangSC-Medium;
              font-weight: 500;
            }
          }
        }

        .relavant-lists {
          padding-top: 12px;

          .relavant-list {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            cursor: default;

            &:last-child {
              margin-bottom: 0;
            }

            .label {
              flex-shrink: 0;
              box-sizing: border-box;
              display: inline-block;
              padding: 0 6px;
              height: 20px;
              line-height: 20px;
              background: #fff1e8;
              border-radius: 2px;
              font-size: 12px;
              font-weight: 400;
              color: #c28358;
              text-align: center;
              margin-right: 7px;
              border: 1px solid #ffdac2;
            }

            .content {
              flex: 1;
              flex-shrink: 0;
              font-size: 12px;
              font-weight: 400;
              color: #4b5b76;
            }

            // &:hover {
            //   .content {
            //     color: #1975ff;
            //   }
            // }

            &-ability .content {
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }

            &-calendar {
              margin-bottom: 8px;

              .content {
                display: flex;
                align-items: center;

                .dot {
                  flex-shrink: 0;
                  display: inline-block;
                  width: 22px;
                  height: 22px;
                  border-radius: 50%;
                  background: #b1b1b1;
                  position: relative;
                  transform: scale(0.5);
                  margin-right: 3px;

                  &::after {
                    content: '';
                    width: 20px;
                    height: 20px;
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    border-radius: 50%;
                    transform: translate3d(-50%, -50%, 0);
                    background-color: #fff;
                  }

                  &::before {
                    content: '';
                    width: 0;
                    height: 48px;
                    border-right: 1px dashed #cecece;
                    position: absolute;
                    top: 100%;
                    left: 50%;
                    transform: translateX(-50%);
                  }
                }

                .desc {
                  width: 170px;
                  flex-shrink: 0;
                  padding-right: 6px;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                }
              }

              .time {
                flex-shrink: 0;
                font-size: 12px;
                color: #64738d;
              }

              &:last-child .content .dot::before {
                display: none;
              }

              &:hover {
                .content .dot {
                  background-color: #1975ff;
                }
              }
            }
          }
        }
      }
    }
  }

  .hm-user-card-container-no-login {
    position: absolute;
    z-index: 1;
    right: 0;
    margin-right: calc(50% - 600px);
    top: 68px;

    .hm-user-card {
      width: 321px;
      height: 322px;
      box-sizing: border-box;
      padding: 40px;
      text-align: center;
      background: url(~@A/images/home/<USER>/card2.png) center center / 100% 100%
        no-repeat;
    }

    .card-title {
      font-size: 22px;
      font-weight: 600;
      color: #4b5b76;
      margin-bottom: 4px;
      text-align: left;
      padding-left: 7px;
    }

    .card-subtitle {
      font-size: 16px;
      font-weight: 600;
      color: #4b5b76;
      letter-spacing: 1px;
      text-align: left;
      padding-left: 7px;
      margin-top: 8px;
    }

    .card-tip {
      margin-top: 69px;
      font-size: 14px;
      font-family: PingFangSC, PingFangSC-Regular;
      font-weight: 400;
      text-align: center;
      color: #96aec6;
    }

    .hm-card-button {
      width: 241px;
      height: 42px;
      margin-top: 24px;
      background: linear-gradient(270deg, #00d7ff, #235ae8);
      border-radius: 4px;
      text-align: center;
      font-size: 16px;
      font-weight: 600;
      color: #ffffff;
      line-height: 42px;
      margin-bottom: 17px;
      cursor: pointer;
      letter-spacing: 1px;

      &:hover {
        background: linear-gradient(270deg, #00d7ff, #235ae8);
        box-shadow: 0px 4px 12px 0px rgba(74, 104, 176, 0.4);
      }
    }
  }

  .hm-guide-container {
    width: 100%;
    height: 88px;
    background: linear-gradient(180deg, #ffffff, #effaff 57%, #eef7ff);
    box-shadow: 0px 6px 24px 0px rgba(62, 117, 251, 0.2);

    & * {
      box-sizing: border-box;
    }

    .hm-guide {
      width: 1200px;
      height: 100%;
      margin: 0 auto;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .hm-guide-item {
        flex-shrink: 0;
        flex: 1;
        height: 100%;
        text-decoration: none;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        padding-left: 16px;
        box-sizing: border-box;
        border-right: 2px solid;
        border-image: linear-gradient(
            180deg,
            rgba(188, 231, 255, 0),
            #bce7ff 54%,
            rgba(188, 231, 255, 0)
          )
          2 2;

        &:hover {
          .hm-guide-item-title,
          .hm-guide-item-subtitle {
            color: #1975ff;
          }
        }

        &:last-child {
          border-right: none;
        }

        .img-wrap {
          width: 74px;
          height: 67px;
          display: flex;
          justify-content: center;
          align-items: center;
          margin-right: 16px;

          > img {
            max-width: 100%;
            max-height: 100%;
          }
        }

        &-title {
          font-size: 18px;
          font-weight: 600;
          color: #000000;
          line-height: 26px;
          font-family: PingFangSC, PingFangSC-Medium, Microsoft YaHei;
        }

        &-subtitle {
          font-size: 14px;
          font-weight: 400;
          color: #8e90a5;
          margin-top: 4px;
          font-family: PingFangSC, PingFangSC-Regular, Microsoft YaHei;
        }
      }
    }
  }
}

:deep(.slick-dots) {
  position: absolute;
  bottom: 24px;
  display: block;
  list-style: none;
  z-index: 100;
  box-sizing: border-box;
  width: 1200px;
  left: 50%;
  transform: translate(-50%, 0);

  li {
    position: relative;
    display: inline-block;
    width: 100px;
    height: 4px;
    background: hsla(0, 0%, 100%, 0.3);
    margin-right: 12px;
    margin-top: 46px;
    cursor: pointer;
  }

  .slick-active {
    position: relative;

    &::before {
      content: ' ';
      position: absolute;
      border-radius: 0px;
      z-index: 100;
      width: 100px;
      height: 4px;
      background: #fff;
      top: 0;
      left: 0;
      animation: ani-step 3s linear 0s 1;
    }

    .hm-dot-title {
      font-weight: 600;
      opacity: 1;
    }

    .hm-dot-progress {
      opacity: 1;
      position: relative;

      &::after {
        opacity: 1;
        width: 118px;
        animation: borderAni 3s linear;
        transform-origin: left center;
      }

      .icon-bug {
        z-index: 2;
        opacity: 0;
        display: block;
        animation: bugAni 3s linear;
        animation-fill-mode: forwards;
      }
    }
  }
}

@keyframes borderAni {
  from {
    transform: scaleX(0);
  }

  to {
    transform: scaleX(1);
  }
}

@keyframes bugAni {
  from {
    opacity: 1;
    transform: translate3d(0, -7px, 0);
  }

  to {
    opacity: 1;
    transform: translate3d(104px, -7px, 0);
  }
}

@keyframes ani-step {
  from {
    width: 0px;
  }

  to {
    width: 100%;
  }
}
</style>
