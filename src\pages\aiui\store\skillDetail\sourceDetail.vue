<template>
  <div class="store-skill-sources container">
    <p class="store-skill-label mgb8">信源列表</p>

    <div class="store-skill-content">
      <div
        class="store-skill-source"
        v-for="(provider, index) in providers"
        :key="index"
      >
        <div class="store-skill-source-info">
          <div class="store-skill-source-info-thumb">
            <img :src="provider.logo" />
          </div>
          <span>{{ provider.name }}</span>
        </div>
        <div class="store-skill-source-desc">
          {{ provider.description }}
        </div>
      </div>
      <p class="store-skill-label mgb8">信源接口</p>
      <template v-for="agreement in sourceAgreement">
        <div v-for="(func, index) in agreement.functions" :key="index">
          <p class="source-title">
            {{ func.name }}
          </p>
          <el-table
            class="store-skill-table-default"
            :data="func.outputFields"
            style="width: 100%"
          >
            <el-table-column prop="name" label="字段名" min-width="150">
            </el-table-column>
            <el-table-column prop="datatype" label="字段类型" min-width="100">
            </el-table-column>
            <el-table-column prop="isSort" label="有序" min-width="50">
            </el-table-column>
            <el-table-column
              min-width="500"
              prop="description"
              label="字段说明（包含取值）"
              show-overflow-tooltip
            >
            </el-table-column>
          </el-table>
        </div>
      </template>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {}
  },
  props: {
    providers: Array,
    sourceAgreement: Array,
  },
}
</script>
<style lang="scss" scoped>
.container {
  padding-top: 30px;
}
.source-title {
  margin-top: 10px;
  margin-bottom: 4px;
}
// 技能商店适配不同屏幕
@media screen and (max-width: 1601px) {
  .container {
    padding-top: 20px;
  }
  .source-title {
    font-size: 12px;
  }
}
</style>
