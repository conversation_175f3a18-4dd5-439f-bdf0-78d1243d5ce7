<template lang="html">
  <div class="aiui">
    <div class="banner-wrap">
      <div class="service-wrap">
        <div class="service">
          <a class="service-btn mobile-btn" href="/app/add?platform=WebAPI"
            >免费试用</a
          >
          <a class="service-btn" :href="DocsInUrl" target="_blank">阅读文档</a>
        </div>
      </div>
    </div>
    <div class="group-wrap">
      <div class="group-title">方案能力</div>
      <div class="group-content solution-capabilities">
        <p>
          AIUI服务器接收文本或者一段小于60s的音频，借助讯飞的人工智能引擎，返回给开发者听写结果和结构化的语义信息
        </p>
      </div>
    </div>
    <div class="group-wrap apply-platform">
      <div class="group-title">应用平台</div>
      <div class="apply-platform-info">
        采用REST API，将选择权交给开发者<br />
        通过HTTP协议，可用于网站，Java、Python、小程序、微信等多种编程场景
      </div>
    </div>
    <div class="group-wrap">
      <div class="group-title">接入流程</div>
      <div class="using-process-content"></div>
    </div>
  </div>
</template>

<script>
import utils from '../../../assets/lib/utils.js'

export default {
  layout: 'aiuiHome',
  head() {
    return {
      title: 'AIUI WebAPI解决方案',
      meta: [
        { name: 'keywords', content: 'AIUI，科大讯飞，AIUI 开放平台，WebAPI' },
        {
          name: 'description',
          content:
            '提供基于 WebAPI 的协议接入方案，使用于多种操作系统。提供完备的智能语音，语义理解，闲聊问答等解决方案。',
        },
      ],
    }
  },
  data() {
    return {
      DocsInUrl: `${this.$config.docs}doc-17/`,
    }
  },
  methods: {
    coreClick(index) {
      this.coreIndex = index
      this.coreSwiper.slideTo(index, 1000, false)
    },
  },
}
</script>
<style lang="scss" scoped>
.banner-wrap {
  min-width: 1200px;
  position: relative;
  height: 460px;
  padding-top: 60px;
  box-sizing: border-box;
  background: url('../../../assets/images/solutions/webapi/banner.png') #05134a
    no-repeat center bottom;
}

.prompt_content {
  padding: 0 0 0 20px;
}

.service-wrap {
  width: 1200px;
  margin: 0 auto;
  color: #fff;
  font-size: 16px;
  position: relative;
}

.service {
  position: absolute;
  top: 255px;
  left: 95px;
}
.service-btn {
  width: 146px;
  height: 45px;
  color: #fff;
  line-height: 45px;
  text-align: center;
  border-radius: 5px;
  background: #3f4ccc;
  border: 2px solid #3f4ccc;
  display: inline-block;
}

.mobile-btn {
  margin-right: 35px;
  border-color: #fff;
  background: none;
}
.group-wrap {
  margin-top: 65px;
  margin-bottom: 30px;
  min-width: 1200px;

  .group-title {
    height: 70px;
    font-size: 26px;
    margin-bottom: 25px;
    text-align: center;
    background: url('../../../assets/images/solutions/webapi/hp-title-icon.png')
      no-repeat center;
    background-position-y: 45px;
  }
  .group-content {
    color: #666;
    font-size: 16px;
    text-align: center;
  }
}

.apply-area-service {
  width: 348px;
  height: 370px;
  display: inline-block;
  vertical-align: middle;
  .service-item {
    margin-top: 300px;
  }
}

.solution-capabilities {
  height: 240px;
  background: url('../../../assets/images/solutions/webapi/solution-capabilities.png')
    no-repeat;
  background-position: center 50px;
}
.apply-platform {
  height: 380px;
  font-size: 18px;
  text-align: center;
  background: url('../../../assets/images/solutions/webapi/app-platform.png')
    no-repeat center 100px;

  .apply-platform-info {
    margin-top: 90px;
    line-height: 40px;
    color: #fff;
  }
}

.using-process-content {
  height: 280px;
  margin: auto;
  background: url('../../../assets/images/solutions/webapi/using-process.png')
    no-repeat center;
}
.experience-btn {
  width: 246px;
  height: 60px;
  line-height: 60px;
  font-size: 18px;
  color: #333;
  border-color: #6f70b4;
  background: #fff;
  margin: 20px 0 60px;
}
</style>
