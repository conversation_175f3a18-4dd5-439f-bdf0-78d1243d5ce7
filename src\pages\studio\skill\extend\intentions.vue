<template>
  <os-page :options="pageOptions" v-loading="tableData.loading">
    <studio-skill-header-right slot="btn" />
    <div class="os-scroll">
      <div class="mgt32 mgb24" style="font-size: 0">
        <el-button
          class="mgr16"
          icon="ic-r-plus"
          type="primary"
          size="small"
          :disabled="!subAccountEditable"
          @click="openCreateIntent"
        >
          创建意图
        </el-button>

        <upload
          :businessName="skill.zhName"
          :businessId="businessId"
          :limitCount="limitCount"
          :subAccountEditable="subAccountEditable"
          :skillType="skill.type"
          :subAccount="subAccount"
          @setLoad="setLoad"
          @reloadAfterUpload="reloadAfterUpload"
        ></upload>
        <el-popover
          placement="right"
          width="136"
          trigger="click"
          :disabled="!subAccountEditable"
        >
          <el-switch
            v-model="isFuzzy"
            :active-value="true"
            :inactive-value="false"
            active-text="模糊匹配"
            @change="save"
          />
          <i
            slot="reference"
            class="ic-r-more btn-fuzzy"
            :class="{ 'not-allowed': !subAccountEditable }"
            style="margin-left: 10px"
          />
        </el-popover>

        <!-- <div class="ib">
          <el-button size="small" v-if="!skill.privateSkill" @click="openAddOfficialIntent">
            引用官方意图
          </el-button>
        </div> -->
      </div>
      <div class="mgb24" @keyup.enter="searchIntention">
        <el-input
          class="search-area"
          placeholder="搜索意图"
          v-model="intentionSearchName"
        >
          <i
            slot="suffix"
            class="el-input__icon el-icon-search search-area-btn"
            @click="searchIntention"
          />
        </el-input>
      </div>

      <os-table
        :tableData="tableData"
        style="margin-bottom: 56px"
        @change="changePage"
        @row-click="toEdit"
        :span-method="mergeCells"
      >
        <el-table-column type="index" width="50">
          <template slot-scope="scope">
            {{ (tableData.page - 1) * tableData.size + scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="zhName" label="意图名称">
          <div class="zhName-wrap" slot-scope="scope">
            <div class="intent-zhname ib">
              {{ scope.row.zhName }}
            </div>
            <el-tooltip
              v-if="scope.row.source"
              effect="dark"
              content="源技能的意图"
              placement="top"
            >
              <div class="intent-tag ib source-skill-icon">源</div>
            </el-tooltip>
            <el-tooltip
              v-if="scope.row.official || scope.row.type == 6"
              effect="dark"
              content="官方意图"
              placement="top"
            >
              <div class="intent-tag ib">官</div>
            </el-tooltip>
          </div>
        </el-table-column>
        <el-table-column prop="name" label="英文标识"> </el-table-column>
        <el-table-column label="操作" width="120">
          <template slot-scope="scope">
            <i class="ic-r-edit cell-handle-ic"></i>
            <i
              v-if="subAccountEditable && !scope.row.source"
              class="cell-handle-hovershow ic-r-delete cell-handle-ic"
              @click.prevent.stop="toDel(scope.row)"
            ></i>
          </template>
        </el-table-column>
      </os-table>
    </div>
    <create-intent-dialog
      :dialog="createIntentDialog"
      @change="getIntentions"
    />
    <add-official-intent-dialog
      :dialog="addOfficialIntentDialog"
      @change="getIntentions"
    />
  </os-page>
</template>

<script>
import { mapGetters } from 'vuex'
import CreateIntentDialog from './dialog/createIntent'
import AddOfficialIntentDialog from '../dialog/addOfficialIntent'
import Upload from '../uploadIntent'

export default {
  name: 'extend-skill-intentions',
  data() {
    return {
      pageOptions: {
        title: '意图',
        loading: false,
      },
      intentionSearchName: '',
      tableData: {
        loading: true,
        total: 0,
        page: 1,
        size: 10,
        list: [],
      },
      initIntentList: [],
      createIntentDialog: {
        show: false,
      },
      addOfficialIntentDialog: {
        show: false,
      },
      selectedIntention: {},
      isFuzzy: true,
    }
  },
  computed: {
    ...mapGetters({
      businessId: 'studioSkill/id',
      skill: 'studioSkill/skill',
      limitCount: 'aiuiApp/limitCount',
      subAccount: 'user/subAccount',
      subAccountSkillAuths: 'studioSkill/subAccountSkillAuths',
    }),
    subAccountEditable() {
      return this.subAccountSkillAuths[this.skill.id] == 2 ? false : true
    },
  },
  watch: {
    'skill.isFuzzy': function (val) {
      this.isFuzzy = val ? true : false
    },
  },
  created() {
    this.getIntentions()
    this.isFuzzy = this.skill.isFuzzy ? true : false
  },
  methods: {
    save() {
      let self = this
      this.$utils.httpPost(
        this.$config.api.STUDIO_SKILL_UP_FUZZY,
        {
          skillId: this.businessId,
          isFuzzy: this.isFuzzy ? 1 : 0,
        },
        {
          success: (res) => {
            self.$message.success('保存成功')
            self.$store.dispatch('studioSkill/setSkill', this.businessId)
          },
          error: (err) => {
            console.log(err)
          },
        }
      )
    },
    getIntentions(page) {
      this.tableData.loading = true
      this.$utils.httpGet(
        this.$config.api.STUDIO_INTENTS,
        {
          businessId: this.businessId,
          pageIndex: page || this.tableData.page,
          pageSize: '',
          search: this.intentionSearchName,
        },
        {
          success: (res) => {
            this.initIntentList = res.data.intents
            let blackIntentIndex = -1
            this.initIntentList.forEach((item, index) => {
              if (item.name == 'iFLYTEK.BlackIntent' && item.type == 6) {
                blackIntentIndex = index
              }
            })
            if (blackIntentIndex !== -1) {
              this.initIntentList.unshift(this.initIntentList[blackIntentIndex])
              this.initIntentList.splice(blackIntentIndex + 1, 1)
              this.$store.dispatch('studioSkill/setHasBlackIntent', true)
            } else {
              this.$store.dispatch('studioSkill/setHasBlackIntent', false)
            }
            this.tableData.loading = false
            this.tableData.total = res.data.count
            this.formate()
          },
          error: (err) => {
            this.tableData.loading = false
          },
        }
      )
    },
    formate() {
      let list = []
      let page = this.tableData.page
      let size = this.tableData.size
      this.tableData.list.splice(0)
      this.tableData.list = this.initIntentList.slice(
        (page - 1) * size,
        page * size
      )
    },
    changePage(val) {
      this.tableData.page = val
      this.formate()
    },
    searchIntention() {
      this.getIntentions(1)
    },
    toDel(row) {
      let self = this
      let confirmTitle =
        row.zhName && row.zhName.length > 9
          ? `${row.zhName.substring(0, 9)}...`
          : row.zhName
      let desc
      if (this.tableData.list.length === 1) {
        desc = '至少保留一个意图，否则系统将无法进入您的技能。'
      } else {
        desc = '意图删除后不可恢复，请谨慎操作。'
      }

      this.$confirm(desc, `确定删除意图 - ${confirmTitle}`, {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        confirmButtonClass: 'el-button--danger',
        type: 'warning',
        showClose: false,
      })
        .then(() => {
          self.delIntent(row)
        })
        .catch(() => {})
    },
    delIntent(row) {
      let self = this
      this.$utils.httpPost(
        this.$config.api.STUDIO_DEL_INTENT,
        {
          businessId: this.businessId,
          intentId: row.id,
        },
        {
          success: (res) => {
            self.$message.success('删除成功')
            if (self.tableData.list.length === 1 && self.tableData.page > 1) {
              self.tableData.page -= 1
            }
            self.getIntentions()
            self.$store.dispatch('studioSkill/initHasSkillQuote', 0)
            self.$store.dispatch('studioSkill/initHasSkillQuote', 1)
          },
          error: (err) => {
            console.log('page=>>')
            console.log(err)
          },
        }
      )
    },
    openCreateIntent() {
      this.$store.dispatch('studioSkill/openCreateIntentDialog')
    },
    openAddOfficialIntent() {
      this.addOfficialIntentDialog.show = true
    },
    mergeCells({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 5) {
        //5：删除按钮的index值
        if (rowIndex % 2 === 0) {
          return {
            rowspan: 2,
            colspan: 1,
          }
        } else {
          return {
            rowspan: 0,
            colspan: 0,
          }
        }
      }
    },
    toEdit(intention, event, column) {
      if (!intention.id) {
        this.getSourceSkillIntentId(intention)
        return
      }
      if (this.subAccount) {
        this.$router.push({
          name: 'extend-sub-skill-intention',
          params: { intentId: intention.id },
        })
      } else {
        this.$router.push({
          name: 'extend-skill-intention',
          params: { intentId: intention.id },
        })
      }
    },
    pageReturn() {
      this.selectedIntention = {}
    },
    setLoad(val) {
      this.tableData.loading = val
    },
    reloadAfterUpload() {
      this.getIntentions()
      this.$store.dispatch('studioSkill/setSkill', this.businessId)
      this.$store.dispatch('studioSkill/initHasSkillQuote', 0)
      this.$store.dispatch('studioSkill/initHasSkillQuote', 1)
    },
    getSourceSkillIntentId(intent) {
      let self = this
      let data = {
        businessId: this.businessId,
        quoteId: intent.quoteId,
        name: intent.name,
        zhName: intent.zhName,
        type: 7,
      }
      this.$utils.httpPost(this.$config.api.STUDIO_ADD_EDIT_INTENT, data, {
        success: (res) => {
          intent.id = res.data
          if (this.subAccount) {
            this.$router.push({
              name: 'extend-sub-skill-intention',
              params: { intentId: intent.id },
            })
          } else {
            this.$router.push({
              name: 'extend-skill-intention',
              params: { intentId: intent.id },
            })
          }
        },
        error: (err) => {
          this.submitLoading = false
          console.log('page=>>')
          console.log(err)
        },
      })
    },
  },
  components: {
    CreateIntentDialog,
    AddOfficialIntentDialog,
    Upload,
  },
}
</script>

<style lang="scss" scoped>
.intent-handle-group {
  position: relative;
  margin-right: -3px;
  &::after {
    position: absolute;
    content: ' ';
    width: 1px;
    height: 100%;
    top: 0;
    right: -1px;
    background-color: $grey3;
  }
}
.zhName-wrap {
  font-size: 0;
}
.intent-zhname {
  vertical-align: bottom;
  margin-right: 7px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
}
.btn-fuzzy {
  vertical-align: bottom;
  width: 36px;
  height: 37px;
  border-radius: 2px;
  background-color: $grey4-15;
  text-align: center;
  line-height: 37px;
  font-size: 16px;
  cursor: pointer;
  border-left: 1px solid $grey2;
}
.source-skill-icon {
  margin-right: 4px;
}
</style>
