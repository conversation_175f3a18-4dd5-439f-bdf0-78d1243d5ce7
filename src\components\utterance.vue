<template>
  <div
    class="utterance-area"
    :class="{ 'utterance-area-disabled': !subAccountEditable }"
    @mousemove="moveMark"
    @mousedown="clearSelection"
    @mouseup="handleBrowserSelect"
    @mouseout="removeContent"
  >
    <i
      class="utterance-area-template"
      :class="utterance.template === 1 ? 'ic-brace' : 'ic-logo-eye'"
    />
    <!-- <span
      contenteditable="false"
      class="mark1 dis-user-select"
      v-if="utterance.template === 2 && (hoving || droping)"
      :style="markTagStyle.markOne"
      @mousedown="dropMark('one', $event)"
      @mouseenter="hoverMarkTag"
       /> -->
    <!-- :contenteditable="!mark.slotName" -->
    <div
      :contenteditable="!subAccountEditable ? false : true"
      id="content"
      ref="content"
      :class="[
        {
          'extend-utterance': $route.path && $route.path.match('extend-skill'),
        },
      ]"
      @dblclick="handleBrowserSelect"
      @blur="handleBlur"
      @keydown="handleKeyDown"
      @keyup="angleBracketHandle"
      @paste="handlePaste"
    >
      <!-- @keyup.enter="handleEnter" -->
      <template v-if="utterance.template === 2 && utterance.mark.length > 0">
        <template v-for="(mark, index) in utterance.mark">
          <span
            v-if="mark.slotName"
            :data-index="index"
            class="selection"
            :class="{ 'filler-mark': mark.slotType === 1 }"
            :style="utteranceColor[mark.slotName]"
            @click="handleMarkup(mark, $event)"
            v-on:mouseover="showMarkTag(mark, index, $event)"
            v-on:mouseout="removeMarkTag(mark, index, $event)"
            :data-markid="mark.markId"
            :key="index"
            >{{ mark.text ? mark.text : '{' + mark.slotName + '}' }}</span
          >
          <span v-else :data-markid="mark.markId" :key="index">{{
            mark.text
          }}</span>
        </template>
      </template>
      <template v-if="utterance.template === 1 && utterance.mark.length > 0">
        <template v-for="(mark, index) in utterance.mark">
          <span
            v-if="mark.slotName"
            class="selection"
            :class="{ 'filler-mark': mark.slotType === 1 }"
            :style="utteranceColor[mark.slotName]"
            :key="index"
            >{{ mark.text ? mark.text : '{' + mark.slotName + '}' }}</span
          ><span
            contenteditable="false"
            class="text-blue"
            v-else-if="mark.modifierName"
            :key="index"
            >{{ '<' + mark.modifierName + '>' }}</span
          ><span
            v-else-if="
              mark.symbol === '(' ||
              mark.symbol === ')' ||
              mark.symbol === '[' ||
              mark.symbol === ']' ||
              mark.symbol === '|'
            "
            :data-markid="mark.markId"
            class="text-blod"
            :class="{
              mglr2: mark.symbol === '|',
              mgl3mgr1: mark.symbol === '(' || mark.symbol === '[',
              mgl1mgr3: mark.symbol === ')' || mark.symbol === ']',
              'text-red': mark.symbol === '(' || mark.symbol === ')',
              'text-grey': mark.symbol === '[' || mark.symbol === ']',
            }"
            :key="index"
            >{{ mark.text }}</span
          ><template v-else>{{ mark.text }}</template>
        </template>
      </template>

      <span v-if="utterance.mark.length <= 0">
        {{ utterance.utterance }}
      </span>
      <div v-if="referenceMode" class="intent-tag ib" style="margin-left: 5px">
        引
      </div>
    </div>

    <!-- <span
      contenteditable="false"
      v-if="utterance.template === 2 && (hoving || droping)"
      class="mark2 dis-user-select"
      :style="markTagStyle.markTwo"
      @mousedown="dropMark('two', $event)"
      @mouseenter="hoverMarkTag"
    /> -->
    <div
      class="utterance-area-kv-wrap"
      v-if="
        ($route.path &&
          $route.path.match('extend-intention') &&
          intention &&
          intention.name != 'iFLYTEK.BlackIntent' &&
          intention.type != 6) ||
        $store.state.studioSkill.skill.type == '8'
      "
    >
      <span @click="openKvDialog" v-if="!kVNum">KV</span>
      <span @click="openKvDialog" v-else>KV({{ kVNum }})</span>
      <span @click.prevent.stop="spanClick">
        <el-popover
          placement="bottom-end"
          width="240"
          trigger="click"
          :disabled="!subAccountEditable"
          v-model="scoreWrapVisible"
        >
          <span
            slot="reference"
            v-if="(utterance && !utterance.score) || utterance.score == 0"
            >±0</span
          >
          <span slot="reference" v-if="utterance && utterance.score < 0">{{
            utterance.score
          }}</span>
          <span slot="reference" v-if="utterance && utterance.score > 0"
            >+{{ utterance.score }}</span
          >
          <div>
            <p style="margin-bottom: 6px; color: #8c8c8c">加减分</p>
            <el-input-number
              style="margin-bottom: 16px; width: 208px"
              controls-position="right"
              v-model="initScore"
              :step="0.01"
              :min="-0.1"
              :max="0.1"
              :precision="2"
            ></el-input-number>
            <div style="text-align: right; margin: 0">
              <el-button
                size="mini"
                style="min-width: 64px"
                @click="cancelScore"
                >取消</el-button
              >
              <el-button
                type="primary"
                size="mini"
                style="min-width: 64px"
                @click="editScore(utterance)"
                >确定</el-button
              >
            </div>
          </div>
        </el-popover>
      </span>
    </div>
    <i class="utterance-area-del" v-if="subAccountEditable" @click="del"
      ><i class="ic-r-delete"
    /></i>
    <add-kv-dialog
      :dialog="addKvDialog"
      :slotData="utterance.slotData"
      :markList="utterance.mark"
      :subAccountEditable="subAccountEditable"
      @saveKV="saveKV"
    >
    </add-kv-dialog>
    <os-modifier-select
      ref="modifierSelectPopover"
      :variablePopover="modifierSelectPopover"
      @setModifier="setModifier"
    />
  </div>
</template>

<script>
import AddKvDialog from '../pages/studio/skill/extend/dialog/addKv'

export default {
  name: 'OsUtterance',
  props: {
    defaultUtterance: {
      type: Object,
      default: {},
    },
    defaultIndex: {
      type: Number,
      default: 0,
    },
    subAccountEditable: Boolean,
    // 引用官方意图，不可编辑，且语料后面展示 “引”
    referenceMode: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      utterance: {},
      entitySearchName: '',
      contentDom: '',
      range: '',
      rangeText: '',
      rect: '',

      edited: '',
      parentRectX: '', // 整个区域的x
      editRectX: '', // 被移入选中的x
      editRectW: '', // 被移入选中的w
      hoving: false, // 移入
      droping: '', // 抓住
      clickTimer: null, //单击timer
      addKvDialog: {
        show: false,
      },
      scoreWrapVisible: false,
      initScore: 0,
      slotNames: [],
      cursorPos: -1,
      modifierSelectPopover: {
        show: false,
        rect: null,
      },
      markonModifing: '',
      markOnModifingIndex: null,
    }
  },
  computed: {
    markTagStyle() {
      let markOne = {}
      let markTwo = {}
      // console.log(this.editRectX)
      markOne = { left: `${this.editRectX - this.parentRectX}px` }
      markTwo = {
        left: `${this.editRectX - this.parentRectX + this.editRectW}px`,
      }
      return {
        markOne: markOne,
        markTwo: markTwo,
      }
    },
    utteranceColor() {
      return this.$store.state.studioSkill.utteranceColor
    },
    intention() {
      return this.$store.state.studioSkill.intention
    },
    kVNum() {
      if (this.utterance && !this.utterance.slotData) {
        return 0
      }
      /*
       * 示例数据 '{"Ctx":[{"key":"dd","value":"dds"},{"key":"d","value":"d"}],"Sys":[{"key":"d","value":"s"}]}'
       * 根据 '{'的个数判断key value长度。 因最终结果需要去除第一个'{'。所以count 初始值定为 -1
       */
      let reg = /\{/g
      let count = -1
      while (reg.exec(this.utterance.slotData) != null) {
        count += 1
      }
      return count
    },
    limitCount() {
      return this.$store.state.aiuiApp.limitCount
    },
  },
  watch: {
    defaultUtterance: function (val, oldVal) {
      this.utterance = JSON.parse(JSON.stringify(val))
    },
    'utterance.mark': function (val, oldVal) {
      let self = this
      let editNode = ''
      val.forEach((item, index) => {
        if (item.edit) {
          this.edited = index
          editNode = index
        }
      })
      this.contentDom.childNodes.forEach((node, index) => {
        if (node.dataset && parseInt(node.dataset.index) === editNode) {
          this.$nextTick(() => {
            self.editRectX = node.getBoundingClientRect().x
            self.editRectW = node.getBoundingClientRect().width
            self.resizing = false
          })
        }
      })
    },
  },
  created() {
    let self = this
    this.utterance = JSON.parse(JSON.stringify(this.defaultUtterance))
    this.initScore = this.utterance.score || 0
    if (this.utterance && this.utterance.mark) {
      this.utterance.mark.forEach(function (item, index) {
        if (item.slotName) {
          self.$store.dispatch('studioSkill/setUtteranceColor', item.slotName)
        }
      })
    }
  },
  mounted() {
    this.contentDom = this.$refs['content']
  },
  methods: {
    clearSelection(event) {
      if (document.selection) {
        document.selection.empty()
      } else if (window.getSelection()) {
        window.getSelection().removeAllRanges()
      }
    },
    handleBrowserSelect(event) {
      let self = this
      // 鼠标松开，松手
      this.droping = ''
      this.rangeText = ''
      if (!this.subAccountEditable) return //限制仅查看权限的子账号
      if (document.selection) {
        // ie浏览器
        this.range = document.selection.createRange()
        this.rangeText = this.range.text
        if (!this.rangeText) {
          this.range = ''
        }
      } else if (window.getSelection().anchorNode) {
        // 标准浏览器
        this.range = window.getSelection().getRangeAt(0)
        this.rangeText = window.getSelection().toString()
        // console.log('1===>', this.range);
        // console.log('2===>', this.rangeText);
        if (!this.rangeText) {
          this.range = ''
        }
      }
      if (!this.range) {
        return
      }
      this.rect = this.range.getClientRects()[0]
      if (!this.rangeText.trim()) {
        return
      }
      let selectedText = {
        text: this.rangeText,
        startContainer:
          this.range.startContainer && this.range.startContainer.data,
        startOffset: this.range.startOffset,
        rangeStartMark: this.range.startContainer.parentNode.dataset.markid,
        rangeEndMark: this.range.endContainer.parentNode.dataset.markid,
      }

      if (selectedText.rangeStartMark === selectedText.rangeEndMark) {
        // 相同的start和end Mark
        for (let i = 0; i < this.utterance.mark.length; i++) {
          if (
            this.utterance.mark[i].markId ===
              parseInt(selectedText.rangeStartMark) &&
            this.utterance.mark[i].slotName
          ) {
            selectedText.destory = true
          }
        }
      }

      if (
        !selectedText.rangeStartMark &&
        this.utterance.mark.length === 1 &&
        !this.utterance.mark[0].slotName
      ) {
        // 如果是没有markid的，原本语料就一个text
        selectedText.rangeStartMark = 1
        selectedText.rangeEndMark = 1
      }
      if (!selectedText.rangeEndMark && selectedText.rangeStartMark) {
        // 只有startMark没有EndMark，自动划到最后一个Mark
        selectedText.rangeEndMark =
          this.utterance.mark[this.utterance.mark.length - 1].markId
      }
      clearTimeout(this.clickTimer)
      this.clickTimer = setTimeout(function () {
        console.log('谁先')
        self.openSelectEntity({
          utterance: self.utterance,
          selectedText: selectedText,
        })
      }, 250)
    },
    openSelectEntity(data) {
      if (data.utterance.template !== 2) {
        return
      }
      this.$store.dispatch('studioSkill/setUtteranceEntityPopover', {
        show: true,
        data: data,
        rect: {
          top: this.rect.top,
          left: this.rect.left,
          width: this.rect.width,
          y: this.rect.y,
        },
      })
    },
    selectEntity() {
      if (document.selection) {
        this.range.execCommand('BackColor', true, '#1784e9')
      } else {
        var span = document.createElement('span')
        span.style.cssText = 'background-color:#1784e9;user-select:none;'
        try {
          this.range.surroundContents(span)
        } catch (ex) {
          alert(
            '选择出错，请保证不要跨段落选择文本,不要截断页面特殊格式的词语！'
          )
        }
      }
    },
    handleMarkup(mark, event) {
      let self = this
      if (!mark.slotName || !this.subAccountEditable) {
        return
      }
      this.rect = event.target.getBoundingClientRect()
      this.clickTimer = setTimeout(function () {
        self.openSelectEntity({
          utterance: self.utterance,
          selectedText: mark,
        })
      }, 250)
    },
    showMarkTag(mark, index, event) {
      if (!mark.slotName) {
        return
      }
      this.hoving = true
      let marks = Array.prototype.map.call(
        this.utterance.mark,
        function (item, itemIndex) {
          if (itemIndex === index && mark.slotName) {
            item.edit = true
          } else {
            item.edit = false
          }
          return item
        }
      )
      this.parentRectX = event.target.parentNode.getBoundingClientRect().x
      this.utterance.mark = marks
    },
    removeMarkTag(mark, index, event) {
      // console.log(event)
    },
    hoverMarkTag() {
      this.hoving = true
    },
    removeContent(event) {
      if (event.target.className !== 'utterance-area') {
        return
      }
      this.hoving = false
      // let marks = Array.prototype.map.call(this.utterance.mark, function (item, itemIndex) {
      //   item.edit = false
      //   return item
      // })
      // this.utterance.mark = marks
    },
    resizeMark(markTag, to) {
      let mark = this.utterance.mark
      let editedLeft = this.edited - 1 // 左边文本index
      let leftText = '' // 左边文本
      let leftSpliceText = '' // 左边割出的文本
      let editedRight = this.edited + 1 // 右边文本index
      let rightText = '' // 右边文本
      let rightSpliceText = '' // 右边割出的文本
      let editedText = '' // 当前文本
      let editedSpliceText = '' // 当前割出的文本
      switch (to) {
        // 光标向左
        case 'left':
          if (markTag === 'one' && this.edited > 0) {
            if (mark[editedLeft].slotType >= 0) {
              // 如果左边是有type的，不可左移
              return
            } else {
              leftText = mark[editedLeft].text
              if (!leftText) {
                return
              }
              leftSpliceText = leftText[leftText.length - 1]
              leftText = leftText.substring(0, leftText.length - 1)
            }
            mark[this.edited].text = leftSpliceText + mark[this.edited].text
            mark[editedLeft].text = leftText
          } else if (markTag === 'two') {
            if (mark[this.edited].text.length === 0) {
              // 已经没有可选择文本了，不可左移
              return
            } else {
              editedText = mark[this.edited].text
              editedSpliceText = editedText[editedText.length - 1]
              editedText = editedText.substring(0, editedText.length - 1)
            }
            mark[this.edited].text = editedText
            if (editedRight <= mark.length - 1 && !mark[editedRight].slotType) {
              mark[editedRight].text = editedSpliceText + mark[editedRight].text
            } else {
              mark.splice(editedRight, 0, {
                text: editedSpliceText,
              })
            }
          }
          break
        case 'right':
          if (markTag === 'one' && this.edited < mark.length - 1) {
            if (mark[this.edited].text.length === 0) {
              // 右边已经没有可选择文本了，不可右移
              return
            } else {
              editedText = mark[this.edited].text
              editedSpliceText = editedText[0]
              editedText = editedText.substring(1, editedText.length)
            }
            mark[this.edited].text = editedText

            if (editedLeft >= 0 && !(mark[editedLeft].slotType >= 0)) {
              mark[editedLeft].text = mark[editedLeft].text + editedSpliceText
            } else {
              mark.splice(this.edited, 0, {
                text: editedSpliceText,
              })
            }
          } else if (markTag === 'two' && this.edited < mark.length - 1) {
            if (mark[editedRight].slotType >= 0) {
              // 如果右边是有type的，不可右移
              return
            } else {
              rightText = mark[editedRight].text
              if (!rightText) {
                return
              }
              rightSpliceText = rightText[0]
              rightText = rightText.substring(1, rightText.length)
            }
            mark[this.edited].text = mark[this.edited].text + rightSpliceText
            mark[editedRight].text = rightText
          }
          break
        default:
          break
      }
      this.utterance.mark = []
      this.utterance.mark = mark
    },
    dropMark(markTag, event) {
      event.preventDefault()
      this.droping = markTag
    },
    moveMark(event) {
      if (this.droping && !this.resizing) {
        let dist = 0
        switch (this.droping) {
          case 'one':
            dist = event.clientX - this.editRectX
            break
          case 'two':
            dist = event.clientX - (this.editRectX + this.editRectW)
            break
          default:
            break
        }
        if (dist >= 8) {
          this.resizing = true
          this.resizeMark(this.droping, 'right')
        } else if (dist <= -8) {
          this.resizing = true
          this.resizeMark(this.droping, 'left')
        }
      }
    },
    // 删除语料
    del() {
      this.$emit('del', this.defaultUtterance, this.defaultIndex)
    },
    handleEnter(event) {
      event.target.blur()
    },
    handleKeyDown(event) {
      var keycode = window.event ? event.keyCode : event.which
      var evt = event || window.event
      if (keycode == 13 && !evt.ctrlKey) {
        // 发送消息的代码
        event.target.blur()
        event.preventDefault()
        return false
      }
      if (this.modifierSelectPopover.show && keycode === 40) {
        return this.$refs.modifierSelectPopover.handleDown()
      }
    },
    handlePaste(e) {
      e.preventDefault()
      var text
      var clp = (e.originalEvent || e).clipboardData
      if (clp === undefined || clp === null) {
        text = window.clipboardData.getData('text') || ''
        if (text !== '') {
          if (window.getSelection) {
            var newNode = document.createElement('span')
            newNode.innerHTML = text
            window.getSelection().getRangeAt(0).insertNode(newNode)
          } else {
            document.selection.createRange().pasteHTML(text)
          }
        }
      } else {
        text = clp.getData('text/plain') || ''
        if (text !== '') {
          document.execCommand('insertText', false, text)
        }
      }
    },
    /*
     * 20190509 业务定制 kv和slotName的规则校验
     */
    getCurrentUtterFzy(utteranceInput) {
      if (!this.utterance.hasOwnProperty('slotData')) return true
      let kv = JSON.parse(this.utterance.slotData)
      if (!kv.hasOwnProperty('Fzy')) return true
      let fzyValue = kv['Fzy'][0].value
      let reg = /\{(.*?)}/g
      let slotNameList = utteranceInput.match(reg)
      if (slotNameList && slotNameList.indexOf(`{${fzyValue}}`) === -1) {
        this.$message({
          type: 'warning',
          message: '请先删除语料中附加的Fzy类型KV',
        })
        return false
      }
      return true
    },
    // 失去焦点
    handleBlur() {
      let self = this
      let markNodes = this.$refs.content.childNodes
      let marks = []
      let utterance = ''
      let showUtterance = ''
      if (this.cursorPos > -1) return
      if (this.utterance.template === 1) {
        for (let i = 0; i < markNodes.length; i++) {
          if (markNodes[i].dataset) {
            utterance += self.$utils.trimSpace(markNodes[i].textContent).trim()
            showUtterance += markNodes[i].textContent
          } else if (self.$utils.trimSpace(markNodes[i].nodeValue).trim()) {
            utterance += self.$utils.trimSpace(markNodes[i].nodeValue).trim()
            showUtterance += markNodes[i].nodeValue
          }
        }
        if (this.$route.path.match('/extend-skill/')) {
          let needDelFzy = this.getCurrentUtterFzy(utterance)
          if (!needDelFzy) {
            return
          }
        }
      } else {
        if (/[<>]/.test(self.$refs.content.textContent)) {
          return self.$message.warning('例句语料中不能输入<>')
        }
        for (let i = 0; i < markNodes.length; i++) {
          if (markNodes[i].dataset) {
            if (markNodes[i].dataset.markid) {
              let defaultMark = self.getDefaultMark(markNodes[i].dataset.markid)
              if (defaultMark) {
                marks.push({
                  markId: markNodes[i].dataset.markid,
                  slotName: defaultMark.slotName,
                  slotType: defaultMark.slotType,
                  entityId: defaultMark.entityId,
                  entityName: defaultMark.entityName,
                  optional: defaultMark.optional,
                  text: self.$utils.trimSpace(markNodes[i].innerHTML),
                  sort: i,
                })
              } else {
                marks.push({
                  text: self.$utils.trimSpace(markNodes[i].innerHTML),
                  sort: i,
                })
              }
            } else {
              marks.push({
                text: self.$utils.trimSpace(markNodes[i].innerHTML),
                sort: i,
              })
            }
            utterance += self.$utils.trimSpace(markNodes[i].innerHTML)
            showUtterance += markNodes[i].innerHTML
          } else if (self.$utils.trimSpace(markNodes[i].nodeValue).trim()) {
            if (marks[marks.length - 1]) {
              if (marks[marks.length - 1].slotName) {
                marks.push({
                  text: self.$utils.trimSpace(markNodes[i].nodeValue).trim(),
                  sort: i,
                })
              } else {
                marks[marks.length - 1].text += self.$utils
                  .trimSpace(markNodes[i].nodeValue)
                  .trim()
              }
            } else {
              marks.push({
                text: self.$utils.trimSpace(markNodes[i].nodeValue).trim(),
                sort: i,
              })
            }
            utterance += self.$utils.trimSpace(markNodes[i].nodeValue).trim()
            showUtterance += markNodes[i].nodeValue
          }
        }
      }

      if (showUtterance.trim() === this.utterance.utterance) {
        return
      }

      if (!utterance.trim()) {
        return self.$message.warning('语料不能为空')
      }
      let nesting = this.limitCount && this.limitCount['utterance_nesting'] > 0
      let isExtendSkill = this.$route.path.match('extend-skill')
        ? this.$route.path.match('extend-skill')[0]
        : ''
      let nameValid = this.$rules.judgeUtteranceParams(
        utterance.trim(),
        200,
        '语料',
        15,
        nesting
      )
      if (!nameValid.valid) {
        return self.$message.warning(nameValid.data.message)
      }

      let data = null
      let api = ''
      if (this.utterance.template === 1) {
        data = {
          id: this.utterance.id,
          businessId: this.intention.businessId,
          intentId: this.intention.id,
          utterance: utterance.trim(),
          oldUtterance: this.utterance.utterance,
        }
        api = this.$config.api.STUDIO_INTENT_ADD_EDIT_UTTERANCE

        if (this.$store.state.studioSkill.skill.type == 3) {
          data.type = 2
          data.isEnt = this.utterance.isEnt
        }
      } else {
        data = {
          template: this.utterance.template,
          id: this.utterance.id,
          utterance: utterance.trim(),
          businessId: this.intention.businessId,
          intentId: this.intention.id,
          mark: JSON.stringify(marks),
        }
        api = this.$config.api.STUDIO_INTENT_UTTERANCE_MARKS_UPDATE
      }
      this.$utils.httpPost(api, data, {
        success: (res) => {
          self.$emit('change')
        },
        error: (err) => {
          console.log('page=>>')
          console.log(err)
        },
      })
    },
    getDefaultMark(markId) {
      for (let i = 0; i < this.utterance.mark.length; i++) {
        if (this.utterance.mark[i].markId === parseInt(markId)) {
          return this.utterance.mark[i]
        }
      }
      return null
    },
    openKvDialog() {
      this.addKvDialog.show = true
    },
    spanClick() {},
    editScore(utterance) {
      this.utterance.score = this.initScore
      this.$emit('editScore', utterance)
      this.scoreWrapVisible = false
    },
    cancelScore() {
      this.initScore = this.utterance.score || 0
      this.scoreWrapVisible = false
    },
    saveKV(val) {
      this.utterance.slotData = val
      this.$emit('editScore', this.utterance)
    },

    // 自定义修饰语
    angleBracketHandle(e) {
      if (this.utterance.template === 2) return
      let self = this
      let leftAngleBracket = 188,
        rightAngleBracket = 190,
        angleCode = [188, 190]
      if (e.keyCode === 38 || e.keyCode === 40) return //键盘上下方向键
      if (!angleCode.includes(e.keyCode)) return
      if (e.keyCode === rightAngleBracket && self.cursorPos !== -1) {
        return (self.modifierSelectPopover.show = false)
      }
      // this.orderUtterMark()
      const cursor = self.$utils.getCursortPosition(self.$refs['content'])
      // console.log('cursor angleBracketHandle->  ',cursor )
      // this.getModifierPos(cursor-1)
      const value = self.$refs['content'].textContent
      // console.log('value->  ',value )
      if (value[cursor - 1] === '<') {
        // 用 if (value.trim()[cursor - 1] === '<') 时，可以得到视觉长度
        // console.log(`value[${cursor - 1}]->  `,value[cursor - 1] )
        // console.log('value.indexOf(<)   ->  ',value.trim().indexOf('<') )
        self.cursorPos = cursor
      } else {
        return
      }
      const rect = window.getSelection().getRangeAt(0).getBoundingClientRect()
      if (self.cursorPos > cursor) {
        self.modifierSelectPopover.show = false
      }
      setTimeout(function () {
        self.modifierSelectPopover = {
          show: true,
          rect: rect,
          searchVal: value.substring(self.cursorPos, cursor),
        }
      }, 0)
    },
    setModifier(item, e) {
      let self = this
      let utter = self.$refs['content'].textContent
      if (!item) return (this.cursorPos = -1)
      let searchValLen = this.modifierSelectPopover.searchVal
        ? this.modifierSelectPopover.searchVal.length
        : 0
      // this.markOnModifing.text = 'MMMOO'
      utter =
        utter.substring(0, this.cursorPos) +
        item +
        '>' +
        utter.substring(this.cursorPos + searchValLen)
      let currentCursorPos = this.cursorPos + item.length + 1
      // console.log('utter   ',utter)
      self.$refs['content'].textContent = utter
      // self.$utils.setCursortPosition(currentCursorPos, self.$refs['content'])
      this.cursorPos = -1
      this.handleBlur()
    },
    orderUtterMark() {
      // mark 子元素字符站位计算
      let len = this.utterance.mark.length
      let start = 1
      for (let i = 0; i < len; i++) {
        this.$set(this.utterance.mark[i], 'startIndex', start)
        this.$set(
          this.utterance.mark[i],
          'endIndex',
          start + this.utterance.mark[i].end - this.utterance.mark[i].start - 1
        )
        start = this.utterance.mark[i].endIndex + 1
      }
    },
    getModifierPos(cursor) {
      // < 插入的位置
      let len = this.utterance.mark.length
      for (let i = 0; i < len; i++) {
        let start = this.utterance.mark[i].startIndex
        let end = this.utterance.mark[i].endIndex
        if (start <= cursor && cursor <= end) {
          this.markOnModifing = this.utterance.mark[i]
          this.markOnModifingIndex = i
          break
        }
      }
      console.log('cursor ->  ', cursor)
      console.log('markOnModifing ->  ', this.markOnModifing)
      console.log('markOnModifingIndex ->  ', this.markOnModifingIndex)
    },
  },

  components: {
    AddKvDialog,
  },
}
</script>

<style lang="scss">
#content {
  max-width: 98%;
  outline: none;
  position: relative;
  display: inline-block;
  font-size: 14px;
  span {
    font-size: 14px;
  }
}

.utterance-area {
  padding: 11px 27px 11px 11px;
  height: auto;
  min-height: 44px;
  background-color: #fff;
  z-index: 1;
  display: flex;
  align-items: center;
  &-template {
    color: $grey3;
    margin-right: 20px;
  }
  &-del {
    line-height: 22px;
    display: none;
    cursor: pointer;
    color: $grey4;
    position: absolute;
    right: 8px;
  }
  &:hover {
    .mark1,
    .mark2,
    .utterance-area-del {
      display: block;
    }
  }
}
.utterance-area-disabled {
  background-color: #f2f5f7;
}

.selection {
  cursor: pointer;
  // display: inline-block;
}

.lump {
  flex: auto;
  overflow: hidden;
  padding-right: 10px;
  line-height: 28px;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.dis-user-select {
  user-select: none;
}
.filler-mark {
  border-bottom: 1px dashed $grey5;
}

.mark1,
.mark2 {
  position: absolute;
  width: 2px;
  height: 100%;
  background-color: #1784e9;
  display: none;
}

.mark1 {
  left: 0px;
}
.mark2 {
  right: 0px;
}

.mark1::before {
  position: absolute;
  width: 6px;
  height: 6px;
  border-radius: 6px;
  content: ' ';
  background-color: #1784e9;
  top: -6px;
  left: -2px;
  cursor: pointer;
}

.mark2::after {
  position: absolute;
  width: 6px;
  height: 6px;
  border-radius: 6px;
  content: ' ';
  background-color: #1784e9;
  bottom: -6px;
  right: -2px;
  cursor: pointer;
}

//业务定制
.utterance-area-kv-wrap {
  position: absolute;
  right: 28px;
  display: flex;
  padding: 0 12px;
  width: 100px;
  height: 16px;
  line-height: 16px;
  border-left: 1px solid #e4e7ed;
  & span {
    flex: 1;
    color: #1784e9;
    cursor: pointer;
    text-align: right;
    &:first-child {
      text-align: left;
    }
  }
}
.extend-utterance {
  width: calc(100% - 150px);
}
</style>
