<template>
  <div>
    <div class="aiui-store-skills">
      <brief-header :skill="skill" :id="id" :type="type"></brief-header>
      <div class="tab-container tab-container-sticky">
        <el-tabs v-model="tabType">
          <el-tab-pane label="技能概述" name="0" key="0"></el-tab-pane>
          <el-tab-pane
            label="信源详情"
            name="1"
            key="1"
            v-if="skill.hasSource"
          ></el-tab-pane>
          <el-tab-pane
            label="参数详情"
            name="2"
            key="2"
            v-if="skillParamsShow"
          ></el-tab-pane>
        </el-tabs>
      </div>

      <div class="store-skill-currency container" v-if="tabType == '0'">
        <div class="store-skill-content">
          <p class="store-skill-label mgb22">技能简介</p>
          <p class="store-skill-desc" v-html="skill.detail.description"></p>
        </div>
      </div>
      <div v-if="tabType == '0'" class="tab-container">
        <el-tabs v-model="activeName">
          <el-tab-pane
            label="普通话"
            name="mandarin"
            v-if="
              skill.dialect &&
              skill.dialect.dialectinfo &&
              skill.dialect.dialectinfo.includes('普')
            "
          >
          </el-tab-pane>
          <el-tab-pane
            :label="item.zhName"
            :name="item.name"
            v-for="(item, index) in (skill.dialect && skill.dialect.examples) ||
            []"
            :key="index"
          >
          </el-tab-pane>
        </el-tabs>
      </div>
      <dialog-info
        :skill="skill"
        v-if="activeName === 'mandarin' && tabType == '0'"
      ></dialog-info>
      <dialog-info
        v-else-if="dialectSkill[activeName] && tabType == '0'"
        :skill="dialectSkill[activeName]"
      ></dialog-info>
      <param-detail :skill="skill" v-if="tabType == '2'"></param-detail>
      <source-detail
        :providers="providers"
        :sourceAgreement="sourceAgreement"
        v-if="tabType == '1'"
      ></source-detail>
    </div>
  </div>
</template>

<script>
import DialogInfo from './skillDetail/introductionDetail.vue'
import BriefHeader from './skillDetail/briefHeader.vue'
import SourceDetail from './skillDetail/sourceDetail.vue'
import ParamDetail from './skillDetail/paramDetail.vue'

export default {
  name: 'store-skill',
  data() {
    return {
      id: '',
      type: '',
      skill: {
        detail: {},
        faq: [],
        phrases: {},
        protocols: {},
        examples: {},
      },
      providers: [],
      sourceAgreement: [],
      // cantoneseSkill: {},
      // lmzSkill: {},
      dialectSkill: {},

      intents: [],
      semantic: [],
      dialog: {
        show: false,
        data: {},
      },
      activeName: 'mandarin',
      tabType: '0',
    }
  },
  computed: {
    skillParamsShow() {
      return this.skill.protocols.answer || this.skill.protocols.semantic
    },
  },
  created() {
    this.id = this.$route.params.skillId
    this.type = this.$route.query.type
    this.getSkillDetail()
  },
  methods: {
    getFieldValue(data, field, dialect) {
      if (field !== 'single' && field !== 'many') {
        throw new Error('field 必须为single 或 many中的一种')
      }
      const general = Object.assign({}, data)
      return general.dialect.examples.find((item) => item.name === dialect)[
        field
      ]
    },
    getSkillDetail() {
      let self = this
      this.$utils.httpGet(
        this.$config.api.AIUI_STORE_DIALECT_SKILL_DETAIL,
        {
          id: this.id,
        },
        {
          success: (res) => {
            if (res.data.hasSource) {
              self.getProviders()
              self.getSourceAgreement()
            }
            let tempData = res.data || {}
            tempData.detail = {
              url: tempData.picUrl,
              zhName: tempData.name,
              name: tempData.skillIdentify,
              count: tempData.count,
              description: tempData.allIntroduction,
              mc: tempData.mc,
            }
            tempData.phrases = {
              single: tempData.examplePhrase,
              many: tempData.multiExamplePhrase,
            }
            self.skill = tempData
            // 为组件复用，适配数据，方便粤|川 时候的对话示例展示

            const general = Object.assign({}, res.data)

            general.dialect.examples.forEach((item) => {
              let obj = {
                ...general,
                examplePhrase: this.getFieldValue(general, 'single', item.name),
                multiExamplePhrase: this.getFieldValue(
                  general,
                  'many',
                  item.name
                ),
                phrases: {
                  single: this.getFieldValue(general, 'single', item.name),
                  many: this.getFieldValue(general, 'many', item.name),
                },
                examples: { singles: item.singles },
              }
              this.$set(this.dialectSkill, item.name, obj)
            })

            self.intents = self.objToArray(res.data.fieldDesc.intents)
            self.semantic = self.objToArray(res.data.fieldDesc.slots)
            self.$store.dispatch('aiuiStore/setSkillDetail', {
              zhName: res.data.name,
              name: res.data.skillIdentify,
              sysNumber: res.data.sysNumber,
              currentSkillId: res.data.currentSkillId,
            })
          },
        }
      )
    },
    getProviders() {
      let self = this
      this.$utils.httpGet(
        this.$config.api.AIUI_STORE_SKILL_PROVIDERS,
        {
          business: this.id,
        },
        {
          success: (res) => {
            self.providers = res.data
          },
          error: (err) => {},
        }
      )
    },
    getSourceAgreement() {
      let self = this
      this.$utils.httpGet(
        this.$config.api.AIUI_STORE_SKILL_CONTENTDATA,
        {
          method: 'getFunctions',
          business: this.id,
        },
        {
          success: (res) => {
            self.sourceAgreement = res.data
          },
          error: (err) => {},
        }
      )
    },
    answerSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        if (rowIndex % this.skill.protocols.answer.length === 0) {
          return {
            rowspan: this.skill.protocols.answer.length,
            colspan: 1,
          }
        } else {
          return {
            rowspan: 0,
            colspan: 0,
          }
        }
      }
    },
    semanticSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        if (rowIndex % this.skill.protocols.semantic.length === 0) {
          return {
            rowspan: this.skill.protocols.semantic.length,
            colspan: 1,
          }
        } else {
          return {
            rowspan: 0,
            colspan: 0,
          }
        }
      }
    },

    objToArray(obj) {
      let arr = []
      for (let key in obj) {
        arr.push({
          key: key,
          value: obj[key],
        })
      }
      return arr
    },
  },
  components: {
    DialogInfo,
    BriefHeader,
    SourceDetail,
    ParamDetail,
  },
}
</script>
<style lang="scss" scoped>
p {
  margin-bottom: 0;
}
.container {
  padding-top: 30px;
}
.store-skill-detail-tag {
  .icon-ico_fangyan {
    font-size: 20px !important;
    margin-right: 0 !important;
    color: #1b9adc;
  }
}

.aiui-store-skills {
  .tab-container-sticky {
    position: sticky;
    z-index: 10;
  }
}
.tab-container {
  background: #fff;
  top: -1px;
  z-index: 1;
  height: 78px;
  // padding: 0 35px;
  border-bottom: 1px solid #eff1f1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  :deep(.el-tabs__header) {
    margin: 0;
  }
  :deep(.el-tabs__item) {
    padding: 0 50px;
    font-size: 18px;
    height: 78px;
    line-height: 78px;
  }
  :deep(.el-tabs__nav-wrap::after) {
    display: none;
  }
}

// 技能商店适配不同屏幕
@media screen and (max-width: 1601px) {
  .container {
    padding-top: 20px;
  }
  .tab-container {
    height: 48px;

    :deep(.el-tabs__item) {
      padding: 0 50px;
      font-size: 14px;
      height: 48px;
      line-height: 48px;
    }
  }
}
</style>
