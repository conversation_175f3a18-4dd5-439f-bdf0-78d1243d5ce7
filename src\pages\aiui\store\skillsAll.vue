<template>
  <div class="content-container">
    <div class="top-area" :style="{ visibility: fixed ? 'hidden' : 'visible' }">
      <el-tabs v-model="businessType" @tab-click="handleCategoryClick">
        <el-tab-pane
          v-for="(item, index) in categoryTypes"
          :label="item.label"
          :name="item.value"
          :key="index"
        ></el-tab-pane>
      </el-tabs>
      <div class="store-skill-search" @keyup.enter="searchSkill">
        <el-input
          class="search-area"
          size="medium"
          placeholder="输入技能名称搜索"
          v-model.trim="searchVal"
        >
          <i slot="suffix" class="iconfont icon-sousuo" @click="searchSkill" />
        </el-input>
      </div>
    </div>
    <div class="top-area top-area-fixed" v-if="fixed">
      <el-tabs v-model="businessType" @tab-click="handleCategoryClick">
        <el-tab-pane
          v-for="(item, index) in categoryTypes"
          :label="item.label"
          :name="item.value"
          :key="index"
        ></el-tab-pane>
      </el-tabs>
      <div class="store-skill-search" @keyup.enter="searchSkill">
        <el-input
          class="search-area"
          size="medium"
          placeholder="输入技能名称搜索"
          v-model.trim="searchVal"
        >
          <i slot="suffix" class="iconfont icon-sousuo" @click="searchSkill" />
        </el-input>
      </div>
    </div>
    <div class="os-scroll-bottom" v-loading.body="skillsData.loading">
      <template v-if="skillsData.total > 0">
        <div class="store-skills-row">
          <div
            class="store-skills-col ib"
            v-for="(skill, index) in skillsData.list"
            :key="index"
          >
            <div class="os-store-skill" @click="toSkillDetail(skill)">
              <div class="os-store-skill-thumb">
                <img v-if="skill.url" :src="skill.url" />
                <span v-else>{{
                  skill.zhName && skill.zhName.substr(0, 1)
                }}</span>
              </div>
              <div class="os-store-skill-content">
                <p class="os-store-skill-name" :title="skill.zhName">
                  {{ skill.zhName }}
                </p>
                <p class="os-store-skill-provider">
                  {{
                    skill.provider !== 'undefined' ? skill.provider || '-' : '-'
                  }}
                </p>
                <p class="os-store-skill-desc" :title="skill.briefIntroduction">
                  {{ skill.briefIntroduction }}
                </p>
              </div>
              <div class="os-store-skill-tag-area">
                <span class="os-store-skill-tag blue">
                  {{
                    skill.provider.includes('科大讯飞')
                      ? '官方技能'
                      : '第三方技能'
                  }}
                </span>
              </div>
              <div class="os-store-skill-tag-area tag-area-bottom">
                <span v-if="skill.count" class="os-store-skill-tag">
                  {{ skill.count }}个信源
                </span>
                <!-- <span v-if="skill.dialectinfo" class="os-store-skill-dialect-tag">
                {{ skill.dialectinfo }}
              </span> -->
              </div>
            </div>
          </div>
        </div>
        <div class="store-skill-page">
          <el-pagination
            v-if="showPagination"
            ref="pagination"
            class="txt-al-c"
            @current-change="getSkills"
            :current-page="skillsData.page"
            :page-size="skillsData.size"
            :total="skillsData.total"
            :layout="pageLayout"
          >
          </el-pagination>
        </div>
      </template>

      <!-- <div class="empty-skill-tip" v-if="skillsData.total === 0 && !skillsData.loading">
        <p>没有相应的技能</p>
        <p>
          这里的技能还是不能满足您的需求？您还可以
          <router-link :to="{ path: '/studio/skill' }"
            >自定义您的技能</router-link
          >
        </p>
      </div> -->
      <skills-empty v-if="skillsData.total === 0 && !skillsData.loading" />
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import skillsEmpty from './skillsEmpty.vue'

export default {
  name: 'store-skill-all',
  props: ['search', 'fixed', 'scrollTop', 'scrollTo'],
  data() {
    return {
      sortType: 0,
      businessType: '',
      icon: {
        efficiencyTool: 10100,
        lifeService: 10200,
        vedioRead: 10300,
        education: 10400,
        healthy: 10500,
        entertainment: 10600,
        childrenEducation: 10700,
        finance: 10800,
        smartHome: 10900,
        qa: 20100,
      },
      provider: '',
      skillsData: {
        loading: true,
        total: -1,
        page: 1,
        size: 6,
        list: [],
      },
      needSearch: false,
      categoryTypes: [],
      showCategorySearch: true,
      searchVal: '',
    }
  },
  watch: {
    $route: function (val, oldVal) {},
  },
  computed: {
    pageLayout() {
      if (this.skillsData.total / this.skillsData.size > 7) {
        return 'prev, pager, next, jumper'
      }
      return 'prev, pager, next'
    },
    showPagination() {
      return this.skillsData.total > this.skillsData.size
    },
  },
  created() {
    // 根据屏幕尺寸获取分页参数
    this.getAdaptPageSize()
    this.handleSkillType(this.$route.params.skillType)
    // 获取各种二级分类的类型
    this.getSecondCategoryTypes()
  },
  methods: {
    getAdaptPageSize() {
      // if (window.innerHeight < 600) {
      //   this.skillsData.size = 6
      // } else {
      //   this.skillsData.size = 9
      // }
      this.skillsData.size = 18
    },
    searchSkill() {
      this.sortType = 0
      this.businessType = '0'
      this.provider = ''
      this.needSearch = true
      this.showCategorySearch = true
      this.getSkills(1)
    },
    handleSkillType(type) {
      console.log(type)
      this.sortType = 0
      this.provider = ''
      this.needSearch = true
      this.showCategorySearch = true
      this.businessType = '0'
      const infoObj = JSON.parse(
        sessionStorage.getItem('GLOBAL_SKILL_STORE') || '{}'
      )
      if (infoObj.businessType) {
        this.businessType = infoObj.businessType
      }
      if (infoObj.searchVal) {
        this.searchVal = infoObj.searchVal
      }
      if (infoObj.page) {
        this.skillsData.page = infoObj.page
        sessionStorage.removeItem('GLOBAL_SKILL_STORE')
      }

      this.getSkills()
    },
    getSkills(page) {
      this.skillsData.loading = true
      this.$utils.httpGet(
        this.$config.api.AIUI_STORE_SKILLS,
        {
          sortType: this.sortType,
          businessType: this.businessType === '0' ? '' : this.businessType,
          search: this.needSearch ? this.searchVal : '',
          provider: this.provider,
          pageIndex: page || this.skillsData.page,
          pageSize: this.skillsData.size,
        },
        {
          success: (res) => {
            this.skillsData.list = res.data.skills
            this.skillsData.total = res.data.count
            this.skillsData.page = res.data.pageIndex
            // this.skillsData.size = res.data.pageSize
            this.skillsData.loading = false
            this.$nextTick(() => {
              this.restoreScrollTop()
            })
          },
          error: (err) => {
            console.log('page=>>')
            console.log(err)
          },
        }
      )
    },
    restoreScrollTop() {
      let top = sessionStorage.getItem('GLOBAL_SKILL_STORE_SCROLLTOP') || 0
      if (top) {
        this.scrollTo(Number(top))
        sessionStorage.removeItem('GLOBAL_SKILL_STORE_SCROLLTOP')
      }
    },
    toSkillDetail(skill) {
      // 保存当前页码等信息，便于返回时重新获取
      const infoObj = {
        businessType: this.businessType,
        page: this.skillsData.page,
        searchVal: this.searchVal,
      }
      sessionStorage.setItem('GLOBAL_SKILL_STORE', JSON.stringify(infoObj))
      sessionStorage.setItem('GLOBAL_SKILL_STORE_SCROLLTOP', this.scrollTop)
      this.$router.push({
        name: 'store-skill',
        params: { skillId: skill.id },
        query: { type: skill.type },
      })
    },

    getSecondCategoryTypes() {
      let self = this
      this.$utils.httpGet(
        this.$config.api.AIUI_STORE_SKILL_TYPES,
        {},
        {
          success: (res) => {
            let categoryTypes = Array.prototype.map.call(
              Object.keys(res.data),
              (item, index) => {
                return {
                  value: item,
                  label: res.data[item],
                }
              }
            )
            categoryTypes = [{ label: '全部', value: '0' }, ...categoryTypes]
            this.categoryTypes = categoryTypes
          },
          error: (err) => {},
        }
      )
    },
    handleCategoryClick() {
      this.getSkills(1)
    },
  },
  components: {
    skillsEmpty,
  },
}
</script>

<style lang="scss" scoped>
@import '@A/scss/skills.scss';
.icon-sousuo {
  color: $primary;
  font-size: 18px;
}

.store-skill-search {
  width: 380px;
  :deep(.el-input__inner) {
    border: none;
    box-shadow: 0px 5px 20px 0px rgba(165, 165, 165, 0.3);
    height: 46px;
  }
  :deep(.el-input__icon) {
    color: $primary;
  }
  :deep(.el-input__suffix) {
    position: absolute;
    height: 100%;
    right: 10px;
    top: 0;
    text-align: center;
    color: #d5d8de;
    -webkit-transition: all 0.3s;
    transition: all 0.3s;
    pointer-events: none;
    top: 50%;
    transform: translateY(-50%);
    height: auto;
    cursor: pointer;
  }

  :deep(input::-webkit-input-placeholder) {
    font-size: 18px;
  }
  :deep(input::-moz-placeholder) {
    /* Mozilla Firefox 19+ */
    font-size: 18px;
  }
  :deep(input:-ms-input-placeholder) {
    /* Internet Explorer 10-11 */
    font-size: 18px;
  }
}

// 技能商店适配不同屏幕
@media screen and (min-height: 602px) and (max-height: 801px) {
  .icon-sousuo {
    font-size: 14px;
  }

  .store-skill-search {
    width: 254px;
    :deep(.el-input__inner) {
      height: 32px;
    }

    :deep(input::-webkit-input-placeholder) {
      font-size: 12px;
    }
    :deep(input::-moz-placeholder) {
      /* Mozilla Firefox 19+ */
      font-size: 12px;
    }
    :deep(input:-ms-input-placeholder) {
      /* Internet Explorer 10-11 */
      font-size: 12px;
    }
  }
}

@media screen and (max-height: 601px) {
  .icon-sousuo {
    font-size: 14px;
  }

  .store-skill-search {
    width: 254px;
    :deep(.el-input__inner) {
      height: 32px;
    }

    :deep(input::-webkit-input-placeholder) {
      font-size: 12px;
    }
    :deep(input::-moz-placeholder) {
      /* Mozilla Firefox 19+ */
      font-size: 12px;
    }
    :deep(input:-ms-input-placeholder) {
      /* Internet Explorer 10-11 */
      font-size: 12px;
    }
  }
}
</style>
