<template>
  <el-upload
    class="qabank__upload-file"
    :action="`${baseUrl}/aiui/web/qa/doExcelInsert?repoId=${qaId}&isCover=${options.isCover}`"
    :show-file-list="false"
    :before-upload="beforeUpload"
    :on-success="success"
    :on-error="uploadError"
  >
    <el-button size="small"> {{ options.text }}</el-button>
  </el-upload>
</template>
<script>
export default {
  props: {
    qaId: '',
    options: {
      isCover: {
        type: Boolean,
      },
      text: '',
    },
    limitCount: Object,
  },
  data() {
    return {
      baseUrl: this.$config.server,
    }
  },
  methods: {
    beforeUpload(file) {
      let reg = /\.xls(x)?$/i
      let type = reg.test(file.name)
      let unExceed = file.size < 1024 * 1024 * this.limitCount['qa_file_size']
      if (!type) {
        this.$message.error('仅支持xls或xlsx文件')
      }
      if (!unExceed) {
        this.$message.error(`文件不能超过${this.limitCount['qa_file_size']}M`)
      }
      this.$emit('setLoad', type && unExceed)
      return type && unExceed
    },
    success(data) {
      if (data.flag) {
        this.$emit('setLoad', false)
        this.$emit('getQaPair')
        this.$message.success('上传成功')
      } else {
        this.$emit('setLoad', false)
        this.$message.error(data.desc || '上传失败')
      }
    },
    uploadError() {
      this.$emit('setLoad', false)
    },
  },
}
</script>
<style lang="scss">
.qabank__upload-file {
  .el-button {
    padding: 0 !important;
    height: unset;
    min-width: unset;
    text-align: left;
    color: $grey6;
    border: none;
    background: transparent;
    &:hover {
      background-color: #e8f3fd;
      color: #459ded;
    }
  }
}
</style>
