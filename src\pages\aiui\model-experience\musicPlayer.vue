<template>
  <div class="music-player">
    <div class="music-content">
      <div class="music-info">
        <div class="music-animation">
          <span class="music-name">{{ current.name }}</span
          ><span
            class="music-author"
            v-if="current.author && current.author.length > 0"
            >&nbsp;—&nbsp;{{ current.author.join('、') }}</span
          >
        </div>

        <!-- <span>{{ currentTime }}/{{ duration }}</span> -->
      </div>
      <div class="music-btns">
        <div class="play-sprite icon-pre" @click="playPre"></div>
        <div
          v-if="!isPlaying"
          @click="playMusic(true)"
          class="play-sprite icon-play"
        ></div>
        <div v-else @click="pauseMusic" class="play-sprite icon-pause"></div>
        <div class="play-sprite icon-next" @click="playNext"></div>
      </div>
    </div>
    <div class="icon-close" @click="onClose"></div>

    <audio
      style="visibility: hidden"
      :src="current.url"
      v-if="current.url"
      ref="audio"
      @play="onPlayStart"
      @ended="onPlayEnded"
      @pause="onPlayPause"
      @loadeddata="onLoadeddata"
      @timeupdate="onTimeupdate"
    ></audio>
  </div>
</template>
<script>
export default {
  props: {
    resources: {
      type: Array,
      default: [],
    },
  },
  data() {
    return {
      isPlaying: false,
      currentIndex: 0,
      current: {},
      duration: 0,
      currentTime: 0,
    }
  },
  created() {
    this.playMusic()
  },
  watch: {
    resources(val, oldVal) {
      if (val.length > 0 && oldVal.length > 0) {
        this.resetPlayer()
      }
    },
  },
  methods: {
    onClose() {
      this.$emit('close')
    },
    resetPlayer() {
      this.currentIndex = 0
      this.isPlaying = false
      this.playMusic()
    },
    playMusic(isContinue) {
      if (isContinue) {
        this.$refs.audio && this.$refs.audio.play()
        return
      }
      // 获取current
      this.current = {
        ...this.resources[this.currentIndex],
        url: this.resources[this.currentIndex].url
          ? this.resources[this.currentIndex].url
          : '',
      }
      this.$refs.audio && this.$refs.audio.pause()
      if (this.current.url) {
        this.$nextTick(() => {
          this.$refs.audio && this.$refs.audio.play()
        })
      } else if (this.current.itemid) {
        // 请求接口获取url
        this.$utils.httpGet(
          this.$config.api.CHAT_MUSIC,
          {
            itemid: this.current.itemid,
          },
          {
            success: (res) => {
              console.log('获取哦音乐链接', res)
              if (res.code == '0') {
                if (res.data && res.data[0]) {
                  this.$set(this.current, 'url', res.data[0].audiopath)
                  this.$nextTick(() => {
                    this.$refs.audio && this.$refs.audio.play()
                  })
                }
              }
            },
            error: (err) => {},
          }
        )
      }
    },
    pauseMusic() {
      this.$refs.audio && this.$refs.audio.pause()
    },

    onPlayStart() {
      this.isPlaying = true
    },
    onPlayEnded() {
      this.isPlaying = false
    },
    onPlayPause() {
      this.isPlaying = false
    },
    onLoadeddata() {
      // console.log('duration', this.$refs.audio.duration)
      this.duration = parseInt(this.$refs.audio.duration)
    },
    onTimeupdate(val) {
      if (this.$refs.audio) {
        // console.log('currentTime', this.$refs.audio.currentTime)
        // this.currentTime = parseInt(this.$refs.audio.currentTime)
      }
    },
    playNext() {
      if (this.currentIndex < this.resources.length - 1) {
        this.currentIndex++
        this.playMusic()
      } else {
        this.$message.warning('已经是最后一首了')
      }
    },
    playPre() {
      if (this.currentIndex > 0) {
        this.currentIndex--
        this.playMusic()
      } else {
        this.$message.warning('已经是第一首')
      }
    },
  },
}
</script>
<style lang="scss" scoped>
.music-player {
  background: url(~@A/images/model-exeperience/player-bg.png) center/contain
    no-repeat;
  width: 348px;
  height: 52px;
  position: relative;
  // position: absolute;
  // z-index: 1;
  // right: 30px;
  // top: -120px;
  padding-left: 54px;
  margin-left: -34px;
  .music-content {
    display: flex;
    align-items: center;
    position: absolute;
    z-index: 1;
    right: 48px;
    top: 14px;
  }

  .music-close {
    position: absolute;
    z-index: 1;
    top: 5px;
    right: 5px;
    font-size: 30px;
    cursor: pointer;
  }
  .music-btns {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .music-info {
    text-align: center;
    font-size: 14px;
    width: 132px;
    white-space: nowrap;
    padding-right: 10px;
    text-overflow: ellipsis;
    overflow: hidden;
  }
}

.play-sprite {
}
.play-sprite + .play-sprite {
  // margin-left: 9px;
}
.icon-close {
  height: 16px;
  width: 16px;
  background: url(~@A/images/model-exeperience/<EMAIL>) center/contain
    no-repeat;
  position: absolute;
  right: 0;
  top: 16px;
  cursor: pointer;
}
.icon-pre {
  height: 12px;
  width: 14px;
  background: url(~@A/images/model-exeperience/<EMAIL>) center/contain
    no-repeat;
  margin-right: 20px;
  cursor: pointer;
}
.icon-next {
  height: 12px;
  width: 14px;
  background: url(~@A/images/model-exeperience/<EMAIL>) center/contain
    no-repeat;
  margin-left: 20px;
  cursor: pointer;
}
.icon-pause {
  height: 20px;
  width: 20px;
  background: url(~@A/images/model-exeperience/<EMAIL>)
    center/contain no-repeat;
  cursor: pointer;
}
.icon-play {
  height: 20px;
  width: 20px;
  background: url(~@A/images/model-exeperience/<EMAIL>) center/contain
    no-repeat;
  cursor: pointer;
}

.music-animation {
  animation: scrolling 5s linear infinite;
  width: fit-content;
  display: flex;
  align-items: center;
}
.music-name {
  display: inline-block;
}
.music-author {
  display: inline-block;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
@keyframes scrolling {
  0% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(-100%);
  }
}
</style>
