<template>
  <div :class="`qs-box scroll-box `">
    <ul style="margin-top: 10px" class="qs-list">
      <li
        v-for="(qs, index) in qsListRes"
        :key="index"
        :title="qs.query"
        class="qs-item qs-hover"
      >
        <span class="qs-index">{{ index + 1 }}</span>
        <span class="qs-name ellipsis">{{ qs.query }}</span>
        <i class="el-icon-close qs-del" @click="qsDel(index)"></i>
      </li>
      <li class="qs-item">
        <span class="qs-index ellipsis">{{ qsListRes.length + 1 }}</span>
        <el-input
          v-model="rowData.input"
          placeholder="输入问题，回车键添加"
          style="width: 85%"
          @input="validateInput"
          @keyup.enter.native="addQs()"
        ></el-input>
      </li>
    </ul>
    <!-- <i class="el-icon-circle-check custom-btn fr" v-show="!editDisabled"></i> -->
  </div>
</template>

<script>
export default {
  name: 'knowledge-qs-list',
  props: {
    editDisabled: {
      type: Boolean,
      default: true,
    },
    qsList: {
      type: Array,
      default: [],
    },
    rowData: {
      type: Object,
    },
  },
  data() {
    return {
      qsListRes: [],
      // qsInput: "",
      saveDisabled: false,
    }
  },
  mounted() {
    this.qsListRes = this.$deepClone(this.qsList)
  },
  computed: {},
  watch: {
    qsList: function (val) {
      this.qsListRes = this.$deepClone(this.qsList)
    },
  },
  methods: {
    handleClose(val) {
      this.rowData.input = ''
      this.qsList = []
      this.$emit('changeVisible', false)
    },
    handleQsSave() {
      if (this.rowData.input.length > 0) {
        this.qsList.push(this.rowData.input)
        this.rowData.input = ''
      }
      let data = {
        pointIds: this.pointIds,
        queryList: this.qsList,
      }
      let self = this
      this.$utils.httpPost(this.$config.api.KNOWLEDGE_STORE_QS_ADD, data, {
        success: (res) => {
          if (res) {
            this.$message.success('问题批量添加成功')
            this.$emit('changeVisible', false)
          }
        },
        error: (err) => {},
      })
    },
    validateInput() {
      if (this.rowData.input.length > 0) {
        if (!this.validateQs(this.rowData.input)) {
          this.$message.warning(
            '仅支持汉字/字母/数字/空格/{}/_/?/°，且每条不超过60字'
          )
          // this.saveDisabled = true;
        } else {
          // this.saveDisabled = false;
        }
      }
    },
    validateQs(val) {
      const qsPattern = /^[\u4e00-\u9fffa-zA-Z0-9 {}_?？°]{1,60}$/
      return qsPattern.test(val)
    },
    addQs() {
      let qsAddItem = {
        query: this.rowData.input,
        pointIds: this.rowData.id,
        docId: this.rowData.docId,
      }
      this.qsListRes.push(qsAddItem)
      this.rowData.input = ''
      this.$emit('qsAdd', qsAddItem)
    },
    qsDel(index) {
      this.$emit('qsDel', this.qsListRes[index].id)
      this.qsListRes.splice(index, 1)
    },
  },
}
</script>

<style lang="scss" scoped>
.qs-box {
  // width: 323px;
  // height: 260px;
  border: 1px solid #dcdfe6;
  padding-bottom: 10px;
  overflow-y: auto;
  .qs-list {
    .qs-item {
      background: #f5f5fa;
      border-radius: 4px;
      height: 40px;
      margin: 8px 6px;
      display: flex;
      padding: 5px;
      position: relative;
      .qs-index {
        width: 26px;
        height: 26px;
        background: #ffffff;
        border-radius: 4px;
        display: inline-block;
        text-align: center;
        padding: 3px;
        // position: relative;
        // top: 5px;
        // left: 5px;
      }
      .qs-name {
        width: 85%;
        display: inline-block;
        height: 30px;
        line-height: 30px;
        padding-left: 12px;
      }
      .qs-del {
        font-size: 18px;
        cursor: pointer;
        position: absolute;
        top: 10px;
        right: 8px;
        display: none;
      }
      :deep(.el-input__inner) {
        background-color: #f5f5fa;
        border: none;
        height: 30px;
      }
    }
    .qs-hover:hover {
      .qs-del {
        display: inline-block;
      }
    }
  }
}
.scroll-box::-webkit-scrollbar {
  width: 6px;
}

/* Track */
.scroll-box::-webkit-scrollbar-track {
  background-color: #f1f1f1;
}

/* Handle */
.scroll-box::-webkit-scrollbar-thumb {
  background-color: #909399;
  border-radius: 6px;
}

/* Handle on hover */
.scroll-box::-webkit-scrollbar-thumb:hover {
  background-color: #555;
}

.edit-box {
  border: 1px solid #3d6fff;
}

.custom-btn {
  font-size: 18px;
  color: #909399;
  margin-right: 10px;
}
</style>
