<template>
  <div class="os-scroll os_scroll">
    <div class="entity-page" v-loading="tableData.loading">
      <div class="entity-page-head">
        <!-- <i class="ic-r-angle-l-line entity-page-head-back" @click="back" /> -->
        <div class="entity_page_head_left">
          <back-icon @click="back" style="margin-right: 10px"></back-icon>
          <el-popover placement="bottom" width="264" trigger="click">
            <select-entity :subAccount="subAccount" />
            <div slot="reference" class="entity-page-head-title">
              <span
                style="max-width: 250px"
                class="txt-ellipsis-nowrap"
                :title="oldEntity.zhName"
                >{{ oldEntity.zhName }}</span
              >
              <i class="ic-r-triangle-down" />
            </div>
          </el-popover>
        </div>

        <div class="header-right">
          <a
            class="btn-dynamic-docs"
            v-if="entity.type == 3"
            :href="`${$config.docs}doc-49/`"
            target="_blank"
            >查看文档</a
          >
          <div v-if="entity.operator">
            <p class="header-save-time">
              最近由<span class="text-blod" style="color: #262626">{{
                entity.operator
              }}</span>
            </p>
            <p
              v-if="entity.updateTime"
              class="header-save-time"
              style="text-align: right"
            >
              保存于{{ entity.updateTime | time }}
            </p>
          </div>
          <span
            class="header-save-time"
            v-if="entity.updateTime && !entity.operator"
            >最近保存 {{ entity.updateTime | time }}</span
          >
          <template v-if="showStructureBtn">
            <span class="header-qa">
              <el-tooltip
                class="item"
                effect="dark"
                content="实体构建后方可生效"
                placement="bottom"
              >
                <i class="el-icon-question" />
              </el-tooltip>
            </span>
            <el-button
              size="small"
              type="primary"
              @click="structure"
              :loading="structureLoading"
            >
              {{ structureLoading ? '构建中...' : '构建实体' }}
            </el-button>
          </template>
        </div>
      </div>
      <!-- <os-divider class="mgb40" /> -->
      <!-- <div class="mgb45"> -->
      <!-- <os-page-label :showAngle="true" :angleDown="angleDownInfo" @toggle="angleDownInfo=!angleDownInfo" label="基本信息" class="mgb24" /> -->
      <div class="os_scroll_content">
        <div class="main">
          <os-page-label label="基本信息" class="mgb12" />
          <el-form
            class="mgb20"
            :model="entity"
            ref="entityForm"
            label-width="104px"
            :rules="rules"
            inline
            label-position="left"
          >
            <el-form-item
              label="实体名称"
              prop="zhName"
              class="entity-page-form-item"
              style="width: 380px"
            >
              <template v-if="!edit">
                <span class="entity-name" :title="entity.zhName">{{
                  entity.zhName
                }}</span>
                <div class="ib entity-edit-btn" @click="toEdit">
                  <i class="ic-r-edit" />
                  <span>编辑</span>
                </div>
              </template>
              <div v-else @keyup.enter="editEntityBlur" class="mgb16">
                <el-input
                  v-model="entity.zhName"
                  ref="entityNameInput"
                  class="entity-page-form-input"
                  placeholder="请输入实体中文名"
                  @blur="editEntity"
                />
                <input type="text" style="display: none" />
                <i
                  class="entity-page-form-save el-icon-check"
                  @click="$refs.entityForm.validate()"
                />
                <i
                  class="entity-page-form-cancel el-icon-close"
                  data-action="del"
                  @mousedown="cancelEdit"
                />
              </div>
            </el-form-item>
            <el-form-item
              label="实体标识"
              prop="name"
              class="entity-page-form-item"
            >
              <span>{{ entity.name || '-' }}</span>
            </el-form-item>
            <el-form-item
              label="实体类型"
              prop="name"
              class="entity-page-form-item"
            >
              <span>{{ entity.type | entityType }}</span>
            </el-form-item>
          </el-form>
          <os-divider class="mgb24" />
          <!-- </div> -->
          <div v-if="entity.type === 2 || entity.type === 4">
            <div class="mgb45">
              <os-page-label
                :showAngle="true"
                :angleDown="angleDownQuote"
                @toggle="angleDownQuote = !angleDownQuote"
                label="引用实体"
                class="mgb24"
              >
                <el-tooltip
                  class="item"
                  effect="dark"
                  content="引用其他静态实体，快速扩充实体词条。"
                  placement="bottom"
                >
                  <i class="el-icon-question quote-entity-tips" />
                </el-tooltip>
              </os-page-label>
              <div class="quote-entity" v-show="angleDownQuote">
                <!-- <el-tag
              class="entity"
              type="info"
              :key="index"
              v-for="(entity, index) in entity.parentEntityList"
              closable
              :disable-transitions="false"
              @close="deleteQuoteEntity(index)"
            >
              <span
                class="entity-tag"
                :title="`${entity.value}(${entity.name})`"
                >{{ entity.value }}({{ entity.name }})</span
              >
            </el-tag> -->

                <el-popover
                  v-if="
                    entity.parentEntityList &&
                    entity.parentEntityList.length < entityQuoteCount
                  "
                  @show="quoteEntityShow"
                  placement="bottom-start"
                  width="360"
                  trigger="click"
                >
                  <quote-entity
                    :count="isQuoteEntityShow"
                    :entityId="entityId"
                    @select="quoteEntity"
                  />
                  <span
                    slot="reference"
                    class="quote-entity-btn"
                    ref="quoteEntity"
                    ><i class="ic-r-plus"></i
                  ></span>
                </el-popover>
                <el-button size="small">
                  <a
                    class="download-anchor"
                    target="_blank"
                    :href="`${this.$config.server}/aiui/web/download?url=https://aiui-file.cn-bj.ufileos.com/entity_compare.txt&fileName=entity_compare.txt&code=UTF-8`"
                    >下载实体模板</a
                  >
                </el-button>
                <el-button size="small" @click="showUploadAndCompare"
                  >上传比对实体</el-button
                >
                <!-- 列表展示 -->
                <el-table
                  class="gutter-table-style secondary-table"
                  style="margin-top: 20px"
                  :data="entity.parentEntityList"
                >
                  <el-table-column prop="name" label="实体" min-width="100px">
                    <template slot-scope="scope">
                      <div class="intent-name">
                        {{ scope.row.value }}
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="name"
                    label="英文标识"
                    min-width="200px"
                  >
                    <template slot-scope="scope">
                      <div class="intent-name">
                        {{ scope.row.name }}
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="name" label="描述" min-width="200px">
                    <template slot-scope="scope">
                      <div class="intent-name">
                        {{ scope.row.description }}
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="name" label="示例" min-width="400px">
                    <template slot-scope="scope">
                      <div class="intent-name">
                        {{ scope.row.example }}
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="name" label="操作" min-width="100px">
                    <template slot-scope="scope">
                      <i
                        class="ic-r-delete cell-handle-ic"
                        @click="delEntityInTable(scope.row)"
                      ></i>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>

            <os-page-label
              :showAngle="true"
              :angleDown="angleDownDict"
              @toggle="angleDownDict = !angleDownDict"
              label="词条管理"
              class="mgb24"
            />

            <div v-show="angleDownDict">
              <el-tabs
                style="margin-bottom: 100px"
                v-model="dictType"
                type="card"
                :class="[
                  'entity-dict-tabs',
                  {
                    'hide-tabs-header':
                      entity.parentEntityList &&
                      !entity.parentEntityList.length,
                  },
                ]"
              >
                <el-tab-pane name="1">
                  <span slot="label">
                    新增词条
                    <el-tooltip
                      class="item"
                      effect="dark"
                      content="在引用的实体的基础上，添加更多词条。"
                      placement="bottom"
                    >
                      <i class="el-icon-question quote-entity-tips" />
                    </el-tooltip>
                  </span>
                  <template v-if="dictType == 1">
                    <div class="mgb16 btn_search_area">
                      <div @keyup.enter="searchEntry">
                        <el-input
                          class="entry-search-area"
                          size="medium"
                          placeholder="搜索词条"
                          v-model="entrySearchName"
                        >
                          <i
                            slot="suffix"
                            class="el-input__icon el-icon-search search-area-btn"
                            @click="searchEntry"
                          />
                        </el-input>
                      </div>
                      <div>
                        <el-button
                          class="mgr16"
                          icon="ic-r-plus"
                          size="small"
                          type="primary"
                          :disabled="tableData.total >= entityLimitCount"
                          @click="addRow"
                        >
                          添加词条
                        </el-button>
                        <el-dropdown
                          trigger="click"
                          @command="handleCommand"
                          placement="bottom-start"
                        >
                          <el-button size="small">
                            批量操作
                            <i class="ic-r-triangle-down el-icon--right" />
                          </el-button>
                          <el-dropdown-menu
                            style="width: 120px"
                            slot="dropdown"
                          >
                            <el-dropdown-item>
                              <div @click="coverShow = true">批量覆盖</div>
                            </el-dropdown-item>
                            <el-dropdown-item
                              v-if="tableData.total >= entityLimitCount"
                              style="padding: 0"
                            >
                              <div class="import-disabled">批量追加</div>
                            </el-dropdown-item>
                            <el-dropdown-item v-else>
                              <upload
                                :dictId="dictIdList[dictType]"
                                :options="addOnly"
                                :limitCount="limitCount"
                                :subAccount="subAccount"
                                @setErrInfo="setErrInfo"
                                @setLoad="setLoad"
                                @getEntryList="getEntryList(1)"
                              ></upload>
                            </el-dropdown-item>
                            <el-dropdown-item command="export">
                              导出实体
                            </el-dropdown-item>
                            <el-dropdown-item command="download">
                              下载模版
                            </el-dropdown-item>
                          </el-dropdown-menu>
                        </el-dropdown>
                      </div>
                    </div>
                    <os-table
                      ref="entityTable"
                      :border="true"
                      class="entry-table secondary-table"
                      :tableData="tableData"
                      style="margin-bottom: 56px"
                      @change="getEntryList"
                      @del="delEntry"
                    >
                      <el-table-column prop="value" width="200" label="词条">
                        <template slot-scope="scope">
                          <el-input
                            :ref="'entityValueInput' + scope.$index"
                            class="entry-value"
                            size="small"
                            placeholder="输入词条，回车添加"
                            v-model="scope.row.value"
                            :title="scope.row.value"
                            @keyup.enter.native="editEntryBlur"
                            @blur="editEntry(scope.row, scope.$index)"
                          >
                          </el-input>
                        </template>
                      </el-table-column>
                      <el-table-column prop="lemma" label="别名">
                        <template slot-scope="scope">
                          <div class="entry-lemma-area">
                            <el-tag
                              v-for="(tag, index) in scope.row.lemmaArr"
                              :key="index"
                              closable
                              :disable-transitions="false"
                              @close="delLemma(index, scope.row)"
                            >
                              <span class="entry-lemma-area-tag" :title="tag">{{
                                tag
                              }}</span>
                            </el-tag>
                            <el-input
                              class="entry-lemma-add"
                              size="small"
                              v-model="scope.row.addLemmaName"
                              :placeholder="
                                (scope.$index === 0 && tableData.page === 1) ||
                                !scope.row.id
                                  ? '输入别名，回车添加'
                                  : ''
                              "
                              ref="saveTagInput"
                              @keyup.enter.native="addLemmaBlur"
                              @blur="addLemma(scope.row, scope.$index, $event)"
                            ></el-input>
                          </div>
                        </template>
                      </el-table-column>
                    </os-table>
                  </template>
                </el-tab-pane>
                <el-tab-pane name="2">
                  <span slot="label">
                    词条黑名单
                    <el-tooltip
                      class="item"
                      effect="dark"
                      content="在引用的实体的基础上，添加不需要命中该实体的词条。"
                      placement="bottom"
                    >
                      <i class="el-icon-question quote-entity-tips" />
                    </el-tooltip>
                  </span>
                  <template v-if="dictType == 2">
                    <div class="mgb16 btn_search_area">
                      <div @keyup.enter="searchEntry">
                        <el-input
                          class="entry-search-area"
                          size="medium"
                          placeholder="搜索黑名单词条"
                          v-model="entrySearchName"
                        >
                          <i
                            slot="suffix"
                            class="el-input__icon el-icon-search search-area-btn"
                            @click="searchEntry"
                          />
                        </el-input>
                      </div>
                      <div>
                        <el-button
                          class="mgr16"
                          icon="ic-r-plus"
                          size="small"
                          type="primary"
                          :disabled="tableData.total >= entityLimitCount"
                          @click="addRow(2)"
                        >
                          黑名单词条
                        </el-button>
                        <el-dropdown
                          trigger="click"
                          @command="handleCommand"
                          placement="bottom-start"
                        >
                          <el-button size="small">
                            批量操作
                            <i class="ic-r-triangle-down el-icon--right" />
                          </el-button>
                          <el-dropdown-menu
                            style="width: 120px"
                            slot="dropdown"
                          >
                            <el-dropdown-item>
                              <div @click="coverShow = true">批量覆盖</div>
                            </el-dropdown-item>
                            <el-dropdown-item
                              v-if="tableData.total >= entityLimitCount"
                              style="padding: 0"
                            >
                              <div class="import-disabled">批量追加</div>
                            </el-dropdown-item>
                            <el-dropdown-item v-else>
                              <upload
                                :dictId="dictIdList[dictType]"
                                :options="addOnly"
                                :limitCount="limitCount"
                                :subAccount="subAccount"
                                @setLoad="setLoad"
                                @setErrInfo="setErrInfo"
                                @getEntryList="getEntryList(1)"
                              ></upload>
                            </el-dropdown-item>
                            <el-dropdown-item command="export">
                              导出实体
                            </el-dropdown-item>
                            <el-dropdown-item command="download">
                              下载模版
                            </el-dropdown-item>
                          </el-dropdown-menu>
                        </el-dropdown>
                      </div>
                    </div>
                    <div style="margin-bottom: 56px">
                      <template v-if="tableData.list && tableData.list.length">
                        <div class="dict-list-wrap">
                          <el-input
                            ref="entityValueInput"
                            class="dict-input"
                            placeholder="输入词条/别名，回车添加"
                            v-for="(dict, index) in tableData.list"
                            :key="index"
                            v-model="dict.value"
                            :title="dict.value"
                            @keyup.enter.native="editEntryBlur"
                            @blur="editEntry(dict, index)"
                          >
                            <i
                              slot="suffix"
                              class="ic-r-delete"
                              @click="delEntry(dict, index)"
                            />
                          </el-input>
                        </div>
                        <el-pagination
                          v-if="tableData.total > tableData.blackSize"
                          class="pagination-wrap"
                          layout="prev, pager, next"
                          :page-size="tableData.blackSize"
                          :total="tableData.total"
                          @current-change="getEntryList"
                        >
                        </el-pagination>
                      </template>
                      <p class="no-data-tip" v-else>暂无数据</p>
                    </div>
                  </template>
                </el-tab-pane>
              </el-tabs>
            </div>
          </div>
          <el-dialog
            class="upload-cover-dialog"
            width="480px"
            :visible.sync="coverShow"
          >
            <div slot="title" class="dialog-title">
              <i class="ic-r-exclamation"></i>确定批量导入覆盖吗？
            </div>
            <span>一旦导入成功，技能现有信息将被完全覆盖！</span>
            <div slot="footer" class="dialog-footer">
              <el-button @click="coverShow = false">取 消</el-button>
              <el-button @click="coverShow = false" class="upload-btn-wrap">
                <upload
                  :dictId="dictIdList[dictType]"
                  :name="entity.name"
                  :options="cover"
                  :limitCount="limitCount"
                  :subAccount="subAccount"
                  @setLoad="setLoad"
                  @setErrInfo="setErrInfo"
                  @getEntryList="getEntryList(1)"
                ></upload>
              </el-button>
            </div>
          </el-dialog>
          <dynamic-entity
            ref="dynamicEntity"
            :entityId="entityId"
            @change="getEntity"
            v-if="entity.type === 3"
            @setDynamicChanged="setDynamicChanged"
          />
          <see-say-entity
            :entityId="entityId"
            @change="getEntity"
            v-if="entity.type === 5"
          />
        </div>
      </div>
    </div>
    <!-- 批量操作错误提示 -->
    <el-dialog title="错误提示" :visible.sync="showErrDialog" width="50%">
      <div style="margin-bottom: 20px">
        <p
          style="line-height: 22px"
          v-for="(text, index) in errList"
          :key="index"
        >
          {{ text }}
        </p>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="showErrDialog = false"
          >确 定</el-button
        >
      </span>
    </el-dialog>
    <!-- 子账号登录后首次进入提示 -->
    <cooperate-warn-dialog
      :dialog="cooperateDialog"
      type="firstEnterEntity"
    ></cooperate-warn-dialog>
    <page-leave-tips
      :dialog="leaveDialog"
      @save="save"
      @noSave="noSave"
      @noJump="noJump"
    />

    <upload-and-compare-entity
      :dialog="dialog"
      :options="
        (entity.parentEntityList || []).filter((item) => item.type == '1')
      "
    ></upload-and-compare-entity>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import SelectEntity from './selectEntity.vue'
import QuoteEntity from './quoteEntity.vue'
import DynamicEntity from './dynamicEntity.vue'
import SeeSayEntity from './seeSayEntity.vue'
import Upload from './uploadFile'
import CooperateWarnDialog from '@C/cooperatWarnDialog'
import UploadAndCompareEntity from './uploadAndCompareEntity.vue'

export default {
  name: 'entity',
  data() {
    return {
      entityId: '',
      entity: {},
      oldEntity: {},
      angleDownInfo: false,
      angleDownQuote: false,
      angleDownDict: true,
      isQuoteEntityShow: false,
      rules: {
        zhName: [
          this.$rules.required('实体名称不能为空'),
          this.$rules.lengthLimit(1, 32, '实体名称长度不能超过32个字符'),
          this.$rules.baseRegLimit(),
        ],
      },
      edit: false,
      dictType: '1', // 对应dictIdList role 标识是否黑白词条
      dictIdList: {}, //存放dictId
      entrySearchName: '',
      tableData: {
        loading: false,
        total: 0,
        page: 1,
        size: 10,
        defaultSize: 10,
        blackSize: 40, // 黑名单词条一页词条数
        handles: ['del'],
        handleColumnText: '',
        list: [],
      },
      oldTableDataList: [],
      structureLoading: false,
      checkCount: 0,
      editEntryEnterBlur: false,
      cover: {
        type: 1,
        text: '覆 盖',
      },
      addOnly: {
        type: 0,
        text: '批量追加',
      },
      coverShow: false,
      showErrDialog: false,
      errList: [],
      regOfEntry:
        /^[\u4e00-\u9faf\uff00-\uffef\u30a0-\u30ff\u3000-\u303f\u3040-\u309F\u0400-\u052f\u00a0-\u00ff\u0100-\u017f\u0180-\u024f\u4e00-\u9fffa-zA-Z0-9\(\)\+\.\*`\%'_ -]+$/,
      // regDescOfEntry: '词条名仅支持汉字/字母/点/空格/数字/下划线/短横杠/英文单引号/百分号/反单引号,
      regDescOfEntry: "词条名仅支持中英文/数字/空格和._-'%`()*+",
      regOfLemma:
        /^[\u4e00-\u9faf\uff00-\uffef\u30a0-\u30ff\u3000-\u303f\u3040-\u309F\u0400-\u052f\u00a0-\u00ff\u0100-\u017f\u0180-\u024f\u4e00-\u9fffa-zA-Z0-9\(\)\+\.\*\%'_ =-]+$/,
      regDescOfLemma: "词条别名仅支持中英文/数字/空格和()._-'%*+=",
      cooperateDialog: {
        show: false,
      },
      dynamicChanged: false,
      leaveDialog: {
        show: false,
      },
      routeTo: {},
      dialog: {
        show: false,
      },
    }
  },
  beforeRouteLeave: function (to, from, next) {
    if (this.dynamicChanged) {
      this.leaveDialog.show = true
      this.routeTo = to
      next(false)
    } else {
      next()
    }
  },
  computed: {
    showStructureBtn() {
      return this.entity.type != 3 && this.entity.type != 5
    },
    ...mapGetters({
      limitCount: 'aiuiApp/limitCount',
      subAccount: 'user/subAccount',
    }),
    isCharNoLimit() {
      return this.limitCount['char_no_limit_language'] > 0 // 0：限制；>0不限制
    },
    entityLimitCount() {
      return this.limitCount['entity_entry_count'] || '20000'
    },
    entityQuoteCount() {
      return this.limitCount['entity_quote_count'] || '3'
    },
  },
  mounted() {
    this.$nextTick(function () {
      window.onbeforeunload = () => {
        if (this.dynamicChanged) return '放弃当前未保存内容而关闭页面？'
      }
    })
  },
  created() {
    if (this.$route.params.entityId) {
      this.entityId = this.$route.params.entityId
      this.getEntity(true)
    } else {
      this.$router.push({ name: 'studio-handle-platform-entities' })
    }
    if (this.subAccount && localStorage.getItem('firstEnterEntity')) {
      this.cooperateDialog.show = true
    }
  },
  watch: {
    'tableData.loading': function (val, oldVal) {
      if (!val && this.$refs.entityTable) {
        this.$refs.entityTable.doLayout()
      }
    },
    dictType() {
      this.getEntryList(1)
    },
  },
  methods: {
    setLoad(val) {
      this.tableData.loading = val
    },
    setErrInfo(data, type) {
      this.errList = JSON.parse(data)
      this.showErrDialog = type
    },
    back() {
      // this.subAccount
      //   ? this.$router.push({ name: 'sub-studio-handle-platform-entities' })
      //   : this.$router.push({ name: 'studio-handle-platform-entities' })

      if (this.subAccount) {
        this.$router.push({ name: 'sub-studio-handle-platform-entities' })
      } else {
        this.$router.push({ name: 'studio-skill', query: { type: 'entity' } })
      }
    },
    getEntity(initData) {
      let self = this
      this.$utils.httpGet(
        this.$config.api.STUDIO_ENTITY_DETAIL,
        {
          entityId: this.entityId,
        },
        {
          success: (res) => {
            self.entity = res.data
            self.oldEntity = JSON.parse(JSON.stringify(res.data))
            if (res.data.type === 2) {
              res.data.dictList.forEach((item) => {
                if (item.role == '2') {
                  self.dictIdList['2'] = item.dictId
                } else {
                  self.dictIdList['1'] = item.dictId
                }
              })
              if (initData) {
                self.getEntryList()
              }
            }
          },
          error: (err) => {
            self.$router.push({ name: 'studio-handle-platform-entities' })
          },
        }
      )
    },
    toEdit() {
      this.edit = true
      this.$nextTick(function () {
        this.$refs['entityNameInput'] && this.$refs['entityNameInput'].focus()
      })
    },
    cancelEdit() {
      this.edit = false
      this.entity.zhName = this.oldEntity.zhName
      this.$refs.entityForm && this.$refs.entityForm.clearValidate()
    },
    editEntityBlur(e) {
      e.target.blur()
    },
    editEntity() {
      if (this.entity.zhName === this.oldEntity.zhName) {
        this.edit = false
        return
      }
      if (!this.edit) {
        return
      }
      this.$refs.entityForm.validate((valid) => {
        if (valid) {
          this.$utils.httpPost(
            this.$config.api.STUDIO_ENTITY_EDIT,
            {
              entityId: this.entityId,
              value: this.entity.zhName,
            },
            {
              success: (res) => {
                this.oldEntity.zhName = this.entity.zhName
                this.$message.success('修改成功')
                this.edit = false
                this.getEntity()
              },
              error: (err) => {},
            }
          )
        }
      })
    },
    quoteEntityShow() {
      this.isQuoteEntityShow = !this.isQuoteEntityShow
    },
    quoteEntity(item) {
      let self = this
      this.$utils.httpPost(
        this.$config.api.STUDIO_ENTITY_COMPILE_BASE_ADD,
        {
          entityId: this.entityId,
          baseEntityId: item.id,
        },
        {
          success: (res) => {
            self.entity.parentEntityList.push(item)
            self.getEntity()
          },
          error: (err) => {},
        }
      )
      // 成功后关闭弹窗
      this.$refs.quoteEntity.click()
    },
    deleteQuoteEntity(index) {
      let self = this
      this.$utils.httpPost(
        this.$config.api.STUDIO_ENTITY_COMPILE_BASE_DEL,
        {
          entityId: this.entityId,
          baseEntityId: this.entity.parentEntityList[index].id,
        },
        {
          success: (res) => {
            self.getEntity()
            self.entity.parentEntityList.splice(index, 1)
            if (self.entity.parentEntityList.length == 0) {
              self.dictType = '1'
            }
          },
          error: (err) => {},
        }
      )
    },
    handleCommand(command) {
      let self = this
      switch (command) {
        case 'export':
          self.exportExcel()
          break
        case 'download':
          self.downloadExcel()
          break
        default:
          break
      }
    },
    exportExcel() {
      this.$utils.postopen(
        this.$config.api.STUDIO_ENTITY_EXPORT_EXCEL,
        {
          name: this.entity.name,
          dictId: this.dictIdList[this.dictType],
        },
        this.subAccount
      )
    },
    downloadExcel() {
      window.open(
        'https://aiui-file.cn-bj.ufileos.com/DemoEntity.xlsx',
        '_self'
      )
    },
    getEntryList(page) {
      let self = this
      this.tableData.loading = true
      this.$utils.httpGet(
        this.$config.api.STUDIO_ENTITY_DICT_DATA,
        {
          dictId: this.dictIdList[this.dictType],
          pageIndex: page || this.tableData.page,
          pageSize:
            this.dictType == 2
              ? this.tableData.blackSize
              : this.tableData.defaultSize,
          search: this.entrySearchName,
        },
        {
          success: (res) => {
            let list = res.data.results
            this.tableData.list = Array.prototype.map.call(
              list,
              (item, index) => {
                item.lemmaArr = item.lemma ? item.lemma.split('|') : []
                return item
              }
            )
            this.oldTableDataList = JSON.parse(
              JSON.stringify(this.tableData.list)
            )
            this.tableData.total = res.data.count
            this.tableData.page = res.data.pageIndex
            this.tableData.size = res.data.pageSize
            this.tableData.loading = false
          },
          error: (err) => {},
        }
      )
    },
    searchEntry() {
      this.getEntryList(1)
    },
    addRow(type) {
      let self = this
      let entry = this.tableData.list[0] || {}
      if (entry.id || this.tableData.list.length <= 0) {
        this.tableData.list.unshift({})
        if (this.tableData.total % 10 === 0) {
          this.tableData.size = 11
        } else {
          this.tableData.size = 10
        }
        this.tableData.total += 1
      }
      this.editEntryEnterBlur = false
      if (type == 2) {
        this.$nextTick(function () {
          self.$refs['entityValueInput'] &&
            self.$refs['entityValueInput'][0].focus()
        })
      } else {
        this.$nextTick(function () {
          self.$refs['entityValueInput0'] &&
            self.$refs['entityValueInput0'].focus()
        })
      }
    },
    // 添加词条
    addEntry(data) {
      let self = this
      let reg = self.regOfEntry
      if (data.value) {
        if (data.value.length > 128) {
          return self.$message.warning('词条名不能超过128个字符')
        }
        if (!this.isCharNoLimit) {
          if (!reg.test(data.value)) {
            return self.$message.warning(self.regDescOfEntry)
          }
        }
      } else {
        return
      }
      let len = self.tableData.list.length
      if (len > 10) {
        self.tableData.list.splice(10, 1)
        // this.tableData.loading = true
      }
      this.$utils.httpPost(
        this.$config.api.STUDIO_ENTITY_DICT_ADD,
        {
          dictId: this.dictIdList[this.dictType],
          value: data.value,
        },
        {
          success: (res) => {
            let entry = self.tableData.list[0]
            entry.id = res.data.id
            entry.lemma = res.data.lemma
            entry.lemmaArr = []
            self.tableData.loading = false
            if (self.editEntryEnterBlur) {
              self.addRow(self.dictType)
            }
            // self.getEntryList()
          },
          error: (err) => {},
        }
      )
    },
    editEntryBlur(event) {
      event.target.blur()
      this.editEntryEnterBlur = true
    },
    // 编辑词条
    editEntry(data, index) {
      let self = this
      let reg = self.regOfEntry
      if (data.id) {
        if (!data.value) {
          return this.$message.warning('词条不能为空')
        }
        if (!self.tableData.list[0].id) {
          index -= 1
        }

        if (self.oldTableDataList[index]?.value === data.value) {
          return
        }
        if (data.value.length > 128) {
          return self.$message.warning('词条名不能超过128个字符')
        }
        if (!this.isCharNoLimit) {
          if (!reg.test(data.value)) {
            return self.$message.warning(self.regDescOfEntry)
          }
        }
        // this.tableData.loading = true
        this.$utils.httpPost(
          this.$config.api.STUDIO_ENTITY_DICT_EDIT,
          {
            dictId: this.dictIdList[this.dictType],
            id: data.id,
            value: data.value,
            lemma: data.lemma,
          },
          {
            success: (res) => {
              if (self.oldTableDataList[index]) {
                self.oldTableDataList[index].value = data.value
              }
            },
            error: (err) => {},
          }
        )
      } else {
        this.addEntry(data)
      }
    },
    toDel(data) {
      let self = this
      this.$confirm(
        '词条删除后不可恢复，请谨慎操作。',
        `确定删除词条 - ${data.value}`,
        {
          confirmButtonText: '确定删除',
          cancelButtonText: '取消',
          confirmButtonClass: 'el-button--danger',
          type: 'warning',
          showClose: false,
        }
      )
        .then(() => {
          self.delEntry(data)
        })
        .catch(() => {})
    },
    delEntry(data, index) {
      let self = this
      if (!data.id) {
        this.tableData.list.splice(index, 1)
        this.tableData.total -= 1
        return
      }
      this.tableData.loading = true
      this.$utils.httpPost(
        this.$config.api.STUDIO_ENTITY_DICT_DEL,
        {
          dictId: this.dictIdList[this.dictType],
          ids: data.id,
        },
        {
          success: (res) => {
            self.$message.success('删除成功')
            if (self.tableData.list.length === 1 && self.tableData.page > 1) {
              self.tableData.page -= 1
            }
            self.getEntity()
            self.getEntryList()
          },
          error: (err) => {},
        }
      )
    },
    delLemma(index, data) {
      let self = this
      let lemmaArr = JSON.parse(JSON.stringify(data.lemmaArr))
      let lemma = ''
      lemmaArr.splice(index, 1)
      if (lemmaArr.length) {
        lemma = lemmaArr.join('|')
      }
      this.tableData.loading = true
      this.$utils.httpPost(
        this.$config.api.STUDIO_ENTITY_DICT_EDIT,
        {
          dictId: this.dictIdList[this.dictType],
          id: data.id,
          value: data.value,
          lemma: lemma,
        },
        {
          success: (res) => {
            data.lemmaArr.splice(index, 1)
            data.lemma = data.lemmaArr.join('|')
            self.tableData.loading = false
            self.getEntity()
          },
          error: (err) => {},
        }
      )
    },
    addLemmaBlur(event) {
      event.target.blur()
    },
    addLemma(data, index, event) {
      let self = this
      let reg = self.regOfLemma
      if (!data.addLemmaName) {
        return
      }
      if (!data.id) {
        return self.$message.warning('请先填写词条名创建词条')
      }
      if (data.lemmaArr && data.lemmaArr.indexOf(data.addLemmaName) !== -1) {
        return self.$message.warning('该别名已存在，请勿重复')
      }
      if (data.addLemmaName) {
        if (!data.value) {
          return this.$message.warning('词条不能为空')
        }
        if (data.addLemmaName.length > 128) {
          return self.$message.warning('词条别名不能超过128个字符')
        }
        if (!this.isCharNoLimit) {
          if (!reg.test(data.addLemmaName)) {
            return self.$message.warning(self.regDescOfLemma)
          }
        }
        let lemma = data.lemmaArr.length
          ? `${data.lemma}|${data.addLemmaName}`
          : data.addLemmaName
        // this.tableData.loading = true
        this.$utils.httpPost(
          this.$config.api.STUDIO_ENTITY_DICT_EDIT,
          {
            dictId: this.dictIdList[this.dictType],
            id: data.id,
            value: data.value,
            lemma: lemma,
          },
          {
            success: (res) => {
              data.lemmaArr.push(data.addLemmaName)
              data.lemma = data.lemmaArr.join('|')
              data.addLemmaName = ''
              // self.tableData.loading = false
              event.target.focus()
              self.getEntity()
            },
            error: (err) => {},
          }
        )
      }
    },
    // 构建
    structure() {
      let self = this
      if (
        this.tableData.list.length <= 0 &&
        this.entity.parentEntityList &&
        this.entity.parentEntityList.length <= 0
      ) {
        return this.$message.warning('请添加至少一个词条再进行构建')
      }
      this.structureLoading = true
      this.$utils.httpPost(
        this.$config.api.STUDIO_ENTITY_COMPILE,
        {
          entityId: this.entityId,
        },
        {
          success: (res) => {
            self.$message.success('提交成功，正在构建...')
            self.checkStatus()
          },
          error: (err) => {
            self.structureLoading = false
          },
        }
      )
    },
    checkStatus() {
      let self = this
      this.checkCount += 1
      this.$utils.httpGet(
        this.$config.api.STUDIO_ENTITY_CHECK_COMPILE_STATUS,
        {
          entityId: this.entityId,
        },
        {
          success: (res) => {
            if (res.data === 4) {
              if (self.structureLoading) {
                self.$message.error('构建失败')
              }
              self.structureLoading = false
              self.checkCount = 0
            } else if (res.data === 1) {
              if (self.structureLoading) {
                self.$message.success('构建成功')
              }
              self.structureLoading = false
              self.checkCount = 0
            } else {
              if (self.checkCount < 300) {
                setTimeout(function () {
                  self.checkStatus()
                }, 2000)
              } else {
                if (self.structureLoading) {
                  self.$message.error('构建失败')
                }
                self.structureLoading = false
                self.checkCount = 0
              }
            }
          },
          error: (err) => {
            console.log('page=>>')
            console.log(err)
          },
        }
      )
    },
    setDynamicChanged(val) {
      this.dynamicChanged = val
    },
    save() {
      this.$refs.dynamicEntity.onSubmit()
    },
    noSave() {
      this.$refs.dynamicEntity.noSave()
      this.dynamicChanged = false
      if (this.routeTo.name) {
        this.$router.push({
          name: this.routeTo.name,
          params: this.routeTo.params,
        })
      }
    },
    noJump() {
      this.routeTo = {}
    },

    delEntityInTable(data) {
      console.log('del data', data)
      //处理数据兼容，以便调用 deleteQuoteEntity 方法
      const index = this.entity.parentEntityList.findIndex(
        (item) => item.name === data.name
      )
      this.deleteQuoteEntity(index)
    },

    showUploadAndCompare() {
      this.dialog.show = true
    },
  },
  components: {
    Upload,
    SelectEntity,
    QuoteEntity,
    DynamicEntity,
    SeeSayEntity,
    CooperateWarnDialog,
    UploadAndCompareEntity,
  },
}
</script>

<style scoped lang="scss">
.os_scroll {
  overflow-y: hidden !important;

  .entity-page {
    background-color: $secondary-bgc;
    .entity-page-head {
      padding: 0 24px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      background-color: #fff;
      .entity_page_head_left {
        display: flex;
        align-items: center;

        .txt-ellipsis-nowrap {
          font-weight: 600;
          font-size: 20px;
          color: #000000;
        }
      }
    }
  }

  .os_scroll_content {
    padding: 15px 20px;
    height: calc(100vh - 64px);
    overflow-y: auto;
    .main {
      min-height: 100%;
      padding: 20px 20px;
      border-radius: 16px;
      background-color: #fff;
      :deep(.os-page-label) {
        height: 24px;
        i {
          font-size: 14px;
        }
        .title {
          font-size: 18px;
          font-weight: 700;
        }
      }
    }
  }
}
.dialog-footer {
  display: flex;
  justify-content: flex-end;
}

.upload-btn-wrap {
  align-items: center;
  padding: 0 !important;
  box-sizing: content-box;
}
</style>

<style lang="scss">
.entity-page {
  // max-width: 1200px;
  // padding-top: 24px;
  margin: auto;
  &-head {
    height: 63px;
    display: flex;
    align-items: center;
    position: relative;
    border-bottom: 1px solid #e7e7e7;
    &-back {
      cursor: pointer;
      margin-right: 16px;
      color: $grey4;
    }
    &-title {
      flex: auto;
      display: flex;
      align-items: center;
      cursor: pointer;
      i {
        color: $grey5;
        margin-left: 4px;
      }
    }
    &-right {
      position: absolute;
      right: 0;
    }
  }
}
.entity-name {
  display: inline-block;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.entity-edit-btn {
  vertical-align: top;
  margin-left: 9px;
  color: $primary;
  cursor: pointer;
}

.entity-page-form-item {
  width: 30%;
  margin-bottom: 0;
}
.entity-page-form-input {
  width: 188px;
}
.entity-page-form-save,
.entity-page-form-cancel {
  margin-left: 16px;
  cursor: pointer;
  &:hover {
    color: $primary;
  }
}

.quote-entity-tips {
  color: $grey3;
  font-size: 16px !important;
  margin-left: 5px;
}
.quote-entity {
  .entity {
    margin: 0 8px 8px 0;
  }
  .entity-tag {
    vertical-align: middle;
    display: inline-block;
    max-width: 90px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  &-btn {
    display: inline-block;
    width: 36px;
    height: 36px;
    line-height: 36px;
    text-align: center;
    font-size: 14px;
    color: $grey6;
    background: $grey4-15;
    border-radius: 2px;
    vertical-align: middle;
    cursor: pointer;
    margin-bottom: 4px;
  }
}

.entity-dict-tabs {
  &.hide-tabs-header .el-tabs__header {
    display: none;
  }
  .dict-list-wrap {
    min-height: 110px;
  }
  .dict-input {
    width: 25%;
    .el-input__suffix {
      top: 10px;
      visibility: hidden;
    }
    &:hover {
      .el-input__suffix {
        visibility: visible;
      }
    }
    .el-input__inner {
      height: 40px;
      line-height: 40px;
      text-overflow: ellipsis;
      border-radius: 0;
      border-color: $grey3;
      border-left: 1px solid transparent;
      border-bottom: 1px solid transparent;
    }
    &:nth-child(4n - 3) {
      .el-input__inner {
        border-left: 1px solid $grey3;
      }
    }
    &:nth-last-child(4),
    &:nth-last-child(3),
    &:nth-last-child(2),
    &:last-child {
      .el-input__inner {
        border-bottom: 1px solid $grey3;
      }
    }
  }
  .ic-r-delete {
    font-size: 20px;
    cursor: pointer;
  }
  .pagination-wrap {
    margin: 24px auto;
    text-align: center;
  }

  .no-data-tip {
    margin: 50px auto;
    text-align: center;
  }
}
.entry-search-area {
  width: 480px;
}

.entry-value {
  input {
    height: 54px !important;
    line-height: 54px !important;
    border: 0 !important;
    padding-left: 0;
    color: $semi-black;
    font-weight: 600;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.entry-table {
  .el-table--border {
    border: none;
  }
  margin-bottom: 100px;
  .el-table__row td {
    padding: 0;
    height: 56px;
  }
  .el-table__body tr:hover > td {
    background-color: #fff;
  }
  td .cell {
    padding-left: 0;
  }
}

.entry-lemma-area {
  display: flex;
  align-items: center;
  min-height: 54px;
  padding: 3px 0 3px 8px;
  display: inline-flex;
  flex-flow: wrap;
  height: auto;
  width: 100%;
  box-sizing: border-box;
  border: 1px solid transparent;
  &:hover,
  &:focus-within {
    border: 1px solid $primary;
  }
  .el-tag {
    display: flex;
    align-items: baseline;
    margin: 3px 8px 3px 0;
  }
  &-tag {
    display: inline-block;
    max-width: 160px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  // .el-tag + .el-tag {
  //   margin-right: 8px;
  // }
  .el-tag + .el-input {
    flex: 1;
    min-width: 100px;
  }
}

.entry-lemma-add {
  margin-left: 4px;
  input {
    height: 36px !important;
    line-height: 36px !important;
    padding-left: 0px;
    border: 0 !important;
    color: $semi-black;
    font-weight: 600;
  }
}

.upload-cover-dialog {
  // .el-button {
  //   padding: 0;
  //   width: 130px;
  //   height: 44px;
  //   line-height: 44px;
  //   overflow: hidden;
  // }
  .el-upload .el-button {
    // width: 130px;
    // height: 44px;
    // font-size: 16px;
    color: $dangerous;
    min-width: 90px;
  }
  .dialog-title {
    font-size: 20px;
  }
  .ic-r-exclamation {
    margin-right: 16px;
    vertical-align: -1px;
    color: $warning;
  }
  .el-dialog__header {
    padding: 32px 32px 12px;
  }
  .el-dialog__body {
    padding-top: 0;
    padding-bottom: 23px;
    font-size: 16px;
    color: $semi-black;
    padding-left: 70px;
  }
  .el-dialog__footer {
    padding-bottom: 32px;
  }
}
.btn-dynamic-docs {
  margin-right: 24px;
  font-size: 14px;
}
.download-anchor {
  color: #262626;
}
.btn_search_area {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
