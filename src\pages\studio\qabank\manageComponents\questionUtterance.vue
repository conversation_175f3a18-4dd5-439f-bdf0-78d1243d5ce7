<template>
  <os-text-adder
    ref="textAdder"
    class="mgb24"
    :data="convertList(list.data || [])"
    :reg="textReg"
    :warning="warning"
    @add="addUtterance"
    @del="delUtterance"
    :readonly="true"
    :placeholder="list.tip"
    :disabled="!editable"
  />
  <!-- :max="10" -->
</template>
<script>
export default {
  name: 'expample-utterance',
  props: {
    list: {
      data: '',
      isCheck: false,
      type: '',
      tip: '',
    },
    subAccountEditable: Boolean,
  },
  data() {
    return {
      // textReg: /^[\u4e00-\u9fffa-zA-Z_\{\}\[\]\(\),\.!\?%\|\+×'\*÷\/-]{1,40}$/,
      // 中英文数字标点符号
      // textReg:
      //   /^[\u4e00-\u9fa5_a-zA-Z0-9\s\·\~\！\@\#\￥\%\……\&\*\（\）\——\-\+\=\【\】\{\}\、\|\；\‘\’\：\“\”\《\》\？\，\。\、\`\~\!\#\$\%\^\&\*\(\)\_\[\]{\}\\\|\;\'\'\:\"\"\,\.\/\<\>\?]{1,40}$/,
      // warning: '仅支持中英文、数字和标点符号,且每条不超过40字'
      textReg: /^[\u4e00-\u9fffa-zA-Z0-9 {}_?？°]{1,40}$/,
      warning: '仅支持汉字/字母/数字/空格/{}/_/?/°，且每条不超过40字',
    }
  },
  computed: {
    editable() {
      return !this.list.isCheck && this.subAccountEditable
    },
  },
  methods: {
    addUtterance(val) {
      // console.log('addUtterance', val)
      // this.list.data.push(val);
      this.list.query = val
      // this.$emit('qsAdd', JSON.stringify(this.list));
      this.$emit('qsAdd', this.list)
    },
    delUtterance(val, index) {
      // this.list.data = Array.prototype.filter.call(
      //   this.list.data,
      //   function (item, index) {
      //     return item != val;
      //   }
      // );
      // console.log(this.list.data[index].id, this.list.data[index].query, val)
      this.$emit('qsDel', this.list.data[index].id)
    },
    delText() {
      this.$refs.textAdder.delText()
    },
    convertList(list) {
      let res = []
      list.forEach((element) => {
        res.push(element.query)
      })
      return res
    },
  },
}
</script>
