<template>
  <el-dialog title="选择应用" :visible.sync="dialog.show" width="640px">
    <el-table
      ref="table"
      class="mgb40"
      :data="apps"
      height="300"
      style="width: 100%"
      v-loading="loading"
      @row-click="handleSelect"
    >
      <el-table-column
        class="el-table-column"
        prop="appName"
        label="应用名称"
        width="130"
      >
      </el-table-column>
      <el-table-column
        class="el-table-column"
        prop="appid"
        label="APPID"
        width="160"
      >
      </el-table-column>
      <el-table-column width="100" label="选择应用" class="el-table-column">
        <template slot-scope="scope">
          <el-radio
            class="no_radio_label"
            v-model="selectedApp"
            :label="scope.row"
          ></el-radio>
        </template>
      </el-table-column>
    </el-table>
    <!-- <el-checkbox v-model="checked" class="mgb16"><span>我已阅读并同意<a>《科大讯飞用户协议》</a></span></el-checkbox> -->
    <span slot="footer" class="dialog-footer">
      <el-button
        class="dialog-btn"
        type="primary"
        style="min-width: 100%"
        @click="save"
      >
        继续购买
      </el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  props: {
    dialog: {
      type: Object,
      default: {},
    },
  },
  data() {
    return {
      loading: true,
      apps: [],
      selectedApp: {},
      selectedAppId: null,
    }
  },
  computed: {
    btnDisabled() {
      return !this.selectedApp.appid
    },
  },
  watch: {
    selectedAppId() {
      // alert(this.selectedAppId)
    },
    'dialog.show': function (val, oldVal) {
      if (val) {
        this.apps = []
        this.selectedApp = {}
        this.getApps()
      } else {
      }
    },
  },
  mounted() {},
  methods: {
    getApps() {
      let self = this
      this.loading = true
      this.$utils.httpGet(
        this.$config.api.GET_APP,
        {
          wareId: this.dialog.wareId,
        },
        {
          success: (res) => {
            console.log(JSON.stringify(res))
            if (res.flag) {
              self.apps = res.data.apps
            }
            /*self.apps = Array.prototype.map.call(res.data, function (item, index) {
            item.selected = item.isUsed
            return item
          })*/
            self.loading = false
          },
          error: (err) => {},
        }
      )
    },
    handleSelect(row, event, column) {
      let self = this
      // let apps = Array.prototype.map.call(this.apps, function (item, index) {
      //   if (item.id === row.id && item.sceneId === row.sceneId) {
      //     item.selected = true
      //     self.selectedApp = item
      //   } else {
      //     item.selected = false
      //   }
      //   return item
      // })
      // this.apps = apps
      if (!row.isUsed) {
        row.selected = false
        self.selectedApp = row
      }
    },
    save() {
      if (!this.selectedApp || JSON.stringify(this.selectedApp) == '{}') {
        this.$message.error('请选择应用')
        return
      }
      window.open(
        `${this.$config.xfyunConsole}sale/buy?platform=aiui&packageId=${this.dialog.packageId}&wareId=${this.dialog.wareId}&appId=${this.selectedApp.appid}&appName=${this.selectedApp.appName}&serviceName=AIUI服务`,
        '_blank'
      )
    },
  },
  components: {},
}
</script>
<style></style>
<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="scss" scoped>
:deep(.el-table__body-wrapper > table > tbody > tr:last-of-type > td) {
  border-bottom: none !important;
}
</style>
