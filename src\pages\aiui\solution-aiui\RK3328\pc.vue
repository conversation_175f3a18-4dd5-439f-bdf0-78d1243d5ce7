<template>
  <div class="main-content">
    <section class="main-content-banner">
      <div class="banner-text">
        <h2
          v-html="'RK3328<br/>AIUI降噪板开发套件'"
          style="line-height: 60px"
        ></h2>

        <p class="pc-show banner-text-content">
          直接输出降噪后音频的多麦克风阵列语音交互解决方案
          <br />
          适用于服务机器人场景
        </p>

        <div class="hor-btn">
          <div class="banner-text-button" @click="toConsole">合作咨询</div>

          <div class="banner-text-buy" @click="toBuy">立即购买</div>
        </div>
      </div>
    </section>

    <section class="section section-2">
      <div class="section-title">应用场景</div>

      <div class="section-item">
        <ul>
          <li v-for="(item, index) in app_scenario" :key="index" class="app">
            <img :src="item.src" :alt="item.alt" />

            <p class="app-text">{{ item.alt }}</p>
          </li>
        </ul>
      </div>
    </section>

    <section class="section section-3">
      <div class="section-title">产品功能</div>

      <div class="section-item"></div>
    </section>

    <section class="section section-4">
      <div class="section-title">产品图片</div>

      <div class="section-item">
        <div>
          <p>RK3328降噪板</p>

          <p>环形模拟硅麦6麦</p>

          <p>线性模拟硅麦4麦</p>

          <p>线性模拟硅麦6麦</p>
        </div>
      </div>
    </section>

    <section class="section section-5">
      <div class="section-title">使用说明</div>

      <div class="section-item">
        <div>
          <p v-html="'录制原始音频<br/>给主板'"></p>

          <p v-html="'声学处理后纯净音频<br/>上传至上位机'"></p>

          <p v-html="'回采信号输入<br/>到主板'"></p>

          <p>麦克风阵列</p>

          <p>降噪主板</p>

          <p>上位机</p>
        </div>
      </div>
    </section>

    <section class="section section-6">
      <div class="section-title">主板接口说明</div>

      <div class="section-item"></div>
    </section>

    <section class="section section-7">
      <div class="section-title">硬件参数</div>

      <div class="section-item"></div>
    </section>

    <section class="section section-8">
      <div class="section-title">产品清单</div>

      <div class="section-item">
        <div v-for="item in product_list" :key="item.index" class="item">
          <img :src="item.src" />

          <p>{{ item.title }}</p>

          <p v-html="item.sub_title"></p>
        </div>
      </div>
    </section>

    <section class="section section-9">
      <div class="section-title">开发材料</div>

      <div class="section-item">
        <div class="item">
          <a
            v-for="item in develop_doc"
            :key="item.index"
            @click="toDoc(item.link)"
            style="color: #666666"
          >
            {{ item.name }}
          </a>
        </div>
      </div>
    </section>

    <!-- <section class="section section-5">
      <div class="section-title">
        合作咨询
        <p>提交信息，我们会尽快与您联系</p>
      </div>
      <div class="section-item">
        <aiui-button hasTop @click.native="toConsole">申请合作</aiui-button>
      </div>
    </section> -->

    <!-- <div class="contact-wrap">
      <div class="title">合作咨询</div>
      <p class="desc">提交信息，我们会尽快与您联系</p>

      <aiui-button>
        <a hasTop @click="toConsole">申请合作</a>
      </aiui-button>
    </div> -->
  </div>
</template>

<script>
import corp from '@P/aiui/solution-aiui/components/corp.vue'

export default {
  layout: 'aiuiHome',
  data() {
    return {
      app_scenario: [
        {
          alt: '地铁购票机',
          src: require('../../../../assets/images/solution/soft-hardware/3328/section-2-1.png'),
        },
        {
          alt: '医院挂号机',
          src: require('../../../../assets/images/solution/soft-hardware/3328/section-2-2.png'),
        },
        {
          alt: '服务机器人',
          src: require('../../../../assets/images/solution/soft-hardware/3328/section-2-3.png'),
        },
      ],
      product_list: [
        {
          title: '硬件',
          sub_title:
            '• 降噪板     • 麦克风板<br/>• 麦克风排线 • 回采线<br/>• USB线',
          src: require('../../../../assets/images/solution/soft-hardware/7911/section-8-1.png'),
        },
        {
          title: '软件',
          sub_title:
            '唤醒SDK，集成前端声学算法<br/>AIUI SDK，集成云端交互能力（上位机集成）',
          src: require('../../../../assets/images/solution/soft-hardware/7911/section-8-2.png'),
        },
        {
          title: '服务',
          sub_title: '为期两个月的VIP技术支持<br/>浅定制资源定制支持',
          src: require('../../../../assets/images/solution/soft-hardware/7911/section-8-3.png'),
        },
      ],
      service: [
        {
          title: '免费定制服务',
          sub_title:
            '支持自定义云端交互<br/>支持唤醒词定制<br/>支持离线命令词定制',
          src: require('../../../../assets/images/solution/soft-hardware/7911/section-9-1.png'),
        },
        {
          title: '增值服务',
          sub_title: '硬件PCBA定制<br/>系统固件及软件应用定制',
          src: require('../../../../assets/images/solution/soft-hardware/7911/section-9-2.png'),
        },
      ],
      develop_doc: [
        {
          name: '1.《RK3328 AIUI降噪板开发套件产品白皮书.pdf》',
          link: 'https://aiui-doc.xf-yun.com/project-1/doc-104/',
        },
        {
          name: '2.《RK3328 AIUI降噪板开发套件产品规格书.pdf》',
          link: 'https://aiui-doc.xf-yun.com/project-1/doc-107/',
        },
        {
          name: '3.《RK3328 AIUI降噪板开发套件产品使用手册.pdf》',
          link: 'https://aiui-doc.xf-yun.com/project-1/doc-106/',
        },
        {
          name: '4.《RK3328 AIUI降噪板开发套件协议手册.pdf》',
          link: 'https://aiui-doc.xf-yun.com/project-1/doc-108/',
        },
      ],
    }
  },
  methods: {
    toConsole() {
      // 判断是否有百度SEM营销参数，有的话，提交表单页需要带上
      const search = window.location.search
      if (search) {
        window.open(`/solution/apply/23${search}`)
      } else {
        window.open('/solution/apply/23')
      }
    },
    toBuy() {
      window.open('https://www.aifuwus.com/onstage/cmddetail?product_type=4436')
    },
    toDoc(link) {
      window.open(link)
    },
  },
  components: { corp },
}
</script>

<style lang="scss" scoped>
@import '../../../../assets/scss/screen-and-lamp.scss';
@media screen and (min-width: 751px) {
  .main-content {
    &-banner {
      background: url(~@A/images/solution/soft-hardware/3328/banner_bg.png)
        center no-repeat;
      background-size: cover;
      height: 500px;
      overflow: hidden;
      width: 100%;
      .banner-text {
        max-width: 1200px;
        color: #fff;
        height: 100%;
        margin: auto;
        .hor-btn {
          display: flex;
          flex-direction: row;
          justify-content: flex-start;

          div:nth-child(2) {
            margin-left: 30px;
          }
        }
        &-button {
          font-size: 16px;
          text-align: center;
          font-weight: 400;
          width: 140px;
          height: 40px;
          line-height: 40px;
          border: none;
          color: #fff;
          cursor: pointer;
          transition: 0.6s;
          background: linear-gradient(0deg, #00b4ff 0%, #2371ff 100%);
          border-radius: 20px;
        }
        &-buy {
          font-size: 16px;
          text-align: center;
          font-weight: 400;
          width: 140px;
          height: 40px;
          line-height: 40px;
          color: #fff;
          cursor: pointer;
          transition: 0.6s;
          background: transparent;
          border-radius: 20px;
          border: 1px solid #ffffff;
        }
        h2 {
          color: #fff;
          padding-top: 148px;
          margin-bottom: 29px;
          font-size: 48px;
          font-weight: 500;
          line-height: 48px;
        }
        p {
          font-size: 18px;
          margin-bottom: 74px;
        }

        .banner-text-content {
          width: 570px;
          font-size: 16px;
          font-weight: 400;
          color: rgba(255, 255, 255, 0.86);
          line-height: 30px;
        }
      }
    }
  }
  .section-title {
    font-size: 34px;
    font-weight: bold;
    color: #333;
    margin-top: 0 !important;
    margin-bottom: 30px !important;
  }
  .section-sub-title {
    font-size: 16px;
    font-weight: 400;
    text-align: center;
    color: #666666;
    margin-bottom: 60px !important;
  }
  .app-text {
    color: #666;
  }
  .section-2 {
    .section-item {
      margin-top: 50px !important;
      > ul {
        display: flex;
        justify-content: space-between;
        li {
          width: 28% !important;
          img {
            width: 100%;
          }
        }
      }
    }
  }
  .section-3 {
    background: url('../../../../assets/images/solution/soft-hardware/3328/section-3-bg.png')
      center no-repeat !important;
    background-size: cover !important;
    height: 600px;
    width: 100%;
    .section-item {
      background: url('../../../../assets/images/solution/soft-hardware/3328/section-3.png')
        center no-repeat !important;
      background-size: contain !important;
      height: 507px;
    }
    .section-title {
      margin-bottom: 0 !important;
    }
  }
  .section-4 {
    background: white !important;
    height: 700px !important;
    width: 100%;
    .section-title {
      margin-bottom: 0 !important;
    }
    .section-item {
      margin-top: 0 !important;
      padding: 0 !important;
      background: url('../../../../assets/images/solution/soft-hardware/3328/section-4.png')
        center no-repeat !important;
      background-size: cover !important;
      height: 550px;
      position: relative;
      div {
        position: absolute;
        bottom: 0;
        left: 0;
        display: flex;
        width: 100%;
        flex-direction: row;
        justify-content: flex-start;
        p {
          font-size: 14px;
          font-weight: 400;
          text-align: center;
          color: #262626;
        }
        p:nth-child(1) {
          margin-left: 220px;
        }
        p:nth-child(2) {
          margin-left: 230px;
        }
        p:nth-child(3) {
          margin-left: 65px;
        }
        p:nth-child(4) {
          margin-left: 25px;
        }
      }
    }
  }
  .section-5 {
    background: url('../../../../assets/images/solution/soft-hardware/3328/section-5-bg.png')
      center no-repeat !important;
    background-size: cover !important;
    width: 100%;
    height: 600px;
    max-width: 100% !important;
    position: relative;
    .section-item {
      text-align: center;
      div:nth-child(1) {
        padding: 30px 30px;
        background: url('../../../../assets/images/solution/soft-hardware/3328/section-5.png')
          center no-repeat !important;
        background-size: contain !important;
        height: 300px;
        width: 800px;
        margin: 0 auto;
        position: relative;
        p {
          font-size: 14px;
          font-weight: 400;
          text-align: center;
          color: #666666;
        }
        p:nth-child(1) {
          position: absolute;
          bottom: 55px;
          left: 220px;
        }
        p:nth-child(2) {
          position: absolute;
          top: 65px;
          right: 140px;
        }
        p:nth-child(3) {
          position: absolute;
          bottom: 55px;
          right: 150px;
        }
        p:nth-child(4) {
          font-size: 20px;
          font-weight: 600;
          text-align: left;
          color: #262626;
          position: absolute;
          bottom: -50px;
          left: 80px;
        }
        p:nth-child(5) {
          font-size: 20px;
          font-weight: 600;
          text-align: left;
          color: #262626;
          position: absolute;
          bottom: -50px;
          left: 390px;
        }
        p:nth-child(6) {
          font-size: 20px;
          font-weight: 600;
          text-align: left;
          color: #262626;
          position: absolute;
          bottom: -50px;
          right: 20px;
        }
      }
    }
  }
  .section-6 {
    .section-item {
      margin-top: 50px;
      background: url('../../../../assets/images/solution/soft-hardware/3328/section-6.png')
        center no-repeat !important;
      background-size: contain !important;
      height: 385px;
      width: 100%;
      max-width: 1200px;
      position: relative;
    }
  }
  .section-7 {
    background: url('../../../../assets/images/solution/soft-hardware/3328/section-7-bg.png')
      center no-repeat !important;
    background-size: cover !important;
    width: 100%;
    max-width: 100% !important;
    position: relative;
    .section-title {
      margin-bottom: 40px !important;
    }
    .section-item {
      margin-top: 50px;
      background: url('../../../../assets/images/solution/soft-hardware/3328/section-7.png')
        center no-repeat !important;
      background-size: contain !important;
      height: 415px;
      width: 100%;
      position: relative;
    }
  }

  .section-8 {
    .section-item {
      display: flex;
      flex-direction: row;
      justify-content: space-around;
      align-items: center;
      .item {
        text-align: center;
        height: 200px;
        width: 350px;
        img {
          width: 100px;
          height: 100px;
        }

        p:nth-child(2) {
          margin-top: 30px;
          font-size: 20px;
          font-weight: bold;
          text-align: center;
          color: #262626;
        }

        p:nth-child(3) {
          font-size: 16px;
          font-weight: 400;
          text-align: center;
          color: #666666;
        }
      }
    }
  }

  .section-9 {
    background: url('../../../../assets/images/solution/soft-hardware/7911/section-9-bg.png')
      center no-repeat !important;
    background-size: cover !important;
    width: 100%;
    max-width: 100% !important;
    margin-top: 50px !important;
    position: relative;

    .section-item {
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      background: url('../../../../assets/images/solution/soft-hardware/3328/section-9.png')
        center no-repeat !important;
      background-size: contain !important;
      height: 240px;
      .item {
        padding-top: 50px;
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        grid-gap: 10px;
      }
    }
  }
}
@media screen and (max-width: 750px) {
  .main-content {
    &-banner {
      background: url('../../../../assets/images/solution/soft-hardware/3328/banner_bg.png')
        center no-repeat;
      background-size: cover;
    }
  }
}

.contact-wrap {
  // padding-top: 100px;
  height: 400px;
  text-align: center;
  .title {
    margin-bottom: 16px;
    font-size: 34px;
    color: #333;
    font-weight: bold;
  }
  .desc {
    font-size: 16px;
    color: #666;
    margin-bottom: 50px;
  }
  .apply-btn {
    margin: 60px auto 0;
    background: #1784e9;
    &:hover {
      color: #fff;
    }
  }
}
</style>
