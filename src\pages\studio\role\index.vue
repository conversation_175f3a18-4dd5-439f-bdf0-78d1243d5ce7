<template>
  <div class="index-content">
    <h3 class="index-header">
      <span class="index-title">角色</span>
      <span class="index-dsp">自定义捏人 打造专属于你的角色IP</span>
    </h3>
    <div class="role-header">
      <div
        class="role-header-item"
        :class="roleType === 'role' ? 'role-header-item-active' : ''"
        @click="changeRoleType('role')"
      >
        大模型角色
      </div>
      <div
        class="role-header-item"
        :class="roleType === 'character' ? 'role-header-item-active' : ''"
        @click="changeRoleType('character')"
      >
        设备人设
      </div>
      <div class="line"></div>
      <el-input
        class="search-area"
        :placeholder="`搜索${roleType === 'role' ? '大模型角色' : '人设'}`"
        size="medium"
        v-model="searchVal"
        @keyup.enter.native="handleSearch"
      >
        <i
          slot="prefix"
          class="el-input__icon el-icon-search search-area-btn"
          @click="handleSearch()"
        />
      </el-input>
      <el-button
        icon="ic-r-plus"
        type="primary"
        size="medium"
        style="margin-left: auto"
        @click="create"
      >
        &nbsp;创建{{ roleType === 'character' ? '人设' : '角色' }}
      </el-button>
    </div>
    <el-scrollbar
      v-loading="tableData.loading"
      v-if="roleType === 'role' && tableData.list.length > 0"
    >
      <div class="card-layout">
        <RoleCard
          v-for="(item, index) in tableData.list"
          :key="index"
          :cardData="item"
          :dialog="dialog"
          @change="getRoles"
        />
      </div>
    </el-scrollbar>
    <el-pagination
      v-if="roleType === 'role' && tableData.total > tableData.size"
      ref="pagination"
      :current-page="tableData.page"
      :page-size="tableData.size"
      :total="tableData.total"
      :layout="pageLayout"
      @current-change="pageChange"
      class="txt-al-c pagination_wrap"
    ></el-pagination>

    <os-table
      v-if="roleType === 'character'"
      :tableData="tableData"
      :height="'calc(100vh - 206.67px)'"
      class="characters-table transparent-bgc-role"
      @change="getCharacters"
      @edit="toEdit"
      @copy="copy"
      @del="del"
      @row-click="toEdit"
    >
      <el-table-column prop="name" width="360" label="设备人设">
        <template slot-scope="scope">
          <os-character-simple-item
            class="cp characters-page-character-zh-name"
            :name="scope.row.name || '-'"
            :url="scope.row.image"
            @click.native.stop.prevent="toEdit(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column prop="propertyCount" width="100" label="属性数量">
        <template slot-scope="scope">{{ scope.row.propertyCount }}</template>
      </el-table-column>
      <el-table-column prop="updateTime" width="200" label="更新时间">
        <template slot-scope="scope">
          <div>{{ scope.row.updateTime | date('yyyy-MM-dd hh:mm:ss') }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="useCount" width="100" label="被引用数">
        <template slot-scope="scope">
          <div
            v-if="scope.row.useCount"
            class="text-primary"
            style="cursor: pointer; height: 40px; line-height: 40px"
            @click.stop="openCountDialog(scope.row)"
          >
            {{ scope.row.useCount }}
          </div>
          <span v-else>{{ scope.row.useCount }}</span>
        </template>
      </el-table-column>
    </os-table>
    <empty
      v-if="
        roleType === 'role' && !tableData.loading && tableData.list.length <= 0
      "
      :emptyText="`还没有角色`"
    />
    <create-role-dialog :dialog="dialog" @change="getRoles" />
    <create-character-dialog :dialog="chaDialog" slot="character" />
  </div>
</template>
<script>
import RoleCard from './roleCard.vue'
import CreateRoleDialog from './dialog/createRole.vue'
import CreateCharacterDialog from '@P/studio/handlePlatform/dialog/createCharacter.vue'

import Empty from './empty.vue'
export default {
  name: '',
  components: {
    RoleCard,
    CreateRoleDialog,
    CreateCharacterDialog,
    Empty,
  },
  data() {
    return {
      roleType: '', //role大模型角色 character人设
      searchVal: '',
      tableData: {
        loading: true,
        total: 0,
        page: 1,
        size: 12,
        list: [],
        handles: ['edit', 'copy', 'del'],
        handleColumnText: '操作',
      },
      dialog: {
        show: false,
      },
      chaDialog: {
        type: 'create',
        show: false,
      },
    }
  },
  computed: {
    pageLayout() {
      return this.tableData.total > this.tableData.size
        ? 'prev, pager, next, jumper, total'
        : 'prev, pager, next'
    },
  },
  methods: {
    changeRoleType(type) {
      if (this.roleType === type) return
      this.roleType = type
      this.tableData.list = []
      if (type === 'role') {
        this.getRoles(1)
      } else {
        this.getCharacters(1)
      }
    },
    pageChange(e) {
      this.tableData.page = e
      this.getRoles()
    },
    handleSearch() {
      this.tableData.page = 1
      if (this.roleType === 'role') {
        this.getRoles()
      } else {
        this.getCharacters()
      }
    },
    getRoles(page) {
      let self = this
      this.tableData.loading = true
      this.$utils.httpGet(
        this.$config.api.AIUI_ROLE_LIST,
        {
          pageIndex: page || this.tableData.page,
          pageSize: this.tableData.size,
          search: this.searchVal,
        },
        {
          success: (res) => {
            if (res.data.count <= 0 && !self.searchVal) {
              self.hasItem = false
            } else {
              self.hasItem = true
            }
            self.tableData.list = res.data.list || []
            self.tableData.total = res.data.count
            self.tableData.page = res.data.pageIndex
            self.tableData.size = res.data.pageSize
            self.tableData.loading = false
          },
          error: (err) => {},
        }
      )
    },
    getCharacters(page) {
      let self = this
      this.tableData.loading = true
      this.$utils.httpGet(
        this.$config.api.STUDIO_CHARACTER_LIST,
        {
          pageIndex: page || this.tableData.page,
          pageSize: this.tableData.size,
          search: this.searchVal,
        },
        {
          success: (res) => {
            if (res.data.list && !res.data.list.length && !self.searchVal) {
              self.hasCharacter = false
            } else {
              self.hasCharacter = true
            }
            // self.tableData.list = res.data.list.slice(0,5) //调试用！！！
            self.tableData.list = res.data.list
            self.tableData.total = res.data.count
            self.tableData.page = res.data.pageIndex
            self.tableData.size = res.data.pageSize
            self.tableData.loading = false
          },
          error: (err) => {},
        }
      )
    },
    create() {
      if (this.roleType === 'role') {
        this.dialog = {
          show: true,
        }
      } else {
        this.chaDialog.show = true
      }
    },
    // 编辑
    toEdit(data) {
      let routeData
      // routeData = this.$router.resolve({
      //   name: 'character',
      //   params: { characterId: data.id },
      // })
      // window.open(routeData.href, '_blank')
      this.$router.push({
        name: 'character',
        params: { characterId: data.id },
      })
    },
    copy(data) {
      if (data.name && data.name.length > 28) {
        this.openCharacter('copy', data)
        return
      }
      let params = {
        fromId: data.id,
      }
      this.tableData.loading = true
      let api = this.$config.api.STUDIO_CHARACTER_ADD
      this.$utils.httpPost(api, params, {
        success: (res) => {
          this.tableData.loading = false
          this.$message.success('成功创建副本')
          this.getCharacters()
        },
        error: (err) => {
          this.tableData.loading = false
        },
      })
    },
    // 删除
    del(data) {
      let self = this
      if (data.type === 2) {
        this.$message.warning('默认人设不支持删除')
        return
      }
      if (data.useCount) {
        this.$message.warning(
          '设备人设已被引用，为了您的设备/应用保持正常的语音交互，请取消引用后再删除'
        )
        return
      }
      this.$confirm(
        '删除后不可恢复，请谨慎操作。',
        `要删除设备人设 - ${data.name}？`,
        {
          confirmButtonText: '确定删除',
          cancelButtonText: '取消',
          confirmButtonClass: 'el-button--danger',
          type: 'warning',
          showClose: false,
        }
      )
        .then(() => {
          self.delCharacter(data)
        })
        .catch(() => {})
    },
    delCharacter(data) {
      let params = {
        id: data.id,
      }
      let api = this.$config.api.STUDIO_CHARACTER_DEL
      this.$utils.httpPost(api, params, {
        success: (res) => {
          this.$message.success('删除成功')
          this.getCharacters()
        },
        error: (err) => {
          console.log(err)
        },
      })
    },
  },
  created() {
    if (this.$route.query && this.$route.query.roletype) {
      this.changeRoleType(this.$route.query.roletype)
      this.$router.replace({ query: '' })
    } else {
      this.changeRoleType('role')
    }
  },
}
</script>
<style lang="scss" scoped>
.role-header {
  display: flex;
  align-items: center;
  margin: 12px 0;
}
.search-area {
  width: 200px;
}
.role-header-item {
  background: #ececf1;
  border-radius: 10px;
  padding: 7px 14px;
  color: #3f3f44;
  margin-right: 18px;
  cursor: pointer;
  font-weight: 400;
  font-family: PingFang SC;
  &-active {
    background: #e5f5ff;
    color: #009bff;
    font-weight: 500;
  }
}
.line {
  width: 1px;
  background: #dadada;
  height: 32px;
  margin-right: 26px;
}

.card-layout {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(345px, 1fr));
  gap: 20px;
  transition: all 0.3s;
  padding-bottom: 24px;
}
.pagination_wrap {
  height: 58px;
  margin-top: 24px;
}
.characters-table {
  :deep(.el-table) {
    // .el-table__header-wrapper {
    //   th {
    //     background: #fff;
    //     border-bottom: none;
    //   }
    // }
    td {
      padding: 0;
    }
  }
}
</style>
<style lang="scss">
.index-content {
  background: #f7f8fa;
  padding: 24px;
  height: 100%;
  position: relative;
  overflow: auto;
  display: flex;
  flex-direction: column;
  .index-header {
    display: flex;
    align-items: center;
  }

  .index-title {
    font-size: 20px;
    font-family: PingFang SC, PingFang SC-500;
    font-weight: 600;
    color: #000000;
    margin-right: 10px;
  }

  .index-dsp {
    font-size: 16px;
    font-family: PingFang SC, PingFang SC-400;
    font-weight: 400;
    color: #666666;
  }

  .el-scrollbar {
    flex: 1;
  }
}
</style>
