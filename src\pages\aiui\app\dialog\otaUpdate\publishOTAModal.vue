<template>
  <el-dialog class="dg-body" title="固件发布" :visible.sync="modalParam.show">
    <el-radio-group v-model="radio" style="margin-bottom: 20px">
      <el-radio :label="1">手动更新</el-radio>
      <el-radio :label="2">强制更新</el-radio>
    </el-radio-group>
    <p>发布前请先按照验证流程充分测试设备固件升级，确认全量发布么？</p>
    <div class="modal-btn-container">
      <el-button size="small" @click="closeModal">取消</el-button>
      <el-button size="small" type="primary" @click="handlePublishItem"
        >确定</el-button
      >
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'publishOTAModal',
  props: {
    modalParam: {
      type: Object,
      default: {
        show: Boolean,
        item: Object,
      },
    },
  },
  data() {
    return {
      radio: 2,
    }
  },

  methods: {
    closeModal() {
      this.modalParam.show = false
    },
    handlePublishItem() {
      this.$utils.httpGet(
        this.$config.api.OTA_FORM_SUBMIT,
        {
          appid: this.appId,
          ...this.modalParam.item,
          updateWay: this.radio,
          status: 3,
        },
        {
          success: (res) => {
            if (res.flag) {
              this.modalParam.show = false
              this.$message.success('固件发布成功')
              this.$emit('getList')
            } else {
              this.$message.error(res.desc)
            }
          },
        }
      )
    },
  },

  watch: {
    'modalParam.show': function (val) {
      if (val) {
      }
    },
  },

  computed: {
    appId() {
      return this.$route.params.appId
    },
  },
}
</script>

<style lang="scss" scoped>
.modal-btn-container {
  display: flex;
  justify-content: flex-end;
  padding: 30px 0;
  width: 100%;
}
</style>
