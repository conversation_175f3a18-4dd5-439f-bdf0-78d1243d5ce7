<template>
  <div class="main-content">
    <section class="main-content-banner">
      <div class="banner-right-image"></div>
      <div class="banner-text">
        <h2>软硬件一体化</h2>
        <p class="banner-text-content">
          为满足客户快速集成AI能力，即插即用
          AIUI平台打造多款软硬件一体产品，满足录音、降噪、交互等多种需求
        </p>
        <!-- <div class="banner-text-button" @click="toConsole">合作咨询</div> -->
      </div>
    </section>
    <section class="section section-2">
      <div class="section-title">
        <i class="arrow arrow-left"></i>
        <span>产品介绍</span>
        <i class="arrow arrow-right"></i>
      </div>
      <ul class="solutions">
        <!-- <li class="solution-child">
          <div class="solution-image solution-image-0"></div>

          <div class="whole">
            <h1>讯飞智能台历企业定制</h1>
            <h2>能听会说的桌面小帮手，外观时尚、轻巧易用</h2>
            <div class="text-area">
              <p>
                - 具备蓝牙音箱、小夜灯、助眠仪、香薰机、闹钟、万年
                历等功能，适用于家庭床头与办公桌面
              </p>
              <p>
                - 面向酒店、金融、保险、医疗、公益等行业的生活、服
                务、礼品等场景提供定制服务
              </p>
            </div>
            <div class="learn-more" @click="toCalendar">合作咨询 ></div>
          </div>
        </li> -->
        <li class="solution-child">
          <div class="solution-image solution-image-1"></div>
          <!-- <p>AIUI USB声卡开发套件</p>
          <p>录音回采二合一，配合麦克风阵列算法助力客户快速集成前端声学能力</p> -->
          <div class="whole">
            <h1>AIUI USB声卡开发套件</h1>
            <h2>
              录音回采二合一，配合麦克风阵列算法助力客户快速集成前端声学能力
            </h2>
            <div class="text-area">
              <p>- 用于外部音频采集，内部喇叭信号回采</p>
              <p>- 兼容线性2、4、6麦及环形6麦阵列</p>
              <p>- USB口输出数字音频</p>
            </div>
            <div class="learn-more" @click="toUSB">合作咨询 ></div>
          </div>
        </li>
        <!-- <li class="solution-child">
          <div class="solution-image solution-image-2"></div>
          <div class="whole">
            <h1>AIUI R818 麦克阵列开发套件</h1>
            <h2>内置全栈前端声学技术及离线命令，即插即用</h2>
            <div class="text-area">
              <p>- 外部录音，喇叭信号回采</p>
              <p>- 内置语音唤醒、降噪算法及离线命令</p>
              <p>- 适配线性2、4、6麦及环形6麦阵列</p>
              <p>- USB口输出降噪音频</p>
              <p>- 上位机串口通信</p>
            </div>
            <div class="learn-more" @click="toR818">合作咨询 ></div>
          </div>
        </li> -->
        <!-- <li class="solution-child">
          <div class="solution-image solution-image-3"></div>
          <p>AIUI RK3328 语音交互开发套件</p>
          <p>括载AIUI全链路语音交互能力，方便客户快速熟悉平台，完成方案验证</p>
          <div class="whole">
            <h1>AIUI RK3328 语音交互开发套件</h1>
            <div class="text-area">
              <p>
                集成前端声学能力及硬件，接入AIUI人机对话平台，实现唤醒、降噪及语音全链路交互能力。
              </p>
              <p>通过该开发套件可快速了解AIUI各项能力及方案验证。</p>
            </div>
            <div class="learn-more" @click="toConsole('12')">合作咨询 ></div>
          </div>
        </li> -->
        <li class="solution-child">
          <div class="solution-image solution-image-4"></div>
          <!-- <p>AIUI RK3399 多模态交互开发盒子</p>
          <p>内置讯飞多模态能力，适用公共场所人机交互</p> -->
          <div class="whole">
            <h1>AIUI RK3399 PRO 多模态交互开发盒子</h1>
            <h2>内置讯飞多模态能力，适用公共场所人机交互</h2>
            <div class="text-area">
              <p>- 高性能配置、尺寸小巧</p>
              <p>- 兼容线性2、4、6、8麦及环形6麦阵列</p>
              <p>- 接口类型丰富</p>
              <p>- 内部集成前端声学算法、多模态能力、全链路语音交互</p>
            </div>
            <div class="learn-more" @click="toConsole('13')">合作咨询 ></div>
          </div>
        </li>
        <!-- <li class="solution-child">
          <div class="solution-image solution-image-5"></div>
         
          <div class="whole">
            <h1>AIUI PAGER 智慧大屏演示器</h1>
            <h2>手持演示器，让大屏交互更自然</h2>
            <div class="text-area">
              <p>-支持翻页/激光/聚光灯</p>
              <p>-双麦克风搭配降噪增益算法，实时转写语音</p>
              <p>-语音指令，智能操控</p>
              <p>-支持语音交互定制</p>
            </div>

            <div class="learn-more" @click="toConsole('14')">合作咨询 ></div>
          </div>
        </li> -->
        <li class="solution-child">
          <div class="solution-image solution-image-6"></div>

          <div class="whole">
            <h1>AIUI AC7911 语音交互开发套件</h1>
            <h2>
              支持离在线语音交互及AIUI
              官方技能，提供简单友好的客制化平台开发工具
            </h2>
            <div class="text-area">
              <p>-200条以内离线控制指令</p>
              <p>-语音+WIFI+蓝牙三合一</p>
              <p>-串口通信</p>
              <p>-核心模组尺寸：17mm x 18mm x 2.5mm</p>
              <p>-适用场景：智能家电、智能音箱等品类</p>
            </div>

            <div class="learn-more" @click="toConsole('21')">合作咨询 ></div>
          </div>
        </li>
        <li class="solution-child">
          <div class="solution-image solution-image-7"></div>

          <div class="whole">
            <h1>AIUI ZG801 语音交互开发套件</h1>
            <h2>支持多语种离线控制，提供简单友好的客制化平台开发工具</h2>
            <div class="text-area">
              <p>-30条以内离线控制指令</p>
              <p>-支持中、英、日、韩等多语种控制</p>
              <p>-支持多语种发音人</p>
              <p>-串口通信</p>
              <p>-尺寸：17mm x 18.5mm x 2.5mm</p>
              <p>-适用场景：智能家电、智能穿戴等品类</p>
            </div>

            <div class="learn-more" @click="toConsole('22')">合作咨询 ></div>
          </div>
        </li>
        <li class="solution-child">
          <div class="solution-image solution-image-8"></div>

          <div class="whole">
            <h1>AIUI RK3328 语音交互开发套件</h1>
            <h2>
              搭载AIUI全链路语音交互能力，方便客户快速熟悉平台，完成方案评估和验证
            </h2>
            <div class="text-area">
              <p>-支持线阵4麦或者环阵6麦</p>
              <p>-对外提供WIFI、HDMI、RJ45等丰富的接口</p>
              <p>-提供丰富技能以及海量的内容资源</p>
              <p>-尺寸：150mm x 96.2mm x 1.6mm</p>
              <p>-适用场景：智能机器人、智能家居等品类</p>
            </div>

            <div class="learn-more" @click="toConsole('12')">合作咨询 ></div>
          </div>
        </li>
      </ul>
    </section>
    <!-- <section class="section section-4">
      <div class="section-title">
        <p>合作咨询</p>
        <p>提交信息，我们会尽快与你联系</p>
      </div>
      <div class="section-item">
        <div class="section-button" @click="toConsole">申请合作</div>
      </div>
    </section> -->
  </div>
</template>

<script>
export default {
  name: 'smart-hardware',
  data() {
    return {}
  },
  methods: {
    // toConsole(type) {
    //   window.open(`/solution/apply/${type}`)
    // },
    toCalendar() {
      window.open(`https://www.aifuwus.com/onstage/cmddetail?product_type=3984`)
    },
    toConsole(type) {
      // 判断是否有百度SEM营销参数，有的话，提交表单页需要带上
      const search = window.location.search
      if (search) {
        window.open(`/solution/apply/${type}${search}`)
      } else {
        window.open(`/solution/apply/${type}`)
      }
    },
    toR818() {
      window.open(`https://www.aifuwus.com/onstage/cmddetail?id=2998`)
    },
    toUSB() {
      window.open(`https://www.aifuwus.com/onstage/cmddetail?id=3062`)
    },
  },
}
</script>

<style lang="scss" scoped>
.main-content {
  &-banner {
    background: url(~@A/images/solution/soft-hardware/banner.png) center
      no-repeat;
    background-size: cover;
    height: 626px;
    overflow: hidden;
    width: 100%;
    // display: flex;
    // align-items: center;
    .banner-text {
      max-width: 1200px;
      color: #fff;
      // height: 100%;
      margin: auto;
      &-button {
        font-size: 18px;
        text-align: center;
        font-weight: 500;
        width: 240px;
        height: 52px;
        line-height: 52px;
        letter-spacing: 1px;
        border: 1px solid #fff;
        border-radius: 1px;
        color: #fff;
        cursor: pointer;
        transition: 0.6s;
        margin-left: 20px;
        &:hover {
          color: #002985;
          background: #fff;
          transition: 0.3s;
        }
      }
      h2 {
        color: #fff;
        margin-bottom: 29px;
        font-size: 39px;
        font-weight: 500;
        line-height: 59px;
        padding-left: 20px;
      }
      p {
        font-size: 14px;
        line-height: 24px;
      }

      .banner-text-content {
        padding: 20px;
        font-size: 18px;
        font-weight: 400;
        color: rgba(255, 255, 255, 0.86);
        line-height: 28px;
      }
    }
    .banner-right-image {
      margin: 80px auto 0;
      width: 328px;
      height: 202px;
      background: url(~@A/images/solution/soft-hardware/pic_ryj.png) center
        no-repeat;
      background-size: contain;
    }
  }

  .section {
    p,
    ul {
      margin-bottom: 0;
    }
    max-width: 1200px;
    overflow: hidden;
    margin: 0 auto;
    .section-title {
      text-align: center;
      font-size: 35px;
      font-family: SourceHanSansSC-Medium, SourceHanSansSC;
      font-weight: 500;
      color: #444444;
      line-height: 53px;
      .arrow {
        width: 25px;
        height: 23px;
        display: inline-block;
        background-repeat: no-repeat;
        background-size: contain;
      }
      .arrow-left {
        background-position: left;
        background-image: url(~@A/images/solution/smart-hardware/arrow-left.png);
      }
      .arrow-right {
        background-position: right;
        background-image: url(~@A/images/solution/smart-hardware/arrow-right.png);
      }
    }
    .section-title-spec {
      font-size: 30px;
      font-weight: 500;
      color: #656565;
      line-height: 42px;
    }
    .section-tabs {
      margin-top: 70px;
    }
    .section-sub-title {
      text-align: center;
      font-size: 18px;
      font-weight: 400;
      color: #777777;
      line-height: 25px;
      margin-top: 42px;
    }
    .section-sub-title-spec {
      font-size: 17px;
      font-weight: 400;
      color: #999999;
      line-height: 28px;
      margin-top: 18px;
    }
  }

  .section-2 {
    max-width: 1300px;
    margin-top: 78px;
    .solutions {
      display: flex;
      flex-flow: wrap;
      flex-direction: row;
      justify-content: center;
      margin: 67px auto;

      li + li {
        margin-top: 30px;
      }
      li:not(:last-child) {
        border: 1px solid #ebebeb;
        border-radius: 4px;
      }

      li {
        // margin: 30PX;
        width: 379px;
        height: 380px;
        // border: 1PX solid #ebebeb;
        text-align: center;
        background-size: contain;
        position: relative;
        .whole {
          width: 100%;
          height: 100%;
          position: absolute;
          z-index: 10;
          left: 0;
          top: 0;
          // background: rgba(1, 17, 72, 0.39);
          background: #1784e9;
          opacity: 0.8;
          box-shadow: 6px 7px 16px 0px rgba(242, 242, 242, 0.5);
          transition: all 0.5s ease;
          padding-top: 30px;
          h1 {
            font-size: 20px;
            font-weight: 600;
            color: #ffffff;
            line-height: 28px;
          }
          h2 {
            font-size: 16px;
            font-weight: 400;
            color: #ffffff;
            line-height: 24px;
          }
          .text-area {
            font-size: 14px;
            color: #ffffff;
            line-height: 30px;
            padding: 0 40px;
            text-align: left;
            margin-top: 20px;
          }
          .learn-more {
            width: 318px;
            height: 50px;
            background: #ffffff;
            border-radius: 100px;
            border: 1px solid #ffffff;
            text-align: center;
            line-height: 50px;
            cursor: pointer;
            position: absolute;
            bottom: 10px;
            left: 50%;
            transform: translateX(-50%);
          }
        }
        &:hover {
          .whole {
            display: block;
          }
          > p {
            display: none;
          }
          .solution-image {
            margin-top: 40px;
          }
        }

        > p {
          display: none;
        }

        > p:nth-of-type(1) {
          font-size: 16px;
          font-weight: 500;
          color: #1a1a1a;
          line-height: 22px;
        }
        > p:nth-of-type(2) {
          margin: top 12px;
          height: 44px;
          font-size: 14px;
          font-weight: 400;
          color: #777777;
          line-height: 22px;
          padding: 0 25px;
        }
        .solution-image {
          width: 379px;
          height: 208px;
          background-size: cover;
          top: 50%;
          left: 50%;
          position: absolute;
          transform: translate(-50%, -50%);
        }
        .solution-image-0 {
          background: url(~@A/images/solution/soft-hardware/img_taili.png)
            center no-repeat;
        }
        .solution-image-1 {
          background: url(~@A/images/solution/soft-hardware/pic_1.png) center
            no-repeat;
        }
        .solution-image-2 {
          background: url(~@A/images/solution/soft-hardware/pic_2.png) center
            no-repeat;
        }
        .solution-image-3 {
          background: url(~@A/images/solution/soft-hardware/pic_3.png) center
            no-repeat;
        }
        .solution-image-4 {
          background: url(~@A/images/solution/soft-hardware/pic_4.png) center
            no-repeat;
          background-size: contain !important;
        }
        .solution-image-5 {
          background: url(~@A/images/solution/soft-hardware/pic_5.png) center
            no-repeat;
        }

        .solution-image-6 {
          background: url(~@A/images/solution/soft-hardware/img_product_7911.png)
            center no-repeat;
          width: 329px;
          height: 340px;
        }
        .solution-image-7 {
          background: url(~@A/images/solution/soft-hardware/img_product_zg801.png)
            center no-repeat;
          width: 340px;
          height: 170px;
        }
        .solution-image-8 {
          background: url(~@A/images/solution/soft-hardware/img_product_rk23328.png)
            center no-repeat;
          width: 340px;
          height: 230px;
        }
      }
    }
  }

  .section-4 {
    margin-top: 109px;
    padding-bottom: 129px;
    text-align: center;
    p {
      margin-bottom: 0;
    }
    .section-title {
      p:first-child {
        font-size: 28px;
        font-weight: 400;
        color: #444444;
        line-height: 40px;
      }
      p:last-child {
        font-size: 16px;
        font-weight: 400;
        color: #777777;
        line-height: 22px;
        margin-top: 18px;
      }
    }
    .section-item {
      margin-top: 49px;
      .section-button {
        color: #fff;
        background: #1784e9;
        width: 195px;
        height: 50px;
        line-height: 50px;
        text-align: center;
        margin: 0 auto;
        cursor: pointer;
      }
    }
  }
}
</style>
