<template>
  <el-popover
    placement="bottom-start"
    width="240"
    trigger="click"
    v-model="visible"
  >
    <div class="give-up-save-title">
      <i class="ic-r-exclamation" />
      <span>确定放弃修改吗？</span>
    </div>
    <p class="give-up-save-content">所修改的内容将会丢失。</p>
    <div style="text-align: right; margin: 0">
      <el-button size="mini" style="min-width: 64px;" @click="visible = false">取消</el-button>
      <el-button type="danger" size="mini" style="min-width: 84px;" @click="noSave">放弃修改</el-button>
    </div>

    <el-button class="btn-give-up" slot="reference" type="text" :disabled="!edited">放弃修改</el-button>
  </el-popover>

</template>

<script>
export default {
  name: 'OsGiveUpSave',
  props: {
    edited: {
      type: Boolean,
      default: true
    }
  },
  data () {
    return {
      visible: false
    }
  },
  computed: {

  },
  mounted() {

  },
  methods: {
    noSave () {
      this.visible = false
      this.$emit('noSave')
    }
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="scss" scoped>
.give-up-save-title {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  i {
    font-size: 16px;
    color: $warning;
  }
  span {
    margin-left: 8px;
    font-size: 16px;
    font-weight: 500;
  }
}
.give-up-save-content {
  padding-left: 24px;
  margin-bottom: 16px;
}
.btn-give-up {
  color: $primary;
  &:hover{
    color: $primary;
  }
}
</style>

