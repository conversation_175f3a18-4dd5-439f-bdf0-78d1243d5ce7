<template>
  <os-collapse
    v-if="tableData.total"
    :default="true"
    title="嵌套自定义修饰语"
  >
    <os-table
      class="slot-table"
      :border="true"
      :tableData="tableData"
      ref="auxiliarySlotTable">
      <el-table-column
        label="修饰语名称"
        prop="name"
        width="280">
        <template slot-scope="scope">
          <el-tooltip
            v-if="scope.row.source"
            effect="dark" content="源技能的修饰语不可编辑" placement="top">
            <span style="color: #8C8C8C">{{scope.row.name}}</span>
          </el-tooltip>
          <span v-else>{{scope.row.name}}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="fragments"
        label="修饰语内容">
        <div slot-scope="scope" class="fragments-wrap">
          <el-popover
            v-if="scope.row.fragments && scope.row.fragments.length"
            placement="bottom-start"
            trigger="hover"
            >
            <el-scrollbar style="padding-right: 12px; height: 232px;">
              <p class="modifier-popover-line" v-for="(item, index) of scope.row.fragments" :key="index">
                {{item}}
              </p>
            </el-scrollbar>
            <template slot="reference">
              <span>{{scope.row.fragments[0]}}</span>
              <div class="intent-tag ib">{{scope.row.fragments.length}}</div>
            </template>
          </el-popover>
          <span v-else style="color: #8c8c8c">-</span>
        </div>
      </el-table-column>
    </os-table>
  </os-collapse>
</template>

<script>
  export default {
    name: 'modifier-in-modifier',
    props: {
      businessId: String,
      dialog: {
        type: Object,
        required: true,
        default: () => ({ 
          show: false,
          modifierId: ''
        })
      }
    },
    data(){
      return {
        tableData: {
          loading: true,
          total: 0,
          page: 1,
          size: 5,
          list: []
        }
      }
    },
    created(){
      this.getData()
    },
    methods: {
      getData(){
        let self = this
        if(!self.dialog.modifierId) return
        self.tableData.loading = true
        let api = self.$config.api.STUDIO_MODIFIER_USED_BY_MODIFIER
        this.$utils.httpPost(api, {
          businessId: self.businessId,
          modifierId: self.dialog.modifierId
        }, {
          success: res => {
            this.tableData.loading = false
            this.tableData.list = res.data || []
            this.tableData.total = this.tableData.list.length
          },
          error: err => {
            this.tableData.loading = false
          }
        })
      },
      refreshData(){
        this.getData()
        this.$emit('change')
      }
    }
  }
</script>

<style lang="scss" scoped>

</style>