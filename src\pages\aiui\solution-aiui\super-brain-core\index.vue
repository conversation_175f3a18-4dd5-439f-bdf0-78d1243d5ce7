<template>
  <div class="main_content">
    <section class="main_content_banner">
      <div class="banner_text_wrapper">
        <div class="banner_text">
          <div class="banner_text_title">超脑核心板</div>
          <div class="info">
            基于Rockchip的芯片RK3588S平台的嵌入式系统开发板
          </div>
        </div>
        <div class="banner_text_button_wrap">
          <div class="button primary_button" @click="toConsole">合作咨询</div>
          <!-- <div class="button text_button" plain @click="toBuy">立即购买</div> -->
        </div>
      </div>
    </section>

    <div class="section section1">
      <div class="title section_title">应用场景</div>
      <div class="section_content">
        <div
          class="content_item"
          v-for="(item, index) in applicationScenarios"
          :key="index"
        >
          <img :src="item.imgSrc" :alt="item.title" />
          <div class="item_title">{{ item.title }}</div>
        </div>
      </div>
    </div>

    <div class="product_features_wrap">
      <div class="product_features">
        <h2 class="section_title">产品特点</h2>

        <div class="card_wrapper">
          <div class="model_card">
            <h3>超大内存</h3>
            <ul>
              <li>
                <img
                  src="@/assets/images/solution/super-brain-core/product-features-icon.png"
                  alt=""
                />
                <span>最大可配32G内存</span>
              </li>
            </ul>
            <img
              src="@/assets/images/solution/super-brain-core/product-features-01.png"
              alt=""
            />
          </div>

          <div class="model_card">
            <h3>丰富接口 可扩展强</h3>
            <ul>
              <li>
                <img
                  src="@/assets/images/solution/super-brain-core/product-features-icon.png"
                  alt=""
                />
                <span>集成ARM MALI-G610 MP4核GPU</span>
              </li>
              <li style="padding-right: 47px">
                <img
                  src="@/assets/images/solution/super-brain-core/product-features-icon.png"
                  alt=""
                />
                <span>内置NPU，可提供6TOPS算力</span>
              </li>
            </ul>
            <img
              src="@/assets/images/solution/super-brain-core/product-features-02.png"
              alt=""
            />
          </div>
        </div>
      </div>
    </div>

    <div class="chip_integration">
      <div class="title section_title">芯片集成框图</div>
      <div class="chip_integration_content"></div>
    </div>

    <!-- 小系统接口图 -->
    <div class="system_interface">
      <div class="title section_title">小系统接口图</div>
      <div class="system_interface_content">
        <img
          src="@/assets/images/solution/super-brain-core/system-interface.png"
          alt=""
        />
      </div>
    </div>

    <!-- 硬件规格 -->
    <div class="hardware_specs_wrap">
      <div class="hardware_specs">
        <div class="hardware_specs_card">
          <h2>硬件规格</h2>
          <div class="table_container">
            <el-table
              :data="coreParameters"
              style="width: 100%"
              :span-method="tableSpanMethod"
            >
              <el-table-column
                prop="parameter"
                label="核心参数"
                width="180"
              ></el-table-column>
              <el-table-column
                prop="classification"
                label=""
                width="190"
              ></el-table-column>
              <el-table-column prop="specification" label=""></el-table-column>
            </el-table>

            <el-table
              :data="hardwareFeatures"
              style="width: 100%; margin-top: 30px"
            >
              <el-table-column
                prop="parameter"
                label="硬件特性"
                width="180"
              ></el-table-column>
              <el-table-column
                prop="classification"
                label=""
                width="190"
              ></el-table-column>
              <el-table-column prop="specification" label=""></el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </div>

    <!-- 合作咨询 -->
    <div class="cooperation_consulting_wrapper">
      <div class="banner-text">
        <h2>立即联系您的专属顾问</h2>
        <p class="banner_text_content">
          免费咨询专属顾问 为您量身定制产品推荐方案<br />
        </p>
        <div class="banner-text-button" @click="toConsole">合作咨询</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AiuiWebChildToys',

  data() {
    return {
      applicationScenarios: [
        {
          title: '边缘计算',
          imgSrc: require('@/assets/images/solution/super-brain-core/application-scenarios-01.jpg'),
        },
        {
          title: '人工智能',
          imgSrc: require('@/assets/images/solution/super-brain-core/application-scenarios-02.jpg'),
        },
        {
          title: '云计算',
          imgSrc: require('@/assets/images/solution/super-brain-core/application-scenarios-03.jpg'),
        },
        {
          title: '虚拟/增强现实',
          imgSrc: require('@/assets/images/solution/super-brain-core/application-scenarios-04.jpg'),
        },
      ],
      microphoneTypes: [
        {
          title: '线性2麦',
          imgSrc: require('@/assets/images/solution/offline-interaction/microphone-type1.png'),
          descriptions: ['专为近场交互优化', '适用于桌面级设备'],
        },
        {
          title: '线性4麦',
          imgSrc: require('@/assets/images/solution/offline-interaction/microphone-type2.png'),
          descriptions: ['前向增强拾音', '适配高度>150cm设备'],
        },
        {
          title: '环形6麦',
          imgSrc: require('@/assets/images/solution/offline-interaction/microphone-type3.png'),
          descriptions: ['360°全向收音', '适配高度<150cm设备'],
        },
      ],
      coreParameters: [
        {
          parameter: '处理器',
          classification: 'CPU',
          specification:
            '八核64位（4xCortex-A76+4xCortex-A55），8nm 先进工艺，主频高达2.4GHz',
        },
        {
          parameter: '处理器',
          classification: 'CPU',
          specification:
            'ARM Mali-G610 MP4四核GPU支持 OpenGL ES3.2 / OpenCL2.2 / Vulkan1.1,450 GFLOPS',
        },
        {
          parameter: '处理器',
          classification: 'NPU',
          specification:
            'NPU算力高达6TOPS，支持INT4/INT8/INT16混合运算，可实现基于TensorFlow / MXNet / PyTorch / Caffe等系列框架的网络模型转换',
        },
        {
          parameter: '内存',
          classification: `LPDDR4/LPD-DR4X/LPDDR5`,
          specification: '4通道外部存储器接口（LPDDR4/LPDDR4X/LPDDR5）',
        },
        {
          parameter: '存储',
          classification: 'EMMC',
          specification: '16GB/32GB/64GB/128GB eMMC',
        },

        {
          parameter: '存储扩展',
          classification: 'SSD',
          specification:
            '1 x M.2接口，可扩展2242 SATA3.0 SSD（默认），兼容2242 PCle2.0 NVMe SSD1 x TF Card',
        },
        {
          parameter: '图像处理',
          classification: 'EMMC',
          specification: '集成48MP ISP with HDR&3DNR',
        },
        {
          parameter: '图像处理',
          classification: '编解码',
          specification:
            '视频解码：8K@60fps H.265/VP9/AVS28K@30fps H.264 AVC/MVC4K@60fps AV11080P@60fps MPEG-2/-1/VC-1/VP8 视频编码：8K@30fps编码，支持H.265 / H.264*最高可实现32路',
        },
      ],
      hardwareFeatures: [
        {
          parameter: '资源接口',
          classification: '核心板引出全部外设资源',
          specification:
            'I2S*2，PCIE2.0*2，USB2.0 HOST*2，USB3.0*1，SDIO*1，SPI*2，RGMII*1，I2C*4，UART*7，MIPI*6',
        },
        {
          parameter: '系统参数',
          classification: '编解码',
          specification:
            '视频解码：8K@60fps H.265/VP9/AVS28K@30fps H.264 AVC/MVC4K@60fps AV11080P@60fps MPEG-2/-1/VC-1/VP8 视频编码：8K@30fps编码，支持H.265 / H.264*最高可实现32路',
        },
      ],
      headerStyle: {
        background: 'linear-gradient(85deg,#569cfe 0%, #227eff 100%)',
        color: '#fff',
        height: '60px',
        fontSize: '16px',
        fontWeight: 'bold',
      },
    }
  },

  mounted() {},

  methods: {
    toConsole() {
      // 判断是否有百度SEM营销参数，有的话，提交表单页需要带上
      const search = window.location.search
      if (search) {
        window.open(`/solution/apply/47${search}`)
      } else {
        window.open('/solution/apply/47')
      }
    },

    toBuy() {
      window.open('https://www.aifuwus.com/onstage/cmddetail?id=4434')
    },
    tableSpanMethod({ row, column, rowIndex, columnIndex }) {
      // 只处理第一列（parameter）
      if (columnIndex === 0) {
        if (rowIndex === 0) {
          // 第1行合并3行
          return [3, 1]
        } else if (rowIndex === 1 || rowIndex === 2) {
          // 第2、3行隐藏
          return [0, 0]
        }
        // 合并最后两行
        if (rowIndex === this.coreParameters.length - 2) {
          // 倒数第2行合并2行
          return [2, 1]
        } else if (rowIndex === this.coreParameters.length - 1) {
          // 最后一行隐藏
          return [0, 0]
        }
      }
      // 其他单元格正常显示
      return [1, 1]
    },
  },
}
</script>

<style lang="scss" scoped>
.main_content {
  background-color: #fff;
  .main_content_banner {
    background: url(~@A/images/solution/super-brain-core/banner.jpg) center
      no-repeat;
    background-size: cover;
    height: 500px;
    overflow: hidden;
    width: 100%;
  }

  .banner_text_wrapper {
    max-width: 1200px;
    color: #fff;
    height: 100%;
    margin: auto;

    .banner_text {
      width: 500px;
      margin: 137px 0 35px 0;
      color: #000000;
      .banner_text_title {
        font-size: 44px;
        letter-spacing: 1px;
        font-weight: 600;
      }
      .info {
        font-size: 18px;
        margin-top: 20px;
        width: 552px;
        height: 100px;
        line-height: 100px;
        transform: translateX(-66px);
        padding-left: 66px;
        backdrop-filter: blur(1px);
        border-radius: 50px;
      }
    }
    .banner_text_button_wrap {
      display: flex;
      gap: 30px;
      .button {
        font-size: 18px;
        text-align: center;
        font-weight: 400;
        width: 183px;
        height: 60px;
        line-height: 60px;
        border-radius: 8px;

        cursor: pointer;
        letter-spacing: 1px;
        &.primary_button {
          background: #1d69ff;
          color: #fff;
        }
        &.text_button {
          border: 1.5px solid #1d69ff;
          color: #1d69ff;
          font-weight: 600;
        }
      }
    }
  }

  .section_title {
    font-size: 34px;
    color: #000000;
  }
  .section {
    padding: 50px 0 128px;
    text-align: center;
    .title {
      margin-bottom: 50px;
    }
    .section_content {
      display: flex;
      justify-content: center;
      gap: 20px;
      flex-wrap: wrap;
    }
    .content_item {
      position: relative;
      img {
        width: 286px;
        height: 434x;
        object-fit: cover;
        display: block;
      }

      .item_title {
        position: absolute;
        top: 10%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 100%;
        font-size: 20px;
        font-weight: 600;
        color: #ffffff;
      }
    }
  }

  .product_features_wrap {
    text-align: center;
    background-color: #e6edf6;

    .product_features {
      background: url(~@A/images/solution/super-brain-core/product-features-bg.png)
        center no-repeat;
      background-size: cover;
      padding-bottom: 92.5px;
      h2 {
        padding: 60px 0 40px 0;
      }
      .card_wrapper {
        display: flex;
        justify-content: center;
        gap: 20px;
        .model_card {
          background: linear-gradient(180deg, #e7ecf2, #ffffff 100%);
          border: 2px solid #ffffff;
          border-radius: 20px;
          box-shadow: 0px 50px 100px 0px rgba(16, 38, 93, 0.2);
          padding: 50px 43px 55px 43px;
          width: 590px;

          h3 {
            font-weight: 600;
            margin-bottom: 30px;
            font-size: 28px;
            font-family: PingFang SC, PingFang SC-600;
            color: #1d1d1d;
          }

          ul {
            height: 80px;
            li {
              margin-bottom: 10px;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 20px;
              font-family: Alibaba PuHuiTi, Alibaba PuHuiTi-400;
              color: #2a2a2a;
              line-height: 22px;
              img {
                width: 20px;
                height: 21px;
                margin-right: 8px;
              }
            }
          }
          img {
            width: 494px;
            height: 255px;
          }
        }
      }
    }
  }

  .chip_integration {
    background: linear-gradient(180deg, #dce9fc, #f3f5f9 100%);
    padding-bottom: 100px;
    .title {
      padding: 60px 0 40px 0;
      text-align: center;
    }
    .chip_integration_content {
      width: 1200px;
      height: 1185px;
      background: url(~@A/images/solution/super-brain-core/chip-integration.png)
        center no-repeat;
      background-size: cover;
      margin: auto;
    }
  }

  .system_interface {
    background: linear-gradient(182deg, #dce9fc 0%, #f3f5f9 100%);
    padding-bottom: 88px;
    .title {
      padding: 93px 0 40px 0;
      text-align: center;
    }
    .system_interface_content {
      width: 1200px;
      margin: auto;
      background: rgba(255, 255, 255, 0.5);
      border: 2px solid #ffffff;
      border-radius: 30px;
      padding: 40px 90px;
      img {
        width: 100%;
      }
    }
  }
  .hardware_specs_wrap {
    text-align: center;
    background-color: #e6edf6;
    .hardware_specs {
      padding-bottom: 100px;
      background: url(~@A/images/solution/super-brain-core/hardware-parameters-bg.png)
        center no-repeat;
      background-size: cover;

      .hardware_specs_card {
        width: 1200px;
        margin: auto;
        border-radius: 20px;

        h2 {
          text-align: center;
          padding: 90px 0 75px 0;
          font-size: 52px;
          font-family: Alibaba PuHuiTi, Alibaba PuHuiTi-500;
          font-weight: 500;
          color: #1e1e1e;
        }

        .table_container {
          :deep(.el-table) {
            overflow: hidden;
            border-radius: 20px;
            border: 1px solid #d7dfee;

            .el-table__header-wrapper {
              height: 60px !important;
              table {
                thead {
                  tr {
                    background: linear-gradient(0deg, #569cfe 0%, #227eff 100%);
                    th {
                      background: none;
                      padding: 19px 0;
                      .cell {
                        font-size: 20px;
                        color: #ffffff;
                        padding: 0 40px;
                      }
                    }
                  }
                }
              }
            }

            .el-table__body-wrapper {
              .el-table__body {
                tbody {
                  tr {
                    pointer-events: none !important;
                    td {
                      pointer-events: none !important;
                      border-right: 1px solid #d7dfee;
                      border-color: #d7dfee;
                      .cell {
                        font-size: 18px;
                        color: #36363b;
                        padding: 0 30px;
                        line-height: 30px;
                      }

                      &:last-child {
                        border-right: none;
                        .cell {
                          padding: 0 20px;
                        }
                      }
                    }
                    &:last-child {
                      td {
                        border-bottom: none;
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

.cooperation_consulting_wrapper {
  background: url(~@A/images/solution/child-education/img_guwenBG.png) center
    no-repeat;
  background-size: cover;
  height: 300px;
  overflow: hidden;
  width: 100%;
  .banner-text {
    max-width: 1200px;
    color: #fff;
    height: 100%;
    margin: auto;
    &-button {
      font-size: 16px;
      text-align: center;
      font-weight: 400;
      width: 140px;
      height: 40px;
      line-height: 40px;
      background: #1d69ff;
      border-radius: 4px;
      color: #fff;
      cursor: pointer;
    }
    h2 {
      color: #181818;
      padding-top: 50px;
      margin-bottom: 20px;
      font-size: 36px;
      font-weight: 400;
      line-height: 48px;
    }
    p {
      font-size: 18px;
      margin-bottom: 50px;
    }

    .banner_text_content {
      width: 570px;
      font-size: 16px;
      font-family: SourceHanSansSC-Regular, SourceHanSansSC;
      font-weight: 400;
      color: #444444;
      line-height: 30px;
    }
  }
}
</style>
