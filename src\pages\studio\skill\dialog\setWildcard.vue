<template>
  <div
    class="
      set-wild-card-popover
      el-popover el-popper
      el-popover--plain
      wild-select-popover
      editor-select-popover
    "
    v-if="variablePopover.show"
    v-clickoutside="closePopover"
    v-scrollcb="handleScroll"
    :style="popperStyle"
    visible-arrow="false"
    :x-placement="showTop ? 'top-start' : 'bottom-start'"
  >
    <p class="top-tip">
      通配实体的使用会影响其他技能及意图的使用效果，请谨慎使用。
    </p>
    <p class="mgb16">
      你为{{ '{' + slotName + '}' }}添加了通配实体，请选择通配实体的范围。
    </p>
    <div x-arrow="" class="popper__arrow" style="left: 13.5px"></div>
    <el-form
      class="wildcard-form"
      ref="wildcardForm"
      label-position="top"
      inline
    >
      <el-form-item
        style="margin-right: 14px"
        label="最小（字数）"
        class="is-required"
      >
        <el-input-number
          size="small"
          v-model="minNum"
          controls-position="right"
          :min="1"
          :max="20"
          :step="1"
          :precision="0"
        />
      </el-form-item>
      <el-form-item label="最大（字数）" class="is-required">
        <el-input-number
          size="small"
          v-model="maxNum"
          controls-position="right"
          :min="1"
          :max="99"
          :step="1"
          :precision="0"
        />
      </el-form-item>
    </el-form>
    <div class="btn-wrap">
      <el-button size="small" class="dialog-btn" @click="cancel">
        取消
      </el-button>
      <el-button
        size="small"
        class="dialog-btn"
        type="primary"
        @click="save"
        :loading="saving"
      >
        {{ saving ? '保存中...' : '确定' }}
      </el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'set-wild-card-popover',
  props: {
    variablePopover: {
      type: Object,
      default: () => ({
        show: false,
        data: {},
      }),
    },
  },
  data() {
    return {
      loading: false,
      rect: {
        top: 0,
        left: 0,
        width: 0,
      },
      saving: false,
      id: '',
      slotName: '',
      minNum: 1,
      maxNum: 5,
      initMinNum: 1,
      initMaxNum: 5,
    }
  },
  computed: {
    popperStyle() {
      if (this.rect) {
        if (this.showTop) {
          return {
            top: `${this.rect.top - 330}px`,
            left: `${this.rect.left}px`,
          }
        } else {
          return {
            top: `${this.rect.top + 15}px`,
            left: `${this.rect.left}px`,
          }
        }
      } else {
        return {
          display: `none`,
        }
      }
    },
    intent() {
      return this.$store.state.studioSkill.intention
    },
    showTop() {
      const clientHeight = document.body.clientHeight
      console.log('clientHeight, this.rect.top', clientHeight, this.rect.top)
      return clientHeight - this.rect.top < 300
    },
  },
  watch: {
    'variablePopover.rect': function () {
      this.rect = JSON.parse(JSON.stringify(this.variablePopover.rect))
      this.baseRectTop = JSON.parse(
        JSON.stringify(this.variablePopover.rect)
      ).top
    },
    'variablePopover.show': function (val) {
      if (val) {
        this.initData()
      }
    },
  },
  methods: {
    handleScroll(e, top) {
      this.rect.top = this.baseRectTop - top
    },
    closePopover(e) {
      if (e && e.target.classList.contains('wildcard')) {
        return
      }
      this.variablePopover.show = false
    },
    initData() {
      this.slotName = this.variablePopover.data.slotName
      this.id = ''
      this.minNum = 1
      this.maxNum = 5
      this.initMinNum = 1
      this.initMaxNum = 5
      this.saving = false
      this.$utils.httpGet(
        this.$config.api.STUDIO_SLOT_GET_WILDCARD,
        {
          slotId: this.variablePopover.data.id,
        },
        {
          success: (res) => {
            if (res.data) {
              this.id = res.data.id
              this.minNum = res.data.min
              this.maxNum = res.data.max
              this.initMinNum = this.minNum
              this.initMaxNum = this.maxNum
            }
          },
          error: (err) => {},
        }
      )
    },
    save() {
      let self = this
      if (this.minNum > this.maxNum) {
        this.$message.warning('最小值不得大于最大值')
        return
      }
      this.saving = true
      let data = {
        slotId: this.variablePopover.data.id,
        businessId: this.intent.businessId,
        min: this.minNum,
        max: this.maxNum,
      }
      if (this.id) {
        data.id = this.id
      }
      this.$utils.httpPost(
        this.$config.api.STUDIO_SLOT_ADD_EDIT_WILDCARD,
        data,
        {
          success: (res) => {
            self.saving = false
            self.$message.success('保存成功')
            self.$emit('change')
            self.closePopover()
          },
          error: (err) => {
            self.saving = false
          },
        }
      )
    },
    cancel() {
      this.minNum = this.initMinNum
      this.maxNum = this.initMaxNum
      this.closePopover()
    },
  },
}
</script>
<style lang="scss">
.set-wild-card-popover {
  padding: 16px !important;
  width: 320px !important;
  .top-tip {
    margin-bottom: 24px;
    padding: 9px 16px;
    border-radius: 3px;
    background: rgba(255, 164, 0, 0.07);
  }
  // 通配实体dialog
  .wildcard-form {
    font-size: 0;
    .el-form-item {
      margin-right: 0;
    }
    .el-input-number {
      width: 132px;
    }
  }
  .btn-wrap {
    display: block;
    text-align: right;
    font-size: 0;
  }
  .dialog-btn {
    padding: 6px 12px;
    min-width: unset;
    width: 64px;
    :first-child {
      margin-right: 8px;
    }
  }
}
</style>
