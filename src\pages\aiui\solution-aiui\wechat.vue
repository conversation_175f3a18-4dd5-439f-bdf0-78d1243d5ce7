<template lang="html">
  <div class="aiui">
    <div class="banner-wrap">
			<div class="banner-content">
				<p class="banner-title">微信解决方案</p>
				<p class="banner-desc">让你的公众号拥有智能语音对话能力</p>
				<a class="weChat-btn use-btn" href="/app/add?platform=WeChat">立即使用</a>
				<a class="weChat-btn guide-btn" :href="DocsInUrl" target="_blank">上手指南</a>
			</div>
		</div>
		<div class="product-character">
			<p class="character-title">产品特点</p>
			<ul class="product-desc">
				<li>
					<p class="character">容易接入</p>
					<p class="character-desc">公众号运营人员扫码即可接入</p>
				</li>
				<li>
					<p class="character">能力丰富</p>
					<p class="character-desc">技能商店拥有众多能力并持续增加</p>
				</li>
				<li>
					<p class="character">扩展性强</p>
					<p class="character-desc">自定义技能和问答定制个性化业务</p>
				</li>
			</ul>
		</div>
		<div class="function-banner">
			<ul class="introduce-function">
				<li class="left-banner"></li>
				<li class="right-content">
					<p class="function-title">功能介绍</p>
					<ul class="function-desc">
						<li>
							<i></i>
							<p>微信公众号通过扫码绑定科大讯飞AIUI开放平台，托管智能回复功能。</p>
						</li>
						<li>
							<i></i>
							<p>AIUI可以接收公众号用户的语音和文本请求，并对文本请求合适的语义理解<br>
								以及业务逻辑处理，返回给用户图文并茂的消息内容。</p>
						</li>
						<li>
							<i></i>
							<p>公众号运营人员可以在AIUI后台维护自定义问答，添加商店技能。<br>
								开发人员可以通过编写技能和代码实现复杂的业务逻辑。</p>
						</li>
					</ul>
					<div class="function-desc-banner"></div>
				</li>
			</ul>
		</div>
		<div class="application-scene">
			<p class="scene-title">应用场景</p>
			<ul class="scene-content">
				<li>
					<i class="diamond"></i>
					<p class="scene">智能客服</p>
					<p class="scene-desc">
						AIUI自定义问答支持多问多答以及<br>
						模糊匹配通过简单易用的配置即可<br>
						实现智能客服功能。
					</p>
				</li><li>
					<i class="diamond"></i>
					<p class="scene">闲聊</p>
					<p class="scene-desc">
						通过添加商店技能、问答、即可实<br>
						现闲聊、天气、笑话、故事等趣味<br>
						功能。
					</p>
				</li><li class="scene">
					<i class="diamond"></i>
					<p class="scene">家居控制</p>
					<p class="scene-desc">
						公众号的开发人员可以通过编写自<br>
						定义技能配合云函数，实现诸如智<br>
						能家居控制等功能类需求。
					</p>
				</li>
			</ul>
		</div>
  </div>
</template>

<script>

export default {
  layout: 'aiuiHome',
  head () {
    return {
      title: 'AIUI 微信解决方案',
      meta: [
        { name: 'keywords', content: 'AIUI，科大讯飞，AIUI 开放平台，AIUI 开发者，AIUI 微信解决方案' },
        { name: 'description', content: 'AIUI 目前为公众号和服务号提供托管服务，微信公众号类型应用可以自定义技能以及部分商店技能以及开放问答。目前AIUI微信解决方案支持用户语音、文本、文本+系统表情、自定义表情、收藏表情进行人机交互。' }
      ]
    }
  },
  data () {
    return {
			DocsInUrl: `${process.env.docHost}/aiui/sdk/more_doc/access_wechat.html`
    }
  },
  methods: {
    coreClick(index) {
      this.coreIndex = index
      this.coreSwiper.slideTo(index, 1000, false)
    }
  }
}
</script>
<style lang="scss" scoped>
.header-wrap {
		position: absolute;
		width: 100%;
		z-index: 2;
		background: 0 0;
		border-bottom: 1px solid rgba(255,255,255,.2);
	}
	.banner-wrap{
		min-width: 1200px;
		height: 471px;
		background: #13182c url("../../../assets/images/solutions/wechat/banner.jpg") no-repeat center;
	}
	.banner-content {
		position: relative;
		width: 1200px;
		margin: 0 auto;
		.banner-title {
			position: absolute;
			top: 171px;
			left: 40px;
			color: #fff;
			font-size: 46px;
			line-height: 40px;
			letter-spacing: 5px;
		}
		.banner-desc {
			position: absolute;
			top: 231px;
			left: 40px;
			color: #fff;
			font-size: 18px;
			letter-spacing: 3px;
		}
	}
	.weChat-btn {
		box-sizing: content-box;
		position: absolute;
		top: 291px;
		width: 122px;
		height: 37px;
		font-size: 14px;
		color: #fff;
		letter-spacing: 1px;
		text-align: center;
		line-height: 37px;
		border-radius: 3px;
		cursor: pointer;
	}
	.use-btn {
		left: 40px;
		background: #108bf3;
		border: 1px solid #108bf3;
		&:hover {
			border: 1px solid #0982e8;
			background: #0982e8;
		}
	}
	.guide-btn {
		left: 181px;
		border: 1px solid #fff;
		&:hover {
			color: #333;
			background: #fff;
		}
	}

	.product-character {
		margin-top: 55px;
		.character-title {
			font-size: 34px;
			line-height: 34px;
			color: #393939;
			text-align: center;
			letter-spacing: 3px;
		}
		.product-desc {
			width: 1200px;
			margin: 50px auto 0;
			li {
				width: 350px;
				height: 210px;
				padding: 40px 0 0 30px;
				display: inline-block;
				box-shadow: 0.31px 5.99px 18px 0px rgba(37, 77, 132, 0.06);
				&:hover{
					position: relative;
					top: -2px;
				}
			}
			li:nth-child(1){
				margin-right: 70px;
				background: url("../../../assets/images/solutions/wechat/access-banner.png") no-repeat center;
			}
			li:nth-child(2){
				margin-right: 70px;
				background: url("../../../assets/images/solutions/wechat/ability-banner.png") no-repeat center;
			}
			li:nth-child(3){
				background: url("../../../assets/images/solutions/wechat/extend-banner.png") no-repeat center;
			}
		}
	}
	.character {
		color: #333;
		font-size: 24px;
	}
	.character-desc {
		color: #888;
		font-size: 14px;
		line-height: 45px;
	}

	.function-banner {
		background: #fafafa;
	}
	.introduce-function {
		position: relative;
		width: 1200px;
		height: 467px;
		margin: 127px auto 0;
		>li {
			position: absolute;
			display: inline-block;
		}
		.left-banner {
			top: -37px;
			left: -309px;
			width: 909px;
			height: 504px;
			background: url("../../../assets/images/solutions/wechat/introduce.png") no-repeat center;
		}
		.right-content {
			left: 600px;
			padding: 100px 0 0 65px;
		}
	}
	.function-title {
		padding-left: 20px;
		font-size: 34px;
		line-height: 34px;
		color: #393939;
		letter-spacing: 3px;
	}
	.function-desc {
		margin-top: 35px;
		color: #666;
		line-height: 32px;
		li {
			position: relative;
			i {
				position: absolute;
				top: 14px;
				left: -6px;
				width: 6px;
				height: 6px;
				border-radius: 50%;
				display: inline-block;
				background: #707070;
			}
			p {
				padding-left: 12px;
				display: inline-block;
			}
		}
	}
	.function-desc-banner {
		width: 500px;
		height: 36px;
		margin-top: 30px;
		background: url("../../../assets/images/solutions/wechat/function-banner.png") no-repeat center;
	}

	.application-scene {
		margin: 80px 0 150px;
		.scene-title {
			font-size: 34px;
			line-height: 34px;
			color: #393939;
			text-align: center;
			letter-spacing: 3px;
		}
		.scene-content {
			width: 1200px;
			margin: 80px auto 0;
			li {
				position: relative;
				width: 388px;
				height: 309px;
				display: inline-block;
				border: 1px solid #ebebeb;
				&:hover {
					border: 1px solid transparent;
					box-shadow: 0.37px 6.99px 18px 0px rgba(66, 74, 86, 0.07);
					transition: .5s;
				}
			}
			li:nth-child(1) {
				margin-right: 15px;
				background: url("../../../assets/images/solutions/wechat/service-banner.png") no-repeat top;
				i {
					background: url("../../../assets/images/solutions/wechat/1.png") no-repeat center;
				}
			}
			li:nth-child(2) {
				margin-right: 15px;
				background: url("../../../assets/images/solutions/wechat/chat-banner.png") no-repeat top;
				i {
					background: url("../../../assets/images/solutions/wechat/2.png") no-repeat center;
				}
			}
			li:nth-child(3) {
				top: 0;
				left: 0;
				background: url("../../../assets/images/solutions/wechat/intelligent-banner.png") no-repeat top;
				i {
					background: url("../../../assets/images/solutions/wechat/3.png") no-repeat center;
				}
			}
		}
	}
	.diamond {
		position: absolute;
		top: 132px;
		left: 286px;
		display: inline-block;
		width: 77px;
		height: 79px;
	}
	.scene {
		position: absolute;
		top: 125px;
		left: 50px;
		color: #fff;
		font-size: 22px;
	}
	.scene-desc {
		position: absolute;
		top: 195px;
		left: 50px;
		color: #666;
		font-size: 14px;
		line-height: 25px;
	}
</style>
