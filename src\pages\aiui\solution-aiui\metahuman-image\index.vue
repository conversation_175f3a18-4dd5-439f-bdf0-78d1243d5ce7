<template>
  <div class="main-content">
    <section class="main-content-banner">
      <div class="banner-text">
        <h2><span>虚拟人形象资产库</span></h2>
        <p class="banner-text-content">
          提供丰富2D/3D虚拟人形象和个性化定制服务<br />
          快速接入，满足不同场景的虚拟人交互需求
        </p>
        <div class="banner-button">
          <div class="banner-text-button" @click="toConsole">形象定制咨询</div>
          <div class="banner-white-button" @click="toCreate">创建数字分身</div>
        </div>
      </div>
    </section>

    <section class="section tab-section">
      <el-tabs v-model="activeName">
        <el-tab-pane label="云端形象" name="cloud"></el-tab-pane>
        <el-tab-pane label="端侧形象" name="edge"></el-tab-pane>
      </el-tabs>
      <div class="search-header" style="margin-left: auto">
        <div>
          适用场景
          <el-select v-model="scene" placeholder="请选择" style="width: 120px">
            <el-option
              v-for="item in sceneList"
              :key="item"
              :label="item"
              :value="item"
            >
            </el-option>
          </el-select>
        </div>
        <div style="margin-left: 24px">
          形象类型
          <el-select v-model="image" placeholder="全部" style="width: 120px">
            <el-option label="2D虚拟人" value="2d" />
            <el-option label="3D虚拟人" value="3d" />
            <el-option label="超拟人虚拟人" value="super" />
          </el-select>
        </div>
        <div style="margin-left: 24px">
          性别
          <el-select v-model="gender" placeholder="全部" style="width: 120px">
            <el-option label="男" value="male" />
            <el-option label="女" value="female" />
          </el-select>
        </div>
        <div style="margin-left: 24px">
          关键词
          <el-input
            v-model="searchText"
            placeholder="输入名称或标签id"
            style="width: 210px; margin-left: 14px"
            clearable
          />
        </div>

        <div style="margin-left: auto">
          <el-button type="primary" icon="el-icon-search" @click="filterHuman"
            >查询</el-button
          >
          <el-button @click="reset">重置</el-button>
        </div>
      </div>
    </section>
    <!-- 超拟人自成一行 -->
    <section class="section card-section" v-if="activeName == 'cloud'">
      <meta-card
        v-for="item in filteredImageData"
        v-if="showCard(item) && item.type == activeName && item.isSuper"
        :key="item.anchor_id"
        :humanData="item"
        @showDetail="showDetail"
      />
    </section>
    <section class="section card-section">
      <meta-card
        v-for="item in filteredImageData"
        v-if="showCard(item) && item.type == activeName && !item.isSuper"
        :key="item.anchor_id"
        :humanData="item"
        @showDetail="showDetail"
      />
    </section>

    <el-dialog title="形象" :visible.sync="dialogShow" width="980px">
      <div class="action-layout">
        <div
          class="action-card"
          v-for="item in anchorDialog"
          :key="item.action_id"
          @mouseenter="showMask(item)"
          @mouseleave="hideMask(item)"
        >
          <!-- 遮罩层 -->
          <div class="hover-mask" v-show="item.showMask">
            <img
              :src="playPauseIcon"
              class="play-icon"
              @click="toggleVideo(item)"
            />
          </div>

          <div class="action-name">{{ item.actionName }}</div>
          <video
            :src="item.videoUrl"
            height="355"
            width="200"
            ref="videoPlayer"
          ></video>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import metaCard from './meta-card.vue'
import { imageData, actionData } from './image-data.js'
export default {
  name: 'metahuman-image',
  components: {
    metaCard,
  },
  data() {
    return {
      imageData,
      actionData,
      isSearch: false,
      activeName: 'cloud',
      scene: '',
      image: '',
      gender: '',
      searchText: '',
      sceneList: ['通用', '银行服务', '展厅接待', '商超零售'],
      dialogShow: false,
      anchorChoosed: '',
      anchorDialog: [],
    }
  },
  computed: {
    filteredImageData() {
      return this.imageData.filter((item) => this.showCard(item))
    },
    playPauseIcon() {
      // const videoIndex = this.actionData[this.anchorChoosed].indexOf(this.item); //这里this.item不存在
      // const video = this.$refs.videoPlayer && this.$refs.videoPlayer[videoIndex];

      // // 如果视频对象存在并且它的播放状态可以访问
      // if (video) {
      //   return video.paused
      //     ? require('/src/assets/images/solution/metahuman-image/play_fill.png')
      //     : require('/src/assets/images/solution/metahuman-image/pause_fill.png');
      // }

      // 默认图标
      return require('/src/assets/images/solution/metahuman-image/play_fill.png')
    },
  },
  methods: {
    toConsole() {
      // 判断是否有百度SEM营销参数，有的话，提交表单页需要带上
      const search = window.location.search
      if (search) {
        window.open(`/solution/apply/37${search}`)
      } else {
        window.open('/solution/apply/37')
      }
    },
    toCreate() {
      window.open('https://www.xfzhizuo.cn/ai-tools/anchor-train')
    },
    filterHuman() {
      this.isSearch = true
    },
    showCard(item) {
      if (this.isSearch) {
        // 适用场景筛选
        if (this.scene && item.field !== this.scene) {
          return false
        }
        // 形象类型筛选
        if (this.image && item.image != this.image) {
          return false
        }
        // 性别筛选
        if (this.gender && item.gender !== this.gender) {
          return false
        }
        // 关键词筛选
        if (this.searchText) {
          const searchTextLower = this.searchText.toLowerCase()
          const nameMatch = item.name.toLowerCase().includes(searchTextLower)
          const anchorIdMatch = item.anchor_id.includes(this.searchText)
          // const labelsMatch = item.labels.some(label => label.toLowerCase().includes(searchTextLower));
          if (!nameMatch && !anchorIdMatch) {
            return false
          }
        }
      }
      return true
    },
    filterCard(cardData) {
      if (cardData.type != this.activeName) {
        return false
      }
      if (cardData.isSuper) {
        return false
      }
      return true
    },
    filterSuperCard(cardData) {
      if (cardData.type != this.activeName) {
        return false
      }
      if (!cardData.isSuper) {
        return false
      }
      return true
    },
    showSuperCard(item) {
      return this.showCard(item) && item.isSuper
    },
    showDetail(aId) {
      this.anchorChoosed = aId
      // actionData[this.anchorChoosed].map( a => ({...a, showMask:true }))
      this.anchorDialog = actionData[this.anchorChoosed].map((a) => ({
        ...a,
        showMask: false,
      }))
      this.dialogShow = true
    },
    reset() {
      this.isSearch = false
      this.scene = ''
      this.image = ''
      this.gender = ''
      this.searchText = ''
    },
    toggleVideo(item) {
      const video = this.$refs.videoPlayer[this.anchorDialog.indexOf(item)]
      // console.log('video', video.paused)
      // if (video.paused) {
      //   video.play()
      // } else {
      //   video.pause()
      // }
      video.play()
      item.showMask = false
    },
    showMask(item) {
      item.showMask = true
    },
    // 隐藏遮罩层
    hideMask(item) {
      item.showMask = false
    },
  },
}
</script>
<style lang="scss" scoped>
.main-content {
  &-banner {
    background: url(~@A/images/solution/metahuman-image/tiny-banner.png) center
      no-repeat;
    background-size: cover;
    height: 498px;
    overflow: hidden;
    width: 100%;
    .banner-button {
      display: flex;
      align-items: center;
    }
    .banner-white-button {
      width: 160px;
      height: 60px;
      line-height: 60px;
      color: $primary;
      border: 1px solid #2274ff;
      font-size: 20px;
      border-radius: 10px;
      margin-left: 18px;
      text-align: center;
      cursor: pointer;
    }
    .banner-text {
      max-width: 1200px;
      height: 100%;
      margin: auto;

      &-button {
        font-size: 20px;
        text-align: center;
        font-weight: 400;
        width: 160px;
        height: 60px;
        line-height: 60px;
        // border: 1px solid #fff;
        // border-radius: 40px;
        color: #fff;
        cursor: pointer;
        // background: $primary;
        border-radius: 10px;
        background: linear-gradient(90deg, #5594ff 23%, #2274ff 75%);
        // transition: 0.6s;
      }

      h2 {
        padding-top: 148px;
        margin-bottom: 20px;
        text-align: left;
        span {
          font-size: 48px;
          font-weight: 600;
          text-align: left;
          line-height: 54px;
          color: #000000;
          // color: #0065f9;
          // -webkit-text-fill-color: transparent;
          // -webkit-background-clip: text;
          // background-clip: text;
          // background-image: linear-gradient(
          //   270deg,
          //   #006cf2 40%,
          //   #00a9f9 56%,
          //   #00d7ff 98%
          // );
        }
      }

      .banner-text-content {
        font-size: 16px;
        font-weight: 400;
        text-align: left;
        color: #282a47;
        line-height: 31px;
        margin-bottom: 40px;
      }
    }
  }
  .section {
    max-width: 1200px;
    margin: 0 auto;
  }
  .tab-section {
    margin-top: 28px;
    :deep(.el-tabs) {
      .el-tabs__active-bar {
        height: 3px;
        background-color: #2e7cff;
      }
      .el-tabs__item {
        font-size: 16px;
        height: 48px;
        font-weight: 500;
      }
    }
  }
  .search-header {
    display: flex;
    align-items: center;
    margin-top: 20px;
    margin-bottom: 33px;
    font-size: 16px;
    font-family: PingFang SC, PingFang SC-Medium;
    font-weight: 500;
    color: #333333;
    :deep(.el-select) {
      margin-left: 14px;
      .el-input {
        .el-input__inner {
          background: #f7f8fa;
          height: 31px;
          border: none;
        }
      }
      .el-input.is-focus {
        .el-input__inner {
          background: #ecf2fe;
          color: #2e7cff;
          &::placeholder {
            color: #2e7cff;
          }
        }
        .el-select__caret {
          color: #2e7cff;
        }
      }

      .el-input__suffix i {
        line-height: 31px;
      }
    }

    :deep(.el-input) {
      .el-input__inner {
        background: #f7f8fa;
        height: 31px;
        border: none;
        &:focus {
          border: 1px solid #2e7cff;
        }
      }
    }
  }

  .card-section {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    grid-gap: 36px;
    margin-bottom: 48px;
  }
}

.action-layout {
  display: flex;
  // grid-template-columns: repeat(4, 1fr); /* 定义三列，每列占1等分 */
  gap: 32px;
  flex-flow: wrap;
  padding-bottom: 12px;
  video {
    border-radius: 11px;
  }
  .action-card {
    position: relative;
    height: 355px;
    .action-name {
      background: rgba(25, 25, 25, 0.29);
      color: #ffffff;
      border-radius: 0 0 11px 11px;
      position: absolute;
      bottom: 0px;
      height: 30px;
      width: 100%;
      line-height: 30px;
      text-align: center;
    }

    .hover-mask {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      justify-content: center;
      align-items: center;
      // display: none;
    }
    &:hover {
      .hover-mask {
        display: flex;
      }
    }

    .hidden {
      opacity: 0;
      pointer-events: none;
    }
    .play-icon {
      // background: none;
      // border: none;
      // color: white;
      // font-size: 32px;
      width: 32px;
      height: 32px;
      z-index: 2;
      cursor: pointer;
      transition: transform 0.2s;
    }
    .play-icon:hover {
      transform: scale(1.1);
    }
  }
}
</style>
