import { Message } from 'element-ui'
import TransWorker from './ttstranscode.worker.js'
const transWorker = new TransWorker()

class TTSRecorder {
  constructor(statusCb, supressTip) {
    this.audioData = []
    this.rawAudioData = []
    this.audioDataOffset = 0
    this.status = 'init'
    this.statusCb = statusCb
    this.supressTip = supressTip
    statusCb && statusCb(this.status)
    this.audioInit()
    transWorker.onmessage = (e) => {
      this.audioData.push(...e.data.data)
      this.rawAudioData.push(...e.data.rawAudioData)
      // 收到数据后开始合成播放
      if (this.status !== 'play') {
        !this.supressTip && Message.info('正在为您播报...')
        this.start()
      }
    }
  }

  // 修改录音听写状态
  setStatus(status) {
    this.status = status
    this.statusCb && this.statusCb(status)
  }

  // 接收数据的处理
  result(resultData) {
    transWorker.postMessage(resultData)
  }
  // 重置音频数据
  resetAudio() {
    setTimeout(() => {
      this.audioDataOffset = 0
      this.audioData = []
      this.rawAudioData = []
    })
  }
  // 音频初始化
  audioInit() {
    let AudioContext = window.AudioContext || window.webkitAudioContext
    if (AudioContext) {
      this.audioContext = new AudioContext()
      this.audioContext.resume()
      this.audioDataOffset = 0
    }
  }
  // 音频播放
  audioPlay() {
    this.setStatus('play')
    let audioData = this.audioData.slice(this.audioDataOffset)
    this.audioDataOffset += audioData.length
    let audioBuffer = this.audioContext.createBuffer(1, audioData.length, 22050)
    let nowBuffering = audioBuffer.getChannelData(0)
    if (audioBuffer.copyToChannel) {
      audioBuffer.copyToChannel(new Float32Array(audioData), 0, 0)
    } else {
      for (let i = 0; i < audioData.length; i++) {
        nowBuffering[i] = audioData[i]
      }
    }
    let bufferSource = (this.bufferSource =
      this.audioContext.createBufferSource())
    bufferSource.buffer = audioBuffer
    bufferSource.connect(this.audioContext.destination)
    bufferSource.start()
    bufferSource.onended = (event) => {
      if (this.status !== 'play') {
        return
      }
      if (this.audioDataOffset < this.audioData.length) {
        this.audioPlay()
      } else {
        this.audioStop()
        this.resetAudio()
      }
    }
  }
  // 音频播放结束
  audioStop() {
    this.setStatus('endPlay')
    this.audioDataOffset = 0
    if (this.bufferSource) {
      try {
        this.bufferSource.stop()
      } catch (e) {
        console.log(e)
      }
    }
  }
  start() {
    if (this.audioData.length) {
      this.audioPlay()
    } else {
      if (!this.audioContext) {
        this.audioInit()
      }
      if (!this.audioContext) {
        alert('该浏览器不支持webAudioApi相关接口')
        return
      }
    }
  }
  stop() {
    this.audioStop()
  }
}

export default TTSRecorder
