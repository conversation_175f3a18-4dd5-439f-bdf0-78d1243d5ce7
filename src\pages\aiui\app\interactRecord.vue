<template>
  <os-page :options="pageOptions">
    <div class="interact-record-page">
      <div class="sp-tips">
        <span>展示最近的200条交互日志，帮助你验证与优化服务。</span>
      </div>
      <div class="buy-section buy-section-3">
        <ul class="buy-section-3-header">
          <li>识别结果</li>
          <li>回复技能</li>
          <li>回复语</li>
        </ul>

        <el-tabs tab-position="left" v-model="list">
          <div class="tab-content" v-if="list.list.length" v-loading="loading">
            <div
              v-for="(item, index) in list.list"
              :key="index"
              class="tab-content-list"
            >
              <div class="tab-text" :title="item.text">
                {{ item.text }}
              </div>
              <div class="tab-text" :title="item.service">
                {{ item.service }}
              </div>
              <div class="tab-text" :title="item.answer">
                {{ item.answer }}
              </div>
            </div>
          </div>
          <div
            v-else
            class="tab-content"
            style="text-align: center; padding-top: 50px; color: #959595"
          >
            暂无数据
          </div>
        </el-tabs>
        <div class="mgt24" v-if="list.total > 10">
          <el-pagination
            ref="pagination"
            v-if="list.list.length"
            class="txt-al-c"
            @current-change="getQaPair"
            :current-page="list.pageIndex"
            :page-size="list.pageSize"
            :total="list.total"
            :layout="pageLayout"
          >
          </el-pagination>
        </div>
      </div>
    </div>
  </os-page>
</template>

<script>
export default {
  name: 'interactRecord',
  data() {
    return {
      list: {
        total: 1,
        pageIndex: 1,
        pageSize: 10,
        list: [],
      },
      pageOptions: {
        title: '交互记录',
        loading: false,
        returnBtn: false,
      },
    }
  },
  computed: {
    appId() {
      return this.$route.params.appId
    },
    pageLayout() {
      if (this.list.total / this.list.list > 10) {
        return 'prev, pager, next, jumper, total'
      }
      return 'prev, pager, next, jumper, total'
    },
  },
  created() {
    this.getResList()
  },
  methods: {
    getQaPair(page) {
      this.list.pageIndex = page
      this.getResList(page)
    },
    getResList(page) {
      this.$utils.httpGet(
        this.$config.api.AIUI_APP_INTER_LIST,
        {
          appid: this.appId,
          //filter: 'aiui',
          //appid: '5d1d9982',
          pageIndex: page || this.list.pageIndex,
          pageSize: this.list.pageSize,
        },
        {
          success: (res) => {
            this.list.total = res.data.count
            this.list.list = res.data.list
          },
          error: (err) => {},
        }
      )
    },
  },
}
</script>

<style scoped lang="scss">
.interact-record-page {
  .sp-tips {
    margin-top: 2%;
    margin-bottom: 2%;
    color: $warning;
    span {
      line-height: 28px;
      font-size: 14px;
      padding-top: 1%;
      margin: inherit;
    }
  }
}

.template-ul {
  display: block;
  li {
    margin-right: 5%;
    margin-top: 2%;
    display: inline-block;
  }
}

.buy-section-3-header {
  display: flex;
  justify-content: space-around;
  align-items: center;
  height: 56px;
  background: #f2f5f7;
  border: 1px solid #e4e7ed;
  border-bottom: 0;
  border-right: 0;
  li {
    flex: 1;
    text-align: center;
    border-right: 1px solid #e4e7ed;
    height: 56px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  :deep(.el-input__inner) {
    border: 0;
    width: 120px;
    padding-right: 30px;
    background: #f2f5f7;
  }
}

.tab-content {
  border: 1px solid #e4e7ed !important;
  border-left: 0;
  height: auto !important;
  overflow-y: auto;
  &-list:not(:last-of-type) {
    border-bottom: 1px solid #e4e7ed !important;
  }
  &-list {
    display: flex;
    height: 56px;
    justify-content: space-around;
    align-items: center;
    &:hover {
      background-color: #e8f3fd;
    }
    .add-input {
      //border: 1px solid #d5d8de;
      border-radius: 2px;
      background: #1a17e9;
      color: darkgrey;
      .el-input {
        position: relative;
        .ic-r-plus {
          position: relative;
          /*line-height: 36px;*/
          display: inline-block;
          vertical-align: top;
          /*width: 50px;*/
          text-align: center;
          color: #d5d8de;
        }
      }
    }
    &:hover &-del {
      display: block;
    }
    &-del {
      position: relative;
      color: $grey4;
      font-size: 20px;
      cursor: pointer;
      display: none;
      margin: 0 auto;
      text-align: center;
      align-items: center;
      justify-content: center;
      flex: 1;
    }
    > div {
      flex: 1;
      text-align: center;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      .san-circle {
        width: 30px;
        height: 30px;
        margin: 0 auto;
        border-radius: 50%;
        background: #1784e9;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        .san.play {
          display: none;
        }
        .span.pause {
          display: inline-block;
        }
        .er.play {
          display: inline-block;
        }
        .er.pause {
          display: none;
        }
        .san {
          width: 0;
          height: 0;
          border-left: 10px solid transparent;
          border-right: 10px solid transparent;
          border-top: 10px solid transparent;
          border-bottom: 10px solid #fff;
          border-radius: 3px;
          display: inline-block;
          transform: rotate(90deg);
          position: relative;
          left: 7px;
        }
        .er {
          span {
            display: inline-block;
            width: 4px;
            height: 15px;
            background: #fff;
            margin-top: 5px;
          }
        }
      }
    }
  }
}
</style>
