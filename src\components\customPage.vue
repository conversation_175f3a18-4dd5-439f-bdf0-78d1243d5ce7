<template>
  <div
    class="aiui-page"
    v-loading="options.loading"
    :class="{ 'aiui-page-screen': options.screen }"
  >
    <div v-show="options.title || options.showHead" class="aiui-page-head">
      <!-- <i
        v-if="options.returnBtn"
        class="el-icon-back aiui-page-head-back"
        @click="back"
      /> -->
      <back-icon
        v-if="options.returnBtn"
        @click="back"
        style="margin-right: 9px"
      ></back-icon>
      <div v-if="options.title" class="aiui-page-head-line"></div>
      <div
        v-if="options.title"
        class="aiui-page-head-title"
        v-html="options.title"
      ></div>
      <div class="aiui-page-head-title">
        <slot name="headLeft"></slot>
      </div>
      <div class="aiui-page-btns">
        <slot name="btn"></slot>
      </div>
    </div>
    <!-- <aiui-divider /> -->
    <div
      id="scrollDom"
      class="os-scroll"
      :style="{
        padding: '0 30px',
        height:
          options.title || options.showHead
            ? 'calc(100% - 65px)'
            : 'calc(100% - 1px)',
      }"
    >
      <div id="scrollDomDiv" style="padding-bottom: 70px">
        <slot></slot>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AiuiPage',
  props: {
    options: {
      type: Object,
      default: {
        title: '',
        loading: true,
      },
    },
  },
  data() {
    return {}
  },
  mounted() {},
  methods: {
    back() {
      this.$emit('returnCb')
    },
  },
}
</script>

<style lang="scss" scoped>
.aiui-page {
  height: 100%;
  display: flex;
  flex-direction: column;

  &-screen {
    width: 80%;
    margin: 0 auto;
  }

  &-head {
    // height: 64px;
    font-size: 24px;
    padding: 0 30px;
    display: flex;

    &-back {
      cursor: pointer;
      // color: $grey4;
      font-size: 18px;
      margin-right: 16px;
    }

    &-line {
      width: 3px;
      height: 19px;
      background: $primary;
      margin-right: 9px;
    }

    &-title {
      flex: auto;
      width: 100px;
      font-size: 16px;
      font-weight: 500;
      text-align: left;
      color: #262626;
      line-height: 50px;
    }
  }

  &-scoll {
    flex: 1;
  }

  &-btns {
    display: flex;
    align-items: center;
  }
}
</style>
