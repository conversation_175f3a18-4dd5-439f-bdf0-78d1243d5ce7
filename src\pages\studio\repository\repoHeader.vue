<template>
  <div class="repo-page-header">
     <div class="repo-head">
        <i class="ic-r-angle-l-line repo-head-back" @click="back"/>
        <div class="repo-head-title">
          <el-popover
            placement="bottom"
            width="264"
            trigger="click">
            <select-repo />
            <span slot="reference" class="repo-head-title-select">
              <span style="max-width: 250px;" class="txt-ellipsis-nowrap" :title="repoName">{{repoName}}</span>
              <i class="ic-r-triangle-down"/>
            </span>
          </el-popover>
        </div>
      </div>
      <os-divider />
  </div>
</template>

<script>
import SelectRepo from './dailog/selectRepo'
  export default {
    name: 'repository-header',
    props: {
      backToParams: String
    },
    data() {
      return {
        repoName: ''
      }
    },
    computed: {
      repoId() {
        return this.$route.params.repoId || ''
      }
    },
    watch: {
      repoId(val, oldVal) {
        if(val !== oldVal) {
          this.getData()
        }
      }
    },
    created() {
      this.getData()
    },
    methods: {
      back () {
        switch(this.backToParams) {
          case 'qabanks':
            this.$router.push({name: 'studio-handle-platform-qabanks'})
            break
          case 'qa-relations':
            this.$router.push({name: 'qa-relations', params: {repoId: this.repoId}})
            break
          default:
            this.$router.push({name: 'studio-handle-platform-qabanks'})
            break
        }
      },
      getData () {
        let self = this
        this.$utils.httpGet(this.$config.api.STUDIO_QA_INFO, {
          repoId: this.repoId
        }, {
          success: (res) => {
            self.repoName =  res.data && res.data.name ? res.data.name : '占个位'
          },
          error: (err) => {
            self.$router.push({'name': 'studio-handle-platform-qabanks'})
          }
        })
      }
    },
    components: { SelectRepo }
  }
</script>

<style lang="scss" scoped>
.repo-head {
    font-size: 24px;
    margin-bottom: 21px;
    display: flex;
    align-items: center;
    &-back {
      cursor: pointer;
      margin-right: 16px;
      color: $grey4;
    }
    &-title {
      flex: auto;
      display: flex;
      align-items: center;
    }
    &-title-select {
      display: flex;
      align-items: center;
      cursor: pointer;
      margin-right: 16px;
      i {
        padding-left: 8px;
        color: $grey5;
      }
    }
  }
</style>