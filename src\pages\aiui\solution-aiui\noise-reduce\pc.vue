<template>
  <div class="main-content">
    <banner
      :src="'solution/noise-reduce/img_Call_banner.png'"
      @jump="toConsole"
    >
      <template v-slot:title> 通话降噪解决方案 </template
      ><template>
        实时分离人声与环境噪声<br />
        为用户带来更清晰动听的通话体验。
      </template></banner
    >
    <section class="section section-1">
      <div class="section-title">
        <i class="arrow arrow-left"></i
        ><span class="section-title-bold">应用场景</span
        ><i class="arrow arrow-right"></i>
      </div>
      <!-- <div class="section-desc" style="text-align: center">
        基于婴儿啼哭检测能力，配合周边设备震动，闪烁，短信等，第一时间提醒父母
      </div> -->
      <ul class="product-list">
        <li
          v-for="(item, index) in productList"
          :key="index"
          :class="item.klass"
        >
          <!-- <h1>{{ item.name }}</h1> -->
          <div class="desc-wrap">
            <h2>{{ item.name }}</h2>
            <p>
              {{ item.desc }}
            </p>
          </div>
          <!-- <div class="overlay"></div> -->
        </li>
      </ul>
    </section>

    <section class="section section-8">
      <div class="section-title">
        <i class="arrow arrow-left"></i
        ><span class="section-title-bold">效果试听</span
        ><i class="arrow arrow-right"></i>
      </div>
      <div class="section-desc" style="text-align: center">
        实时分离人声与环境噪声，确保用户在嘈杂环境中也能清晰交谈
      </div>
      <ul class="audio-list">
        <li v-for="(item, index) in audioList" :key="index">
          <h1>{{ item.title }}</h1>
          <div
            class="audio"
            :class="{
              'audio-origin-play': !isPlaying[index * 2],
              'audio-origin-pause': isPlaying[index * 2],
            }"
            @click="togglePlay(index * 2)"
          >
            <i class="icon-now-playing" v-if="isPlaying[index * 2]"></i>
            <span class="duration-text">{{ item.duration }}″</span>
            <div
              class="duration-overlay overlay-gray"
              v-if="isPlaying[index * 2]"
            ></div>
          </div>
          <p class="audio-desc">原始音频</p>
          <div
            class="audio audio-gutter"
            :class="{
              'audio-now-play': !isPlaying[index * 2 + 1],
              'audio-now-pause': isPlaying[index * 2 + 1],
            }"
            @click="togglePlay(index * 2 + 1)"
          >
            <i class="icon-now-playing" v-if="isPlaying[index * 2 + 1]"></i>
            <span class="duration-text">{{ item.duration }}″</span>
            <div
              class="duration-overlay overlay-blue"
              v-if="isPlaying[index * 2 + 1]"
            ></div>
          </div>
          <p class="audio-desc">处理后音频</p>
        </li>
      </ul>
      <audio id="audio_player" :src="currentSrc"></audio>
    </section>

    <section class="section section-7">
      <div>
        <div class="section-title">
          <i class="arrow arrow-left"></i
          ><span class="section-title-bold">方案介绍</span
          ><i class="arrow arrow-right"></i>
        </div>
        <div class="section-tabs">
          <ul>
            <li
              :class="{ active: activeName == '0' }"
              @click="toggleActive('0')"
            >
              效率表现
            </li>
            <li
              :class="{ active: activeName == '1' }"
              @click="toggleActive('1')"
            >
              情景支持
            </li>
            <li
              :class="{ active: activeName == '2' }"
              @click="toggleActive('2')"
            >
              接入说明
            </li>
          </ul>
        </div>

        <div class="section-swiper" v-swiper:swiper="swiperOption">
          <div class="swiper-wrapper">
            <div class="swiper-slide" key="1">
              <div class="section-desc" style="text-align: center">
                一次性通过Zoom会议摸底测试，双讲性能高达84%
              </div>
              <div class="section-item">
                <ul>
                  <li class="cell">
                    <div class="cell-desc desc1">
                      <p>智能降噪</p>
                      <p>平均信噪比提升<span>25dB</span></p>
                    </div>
                  </li>
                  <li class="cell">
                    <div class="cell-desc desc2">
                      <p>回声消除</p>
                      <p>回声抑制量超过<span>65dB</span></p>
                    </div>
                  </li>
                  <li class="cell">
                    <div class="cell-desc desc3">
                      <p>混响抑制</p>
                      <p>可处理混响时间><span>1000ms</span></p>
                    </div>
                  </li>
                  <li class="cell">
                    <div class="cell-desc desc4">
                      <p>啸声抑制</p>
                      <p>智能抑制啸声产生</p>
                    </div>
                  </li>
                  <li class="cell">
                    <div class="cell-desc desc5">
                      <p>自动增益</p>
                      <p>清晰拾音距离<span>5m</span></p>
                    </div>
                  </li>
                  <li class="cell">
                    <div class="cell-desc desc6">
                      <p>人声美化</p>
                      <p>人声美化EQ<span>10档</span></p>
                    </div>
                  </li>
                </ul>
              </div>
            </div>
            <div class="swiper-slide" key="2">
              <div class="section-item2">
                <ul>
                  <li class="cell">
                    <div class="head-image image-1"></div>
                    <div class="cell-desc">
                      <p>耳机情景</p>
                      <p>近讲模式下去除风噪、干扰噪声</p>
                    </div>
                  </li>
                  <li class="cell">
                    <div class="head-image image-2"></div>
                    <div class="cell-desc">
                      <p>手持情景</p>
                      <p>近讲模式下去除环境噪音</p>
                    </div>
                  </li>
                  <li class="cell">
                    <div class="head-image image-3"></div>
                    <div class="cell-desc">
                      <p>免提情景</p>
                      <p>远讲模式下去除环境噪音、混响与回声</p>
                    </div>
                  </li>
                  <li class="cell">
                    <div class="head-image image-4"></div>
                    <div class="cell-desc">
                      <p>视频与直播情景</p>
                      <p>远讲模式下去除环境噪音与混响</p>
                    </div>
                  </li>
                </ul>
              </div>
            </div>
            <div class="swiper-slide" key="3">
              <div class="access">
                <div class="access-left">
                  <p>软核接入算力要求</p>
                  <p>
                    支持安卓、ios、linux(arm)系统<br />
                    端上2核，主频1.2ghz，内存32M即可使用双麦通话降噪算法
                  </p>
                </div>
                <div class="access-right">
                  <div class="box1">硬件评估</div>
                  <div class="arrow1"></div>
                  <div class="box2">算法选型</div>
                  <div class="arrow2"></div>
                  <div class="box-wrap">
                    <div class="box2">软核接入</div>
                    <div class="box2">芯片接入</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    <section class="section section-8">
      <div class="section-title">
        <i class="arrow arrow-left"></i
        ><span class="section-title-bold">方案优势</span
        ><i class="arrow arrow-right"></i>
      </div>

      <div class="section-item">
        <div class="left-part">
          <p>易集成高表现</p>
          <p>
            支持快速量产 <br />
            不同场景下的效果表现稳定、通话自然
          </p>
        </div>
        <div class="right-part"></div>
      </div>
    </section>
    <corp @jump="toConsole">
      <template> 提交信息，我们会尽快与您联系</template>
    </corp>
  </div>
</template>

<script>
import banner from '@P/aiui/solution-aiui/components/banner.vue'
import corp from '@P/aiui/solution-aiui/components/corp.vue'
import '../../../../../static/vue-awesome-swiper'

import miantiOldSrc from '@A/images/solution/noise-reduce/mianti_old.wav'
import miantiNewSrc from '@A/images/solution/noise-reduce/mianti_new.wav'

import huwaiOldSrc from '@A/images/solution/noise-reduce/huwaifengzao_old.wav'
import huwaiNewSrc from '@A/images/solution/noise-reduce/huwaifengzao_new.wav'

import banshiOldSrc from '@A/images/solution/noise-reduce/banshidating_old.wav'
import banshiNewSrc from '@A/images/solution/noise-reduce/banshidating_new.wav'

import renshengOldSrc from '@A/images/solution/noise-reduce/renshengganrao_old.wav'
import renshengNewSrc from '@A/images/solution/noise-reduce/renshengganrao_new.wav'

const audioSrcs = [
  miantiOldSrc,
  miantiNewSrc,
  huwaiOldSrc,
  huwaiNewSrc,
  banshiOldSrc,
  banshiNewSrc,
  renshengOldSrc,
  renshengNewSrc,
]
let audioPlayer

export default {
  layout: 'aiuiHome',
  data() {
    return {
      productList: [
        {
          name: '智能手机',
          desc: '',
          klass: 'img_domestic_cellphone',
        },
        {
          name: 'TWS耳机',
          desc: '',
          klass: 'img_smartwatch',
        },
        {
          name: '教育平板',
          desc: '',
          klass: 'img_instrument_smar_bracelet',
        },
        {
          name: '会议大屏',
          desc: '',
          klass: 'img_sports_camera',
        },
      ],
      audioList: [
        { title: '免提情景', duration: 5 },
        { title: '户外风噪', duration: 5 },
        { title: '办事大厅', duration: 3 },
        { title: '人声分离', duration: 4 },
      ],
      activeName: '0',
      swiperOption: {
        loop: true,
        autoplay: {
          delay: 4000,
          // disableOnInteraction: false,
        },
      },

      currentIndex: -1,
      isPlaying: [false, false, false, false, false, false, false, false],
    }
  },
  mounted() {
    this.swiper.on('slideChange', () => {
      this.activeName = this.swiper.realIndex + ''
    })
    audioPlayer = document.getElementById('audio_player')
    audioPlayer.onended = () => {
      this.$set(this.isPlaying, this.currentIndex, false)
      this.currentIndex = -1
    }
    audioPlayer.onpause = () => {
      this.$set(this.isPlaying, this.currentIndex, false)
      this.currentIndex = -1
    }
    audioPlayer.oncanplay = () => {
      console.log('oncanplay', audioPlayer.duration)
      let duration = 1000 * audioPlayer.duration
      let dom = document.getElementsByClassName('icon-now-playing')[0]
      dom.style.left = '78px'
      dom.style.transition = `-webkit-transform ${duration}ms linear`
      dom.style.webkitTransform = 'translate(31px, 0)'

      let dom2 = document.getElementsByClassName('duration-overlay')[0]
      dom2.style.minWidth = '110px'
      dom2.style.transition = `min-width ${duration}ms linear`
      dom2.style.minWidth = '78px'
    }
  },
  computed: {
    currentSrc() {
      return audioSrcs[this.currentIndex]
    },
  },
  methods: {
    toConsole() {
      // 判断是否有百度SEM营销参数，有的话，提交表单页需要带上
      const search = window.location.search
      if (search) {
        window.open(`/solution/apply/18${search}`)
      } else {
        window.open('/solution/apply/18')
      }
    },
    toggleActive(val) {
      this.swiper.slideToLoop(Number(val))
    },
    togglePlay(index) {
      if (this.isPlaying[index]) {
        // 正在播放
        audioPlayer && audioPlayer.pause()
        this.$set(this.isPlaying, index, false)
      } else {
        // 关闭其他所有可能在播放的项目
        audioPlayer && audioPlayer.pause()
        for (let i = 0; i < this.isPlaying.length; i++) {
          this.$set(this.isPlaying, i, false)
        }
        this.currentIndex = index
        this.$set(this.isPlaying, index, true)
        this.$nextTick(() => {
          audioPlayer && audioPlayer.play()
        })
      }
    },
  },
  components: {
    banner,
    corp,
  },
}
</script>

<style lang="scss" scoped>
.main-content {
  .section {
    p,
    ul {
      margin-bottom: 0;
    }
    max-width: 1200px;
    overflow: hidden;
    margin: 0 auto;
    .section-title {
      text-align: center;
      font-size: 34px;
      font-family: SourceHanSansSC-Medium, SourceHanSansSC;
      font-weight: bold;
      color: #333;
      line-height: 34px;
      position: relative;
      width: 200px;
      margin: 0 auto;
      .arrow {
        width: 160px;
        height: 8px;
        display: inline-block;
        background-repeat: no-repeat;
        background-size: contain;
        position: absolute;
        z-index: 1;
      }
      .arrow-left {
        background-position: left;
        background-image: url(~@A/images/solution/offline/img_title_01.png);
        top: 50%;
        left: -160px;
      }
      .arrow-right {
        background-position: right;
        background-image: url(~@A/images/solution/offline/img_title_02.png);
        top: 50%;
        right: -160px;
      }
      .arrow-left1 {
        background-position: left;
        background-image: url(~@A/images/solution/wakeup/img_access_title_01.png);
        top: 50%;
        left: -160px;
      }
      .arrow-right1 {
        background-position: right;
        background-image: url(~@A/images/solution/wakeup/img_access_title_02.png);
        top: 50%;
        right: -160px;
      }
    }
    .section-desc {
      text-align: left;
      margin-top: 70px;
      font-size: 16px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #999999;
    }
    .section-title-bold {
      font-size: 34px;
      font-family: Microsoft YaHei;
      font-weight: bold;
      color: #333333;
    }
    .section-title-spec {
      font-size: 30px;
      font-weight: 500;
      color: #656565;
      line-height: 42px;
    }
    .section-tabs {
      margin-top: 70px;
      ul {
        display: flex;
        justify-content: center;
      }
      li {
        font-size: 20px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #666;
        line-height: 30px;
        position: relative;
        cursor: pointer;
        &:hover {
          color: #1f90fe;
        }
        &.active {
          color: #1f90fe;
          &::before {
            position: absolute;
            content: ' ';
            z-index: 1;
            width: 180px;
            height: 4px;
            background: #1f90fe;
            border-radius: 2px;
            bottom: -32px;
            left: -48px;
          }
          // &::after {
          //   position: absolute;
          //   content: ' ';
          //   display: inline-block;
          //   width: 0;
          //   height: 0;
          //   bottom: -100px;
          //   left: 50%;
          //   transform: translateX(-50%);
          //   border: 46px solid;
          //   border-color: transparent transparent #f4f7f9;
          // }
        }
      }
      li + li {
        margin-left: 420px;
      }
    }
    .section-sub-title {
      text-align: center;
      font-size: 18px;
      font-weight: 400;
      color: #777777;
      line-height: 25px;
      margin-top: 42px;
    }
    .section-sub-title-spec {
      font-size: 17px;
      font-weight: 400;
      color: #999999;
      line-height: 28px;
      margin-top: 18px;
    }

    .product-list {
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 50px auto 0;

      li {
        position: relative;
        text-align: center;
        width: 288px;
        height: 411px;

        .desc-wrap {
          padding-top: 39px;
        }
        .overlay {
          display: none;
          width: 100%;
          height: 100%;
          // background: rgba(0, 0, 0, 0.3);
          background-image: linear-gradient(
            0deg,
            rgb(0, 54, 255) 0%,
            rgb(39, 12, 73) 100%
          );
          opacity: 0.502;
          position: absolute;
          top: 0;
          left: 0;
          z-index: 1;
        }

        h1 {
          // display: none;
          text-align: left;
          max-width: 25px;
          font-size: 24px;
          font-family: Microsoft YaHei;
          font-weight: 400;
          color: #ffffff;
          line-height: 30px;
          margin: 0 auto;
          position: relative;
          top: 50%;
          transform: translateY(-50%);
        }
        h2 {
          // display: none;
          text-align: center;
          // padding-top: 178px;
          // padding-left: 35px;
          font-size: 21px;
          // font-weight: bold;
          color: #ffffff;
          line-height: 21px;
        }
        p {
          // display: none;
          margin-top: 32px;
          width: 232px;
          font-size: 16px;
          font-weight: 400;
          color: #ffffff;
          line-height: 32px;
          padding-left: 35px;
          text-align: left;
        }

        &.img_domestic_cellphone {
          background: url(~@A/images/solution/noise-reduce/img_smart_phone.png)
            center/100% no-repeat;
        }
        &.img_smartwatch {
          background: url(~@A/images/solution/noise-reduce/img_headset.png)
            center/100% no-repeat;
        }
        &.img_instrument_smar_bracelet {
          background: url(~@A/images/solution/noise-reduce/img_education_tablet.png)
            center/100% no-repeat;
        }
        &.img_sports_camera {
          background: url(~@A/images/solution/noise-reduce/img_meeting_time.png)
            center/100% no-repeat;
        }
        &.img_smart_helmet {
          background: url(~@A/images/solution/workflow/img_government.png)
            center/100% no-repeat;
        }
      }

      li + li {
        margin-left: 16px;
      }
    }
    .audio-list {
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 50px auto 0;

      li {
        position: relative;
        text-align: center;
        width: 270px;
        height: 360px;
        background: #fff;
        box-shadow: 0px 5px 20px 0px rgba(165, 165, 165, 0.2);
        border-radius: 20px;
        padding-top: 40px;
        h1 {
          font-size: 26px;
          font-family: Microsoft YaHei;
          font-weight: bold;
          color: #333333;
          text-align: center;
          margin-bottom: 25px;
        }
        .audio {
          width: 190px;
          height: 54px;
          margin: 0 auto;
          line-height: 54px;
          padding-right: 22px;
          font-size: 18px;
          color: #fff;
          text-align: right;
          cursor: pointer;
          position: relative;
        }
        .icon-now-playing {
          display: inline-block;
          position: absolute;
          z-index: 1;
          width: 2px;
          height: 60px;
          background: url(~@A/images/solution/noise-reduce/icon_now_Playing.png)
            center/100% no-repeat;
          top: -3px;
          left: 78px;
        }
        .duration-text {
          position: absolute;
          z-index: 10;
          right: 22px;
          top: 50%;
          transform: translateY(-50%);
        }
        .duration-overlay {
          position: absolute;
          z-index: 5;
          top: 0;
          right: 0;
          min-width: 110px;
          height: 54px;
          border-top-right-radius: 27px;
          border-bottom-right-radius: 27px;
          opacity: 0.5;
          &.overlay-blue {
            background: $primary;
          }
          &.overlay-gray {
            background: #aebbc7;
          }
        }
        .audio-origin-play {
          background: url(~@A/images/solution/noise-reduce/img_play_gray.png)
            bottom center/100% no-repeat;
        }
        .audio-origin-pause {
          background: url(~@A/images/solution/noise-reduce/img_suspended_gray.png)
            bottom center/100% no-repeat;
        }
        .audio-now-play {
          background: url(~@A/images/solution/noise-reduce/img_play_blue.png)
            bottom center/100% no-repeat;
        }
        .audio-now-pause {
          background: url(~@A/images/solution/noise-reduce/img_suspended_blue.png)
            bottom center/100% no-repeat;
        }
        .audio-desc {
          font-size: 18px;
          font-family: Microsoft YaHei;
          font-weight: 400;
          color: #666666;
          line-height: 18px;
          text-align: center;
          margin-top: 20px;
        }
        .audio-gutter {
          margin-top: 25px;
        }
      }

      li + li {
        margin-left: 40px;
      }
    }
  }

  .section-1 {
    margin-top: 110px;
  }

  .section-3 {
    max-width: unset;
    padding: 50px 0 5px 0;
    > div {
      margin: 0 auto;
    }

    p {
      margin-bottom: 0;
    }
    .advantage {
      margin-top: 84px;
      display: flex;
      justify-content: center;

      > li {
        text-align: center;
        width: 260px;
        > div {
          margin: 0 auto;
        }
        p {
          margin-bottom: 0;
          &:first-of-type {
            font-size: 18px;
            font-family: Microsoft YaHei;
            font-weight: bold;
            color: #333333;
            margin-top: 40px;
          }
          &:last-of-type {
            font-size: 16px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            color: #999999;
            line-height: 30px;
            margin-top: 22px;
          }
        }
        .ad {
          width: 107px;
          height: 106px;
        }
        .ad1 {
          background: url(~@A/images/solution/workflow/icon_expand.png) bottom
            center/100% no-repeat;
        }
        .ad2 {
          background: url(~@A/images/solution/workflow/icon_closed_loop.png)
            bottom center/100% no-repeat;
        }
        .ad3 {
          background: url(~@A/images/solution/workflow/icon_pra.png) bottom
            center/100% no-repeat;
        }
        .ad4 {
          background: url(~@A/images/solution/workflow/icon_cloud_deployment.png)
            bottom center/100% no-repeat;
        }
      }

      li + li {
        margin-left: 23px;
      }
    }
  }

  .section-swiper {
    .swiper-wrapper {
      max-width: 1200px;
    }
  }

  .section-7 {
    width: 100%;
    max-width: 100%;
    margin-top: 70px;
    padding: 40px 0 5px 0;
    .section-title {
      color: #fff;
    }
    .section-item {
      margin-top: 50px;
      > ul {
        display: flex;
        align-items: center;
        justify-content: center;
        max-width: 1200px;
        flex-wrap: wrap;
        margin: 0 auto;
        li {
          position: relative;
        }
        li + li {
          // margin-left: 30px;
        }
        li:nth-child(n + 4) {
          margin-top: 30px;
        }
        li:nth-child(3n + 3) {
          margin-left: 30px;
        }
        li:nth-child(3n + 2) {
          margin-left: 30px;
        }
        .cell {
          .cell-desc {
            padding: 46px 0 0 30px;
            border-radius: 10px;
            width: 380px;
            height: 150px;
            p {
              margin-bottom: 0;
              &:first-child {
                font-size: 20px;
                font-family: Microsoft YaHei;
                font-weight: bold;
                color: #333333;
                line-height: 20px;
              }
              &:last-child {
                font-size: 18px;
                font-family: Microsoft YaHei;
                color: #9ba7b9;
                line-height: 30px;
                margin-top: 18px;
              }
            }
          }
          .desc1 {
            background: url(~@A/images/solution/noise-reduce/img_denoise.png)
              center/100% no-repeat;
          }
          .desc2 {
            background: url(~@A/images/solution/noise-reduce/img_eliminate.png)
              center/100% no-repeat;
          }
          .desc3 {
            background: url(~@A/images/solution/noise-reduce/img_noise_restrain.png)
              center/100% no-repeat;
          }
          .desc4 {
            background: url(~@A/images/solution/noise-reduce/img_squelch.png)
              center/100% no-repeat;
          }
          .desc5 {
            background: url(~@A/images/solution/noise-reduce/img_gain.png)
              center/100% no-repeat;
          }
          .desc6 {
            background: url(~@A/images/solution/noise-reduce/img_vocal_beautification.png)
              center/100% no-repeat;
          }
        }
      }
    }
    .section-item2 {
      margin-top: 50px;
      > ul {
        display: flex;
        align-items: center;
        justify-content: center;
        max-width: 1200px;
        margin: 0 auto;
        li {
          position: relative;
          width: 279px;
        }
        li + li {
          margin-left: 20px;
        }

        .cell {
          .head-image {
            width: 100%;
            height: 342px;
          }
          .image-1 {
            background: url(~@A/images/solution/noise-reduce/img_headset_scene.png)
              center/100% no-repeat;
          }
          .image-2 {
            background: url(~@A/images/solution/noise-reduce/img_handheld_scene.png)
              center/100% no-repeat;
          }
          .image-3 {
            background: url(~@A/images/solution/noise-reduce/img_hands_free_scene.png)
              center/100% no-repeat;
          }
          .image-4 {
            background: url(~@A/images/solution/noise-reduce/iimg_live_video.png)
              center/100% no-repeat;
          }
          .cell-desc {
            padding-top: 20px;
            p {
              margin-bottom: 0;
              text-align: center;

              &:first-child {
                font-size: 20px;
                font-family: Microsoft YaHei;
                font-weight: bold;
                color: #333333;
                line-height: 20px;
              }
              &:last-child {
                font-size: 16px;
                font-family: Microsoft YaHei;
                color: #999;
                font-weight: 400;
                line-height: 30px;
                margin-top: 18px;
                white-space: nowrap;
              }
            }
          }
        }
      }
    }
    .access {
      width: 1200px;
      height: 320px;
      background: #ffffff;
      box-shadow: 0px 5px 20px 0px rgba(165, 165, 165, 0.3);
      border-radius: 15px;
      display: flex;
      align-items: center;
      padding: 0 50px;
      margin: 50px auto 0;
      justify-content: space-between;
      .access-left {
        p {
          &:first-child {
            font-size: 34px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            color: #333333;
            line-height: 34px;
          }
          &:last-child {
            font-size: 16px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            color: #999999;
            line-height: 30px;
            margin-top: 40px;
          }
        }
      }
      .access-right {
        display: flex;
        align-items: center;
        .box1 {
          width: 159px;
          height: 65px;
          background: url(~@A/images/solution/noise-reduce/img_dotted_line.png)
            center/100% no-repeat;
        }
        .box2 {
          width: 159px;
          height: 65px;
          background: url(~@A/images/solution/noise-reduce/img_dashed_frame.png)
            center/100% no-repeat;
        }
        .box1,
        .box2 {
          font-size: 22px;
          font-family: Microsoft YaHei;
          font-weight: 400;
          color: $primary;
          line-height: 65px;
          text-align: center;
        }
        .arrow1 {
          width: 61px;
          height: 18px;
          background: url(~@A/images/solution/noise-reduce/arrow1.png)
            center/100% no-repeat;
        }
        .arrow2 {
          width: 76px;
          height: 150px;
          background: url(~@A/images/solution/noise-reduce/arrow2.png)
            center/100% no-repeat;
        }
        .box-wrap {
          .box2:last-child {
            margin-top: 64px;
          }
        }
      }
    }
  }

  .section-8 {
    width: 100%;
    max-width: 100%;
    margin-top: 100px;
    background: #f4f7f9;

    padding: 110px 0 109px 0;
    .section-title {
      color: #fff;
    }
    .section-item {
      width: 1100px;
      margin: 0 auto;
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 98px;
      .right-part {
        background: url(~@A/images/solution/noise-reduce/img_integration.png)
          center/100% no-repeat;
        width: 480px;
        height: 320px;
      }
      .left-part {
        padding: 18px 40px 0 40px;
        p:first-child {
          font-size: 34px;
          font-family: Microsoft YaHei;
          font-weight: 400;
          color: $grey002;
          line-height: 34px;
        }
        p:last-child {
          font-size: 16px;
          font-family: Microsoft YaHei;
          font-weight: 400;
          color: #999999;
          line-height: 30px;
          margin-top: 40px;
        }
      }
    }
  }
}
</style>
