.animate__animated {
  animation-duration: 1s;
  animation-fill-mode: both;
}

.animate__fadeInUp {
  animation-name: fadeInUp;
}

.animate__fadeInLeft {
  animation-name: fadeInLeft;
}

.animate__fadeInRight {
  animation-name: fadeInRight;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translate3d(0, 70%, 0);
  }

  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

@keyframes fadeInRight {
  0% {
    opacity: 0;
    transform: translate3d(100%, 0, 0);
  }

  to {
    opacity: 1;
    transform: translateZ(0);
  }
}

@keyframes fadeInLeft {
  0% {
    opacity: 0;
    transform: translate3d(-100%, 0, 0);
  }

  to {
    opacity: 1;
    transform: translateZ(0);
  }
}

h1,
h2,
p,
ul,
dl,
ol {
  margin-bottom: 0;
}

.el-input > .el-input__inner,
.el-textarea > .el-textarea__inner {
  border: 1px solid #e7e9ed;
  border-radius: 4px;
}

.el-input__inner,
.el-textarea__inner {
  border-radius: 4px;
}

.el-input--mini {
  height: 30px;
  border-radius: 4px;

  .el-input__inner {
    height: 30px;
  }
}

.os-text-adder-row {
  .el-input > .el-input__inner {
    border: none;
  }
}

.os-text-adder {
  border: 1px solid #e7e9ed;
  border-radius: 4px;
}

.el-button {
  font-size: 14px !important;
}

.el-button--default {
  border: 1px solid $primary;
  border-radius: 4px;
  background: transparent;
  color: $primary;
  height: 36px;
  padding: 11px 16px !important;
  min-width: 90px;
}

.el-button--primary {
  border: 1px solid $primary;
  background: $primary;
  border-radius: 4px;
  height: 36px;
  padding: 11px 16px !important;
  min-width: 90px;
  color: #fff;
}

.el-button--mini {
  height: 30px;
  border-radius: 4px;
  padding: 7px 16px !important;
}

.el-button--text {
  border-radius: 4px;
  height: 36px;
  padding: 11px 16px;
  min-width: 90px;
}

.el-table,
.el-table--fit {
  /*border: 1px solid #e7e9ed;*/
  .el-table__header-wrapper {
    height: 36px !important;
    tr {
      background: #f2f3f5;
      th {
        padding: 6px 0;
        background: #f2f3f5;
        div {
          font-size: 14px;
          font-family: PingFang SC, PingFang SC-Medium;
          font-weight: 500;
          color: #262626;
        }
      }
    }
  }

  .el-table__body-wrapper {
    tbody,
    tr {
      &:hover {
        background: #f0f6ff;
      }
      .cell {
        color: #262626;
        font-size: 14px;
        font-family: PingFang SC, PingFang SC-Regular;
        font-weight: 400;
      }
    }
  }

  .el-table__empty-block {
    width: 100% !important;
  }
}

.el-form-item__label {
  color: #262626;
}

.el-dialog__header {
  .el-dialog__title {
    font-size: 18px;
    font-family: PingFang SC, PingFang SC-Bold;
    font-weight: 700;
    text-align: left;
    color: #000000;
  }
}
