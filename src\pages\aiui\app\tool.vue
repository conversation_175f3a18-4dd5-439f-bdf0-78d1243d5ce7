<template>
  <div class="main-content-container">
    <!-- <div class="tab-container" style="margin-bottom: 39px">
      <el-tabs v-model="partType" @tab-click="onPartTypeChange">
        <el-tab-pane label="SDK接入" name="1" key="1"></el-tab-pane>
        <el-tab-pane label="WEB SOCKET接入" name="2" key="2"></el-tab-pane>
      </el-tabs>
    </div> -->
    <div
      class="tool-item"
      v-loading="sdkLoading"
      element-loading-text="SDK 生成中，请稍候"
    >
      <div class="SDK_download_wrapper">
        <div class="ability">
          <div class="ability_left">
            <div class="ability-title">SDK下载</div>
            <div class="ability-desc" style="margin-bottom: 10px">
              下载SDK，将应用快速集成到你的设备中。
            </div>
          </div>
          <div class="ability_right">
            <a class="link-btn" @click="sdkLogVisible = !sdkLogVisible"
              >查看更新日志</a
            >
            <div class="ability-split"></div>
            <a class="link-btn" :href="`${$config.docs}doc-11/`" target="_blank"
              >查看SDK接口文档</a
            >
          </div>
        </div>

        <div class="button-wrap border_bottom">
          <el-button
            size="medium"
            type="default"
            @click="downloadSDK('Android')"
            >Android SDK</el-button
          >
          <el-button size="medium" type="default" @click="downloadSDK('Linux')"
            >Linux SDK</el-button
          >
          <el-button
            size="medium"
            type="default"
            @click="downloadSDK('Windows')"
            >Windows SDK</el-button
          >
          <el-button size="medium" type="default" @click="downloadSDK('iOS')"
            >iOS SDK</el-button
          >
          <el-button
            size="medium"
            type="default"
            v-if="app.platformNum != 'all'"
            @click="downloadAIUISDK"
            >唤醒SDK</el-button
          >
        </div>
        <sdk-detail></sdk-detail>
      </div>

      <div class="awake_word_wrapper">
        <div class="ability">
          <div class="ability-content">
            <div class="ability-title">唤醒词制作</div>
            <div class="ability-desc" style="margin-bottom: 10px">
              每个词建议2-10个字，编辑完成后点击“下载”即可完成唤醒词制作。
            </div>
          </div>
        </div>
        <resource></resource>
      </div>

      <div id="advance_config_id" ref="websocket">
        <div class="ability border_bottom">
          <div class="ability-api-content">
            <div class="ability-title">WEB SOCKET接入</div>
            <div class="ability-desc">
              通过WEB API的方式将能力迅速介入的到你的设备中。
              <a
                class="link-btn"
                :href="`${$config.docs}doc-17/`"
                target="_blank"
                >查看WEB API接口文档</a
              >
            </div>
            <div class="ability-desc">
              AIUI提供了WEB API介入的
              <a
                class="link-btn"
                href="https://github.com/IflytekAIUI/DemoCode/tree/master/websocket"
                target="_blank"
                >示例代码</a
              >
            </div>
          </div>

          <!-- <div class="ability-logo api-logo"></div> -->
        </div>

        <webapiDebug
          :appInfo="app"
          :subAccountEditable="subAccountEditable"
          :appId="$route.params.appId"
        />
        <whitelist :subAccountEditable="subAccountEditable"></whitelist>
        <!-- <p class="tool-title">WeChat（微信公众号）</p> -->
        <div>
          <!-- <a
              class="tool-link"
              target="_blank"
              :href="`${$config.docs}aiui/develop/more_doc/access_wechat.html`"
              >查看帮助文档</a
            > -->

          <a
            class="tool-link"
            @click="applyBoardVisible = !applyBoardVisible"
            v-if="app.platform === 'aiui' && subAccountEditable"
            >量产板申请</a
          >
        </div>
      </div>
      <div
        :style="{
          width: '100%',
          height: advancedPlaceholderHeight,
        }"
      ></div>
    </div>
    <!-- TODO：确认aiui平台 -->
    <voice-resource
      class="tool-item"
      v-if="app.platform && app.platform.toLowerCase() === 'aiui'"
      :appInfo="app"
      :subAccountEditable="subAccountEditable"
    ></voice-resource>

    <!-- TODO: 量产板申请不用展示了么 -->
    <el-dialog
      title="量产板申请"
      :visible.sync="applyBoardVisible"
      width="500px"
    >
      <span class="apply-tips">量产板300套起卖，如需购买请和我们联系。</span>

      <div class="apply-wrap">
        <p class="apply-title">联系方式：</p>
        <p>
          <span class="mgr8">华南区：</span
          ><span class="mgr16">***********</span
          ><span class="mgr8">华东区：</span>***********
        </p>
        <p>
          <span class="mgr8">华北区：</span
          ><span class="mgr16">***********</span>
          <span class="mgr8">其他区域：</span> 0551-********
        </p>
      </div>
    </el-dialog>

    <el-dialog
      title="AIUI SDK 更新日志"
      :visible.sync="sdkLogVisible"
      width="700px"
    >
      <el-tabs v-model="activeName">
        <el-tab-pane label="Android SDK" name="Android"
          ><sdk-log platform="Android"></sdk-log
        ></el-tab-pane>
        <el-tab-pane label="Linux SDK" name="Linux"
          ><sdk-log platform="Linux"></sdk-log
        ></el-tab-pane>
        <el-tab-pane label="Windows SDK" name="Windows"
          ><sdk-log platform="Windows"></sdk-log
        ></el-tab-pane>
        <el-tab-pane label="iOS SDK" name="iOS">
          <sdk-log platform="iOS"></sdk-log
        ></el-tab-pane>
      </el-tabs>

      <span slot="footer" class="dialog-footer"></span>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { Base64 } from 'js-base64'
import webapiDebug from './webapiDebug.vue'
import voiceResource from './voiceResource.vue'
import whitelist from './whitelist.vue'
import sdkLog from './sdkLog.vue'
import resource from './resource.vue'
import sdkDetail from './sdkDetail.vue'

export default {
  name: 'app-tool',
  components: {
    webapiDebug,
    voiceResource,
    whitelist,
    sdkLog,
    resource,
    sdkDetail,
  },
  props: {
    subAccountEditable: Boolean,
  },
  data() {
    return {
      pageOptions: {
        title: '接入配置',
        loading: false,
        returnBtn: false,
      },
      sdkLoading: false,
      sdkLogVisible: false,
      applyBoardVisible: false,
      activeName: 'Android',
      partType: '1',
      advancedPlaceholderHeight: 0,
    }
  },
  methods: {
    handleObserve() {
      let that = this
      // 获取要监听的DOM元素
      const targetElement = document.getElementById('advance_config_id')

      // 创建 MutationObserver 实例
      const observer = new MutationObserver((mutations) => {
        console.log('监听到的mutation', mutations)
        mutations.forEach((mutation) => {
          // 当元素样式发生变化时
          adjustElementHeight()
        })
      })

      // 配置 MutationObserver 监听属性变化
      const config = {
        childList: true, // 子节点的变动（新增、删除或者更改）
        attributes: true, // 属性的变动
        attributeFilter: ['style'],
        characterData: true, // 节点内容或节点文本的变动
        subtree: true, // 是否将观察器应用于该节点的所有后代节点
      }

      // 开始监听
      observer.observe(targetElement, config)

      // 函数用于确保元素高度至少为一屏的高度
      function adjustElementHeight() {
        const screenHeight = window.innerHeight
        const elementHeight = targetElement.getBoundingClientRect().height
        if (elementHeight <= screenHeight) {
          console.log(
            'elementHeight <= screenHeight',
            elementHeight,
            screenHeight
          )
          that.advancedPlaceholderHeight = `${Math.max(
            screenHeight - elementHeight - 180,
            24
          )}px`
        } else {
          console.log(
            'elementHeight> screenHeight',
            elementHeight,
            screenHeight
          )
          that.advancedPlaceholderHeight = 24
        }
      }
      adjustElementHeight()
    },
    downloadSDK(platform) {
      let self = this
      self.sdkLoading = true
      this.$utils.httpGet(
        this.$config.api.AIUI_APP_DOWNLOADSDK,
        {
          appid: this.appId,
          platform,
        },
        {
          success: (res) => {
            if (res.flag) {
              location.href = res.data
            } else {
              this.$message.error(res.desc)
            }

            self.sdkLoading = false
          },
        }
      )
    },
    downloadAIUISDK() {
      window.open(
        'http://www.xfyun.cn/sdk/dispatcher?app_id=' +
          Base64.encode(this.appId),
        '_blank'
      )
    },

    onPartTypeChange() {
      let scrollDom = document.getElementById('scrollDom')
      if (this.partType === '1') {
        scrollDom.scrollTo({
          top: 0,
          behavior: 'smooth',
        })
      } else if (this.partType === '2') {
        console.log(`this.$refs['semantic'].offsetTop`, this.$refs['websocket'])
        scrollDom.scrollTo({
          top: this.$refs['websocket'].offsetTop - 180,
          behavior: 'smooth',
        })
      }
    },

    handleScroll() {
      let scrollDom = document.getElementById('scrollDom')
      if (scrollDom.scrollTop >= this.$refs['websocket'].offsetTop - 180) {
        this.partType = '2'
        return
      }

      this.partType = '1'
    },
  },
  mounted() {
    const dom = document.getElementById('scrollDom')
    if (dom) {
      dom.addEventListener('scroll', this.handleScroll)
    }
    this.handleObserve()
  },
  destroyed() {
    const dom = document.getElementById('scrollDom')
    if (dom) {
      dom.removeEventListener('scroll', this.handleScroll)
    }
  },
  computed: {
    ...mapGetters({
      app: 'aiuiApp/app',
    }),
    appId() {
      return this.$route.params.appId
    },
  },
}
</script>

<style lang="scss" scoped>
@import './config/main/common.scss';
.tool-item {
  // margin-top: 40px;
}
.tool-title {
  color: $semi-black;
  font-size: 20px;
  margin-bottom: 8px;
  font-weight: bold;
}
.tool-desc {
  color: $grey5;
  margin-bottom: 40px;
}
.tool-link {
  display: inline-block;
  width: 190px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  border-radius: 2px;
  border: 1px solid $grey3;

  &:hover {
    border-color: $primary;
  }

  & + .tool-link {
    margin-left: 10px;
  }
}
.apply-wrap {
  padding: 20px 0;
}
.apply-tips {
  color: $grey4;
  font-size: 14px;
}
.apply-title {
  color: $grey5;
  font-size: 16px;
  margin-bottom: 8px;
}
.sdk-log-wrap {
  max-height: 500px;
  overflow-y: auto;
  li {
    margin-bottom: 40px;
  }
}
.log-title {
  font-size: 16px;
  font-weight: 600;
  color: $semi-black;
  margin-bottom: 12px;

  span {
    font-weight: 600;
    margin-right: 40px;
  }
}
.log-content {
  color: $grey4;
  white-space: pre-wrap;
}
.button-wrap {
  padding-bottom: 18px;
  .el-button--default {
    background-color: #eaecf4;
    color: #555454;
    border: none;

    &:hover,
    &:focus {
      background-color: #e2e9f4;
      color: $primary;
    }
  }
}
.web-api-tips {
  p {
    color: #999999;
    margin-bottom: 10px;
  }
  .link-btn {
    color: #1f90fe;
  }
}

.tab-container {
  position: sticky;
  top: 0px;
  z-index: 2;
  background: #fff;
  height: 60px;
  border-bottom: 1px solid #e7e9ed;
  display: flex;
  align-items: center;
  justify-content: space-between;
  :deep(.el-tabs__header) {
    margin: 0;
  }
  :deep(.el-tabs__item) {
    padding: 0 50px;
    font-size: 14px;
    height: 60px;
    line-height: 60px;
  }
  :deep(.el-tabs__nav-wrap::after) {
    display: none;
  }
}

.ability {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .ability_left {
  }
  .ability_right {
    display: flex;
    align-items: center;
  }
}

.ability-logo {
  width: 167px;
  position: absolute;
  z-index: 1;
  left: 24px;
  top: 0;
}

.ability-content {
}
.ability-api-content {
  // padding-bottom: 20px;
}
.ability-title {
  font-size: 18px;
  font-weight: 600;
  color: #000000;
  white-space: nowrap;
}
.ability-split {
  width: 1px;
  height: 14px;
  background: $primary;
  margin: 0 10px;
}
.ability-desc {
  font-size: 14px;
  color: #bebfc8;
  margin-top: 5px;
}

.ability-tool {
  background: url(~@A/images/aiui5/ability-tool.png) left/cover no-repeat;
}
.tool-logo {
  background: url(~@A/images/aiui5/tool-logo.png) center/contain no-repeat;
}

.ability-awake {
  background: url(~@A/images/aiui5/ability-awake.png) left/cover no-repeat;
}
.awake-logo {
  background: url(~@A/images/aiui5/awake-logo.png) center/contain no-repeat;
}

.ability-api {
  height: 132px;
  background: url(~@A/images/aiui5/ability-api.png) left/cover no-repeat;
}
.api-logo {
  height: 132px;
  background: url(~@A/images/aiui5/tool-api.png) center/contain no-repeat;
}
.border_bottom {
  border-bottom: 1px solid $grey007;
}
#advance_config_id {
  margin-top: 30px;
  .ability {
    padding-bottom: 18px;
  }
}
</style>
<style lang="scss">
.global-debug-wrap {
  width: 1000px;

  .el-message-box__content {
    height: 400px;
    overflow: auto;
  }
}
.el-message-box__wrapper {
  position: fixed;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
}
</style>
