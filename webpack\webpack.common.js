const HtmlWebpackPlugin = require('html-webpack-plugin')
const { VueLoaderPlugin } = require('vue-loader')
const CopyWebpackPlugin = require('copy-webpack-plugin')
const {
  getExternals,
  getCdnConfig,
  resolve,
  getConditionalLoader,
} = require('./utils')
const { ProvidePlugin } = require('webpack')

module.exports = {
  entry: {
    index: './src/main.js',
  },
  output: {
    filename: 'js/[name].[fullhash:8].js',
    path: resolve('dist'),
    publicPath: '/',
  },
  cache: {
    type: 'filesystem',
    buildDependencies: {
      config: [resolve('.env.development'), resolve('.env.production')],
    },
  },
  externals: getExternals(),
  module: {
    rules: [
      {
        test: /\.js$/,
        exclude: [resolve('node_modules')],
        use: ['babel-loader', getConditionalLoader()],
      },
      {
        test: /\.mjs$/,
        use: ['babel-loader', getConditionalLoader()],
        include: [resolve('node_modules')],
        type: 'javascript/auto',
      },
      {
        test: /\.worker\.js$/,
        loader: 'worker-loader',
      },
      // {
      //   test: /\.worker\.mjs$/,
      //   loader: 'worker-loader',
      // },
      {
        test: /\.(png|gif|jpe?g|svg)$/,
        type: 'asset', // webpack5使用内置静态资源模块，且不指定具体，根据以下规则使用
        generator: {
          filename: 'img/[name]_[hash][ext]', // ext本身会附带点，放入img目录下
        },
        parser: {
          dataUrlCondition: {
            maxSize: 10 * 1024, // 超过10kb的进行复制，不超过则直接使用base64
          },
        },
        exclude: [resolve('src/assets/svgs')],
      },
      {
        test: /\.svg$/,
        include: [resolve('src/assets/svgs')],
        use: {
          loader: 'svg-sprite-loader',
          options: {
            symbolId: 'icon-[name]',
          },
        },
      },
      {
        test: /\.(ttf|woff2?|eot|otf)$/,
        type: 'asset/resource', // 指定静态资源类复制
        generator: {
          filename: 'font/[name][ext]', // 放入font目录下
        },
      },
      {
        test: /\.(mp4|webm|ogg|mp3|wav|flac|aac)(\?.*)?$/,
        type: 'asset/resource', // 指定静态资源类复制
        generator: {
          filename: 'media/[name][ext]', // 放入meida目录下
        },
      },
      {
        test: /\.vue$/,
        use: ['vue-loader', getConditionalLoader()],
      },
    ],
  },
  plugins: [
    new VueLoaderPlugin(),
    new HtmlWebpackPlugin({
      template: resolve('public/index.html'),
      inject: 'body',
      minify: {
        removeComments: true, // 移除HTML中的注释
        collapseWhitespace: true, // 删除空符与换符
        minifyCSS: true, // 压缩内联css
      },
      cdnConfig: getCdnConfig(),
    }),
    // new CopyWebpackPlugin({
    //   patterns: [
    //     {
    //       from: resolve('public'),
    //       to: resolve('dist'),
    //       globOptions: {
    //         dot: true,
    //         gitignore: true,
    //         ignore: ['**/index.html'],
    //       },
    //     },
    //   ],
    // }),
    new ProvidePlugin({
      'window.Quill': 'quill',
      Quill: 'quill',
      process: 'process/browser',
    }),
  ],
  resolve: {
    symlinks: false,
    extensions: ['.vue', '.js', '.json', '.mjs'],
    alias: {
      vue$: 'vue/dist/vue.esm.js',
      '@': resolve('src'),
      '@A': resolve('src/assets'),
      '@C': resolve('src/components'),
      '@M': resolve('src/model'),
      '@L': resolve('src/layouts'),
      '@P': resolve('src/pages'),
      '@U': resolve('src/utils'),
      'os-element': resolve('src/assets/lib/os-element'),
      sso: resolve('src/assets/lib/sso'),
      '@static': resolve('static'),
    },
    fallback: {
      crypto: require.resolve('crypto-browserify'),
      buffer: require.resolve('buffer/'),
      stream: require.resolve('stream-browserify'),
      vm: require.resolve('vm-browserify'),
      process: require.resolve('process/browser'),
    },
  },
}
