<template>
  <el-container class="is-vertical">
    <div id="Header"></div>
    <el-main style="padding-top: 60px;">
      <router-view></router-view>
    </el-main>
  </el-container>

</template>

<script>
export default {
  // 控制台 header-content布局
  name: 'consoleHC',
  data () {
    return {
      msg: 'Welcome to Your console'
    }
  },
  mounted() {
    this.$utils.initHeader()
  }
}
</script>


<style lang="scss" scoped>

</style>
