<template>
  <div v-if="tableData.total">
    <os-collapse :default="true" size="large" title="自定义修饰语">
      <os-table
        class="slot-table"
        :border="true"
        :tableData="tableData"
        ref="auxiliarySlotTable"
      >
        <el-table-column label="修饰语名称" prop="name" width="230">
          <template slot-scope="scope">
            <el-tooltip
              v-if="scope.row.source"
              effect="dark"
              content="源技能的修饰语不可编辑"
              placement="top"
            >
              <span style="color: #8c8c8c">{{ scope.row.name }}</span>
            </el-tooltip>
            <span v-else>{{ scope.row.name }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="fragments"
          label="修饰语内容"
          @click.native="toEdit(scope.row)"
        >
          <div
            slot-scope="scope"
            class="cp fragments-wrap"
            @click="toEdit(scope.row)"
          >
            <el-popover
              v-if="scope.row.fragments && scope.row.fragments.length"
              placement="bottom-start"
              trigger="hover"
            >
              <el-scrollbar style="padding-right: 12px; height: 232px">
                <p
                  class="modifier-popover-line"
                  v-for="(item, index) of scope.row.fragments"
                  :key="index"
                >
                  {{ item }}
                </p>
              </el-scrollbar>
              <template slot="reference">
                <span>{{ scope.row.fragments[0] }}</span>
                <div class="intent-tag ib">
                  {{ scope.row.fragments.length }}
                </div>
              </template>
            </el-popover>
            <a v-else>设置修饰语</a>
          </div>
        </el-table-column>
      </os-table>
      <modifier-dialog
        v-if="dialog.show"
        :dialog="dialog"
        @change="refreshData"
      ></modifier-dialog>
    </os-collapse>
    <os-divider class="mgt28" />
  </div>
</template>

<script>
import ModifierDialog from './modifierDialog/modifierDialog'
export default {
  name: 'modifier-in-intention',
  props: {
    intention: {
      type: Object,
      required: true,
    },
    subAccountEditable: Boolean,
  },
  data() {
    return {
      tableData: {
        loading: true,
        total: 0,
        page: 1,
        size: 5,
        list: [],
      },
      dialog: {
        show: false,
      },
    }
  },
  created() {
    this.getData()
  },
  methods: {
    getData() {
      this.tableData.loading = true
      let data = {
        intentId: this.intention.id,
      }
      if (this.intention.type == 7) {
        data.quoteId = this.intention.quoteId
      }
      this.$utils.httpPost(
        this.$config.api.STUDIO_MODIFIER_IN_INTENTION,
        data,
        {
          success: (res) => {
            this.tableData.loading = false
            this.tableData.list = res.data || []
            this.tableData.total = this.tableData.list.length
          },
          error: (err) => {
            this.tableData.loading = false
          },
        }
      )
    },
    toEdit(row) {
      if (!this.subAccountEditable) return
      if (row.source) return
      this.dialog.show = true
      this.dialog.modifierId = row.id
    },
    refreshData() {
      this.getData()
      this.$emit('change')
    },
  },
  components: {
    ModifierDialog,
  },
}
</script>

<style lang="scss" scoped></style>
