<template>
  <!--<div class="div-user-ask">
      <section>
        <
      </section>
  </div>-->
  <os-page :options="pageOptions">
    <div class="div-user-ask">
      <section class="sec-block">
        <div class="mgb24 btn-left">
          <el-button
            icon="ic-r-plus"
            type="primary"
            size="medium"
            @click="submitAsk()"
          >
            &nbsp;提交问题
          </el-button>
        </div>
      </section>
      <section class="sec-block">
        <div class="buy-section buy-section-3">
          <ul class="buy-section-3-header">
            <li>编号</li>
            <li>分类</li>
            <li>内容</li>
            <li>创建时间</li>
            <li>状态</li>
            <li>操作</li>
          </ul>

          <el-tabs tab-position="left" v-model="list">
            <div
              class="tab-content"
              v-if="list.list.length"
              v-loading="loading"
            >
              <div
                v-for="(item, index) in list.list"
                :key="index"
                class="tab-content-list"
              >
                <div class="tab-text" :title="item.code">
                  {{ item.code }}
                </div>
                <div class="tab-text" :title="item.type">
                  {{ item.type }}
                </div>
                <div class="tab-text" :title="item.content">
                  {{ item.content }}
                </div>
                <div class="tab-text" :title="item.createtime">
                  {{ item.createtime }}
                </div>
                <div class="tab-text" :title="item.statu">
                  {{ item.statu }}
                </div>
                <div class="tab-text" :title="查看详情">
                  <a @click="seekDetail(item)">查看详情</a>
                </div>
              </div>
            </div>
            <div
              v-else
              class="tab-content"
              style="text-align: center; padding-top: 50px; color: #959595"
            >
              暂无数据
            </div>
          </el-tabs>
          <div class="mgt24" v-if="list.total > 10">
            <el-pagination
              ref="pagination"
              v-if="list.list.length"
              class="txt-al-c"
              @current-change="getQaPair"
              :current-page="list.pageIndex"
              :page-size="list.pageSize"
              :total="list.total"
              :layout="pageLayout"
            >
            </el-pagination>
          </div>
        </div>
      </section>
    </div>
  </os-page>
</template>

<script>
export default {
  name: 'index',
  data() {
    return {
      pageOptions: {
        title: '我的提问',
        loading: false,
        returnBtn: false,
        screen: true,
      },
      list: {
        total: 1,
        pageIndex: 1,
        pageSize: 10,
        list: [],
      },
      loading: false,
    }
  },
  computed: {
    pageLayout() {
      if (this.list.total / this.list.list > 10) {
        return 'prev, pager, next, jumper, total'
      }
      return 'prev, pager, next, jumper, total'
    },
  },
  created() {
    this.getAllWorkerOrder()
  },
  methods: {
    getQaPair(page) {
      this.list.pageIndex = page
      this.getAllWorkerOrder(page)
    },
    submitAsk() {
      this.$router.push({ name: 'submitAsk' })
    },
    getAllWorkerOrder(page) {
      let self = this
      self.loading = true
      this.$utils.httpGet(
        this.$config.api.WORKER_ORDER_ALL_GET,
        {
          pageCurrent: page || this.list.pageIndex,
          pageSize: this.list.pageSize,
        },
        {
          success: (res) => {
            self.list.list = res.data.rows
            //self.oldList = JSON.parse(JSON.stringify(res.data.topics))
            //self.tableData.list = res.data.topics
            self.list.total = res.data.total
            self.list.page = this.list.pageIndex
            self.list.size = this.list.pageSize
            self.loading = false
          },
          error: (err) => {
            self.loading = false
          },
        }
      )
    },
    seekDetail(item) {
      // :href="`${'/ask/detail/' + item.id}`"
      this.$router.push(`/ask/detail/${item.id}`)
    },
  },
}
</script>

<style scoped lang="scss">
.div-user-ask {
  .sec-block {
    display: flow-root;
  }
  .btn-left {
    position: relative;
    float: right;
    margin-top: 2%;
  }
}

.buy-section-3-header {
  display: flex;
  justify-content: space-around;
  align-items: center;
  height: 56px;
  background: #f2f5f7;
  border: 1px solid #e4e7ed;
  border-bottom: 0;
  border-right: 0;
  li {
    flex: 1;
    text-align: center;
    border-right: 1px solid #e4e7ed;
    height: 56px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  :deep(.el-input__inner) {
    border: 0;
    width: 120px;
    padding-right: 30px;
    background: #f2f5f7;
  }
}

.tab-content {
  border: 1px solid #e4e7ed !important;
  border-left: 0;
  height: auto !important;
  overflow-y: auto;
  &-list:not(:last-of-type) {
    border-bottom: 1px solid #e4e7ed !important;
  }
  &-list {
    display: flex;
    height: 56px;
    justify-content: space-around;
    align-items: center;
    &:hover {
      background-color: #e8f3fd;
    }
    .add-input {
      //border: 1px solid #d5d8de;
      border-radius: 2px;
      background: #1a17e9;
      color: darkgrey;
      .el-input {
        position: relative;
        .ic-r-plus {
          position: relative;
          /*line-height: 36px;*/
          display: inline-block;
          vertical-align: top;
          /*width: 50px;*/
          text-align: center;
          color: #d5d8de;
        }
      }
    }
    &:hover &-del {
      display: block;
    }
    &-del {
      position: relative;
      color: $grey4;
      font-size: 20px;
      cursor: pointer;
      display: none;
      margin: 0 auto;
      text-align: center;
      align-items: center;
      justify-content: center;
      flex: 1;
    }
    > div {
      flex: 1;
      text-align: center;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      .san-circle {
        width: 30px;
        height: 30px;
        margin: 0 auto;
        border-radius: 50%;
        background: #1784e9;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        .san.play {
          display: none;
        }
        .span.pause {
          display: inline-block;
        }
        .er.play {
          display: inline-block;
        }
        .er.pause {
          display: none;
        }
        .san {
          width: 0;
          height: 0;
          border-left: 10px solid transparent;
          border-right: 10px solid transparent;
          border-top: 10px solid transparent;
          border-bottom: 10px solid #fff;
          border-radius: 3px;
          display: inline-block;
          transform: rotate(90deg);
          position: relative;
          left: 7px;
        }
        .er {
          span {
            display: inline-block;
            width: 4px;
            height: 15px;
            background: #fff;
            margin-top: 5px;
          }
        }
      }
    }
  }
}
</style>
