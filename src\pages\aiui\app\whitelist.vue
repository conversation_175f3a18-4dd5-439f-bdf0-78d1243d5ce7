<template>
  <!-- <os-page :options="pageOptions"> -->
  <div class="app-whitelist-page">
    <el-form
      ref="whitelistForm"
      label-width="100px"
      label-position="right"
      :disabled="!subAccountEditable"
    >
      <el-form-item
        label="白名单开关"
        class="whitelist-conf-title"
        style="margin-bottom: 0"
        label-width="90px"
      >
        <el-switch v-model="flag" @change="switchWhiteIp"></el-switch>
      </el-form-item>
      <p class="whitelist-desc">
        启用白名单时，服务器仅响应IP白名单中的数据请求，否则任意终端均可访问你的应用。
      </p>
      <el-form-item v-if="flag" label="白名单列表：">
        <os-text-adder
          class="mgb24"
          :data="ips"
          :reg="textReg"
          warning="请输入正确的ip地址"
          :max="20"
          :readonly="true"
          :disabled="!subAccountEditable"
          @add="add"
          @del="del"
          placeholder="回车新增，如： *************"
        />
      </el-form-item>
    </el-form>
  </div>
  <!-- </os-page> -->
</template>

<script>
export default {
  name: 'app-whitelist',
  props: {
    subAccountEditable: Boolean,
  },
  data() {
    return {
      pageOptions: {
        title: 'IP白名单',
        loading: false,
        returnBtn: false,
      },
      textReg:
        /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/,
      saving: false,
      flag: false,
      ips: [],
    }
  },
  computed: {
    appId() {
      return this.$store.state.aiuiApp.id
    },
  },
  created() {
    this.getWhiteIp()
  },
  methods: {
    getWhiteIp() {
      let self = this
      this.$utils.httpGet(
        this.$config.api.AIUI_APP_GET_WHITE_IPS,
        {
          appid: this.appId,
        },
        {
          success: (res) => {
            self.flag = res.data.flag
            if (res.data.ips) {
              self.ips = res.data.ips.split(',')
            }
          },
          error: (err) => {},
        }
      )
    },
    switchWhiteIp() {
      let self = this
      this.$utils.httpPost(
        this.$config.api.AIUI_APP_SWITCH_WHITE_IPS,
        {
          appid: this.appId,
          flag: this.flag,
        },
        {
          success: (res) => {
            self.$message.success('保存成功')
          },
          error: (err) => {},
        }
      )
    },
    add(text) {
      let ips = JSON.parse(JSON.stringify(this.ips))
      ips[ips.length] = text
      this.save(ips)
    },
    del(text) {
      if (this.saving) {
        return
      }
      this.saving = true
      let ips = Array.prototype.filter.call(this.ips, function (item, index) {
        return item != text
      })
      this.save(ips)
    },
    save(list) {
      let self = this
      let ips = ''
      if (list.length) {
        ips = list.length > 1 ? list.join(',') : list[0]
      }
      this.$utils.httpPost(
        this.$config.api.AIUI_APP_SET_WHITE_IPS,
        {
          appid: this.appId,
          ips: ips,
        },
        {
          success: (res) => {
            self.saving = false
            self.$message.success('保存成功')
            self.ips = list
          },
          error: (err) => {},
        }
      )
    },
  },
}
</script>

<style lang="scss" scoped>
.app-whitelist-page {
  width: 60%;
}
.whitelist-desc {
  margin: 5px 0 30px;
  color: $grey5;
}
.whitelist-conf-title {
  position: relative;
  font-size: 16px;
  font-weight: 500;
  margin-top: 24px;
  margin-bottom: 8px;
  &:before {
    width: 2px;
    height: 16px;
    background-color: $primary;
    content: ' ';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    margin: auto;
  }
}
</style>
<style lang="scss">
.whitelist-conf-title {
  .el-form-item__label {
    color: #333;
  }
}
</style>
