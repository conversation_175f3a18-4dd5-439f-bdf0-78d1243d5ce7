<template>
  <card>
    <template #title> 语音识别配置 </template>
    <div>
      <p class="item-title" style="margin-top: 0">识别设置</p>
      <p class="item-desc">以下选项仅对中文生效，不影响语义理解效果。</p>
      <div class="config-content mgb24">
        <p class="advanced-setting">
          <el-checkbox
            v-model="form.ptt"
            :disabled="!subAccountEditable"
            :true-label="1"
            :false-label="0"
            @change="emitAacChange"
            >识别结果添加标点</el-checkbox
          >
        </p>
        <p class="advanced-setting">
          <el-checkbox
            v-model="form.nunum"
            :disabled="!subAccountEditable"
            :true-label="1"
            :false-label="0"
            @change="emitAacChange"
            >识别结果优先阿拉伯数字</el-checkbox
          >
          <el-tooltip
            content="例如：系统会更倾向识别出“我今年12岁”，而不是“我今年十二岁”"
            placement="right"
          >
            <i class="ic-r-tip" />
          </el-tooltip>
        </p>
        <p class="advanced-setting">
          <el-checkbox
            v-model="form.dwa"
            :disabled="!subAccountEditable"
            :true-label="1"
            :false-label="0"
            @change="emitAacChange"
            >progressive 流式识别</el-checkbox
          >
          <el-tooltip
            content="边说边返回识别结果，不断修正，有屏设备语音输入体验更佳。"
            placement="right"
          >
            <i class="ic-r-tip" />
          </el-tooltip>
        </p>
        <!-- <p class="advanced-setting">
          <el-checkbox
            v-model="seeCanSay"
            :disabled="!subAccountEditable"
            true-label="1"
            false-label="0"
            @change="emitDhwChange"
            >所见即可说</el-checkbox
          >
          <el-tooltip
            content="开启之后，端上传界面里的文本信息，提高识别准确率从而提升语义效果。"
            placement="right"
          >
            <i class="ic-r-tip" />
          </el-tooltip>
        </p> -->
      </div>

      <p class="item-title">识别热词</p>
      <p class="item-desc">
        上传热词有助于提高词组识别的准确率，技能中的实体会自动生效为热词，不需要重复上传。热词最多支持1000条，不超过{{
          this.limitCount['hot_word_size'] / 1024
        }}kb。
      </p>

      <div class="hot-word-wrap" v-if="isHot === '1'">
        <p class="hot-word-title">{{ hotWordInfo.fileName }}</p>
        <p class="hot-word-time">
          {{ $utils.dateFormat(hotWordInfo.date, 'yyyy-MM-dd hh:mm:ss') }}
        </p>
        <div class="hot-word-config">
          <a class="mgr8" @click="downloadFile">下载</a>
          <a v-if="subAccountEditable" @click="deleteHotWords">清空</a>
        </div>
      </div>

      <div class="config-content">
        <el-upload
          class="ib"
          ref="hotWordUpload"
          :action="`${$config.server}/aiui/${
            subAccount ? 'sub' : ''
          }web/app/dist/uploadHotWordFDFS?appid=${appId}&sceneId=${
            currentScene.sceneBoxId
          }&sceneName=${currentScene.sceneBoxName}`"
          :show-file-list="false"
          :before-upload="beforeUpload"
          :on-success="handleUploadSuccess"
          :disabled="!subAccountEditable"
        >
          <el-button
            size="small"
            class="mgr16"
            :loading="isUploading"
            :disabled="!subAccountEditable"
            type="default"
            >{{ isHot === '0' ? '上传热词' : '重新上传' }}</el-button
          >
        </el-upload>
        <a @click="downloadHotWordTemplate">下载热词模板</a>
      </div>

      <recogSensitiveWord
        key="recogonition"
        ref="refSensitiveWord"
        :type="0"
        :changeType="0"
        :appId="appId"
        :limitCount="limitCount"
        :currentScene="currentScene"
        :subAccount="subAccount"
        :subAccountEditable="subAccountEditable"
        :form="form"
      />
    </div>
  </card>
</template>
<script>
import { mapGetters } from 'vuex'
import recogSensitiveWord from '../sensitiveWord/recogSensitiveWord'
import card from '../components/card'

export default {
  data() {
    return {
      form: {
        language: 'zh-cn', // 语言
        accent: 'mandarin', // 方言
        domain: 'sms', // 领域
        isFar: '0', // 距离
        ptt: '0',
        nunum: '0',
        dwa: '0',
        // isHot: '0',
        audioConf: '', // 集合配置
      },
      isHot: '0',
      hotWordInfo: {},
      isUploading: false,
      downloading: false,
      downloadTemplate: false,
      seeCanSay: false, //所见即可说
    }
  },
  created() {
    if (this.currentScene && this.currentScene.sceneBoxId) {
      this.getAacConf()
      this.getHotWordInfo()
      this.getDhwConfig()
    }
  },
  computed: {
    ...mapGetters({
      currentScene: 'aiuiApp/currentScene',
      appInfo: 'aiuiApp/app',
      limitCount: 'aiuiApp/limitCount',
      subAccount: 'user/subAccount',
      subAccountEditable: 'aiuiApp/subAccountEditable',
    }),
    appId() {
      return this.$route.params.appId
    },
  },
  watch: {
    currentScene(scene) {
      if (scene && scene.sceneBoxId) {
        this.getAacConf()
        this.getHotWordInfo()
        this.getDhwConfig()
      }
    },
  },
  methods: {
    getHotWordInfo() {
      let self = this
      this.$utils.httpGet(
        this.$config.api.AIUI_DIST_HOTWORD,
        {
          appid: this.appId,
          sceneId: this.currentScene.sceneBoxId,
          sceneName: this.currentScene.sceneBoxName,
        },
        {
          success: (res) => {
            if (res.data?.fileName) {
              self.isHot = '1'
            }
            self.hotWordInfo = res.data
          },
        }
      )
    },

    downloadHotWordTemplate() {
      let self = this
      if (!this.downloadTemplate) {
        this.downloadTemplate = true
        window.open(
          `${this.$config.server}/aiui/web/download?url=https://aiui-file.cn-bj.ufileos.com/hot_words.txt&fileName=hot_words.txt&code=GB2312`,
          '_self'
        )
        setTimeout(function () {
          self.downloadTemplate = false
        }, 5000)
      } else {
        this.$message.warning('操作过快，请稍后再试')
      }
    },
    beforeUpload(file, fileList) {
      if (file.type != 'text/plain') {
        this.$message.warning('导入文件格式不对')
        return false
      }
      if (file.size > this.limitCount['hot_word_size']) {
        this.$message.warning(
          `导入文件不得超过${this.limitCount['hot_word_size'] / 1024}KB`
        )
        return false
      }
      this.isUploading = true
    },
    handleUploadSuccess(data) {
      if (data.flag) {
        this.isHot = '1'
        this.getHotWordInfo()
      } else {
        this.$message.warning(data.desc)
      }
      this.isUploading = false
    },
    downloadFile() {
      let self = this
      if (!this.downloading) {
        let isSubAccount = this.subAccount ? true : false
        this.downloading = true
        this.$utils.postopen(
          this.$config.api.AIUI_DIST_DOWN_HOT_WORD,
          {
            appid: this.appId,
            sceneId: this.currentScene.sceneBoxId,
            sceneName: this.currentScene.sceneBoxName,
          },
          isSubAccount
        )

        setTimeout(function () {
          self.downloading = false
        }, 5000)
      } else {
        this.$message.warning('操作过快，请稍后再试')
      }
    },
    deleteHotWords() {
      let self = this
      let data = {
        appid: self.appId,
        sceneId: self.currentScene.sceneBoxId,
        sceneName: self.currentScene.sceneBoxName,
      }

      this.$utils.httpPost(this.$config.api.AIUI_DIST_DELETE_HOT_WORD, data, {
        success: (res) => {
          self.isHot = '0'
          self.hotWordInfo = {}
        },
      })
    },
    getDhwConfig() {
      let self = this
      this.$utils.httpGet(
        this.$config.api.AIUI_DIST_DHW,
        {
          appid: this.appId,
          sceneName: this.currentScene.sceneBoxName,
        },
        {
          success: (res) => {
            this.seeCanSay = res.data.dhw
          },
        }
      )
    },
    saveDhwConfig() {
      let self = this
      this.$utils.httpGet(
        this.$config.api.AIUI_DIST_SAVE_DHW,
        {
          appid: this.appId,
          sceneName: this.currentScene.sceneBoxName,
          dhw: this.seeCanSay,
        },
        {
          success: (res) => {
            this.initSeeCanSay = this.seeCanSay
          },
        }
      )
    },

    getAacConf() {
      let self = this
      this.$utils.httpGet(
        this.$config.api.AIUI_BOT_IAT_GETCONFIG,
        {
          botId: this.currentScene.botBoxId,
        },
        {
          success: (res) => {
            if (res.flag) {
              if (!res.data) {
                self.getDataEnd = self.getDataEnd + 1
                return
              }
              let formInitData = res.data
              if (!formInitData.config) {
                formInitData.config = {}
              }
              // 组合字段拼接
              if (
                res.data.language == 'zh-cn' &&
                (res.data.domain == 'sos-tv' || res.data.domain == 'tv') &&
                res.data.isFar == '0'
              ) {
                formInitData.audioConf = 'sos-tv,zh-cn,0'
              } else {
                formInitData.audioConf = [
                  res.data.domain,
                  res.data.language,
                  res.data.isFar,
                  res.data.accent,
                ].join()
              }
              self.form = formInitData
              if (Object.keys(self.form.config).length) {
                let temp1 =
                  (self.form.config.accent &&
                    JSON.parse(self.form.config.accent)) ||
                  {}
                let temp2 =
                  (self.form.config.domain &&
                    JSON.parse(self.form.config.domain)) ||
                  {}
                self.accentConfigKeys = Object.keys(temp1)
                self.domainConfigKeys = Object.keys(temp2)
              }
              self.getDataEnd = self.getDataEnd + 1
              if (
                res.data.config &&
                res.data.config.hasOwnProperty('translateScene')
              ) {
                self.$emit('setTranslateScene', res.data.config.translateScene)
              }
            } else {
              this.$message.error(res.desc)
            }
          },
          error: (err) => {
            console.log('page=>>')
            console.log(err)
          },
        }
      )
    },

    saveAac() {
      let data = {
        botId: this.currentScene.botBoxId,
        platform: this.appInfo.platform,
        language: this.form.language,
        isFar: this.form.isFar,
        accent: this.form.accent,
        domain: this.form.domain,
        ptt: this.form.ptt || '0',
        nunum: this.form.nunum,
        dwa: this.form.dwa,
      }
      let self = this
      this.$utils.httpPost(
        this.$config.api.AIUI_BOT_IAT_SAVECONFIG,
        JSON.stringify(data),
        {
          config: {
            headers: {
              'Content-Type': 'application/json;charset=UTF-8',
            },
          },
          success: (res) => {
            if (res.flag) {
            } else {
            }
          },
          error: (err) => {},
        }
      )
    },

    emitAacChange() {
      this.saveAac()
    },
    emitDhwChange() {
      this.saveDhwConfig()
    },
  },
  components: { recogSensitiveWord, card },
}
</script>
<style lang="scss" scoped>
@import '../common.scss';
@import './style.scss';
</style>
