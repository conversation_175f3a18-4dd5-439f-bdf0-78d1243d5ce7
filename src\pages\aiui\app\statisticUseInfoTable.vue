<template>
  <div class="app-use-info-wrap">
    <statistic-normal v-if="modelAuth === '1'"></statistic-normal>
    <statistic-model v-else-if="modelAuth === '2'"></statistic-model>

    <div v-else class="statistic-loading" v-loading="true"></div>
  </div>
</template>
<script>
import statisticNormal from './statisticNormal.vue'
import statisticModel from './statisticModel.vue'
// import priceList from './priceList'
// import { mapGetters } from 'vuex'

export default {
  name: 'statistic-use-info-table',
  data() {
    return {
      // 有没有大模型权限 1 aiui 4.0 2 交互大模型
      // modelAuth: '0'
      modelAuth: '0',
    }
  },
  created() {
    // 检查应用是否有大模型权限
    this.checkAppAuth()
  },

  methods: {
    checkAppAuth() {
      this.$utils.httpGet(
        this.$config.api.AIUI_APP_SCENE_CHECKAUTH,
        {
          appid: this.$route.params.appId,
          point: '13',
        },
        {
          success: (res) => {
            if (res.flag) {
              if (res.data.check) {
                this.modelAuth = '2'
              } else {
                this.modelAuth = '1'
              }
            }
          },
        }
      )
    },
  },
  components: {
    // priceList,
    statisticNormal,
    statisticModel,
  },
}
</script>
<style lang="scss" scoped>
.statistic-loading {
  width: 100%;
  height: 100px;
}
</style>
