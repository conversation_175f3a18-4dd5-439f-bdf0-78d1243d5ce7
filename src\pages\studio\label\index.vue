<template>
  <div class="os-scroll">
    <div class="entity-page">
      <div class="entity-page-head">
        <i class="ic-r-angle-l-line entity-page-head-back" @click="back" />
        <el-popover placement="bottom" width="264" trigger="click">
          <select-label :subAccount="subAccount" />
          <div slot="reference" class="entity-page-head-title">
            <span
              style="max-width: 250px"
              class="txt-ellipsis-nowrap"
              :title="oldLabel.zhName"
              >{{ oldLabel.zhName }}</span
            >
            <i class="ic-r-triangle-down" />
          </div>
        </el-popover>
        <div class="header-right entity-page-head-right">
          <!-- <div> -->
          <!-- <p class="header-save-time">
              最近由<span class="text-blod" style="color: #262626">{{
                label.operator
              }}</span>
            </p> -->
          <div
            v-if="label.updateTime"
            class="header-save-time"
            style="text-align: right"
          >
            最近保存{{ label.updateTime | time }}
          </div>
          <!-- </div> -->
        </div>
      </div>
      <os-divider class="mgb56" />
      <div class="mgb56">
        <os-page-label label="基本信息" class="mgb24" />
        <el-form
          :model="label"
          ref="entityForm"
          label-width="104px"
          :rules="rules"
          inline
          label-position="left"
        >
          <el-form-item
            label="交互标签名称"
            prop="zhName"
            class="entity-page-form-item"
            style="width: 380px"
          >
            <template v-if="!edit">
              <span class="entity-name" :title="label.zhName">{{
                label.zhName || '-'
              }}</span>
              <div class="ib entity-edit-btn" @click="toEdit">
                <i class="ic-r-edit" />
                <span>编辑</span>
              </div>
            </template>
            <div v-else @keyup.enter="editEntityBlur">
              <el-input
                v-model="label.zhName"
                ref="labelNameInput"
                class="entity-page-form-input"
                placeholder="请输入交互标签名称"
              />
              <input type="text" style="display: none" />
              <i
                class="entity-page-form-save el-icon-check"
                @click="editZhame"
              />
              <i
                class="entity-page-form-cancel el-icon-close"
                @mousedown="cancelEdit"
              />
            </div>
          </el-form-item>

          <el-form-item
            label="英文标识"
            prop="value"
            class="entity-page-form-item ib"
          >
            <span>{{ label.value }}</span>
          </el-form-item>
          <!-- <div class="upload-container"> -->
          <el-form-item
            label="图片"
            prop="picture"
            class="entity-page-form-item ib"
          >
            <el-upload
              v-loading="loading"
              :action="`${$config.server}${this.$store.state.user.baseUrl}/skill/avatar/upload`"
              :show-file-list="false"
              :on-success="uploadSuccess"
              :before-upload="beforeUpload"
            >
              <img v-if="label.picture" :src="label.picture" class="avatar" />
              <i v-else class="ic-r-plus avatar-uploader-icon"></i>
            </el-upload>
          </el-form-item>
          <p class="text-tip">
            (支持gif/jpg/png/jpeg文件，宽高须相等，大小不超过500k)
          </p>
          <!-- </div> -->
          <el-form-item
            label="描述"
            prop="description"
            class="entity-page-form-item ib item-description"
          >
            <template v-if="!editDes">
              <span class="entity-name" :title="label.description">{{
                label.description || '-'
              }}</span>
              <div class="ib entity-edit-btn" @click="toEditDes">
                <i class="ic-r-edit" />
                <span>编辑</span>
              </div>
            </template>
            <div v-else @keyup.enter="editEntityBlur" class="edit-wrap">
              <el-input
                ref="desInput"
                v-model.trim="label.description"
                placeholder="支持中文/标点符号，不超过50个字符"
              />
              <input type="text" style="display: none" />
              <i
                class="entity-page-form-save el-icon-check"
                @click="editDescription"
              />
              <i
                class="entity-page-form-cancel el-icon-close"
                @mousedown="cancelEditDes"
              />
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import SelectLabel from './selectLabel.vue'

export default {
  name: 'label',
  data() {
    return {
      rules: {
        zhName: [
          this.$rules.required('动作中文名不能为空'),
          this.$rules.lengthLimit(1, 32, '动作中文名长度不能超过32个字符'),
          this.$rules.baseRegLimit(),
        ],
        description: [
          this.$rules.lengthLimit(1, 50, '动作描述长度不能超过50个字符'),
          {
            pattern:
              /^[\u4e00-\u9fa5\s\·\~\！\@\#\￥\%\……\&\*\（\）\——\-\+\=\【\】\{\}\、\|\；\‘\’\：\“\”\《\》\？\，\。\、\`\~\!\#\$\%\^\&\*\(\)\_\[\]{\}\\\|\;\'\'\:\"\"\,\.\/\<\>\?]{0,}$/,
            message: '只支持汉字/标点符号',
            trigger: 'blur',
          },
        ],
      },
      edit: false,
      editDes: false,
      label: {},
      oldLabel: {},

      limitSize: 0.5, // 单位/M
      loading: false,
      //   imgBeforeUpload: '',
    }
  },
  computed: {
    ...mapGetters({
      limitCount: 'aiuiApp/limitCount',
      subAccount: 'user/subAccount',
    }),
  },
  created() {
    if (this.$route.params.labelId) {
      this.labelId = this.$route.params.labelId
      this.getDetail()
    } else {
      this.back()
    }
  },
  methods: {
    back() {
      this.subAccount
        ? this.$router.push({ name: 'sub-studio-handle-platform-labels' })
        : this.$router.push({ name: 'studio-handle-platform-labels' })
    },
    getDetail() {
      //  获取详情
      let self = this
      this.$utils.httpGet(
        this.$config.api.STUDIO_LABEL_DETAIL,
        {
          id: this.labelId,
        },
        {
          success: (res) => {
            self.label = res.data
            self.oldLabel = JSON.parse(JSON.stringify(res.data))
          },
          error: (err) => {
            // self.$router.push({ name: 'studio-handle-platform-auxiliaries' })
          },
        }
      )

      this.oldLabel = JSON.parse(JSON.stringify(this.label))
    },
    toEdit() {
      this.edit = true
      this.$nextTick(function () {
        this.$refs['labelNameInput'] && this.$refs['labelNameInput'].focus()
      })
    },
    toEditDes() {
      this.editDes = true
      this.$nextTick(function () {
        this.$refs['desInput'] && this.$refs['desInput'].focus()
      })
    },
    cancelEdit() {
      this.edit = false
      this.label.zhName = this.oldLabel.zhName
      this.$refs.entityForm && this.$refs.entityForm.clearValidate('zhName')
    },
    cancelEditDes() {
      this.editDes = false
      this.label.description = this.oldLabel.description
      this.$refs.entityForm &&
        this.$refs.entityForm.clearValidate('description')
    },
    editEntityBlur(e) {
      e.target.blur()
    },
    editZhame() {
      if (this.label.zhName === this.oldLabel.zhName) {
        this.edit = false
        return
      }
      if (!this.edit) {
        return
      }
      this.$refs.entityForm.validateField('zhName', (valid) => {
        if (valid) {
          return
        }
        this.$utils.httpPost(
          this.$config.api.STUDIO_LABEL_EDIT,
          {
            id: this.labelId,
            zhName: this.label.zhName,
          },
          {
            success: (res) => {
              this.$message.success('修改成功')
              this.oldLabel.zhName = this.label.zhName
              this.edit = false
              this.getDetail()
            },
            error: (err) => {},
          }
        )
      })
    },

    editDescription() {
      if (this.label.description === this.oldLabel.description) {
        this.editDes = false
        return
      }
      if (!this.editDes) {
        return
      }
      this.$refs.entityForm.validateField('description', (valid) => {
        if (valid) {
          return
        }
        this.$utils.httpPost(
          this.$config.api.STUDIO_LABEL_EDIT,
          {
            id: this.labelId,
            zhName: this.oldLabel.zhName,
            description: this.label.description,
          },
          {
            success: (res) => {
              this.$message.success('修改成功')
              this.oldLabel.description = this.label.description
              this.editDes = false
              this.getDetail()
            },
            error: (err) => {},
          }
        )
      })
    },

    beforeUpload(file) {
      let isPNGOrJPG =
        file.type === 'image/gif' ||
        file.type === 'image/png' ||
        file.type === 'image/jpg' ||
        file.type === 'image/jpeg'
      let unExceed = file.size < 1024 * 1024 * 0.5
      if (!isPNGOrJPG) {
        this.$message.error('仅支持.png .jpg .jpeg格式文件')
        return false
      }
      if (!unExceed) {
        this.$message.error(`文件不能超过500k`)
        return false
      }
      let self = this
      self.loading = true
      return new Promise(function (resolve, reject) {
        let reader = new FileReader()
        reader.readAsDataURL(file)
        reader.onload = function (theFile) {
          let img = new Image()
          img.src = theFile.target.result
          img.onload = function () {
            if (img.height !== img.width) {
              self.$message.error(`图片宽高尺寸应相同`)
              self.loading = false
              reject('图片尺寸不对')
            } else {
              //   self.imgBeforeUpload = img.src
              resolve(file)
            }
          }
        }
      })
    },
    uploadSuccess(res) {
      if (res.data) {
        this.loading = false
        this.changePicture(res.data.url)
      } else {
        this.loading = false
        this.$message.error(res.desc)
      }
    },
    changePicture(url) {
      this.$utils.httpPost(
        this.$config.api.STUDIO_LABEL_EDIT,
        {
          id: this.labelId,
          zhName: this.oldLabel.zhName,
          picture: url,
        },
        {
          success: (res) => {
            this.$message.success('修改成功')
            this.label.picture = url
            this.oldLabel.picture = url
            this.editDes = false
            this.getDetail()
          },
          error: (err) => {},
        }
      )
    },
  },
  components: {
    SelectLabel,
  },
}
</script>

<style lang="scss">
.entity-page {
  max-width: 1200px;
  padding-top: 24px;
  padding-bottom: 70px;
  margin: auto;
  &-head {
    font-size: 24px;
    margin-bottom: 21px;
    display: flex;
    align-items: center;
    position: relative;
    &-back {
      cursor: pointer;
      margin-right: 16px;
      color: $grey4;
    }
    &-title {
      flex: auto;
      display: flex;
      align-items: center;
      cursor: pointer;
      i {
        color: $grey5;
        margin-left: 4px;
      }
    }
    &-right {
      position: absolute;
      right: 0;
    }
  }
}
.entity-name {
  display: inline-block;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.entity-edit-btn {
  vertical-align: top;
  margin-left: 9px;
  color: $primary;
  cursor: pointer;
}

.entity-page-form-item {
  vertical-align: top;
  width: 30%;
  margin-bottom: 0;
}
.entity-page-form-input {
  width: 188px;
}
.entity-page-form-save,
.entity-page-form-cancel {
  margin-left: 16px;
  cursor: pointer;
  &:hover {
    color: $primary;
  }
}
.entry-search-area {
  width: 240px;
}
.entry-value {
  input {
    height: 54px !important;
    line-height: 54px !important;
    border: 0 !important;
    padding-left: 0;
    color: $semi-black;
    font-weight: 600;
  }
}

.entry-table {
  .el-table__row td {
    padding: 0;
    height: 56px;
  }
  .el-table__body tr:hover > td {
    background-color: #fff;
  }
  td .cell {
    padding-left: 0;
  }
}
.upload-cover-dialog {
  .el-button {
    padding: 0;
    width: 130px;
    height: 44px;
    line-height: 44px;
    overflow: hidden;
  }
  .el-upload .el-button {
    width: 130px;
    height: 44px;
    font-size: 16px;
    color: $dangerous;
  }
  .dialog-title {
    font-size: 20px;
  }
  .ic-r-exclamation {
    margin-right: 16px;
    vertical-align: -1px;
    color: $warning;
  }
  .el-dialog__header {
    padding: 32px 32px 12px;
  }
  .el-dialog__body {
    padding-top: 0;
    padding-bottom: 23px;
    font-size: 16px;
    color: $semi-black;
    padding-left: 70px;
  }
  .el-dialog__footer {
    padding-bottom: 32px;
  }
}
</style>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="scss" scoped>
.avatar-uploader-icon {
  width: 80px;
  height: 80px;
  line-height: 80px;
}
.ic-r-plus {
  font-size: 32px;
  color: $grey3;
}
.upload-tip {
  padding-top: 7px;
  padding-left: 16px;
  line-height: 22px;
}
.text-tip {
  font-size: 12px;
  color: #666;
  margin-bottom: 0;
  padding-left: 60%;
  //   position: absolute;
  //   z-index: 1;
  //   top: -38px;
  //   left: 40px;
}

.upload-container {
  position: relative;
}

:deep(.el-upload) {
  width: 80px;
  height: 80px;
  border-radius: 2px;
  border: 1px dashed #d9d9d9;
  text-align: left;
  .avatar {
    width: 100%;
    height: 100%;
  }
  i {
    padding-left: 23px;
  }
}

.item-description {
  width: 60%;
  :deep(.el-form-item__content) {
    width: 60%;
  }
}

.edit-wrap {
  display: flex;
  align-items: center;
}
</style>
