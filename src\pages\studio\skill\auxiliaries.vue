<template>
  <os-page :options="pageOptions">
    <studio-skill-header-right slot="btn" />
    <div class="os-scroll">
      <div
        v-if="$store.state.studioSkill.skill.type != '3'"
        class="mgt32 mgb24"
        @keyup.enter="searchEntities"
      >
        <el-input
          class="search-area"
          placeholder="搜索引用的辅助词"
          v-model="entitySearchName"
        >
          <i
            slot="suffix"
            class="el-input__icon el-icon-search search-area-btn"
            @click="searchEntities"
          />
        </el-input>
      </div>
      <div v-else class="mgt32 mgb24" @keyup.enter="searchEntities">
        <div class="btn-wrap">
          <el-button
            size="medium"
            :type="activeType == 'all' ? 'primary' : ''"
            plain
            @click="activeType = 'all'"
            >全部</el-button
          >
          <el-tooltip effect="dark" content="源技能的辅助词" placement="top">
            <el-button
              size="medium"
              :type="activeType == 'sourceSkill' ? 'primary' : ''"
              plain
              @click="activeType = 'sourceSkill'"
              >源技能的</el-button
            >
          </el-tooltip>
          <el-tooltip
            effect="dark"
            content="所有新增的非源技能的辅助词"
            placement="top"
          >
            <el-button
              size="medium"
              :type="activeType == 'notSourceSkill' ? 'primary' : ''"
              plain
              @click="activeType = 'notSourceSkill'"
              >新增</el-button
            >
          </el-tooltip>
          <el-tooltip effect="dark" content="所有官方辅助词" placement="top">
            <el-button
              size="medium"
              :type="activeType == 'iflyos' ? 'primary' : ''"
              plain
              @click="activeType = 'iflyos'"
              >官方</el-button
            >
          </el-tooltip>
          <el-tooltip effect="dark" content="所有自定义辅助词" placement="top">
            <el-button
              size="medium"
              :type="activeType == 'self' ? 'primary' : ''"
              plain
              @click="activeType = 'self'"
              >自定义</el-button
            >
          </el-tooltip>
        </div>
        <el-input
          class="extend-search-area"
          size="medium"
          placeholder="搜索引用的辅助词"
          v-model="entitySearchName"
        >
          <i
            slot="suffix"
            class="el-input__icon el-icon-search search-area-btn"
            @click="searchEntities"
          />
        </el-input>
      </div>
      <os-table
        class="skill-auxiliaries-table"
        :tableData="tableData"
        @change="changePage"
        @edit="toEdit"
      >
        <el-table-column type="index" width="50" label="#">
          <template slot-scope="scope">
            {{ (tableData.page - 1) * tableData.size + scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="zhName" label="辅助词名称">
          <template slot-scope="scope">
            <div class="intent-zhname ib" @click="toEdit(scope.row)">
              {{ scope.row.zhName }}
            </div>
            <el-tooltip
              v-if="scope.row.source == 1"
              effect="dark"
              content="源技能的辅助词"
              placement="top"
            >
              <div class="intent-tag ib source-skill-icon">源</div>
            </el-tooltip>
            <div class="intent-tag ib" v-if="scope.row.type === 7">官</div>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="辅助词标识"> </el-table-column>
        <el-table-column prop="description" label="描述"> </el-table-column>
        <el-table-column prop="example" label="示例"> </el-table-column>
        <el-table-column prop="count" label="词条数"> </el-table-column>
      </os-table>
    </div>
  </os-page>
</template>

<script>
export default {
  name: 'skill-auxiliaries',
  data() {
    return {
      pageOptions: {
        title: '引用的辅助词',
        loading: false,
      },
      entitySearchName: '',
      tableData: {
        loading: true,
        total: 0,
        page: 1,
        size: 10,
        handles: ['edit'],
        handleColumnText: '操作',
        list: [],
      },
      activeType: 'all',
      originList: [],
    }
  },
  computed: {
    businessId() {
      return this.$store.state.studioSkill.id
    },
  },
  watch: {
    activeType(val) {
      this.formate(val)
    },
  },
  created() {
    this.getEntities()
  },
  methods: {
    getEntities(page) {
      this.tableData.loading = true
      this.$utils.httpGet(
        this.$config.api.STUDIO_SKILL_ENTITYS,
        {
          businessId: this.businessId,
          pageIndex: page || this.tableData.page,
          pageSize: this.tableData.size,
          search: this.entitySearchName,
          type: 1,
        },
        {
          success: (res) => {
            this.originList = res.data.entities

            this.formate(this.activeType)
          },
          error: (err) => {
            console.log('page=>>')
            console.log(err)
          },
        }
      )
    },
    searchEntities() {
      this.getEntities(1)
    },
    toEdit(entity) {
      if (entity.type !== 6) {
        return this.$message.warning('该辅助词为官方辅助词，不可编辑')
      }
      if (entity.source == 1) {
        return this.$message.warning('源技能的辅助词不可编辑')
      }
      let routeData = this.$router.resolve({
        name: 'auxiliary',
        params: { entityId: entity.id },
      })
      window.open(routeData.href, '_blank')
    },
    changePage(val) {
      this.tableData.page = val
      this.formate(this.activeType)
    },
    formate(type) {
      let tmp
      let list = []
      let page = this.tableData.page
      let size = this.tableData.size
      for (let i = 0; i < this.originList.length; i++) {
        let item = this.originList[i]
        if (type == 'all') {
          if (
            (item.type === 7 &&
              !item.hasOwnProperty('dictDetail') &&
              !item.hasOwnProperty('noEdit')) ||
            item.source === 1
          ) {
            item.noEdit = true
          }
          list.push(item)
        }
        if (
          type == 'iflyos' &&
          item.type === 7 &&
          !item.hasOwnProperty('dictDetail')
        ) {
          if (!item.hasOwnProperty('noEdit')) {
            item.noEdit = true
          }
          list.push(item)
        }
        if (type == 'sourceSkill' && item.source === 1) {
          if (!item.hasOwnProperty('noEdit')) {
            item.noEdit = true
          }
          list.push(item)
        }
        if (type == 'notSourceSkill' && item.source !== 1) {
          list.push(item)
        }
        if (type == 'self' && item.type !== 7 && item.source !== 1) {
          list.push(item)
        }
      }
      this.tableData.total = list.length
      this.tableData.list.splice(0)
      this.tableData.list = list.slice((page - 1) * size, page * size)
      this.tableData.loading = false
    },
  },
  components: {},
}
</script>

<style lang="scss" scoped>
.search-area-btn {
  cursor: pointer;
}
.intent-handle-group {
  position: relative;
  margin-right: -3px;
  &::after {
    position: absolute;
    content: ' ';
    width: 1px;
    height: 100%;
    top: 0;
    right: -1px;
    background-color: $grey3;
  }
}
.intent-zhname {
  margin-right: 7px;
  cursor: pointer;
  font-weight: 600;
}
.source-skill-icon {
  margin-right: 4px;
}
.btn-wrap {
  display: inline-block;
  font-size: 0;
  .el-button {
    margin-left: 0;
    padding: 10px 16px;
    min-width: unset;
    background: $white;
    border-color: $grey3;
    border-right: none;
    border-radius: 0;
    &:first-child {
      border-radius: 2px 0 0 2px;
    }
    &:last-child {
      border-right: 1px solid $grey3;
      border-radius: 0 2px 2px 0;
      &:hover {
        border-right: 1px solid $primary;
      }
    }
  }
  .el-button:hover,
  .el-button:focus,
  .el-button--primary {
    border-color: $primary;
    background-color: $primary-light-12;
  }
  .el-button:hover + .el-button {
    border-left-color: $primary;
  }
  .el-button:focus + .el-button,
  .el-button--primary + .el-button {
    border-left-color: transparent;
  }
  .el-button--primary:hover,
  .el-button--primary:focus {
    box-shadow: unset;
  }
  .el-button--primary {
    color: $primary;
    border: 1px solid $primary !important;
  }
}
.extend-search-area {
  float: right;
  width: 240px;
}
</style>
<style lang="scss">
.skill-auxiliaries-table {
  tr:hover {
    .intent-zhname {
      color: $primary;
    }
  }
  .el-table .ic-r-edit {
    color: $primary;
  }
}
</style>
