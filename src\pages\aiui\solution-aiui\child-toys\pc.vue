<template>
  <div class="main-content">
    <section class="main-content-banner">
      <div class="banner-text">
        <h2><span>儿童玩具解决方案</span></h2>
        <p class="banner-text-content">
          有趣的大模型语音交互
          <br />
          角色互动、情感对话和知识启蒙
          <br />
          接入开源，快速完成玩具方案验证
        </p>
        <div class="banner-side-image"></div>

        <div class="banner-text-button" @click="toConsole">合作咨询</div>
      </div>
    </section>

    <div class="section_0_wrap">
      <section class="section_0">
        <div class="section-title">
          <span class="section-title-bold">
            <span style="margin-right: 30px">会说话的玩伴</span
            ><span>会思考的童年</span></span
          >
        </div>
        <div
          class="video_container"
          @click="isPlaying = true"
          v-show="!isPlaying"
        >
          <div class="play_button"></div>
        </div>
        <div v-show="isPlaying" class="case_video_player">
          <!-- <div class="case_video_player_inner">
          <video :src="videoSrc" autoplay preload controls></video>
        </div> -->
          <video ref="videoPlayer" :src="videoSrc" preload controls></video>
        </div>
      </section>
    </div>

    <div class="section section1">
      <div class="section-title">应用场景</div>
      <p class="section-sub-title">对话式儿童陪伴玩具</p>
      <div class="section-content">
        <div class="content-item">
          毛绒玩具
          <div class="toys"></div>
        </div>
        <div class="content-item">
          塑料玩具
          <div class="toys"></div>
        </div>
        <div class="content-item">
          儿童陪伴机器人
          <div class="toys"></div>
        </div>
        <div class="content-item">
          儿童故事机
          <div class="toys"></div>
        </div>
      </div>
    </div>

    <div class="section-3-wrap">
      <section class="section-option section-3">
        <div class="section-title">
          <span class="section-title-bold">方案优势</span>
        </div>

        <ul class="advantage">
          <li>
            <div class="advantage-text">
              <p class="advantage-tag-wrap">
                <span class="advantage-tag"></span
                ><span class="advantage-tag-text">快速打造玩具IP</span>
              </p>
              <ul>
                <li>
                  <i class="tick"></i
                  ><span class="tick-text">自定义玩具人设及对话风格</span>
                </li>
                <li>
                  <i class="tick"></i
                  ><span class="tick-text"
                    >自定义大模型文档问答，实现品牌知识问答</span
                  >
                </li>
              </ul>
            </div>
            <div class="advantage-image"></div>
          </li>

          <li>
            <div class="advantage-image"></div>
            <div class="advantage-text">
              <p class="advantage-tag-wrap">
                <span class="advantage-tag"></span
                ><span class="advantage-tag-text">有趣有料的儿童专属内容</span>
              </p>
              <ul>
                <li>
                  <i class="tick"></i
                  ><span class="tick-text"
                    >丰富的音乐、故事等正版儿童音频信源</span
                  >
                </li>
                <li>
                  <i class="tick"></i
                  ><span class="tick-text"
                    >孩子与大模型共同创作故事，释放孩子想象力</span
                  >
                </li>
              </ul>
            </div>
          </li>

          <li>
            <div class="advantage-text">
              <p class="advantage-tag-wrap">
                <span class="advantage-tag"></span
                ><span class="advantage-tag-text">儿童科普满足好奇心</span>
              </p>
              <ul>
                <li>
                  <i class="tick"></i
                  ><span class="tick-text">科普大模型，随时随地知识问答</span>
                </li>
                <li>
                  <i class="tick"></i
                  ><span class="tick-text">孩子听得懂的趣味讲解</span>
                </li>
              </ul>
            </div>
            <div class="advantage-image"></div>
          </li>

          <li>
            <div class="advantage-image"></div>
            <div class="advantage-text">
              <p class="advantage-tag-wrap">
                <span class="advantage-tag"></span
                ><span class="advantage-tag-text"
                  >儿童口语启蒙，锻炼表达能力</span
                >
              </p>
              <ul>
                <li>
                  <i class="tick"></i
                  ><span class="tick-text">提供口语老师教学陪练</span>
                </li>
                <li>
                  <i class="tick"></i
                  ><span class="tick-text">支持智能评价反馈</span>
                </li>
              </ul>
            </div>
          </li>

          <li>
            <div class="advantage-text">
              <p class="advantage-tag-wrap">
                <span class="advantage-tag"></span
                ><span class="advantage-tag-text"
                  >儿童共情闲聊，呵护心理健康</span
                >
              </p>
              <ul>
                <li>
                  <i class="tick"></i
                  ><span class="tick-text">大模型在对话中识别情绪变化</span>
                </li>
                <li>
                  <i class="tick"></i
                  ><span class="tick-text"
                    >正面回应孩子心理需求，给予鼓励与引导</span
                  >
                </li>
              </ul>
            </div>
            <div class="advantage-image"></div>
          </li>

          <li>
            <div class="advantage-image"></div>
            <div class="advantage-text">
              <p class="advantage-tag-wrap">
                <span class="advantage-tag"></span
                ><span class="advantage-tag-text">复刻家人声音的情感陪伴</span>
              </p>
              <ul>
                <li>
                  <i class="tick"></i
                  ><span class="tick-text">一句话复刻真人声音</span>
                </li>
                <li>
                  <i class="tick"></i
                  ><span class="tick-text"
                    >有温度的玩具对话，宛如亲人时刻陪伴</span
                  >
                </li>
              </ul>
            </div>
          </li>

          <li>
            <div class="advantage-text">
              <p class="advantage-tag-wrap">
                <span class="advantage-tag"></span
                ><span class="advantage-tag-text"
                  >多模态交互，与孩子共同探索世界</span
                >
              </p>
              <ul>
                <li>
                  <i class="tick"></i
                  ><span class="tick-text"
                    >支持音视频输入，让玩具感知现实世界
                  </span>
                </li>
                <li>
                  <i class="tick"></i
                  ><span class="tick-text"
                    >提供摄像头、麦克风阵列等硬件设备</span
                  >
                </li>
              </ul>
            </div>
            <div class="advantage-image"></div>
          </li>
        </ul>
      </section>
    </div>

    <div class="section section5">
      <h2>接入方式</h2>
      <p>接入便捷，低配置成本</p>
      <div class="item">
        <div class="border">
          <div class="title">纯软接入</div>

          <div class="desc">接入代码开源，快速实现大模型语音交互</div>

          <div class="sdk-button" @click="gotoSDK">开源SDK</div>

          <div class="other-btn">
            <div class="single">RTOS</div>
            <div class="single">安卓</div>
            <div class="single">Linux</div>
            <div class="single">鸿蒙</div>
          </div>
        </div>

        <div class="border">
          <div class="title">玩具硬件模组</div>
          <div class="desc">开箱即用，适合项目验证及产品量产</div>
          <div class="model-item" @click="gotoClick"></div>
        </div>
      </div>
    </div>

    <div class="section section6">
      <h2>接入儿童大模型交互</h2>
      <p>免费咨询专属顾问 为您量身定制产品推荐方案</p>
      <div class="corp-button" @click="toConsole">合作咨询</div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AiuiWebChildToys',

  data() {
    return {
      videoSrc: 'https://aiui-file.xfyun.cn/vedio/child.mp4',
      isPlaying: false,
    }
  },

  mounted() {},

  watch: {
    isPlaying(newVal) {
      if (newVal && this.$refs.videoPlayer) {
        // 当isPlaying变为true时，手动播放视频
        this.$nextTick(() => {
          this.$refs.videoPlayer.play().catch((err) => {
            console.error('自动播放失败:', err)
            // 一些浏览器可能会阻止自动播放，这里可以添加处理逻辑
          })
        })
      }
    },
  },

  methods: {
    toConsole() {
      // 判断是否有百度SEM营销参数，有的话，提交表单页需要带上
      const search = window.location.search
      if (search) {
        window.open(`/solution/apply/36${search}`)
      } else {
        window.open('/solution/apply/36')
      }
    },

    gotoSDK() {
      window.open('https://aiui-doc.xf-yun.com/project-1/doc-418/')
    },

    gotoClick() {
      window.open('https://www.aifuwus.com/onstage/cmddetail?id=4434')
    },
  },
}
</script>

<style lang="scss" scoped>
.main-content {
  &-banner {
    background: url(~@A/images/solution/child-toys/banner.png) center no-repeat;
    background-size: cover;
    height: 500px;
    overflow: hidden;
    width: 100%;
  }
  .banner-text {
    max-width: 1200px;
    color: #fff;
    height: 100%;
    margin: auto;

    &-button {
      font-size: 16px;
      text-align: center;
      font-weight: 400;
      width: 140px;
      height: 40px;
      line-height: 40px;
      background: $primary;
      border-radius: 4px;
      color: #fff;
      cursor: pointer;
    }

    h2 {
      color: #181818;
      padding-top: 148px;
      margin-bottom: 29px;
      font-size: 48px;
      font-weight: 600;
      font-family: PingFang SC, PingFang SC-Semibold;
      line-height: 48px;
    }
    p {
      font-size: 18px;
      margin-bottom: 74px;
    }

    .banner-text-content {
      width: 570px;
      font-size: 16px;
      font-family: SourceHanSansSC-Regular, SourceHanSansSC;
      font-weight: 400;
      // color: rgba(255, 255, 255, 0.86);
      color: #444444;
      line-height: 30px;
    }
  }
  .section_0_wrap {
    background: url(~@A/images/solution/child-toys/video_bg.jpg) center/cover
      no-repeat;
    margin-bottom: 40px;
    padding-bottom: 88px;
  }

  .section_0 {
    .section-title {
      padding-top: 80px;
      span {
        font-size: 40px;
      }
    }

    .video_container {
      width: 732px;
      height: 410px;
      background: url(~@A/images/solution/child-toys/video-overview.png)
        center/100% no-repeat;
      margin: 68px auto 0;
      cursor: pointer;
      position: relative;
      .play_button {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 90px;
        height: 90px;
        background: url(~@A/images/solution/child-toys/video-overview-btn.png)
          center/100% no-repeat;
      }
    }
    .case_video_player {
      // width: 1028px;
      // height: 611px;
      width: 732px;
      height: 410px;
      margin: 68px auto 0;
      position: relative;
      border-radius: 20px;
      overflow: hidden;
      video {
        width: 100%;
        height: 100%;
      }
      .case_video_player_inner {
      }
    }
  }

  .section-title {
    text-align: center;
    font-size: 34px;
    font-weight: bold;
    color: #333;
    margin-top: 0 !important;
    margin-bottom: 30px !important;
  }
  .section-sub-title {
    font-size: 16px;
    font-weight: 400;
    text-align: center;
    color: #666666;
    margin-bottom: 60px !important;
  }
  .app-text {
    color: #666;
  }

  .section {
    padding: 30px 0 84px;
    text-align: center;
    .section-title {
      margin-bottom: 40px;
      font-size: 40px;
      font-weight: 500;
    }
    .section-content {
      margin: 50px auto 0;
      width: 1200px;
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
    }
    .content-item {
      flex: 0 0 auto;
    }
  }

  .section-option {
    p,
    ul {
      margin-bottom: 0;
    }
    max-width: 1200px;
    overflow: hidden;
    margin: 0 auto;
    .section-title {
      text-align: center;
      font-size: 34px;
      font-family: SourceHanSansSC-Medium, SourceHanSansSC;
      font-weight: bold;
      color: #333;
      line-height: 34px;
      position: relative;
      width: 200px;
      margin: 0 auto;
      .arrow {
        width: 160px;
        height: 8px;
        display: inline-block;
        background-repeat: no-repeat;
        background-size: contain;
        position: absolute;
        z-index: 1;
      }
      .arrow-left {
        background-position: left;
        top: 50%;
        left: -160px;
      }
      .arrow-right {
        background-position: right;
        top: 50%;
        right: -160px;
      }
    }
    .section-desc {
      text-align: center;
      margin-top: 30px;
      font-size: 16px;
      font-weight: 400;
      color: #666666;
      line-height: 25px;
    }
    .section-title-bold {
      font-size: 40px;
      font-weight: 500;
      color: #333;
      line-height: 44px;
    }

    .section-tabs {
      margin-top: 70px;
      ul {
        display: flex;
        justify-content: center;
      }
      li {
        font-size: 20px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #666;
        line-height: 30px;
        position: relative;
        cursor: pointer;
        &:hover {
          color: #1f90fe;
        }
        &.active {
          color: #1f90fe;
          &::before {
            position: absolute;
            content: ' ';
            z-index: 1;
            width: 180px;
            height: 4px;
            background: #1f90fe;
            border-radius: 2px;
            bottom: -32px;
            left: -48px;
          }
          &::after {
            position: absolute;
            content: ' ';
            display: inline-block;
            width: 0;
            height: 0;
            bottom: -100px;
            left: 50%;
            transform: translateX(-50%);
            border: 46px solid;
            border-color: transparent transparent #f4f7f9;
          }
        }
      }
      li + li {
        margin-left: 165px;
      }
    }
    .section-sub-title {
      text-align: center;
      font-size: 18px;
      font-weight: 400;
      color: #777777;
      line-height: 25px;
      margin-top: 42px;
    }
    .section-sub-title-spec {
      font-size: 17px;
      font-weight: 400;
      color: #999999;
      line-height: 28px;
      margin-top: 18px;
    }
  }

  .content-item {
    padding: 20px;
    position: relative;
    flex: 0 0 auto;
    padding-top: 30px;
    width: 280px;
    height: 370px;
    text-align: center;
    font-size: 18px;
    color: #666;
    background: url('../../../../assets/images/solution/child-toys/toys-border.png')
      center no-repeat;
    background-size: cover;
    overflow: hidden;
    .toys {
      background: url('../../../../assets/images/solution/child-toys/toys1.png')
        center no-repeat;
      background-size: cover;
      width: 237px;
      height: 195px;
      position: absolute;
      left: 50%;
      bottom: 40px;
      translate: -50%;
    }
  }

  .content-item:nth-child(2) {
    .toys {
      background: url('../../../../assets/images/solution/child-toys/toys2.png')
        center no-repeat;
      background-size: cover;
      width: 203px;
      height: 204px;
    }
  }

  .content-item:nth-child(3) {
    .toys {
      background: url('../../../../assets/images/solution/child-toys/toys3.png')
        center no-repeat;
      background-size: cover;
      width: 136px;
      height: 208px;
    }
  }

  .content-item:nth-child(4) {
    .toys {
      background: url('../../../../assets/images/solution/child-toys/toys4.png')
        center no-repeat;
      background-size: cover;
      width: 135px;
      height: 205px;
    }
  }

  .section-3-wrap {
    padding: 80px 0 82px 0;
    background: #eff7ff;
    margin-top: 55px;
  }
  .section-3 {
    p {
      margin-bottom: 0;
    }
    // margin-top: 50px;

    .advantage {
      margin-top: 60px;
      > li {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      > li:nth-child(1) {
        padding: 29px;
        .advantage-image {
          position: relative;
          cursor: pointer;
          width: 527px;
          height: 373px;
          background: url(~@A/images/solution/child-toys/option1.png)
            center/contain no-repeat;
        }
      }
      > li:nth-child(2) {
        margin-top: 86px;
        padding: 29px;
        .advantage-image {
          position: relative;
          cursor: pointer;
          width: 594px;
          height: 348px;
          background: url(~@A/images/solution/child-toys/option2.png)
            center/contain no-repeat;
        }
      }
      > li:nth-child(3) {
        margin-top: 86px;
        padding: 29px;
        .advantage-image {
          position: relative;
          cursor: pointer;
          width: 597px;
          height: 348px;
          background: url(~@A/images/solution/child-toys/option3.png)
            center/contain no-repeat;
        }
      }
      > li:nth-child(4) {
        margin-top: 86px;
        padding: 29px;
        .advantage-image {
          position: relative;
          cursor: pointer;
          width: 591px;
          height: 348px;
          background: url(~@A/images/solution/child-toys/option4.png)
            center/contain no-repeat;
        }
      }
      > li:nth-child(5) {
        margin-top: 86px;
        padding: 29px;
        .advantage-image {
          position: relative;
          cursor: pointer;
          width: 597px;
          height: 348px;
          background: url(~@A/images/solution/child-toys/option5.png)
            center/contain no-repeat;
        }
      }
      > li:nth-child(6) {
        margin-top: 86px;
        padding: 29px;
        .advantage-image {
          position: relative;
          cursor: pointer;
          width: 597px;
          height: 348px;
          background: url(~@A/images/solution/child-toys/option6.png)
            center/contain no-repeat;
        }
      }
      > li:nth-child(7) {
        margin-top: 86px;
        padding: 29px;
        .advantage-image {
          position: relative;
          cursor: pointer;
          width: 597px;
          height: 348px;
          background: url(~@A/images/solution/child-toys/option7.png)
            center/contain no-repeat;
        }
        .advantage-tag-text {
          width: 450px !important;
          display: block;
          // color: red;
        }
      }
    }
    .advantage-text {
      .tick {
        width: 30px;
        height: 30px;
        display: inline-block;
        background: url(~@A/images/solution/multimodal-interaction/tik.png)
          center/contain no-repeat;
        margin-right: 17px;
      }

      width: 450px;
      p {
        font-size: 30px;
        font-weight: 600;
        color: #000;
        height: 30px;
        min-width: 200px;
        position: relative;
      }

      ul {
        margin-top: 40px;
        li {
          font-size: 20px;
          font-weight: 400;
          color: #36485d;
          line-height: 40px;
          white-space: nowrap;
          display: flex;
          align-items: center;
        }
      }
    }
  }

  .section5 {
    position: relative;
    background: url(~@A/images/solution/child-toys/inter-bg.png) center
      no-repeat;
    background-size: cover;
    height: 622px;
    margin-top: 0;
    overflow: hidden;
    width: 100%;
    h2 {
      font-size: 30px;
      line-height: 60px;
      color: #262626;
      font-weight: 700;
    }
    .item {
      display: flex;
      justify-content: center;
      gap: 100px;
      .border {
        // margin: 30px auto 0;
        margin-top: 30px;
        width: 470px;
        height: 312px;
        background: url(~@A/images/solution/child-toys/inter-border.png) center
          no-repeat;
        background-size: cover;
        .title {
          text-align: center;
          line-height: 50px;
          height: 30px;
          font-size: 24px;
          color: #fff;
        }
        .desc {
          font-size: 18px;
          text-align: center;
          color: #262b4f;
          margin: 30px 20px;
          margin-top: 50px;
        }
        .sdk-button {
          background: url(~@A/images/solution/child-toys/btn-border.png) center
            no-repeat;
          background-size: cover;
          width: 423px;
          height: 39px;
          font-weight: 400;
          margin: 0 auto;
          text-align: center;
          color: #049bfd;
          line-height: 40px;
          cursor: pointer;
        }
        // .sdk-button:hover {
        //   cursor: pointer;
        // }
        .other-btn {
          display: flex;
          flex-wrap: wrap;
          margin-top: 20px;
          gap: 10px;
          justify-content: center;
          .single {
            width: 200px;
            height: 39px;
            background: #f2f9ff;
            border-radius: 14px;
            font-weight: 400;
            text-align: center;
            color: #049bfd;
            line-height: 39px;
          }
        }
      }
      .border:nth-child(2) {
        .desc {
          text-align: center;
          margin-bottom: 10px;
        }
        .model-item {
          cursor: pointer;
          width: 154px;
          height: 190px;
          background: url(~@A/images/solution/child-toys/model.png) center
            no-repeat;
          background-size: cover;
          margin: 0 auto;
        }
      }
    }
  }
  .section6 {
    width: 100%;
    h2 {
      font-size: 30px;
      font-weight: 700;
      margin-top: 30px;
      margin-bottom: 30px;
    }
    p {
      font-size: 20px;
      color: #999999;
      margin-bottom: 30px;
    }
    .corp-button {
      width: 163px;
      height: 60px;
      background: linear-gradient(90deg, #26bff5 0%, #1a7af6 100%);
      border-radius: 60px 60px 60px 60px;
      line-height: 60px;
      font-weight: 600;
      font-size: 20px;
      color: #ffffff;
      text-align: center;
      margin: 0 auto;
      cursor: pointer;
    }
  }
}
</style>
