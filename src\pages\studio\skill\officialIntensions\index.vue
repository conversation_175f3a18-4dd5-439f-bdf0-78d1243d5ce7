<template>
  <div>
    <div class="mgb24" @keyup.enter="searchIntention">
      <el-input
        class="search-area"
        placeholder="搜索意图"
        v-model="intentionSearchName"
      >
        <i
          slot="suffix"
          class="el-input__icon el-icon-search search-area-btn"
          @click="searchIntention"
        />
      </el-input>
    </div>

    <os-table
      class="intentions-table"
      :tableData="tableData"
      style="margin-bottom: 56px"
      @change="getIntentions"
    >
      <el-table-column type="index" width="50">
        <template slot-scope="scope">
          {{ (tableData.page - 1) * tableData.size + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column prop="zhName" label="意图名称" min-width="100px">
        <template slot-scope="scope">
          <div
            class="intent-zhname ib"
            :class="{ 'no-pointer': scope.row.type && scope.row.type == 6 }"
            @click="toEdit(scope.row)"
          >
            {{ scope.row.zhName }}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="descrition" label="意图概述" min-width="300px">
        <template slot-scope="scope">
          <div
            class="intent-name"
            :class="{ 'no-pointer': scope.row.type && scope.row.type == 6 }"
            @click="toEdit(scope.row)"
            :title="scope.row.descrition"
          >
            {{ scope.row.descrition }}
          </div>
        </template>
      </el-table-column>
      <!-- <template v-if="!subAccountEditable"> -->

      <el-table-column label="引用" prop="checked">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.checked"
            @change="(val) => changeSwitch(scope.row, val)"
          ></el-switch>
        </template>
      </el-table-column>
      <!-- </template> -->
    </os-table>
  </div>
</template>
<script>
import { mapGetters } from 'vuex'
export default {
  name: 'official-intention',
  props: {
    type: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      intentionSearchName: '',
      tableData: {
        loading: true,
        total: 0,
        page: 1,
        size: 10,
        // handles: ['switch'],
        // switchOptions: {
        //   column: 'type',
        //   active: 2,
        //   inactive: 3,
        //   activeText: '',
        //   inactiveText: '',
        // },
        // handleColumnText: '引用',
        list: [],
      },
    }
  },
  created() {
    this.getIntentions()
  },
  watch: {
    type(val) {
      if (val) {
        this.getIntentions(1)
      }
    },
  },
  computed: {
    ...mapGetters({
      businessId: 'studioSkill/id',
      skill: 'studioSkill/skill',
    }),
  },
  methods: {
    searchIntention() {
      this.getIntentions(1)
    },
    getIntentions(page) {
      this.tableData.loading = true
      this.$utils.httpGet(
        this.$config.api.STUDIO_SYSTEM_INTENTS,
        {
          businessId: this.businessId,
          pageIndex: page || this.tableData.page,
          pageSize: this.tableData.size,
          search: this.intentionSearchName,
          classifyId: this.type,
        },
        {
          success: (res) => {
            if (res && res.data && !res.data.count) {
              this.tableData.loading = false
              this.tableData.total = 0
              this.tableData.list = []
              return
            }
            this.tableData.loading = false
            this.tableData.total = res.data.count
            this.tableData.page = res.data.pageIndex
            this.tableData.size = res.data.pageSize

            this.tableData.list = (res.data.intents || []).map((item) => {
              return {
                ...item,
              }
            })
          },
          error: (err) => {
            this.tableData.loading = false
            console.log('page=>>')
            console.log(err)
          },
        }
      )
    },
    changeSwitch(data, val) {
      let param = {}
      let url
      let tip
      let self = this
      if (val) {
        param = {
          businessId: this.businessId,
          quoteId: data.id,
          name: data.name.replace(/iFLYTEK./g, ''),
          zhName: data.zhName,
          type: '6',
        }
        url = this.$config.api.STUDIO_ADD_EDIT_INTENT
        tip = '引用成功'
      } else {
        if (/fallback|cancel/.test(data.name) && this.skill.type == '9') {
          this.$message.error('该意图不能取消引用')
          self.getIntentions()
          return
        }
        param = {
          businessId: this.businessId,
          intentId: data.intentId,
        }
        url = this.$config.api.STUDIO_DEL_INTENT
        tip = '取消引用成功'
      }

      this.$utils.httpPost(url, param, {
        success: (res) => {
          self.$message.success(tip)
          self.getIntentions()
        },
        error: (err) => {
          self.getIntentions()
        },
      })
    },
  },
}
</script>
<style lang="scss">
.el-switch.cell-handle-hovershow {
  display: inline-flex;
}
</style>
