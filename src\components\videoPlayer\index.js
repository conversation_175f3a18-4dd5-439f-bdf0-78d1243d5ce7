import Vue from 'vue'
import VideoPlayer from './videoPlayer.vue'

//创建VideoPlayer构造器
let VideoPlayerConstrutor = Vue.extend(VideoPlayer)
let instance

const videoPlayer = function (options = {}) {
  //设置默认参数为对象，如果参数为字符串，参数中videoSrc属性等于该参数
  if (typeof options === 'string') {
    options = {
      videoSrc: options,
    }
  }
  //创建实例
  instance = new VideoPlayerConstrutor({
    data: options,
  })
  //将实例挂载到body下
  document.body.appendChild(instance.$mount().$el)
}

export default videoPlayer
