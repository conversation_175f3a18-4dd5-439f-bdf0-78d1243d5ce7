let Module = Quill.import('core/module')
let Block = Quill.import('blots/block')
let Inline = Quill.import('blots/inline')
let BlockEmbed = Quill.import('blots/block/embed')
import Vue from 'vue'
import QuillEditor from '../../../QuillEditor/QuillEditor.vue'

class Td extends Block {
  constructor(quill, options) {
    super(quill, options)
    console.log(this.tagName + ' created', quill, options)
  }

  static create(options) {
    let node = super.create()
    console.log('Creating ' + this.tagName + ' >>', options, node)

    if (options.text) {
      let reference = options.reference || {}
      if (Object.keys(reference).length === 0) {
        node.appendChild(document.createTextNode(options.text))
      } else {
        const component = new Vue({
          render(h) {
            return h(QuillEditor, {
              props: {
                value: {
                  knowledge: options.text,
                  reference: options.reference,
                },
                disabled: true,
              },
            })
          },
        }).$mount()
        node.appendChild(component.$el)
      }
    } else {
      node.appendChild(
        document.createTextNode('\u00A0') // &nbsp;
      )
    }

    if (options.spanStyle !== 'Normal') {
      let colspan = 1,
        rowspan = 1
      // TODO: 确认既有colspan，又有rowspan时候的返回
      if (options.spanStyle && options.spanStyle.includes('colspan=')) {
        colspan = Number(options.spanStyle.split('=')[1])
      }
      if (options.spanStyle && options.spanStyle.includes('rowspan=')) {
        rowspan = Number(options.spanStyle.split('=')[1])
      }
      if (colspan > 1) {
        node.setAttribute('colspan', colspan)
      }
      if (rowspan > 1) {
        node.setAttribute('rowspan', rowspan)
      }
    }

    return node
  }

  static value(node) {
    let content = {
        content: { text: node.innerHTML },
      },
      span

    if (node.hasAttribute('colspan')) {
      span = node.getAttribute('colspan')
      if (span > 1) {
        content.colspan = span
      }
    }
    if (node.hasAttribute('rowspan')) {
      span = node.getAttribute('rowspan')
      if (span > 1) {
        content.rowspan = span
      }
    }

    return content
  }
}
Td.blotName = 'td'
Td.tagName = 'td'
Td.allowedChildren = [Inline, Block]
// Quill.register('blots/td', Td)

class Th extends Td {
  static value(node) {
    let content = super.value(node)
    content.heading = true
    return content
  }
}
Th.blotName = 'th'
Th.tagName = 'th'
// Quill.register('blots/th', Th)

class Tr extends BlockEmbed {
  constructor(quill, options) {
    super(quill, options)
    console.log('TR created', quill, options)
  }

  static create(options) {
    let node = super.create()
    console.log('Creating TR >>', options, node)

    if (options.length) {
      options.forEach((item, index) => {
        if (item.heading) {
          node.appendChild(Th.create(item))
        } else {
          node.appendChild(Td.create(item))
        }
      })
    } else {
      node.appendChild(Td.create({}))
    }

    return node
  }

  static value(node) {
    let cells = [],
      children = node.childNodes

    children.forEach((item, index) => {
      if (item.tagName.toUpperCase() === 'TH') {
        cells.push(Th.value(item))
      } else {
        cells.push(Td.value(item))
      }
    })
    return cells
  }
}
Tr.blotName = 'tr'
Tr.tagName = 'tr'
Tr.allowedChildren = [Th, Td]
Tr.defaultChild = Td
// Quill.register('blots/tr', Tr)

class Tbody extends BlockEmbed {
  constructor(quill, options) {
    super(quill, options)
    console.log(this.tagName + ' created', quill, options)
  }

  static create(options) {
    let node = super.create()
    console.log('Creating ' + this.tagName + ' >>', options, node)
    if (options.length) {
      options.forEach((item, index) => {
        node.appendChild(Tr.create(item))
      })
    } else {
      node.appendChild(Tr.create([]))
    }

    return node
  }

  static value(node) {
    let rows = [],
      children = node.childNodes

    children.forEach((item, index) => {
      rows.push(Tr.value(item))
    })
    return rows
  }
}
Tbody.blotName = 'tbody'
Tbody.tagName = 'tbody'
Tbody.allowedChildren = [Tr]
Tbody.defaultChild = Tr
// Quill.register('blots/tbody', Tbody)

class Thead extends Tbody {}
Thead.blotName = 'thead'
Thead.tagName = 'thead'
Thead.allowedChildren = [Tr]
Thead.defaultChild = Tr
// Quill.register('blots/thead', Thead)

class Tfoot extends Tbody {}
Tfoot.blotName = 'tfoot'
Tfoot.tagName = 'tfoot'
Tfoot.allowedChildren = [Tr]
Tfoot.defaultChild = Tr
// Quill.register('blots/tfoot', Tfoot)

class Table extends BlockEmbed {
  constructor(quill, options) {
    super(quill, options)
    console.log('TABLE created', quill, options)
  }

  static create(options) {
    let node = super.create()
    node.className = 'quill-table-detail'

    console.log('Creating TABLE >>', options, node)
    let content = JSON.parse(options.content)
    console.log('table paragram', content)
    if (content.title && content.title.length > 0) {
      node.appendChild(Thead.create([content.title]))
    }
    if (content.header) {
      node.appendChild(Thead.create(content.header))
    }
    if (content.cells) {
      node.appendChild(Tbody.create(content.cells))
    } else {
      node.appendChild(Tbody.create([]))
    }
    if (content.footer) {
      node.appendChild(Tfoot.create(content.footer))
    }

    return node
  }

  static formats(node) {
    // We still need to report unregistered embed formats
    let format = {
      class: 'quill-table-detail',
    }
    return format
  }
  static value(node) {
    let header = [],
      body = [],
      footer = [],
      children = node.childNodes,
      classNames = node.className.split(' ')

    children.forEach((item, index) => {
      if (item.tagName.toUpperCase() === 'THEAD') {
        header = Thead.value(item)
      } else if (item.tagName.toUpperCase() === 'TBODY') {
        body = Tbody.value(item)
      } else if (item.tagName.toUpperCase() === 'TFOOT') {
        body = Tfoot.value(item)
      }
    })

    return {
      class: 'quill-table-detail',
      // stripped: classNames.indexOf('table-stripped') !== -1,
      // hover: classNames.indexOf('table-hover') !== -1,
      // border: classNames.indexOf('table-border') !== -1,
      header: header,
      body: body,
      footer: footer,
    }
  }
}
Table.blotName = 'table-detail'
Table.tagName = 'table'
Table.allowedChildren = [Thead, Tbody, Tfoot]
Table.defaultChild = Tbody
Quill.register('blots/table', Table)

let icons = Quill.import('ui/icons')
icons['table'] = '<i class="glyphicon glyphicon-th"></i>'

console.log(Quill.imports)

export default Table
