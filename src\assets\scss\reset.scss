.debug-input {
  width: 252px;
}

// 技能发布 上传图片
.release-form-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 2px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  float: left;
}
.release-form-uploader .el-upload:hover {
  border-color: $primary;
  .release-form-uploader-icon {
    color: $primary;
  }
}
.release-form-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 160px;
  height: 160px;
  line-height: 160px;
  text-align: center;
}

// radio无label
.no_radio_label {
  .el-radio__label {
    display: none;
  }
}

// 只有一个内容输入的form
.one-input-form {
  // height: 54px;
  .el-form-item {
    margin: 0;
  }
  // .el-input {
  //   height: 30px;
  // }
  // .el-form-item__content {
  //   height: 30px;
  // }
  .el-form-item__error {
    top: 60%;
  }
}

.el-radio-button__orig-radio:active {
  box-shadow: none;
}

.el-container {
  height: 100%;
  overflow-x: hidden;
}

.el-main {
  padding: 0;
}

.os-table {
  .el-table__empty-block {
    width: 100% !important;
  }
}

// 技能发布版本号input
.release-form-version-input input {
  padding-right: 8px !important;
}

.small-table {
  .el-table--border th {
    padding: 0;
  }
  .el-table__body tr > td {
    padding: 0;
    border-top: 1px solid #fff;
  }
  .el-table__body tr:hover > td {
    background-color: #fff;
  }
  thead > tr > th {
    padding: 8px 0;
    & .cell {
      padding: 0 12px !important;
      font-weight: 600;
    }
  }
  tbody .cell {
    // padding: 0 !important;
    line-height: 28px;
    padding-top: 6px;
    padding-bottom: 6px;
  }
  &-pd {
    tbody .cell {
      padding: 4px 12px !important;
    }
  }
  .el-table__expanded-cell {
    border-bottom: 1px solid #e4e7ed;
  }
  .el-table__expand-icon > .el-icon {
    left: 10px;
  }
  &__border,
  &__border-hover {
    border: 1px solid $primary-light-50 !important;
  }
  &__input {
    width: 100%;
    & input {
      width: 100%;
      height: 24px;
      line-height: 24px;
      border-color: transparent !important;
      padding: 0;
      &:hover {
        border-color: transparent !important;
      }
      &:focus {
        outline: none;
        border-color: transparent;
      }
    }
  }
  &__tags {
    display: flex;
    flex-wrap: wrap;
    margin-top: -6px;
    .el-tag {
      display: flex;
      align-items: center;
      border-radius: 2px;
      padding: 4px 8px;
      max-width: 120px;
      height: 28px;
      line-height: 22px;
      font-size: 14px;
      color: $semi-black;
      border: none;
      background-color: $grey4-15;
      margin-top: 6px;
      & span {
        overflow: hidden;
        text-overflow: ellipsis;
        margin-right: 4px;
      }
    }
    .el-tag .el-icon-close {
      display: contents;
    }
    &-item {
      margin-right: 4px;
    }
    &-input {
      width: 100%;
      margin-top: 6px;
      & input {
        width: 100%;
        height: 28px;
        line-height: 28px;
        border-color: transparent;
        padding: 0;
        &:hover {
          border-color: transparent;
        }
        &:focus {
          outline: none;
          border-color: transparent;
        }
      }
    }
  }
}

//登录注册 表单
.login-reg-form,
.super-form {
  .el-form-item__label {
    font-size: 14px;
    line-height: 22px;
    padding-bottom: 4px;
  }
  & input {
    height: 44px;
    line-height: 44px;
    padding: 0 17px;
  }
  .is-required {
    .el-form-item__label {
      &:before {
        content: ' ';
      }
    }
  }
  .el-input-group__append {
    background: #fff;
  }

  .el-select .el-input {
    width: 110px;
  }
  .input-with-select .el-input-group__prepend {
    background-color: #fff;
  }
}
