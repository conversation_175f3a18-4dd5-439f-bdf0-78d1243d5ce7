<template>
  <el-dialog
    class="page-leave-tips-dialog"
    :visible.sync="dialog.show"
    :show-close="false"
    width="480px"
  >
    <div slot="title">
      <div class="page-leave-tips-title">
        <i class="el-icon-warning"/>
        <span>要保存修改吗？</span>
      </div>
    </div>
    <p class="page-leave-tips-content">如果不保存，所修改的内容将会丢失。</p>
    <span slot="footer" class="dialog-footer">
      <el-button class="dialog-btn fl" type="danger" style="min-width: 104px;" @click="noSave">不保存</el-button>
      <el-button class="dialog-btn" style="min-width: 104px;" @click="closeTip">取消</el-button>
      <el-button class="dialog-btn" type="primary" style="min-width: 104px;" @click="save">保存</el-button>
    </span>
  </el-dialog>
</template>

<script>

export default {
  name: 'PageLeaveTips',
  props: {
    dialog: {
      type: Object,
      default: {}
    }
  },
  methods: {
    noSave () {
      this.dialog.show = false
      this.$emit('noSave')
    },
    save () {
      this.dialog.show = false
      this.$emit('save')
    },
    closeTip () {
      this.dialog.show = false
      this.$emit('noJump')
    }
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="scss">
.page-leave-tips-dialog {
  .el-dialog {
    margin-top: 24vh !important;
  }
  .el-dialog__header {
    padding: 32px 32px 12px;
  }
  .el-dialog__body {
    padding: 0 62px;
  }
  .el-dialog__footer {
    padding: 32px;
  }
}
.page-leave-tips-title {
  display: flex;
  align-items: center;
  i {
    color: $warning;
  }
  span {
    padding-left: 16px;
    font-size: 20px;
    font-weight: 500;
  }
}
.page-leave-tips-content {
  font-size: 16px;
  color: $semi-black;
}

</style>
