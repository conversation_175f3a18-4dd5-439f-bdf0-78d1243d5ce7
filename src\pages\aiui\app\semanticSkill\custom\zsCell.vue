<template>
  <div>
    <div :class="['skill', { 'skill-active': item.used }]" @click="toQa(item)">
      <div
        :class="[
          'content-wrap',
          {
            'cursor-default': subAccount || (item.type == '2' && !!item.domain),
          },
        ]"
      >
        <div
          v-if="item.outNumber && item.outNumber !== item.newestNumber"
          class="update-state-label"
          style="background: #ff5a5a"
        >
          可更新
        </div>
        <i class="skill-icon">{{ item.name && item.name.substr(0, 1) }}</i>
        <div class="skill-info">
          <p class="skill-title" :title="item.name">
            {{ item.name }}
          </p>

          <!-- 标题下按钮 -->
          <div class="title-btm-btn-group">
            <p class="ability-tag">{{ item | filterType }}</p>
            <p
              v-if="item.outNumber"
              :title="item.outNumber"
              style="margin-left: 8px; padding-top: 3px"
            >
              {{ item.outNumber }}
            </p>
          </div>
        </div>
      </div>

      <div @click.stop class="switch-wrap">
        <el-switch
          :disabled="!subAccountEditable"
          size="small"
          v-model="item.used"
          @change="(val) => onSwitchChange(val, item)"
        >
        </el-switch>
      </div>
      <!-- 下面的label area -->
      <div
        v-if="
          !!item.domain ||
          (item.type != 2 && subAccountEditable && !mustAnswer && item.used)
        "
        :class="[
          'label-wrap',
          {
            'cursor-default': subAccount || (item.type == '2' && !!item.domain),
          },
        ]"
      >
        <p class="skill-desc" :title="item.name">
          <span v-if="!!item.domain">{{ item.domain }}</span>
        </p>
        <i
          @click.prevent.stop="showThresholdInfo(item)"
          class="skill-config-new AIUI-myapp-iconfont ai-myapp-setting2"
          v-if="
            item.type != 2 && subAccountEditable && !mustAnswer && item.used
          "
        ></i>
      </div>
    </div>
    <threshold-info
      :thresholdVisible="thresholdVisible"
      :configSkillItem="configSkillItem"
      :appId="appId"
      :currentScene="currentScene"
      :skillThresholdConfig="skillThresholdConfig"
      :qaThresholdConfig="qaThresholdConfig"
      :qaConfig="qaConfig"
      :globalThresholdChange="globalThresholdChange"
      @thresholdVisibleChange="onThresholdVisibleChange"
      @change="$emit('change')"
    ></threshold-info>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import thresholdInfo from './zsCellThresholdInfo.vue'

export default {
  components: { thresholdInfo },
  data() {
    return {
      thresholdVisible: false,
      configSkillItem: {},
    }
  },
  props: {
    item: Object,
    qaConfig: Object,
    currentScene: Object,
    appId: String,
    skillThresholdConfig: Object,
    qaThresholdConfig: Object,
    skillConfig: Object,
    globalThresholdChange: Boolean,
    mustAnswer: Boolean,
  },
  computed: {
    ...mapGetters({
      subAccount: 'user/subAccount',
      subAccountAppAuths: 'aiuiApp/subAccountAppAuths',
      subAccountSkillAuths: 'aiuiApp/subAccountSkillAuths',
    }),
    subAccountEditable() {
      return this.subAccountAppAuths[this.$route.params.appId] == 2
        ? false
        : true
    },
  },
  filters: {
    filterType(item) {
      if (item.type == 2) {
        if (item.domain) {
          return '知识图谱'
        }
        return '知识库'
      } else if (item.type == 0) {
        return '语句问答'
      }
    },
  },
  methods: {
    onThresholdVisibleChange(val) {
      this.thresholdVisible = val
    },
    showThresholdInfo(item) {
      this.configSkillItem = item
      this.thresholdVisible = true
    },
    onSwitchChange(val, item) {
      console.log('onSwitchChange', val, item)
      this.$emit('change')
      let data
      let operation = val ? 'open' : 'close'
      // 自定义问答
      data = {
        repositoryId: item.id,
        type: item.type,
        operation,
      }
      this.qaConfig[item.id] = data
    },
    toQa(item) {
      let self = this
      if (self.subAccount) return
      if (item.type == '2' && item.domain) {
        // 知识图谱，暂不跳转
        return
      }
      window.open(
        `/studio/${item.type == 2 ? 'repo' : 'qabank'}/${item.id}`,
        '_blank'
      )
    },
  },
}
</script>
<style lang="scss" scoped>
@import '../style.scss';
// .skill {
//   height: 117px;
// }

.content-wrap {
  display: flex;
  // height: 70px;
}
</style>
