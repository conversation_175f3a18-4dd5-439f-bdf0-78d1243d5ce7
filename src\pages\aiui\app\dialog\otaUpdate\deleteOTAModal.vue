<template>
  <el-dialog class="dg-body" title="固件删除" :visible.sync="modalParam.show">
    <p>如果有设备正在升级，可能会造成影响，确定删除该固件吗？</p>
    <div class="modal-btn-container">
      <el-button size="small" @click="closeModal">取消</el-button>
      <el-button size="small" type="primary" @click="handleDeleteItem"
        >确定</el-button
      >
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'deleteOTAModal',
  props: {
    modalParam: {
      type: Object,
      default: {
        show: Boolean,
        item: Object,
      },
    },
  },
  data() {
    return {}
  },

  methods: {
    closeModal() {
      this.modalParam.show = false
    },
    handleDeleteItem() {
      this.$utils.httpGet(
        this.$config.api.OTA_FORM_SUBMIT,
        {
          appid: this.appId,
          ...this.modalParam.item,
          delFlag: 1,
        },
        {
          success: (res) => {
            if (res.flag) {
              this.modalParam.show = false
              this.$message.success('固件删除成功')
              this.$emit('getList')
            } else {
              this.$message.error(res.desc)
            }
          },
        }
      )
    },
  },

  watch: {
    'modalParam.show': function (val) {
      if (val) {
      }
    },
  },

  computed: {
    appId() {
      return this.$route.params.appId
    },
  },
}
</script>

<style lang="scss" scoped>
.modal-btn-container {
  display: flex;
  justify-content: flex-end;
  padding: 30px 0;
  width: 100%;
}
</style>
