<template>
  <div>
    <el-form-item>
      <el-row :gutter="20">
        <el-col :span="8" class="name-col">
          <div :class="['name-wrapper', `level-${level}`]">
            <i
              v-if="item.type === 'object' || item.type === 'array'"
              :class="[
                item.collapsed ? 'el-icon-arrow-right' : 'el-icon-arrow-down',
                'collapse-icon',
              ]"
              @click="toggleCollapse(item)"
            ></i>
            <template v-if="parentType === 'array'">
              <el-input v-model="item.name" disabled />
            </template>
            <template v-else>
              <span class="property-name">{{ item.name }}</span>
            </template>
          </div>
        </el-col>
        <el-col :span="6">
          <el-input v-model="item.type" disabled />
        </el-col>
        <el-col :span="5">
          <el-input
            v-if="item.type !== 'object' && item.type !== 'array'"
            v-model="item.defaultValue"
            placeholder="请输入默认值"
          />
          <div v-else class="empty-default-value"></div>
        </el-col>
        <el-col :span="5" class="operation-column">
          <div class="operation-buttons">
            <i class="el-icon-remove" @click="handleRemove" />
            <i
              v-if="item.type === 'array'"
              class="el-icon-circle-plus"
              @click="handleAddArrayItem(item)"
            />
          </div>
        </el-col>
      </el-row>

      <template
        v-if="
          (item.type === 'object' || item.type === 'array') && !item.collapsed
        "
      >
        <div v-for="child in item.children" :key="child.id" class="nested-item">
          <array-item-component
            :item="child"
            :level="level + 1"
            :parent-type="item.type"
            @remove="handleRemoveArrayItem(item, child)"
            @add-array-item="$emit('add-array-item', $event)"
            @remove-array-item="$emit('remove-array-item', $event)"
          />
        </div>
      </template>
    </el-form-item>
  </div>
</template>

<script>
export default {
  name: 'ArrayItemComponent',
  props: {
    item: {
      type: Object,
      required: true,
    },
    level: {
      type: Number,
      default: 0,
    },
    parentType: {
      type: String,
      default: 'root',
    },
  },
  methods: {
    toggleCollapse(item) {
      if (item.type === 'object' || item.type === 'array') {
        this.$set(item, 'collapsed', !item.collapsed)
      }
    },
    handleAddArrayItem(item) {
      this.$emit('add-array-item', item)
    },
    handleRemoveArrayItem(parent, child) {
      this.$emit('remove-array-item', { parent, child })
    },
    handleRemove() {
      // 获取当前项的父级
      let parentItem = null
      let currentItem = this.item

      // 如果是数组的子项，需要找到真实的父级数组
      if (this.$parent && this.$parent.$parent && this.$parent.$parent.item) {
        parentItem = this.$parent.$parent.item
      }

      // 如果找到了父级数组，使用 remove-array-item
      if (parentItem && parentItem.type === 'array') {
        this.$emit('remove-array-item', {
          parent: parentItem,
          child: currentItem,
        })
      } else if (this.parentType === 'array') {
        // 处理直接父级是数组的情况
        this.$emit('remove-array-item', {
          parent: this.$parent.item,
          child: this.item,
        })
      } else {
        // 处理顶层项的删除
        this.$emit('remove')
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.nested-item {
  margin-top: 10px;
}

.name-col {
  .name-wrapper {
    display: flex;
    align-items: center;

    &.level-0 {
      padding-left: 0;
    }
    &.level-1 {
      padding-left: 20px;
    }
    &.level-2 {
      padding-left: 40px;
    }
    &.level-3 {
      padding-left: 60px;
    }
    &.level-4 {
      padding-left: 80px;
    }
    &.level-5 {
      padding-left: 100px;
    }

    .collapse-icon {
      margin-right: 8px;
      cursor: pointer;
    }

    .property-name {
      font-weight: 500;
      margin-right: 4px;
    }
  }
}

.empty-default-value {
  min-height: 40px; /* 与el-input的高度保持一致 */
}

.operation-column {
  display: flex;
  align-items: center;
  justify-content: flex-start; /* 确保操作按钮靠左对齐 */
}

.operation-buttons {
  display: flex;
  gap: 12px;
  align-items: center;
  min-height: 32px;
}

.el-icon-circle-plus,
.el-icon-remove {
  cursor: pointer;
  font-size: 18px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.el-icon-circle-plus {
  color: #409eff;
  &:hover {
    opacity: 0.8;
  }
}

.el-icon-remove {
  color: #f56c6c;
  &:hover {
    opacity: 0.8;
  }
}
</style>
