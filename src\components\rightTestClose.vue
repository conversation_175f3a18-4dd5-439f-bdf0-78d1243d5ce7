<template>
  <div
    v-show="!rightTestOpen"
    @click="checkAndOpenDialog"
    class="right-test-collapse collapse-btn-wrap"
  >
    <i class="el-icon-d-arrow-left" />
    <p>{{ debugText }}</p>
  </div>
</template>

<script>
export default {
  name: 'right-test-close',
  props: {
    rightTestOpen: {
      type: Boolean,
      default: true,
    },
    debugType: String,
    firstVisit: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    debugText() {
      let maps = {
        app: '模拟测试',
        character: '设备人设体验',
        qa: '问答库体验',
        skill: '技能体验',
        knowledge: '知识体验',
        plugin: '模拟测试',
      }
      if (this.debugType) {
        return maps[this.debugType]
      } else {
        return '技能体验'
      }
    },
  },
  methods: {
    checkAndOpenDialog() {
      // 只有在智能体且是首次访问时才需要检查
      if (this.debugType === 'plugin' && this.firstVisit) {
        // 获取当前插件ID
        const agentItemData = JSON.parse(
          localStorage.getItem('agentItemData') || '{}'
        )
        // 调用AGENT_DIALOG_CHECK接口检查插件是否可对话体验
        this.$utils.httpPost(
          this.$config.api.AGENT_DIALOG_CHECK,
          JSON.stringify({ pluginId: agentItemData.pluginId }),
          {
            config: {
              headers: {
                'Content-Type': 'application/json;charset=UTF-8',
              },
            },
            success: (res) => {
              if (res.code === 0 || res.code === '0') {
                // 检查通过，打开侧窗
                this.$store.dispatch('studioSkill/setRightTestOpen', true)
                this.$emit('updateFirstVisit', false)
              }
            },
            error: (err) => {
              this.$emit('updateFirstVisit', true)
              this.$message.error(err?.desc || '检查插件对话体验失败')
            },
          }
        )
      } else {
        // 非plugin类型或非首次访问直接打开
        this.$store.dispatch('studioSkill/setRightTestOpen', true)
      }
    },
  },
}
</script>
