<template>
  <el-dialog
    :title="title"
    :visible.sync="isShow"
    :close-on-click-modal="false"
    @close="cancel"
    width="480px"
  >
    <el-form
      :model="form"
      ref="agentForm"
      :rules="rules"
      label-width="90px"
      label-position="top"
      size="small"
    >
      <el-form-item label="智能体名称：" prop="pluginName">
        <el-input
          placeholder="支持中英文/数字/小数点/短横线/下划线,不超过32个字符"
          v-model.trim="form.pluginName"
          maxlength="32"
        ></el-input>
      </el-form-item>

      <el-form-item label="描述：" prop="pluginDesc">
        <el-input
          type="textarea"
          :rows="2"
          placeholder="请输入智能体描述,不超过250个字符"
          maxlength="250"
          v-model.trim="form.pluginDesc"
          show-word-limit
        ></el-input>
      </el-form-item>

      <el-form-item label="类型：" prop="agentType">
        <el-cascader
          :disabled="this.title === '编辑智能体'"
          v-model="form.agentType"
          :props="{
            expandTrigger: 'hover',
            value: 'id',
            label: 'name',
          }"
          :show-all-levels="false"
          clearable
          :options="formatOptions"
          filterable
        >
        </el-cascader>
      </el-form-item>
    </el-form>

    <span slot="footer" class="dialog-footer">
      <el-button @click="cancel">取消</el-button>
      <el-button
        class="dialog-btn"
        type="primary"
        @click="handleConfirm"
        :loading="saving"
      >
        确定
      </el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: 'IflyAIuiWebCreateAgent',

  data() {
    return {
      agent_id_edit: null,
      saving: false,
      isShow: false,
      title: '创建智能体',
      agentId: null,
      agentTypeArr: [
        { id: 20, name: '三方' },
        { id: 21, name: '模板' },
        { id: 22, name: '工作流' },
      ],
      form: {
        pluginName: null,
        pluginDesc: null,
        agentType: null,
        // url: null,
        // method: null,
        // auth: null,
        // type: null,
        // key: null,
      },
      agentTypeList: [],
      classify_type_list: [],
      rules: {
        pluginName: [
          { required: true, message: '请输入智能体名称', trigger: 'blur' },
          {
            pattern: /^[\u4e00-\u9fa5a-zA-Z0-9._-]{0,32}$/,
            message:
              '名称只能包含中文、英文字母、数字、小数点、短横线和下划线,长度不超过32个字符',
            trigger: 'blur',
          },
        ],
        pluginDesc: [
          { required: true, message: '请输入智能体描述', trigger: 'blur' },
          { max: 250, message: '描述不能超过250个字符', trigger: 'blur' },
        ],
        agentType: [{ required: true, message: '请选择类型', trigger: 'blur' }],
      },
    }
  },

  mounted() {
    this.getAgentTypeList()
  },
  computed: {
    formatOptions() {
      if (this.title == '编辑智能体') {
        return this.agentTypeArr
      } else {
        return this.transformProperties(this.agentTypeList)
      }
    },
  },
  methods: {
    show(data) {
      this.isShow = true
      this.$nextTick(() => {
        this.$refs.agentForm.resetFields()
        if (data) {
          console.log('data item=>', data)
          this.title = '编辑智能体'
          this.agentId = data.pluginId
          this.form = {
            pluginName: data.pluginName,
            pluginDesc: data.pluginDesc,
            agentType: [data.pluginType],
          }
        } else {
          this.title = '创建智能体'
        }
      })
    },
    getAgentTypeList() {
      this.$utils.httpGet(
        this.$config.api.AGENT_TYPE_LIST,
        {},
        {
          success: (res) => {
            if (res.code == 0) {
              this.agentTypeList = res.data
            }
          },
          error: (err) => {},
        }
      )
    },

    handleConfirm() {
      this.$refs.agentForm.validate((valid) => {
        if (valid) {
          const paramsData = {
            pluginName: this.form.pluginName,
            pluginDesc: this.form.pluginDesc,
          }

          this.saving = true
          if (this.title === '编辑智能体') {
            this.update({ ...paramsData, pluginId: this.agentId })
          } else {
            this.create({
              ...paramsData,
              pluginType: this.form.agentType ? this.form.agentType[0] : null,
              templateId:
                this.form.agentType?.length > 1
                  ? this.form.agentType?.slice(-1)[0]
                  : null,
            })
          }
        }
      })
    },

    create(data) {
      this.$utils.httpPost(
        this.$config.api.AGENT_CREATE,
        JSON.stringify(data),
        {
          config: {
            headers: {
              'Content-Type': 'application/json;charset=UTF-8',
            },
          },
          success: (res) => {
            if (res.code == 0) {
              this.$message.success(res.desc)
              this.cancel()
              this.$emit('refresh', (agentList) => {
                if (agentList && agentList.length > 0) {
                  this.$emit('handleAgentItem', agentList[0])
                }
              })
            }
          },
          error: (err) => {
            this.saving = false
            this.$message.error(err.desc)
          },
        }
      )
    },

    update(data) {
      console.log('update=>', data)
      this.$utils.httpPost(this.$config.api.AGENT_EDIT, JSON.stringify(data), {
        config: {
          headers: {
            'Content-Type': 'application/json;charset=UTF-8',
          },
        },
        success: (res) => {
          if (res.code == 0) {
            this.$message.success(res.desc)
            this.cancel()
            this.$emit('refresh')
          }
        },
        error: (err) => {
          this.$message.error(err.desc)
          this.saving = false
        },
      })
    },

    cancel() {
      this.saving = false
      this.isShow = false
      this.agent_id_edit = null
      this.agentId = null
      this.form.agentType = null
    },
    transformProperties(arr) {
      return arr.map((item) => {
        if (item.children) {
          return {
            ...item,
            children: this.transformProperties(item.children),
          }
        }
        if ('pluginId' in item || 'pluginName' in item) {
          const newItem = { ...item }
          if ('pluginId' in newItem) {
            newItem.id = newItem.pluginId
            delete newItem.pluginId
          }

          if ('pluginName' in newItem) {
            newItem.name = newItem.pluginName
            delete newItem.pluginName
          }

          return newItem
        }

        return item
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.dialog-btn {
  min-width: 104px;
}

:deep(.el-form, .el-textarea) {
  .el-form-item__label {
    padding: 0;
  }
  .el-input__count {
    color: #909399;
    position: absolute;
    font-size: 12px;
    bottom: -25px;
    right: 10px;
  }
}
</style>
