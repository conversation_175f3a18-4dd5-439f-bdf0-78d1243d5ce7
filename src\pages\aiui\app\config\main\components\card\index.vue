<template>
  <div
    class="custom-card"
    :style="{
      background: background
        ? 'linear-gradient(180deg,#f3faff, #ffffff 100%)'
        : '#fff',
      boxShadow: background ? '0px 10px 20px 0px rgba(0,0,0,0.03)' : 'none',
    }"
  >
    <!-- 标题区域 -->
    <div
      class="card-header"
      :style="{ borderBottom: isCollapsed ? 'none' : '1px solid #eef3fa' }"
    >
      <div class="header-left">
        <slot name="title">
          {{ title }}
        </slot>
      </div>
      <div class="header-right" @click.stop="toggleCollapse">
        <i class="el-icon-arrow-down" v-if="isCollapsed"></i>
        <i class="el-icon-arrow-up" v-else></i>
      </div>
    </div>

    <!-- 内容区域 -->
    <div
      class="card-content"
      v-show="!isCollapsed"
      :style="{ paddingBottom: isCollapsed ? 0 : '20px' }"
    >
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CustomCard',
  props: {
    title: {
      type: String,
      default: '卡片标题',
    },
    // 初始是否折叠
    collapsed: {
      type: Boolean,
      default: false,
    },
    background: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      isCollapsed: this.collapsed,
    }
  },
  methods: {
    toggleCollapse() {
      this.isCollapsed = !this.isCollapsed
      //   this.$emit('collapse-change', this.isCollapsed)
    },
  },
  watch: {
    collapsed(newVal) {
      this.isCollapsed = newVal
    },
  },
}
</script>
<style lang="scss" scoped>
.custom-card {
  background: #ffffff;
  border-radius: 14px;
  padding: 0 20px;
  .card-header {
    height: 64px;
    // border-bottom: 1px solid #eef3fa;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .header-left {
      font-size: 18px;
      font-weight: 500;
      color: #000;
    }
    .header-right {
      cursor: pointer;
    }
  }
  .card-content {
    padding: 20px;
  }
}
</style>
