<template>
  <div class="lib-solution main-content">
    <section class="main-content-banner">
      <div class="banner-text">
        <h2>语音鼠标解决方案</h2>
        <p class="banner-text-content">
          融合语音转写、翻译、方言识别、语音控制等AI能力的<br />
          语音鼠标解决方案，让鼠标能打字会翻译。
        </p>

        <div class="banner-text-button" @click="toConsole">合作咨询</div>
      </div>
    </section>

    <div class="section section1">
      <div class="section-title">应用领域</div>
      <div class="section-content">
        <div class="content-item">文字写作</div>
        <div class="content-item">上网助手</div>
        <div class="content-item">外贸交流</div>
      </div>
    </div>

    <div class="section section3">
      <p class="section-title">方案优势</p>
      <div class="section-content">
        <div class="content-item fist-content-item">
          <div class="bg"></div>
          <p class="item-title">1分钟四百字</p>
          <p class="show-in-pc item-desc">释放双手，“智”在必得</p>
          <p class="show-in-mobile">释放双手<br />“智”在必得</p>
        </div>
        <div class="content-item">
          <div class="bg bg2"></div>
          <p class="item-title">语音识别率98%</p>
          <p class="show-in-pc item-desc">准确高效，减少二次校对</p>
          <p class="show-in-mobile">准确高效<br />减少二次校对</p>
        </div>
        <div class="content-item">
          <div class="bg bg3"></div>
          <p class="item-title">28国语言互译</p>
          <p class="show-in-pc item-desc">实时翻译，跨语种沟通无障碍</p>
          <p class="show-in-mobile">实时翻译<br />跨语种沟通无障碍</p>
        </div>
        <!-- 只在移动端展示 start -->
        <div class="content-item show-in-mobile">
          <div class="bg bg4"></div>
          <p class="item-title">23种方言识别</p>
          <p class="item-desc">粤语、东北话、四川话等方言智能识别</p>
        </div>
        <div class="content-item show-in-mobile">
          <div class="bg bg5"></div>
          <p class="item-title">语音控制</p>
          <p class="item-desc">
            智能解析语音指令<br />智能操控<br />便捷生活，开口即得
          </p>
        </div>
        <div class="content-item show-in-mobile">
          <div class="bg bg6"></div>
          <p class="item-title">OCR识别</p>
          <p class="item-desc">准确识别图片文字信息<br />快速提取文字</p>
        </div>
        <!-- 只在移动端展示 end -->
      </div>
      <div class="section-content show-in-pc">
        <div class="content-item">
          <div class="bg bg4"></div>
          <p class="item-title">23种方言识别</p>
          <p class="item-desc">粤语、东北话、四川话等<br />方言智能识别</p>
        </div>
        <div class="content-item">
          <div class="bg bg5"></div>
          <p class="item-title">语音控制</p>
          <p class="item-desc">
            智能解析语音指令，智能操控<br />便捷生活，开口即得
          </p>
        </div>
        <div class="content-item">
          <div class="bg bg6"></div>
          <p class="item-title">OCR识别</p>
          <p class="item-desc">准确识别图片文字信息<br />快速提取文字</p>
        </div>
      </div>
    </div>
    <corp @jump="toConsole">
      <template> 提交信息，我们会尽快与您联系</template>
    </corp>
    <!-- <div class="section contact-wrap">
      <div class="section-title">合作咨询</div>
      <p class="desc">提交信息，我们会尽快与您联系</p>
      <aiui-button hasTop>
        <a @click="toConsole">申请合作</a>
      </aiui-button>
    </div> -->
  </div>
</template>

<script>
import utils from '../../../assets/lib/utils.js'
import bus from '../../../assets/lib/bus.js'
import corp from '@P/aiui/solution-aiui/components/corp.vue'

export default {
  layout: 'aiuiHome',
  methods: {
    // toConsole() {
    //   if (utils.isMobile()) {
    //     bus.$emit('osMessageShow')
    //   } else {
    //     location.href = '/console'
    //   }
    // },
    toConsole() {
      // 判断是否有百度SEM营销参数，有的话，提交表单页需要带上
      const search = window.location.search
      if (search) {
        window.open(`/solution/apply/4${search}`)
      } else {
        window.open('/solution/apply/4')
      }
    },
  },
  components: { corp },
}
</script>

<style lang="scss" scoped>
.main-content {
  &-banner {
    background: url(~@A/images/solution/mouse/img_voice_mouse_bg_banner.png)
      center no-repeat;
    background-size: cover;
    height: 500px;
    overflow: hidden;
    width: 100%;
    .banner-text {
      max-width: 1200px;
      color: #fff;
      height: 100%;
      margin: auto;
      &-button {
        font-size: 16px;
        text-align: center;
        font-weight: 400;
        width: 140px;
        height: 40px;
        line-height: 40px;
        border: 1px solid #fff;
        border-radius: 40px;
        color: #fff;
        cursor: pointer;
        transition: 0.6s;
      }
      h2 {
        color: #fff;
        padding-top: 148px;
        margin-bottom: 29px;
        font-size: 48px;
        font-weight: 500;
        line-height: 48px;
      }
      p {
        font-size: 18px;
        margin-bottom: 74px;
      }

      .banner-text-content {
        width: 570px;
        font-size: 16px;
        font-family: SourceHanSansSC-Regular, SourceHanSansSC;
        font-weight: 400;
        color: rgba(255, 255, 255, 0.86);
        line-height: 30px;
      }
    }
  }

  .section {
    p,
    ul {
      margin-bottom: 0;
    }
    max-width: 1200px;
    overflow: hidden;
    margin: 0 auto;
    .section-title {
      text-align: center;
      font-size: 34px;
      font-family: SourceHanSansSC-Medium, SourceHanSansSC;
      font-weight: bold;
      color: #333;
      line-height: 34px;
      position: relative;
      width: 200px;
      margin: 0 auto;
      .arrow {
        width: 160px;
        height: 8px;
        display: inline-block;
        background-repeat: no-repeat;
        background-size: contain;
        position: absolute;
        z-index: 1;
      }
      .arrow-left {
        background-position: left;
        // background-image: url(~@A/images/solution/smart-hardware/arrow-left.png);
        background-image: url(~@A/images/solution/offline/img_title_01.png);
        top: 50%;
        left: -160px;
      }
      .arrow-right {
        background-position: right;
        // background-image: url(~@A/images/solution/smart-hardware/arrow-right.png);
        background-image: url(~@A/images/solution/offline/img_title_02.png);
        top: 50%;
        right: -160px;
      }
    }
    .section-desc {
      text-align: center;
      margin-top: 40px;
      font-size: 16px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #999999;
    }
    .section-title-bold {
      font-size: 34px;
      font-family: Microsoft YaHei;
      font-weight: bold;
      color: #333333;
    }
    .section-title-spec {
      font-size: 30px;
      font-weight: 500;
      color: #656565;
      line-height: 42px;
    }
    .section-tabs {
      margin-top: 70px;
      ul {
        display: flex;
        justify-content: center;
      }
      li {
        font-size: 20px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #666;
        line-height: 30px;
        position: relative;
        cursor: pointer;
        &:hover {
          color: #1f90fe;
        }
        &.active {
          color: #1f90fe;
          &::before {
            position: absolute;
            content: ' ';
            z-index: 1;
            width: 180px;
            height: 4px;
            background: #1f90fe;
            border-radius: 2px;
            bottom: -32px;
            left: -48px;
          }
          &::after {
            position: absolute;
            content: ' ';
            display: inline-block;
            width: 0;
            height: 0;
            bottom: -100px;
            left: 50%;
            transform: translateX(-50%);
            border: 46px solid;
            border-color: transparent transparent #f4f7f9;
          }
        }
      }
      li + li {
        margin-left: 165px;
      }
    }
    .section-sub-title {
      text-align: center;
      font-size: 18px;
      font-weight: 400;
      color: #777777;
      line-height: 25px;
      margin-top: 42px;
    }
    .section-sub-title-spec {
      font-size: 17px;
      font-weight: 400;
      color: #999999;
      line-height: 28px;
      margin-top: 18px;
    }
  }
}
.banner-wrap {
  width: 100%;
  min-width: 1000px;
  padding-top: 60px;
  background: url('../../../assets/images/solution/mouse/banner.jpg') center
    no-repeat;
}
.banner {
  margin: 0 auto;
  padding-top: 112px;
  height: 500px;
  width: 1200px;
  position: relative;
  color: #fff;
}
.banner-title {
  margin-bottom: 20px;
  font-size: 46px;
  line-height: 60px;
  letter-spacing: 2px;
}
.banner-desc {
  width: 528px;
  font-size: 20px;
  line-height: 32px;
}
.indevice-btn {
  display: inline-block;
  margin-top: 64px;
  margin-right: 16px;
  font-size: 16px;
  text-align: center;
  font-weight: 500;
  width: 200px;
  height: 52px;
  line-height: 52px;
  letter-spacing: 0.5px;
  border: 1px solid #fff;
  border-radius: 2px;
  color: #fff;
  cursor: pointer;
  transition: 0.6s;
  &:hover {
    color: #002985;
    background: #fff;
  }
}
.section {
  padding: 50px 0 84px;
  text-align: center;
  .section-title {
    margin-bottom: 60px;
    font-size: 40px;
    font-weight: 500;
  }
  .section-content {
    margin: 50px auto 0;
    width: 1200px;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
  }
  .content-item {
    flex: 0 0 auto;
  }
}

.section1 {
  .content-item {
    position: relative;
    padding-top: 48px;
    width: 384px;
    height: 480px;
    text-align: center;
    font-size: 26px;
    color: #666;
    background: url('../../../assets/images/solution/mouse/application-bg2.png')
      center no-repeat;
    background-size: cover;
    overflow: hidden;
    &:first-child {
      background: url('../../../assets/images/solution/mouse/application-bg1.png')
        center no-repeat;
      background-size: cover;
    }
    &:last-child {
      background: url('../../../assets/images/solution/mouse/application-bg3.png')
        center no-repeat;
      background-size: cover;
    }
  }
  .question {
    float: right;
    margin-bottom: 24px;
    padding: 10px 16px;
    line-height: 22px;
    color: #fff;
    background: rgba(38, 38, 38, 1);
    box-shadow: 0px 1px 3px 0px rgba(23, 132, 233, 0.2);
    border-radius: 21px 21px 0px 21px;
  }
  .before-short-answer {
    margin-left: 150px;
  }
  .answer {
    float: left;
    margin-bottom: 24px;
    padding: 10px 16px;
    line-height: 22px;
    text-align: left;
    border-radius: 21px 21px 21px 0px;
    background: #fff;
  }
}
.section3 {
  background: #f8fafb;
  max-width: unset !important;
  .content-item {
    width: 240px;
    height: 232px;
    text-align: center;
    font-size: 16px;
    color: $grey5;
  }
  .fist-content-item {
    margin-bottom: 34px;
  }
  .bg {
    // border: 1px solid red;
    margin: 0 auto;
    width: 111px;
    height: 90px;
    background: url(~@A/images/solution/mouse/icon_word.png) center/100%
      no-repeat;
  }
  .bg2 {
    width: 111px;
    height: 90px;
    background: url(~@A/images/solution/mouse/icon_speech_recognition.png)
      center/100% no-repeat;
  }
  .bg3 {
    width: 100px;
    height: 90px;
    background: url(~@A/images/solution/mouse/icon_translation.png) center/100%
      no-repeat;
  }
  .bg4 {
    width: 118px;
    height: 100px;
    background: url(~@A/images/solution/mouse/icon_dialect.png) center/100%
      no-repeat;
  }
  .bg5 {
    width: 118px;
    height: 100px;
    background: url(~@A/images/solution/mouse/icon_voice_control.png)
      center/100% no-repeat;
  }
  .bg6 {
    width: 118px;
    height: 100px;
    background: url(~@A/images/solution/mouse/icon_ocr.png) center/100%
      no-repeat;
  }
  .item-title {
    margin: 16px 0 8px;
    font-size: 18px;
    line-height: 18px;
    color: #333;
    font-weight: bold;
  }
  .item-desc {
    font-size: 16px;
    font-weight: 400;
    color: #999;
    line-height: 30px;
    margin-top: 22px;
    text-align: left;
    display: inline-block;
  }
}

.show-in-mobile {
  display: none !important;
}
.show-in-pc {
  display: block;
}

.contact-wrap {
  text-align: center;
  .section-title {
    margin-bottom: 6px;
  }
  .desc {
    font-size: 16px;
    color: #666;
    margin-top: 43px;
  }
  .apply-btn {
    margin: 23px auto 0;
    background: #1784e9;
    &:hover {
      color: #fff;
    }
  }
}

@media screen and (max-width: 719px) {
  .banner-wrap {
    min-width: unset;
    height: 520px;
    text-align: center;
    background: url('../../../assets/images/solution/mouse/banner-m.jpg') center
      no-repeat;
    background-size: 478px;
    background-position-y: bottom;
    .banner {
      width: 100%;
      padding-top: 40px;
      padding-left: 24px;
      padding-right: 24px;
      height: 460px;
    }
    .banner-title {
      margin-bottom: 8px;
      font-size: 30px;
      line-height: 40px;
    }
    .banner-desc {
      width: 100%;
      font-size: 14px;
      line-height: 22px;
    }
  }

  .indevice-btn {
    display: inline-block;
    margin-top: 32px;
    margin-bottom: 64px;
    width: 160px;
  }
  .section {
    padding: 0 24px;
    .section-title {
      margin-top: 64px;
      margin-bottom: 32px;
      font-size: 24px;
    }
    .section-content {
      margin: 0 auto;
      width: 100%;
      display: flex;
      flex-direction: column;
    }
    .content-item {
      flex: 0 0 auto;
    }
  }
  .section1 {
    .content-item {
      margin-bottom: 24px;
      padding-top: 40px;
      width: 100%;
      font-size: 20px;
    }
  }

  .section3 {
    .section-title {
      padding-top: 56px;
    }
    .section-content {
      flex-direction: row;
      flex-wrap: wrap;
    }
    .content-item {
      flex-direction: column;
      margin-bottom: 36px;
      width: 152px;
      height: unset;
      font-size: 12px;
      &:nth-child(5) {
        margin-bottom: 64px;
      }
    }
    // .bg {
    //   width: 80px;
    //   height: 80px;
    //   background-position-x: -78px;
    //   background-size: cover;
    // }

    // .bg2 {
    //   background-position-x: 78px;
    // }
    // .bg3 {
    //   background-position-x: 0;
    // }
    // .bg4 {
    //   background-position-x: -156px;
    // }
    // .bg5 {
    //   background-position-x: 156px;
    // }
    // .bg6 {
    //   background-position-x: 234px;
    // }
    .item-title {
      margin: 10px 0 8px;
      font-size: 16px;
      line-height: 22px;
    }
  }
  .show-in-mobile {
    display: block !important;
  }
  .show-in-pc {
    display: none !important;
  }

  .contact-wrap {
    .section-title {
      margin-bottom: 12px;
    }
    .desc {
      line-height: 22px;
      font-size: 14px;
    }
    .apply-btn {
      margin: 32px auto 64px;
    }
  }
}

@media screen and (max-width: 330px) {
  .section3 {
    .content-item {
      width: 132px;
    }
  }
}
</style>
