<template>
  <div class="debug-container">
    <p class="tool-title">
      <span>接口调试</span>
      <el-switch v-model="apiSwitch"></el-switch>
    </p>
    <div class="tool-content" v-if="apiSwitch">
      <el-form
        class="mgt32"
        ref="form"
        size="medium"
        :model="form"
        label-width="100px"
        label-position="right"
        :disabled="!subAccountEditable"
      >
        <el-form-item label="情景模式：">
          <el-select
            class="form-select"
            v-model="form.scene"
            placeholder="请选择情景模式"
          >
            <el-option
              v-for="(item, index) in sceneList"
              :key="index"
              :label="item.sceneName"
              :value="item.sceneBoxName"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="authId">
          <el-input v-model="form.authId"></el-input>
        </el-form-item> -->
        <el-form-item label="ApiKey：">
          <div>{{ appInfo.appkey }}</div>
        </el-form-item>
        <el-form-item label="时间戳：">
          <div>当前UTC时间戳（单位:s）</div>
        </el-form-item>
        <el-form-item label="数据类型：">
          <el-select
            class="form-select"
            v-model="form.dataType"
            placeholder="数据类型"
            @change="dataTypeChange"
          >
            <el-option label="文本" value="text"></el-option>
            <el-option label="音频" value="audio"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="form.dataType === 'text'" label="测试文本：">
          <el-input
            type="textarea"
            v-model.trim="form.text"
            :maxlength="120"
            placeholder="请输入测试文本"
          ></el-input>
        </el-form-item>
        <template v-else>
          <el-form-item label="音频采样率：">
            <el-select
              class="form-select"
              v-model="form.sampleRate"
              placeholder="音频采样率"
            >
              <el-option label="8000" value="8000"></el-option>
              <el-option label="16000" value="16000"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="音频编码：">
            <el-select
              class="form-select"
              v-model="form.aue"
              placeholder="数据类型"
              @change="aueChange"
            >
              <el-option label="raw" value="raw"></el-option>
              <el-option label="speex" value="speex"></el-option>
              <el-option label="speex-wb" value="speex-wb"></el-option>
            </el-select>
          </el-form-item>
        </template>

        <el-form-item
          v-if="form.aue === 'speex' || form.aue === 'speex-wb'"
          label="speex_size："
        >
          <el-input
            v-model="form.speexSize"
            placeholder="音频帧率，如：60"
          ></el-input>
        </el-form-item>
        <el-form-item v-if="form.dataType === 'audio'" label="音频文件：">
          <el-upload
            class="audio-upload"
            ref="upload"
            accept=".pcm, .wav, .speex, .spx"
            :action="`${$config.server}${
              this.$store.state.user.baseUrl
            }/app/webapi/debug/webaiui?appid=${appId}&apikey=${
              appInfo.appkey
            }&scene=${form.scene}&dataType=audio&curTime=${
              Date.parse(new Date()) / 1000
            }&sampleRate=${form.sampleRate}&aue=${form.aue}&speexSize=${
              form.speexSize
            }`"
            :on-change="handleChange"
            :on-remove="handleRemove"
            :before-upload="beforeUpload"
            :on-success="handleSuccess"
            :file-list="fileList"
            :auto-upload="false"
          >
            <el-button slot="trigger" size="small" type="primary"
              >选取文件</el-button
            >
            <a
              class="link-text"
              style="margin-left: 20px"
              href="https://aiui-file.cn-bj.ufileos.com/16kVoice.pcm"
              >模板</a
            >
            <div slot="tip" class="el-upload__tip">
              {{ form.uploadTips[form.aue] }}
            </div>
          </el-upload>
        </el-form-item>
        <el-form-item class="btn-group">
          <el-button
            v-if="form.dataType === 'text'"
            :loading="isSubmitting"
            @click="textDebug"
            >开始测试</el-button
          >
          <el-button v-else :loading="isSubmitting" @click="audioDebug"
            >开始测试</el-button
          >
        </el-form-item>
      </el-form>

      <el-dialog
        title="调试结果"
        :visible.sync="showDebugResult"
        width="80%"
        top="5vh"
        custom-class="webapi-debug-dialog "
      >
        <div class="webapi-debug-container">
          <p class="debug-title">请求</p>
          <el-tabs class="debug-res-tabs mgb24">
            <el-tab-pane label="Header">
              <table class="params-table">
                <tr>
                  <th>params</th>
                  <th>value</th>
                </tr>
                <tr v-for="item in reqHeader">
                  <td>{{ item.split(':')[0] }}</td>
                  <td>{{ item.split(':')[1] }}</td>
                </tr>
              </table>
            </el-tab-pane>
            <el-tab-pane label="Body">
              <table class="params-table">
                <tr>
                  <td>{{ reqBody }}</td>
                </tr>
              </table>
            </el-tab-pane>
          </el-tabs>
          <p class="debug-title">返回</p>
          <p class="debug-res-tip">请求结果：{{ resTip }}</p>
          <div class="debug-res-wrap">
            <p>
              自定义菜单：WebAPI 接口/V2/{{
                form.dataType === 'text' ? '文本' : '音频'
              }}
            </p>
            <p>请求地址：http://openapi.xfyun.cn/v2/aiui</p>
            <p>返回结果：</p>
            <pre class="debug-res-json"> {{ resBody }}</pre>
          </div>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import md5 from 'blueimp-md5'

export default {
  name: 'webapi-debug',
  props: {
    appInfo: Object,
    subAccountEditable: Boolean,
    appId: String,
  },
  data() {
    return {
      sceneList: [],
      loading: false,
      form: {
        scene: 'main_box',
        // authId: md5(this.$utils.experienceUid()), // 体验uid md5后的值,
        dataType: 'text',
        text: '北京今天的天气',
        sampleRate: '16000',
        aue: 'raw',
        uploadTips: {
          raw: '仅支持后缀名为pcm/wav文件，且不超过2M',
          speex: '仅支持后缀名为speex/spx文件，且不超过512K',
          'speex-wb': '仅支持后缀名为speex/spx文件，且不超过512K',
        },
        speexSize: '',
      },
      fileList: [],
      hasUploadFile: false,
      isSubmitting: false,
      reqHeader: '',
      reqBody: '',
      resBody: '',
      resTip: '',
      showDebugResult: false,
      apiSwitch: false,
    }
  },
  methods: {
    getAppSceneList() {
      let self = this
      self.loading = true
      this.$utils.httpGet(
        this.$config.api.AIUI_SCENE_LIST,
        {
          appid: this.appId,
        },
        {
          success: (res) => {
            self.loading = false
            if (res.flag) {
              self.sceneList = res.data
            } else {
              this.$message.error(res.desc)
            }
          },
          error: () => {
            self.loading = false
          },
        }
      )
    },
    textDebug() {
      let self = this
      // if (!this.checkAuthId()) return
      if (!this.form.text) {
        this.$message.warning('请输入测试文本')
        return
      }
      let data = {
        appid: this.appId,
        apikey: this.appInfo.appkey,
        scene: this.form.scene,
        // authId: this.form.authId,
        text: this.form.text,
        dataType: this.form.dataType,
        curTime: Date.parse(new Date()) / 1000,
      }

      this.isSubmitting = true
      this.$utils.httpPost(this.$config.api.AIUI_WEBAPI_DEBUG, data, {
        success: (res) => {
          if (res.flag) {
            self.reqHeader = res.data.reqHeader
            self.reqBody = res.data.reqBody
            let data = JSON.parse(res.data.resBody)
            self.resBody = JSON.stringify(data, null, '    ')
            self.resTip = data.desc
            self.isDebug = true
            self.showDebugResult = true
          } else {
            this.$message.error(res.desc)
          }

          self.isSubmitting = false
        },
      })
    },
    beforeUpload(file) {
      this.isSubmitting = true
    },
    handleChange(file, fileList) {
      let name = file.name.split('.')[file.name.split('.').length - 1]
      let size = file.size
      if (
        name !== 'pcm' &&
        name !== 'wav' &&
        name !== 'speex' &&
        name !== 'spx'
      ) {
        this.$message.warning('仅支持后缀名pcm/wav/speex/spx音频文件')

        this.$refs.upload.handleRemove(fileList)
        return false
      }

      /**
       * 音频类型限制
       * raw（未压缩的pcm或wav格式）
       * speex（speex格式）
       * speex-wb（宽频speex格式）
       *
       */

      if (this.form.aue === 'raw' && name !== 'pcm' && name !== 'wav') {
        this.$message({
          message: '音频编码为raw，仅支持后缀名为pcm/wav音频文件',
          type: 'warning',
        })

        this.$refs.upload.handleRemove(fileList)
        return false
      }

      if (
        (this.form.aue === 'speex' || this.form.aue === 'speex-wb') &&
        name !== 'speex' &&
        name !== 'spx'
      ) {
        this.$message({
          message: `音频编码为${this.form.aue} 时，仅支持后缀名为speex/spx音频文件`,
          type: 'warning',
        })

        this.$refs.upload.handleRemove(fileList)
        return false
      }

      if (this.form.aue === 'raw' && size / 1024 / 1024 > 2) {
        this.$message.warning('音频文件不能超过2M')

        this.$refs.upload.handleRemove(fileList)
        return false
      }

      if (
        (this.form.aue === 'speex' || this.form.aue === 'speex-wb') &&
        size / 1024 > 512
      ) {
        this.$message.warning('音频文件不能超过512K')

        this.$refs.upload.handleRemove(fileList)
        return false
      }

      if (fileList.length === 1) {
        this.hasUploadFile = true
      } else if (fileList.length === 2) {
        this.$refs.upload.handleRemove(fileList[0])
        this.hasUploadFile = true
      }
    },
    // checkAuthId() {
    //   if (this.form.authId === '') {
    //     this.$message.warning('auth_id 不能为空')
    //     return false
    //   }
    //   if (
    //     !/^[a-z0-9]+$/.test(this.form.authId) ||
    //     this.form.authId.length !== 32
    //   ) {
    //     this.$message.warning(
    //       'auth_id 应为长度32位字符串，支持英文小写字母和数字'
    //     )
    //     return false
    //   }
    //   return true
    // },
    checkSpxFileSize() {
      if (this.form.speexSize === '') {
        this.$message.warning('speex_size不能为空')
        return false
      }
      if (!/^[0-9]+$/.test(this.form.speexSize)) {
        this.$message.warning('speex_size仅支持仅支持数字')
        return false
      }
      return true
    },
    aueChange() {
      if (this.$refs.upload) this.$refs.upload.handleRemove()
    },
    handleRemove(file, fileList) {
      this.hasUploadFile = false
    },
    handleSuccess(res, file, fileList) {
      if (res.flag) {
        this.reqHeader = res.data.reqHeader
        this.reqBody = '音频文件'
        let data = JSON.parse(res.data.resBody)
        this.resBody = JSON.stringify(data, null, '    ')
        this.resTip = data.desc
        this.showDebugResult = true
      } else {
        this.$message.error(res.desc || '文件上传失败')
      }

      this.$refs.upload.clearFiles()
      this.hasUploadFile = false
      this.isSubmitting = false
    },
    dataTypeChange() {
      this.isSubmitting = false
      if (this.form.dataType === 'text') {
        this.form.sampleRate = '16000'
        this.form.aue = 'raw'
      }
    },
    audioDebug() {
      // if (!this.checkAuthId()) return
      if (
        (this.form.aue === 'speex' || this.form.aue === 'speex-wb') &&
        !this.checkSpxFileSize()
      )
        return
      if (!this.hasUploadFile) {
        this.$message.warning('请选择音频文件')
        return
      }
      this.$refs.upload.submit()
    },
  },
  // computed: {
  //   appId() {
  //     return this.$route.params.appId
  //   },
  // },
  created() {
    this.getAppSceneList()
  },
}
</script>

<style lang="scss" scoped>
.debug-container {
  width: 60%;
}
.tool-title {
  position: relative;
  font-weight: 500;
  padding-left: 10px;
  margin-top: 18px;
  // margin-bottom: 8px;
  & > span {
    display: inline-block;
    width: 65px;
    color: #333;
    font-weight: bold;
    font-size: 15px;
  }
  &:before {
    width: 2px;
    height: 16px;
    background-color: $primary;
    content: ' ';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    margin: auto;
  }
}
.webapi-debug-container {
  height: 75vh;
  overflow-y: auto;
}
.tool-content {
  padding-left: 0px;
}
.form-select {
  width: 100%;
}
.debug-title {
  color: $semi-black;
  font-size: 18px;
  margin-bottom: 8px;
}
.params-table {
  width: 100%;
  text-align: center;
  table-layout: fixed;
  word-break: break-all;
  border-spacing: 0px;
  border: 1px solid #dfe6ec;

  th {
    width: 50%;
    height: 40px;
    background: #eef1f6;
    line-height: 40px;
    border-bottom: 1px solid $grey2;
  }
  td {
    width: 50%;
    padding: 10px 5px;
    vertical-align: middle;
    border-bottom: 1px solid $grey2;
  }
  tr:last-child td {
    border-bottom: none;
  }
}
.debug-res-tip {
  color: $primary;
  margin-bottom: 8px;
}
.debug-res-wrap {
  width: 100%;
  color: $white;
  padding: 12px 16px;
  line-height: 26px;
  background: $semi-black;
  border-radius: 2px;
}
.debug-res-json {
  padding: 0 15px;
  overflow-x: auto;
}
.el-upload__tip {
  color: $grey5;
}
.btn-group {
  display: flex;
  justify-content: flex-end;
}
</style>
<style lang="scss">
.audio-upload {
  .el-upload {
    display: inline-block;
  }
}
.webapi-debug-dialog {
  margin-bottom: 0;
  .el-dialog__body {
    height: 82vh;
  }
}
</style>
