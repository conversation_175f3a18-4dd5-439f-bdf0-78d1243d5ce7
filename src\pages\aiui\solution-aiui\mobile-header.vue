<template>
  <div class="header">
    <img
      :src="
        require('../../../assets/images/solution/smart-hardware/mobile/mobileHeader.png')
      "
      alt=""
    />

    <!-- <i class="el-icon-menu" @click="toggleSidebar" id="el-icon-menu"></i> -->

    <div class="my-icon" @click="toggleSidebar">
      <svg-icon iconClass="menu" id="el-icon-menu"></svg-icon>
    </div>

    <div class="overlay" v-if="isSidebarVisible" @click="closeSidebar"></div>

    <div class="sidebar" :class="{ 'is-visible': isSidebarVisible }">
      <!-- <ul class="menu">
        <li v-for="item in menuData" :key="item.title" class="menu-item">
          <div class="menu-title" @click="toggleSubMenu(item)">
            {{ item.title }}
            <span v-if="item.children" class="arrow">
              {{ item.isOpen ? '▲' : '▼' }}
            </span>
          </div>

          <ul v-if="item.children && item.isOpen" class="submenu">
            <li
              v-for="subItem in item.children"
              :key="subItem.title"
              class="submenu-item"
            >
              {{ subItem.title }}
            </li>
          </ul>
        </li>
      </ul> -->

      <el-row>
        <!-- 左侧一级菜单 -->
        <el-col :span="8" class="left-menu">
          <el-menu
            class="el-menu-vertical"
            :default-active="String(activeIndex)"
            @select="handleSelect"
          >
            <el-menu-item index="0">
              <span slot="title" @click="toBlankPage('/')">首页</span>
            </el-menu-item>

            <el-menu-item index="1">
              <span slot="title">平台能力</span>
            </el-menu-item>

            <el-menu-item index="2">
              <span slot="title">行业方案</span>
            </el-menu-item>

            <el-menu-item index="3">
              <span slot="title" @click="toBlankPage('/modelExperience')"
                >交互体验</span
              >
            </el-menu-item>

            <el-menu-item index="4">
              <span slot="title">产品接入</span>
            </el-menu-item>

            <el-menu-item index="5">
              <!-- <span slot="title">文档中心</span> -->

              <a :href="`${this.$config.docs}doc-1/`" target="_blank"
                >文档中心</a
              >
            </el-menu-item>
          </el-menu>
        </el-col>

        <el-col :span="16" class="right-menu">
          <div v-if="menuData[activeIndex] && menuData[activeIndex].children">
            <el-menu class="el-menu-vertical" :unique-opened="true">
              <el-submenu
                v-for="(submenu, index) in menuData[activeIndex].children"
                :key="index"
                :index="String(index)"
              >
                <template #title>{{ submenu.title }}</template>
                <el-menu-item
                  v-for="(child, subIndex) in submenu.children || []"
                  :key="subIndex"
                  :index="`${activeIndex}-${index}-${subIndex}`"
                  @click="gotolink(child.link)"
                >
                  {{ child.title }}
                </el-menu-item>
              </el-submenu>
            </el-menu>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { transJumpPageUrl } from '@U/transJumpPageUrl.js'
export default {
  name: 'AiuiWebMobileHeader',

  data() {
    return {
      isSidebarVisible: false,

      activeIndex: 0, // 当前激活的一级菜单索引

      menuData: [
        {
          title: '首页',
        },
        {
          title: '平台能力',
          isOpen: false,
          children: [
            {
              title: '通用能力',
              children: [
                {
                  title: '多模态交互',
                  link: '/solution/multimodal-interaction',
                },
                { title: '虚拟人交互', link: '/solution/meta-human' },
                { title: '免唤醒语音交互', link: '/solution/wakeup' },
                { title: '离线语音交互', link: '/solution/offline' },
                { title: '多模态降噪', link: '/solution/multimodality' },
                { title: '低功耗唤醒', link: '/solution/three-wakeup' },
              ],
            },
            {
              title: '硬件模组',
              children: [
                {
                  title: '离线语音识别套件',
                  link: '/solution/soft-hardware/ZG803',
                },
                {
                  title: 'USB声卡开发套件',
                  link: '/solution/soft-hardware/usb-soundcard',
                },
                {
                  title: '降噪开发套件',
                  link: '/solution/soft-hardware/RK3328',
                },
                {
                  title: '语音交互开发套件',
                  link: '/solution/soft-hardware/RK3328S',
                },
                {
                  title: '多模态交互开发套件',
                  link: '/solution/soft-hardware/RK3588',
                },
              ],
            },
            {
              title: '语义技能',
              children: [{ title: '技能商店', link: '/store/all' }],
            },
          ],
        },
        {
          title: '行业方案',
          isOpen: false,
          children: [
            {
              title: '智能硬件',
              children: [
                {
                  title: '儿童玩具',
                  link: '/solution/child-toys',
                },
                {
                  title: '儿童教育',
                  link: '/solution/child-education',
                },
                {
                  title: '语音点歌',
                  link: '/solution/ktv',
                },
                {
                  title: '智能投影',
                  link: '/solution/reflection',
                },
                {
                  title: '智能鼠标',
                  link: '/solution/mouse',
                },
              ],
            },
            {
              title: '企业助手',
              children: [
                {
                  title: '大屏调度',
                  link: '/solution/screen',
                },
              ],
            },
            {
              title: '机器人/虚拟人件',
              children: [
                {
                  title: '服务机器人',
                  link: '/solution/robot',
                },
                {
                  title: '营销机器人',
                  link: '/solution/smart-retail',
                },
              ],
            },
            {
              title: '智慧交通',
              children: [
                {
                  title: '智慧轨道交通',
                  link: '/solution/subway',
                },
              ],
            },
          ],
        },
        {
          title: '交互体验',
        },
        {
          title: '产品接入',
          isOpen: false,
          children: [
            {
              title: '我的应用',
              children: [
                {
                  title: '我的应用',
                  link: !this.subAccountInfo ? '/app' : '/sub/apps',
                },
              ],
            },
            {
              title: '自定义业务',
              children: [
                {
                  title: '自定义业务',
                  link: !this.subAccountInfo ? '/studio/qaBank' : '/sub/skills',
                },
              ],
            },
          ],
        },
        {
          title: '文档中心',
        },
      ],
    }
  },

  computed: {
    ...mapGetters({
      userInfo: 'user/userInfo',
      messageUnRead: 'user/messageUnRead',
      subAccountInfo: 'user/subAccountInfo',
      subAccount: 'user/subAccount',
      //showGiftMenu: 'user/showGiftMenu',
      limitCount: 'aiuiApp/limitCount',
      subAccountHasCreateSkillAuth: 'aiuiApp/subAccountHasCreateSkillAuth',
      subAccountSkillAuths: 'aiuiApp/subAccountSkillAuths',
    }),

    hasUserInfo() {
      return this.userInfo || this.subAccountInfo
    },
  },

  methods: {
    toggleSidebar() {
      this.isSidebarVisible = !this.isSidebarVisible
    },
    closeSidebar() {
      this.isSidebarVisible = false
    },

    toggleSubMenu(item) {
      item.isOpen = !item.isOpen // 切换展开状态
    },

    handleSelect(index) {
      // 选中一级菜单
      this.activeIndex = parseInt(index)
    },

    handleMenuClick(index) {
      // 如果是带子级菜单的条目，展开/折叠子菜单
      const menuItem = this.menuData[index]
      if (menuItem.children) {
        menuItem.isOpen = !menuItem.isOpen
      }
    },

    toBlankPage(path) {
      const routeData = this.$router.resolve({ path })
      window.open(routeData.href, '_blank')
    },

    gotolink(name) {
      let url = transJumpPageUrl(name, {
        chan: 'AIUI',
        way: 'menu',
      })
      window.open(url, '_blank')
    },

    // handleOpen() {},
    // handleClose() {},
  },
}
</script>

<style lang="scss" scoped>
.header {
  max-width: 750px;
  padding: 0 15px;
  height: 48px;
  background-color: #fff;
  img {
    float: left;
    background-color: #fff;
  }
  .my-icon {
    float: right;
    font-size: 30px;
    height: 30px;
    width: 30px;
    // margin-top: 10px;
  }
  i {
    float: right;
    font-size: 30px;
    height: 30px;
    width: 30px;
    margin-top: 10px;
  }

  /* 遮罩层 */
  .overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
  }

  /* 侧边栏 */
  .sidebar {
    position: fixed;
    top: 0;
    right: -300px; /* 初始状态在屏幕外 */
    width: 300px;
    height: 100%;
    background-color: #fff;
    box-shadow: -2px 0 5px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    transition: right 0.3s ease;
  }

  .sidebar {
    position: fixed;
    top: 0;
    right: -350px; /* 初始状态在屏幕外 */
    width: 350px;
    height: 100%;
    background-color: #fff;
    box-shadow: -2px 0 5px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    transition: right 0.3s ease;
    overflow-y: auto;
  }

  .sidebar.is-visible {
    right: 0; /* 显示侧边栏 */
  }

  /* 菜单样式 */
  .menu {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .menu-item {
    border-bottom: 1px solid #eee;
  }

  .menu-title {
    padding: 15px;
    font-size: 16px;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .arrow {
    font-size: 12px;
    color: #999;
  }
  .left-menu {
    border-right: 1px solid #e4e4e4;
    height: 100vh;
    a {
      color: #262626;
    }
  }

  .right-menu {
    padding: 20px;
    padding-left: 10px;
    .el-submenu .el-menu-item {
      padding-left: 40px !important;
    }
  }
}
</style>

<style lang="scss" scoped>
.router-link-active {
  opacity: 1 !important;
  color: $primary !important;
  font-weight: 500 !important;
}
</style>
