<template>
  <el-dialog
    title="新增子账号"
    :visible.sync="dialog.show"
    width="480px"
  >
    <div v-loading="loading">
      <p class="top-tip">
        <i class="ic-r-exclamation"></i>新增子帐号后，请提醒子账号用户及时修改密码。</p>
      <a @click="copy">
        <i class="ic-r-copy" title="复制"></i>复制帐号和密码</a>
      <div class="account-info-wrap">
        <p class="title">帐号</p>
        <p class="title">初始密码</p>
        <p>{{ account }}</p>
        <p>{{ pwd }}</p>
      </div>
      <label style="color: #8C8C8C; font-weight: 600;">备注</label>
      <el-input
        placeholder="输入备注名，便于识别"
        style="margin: 6px 0 23px;"
        v-model="remark"></el-input>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button class="dialog-btn"
        type="primary"
        size="medium"
        style="min-width: 104px;"
        @click="save">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    props: {
      dialog: {
        type: Object,
        default: {}
      }
    },
    data() {
      return {
        account: null,
        pwd: '123456',
        remark: '',
        loading: false,
        subUid: ''
      }
    },
    watch: {
      'dialog.show': function(val, oldVal) {
        let self = this
        self.account = null
        self.remark = ''
        if (val) {
          this.createAccount()
        }
      }
    },
    methods: {
      copy(){
        let value = `账号${this.account}    密码${this.pwd}`
        this.$utils.copyClipboard(value)
      },
      save(){
        if(!this.remark) {
          this.$message.success('操作成功')
          this.dialog.show = false
          this.$emit('getAccounts', 1)
        } else {
          this.saveRemark()
        }
      },
      createAccount() {
        let self = this
        self.loading= true
        this.$utils.httpPost(this.$config.api.COOP_ADD_ACCOUNTS, { remark: self.remark }, {
          success: (res) => {
            self.loading= false
            self.account = res.data.login
            self.subUid = res.data.subUid
          },
          error: (err) => {
            self.loading= false
            console.log('page=>>');
            console.log(err);
          }
        })
      },
      saveRemark() {
        let self = this
        let data = {
          subUid: self.subUid,
          remark: self.remark
        }
        self.$utils.httpPost(this.$config.api.COOP_EDIT_ACCOUNTS, data, {
          success: (res) => {
            this.$message.success('操作成功')
            this.dialog.show = false
            this.$emit('getAccounts', 1)
          },
          error: (err) => {
            
          }
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
.top-tip {
  margin-bottom: 24px;
  padding-left: 16px;
  width:416px;
  line-height:40px;
  height:40px;
  background: rgba(255,164,0,0.07);
  border-radius:2px;
  i {
    margin-right: 16px;
    vertical-align: -1px;
    color: $warning;
    font-size: 16px;
  }
}
.ic-r-copy {
  vertical-align: -1px;
  margin-right: 4px;
}
.account-info-wrap {
  margin: 24px 0;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  p {
    flex: 0 0 184px;
  }
  .title {
    margin-bottom: 6px;
    color: #8C8C8C;
    font-weight:600;
  }
}
</style>