<template>
  <div class="buy-main">
    <div class="buy-section buy-section-1">
      <div class="buy-section-title">服务次数包</div>
      <div class="buy-section-tips">
        以下套餐服务量适用于单识别、单语义、单合成及全链路的调用。
      </div>
      <el-table border :data="sectionData1" class="first-td-bg">
        <el-table-column prop="name" label="套餐" align="center">
        </el-table-column>
        <el-table-column
          prop="count"
          label="服务量"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="time"
          label="有效期"
          align="center"
        ></el-table-column>
        <el-table-column label="总价" align="center" width="300">
          <template slot-scope="scope">
            <span :class="{ 'before-price': scope.row.price !== '商务咨询' }">
              {{ scope.row.price }}</span
            >
            <!-- <span
              class="discount-price"
              v-if="scope.row.price !== '商务咨询'"
              >{{ scope.row.priceNew }}</span
            > -->
          </template>
        </el-table-column>
        <el-table-column label="单价（万次）" align="center">
          <template slot-scope="scope">
            <span :class="{ 'before-price': scope.row.oncePrice !== '-' }">
              {{ scope.row.oncePrice }}</span
            >
            <!-- <span class="discount-price" v-if="scope.row.oncePrice !== '-'">{{
              scope.row.oncePriceNew
            }}</span> -->
          </template>
        </el-table-column>
        <el-table-column label="购买" align="center">
          <template slot-scope="scope">
            <el-button
              type="text"
              v-if="scope.row.price === '商务咨询'"
              @click="modalConfirm"
              >联系商务</el-button
            >
            <el-button
              type="text"
              v-else
              @click="buyControl(scope.row, wareData['AIUI服务'])"
              >立即购买</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="buy-section-tips">
        <p>温馨提示：</p>
        <p>
          1.
          新用户默认可使用500次/日服务量进行测试使用；发音人可从免费的发音人中选择，如果需要精品发音人，需要单独购买；
        </p>
        <p>
          2. 若一次性购买超过1000万次以上服务量，可联系商务享受更多价格优惠；
        </p>
        <p>3. 如需开通翻译、音乐信源等，请联系商务线下购买；</p>
        <p>
          4.
          针对KTV、机器人、大屏等交互场景，提供按台授权的售卖模式，请联系商务线下购买；
        </p>
        <p>5. 商务联系方式：<EMAIL>。</p>
      </div>
    </div>
    <div class="buy-section buy-section-2">
      <div class="buy-section-title">方言</div>
      <div class="buy-section-tips">
        此处仅是购买方言类型，服务次数需要通过上面的服务包购买，方言购买后有效期为1年，购买后可在“我的应用-编辑-应用配置-语音识别-方言”页面配置。<br />
        四川话和粤语为免费方言，可直接使用。
      </div>
      <LocalismBuy />
    </div>
    <div class="buy-section buy-section-3">
      <div class="buy-section-title">发音人</div>
      <div class="buy-section-tips">
        此处仅是购买合成的发音人，服务次数需要通过上面的服务包购买，发音人购买后有效期为1年，购买后可在“我的应用-编辑-应用配置-语音合成”页面配置。
      </div>
      <AnchorBuy />
    </div>
    <add-to-app :dialog="add2AppDialog"></add-to-app>
  </div>
</template>

<script>
import AddToApp from './addToApp'
import LocalismBuy from '../../../components/localismBuy.vue'
import AnchorBuy from '../../../components/anchorBuy.vue'

export default {
  name: 'buyService',
  components: {
    AddToApp,
    LocalismBuy,
    AnchorBuy,
  },
  data() {
    return {
      add2AppDialog: {
        show: false,
      },
      wareData: {},
      sectionData1: [
        {
          id: '001',
          name: '体验包',
          count: '100万次',
          price: '￥6500.00',
          priceNew: '￥4550.00 (折)',
          time: '1年',
          oncePrice: '￥65.00',
          oncePriceNew: '￥45.50 (折)',
        },
        {
          id: '002',
          name: '初级包',
          count: '500万次',
          price: '￥30000.00',
          priceNew: '￥21000.00 (折)',
          time: '1年',
          oncePrice: '￥60.00',
          oncePriceNew: '￥42.00 (折)',
        },
        {
          id: '003',
          name: '中级包',
          count: '1000万次',
          price: '￥54000.00',
          priceNew: '￥37800.00 (折)',
          time: '1年',
          oncePrice: '￥54.00',
          oncePriceNew: '￥37.80 (折)',
        },
        {
          id: '004',
          name: '高级包',
          count: '>1000万次',
          price: '商务咨询',
          time: '1年',
          oncePrice: '-',
        },
      ],
    }
  },
  created() {
    this.getWare()
  },
  methods: {
    getWare() {
      let thiz = this
      this.$utils.httpGet(
        this.$config.api.GET_APP_WARE,
        {},
        {
          success: (res) => {
            if (res.flag) {
              thiz.wareData = res.data
            }
            /*self.apps = Array.prototype.map.call(res.data, function (item, index) {
              item.selected = item.isUsed
              return item
            })*/
          },
          error: (err) => {},
        }
      )
    },
    modalConfirm() {
      this.$confirm('联系商务  <EMAIL>', '提示', {
        confirmButtonText: '复制邮箱',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          this.$utils.copyClipboard('<EMAIL>')
        })
        .catch(() => {
          return 0
        })
    },
    buyControl(row, type) {
      if (type == 1701) {
        this.add2AppDialog = {
          show: true,
          wareId: type,
          packageId: type + row.id,
        }
      } else if (type == 7002 || type == 1202) {
        this.add2AppDialog = {
          show: true,
          wareId: type,
          packageId: row.packageId,
        }
      }
    },
  },
}
</script>

<style scoped lang="scss">
.tab-content {
  background: #fff;
}
.before-price {
  // text-decoration: line-through;
  font-size: 14px;
}
.discount-price {
  font-size: 16px;
  color: #ff455b;
}
.buy-main {
  width: 1200px;
  margin: 0 auto;
  height: 100%;
  overflow-y: auto;
  padding-bottom: 100px;

  :deep(.first-td-bg) {
    .el-table__row {
      td:first-of-type {
        background: #f2f5f7;
      }
    }
  }

  :deep(.el-table--border th),
  :deep(.el-table--border td) {
    border-right: 1px solid #e4e7ed;
  }

  .buy-section-title {
    color: #262626;
    font-size: 20px;
    text-align: center;
    margin: 50px 0 30px;
  }

  .buy-section-tips {
    font-size: 14px;
    color: #959595;
    letter-spacing: 0;
    line-height: 22px;
    margin: 10px 0 20px;
  }
}
</style>
