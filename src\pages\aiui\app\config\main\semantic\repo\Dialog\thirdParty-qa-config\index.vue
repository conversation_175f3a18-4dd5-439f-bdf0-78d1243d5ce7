<template>
  <!-- 只展示文档问答 -->
  <el-dialog
    title="添加问答库"
    :visible.sync="visible"
    width="844px"
    :before-close="handleCancel"
  >
    <el-tabs tab-position="left" v-model="activeTab">
      <el-tab-pane label="文档问答" name="doc">
        <doc-qa-pane
          ref="docQaPane"
          :appId="appId"
          :currentScene="currentScene"
          @saveSuccess="onDocQaSaveSuccess"
          @dataChanged="onDataChanged"
        ></doc-qa-pane>
      </el-tab-pane>
    </el-tabs>

    <div slot="footer" class="dialog_footer">
      <div class="dialog_footer_left"></div>
      <div class="dialog_footer_right">
        <el-button @click="handleCancel" size="small">取消</el-button>
        <el-button
          v-show="activeTab == 'doc'"
          type="primary"
          @click="handleConfirm"
          size="small"
          :disabled="!docDataChanged"
          >确定</el-button
        >
      </div>
    </div>
  </el-dialog>

  <!-- 展示所有问答库 -->

  <!-- <el-dialog
    title="添加问答库"
    :visible.sync="visible"
    width="995px"
    :before-close="handleCancel"
  >
    <el-tabs tab-position="left" v-model="activeTab">
      <el-tab-pane label="文档问答" name="doc">
        <doc-qa-pane
          ref="docQaPane"
          :appId="appId"
          :currentScene="currentScene"
          @saveSuccess="onDocQaSaveSuccess"
        ></doc-qa-pane>
      </el-tab-pane>
      <el-tab-pane label="关键词问答" name="keyword">
        <keyword-qa-pane
          ref="keywordQaPane"
          :appId="appId"
          :currentScene="currentScene"
          @saveSuccess="onDocQaSaveSuccess"
        ></keyword-qa-pane>
      </el-tab-pane>
      <el-tab-pane label="语句问答" name="sentence">
        <sentence-qa-pane ref="sentenceQaPane" 
          :appId="appId"
          :currentScene="currentScene" 
          @saveSuccess="onDocQaSaveSuccess"></sentence-qa-pane>
      </el-tab-pane>
      <el-tab-pane label="三方知识库" name="thirdParty">
        <third-party-qa-pane
          ref="thirdPartyQaPane"
          :model-info="modelInfo"
        ></third-party-qa-pane>
      </el-tab-pane>
    </el-tabs>

    <div slot="footer" class="dialog_footer">
      <div class="dialog_footer_left"></div>
      <div class="dialog_footer_right">
        <el-button @click="handleCancel" size="small">取消</el-button>
        <el-button
          v-show="activeTab == 'thirdParty'"
          type="primary"
          @click="handleConfirm"
          size="small"
          >确定</el-button
        >
      </div>
    </div>
  </el-dialog> -->
</template>

<script>
import DocQaPane from './components/docQaPane.vue'
import KeywordQaPane from './components/keywordQaPane.vue'
import SentenceQaPane from './components/sentenceQaPane.vue'
import ThirdPartyQaPane from './components/thirdPartyQaPane.vue'

export default {
  name: 'thirdPartyQAConfig',
  components: {
    DocQaPane,
    KeywordQaPane,
    SentenceQaPane,
    ThirdPartyQaPane,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    modelInfo: {
      type: Object,
      default: () => ({}),
    },
    appId: {
      type: String,
      default: '',
    },
    currentScene: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      activeTab: 'doc',
      docDataChanged: false,
    }
  },
  computed: {},
  methods: {
    // 取消按钮
    handleCancel() {
      this.$emit('update:visible', false)
    },

    handleConfirm() {
      // 调用子组件里的saveSwitchData方法
      this.$refs.docQaPane.saveData()
      setTimeout(() => {
        this.$emit('update:visible', false)
      }, 800)
    },

    onDocQaSaveSuccess() {
      // 将事件传递给祖父组件
      this.$emit('saveSuccess')
    },

    // 监听子组件的数据变化事件
    onDataChanged(changed) {
      this.docDataChanged = changed
    },
  },
}
</script>

<style lang="scss" scoped>
:deep(.el-tabs) {
  margin-left: 0;
  .el-tabs__header {
    display: none; // 先隐藏左侧tab
    margin-right: 0px;
    padding: 20px 12px 0 12px;
    width: 149px;
    // 移除tab内容区域左侧边框
    .el-tabs__nav-wrap::after {
      display: none;
    }
    .el-tabs__item {
      text-align: left;
      padding: 0 15px;
      border-radius: 6px;
      &.is-active {
        background-color: #eff3f9;
        font-weight: 600;
        color: #17171e;
      }
      &:hover:not(.is-active) {
        color: #666;
        background-color: #eff3f9;
      }
    }

    .el-tabs__active-bar {
      &.is-left {
        right: 0;
        width: 0 !important;
        display: none !important;
      }
    }
  }
  .el-tabs__content {
    .el-tab-pane {
      background-color: #f7f7fa;
      padding: 20px 16px;
      padding: 16px 32px 0 32px; // 先隐藏左侧tab
      height: 450px;
      overflow-y: auto;
      // border: 1px solid #e1e1e1;
      border-right: none;
      border-top: none;
    }
  }
}

:deep(.el-dialog) {
  .el-dialog__header {
    border-bottom: 1px solid #e1e1e1;
  }
  .el-dialog__body {
    padding-top: 0;
    padding: 0px;
  }
  .el-dialog__footer {
    padding: 0 !important;
    .dialog_footer {
      display: flex;
      justify-content: space-between;

      .dialog_footer_left {
        display: none; // 先隐藏左侧tab
        width: 149.5px;
        padding: 14px 32px 14px 0;
        border-right: 1px solid #e1e1e1;
      }
      .dialog_footer_right {
        flex: 1;
        padding: 14px 32px 14px 0;
      }
    }
  }
}
</style>
