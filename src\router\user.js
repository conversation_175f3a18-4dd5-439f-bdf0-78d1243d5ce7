import homeCFLayout from '@L/homeCFLayout'
import aiuiHome from '@L/aiuiHome'
import consoleHCLayout from '@L/consoleHC'
import aiuiDefaultLayout from '@L/aiuiDefaultLayout'

export default [
  {
    path: '/user',
    component: homeCFLayout,
    meta: {
      title: 'AIUI开放平台',
    },
    children: [
      {
        path: '/',
        name: 'user',
        redirect: { name: 'user-login' },
      },
      {
        path: 'login',
        name: 'user-login',
        component: () =>
          import(/* webpackChunkName: "login" */ '@P/user/login'),
      },
      {
        path: 'register',
        name: 'user-register',
        component: () =>
          import(/* webpackChunkName: "login" */ '@P/user/register'),
      },
      {
        path: 'ubot-login',
        name: 'ubot-login',
        component: () =>
          import(/* webpackChunkName: "login" */ '@P/user/ubotLogin'),
      },
      {
        path: 'ubot-register',
        name: 'ubot-register',
        component: () =>
          import(/* webpackChunkName: "login" */ '@P/user/ubotRegister'),
      },
      {
        path: 'logout',
        name: 'user-logout',
        component: () =>
          import(/* webpackChunkName: "logout" */ '@P/user/logout'),
      },
    ],
  },
  {
    path: '/user',
    component: aiuiDefaultLayout,
    meta: {
      title: 'AIUI开放平台',
    },
    children: [
      {
        path: 'info',
        name: 'info',
        component: () => import(/* webpackChunkName: "login" */ '@P/user/info'),
      },
    ],
  },
  // {
  //   path:"/user",
  //   component: aiuiHome,
  //   meta: {
  //     title: 'AIUI开放平台'
  //   },
  //   children: [
  //     {
  //       path: 'gift',
  //       name: 'gift',
  //       component:()=>import(/* webpackChunkName: "login" */ "@P/user/novice/giftCollect")
  //     },
  //   ]
  // }
]
