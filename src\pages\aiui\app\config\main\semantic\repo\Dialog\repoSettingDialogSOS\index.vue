<template>
  <el-dialog
    title="问答设置"
    :visible.sync="dialog.show"
    width="600px"
    top="5vh"
    :show-close="true"
    @closed="closeSkillDialog"
  >
    <search-config :form="configForm" @change="onConfigChange"></search-config>

    <span slot="footer" class="dialog-footer">
      <el-button size="small" @click="dialog.show = false">取消</el-button>
      <el-button
        size="small"
        type="primary"
        @click="saveChangeData"
        :loading="saveLoading"
        :disabled="!cofigHasChange"
      >
        保存配置
      </el-button>
    </span>
  </el-dialog>
</template>
<script>
import searchConfig from './searchConfig.vue'

export default {
  props: {
    dialog: Object,
    appId: '',
    currentScene: Object,
  },
  data() {
    return {
      saveLoading: false,

      configForm: {
        channel: 2,
        threshold: 0.1,
      },
      cofigHasChange: false,
    }
  },
  methods: {
    saveChangeData() {
      let that = this
      let param = {
        botId: this.currentScene.botBoxId,
        channel: this.configForm.channel,
        threshold: this.configForm.threshold,
        addRepos: [],
        updateRepos: [],
        delRepos: [],
      }

      // if (this.cofigHasChange) {
      //   param.knowledgeSearch = JSON.stringify(this.configForm)
      // }

      this.$utils.httpPost(
        this.$config.api.AIUI_BOT_CONFIG_SAVE_RAGREOPCONFIG,

        JSON.stringify(param),
        {
          config: {
            headers: {
              'Content-Type': 'application/json',
            },
          },
          success: (res) => {
            that.saveLoading = false
            that.cofigHasChange = false
            that.$emit('saveSuccess')
            that.$message.success('保存成功')
            that.dialog.show = false
          },
          error: (err) => {
            that.saveLoading = false
            that.$message.error(err.desc)
          },
        }
      )
    },

    // 关闭弹窗
    closeSkillDialog() {},

    getRagInfo() {
      let that = this
      this.loading = true
      this.$utils.httpGet(
        this.$config.api.AIUI_BOT_CONFIG_GET_BOTRAGREPOS,
        {
          botId: this.currentScene.botBoxId,
          pageIndex: 1,
          pageSize: 1000,
        },
        {
          success: (res) => {
            console.log(res, '这个是知识库的res')

            that.configForm.channel = res.data.channel
            that.configForm.threshold = Number(res.data.threshold)
          },
          error: (res) => {},
        }
      )
    },

    onConfigChange(type, val) {
      this.cofigHasChange = true
      this.configForm[type] = val
    },
  },
  watch: {
    'dialog.show'(val) {
      if (val) {
        this.getRagInfo()
      }
    },
  },
  components: { searchConfig },
}
</script>
<style lang="scss" scoped>
.skill-header {
  position: absolute;
  top: 12px;
  right: 20px;
}
.tab-container {
  display: flex;
  position: relative;
  &::before {
    position: absolute;
    content: ' ';
    width: 100%;
    height: 1px;
    background: #e7e9ed;
    bottom: 0;
  }
}

.skill-type {
  margin-top: 1%;
  margin-bottom: 1%;
}
.add-skill-tab {
  a {
    display: inline-block;
    width: 108px;
    height: 40px;
    line-height: 40px;
    cursor: pointer;
    color: unset;
    text-align: center;
  }
  .active {
    position: relative;
    color: $primary;
    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      display: inline-block;
      width: 88px;
      height: 2px;
      background-color: #1f90fe;
      border-radius: 2px;
      transform: translateX(-50%);
    }
  }
}
// .check-config {
//   margin-left: 15px;
//   margin-top: 10px;
//   span {
//     color: #1f90fe;
//     cursor: pointer;
//   }
// }
.el-tabs {
  margin-left: 20px;
}
</style>
