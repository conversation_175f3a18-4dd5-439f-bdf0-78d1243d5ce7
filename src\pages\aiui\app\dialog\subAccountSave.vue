<template>
  <el-dialog
    title="保存修改"
    :visible.sync="dialog.show"
    width="480px"
    >
    <el-form :model="form" ref="form" label-width="50px" class="demo-ruleForm"
      @submit.native.prevent>
      <el-form-item
        label="备注"
        prop="content"
        :rules="[
          { required: true, message: '备注不能为空'},
          { min: 1, max: 200, message: '长度在 1 到 200 个字符', trigger: 'blur' }
        ]"
      >
        <el-input type="textarea" v-model="form.content" autocomplete="off"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary"
          size="medium"
          style="margin-bottom:32px; min-width: 104px;" @click="submitForm">提交</el-button>
        <el-button size="medium" style="min-width: 104px;" @click="cancel">取消</el-button>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script>
  export default {
    name: 'sub-account-save-dialog',
    props: {
      dialog: {
        type: Object,
        default: () => ({
          show: false
        })
      }
    },
    data(){
      return {
        form: {
          content: ''
        }
      }
    },
    computed: {
      appId() {
        return this.$route.params.appId
      }
    },
    watch: {
      'dialog.show': function(val) {
        if(val) {
          this.form.content = ''
          this.$refs.form && this.$refs.form.resetFields()
        }
      }
    },
    methods: {
      submitForm(){
        let self = this
        this.$refs.form.validate((valid) => {
          if(valid) {
            this.dialog.show = false
            this.saveSubAccountLog()
          }
        })
      },
      cancel(){
        this.$refs.form.resetFields()
        this.dialog.show = false
      },
      saveSubAccountLog(){
        this.$utils.httpPost(this.$config.api.AIUI_APP_SUB_ACCOUNT_LOG_SAVE, {
          appid: this.appId,
          content: this.form.content },{
          success: res => {
            if(res.flag) {
              this.$emit('saveSubAccountEdit')
            }
          },
          error: err => {
            this.$message.error( err || '保存修改说明保存失败')
          }
        })
      }
    }
  }
</script>

<style lang="scss" scoped>

</style>