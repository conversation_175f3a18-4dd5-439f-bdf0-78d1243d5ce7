import State from './State'
import RECOGNITION_State from './RECOGNITION_State'
import RECOGNITION_SEMANTIC_POSTPROCESS_State from './RECOGNITION_SEMANTIC_POSTPROCESS_State'

class RECOGNITION_POSTPROCESS_State extends State {
  constructor() {
    super('1,3')
  }
  //     RECOGNITION: '1',
  //     RECOGNITION_SEMANTIC: '1,2',
  //     RECOGNITION_SEMANTIC_SYNTHESIS: '1,2,8',
  //     RECOGNITION_TRANSLATE: '1,4',
  //     RECOGNITION_TRANSLATE_SYNTHESIS: '1,4,8',
  //     RECOGNITION_LLM_SEMANTIC: '1,13',
  //     RECOGNITION_LLM_SEMANTIC_SYNTHESIS: '1,13,14',
  //     RECOGNITION_POSTPROCESS: '1,3',
  //     RECOGNITION_SEMANTIC_POSTPROCESS: '1,2,3',
  //     RECOGNITION_SEMANTIC_POSTPROCESS_SYNTHESIS: '1,2,3,8',
  //     RECOGNITION_POSTPROCESS_SYNTHESIS: '1,3,8',
  //     RECOGNITION_SYNTHESIS: '1,8'

  // 1,3 -> 1
  // 1,3 -> 1,2,3
  handle(context, action) {
    switch (action) {
      case 'to_RECOGNITION':
        context.setState(new RECOGNITION_State())
        break
      case 'to_RECOGNITION_SEMANTIC_POSTPROCESS':
        context.setState(new RECOGNITION_SEMANTIC_POSTPROCESS_State())
        break

      default:
        super.handle(context, action)
    }
  }
}
export default RECOGNITION_POSTPROCESS_State
