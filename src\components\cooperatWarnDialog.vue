<template>
  <el-dialog
    title="协同操作可能会出现冲突"
    :visible.sync="dialog.show"
    width="440px"
    @close="clearLocalstorage"
  >
    <i class="ic-r-exclamation"></i>
    <p style="display: inline-block;">多人协同操作可能会出现冲突，导致你的配置无<br/>
    法生效，建议与其他成员沟通后再操作。</p>
    <span slot="footer" class="dialog-footer">
      <el-button class="dialog-btn"
        type="primary"
        size="medium"
        style="min-width: 90px;"
        @click="clearLocalstorage">知道了</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    name: 'cooperat-warn-dialog',
    props: {
      dialog: {
        type: Object,
        default: {}
      },
      type: String
    },
    data() {
      return {}
    },
    methods: {
      clearLocalstorage() {
        this.dialog.show = false
        localStorage.removeItem(this.type)
      }
    }
  }
</script>

<style lang="scss" scoped>
.ic-r-exclamation {
  vertical-align: top;
  margin-right: 14px;
  font-size: 20px;
  color: $warning;
}
</style>