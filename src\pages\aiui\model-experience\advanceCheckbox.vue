<template>
  <div class="checkbox-wrap" @click="toggleChecked">
    <div :class="{ checkbox: true, checked: value }"></div>
    <div :class="{ 'check-text': true, checked: value }"><slot></slot></div>
  </div>
</template>
<script>
/**
 * 自定义checkbox，主要是选中时的勾样式
 */
export default {
  props: {
    value: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {}
  },
  methods: {
    toggleChecked() {
      this.$emit('input', !this.value)
    },
  },
}
</script>
<style lang="scss" scoped>
.checkbox-wrap {
  display: flex;
  align-items: center;
  cursor: pointer;
}
.checkbox-wrap + .checkbox-wrap {
  margin-left: 32px;
}
.checkbox {
  display: inline-block;
  position: relative;
  border: 1px solid #d5d8de;
  border-radius: 2px;
  box-sizing: border-box;
  width: 14px;
  height: 14px;
  background-color: #fff;
  position: relative;
  cursor: pointer;
  &.checked {
    &::after {
      box-sizing: content-box;
      content: ' ';
      width: 16px;
      height: 13px;
      left: 2px;
      position: absolute;
      top: -2px;
      background: url(~@A/images/model-exeperience/v2/<EMAIL>)
        center/contain no-repeat;
    }
  }
}
.check-text {
  font-size: 14px;
  font-weight: 400;
  color: #454973;
  line-height: 20px;
  margin-left: 12px;
  &.checked {
    color: $primary;
  }
}
</style>
