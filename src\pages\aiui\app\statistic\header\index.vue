<template>
  <div class="application-header header2">
    <tabs />
  </div>
</template>
<script>
import { mapGetters } from 'vuex'
import tabs from './tab'

export default {
  data() {
    return {}
  },
  computed: {
    ...mapGetters({
      currentScene: 'aiuiApp/currentScene',
      app: 'aiuiApp/app',
      subAccountEditable: 'aiuiApp/subAccountEditable',
    }),
    appId() {
      return this.$route.params.appId
    },
  },
  methods: {},
  components: { tabs },
}
</script>
<style lang="scss" scoped>
.application-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 35px;
  padding: 0 20px;
  &__left {
    display: flex;
    align-items: center;
  }
  &__right {
    display: flex;
    align-items: center;
  }
  .back-icon {
    margin-right: 14px;
  }

  .config-title {
    font-size: 20px;
    font-weight: 500;
    color: #000000;
    line-height: 20px;
    margin-right: 14px;
  }
}

.header2 {
  // border-top: 1px solid #f4f4f4;
  height: 64px;
}
</style>
