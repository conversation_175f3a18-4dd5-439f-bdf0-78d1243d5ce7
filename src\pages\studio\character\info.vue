<template>
  <os-page :options="pageOptions">
    <studio-character-header-right slot="btn" />
    <div class="info-page">
      <el-form
        class="mgt48 mgb56"
        ref="skillForm"
        :rules="rules"
        :model="character"
        label-width="118px"
        label-position="left"
      >
        <el-form-item
          label="设备人设名称"
          prop="name"
          :placeholder="'请输入设备人设名称'"
        >
          <el-input v-model="character.name"></el-input>
        </el-form-item>
      </el-form>

      <div style="margin-top: 40px">
        <el-button
          type="primary"
          @click="onSubmit(2)"
          :loading="saving"
          :disabled="!changed"
        >
          {{ saving ? '保存中...' : '保存' }}
        </el-button>
        <os-give-up-save :edited="changed" @noSave="noSave" />
      </div>
    </div>
    <page-leave-tips
      :dialog="leaveDialog"
      @save="onSubmit"
      @noSave="noSave"
      @noJump="noJump"
    />
  </os-page>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'character-info',
  data() {
    return {
      pageOptions: {
        title: '基本信息',
        loading: false,
      },
      rules: {
        name: [
          {
            required: true,
            message: '人设名称不能为空',
            trigger: 'blur',
          },
          {
            min: 0,
            max: 32,
            message: '人设名称长度不能超过32个字符',
            trigger: 'blur',
          },
          {
            pattern: /^[a-zA-Z0-9\u4e00-\u9fff]+$/,
            message: '人设名称仅支持汉字、字母、数字',
            trigger: 'blur',
          },
        ],
      },
      character: {},

      saving: false,
      leaveDialog: {
        show: false,
      },
      routeTo: {},

      initDataChanged: false, //记录computed检测不到的数据是否有改变(包括：别名、示例说法、iconUrl)
    }
  },
  beforeRouteLeave: function (to, from, next) {
    if (this.changed) {
      this.leaveDialog.show = true
      this.routeTo = to
      next(false)
    } else {
      next()
    }
  },
  computed: {
    ...mapGetters({
      originalCharacter: 'studioCharacter/character',
    }),
    edited() {
      let self = this
      return self.character.name !== self.originalCharacter.name
    },
    changed() {
      return this.edited || this.initDataChanged
    },
  },
  watch: {
    originalCharacter: function (val, oldVal) {
      this.character = this.$deepClone(val)

      if (this.routeTo.name) {
        this.$router.push({
          name: this.routeTo.name,
          params: this.routeTo.params,
        })
      }
    },
  },
  created() {
    if (this.$store.state.studioCharacter.character.id) {
      this.character = this.$deepClone(
        this.$store.state.studioCharacter.character
      )
    }
  },
  methods: {
    onZhNameBlur(e) {
      this.$refs.skillForm.validateField('name')
    },
    checkInitDataStatus() {
      let self = this
      if (self.character.name != self.originalCharacter.name) {
        self.initDataChanged = true
      } else {
        self.initDataChanged = false
      }
    },

    onSubmit() {
      let self = this
      if (this.saving) {
        return
      }

      this.$refs.skillForm.validate((valid) => {
        if (valid) {
          this.saving = true
          let data = {
            repositoryId: this.$route.params.characterId,
            repositoryName: this.character.name,
          }
          const api = this.$config.api.STUDIO_CHARACTER_ATTRIBUTE_EDIT
          this.$utils.httpPost(api, JSON.stringify(data), {
            config: {
              headers: {
                'Content-Type': 'application/json',
              },
            },

            success: (res) => {
              self.saving = false
              self.$message.success('保存成功')
              self.$refs.skillForm && self.$refs.skillForm.clearValidate()
              self.$store.dispatch(
                'studioCharacter/setCharacter',
                this.character.id
              )

              self.initDataChanged = false
            },
            error: (err) => {
              this.saving = false
              console.log('page=>>')
              console.log(err)
            },
          })
        }
      })
    },
    noSave() {
      this.$refs.skillForm.clearValidate()
      this.initDataChanged = false
      this.character = this.$deepClone(this.originalCharacter)

      if (this.routeTo.name) {
        this.$router.push({
          name: this.routeTo.name,
          params: this.routeTo.params,
        })
      }
    },
    noJump() {
      this.routeTo = {}
    },
  },
  components: {},
}
</script>

<style lang="scss" scoped></style>
