<template>
  <div>
    <div
      v-if="showValue"
      :contenteditable="!subAccountEditable ? false : true"
      :id="'content' + utterance.id"
      :ref="'content' + utterance.id"
      @blur="handleBlur"
      @keyup="flowerBracketHandle"
      @keydown="handleKeyDown"
      @keyup.up="handleUp"
      @keyup.down="handleDown"
    >
      <template v-if="utterance.mark.length > 0">
        <template
          v-for="(mark, index) in utterance.mark"
        >
          <span
            v-if="mark.slotName"
            :style="utteranceColor[mark.slotName]"
            :key="index"
            >{{mark.text ? mark.text : '{'+mark.slotName+'}'}}</span
          ><template v-else>{{ mark.text}}</template>
        </template>
      </template>
    </div>
    <entity-auxiliary-popover ref="entityAuxiliaryPopover" :variablePopover="variablePopover"
      @setSlot="setSlot" />
  </div>
</template>

<script>
  import EntityAuxiliaryPopover from './replyEntityAuxiliaryPopover.vue'
  export default {
    name: 'reply-editor',
    props: {
      utterance: {},
      subAccountEditable: <PERSON>olean,
      slotId: Number,
      slotNames: {
        type: Array,
        required: true,
        default: () => ([])
      }
    },
    data() {
      return {
        showValue: true,
        editSlot: false,
        cursorPos: -1,
        variablePopover: {
          show: false,
          rect: null
        },
        initUtterance: ''
      }
    },
    computed: {
      utteranceColor () {
        return this.$store.state.studioSkill.utteranceColor
      },
      intention () {
        return this.$store.state.studioSkill.intention
      },
    }, 
    mounted() {
      this.initUtterance = this.utterance.utterance
    },
    watch: {
      showValue: function(val) {
        let self = this
        if (val) {
          this.$nextTick(function() {
            let value = self.getNodesValue()
            if (value && self.cursorPos >= 0) {
              let editableValue = self.formatValues(
                value.slice(0, self.cursorPos)
              )
              if (editableValue.length && editableValue[editableValue.length - 1].text === '{') {
                self.setCursortPosition(editableValue.length)
              } else {
                self.setCursortPosition(editableValue.length + 1)
              }
              self.cursorPos = -1
            }
          })
        }
      }
    },
    methods: {
      utterHasSlots(data){
        let self = this
        let tmp = data.split(/[{}]/) || []
        const result = tmp.filter(item => self.slotNames.includes(item))
        if(result.length){
          return true
        }
        return false
      },
      handleBlur(e) {
        let self = this
        if( self.editSlot ) return
        let markNodes = this.$refs[`content${this.utterance.id}`].childNodes
        let marks = []
        let utterance = ''
        let showUtterance = ''
        for (let i = 0; i < markNodes.length; i++) {
          if (markNodes[i].dataset) {
            utterance += self.$utils.strictTrimSpace(markNodes[i].innerHTML).trim()
            showUtterance += markNodes[i].innerHTML
          } else if (self.$utils.strictTrimSpace(markNodes[i].nodeValue).trim()) {
            utterance += self.$utils.strictTrimSpace(markNodes[i].nodeValue).trim()
            showUtterance += markNodes[i].nodeValue
          }
        }
        if (showUtterance.trim() === this.initUtterance) {
          return
        }

        if (!utterance.trim()) {
          return self.$message.warning('语料不能为空')
        }
        let nameValid = this.$rules.judgeUtteranceParams(utterance.trim(), 50)
        if (!nameValid.valid) {
          return self.$message.warning(nameValid.data.message)
        }
        if(!self.utterHasSlots(utterance)) {
          return self.$message.warning('回答语料中必须包含此意图内实体槽位')
        }
        let data = {
          id: this.utterance.id,
          businessId: this.intention.businessId,
          intentId: this.intention.id,
          utterance: utterance.trim(),
          oldUtterance: self.initUtterance,
          type: 3,
          slotId: self.slotId
        }
        this.$utils.httpPost(this.$config.api.STUDIO_INTENT_ADD_EDIT_UTTERANCE, data, {
          success: (res) => {
            self.$emit('change')
            self.initUtterance = this.utterance.utterance
            self.editSlot = false
          },
          error: (err) => {
          }
        })
      },
      handleKeyDown (event) {
        let self = this
        var keycode = window.event ? event.keyCode : event.which;
        var evt = event || window.event;
        if (keycode == 13 && !(evt.ctrlKey)) {
          event.target.blur()
          event.preventDefault();
          return false;
        }
      },
      flowerBracketHandle(e) {
        let self = this
        let flowerKeyCode = [219, 37]
        if(e.keyCode === 38 || e.keyCode === 40) return  //键盘上下方向键
        if(!flowerKeyCode.includes(e.keyCode) && self.cursorPos === -1) return
        const cursor = self.getCursortPosition()
        const value = e.target.textContent
        const rect = window
          .getSelection()
          .getRangeAt(0)
          .getBoundingClientRect()
        if (value[cursor - 1] === '{' || value.substring(cursor - 2, cursor)  == '{}') {
          self.editSlot = true
          self.cursorPos = cursor
        }
        if(self.cursorPos > cursor) {
          self.variablePopover.show = false
        }
        setTimeout(function() {
          self.variablePopover= {
            show: true,
            rect: rect,
            searchVal: value.substring(self.cursorPos, cursor).replace(/}+/g, '')
          }
        }, 0)
      },
      setSlot(item) {
        let self = this
        if(!item) {
         self.editSlot = false
         self.cursorPos = -1
         return
        }
        self.showValue = false
        let searchValLen = this.variablePopover.searchVal ? this.variablePopover.searchVal.length : 0
        let value = this.getNodesValue()
        let left = value.substring(0, this.cursorPos)
        let right = value.substring(this.cursorPos + searchValLen)
        if(item){
          value = `${left}${item}}${right}`
          self.editSlot = false
        }
        let regL = /{+/g
        let regR = /}+/g
        value = value.replace(regL, '{').replace(regR, '}')
        let newMarks  = this.formatValues(value)
        this.$set(this.utterance, 'utterance', value)
        this.$set(this.utterance, 'mark', newMarks)
        this.$nextTick(function() {
          self.showValue = true
        })
      },
      formatValues(data){
        let self = this
        if(!data) return
        let tmp = data.split(/[{}]/) || []
        let regexValues = data.match(/{.*?}/g) || []
        let newMark = []
        tmp.forEach((item, index) => {
          if(self.$utils.inArray(regexValues, `{${item}}`)) {
            newMark.push({
              slotName: item
            })
          } else {
            item && newMark.push({
              text: item
            })
          }
        })
        return newMark
      },
      getNodesValue() {
        let nodes = this.$refs[`content${this.utterance.id}`].childNodes
        let value = ''
        for (let i = 0; i <= nodes.length - 1; i++) {
          value +=
            nodes[i].innerText || nodes[i].nodeValue || nodes[i].textContent
        }
        return value
      },
      // 获取光标位置
      getCursortPosition() {
        var caretOffset = 0
        var element = this.$refs[`content${this.utterance.id}`]
        var doc = element.ownerDocument || element.document
        var win = doc.defaultView || doc.parentWindow
        var sel
        if (typeof win.getSelection != 'undefined') {
          //谷歌、火狐
          sel = win.getSelection()
          if (sel.rangeCount > 0) {
            //选中的区域
            var range = win.getSelection().getRangeAt(0)
            var preCaretRange = range.cloneRange() //克隆一个选中区域
            preCaretRange.selectNodeContents(element) //设置选中区域的节点内容为当前节点
            preCaretRange.setEnd(range.endContainer, range.endOffset) //重置选中区域的结束位置
            caretOffset = preCaretRange.toString().length
          }
        } else if ((sel = doc.selection) && sel.type != 'Control') {
          //IE
          var textRange = sel.createRange()
          var preCaretTextRange = doc.body.createTextRange()
          preCaretTextRange.moveToElementText(element)
          preCaretTextRange.setEndPoint('EndToEnd', textRange)
          caretOffset = preCaretTextRange.text.length
        }
        return caretOffset
      },
      // 设置光标位置
      setCursortPosition(pos) {
        var range, selection
        var element = this.$refs[`content${this.utterance.id}`]
        if (document.createRange) {
          //Firefox, Chrome, Opera, Safari, IE 9+
          range = document.createRange() //创建一个选中区域
          range.selectNodeContents(element) //选中节点的内容
          var text = ''
          if (!element.childNodes[pos]) {
            text = document.createTextNode('')
            element.appendChild(text)
            if (element.childNodes.length === pos) {
              pos -= 1
            }
          } else if (element.childNodes[pos].nodeName === 'SPAN') {
            text = document.createTextNode('')
            element.insertBefore(text, element.childNodes[pos])
          }
          range.setStart(element.childNodes[pos], 0) //设置光标起始为指定位置
          range.collapse(true) //设置选中区域为一个点
          selection = window.getSelection() //获取当前选中区域
          selection.removeAllRanges() //移出所有的选中范围
          selection.addRange(range) //添加新建的范围
        } else if (document.selection) {
          //IE 8 and lower
          range = document.body.createTextRange() //Create a range (a range is a like the selection but invisible)
          range.moveToElementText(element) //Select the entire contents of the element with the range
          range.collapse(false) //collapse the range to the end point. false means collapse to end rather than the start
          range.select() //Select the range (make it the visible selection
        }
      },
      handleUp(){
        if(!this.variablePopover.show) return
        this.$refs.entityAuxiliaryPopover.handleUp()
      },
      handleDown(){
        if(!this.variablePopover.show) return
        this.$refs.entityAuxiliaryPopover.handleDown()
      }
    },
    components: {
      EntityAuxiliaryPopover
    }
  }
</script>

<style lang="scss" scoped>
</style>