<template>
  <div class="main-content">
    <section class="main-content-banner">
      <div class="banner-text">
        <h2>APP语音助手</h2>
        <p class="banner-text-content">
          让APP能听会说，带来智慧新体验支持免唤醒语音交互，提供丰富技能和海量内容
        </p>
      </div>
    </section>
    <section class="section section-1">
      <div class="section-title">
        <i class="arrow arrow-left"></i>
        <span>应用场景</span>
        <i class="arrow arrow-right"></i>
      </div>

      <div class="section-swiper advantage" v-swiper:swiper="swiperOption">
        <div class="swiper-wrapper">
          <div class="swiper-slide" key="1">
            <div class="advantage-image advantage-image-1"></div>
            <div class="advantage-text">
              <p>语音搜索</p>
              <p>想搜什么说什么，简单更高效</p>
              <ul>
                <li>功能直达，无障碍交互</li>
                <li>内容查找，一语中的</li>
              </ul>
            </div>
          </div>
          <div class="swiper-slide" key="2">
            <div class="advantage-image advantage-image-2"></div>
            <div class="advantage-text">
              <p>语音操控</p>
              <p>一语即控，让操控更轻松</p>
              <ul>
                <li>毫秒级响应</li>
                <li>多重校验，防止误触发</li>
              </ul>
            </div>
          </div>
          <div class="swiper-slide" key="3">
            <div class="advantage-image advantage-image-3"></div>
            <div class="advantage-text">
              <p>语音客服</p>
              <p>降低客服成本，提升咨询体验</p>
              <ul>
                <li>24H在线智能回复，问题解决率达85%</li>
                <li>支持多轮对话，招呼闲聊</li>
              </ul>
            </div>
          </div>
          <div class="swiper-slide" key="4">
            <div class="advantage-image advantage-image-4"></div>
            <div class="advantage-text">
              <p>语音录入</p>
              <p>即时语音、即时输入</p>
              <ul>
                <li>一分钟400字，准确率98%</li>
                <li>支持24种方言及35个语种</li>
              </ul>
            </div>
          </div>
          <div class="swiper-slide" key="5">
            <div class="advantage-image advantage-image-5"></div>
            <div class="advantage-text">
              <p>语音播报</p>
              <p>文字转语音，让APP更有温度</p>
              <ul>
                <li>100+发音人，男女老少，风格随心选</li>
                <li>消息主动播报，关怀弱势群体</li>
              </ul>
            </div>
          </div>
        </div>
        <div class="swiper-pagination"></div>
      </div>
    </section>
    <section class="section section-2">
      <div class="section-title">
        <i class="arrow arrow-left"></i>
        <span>方案亮点</span>
        <i class="arrow arrow-right"></i>
      </div>
      <ul class="advantage">
        <li>
          <div class="advantage-image"></div>
          <div class="advantage-text">
            <p>无需唤醒，直接交互</p>
            <p>
              直接说出语音指令，无需语音唤醒。基于用户意图甄别，有效防止误触发
            </p>
          </div>
        </li>
      </ul>
    </section>
    <section class="section section-3">
      <div class="section-title section-title-spec">
        <span>提供丰富技能和海量内容</span>
      </div>
      <p class="section-sub-title section-sub-title-spec">
        200+常用技能，涵盖生活、娱乐、游戏、办公、搜索导航、
        AIOT控制；内容资源覆盖1500万首曲库，10余个视频品牌，有声内容1200万+小时
      </p>
      <div class="section-content">
        <div class="content-container">
          <p class="content-title">200+常用技能</p>
          <ul class="li-wrap">
            <li class="">
              <i class="content-icon icon-clock"></i>
              <p>闹钟</p>
            </li>
            <li class="">
              <i class="content-icon icon-dictionary"></i>
              <p>成语词典</p>
            </li>
            <li class="">
              <i class="content-icon icon-qa"></i>
              <p>百科问答</p>
            </li>
            <li class="">
              <i class="content-icon icon-poetry"></i>
              <p>古诗词</p>
            </li>
            <li class="">
              <i class="content-icon icon-chat"></i>
              <p>闲聊</p>
            </li>
            <li class="">
              <i class="content-icon icon-more"></i>
              <p>更多内容</p>
            </li>
          </ul>
        </div>
        <div class="content-container">
          <p class="content-title">海量内容</p>
          <ul class="li-wrap">
            <li class="">
              <i class="content-icon icon-xmly"></i>
              <p>喜马拉雅听说</p>
            </li>
            <li class="">
              <i class="content-icon icon-kwyy"></i>
              <p>酷我音乐</p>
            </li>
            <li class="">
              <i class="content-icon icon-xlxw"></i>
              <p>新浪新闻</p>
            </li>
            <li class="">
              <i class="content-icon icon-aqy"></i>
              <p>爱奇艺</p>
            </li>
            <li class="">
              <i class="content-icon icon-bilibili"></i>
              <p>哔哩哔哩</p>
            </li>
            <li class="">
              <i class="content-icon icon-more"></i>
              <p>更多内容</p>
            </li>
          </ul>
        </div>
      </div>
    </section>
    <section class="btn-wrap">
      <div class="section-btn" @click="toConsole">合作咨询</div>
    </section>
  </div>
</template>

<script>
import '../../../../../../static/vue-awesome-swiper'

export default {
  // name: "",
  data() {
    return {
      activeName: '0',

      swiperOption: {
        loop: true,
        autoplay: {
          delay: 4000,
          // disableOnInteraction: false,
        },
        pagination: {
          el: '.swiper-pagination',
          clickable: true,
          renderBullet: function (index, className) {
            return '<span class="' + className + '">' + '</span>'
          },
        },
      },
    }
  },
  methods: {
    toConsole() {
      // 判断是否有百度SEM营销参数，有的话，提交表单页需要带上
      const search = window.location.search
      if (search) {
        window.open(`/solution/apply/9${search}`)
      } else {
        window.open('/solution/apply/9')
      }
    },
    handleClick() {
      this.swiper.slideToLoop(Number(this.activeName))
    },
  },
  mounted() {
    this.swiper.on('slideChange', () => {
      this.activeName = this.swiper.realIndex + ''
    })
  },
  // components: { Cells }
}
</script>

<style lang="scss" scoped>
.main-content {
  max-width: 750px;
  overflow: hidden;
  &-banner {
    background: url(~@A/images/solution/assistant/mobile/banner.png) center
      no-repeat;
    background-size: cover;
    height: 1052px;
    overflow: hidden;
    width: 100%;
  }
  .banner-text {
    padding-top: 774px;
    text-align: center;
    h2 {
      font-size: 58px;
      font-weight: 500;
      color: #ffffff;
      line-height: 88px;
    }
    p {
      width: 654px;
      font-size: 24px;
      font-weight: 400;
      color: rgba(255, 255, 255, 0.8);
      line-height: 34px;
      margin: 30px auto 0;
    }
  }

  .section {
    p,
    ul {
      margin-bottom: 0;
    }
    max-width: 750px;
    overflow: hidden;
    margin: 0 auto;
    .section-title {
      text-align: center;
      font-size: 36px;
      font-weight: 500;
      color: #444444;
      line-height: 54px;
      .arrow {
        width: 26px;
        height: 24px;
        display: inline-block;
        background-repeat: no-repeat;
        background-size: contain;
      }
      .arrow-left {
        background-position: left;
        background-image: url(~@A/images/solution/smart-hardware/arrow-left.png);
      }
      .arrow-right {
        background-position: right;
        background-image: url(~@A/images/solution/smart-hardware/arrow-right.png);
      }
    }
    .advantage {
      margin-top: 74px;
    }

    .section-title-spec {
      font-size: 32px;
      font-weight: 500;
      color: #656565;
      line-height: 44px;
    }
    .section-sub-title-spec {
      font-size: 24px;
      font-weight: 400;
      color: #999999;
      line-height: 34px;
      width: 670px;
      margin: 0 auto;
      margin-top: 24px;
    }
  }

  .section-1 {
    p {
      margin-bottom: 0;
    }
    margin-top: 160px;
    .advantage-image-1 {
      background: url(~@A/images/solution/assistant/sce_1.png) center no-repeat;
    }
    .advantage-image-2 {
      background: url(~@A/images/solution/assistant/sce_2.png) center no-repeat;
    }
    .advantage-image-3 {
      background: url(~@A/images/solution/assistant/sce_3.png) center no-repeat;
    }
    .advantage-image-4 {
      background: url(~@A/images/solution/assistant/sce_4.png) center no-repeat;
    }
    .advantage-image-5 {
      background: url(~@A/images/solution/assistant/sce_5.png) center no-repeat;
    }
    .advantage-text {
      margin: 114px auto 0;
      width: 373px;
      p:first-child {
        font-size: 34px;
        font-weight: 500;
        color: #444444;
        line-height: 34px;
      }
      p:nth-child(2) {
        margin-top: 20px;
        font-size: 24px;
        font-weight: 400;
        color: #777777;
        line-height: 24px;
      }
      ul {
        margin-top: 24px;
        li {
          font-size: 22px;
          font-weight: 400;
          color: #999999;
          line-height: 34px;
          white-space: nowrap;
          &::before {
            display: inline-block;
            content: ' ';
            width: 12px;
            height: 12px;
            border-radius: 100%;
            background: #3a91ff;
            margin-right: 10px;
          }
        }
        li + li {
          margin-top: 14px;
        }
      }
    }
    .advantage-image {
      margin: 0 auto;
      width: 373px;
      height: 352px;
      background-size: cover !important;
    }
  }

  .section-2 {
    p {
      margin-bottom: 0;
    }
    margin-top: 160px;
    .advantage {
      margin-top: 84px;
      li {
        // display: flex;
        // justify-content: center;
        text-align: center;
      }
      li:nth-child(1) {
        .advantage-image {
          background-image: url(~@A/images/solution/assistant/adv.png);
        }
        .advantage-text {
        }
      }
    }
    .advantage-text {
      width: 373px;
      margin: 0 auto;
      p:first-child {
        font-size: 32px;
        font-weight: 500;
        color: #656565;
        line-height: 44px;
      }
      p:last-child {
        font-size: 24px;
        font-weight: 400;
        color: #999999;
        line-height: 40px;
        margin-top: 24px;
      }
    }
    .advantage-image {
      margin: 0 auto;
      width: 398px;
      height: 313px;
      background-repeat: no-repeat;
      background-size: cover;
    }
  }

  .section-3 {
    margin-top: 160px;
    max-width: 750px;
    padding-bottom: 340px;
    .content-icon {
      display: inline-block;
      width: 115px;
      height: 115px;
      background-repeat: no-repeat;
      background-size: 100%;
    }
    .icon-xmly {
      background-image: url(~@A/images/solution/assistant/logos/s_1.png);
    }

    .icon-kwyy {
      background-image: url(~@A/images/solution/assistant/logos/s_2.png);
    }

    .icon-xlxw {
      background-image: url(~@A/images/solution/assistant/logos/s_3.png);
    }

    .icon-aqy {
      background-image: url(~@A/images/solution/assistant/logos/s_4.png);
    }

    .icon-bilibili {
      background-image: url(~@A/images/solution/assistant/logos/s_5.png);
    }

    .icon-clock {
      background-image: url(~@A/images/solution/assistant/logos/t_1.png);
    }

    .icon-dictionary {
      background-image: url(~@A/images/solution/assistant/logos/t_2.png);
    }

    .icon-qa {
      background-image: url(~@A/images/solution/assistant/logos/t_3.png);
    }

    .icon-poetry {
      background-image: url(~@A/images/solution/assistant/logos/t_4.png);
    }

    .icon-chat {
      background-image: url(~@A/images/solution/assistant/logos/t_5.png);
    }

    .btn-more-wrap {
      padding-top: 47px;
    }

    .btn-more {
      display: inline-block;
      width: 52px;
      height: 10px;
      background-image: url(~@A/images/solution/assistant/logos/btn_more.png);
    }

    .li-wrap {
      display: flex;
      justify-content: center;
      margin-bottom: 0;
      flex-wrap: wrap;
      li {
        text-align: center;
        p {
          margin-top: 15px;
          font-size: 18px;
          font-weight: 400;
          color: #444444;
          line-height: 25px;
        }
      }
    }

    .content-title {
      max-width: 750px;
      font-size: 28px;
      font-weight: 400;
      color: #444444;
      line-height: 40px;
      margin: 0 auto 58px;
      text-align: center;
    }

    .section-content {
      padding-top: 47px;
      // background: rgba(171, 212, 255, 0.08);
      // hr {
      //   width: 100%;
      //   height: 1px;
      //   background: #d1e8ff;
      // }
    }
  }

  .btn-wrap {
    position: fixed;
    bottom: 148px;
    width: 100%;
    z-index: 100;
    .section-btn {
      width: 622px;
      height: 100px;
      text-align: center;
      color: #fff;
      line-height: 100px;
      font-size: 34px;
      background: #348cff;
      margin: 0 auto;
    }
  }

  .content-container {
    max-width: 750px;
    overflow: hidden;
    padding-top: 61px;
    padding-bottom: 75px;
    // background: rgba(171, 212, 255, 0.08);
    .content-sub-title {
      text-align: center;
      margin: 0 auto 72px;
      width: 670px;
      font-size: 24px;
      font-weight: 400;
      color: #999999;
      line-height: 34px;
    }
    .content-icon {
      display: inline-block;
      width: 69px;
      height: 69px;
      background-repeat: no-repeat;
      background-size: contain;
    }

    .icon-xmly {
      background-image: url(~@A/images/solution/assistant/logos/s_1.png);
    }

    .icon-kwyy {
      background-image: url(~@A/images/solution/assistant/logos/s_2.png);
    }

    .icon-xlxw {
      background-image: url(~@A/images/solution/assistant/logos/s_3.png);
    }

    .icon-aqy {
      background-image: url(~@A/images/solution/assistant/logos/s_4.png);
    }

    .icon-bilibili {
      background-image: url(~@A/images/solution/assistant/logos/s_5.png);
    }

    .icon-clock {
      background-image: url(~@A/images/solution/assistant/logos/t_1.png);
    }

    .icon-dictionary {
      background-image: url(~@A/images/solution/assistant/logos/t_2.png);
    }

    .icon-qa {
      background-image: url(~@A/images/solution/assistant/logos/t_3.png);
    }

    .icon-poetry {
      background-image: url(~@A/images/solution/assistant/logos/t_4.png);
    }

    .icon-chat {
      background-image: url(~@A/images/solution/assistant/logos/t_5.png);
    }
    .icon-more {
      background-image: url(~@A/images/solution/assistant/logos/more.png);
    }

    .li-wrap {
      display: flex;
      flex-wrap: wrap;
      padding-top: 61px;
      padding-bottom: 69px;
      padding-left: 60px;
      padding-right: 60px;

      max-width: 750px;
      margin: 0 auto;
      > li {
        width: 124px;
        text-align: center;
        .content-icon {
          width: 80px;
          height: 80px;
          background-repeat: no-repeat;
          background-size: contain;
          margin: 0 auto;
        }
        p {
          font-size: 22px;
          font-weight: 400;
          color: #444444;
          line-height: 32px;
          margin-top: 26px;
        }
      }

      > li:not(:nth-child(3n-2)) {
        margin-left: 92px;
      }
      > li:nth-child(n + 4) {
        margin-top: 32px;
      }
    }
  }
}
</style>
<style lang="scss">
.section-1 {
  .swiper-slide {
    text-align: center;
  }
  .swiper-pagination {
    bottom: 210px !important;
  }
  .swiper-pagination-bullet {
    width: 16px;
    height: 8px;
    background: #4098ff;
    border-radius: 4px;
  }

  .swiper-pagination-bullet-active {
    width: 36px;
  }
}
</style>
