<template>
  <div class="upload-wrap">
    <el-upload
      v-if="!hasFile"
      class="upload-container"
      ref="fileUpload"
      :action="`/aiui/web/user/chat/file/upload`"
      :show-file-list="false"
      :before-upload="beforeUpload"
      :on-success="handleUploadSuccess"
      drag
    >
      <div class="upload-content">
        <div class="icon-add"></div>
        <p class="description1">
          将.txt文件(UTF-8格式，不超过500KB)拖拽至框内上传
        </p>
        <p class="description2">请您确保上传的内容符合法律法规，符合公共道德</p>
        <div class="upload-button">上传文件</div>
      </div>
    </el-upload>
    <div v-else style="width: 100%">
      <div class="hot-word-wrap">
        <p class="hot-word-title">{{ fileName }}</p>
        <p class="hot-word-time">
          {{ $utils.dateFormat(date, 'yyyy-MM-dd hh:mm:ss') }}
        </p>
        <div class="hot-word-config">
          <a class="mgr8" @click="downloadFile">下载</a>
          <a @click="deleteFile">清空</a>
        </div>
      </div>
      <el-upload
        class="upload-container"
        ref="fileUpload"
        :action="`/aiui/web/user/chat/file/upload`"
        :show-file-list="false"
        :before-upload="beforeUpload"
        :on-success="handleUploadSuccess"
      >
        <div class="">
          <div class="upload-button">重新上传</div>
        </div>
      </el-upload>
    </div>
  </div>
</template>
<script>
import { bus } from '@U/bus'
export default {
  data() {
    return {
      hasFile: false,
      fileName: '',
      date: '',
      loading: null,
    }
  },
  created() {
    this.getFile()
  },
  methods: {
    beforeUpload(file, fileList) {
      if (file.type != 'text/plain') {
        this.$message.warning('导入文件格式不对')
        return false
      }
      if (file.size > 500 * 1024) {
        this.$message.warning(`文件不得超过500KB`)
        return false
      }
      this.loading = this.$loading({
        lock: true,
        text: '文件上传中...',
        spinner: 'el-icon-loading',
        background: 'hsla(0,0%,100%,.9)',
      })
    },
    handleUploadSuccess(data) {
      console.log('handleUploadSuccess', data)
      if (data.flag) {
        // TODO: 获取文件信息
        this.$message.success('上传成功')
        this.getFile()
      } else {
        this.$message.warning(data.desc)
      }
      this.loading.close()
    },

    getFile() {
      this.$utils.httpGet(
        this.$config.api.CHAT_FILE_GET,
        {},
        {
          success: (res) => {
            console.log('----------get file--------', res)
            if (res.data.fileName) {
              this.hasFile = true
              bus.$emit('doc', '1')
              this.fileName = res.data.fileName
              this.date = res.data.date
            }
          },
          error: (err) => {
            console.log('page=>>')
            console.log(err)
            if (err && err.code === '300004') {
              window.location.href = '/solution/apply/24'
            }
            if (err && err.code === '300001') {
              window.location.href = '/user/login'
            }
          },
        }
      )
    },

    downloadFile() {
      let self = this
      if (!this.downloading) {
        this.downloading = true
        this.$utils.postopen(this.$config.api.CHAT_FILE_DOWNLOAD, {})

        setTimeout(function () {
          self.downloading = false
        }, 5000)
      } else {
        this.$message.warning('操作过快，请稍后再试')
      }
    },
    deleteFile() {
      let self = this
      let data = {}

      this.$utils.httpPost(this.$config.api.CHAT_FILE_DELETE, data, {
        success: (res) => {
          self.hasFile = false
          bus.$emit('doc', '0')
          self.fileName = ''
          self.date = undefined
        },
      })
    },
  },
}
</script>
<style scoped lang="scss">
p {
  margin-bottom: 0;
}

.upload-wrap {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  height: calc(100% - 80px);
  // height: calc(100% -77px);
  background: #f9fcff;
}

.upload-container {
  width: 100%;
  height: 100%;

  :deep(.el-upload) {
    height: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 100%;
      border: none;

      .upload-content {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
      }
    }
  }
}

.icon-add {
  width: 61px;
  height: 61px;
  margin: 0 auto;
  background: url(~@A/images/model-exeperience/<EMAIL>) center/cover
    no-repeat;
}

.upload-button {
  cursor: pointer;
  width: 120px;
  height: 36px;
  text-align: center;
  line-height: 36px;
  font-size: 14px;
  font-weight: 700;
  color: #fff;
  margin: 10px auto 0;
  background: $primary;
  border-radius: 4px;
}

.description1 {
  font-size: 14px;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: center;
  color: #011e44;
  margin-top: 30px;
}

.description2 {
  margin: 10px 0;
  font-size: 14px;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: center;
  color: #a0a0a0;
}

.hot-word-wrap {
  position: relative;
  height: 80px;
  border-radius: 2px;
  padding: 18px;
  border: 1px dashed $grey4;
  // margin-bottom: 24px;
  margin: 0 24px auto;
  width: 90%;
}

.hot-word-title {
  font-size: 16px;
  font-weight: 500;
  color: $semi-black;
}

.hot-word-time {
  color: $grey5;
  font-size: 12px;
}

.hot-word-config {
  position: absolute;
  top: 0;
  right: 32px;
  line-height: 80px;
}

:deep(.el-upload-dragger) {
  background-color: unset;
}
</style>
