<template>
  <div class="rightChatboxContainer">
    <div class="box">
      <div class="title">
        <span></span>
        <span></span>
        <span></span>
      </div>
      <div class="content">
        <div class="rightChatbox" ref="rightChatbox">
          <div
            v-for="(item, index) in chat"
            :key="index"
            :class="item.people === 'me' ? 'me' : 'ai'"
            class="chatbox"
          >
            <div class="con" style="position: relative">
              <span v-html="item.con"></span>
              <div class="icon-me" v-if="item.people === 'me'"></div>
              <div class="icon-ai" v-if="item.people === 'ai'"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    chat: Array,
  },
}
</script>
<style lang="scss" scoped>
.rightChatboxContainer {
  width: 100%;
  height: calc(100% - 97px);
  position: relative;
  overflow: hidden;
}

.box {
  width: 100%;
  height: 100%;
  overflow: hidden;
  padding: 20px 30px;
  box-sizing: border-box;

  .title {
    width: 100%;
    height: 24px;
    background: rgb(198, 215, 253);
    padding: 0 12px;

    span {
      display: inline-block;
      width: 6px;
      height: 6px;
      background: #fff;
      border-radius: 50%;
      margin: 0 4px;
    }
  }
}

.content {
  width: 100%;
  height: 100%;
  overflow: auto;
}

.rightChatbox {
  width: 100%;
  // height: 100%;
  overflow: auto;
  box-sizing: border-box;
  // padding-top: 57px;
  // padding-bottom: 150px;
  padding: 12px 0 30px 0;
  background: rgb(232, 240, 254);

  &::-webkit-scrollbar-track {
    /* -webkit-box-shadow: inset006pxrgba(0, 0, 0, 0.5); */
    background-color: rgba(255, 255, 255, 0.3);
  }

  &::-webkit-scrollbar-thumb {
    background: #bacff2;
    /* -webkit-box-shadow: inset006pxrgba(0, 0, 0, 0.5); */
  }
}

.chatbox {
  margin-bottom: 16px;
  padding: 0 20px;
}

.me {
  display: flex;
  justify-content: flex-start;
  flex-direction: row-reverse;

  .icon-me {
    width: 34px;
    height: 34px;
    position: absolute;
    z-index: 1;
    top: 4px;
    right: 8px;
    background: url(~@A/images/model-exeperience/icon-me2.png) center/contain
      no-repeat;
  }

  .con {
    display: flex;
    align-items: center;
    max-width: 90%;
    min-height: 46px;
    padding: 10px 16px;
    min-width: 100px;
    padding-right: 76px;
    font-size: 14px;
    line-height: 20px;
    word-break: break-word;
    // background: linear-gradient(-90deg, #1670df 0%, #27cbff 100%);
    // border-radius: 20px 0px 20px 20px;
    background: #cadefe;
    border: 1px solid rgba(255, 255, 255, 0.66);
    border-radius: 20px;
    box-shadow: NaNpx NaNpx 2.91px NaNpx rgba(217, 231, 253, 0.61);
    color: #000;
  }
}

.ai {
  .icon-ai {
    width: 34px;
    height: 34px;
    position: absolute;
    z-index: 1;
    top: 4px;
    left: 8px;
    background: url(~@A/images/model-exeperience/icon-ai.png) center/contain
      no-repeat;
  }

  .con {
    box-sizing: border-box;
    width: fit-content;
    max-width: 90%;
    min-width: 100px;
    min-height: 46px;
    padding: 10px 16px 10px 52px;
    line-height: 20px;
    word-break: break-all;

    font-size: 14px;
    font-family: PingFang SC, PingFang SC-Regular;
    font-weight: 400;
    text-align: left;
    color: #262626;
    background: rgb(246, 249, 255);
    border: 1px solid #ffffff;
    border-radius: 20px;
  }
}

@media screen and (max-height: 800px) {
  .box {
    padding: 15px;
  }

  .rightChatboxContainer {
    height: calc(100% - 70px);
  }
}
</style>
