.gutter-table-style {
  border: none;
  .el-table__header {
    thead {
      th {
        background: #fff;
        border-bottom: none;
      }
    }
  }
  .el-table__body {
    border-collapse: collapse;
    tbody {
      // tr {
      //   background: #f5f7fa;
      // }
      // tr:not(:first-child) {
      //   border-top: 20px solid transparent;
      // }
      td {
        // border: none;
        padding: 0;
        .cell {
          // height: 66px;
          // line-height: 66px;
          height: 60px;
          line-height: 60px;
        }
      }
      tr > td:first-child {
        .cell {
          // border-left: 1px solid #f5f7fa !important;
          border-top-left-radius: 4px;
          border-bottom-left-radius: 4px;
        }
      }
      tr > td:last-child {
        .cell {
          // border-right: 1px solid #f5f7fa !important;
          border-top-right-radius: 4px;
          border-bottom-right-radius: 4px;
        }
      }
      // tr:hover > td {
      //   background-color: #edf4ff;
      //   .cell {
      //     border-top: 1px solid #498dff !important;
      //     border-bottom: 1px solid #498dff !important;
      //   }
      // }

      // tr:hover > td:first-child {
      //   .cell {
      //     border-left: 1px solid #498dff !important;
      //     border-top-left-radius: 4px;
      //     border-bottom-left-radius: 4px;
      //   }
      // }
      // tr:hover > td:last-child {
      //   .cell {
      //     border-right: 1px solid #498dff !important;
      //     border-top-right-radius: 4px;
      //     border-bottom-right-radius: 4px;
      //   }
      // }
    }
  }
}

.transparent-bgc {
  .el-table tr,
  .el-table__body-wrapper,
  .el-table__header thead th {
    background: $white-grey;
  }
}

.transparent-bgc-role {
  .el-table tr,
  .el-table__body-wrapper,
  .el-table__header thead th {
    background: #f7f8fa;
  }
  .el-table__header thead th {
    border-bottom: 1px solid #f7f8fa;
  }
}

.secondary-table {
  .el-table__header-wrapper {
    // border-radius: 4px 4px 0px 0px;
    .el-table__header thead th {
      background: #eff3f9;
      .cell {
        color: #555454 !important;
      }
      &:not(:last-child) {
        border-right: 1px solid #d8e0ed;
      }
    }
  }
  .el-table__body-wrapper {
    border-left: 1px solid #d8e0ed;
    border-right: 1px solid #d8e0ed;
    // border-radius: 0px 0px 4px 4px;
    // transform: translateY(-1px);
  }
}

.secondary-table-thead {
  .el-table__header-wrapper {
    .el-table__header thead th {
      background: #eff3f9;
      .cell {
        color: #555454 !important;
      }
      &:not(:last-child) {
        border-right: 1px solid #d8e0ed;
      }
    }
  }
}
