<template>
  <div class="main-content">
    <section class="main-content-banner">
      <div class="banner-text">
        <h2>免唤醒语音交互解决方案</h2>
        <p class="banner-text-content">
          无需唤醒设备即可语音点播内容、控制设备交互，<br />
          为产品打造自然流畅的语音交互体验。
        </p>
        <div class="banner-text-button" @click="toConsole">合作咨询</div>
      </div>
    </section>
    <section class="section section-1">
      <div class="section-title">
        <i class="arrow arrow-left"></i
        ><span class="section-title-bold">适用产品</span
        ><i class="arrow arrow-right"></i>
      </div>
      <div class="section-desc" style="text-align: center">
        适用于各类生活智能硬件
      </div>
      <ul class="product-list">
        <li
          v-for="(item, index) in productList"
          :key="index"
          :class="item.klass"
        >
          <!-- <h1>{{ item.name }}</h1> -->
          <div class="desc-wrap">
            <h2>{{ item.name }}</h2>
            <p>
              {{ item.desc }}
            </p>
          </div>
          <!-- <div class="overlay"></div> -->
        </li>
      </ul>
    </section>

    <section class="section section-3">
      <div>
        <div class="section-title">
          <i class="arrow arrow-left"></i
          ><span class="section-title-bold">方案亮点</span
          ><i class="arrow arrow-right"></i>
        </div>
        <ul class="advantage">
          <li>
            <div class="advantage-text">
              <p>离线+离在线结合</p>
              <ul>
                <li>可以纯离线，没有网络也可以使用；</li>
                <li>300ms响应，打造产品操控体验。</li>
              </ul>
            </div>
            <div class="advantage-image"></div>
          </li>
          <li>
            <div class="advantage-image"></div>
            <div class="advantage-text">
              <p>说法支持自由定制</p>
              <ul>
                <li>根据产品需要自定义说法；</li>
                <li>满足产品在不同情境下的个性化需求。</li>
              </ul>
            </div>
          </li>
          <li>
            <div class="advantage-text">
              <p>设备控制直接说</p>
              <ul>
                <li>开机关机、屏幕亮度、声音大小；</li>
                <li>无需唤醒设备，直接语音交互；</li>
                <li>支持无网使用。</li>
              </ul>
            </div>
            <div class="advantage-image"></div>
          </li>
          <li>
            <div class="advantage-image"></div>
            <div class="advantage-text">
              <p>播放控制直接说</p>
              <ul>
                <li>可以直接语音控制播放器，支持无网使用；</li>
                <li>如：快进快退、跳过片头、换一首、上一首、下一首。</li>
              </ul>
            </div>
          </li>
          <li>
            <div class="advantage-text">
              <p>界面交互直接说</p>
              <ul>
                <li>页面导航所见即可直接说；</li>
                <li>语音模拟点击；</li>
                <li>跳转至任意层级页面。</li>
              </ul>
            </div>
            <div class="advantage-image"></div>
          </li>
          <li>
            <div class="advantage-image"></div>
            <div class="advantage-text">
              <p>内容点播直接说</p>
              <ul>
                <li>电影动画、音乐儿歌、戏曲小说；</li>
                <li>无需唤醒，可以直接语音点播。</li>
              </ul>
            </div>
          </li>
        </ul>
      </div>
    </section>

    <section class="section section-7">
      <div class="section-title">
        <i class="arrow arrow-left1"></i
        ><span class="section-title-bold" style="color: #fff">接入方式</span
        ><i class="arrow arrow-right1"></i>
      </div>
      <div class="section-item">
        <ul>
          <li v-for="(item, index) in access" :key="index">
            <div class="title-desc">
              <h1 v-html="item.title"></h1>
              <h2 v-html="item.title2"></h2>
            </div>

            <img class="section-4-bg" :src="item.img" :alt="item.title" />
          </li>
        </ul>
      </div>
    </section>
    <corp @jump="toConsole">
      <template>
        免唤醒语音交互方案<br />
        帮你打造自然的语音交互产品</template
      >
    </corp>
    <!-- <section class="section section-5">
      <div class="section-title">
        <p class="section-title-bold">合作咨询</p>
        <p class="section-desc">
          免唤醒语音交互方案<br />
          帮你打造自然的语音交互产品
        </p>
      </div>

      <div class="section-item">
        <aiui-button hasTop @click.native="toConsole">申请合作</aiui-button>
      </div>
    </section> -->
  </div>
</template>

<script>
import corp from '@P/aiui/solution-aiui/components/corp.vue'

export default {
  layout: 'aiuiHome',
  data() {
    return {
      productList: [
        {
          name: '家用机器人',
          desc: '',
          klass: 'img_full_duplex',
        },
        {
          name: '投影仪',
          desc: '',
          klass: 'img_free_wake_click',
        },
        {
          name: '护眼仪',
          desc: '',
          klass: 'img_multimodal_click',
        },
        {
          name: '按摩椅',
          desc: '',
          klass: 'img_offlineinteraction_click',
        },
      ],

      access: [
        {
          title: '纯软接入、无需改造硬件',
          title2:
            '提供SDK,可以直接端集成；说法量身定制，端<br />上适配即可使用',
          img: require('@A/images/solution/wakeup/img_pure_soft.png'),
        },
        {
          title: '麦克风阵列+声学算法',
          title2:
            '针对没有录音装置的设备；提供2/4/6麦USB<br />语音模组和CAE算法；帮你解决硬件收音问题',
          img: require('@A/images/solution/wakeup/img_microphone_array.png'),
        },
      ],
    }
  },
  methods: {
    toConsole() {
      // 判断是否有百度SEM营销参数，有的话，提交表单页需要带上
      const search = window.location.search
      if (search) {
        window.open(`/solution/apply/6${search}`)
      } else {
        window.open('/solution/apply/6')
      }
    },
  },
  components: { corp },
}
</script>

<style lang="scss" scoped>
// @import '../../../assets/scss/screen-and-lamp.scss';
.main-content {
  &-banner {
    background: url(~@A/images/solution/wakeup/img_free_wake.png) center
      no-repeat;
    background-size: cover;
    height: 500px;
    overflow: hidden;
    width: 100%;
    .banner-text {
      max-width: 1200px;
      color: #fff;
      height: 100%;
      margin: auto;
      &-button {
        font-size: 16px;
        text-align: center;
        font-weight: 400;
        width: 140px;
        height: 40px;
        line-height: 40px;
        border: 1px solid #fff;
        border-radius: 40px;
        color: #fff;
        cursor: pointer;
        transition: 0.6s;
      }
      h2 {
        color: #fff;
        padding-top: 148px;
        margin-bottom: 29px;
        font-size: 48px;
        font-weight: 500;
        line-height: 48px;
      }
      p {
        font-size: 18px;
        margin-bottom: 74px;
      }

      .banner-text-content {
        width: 570px;
        font-size: 16px;
        font-family: SourceHanSansSC-Regular, SourceHanSansSC;
        font-weight: 400;
        color: rgba(255, 255, 255, 0.86);
        line-height: 30px;
      }
    }
  }
  .section {
    p,
    ul {
      margin-bottom: 0;
    }
    max-width: 1200px;
    overflow: hidden;
    margin: 0 auto;
    .section-title {
      text-align: center;
      font-size: 34px;
      font-family: SourceHanSansSC-Medium, SourceHanSansSC;
      font-weight: bold;
      color: #333;
      line-height: 34px;
      position: relative;
      width: 210px;
      margin: 0 auto;
      .arrow {
        width: 160px;
        height: 8px;
        display: inline-block;
        background-repeat: no-repeat;
        background-size: contain;
        position: absolute;
        z-index: 1;
      }
      .arrow-left {
        background-position: left;
        background-image: url(~@A/images/solution/offline/img_title_01.png);
        top: 50%;
        left: -160px;
      }
      .arrow-right {
        background-position: right;
        background-image: url(~@A/images/solution/offline/img_title_02.png);
        top: 50%;
        right: -160px;
      }
      .arrow-left1 {
        background-position: left;
        background-image: url(~@A/images/solution/wakeup/img_access_title_01.png);
        top: 50%;
        left: -160px;
      }
      .arrow-right1 {
        background-position: right;
        background-image: url(~@A/images/solution/wakeup/img_access_title_02.png);
        top: 50%;
        right: -160px;
      }
    }
    .section-desc {
      text-align: left;
      margin-top: 40px;
      font-size: 16px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #999999;
    }
    .section-title-bold {
      font-size: 34px;
      font-family: Microsoft YaHei;
      font-weight: bold;
      color: #333333;
    }
    .section-title-spec {
      font-size: 30px;
      font-weight: 500;
      color: #656565;
      line-height: 42px;
    }
    .section-tabs {
      margin-top: 70px;
      ul {
        display: flex;
        justify-content: center;
      }
      li {
        font-size: 20px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #666;
        line-height: 30px;
        position: relative;
        cursor: pointer;
        &:hover {
          color: #1f90fe;
        }
        &.active {
          color: #1f90fe;
          &::before {
            position: absolute;
            content: ' ';
            z-index: 1;
            width: 180px;
            height: 4px;
            background: #1f90fe;
            border-radius: 2px;
            bottom: -32px;
            left: -48px;
          }
          &::after {
            position: absolute;
            content: ' ';
            display: inline-block;
            width: 0;
            height: 0;
            bottom: -100px;
            left: 50%;
            transform: translateX(-50%);
            border: 46px solid;
            border-color: transparent transparent #f4f7f9;
          }
        }
      }
      li + li {
        margin-left: 165px;
      }
    }
    .section-sub-title {
      text-align: center;
      font-size: 18px;
      font-weight: 400;
      color: #777777;
      line-height: 25px;
      margin-top: 42px;
    }
    .section-sub-title-spec {
      font-size: 17px;
      font-weight: 400;
      color: #999999;
      line-height: 28px;
      margin-top: 18px;
    }

    .product-list {
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 50px auto 0;

      li {
        position: relative;
        text-align: center;
        cursor: pointer;
        width: 287px;
        height: 410px;

        .desc-wrap {
          padding-top: 50px;
        }
        .overlay {
          display: none;
          width: 100%;
          height: 100%;
          // background: rgba(0, 0, 0, 0.3);
          background-image: linear-gradient(
            0deg,
            rgb(0, 54, 255) 0%,
            rgb(39, 12, 73) 100%
          );
          opacity: 0.502;
          position: absolute;
          top: 0;
          left: 0;
          z-index: 1;
        }

        h1 {
          display: none;
          text-align: left;
          max-width: 25px;
          font-size: 24px;
          font-family: Microsoft YaHei;
          font-weight: 400;
          color: #ffffff;
          line-height: 30px;
          margin: 0 auto;
          position: relative;
          top: 50%;
          transform: translateY(-50%);
        }
        h2 {
          // display: none;
          text-align: center;
          // padding-top: 178px;
          // padding-left: 35px;
          font-size: 26px;
          // font-weight: bold;
          color: #ffffff;
          line-height: 40px;
        }
        p {
          // display: none;
          margin-top: 32px;
          width: 232px;
          font-size: 16px;
          font-weight: 400;
          color: #ffffff;
          line-height: 32px;
          padding-left: 35px;
          text-align: left;
        }

        &.img_full_duplex {
          background: url(~@A/images/solution/wakeup/img_domestic_robot.png)
            center/100% no-repeat;
        }
        &.img_free_wake_click {
          background: url(~@A/images/solution/wakeup/img_projector.png)
            center/100% no-repeat;
        }
        &.img_multimodal_click {
          background: url(~@A/images/solution/wakeup/img_instrument_shield_eye.png)
            center/100% no-repeat;
        }
        &.img_offlineinteraction_click {
          background: url(~@A/images/solution/wakeup/img_massage_armchair.png)
            center/100% no-repeat;
        }
      }

      li + li {
        margin-left: 16px;
      }
    }
  }

  .section-1 {
    margin-top: 110px;
  }
  .section-2 {
    .section-title-2 {
      font-size: 18px;
      color: #8c8c8c;
      text-align: center;
      margin-top: 15px;
    }
    .section-item {
      > ul {
        margin-bottom: 80px;
        li {
          width: 150px;
          img {
            width: 100%;
          }
          .app-text {
            position: static;
            text-align: center;
            font-size: 18px;
            color: #202020;
            margin-top: 15px;
            transform: none;
          }
        }
      }
    }
  }
  .section-3 {
    background: #f4f7f9;
    max-width: 3000px;
    padding: 110px 0 110px 0;
    > div {
      max-width: 1100px;
      margin: 0 auto;
    }

    p {
      margin-bottom: 0;
    }
    margin-top: 100px;
    .advantage {
      margin-top: 84px;
      > li {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      > li:not(:first-child) {
        margin-top: 65px;
      }

      > li:nth-child(1) {
        .advantage-image {
          width: 453px;
          height: 267px;
          background: url(~@A/images/solution/wakeup/img_offline_combination.png)
            center/100% no-repeat;
        }
      }
      > li:nth-child(2) {
        .advantage-image {
          width: 454px;
          height: 271px;
          background: url(~@A/images/solution/wakeup/img_free_custom.png)
            center/100% no-repeat;
        }
      }
      > li:nth-child(3) {
        .advantage-image {
          width: 454px;
          height: 302px;
          background: url(~@A/images/solution/wakeup/img_equipment_control.png)
            center/100% no-repeat;
        }
      }
      > li:nth-child(4) {
        .advantage-image {
          width: 454px;
          height: 309px;
          background: url(~@A/images/solution/wakeup/img_play_control.png)
            center/100% no-repeat;
        }
      }
      > li:nth-child(5) {
        .advantage-image {
          width: 454px;
          height: 297px;
          background: url(~@A/images/solution/wakeup/img_interface_interaction.png)
            center/100% no-repeat;
        }
      }
      > li:nth-child(6) {
        .advantage-image {
          width: 454px;
          height: 249px;
          background: url(~@A/images/solution/wakeup/img_content_demand.png)
            center/100% no-repeat;
        }
      }
    }
    .advantage-text {
      width: 411px;
      p {
        font-size: 34px;
        font-weight: 400;
        color: #666;
        line-height: 34px;
      }

      ul {
        margin-top: 45px;
        li {
          font-size: 16px;
          font-weight: 400;
          color: #999999;
          line-height: 30px;
          white-space: nowrap;
        }
      }
    }
  }
  .section-5 {
    margin: 50px auto 70px;
  }
  .section-7 {
    width: 100%;
    max-width: 100%;
    // background: #262626;
    background: url(~@A/images/solution/wakeup/img_hardware_access_bg.png)
      center/cover no-repeat;
    padding: 110px 0 100px 0;
    .section-title {
      color: #fff;
    }
    .section-item {
      margin-top: 70px;
      .title-desc {
        position: absolute;
        z-index: 1;
        left: 128px;
        top: 40px;
        h1,
        h2 {
          color: #fff;
          text-align: left;
        }
        h1 {
          font-size: 30px;
        }
        h2 {
          font-size: 16px;
          margin-top: 26px;
        }
      }

      > ul {
        display: flex;
        align-items: center;
        justify-content: center;
        li {
          position: relative;
        }
        li + li {
          margin-left: 20px;
          width: 590px;
          height: 390px;
        }
      }
    }
  }
}
</style>
