<template>
  <el-dialog
    title="编辑数组参数"
    :visible.sync="visible"
    width="60%"
    @close="handleClose"
    :close-on-click-modal="false"
  >
    <el-form :model="form" label-position="left">
      <el-row :gutter="20" style="margin-bottom: 10px">
        <el-col :span="8">参数名称</el-col>
        <el-col :span="6">参数类型</el-col>
        <el-col :span="5">默认值</el-col>
        <el-col :span="2">操作</el-col>
        <el-col :span="3">
          <i class="el-icon-circle-plus" @click="addItem"></i>
        </el-col>
      </el-row>
      <el-form-item
        v-for="(item, index) in form.children"
        :key="item.id"
        :prop="'children.' + index + '.defaultValue'"
      >
        <el-row :gutter="20">
          <el-col :span="8">
            <el-input v-model="item.name" disabled />
          </el-col>
          <el-col :span="6">
            <el-input v-model="item.type" disabled />
          </el-col>
          <el-col :span="5">
            <el-input v-model="item.defaultValue" placeholder="请输入默认值" />
          </el-col>
          <el-col :span="2">
            <i class="el-icon-remove" type="text" @click="removeItem(index)" />
          </el-col>
        </el-row>
      </el-form-item>
    </el-form>

    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取 消</el-button>
      <el-button type="primary" @click="handleSubmit">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { v4 as uuidv4 } from 'uuid'

export default {
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    arrayData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      visible: this.value,
      form: {
        children: [],
      },
    }
  },
  watch: {
    value(val) {
      this.visible = val
    },
    visible(val) {
      this.$emit('input', val)
    },
    arrayData: {
      immediate: true,
      handler(val) {
        if (val && val.children) {
          this.form = JSON.parse(JSON.stringify(val))
        }
      },
    },
  },
  methods: {
    addItem() {
      const newItem = {
        id: uuidv4(),
        name: '[Array Item]',
        type: this.form.children[0]?.type || 'string',
        defaultValue: '',
      }
      this.form.children.push(newItem)
    },
    removeItem(index) {
      this.form.children.splice(index, 1)
    },
    handleSubmit() {
      this.$emit('submit', this.form)
      this.visible = false
    },
    handleClose() {
      this.$emit('close')
    },
  },
}
</script>

<style lang="scss" scoped></style>
