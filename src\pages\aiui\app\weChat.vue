<template>
  <div>
    <div class="mgb24">
      <div class="auth-item" v-for="(item, key) in authList">
        <img class="icon-wx" v-if="item.imgUrl" :src="item.imgUrl" />
        <span class="icon-wx" v-else style="display: inline-block"></span>
        <div class="wx-info ib">
          <p class="wx-title">{{ item.name }}</p>
          <div class="wx-location">
            <span>位置授权</span>
            <el-tooltip placement="right">
              <p slot="content">
                使用位置，您可以获得更智能的语义理解服务。<br />
                比如问：今天有雨吗？AIUI
                回答会分析您的位置给出更符合用户需要的答案。
              </p>
              <i class="el-icon-question" />
            </el-tooltip>

            <template v-if="location[item.wxId] == 1">
              <span class="text-success">已授权</span>
              <a @click="closeLocation(item, index)">关闭授权</a>
            </template>
            <a v-else @click="configLocation(item, index, 1)">同意授权</a>
          </div>
        </div>
      </div>
      <el-button
        v-if="authList.length < 2"
        size="small"
        style="vertical-align: top"
        @click="doAuthorize"
        >授权绑定微信公众号</el-button
      >
    </div>
    <p class="mgb8" style="line-height: normal">
      绑定微信公众号，让你的公众号快速使用AIUI语音交互。
    </p>
    <p class="wechat-desc">
      注意：1. 使用 AIUI 服务时，请关闭微信自动回复功能。<br />
      2. 一个 APPID 最多绑定两个公众号。
    </p>
  </div>
</template>

<script>
export default {
  name: 'weChat',
  props: {
    appId: '',
    appInfo: Object,
  },
  data() {
    return {
      contentShow: true,
      authList: [],
      location: {},
      backTypeList: ['返回文本'],
    }
  },
  methods: {
    getWeChatList() {
      let self = this
      this.$utils.httpGet(
        this.$config.api.AIUI_APP_WX_LIST,
        {
          appid: this.appId,
        },
        {
          success: (res) => {
            if (res.flag) {
              self.authList = res.data || []

              if (res.data && res.data.length) {
                self.getWeChatLocation()
              }
            }
          },
        }
      )
    },
    getWeChatConfig() {
      this.$utils.httpGet(
        this.$config.api.AIUI_APP_WX_CONFIG,
        {
          appid: this.appId,
        },
        {
          success: (res) => {
            if (res.flag) {
            }
          },
        }
      )
    },
    getWeChatLocation() {
      let self = this
      this.$utils.httpGet(
        this.$config.api.AIUI_APP_WX_LOCATION,
        {
          appid: this.appId,
        },
        {
          success: (res) => {
            if (res.flag) {
              self.location = res.data
            }
          },
        }
      )
    },
    doAuthorize() {
      window.open(
        `http://weixin.xfyun.cn/aiuiwx/auth/index?aiuiAppid=${this.appId}`,
        '_blank'
      )
      let self = this
      self.$alert('是否完成公众号授权？', '公众号授权', {
        confirmButtonText: '已完成',
        type: 'info',
        callback: (action) => {
          self.getWeChatList()
        },
      })
    },
    closeLocation(item, index) {
      let self = this
      self
        .$confirm(
          '你确定要关闭微信公众号位置授权吗？关闭后无法使用微信位置信息，需要位置的技能可能无法正常使用，比如：天气。',
          '关闭公众号',
          {
            confirmButtonText: '仍然关闭',
            cancelButtonText: '取消 不关闭',
            type: 'warning',
          }
        )
        .then(() => {
          self.configLocation(item, index, 0)
        })
        .catch(() => {})
    },
    configLocation(item, index, option) {
      let self = this
      let data = {
        appid: this.appId,
        wxId: item.wxId,
        option: option,
      }
      this.$utils.httpPost(this.$config.api.AIUI_APP_WX_SAVE_LOCATION, data, {
        success: (res) => {
          if (res.flag) {
            self.location[item.wxId] = option
          } else {
            self.$message.error(res.desc)
          }
        },
      })
    },
  },
  created() {
    this.getWeChatList()
  },
}
</script>

<style lang="scss" scoped>
.wechat-desc {
  padding: 9px 16px 7px;
  width: 400px;
  line-height: 1.57;
  background-color: $warning-light-12;
}
.auth-item {
  display: inline-block;
  width: 320px;
  height: 104px;
  border-radius: 2px;
  border: 1px solid $grey2;
  margin-bottom: 10px;
  padding: 16px;
  &:first-child {
    margin-right: 16px;
  }
}
.icon-wx {
  width: 72px;
  height: 72px;
  background: $grey2;
  border-radius: 2px;
}
.wx-info {
  margin-left: 20px;
  vertical-align: top;
  line-height: normal;
}
.wx-title {
  font-size: 16px;
  color: $grey6;
  font-weight: 500;
  margin: 8px 0;
}
.params-item-title {
  font-size: 16px;
  font-weight: 500;
  margin: 20px 0;
}
.el-icon-question {
  font-size: 20px;
  color: $grey4;
  margin-left: 10px;
}
.wx-location .el-icon-question {
  margin-left: 5px;
  font-size: 16px;
}
.text-success {
  color: $success;
}
</style>
