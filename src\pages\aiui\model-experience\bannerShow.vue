<template>
  <div class="card card-top">
    <div class="title">
      <i class="icon-feature"></i><span class="exmaple-title">交互特性</span>
    </div>
    <div class="body">
      <div
        class="bannerSwiperContainer"
        v-swiper:bannerSwiper="bannerSwiperOption"
        style="height: 100%"
      >
        <div class="swiper-wrapper">
          <div
            class="swiper-slide"
            v-for="(item, index) in banner"
            :key="index"
          >
            <div class="unit">
              <div
                class="decoration decoration1"
                :style="{ backgroundImage: 'url(' + item.bg + ')' }"
              ></div>
              <div class="description">
                {{ item.description }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- <div class="pagination">pagination</div> -->
    <div
      class="banner-pagination swiper-pagination-clickable swiper-pagination-bullets"
    >
      <span
        v-for="(item, index) in banner"
        :key="index"
        @click="toggleBanner(index)"
        :class="[
          'swiper-pagination-bullet',
          {
            'swiper-pagination-bullet-active': activeBannerIndex === index,
          },
        ]"
        :tabindex="index"
        role="button"
      ></span>
    </div>
  </div>
</template>
<script>
import '@static/vue-awesome-swiper'

import decoration1 from '@A/images/model-exeperience/bg1.png'
import decoration2 from '@A/images/model-exeperience/bg2.png'
import decoration3 from '@A/images/model-exeperience/bg3.png'
import decoration4 from '@A/images/model-exeperience/bg4.png'
import decoration5 from '@A/images/model-exeperience/bg5.png'

export default {
  data() {
    return {
      bannerSwiperOption: {
        loop: true,
        autoplay: {
          delay: 4000,
          // disableOnInteraction: false,
        },
        pagination: {
          el: '.card-top .banner-pagination',
          clickable: true,
        },
      },
      banner: [
        {
          description: '贯穿技能、问答、闲聊的上下文多轮穿插式对话',
          bg: decoration1,
        },
        {
          description:
            '针对百科、景点、美食、影视等领域，可进行多样化的深入回复',
          bg: decoration2,
        },
        {
          description: '具备基础的内容生成能力',
          bg: decoration3,
        },
        { description: '具备基础的推理问答能力', bg: decoration4 },
        // {
        //   description: '支持用户在部分对话场景纠偏纠错',
        //   bg: decoration5,
        // },
      ],
      activeBannerIndex: 0,
    }
  },
  mounted() {
    this.bannerSwiper.on('slideChange', () => {
      this.$nextTick(() => {
        this.activeBannerIndex = this.bannerSwiper.realIndex
      })
    })
  },
  methods: {
    toggleBanner(index) {
      this.bannerSwiper.slideToLoop(index)
    },
  },
}
</script>
<style scoped lang="scss">
.card {
  border-radius: 8px;
  box-shadow: 0px 0px 23.75px 0px #cbd5fb;
  position: relative;
}
.card-top {
  height: calc(50% - 10px);
}

.title {
  display: flex;
  align-items: center;
  padding: 10px;
}

.icon-feature {
  width: 14px;
  height: 15px;
  display: inline-block;
  background: url(~@A/images/model-exeperience/<EMAIL>) center/contain
    no-repeat;
  margin-right: 2px;
}

.exmaple-title {
  font-size: 14px;
  font-weight: bold;
}

.pagination {
  height: 40px;
}
.body {
  height: calc(100% - 40px);
}

.unit {
  width: 100%;
  height: 100%;
  background: linear-gradient(0deg, #edefff 0%, #dff4ff 100%);
  border-radius: 4px;
  box-shadow: 0px 0px 23.75px 0px #cbd5fb;
}

.decoration {
  width: 314px;
  height: 194px;
  position: absolute;
  z-index: 1;
  top: 30px;
  left: 50%;
  transform: translateX(-50%);
}
.decoration1 {
  background: url(~@A/images/model-exeperience/bg1.png) center/cover no-repeat;
}

.description {
  width: 80%;
  height: 48px;
  opacity: 0.6;
  background: #ffffff;
  border-radius: 4px;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  z-index: 2;
  bottom: 20px;

  font-size: 12px;
  font-weight: 400;
  text-align: center;
  color: #011e44;
  line-height: 48px;
}

.banner-pagination {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  top: 10px;
  z-index: 10;
  width: 260px;
  :deep(.swiper-pagination-bullet) {
    width: 44px;
    height: 4px;
    border-radius: 0px;
    background: #197fe4;
    margin: 0 4px;
    outline: none;
  }
}

@media screen and (max-width: 1601px) {
  .decoration {
    top: 20px;
    width: 240px;
    height: 150px;
  }
  .description {
    height: 36px;
    line-height: 36px;
  }

  .card-top {
    height: calc(50% - 5px);
  }
}
</style>
