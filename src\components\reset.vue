<template>
  <el-popover
    placement="bottom-start"
    width="240"
    trigger="click"
    v-model="visible"
  >
    <div class="give-up-save-title">
      <i class="ic-r-exclamation" />
      <span>{{tips.title}}</span>
    </div>
    <p class="give-up-save-content">{{tips.optionDesc}}</p>
    <div style="text-align: right; margin: 0">
      <el-button size="mini" style="min-width: 64px;" @click="visible = false">取消</el-button>
      <el-button type="danger" size="mini" style="min-width: 84px;" @click.stop.prevent="reset">{{tips.confirmText}}</el-button>
    </div>
    <el-button slot="reference" type="text">{{tips.btn}}</el-button>
  </el-popover>

</template>

<script>
export default {
  name: 'OsReset',
  props: {
    tips: {
      type: Object,
      title: '',
      optionDesc:'',
      confirmText: '',
      btn: ''
    }
  },
  data () {
    return {
      visible: false
    }
  },
  methods: {
    reset () {
      this.visible = false
      this.$emit('reset')
    }
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="scss" scoped>
.give-up-save-title {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  i {
    font-size: 16px;
    color: $warning;
  }
  span {
    margin-left: 8px;
    font-size: 16px;
    font-weight: 500;
  }
}
.give-up-save-content {
  padding-left: 24px;
  margin-bottom: 16px;
}
</style>
