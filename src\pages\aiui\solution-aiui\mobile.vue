<template lang="html">
  <div class="aiui">
    <div class="banner-wrap">
			<!--<p>AIUI移动端解决方案</p>-->
			<a class="mobile-btn" href="/app/add">开始试用</a>
		</div>
		<div class="group-wrap">
			<div class="group-title">应用领域</div>
			<div class="group-content apply-area">
				<div class="apply-area-item apply-area-voice-service">
					<div class="voice-service-title apply-item-title">语音助手</div>
				</div>
				<div class="map-info-wrap">
					<div class="apply-area-item apply-area-map">
						<div class="map-info-title apply-item-title">地图查询</div>
					</div>
					<div class="apply-area-item apply-area-info">
						<div class="map-info-title apply-item-title">信息查询</div>
					</div>
				</div>

				<div class="apply-area-item apply-area-voice-service">
					<div class="voice-service-title apply-item-title">智能客服</div>
				</div>
			</div>
		</div>
		<div class="group-wrap">
			<div class="group-title">方案能力</div>
			<div class="group-content solution-capabilities">
			</div>
		</div>
		<div class="group-wrap apply-platform">
			<div class="group-title">应用平台</div>
			<div class="apply-platform-info">提供基于Android、IOS、Windows、Linux平台的互联网解决方案，适用于各类平台应用开发的需求</div>
		</div>
		<div class="group-wrap">
			<div class="group-title">使用流程</div>
			<div class="using-process-content">

			</div>
		</div>
  </div>
</template>

<script>
import utils from '../../../assets/lib/utils.js'

export default {
  layout: 'aiuiHome',
  head () {
    return {
      title: 'AIUI 移动端解决方案',
      meta: [
        { name: 'keywords', content: 'AIUI，科大讯飞，AIUI 开放平台，AIUI 移动端，AIUI APP' },
        { name: 'description', content: 'AIUI WebAPI 使用 HTTP(S) 协议，包含语音识别、语音语义、文本语义接口，适用于各种编程语言，以及各种低配置机器，接收音频文件上传，并返回解析结果。' }
      ]
    }
  },
  data () {
    return {
    }
  },
  methods: {
    coreClick(index) {
      this.coreIndex = index
      this.coreSwiper.slideTo(index, 1000, false)
    }
  }
}
</script>
<style lang="scss" scoped>
	.banner-wrap{
		height: 400px;
		position: relative;
		background: url("../../../assets/images/solutions/mobile/hp-banner2.jpg") no-repeat center;
		.mobile-btn{
			display: block;
			width: 142px;
			height: 41px;
			margin-left: -73px;
			position: absolute;
			top: 47%;
			left: 50%;
			color: #fff;
			border: 2px solid #fff;
			border-radius: 5px;
			text-align: center;
			line-height: 41px;
			cursor: pointer;
			&:hover{
				border: 2px solid #45a9eb;
			}
		}
	}
	.group-wrap{
		margin-top: 65px;
		margin-bottom: 30px;
		.apply-area{
			background: url("../../../assets/images/solutions/mobile/hp-apply-area2.png") no-repeat center;
		}
		.apply-item-title{
			width: 100%;
			height: 45px;
			line-height: 45px;
			font-size: 21px;
			text-align: center;
			color: #fff;
			background: url("../../../assets/images/solutions/mobile/hp-apply-area-icon.png") no-repeat center;
			background-position-y: 0;
		}
		.group-title{
			margin: 0 auto 50px;
			height: 70px;
			width: 120px;
			font-size: 28px;
			text-align: center;
			background: url("../../../assets/images/solutions/mobile/hp-title-icon.png") no-repeat center;
			background-position-y: 45px;
		}
		.group-content{
			width: 986px;
			margin: auto;
			font-size: 0;
		}
		.apply-area-voice-service{
			width: 350px;
			height: 370px;
			display: inline-block;
			vertical-align: top;
			.voice-service-title{
				margin-top: 80%;
			}
		}
		.map-info-wrap{
			width: 286px;
			height: 374px;
			display: inline-block;
			vertical-align: middle;
			font-size: 0;
			.apply-area-map{
				height: 185px;
			}
			.apply-area-info{
				height: 185px;
			}
			.map-info-title{
				padding-top: 45%;
				background-position-y: 130px;
			}
		}
		.apply-area-item:hover{
			background: rgba(92,105,164,0.8);
			.voice-service-title{
				margin-top: 55%;
			}
			.map-info-title{
				padding-top: 30%;
				background-position-y: 85px;
			}
		}


		.apply-area-service{
			width: 348px;
			height: 370px;
			display: inline-block;
			vertical-align: middle;
			.service-item{
				margin-top: 300px;
			}
		}
	}
	.solution-capabilities{
		height: 350px;
		font-size: 19px;
		color: #969696;
		background: url("../../../assets/images/solutions/mobile/hp-solutions-capabilities3.png") no-repeat center;
		background-position-x: 70px;
		background-position-y: 0;
	}
	.apply-platform{
		height: 400px;
		background: url("../../../assets/images/solutions/mobile/hp-app-platform.png") no-repeat center;
		background-position-y: 100px;
		.apply-platform-info{
			margin: auto;
			margin-top: 90px;
			width: 750px;
			line-height: 40px;
			text-align: center;
			font-size: 24px;
			color: #fff;
		}
	}
	.using-process-content{
		height: 270px;
		margin: auto;
		background: url("../../../assets/images/solutions/mobile/hp-using-process-all.png") no-repeat center;
	}
	.charging-mode-bg{
		height: 400px;
		background: url("../../../assets/images/solutions/mobile/hp-charging-mode.png") no-repeat center;
		background-position-y: 0;
	}
</style>
