import DefaultLayout from '@L/defaultLayout'
import StudioSkillLayout from '@L/studioSkillLayout'

export default [
  {
    path: '/sub',
    component: DefaultLayout,
    children: [
      {
        path: '/',
        name: 'sub-studio', // 控制台
        redirect: {
          name: 'sub-studio-handle-platform-skills',
          params: { nav: 'skills' },
        },
      },
      {
        path: 'skills',
        name: 'sub-studio-handle-platform-skills', // 控制台-我的技能
        component: () =>
          import(
            /* webpackChunkName: "sub-studio-skills" */ '@P/studio/handlePlatform/skills'
          ),
      },
      {
        path: 'entities',
        name: 'sub-studio-handle-platform-entities', // 控制台-我的实体
        component: () =>
          import(
            /* webpackChunkName: "sub-studio-entities" */ '@P/studio/handlePlatform/entities'
          ),
      },
      {
        path: 'auxiliaries',
        name: 'sub-studio-handle-platform-auxiliaries', // 控制台-我的辅助词
        component: () =>
          import(
            /* webpackChunkName: "sub-studio-auxiliaries" */ '@P/studio/handlePlatform/auxiliaries'
          ),
      },
      {
        path: 'labels',
        name: 'sub-studio-handle-platform-labels', // 控制台-我的交互标签
        component: () =>
          import(
            /* webpackChunkName: "sub-studio-labels" */ '@P/studio/handlePlatform/labels'
          ),
      },
      {
        path: 'official-entities',
        name: 'sub-studio-official-entities', // 官方实体
        component: () =>
          import(
            /* webpackChunkName: "sub-studio-official-entities" */ '@P/studio/handlePlatform/official-entities'
          ),
      },
      {
        path: 'official-auxiliaries',
        name: 'sub-studio-official-auxiliaries', // 官方辅助词
        component: () =>
          import(
            /* webpackChunkName: "sub-studio-official-auxiliaries" */ '@P/studio/handlePlatform/official-auxiliaries'
          ),
      },
      {
        path: 'official-labels',
        name: 'sub-studio-official-labels', // 官方交互标签
        component: () =>
          import(
            /* webpackChunkName: "sub-studio-official-labels" */ '@P/studio/handlePlatform/official-labels'
          ),
      },
    ],
  },
  {
    path: '/sub/skill/:skillId',
    component: StudioSkillLayout,
    children: [
      {
        path: '/',
        name: 'sub-skill', // 技能页
        redirect: { name: 'sub-skill-intentions' },
      },
      {
        path: 'info',
        name: 'sub-skill-info', // 基本信息页
        component: () =>
          import(
            /* webpackChunkName: "sub-skill-info" */ '@P/studio/skill/info'
          ),
      },
      {
        path: 'intentions',
        name: 'sub-skill-intentions', // 意图页
        component: () =>
          import(
            /* webpackChunkName: "sub-skill-intentions" */ '@P/studio/skill/intentions'
          ),
      },
      {
        path: 'referedIntentions',
        name: 'sub-skill-refered-intentions', // 引用的意图
        component: () =>
          import(
            /* webpackChunkName: "refered-intentions" */ '@P/studio/skill/referedIntentions'
          ),
      },
      {
        path: 'referedLabels',
        name: 'sub-skill-refered-labels', // 引用的交互标签
        component: () =>
          import(
            /* webpackChunkName: "sub-refered-labels" */ '@P/studio/skill/referedLabels'
          ),
      },
      {
        path: 'referOfficialIntentions',
        name: 'sub-refer-official-intentions', // 引用官方意图页
        component: () =>
          import(
            /* webpackChunkName: "refer-official-intentions" */ '@P/studio/skill/referOfficialIntentions'
          ),
      },
      {
        path: 'intentionReferenced/:intentId/:quoteId',
        name: 'sub-skill-intention-referenced', // 意图详情页
        component: () =>
          import(
            /* webpackChunkName: "skill-intention-referenced" */ '@P/studio/skill/intentionReferenced/index.vue'
          ),
      },
      {
        path: 'modifiers',
        name: 'sub-skill-modifiers', // 自定义修饰语
        component: () =>
          import(
            /* webpackChunkName: "studio-modifiers" */ '@P/studio/skill/modifiers'
          ),
      },
      {
        path: 'entities',
        name: 'sub-skill-entities', // 引用的实体页
        component: () =>
          import(
            /* webpackChunkName: "sub-skill-entities" */ '@P/studio/skill/entities'
          ),
      },
      {
        path: 'auxiliaries',
        name: 'sub-skill-auxiliaries', // 引用的辅助词页
        component: () =>
          import(
            /* webpackChunkName: "sub-skill-auxiliaries" */ '@P/studio/skill/auxiliaries'
          ),
      },
      {
        path: 'intention/:intentId',
        name: 'sub-skill-intention', // 意图详情页
        component: () =>
          import(
            /* webpackChunkName: "sub-skill-intention" */ '@P/studio/skill/intention'
          ),
      },
      {
        path: 'postprocess',
        name: 'sub-skill-post-process', // 后处理
        component: () =>
          import(
            /* webpackChunkName: "sub-skill-postprocess" */ '@P/studio/skill/postProcess'
          ),
      },
      {
        path: 'publish',
        name: 'sub-skill-publish', // 发布页
        component: () =>
          import(
            /* webpackChunkName: "sub-skill-publish" */ '@P/studio/skill/publish'
          ),
      },
      // 2020-01-08 推荐优化暂时下线
      {
        path: 'optimize',
        redirect: { name: 'skill-intentions' },
        //   name: 'sub-skill-optimize', // 推荐优化页
        //   component:() => import(/* webpackChunkName: "sub-skill-optimize" */ '@P/studio/skill/optimize')
      },
      {
        path: 'version',
        name: 'sub-skill-version', // 版本管理
        component: () =>
          import(
            /* webpackChunkName: "sub-skill-version" */ '@P/studio/skill/version'
          ),
      },
      //业务定制
      {
        path: 'extend-info',
        name: 'extend-sub-skill-info', // 基本信息页
        component: () =>
          import(
            /* webpackChunkName: "extend-sub-skill-info" */ '@P/studio//skill/extend/info'
          ),
      },
      {
        path: 'extend-intentions',
        name: 'extend-sub-skill-intentions', // 意图页
        component: () =>
          import(
            /* webpackChunkName: "extend-sub-skill-intentions" */ '@P/studio/skill/extend/intentions'
          ),
      },
      {
        path: 'extend-intention/:intentId',
        name: 'extend-sub-skill-intention', // 意图详情页
        component: () =>
          import(
            /* webpackChunkName: "extend-sub-skill-intention" */ '@P/studio/skill/extend/intention'
          ),
      },
      {
        path: 'extend-entities',
        name: 'extend-sub-skill-entities', // 引用的实体页
        component: () =>
          import(
            /* webpackChunkName: "extend-sub-skill-entities" */ '@P/studio/skill/extend/entities'
          ),
      },
      {
        path: 'extend-entity/:addDictId/:delDictId',
        name: 'extend-sub-skill-entity', // 定制的实体详情页
        component: () =>
          import(
            /* webpackChunkName: "extend-sub-skill-entity" */ '@P/studio/skill/extend/extendEntitie'
          ),
      },
    ],
  },
  {
    path: '/sub/entity/:entityId',
    component: DefaultLayout,
    children: [
      {
        path: '/',
        name: 'sub-entity', // 实体详情页
        component: () =>
          import(/* webpackChunkName: "sub-entity" */ '@P/studio/entity'),
      },
    ],
  },
  {
    path: '/sub/auxiliary/:entityId',
    component: DefaultLayout,
    children: [
      {
        path: '/',
        name: 'sub-auxiliary', // 实体详情页
        component: () =>
          import(/* webpackChunkName: "sub-auxiliary" */ '@P/studio/auxiliary'),
      },
    ],
  },
]
