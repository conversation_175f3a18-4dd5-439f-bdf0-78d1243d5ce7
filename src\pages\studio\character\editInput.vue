<template>
  <div class="editInput" :data-type="valueType">
    <el-input
      v-if="valueType === 'input'"
      class="small-table__input"
      v-model.trim="scope.row.value"
      @focus="handleInputFocus"
      @blur="handleInputBlur"
      @keyup.enter.native="handleInputEnter(scope)"
    />
    <div v-else-if="valueType === 'tag'" class="small-table__tags">
      <el-tag
        class="small-table__tags-item"
        v-for="(tag, index) in taglist"
        :key="index"
        closable
        :disable-transitions="false"
        @close.stop="delTag(scope.row, index)"
      >
        <span :title="tag">{{ tag }}</span>
      </el-tag>
      <el-input
        v-model.trim="addTags"
        class="small-table__tags-input"
        placeholder="输入标签，回车隔开"
        @keyup.enter.native="
          handleTagInputEnter(scope.row, scope.$index, $event)
        "
        @blur="handleTagInputBlur(scope)"
        @focus="handleTagInpuFocus(scope)"
      />
    </div>
    <div v-else-if="valueType === 'autocomplete'">
      <el-autocomplete
        class="autocomplete__input small-table__input"
        size="medium"
        v-model="scope.row.value"
        :fetch-suggestions="querySearch"
        placeholder="请输入内容"
        @select="handleSelect"
        @blur="handleInputBlur"
        @focus="handleInputFocus"
      ></el-autocomplete>
    </div>
    <div v-else-if="valueType === 'birthday'">
      <el-input
        v-if="birthdayType === '自定义'"
        class="small-table__input birthday"
        placeholder="请输入"
        v-model.trim="birthdayCustomValue"
        @focus="openExpand(true)"
        @blur="handlebirthdayInputBlur"
        @keyup.enter.native="handlebirthdayInputEnter"
      />

      <el-date-picker
        class="datepicker"
        v-if="birthdayType === '具体日期'"
        type="date"
        placeholder="选择日期"
        value-format="yyyy-MM-dd"
        v-model="birthdaySpecficValue"
        @change="handleDatePickerChange"
        @blur="handleDatePickerBlur($event)"
      ></el-date-picker>
      <div
        v-if="birthdayType === '设备激活日'"
        style="width: 180px; height: 10px; display: inline-block"
      ></div>
      <el-select
        class="personal-select"
        v-model="birthdayType"
        @change="handlebirthdaySelect"
        placeholder="请选择"
      >
        <el-option
          v-for="(item, index) in birthdayTypes"
          :key="index"
          :label="item"
          :value="item"
        ></el-option>
      </el-select>
    </div>
    <template v-else-if="valueType === 'unedit'">{{ value }}</template>
  </div>
</template>
<script>
export default {
  props: {
    scope: {
      type: Object,
    },
    activeName: {
      type: String,
    },
  },
  data() {
    return {
      addTags: '',
      taglist: [],
      value: '',
      originValue: '',
      tagInputModel: '',
      rowEditable: false,
      constellations: [],
      zodiacs: [],
      // birthdayType: '设备激活日',
      // birthdayTypes: ['设备激活日', '具体日期', '自定义'],
      birthdayType: '具体日期',
      birthdayTypes: ['具体日期', '自定义'],
      birthdayCustomValue: '',
      birthdaySpecficValue: '',
      age: '',
      zodiac: '',
      constellation: '',
      options: [],
    }
  },
  watch: {
    'scope.row.value'(val, oldval) {
      console.log(val)
      this.$emit('changeSysAnswerSet', this.scope)
    },
    birthdayCustomValue(val, oldval) {
      this.scope.row.value = val
    },
    value(v, oldval) {
      if (v !== oldval) {
        if (v !== this.originValue) {
          this.$emit('isEdited', this.activeName, this.scope.row, true)
        }
        if (this.valueType === 'tag') {
          this.scope.row.value = v
          if (v !== this.originValue) {
            this.$emit('isEdited', this.activeName, this.scope.row, true)
          }
          this.taglist = v.length === 0 ? null : this.value.split('|')
        }
      }
    },
  },
  computed: {
    // originValue(){
    //     return this.$deepClone(this.value)
    // },
    valueType() {
      let rowVal = this.scope.row.value
      let sysValueType = this.scope.row.sysValueType
      if (sysValueType === 0) {
        if (this.scope.row.isSingleValue) {
          this.value = rowVal
          return 'input'
        } else {
          this.value = rowVal
          return 'tag'
        }
      } else if (sysValueType === 1) {
        this.value = rowVal
        return 'autocomplete'
      } else if (sysValueType === 2) {
        if (rowVal === '{{birthday}}') {
          this.birthdayType = '设备激活日'
        } else {
          if (!rowVal) {
            this.birthdaySpecficValue = this.birthdaySpecficValue
              ? this.birthdaySpecficValue
              : rowVal
            this.birthdayCustomValue = this.birthdayCustomValue
              ? this.birthdayCustomValue
              : rowVal
            this.birthdayType =
              this.birthdayType === '设备激活日' ? '自定义' : this.birthdayType
          } else {
            if (rowVal.indexOf('-') !== -1) {
              this.birthdayType = '具体日期'
              this.birthdaySpecficValue = rowVal
            } else {
              this.birthdayType = '自定义'
              this.birthdayCustomValue = rowVal
            }
          }
        }
        return 'birthday'
      } else if (sysValueType === 3) {
        this.value = rowVal
        return 'unedit'
      }
    },
  },
  created() {
    this.originValue = this.scope.row.value
  },
  updated() {
    // console.log( this.scope.row.value)
  },
  mounted() {
    this.loadAll()
  },
  //   },
  methods: {
    findParentByTagName(el, parentTags) {
      let currentEl = el
      let commonTags = Array.isArray(parentTags)
        ? parentTags.map((v) => v.toLocaleUpperCase())
        : [parentTags.toLocaleUpperCase()]

      while (currentEl.parentNode && !commonTags.includes(currentEl.tagName)) {
        currentEl = currentEl.parentNode
      }
      return currentEl
    },
    checkValue(v) {
      if (v.length > 20) {
        return this.$message.warning('属性长度不能超过20个字符')
      }
      let reg = /^[a-zA-Z0-9\u4e00-\u9fff]+$/
      if (!reg.test(v)) {
        if (v) {
          return this.$message.warning('属性仅支持汉字/字母/数字')
        }
      }
    },
    changeValue(v) {
      // if(!v){
      //   this.$emit('changeSysAnswerSet',this.scope)
      // }
    },
    handleInputBlur() {
      // this.openExpand();

      this.checkValue(event.target.value)
      this.changeValue(event.target.value)
      this.findParentByTagName(event.srcElement, 'td').classList.toggle(
        'small-table__border',
        false
      )
    },
    handlebirthdayInputEnter() {
      event.target.blur()

      this.findParentByTagName(event.srcElement, 'td').classList.toggle(
        'small-table__border-hover',
        false
      )
      let data = {
        value: this.birthdayCustomValue,
        age: null,
        zodiac: null,
        constellation: null,
      }
      this.$emit('birthdateHandle', data)
    },
    handlebirthdayInputBlur() {
      // this.openExpand();
      if (event.target.value.length > 20) {
        this.birthdayCustomValu = ''
        return this.$message.warning(`自定义内容不能大于20个字符`)
      }
      this.checkValue(event.target.value)
      this.findParentByTagName(event.srcElement, 'td').classList.toggle(
        'small-table__border',
        false
      )
      let data = {
        value: this.birthdayCustomValue,
        age: null,
        zodiac: null,
        constellation: null,
      }
      this.$emit('birthdateHandle', data)
    },
    handleInputFocus() {
      this.openExpand(true)
      this.findParentByTagName(event.srcElement, 'td').classList.toggle(
        'small-table__border',
        true
      )
    },
    handleDatePickerBlur(e) {
      // this.birthdayType = "具体日期";
      this.findParentByTagName(e.$el, 'td').classList.toggle(
        'small-table__border',
        false
      )
    },
    handleSelect(item) {
      this.scope.row.value = item.key
    },
    handlebirthdaySelect(v) {
      this.openExpand(true)
      let self = this
      if (v === '自定义') {
        let data = {
          value: this.birthdayCustomValue,
          age: null,
          zodiac: null,
          constellation: null,
        }
        this.$emit('birthdateHandle', data)
      } else if (v === '具体日期') {
        //  this.birthdayType = "具体日期";
        this.handleDatePickerChange()
      } else {
        this.openExpand(true)
        let data = {
          value: '{{birthday}}',
          age: null,
          zodiac: null,
          constellation: null,
        }
        this.$emit('birthdateHandle', data)
      }
    },
    handleDatePickerChange() {
      if (!this.birthdaySpecficValue) {
        let data = {
          value: this.birthdaySpecficValue,
          age: null,
          zodiac: null,
          constellation: null,
        }
        this.$emit('birthdateHandle', data)
        this.$message.warning('具体日期不能为空')
        return
      }
      let arr = this.birthdaySpecficValue
        ? this.birthdaySpecficValue.split('-')
        : []
      let year = parseInt(arr[0])
      let month = parseInt(arr[1])
      let date = parseInt(arr[2])
      let zodiac = this.getZodiac(year)
      let age = this.getAge(year).toString()
      let constellation = this.getConstellation(month, date)
      let data = {
        value: this.birthdaySpecficValue,
        age: age,
        zodiac: zodiac,
        constellation: constellation + '座',
      }
      this.$emit('birthdateHandle', data)
    },
    handleInputEnter(scope) {
      event.target.blur()
      this.findParentByTagName(event.srcElement, 'td').classList.toggle(
        'small-table__border-hover',
        false
      )
    },
    delTag(row, index) {
      let arr = this.value.split('|')
      arr.splice(index, 1)
      if (arr.length === 0) {
        this.value = ''
      } else {
        this.value = arr.join('|')
      }
    },
    handleTagInputEnter(row) {
      this.addTag(row)
    },
    handleTagInputSpace(row) {
      let canAdd = this.addTags.split(' ').some((tag) => {
        return tag === ''
      })
      if (canAdd) {
        this.addTag(row)
      }
    },

    handleTagInputBlur(scope) {
      // this.openExpand();
      this.findParentByTagName(event.srcElement, 'td').classList.toggle(
        'small-table__border',
        false
      )
      event.srcElement.parentNode.classList.toggle('show', false)
      this.addTag(scope.row)
    },
    handleTagInpuFocus(scope) {
      this.openExpand(true)
      this.findParentByTagName(event.srcElement, 'td').classList.toggle(
        'small-table__border',
        true
      )
    },
    openExpand(v) {
      this.$emit('openExpand', v)
    },
    addTag(row) {
      let self = this
      let arr = this.taglist || []
      let addTags = this.addTags

      if (addTags) {
        let checkTag = false
        let tag
        if (addTags == '') {
          return
        } else if (arr.length > 9) {
          self.$message.warning(`最多支持10个标签`)
          return
        } else if (self.$utils.inArray(arr, addTags)) {
          self.$message.warning(`请勿重复添加`)
          return
        } else if (addTags.length > 20) {
          self.$message.warning(`标签不能大于20个字符`)
          return
        } else if (addTags.length === 0) {
          self.$message.warning(`请勿重复添加`)
          return
        } else {
          tag = addTags
        }
        if (this.value.length === 0) {
          this.value = tag
        } else {
          this.value = this.value + '|' + tag
        }
        this.addTags = ''
      }
    },
    querySearch(queryString, cb) {
      var arr = []
      this.scope.row.sysValueSet.map((item, index) => {
        arr.push({
          key: item,
          value: item,
        })
      })
      var results = queryString
        ? arr.filter(this.createFilter(queryString))
        : arr
      // 调用 callback 返回建议列表的数据
      cb(results)
    },
    createFilter(queryString) {
      let type = this.scope.row
      return (type) => {
        return type.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0
      }
    },
    getAge(year) {
      var now = new Date().getFullYear()
      return now - year
    },
    getZodiac(year) {
      let zodiacArr = [
        '猪',
        '鼠',
        '牛',
        '虎',
        '兔',
        '龙',
        '蛇',
        '马',
        '羊',
        '猴',
        '鸡',
        '狗',
      ]
      return zodiacArr[parseInt((year + 9) % 12)]
    },
    getConstellation(month, day) {
      var s = '魔羯水瓶双鱼白羊金牛双子巨蟹狮子处女天秤天蝎射手魔羯'
      var arr = [20, 19, 21, 21, 21, 22, 23, 23, 23, 23, 22, 22]
      return s.substr(month * 2 - (day < arr[month - 1] ? 2 : 0), 2)
    },
    loadAll() {
      this.constellations = [
        { value: '水瓶', key: '水瓶' },
        { value: '双鱼', key: '双鱼' },
        { value: '白羊', key: '白羊' },
        { value: '金牛', key: '金牛' },
        { value: '双子', key: '双子' },
        { value: '巨蟹', key: '巨蟹' },
        { value: '狮子', key: '狮子' },
        { value: '处女', key: '处女' },
        { value: '天秤', key: '天秤' },
        { value: '天蝎', key: '天蝎' },
        { value: '射手', key: '射手' },
        { value: '魔羯', key: '魔羯' },
      ]
      this.zodiacs = [
        { value: '猪', key: '猪' },
        { value: '鼠', key: '鼠' },
        { value: '牛', key: '牛' },
        { value: '虎', key: '虎' },
        { value: '兔', key: '兔' },
        { value: '龙', key: '龙' },
        { value: '蛇', key: '蛇' },
        { value: '马', key: '马' },
        { value: '羊', key: '羊' },
        { value: '猴', key: '猴' },
        { value: '鸡', key: '鸡' },
        { value: '狗', key: '狗' },
      ]
    },
  },
}
</script>
<style lang="scss">
.editInput {
  .small-table__tags-input {
    display: none;
    width: 130px;
    &.show {
      display: inline-block;
    }
  }
  .autocomplete__input {
    width: 100%;
    .el-input__inner {
      border: none;
      height: 24px;
      line-height: 24px;
    }
  }
  .personal-select {
    display: inline-block;
    width: 100px;
    input {
      border: 0;
      padding-left: 0;
      height: 24px;
      line-height: 24px;
    }
    .el-input__icon {
      line-height: 24px;
    }
  }
  .birthday {
    display: inline-block;
    width: 180px;
  }
  .datepicker {
    display: inline-block;
    width: 180px;
    input {
      border: 0;
      height: 24px;
      line-height: 24px;
    }
    .el-input__icon {
      line-height: 24px;
    }
  }
}
</style>
