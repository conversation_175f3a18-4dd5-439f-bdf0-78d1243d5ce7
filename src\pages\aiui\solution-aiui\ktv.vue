<template>
  <div class="ktv-solution main-content">
    <section class="main-content-banner">
      <div class="banner-text">
        <h2>KTV智能交互解决方案</h2>
        <p class="banner-text-content">
          针对KTV场景量身定制，<br />
          通过语音识别、语义理解、语音播控等语音能力，<br />
          创造不一样的娱乐体验，助力传统KTV升级转型。
        </p>

        <div class="banner-text-button" @click="toConsole">合作咨询</div>
      </div>
    </section>
    <section class="section section-0">
      <div class="section-title">
        <span class="section-title-bold">方案优势</span>
      </div>
    </section>
    <div class="character-section section">
      <div class="character-item" style="padding-left: 15%">
        <p class="item-title">语音点歌</p>
        <p class="item-desc">
          无需手动触屏，歌名歌手语音搜索，<br />让点唱方式更简单更有趣。
        </p>
      </div>
      <div class="character-item bg1"></div>
      <div class="character-item">
        <div class="bg2"></div>
      </div>
      <div class="character-item">
        <p class="item-title">语音控制</p>
        <p class="item-desc">
          无需唤醒，直接命令控制，<br />点歌、切歌、伴唱、呼叫服务、<br />玩转KTV说句话就能轻松搞定。
        </p>
      </div>
      <div class="character-item" style="padding-left: 15%">
        <p class="item-title">语音点单</p>
        <p class="item-desc">
          K歌过程需要呼叫服务员，购买商品，<br />拿起话筒直接说出所需服务，响应快速，服务高效。
        </p>
      </div>
      <div class="character-item bg3"></div>
    </div>
    <div class="cooperation-case-wrap">
      <div class="head-wrap">
        <p class="title">合作案例「 巨嗨AI点播系统 」</p>
        <p class="desc">
          巨嗨AI点播系统，应用科大讯飞AI技术，<br />
          深度了解KTV交互场景后，提供优化适配的KTV语音语义服务，<br />
          包括但不限于个性化推荐、在线预订、在线点单等服务模式，<br />
          创新式采用无唤醒词命令，让用户感受自然流畅的智能语音体验。<br />
          轻松勾勒出充满科技感趣味性的KTV娱乐场景。
        </p>
      </div>

      <div class="case-video" @click="play" v-if="!isPlaying">
        <i class="icon-video-play"></i>
      </div>
      <div v-else class="case-video-player">
        <video :src="videoSrc" autoplay preload controls></video>
      </div>
    </div>
    <corp @jump="toConsole">
      <template> 提交信息，我们会尽快与您联系</template>
    </corp>
    <!-- <div class="contact-wrap">
      <div class="title">合作咨询</div>
      <p class="desc">提交信息，我们会尽快与您联系</p>

      <aiui-button>
        <a hasTop @click="toConsole">申请合作</a>
      </aiui-button>
    </div> -->
    <!-- <video-player
      v-if="videoVisible"
      :width="1000"
      :height="426"
      :videoSrc="videoSrc"
      :videoStyle="videoStyle"
      @close="videoVisible = false"
    /> -->
  </div>
</template>
<script>
// import VideoPlayer from '../../../components/videoPlayer'
import videoPlayer from '@C/videoPlayer/index'
import corp from '@P/aiui/solution-aiui/components/corp.vue'

export default {
  layout: 'aiuiHome',
  data() {
    return {
      videoVisible: false,
      videoStyle: {
        'max-width': '1000px',
        width: '100%',
        height: '426px',
        'box-sizing': 'border-box',
        'margin-left': '-500px',
        'margin-top': '-213px',
      },
      isPlaying: false,
      videoSrc:
        'https://gh-test1.oss-cn-beijing.aliyuncs.com/temp/%E5%B7%A8%E5%97%A8%E8%AF%AD%E9%9F%B3%E6%95%99%E7%A8%8B.mp4',
    }
  },
  methods: {
    getWindowHeight() {
      return 'innerHeight' in window
        ? window.innerHeight
        : document.documentElement.offsetHeight
    },
    // play() {
    //   let height = Math.min(this.getWindowHeight() * 0.9, 562)
    //   let width = parseInt((1920 * height) / 1080)
    //   videoPlayer({
    //     width,
    //     height,
    //     videoSrc: this.videoSrc,
    //     videoStyle: {
    //       width: `${width}px`,
    //       height: `${height}px`,
    //       'box-sizing': 'border-box',
    //       'margin-left': `-${width * 0.5}px`,
    //       'margin-top': `-${height * 0.5}px`,
    //     },
    //   })
    // },
    play() {
      this.isPlaying = true
    },
    toConsole() {
      // 判断是否有百度SEM营销参数，有的话，提交表单页需要带上
      const search = window.location.search
      if (search) {
        window.open(`/solution/apply/1${search}`)
      } else {
        window.open('/solution/apply/1')
      }
    },
  },
  components: {
    // VideoPlayer,
    corp,
  },
}
</script>
<style lang="scss" scoped>
.main-content {
  &-banner {
    background: url(~@A/images/solution/ktv/img_voice_so.png) center no-repeat;
    background-size: cover;
    height: 500px;
    overflow: hidden;
    width: 100%;
    .banner-text {
      max-width: 1200px;
      color: #fff;
      height: 100%;
      margin: auto;
      &-button {
        font-size: 16px;
        text-align: center;
        font-weight: 400;
        width: 140px;
        height: 40px;
        line-height: 40px;
        border: 1px solid #fff;
        border-radius: 40px;
        color: #fff;
        cursor: pointer;
        transition: 0.6s;
      }
      h2 {
        color: #fff;
        padding-top: 148px;
        margin-bottom: 29px;
        font-size: 48px;
        font-weight: 500;
        line-height: 48px;
      }
      p {
        font-size: 18px;
        margin-bottom: 74px;
      }

      .banner-text-content {
        width: 570px;
        font-size: 16px;
        font-family: SourceHanSansSC-Regular, SourceHanSansSC;
        font-weight: 400;
        color: rgba(255, 255, 255, 0.86);
        line-height: 30px;
      }
    }
  }

  .section {
    p,
    ul {
      margin-bottom: 0;
    }
    max-width: 1200px;
    overflow: hidden;
    margin: 0 auto;
    .section-title {
      text-align: center;
      font-size: 34px;
      font-family: SourceHanSansSC-Medium, SourceHanSansSC;
      font-weight: bold;
      color: #333;
      line-height: 34px;
      position: relative;
      width: 200px;
      margin: 0 auto;
      .arrow {
        width: 160px;
        height: 8px;
        display: inline-block;
        background-repeat: no-repeat;
        background-size: contain;
        position: absolute;
        z-index: 1;
      }
      .arrow-left {
        background-position: left;
        // background-image: url(~@A/images/solution/smart-hardware/arrow-left.png);
        background-image: url(~@A/images/solution/offline/img_title_01.png);
        top: 50%;
        left: -160px;
      }
      .arrow-right {
        background-position: right;
        // background-image: url(~@A/images/solution/smart-hardware/arrow-right.png);
        background-image: url(~@A/images/solution/offline/img_title_02.png);
        top: 50%;
        right: -160px;
      }
    }
    .section-desc {
      text-align: center;
      margin-top: 40px;
      font-size: 16px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #999999;
    }
    .section-title-bold {
      font-size: 34px;
      font-family: Microsoft YaHei;
      font-weight: bold;
      color: #333333;
    }
    .section-title-spec {
      font-size: 30px;
      font-weight: 500;
      color: #656565;
      line-height: 42px;
    }
    .section-tabs {
      margin-top: 70px;
      ul {
        display: flex;
        justify-content: center;
      }
      li {
        font-size: 20px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #666;
        line-height: 30px;
        position: relative;
        cursor: pointer;
        &:hover {
          color: #1f90fe;
        }
        &.active {
          color: #1f90fe;
          &::before {
            position: absolute;
            content: ' ';
            z-index: 1;
            width: 180px;
            height: 4px;
            background: #1f90fe;
            border-radius: 2px;
            bottom: -32px;
            left: -48px;
          }
          &::after {
            position: absolute;
            content: ' ';
            display: inline-block;
            width: 0;
            height: 0;
            bottom: -100px;
            left: 50%;
            transform: translateX(-50%);
            border: 46px solid;
            border-color: transparent transparent #f4f7f9;
          }
        }
      }
      li + li {
        margin-left: 165px;
      }
    }
    .section-sub-title {
      text-align: center;
      font-size: 18px;
      font-weight: 400;
      color: #777777;
      line-height: 25px;
      margin-top: 42px;
    }
    .section-sub-title-spec {
      font-size: 17px;
      font-weight: 400;
      color: #999999;
      line-height: 28px;
      margin-top: 18px;
    }
  }
  .section-0 {
    padding: 48px 0 10px 0;
  }
}
.banner-wrap {
  width: 100%;
  min-width: 1000px;
  padding-top: 60px;
  background: url('../../../assets/images/ktv-solution/banner-ktv.jpg') center
    no-repeat;
  background-size: cover;
}
.banner {
  margin: 0 auto;
  padding-top: 112px;
  height: 500px;
  width: 1200px;
  position: relative;
  background-size: 100%;
  background-position-x: 390px;
  color: #fff;
}
.banner-title {
  font-size: 46px;
  line-height: 60px;
  letter-spacing: 2px;
}
.banner-desc {
  margin: 16px 0 64px;
  font-size: 20px;
  letter-spacing: 1px;
  line-height: 32px;
}
.indevice-btn {
  display: block;
  margin-bottom: 100px;
  font-size: 16px;
  text-align: center;
  font-weight: 500;
  width: 240px;
  height: 52px;
  line-height: 52px;
  letter-spacing: 0.5px;
  border: 1px solid #fff;
  border-radius: 2px;
  color: #fff;
  cursor: pointer;
  transition: 0.6s;
  &:hover {
    color: #002985;
    background: #fff;
  }
}

.character-section {
  min-width: 1200px;
  font-size: 0;
}
.character-item {
  display: inline-block;
  width: 600px;
  vertical-align: top;
  padding-top: 88px;
  height: 320px;
  .item-title {
    margin-bottom: 16px;
    font-size: 34px;
    line-height: 34px;
    color: #666;
  }
  .item-desc {
    font-size: 16px;
    line-height: 30px;
    letter-spacing: 0.5px;
    color: #999;
    margin-top: 43px;
  }
  &:nth-child(2n + 1) {
    margin-left: calc(50% - 600px);
  }
  &:nth-child(3) {
    width: 50%;
    margin-left: 0;
    padding-top: 0;
    padding-left: calc(50% - 600px);
    background-color: rgba(242, 245, 247, 0.3);
  }
  &:nth-child(4) {
    width: 50%;
    background-color: rgba(242, 245, 247, 0.3);
    padding-left: 120px;
  }
}
.bg1 {
  background: url('../../../assets/images/ktv-solution/ktv_features.png') -10px -10px;
  background-size: cover;
}
.bg2 {
  width: 600px;
  height: 320px;
  background: url('../../../assets/images/ktv-solution/ktv_features.png') 1200px -320px;
  background-size: cover;
}
.bg3 {
  background: url('../../../assets/images/ktv-solution/ktv_features.png') 600px -640px;
  background-size: cover;
}

.cooperation-case-wrap {
  padding: 50px 0 100px 0;
  height: 858px;
  background-color: #13699b;
  text-align: center;
  color: #fff;
  background: url('~@A/images/solution/ktv/img_cooperation_case_bg.png') center
    no-repeat;
  background-size: cover;
  .head-wrap {
    margin: 0 auto;
  }
  .title {
    margin-bottom: 24px;
    font-size: 34px;
    line-height: 40px;
    text-align: center;
  }
  .desc {
    line-height: 34px;
    font-size: 16px;
    font-weight: 300;
    display: inline-block;
    text-align: left;
    margin: 40px auto 0;
  }
  .case-video {
    position: relative;
    margin: 56px auto 0;
    padding-top: 164px;
    width: 696px;
    height: 392px;
    background: url('../../../assets/images/ktv-solution/img_video_juhai.jpg');
    background-size: contain;
    cursor: pointer;
  }
  .case-video-player {
    margin: 56px auto 0;
    width: 696px;
    height: 392px;
    video {
      width: 100%;
      height: 100%;
    }
  }
  .btn-play {
    padding-top: 12px;
    transform: rotateZ(270deg);
    font-size: 32px;
    height: 64px;
    width: 64px;
    border-radius: 50%;
    border: 1px solid;
    margin: 0 auto;
    cursor: pointer;
  }
}

.contact-wrap {
  padding-top: 100px;
  height: 400px;
  text-align: center;
  .title {
    margin-bottom: 16px;
    font-size: 34px;
    color: #333;
    font-weight: bold;
  }
  .desc {
    font-size: 16px;
    color: #666;
    margin-bottom: 50px;
  }
  .apply-btn {
    margin: 60px auto 0;
    background: #1784e9;
    &:hover {
      color: #fff;
    }
  }
}
@media screen and (max-width: 1400px) {
  .banner-wrap {
    background-position-x: 50%;
  }
}
@media screen and (max-width: 719px) {
  .banner-wrap {
    min-width: unset;
    height: 452px;
    text-align: center;
    background: transparent;
    .banner {
      width: 100%;
      padding-top: 40px;
      height: 392px;
      background: url('../../../assets/images/ktv-solution/banner-ktv-m.jpg')
        center no-repeat;
      background-size: cover;
    }
    .banner-title {
      font-size: 30px;
      line-height: 40px;
      letter-spacing: 0.5px;
    }
    .banner-desc {
      margin: 16px 0 32px;
      font-size: 14px;
      letter-spacing: normal;
      line-height: 22px;
    }
    .indevice-btn {
      display: inline-block;
    }
  }
  .character-section {
    position: relative;
    margin-bottom: 56px;
    width: 100%;
    min-width: unset;
    .character-item {
      display: block;
      margin: 0 auto;
      padding: 56px 20px 0;
      max-width: 380px;
      min-width: 375px;
      height: 200px;
      text-align: center;
      &:nth-child(2n + 1) {
        margin-left: auto;
      }
      &:nth-child(3) {
        margin-top: 200px;
        padding-top: 0;
        background-color: transparent;
      }
      &:nth-child(4) {
        position: absolute;
        padding-left: 0;
        top: 400px;
        width: 100%;
        max-width: unset;
        background-color: transparent;
      }
      .item-title {
        margin-bottom: 16px;
        font-size: 24px;
        line-height: 32px;
      }
      .item-desc {
        font-size: 16px;
        line-height: 24px;
        letter-spacing: 0.44px;
      }
    }
    .bg1 {
      background-position: 0;
    }
    .bg2 {
      width: 100%;
      height: 200px;
      background-position: 745px -200px;
    }
    .bg3 {
      background-position: 1495px -200px;
    }
  }
  .cooperation-case-wrap {
    padding: 56px 20px;
    height: auto;
    text-align: left;
    .title {
      margin-bottom: 16px;
      font-size: 24px;
    }
    .desc {
      line-height: 28px;
      font-size: 16px;
      font-weight: normal;
    }
    .case-video {
      position: relative;
      margin: 32px auto 0;
      padding-top: 78.9px;
      width: 335px;
      height: 188.7px;
      background: url('../../../assets/images/ktv-solution/img_video_juhai.jpg');
      background-size: contain;
      cursor: pointer;
    }
    .btn-play {
      padding-top: 2px;
      padding-left: 4px;
      transform: rotateZ(270deg);
      font-size: 20px;
      height: 31px;
      width: 31px;
      border-radius: 50%;
      border: 1px solid;
      margin: 0 auto;
      cursor: pointer;
    }
  }

  .video-layer {
    min-width: unset;
    .vjs_video_3-dimensions {
      width: 100% !important;
    }
    .dialog {
      width: 100%;
      margin-left: -50%;
    }
  }

  .contact-wrap {
    padding: 56px 20px;
    height: 274px;
    .title {
      margin-bottom: 12px;
      font-size: 24px;
    }
    .desc {
      line-height: 26px;
      font-size: 16px;
    }
    .apply-btn {
      margin: 40px auto 0;
    }
  }
}
.icon-video-play {
  display: inline-block;
  width: 100px;
  height: 100px;
  position: absolute;
  z-index: 10;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: url(~@A/images/solution/ktv/btn_video_playback.png) center/100%
    no-repeat;
}

@media screen and (min-width: 500px) and (max-width: 719px) {
  .cooperation-case-wrap {
    text-align: center;
  }
}
</style>
