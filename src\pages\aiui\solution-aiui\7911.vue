<template>
  <div class="main-content">
    <section class="main-content-banner">
      <div class="banner-text">
        <h2 v-html="'AC7911<br/>AIUI语音交互开发套件'" style="line-height: 60px"></h2>
        <p class="pc-show banner-text-content">
          一站式低成本离在线语音交互解决方案
          <br />适用于小家电产品的智能硬件开发评估
        </p>
        <div class="hor-btn">
          <div class="banner-text-button" @click="toConsole">合作咨询</div>
          <div class="banner-text-buy" @click="toBuy">立即购买</div>
        </div>
      </div>
    </section>
    <section class="section section-2">
      <div class="section-title">应用场景</div>
      <div class="section-item">
        <ul>
          <li v-for="(item, index) in app_scenario" :key="index" class="app">
            <img :src="item.src" :alt="item.alt" />
            <p class="app-text">{{ item.alt }}</p>
          </li>
        </ul>
      </div>
    </section>
    <section class="section section-3">
      <div class="section-title">产品功能</div>
      <div class="section-item">
        <ul>
          <li v-for="(item, index) in product_ability" :key="index" class="app">
            <img :src="item.src" :alt="item.alt" />
            <p class="app-text">{{ item.alt }}</p>
          </li>
        </ul>
      </div>
    </section>
    <section class="section section-4">
      <div class="section-title">产品图片</div>
      <div class="section-item"></div>
    </section>
    <section class="section section-5">
      <div class="section-title">使用说明</div>
      <div class="section-item">
        <div class="use-img">
          <img :src="use_explain[0].src" />
          <img :src="use_explain[1].src" />
        </div>
        <div class="use-text">
          <div>
            <p>{{ use_explain[0].title }}</p>
            <p v-html="use_explain[0].sub_title">
              {{ use_explain[0].sub_title }}
            </p>
          </div>
          <div>
            <p>{{ use_explain[1].title }}</p>
            <p v-html="use_explain[1].sub_title">
              {{ use_explain[1].sub_title }}
            </p>
          </div>
        </div>
      </div>
    </section>
    <section class="section section-6">
      <div class="section-title">硬件参数</div>
      <div class="section-item"></div>
    </section>
    <section class="section section-7">
      <div class="section-title">规格尺寸</div>
      <div class="section-item">
        <div class="item">
          <div>
            <p>两层板语音模组尺寸（L*W*H）</p>
            <p>34mm*34mm*3.5mm</p>
          </div>
          <img
            src="../../../assets/images/solution/soft-hardware/7911/section-7-1.png"
          />
        </div>
        <div class="item">
          <div>
            <p>四层板语音模组尺寸（L*W*H）</p>
            <p>34mm*26mm*3.5mm</p>
          </div>
          <img
            src="../../../assets/images/solution/soft-hardware/7911/section-7-2.png"
          />
        </div>
      </div>
    </section>
    <section class="section section-8">
      <div class="section-title">产品清单</div>
      <div class="section-item">
        <div v-for="item in product_list" class="item">
          <img :src="item.src" />
          <p>{{ item.title }}</p>
          <p v-html="item.sub_title">{{ item.sub_title }}</p>
        </div>
      </div>
    </section>
    <section class="section section-9">
      <div class="section-title">服务说明</div>
      <div class="section-item">
        <div v-for="item in service" class="item">
          <img :src="item.src" />
          <p>{{ item.title }}</p>
          <p v-html="item.sub_title">{{ item.sub_title }}</p>
        </div>
      </div>
    </section>
    <section class="section section-10">
      <div class="section-title">开发材料</div>
      <div class="section-item">
        <div class="item">
          <a
            v-for="item in develop_doc"
            @click="toDoc(item.link)"
            style="color: #666666"
          >
            {{ item.name }}
          </a>
        </div>
      </div>
    </section>
    <!-- <section class="section section-5">
      <div class="section-title">
        合作咨询
        <p>提交信息，我们会尽快与您联系</p>
      </div>
      <div class="section-item">
        <aiui-button hasTop @click.native="toConsole">申请合作</aiui-button>
      </div>
    </section> -->
    <!-- <div class="contact-wrap">
      <div class="title">合作咨询</div>
      <p class="desc">提交信息，我们会尽快与您联系</p>

      <aiui-button>
        <a hasTop @click="toConsole">申请合作</a>
      </aiui-button>
    </div> -->
  </div>
</template>

<script>
import corp from '@P/aiui/solution-aiui/components/corp.vue'

export default {
  layout: 'aiuiHome',
  data() {
    return {
      app_scenario: [
        {
          alt: '按摩椅',
          src: require('../../../assets/images/solution/soft-hardware/7911/section-2-1.png'),
        },
        {
          alt: '故事机',
          src: require('../../../assets/images/solution/soft-hardware/7911/section-2-2.png'),
        },
        {
          alt: '智能音箱',
          src: require('../../../assets/images/solution/soft-hardware/7911/section-2-3.png'),
        },
        {
          alt: '智能灯具',
          src: require('../../../assets/images/solution/soft-hardware/7911/section-2-4.png'),
        },
      ],
      product_ability: [
        {
          alt: '正版酷狗音乐',
          src: require('../../../assets/images/solution/soft-hardware/7911/section-3-1.png'),
        },
        {
          alt: '单双麦',
          src: require('../../../assets/images/solution/soft-hardware/7911/section-3-2.png'),
        },
        {
          alt: '打断唤醒',
          src: require('../../../assets/images/solution/soft-hardware/7911/section-3-3.png'),
        },
        {
          alt: '本地离线控制',
          src: require('../../../assets/images/solution/soft-hardware/7911/section-3-4.png'),
        },
        {
          alt: '语音交互',
          src: require('../../../assets/images/solution/soft-hardware/7911/section-3-5.png'),
        },
        {
          alt: '100+云端技能',
          src: require('../../../assets/images/solution/soft-hardware/7911/section-3-6.png'),
        },
        {
          alt: '内置功放',
          src: require('../../../assets/images/solution/soft-hardware/7911/section-3-7.png'),
        },
        {
          alt: '串口通信',
          src: require('../../../assets/images/solution/soft-hardware/7911/section-3-8.png'),
        },
        {
          alt: '2.4G wifi',
          src: require('../../../assets/images/solution/soft-hardware/7911/section-3-9.png'),
        },
        {
          alt: '蓝牙5.0',
          src: require('../../../assets/images/solution/soft-hardware/7911/section-3-10.png'),
        },
        {
          alt: '丰富的外设',
          src: require('../../../assets/images/solution/soft-hardware/7911/section-3-11.png'),
        },
        {
          alt: '配网小程序',
          src: require('../../../assets/images/solution/soft-hardware/7911/section-3-12.png'),
        },
      ],
      use_explain: [
        {
          title: '单独使用',
          sub_title:
            '基于套件已有的产品功能，<br/>作为无屏音箱类原型机开发测试使用',
          src: require('../../../assets/images/solution/soft-hardware/7911/section-5-1.png'),
        },
        {
          title: '系统集成使用',
          sub_title:
            '作为产品内部语音处理模块单元， <br/>通过串口实现与上位机的通信，<br/>进行语音交互的功能开发体验和方案验证',
          src: require('../../../assets/images/solution/soft-hardware/7911/section-5-2.png'),
        },
      ],
      product_list: [
        {
          title: '硬件',
          sub_title: '开发套件整机<br/>USB连接线',
          src: require('../../../assets/images/solution/soft-hardware/7911/section-8-1.png'),
        },
        {
          title: '软件',
          sub_title: '前端声学算法<br/>离线命令词<br/>AIUI在线语音交互',
          src: require('../../../assets/images/solution/soft-hardware/7911/section-8-2.png'),
        },
        {
          title: '服务',
          sub_title: '2个月技术支持<br/>50万次在线语音交互授权',
          src: require('../../../assets/images/solution/soft-hardware/7911/section-8-3.png'),
        },
      ],
      service: [
        {
          title: '免费定制服务',
          sub_title:
            '支持自定义云端交互<br/>支持唤醒词定制<br/>支持离线命令词定制',
          src: require('../../../assets/images/solution/soft-hardware/7911/section-9-1.png'),
        },
        {
          title: '增值服务',
          sub_title: '硬件PCBA定制<br/>系统固件及软件应用定制',
          src: require('../../../assets/images/solution/soft-hardware/7911/section-9-2.png'),
        },
      ],
      develop_doc: [
        {
          name: '1.《AC7911 AIUI语音交互开发套件产品白皮书.pdf》',
          link: 'https://aiui-doc.xf-yun.com/project-1/doc-109/',
        },
        {
          name: '2.《AC7911 AIUI语音交互开发套件开发指南.pdf》',
          link: 'https://aiui-doc.xf-yun.com/project-1/doc-111/',
        },
        {
          name: '3.《AC7911 AIUI语音交互开发套件快速体验指南.pdf》',
          link: 'https://aiui-doc.xf-yun.com/project-1/doc-110/',
        },
        {
          name: '4.《AC7911 AIUI语音交互开发套件协议手册.pdf》',
          link: 'https://aiui-doc.xf-yun.com/project-1/doc-112/',
        },
      ],
    }
  },
  methods: {
    toConsole() {
      // 判断是否有百度SEM营销参数，有的话，提交表单页需要带上
      const search = window.location.search
      if (search) {
        window.open(`/solution/apply/21${search}`)
      } else {
        window.open('/solution/apply/21')
      }
    },
    toBuy(){
      window.open('https://www.aifuwus.com/onstage/cmddetail?product_type=4434');
    },
    toDoc(link) {
      window.open(link)
    },
  },
  components: { corp },
}
</script>

<style lang="scss" scoped>
@import '../../../assets/scss/screen-and-lamp.scss';
@media screen and (min-width: 751px) {
  .main-content {
    &-banner {
      background: url(~@A/images/solution/soft-hardware/7911/banner_bg.png)
        center no-repeat;
      background-size: cover;
      height: 500px;
      overflow: hidden;
      width: 100%;
      .banner-text {
        max-width: 1200px;
        color: #fff;
        height: 100%;
        margin: auto;
        .hor-btn {
          display: flex;
          flex-direction: row;
          justify-content: flex-start;

          div:nth-child(2) {
            margin-left: 30px;
          }
        }
        &-button {
          font-size: 16px;
          text-align: center;
          font-weight: 400;
          width: 140px;
          height: 40px;
          line-height: 40px;
          border: none;
          color: #fff;
          cursor: pointer;
          transition: 0.6s;
          background: linear-gradient(0deg, #00b4ff 0%, #2371ff 100%);
          border-radius: 20px;
        }
        &-buy {
          font-size: 16px;
          text-align: center;
          font-weight: 400;
          width: 140px;
          height: 40px;
          line-height: 40px;
          color: #fff;
          cursor: pointer;
          transition: 0.6s;
          background: transparent;
          border-radius: 20px;
          border: 1px solid #ffffff;
        }
        h2 {
          color: #fff;
          padding-top: 148px;
          margin-bottom: 29px;
          font-size: 48px;
          font-weight: 500;
          line-height: 48px;
        }
        p {
          font-size: 18px;
          margin-bottom: 74px;
        }

        .banner-text-content {
          width: 570px;
          font-size: 16px;
          font-weight: 400;
          color: rgba(255, 255, 255, 0.86);
          line-height: 30px;
        }
      }
    }
  }
  .section-title {
    font-size: 34px;
    font-weight: bold;
    color: #333;
    margin-top: 0 !important;
    margin-bottom: 30px !important;
  }
  .section-sub-title {
    font-size: 16px;
    font-weight: 400;
    text-align: center;
    color: #666666;
    margin-bottom: 60px !important;
  }
  .app-text {
    color: #666;
  }
  .section-2 {
    .section-item {
      margin-top: 50px !important;
      > ul {
        display: flex;
        justify-content: space-between;
        li {
          width: 20% !important;
          img {
            width: 100%;
          }
        }
      }
    }
  }
  .section-3 {
    background: url('../../../assets/images/solution/soft-hardware/7911/section-3-bg.png')
      center no-repeat !important;
    background-size: cover !important;
    width: 100%;
    max-width: 100% !important;
    position: relative;
    .section-title {
    }
    ul {
      display: grid;
      grid-template-columns: repeat(6, 1fr);
      grid-gap: 10px;
      img {
        width: 60px;
        height: 60px;
        object-fit: contain;
      }
      .app {
        display: flex;
        flex-direction: column;
        margin-top: 30px;

        .app-text {
          margin-top: 10px;
          font-size: 16px;
          font-weight: 400;
          text-align: center;
          color: #262626;
        }
      }
      li:nth-child(2),
      li:nth-child(4) {
        .section-item-text {
          padding-left: 10%;
        }
      }
    }
  }
  .section-4 {
    background: white !important;
    height: 700px !important;
    width: 100%;
    .section-item {
      background: url('../../../assets/images/solution/soft-hardware/7911/section-4-bg.png')
        center no-repeat !important;
      background-size: contain !important;
      height: 500px;
    }
  }
  .section-5 {
    background: url('../../../assets/images/solution/soft-hardware/7911/section-5-bg.png')
      center no-repeat !important;
    background-size: cover !important;
    width: 100%;
    max-width: 100% !important;
    position: relative;
    .section-title {
      margin-bottom: 0 !important;
    }
    .section-item {
      padding: 20px 30px 10px 30px;
      .use-img {
        display: flex;
        flex-direction: row;
        justify-content: space-evenly;
        align-items: center;
        img:nth-child(1) {
          width: 24%;
        }
        img:nth-child(2) {
          padding: 20px 50px;
          width: 30%;
        }
      }

      .use-text {
        display: flex;
        flex-direction: row;
        justify-content: space-evenly;
        margin-top: 20px;

        div {
          width: 24%;
          p:nth-child(1) {
            font-size: 20px;
            text-align: center;
            font-weight: 600;
            color: #262626;
          }
          p:nth-child(2) {
            font-size: 16px;
            font-weight: 400;
            text-align: center;
            color: #666666;
          }
        }

        div:nth-child(2) {
          margin-top: -10px;
          width: 30%;
        }
      }
    }
  }
  .section-6 {
    .section-item {
      margin-top: 50px;
      background: url('../../../assets/images/solution/soft-hardware/7911/section-6.png')
        center no-repeat !important;
      background-size: cover !important;
      height: 305px;
      width: 100%;
      max-width: 1200px;
      position: relative;
    }
  }
  .section-7 {
    background: url('../../../assets/images/solution/soft-hardware/7911/section-7-bg.png')
      center no-repeat !important;
    background-size: cover !important;
    width: 100%;
    max-width: 100% !important;
    position: relative;
    .section-title {
      margin-bottom: 40px !important;
    }
    .section-item {
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: flex-start;
      .item {
        width: 30%;
        justify-content: flex-start;
        display: flex;
        flex-direction: column;
        align-items: center;
        div {
          width: 100%;
          border-top: 1px solid transparent;
          border-bottom: 1px solid transparent;
          border-image: linear-gradient(
            to right,
            transparent,
            rgba(2, 180, 255, 0.2),
            transparent
          );
          border-image-slice: 1;
          border-left-style: none;
          border-right-style: none;
          background: -webkit-linear-gradient(
            left,
            rgba(2, 180, 255, 0) 0%,
            rgba(2, 180, 255, 0.2) 50%,
            rgba(2, 180, 255, 0) 100%
          );
          border-left: none;
          border-right: none;
          padding: 12px 20px;
          text-align: center;

          p {
            font-size: 14px;
            font-weight: 600;
            text-align: center;
            color: #262626;
            margin-bottom: 0;
          }

          p:nth-child(2) {
            margin-top: 10px;
            font-size: 12px;
            font-weight: 400;
          }
        }

        img {
          margin-top: 20px;
          width: 140px;
        }
      }
    }
  }

  .section-8 {
    .section-item {
      display: flex;
      flex-direction: row;
      justify-content: space-around;
      align-items: center;
      .item {
        text-align: center;
        width: 200px;
        height: 200px;
        img {
          width: 100px;
        }

        p:nth-child(2) {
          margin-top: 30px;
          font-size: 20px;
          font-weight: bold;
          text-align: center;
          color: #262626;
        }

        p:nth-child(3) {
          font-size: 16px;
          font-weight: 400;
          text-align: center;
          color: #666666;
        }
      }
    }
  }

  .section-9 {
    background: url('../../../assets/images/solution/soft-hardware/7911/section-9-bg.png')
      center no-repeat !important;
    background-size: cover !important;
    width: 100%;
    max-width: 100% !important;
    margin-top: 50px !important;
    position: relative;

    .section-item {
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      .item {
        text-align: center;
        margin: 0 100px;
        width: 300px;
        height: 300px;
        background: linear-gradient(
          to bottom,
          rgba(255, 255, 255, 0.4),
          rgba(255, 255, 255, 0)
        );
        border-bottom: none;
        padding-top: 30px;
        position: relative;
        border-radius: 20px 20px 0 0;
        box-shadow: inset 1px 1px 0 0 rgba(255, 255, 255, 0.4),
          inset -1px 1px 0 0 rgba(255, 255, 255, 0.4);

        img {
          width: 75px;
          height: 75px;
        }

        p:nth-child(2) {
          margin-top: 30px;
          font-size: 20px;
          font-weight: bold;
          text-align: center;
          color: #262626;
        }

        p:nth-child(3) {
          font-size: 16px;
          font-weight: 400;
          text-align: center;
          color: #666666;
        }
      }
    }
  }

  .section-10 {
    padding-top: 50px !important;
    .section-item {
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      background: url('../../../assets/images/solution/soft-hardware/7911/section-10-bg.png')
        center no-repeat !important;
      background-size: contain !important;
      height: 240px;
      .item {
        padding-top: 50px;
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        grid-gap: 10px;
      }
    }
  }
}
@media screen and (max-width: 750px) {
  .main-content {
    &-banner {
      background: url('../../../assets/images/solution/soft-hardware/7911/banner_bg.png')
        center no-repeat;
      background-size: cover;
    }
  }
}

.contact-wrap {
  // padding-top: 100px;
  height: 400px;
  text-align: center;
  .title {
    margin-bottom: 16px;
    font-size: 34px;
    color: #333;
    font-weight: bold;
  }
  .desc {
    font-size: 16px;
    color: #666;
    margin-bottom: 50px;
  }
  .apply-btn {
    margin: 60px auto 0;
    background: #1784e9;
    &:hover {
      color: #fff;
    }
  }
}
</style>
