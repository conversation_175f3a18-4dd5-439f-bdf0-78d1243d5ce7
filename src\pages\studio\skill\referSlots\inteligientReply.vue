<template>
  <div>
    <div v-loading="resultData.loading">
      <div
        v-for="(item, index) in list"
        class="utterance-collapse-item"
        :key="index"
      >
        <div class="utterance-collapse-item-title">
          <div class="utterance-area">
            <i class="utterance-area-template ic-brace" />
            <reply-editor
              :utterance="item"
              :subAccountEditable="true"
              @change="refreshData"
            />
            <i class="utterance-area-del" v-if="true" @click="del(item, index)"
              ><i class="ic-r-delete"
            /></i>
          </div>
        </div>
      </div>
    </div>
    <os-pagination
      class="utter-pagination"
      v-model="resultData.page"
      :total="resultData.total"
      :size="resultData.size"
      @change="getList"
    />
  </div>
</template>
<script>
import ReplyEditor from './replyEditor.vue'
export default {
  name: 'inteligient-reply',
  data() {
    return {
      resultData: {
        loading: true,
        total: 0,
        page: 1,
        size: 5,
        list: [],
      },
      list: [],
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    // getList(page) {
    //   let self = this
    //   this.resultData.loading = true
    //   this.$utils.httpGet(
    //     this.$config.api.STUDIO_INTENT_UTTERANCES,
    //     {
    //       businessId: '291518',
    //       intentId: '963274',
    //       pageIndex: page || this.resultData.page || 1,
    //       pageSize: this.resultData.size,
    //       type: 3,
    //       // 纯测试数据使用
    //       slotId: '1544734',
    //     },
    //     {
    //       success: (res) => {
    //         self.resultData.list = res.data.utterances
    //         self.list = []
    //         self.resultData.total = res.data.count
    //         self.resultData.page = res.data.pageIndex
    //         self.resultData.size = res.data.pageSize
    //         self.$nextTick(function () {
    //           self.list = res.data.utterances.slice()
    //         })
    //         self.resultData.loading = false
    //       },
    //       error: (err) => {
    //         self.resultData.loading = false
    //       },
    //     }
    //   )
    // },

    getList() {
      const self = this

      // 搞一个假数据
      const res = {
        code: '0',
        flag: true,
        data: {
          utterances: [
            {
              template: 1,
              id: 14813060,
              utterance: '{datetime1.1}真的会下雨吗',
              mark: [
                { slotName: 'datetime1.1', start: 0, end: 13, sort: 0 },
                { start: 13, end: 19, sort: 1, text: '真的会下雨吗' },
              ],
            },
            {
              template: 1,
              id: 14813058,
              utterance: '我非常喜欢{chinacity}的美丽景色。',
              mark: [
                { start: 0, end: 5, sort: 0, text: '我非常喜欢' },
                { slotName: 'chinacity', start: 5, end: 16, sort: 1 },
                { start: 16, end: 22, sort: 2, text: '的美丽景色。' },
              ],
            },
          ],
          pageIndex: 1,
          count: 2,
          pageSize: 5,
        },
        desc: '操作成功',
      }
      self.resultData.list = res.data.utterances
      self.list = []
      self.resultData.total = res.data.count
      self.resultData.page = res.data.pageIndex
      self.resultData.size = res.data.pageSize
      self.$nextTick(function () {
        self.list = res.data.utterances.slice()
      })
      self.resultData.loading = false
    },

    del(utterance, index) {
      let self = this

      this.$utils.httpPost(
        this.$config.api.STUDIO_INTENT_DEL_UTTERANCE,
        {
          id: utterance.id,
          businessId: this.businessId,
          intentId: this.intentId,
          utterance: this.oldUtterances[index].utterance,
        },
        {
          success: (res) => {
            self.$message.success('删除成功')
            let nowTotal = this.utterancesData.total - 1
            let basePage = nowTotal % this.utterancesData.size ? 1 : 0
            let page =
              parseInt(nowTotal / this.utterancesData.size) + basePage >
              this.utterancesData.page
                ? this.utterancesData.page
                : parseInt(nowTotal / this.utterancesData.size) + basePage
            self.refreshData(page)
          },
          error: (err) => {},
        }
      )
    },

    refreshData() {
      console.log('refreshData called')
    },
  },
  components: { ReplyEditor },
}
</script>
<style lang="scss">
.utterance-collapse-item {
  border: 1px solid $grey2;
  border-bottom: 0;
  transition: all 0.2s;
  &-show {
    border-bottom: 2px solid $primary;
  }
}
.utterance-collapse-item-title {
  position: relative;
  &-del {
    position: absolute;
    right: 16px;
    top: 0;
    bottom: 0;
    margin: auto;
    line-height: 44px;
    cursor: pointer;
    display: none;
  }
  &:hover {
    .utterance-collapse-item-title-del {
      display: block;
    }
  }
}
.utterance-area {
  padding: 11px 27px 11px 11px;
  height: auto;
  min-height: 44px;
  background-color: #fff;
  z-index: 1;
  display: flex;
  align-items: center;
  overflow: hidden;
  &-template {
    color: $grey3;
    margin-right: 20px;
  }
  &-del {
    line-height: 22px;
    display: none;
    cursor: pointer;
    color: $grey4;
    position: absolute;
    right: 10px;
  }
  &:hover {
    .mark1,
    .mark2,
    .utterance-area-del {
      display: block;
    }
  }
}
.utterance-area-disabled {
  background-color: #f2f5f7;
}
.utter-pagination {
  margin: 16px auto 28px;
  text-align: center;
}
</style>
