<template>
  <div
    class="os-aiui-header"
    :class="{
      'os-aiui-header-fixed': headerFixed,
      'os-aiui-header-none-access': isNotAccess || isMessage,
    }"
    v-show="userAgent === 'pc'"
  >
    <div class="container">
      <div class="container-logo">
        <a
          @click="toPage('/')"
          :class="{ 'pointer-no-events': subAccountInfo }"
        >
          <img :src="IconLogo" />
        </a>
      </div>

      <div class="container-navs" :style="'flex-grow: 1'">
        <template v-if="!subAccountInfo">
          <a
            :class="[
              { 'router-link-active': pathname === '/' && domHeight == 0 },
            ]"
            @click="toBlankPage('/')"
            >首页</a
          >
          <a
            :class="[
              { 'router-link-active': isActive },
              { active: domHeight > 0 && activeMenu == 0 },
            ]"
            @[dynamicEvent]="showMenuPanel(0)"
            @mouseleave="hideMenuPanel"
          >
            <span>平台能力</span>
            <i
              :class="{
                'el-icon-arrow-down': true,
                rotate180: domHeight > 0 && activeMenu == 0,
              }"
              @click.stop="toggleMenuPanel(0)"
            ></i>
          </a>
          <a
            :class="[
              { 'router-link-active': isIndustryActive },
              { active: domHeight > 0 && activeMenu == 1 },
            ]"
            @[dynamicEvent]="showMenuPanel(1)"
            @mouseleave="hideMenuPanel"
          >
            <span>行业方案</span>
            <i
              :class="{
                'el-icon-arrow-down': true,
                rotate180: domHeight > 0 && activeMenu == 1,
              }"
              @click.stop="toggleMenuPanel(1)"
            ></i>
          </a>
          <a
            :class="[
              {
                'router-link-active':
                  this.$route.path.match('/modelExperience'),
              },
            ]"
            @click="toBlankPage('/modelExperience')"
            >交互体验</a
          ></template
        >

        <a
          style="position: relative"
          :class="[
            {
              'router-link-active':
                pathname.includes('/app') ||
                pathname.includes('/studio') ||
                pathname.includes('/sub/apps') ||
                pathname.includes('/sub/skills'),
            },
            { active: domMenuHeight > 0 },
          ]"
          @[dynamicEvent]="showDropdownMenuPanel"
          @mouseleave="hideDropdownMenuPanel"
        >
          <span>产品接入</span>
          <i
            :class="{
              'el-icon-arrow-down': true,
              rotate180: domMenuHeight > 0,
            }"
            @click.stop="toggleDropdownMenuPanel"
          ></i>
          <div
            class="container-dropdown-menu"
            :style="{
              height: domMenuHeight + 'px',
              display: domMenuHeight ? 'inline-block' : 'none',
            }"
            @mouseenter="showDropdownMenuPanel"
            @mouseleave="hideDropdownMenuPanel"
          >
            <div class="sub-menu">
              <ul>
                <li>
                  <img
                    :src="require('../assets/images/home/<USER>/app.png')"
                    alt=""
                  />
                  <a
                    @click="toBlankPage(!subAccountInfo ? '/app' : '/sub/apps')"
                    >我的应用</a
                  >
                </li>
                <li>
                  <img
                    :src="require('../assets/images/home/<USER>/setting.png')"
                    alt=""
                  /><a
                    @click="
                      toBlankPage(
                        !subAccountInfo ? '/studio/qaBank' : '/sub/skills'
                      )
                    "
                    >自定义业务</a
                  >
                </li>
              </ul>
            </div>
          </div>
        </a>
        <a
          :href="`${this.$config.docs}doc-1/`"
          target="_blank"
          v-if="!subAccountInfo"
          >文档中心</a
        >
        <a
          style="position: relative"
          v-if="subAccountInfo"
          :class="[
            {
              'router-link-active':
                pathname.includes('/sub/user-info') ||
                pathname.includes('/sub/cooperation/logs'),
            },
            { active: domMenuHeight > 0 },
          ]"
          @mouseenter="showDropdownMenuPanel2"
          @mouseleave="hideDropdownMenuPanel2"
        >
          <span>{{ subAccountInfo.login }}</span>
          <i class="el-icon-arrow-down"></i>
          <div
            class="container-dropdown-menu"
            :style="{
              height: domMenuHeight2 + 'px',
              display: domMenuHeight2 ? 'inline-block' : 'none',
            }"
            @mouseenter="showDropdownMenuPanel2"
            @mouseleave="hideDropdownMenuPanel2"
          >
            <div class="sub-menu">
              <ul>
                <li @click="toPage('/sub/user-info', '', '_blank')">
                  基本资料
                </li>
                <li v-if="hasSkillMenu" @click="tologs">协同操作</li>
                <li @click="toPage('/sub/login')">退出</li>
              </ul>
            </div>
          </div>
        </a>
      </div>

      <div
        class="container-dropdown"
        :style="{ height: domHeight + 'px' }"
        @mouseenter="showMenuPanel"
        @mouseleave="hideMenuPanel"
      >
        <div class="sub-menu">
          <div class="sm-left">
            <div class="title">{{ subMenu[activeMenu].title }}</div>
            <div class="sub-title">{{ subMenu[activeMenu].subTitle }}</div>
          </div>
          <div class="sm-middle">
            <div
              class="dropdown-container"
              v-for="(item, index) in subMenu[activeMenu].content"
              :key="index"
            >
              <li>
                <span class="dropdown-container-title">{{ item.title }}</span>
                <span class="line"></span>
              </li>
              <ul class="dropdown-content">
                <a
                  v-for="(it, i) in item.content"
                  :key="i"
                  @click="onTitleClick(it.link)"
                >
                  <li>
                    {{ it.title }} <span>{{ it.tip }}</span>
                  </li>
                  <li class="sub">{{ it.subTitle }}</li>
                </a>
              </ul>
            </div>
            <div class="dropdown-close" @click="hideMenuPanelImediately">×</div>
          </div>
          <div class="sm-right"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import IconLogo from '@A/images/aiui/logo_aiui.png'
import { transJumpPageUrl } from '@U/transJumpPageUrl.js'
import api from '@U/api.js'
import utils from '@A/lib/utils.js'

let hideMenuTimeout = null

export default {
  name: 'aiuiHeader',
  props: {
    headerFixed: {
      type: Boolean,
      default: false,
    },
    noneAccess: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      routerIsAccess: false,
      isMessage: false,
      isShowGiftMenu: false,
      IconLogo,
      headLoading: true,
      domHeight: 0,
      domMenuHeight: 0,
      domMenuHeight2: 0,
      activeMenu: 0,
      subMenu: [
        {
          title: '平台能力',
          subTitle: '全链路人机交互能力硬件模组快速接入支持业务自由定制',
          content: [
            {
              title: '通用能力',
              content: [
                {
                  title: '多模态交互',
                  subTitle: '视频输入、极速响应',
                  link: '/solution/multimodal-interaction',
                  tip: 'HOT',
                },
                {
                  title: '虚拟人交互',
                  subTitle: '实时的对话动作互动',
                  link: '/solution/meta-human',
                  tip: 'HOT',
                },
                {
                  title: '虚拟人形象资产',
                  subTitle: '丰富形象、个性化定制',
                  link: '/solution/metahuman-image',
                  // tip: 'NEW',
                },
                {
                  title: '免唤醒语音交互',
                  subTitle: '适用无网、弱网环境',
                  link: '/solution/wakeup',
                },
                {
                  title: '离线语音交互',
                  subTitle: '适用无网、弱网环境',
                  link: '/solution/offline',
                },
                {
                  title: '多模态降噪',
                  subTitle: '适用公共高噪环境',
                  link: '/solution/multimodality',
                  tip: 'HOT',
                },
                {
                  title: '低功耗唤醒',
                  subTitle: '适用于无电源设备',
                  link: '/solution/three-wakeup',
                },
              ],
            },
            {
              title: '硬件模组',
              content: [
                {
                  title: '离线语音识别套件',
                  subTitle: '低成本',
                  link: '/solution/soft-hardware/ZG803',
                },
                {
                  title: 'USB声卡开发套件',
                  subTitle: '接入简单',
                  link: '/solution/soft-hardware/usb-soundcard',
                },
                {
                  title: '降噪开发套件',
                  subTitle: '无需算法适配',
                  link: '/solution/soft-hardware/RK3328',
                },
                {
                  title: '语音交互开发套件',
                  subTitle: '开箱即用',
                  link: '/solution/soft-hardware/RK3328S',
                },
                {
                  title: '多模态交互开发套件',
                  subTitle: '高算力 效果优',
                  link: '/solution/soft-hardware/RK3588',
                  tip: 'NEW',
                },
                {
                  title: '讯飞超脑核心板',
                  subTitle: '边缘计算、人工智能',
                  link: '/solution/super-brain-core',
                  tip: 'NEW',
                },
                {
                  title: '讯飞超脑板',
                  subTitle: '机器人嵌入式终端主板',
                  link: '/solution/super-brain-ifly',
                  tip: 'NEW',
                },
                {
                  title: '大模型离线交互套件',
                  subTitle: '全链路离线交互',
                  link: '/solution/offline-interaction',
                  tip: 'NEW',
                },

                // {
                //   title: '讯飞智能台历',
                //   subTitle: '支持定制二次开发',
                //   link: ''
                // }, {
                //   title: '大屏调度演示器',
                //   subTitle: '低成本',
                //   link: ''
                // }
              ],
            },
            {
              title: '语义技能',
              content: [
                {
                  title: '技能商店',
                  subTitle: '覆盖常用场景及内容资源',
                  link: '/store/all',
                },
              ],
            },
          ],
        },
        {
          title: '行业方案',
          subTitle: '针对行业制定软硬件方案，赋能人机交互场景化落地',
          content: [
            {
              title: '智能硬件',
              content: [
                {
                  title: '儿童玩具',
                  subTitle: '情感陪伴、心智启蒙',
                  link: '/solution/child-toys',
                  tip: 'HOT',
                },
                {
                  title: '儿童教育',
                  subTitle: '有料、有趣',
                  link: '/solution/child-education',
                },

                {
                  title: '语音点歌',
                  subTitle: '免唤醒点播',
                  link: '/solution/ktv',
                },

                {
                  title: '智能投影',
                  subTitle: '定向打磨影视领域',
                  link: '/solution/reflection',
                },
                {
                  title: '智能鼠标',
                  subTitle: '提升办公效率',
                  link: '/solution/mouse',
                },
                {
                  title: '多语种AI透明屏',
                  subTitle: '服务窗口跨语言沟通',
                  link: '/solution/transparent-screen',
                  tip: 'HOT',
                },
                {
                  title: '移动数字人',
                  subTitle: '智能导览',
                  link: '/solution/mobile-digital-person',
                  tip: 'NEW',
                },
                //  {
                //   title: '智能眼镜',
                //   subTitle: '适用公共高噪环境',
                //   link: ''
                // },
                //  {
                //   title: '智能家居',
                //   subTitle: '交互效果全面升级',
                //   link: ''
                // }, {
                //   title: '扫地机',
                //   subTitle: '兼容三方虚拟人',
                //   link: ''
                // }
              ],
            },
            {
              title: '企业助手',
              content: [
                //   {
                //   title: '企业知识问答',
                //   subTitle: '交互更自由',
                //   link: ''
                // }, {
                //   title: '企业知识培训',
                //   subTitle: '适用无网、弱网环境',
                //   link: '',
                //   tip: 'HOT'
                // }, {
                //   title: '企业数据分析',
                //   subTitle: '适用公共高噪环境',
                //   link: ''
                // },
                {
                  title: '大屏调度',
                  subTitle: '复杂页面一语直达',
                  link: '/solution/screen',
                },
              ],
            },
            {
              title: '机器人',
              content: [
                {
                  title: '服务机器人',
                  subTitle: '多模态交互更拟人',
                  link: '/solution/robot',
                },
                {
                  title: '营销机器人',
                  subTitle: '大模型零售',
                  link: '/solution/smart-retail',
                },
              ],
            },
            {
              title: '智慧交通',
              content: [
                //   {
                //   title: '智慧铁道交通',
                //   subTitle: '一站式语音购票及问询',
                //   link: ''
                // },
                {
                  title: '智慧轨道交通',
                  subTitle: '适用公共高噪环境',
                  link: '/solution/subway',
                },
              ],
            },
            // {
            //   title: '智慧文旅',
            //   content: [{
            //     title: '智慧博物馆',
            //     subTitle: '兼容三方虚拟人',
            //     link: ''
            //   }]
            // }
          ],
        },
      ],
      link: [
        {
          name: [
            '/solution/wakeup',
            '/solution/offline',
            '/solution/multimodality',
            '/solution/three-wakeup',
            '/solution/soft-hardware/ZG803',
            '/solution/soft-hardware/usb-soundcard',
            '/solution/soft-hardware/RK3328',
            '/solution/soft-hardware/7911',
            '/solution/soft-hardware/RK3588',
            '/store/all',
          ],
        },
        {
          name: [
            '/solution/child-education',
            '/solution/ktv',
            '/solution/reflection',
            '/solution/mouse',
            '/solution/screen',
            '/solution/robot',
            '/solution/subway',
          ],
        },
      ],

      userAgent: 'pc',
    }
  },
  computed: {
    ...mapGetters({
      userInfo: 'user/userInfo',
      messageUnRead: 'user/messageUnRead',
      subAccountInfo: 'user/subAccountInfo',
      subAccount: 'user/subAccount',
      //showGiftMenu: 'user/showGiftMenu',
      limitCount: 'aiuiApp/limitCount',
      subAccountHasCreateSkillAuth: 'aiuiApp/subAccountHasCreateSkillAuth',
      subAccountSkillAuths: 'aiuiApp/subAccountSkillAuths',
    }),
    hasUserInfo() {
      return this.userInfo || this.subAccountInfo
    },
    hasSkillMenu() {
      return (
        !this.limitCount ||
        !this.limitCount.hasOwnProperty('sub_skill_menu') ||
        this.limitCount['sub_skill_menu'] != '0' ||
        this.subAccountHasCreateSkillAuth ||
        Object.keys(this.subAccountSkillAuths).length !== 0
      )
    },
    isNotAccess: function () {
      return !this.routerIsAccess && !this.headerFixed
    },
    isAbsolute: function () {
      return this.routerIsAccess
    },
    isActive: function () {
      return (
        this.link[0].name.filter((item) => this.$route.path.match(item))
          .length > 0
      )
    },
    isIndustryActive: function () {
      return (
        this.link[1].name.filter((item) => this.$route.path.match(item))
          .length > 0
      )
    },
    dynamicEvent() {
      if (this.$route.name === 'portal') {
        return 'mouseenter'
      } else {
        return 'click'
      }
    },
  },
  created() {
    this.getUserInfo()
    this.pathname = location.pathname
    this.routerIsAccess =
      this.$route.path === '' ||
      this.$route.path === '/' ||
      this.$route.path === '/access' ||
      this.$route.path === '/user/gift'

    this.isMessage =
      this.$route.path === '/message' || this.$route.path === '/message/'

    this.judgeMobile()
  },
  watch: {
    'userInfo.email': function (val) {
      if (val) {
        this.$store.dispatch('aiuiApp/setLimitCount')
      }
    },
  },
  methods: {
    judgeMobile() {
      const isMobile = utils.isMobile()
      if (isMobile) {
        this.userAgent = 'mobile'
      } else {
        this.userAgent = 'pc'
      }
    },
    solutionJump(name) {
      const routeData = this.$router.resolve({
        name,
        query: { ch: 'aiui', way: 'menu' },
      })
      window.open(routeData.href, '_blank')
    },
    solutionJump2(name, anchor) {
      if (anchor) {
        const routeData = this.$router.resolve({
          path: `/solution/soft-hardware?ch=aiui&way=menu#${anchor}`,
        })
        window.open(routeData.href, '_blank')
      } else {
        const routeData = this.$router.resolve({
          name,
          query: { ch: 'aiui', way: 'menu' },
        })
        window.open(routeData.href, '_blank')
      }
    },
    getUserInfo() {
      let userInoFn = 'user/setUserInfo',
        loginPath = null
      if (
        (this.$route.name && this.$route.name.indexOf('sub-') !== -1) ||
        (this.$route.path && this.$route.path.indexOf('sub/') !== -1)
      ) {
        this.$store.dispatch('user/setAccountType', 'sub')
        userInoFn = 'user/setSubAccountInfo'
        loginPath = `${location.origin}/sub/login?pageFrom=${location.href}`
      } else {
        this.$store.dispatch('user/setAccountType', 'main')
        userInoFn = 'user/setUserInfo'
        loginPath = `${location.origin}/user/login?pageFrom=${location.href}`
      }
      api
        .userInfo()
        .then((res) => {
          this.headLoading = false
          if (res.status === 200 && res.data.flag) {
            this.$store.dispatch(userInoFn, res.data.data)
            !this.subAccount &&
              this.userInfo &&
              this.$store.dispatch('user/setMessageUnReadCount')
            this.userInfo && this.$store.dispatch('aiuiApp/setLimitCount')
            this.subAccountInfo && this.$store.dispatch('aiuiApp/setLimitCount')
            this.subAccountInfo &&
              this.$store.dispatch('aiuiApp/setSubAccountAppAuths')
            this.subAccountInfo &&
              this.$store.dispatch('aiuiApp/setSubAccountMainAccountInfo')
          } else {
          }
        })
        .catch(() => {})
    },
    // 判断是否显示 礼包领取 下拉菜单
    checkGiftShow() {
      this.$utils.httpPost(
        this.$config.api.GIFT_SHOW,
        {},
        {
          success: (res) => {
            if (res.flag) {
              this.isShowGiftMenu = res.data.show
            }
          },
          error: (err) => {},
        }
      )
    },
    toAsk() {
      this.toPage('/ask')
    },
    toOrders() {
      this.toPage('/user/order')
    },
    toCooperation() {
      let routeData = this.$router.resolve({ path: '/cooperation/authorities' })
      window.open(routeData.href, '_blank')
    },
    //子账号
    toUserInfo() {
      let routeData = this.$router.resolve({ path: '/sub/user-info' })
      window.open(routeData.href, '_blank')
    },
    tologs() {
      let routeData = this.$router.resolve({ path: '/sub/cooperation/logs' })
      window.open(routeData.href, '_blank')
    },
    onTitleClick(name) {
      let url = transJumpPageUrl(name, {
        chan: 'AIUI',
        way: 'menu',
      })
      window.open(url, '_blank')
    },
    logout() {
      let self = this
      let data = {
        subSessionId: this.$utils.getCookie('subSessionId'),
        sub_account_id: this.$utils.getCookie('sub_account_id'),
      }
      this.$utils.httpPost(this.$config.api.COOP_SUB_ACCOUNT_LOGOUT, data, {
        success: (res) => {
          if (res.flag) {
            this.$message.success('退出成功')
            this.$router.push({ name: 'user-login' })
          }
        },
        error: (err) => {},
      })
    },
    toPage(path) {
      this.$router.push({ path })
    },
    toBlankPage(path) {
      const routeData = this.$router.resolve({ path })
      window.open(routeData.href, '_blank')
    },
    showMenuPanel(index) {
      this.domMenuHeight = 0
      if (hideMenuTimeout) {
        clearTimeout(hideMenuTimeout)
      }

      if (Number.isFinite(index)) {
        this.activeMenu = index
      }

      this.domHeight = 546
    },
    hideMenuPanel() {
      hideMenuTimeout = setTimeout(() => {
        // TODO: 功能开发完成解开注释
        this.domHeight = 0
      }, 500)
    },
    hideMenuPanelImediately() {
      hideMenuTimeout = setTimeout(() => {
        this.domHeight = 0
      }, 0)
    },
    toggleMenuPanel(val) {
      if (this.domHeight > 0) {
        this.hideMenuPanelImediately()
      } else {
        this.showMenuPanel(val)
      }
    },
    showDropdownMenuPanel() {
      this.domHeight = 0
      if (hideMenuTimeout) {
        clearTimeout(hideMenuTimeout)
      }
      this.domMenuHeight = 96
    },
    hideDropdownMenuPanel() {
      hideMenuTimeout = setTimeout(() => {
        // TODO: 功能开发完成解开注释
        this.domMenuHeight = 0
      }, 500)
    },

    toggleDropdownMenuPanel() {
      if (this.domMenuHeight > 0) {
        this.hideDropdownMenuPanelImediately()
      } else {
        this.showDropdownMenuPanel()
      }
    },

    hideDropdownMenuPanelImediately() {
      hideMenuTimeout = setTimeout(() => {
        // TODO: 功能开发完成解开注释
        this.domMenuHeight = 0
      }, 0)
    },

    showDropdownMenuPanel2() {
      this.domHeight2 = 0
      if (hideMenuTimeout) {
        clearTimeout(hideMenuTimeout)
      }
      this.domMenuHeight2 = 144
    },
    hideDropdownMenuPanel2() {
      hideMenuTimeout = setTimeout(() => {
        // TODO: 功能开发完成解开注释
        this.domMenuHeight2 = 0
      }, 500)
    },
  },
}
</script>

<style lang="scss" scoped>
.os-aiui-header {
  width: 100%;
  height: 64px;
  top: 0;
  position: relative;
  background: #ffffff;
  box-shadow: 0px 2px 12px 0px rgba(0, 0, 0, 0.15);

  z-index: 999;
  transition: all 0.6s linear;

  &-absolute {
    position: absolute;
  }

  &-none-access {
    // background-color: #000;
    background: #fff;
    box-shadow: 0 5px 10px -5px #eee;
  }

  &-fixed {
    position: fixed;
    // background: #000;
    background: #fff;
    box-shadow: 0 5px 10px -5px #eee;
  }

  .container {
    width: 100%;
    height: 100%;
    // padding: 0 16px 0 24px;
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    align-items: center;

    &-logo {
      width: 170px;
      height: 64px;
      display: flex;
      align-items: center;

      a {
        width: 170px;
        height: 31px;
      }

      img {
        width: 170px;
        height: 31px;
      }
    }

    &-navs {
      margin-left: 48px;
      height: 100%;
      display: flex;
      align-items: center;
      z-index: 100;

      a {
        text-align: center;
        line-height: 60px;
        margin: 0 24px;
        font-size: 14px;
        font-family: PingFangSC, PingFangSC-Regular;
        font-weight: 400;
        color: #252b3a;

        &:hover {
          color: $primary;

          // .el-icon-arrow-down {
          //   transform: rotateZ(180deg);
          // }
        }
      }

      .active {
        color: $primary;

        // .el-icon-arrow-down {
        //   transform: rotateZ(180deg);
        // }
      }

      > a {
        display: inline-block;
        white-space: nowrap;
      }

      &-active {
        font-weight: 400;
        // opacity: 1 !important;
        color: #333 !important;
      }
    }

    &-collapse {
      // width: 300px;
      display: flex;
      align-items: center;
      justify-content: flex-end;

      &-login {
        // color: #fff;
        // opacity: 0.85;
        color: #666;
        cursor: pointer;
        // width: 96px;
        margin-right: 19px;
        line-height: 60px;
        font-weight: 500;
        text-align: center;

        &:hover {
          opacity: 1;
        }

        display: flex;
        align-items: center;

        .icon-login {
          width: 20px;
          height: 20px;
          display: inline-block;
          background: url(~@A/images/aiui/main-page/icon-image.png) no-repeat;
          background-position: -61px 0;
          margin-right: 9px;
        }

        > span {
          white-space: nowrap;
        }
      }

      &-register {
        width: 60px;
        background: #1f90fe;
        border-radius: 4px;

        padding: 7px 0;
        color: #fff;
        // background-color: $primary;
        // border-color: $primary;
        text-align: center;
        cursor: pointer;

        &:hover {
          background-color: mix(#fff, #1f90fe, 10%);
        }
      }

      &-email {
        margin-right: 20px;

        .ic-r-email {
          font-size: 20px;
          // color: #fff;
          color: #999;
        }
      }

      &-in,
      &-device {
        color: #999;
        // opacity: 0.85;
        cursor: pointer;
        // width: 70px;
        width: auto;
        line-height: 60px;
        font-weight: 500;
        text-align: left;

        &:hover {
          color: #333;
        }
      }

      &-user,
      &-device {
        position: relative;
        margin-right: 16px;
        // color: #fff;
        color: #252b3a;
        // opacity: 0.85;
        cursor: pointer;
        width: auto;
        line-height: 60px;
        font-weight: 500;
        text-align: left;

        &:hover {
          // opacity: 1;
          color: #333;

          &::before {
            display: block;
          }
        }

        &:after {
          position: absolute;
          content: ' ';
          bottom: 0;
          top: 0;
          right: -16px;
          margin: auto;
          width: 0;
          height: 0;
          border-width: 6px 5px 0px;
          border-style: solid;
          border-color: #999 transparent transparent;
        }
      }

      &-device {
        margin: 0 36px 0 16px;
        font-size: 16px;

        &:after {
          right: -15px;
        }

        > span {
          white-space: nowrap;
        }
      }
    }

    &-dropdown {
      width: 100%;
      min-width: 1200px;
      box-shadow: 0px 12px 11px 0px rgba(0, 0, 0, 0.09);
      position: absolute;
      left: 0;
      top: 64px;
      overflow: hidden;
      transition: height 0.3s;
      background-color: #fff;
      z-index: 9;
      margin: 0 auto;

      &::before {
        content: '';
        width: 100%;
        box-shadow: 0px 2px 4px 1px rgba(0, 0, 0, 0.15);
        position: absolute;
        left: 0;
        top: -1px;
        z-index: 3;
      }

      .sub-menu {
        width: 100%;
        margin: 0 auto;
        height: 100%;
        display: flex;
        justify-content: space-between;
        position: relative;

        .sm-left {
          flex: auto;
          flex-shrink: 0;
          box-sizing: border-box;
          background-color: #fff;
          position: relative;
          width: calc((100% - 996px) / 2 + 102px);
          box-shadow: 6px 0px 24px 0px rgba(62, 117, 251, 0.2);
          z-index: 2;
          padding: 32px 22px;
          display: flex;
          flex-direction: column;
          align-items: flex-end;

          .title {
            width: 182px;
            font-size: 24px;
            font-family: PingFangSC, PingFangSC-Medium;
            font-weight: 500;
            color: #000000;
          }

          .sub-title {
            width: 182px;
            font-size: 14px;
            font-family: PingFangSC, PingFangSC-Regular;
            font-weight: 400;
            color: #8e90a5;
            line-height: 24px;
            margin-top: 24px;
          }
        }

        .sm-right {
          background-color: #f6f8fc;
          width: calc((100% - 996px) / 2 - 102px);
        }

        .sm-middle {
          width: 996px;
          box-sizing: border-box;
          padding: 32px 50px;
          flex-shrink: 0;
          background-color: #f6f8fc;
          position: relative;

          .dropdown-container {
            display: flex;
            flex-direction: column;

            & > li {
              font-size: 14px;
              font-family: PingFangSC, PingFangSC-Regular;
              font-weight: 400;
              color: #8e90a5;
              margin-bottom: 4px;
              display: flex;
              align-items: center;

              .dropdown-container-title {
                display: inline-block;
                width: auto;
              }

              .line {
                display: inline-block;
                flex: 1;
                height: 1px;
                background: #dedede;
                margin-left: 16px;
              }
            }

            .dropdown-content {
              width: 100%;
              display: flex;
              flex-wrap: wrap;
              margin-bottom: 30px;
              padding-left: 70px;
              a {
                width: 20%;
                margin-top: 20px;

                li {
                  font-size: 14px;
                  font-family: PingFangSC, PingFangSC-Medium;
                  font-weight: 500;
                  color: #000000;
                  white-space: nowrap;
                  span {
                    font-size: 14px;
                    font-family: PingFangSC, PingFangSC-Regular;
                    font-weight: 400;
                    color: #ff4a11;
                    margin-left: 2px;
                  }
                }

                .sub {
                  font-weight: 400;
                  color: #8e90a5;
                  margin-top: 4px;
                }
              }
            }

            .dropdown-content-max {
              a {
                width: 33.3%;
              }
            }
          }
        }
      }
    }

    &-dropdown-menu {
      width: 140px;
      height: 0px;
      background: #ffffff;
      transition: height 0.3s;
      z-index: 9;
      box-shadow: 0px 4px 16px 0px rgba(0, 0, 0, 0.15);
      position: absolute;
      left: -30px;
      top: 66px;
      box-sizing: border-box;

      .sub-menu {
        width: 100%;
        height: 100%;
        padding: 10px 16px;

        ul {
          li {
            width: 100%;
            height: 42px;
            line-height: 42px;
            padding: 8px 0;
            color: #000000;
            font-size: 14px;
            display: flex;
            align-items: center;
            font-family: PingFangSC, PingFangSC-Regular;

            a {
              display: inline-block;
              width: auto;
              height: 24px;
              text-align: left;
              margin: 0;
              line-height: 24px;
            }

            img {
              width: 20px;
              height: 20px;
              margin-right: 12px;
            }

            &:hover {
              color: $primary;

              img {
                content: url(../assets/images/home/<USER>/app_active.png);
              }
            }

            &:last-child {
              &:hover {
                img {
                  content: url(../assets/images/home/<USER>/setting_active.png);
                }
              }
            }
          }
        }
      }
    }
  }

  .router-link-active {
    opacity: 1 !important;
    color: $primary !important;
    font-weight: 500 !important;
  }

  .el-badge__content {
    border: none;
    height: 16px;
    line-height: 16px;
  }
}

.el-message {
  z-index: 99999 !important;
}

.pointer-no-events {
  pointer-events: none;
  cursor: default;
}

.dropdown-close {
  position: absolute;
  top: 11px;
  right: 0px;
  cursor: pointer;
  font-size: 40px;
  color: #bababb;
  font-weight: 300;
  &:hover {
    color: #aaa;
  }
}

.rotate180 {
  transform: rotateZ(180deg);
}
</style>
