<template>
  <div>
    <el-form :inline="true" class="search-form">
      <el-form-item label="查询设备：">
        <el-input
          class="search-input"
          v-model="search.search"
          placeholder="请输入设备码查询"
          size="medium"
          @keyup.native.enter="getSearchResult"
          style="width: 200px"
        >
          <i
            slot="suffix"
            class="el-input__icon el-icon-search search-area-btn"
            @click="getSearchResult"
          />
        </el-input>
      </el-form-item>
      <el-form-item label="时间：">
        <date-range @setTime="setTime" :clearable="false"></date-range>
      </el-form-item>
      <el-form-item label="授权类型：">
        <el-select
          placeholder="请选择"
          size="medium"
          v-model="search.authType"
          style="width: 150px"
        >
          <el-option
            :label="item.label"
            :value="item.value"
            :key="item.label"
            v-for="item in authTypes"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" size="medium" @click="getSearchResult"
          >查询</el-button
        >
        <el-button plain @click="exportSnList"
          ><i class="el-icon-download"></i>导出</el-button
        >
      </el-form-item>
    </el-form>

    <div style="color: #ff6300; margin-bottom: 5px">
      共搜索到 {{ tableData.sum }} 个设备信息
    </div>
    <!-- <div style="margin: 40px 0 20px 0">
      <el-button type="primary" size="small">导出ID</el-button>
      <el-button type="text" size="small" @click="selectAll"
        >选择全部</el-button
      >
      <span>已选择 {{ multipleSelection.length }} 条</span>
    </div> -->
    <os-table
      class="app-list-table"
      :tableData="tableData"
      style="margin-bottom: 56px"
      @change="getAppList"
      ref="multipleTable"
    >
      <el-table-column prop="device_id" label="设备信息"> </el-table-column>
      <el-table-column label="注册时间">
        <template slot-scope="scope">
          <!-- {{ $utils.dateFormat(scope.row.createTime) }} -->
          {{ scope.row.time | timeFilter }}
        </template>
      </el-table-column>
      <el-table-column label="设备状态">
        <template slot-scope="scope">
          <span class="dot dot-green" v-if="scope.row.status == '0'"></span>
          <span class="dot dot-yellow" v-if="scope.row.status == '1'"></span>
          <span class="dot dot-red" v-if="scope.row.status == '2'"></span>
          &nbsp;
          {{ scope.row.status | statusFilter }}
        </template>
      </el-table-column>
      <el-table-column label="授权类型">
        <template slot-scope="scope">{{
          scope.row.auth_type | authTypeFilter
        }}</template>
      </el-table-column>
    </os-table>
  </div>
</template>
<script>
import { api } from '../../../../config'
import dateRange from './dateRange'

export default {
  data() {
    return {
      tableData: {
        loading: true,
        total: 0,
        page: 1,
        size: 80,
        list: [],
        sum: 0,
      },
      search: {
        search: '',
        // os: '',
        authType: 'aiui',
        startDate: '',
        endDate: '',
        timeChanged: false,
      },

      multipleSelection: [],

      authTypes: [
        { label: 'AIUI交互', value: 'aiui' },
        // { label: '离线唤醒降噪', value: 'cae' },
        // { label: '离线合成', value: 'tts' },
      ],
    }
  },
  components: { dateRange },
  created() {
    this.getAbilityDataCount()
  },
  computed: {
    appId() {
      return this.$route.params.appId
    },
  },
  watch: {
    'search.timeChanged'(val) {
      if (val) {
        this.getSnList()
      }
    },
  },
  methods: {
    getAbilityDataCount() {
      this.$utils.httpGet(
        this.$config.api.APP_SN_GET_ABILITY_LIST,
        {
          appid: this.appId,
        },
        {
          success: (res) => {
            // this.caeData = res.data.cae
            // this.tts = res.data.tts
            if (res.data && res.data.cae) {
              this.authTypes.push({ label: '离线唤醒降噪', value: 'cae' })
            }
            if (res.data && res.data.tts) {
              this.authTypes.push({ label: '离线合成', value: 'tts' })
            }
          },
          error: (err) => {},
        }
      )
    },
    getSearchResult() {
      this.getAppList(1)
    },
    getAppList(page) {
      this.tableData.page = page
      this.getSnList()
    },
    exportSnList() {
      let param = this.getSearchParam()
      let api = ''
      if (param.authType === 'aiui') {
        param.os = ''
        delete param.authType
        api = this.$config.api.APP_SN_EXPORT_DETAIL
      } else {
        // cae tts
        param.ability = param.authType
        delete param.authType
        api = this.$config.api.APP_SN_ABILITY_EXPORT_DETAIL
      }

      this.$utils.postopen(api, param)
    },
    setTime(start, end) {
      this.search.startDate = start
      this.search.endDate = end
      this.search.timeChanged = true
    },
    getSearchParam() {
      let param = {
        appid: this.$route.params.appId,
        pageIndex: this.tableData.page,
        pageSize: this.tableData.size,
        search: this.search.search.trim(),
        // os: this.search.os,
        authType: this.search.authType,
        startDate: this.search.startDate
          ? `${this.search.startDate} 00:00:00`
          : '',
        endDate: this.search.endDate ? `${this.search.endDate} 23:59:59` : '',
      }

      return param
    },
    getSnList() {
      this.tableData.loading = true
      // 搜索规则：当用户搜索了设备码的时候，无视时间和设备类型，直接展示全部时间段+全部设备类型的内容。
      // 当用户没有输入设备码，按用户选择时间和设备类型来搜索。
      let param = this.getSearchParam()
      let api = ''
      if (param.authType === 'aiui') {
        param.os = ''
        delete param.authType
        api = this.$config.api.APP_SN_SEARCH_LIST
      } else {
        // cae tts
        param.ability = param.authType
        delete param.authType
        api = this.$config.api.APP_SN_SEARCH_ABILITY_LIST
      }

      this.$utils.httpGet(api, param, {
        success: (res) => {
          this.tableData.loading = false
          this.tableData.total = res.data.max * 16
          this.tableData.list = (res.data.list || []).map((item) => {
            return {
              ...item,
              status: item.status ? item.status : '0',
              auth_type: param.authType,
              device_id: item.device_id ? item.device_id : item.sn,
            }
          })
          this.tableData.sum = res.data.count
          this.$nextTick(() => {
            let dom = document.getElementsByClassName('el-pagination__total')[0]
            if (dom) {
              dom.innerHTML = `共 ${res.data.count} 条`
            }
          })
        },
        error: (err) => {
          this.tableData.loading = false
        },
      })
    },

    handleClick(tab, event) {
      console.log(tab, event)
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
      console.log('handleSelectionChange', this.multipleSelection)
    },
    selectAll() {
      console.log(this.$refs.multipleTable)
      this.tableData.list.forEach((row) => {
        this.$refs.multipleTable.toggleRowSelection(row, true)
      })
    },
  },
  filters: {
    statusFilter(val) {
      let statusMap = { 0: '正常使用', 1: '已到期', 2: '已禁用' }
      return statusMap[val] || ''
    },
    timeFilter(val) {
      if (val) {
        let time = val.split('.')
        return time[0]
      } else {
        return '-'
      }
    },
    authTypeFilter(val) {
      let statusMap = { aiui: 'AIUI交互', cae: '离线唤醒降噪', tts: '离线合成' }
      return statusMap[val] || ''
    },
  },
}
</script>
<style lang="scss" scoped>
.online-device-header {
  display: flex;
  margin: 20px 0;
  .online-device-header-item + .online-device-header-item {
    margin-left: 15px;
  }
  :deep(.el-date-editor .el-range-separator) {
    padding: 0 !important;
  }
}

.search-form {
  margin: 10px 0 20px 0;

  :deep(.el-form-item) {
    margin-bottom: 0;
    margin-right: 15px;
  }

  :deep(.el-form-item__label) {
    font-weight: normal;
    padding: 0;
  }

  :deep(.el-date-editor .el-range-separator) {
    padding: 0 !important;
  }
}

.dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  display: inline-block;
  &.dot-green {
    border: 2px solid #18da00;
  }
  &.dot-yellow {
    border: 2px solid #ffa400;
  }
  &.dot-red {
    border: 2px solid #ff5a5a;
  }
}
</style>
