<template>
  <div class="container">
    <div class="ib container-left">
      <img :src="BannerImg" alt="login-register.png" />
    </div>
    <div class="ib container-right">
      <div class="flex" style="margin-bottom: 48px">
        <a href="/index-aiui" target="_self" class="container-right-logo fx1">
          <img class="vt-middle" alt="AIUI" :src="IconLogo" />
        </a>
        <router-link
          :to="{ name: 'user-login' }"
          class="fx1 text-black fs18 txt-al-r"
          style="line-height: 32px"
        >
          登录
        </router-link>
      </div>
      <p class="fs25 text-black ff-medium mgb32">注册</p>
      <!-- <p class="fs14 text-grey mgb40">讯飞云平台及AIUI用户可直接登录</p> -->
      <el-form
        :model="registerForm"
        label-position="top"
        :rules="rules"
        ref="registerForm"
        label-width="100px"
        class="login-reg-form mgb48"
      >
        <!-- 解决浏览器自动填充 start -->
        <input
          type="text"
          name="clear"
          style="position: fixed; bottom: -9999px"
        />
        <input
          type="password"
          name="clear"
          style="position: fixed; bottom: -9999px"
        />
        <!-- 解决浏览器自动填充 end -->
        <el-form-item
          label="账号"
          prop="mobile"
          :rules="[
            { required: true, message: '手机号不能为空', trigger: 'blur' },
            { validator: checkMobile, trigger: 'blur' },
          ]"
        >
          <el-input
            placeholder="手机号"
            v-model="registerForm.mobile"
            class="input-with-select"
            autocomplete="off"
          >
            <el-select v-model="areaCode" slot="prepend" placeholder="请选择">
              <el-option
                v-for="(area, index) in areas"
                :key="index"
                :label="'+' + area.key"
                :value="area.key"
              ></el-option>
            </el-select>
          </el-input>
        </el-form-item>
        <el-form-item
          label="短信验证码"
          prop="code"
          :rules="[
            { required: true, message: '验证码不能为空', trigger: 'blur' },
            { validator: checkCode, trigger: 'blur' },
          ]"
        >
          <el-input
            placeholder="短信验证码"
            v-model="registerForm.code"
            autocomplete="new-password"
          >
            <div
              slot="append"
              class="send-code-btn"
              :class="{ 'send-code-btn-primary': !sendCodeLock }"
              @click="sendCode"
            >
              {{ second ? second + 's' : '发送验证码' }}
            </div>
          </el-input>
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input
            type="password"
            v-model="registerForm.password"
            placeholder="请输入密码"
            autocomplete="new-password"
          ></el-input>
        </el-form-item>
      </el-form>
      <div>
        <el-checkbox v-model="checked" class="mgb16"
          ><div class="text-black">
            <span>我已阅读并接受</span>
            <div>
              <a :href="`${$config.docs}doc-85/`" target="_blank"
                >《AIUI开放平台服务协议》</a
              ><a :href="`${$config.docs}doc-86/`" target="_blank"
                >《AIUI开放平台隐私政策》</a
              >
            </div>
          </div></el-checkbox
        >
      </div>

      <div class="mgb24">
        <el-button class="register-btn" type="primary" @click="submit"
          >注册</el-button
        >
      </div>
    </div>
  </div>
</template>

<script>
import IconLogo from '@A/images/aiui/logo_aiui_black.png'
import BannerImg from '@A/images/banner-login-signup.png'
import areacode from '@M/areacode'
import SSO from '@A/lib/sso/sso.js'
import api from '@U/api.js'

export default {
  name: 'register',
  data() {
    return {
      IconLogo: IconLogo,
      BannerImg: BannerImg,
      areas: areacode.areaCodeList.sort(function (arr1, arr2) {
        return parseInt(arr1.key) - parseInt(arr2.key)
      }),
      areaCode: '86',
      hasReg: true,
      checked: false,
      sendCodeLock: true,
      second: 0,
      registerForm: {
        mobile: '',
        code: '',
        password: '',
      },
      docUrl: `https://doc.iflyos.cn/device/development_agreement.html`,
      rules: {
        // code: [{ required: true, message: '验证码不能为空', trigger: 'blur' }],
        // code: [{ validator: checkCode, trigger: 'blur' }],
        password: [
          { required: true, message: '密码不能为空', trigger: 'blur' },
        ],
      },
      captchaShow: false,
    }
  },
  watch: {
    'registerForm.mobile': function (val, oldVal) {
      this.hasReg = true
    },
    hasReg: function (val, oldVal) {
      if (this.areaCode === '86' && !val) {
        this.sendCodeLock = !this.$utils.regTest(
          this.registerForm.mobile,
          'phone'
        )
      } else {
        this.sendCodeLock = false || val
      }
    },
  },
  created() {
    if (this.$route.query.pageFrom) {
      this.pageFrom = this.$route.query.pageFrom
    }
  },
  methods: {
    checkCode(rule, val, callback) {
      const regex = /^\d{6}$/
      const result = regex.test(val)
      if (!result) {
        callback(new Error('请输入6位数字组成的验证码'))
      } else {
        callback()
      }
    },
    checkMobile(rule, val, callback) {
      let self = this
      if (this.areaCode === '86' && !this.$utils.regTest(val, 'phone')) {
        return callback(new Error('请输入正确的手机号'))
      }
      this.$utils.httpPost(
        'user/register/checkRegisterPhoneOrEmail',
        {
          phone: val,
          formData: true,
        },
        {
          success: (res) => {
            if (res.flag) {
              this.hasReg = false
              callback()
            } else {
              this.hasReg = true
              callback(new Error(res.desc))
            }
          },
          error: (err) => {
            // callback(err.desc)
          },
        }
      )
    },
    sendCode() {
      if (this.sendCodeLock || this.second > 0) {
        return
      }
      this.sendMsg()
    },
    sendMsg() {
      let self = this
      let data = {
        countryCode: self.areaCode,
        mobile: self.registerForm.mobile,
      }
      SSO.getRegisterMobileCode(data, (result) => {
        let res = typeof result === 'string' ? JSON.parse(result) : resut
        if (res.code === 0) {
          self.$message.success('验证码已发送')
          let time = 60
          var timer = null
          self.second = time
          self.sendCodeLock = true
          timer = setInterval(function () {
            self.second = --time
            if (time <= 0) {
              self.sendCodeLock = false
              clearInterval(timer)
            }
          }, 1000)
        } else {
          self.$message.warning(res.desc)
        }
      })
    },
    submit() {
      let self = this
      if (this.hasReg) {
        return
      }
      this.$refs.registerForm.validate((valid) => {
        if (valid) {
          if (!self.checked) {
            return self.$message.warning(
              '请阅读并接受《AIUI开放平台用户服务协议》《AIUI开放平台隐私政策》'
            )
          }
          SSO.register(
            {
              countryCode: self.areaCode,
              mobile: this.registerForm.mobile,
              verifyCode: this.registerForm.code,
              password: this.registerForm.password,
              registerFrom: 'aiui',
            },
            (err) => {
              self.$message.error(err.desc)
            },
            (result) => {
              // 登录成功
              let jump = self.$utils.toPage('/', 'aiui', 'none')
              if (self.pageFrom) {
                jump = self.pageFrom
              }
              SSO.login(
                {
                  accountName: self.registerForm.mobile,
                  accountPwd: self.registerForm.password,
                  jump: jump,
                  isAct: false,
                },
                (err) => {
                  if (err.desc) {
                    self.$message.error(err.desc)
                  }
                },
                (res, cb) => {
                  let msg = self.$message.success('登录成功，正在跳转')
                  api
                    .userInfo()
                    .then((res) => {
                      if (res.status === 200 && res.data.flag) {
                        cb && cb()
                      } else {
                      }
                    })
                    .catch(() => {})
                }
              )
            }
          )
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
    },
  },
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="scss" scoped>
.container {
  width: 100%;
  height: 100%;
  &-left {
    width: 100%;
    height: 100%;
    padding-right: 520px;
    background-color: #0d2a96;
    display: flex;
    align-items: center;
    justify-content: center;
    & img {
      width: 582px;
      height: 582px;
    }
  }
  &-right {
    position: fixed;
    width: 520px;
    height: 100%;
    padding: 40px 80px;
    top: 0;
    right: 0;
    background-color: #fff;
    overflow-y: scroll;
    &-logo {
      align-items: center;
      display: flex;
      & img {
        width: 154px;
      }
    }
  }
  .img-code-btn {
    width: 70px;
    background: #fff;
    text-align: center;
    & img {
      width: 70px;
    }
  }
  .send-code-btn {
    width: 70px;
    background: #fff;
    text-align: center;
    &-primary {
      color: #1784e9;
      cursor: pointer;
    }
  }
  .input-topright-btn {
    position: absolute;
    right: 0;
    top: -26px;
    line-height: 22px;
  }
  .register-btn {
    width: 100%;
    height: 52px;
    font-size: 16px;
  }
}

@media screen and (max-width: 519px) {
  .container {
    width: 100%;
    height: 100%;
    &-left {
      display: none;
    }
    &-right {
      width: 100%;
      height: 100%;
      padding: 20px 24px;
      background-color: #fff;
    }
  }
}
</style>
