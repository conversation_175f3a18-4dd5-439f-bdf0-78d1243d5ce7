<template>
  <div ref="chartWrap" :style="wrapStyle"></div>
</template>

<script>
import echarts from 'echarts'

export default {
  name: 'OsChartBar',
  props: {
    wrapStyle: {
      default: () => ({
        width: '100%',
        height: '400px',
      }),
    },
  },
  data() {
    return {
      chart: null,
    }
  },
  methods: {
    setOption(option) {
      this.chart && this.chart.setOption(option)
    },
  },
  mounted() {
    const self = this
    self.chart = echarts.init(self.$refs.chartWrap)
    window.addEventListener('resize', function () {
      self.chart.resize()
    })
  },
  beforeDestroy() {
    const self = this
    window.removeEventListener('resize', self.chart.resize())
  },
}
</script>

<style scoped></style>
