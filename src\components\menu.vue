<template>
  <div class="os-scroll menu-wrap">
    <el-menu class="os-menu" @open="handleOpen" @close="handleClose">
      <div
        v-for="(type, typeIndex) in menus"
        :key="typeIndex"
        class="each-menu"
      >
        <div
          :class="{
            'os-menu-title': true,
            active: type.menus.find((it) => it.key === routeName),
          }"
        >
          <img
            :src="
              type.menus.find((it) => it.key === routeName)
                ? type.activeIcon
                : type.icon
            "
          />
          <span v-if="type.name">{{ type.name }}</span>
        </div>

        <div
          v-for="(menu, index) in type.menus"
          :key="index"
          class="each-sub-menu"
        >
          <el-submenu v-if="menu.sub" :index="menu.key">
            <template slot="title">
              <i :class="['os-menu-icon', menu.icon]"></i>
              <span>{{ menu.value }}</span>
            </template>
            <el-menu-item-group>
              <el-menu-item
                v-for="(submenu, subindex) in menu.sub"
                :index="submenu.key || submenu.path"
                :key="submenu.index"
                :class="{
                  'os-menu-active': submenu.key
                    ? routeName === submenu.key
                    : routePath === submenu.path,
                }"
                @click="selectMenu(submenu)"
              >
                {{ submenu.value }}
              </el-menu-item>
            </el-menu-item-group>
          </el-submenu>
          <el-menu-item
            v-else
            :key="menu.index"
            :index="menu.key || menu.path"
            :class="{
              'os-menu-active': menu.key
                ? routeName === menu.key
                : routePath === menu.path,
            }"
            :disabled="menuItemDisabled && menu.index !== 0"
            @click="selectMenu(menu)"
          >
            <!-- <i :class="['os-menu-icon', menu.icon]"></i> -->
            <span slot="title">{{ menu.value }}</span>
          </el-menu-item>
        </div>
      </div>
    </el-menu>
  </div>
</template>
<script>
export default {
  name: 'OsMenu',
  props: {
    menus: {
      type: Array,
      default: [],
    },
  },
  data() {
    return {
      routeName: '',
      routePath: '',
    }
  },
  computed: {
    menuItemDisabled() {
      //当应用平台信息为all时，除了‘应用信息’外，其他 menu-item 均不可点击
      // if(this.$route.name != 'app-info') return false
      // let appPlatformNum = this.$store.state.aiuiApp.app.platformNum || ''
      // if(appPlatformNum =='all') {
      //   return true
      // }
      // return false
      return false
    },
  },
  created() {
    let self = this
    if (self.$router.match(location.pathname)) {
      self.routeName = self.$router.match(location.pathname).name
      self.routePath = self.$router.match(location.pathname).path
    }
  },
  mounted() {},
  watch: {
    $route: function (to, from) {
      let self = this
      self.routeName = to.name
      self.routePath = to.path
    },
  },
  methods: {
    selectMenu(menu) {
      let routeData = {}
      if (menu.path) {
        routeData.path = menu.path
      } else {
        routeData.name = menu.key
      }
      if (menu.params) {
        routeData.params = menu.params
      }
      this.$router.push(routeData)
    },
    handleOpen(key, keyPath) {},
    handleClose(key, keyPath) {},
  },
}
</script>

<style lang="scss" scoped>
.menu-wrap {
  height: calc(100% - 96px);
  :deep(.el-menu) {
    background: #f2f3f5;
  }
}

.each-menu {
  margin-bottom: 20px;
}

.each-sub-menu {
  position: relative;
  &::before {
    width: 1px;
    height: 64px;
    background: #bfc5cb;
    content: ' ';
    position: absolute;
    left: 40px;
    top: 0;
    bottom: 0;
    margin: auto;
    z-index: 1;
  }
}

.os-menu-title {
  display: flex;
  align-items: center;
  padding: 0 0 0 24px;
  height: 50px;
  img {
    width: 27px;
    height: 22px;
    margin-right: 6px;
  }
  span {
    display: inline-block;
    font-size: 16px;
    color: #262626;
  }
  &.active {
    background: #dee6f7;
    border-radius: 4px;
    height: 50px;
    span {
      color: $primary;
    }
  }
}
</style>
<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="scss">
.os-menu {
  border-right: 0;
  &-title {
    font-size: 13px;
    padding-left: 24px;
    padding-top: 16px;
    padding-bottom: 8px;
    color: $grey5;
  }
  .el-menu-item,
  .el-submenu__title {
    padding-left: 60px !important;
    font-size: 14px;
  }
  .el-submenu .el-menu-item {
    padding-left: 80px !important;
  }

  .el-menu-item:hover {
    outline: none;
    background-color: unset;
  }

  &-active {
    color: $primary;
    // background-color: $primary-light-10;
    i {
      color: $primary;
    }
    &::before {
      width: 2px;
      height: 21px;
      background: $primary;
      content: ' ';
      position: absolute;
      left: 40px;
      top: 4px;
      bottom: 0;
      margin: auto;
      z-index: 2;
    }
  }
  .os-menu-icon {
    margin-right: 12px;
    width: 32px;
    text-align: center;
    font-size: 32px;
    vertical-align: middle;
  }
}
</style>
