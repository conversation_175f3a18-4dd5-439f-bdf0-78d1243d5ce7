<template>
  <div class="section-industry wow fadeInUp">
    <div class="title">
      <span>行业方案</span>
      <span class="sub-title"
        >针对行业制定软硬件方案，赋能人机交互场景化落地</span
      >
    </div>
    <div class="contain">
      <div
        :class="['option', { active: optionActive === index }]"
        v-for="(item, index) in list"
        :key="index"
        @mouseenter="optionActive = index"
      >
        <div
          :key="optionActive == index ? item.active : item.img"
          v-lazy:background-image="
            optionActive == index ? item.active : item.img
          "
          class="option-inner"
        >
          <div class="option-title">{{ item.title }}</div>
          <div class="option-desc" v-if="optionActive === index">
            {{ item.desc }}
          </div>
          <div class="option-use" v-if="optionActive === index">
            <div
              class="btn"
              v-for="(it, i) in item.use"
              :key="i"
              @click="onBtnClick(it.link)"
            >
              {{ it.title }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { transJumpPageUrl } from '@U/transJumpPageUrl.js'
export default {
  name: 'section-industry',
  data() {
    return {
      optionActive: 0,
      list: [
        {
          title: '智能硬件',
          desc: '覆盖品类齐全 商业模式灵活',
          use: [
            {
              title: '儿童教育',
              link: '/solution/child-education',
            },
            {
              title: '智能投影',
              link: '/solution/reflection',
            },
            {
              title: '语音点歌',
              link: '/solution/ktv',
            },
            {
              title: '智能鼠标',
              link: '/solution/mouse',
            },
          ],
          img: require('@A/images/home/<USER>/card11.png'),
          active: require('@A/images/home/<USER>/card1_active.png'),
        },
        {
          title: '企业助手',
          desc: '帮助企业快速构建多层级关系的知识库，使用自然语言交互',
          use: [
            {
              title: '智能接待',
              link: '/solution/screen',
            },
            {
              title: '智能调度',
              link: '/solution/screen',
            },
            {
              title: '数据分析',
              link: '/solution/screen',
            },
            {
              title: '管理工单',
              link: '/solution/screen',
            },
          ],
          img: require('@A/images/home/<USER>/card21.png'),
          active: require('@A/images/home/<USER>/card2_active.png'),
        },
        {
          title: '机器人/虚拟人',
          desc: '以多模态和大模型为核心，面向下一代虚实结合、软硬一体的机器人解决方案',
          use: [
            {
              title: '多模态感知表达',
              link: '/solution/robot',
            },
            {
              title: '大模型理解决策',
              link: '/solution/robot',
            },
            {
              title: '软硬一体化接入',
              link: '/solution/robot',
            },
          ],
          img: require('@A/images/home/<USER>/card31.png'),
          active: require('@A/images/home/<USER>/card3_active.png'),
        },
        {
          title: '智慧交通',
          desc: '智能化站内服务与远程作坐席中心协同、降本增效，提升乘客智能出行体验',
          use: [
            {
              title: '智能问询',
              link: '/solution/subway',
            },
            {
              title: '远程召援',
              link: '/solution/subway',
            },
            {
              title: '多终端接入',
              link: '/solution/subway',
            },
            {
              title: '数字化运营',
              link: '/solution/subway',
            },
            // {
            //   title: '智慧铁路交通',
            //   link: ''
            // },
          ],
          img: require('@A/images/home/<USER>/card41.png'),
          active: require('@A/images/home/<USER>/card4_active.png'),
        },
        // {
        //   title: '智慧文旅',
        //   desc: '文旅知识讲解导览',
        //   use: ['智慧博物馆'],
        //   img: require("@A/images/home/<USER>/card5.png"),
        //   active: require("@A/images/home/<USER>/card5_active.png")
        // }
      ],
    }
  },
  methods: {
    onBtnClick(name) {
      let url = transJumpPageUrl(name, {
        chan: 'AIUI',
        way: 'card',
      })
      window.open(url, '_blank')
    },
  },
}
</script>
<style lang="scss" scoped>
@import './common.scss';

.section-industry {
  width: auto;
  height: 680px;
  background: linear-gradient(180deg, #e7f4ff, #e4f3ff 41%, #ffffff);

  .title {
    width: 1200px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    padding: 64px 0 56px;

    span {
      font-size: 30px;
      font-family: PingFangSC, PingFangSC-Medium;
      font-weight: 500;
      text-align: justify;
      color: #000000;
    }

    .sub-title {
      font-size: 16px;
      font-family: PingFangSC, PingFangSC-Regular;
      font-weight: 400;
      color: #676a99;
      margin-left: 24px;
    }
  }

  .contain {
    margin-top: 48px;
    width: 1200px;
    margin: 0 auto;
    height: 446px;
    display: flex;

    .option {
      min-width: 271px;
      border: 1px solid #ffffff;
      box-shadow: 0px 6px 24px 0px rgba(62, 187, 251, 0.3),
        0px 2px 4px 0px #d7f6ff inset;
      cursor: pointer;
      transition: min-width 0.5s cubic-bezier(0.05, 0.61, 0.41, 0.95);

      .option-inner {
        padding: 48px 0;
        width: 100%;
        height: 100%;
      }

      .option-title {
        width: 100%;
        font-size: 24px;
        font-family: PingFangSC, PingFangSC-Regular;
        font-weight: 400;
        text-align: justify;
        color: #032340;
        line-height: 33px;
        text-align: center;
      }

      .option-title,
      .option-desc,
      .option-use {
        transition-duration: 0.2s;
        transition-property: all;
        animation: rotating 0.6s linear;
      }
    }

    .active {
      min-width: 400px;

      box-shadow: 0px 6px 24px 0px rgba(62, 187, 251, 0.3);

      .option-inner {
        padding: 48px;
      }

      .option-title {
        text-align: left;
        color: #00fff1;
      }

      .option-desc {
        font-size: 16px;
        font-family: PingFangSC, PingFangSC-Regular;
        font-weight: 400;
        text-align: left;
        color: #ffffff;
        line-height: 22px;
        margin-top: 8px;
        width: 300px;
      }

      .option-use {
        margin-top: 45px;

        .btn {
          width: 108px;
          height: 32px;
          line-height: 30px;
          text-align: center;
          border: 1px solid #9edbff;
          font-size: 14px;
          font-family: PingFangSC, PingFangSC-Regular;
          color: #fff;
          margin-bottom: 8px;

          &:hover {
            background: rgba(0, 255, 241, 0.3);
            border: 1px solid #00fff1;
          }
        }
      }
    }
  }
}

@keyframes rotating {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}
</style>
