<template>
  <div class="main-content">
    <MyHeader> </MyHeader>
    <section class="main-content-banner">
      <div class="banner-text">
        <h2>RK3328 AIUI 降噪开发板</h2>

        <p class="banner-text-content">
          直接输出降噪后音频的多麦克风阵列语音交互解决方案。适用于服务机器人场景
        </p>

        <div class="hor-btn">
          <el-button
            class="banner-text-button"
            @click="toConsole"
            type="primary"
            round
            >合作咨询</el-button
          >
          <el-button class="banner-text-buy" @click="toBuy" round plain
            >立即购买</el-button
          >
        </div>
      </div>
    </section>

    <section ref="sectionNav" :class="['section-nav', isFixed ? 'fixed' : '']">
      <ul class="nav-ul" id="nav-ul">
        <li
          v-for="item in nav_list"
          :key="item.id"
          @click="gotoSelection(item.id)"
        >
          {{ item.name }}
        </li>
      </ul>

      <i
        v-if="nav_list.length > 4"
        :class="[
          'nav-btn',
          nav_flag ? 'el-icon-caret-bottom' : 'el-icon-caret-top',
        ]"
        id="nav-btn"
        @click="handleNavExpand"
      ></i>
    </section>

    <section class="section section1">
      <h2>应用场景</h2>

      <ul class="grid-box">
        <li>
          <div class="group"></div>
          <p>手机助手</p>
        </li>
        <li>
          <div class="group"></div>
          <p>儿童陪伴机</p>
        </li>
        <li>
          <div class="group"></div>
          <p>虚拟人一体机</p>
        </li>
        <li>
          <div class="group"></div>
          <p>机器人</p>
        </li>
        <li>
          <div class="group"></div>
          <p>自助终端</p>
        </li>
      </ul>
    </section>

    <section class="section section2">
      <h2>产品功能</h2>
      <ul>
        <li v-for="item in product_info" :key="item.index">
          <p class="product-title">{{ item.title }}</p>
          <p class="product-desc">· {{ item.desc }}</p>
        </li>
      </ul>
    </section>

    <section class="section section3">
      <h2>产品图片</h2>
      <div class="entity-pic">
        <div class="product">
          <div class="product-pic"></div>
          <div class="product-text">线性驻极体6麦</div>
        </div>

        <div class="product">
          <div class="product-pic"></div>
          <div class="product-text">线性模拟6硅麦</div>
        </div>

        <div class="product">
          <div class="product-pic"></div>
          <div class="product-text">线性模拟4硅麦</div>
        </div>

        <div class="product">
          <div class="product-pic"></div>
          <div class="product-text">线性驻极体4麦</div>
        </div>

        <div class="product">
          <div class="product-pic"></div>
          <div class="product-text">RK3328评估板</div>
        </div>

        <div class="product-flex">
          <div>
            <div class="product-pic-flex1"></div>
            <div class="product-text">环形驻极体6麦</div>
          </div>
          <div>
            <div class="product-pic-flex2"></div>
            <div class="product-text">模拟6硅麦</div>
          </div>
        </div>
      </div>
    </section>

    <section class="section section4">
      <h2>主板接口说明</h2>
      <div class="explain-content">
        <div class="explain-content-pic"></div>
        <ul class="explain-content-list">
          <li v-for="(item, index) in explain_board_list" :key="index">
            <div class="explain-content-list-title">
              {{ 0 + index + 1 }} - {{ item.title }}
            </div>
            <div class="explain-content-list-text">· {{ item.sub_title }}</div>
          </li>
        </ul>

        <i
          class="el-icon-arrow-down expand-arrow"
          v-if="!expandFlag"
          @click="doBoardExpand"
        ></i>
        <i
          class="el-icon-arrow-up shrink-arrow"
          v-else
          @click="doBoardExpand"
        ></i>
      </div>
    </section>

    <section class="section section5">
      <h2>硬件参数</h2>
      <div class="params-content">
        <div class="params-content-pic"></div>
        <ul class="params-content-list">
          <li v-for="(item, index) in hardware_params" :key="index">
            <div class="params-content-list-title">
              {{ item.title }}
            </div>
            <div class="params-content-list-text">
              · &nbsp; {{ item.sub_title }}
            </div>
          </li>
        </ul>

        <i
          class="el-icon-arrow-down expand-arrow"
          v-if="hardware_expand_flag"
          @click="doParmasExpand"
        ></i>
        <i
          class="el-icon-arrow-up shrink-arrow"
          v-else
          @click="doParmasExpand"
        ></i>
      </div>
    </section>

    <section class="section section6">
      <h2>产品清单</h2>
      <div class="product-goods">
        <ul>
          <li>
            <div class="product-goods-title">硬件</div>
            <div class="product-goods-text">
              ·&nbsp;评估板套件 &nbsp;&nbsp; ·&nbsp;麦克风板 &nbsp;&nbsp;
              ·&nbsp;麦克风排线 &nbsp;&nbsp; ·&nbsp;回菜线 &nbsp;&nbsp;
              ·&nbsp;USB线
            </div>
          </li>
          <li>
            <div class="product-goods-title">软件</div>
            <div class="product-goods-text">
              ·&nbsp;唤醒SDK , 集成前端声学算法&nbsp;&nbsp; ·&nbsp;AIUI SDK,
              集成云端交互能力
            </div>
          </li>
          <li>
            <div class="product-goods-title">服务</div>
            <div class="product-goods-text">
              ·&nbsp;为其两个月的VIP技术支持浅定制资源定制支持
            </div>
          </li>
        </ul>
      </div>
    </section>

    <section class="section section7">
      <h2>开发材料</h2>
      <ul>
        <li
          v-for="item in develop_doc"
          :key="item.index"
          @click="toDoc(item.link)"
        >
          {{ item.name }}
        </li>
      </ul>
    </section>

    <section class="section section-cooperation">
      <div class="cooperation-btn" @click="toConsole">合作咨询</div>
    </section>

    <section class="section section-footer">
      <!-- <aiuiMobileFooter> </aiuiMobileFooter> -->
      <div class="footer-title">
        联系我们
        <span class="arrow" @click="clickFooterArrow">▼</span>
      </div>
      <div class="content">
        <p>联系电话：19955108393</p>
        <p>开发者交流群：617903641</p>
        <p>商务技术支持：<EMAIL></p>
      </div>

      <div class="footer-title">
        友情链接
        <span class="arrow" @click="clickFooterArrow">▼</span>
      </div>
      <div class="content">
        <p>科大讯飞</p>
        <p>讯飞开放平台</p>
      </div>

      <div class="corporation-info">
        ©科大讯飞股份有限公司 皖ICP备05001217号
      </div>
    </section>
  </div>
</template>

<script>
import MyHeader from '@P/aiui/solution-aiui/mobile-header.vue'
import '../../../../../../static/vue-awesome-swiper'
import aiuiMobileFooter from '@C/aiuiFooter.vue'
export default {
  name: 'AiuiWebIndex',

  data() {
    return {
      nav_flag: true,

      expandFlag: true,

      hardware_expand_flag: false,

      isFixed: false,

      nav_list: [
        { name: '应用场景', id: 1 },
        { name: '产品功能', id: 2 },
        { name: '产品图片', id: 3 },
        { name: '主板接口说明', id: 4 },
        { name: '硬件参数', id: 5 },
        { name: '产品清单', id: 6 },
        { name: '开发材料', id: 7 },
      ],

      product_info: [
        { title: '录音', desc: '基于麦克风阵列录制原始音频' },
        { title: '降噪', desc: '基于前端声学算法,可以将外部噪音进行过滤' },
        {
          title: '回声消除',
          desc: '可以介入设备本身的参考信号,屏蔽设备本身音频噪音',
        },
        { title: '声源定位', desc: '可以实现定向拾音,过滤波束外噪音' },
        { title: '唤醒', desc: '集成唤醒SDK,让设备开始交互第一步' },
        { title: '离线命令', desc: '可输出命令词对应串口指令,让设备执行操作' },
      ],
      explain_board_list: [
        { title: '扬声器功能', sub_title: '10W4扬声器,PH2.0-4P' },
        { title: '功防/回采音频输入', sub_title: '功放/回声消除输入信号' },
        { title: '麦克风音频输入接口', sub_title: '处理后的麦克风信号' },
        { title: '麦克风阵列接口', sub_title: '4/6麦克风阵列接口' },
        { title: '电源输入接口', sub_title: 'DC12V3A、 DC5.5-2.1或PH-2.0-2P' },
        { title: 'TTL串口', sub_title: 'TTL3.3V 波特率:115200, PH2.0-3P' },
        { title: 'I2S接口', sub_title: 'I2S数字音频输出,PH2.0-4P' },
        { title: '复位按键', sub_title: '按住此键上电进入刷机模式' },
        { title: 'ADB接口', sub_title: 'MicroUSB,连接电脑OTA或调试' },
        { title: 'Debug接口', sub_title: 'Debug调试口' },
      ],

      hardware_params: [
        { title: 'CPU', sub_title: '四核Cortex-A53' },
        { title: '主频', sub_title: '1.5GHz' },
        { title: '操作系统', sub_title: 'Linux' },
        { title: '电压', sub_title: 'DC12V' },
        { title: '电流', sub_title: '典型值0.13A,max0.24A' },
        { title: '扬声器功率', sub_title: '双10W 4Ω' },
        { title: '麦克风', sub_title: '驻极体麦&amp;模拟硅麦' },
        { title: '灵敏度', sub_title: '-32db' },
        { title: '信噪比', sub_title: '驻极体麦74db;模拟硅麦65db' },
        { title: '主板尺寸', sub_title: '80mm*80mm' },
        { title: '工作环境', sub_title: '-10~75℃,相对湿度≤ 80%' },
      ],

      develop_doc: [
        {
          name: '1.《RK3328 AIUI降噪板开发套件产品白皮书.pdf》',
          link: 'https://aiui-doc.xf-yun.com/project-1/doc-104/',
        },
        {
          name: '2.《RK3328 AIUI降噪板开发套件产品规格书.pdf》',
          link: 'https://aiui-doc.xf-yun.com/project-1/doc-107/',
        },
        {
          name: '3.《RK3328 AIUI降噪板开发套件产品使用手册.pdf》',
          link: 'https://aiui-doc.xf-yun.com/project-1/doc-106/',
        },
        {
          name: '4.《RK3328 AIUI降噪板开发套件协议手册.pdf》',
          link: 'https://aiui-doc.xf-yun.com/project-1/doc-108/',
        },
      ],
    }
  },
  components: {
    aiuiMobileFooter,
    MyHeader,
  },

  mounted() {
    window.addEventListener('scroll', this.checkScrollPosition)
    console.log('一进来监听scroll事件')
  },

  beforeDestroy() {
    // 在组件销毁时移除事件监听
    window.removeEventListener('scroll', this.checkScrollPosition)
  },

  methods: {
    toConsole() {
      const search = window.location.search
      if (search) {
        window.open(`/solution/apply/23${search}`)
      } else {
        window.open(`/solution/apply/23`)
      }
    },

    handleNavExpand() {
      const nav_ul = document.getElementById('nav-ul')
      const navIcon = document.getElementById('nav-btn')

      navIcon.addEventListener('click', () => {
        if (this.nav_flag) {
          nav_ul.classList.add('expanded')
        } else {
          nav_ul.classList.remove('expanded')
        }
        this.nav_flag = !this.nav_flag
      })
    },

    gotoSelection(id) {
      const section = document.getElementsByClassName(`section` + id)[0]
      if (section) {
        section.scrollIntoView({ behavior: 'smooth' })
        this.checkScrollPosition()
      }
    },

    toBuy() {
      window.open('https://www.aifuwus.com/onstage/cmddetail?product_type=4436')
    },

    toDoc(link) {
      window.open(link)
    },

    doBoardExpand() {
      document.addEventListener('click', () => {
        const shrink_list =
          document.getElementsByClassName('explain-content')[0]
        if (this.expandFlag) {
          shrink_list.style.maxHeight = '500px'
        } else {
          shrink_list.style.maxHeight = 'none'
        }
        this.expandFlag = !this.expandFlag
      })
    },

    doParmasExpand() {
      document.addEventListener('click', () => {
        const expand_list = document.getElementsByClassName('params-content')[0]
        if (!this.hardware_expand_flag) {
          expand_list.style.maxHeight = '480px'
        } else {
          expand_list.style.maxHeight = 'none'
        }
        this.hardware_expand_flag = !this.hardware_expand_flag
      })
    },

    clickFooterArrow() {
      const titles = document.querySelectorAll('.footer-title')
      titles.forEach((title) => {
        title.addEventListener('click', () => {
          const content = title.nextElementSibling
          const arrow = title.querySelector('.arrow')

          if (content.style.display === 'none' || !content.style.display) {
            content.style.display = 'block'
            arrow.classList.add('up')
          } else {
            content.style.display = 'none'
            arrow.classList.remove('up')
          }
        })
      })
    },

    checkScrollPosition() {
      console.log('checkScrollPosition执行了')
      // 检查滚动位置，判断是否固定导航栏
      const nav = this.$refs.sectionNav
      const navTop = nav.getBoundingClientRect().top
      this.isFixed = window.scrollY > navTop
    },
  },
}
</script>

<style lang="scss" scoped>
.main-content {
  background-color: #f6f7f9;
  max-width: 750px;
  overflow: hidden;
  &-banner {
    background: url(~@A/images/solution/smart-hardware/mobile/banner2.jpg)
      center no-repeat;
    background-size: cover;
    height: 375px;
    overflow: hidden;
    width: 100%;
  }
  .banner-text {
    margin-top: 64px;
    padding-left: 44px;
    h2 {
      font-size: 48px;
      font-weight: 600;
      color: #000000;
      line-height: 68px;
    }
    p {
      width: 648px;
      font-size: 24px;
      font-weight: 300;
      color: #000000;
      line-height: 40px;
      text-align: left;
    }
    .hor-btn {
      display: flex;
      flex-direction: row;
      justify-content: flex-start;
      margin-top: 40px;
      div:nth-child(2) {
        margin-left: 30px;
      }
    }
  }

  .section {
    h2 {
      font-size: 48px;
      font-weight: 600;
      color: #000000;
      line-height: 68px;
      text-align: center;
      margin: 0 auto;
      margin-top: 50px;
      margin-bottom: 30px;
    }
    p,
    ul {
      margin-bottom: 0;
    }
    max-width: 750px;
    overflow: hidden;
    margin: 0 auto;
    .section-title {
      text-align: center;
      font-size: 36px;
      font-weight: 500;
      color: #444444;
      line-height: 54px;
      .arrow {
        width: 26px;
        height: 24px;
        display: inline-block;
        background-repeat: no-repeat;
        background-size: contain;
      }
      .arrow-left {
        background-position: left;
        background-image: url(~@A/images/solution/smart-hardware/arrow-left.png);
      }
      .arrow-right {
        background-position: right;
        background-image: url(~@A/images/solution/smart-hardware/arrow-right.png);
      }
    }
    .section-tabs {
      margin-top: 60px;
    }
  }

  .section-nav {
    background-color: #ffffff;
    position: relative;
    width: 100%;
    border: 1px solid #ddd;
    padding: 0 30px;
    .nav-ul {
      display: flex;
      justify-content: space-evenly;
      flex-wrap: wrap;
      overflow: hidden; /* 默认隐藏超出部分 */
      max-height: 68px; /* 折叠状态时，只显示一行 */
      transition: max-height 0.3s ease; /* 动画过渡效果 */
    }
    .nav-ul.expanded {
      max-height: 200px; /* 展开状态：允许显示多行 */
    }
    .nav-ul li {
      flex: 0 0 auto;
      margin-right: 30px;
      white-space: nowrap;
      cursor: pointer;
      font-size: 24px;
      font-weight: 400;
      text-align: center;
      line-height: 68px;
      color: #7a7a7a;
    }
    .nav-btn {
      position: absolute;
      height: 68px;
      width: 60px;
      right: 0px;
      top: 60%;
      transform: translateY(-50%);
      font-size: 48px;
      // background-color: red;
    }

    // width: 100%;
    // min-height: 68px;
    // background-color: #ffffff;
    // border: 1px solid #e6e6e6;
    // text-align: center;
    // margin: 0 auto;
    // font-size: 24px;
    // font-weight: 400;
    // text-align: center;
    // color: #7a7a7a;
    // line-height: 68px;
    // padding: 0 30px;
    // &-flex {
    //   display: flex;
    //   justify-content: space-evenly;
    //   flex-wrap: wrap;
    //   div {
    //     // margin-right: 30px;
    //     width: 150px;
    //   }
    // }
  }

  // .section-nav.fixed {
  //   position: fixed;
  //   top: 0;
  //   left: 0;
  //   width: 100%;
  //   z-index: 10;
  //   box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1); /* 可选，增加阴影效果 */
  // }

  .section1 {
    padding: 0 60px;
    padding-right: 40px;
    p {
      font-size: 24px;
      line-height: 33px;
      text-align: center;
      margin: 0 auto;
      color: #535963;
      font-weight: 400;
    }

    .grid-box {
      display: flex;
      flex-wrap: wrap;
      margin-top: 26px;
      li {
        width: 192px;
        height: 290px;
        margin-bottom: 20px;
        .group {
          width: 192px;
          height: 235px;
          border: 1px solid #979797;
          border-radius: 16px;
        }
        p {
          height: 36px;
          font-size: 26px;
          line-height: 38px;
          margin: 15px 0px;
        }
      }
      li:nth-child(2) {
        margin: 0px 20px;
      }
      li:nth-child(3) {
        margin-right: 10px;
      }
      li:nth-child(5) {
        margin: 0px 20px;
      }
    }
    ul li:nth-child(1) .group {
      background: url(~@A/images/solution/smart-hardware/mobile/Group1.png)
        center no-repeat;
      background-size: cover;
    }
    ul li:nth-child(2) .group {
      background: url(~@A/images/solution/smart-hardware/mobile/Group2.png)
        center no-repeat;
      background-size: cover;
    }
    ul li:nth-child(3) .group {
      background: url(~@A/images/solution/smart-hardware/mobile/Group3.png)
        center no-repeat;
      background-size: cover;
    }
    ul li:nth-child(4) .group {
      background: url(~@A/images/solution/smart-hardware/mobile/Group4.png)
        center no-repeat;
      background-size: cover;
    }
    ul li:nth-child(5) .group {
      background: url(~@A/images/solution/smart-hardware/mobile/Group5.png)
        center no-repeat;
      background-size: cover;
    }
  }

  .section2 {
    padding-left: 36px;
    padding-right: 28px;
    ul {
      margin-top: 20px;
    }
    li {
      width: 686px;
      // height: 311px;
      background: linear-gradient(180deg, #ffffff, #ffffff);
      border-radius: 31px;
      padding: 26px 56px;
      margin-bottom: 20px;

      .product-title {
        text-align: left;
        height: 42px;
        font-size: 32px;
        margin-bottom: 20px;
      }
      .product-desc {
        font-size: 26px;
        font-weight: 400;
        text-align: left;
        color: #999999;
        line-height: 40px;
      }
    }
  }

  .section3 {
    padding: 0 26px;
    width: 683px;
    h2 {
      font-size: 48px;
      font-weight: 600;
      color: #000000;
      line-height: 68px;
      text-align: center;
      margin: 36px auto;
    }
    .entity-pic {
      width: 683px;
      height: 1342px;
      background: url(~@A/images/solution/smart-hardware/mobile/product_bg.jpg)
        center no-repeat;
      .product {
        margin: 0 auto;
        .product-pic {
          margin: 0 auto;
        }
        .product-text {
          font-size: 24px;
          margin: 0 auto;
          text-align: center;
          color: #ffffff;
          line-height: 42px;
          font-weight: 400;
          margin-bottom: 15px;
        }
      }
      .product:nth-child(1) .product-pic {
        width: 666px;
        height: 99px;
        background: url(~@A/images/solution/smart-hardware/mobile/product_14.png)
          center no-repeat;
        background-size: cover;
      }
      .product:nth-child(2) .product-pic {
        width: 665px;
        height: 97px;
        background: url(~@A/images/solution/smart-hardware/mobile/product_08.png)
          center no-repeat;
        background-size: cover;
      }
      .product:nth-child(3) .product-pic {
        width: 526px;
        height: 96px;
        background: url(~@A/images/solution/smart-hardware/mobile/product_09.png)
          center no-repeat;
        background-size: cover;
      }
      .product:nth-child(4) .product-pic {
        width: 536px;
        height: 99px;
        background: url(~@A/images/solution/smart-hardware/mobile/product_15.png)
          center no-repeat;
        background-size: cover;
      }
      .product:nth-child(5) .product-pic {
        width: 432px;
        height: 281px;
        background: url(~@A/images/solution/smart-hardware/mobile/product_20.png)
          center no-repeat;
        background-size: cover;
      }
      .product-flex {
        width: 100%;
        display: flex;
        justify-content: space-evenly;
        .product-text {
          font-size: 24px;
          margin: 0 auto;
          text-align: center;
          color: #ffffff;
          line-height: 42px;
          font-weight: 400;
          margin-bottom: 15px;
        }
        .product-pic-flex1 {
          background: url(~@A/images/solution/smart-hardware/mobile/product_22.png)
            center no-repeat;
          background-size: cover;
          width: 277px;
          height: 276px;
        }
        .product-pic-flex2 {
          background: url(~@A/images/solution/smart-hardware/mobile/product_23.png)
            center no-repeat;
          background-size: cover;
          width: 275px;
          height: 281px;
        }
      }
    }
  }

  .section4 {
    padding-left: 36px;
    padding-right: 28px;
    width: 700px;

    .explain-content {
      position: relative;
      width: 100%;
      min-height: 1020px;
      background-color: #fff;
      padding-top: 33px;
      padding-left: 32px;
      padding-right: 43px;
      background: linear-gradient(180deg, #ffffff, #ffffff);
      border: 3px solid #f0f0f0;
      border-radius: 30px;
      box-shadow: 0px 3px 9px 0px rgba(151, 151, 151, 0.06);
      &-pic {
        margin: 0 auto;
        background: url(~@A/images/solution/smart-hardware/mobile/product_spe.png);
        background-size: cover;
        width: 414px;
        height: 365px;
      }

      &-list {
        transition: max-height 0.3s ease;
        li {
          width: 100%;
          height: 124px;
          padding-left: 24px;
          padding-top: 17px;
          box-shadow: 0px -1px 0px 0px #ebebeb inset;
        }
        &-title {
          font-size: 32px;
          line-height: 42px;
          text-align: left;
          color: #000000;
        }
        &-text {
          font-size: 26px;
          font-weight: 400;
          text-align: left;
          color: #999999;
          line-height: 40px;
        }
      }

      .expand-arrow {
        font-size: 40px;
        margin: 0 auto;
        text-align: center;
        position: absolute;
        bottom: -10px;
        left: 50%;
        translate: -50%;
      }
      .shrink-arrow {
        font-size: 40px;
        margin: 0 auto;
        text-align: center;
        position: absolute;
        bottom: 0;
        left: 50%;
        translate: -50%;
      }
    }
  }

  .section5 {
    padding-left: 36px;
    padding-right: 28px;
    width: 700px;
    margin-top: 20px;
    .params-content {
      position: relative;
      width: 100%;
      min-height: 900px;
      background-color: #fff;
      padding-top: 33px;
      padding-left: 32px;
      padding-right: 43px;
      background: linear-gradient(180deg, #ffffff, #ffffff);
      border: 3px solid #f0f0f0;
      border-radius: 30px;
      box-shadow: 0px 3px 9px 0px rgba(151, 151, 151, 0.06);
      &-pic {
        margin: 0 auto;
        background: url(~@A/images/solution/smart-hardware/mobile/product_spe1.png);
        background-size: cover;
        width: 454px;
        height: 345px;
      }

      &-list {
        transition: max-height 0.3s ease;
        li {
          width: 100%;
          height: 124px;
          padding-left: 24px;
          padding-top: 17px;
          box-shadow: 0px -1px 0px 0px #ebebeb inset;
        }
        &-title {
          font-size: 32px;
          line-height: 42px;
          text-align: left;
          color: #000000;
        }
        &-text {
          font-size: 26px;
          font-weight: 400;
          text-align: left;
          color: #999999;
          line-height: 40px;
        }
      }

      .expand-arrow {
        font-size: 40px;
        margin: 0 auto;
        text-align: center;
        position: absolute;
        bottom: -10px;
        left: 50%;
        translate: -50%;
      }
      .shrink-arrow {
        font-size: 40px;
        margin: 0 auto;
        text-align: center;
        position: absolute;
        bottom: 0;
        left: 50%;
        translate: -50%;
      }
    }
  }

  .section6 {
    padding-left: 36px;
    padding-right: 28px;
    width: 700px;
    margin-top: 20px;
    .product-goods {
      width: 100%;
      background-color: #fff;
      padding-top: 33px;
      padding-left: 32px;
      padding-right: 43px;
      background: linear-gradient(180deg, #ffffff, #ffffff);
      border: 3px solid #f0f0f0;
      border-radius: 30px;
      box-shadow: 0px 3px 9px 0px rgba(151, 151, 151, 0.06);
      li {
        width: 100%;
        height: 150px;
        padding-left: 24px;
        padding-right: 30px;
        padding-top: 17px;
        box-shadow: 0px -1px 0px 0px #ebebeb inset;
      }
      &-title {
        font-size: 32px;
        line-height: 42px;
        text-align: left;
        color: #000000;
      }
      &-text {
        font-size: 26px;
        font-weight: 400;
        text-align: left;
        color: #999999;
        line-height: 40px;
      }
    }
  }

  .section7 {
    padding-left: 36px;
    padding-right: 28px;
    margin-top: 20px;
    ul {
      li {
        width: 695px;
        min-height: 94px;
        background: linear-gradient(180deg, #ffffff, #ffffff);
        border-radius: 30px;
        font-size: 26px;
        font-weight: 400;
        text-align: left;
        color: #2470ff;
        line-height: 40px;
        padding: 27px 47px;
        margin-bottom: 20px;
        padding-right: 20px;
      }
    }
  }

  .section-cooperation {
    width: 100%;
    height: 243px;
    .cooperation-btn {
      width: 222px;
      height: 81px;
      background: linear-gradient(90deg, #26bcf6, #1b7bf7 98%);
      border-radius: 52px;
      text-align: center;
      margin: 0 auto;
      font-weight: 600;
      line-height: 81px;
      font-size: 32px;
      color: #f0f0f0;
      margin-top: 60px;
    }
  }

  .section-footer {
    width: 750px;
    background-color: #061930;
    padding: 30px 20px;
    .footer-title {
      color: #c1c1c1;
      display: flex;
      justify-content: space-between;
      align-items: center;
      cursor: pointer;
      font-size: 28px;
      font-weight: bold;
      margin-bottom: 20px;
    }
    .content {
      margin-top: 10px;
      display: none; /* 默认隐藏内容 */
      font-size: 24px;
      color: #7b7e93;
    }

    .content p {
      margin: 5px 0;
      font-size: 24px;
      line-height: 49px;
    }

    .arrow {
      font-size: 26px;
      transition: transform 0.5s ease;
    }

    .arrow.up {
      transform: rotate(180deg);
    }

    .corporation-info {
      margin-top: 30px;
      color: #7b7e93;
      font-size: 24px;
      text-align: left;
      line-height: 33px;
    }
  }
}
</style>
