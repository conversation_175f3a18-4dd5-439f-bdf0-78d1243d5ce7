/**
 * 设备状态本地存储工具
 * 按照用户+appid+scene三个维度存储数据
 */

const STORAGE_KEY_PREFIX = 'device_status_'

/**
 * 生成存储键名
 * @param {string} userId 用户ID
 * @param {string} appId 应用ID
 * @param {string} scene 场景名称
 * @returns {string} 存储键名
 */
function generateStorageKey(userId, appId, scene) {
  return `${STORAGE_KEY_PREFIX}${userId}_${appId}_${scene}`
}

/**
 * 保存设备状态数据到本地存储
 * @param {string} userId 用户ID
 * @param {string} appId 应用ID
 * @param {string} scene 场景名称
 * @param {Array} formFields 表单字段数据
 */
export function saveDeviceStatus(userId, appId, scene, formFields) {
  try {
    const key = generateStorageKey(userId, appId, scene)
    const data = {
      formFields,
      timestamp: Date.now(),
      userId,
      appId,
      scene,
    }
    localStorage.setItem(key, JSON.stringify(data))
    return true
  } catch (error) {
    console.error('保存设备状态数据失败:', error)
    return false
  }
}

/**
 * 从本地存储获取设备状态数据
 * @param {string} userId 用户ID
 * @param {string} appId 应用ID
 * @param {string} scene 场景名称
 * @returns {Array|null} 表单字段数据或null
 */
export function getDeviceStatus(userId, appId, scene) {
  try {
    const key = generateStorageKey(userId, appId, scene)
    const stored = localStorage.getItem(key)
    if (stored) {
      const data = JSON.parse(stored)
      return data.formFields
    }
    return null
  } catch (error) {
    console.error('获取设备状态数据失败:', error)
    return null
  }
}
