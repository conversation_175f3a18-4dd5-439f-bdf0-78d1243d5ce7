<template>
  <div class="dialog_body">
    <div class="tab_container">
      <a
        style="cursor: pointer"
        @click="onCickTab('template')"
        :class="{ active: activeTab === 'template' }"
      >
        模板
      </a>
      <a
        style="cursor: pointer"
        @click="onCickTab('thirdParty')"
        :class="{ active: activeTab === 'thirdParty' }"
      >
        三方
      </a>
      <a
        style="cursor: pointer"
        @click="onCickTab('workflow')"
        :class="{ active: activeTab === 'workflow' }"
      >
        工作流
      </a>
    </div>
    <div class="agent_list_wrapper">
      <!-- 自定义文档问答 -4.5才有 -->
      <div
        class="scroll_wrap"
        v-loading="loading"
        ref="scrollWrap"
        @scroll="handleScroll"
      >
        <!-- 通用可复用的agent_list_container组件 -->
        <div
          class="agent_list_container"
          v-for="(agentGroup, groupIndex) in agentGroups"
          :key="groupIndex"
          :id="`agent_group_${mapGroupIndex[groupIndex]}`"
        >
          <div class="agent_type_title">{{ agentGroup.title }}</div>
          <div class="agent_list">
            <div
              v-for="(item, index) in filteredAgentData(agentGroup.data)"
              class="skill"
              @click="toAgentCard(item)"
              :key="index"
            >
              <div :class="['content_wrap']">
                <div class="skill_info_wrap">
                  <i
                    class="skill-icon"
                    :style="{ backgroundColor: item.color }"
                    >{{ item.pluginName && item.pluginName.substr(0, 1) }}</i
                  >
                  <div class="skill-info">
                    <p class="skill-title" :title="item.pluginName">
                      {{ item.pluginName }}
                    </p>
                  </div>
                </div>

                <div @click.stop class="switch-wrap">
                  <el-switch
                    size="small"
                    :value="item.selected"
                    @change="(val) => onSwitchChange(val, item)"
                  >
                  </el-switch>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- <p class="empty-skill-tips" v-if="clickSearchVal !== ''">暂无搜索结果</p> -->
  </div>
</template>

<script>
export default {
  name: 'AiuiWebAgentList',

  props: {
    agentData: {
      type: Array,
      default() {
        return []
      },
    },
    agentDataCopy: {
      type: Array,
      default() {
        return []
      },
    },
    loading: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      activeTab: 'template', // 默认激活模板tab
      scrollDebounceTimer: null, // 滚动防抖定时器
      isManualTabClick: false, // 标记是否由手动点击触发的tab切换
      manualTabLockTimer: null, // 手动点击锁定计时器
      mapGroupIndex: {
        0: 'template',
        1: 'thirdParty',
        2: 'workflow',
      }, // 映射分组索引到tab名称
    }
  },
  computed: {
    // 将所有agent分组数据聚合到一个数组中，便于循环渲染
    agentGroups() {
      return [
        {
          title: '模板',
          data: this.agentData.filter((item) => item.pluginType === 21),
        },
        {
          title: '三方',
          data: this.agentData.filter((item) => item.pluginType === 20),
        },
        {
          title: '工作流',
          data: this.agentData.filter((item) => item.pluginType === 22),
        },
      ]
    },
  },

  mounted() {
    // 初始化时执行一次滚动检测
    this.$nextTick(() => {
      // 先根据初始滚动位置设置正确的激活标签
      this.initActiveTab()

      // 然后执行一次滚动检测
      this.handleScroll()
    })
  },

  methods: {
    showAgent(item) {
      if (item.official === 1) {
        return item.isShow ? true : false
      } else {
        return true
      }
    },
    toAgentCard(item) {
      window.open(
        `/studio/agent/${item.pluginId}/${item.pluginType}/${item.boardId}`,
        '_blank'
      )
    },
    onSwitchChange(val, item) {
      console.log(val, item.agentId, '智能体的onSwitchChange')
      this.$emit('selectchange', item, val)
    },

    // 初始化激活标签状态
    initActiveTab() {
      // 获取滚动容器的当前滚动位置
      const scrollTop = this.$refs.scrollWrap
        ? this.$refs.scrollWrap.scrollTop
        : 0

      // 如果滚动位置为0或接近顶部，激活模板标签；否则检测当前视图
      if (scrollTop < 20) {
        this.activeTab = 'template'
      } else {
        // 执行一次完整的滚动位置检测
        this.updateActiveTabByScroll(true)
      }
    },

    // 点击tab时滚动到对应区域
    onCickTab(tabKey) {
      // 立即更新激活状态，避免视觉延迟
      this.activeTab = tabKey

      // 设置手动点击标志，防止滚动事件立即覆盖点击设置的状态
      this.isManualTabClick = true

      // 清除之前的锁定计时器
      if (this.manualTabLockTimer) {
        clearTimeout(this.manualTabLockTimer)
      }

      const targetElement = document.getElementById(`agent_group_${tabKey}`)
      if (targetElement) {
        // 修改滚动逻辑，添加适当的偏移量确保标题完全可见
        // 考虑顶部padding和其他可能的元素高度
        const scrollOffset = 140 // 调整为更合适的值，确保标题区域完全可见
        this.$refs.scrollWrap.scrollTo({
          top: targetElement.offsetTop - scrollOffset,
          behavior: 'smooth',
        })

        // 锁定一段时间，防止滚动事件立即覆盖点击设置的状态
        // 平滑滚动大约需要500ms，所以设置为700ms的锁定时间
        this.manualTabLockTimer = setTimeout(() => {
          this.isManualTabClick = false
        }, 700)
      }
    },

    // 监听滚动事件，动态更新activeTab
    handleScroll() {
      // 如果是由手动点击触发的，则不执行滚动更新
      if (this.isManualTabClick) return

      // 添加防抖处理，避免频繁触发
      if (this.scrollDebounceTimer) {
        clearTimeout(this.scrollDebounceTimer)
      }

      this.scrollDebounceTimer = setTimeout(() => {
        this.updateActiveTabByScroll()
      }, 50) // 50ms的防抖延迟
    },

    // 辅助函数：计算元素在滚动容器中的可见程度
    calculateVisibility(element, scrollTop, containerHeight) {
      if (!element) return 0

      // 元素顶部相对于滚动容器的位置
      const elementTop = element.offsetTop - scrollTop
      const elementHeight = element.offsetHeight

      // 计算元素在视窗内的部分
      let visibleHeight = 0

      if (elementTop < 0) {
        // 元素顶部在视窗上方
        visibleHeight = Math.max(0, elementHeight + elementTop)
      } else if (elementTop < containerHeight) {
        // 元素顶部在视窗内
        visibleHeight = Math.min(containerHeight - elementTop, elementHeight)
      }

      // 返回可见百分比
      return visibleHeight / elementHeight
    },

    // 根据滚动位置更新激活的标签
    updateActiveTabByScroll(isInitCall = false) {
      // 如果是由手动点击触发的，则不执行滚动更新
      if (this.isManualTabClick && !isInitCall) return

      // 获取滚动容器的当前滚动位置和大小
      const scrollTop = this.$refs.scrollWrap.scrollTop
      const scrollHeight = this.$refs.scrollWrap.clientHeight
      const scrollContainerHeight = this.$refs.scrollWrap.scrollHeight

      // 检测是否已滚动到顶部或接近顶部(更精确的判断)
      const isAtTop = scrollTop === 0
      const isNearTop = scrollTop <= 20

      // 检测是否已滚动到底部或接近底部
      // 使用更宽松的判断条件，当距离底部50px内时就认为是接近底部
      const isAtBottom = scrollTop + scrollHeight >= scrollContainerHeight - 1
      const isNearBottom =
        scrollTop + scrollHeight >= scrollContainerHeight - 50

      // 获取各个分组的位置
      const templateGroup = document.getElementById('agent_group_template')
      const thirdPartyGroup = document.getElementById('agent_group_thirdParty')
      const workflowGroup = document.getElementById('agent_group_workflow')

      if (!templateGroup || !thirdPartyGroup || !workflowGroup) return

      // 计算各组的可见度
      const templateVisibility = this.calculateVisibility(
        templateGroup,
        scrollTop,
        scrollHeight
      )
      const thirdPartyVisibility = this.calculateVisibility(
        thirdPartyGroup,
        scrollTop,
        scrollHeight
      )
      const workflowVisibility = this.calculateVisibility(
        workflowGroup,
        scrollTop,
        scrollHeight
      )

      // 获取各个组标题在视口中的位置
      const templateTitle = templateGroup.querySelector('.agent_type_title')
      const thirdPartyTitle = thirdPartyGroup.querySelector('.agent_type_title')
      const workflowTitle = workflowGroup.querySelector('.agent_type_title')

      const templateTitleVisible = templateTitle
        ? this.isElementVisibleInViewport(templateTitle, this.$refs.scrollWrap)
        : false
      const thirdPartyTitleVisible = thirdPartyTitle
        ? this.isElementVisibleInViewport(
            thirdPartyTitle,
            this.$refs.scrollWrap
          )
        : false
      const workflowTitleVisible = workflowTitle
        ? this.isElementVisibleInViewport(workflowTitle, this.$refs.scrollWrap)
        : false

      // 最顶部特殊处理：如果在最顶部，直接激活模板标签
      if (isAtTop || isNearTop) {
        if (this.activeTab !== 'template') {
          this.activeTab = 'template'
        }
        return
      }

      // 最底部特殊处理：如果在最底部或接近底部，且能看到工作流内容，直接激活工作流标签
      if ((isAtBottom || isNearBottom) && workflowVisibility > 0) {
        if (this.activeTab !== 'workflow') {
          this.activeTab = 'workflow'
        }
        return
      }

      // 根据可见度和标题可见性判断激活哪个标签
      // 模板标签激活条件
      if (
        (templateVisibility > thirdPartyVisibility &&
          templateVisibility > workflowVisibility &&
          templateVisibility > 0.3) ||
        (templateTitleVisible &&
          !thirdPartyTitleVisible &&
          !workflowTitleVisible)
      ) {
        if (this.activeTab !== 'template') {
          this.activeTab = 'template'
        }
      }
      // 三方标签激活条件
      else if (
        (thirdPartyVisibility > templateVisibility &&
          thirdPartyVisibility > workflowVisibility &&
          thirdPartyVisibility > 0.3) ||
        (thirdPartyTitleVisible &&
          !templateTitleVisible &&
          !workflowTitleVisible) ||
        (templateVisibility < 0.2 &&
          thirdPartyVisibility > 0 &&
          workflowVisibility < 0.2)
      ) {
        if (this.activeTab !== 'thirdParty') {
          this.activeTab = 'thirdParty'
        }
      }
      // 工作流标签激活条件
      else if (
        (workflowVisibility > templateVisibility &&
          workflowVisibility > thirdPartyVisibility &&
          workflowVisibility > 0.3) ||
        (workflowTitleVisible &&
          !templateTitleVisible &&
          !thirdPartyTitleVisible) ||
        (templateVisibility < 0.2 &&
          thirdPartyVisibility < 0.2 &&
          workflowVisibility > 0)
      ) {
        if (this.activeTab !== 'workflow') {
          this.activeTab = 'workflow'
        }
      }
    },

    // 判断元素是否在滚动容器的可视区域内
    isElementVisibleInViewport(element, container) {
      if (!element || !container) return false

      const containerRect = container.getBoundingClientRect()
      const elementRect = element.getBoundingClientRect()

      // 元素顶部在容器可视区域内
      const topVisible =
        elementRect.top >= containerRect.top &&
        elementRect.top < containerRect.bottom

      // 元素底部在容器可视区域内
      const bottomVisible =
        elementRect.bottom > containerRect.top &&
        elementRect.bottom <= containerRect.bottom

      // 元素完全包含容器可视区域
      const elementContainsViewport =
        elementRect.top <= containerRect.top &&
        elementRect.bottom >= containerRect.bottom

      return topVisible || bottomVisible || elementContainsViewport
    },

    filteredAgentData(data) {
      return data.filter((item) => this.showAgent(item))
    },
  },
}
</script>

<style lang="scss" scoped>
@import '../common.scss';

.skill-info {
  width: 60%;
}

.empty-skill-tips {
  color: $grey5;
  margin-top: 80px;
  width: 100%;
  text-align: center;
}
</style>
