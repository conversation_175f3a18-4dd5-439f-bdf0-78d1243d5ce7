@font-face {
  font-family: 'AIUI-myapp-iconfont';
  src: url('../font/AIUI-myapp-iconfont.ttf?pbea4k') format('truetype'),
    url('../font/AIUI-myapp-iconfont.woff?pbea4k') format('woff');
  font-weight: normal;
  font-style: normal;
}

.AIUI-myapp-iconfont {
  font-family: 'AIUI-myapp-iconfont' !important;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// 硬件模组
.ai-myapp-yingjianmozu:before {
  content: '\e61a';
}

// 降噪唤醒算法
.ai-myapp-jiangzaohuanxing:before {
  content: '\e619';
}
// 离在线语音交互
.ai-myapp-lizaixianyuyin:before {
  content: '\e618';
}
// 内容信源
.ai-myapp-neirongxinyuan:before {
  content: '\e616';
}
// 方言及精品发音人
.ai-myapp-fangyanfaying:before {
  content: '\e615';
}
// 私有化部署
.ai-myapp-siyuobushu:before {
  content: '\e617';
}

// 音乐/儿歌
.ai-myapp-yinyueerge:before {
  content: '\e61c';
}

// 影视/电视节目
.ai-myapp-yinshidianshi:before {
  content: '\e61f';
}

// 有声书
.ai-myapp-youshengshu:before {
  content: '\e61e';
}

// 广播电台
.ai-myapp-guangbodiantai:before {
  content: '\e620';
}

// 天气
.ai-myapp-tianqi:before {
  content: '\e61d';
}

// 百科知识
.ai-myapp-kaikezhishi:before {
  content: '\e61b';
}

// 设置
.ai-myapp-setting:before {
  content: '\e65a';
}

// 添加
.ai-myapp-add:before {
  content: '\e634';
}

// 设置2
.ai-myapp-setting2:before {
  content: '\e70f';
}

// 邮件
.ai-myapp-email:before {
  content: '\e649';
}
