<template>
  <div>
    <div class="conf-title item-title" style="margin-top: 0">
      <div style="margin-right: 10px">语音合成</div>
      <!-- <el-switch
        class="mgr16"
        :disabled="!subAccountEditable"
        :value="isOn"
        @change="configSwitch"
      ></el-switch> -->
    </div>
    <p
      class="item-desc"
      v-if="appInfo.platform && appInfo.platform.toLowerCase() !== 'webapi'"
    >
      云端将文本结果合成音频流下发至客户端。仅支持5.5.1036.0000版本后SDK使用。
    </p>
    <div class="form-content">
      <el-form
        ref="form"
        :model="form"
        :disabled="!subAccountEditable"
        label-width="80px"
        label-position="left"
      >
        <el-form-item label="发音人">
          <el-select
            class="select2"
            v-model="search.scene"
            placeholder="请选择场景"
          >
            <el-option
              v-for="item in scene"
              :key="item.key"
              :label="item.label"
              :value="item.label"
              >{{ item.label }}</el-option
            >
          </el-select>
          <el-select
            class="select2"
            v-model="search.age"
            placeholder="请选择年龄"
          >
            <el-option
              v-for="item in age"
              :key="item.key"
              :label="item.label"
              :value="item.label"
              >{{ item.label }}</el-option
            >
          </el-select>
          <el-select
            class="select"
            v-model="form.vcn"
            @change="emitChange"
            :disabled="informantList.length === 0"
            placeholder="请选择发音人"
          >
            <el-option
              v-for="(item, index) in informantList"
              :key="index"
              :label="
                item.name +
                ' (' +
                item.language +
                ')' +
                ' ' +
                (item.auth && item.endTime
                  ? `授权截止时间 ${$options.filters['date'](item.endTime)}`
                  : !item.auth && item.vcn !== initVcn
                  ? '请联系商务(<EMAIL>)开启'
                  : '')
              "
              :value="item.vcn"
              :disabled="!item.auth && item.vcn !== initVcn"
            >
              <span style="float: left">
                <div
                  :style="{ 'background-image': `url(${item.url})` }"
                  class="person-image"
                ></div>
              </span>
              <span style="float: left"
                >{{ item.name }}({{ item.language }})</span
              >
              <span
                v-if="!item.auth && item.vcn !== initVcn"
                class="charge-vcn-tip"
                >请联系商务(<EMAIL>)开启</span
              >
              <span v-if="item.auth && item.endTime" class="charge-vcn-tip"
                >授权截止时间 {{ item.endTime | date }}</span
              >
              <span style="float: right">
                <span class="san-circle" @click.stop="clickSelectOption(item)">
                  <span
                    class="san"
                    :class="item.isPlay ? 'play' : 'pause'"
                  ></span>
                  <span class="er" :class="item.isPlay ? 'play' : 'pause'">
                    <span></span>
                    <span></span>
                  </span>
                </span>
              </span>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="音量">
          <el-slider
            class="slider"
            v-model="form.volume"
            show-input
            @change="emitChange"
          ></el-slider>
        </el-form-item>
        <el-form-item label="语速">
          <el-slider
            class="slider"
            v-model="form.speed"
            show-input
            @change="emitChange"
          ></el-slider>
        </el-form-item>
      </el-form>
      <p class="label">试听文本</p>
      <div>
        <el-input
          type="textarea"
          :rows="3"
          :disabled="!subAccountEditable"
          maxlength="300"
          v-model.trim="formVcn.text"
          class="textarea"
          @input="onInput"
        ></el-input>
      </div>
      <div style="width: 620px">
        <p class="ib listen-desc">若修改配置，请重新点击试听。</p>
        <el-button
          v-if="testStatus === 2"
          type="primary"
          class="fr"
          size="small"
          @click="stop"
          >停止试听</el-button
        >
        <el-button
          v-else
          type="primary"
          class="fr"
          size="small"
          :disabled="!subAccountEditable"
          :loading="testStatus === 1"
          @click="synthetic"
          >试听</el-button
        >
      </div>

      <audio ref="audio" src="" type="audio/ogg"></audio>
      <audio ref="audios"></audio>
    </div>
  </div>
</template>

<script>
// import configItem from './configItem'
import tag from '@/assets/svg/apps/tag.svg'
import { fetchEventSource } from '@microsoft/fetch-event-source'
import TTSRecorder from '@P/aiui/model-experience/tts/TTSRecorder'
import { mapGetters } from 'vuex'

import RECOGNITION_SEMANTIC_SYNTHESIS_State from '@U/AIUIState/RECOGNITION_SEMANTIC_SYNTHESIS_State'
import RECOGNITION_TRANSLATE_SYNTHESIS_State from '@U/AIUIState/RECOGNITION_TRANSLATE_SYNTHESIS_State'
import RECOGNITION_LLM_SEMANTIC_SYNTHESIS_State from '@U/AIUIState/RECOGNITION_LLM_SEMANTIC_SYNTHESIS_State'
import RECOGNITION_SYNTHESIS_State from '@U/AIUIState/RECOGNITION_SYNTHESIS_State'
import RECOGNITION_SEMANTIC_POSTPROCESS_SYNTHESIS_State from '@U/AIUIState/RECOGNITION_SEMANTIC_POSTPROCESS_SYNTHESIS_State'
import RECOGNITION_POSTPROCESS_SYNTHESIS_State from '@U/AIUIState/RECOGNITION_POSTPROCESS_SYNTHESIS_State'
export default {
  name: 'synthesis',
  props: {},
  components: {
    // configItem,
  },
  data() {
    return {
      sysStatus: this.status ? 1 : 0,
      uid: this.$utils.experienceUid(),
      ttsRecorder: null,
      ttsRecorderStatus: 'init',

      inputText: '',
      voiceData: {},
      // contentShow: false,
      change: false,
      informantList: [],
      form: {
        vcn: 'x2_xiaojuan',
        speed: 50,
        volume: 50,
      },
      tag,
      text: '你好，我是小飞，我能听懂你说的话。',
      testStatus: 0, //试听状态 0：初始状态，1：正在合成，2：播放中
      initVcn: '',
      search: {
        scene: '全部场景',
        age: '全部年龄段',
      },
      scene: [
        {
          label: '全部场景',
          key: '0',
        },
        {
          label: '通用场景',
          key: '1',
        },
        {
          label: '方言场景',
          key: '9',
        },
        {
          label: '多语种场景',
          key: '8',
        },
        // {
        //   label: '阅读场景',
        //   key: '2',
        // },
        // {
        //   label: '客服场景',
        //   key: '3',
        // },
        // {
        //   label: '交互场景',
        //   key: '4',
        // },
        // {
        //   label: '新闻场景',
        //   key: '5',
        // },
        // {
        //   label: '小说场景',
        //   key: '6',
        // },
        // {
        //   label: '故事场景',
        //   key: '7',
        // },
        {
          label: '民族语场景',
          key: '10',
        },
      ],
      age: [
        {
          key: '0',
          label: '全部年龄段',
        },
        {
          key: '1',
          label: '女童声',
        },
        {
          key: '2',
          label: '男童声',
        },
        {
          key: '3',
          label: '成年女声',
        },
        {
          key: '4',
          label: '成年男声',
        },
      ],
    }
  },

  methods: {
    onInput(e) {
      this.inputText = e
    },
    clickSelectOption(row) {
      if (row.isPlay) {
        row.isPlay = false
        this.$refs.audios.pause()
      } else {
        this.informantList.forEach((item) => {
          item.isPlay = false
        })
        this.$refs.audios.pause()
        this.$refs.audios.src = row.audio
        this.$refs.audios.play()
        row.isPlay = true
        this.$refs.audios.onended = () => {
          row.isPlay = false
        }
      }
    },
    doClick(item) {
      //this.voiceData = item
    },
    emitChange() {
      // 音色、音量、语速变化
      if (!this.form.vcn || this.form.vcn === '') {
        this.$message.warning('请先选择发音人')
      }
      let data = {
        appid: this.appId,
        sceneId: this.currentScene.sceneBoxId,
        vcn: this.form.vcn,
        speed: this.form.speed,
        volume: this.form.volume,
      }
      let self = this
      this.$utils.httpPost(this.$config.api.AIUI_TTS_SAVETTSCONFIG, data, {
        success: (res) => {
          if (res.flag) {
            //保存成功
          } else {
            this.getTtsConf()
          }
        },
        error: (err) => {
          this.$message.error(err?.desc || '声音保存失败')
          this.getTtsConf()
        },
      })
    },
    configSwitch(val) {
      if (val) {
        this.$store.dispatch('aiuiApp/addSwitches', '8')
      } else {
        this.$store.dispatch('aiuiApp/removeSwitch', '8')
      }
    },
    init() {
      this.getInformantList()
      this.getTtsConf()
    },
    getInformantList() {
      let self = this
      let { scene, age } = this.search
      this.$utils.httpGet(
        this.$config.api.GET_INFORMANTS,
        {
          appid: self.appId,
          scene: scene === '全部场景' ? '' : scene,
          age: age === '全部年龄段' ? '' : age,
          order: '1',
        },
        {
          success: (res) => {
            if (res.flag) {
              res.data.informants.forEach((item) => {
                item.isPlay = false
              })
              self.informantList = res.data.informants
            }
          },
        }
      )
    },
    getTtsConf() {
      let self = this
      let data = {
        appid: this.appId,
        sceneId: this.currentScene.sceneBoxId,
      }
      this.$utils.httpGet(this.$config.api.AIUI_TTS_TTSCONFIG, data, {
        success: (res) => {
          if (res.flag && res.data) {
            self.form = res.data.config
            self.initVcn = self.form.vcn
            self.form.speed = parseInt(res.data.config.speed)
            self.form.volume = parseInt(res.data.config.volume)
          }
        },
      })
    },
    synthetic() {
      let self = this
      if (this.form.vcn === '') {
        this.$message.error('请先选择发音人')
        return
      }
      if (this.text === '') {
        this.$message.error('试听文本不能为空')
        return
      }

      this.ttsRecorder && this.ttsRecorder.resetAudio()
      this.$refs.audio && this.$refs.audio.pause()
      setTimeout(() => {
        this.testStatus = 1
      })

      // 超拟人试听单独处理
      if (this.formVcn.ttsType === 3) {
        // 是超拟人
        this.sendHtsExperience()
      } else {
        let data = {
          appid: this.appId,
          text: this.text,
          vcn: this.form.vcn,
          speed: this.form.speed,
          volume: this.form.volume,
        }

        this.$utils.httpPost(
          this.$config.api.AIUI_TTS_AUDITIONSYNTHESIS,
          data,
          {
            success: (res) => {
              if (res.flag) {
                self.$refs.audio.src =
                  this.$config.server + '/aiui/web' + res.data
                self.$refs.audio.play()
                self.testStatus = 2
                self.$refs.audio.onended = function () {
                  self.testStatus = 0
                }
              } else {
                this.$message.error(res.desc)
              }
            },
          }
        )
      }
    },
    sendHtsExperience() {
      let that = this
      this.ttsRecorder && this.ttsRecorder.resetAudio()
      const baseUrl = '/aiui/web/user/chat'
      let formData = new FormData()
      formData.append('version', 'vtts')
      formData.append('expUid', this.uid)
      formData.append('query', this.text)
      formData.append('vcn', this.form.vcn)
      formData.append('speed', this.form.speed)
      formData.append('volume', this.form.volume)
      formData.append('ttsType', 3)
      try {
        fetchEventSource(baseUrl, {
          method: 'POST',
          openWhenHidden: true,
          body: formData,
          async onopen(response) {
            if (response.ok) {
              console.log('连接了')
              // that.isReplying = true
              that.testStatus = 2
            } else {
            }
          },

          onmessage(event) {
            try {
              const result = JSON.parse(event.data || '{}')
              console.log('result----------------', result)
              // 处理每一条信息
              if (result.code == '300001') {
                that.$message.error('请先登录')
                // that.isReplying = false
                setTimeout(() => {
                  window.location.href = '/user/login'
                }, 2000)
              } else if (result.code == '0') {
                const data = result.data
                that.handleMessage(data)
              } else {
                that.$message.error(result.desc || '未知错误')
                // that.isReplying = false
              }
            } catch (e) {
              console.log(e)
              // that.isReplying = false
            }
          },
          onclose() {
            console.info('断开了')
          },
          onerror(err) {
            console.log('报错了', err)
            // that.isReplying = false
            throw new Error(err)
          },
        })
      } catch (e) {
        console.log('fetchEventSource e', e)
      }
    },
    handleMessage(data) {
      if (data.type === 'tts') {
        if (data.audio) {
          this.ttsRecorder.result(data.audio)
        }
      }
    },
    ttsRecorderStatusCb(status) {
      // console.log('ttsRecorederStaus', status)
      this.ttsRecorderStatus = status
    },
    stop() {
      setTimeout(() => {
        this.ttsRecorder && this.ttsRecorder.resetAudio()
      }, 0)
      this.$refs.audio && this.$refs.audio.pause()

      this.testStatus = 0
    },
    saveTtsConfig() {
      if (!this.informantList.length) {
        this.$message.error('该对应分类没有发音人')
        this.$emit('saveFail')
        return
      }
      if (this.form.vcn === '') {
        this.$message.error('请先选择发音人')
        this.$emit('saveFail')
        return
      }
      let data = {
        appid: this.appId,
        sceneId: this.currentScene.sceneBoxId,
        vcn: this.form.vcn,
        speed: this.form.speed,
        volume: this.form.volume,
      }
      let find = this.informantList.find((item) => item.vcn == data.vcn)
      if (find && !find.auth && find.vcn !== this.initVcn) {
        this.$message.error('合成发音人未授权，请重新选择')
        this.$emit('saveFail')
        return 0
      }

      let self = this
      this.$utils.httpPost(this.$config.api.AIUI_TTS_SAVETTSCONFIG, data, {
        success: (res) => {
          if (res.flag) {
            self.change = false
            self.$emit('saveSuccess')
          } else {
            self.$emit('saveFail')
          }
        },
        error: (err) => {
          self.$emit('saveFail')
        },
      })
    },
  },
  watch: {
    currentScene(scene) {
      if (scene && scene.sceneBoxId) {
        this.init() // 获取配置列表
      }
    },
    search: {
      handler(val) {
        this.form.vcn = ''
        this.getInformantList()
      },
      deep: true,
    },

    ttsRecorderStatus(val) {
      console.log('watch ttsRecorderStatus', val)
      if (val === 'endPlay') {
        this.testStatus = 0
      }
    },
  },
  computed: {
    ...mapGetters({
      currentScene: 'aiuiApp/currentScene',
      appInfo: 'aiuiApp/app',
      context: 'aiuiApp/context',
      subAccountEditable: 'aiuiApp/subAccountEditable',
    }),
    appId() {
      return this.$route.params.appId
    },

    //     RECOGNITION: '1',
    //     RECOGNITION_SEMANTIC: '1,2',
    //     RECOGNITION_SEMANTIC_SYNTHESIS: '1,2,8',
    //     RECOGNITION_TRANSLATE: '1,4',
    //     RECOGNITION_TRANSLATE_SYNTHESIS: '1,4,8',
    //     RECOGNITION_LLM_SEMANTIC: '1,13',
    //     RECOGNITION_LLM_SEMANTIC_SYNTHESIS: '1,13,14',
    //     RECOGNITION_POSTPROCESS: '1,3',
    //     RECOGNITION_SEMANTIC_POSTPROCESS: '1,2,3',
    //     RECOGNITION_SYNTHESIS: '1,8'

    isOn() {
      if (this.currentScene && this.currentScene.point && this.context) {
        const point = this.currentScene.point
        const context = this.context
        if (
          context.isCurrentState(RECOGNITION_SEMANTIC_SYNTHESIS_State) ||
          context.isCurrentState(RECOGNITION_TRANSLATE_SYNTHESIS_State) ||
          context.isCurrentState(RECOGNITION_LLM_SEMANTIC_SYNTHESIS_State) ||
          context.isCurrentState(RECOGNITION_SYNTHESIS_State) ||
          context.isCurrentState(
            RECOGNITION_SEMANTIC_POSTPROCESS_SYNTHESIS_State
          ) ||
          context.isCurrentState(RECOGNITION_POSTPROCESS_SYNTHESIS_State)
        ) {
          return true
        }
      }
      return false
    },

    formVcn() {
      this.voiceData = this.informantList.find(
        (item) => item.vcn == this.form.vcn
      )
      if (!this.voiceData || !this.voiceData.text) {
        this.voiceData = {
          ...this.voiceData,
          text: this.inputText,
        }
      }
      this.text = this.voiceData.text
      return this.voiceData
    },
  },
  created() {
    if (this.currentScene && this.currentScene.sceneBoxId) {
      this.init() // 获取配置列表
    }
    this.ttsRecorder = new TTSRecorder(this.ttsRecorderStatusCb, true)
  },
}
</script>

<style lang="scss" scoped>
@import '../common.scss';
.conf-title {
  display: flex;
  align-items: center;
}

.content {
  width: 620px;
}
.select,
.slider {
  width: 540px;
}
.select2 {
  width: 267px;
  margin-bottom: 5px;
}
.textarea {
  width: 620px;
  margin-bottom: 8px;
}
.label {
  color: $grey5;
  font-weight: 600;
  margin-bottom: 6px;
}
.listen-desc {
  color: $grey5;
}
.charge-vcn-tip {
  margin: 0 10px;
  display: none;
  /*float: right;*/
  color: $grey3;
  font-size: 13px;
}
.san-circle {
  width: 30px;
  height: 30px;
  margin: 0 auto;
  border-radius: 50%;
  background: #1784e9;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  .san.play {
    display: none;
  }
  .span.pause {
    display: inline-block;
  }
  .er.play {
    display: inline-block;
  }
  .er.pause {
    display: none;
  }
  .san {
    width: 0;
    height: 0;
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
    border-top: 10px solid transparent;
    border-bottom: 10px solid #fff;
    border-radius: 3px;
    display: inline-block;
    transform: rotate(90deg);
    position: relative;
    left: 7px;
  }
  .er {
    span {
      display: inline-block;
      width: 4px;
      height: 15px;
      background: #fff;
      margin-top: 10px;
    }
  }
}

.ability-synthesis {
  background: url(~@A/images/aiui5/app/synthesis.png) left/cover no-repeat;
}
.ability-synthesis-closed {
  filter: grayscale(100%);
}

.synthesis-logo {
  background: url(~@A/images/aiui5/app/<EMAIL>) center/contain
    no-repeat;
}

.form-content {
  padding-bottom: 16px;
  :deep(.el-slider__button-wrapper) {
    z-index: 99;
  }
}

.person-image {
  width: 30px;
  height: 30px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}
</style>
<style lang="scss">
.el-select-dropdown__item {
  &:hover {
    .charge-vcn-tip {
      display: inline;
    }
  }
}
</style>
