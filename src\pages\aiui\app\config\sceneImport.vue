<template>
  <div>
    <div class="scene-wrap">
      <!-- <span class="item-title">应用配置</span> -->

      <span class="scene-label">情景模式：</span>

      <el-select
        filterable
        style="width: 160px; margin-right: 10px"
        :value="currentScene.sceneName"
        placeholder="情景模式"
      >
        <el-option
          v-for="(item, index) in sceneList"
          :key="index"
          :label="item.sceneName"
          :value="item.sceneName"
          @click.native="changeScene(item)"
        >
          <span class="fl">{{ item.sceneName }}</span>
          <i
            class="ic-r-delete fr"
            v-if="item.sceneName !== 'main' && subAccountEditable"
            @click.stop="deleteScene(item, index)"
          ></i>
        </el-option>
      </el-select>
      <el-button
        v-if="currentScene.sos !== true"
        type="normal"
        @click="importDialogVisible = true"
        ><svg-icon iconClass="icon-upload" />&nbsp;导入</el-button
      >
      <el-button
        type="normal"
        @click="createSceneVisible"
        v-if="
          currentScene.point && currentScene.point.split(',').indexOf('4') < 0
        "
        ><svg-icon iconClass="icon-add" />&nbsp;添加</el-button
      >
    </div>

    <el-dialog
      title="新增情景模式"
      :visible.sync="dialogVisible"
      @close="$refs['form'].resetFields()"
      width="560px"
      :append-to-body="true"
    >
      <el-form
        ref="form"
        :model="form"
        :rules="formRules"
        label-width="120px"
        onSubmit="return false"
      >
        <el-form-item
          label="情景模式名称"
          prop="sceneName"
          class="scene-edit-wrap"
        >
          <el-input
            type="text"
            class="wild-card-input"
            v-model="form.sceneName"
            auto-complete="off"
            placeholder="长度为1-16个字符，仅支持字母/数字/-"
            @keyup.enter.native="$event.target.blur(), doSubmit('form')"
          ></el-input>
        </el-form-item>
        <!-- <el-form-item prop="point">
          <el-radio-group v-model="form.point">
            <el-radio label="1,2">语音语义</el-radio>
            <el-radio label="1,13">超拟人交互</el-radio>
            <el-radio label="1,4">语音翻译</el-radio>
          </el-radio-group>
        </el-form-item> -->
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="doSubmit('form')">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 导入情景模式 -->
    <el-dialog
      :visible.sync="importDialogVisible"
      width="650px"
      custom-class="import-dialog custom-style-dialog"
      title="请选择要导入的情景模式"
      top="5vh"
      :append-to-body="true"
    >
      <p class="top-tips">导入后与原情景模式应用配置保持一致，支持修改。</p>
      <div class="top-search">
        <el-input
          class="search-area"
          placeholder="搜索"
          size="medium"
          v-model="sceneSearchValInput"
          @keyup.enter.native="handleSceneSearch"
        >
          <i
            slot="suffix"
            class="el-input__icon el-icon-search search-area-btn"
            @click="handleSceneSearch"
          />
        </el-input>
      </div>
      <div class="list-container" v-loading="sceneLoading">
        <el-radio-group v-model="impRadio">
          <ul>
            <li
              v-for="(item, idx) in impList"
              :key="idx"
              :class="impRadio == item ? 'is-active' : ''"
            >
              <el-radio class="radio-item" :label="item">
                <span class="radio-item-appname">
                  {{ item.appName }}
                </span>
                <span style="display: inline-block; width: 100px">
                  {{ item.appid }}
                </span>
                <span
                  style="
                    display: inline-block;
                    min-width: 80px;
                    margin-right: 5px;
                  "
                >
                  {{ item.sceneName }}
                </span>
                <span v-if="item.sceneName.indexOf('_box') !== -1">
                  (测试环境)
                </span>
              </el-radio>
            </li>
          </ul>
        </el-radio-group>
      </div>

      <div class="pagination-container" v-if="pageTotal > pageSize">
        <el-pagination
          background
          layout="prev, pager, next"
          :total="pageTotal"
          :page-size="pageSize"
          :current-page="pageIndex"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="importDialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          @click="submitImpRadio"
          :loading="loading"
          :disabled="!impRadio"
          >确定</el-button
        >
      </span>
    </el-dialog>
    <!-- 情景模式导入二次确认弹窗 -->
    <el-dialog
      title="提示"
      :visible.sync="submitDialogVisible"
      width="520px"
      @closed="toReloadPage"
    >
      <p style="line-height: 22px" class="mgb24">
        已经为你导入了情景模式的配置，应用购买的方言、发音人需要你手动再配置。
      </p>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitDialogVisible = false">
          知道了
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { mapGetters } from 'vuex'

export default {
  data() {
    const checkKeyWord = (rule, value, callback) => {
      let keyWordsArr = ['null', 'nil', 'true', 'false']
      for (let i = 0, len = keyWordsArr.length; i < len; i++) {
        if (keyWordsArr[i] == value.toLowerCase()) {
          callback(new Error('情景模式名称不能为关键字'))
        }
      }

      callback()
    }
    const repeatedSceneName = (rule, value, callback) => {
      for (let i = 0, len = this.sceneList.length; i < len; i++) {
        if (this.sceneList[i].sceneName.toLowerCase() === value.toLowerCase()) {
          callback(new Error('情景模式名称已存在'))
        }
      }
      callback()
    }
    return {
      sceneName: 'main',
      dialogVisible: false,
      form: {
        sceneName: '',
        point: '1,2',
      },
      formRules: {
        sceneName: [
          { required: true, message: '请输入情景模式名称' },
          { max: 16, message: '情景模式名称长度不能超过16个字符' },
          {
            pattern: /^[a-zA-Z0-9\-]+$/,
            message: '情景模式名称仅支持字母/数字/-',
            trigger: ['blur', 'change'],
          },
          { validator: checkKeyWord, trigger: 'blur' },
          { validator: repeatedSceneName, trigger: 'blur' },
        ],
      },
      formSubmitting: false,
      submitDialogVisible: false,
      importDialogVisible: false, // 情景模式导入弹窗和
      impRadio: '', // 情景模式变量
      impList: [], // 情景模式导入列表
      loading: false,
      pageSize: 5,
      pageIndex: 1,
      pageTotal: 0,
      sceneLoading: false,
      sceneSearchVal: '',
      sceneSearchValInput: '',
    }
  },
  props: {
    appId: String,
    appInfo: Object,
    currentScene: Object,
    globalChange: Boolean,
    subAccount: Boolean,
    // subAccountEditable: Boolean,
  },
  computed: {
    ...mapGetters({
      limitCount: 'aiuiApp/limitCount',
      subAccountEditable: 'aiuiApp/subAccountEditable',
      sceneList: 'aiuiApp/sceneList',
    }),
  },
  created() {
    this.getAppSceneList()
  },
  methods: {
    createSceneVisible() {
      if (this.sceneList.length < this.limitCount['app_scene_count']) {
        this.dialogVisible = true
      } else {
        this.$message.warning('情景模式个数已达上限')
      }
    },
    doSubmit(formName) {
      let self = this
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (self.formSubmitting) return
          self.formSubmitting = true

          let data = {
            appid: self.sceneList[0].appid,
            sceneName: self.form.sceneName,
            point: self.form.point,
          }
          if (data.point === '1,13') {
            data.type = 'sos'
          }
          // 如果是魔飞应用创建语义情景模式，则默认开通语义后处理
          if (this.appInfo.platform === 'morfei' && data.point === '1,2') {
            data.point = '1,2,3'
            data.platform = 'morfei'
          }

          // 如果是魔飞MFCore应用创建语义情景模式，则默认开通语义后处理
          if (this.appInfo.platform === 'MorfeiCore' && data.point === '1,2') {
            data.point = '1,2,3'
            data.platform = 'morfeicore'
          }

          this.$utils.httpPost(this.$config.api.AIUI_SCENE_CREATE, data, {
            success: (res) => {
              if (res.flag) {
                self.dialogVisible = false
                self.addScene(res.data)
              } else {
                this.$message.error(res.desc)
              }
              self.formSubmitting = false
            },
            error: (err) => {
              console.log('page=>>')
              console.log(err)
            },
          })
        } else {
          return false
        }
      })
    },
    addScene(scene) {
      this.$store.dispatch('aiuiApp/setSceneList', [...this.sceneList, scene])
      this.changeScene(scene)
    },

    changeScene(scene) {
      if (scene.sceneName === this.currentScene.sceneName) return
      //  this.$emit('change', {})
      //   this.$router.push({
      //     name: `${this.subAccount ? 'sub-' : ''}app-config`,
      //     params: { appid: scene.appid },
      //     query: { sceneName: scene.sceneName },
      //   })

      //   setTimeout(() => {
      //     this.$emit('change', scene)
      //   })

      this.$store.dispatch('aiuiApp/setCurrentScene', scene)

      this.$router.push({
        // name: `${this.subAccount ? 'sub-' : ''}app-config`,
        // params: { appid: scene.appid },
        query: { sceneName: scene.sceneName },
      })
    },
    // changeScene(scene, forceChange) {
    //   if (scene.sceneName === this.currentScene.sceneName) return
    //   if (!this.globalChange || forceChange) {
    //     this.$emit('change', {})
    //     this.$router.push({
    //       name: `${this.subAccount ? 'sub-' : ''}app-config`,
    //       params: { appid: scene.appid },
    //       query: { sceneName: scene.sceneName },
    //     })

    //     setTimeout(() => {
    //       this.$emit('change', scene)
    //     })
    //   } else {
    //     this.$confirm('放弃当前未保存内容而切换到新情景模式？', '提示', {
    //       confirmButtonText: '确定',
    //       cancelButtonText: '取消',
    //       type: 'warning',
    //     })
    //       .then(() => {
    //         this.$emit('change', {})
    //         this.$router.push({
    //           name: `${this.subAccount ? 'sub-' : ''}app-config`,
    //           params: { appid: scene.appid },
    //           query: { sceneName: scene.sceneName },
    //         })

    //         setTimeout(() => {
    //           this.$emit('change', scene)
    //         })
    //       })
    //       .catch(() => {
    //         return
    //       })
    //   }
    // },
    deleteScene(item, index) {
      let self = this
      this.$confirm('确定删除？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          self.$utils.httpPost(
            this.$config.api.AIUI_SCENE_DELETE,
            {
              appid: item.appid,
              sceneId: item.sceneId,
              sceneName: item.sceneName,
            },
            {
              success: (res) => {
                if (
                  self.sceneList[index].sceneName ===
                  self.currentScene.sceneName
                ) {
                  self.changeScene(this.sceneList[0], true)
                }
                let scenes = self.sceneList.slice()
                scenes.splice(index, 1)
                self.$store.dispatch('aiuiApp/setSceneList', scenes)
                this.$message.success('情景模式删除成功')
              },
              error: (err) => {
                console.log('page=>>')
                console.log(err)
              },
            }
          )
        })
        .catch(() => {})
    },
    submitImpRadio() {
      if (!this.impRadio) return
      this.loading = true
      this.$utils.httpGet(
        this.$config.api.AIUI_COPY_SCENE,
        {
          appid: this.appId,
          sceneName: this.currentScene.sceneBoxName,
          fromAppid: this.impRadio.appid,
          fromSceneName: this.impRadio.sceneName,
        },
        {
          success: (res) => {
            this.loading = false
            this.importDialogVisible = false
            if (res.data.pay) {
              setTimeout(() => {
                this.submitDialogVisible = true
              }, 300)
            } else {
              this.toReloadPage()
            }
          },
          error: (err) => {
            this.loading = false
            console.log(err)
          },
        }
      )
    },
    getImpSceneList() {
      this.sceneLoading = true
      this.$utils.httpGet(
        this.$config.api.AIUI_COPY_SCENE_LIST,
        {
          appid: this.appId,
          sceneName: this.currentScene.sceneBoxName,
          pageIndex: this.pageIndex,
          pageSize: this.pageSize,
          search: this.sceneSearchVal,
        },
        {
          success: (res) => {
            if (res.flag) {
              this.impList = res.data.scenes
              this.pageSize = res.data.pageSize
              this.pageIndex = res.data.pageIndex
              this.pageTotal = res.data.count
            }
            this.sceneLoading = false
          },
          error: (err) => {
            console.log(err)
            this.sceneLoading = false
          },
        }
      )
    },
    toReloadPage() {
      location.reload()
    },
    handleCurrentChange(val) {
      this.impRadio = ''
      this.pageIndex = val
      this.getImpSceneList()
    },
    handleSceneSearch() {
      this.sceneSearchVal = this.sceneSearchValInput
      this.pageIndex = 1
      this.pageTotal = 0
      this.getImpSceneList()
    },

    getAppSceneList() {
      let self = this
      this.$utils.httpGet(
        this.$config.api.AIUI_SCENE_LIST,
        {
          appid: this.appId,
        },
        {
          success: (res) => {
            if (res.flag) {
              const scenes = res.data || []
              self.$store.dispatch('aiuiApp/setSceneList', scenes)
              // 获取当前场景
              let sceneName = self.$route.query.sceneName || 'main'
              scenes.forEach((item) => {
                if (item.sceneName === sceneName) {
                  // self.initParams(item)
                  self.$store.dispatch('aiuiApp/setCurrentScene', item)
                }
              })
            } else {
              // TODO: 确认？
              self.$router.push({ name: 'apps' })
            }
          },
          error: (err) => {
            console.log('page=>>')
            console.log(err)
          },
        }
      )
    },
  },
  watch: {
    'currentScene.sceneName': function () {
      this.sceneName = this.currentScene.sceneName
    },
    currentScene() {
      if (this.currentScene.sceneBoxName) {
        this.getImpSceneList()
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.scene-wrap {
  display: flex;
  align-items: center;
}
.scene-import {
  font-size: 14px;
  // margin-left: 28px;
  margin-right: 10px;
}
.scene-add {
  width: 14px;
  height: 14px;
  display: inline-block;
  border-radius: 100%;
  border: 1px solid $primary;
  text-align: center;
  line-height: 11px;
  color: $primary;
  cursor: pointer;
  margin-right: 10px;
  margin-left: 10px;
}

.item-title {
  position: relative;
  font-size: 16px;
  font-weight: 500;
  padding-left: 10px;
  margin-right: 18px;

  &:before {
    width: 2px;
    height: 16px;
    background-color: $primary;
    content: ' ';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    margin: auto;
  }
}
.scene-label {
  font-size: 14px;
  color: $grey6;
}
.ic-r-delete {
  font-size: 16px;
  color: $grey4;
}
.create-scene {
  width: 36px;
  height: 36px;
  line-height: 32px;
  border-radius: 2px;
  margin-left: 10px;
  font-size: 26px;
  cursor: pointer;
  color: $primary;
  border: 1px solid $grey3;
  text-align: center;
  vertical-align: middle;
}
.imp-btn {
  margin-left: 10px;
  min-width: 80px;
}
.import-dialog {
  .top-tips {
    margin-bottom: 15px;
    padding: 0 32px;
    font-size: 16px;
    color: #666666;
  }
  .top-search {
    margin-bottom: 15px;
    padding: 0 32px;
    width: 250px;
  }
  .list-container {
    height: 360px;
    ul {
      width: 100%;
      .is-active {
        background-color: #e8f4ff;
      }
      li {
        padding: 0 30px;
        width: 100%;
        height: 70px;
        & + li {
          border-top: 1px solid #eff1f1;
        }
        .radio-item {
          display: flex;
          align-items: center;
          width: 100%;
          height: 100%;
          font-size: 16px;

          &-appname {
            display: inline-block;
            width: 130px;
            padding-right: 5px;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
      }
    }
  }
  .pagination-container {
    display: flex;
    justify-content: flex-end;
    padding: 0 40px;
    margin-bottom: 10px;
  }
  .btn-group {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 40px;
    width: 100%;
    height: 100px;
  }
}
</style>
<style lang="scss">
.import-dialog {
  margin: 0 auto;
  .el-dialog__header {
    // padding: 15px 32px 0px;
    .el-dialog__title {
      font-weight: bold;
    }
  }
  .el-dialog__body {
    padding: 16px 0 0;
  }

  .list-container {
    .radio-item {
      .el-radio__label {
        font-size: 16px;
      }
      .el-radio__inner {
        width: 24px;
        height: 24px;
        &::after {
          width: 13px;
          height: 13px;
        }
      }
    }
    .is-active {
      .radio-item {
        .el-radio__label {
          color: #1f90fe;
        }
      }
    }
  }
  .pagination-container {
    .el-pagination span:not([class*='suffix']),
    .el-pagination button {
      height: 32px;
      line-height: 32px;
    }
    .el-pager li {
      height: 32px;
      line-height: 32px;
    }
  }
}
</style>
