<template>
  <div class="info-tip">
    <i class="iconfont icon-icon_complete"></i>
    <p class="upload-complete">上传完成</p>
    <p>
      已为您比对实体，有<span class="upload-count"> {{ count }} </span
      >条词条不同。<a
        :style="{ fontSize: '14px' }"
        v-if="count > 0"
        target="_blank"
        :href="`${this.$config.server}/aiui/web/entity/compare/export?entityId=${this.entityId}`"
        >下载词条</a
      >
    </p>
    <p v-if="count > 0" class="soft-tip">下载的文件可以批量加入词条中</p>
  </div>
</template>
<script>
export default {
  props: {
    count: {
      type: Number,
      default: 0,
    },
    entityId: {
      type: String,
      default: '',
    },
  },
  data() {
    return {}
  },
  created() {},
}
</script>
<style lang="scss" scoped>
.info-tip {
  text-align: center;
  padding-top: 20px;
  .upload-complete {
    font-size: 24px;
  }
  .upload-count {
    color: $dangerous;
  }
  .icon-icon_complete {
    font-size: 60px;
    color: $success;
  }
  .soft-tip {
    color: #999999;
    font-size: 14px;
    font-weight: normal;
  }
}
</style>

<style>
.el-message-box__status + .el-message-box__message {
  padding-left: 0;
  padding-right: 0;
}
</style>
