<template>
  <os-page :options="pageOptions">
    <studio-skill-header-right slot="btn" />
    <div class="os-scroll">
      <div class="mgt32 mgb24" @keyup.enter="searchLabel">
        <el-input
          class="search-area"
          placeholder="搜索交互标签"
          v-model="labelSearchName"
        >
          <i
            slot="suffix"
            class="el-input__icon el-icon-search search-area-btn"
            @click="searchLabel"
          />
        </el-input>
      </div>

      <os-table
        class="intentions-table"
        :tableData="tableData"
        style="margin-bottom: 56px"
        @change="getLabels"
        @edit="toEdit"
      >
        <el-table-column type="index" width="50">
          <template slot-scope="scope">
            {{ (tableData.page - 1) * tableData.size + scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="zhName" label="交互标签名称" min-width="120">
          <template slot-scope="scope">
            <!-- <div
              class="intent-zhname ib"
              :class="{ 'no-pointer': scope.row.type && scope.row.type == 6 }"
              @click="toEdit(scope.row)"
            >
              {{ scope.row.zhName }}
            </div>
            <div
              class="intent-tag ib"
              v-if="scope.row.official || scope.row.type == 6"
            >
              官
            </div> -->
            {{ scope.row.zhName }}
          </template>
        </el-table-column>
        <el-table-column prop="value" label="英文标识" min-width="200">
          <template slot-scope="scope">
            {{ scope.row.value }}
          </template>
        </el-table-column>
        <el-table-column prop="description" min-width="200" label="描述">
          <template slot-scope="scope">
            <div>{{ scope.row.description || '-' }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="label" min-width="100" label="类型">
          <template slot-scope="scope">
            <div>{{ scope.row.label }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="picture" min-width="100" label="示意">
          <template slot-scope="scope">
            <div>
              <img
                :src="
                  scope.row.picture ||
                  'https://aiui-file.cn-bj.ufileos.com/avatar/default.png'
                "
                class="label-picture"
              />
            </div>
          </template>
        </el-table-column>
      </os-table>
    </div>
  </os-page>
</template>
<script>
import { mapGetters } from 'vuex'
export default {
  name: 'refered-intentions',
  props: {
    type: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      pageOptions: {
        title: '引用的交互标签',
        loading: false,
      },
      labelSearchName: '',
      tableData: {
        loading: true,
        total: 0,
        page: 1,
        size: 10,
        handles: [],

        handleColumnText: '操作',
        list: [],
      },
    }
  },
  created() {
    this.getLabels()
    this.handleSubAccountEditable()
  },
  computed: {
    ...mapGetters({
      businessId: 'studioSkill/id',
      subAccount: 'user/subAccount',
      subAccountSkillAuths: 'studioSkill/subAccountSkillAuths',
    }),
  },
  watch: {
    subAccountSkillAuths(val) {
      if (val) {
        this.handleSubAccountEditable()
      }
    },
  },
  methods: {
    handleSubAccountEditable() {
      let auth = this.subAccountSkillAuths[this.businessId]
      if (auth == 2) {
        this.tableData.handles = []
      }
    },

    searchLabel() {
      this.getLabels(1)
    },
    getLabels(page) {
      this.tableData.loading = true
      this.$utils.httpGet(
        this.$config.api.STUDIO_QUOTE_LABELS,
        {
          businessId: this.businessId,
          pageIndex: page || this.tableData.page,
          pageSize: this.tableData.size,
          search: this.labelSearchName,
        },
        {
          success: (res) => {
            if (res && res.data && !res.data.count) {
              this.tableData.list = []
              this.tableData.loading = false
              return
            }
            this.tableData.loading = false
            this.tableData.total = res.data.count
            this.tableData.page = res.data.pageIndex
            this.tableData.size = res.data.pageSize

            this.tableData.list = (res.data.labels || []).map((item) => {
              return {
                ...item,
              }
            })
          },
          error: (err) => {
            this.tableData.loading = false
            console.log('page=>>')
            console.log(err)
          },
        }
      )
    },
    toEdit(intention) {
      if (this.subAccount) {
        // TODO: 待确认子账号
        this.$router.push({
          name: 'sub-skill-intention-referenced',
          params: { quoteId: intention.quoteId, intentId: intention.id },
        })
      } else {
        this.$router.push({
          name: 'skill-intention-referenced',
          params: { quoteId: intention.quoteId, intentId: intention.id },
        })
      }
    },
    changeSwitch(data) {
      alert(data)
      let self = this
    },
  },
}
</script>
<style lang="scss" scoped>
.intent-zhname {
  margin-right: 7px;
  cursor: pointer;
  font-weight: 600;
}
.label-picture {
  width: 20px;
  height: 20px;
}
</style>
<style lang="scss">
.el-switch.cell-handle-hovershow {
  display: inline-flex;
}
</style>
