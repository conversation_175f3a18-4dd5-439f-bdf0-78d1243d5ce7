<template>
  <div class="body_wrapper">
    <header class="header">
      <div class="search_wrapper">
        <!-- 具名插槽搜索区域 -->
        <slot name="search"></slot>
        <el-button
          icon="ic-r-plus"
          plain
          size="medium"
          @click="jump"
          style="margin-left: 10px"
          >&nbsp;{{ abbBtnText }}</el-button
        >
      </div>
      <!-- 具名插槽配置区域 -->
      <slot name="config"></slot>
    </header>
    <main
      class="main"
      v-loading="loading"
      :style="{ height: scrollHeight + 'px' }"
    >
      <CardList
        :tagText="tagText"
        :data="ragData"
        :dataCopy="ragDataCopy"
        :switchKey="switchKey"
        @selectchange="onSelectchange"
        @toDocCard="toDocCard"
      ></CardList>
    </main>
  </div>
</template>

<script>
import CardList from './cardList.vue'

export default {
  name: 'CommonDialogBody',
  components: { CardList },
  props: {
    appId: {
      type: String,
      default: '',
    },
    abbBtnText: {
      type: String,
      default: '',
    },
    jumpUrl: {
      type: String,
      default: '',
    },
    scrollHeight: {
      type: Number,
      default: 316,
    },
    switchKey: {
      type: String,
      default: '',
    },
    tagText: {
      type: String,
      default: '',
    },
    loading: {
      type: Boolean,
      default: false,
    },
    currentScene: {
      type: Object,
      default: () => ({}),
    },
    ragData: {
      type: Array,
      default: () => [],
    },
    ragDataCopy: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {}
  },

  methods: {
    jump() {
      window.open(this.jumpUrl, '_blank')
    },

    onSelectchange(item, isSelected) {
      this.$emit('selectchange', item, isSelected)
    },
    toDocCard(item) {
      this.$emit('toDocCard', item)
    },
  },
}
</script>

<style lang="scss" scoped>
.body_wrapper {
  height: 100%;
  .header {
    margin-bottom: 20px;
    .search_wrapper {
      display: flex;
      align-items: center;
    }
  }
}
</style>
