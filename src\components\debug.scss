.notice-title {
  position: relative;
  .circle {
    width: 8px;
    height: 8px;
    border: 2px solid #1ed3ff;
    border-radius: 50%;
    display: inline-block;
  }
  .title {
    font-size: 14px;
    font-family: Microsoft YaHei;
    font-weight: bold;
    color: $primary;
    position: relative;
  }
  .plus {
    position: absolute;
    z-index: 1;
    top: 0px;
    right: -6px;
    color: #1ed3ff;
    font-size: 12px;
    display: inline-block;
    line-height: 0;
  }
}
.debug-input {
  width: 100%;
}
.view-button-group {
  position: absolute;
  bottom: -44px;
  left: 0px;
  display: flex;
}
.view-button {
  i {
    color: $primary;
  }
  display: flex;
  align-items: center;
  padding: 1px 15px !important;
  color: $primary;
  background: #fff;
  border-radius: 4px;
  font-size: 14px;
  border: 1px solid $primary;
  height: 30px;
}
.view-button + .view-button {
  margin-left: 5px;
}
.icon-chakan {
  font-size: 20px;
  color: #fff;
}
.debug-wrap {
  position: relative;
  height: 100%;
  color: $semi-black;
  background: #f7f8fa;
}
.debug-header {
  padding: 1px 16px;
}
.send-wrap {
  position: relative;
  padding: 0 15px;
  :deep(.el-input__inner) {
    border: 1px solid #e2e4e9;
    border-radius: 6px;
    height: 50px;
    padding-right: 100px;
  }
}
.debug-send {
  position: absolute;
  z-index: 1;
  right: 24px;
  top: 50%;
  transform: translateY(-50%);
  opacity: 1;
  color: #fff;
  background-color: $primary;
  height: 28px;
  display: inline-block;
  line-height: 28px;
  width: 80px;
  text-align: center;
  border-radius: 3px;
  cursor: pointer;
  &:hover {
    background-color: $hover;
  }
}
.debug-send-active {
  opacity: 1;
}
.robot {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: $primary;
  text-align: center;
  line-height: 36px;
  margin: 4px 0 10px 0;
  i {
    color: $white;
    font-size: 20px;
  }
}
.clear {
  position: absolute;
  display: flex;
  align-items: center;
  //   width: 36px;

  height: 30px;
  line-height: 24px;
  color: #b8babf;
  cursor: pointer;
  font-size: 18px;
  padding: 4px 13px;
  z-index: 2;
  background: #fff;
  box-shadow: 0px 2px 10px 0px rgba(112, 112, 112, 0.05);
  text-align: center;
  bottom: 73px;
  left: 17px;
  .divider {
    width: 1px;
    height: 20px;
    background: rgba(151, 151, 151, 0.36);
  }
  .ic-r-delete {
    color: #31323b;
  }
  .icon-shanchu {
    color: #31323b;
  }
  .el-icon-setting {
    color: #31323b;
    margin-right: 10px;
  }
}
.ic-ed-delete {
  vertical-align: top;
}
.debug-dialog {
  overflow: auto;
  height: calc(100% - 100px);
  padding: 48px 16px 45px;
  width: 100%;
  .dialog {
    margin-bottom: 18px;
  }
  .dialog-answer:nth-of-type(2) {
    margin-bottom: 18px;
  }
  .dialog-answer:not(:nth-of-type(1)) {
    margin-bottom: 56px;
  }
}
.icon-debug-answer {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  vertical-align: top;
  margin-right: 8px;
  background: url('@A/svg/debug/icon-debug-answer.svg') no-repeat 5px 5px
    $primary-light-12;
}

.dialog-answer {
  .message {
    transition: 0.3s;
    position: relative;
    max-width: 410px;
    color: $grey002;
    padding: 9px 0;
    background: rgba(255, 255, 255, 1);
    border-radius: 0px 8px 8px 8px;
    box-shadow: 0px 2px 10px 0px rgba(112, 112, 112, 0.05);
  }
}

.dialog-question {
  text-align: right;
  .message {
    position: relative;
    // float: right;
    color: $white;
    max-width: 225px;
    word-break: break-word;
    word-wrap: break-word;
    background: $primary;
    padding: 9px 16px;
    margin-right: 10px;
    border-radius: 8px 0px 8px 8px;
    font-size: 14px;
    text-align: left;
  }
}

.debug-threshold {
  position: fixed;
  bottom: 0;
  width: 550px;
  height: 48px;
  line-height: 28px;
  padding: 10px 24px;
  border-top: 1px solid $grey2;
  span {
    cursor: pointer;
  }
}
@media screen and (max-width: 1601px) {
  .debug-threshold {
    width: 367px;
  }
}
.threshold-input {
  width: 50px;
}

.json-wrap {
  min-height: 250px;
  max-height: 500px;
  padding: 25px;
  border-radius: 12px;
  margin-bottom: 20px;
  // border: 1px solid $grey4;
  overflow-x: hidden;
  overflow-y: auto;
  word-break: break-word;
  word-wrap: break-word;
  background-color: $grey1-30 !important;
}
.dialog-bottom {
  height: 20px;
}
.ic-r-copy {
  position: absolute;
  right: 24px;
  top: 24px;
  z-index: 8;
  font-size: 16px;
  color: $grey4;
  cursor: pointer;
  &::after {
    content: '';
    display: inline-block;
    position: absolute;
    right: -8px;
    top: -8px;
    z-index: -5;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: $white;
    box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.1);
  }
}
.intent-info-wrap {
  margin-top: 10px;
  padding: 2px 16px 8px;
  border-top: 1px solid rgba(228, 231, 237, 1);

  .tag-wrap {
    > span {
      margin-right: 2px;
      &:nth-child(3n-1) {
        .el-tag {
          background: #d3f2fe;
          color: #2aafe3;
        }
      }
      &:nth-child(3n) {
        .el-tag {
          background: #dcf3eb;
          color: #54b39e;
        }
      }
      &:nth-child(3n + 1) {
        .el-tag {
          background: #e7e8ff;
          color: #797dd5;
        }
      }
    }
  }
}
.el-tag {
  height: 24px;
  line-height: 24px;
  padding: 0 12px;
  margin: 12px 0 8px;
  border-radius: 12px;
  border: none;
}
// .el-tag + .el-tag {
//   margin-left: 2px;
// }
.msg-item {
  padding: 0 16px;
}
.msg-answer-item {
  display: flex;
  align-items: flex-start;
  .robot-avatar {
    display: inline-block;
    width: 36px;
    height: 36px;
    background: url(~@A/images/aiui/<EMAIL>) center/100% no-repeat;
    margin-right: 15px;
    min-width: 36px;
  }
  .msg-item {
    font-size: 14px;
    font-weight: 400;
    color: #666666;
    line-height: 22px;
  }
}
.intent-info-title {
  display: inline-block;
  margin-top: 9px;
  margin-right: 12px;
  width: 56px;
  font-weight: 600;
  color: $grey001;
}
.collapse-btn {
  margin-top: 12px;
  height: 22px;
  font-weight: 600;
  color: rgba(38, 38, 38, 1);
  line-height: 22px;
  cursor: pointer;
  display: flex;
  align-items: center;
  i {
    margin-right: 2px;
    color: $primary;
    font-size: 16px;
  }
  span {
    font-size: 16px;
    color: #333752;
  }
}
.slot-tag {
  cursor: pointer;
}
.el-icon-d-arrow-right {
  font-size: 24px;
  color: $grey001;
  font-weight: 500;
}
.icon-zhankai {
  font-size: 18px;
  color: $grey001;
  font-weight: 500;
  transform: rotate(180deg);
  margin-right: 6px;
}

// 技能商店适配不同屏幕
@media screen and (max-width: 1601px) {
  .dialog-answer {
    // font-size: 12px;
    .message {
      max-width: 270px;
    }
  }
  .dialog-question {
    // font-size: 12px;
  }
  // .view-button {
  //   font-size: 12px;
  // }
  .el-tag {
    padding: 0 7px;
  }
  .send-wrap {
    font-size: 12px;

    :deep(input::-webkit-input-placeholder) {
      font-size: 12px;
    }
    :deep(input::-moz-placeholder) {
      /* Mozilla Firefox 19+ */
      font-size: 12px;
    }
    :deep(input::-moz-placeholder) {
      /* Mozilla Firefox 4 to 18 */
      font-size: 12px;
    }
    :deep(input:-ms-input-placeholder) {
      /* Internet Explorer 10-11 */
      font-size: 12px;
    }
  }
}

.icon-link {
  width: 16px;
  height: 16px;
  display: inline-block;
  background: url(~@A/images/link.png) center/100% no-repeat;
}

.linkDialog {
  :deep(.el-dialog__body) {
    padding: 0 32px 0 32px;
  }
}
.request-json {
  position: relative;
  :deep(.el-tabs--card > .el-tabs__header .el-tabs__nav) {
    height: 40px;
    border: none;
  }
  :deep(.el-tabs--card > .el-tabs__header .el-tabs__item) {
    padding: 0 15px;
    border-left: none;
    border-bottom: 1px solid #e4e7ed;
  }
  :deep(.el-tabs__item.is-top.is-active) {
    border-top: 1px solid #e4e7ed;
    border-right: 1px solid #e4e7ed;
    border-left: 1px solid #e4e7ed;
    border-bottom-color: $white;
    border-radius: 6px 6px 0 0;
  }
}

.entity-item {
  margin-bottom: 8px;
}
.tag {
  display: inline-block;
  margin-right: 12px;
  width: 56px;
  font-weight: 600;
  color: $grey5;
}
