<template>
  <div>
    <div class="mgt28 mgb16 top-edit-wrap">
      <el-button
        icon="ic-r-plus"
        size="small"
        type="primary"
        :disabled="onCreatePage"
        @click="addRow">
        &nbsp;添加
      </el-button>
      <!-- <el-button
        size="small"
        type="primary">
        批量操作
      </el-button> -->
      <div class="fr" @keyup.enter="getList(1)">
        <el-input
          class="search-area"
          placeholder="输入关键字搜索"
          size="medium"
          style="width: 360px;"
          v-model="searchVal">
          <i slot="suffix"
            class="el-input__icon el-icon-search search-area-btn"
            @click="getList(1)" />
        </el-input>
      </div>
    </div>
    <os-table
      class="keyword-reply-table"
      :border="true"
      :tableData="tableData"
      @change="getList"
    >
      <el-table-column
        width="260"
        prop="keyword"
        label="问法关键字">
        <template slot-scope="scope">
          <el-input
            v-if="!scope.row.keywordId"
            :ref="'keywordInput'+scope.$index"
            class="keyword-value highlight-wrap"
            :class="{'highlight-wrap-hover': scope.row.hasOwnProperty('aliasArr') }"
            size="small"
            placeholder="输入问法关键字，回车添加"
            v-model="scope.row.keyword"
            :title="scope.row.keyword"
            @keyup.enter.native="handleEditKeywordEnter(scope.row, $event)"
            @blur="editKeyword(scope.row, scope.$index)">
          </el-input>
          <span v-else>{{scope.row.keyword}}</span>
        </template>
      </el-table-column>
      <el-table-column
        width="420"
        label="问法关键字别名"
        prop="alias">
        <template slot-scope="scope">
          <div class="alias-edit-wrap highlight-wrap" :class="{'highlight-wrap-hover': scope.row.hasOwnProperty('aliasArr') }"
            @click="toEditAlias(scope.row, scope.$index)"
            >
            <template v-if="scope.row.hasOwnProperty('aliasArr')">
              <el-tag
                v-for="(tag, index) in scope.row.aliasArr"
                :key="index"
                closable
                size="medium"
                :disable-transitions="false"
                @close="delTmpAlias(index, scope.row)"
              >
                <span class="alias-tag" :title="tag">{{tag}}</span>
              </el-tag>
              <el-input
                v-if="scope.row.aliasArr && scope.row.aliasArr.length < 200"
                class="alias-input"
                size="small"
                ref="aliasInput"
                v-model="newAlias"
                placeholder="输入问法关键字别名，回车添加"
                @keyup.enter.native="addAliasEnter(scope.row, $event)"
                @blur="addAliasBlur(scope.row, scope.$index, $event)"
              ></el-input>
            </template>
            <span v-else>{{scope.row.alias}}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="preview">
        <template slot="header" slot-scope="scope">
          <span>回复语</span>
          <el-radio v-model="replyType" label="2" @change="getList(1)">全部</el-radio>
          <el-radio v-model="replyType" label="1" @change="getList(1)">未设置</el-radio>
        </template>
        <template slot-scope="scope">
          <el-input
            class="highlight-wrap reply-input"
            :class="{'highlight-wrap-hover': scope.$index == highlightReplyIndex }"
            v-model="scope.row.preview"
            :title="scope.row.preview"
            @focus="highlightReplyIndex = scope.$index"
            @keyup.native.enter="saveReply(scope.row, scope.$index)"
            @blur="saveReply(scope.row, scope.$index)"
            :placeholder="scope.row.hasOwnProperty('aliasArr') ? '输入回复语关键信息，回车添加' : '没有编辑回复，该条不支持问答。'">
              <i slot="suffix" class="el-icon-error" v-if="scope.row && scope.row.replyId" @click="clearReply(scope.row, scope.$index)"></i>
            </el-input>
        </template>
      </el-table-column>
    </os-table>
  </div>
</template>

<script>
  export default {
    name: 'keyword-and-reply',
    props: {
      data: [],
    },
    data() {
      return {
        searchVal: '',
        tableData: {
          loading: false,
          total: 0,
          page: 1,
          size: 10,
          list: []
        },
        initList: [],
        editKeywordEnter: false,
        replyType: '2',
        highlightReplyIndex: null,
        newAlias:'',
        reg: /^[\u4e00-\u9fffa-zA-Z0-9 \.']{0,}$/
      }
    },
    computed: {
      repoId() {
        return this.$route.params.repoId || ''
      },
      themeId() {
        if(this.$route.path.match('/create')) {
          return ''
        }
        return this.$route.params.themeId || ''
      },
      onCreatePage() {
        if(this.$route.path.match('/create')) {
          return true
        }
        return false
      }
    },
    watch: {
      themeId(val, oldVal) {
        if(val) {
          this.getList()
        }
      }
    },
    created(){
      this.newAlias = ''
      this.getList()
    },
    methods: {
      getList(page) {
        let self = this
        if(!self.themeId) return
        if(page) {
          this.tableData.page = page
        }
        self.tableData.list = []
        this.$utils.httpGet(this.$config.api.STUDIO_REPO_REPLY_LIST, {
          repositoryId: this.repoId,
          themeId: this.themeId,
          search: this.searchVal,
          type: this.replyType, //type: 1 查看未设置的回复语
          pageSize: this.tableData.size,
          pageIndex: page || this.tableData.page
        }, {
          success: (res) => {
            if(res.data.count  && res.data.count <= (self.tableData.page -1)* this.tableData.size ) {
              return this.getList(1)
            }
            if(!res.data) {
              return self.$message.error(res.desc || '数据获取失败')
            }
            self.tableData.total = res.data.count
            res.data.list && res.data.list.forEach(item => {
              if(!item.hasOwnProperty('preview')) {
                item.preview = ''
              }
              self.tableData.list.push(item)
            })
            self.initList = res.data.count ? JSON.parse(JSON.stringify(self.tableData.list)) : []
            this.firstQuestionReply()
            if(self.editKeywordEnter) {
              self.addRow()
            }
          },
          error: (err) => {
            self.$router.push({'name': 'studio-handle-platform-qabanks'})
          }
        })
      },
      firstQuestionReply(){
        //提取问法和回复例句
        let item = this.initList.find( item => {
          return item.keyword && item.preview
        })
        item && this.$emit('setQaReply', item.keyword, item.preview)
      },
      addRow () {
        let self = this
        if(this.tableData.list[0] && !this.tableData.list[0].keywordId) return
        if(this.tableData.list[0] && this.tableData.list[0].hasOwnProperty('aliasArr')) {
          this.$delete(this.tableData.list[0], 'aliasArr')
        }
        let item = {
          aliasArr: [],
          keyword: '',
          alias: '',
          preview: ''
        }
        self.newAlias = ''
        this.tableData.list.unshift(item)
        if (this.tableData.total % 10 === 0) {
          this.tableData.size = 11
        } else {
          this.tableData.size = 10
        }
        this.tableData.total += 1
        this.editKeywordEnter = false
        this.$nextTick(function () {
          self.$refs['keywordInput0'] && self.$refs['keywordInput0'].focus()
        })
      },
      addOrEditKA(data, index){
        let self = this
        let len = this.tableData.list.length || 0
        if(len > 10) {
          self.tableData.list.splice(10, 1)
        }
        this.$utils.httpPost(this.$config.api.STUDIO_REPO_KEYWORD_ADD_EDIT, {
          repositoryId: this.repoId,
          word: data.keyword,
          alias: data.alias || '',
          id: data.keywordId || ''
        }, {
          success: (res) => {
            if( data && !data.keywordId ) {
              this.tableData.list[index]['keywordId'] = res.data.id
            }
            if(data.preview && !data.replyId) {
              self.saveReply(data, index)
            }
            if(self.editKeywordEnter) {
              this.addRow()
            }
          },
          error: (err) => {
            // return this.$message.error(err.desc || '添加失败')
          }
        })
      },
      editKeyword (data, index) {
        let self = this
        let len = this.tableData.list.length || 0
        data.keyword = data.keyword && data.keyword.trim()
        if (!data.keyword) return
        if (data.keyword && data.keyword.length > 40) {
          return self.$message.warning('关键字不能超过40个字符')
        }
        if(!self.reg.test(data.keyword)) {
          return self.$message.warning('只支持中文、英文、数字、空格、英文单引号、英文句号')
        }
        this.addOrEditKA(data, index)
      },
      handleEditKeywordEnter(data, event){
        this.editKeywordEnter = true
        event.target.blur()
      },
      toEditAlias(data, index){
        let self = this
        if(!data.hasOwnProperty('aliasArr')) return
        this.$nextTick(function () {
          self.$refs.aliasInput && self.$refs.aliasInput.focus()
        })
      },
      addAliasEnter (data, e) {
        e.stopPropagation()
        let self = this
        self.newAlias = self.newAlias.trim()
        if(!self.newAlias) return
        if(!data.keywordId) {
          return self.$message.warning("请先编辑问法关键字")
        }
        if (self.newAlias.length > 40) {
          return self.$message.warning('关键字别名不能超过40个字符')
        }
        if(!self.reg.test(self.newAlias)) {
          return self.$message.warning('只支持中文、英文、数字、空格、英文单引号、英文句号')
        }
        data.aliasArr.push(self.newAlias)
        self.newAlias = ''
      },
      delTmpAlias (index, data) {
        e.stopPropagation()
        data.aliasArr.splice(index, 1)
      },
      addAliasBlur(data,index, e) {
        this.addAliasEnter(data, e)
        if(!data.hasOwnProperty('aliasArr')) {
          return 
        }
        if( data.aliasArr && !data.aliasArr.length ) return
        data.alias = data.aliasArr && data.aliasArr.join('|')
        this.$delete(data, 'aliasArr')
        this.addOrEditKA(data, index)
      },
      saveReply(row, index) {
        let self = this
        if(!row.hasOwnProperty('keywordId')) {
          return this.$message.warning('请先编辑问法关键字')
        }
        row.preview = row.preview && row.preview.trim()
        if(!row.preview && !row.replyId) return
        if(self.initList.length && self.initList[index].hasOwnProperty('preview')) {
          if(row.preview ==  self.initList[index].preview) return
        }
        if (row.preview && row.preview.length > 2000) {
          return self.$message.warning('回复语不能超过2000个字符')
        }
        let data = {
          repositoryId: this.repoId,
          themeId: this.themeId,
          keywordId: row.keywordId || '',
          id: row.replyId || '',
          preview: row.preview || '',
          status: row.status || 1
        }
        this.$utils.httpPost(this.$config.api.STUDIO_REPO_REPLY_ADD_EDIT, data, {
          success: (res) => {
            if(res.flag) {
              self.tableData.list[index]['replyId'] = res.data.id
              self.initList = JSON.parse(JSON.stringify(self.tableData.list))
            } else {
              self.$message.warning(res.desc || '回复语保存失败')
            }
            
            // this.getList()
          },
          error: (err) => {
            // return this.$message.error(err.desc || '添加失败')
          }
        })
      },
      clearReply(row, index) {
        row.preview = ''
        this.saveReply(row, index)
      }
    }
  }
</script>

<style lang="scss" scoped>
.highlight-wrap {
  min-height: 56px;
  padding: 6px 12px;
  height: auto;
  width: 100%;
  box-sizing: border-box;
  border: 1px solid transparent;
}
.highlight-wrap-hover {
  &:hover {
    border: 1px solid $primary;
  }
}
.alias-edit-wrap {
  display: flex;
  align-items: center;
  display: inline-flex;
  flex-flow: wrap;
  
  .el-tag {
    display: flex;
    align-items: baseline;
    margin-top: 3px;
    margin-right: 3px;
  }
  .alias-tag {
    display: inline-block;
    max-width: 160px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .el-tag + .el-input {
    flex: 1;
    min-width: 100px;
  }
}
</style>

<style lang="scss">
.keyword-value {
  padding-left: 12px;
  input {
    height: 40px !important;
    line-height: 40px !important;
    border: 0;
    padding-left: 0;
    color: $semi-black;
  }
}
.keyword-reply-table {
  .el-table .cell {
    padding: 0;
    padding-right: 1px;
  }
  .el-table--border td:first-child .cell {
    padding-left: 0;
    span {
      padding-left: 12px;
    }
  }
  .el-table__row td {
    padding: 0;
    height: 56px;
  }
  .el-table__body tr:hover > td {
    background-color: #fff;
  }
  .el-table th > .cell {
    padding-left: 12px;
  }
  .el-radio__label {
    padding-left: 3px;
  }
  .el-radio {
    margin-left: 25px;
  }
  .el-radio + .el-radio {
    margin-left: 12px;
  }

  input {
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .alias-input {
    input {
      height: 22px !important;
      line-height: 22px !important;
      padding-left: 0;
      border: 0;
      color: $semi-black;
    }
  }

  .reply-input {
    .el-input__inner {
      padding-left: 0;
      border: 0;
    }
    .el-input--suffix .el-input__inner {
      padding-right: 35px;
      
    }
    .el-input__suffix {
      line-height: 100%;
      top: calc(50% - 8px );
      right: 16px;
      display: none;
      cursor: pointer;
    }
    &:hover {
      .el-input__suffix {
        display: block;
      }
    }
  }
}
</style>