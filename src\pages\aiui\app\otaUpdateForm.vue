<template>
  <os-page :options="pageOptions" @returnCb="pageReturn">
    <div slot="btn" class="header-app-id">APPID: {{ app.appid }}</div>
    <el-form
      class="mgt48 app-info-form"
      ref="form"
      :rules="rules"
      :model="form"
      label-width="120px"
      label-position="left"
    >
      <el-form-item label="固件名称" prop="name">
        <el-input
          placeholder="支持输入数字、英文、小数点、下划线，长度不超过50个字符"
          v-model.trim="form.name"
        ></el-input>
      </el-form-item>

      <el-form-item label="固件版本" prop="version">
        <el-input
          placeholder="固件版本格式为xx.xx.xx，最小版本号为0.0.0，最大版本号为999.999.999"
          v-model.trim="form.version"
        ></el-input>
      </el-form-item>

      <el-form-item label="版本描述" prop="description">
        <el-input
          placeholder="面向用户用来描述此次版本升级的内容，长度不超过200个字符"
          type="textarea"
          v-model.trim="form.description"
        ></el-input>
      </el-form-item>

      <el-form-item label="上传文件" prop="fileId">
        <el-upload
          class="ota-form-app-upload"
          ref="appVideoUpload"
          :action="`${this.$config.server}/aiui/web/app/firmware/version/upload`"
          :data="{ appid: app.appid }"
          :file-list="appVideoList"
          :on-change="handleAppVideoChange"
          :before-upload="beforeUpload"
          :on-remove="handleAppVideoRemove"
          :on-success="handleAppVideoUrl"
        >
          <el-button size="small" type="primary">上传</el-button>
          <div slot="tip" class="el-upload__tip">
            仅支持 Zip、Ufw 类型文件，大小不超过300M
          </div>
        </el-upload>
      </el-form-item>
      <el-form-item class="form-btm-container">
        <el-button size="small" @click="pageReturn">取消</el-button>
        <el-button size="small" type="primary" @click="onSubmit('form')"
          >保存</el-button
        >
      </el-form-item>
    </el-form>
  </os-page>
</template>

<script>
import { mapGetters } from 'vuex'
export default {
  name: 'app-ota-update',
  data() {
    return {
      pageOptions: {
        title: '固件上传',
        loading: false,
        returnBtn: true,
      },

      form: {
        id: -1, // 版本id，-1为新增
        name: '',
        version: '',
        description: '',
        fileId: '',
      },
      appVideoList: [],
      rules: {
        name: [
          this.$rules.required('固件名称不能为空'),
          this.$rules.lengthLimit(1, 50, '长度不能超过50个字符'),
          this.$rules.englishReglimitForSkillIntent(),
        ],
        version: [
          this.$rules.required('固件版本不能为空'),
          { validator: this.checkVersion, trigger: ['blur'] },
        ],
        description: [
          this.$rules.required('版本描述不能为空'),
          this.$rules.lengthLimit(1, 200, '长度不能超过200个字符'),
        ],
        fileId: [this.$rules.required('上传文件不能为空')],
      },
    }
  },
  methods: {
    // 版本号验证
    checkVersion(rule, val, callback) {
      if (val.split('.').length !== 3) {
        callback(new Error('版本号格式必须为xx.xx.xx'))
      }
      const value = [...val.split('.')]
      const first = Number(value[0]),
        second = Number(value[1]),
        third = Number(value[2])
      if (isNaN(first) || isNaN(second) || isNaN(third)) {
        callback(new Error('版本号必须为数字值'))
      }
      if (
        first < 0 ||
        first > 999 ||
        second < 0 ||
        second > 999 ||
        third < 0 ||
        third > 999
      ) {
        callback(new Error('版本号范围为0.0.0 ~ 999.999.999'))
      }
      callback()
    },
    // 页面返回
    pageReturn() {
      this.$router.push({
        name: `${this.subAccount ? 'sub-' : ''}app-ota`,
      })
    },
    handleAppVideoChange(file, fileList) {
      const isZIP = file.type === 'application/zip'
      const zipEXT = /.zip$/.test(file.name)
      const ufwEXT = /.ufw$/.test(file.name)
      const isLt200M = file.size / 1024 / 1024 < 300
      if (!(isZIP || zipEXT || ufwEXT)) {
        this.$message({
          message: '上传文件仅支持zip和ufw格式',
          type: 'warning',
        })
      }
      if (!isLt200M) {
        this.$message({
          message: '上传文件不能超过300M',
          type: 'warning',
        })
      }
      if ((isZIP || zipEXT || ufwEXT) && isLt200M) {
        this.appVideoList = fileList.slice(-1)
      }
    },
    // 判断上传文件类型和大小
    beforeUpload(file) {
      const isZIP = file.type === 'application/zip'
      const zipEXT = /.zip$/.test(file.name)
      const ufwEXT = /.ufw$/.test(file.name)
      const isLt100M = file.size / 1024 / 1024 < 300
      return (isZIP || zipEXT || ufwEXT) && isLt100M
    },
    handleAppVideoRemove(file) {
      if (file) {
        this.form.fileId = ''
        this.$refs.form.validateField('fileId')
      }
    },
    handleAppVideoUrl(data) {
      if (data.flag) {
        this.form.fileId = data.data.id
        this.$refs.form.validateField('fileId')
      } else {
        this.$message.error(data.desc)
      }
    },
    // 表单提交
    onSubmit(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.$utils.httpGet(
            this.$config.api.OTA_FORM_SUBMIT,
            {
              appid: this.app.appid,
              ...this.form,
            },
            {
              success: (res) => {
                if (res.flag) {
                  this.$message.success(res.data)
                  this.$router.push({
                    name: `${this.subAccount ? 'sub-' : ''}app-ota`,
                  })
                } else {
                  this.$message.error(res.desc)
                }
              },
            }
          )
        } else {
          return false
        }
      })
    },
  },
  mounted() {
    if (this.$route.params.id) {
      this.form = {
        ...this.$route.params,
      }
      this.appVideoList = [
        {
          name: this.$route.params.fileName,
          fileId: this.$route.params.fileId,
        },
      ]
    }
  },
  beforeDestroy() {
    this.$refs['form'].resetFields()
  },
  computed: {
    ...mapGetters({
      app: 'aiuiApp/app',
      subAccount: 'user/subAccount',
    }),
  },
}
</script>

<style lang="scss" scoped>
.header-app-id {
  font-size: 18px;
}
.form-btm-container {
  display: flex;
  flex-direction: row-reverse;
  padding-top: 20px;
}
</style>
<style>
.ota-form-app-upload .el-upload {
  width: 0;
  text-align: left;
}
</style>
