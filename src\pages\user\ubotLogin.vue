<template>
  <div class="container">
    <div class="ib container-left">
      <img :src="BannerImg" alt="login-register.png" />
    </div>
    <div class="ib container-right" @keyup.enter="submit">
      <div class="flex" style="margin-bottom: 80px">
        <a
          href="https://ubot.xfyun.cn"
          target="_self"
          class="container-right-logo fx1"
        >
          <span class="ubot-mark">U-Bot</span>
        </a>
        <router-link
          :to="{ name: 'ubot-register' }"
          class="fx1 text-black fs18 txt-al-r"
          style="line-height: 67px"
        >
          注册
        </router-link>
      </div>
      <p class="fs25 text-black ff-medium mgb12">登录</p>
      <!-- <p class="fs14 text-grey6 mgb40">讯飞云平台及iFLYOS用户可直接登录</p> -->
      <el-form
        :model="loginForm"
        label-position="top"
        :rules="rules"
        ref="loginForm"
        label-width="100px"
        class="login-reg-form mgb48"
      >
        <el-form-item label="账号" prop="username">
          <el-input
            type="input"
            v-model="loginForm.username"
            placeholder="请输入手机号/邮箱/用户名"
            auto-complete="off"
            tabindex="1"
          ></el-input>
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <a
            href="https://passport.xfyun.cn/forget"
            target="_blank"
            class="input-topright-btn"
            >忘记密码?</a
          >
          <el-input
            type="password"
            v-model="loginForm.password"
            placeholder="请输入密码"
            auto-complete="off"
            tabindex="2"
          ></el-input>
        </el-form-item>
      </el-form>
      <div class="mgb24">
        <el-button
          class="login-btn"
          type="primary"
          :loading="submitLoading"
          @click="submit"
          tabindex="3"
        >
          {{ submitLoading ? '登录中...' : '登录' }}
        </el-button>
      </div>
      <p>
        没有账号，<router-link :to="{ name: 'ubot-register' }"
          >注册新用户</router-link
        >
      </p>
    </div>
  </div>
</template>

<script>
// import IconLogo from '@A/images/aiui/logo_aiui_black.png'
import BannerImg from '@A/images/ubot-login-signup.png'
import SSO from 'sso/sso.js'

export default {
  name: 'ubot-login',
  data() {
    return {
      pageFrom: '',
      submitLoading: false,
      // IconLogo: IconLogo,
      BannerImg: BannerImg,
      loginForm: {
        username: '',
        password: '',
      },
      rules: {
        username: [
          { required: true, message: '账号不能为空', trigger: 'blur' },
        ],
        password: [
          { required: true, message: '密码不能为空', trigger: 'blur' },
          { min: 6, message: '请至少输入 6 个字符', trigger: 'blur' },
        ],
      },
      loginTimeout: null,
    }
  },
  created() {
    if (this.$route.query.pageFrom) {
      this.pageFrom = this.$route.query.pageFrom
    }
  },
  methods: {
    submit() {
      let self = this
      this.$utils.clearCookie()
      SSO.logout(function () {
        self.login()
      })
    },
    login() {
      let self = this
      let jump = 'https://ubot.xfyun.cn'
      if (this.pageFrom) {
        jump = this.pageFrom
      }
      if (window.location.port) {
        //开发环境不跳线上地址
        jump = `${window.location.origin}/skills`
      }
      this.$refs.loginForm.validate((valid) => {
        if (valid) {
          self.submitLoading = true
          self.loginTimeout = setTimeout(function () {
            self.$message.error('登录超时，请重新登录')
            self.submitLoading = false
          }, 6000)
          SSO.login(
            {
              accountName: self.loginForm.username,
              accountPwd: self.loginForm.password,
              jump: jump,
              isAct: false,
            },
            (err) => {
              if (err.desc) {
                clearTimeout(self.loginTimeout)
                self.$message.error(err.desc)
                self.submitLoading = false
              }
            },
            (res, cb) => {
              clearTimeout(self.loginTimeout)
              let msg = self.$message.success('登录成功，正在跳转')
              setTimeout(function () {
                cb && cb()
              }, 1000)
            }
          )
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
    },
  },
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="scss" scoped>
.container {
  width: 100%;
  height: 100%;
  &-left {
    width: 100%;
    height: 100%;
    padding-right: 520px;
    background-color: #3486f3;
    display: flex;
    align-items: center;
    // justify-content: center;
    justify-content: flex-start;
    & img {
      // width: 582px;
      // height: 582px;
      max-width: 1380px;
      max-height: 1000px;
      width: 100%;
      height: 100%;
    }
  }
  &-right {
    position: fixed;
    width: 520px;
    height: 100%;
    padding: 56px 80px 40px;
    top: 0;
    right: 0;
    background-color: #fff;
    overflow-y: scroll;
    &-logo {
      align-items: center;
      display: flex;
      & img {
        width: 154px;
      }
    }
  }
  .input-topright-btn {
    position: absolute;
    right: 0;
    top: -26px;
    line-height: 22px;
  }
  .login-btn {
    width: 100%;
    height: 52px;
    font-size: 16px;
  }
  .ubot-mark {
    font-size: 51px;
    color: #226df4;
    font-weight: bold;
  }
}
@media screen and (max-width: 519px) {
  .container {
    width: 100%;
    height: 100%;
    &-left {
      display: none;
    }
    &-right {
      width: 100%;
      height: 100%;
      padding: 20px 24px;
      background-color: #fff;
    }
  }
}
</style>
