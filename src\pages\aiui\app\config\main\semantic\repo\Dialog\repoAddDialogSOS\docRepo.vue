<template>
  <div>
    <div class="new-skill-wrapper">
      <div :class="['skill', 'skill-to-add']" @click="jump">
        <a>
          <i
            class="AIUI-myapp-iconfont ai-myapp-add"
            style="font-size: 20px"
          ></i>
          <p style="margin-top: 10px">去创建文档库</p>
        </a>
      </div>

      <!-- 自定义文档问答 -4.5才有 -->
      <div
        v-for="(item, index) in data"
        :class="['skill', { 'skill-active': item.selected }]"
        @click="toDocCard(item)"
        :key="item.id"
      >
        <div :class="['content-wrap']">
          <i class="skill-icon">{{ item.name && item.name.substr(0, 1) }}</i>
          <div class="skill-info">
            <p class="skill-title" :title="item.name">
              {{ item.name }}
            </p>

            <!-- 标题下按钮 -->
            <div class="title-btm-btn-group">
              <p class="ability-tag">文档问答</p>
            </div>
          </div>
        </div>

        <div @click.stop class="switch-wrap">
          <el-switch
            size="small"
            :value="item.selected"
            @change="(val) => onSwitchChange(val, item)"
            :disabled="!subAccountEditable"
          >
          </el-switch>
        </div>
      </div>
    </div>
    <!-- <p class="empty-skill-tips" v-if="clickSearchVal !== ''">暂无搜索结果</p> -->
  </div>
</template>
<script>
import { mapGetters } from 'vuex'

export default {
  props: {
    data: {
      type: Array,
      default() {
        return []
      },
    },
    dataCopy: {
      type: Array,
      default() {
        return []
      },
    },
  },
  data() {
    return {}
  },
  computed: {
    ...mapGetters({
      subAccountEditable: 'aiuiApp/subAccountEditable',
    }),
  },
  methods: {
    toDocCard(item) {
      console.log('toDocCard', item)
      window.open(`/studio/ragqa/${item.id}/localDoc`, '_blank')
    },
    onSwitchChange(val, item) {
      console.log('onSwitchChange', val, item)
      this.$emit('selectchange', item.id, val)
    },
    jump() {
      window.open('/studio/qaBank', '_blank')
    },
  },
}
</script>

<style lang="scss" scoped>
@import '../common.scss';

.skill-header {
  position: absolute;
  top: 10px;
  right: 20px;
}
.skill-container .skill {
  width: 100%;
}

.skill-header-shrink {
  position: relative;
  width: 100%;
  display: block;
  transform: translateX(0%);
  transition: all 0.4s cubic-bezier(0.42, 0, 1, 1) 0s;
  > ul {
    max-width: 80%;
    :deep(> span) {
      margin: 0 auto !important;
      position: relative;
    }
  }
  > .search-area {
    max-width: 25%;
  }
}

@media screen and (max-width: 1420px) {
  .search-area.fr {
    width: calc(25% - 20px);
  }
}

.add-skill-wrap {
  .skill {
    min-width: 262px;
  }

  .skill-info {
    width: 160px;
  }
  .skill-title {
    max-width: 108px;
  }
}
.skill-icon {
  float: left;
  width: 50px;
  height: 50px;
  line-height: 50px;
  color: $semi-black;
  text-align: center;
  margin-right: 15px;
  font-style: normal;
  font-size: 24px;
  font-family: PingFangSC;
  font-weight: 500;
  color: rgba(23, 132, 233, 1);
}
.skill-info {
  float: left;
  width: 65%;
}
.skill-title {
  max-width: 108px;
  color: $semi-black;
  font-size: 14px;
  margin-bottom: 10px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  vertical-align: middle;
}
.skill-mark {
  position: absolute;
  top: 0px;
  left: 0px;
  display: inline-block;
  padding: 0 2px;
  height: 16px;
  background: #e8f3fd;
  border-radius: 0 0 4px 0;
  color: #1784e9;
  font-size: 12px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  line-height: 16px;
  text-align: center;
}

.private-skill,
.private-repo {
  .skill-title {
    margin-top: 8px;
  }
  .skill-source {
    margin-top: 10px;
    vertical-align: middle;
  }
  .skill-update {
    margin-top: 15px;
    margin-bottom: 5px;
    vertical-align: middle;
    font-size: 12px;
  }
}
.skill-source {
  height: 19px;
  line-height: 8px;
  font-size: 12px;
  margin-left: 5px;
}
.skill-desc {
  font-size: 12px;
  color: $grey5;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.skill-config {
  position: absolute;
  bottom: 10px;
  right: 10px;
  color: $grey4;
  font-size: 18px;
  cursor: pointer;
}
.ic-r-delete {
  display: none;
  top: auto;
  bottom: 10px;
  right: 50px;
}

.empty-skill-tips {
  color: $grey5;
  margin-top: 80px;
  width: 100%;
  text-align: center;
}
.skill-footer {
  position: relative;
  height: 48px;
}
.add-skill {
  margin-top: 8px;
}
.pagination {
  position: absolute;
  top: 0;
  right: 0;
}

.add-skill-desc {
  color: $grey4;
  margin-top: -25px;
  margin-bottom: 32px;
}
.other-skill-source-tips {
  color: $warning;
  margin-top: -27px;
  margin-bottom: 32px;
}
.add-skill-pagination {
  text-align: center;
}
.skill-used {
  position: relative;
  overflow: hidden;
  border: 1px solid $primary;

  &:hover {
    border: 1px solid $primary;
  }

  .ic-r-tick-thin {
    position: absolute;
    right: 5px;
    top: 5px;
    color: #e4e7ed;
    font-size: 12px;
    color: rgba(23, 132, 233, 1);
  }
}

.threshold-label {
  display: inline-block;
  width: 55px;
  font-weight: 600;
  color: $grey5;
}
.threshold-slider {
  display: inline-block;
  vertical-align: middle;
  width: 450px;
}
.threshold-desc {
  color: $grey5;
  margin: 25px 0;
}
.source-list {
  border-top: 1px solid $grey2;
  margin-bottom: 30px;
}
.source-item {
  height: 72px;
  padding: 12px;
  cursor: move;
  border-bottom: 1px solid $grey2;
}
.ic-r-menu {
  color: $grey4;
  font-size: 18px;
  line-height: 48px;
  margin-right: 12px;
}
.source-logo {
  max-width: 48px;
  max-height: 48px;
  border-radius: 50%;
  margin-right: 8px;
}
.source-title {
  color: $semi-black;
  font-weight: 500;
  margin-top: 3px;
}
.source-desc {
  color: $grey5;
  font-size: 12px;
  margin-top: 4px;
}
.source-used {
  margin-top: 12px;
}
.private-skill-number {
  font-size: 12px;
  color: $grey5;
}
.add-threshold-desc {
  color: $grey4;
  margin-bottom: 32px;
}
.add-skill-title {
  line-height: 24px;
  font-size: 18px;
  color: #262626;
  font-weight: 500;
}
.add-qa-threashold {
  color: $grey4;
  margin-bottom: 32px;
  margin-top: -25px;
}
.skill-version-title {
  padding-top: 20px;
  border-top: 1px solid #ccc;
}
.add-skill-search {
  &.charater {
    width: 75%;
    float: right;
    :deep(.el-input__inner) {
      height: 36px;
      line-height: 36px;
    }
  }
}
.table-area {
  height: 328px;
  overflow: hidden;
  :deep(.el-tabs__content) {
    height: 328px;
    overflow-y: scroll;
  }
}
.must-answer-wrap {
  margin: 24px 0 16px;
  color: $grey5;
  font-weight: 600;
  :deep(.el-tooltip) {
    color: $grey3;
  }
  :deep(.el-switch) {
    margin-left: 18px;
  }
}
.cursor-default {
  cursor: default !important;
}

.empty-skill-tips-new {
  position: absolute;
  top: 49%;
  right: -140px;
}

.tab-container {
  display: flex;
  position: relative;
  &::before {
    position: absolute;
    content: ' ';
    width: 100%;
    height: 1px;
    background: #e7e9ed;
    bottom: 0;
  }
}
.skill {
  height: 117px;
}
.label-wrap {
  // clear: both;

  p {
    padding: 0 20px;
    height: 36px;
    line-height: 36px;
    // text-align: center;
    // color: #1784e9;
    // background-color: rgba(227, 240, 252, 1);
    // margin: 0 auto;
    border-radius: 2px;
    margin-bottom: 0;
    // top: 50%;
  }
}
.flex-center {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  padding-bottom: 15px;
}

.new-skill-wrapper {
  width: 100%;
  display: grid;
  grid-template-columns: repeat(3, 1fr); /* 将容器分为3列，每列宽度平均 */
  gap: 18px; /* 设置格子之间的间距 */
  padding: 10px 0;
}
.content-wrap {
  display: flex;
}

.skill-to-add {
  padding-top: 35px;
  text-align: center;
  position: relative;
  background: #fff;
  border: 1px solid #e7e9ed;
  border-radius: 4px;
}
</style>
