<template>
  <div class="os-scroll">
    <div class="relation-detail" v-loading="loadAll">
      <repo-header backToParams="qa-relations"></repo-header>
      <batch
        class="mgt28"
        api="importProperty"
        exportText="导出问答关系"
        :repoId="repoId"
        :themeId="themeId"
        @setLoad="setLoad"
        @getData="getAllData"></batch>
      <basic-info-form :themeInfo="themeInfo" @getInfo="getInfo"></basic-info-form>
      <os-divider />
      <os-collapse size="large" :default="false" title="问法、答案">
        <qa-list type="question"
          listTitle="添加问法"
          searchPlacholder="搜索问法"
          placeholder="回车添加用户常用的问法，例如：{问法关键字}的{关系关键字}是什么"
          :initList="questionList"
          :keyword="keyword"
          :questionExample="questionExample"
          @getInfo="getInfo"></qa-list>
        <qa-list class="mgt28 mgb48" type="answer"
          listTitle="添加答案"
          searchPlacholder="搜索答案"
          placeholder="回车添加用户常见的回复，回复：是{回复语}"
          :initList="answerList"
          :replyExample="replyExample"
          :keyword="keyword"
          @getInfo="getInfo"></qa-list>
      </os-collapse>
      <os-divider />
      <!-- 问法关键字与回复语 -->
      <os-collapse size="large" :default="false" title="问法关键字与回复语">
        <p class="keyword-edit-tip">请在添加问法关键字后添加回复语，无回复语的问法关键字问答将不生效。<br>
          此处问法关键字和别名不能被删除，请前往
          <router-link v-if="!onCreatePage" :to="{name: 'question-keywords', params: {repoId: repoId}}" target="_blank">
            问法关键字 </router-link><span v-else>问法关键字</span>统一管理。</p>
        <keyword-and-reply ref="keywordAndReply" @setQaReply="setQaReply"></keyword-and-reply>
      </os-collapse>
    </div>
  </div>
</template>

<script>
  import RepoHeader from './repoHeader'
  import BasicInfoForm from './basicInfoForm'
  import QaList from './qaList'
  import KeywordAndReply from './keywordAndReply'
  import Batch from './batch'
  export default {
    data() {
      return {
        themeInfo: {},
        questionList: [],
        answerList: [],
        keyword: '',
        questionExample: '',
        replyExample: '',
        loadAll: false
      }
    },
    computed: {
      repoId() {
        return this.$route.params.repoId || ''
      },
      themeId() {
        if(this.$route.path.match('/create')) {
          return ''
        }
        return this.$route.params.themeId || ''
      },
      onCreatePage() {
        if(this.$route.path.match('/create')) {
          return true
        }
        return false
      }
    },
    watch: {
      themeId(val, oldVal) {
        if( val !== oldVal) {
          this.getInfo()
        }
      }
    },
    created() {
      this.getInfo()
    },
    methods: {
      back () {
        this.$router.push({name: 'qa-relations', params: {repoId: this.repoId}})
      },
      getInfo () {
        let self = this
        if(!self.themeId) return
        self.themeInfo.id = ''
        self.answerList.length = 0
        self.questionList.length = 0
        this.$utils.httpGet(this.$config.api.STUDIO_REPO_REL_DETAIL, {
          repositoryId: this.repoId,
          themeId: self.themeId
        }, {
          success: (res) => {
            if(!res.data) {
              return self.$message.error(res.desc || '数据获取失败')
            }
            if (res.data.theme) {
              self.themeInfo = res.data.theme 
              self.keyword = res.data.theme.keyword
            }
            self.answerList = res.data.answerTemplateList && res.data.answerTemplateList.length ? res.data.answerTemplateList : []
            self.questionList = res.data.questionTemplateList && res.data.questionTemplateList.length ? 
              res.data.questionTemplateList : []
          },
          error: (err) => {
            self.$router.push({'name': 'studio-handle-platform-qabanks'})
          }
        })
      },
      setQaReply(question, reply){
        this.questionExample = question
        this.replyExample = reply
      },
      setLoad(val) {
        this.loadAll = val
      },
      getAllData() {
        this.getInfo()
        this.$refs.keywordAndReply.getList(1)
      }
    },
    components: { RepoHeader, BasicInfoForm, QaList, KeywordAndReply, Batch }
  }
</script>

<style lang="scss" scoped>
.relation-detail {
  max-width: 1200px;
  padding: 24px 0 56px;
  margin: auto;
  .top-edit-wrap {
    font-size: 0;
    a {
      margin-left: 12px;
      font-size: 14px;
    }
  }
  .keyword-edit-tip {
    margin-bottom: 24px;
    line-height: 22px;
    font-size: 12px;
  }
}

</style>
<style lang="scss">
.relation-detail {
  .os-collapse-title {
    margin: 28px 0;
    .ic-r-angle-d {
      color: $grey5;
    }
  }
}
</style>
