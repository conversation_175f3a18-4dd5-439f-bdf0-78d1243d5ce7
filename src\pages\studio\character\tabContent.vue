<template>
  <el-table
    :ref="`attrTable${tab.categoryId}`"
    class="small-table"
    :data="tableData.list"
    v-loading="tableData.loading"
    :border="true"
    row-key="id"
    @cell-mouse-enter="handleCellMouseEnter"
    @cell-mouse-leave="handleCellMouseLeave"
    @cell-click="handleCellClick"
    empty-text="暂无参数"
    :expand-row-keys="[1]"
    align="left"
  >
    <el-table-column type="expand" min-width="40">
      <template slot-scope="props">
        <el-alert
          title="设备激活日仅对OS接入设备生效，AIUI应用请勿使用。"
          type="warning"
          center
          v-if="props.row.name === 'birthday'"
        ></el-alert>
        <div class="character-qabank">
          <div class="character-qabank-qa">
            <div class="text">问题</div>
            <div class="list-content">
              <ol class="list">
                <li
                  v-for="(item, index) in props.row.sysQuestionSet"
                  :key="index"
                >
                  {{ item }}
                </li>
              </ol>
            </div>
          </div>
          <div class="character-qabank-answer" v-if="props.row.type === 1">
            <div class="text">回复</div>
            <div class="list-content">
              <el-radio
                v-model="props.row.answerType"
                :label="1"
                class="answer-radio"
                v-if="props.row.answerType !== 3"
                >使用系统回复</el-radio
              >
              <ol
                :class="[
                  'list',
                  'dashed-bottom',
                  {
                    active:
                      props.row.answerType === 1 || props.row.answerType === 3,
                  },
                ]"
              >
                <li
                  v-for="(item, index) in changeSysAnswerSet(props)"
                  :key="index"
                >
                  {{ item }}
                </li>
              </ol>
              <el-radio
                v-model="props.row.answerType"
                :label="2"
                class="answer-radio"
                v-if="props.row.answerType !== 3"
                >自定义</el-radio
              >
              <os-text-adder
                v-if="props.row.answerType !== 3"
                :data="props.row.answerSet || []"
                data-key="answer"
                @add="addAnswer(props.row, arguments)"
                @edit="edit(props.row, arguments)"
                @del="delAnswer(props.row, arguments)"
                @change="change(props.row, arguments)"
                @keyup.delete.native="deleteFn"
                :props="props"
                :ref="'textAdder' + props.row.id"
                width="502px"
                placeholder="输入后回车添加。"
              />
            </div>
          </div>
        </div>
      </template>
    </el-table-column>
    <el-table-column label="状态">
      <template slot-scope="scope">
        <el-switch
          v-model="scope.row.status"
          :active-value="1"
          :inactive-value="0"
        />
      </template>
    </el-table-column>
    <el-table-column
      label="属性"
      min-width="100"
      prop="zhName"
    ></el-table-column>
    <el-table-column
      label="英文标识"
      min-width="120"
      prop="name"
    ></el-table-column>
    <el-table-column label="属性值" min-width="320" prop="type">
      <template slot-scope="scope">
        <editInput
          :scope="scope"
          :activeName="activeName"
          ref="editInput"
          @birthdateHandle="birthdateHandle"
          @openExpand="
            (v) => {
              openExpand(scope.row, v)
            }
          "
          @changeSysAnswerSet="changeSysAnswerSet"
        ></editInput>
      </template>
    </el-table-column>
    <el-table-column label="类别" min-width="100">
      <template slot-scope="scope">{{
        tabsObj[scope.row.categoryId]
      }}</template>
    </el-table-column>
    <el-table-column label="问答" min-width="100">
      <template slot-scope="scope">
        <a @click="openExpand(scope.row)">{{
          scope.row.answerType === 3 ? '查看回复' : '编辑回复'
        }}</a>
      </template>
    </el-table-column>
  </el-table>
</template>
<script>
import EditInput from './editInput.vue'
import { differenceWith, isEqual, throttle } from 'lodash-es'
import { mapGetters } from 'vuex'

export default {
  props: {
    activeName: {
      type: String,
    },
    tab: {
      type: Object,
    },
    tabsObj: {
      type: Object,
    },
  },
  components: {
    EditInput,
  },
  data() {
    return {
      tableData: {
        loading: true,
        total: 0,
        page: 1,
        size: 999,
        list: [],
      },
      originTableData: {},
      edited: false,
    }
  },

  created() {
    if (this.$route.params.characterId) {
      this.characterId = this.$route.params.characterId
      //   this.characterName = this.$route.query.characterName;
      this.getData()
    }
  },
  computed: {
    // categoryName(v){
    //   this.tabs.map(item=>{
    //       if(item.categoryId===v){
    //         return item.categoryName
    //       }
    //   })
    // }
    ...mapGetters({
      limitCount: 'aiuiApp/limitCount',
    }),
  },

  watch: {
    tableData: {
      handler(val, oldVal) {
        let diffArr = differenceWith(
          val.list,
          this.originTableData.list,
          isEqual
        )
        console.log(diffArr)
        //这里不能通过val===oldval进行判断是否改变 因为完全相同。原因：在修改（不是替换）对象或数组时，旧值将与新值相同，因为它们索引同一个对象/数组。Vue 不会保留修改之前值的副本。
        if (diffArr.length) {
          this.$emit('isEdited', this.activeName, diffArr, true)
        } else {
          this.$emit('isEdited', this.activeName, diffArr, false)
        }
      },
      //   immediate: true,
      deep: true,
    },
  },
  methods: {
    dealData(data) {
      data.map((property) => {
        let arr = []
        property.answerSet.map((item) => {
          arr.push({ answer: item })
        })
        property.answerSet = arr
      })
      return data
    },
    compare(origin, target) {
      if (typeof target !== 'object') {
        //target不是对象/数组
        return origin === target //直接返回全等的比较结果
      }

      if (typeof origin !== 'object') {
        //origin不是对象/数组
        return false //直接返回false
      }
      for (let key of Object.keys(target)) {
        //遍历target的所有自身属性的key
        if (!this.compare(origin[key], target[key])) {
          //递归比较key对应的value，
          //value不等，则两对象不等，结束循环，退出函数，返回false
          return false
        }
      }
      //遍历结束，所有value都深度比较相等，则两对象相等
      return true
    },
    getData() {
      let self = this

      this.tableData.loading = true
      this.$utils.httpPost(
        this.$config.api.STUDIO_CHARACTER_ATTRIBUTES,
        {
          repositoryId: this.characterId,
          category: parseInt(this.activeName),
          pageIndex: this.tableData.page,
          pageSize: 999,
        },
        {
          success: (res) => {
            self.tableData.list = []
            self.originTableData = []
            self.$nextTick(function () {
              self.tableData.list = this.dealData(res.data.list)
              this.originTableData = this.$deepClone(self.tableData)
            })
            self.tableData.total = res.data.count
            self.tableData.page = res.data.pageIndex
            self.tableData.size = res.data.pageSize
            self.tableData.loading = false
            self.characterName = res.data.context.name
            self.characterId = res.data.context.id
            // self.$store.dispatch(
            //   'studioCharacter/setCharacter',
            //   res.data.context
            // )
            this.$emit('getTableData', res.data)
            // this.tabData["tab" + this.activeName] = this.$deepClone(
            //   self.tableData
            // );
          },
          error: (err) => {
            console.error(err)
          },
        }
      )
    },
    handleTab(tab, event) {
      this.getData()
    },
    changeSysAnswerSet(scope) {
      let sysAnswerSet = scope.row.sysAnswerSet
      let key = scope.row.name
      let value = scope.row.value
      let answer = []
      let emptyAnswer = []
      sysAnswerSet.map((item) => {
        if (item.indexOf(key) !== -1) {
          answer.push(item)
        } else {
          emptyAnswer.push(item)
        }
      })
      // console.log(answer,emptyAnswer)
      if (!value) {
        return emptyAnswer.splice(0, 3)
      } else {
        return answer.splice(0, 3)
      }
    },
    openExpand(row, v) {
      this.$refs[`attrTable${this.activeName}`] &&
        this.$refs[`attrTable${this.activeName}`].toggleRowExpansion(row, v)
    },
    handleCellMouseEnter(row, column, cell) {
      let type = cell.getElementsByClassName('editInput')[0]
        ? cell.getElementsByClassName('editInput')[0].getAttribute('data-type')
        : 'unedit'
      if (type !== 'unedit') {
        cell.classList.add('small-table__border-hover')
      }
    },
    handleCellMouseLeave(row, column, cell) {
      cell.classList.remove('small-table__border-hover')
    },
    handleCellClick(row, column, cell) {
      let type = cell.getElementsByClassName('editInput')[0]
        ? cell.getElementsByClassName('editInput')[0].getAttribute('data-type')
        : 'unedit'

      let self = this
      if (type === 'input' || 'birthday' || 'autocomplete') {
        setTimeout(function () {
          cell.getElementsByClassName('el-input__inner')[0] &&
            cell.getElementsByClassName('el-input__inner')[0].focus()
        }, 4)
      }
      //  if (type === "birthday") {
      //   setTimeout(function() {
      //     cell.getElementsByClassName('el-input__inner')[0] && cell.getElementsByClassName('el-input__inner')[0].focus()
      //   }, 4);
      // }
      if (type === 'tag') {
        setTimeout(function () {
          cell
            .getElementsByClassName('el-input')[0]
            .classList.toggle('show', true)
          cell.getElementsByClassName('el-input__inner')[0].focus()
        }, 4)
      }

      if (type !== 'unedit') {
        cell.classList.toggle('small-table__border', true)
      }
    },

    delTag(row, index) {
      row.value.splice(index, 1)
    },
    handleTagInputEnter(row) {
      this.addTag(row)
    },
    handleTagInputSpace(row) {
      let canAdd = row.addTags.split(' ').some((tag) => {
        return tag === ''
      })
      if (canAdd) {
        this.addTag(row)
      }
    },

    addTag(row) {
      let self = this
      if (row.addTags) {
        let checkTag = false
        let tags = Array.prototype.filter.call(
          row.addTags.split(' '),
          (tag) => {
            return !self.$utils.inArray(row.value, tag) && tag != ''
          }
        )
        tags.forEach((tag) => {
          if (tag.length > 5) {
            self.$message.warning(`标签：${tag}，不能大于5个字符`)
            checkTag = true
          }
        })
        if (checkTag) {
          return
        }
        row.value = row.value.concat(tags)
        row.addTags = ''
      }
    },
    birthdateHandle(v) {
      let oldAge, oldConstellation, oldZodiac, oldBirthday
      this.originTableData.list.map((item) => {
        if (item.name === 'age') {
          oldAge = item.value
        }
        if (item.name === 'constellation') {
          oldConstellation = item.value
        }
        if (item.name === 'zodiac') {
          oldZodiac = item.value
        }
      })

      this.tableData.list.map((item) => {
        if (item.name === 'age') {
          item.value = v.zodiac ? v.age : oldAge
        }
        if (item.name === 'constellation') {
          item.value = v.zodiac ? v.constellation : oldConstellation
        }
        if (item.name === 'zodiac') {
          item.value = v.zodiac ? v.zodiac : oldZodiac
        }
        if (item.name === 'birthday') {
          item.value = v.value
        }
      })
    },
    checkAnswer(text) {
      var patrn =
        /[`~@#$^&()_\<>"|'\\[\]·~@#￥……&（）——\|《》“”【】、‘’、={}^——「」;；]/im
      // 限制不限制字符
      let notLimit =
        this.limitCount.device_answer_char &&
        Number(this.limitCount.device_answer_char) === 0
      if (notLimit) {
      } else {
        if (patrn.test(text)) {
          this.$message.warning(
            '回复中支持的符号包括冒号，逗号，问号，感叹号（以上符号中英文状态都支持）%+-×*÷/'
          )
          return false
        }
      }
      let charLen =
        (this.limitCount.device_answer_char_len &&
          Number(this.limitCount.device_answer_char_len)) ||
        100
      if (text.length > charLen) {
        this.$message.warning(`每条回复限制${charLen}个字符`)
        return false
      }
      return true
    },
    addAnswer(row, textArr) {
      let text = textArr[0]
      if (row.answerSet.length >= 30) {
        return this.$message.warning('一个属性最多支持30条回复')
      }
      if (this.checkAnswer(text)) {
        row.answerSet.push({
          answer: text,
        })
      }
      // var patrn = /[`~@#$^&()_\<>:"|'\\[\]·~@#￥……&（）——\|《》：“”【】、‘’、={}^——「」]/im;
      // if (patrn.test(text)) {
      //   return this.$message.warning("回复中符号仅支持，。！？.!?%+-×*÷/;；");
      // }
      // if (text.length > 100) {
      //   return this.$message.warning("每条回复限制100个字符");
      // }
      // if (row.answerSet.length >= 30) {
      //   return this.$message.warning("一个属性最多支持30条回复");
      // } else {
      //   row.answerSet.push({
      //     answer: text
      //   });
      // }
    },
    edit: throttle(function (row, arr) {
      this.editAnswer(row, arr)
    }, 500),
    editAnswer(row, arr) {
      let item = arr[0]
      //这里有疑问 为什么change的时候返回的是字符串 blur的时候返回的是对象
      if (typeof item === 'object') {
        if (!item.answer) {
          if (arr[1] !== undefined) {
            row.answerSet.splice(arr[1], 1)
          }
          return
        }
        if (!this.checkAnswer(item.answer)) {
          return
        }
        item.answer = item.answer.trim()
      } else {
        if (!this.checkAnswer(item)) {
          return
        }
        item = item.trim()
      }
    },
    delAnswer(row, arr) {
      let index = arr[1]
      row.answerSet.splice(index, 1)
    },

    change: throttle(function (row, arr) {
      row.answerType = 2
      // this.changeHandle(row, arr);
    }, 500),

    changeHandle(row, arr) {
      let f = arr[0]
      let item = arr[1]
      let index = arr[2]
      row.answerType = 2
      let attribute = row.name
      if (!this.flag) {
        if (f) {
          if (item.charAt(item.length - 1) === '{') {
            let str = item.replace(/{$/g, '{' + attribute + '}')
            this.$refs[`textAdder${row.id}`].changeInputValue(str)
          } else if (item.charAt(item.length - 1) === '}') {
            let str = item.replace(/{}/g, '{' + attribute + '}')
            this.$refs[`textAdder${row.id}`].changeInputValue(str)
          }
        } else {
          if (item.charAt(item.length - 1) === '{') {
            let str = item.replace(/{$/g, '{' + attribute + '}')
            row.answerSet[index].answer = str
          } else if (item.charAt(item.length - 1) === '}') {
            let str = item.replace(/{}/g, '{' + attribute + '}')
            row.answerSet[index].answer = str
          }
        }
      }
    },
    deleteFn: throttle(function (item) {
      // this.deleteHandle(item);
    }, 500),

    deleteHandle(item) {
      this.flag = true
      let val = item.srcElement.value.charAt(item.srcElement.value.length - 1)
      let val2 = item.srcElement.value.charAt(item.srcElement.value.length - 2)
      let val3 = item.srcElement.value.charAt(item.srcElement.value.length - 3)
      if (val2 === '{') {
        this.flag = true
      } else if (val === '}' && val3 === '{') {
        this.flag = true
      } else {
        this.flag = false
      }
    },
  },
}
</script>
