<template>
  <div
    v-if="agentPopover.show"
    v-clickoutside="closePopover"
    class="el-popover el-popper el-popover--plain skill-select-popover"
    :style="popperStyle"
    x-placement="bottom"
  >
    <div>
      <div class="skill-select-popover__head" @keyup.enter="initData">
        <div class="skill-select-popover-search">
          <el-input
            class="search-area"
            placeholder="搜索智能体"
            v-model="searchName"
          >
          </el-input>
        </div>
      </div>
      <div class="skill-select-popover__body">
        <div
          class="skill-select-popover-list"
          v-loadmore="scrollLoad"
          v-loading="loading"
        >
          <div
            class="skill-select-popover-item"
            v-for="(item, index) in agentData.list"
            :key="index"
            @click="selectItem(item)"
          >
            <span :title="item.agentName">{{ item.agentName }}</span>
          </div>
          <div class="el-table__empty-block" v-if="!agentData.list.length">
            <span class="el-table__empty-text">暂无数据</span>
          </div>
        </div>
        <os-divider />
        <div class="skill-select-popover-add">
          <i class="ic-r-plus" />
          <a @click="addAgent">创建新智能体</a>
        </div>
      </div>
    </div>
    <div x-arrow="" class="popper__arrow" style="left: 20px"></div>

    <CreateAgent ref="CreateAgent"></CreateAgent>
  </div>
</template>

<script>
import CreateAgent from '@P/studio/handlePlatform/agent/createAgent.vue'
export default {
  name: 'IflyAIuiWebSelectAgent',

  components: {
    CreateAgent,
  },

  data() {
    return {
      agentPopover: {
        show: false,
      },
      rect: {
        top: 0,
        left: 0,
        width: 0,
      },
      searchName: '',
      loading: false,
      agentData: {
        loading: true,
        loadend: false,
        total: 0,
        page: 1,
        size: 20,
        list: [],
      },
      debounced: null,
    }
  },

  computed: {
    popperStyle() {
      if (this.rect) {
        return {
          top: `${this.rect.top + 20}px`,
          left: '20px',
        }
      } else {
        return {
          display: `none`,
        }
      }
    },
  },

  mounted() {
    this.initData()
    this.setDebounce()
  },

  methods: {
    show(rect) {
      this.agentPopover.show = true
      console.log(rect, '这个打印传过来的rect')
      this.rect = rect
    },
    closePopover() {
      this.agentPopover.show = false
      this.searchName = null
    },

    setDebounce() {
      this.debounced = this.$utils.debounce(
        () => {
          this.initData()
        },
        500,
        true
      )
    },

    scrollLoad() {
      if (this.agentData.loading || this.agentData.loadend) {
        return
      }
      this.initData()
    },

    selectItem(item) {
      let routeData,
        routeName = this.$route.name
      //   console.log(routeName, '这个是this.$route.name')
      console.log(item, '这个是选择的时候的item')
      let nextRouteName = ''
      routeData = this.$router.resolve({
        name: `${nextRouteName}`,
        params: { agentId: item.agentId },
      })
      location.href = routeData.href
    },

    initData() {
      const params = {
        agentId: null,
        agentName: this.searchName,
      }
      this.$utils.httpPost(
        this.$config.api.AGENT_ALL_LIST_OLD,
        JSON.stringify(params),
        {
          config: {
            headers: {
              'Content-Type': 'application/json;charset=UTF-8',
            },
          },
          success: (res) => {
            console.log('全量智能体res', res)
            this.agentData.list = res.data
          },
          error: (err) => {},
        }
      )
    },

    addAgent() {
      // this.$refs.CreateAgent.show()
      this.agentPopover.show = false
      localStorage.setItem('addAgent', true)
      this.$router.push({
        name: 'studio-handle-platform-agent',
      })
    },
  },
}
</script>

<style lang="scss" scoped></style>
