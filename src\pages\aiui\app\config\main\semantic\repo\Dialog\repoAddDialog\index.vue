<template>
  <el-dialog
    title="添加问答库"
    :visible.sync="dialog.show"
    width="760px"
    top="5vh"
    @closed="closeSkillDialog"
  >
    <CommonDialogBody
      abbBtnText="创建文档库"
      tagText="文档问答"
      jumpUrl="/studio/qaBank"
      switchKey="used"
      :scrollHeight="316"
      :ragData="ragData"
      :ragDataCopy="ragDataCopy"
      :loading="loading"
      @selectchange="onSelectchange"
      @toDocCard="toDocCard"
    >
      <template #search>
        <el-input
          size="small"
          class="search-area"
          placeholder="搜索"
          v-model.trim="searchVal"
          @keyup.enter.native="searchAppConfig"
          style="width: 258px"
        >
          <i
            @click.stop.prevent="searchAppConfig"
            slot="suffix"
            class="el-input__icon el-icon-search search-area-btn"
          />
        </el-input>
      </template>
    </CommonDialogBody>

    <span slot="footer" class="dialog-footer">
      <el-button size="small" @click="dialog.show = false">取消</el-button>
      <el-button
        size="small"
        type="primary"
        @click="saveChangeData"
        :loading="saveLoading"
        :disabled="!switchHasChange"
      >
        保存配置
      </el-button>
    </span>
  </el-dialog>
</template>
<script>
import { mapGetters } from 'vuex'
import CommonDialogBody from '../commonDialogBody/index.vue'
export default {
  props: {
    dialog: Object,
    appId: '',
    currentScene: Object,
  },
  data() {
    return {
      saveLoading: false,
      clickSearchVal: '',
      searchVal: '',

      ragData: [],
      ragDataCopy: [],
      originData: [],
      loading: false,

      switchHasChange: false,
    }
  },
  computed: {
    ...mapGetters({
      skillIconBgColors: 'studioSkill/skillIconBgColors',
    }),
  },
  methods: {
    toDocCard(item) {
      window.open(`/studio/ragqa/${item.repoCode}/localDoc`, '_blank')
    },
    onSelectchange(item, val) {
      const code = item.repoCode
      this.ragData = this.ragData.map((item) => {
        if (item.repoCode === code) {
          return {
            ...item,
            used: val,
          }
        } else {
          return { ...item }
        }
      })
      this.ragDataCopy = this.ragDataCopy.map((item) => {
        if (item.repoCode === code) {
          return {
            ...item,
            used: val,
          }
        } else {
          return { ...item }
        }
      })
      if (this.computeDelta().length > 0) {
        this.switchHasChange = true
      } else {
        this.switchHasChange = false
      }
    },

    saveChangeData() {
      let delta = this.computeDelta()
      let param = {
        appid: this.appId,
        chainId: this.currentScene.chainId || 'cbm_v45',
        sceneName: this.currentScene.sceneBoxName,
      }

      if (delta.length > 0) {
        param.knowledgeConfig = JSON.stringify(delta)
      }

      let allPromises = [this.saveChangeKnowl45(param)]

      if (param.knowledgeConfig) {
        this.saveLoading = true
        Promise.all(allPromises)
          .then(() => {
            this.saveLoading = false
            this.switchHasChange = false
            this.$emit('saveSuccess')
            this.$message.success('保存成功')
            this.dialog.show = false
          })
          .catch((err) => {
            this.saveLoading = false
            this.$message.error(err)
          })
      }
    },

    saveChangeKnowl45(param) {
      return new Promise((resolve, reject) => {
        this.$utils.httpPost(
          this.$config.api.AIUI_APP_PLUGINSTUDIO_SAVECONFIG,
          param,
          {
            success: (res) => {
              resolve()
            },
            error: (err) => {
              console.log('err', err)
              reject(err.desc)
            },
          }
        )
      })
    },

    // 计算delta [{"repoId":"shhhkkk","operation":"open","groupId":"group_842e9d1f0b1dcd944651a615"}]
    computeDelta() {
      let delta = []
      this.ragDataCopy.forEach((data) => {
        const originData = this.originData.find(
          (item) => item.repoCode === data.repoCode
        )
        if (data.used !== originData.used) {
          if (data.used) {
            // 打开的情况
            delta.push({
              repoId: data.repoId,
              groupId: data.groupId,
              operation: 'open',
            })
          } else {
            // 关闭的情况
            delta.push({
              repoId: data.repoId,
              groupId: data.groupId,
              operation: 'close',
            })
          }
        }
      })

      return delta
    },

    // 关闭弹窗
    closeSkillDialog() {
      // 搜索框清空
      this.clickSearchVal = ''
      this.searchVal = ''
    },
    searchAppConfig() {
      this.clickSearchVal = this.searchVal
      // this.$emit('search', this.clickSearchVal)
      this.ragData = this.ragDataCopy.filter((it) =>
        it.name.includes(this.clickSearchVal)
      )
    },

    getAppRagConfig() {
      let that = this
      this.loading = true
      this.$utils.httpGet(
        this.$config.api.AIUI_APP_PLUGINSTUDIO_USABLELIST,
        {
          appid: this.appId,
          sceneName: this.currentScene.sceneBoxName,
          type: '6,7',
          fromSource: 'cbm_v45',
          abilityId: 'cbm_intent_domain_agent_default',
        },
        {
          success: (res) => {
            console.log(res, '这个是知识库的res')
            that.loading = false

            const agentArr = JSON.parse(JSON.stringify(res.data.kbs || []))
            const colors = this.skillIconBgColors
            agentArr.forEach((item, index) => {
              item.color = colors[index % colors.length]
            })

            that.originData = agentArr
            that.ragDataCopy = agentArr
            that.ragData = agentArr || []
          },
          error: (res) => {},
        }
      )
    },
  },
  watch: {
    'dialog.show'(val) {
      if (val) {
        this.getAppRagConfig()
      }
    },
  },
  components: { CommonDialogBody },
}
</script>
<style lang="scss" scoped>
.skill-header {
  position: absolute;
  top: 12px;
  right: 20px;
}
.tab-container {
  display: flex;
  position: relative;
  &::before {
    position: absolute;
    content: ' ';
    width: 100%;
    height: 1px;
    background: #e7e9ed;
    bottom: 0;
  }
}

.skill-type {
  margin-top: 1%;
  margin-bottom: 1%;
}
.add-skill-tab {
  a {
    display: inline-block;
    width: 108px;
    height: 40px;
    line-height: 40px;
    cursor: pointer;
    color: unset;
    text-align: center;
  }
  .active {
    position: relative;
    color: $primary;
    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      display: inline-block;
      width: 88px;
      height: 2px;
      background-color: #1f90fe;
      border-radius: 2px;
      transform: translateX(-50%);
    }
  }
}

.el-tabs {
  margin-left: 20px;
}
:deep(.el-dialog) {
  .el-dialog__body {
    border-top: 1px solid #e1e1e1;
    background-color: #f7f7fa;
  }
}
</style>
