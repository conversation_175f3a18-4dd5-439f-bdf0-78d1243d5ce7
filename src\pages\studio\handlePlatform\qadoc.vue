<template>
  <div class="os-scroll">
    <div class="handle-platform-content">
      <div
        class="mgb24"
        style="
          display: flex;
          justify-content: space-between;
          flex-direction: row-reverse;
        "
      >
        <el-button
          icon="ic-r-plus"
          type="primary"
          size="medium"
          @click="openCreateQabank('qa', 0)"
        >
          &nbsp;创建文档问答库
        </el-button>

        <div @keyup.enter="getQabankList(1)">
          <el-input
            class="search-area"
            placeholder="输入文档问答库名称"
            size="medium"
            v-model="searchVal"
          >
            <i
              slot="suffix"
              class="el-input__icon el-icon-search search-area-btn"
              @click="getQabankList(1)"
            />
          </el-input>
        </div>
      </div>
      <os-table
        class="qabanks-table gutter-table-style transparent-bgc"
        :height="'calc(100vh - 230px)'"
        style="margin-bottom: 15px"
        :tableData="tableData"
        @change="getQabankList"
        @row-click="toEdit"
        v-if="hasItem"
      >
        <el-table-column width="320" label="文档问答库名称">
          <template slot-scope="scope">
            <div
              class="text-blod cp qabank-page-qa-zh-name ellipsis"
              :title="scope.row.name"
              @click.stop.prevent="toEdit(scope.row)"
            >
              {{ scope.row.name || '-' }}
              <!-- <i class="ic-qa-repository repo" v-if="scope.row.type == 2"
                >知识库</i
              >
              <i class="ic-qa-repository key-qa" v-else-if="scope.row.type == 3"
                >关键词问答</i
              >
              <i class="ic-qa-repository repo" v-else-if="scope.row.type == 5"
                >文档问答</i
              >
              <i class="ic-qa-repository" v-else>语句问答</i> -->
            </div>
          </template>
        </el-table-column>
        <el-table-column width="120" label="文档数量">
          <template slot-scope="scope">
            <div>{{ scope.row.docCount }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="updateTime" width="200" label="更新时间">
          <template slot-scope="scope">
            <div>{{ scope.row.updateTime | date('yyyy-MM-dd') }}</div>
          </template>
        </el-table-column>
        <el-table-column width="120" label="被引用数">
          <template slot-scope="scope">
            <div
              v-if="scope.row.useCount"
              class="text-primary"
              style="cursor: pointer; height: 40px; line-height: 40px"
              @click.prevent.stop="openCountDialog(scope.row)"
            >
              {{ scope.row.useCount }}
            </div>
            <span v-else>{{ scope.row.useCount }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120">
          <template slot-scope="scope">
            <el-tooltip
              class="item"
              effect="dark"
              content="编辑"
              placement="top"
            >
              <i class="ic-r-edit cell-handle-ic"></i>
            </el-tooltip>

            <i
              class="cell-handle-hovershow ic-r-delete cell-handle-ic"
              @click.prevent.stop="del(scope.row)"
            ></i>
          </template>
        </el-table-column>
      </os-table>
      <div class="create-guide" v-else>
        <div class="icon"></div>
        <p class="title">
          你还没有创建任何文档问答库，
          <a @click="openCreateQabank('qa', 0)"> 点击创建 </a>
        </p>
      </div>
    </div>
    <create-qa-dialog :dialog="dialog" @change="getQabankList" />
    <skill-quote-dialog :dialog="countDialog" type="qabank" />
    <first-enter-dialog :dialog="firstDialog" />
  </div>
</template>

<script>
import CreateQaDialog from './dialog/createQaDoc.vue'
import SkillQuoteDialog from './dialog/docQuote.vue'
import FirstEnterDialog from './dialog/firstEnterDialog.vue'

// import SkillQuoteDialog from './dialog/skillQuote.vue'
import { mapGetters } from 'vuex'

export default {
  name: 'studio-handle-platform-qabanks',
  data() {
    return {
      nav: 'qadoc',
      searchVal: '',
      tableData: {
        loading: true,
        total: 0,
        page: 1,
        size: 10,
        list: [],
      },
      dialog: {
        show: false,
        name: '',
        type: 0,
      },
      countDialog: {
        show: false,
        groupId: '',
      },
      firstDialog: {
        show: false,
      },
      hasItem: true,
    }
  },
  created() {
    this.getQabankList(1)
    console.log(
      `----localStorage.getItem('pageHandle')----,`,
      localStorage.getItem('pageHandle')
    )
    // if (localStorage.getItem('pageHandle') === 'createRepo') {
    //   this.dialog.show = true
    //   this.dialog.name = 'repo'
    //   this.dialog.type = 2
    //   localStorage.setItem('pageHandle', null)
    // }
    // if (localStorage.getItem('pageHandle') === 'createQa') {
    //   this.dialog.show = true
    //   this.dialog.name = 'qa'
    //   this.dialog.type = 0
    //   localStorage.setItem('pageHandle', null)
    // }
    // if (localStorage.getItem('pageHandle') === 'createKeyQa') {
    //   this.dialog.show = true
    //   this.dialog.name = 'qa'
    //   this.dialog.type = 3
    //   localStorage.setItem('pageHandle', null)
    // }
    if (localStorage.getItem('pageHandle') === 'createDocQa') {
      // this.dialog.show = true
      // this.dialog.name = 'qa'
      // this.dialog.type = 5
      this.$nextTick(() => {
        this.openCreateQabank('qa', 0)
        localStorage.setItem('pageHandle', null)
      })
    }

    // if (localStorage.getItem('hasShowDocRepo') !== '1') {
    //   this.firstDialog.show = true
    //   localStorage.setItem('hasShowDocRepo', '1')
    // }
  },
  computed: {
    ...mapGetters({
      userInfo: 'user/userInfo',
      subAccountInfo: 'user/subAccountInfo',
      limitCount: 'aiuiApp/limitCount',
    }),
  },
  methods: {
    getQabankList(page) {
      let self = this
      this.tableData.loading = true
      this.$utils.httpPost(
        this.$config.api.AIUI_KNOWLEDGE_GET_REPO_LIST,
        {
          pageIndex: page || this.tableData.page,
          pageSize: this.tableData.size,
          name: this.searchVal,
        },
        {
          success: (res) => {
            if (res.data.repoPage.total <= 0 && !self.searchVal) {
              self.hasItem = false
            } else {
              self.hasItem = true
            }
            self.tableData.list = res.data.repoPage.records
            self.tableData.total = res.data.repoPage.total
            self.tableData.page = res.data.repoPage.current
            self.tableData.size = res.data.repoPage.size
            self.tableData.loading = false
          },
          error: (err) => {},
        }
      )
    },
    openCreateQabank(name, type) {
      this.dialog.show = true
      this.dialog.name = name
      this.dialog.type = type
    },
    // 打开引用技能的dialog
    openCountDialog(item) {
      this.countDialog.show = true
      this.countDialog.groupId = item.groupId
    },
    toEdit(data) {
      if (!data) return
      window.open(`/studio/ragqa/${data.id}/localDoc`, '_blank')
    },
    del(data) {
      let self = this
      if (data.useCount) {
        this.$message.warning(
          `该文档问答库已被设备/应用引用，请先取消引用后再删除`
        )
        return
      }
      this.$confirm(
        `文档问答库删除后不可恢复，请谨慎操作。`,
        `确定删除文档问答库 - ${data.name}`,
        {
          confirmButtonText: '确定删除',
          cancelButtonText: '取消',
          confirmButtonClass: 'el-button--danger',
          type: 'warning',
          showClose: false,
        }
      )
        .then(() => {
          self.delReop(data)
        })
        .catch(() => {})
    },
    delReop(data) {
      let self = this
      this.tableData.loading = true
      this.$utils.httpPost(
        this.$config.api.AIUI_KNOWLEDGE_REPO_DELETE,
        {
          repoId: data.id,
        },
        {
          success: (res) => {
            self.$message.success('删除成功')
            if (self.tableData.list.length === 1 && self.tableData.page > 1) {
              self.tableData.page -= 1
            }
            self.getQabankList()
          },
          error: (err) => {
            this.tableData.loading = false
          },
        }
      )
    },
  },
  components: {
    // HandlePlatformTop,
    CreateQaDialog,
    SkillQuoteDialog,
    FirstEnterDialog,
  },
}
</script>

<style lang="scss" scoped>
.handle-platform-content {
  // max-width: 1200px;
  width: 100%;
  margin: auto;
}

.search-area {
  width: 480px;
}

.entity-status {
  width: 8px;
  height: 8px;
  border: 2px solid;
  border-radius: 8px;
  margin-right: 6px;

  &-0 {
    border-color: $grey5;
  }

  &-1 {
    border-color: $success;
  }

  &-2 {
    border-color: $grey4;
  }

  &-txt {
    color: $grey5;
  }

  &-count {
    color: $primary;
  }
}

.ic-qa-repository {
  margin-right: 4px;
  // width: 36px;
  width: auto;
  padding: 0 8px;
  height: 20px;
  line-height: 1.5;
  font-size: 12px;
  font-weight: 500;
  color: #0381ec;
  border-radius: 10px;
  text-align: center;
  background-color: $primary-light-12;
}

.repo {
  color: $dangerous;
  background: $dangerous-light-12;
}

.key-qa {
  color: $success;
  background: $success-light-12;
}

.create-guide {
  margin: 76px 0;
  text-align: center;
  font-size: 16px;
  color: $grey5;

  .icon {
    margin: 0 auto 24px;
    width: 120px;
    height: 120px;
    background: url(../../../assets/images/app/create-app.png) center no-repeat;
    background-size: 100%;
  }

  .title {
    font-size: 16px;
    font-weight: 600;

    a {
      font-weight: 600;
    }
  }

  .desc {
    margin: 24px auto;
    width: 480px;
    font-size: 14px;
    line-height: 22px;
  }
}
</style>
<style lang="scss">
.el-table--enable-row-hover .el-table__body tr:hover > td {
  .qabank-page-qa-zh-name {
    color: $primary;
  }
}

.el-table .ic-r-edit {
  color: $primary;
}

.qabanks-table {
  .el-table .cell {
    cursor: pointer;
  }
}
</style>
