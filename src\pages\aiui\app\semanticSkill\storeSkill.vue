<template>
  <div>
    <!-- tab -->
    <div class="skill-type add-skill-tab tab-container">
      <a
        ref="aTab"
        class="a-tab"
        :class="{ active: skillTypeKey === '' }"
        style="cursor: pointer"
        @click="onCickTab('')"
      >
        自定义
      </a>
      <a
        ref="aTab"
        class="a-tab"
        :class="{ active: skillTypeKey === skillTypeKey_ }"
        style="cursor: pointer"
        @click="onCickTab(skillTypeKey_)"
        v-for="(skillTypeValue, skillTypeKey_) in skillTypeList"
        :key="skillTypeKey_"
      >
        {{ skillTypeValue }}
      </a>
    </div>
    <div class="store-skill">
      <!-- <div class="must-answer-wrap" /> -->
      <div
        :class="['skill-wrap']"
        ref="skillListContainer"
        @scroll="handleScroll"
      >
        <!-- 自定义技能 -->
        <slot></slot>
        <div
          class="flex-center"
          v-for="(skillTypeValue, skillTypeKey_) in skillTypeList"
          :key="skillTypeKey_"
        >
          <template v-if="filterSkillList(skillTypeKey_).length !== 0">
            <!-- 类别渲染 -->
            <div class="skill-type-title" :id="`skill_type_${skillTypeKey_}`">
              <div class="empty-card title">{{ skillTypeValue }}</div>
              <div class="empty-card" v-for="i in Array(8)" :key="i"></div>
            </div>

            <!-- 技能卡牌渲染 -->
            <div class="new-skill-wrapper">
              <div
                class="skill"
                :class="{ 'skill-active': item.used }"
                v-for="(item, index) in filterSkillList(skillTypeKey_)"
                :key="index"
                @click="toStoreSkill(item, $event)"
              >
                <div
                  v-if="item.isOffShelf"
                  class="update-state-label"
                  style="background: #ddd"
                >
                  已下线
                </div>
                <div class="content-wrap">
                  <!-- <img class="skill-icon" v-if="item.url" :src="item.url" /> -->
                  <!-- <img class="skill-icon" v-if="item.url" v-lazy="item.url" /> -->
                  <el-image :src="item.url" v-if="item.url">
                    <div slot="error" class="image_slot">
                      <i class="skill-icon">{{ item.zhName.substr(0, 1) }}</i>
                    </div>
                  </el-image>
                  <i class="skill-icon" v-else-if="item.zhName">{{
                    item.zhName.substr(0, 1)
                  }}</i>
                  <i class="skill-icon" v-else-if="item.name">{{
                    item.name.substr(0, 1)
                  }}</i>
                  <div class="skill-info">
                    <p class="skill-title ib" :title="item.zhName || item.name">
                      {{ item.zhName || item.name }}
                    </p>
                    <p
                      class="private-skill-number"
                      v-if="item.used && item.outNumber"
                      :title="item.outNumber"
                    >
                      {{ item.outNumber }}
                    </p>
                    <p
                      class="private-skill-number"
                      v-if="!item.used && item.newestNumber"
                      :title="item.newestNumber"
                    >
                      {{ item.newestNumber }}
                    </p>

                    <!-- 标题下按钮 -->
                    <div class="title-btm-btn-group">
                      <p v-if="item.label">
                        {{ item.label | cardLabelFilter }}
                      </p>
                      <span
                        v-if="
                          item.label && item.sourceCount && item.sourceCount > 0
                        "
                        >|</span
                      >
                      <a
                        @click.stop.prevent="showSource(item)"
                        class="skill-source ib"
                        :class="{ 'skill-disabled': !item.used }"
                        v-if="item.sourceCount && item.sourceCount > 0"
                        >{{ item.sourceCount }}个信源</a
                      >
                    </div>
                  </div>
                </div>

                <!-- <i class="ic ic-r-tick-thin" v-if="item.used"></i> -->
                <div @click.stop class="switch-wrap">
                  <el-switch
                    size="small"
                    v-model="item.used"
                    @change="(val) => onStoreSkillSwitchChange(val, item)"
                  >
                  </el-switch>
                </div>

                <!-- 技能描述 -->
                <div class="label-wrap" v-if="item.briefIntroduction">
                  <span class="skill-desc" :title="item.briefIntroduction">
                    {{ item.briefIntroduction }}
                  </span>
                </div>
              </div>
            </div>
          </template>
        </div>
      </div>
    </div>
    <!-- 信源配置弹窗 -->
    <source-info
      :sourceVisible="sourceVisible"
      :sourceConfig="sourceConfig"
      :skillItem="skillItem"
      :currentScene="currentScene"
      :appId="appId"
      @sourceVisibleChange="toggleVisible"
      @change="$emit('change')"
    ></source-info>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import sourceInfo from './sourceInfo.vue'
import { cloneDeep } from 'lodash-es'

export default {
  components: { sourceInfo },
  name: 'storeSkill',
  props: {
    skillConfig: Object,
    currentScene: Object,
    appId: String,
    sourceConfig: Object,
    searchVal: String,
  },
  computed: {
    ...mapGetters({
      rightTestOpen: 'studioSkill/rightTestOpen',
      subAccount: 'user/subAccount',
      subAccountAppAuths: 'aiuiApp/subAccountAppAuths',
    }),
    subAccountEditable() {
      return this.subAccountAppAuths[this.$route.params.appId] == 2
        ? false
        : true
    },
    subAccountHasCreateAuth() {
      return this.$store.state.aiuiApp.subAccountHasCreateAuth
    },
  },
  data() {
    return {
      addSkillList: [], // 技能列表
      addSkillListCopy: [],
      skillTypeList: [],
      sourceVisible: false,
      skillItem: {},
      skillTypeKey: '',
    }
  },
  created() {
    // 商店技能
    this.getSkillType()
    // 获取技能商店中的技能
    // this.getList()
  },
  methods: {
    filterList() {
      // 根据searchVal 过滤item.name 以及 item.zhName 中包含searchVal的项目
      // 同时兼顾  过滤出相应的skillTypeKey
      let list = this.addSkillListCopy
      if (this.searchVal) {
        let val = this.searchVal.toLowerCase()
        list = list.filter(
          (item) =>
            item.name.toLowerCase().includes(val) ||
            item.zhName.toLowerCase().includes(val)
        )
      }
      this.addSkillList = list
    },
    filterSkillList(skillType) {
      let list = this.addSkillList
      if (skillType) {
        list = list.filter((item) => item.businessType === skillType)
      }
      return list
    },
    toggleVisible(val) {
      this.sourceVisible = val
    },
    showSource(item) {
      if (!item.used) return
      this.sourceVisible = true
      this.skillItem = item
    },
    onCickTab(key) {
      // 自定义技能
      if (!key) {
        this.$refs['skillListContainer'].scrollTo({
          top: 0,
          behavior: 'smooth',
        })
        return
      }

      const ContainerId = document.querySelector('#skill_type_' + key)
      if (ContainerId) {
        this.$refs['skillListContainer'].scrollTo({
          top: ContainerId.offsetTop - 140,
          behavior: 'smooth',
        })
      }
    },
    getSkillType(callback) {
      this.$utils.httpGet(
        this.$config.api.STUDIO_SKILL_SKILL_TYPES,
        {},
        {
          success: (res) => {
            this.skillTypeList = res.data
            if (callback) callback()
          },
          error: (err) => {},
        }
      )
    },
    getList(res) {
      this.addSkillList = []
      this.addSkillListCopy = []
      // sbusinessList 这个是其他账号的私有技能 storeSkillList是已下线的官方技能
      this.getOtherList()
      this.getAddSkillList(res)
    },
    getAddSkillList(res) {
      let self = this

      delete res.data.kzs // 清除开放技能，即不可添加开放技能
      // 重新组装storeSkill, 将item.used为true 放在前面
      let storeSkill
      let totalResult = res.data.storeSkill || []

      let totalResultUsed = totalResult.filter((item) => item.used)
      let totalResultUnsed = totalResult.filter((item) => !item.used)

      storeSkill = totalResult
      let storeSkillUsed = storeSkill.filter((item) => item.used)
      let storeSkillUnused = storeSkill.filter((item) => !item.used)
      let list = [...storeSkillUsed, ...storeSkillUnused]

      if (this.searchVal) {
        let val = this.searchVal.toLowerCase()
        list = list.filter(
          (item) =>
            item.name.toLowerCase().includes(val) ||
            (item.zhName && item.zhName.toLowerCase().includes(val))
        )
      }

      self.addSkillList = [...self.addSkillList, ...list]
      self.addSkillListCopy = cloneDeep([
        ...self.addSkillListCopy,
        ...totalResultUsed,
        ...totalResultUnsed,
      ])
    },
    getOtherList() {
      let self = this
      this.$utils.httpGet(
        this.$config.api.AIUI_APP_OTHER_CONFIG,
        {
          appid: this.appId,
          sceneId: this.currentScene.sceneBoxId,
        },
        {
          success: (res) => {
            let storeSkill
            let totalResult = (res.data.sbusinessList || [])
              .concat(res.data.storeSkillList || [])
              .map((item) => {
                return {
                  ...item,
                  used: true,
                  isOffShelf: true,
                  // businessType: '',
                }
              })

            storeSkill = totalResult

            let list = [...storeSkill]

            if (this.searchVal) {
              let val = this.searchVal.toLowerCase()
              list = list.filter(
                (item) =>
                  item.name.toLowerCase().includes(val) ||
                  (item.zhName && item.zhName.toLowerCase().includes(val))
              )
            }

            self.addSkillList = [...list, ...self.addSkillList]
            self.addSkillListCopy = cloneDeep([
              ...totalResult,
              ...self.addSkillListCopy,
            ])
          },
          error: (res) => {},
        }
      )
    },
    toStoreSkill(item, $event) {
      let self = this
      if (self.subAccount) return
      // 已下架技能
      if (item.isOffShelf) {
        this.$alert('该技能已经下架，无法查看详情。', '提示', {
          type: 'info',
          confirmButtonText: '知道了',
          callback: (action) => {},
        })
        return
      }
      window.open(`/store/skill/${item.id}?type=${item.type}`, '_blank')
    },
    onStoreSkillSwitchChange(val, item) {
      if (item.isOffShelf && !val) {
        this.$confirm(
          '已下架技能，关闭并保存修改后无法再次重新打开。',
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }
        )
          .then(() => {
            this.reallyDoStoreSkillSwitchChange(val, item)
          })
          .catch(() => {
            item.used = true
          })
      } else {
        //  AIUI.control 技能开启时单独处理
        if (val && item.name == 'AIUI.control') {
          this.$confirm(
            '此技能用于基本的播放控制与设备控制，命令控制意图将由此技能优先给出。',
            '提示',
            {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning',
            }
          )
            .then(() => {
              this.reallyDoStoreSkillSwitchChange(val, item)
            })
            .catch(() => {
              item.used = false
            })
        } else {
          this.reallyDoStoreSkillSwitchChange(val, item)
        }
      }
    },
    reallyDoStoreSkillSwitchChange(val, item) {
      this.$emit('change')
      let data = {
        id: item.id,
        name: item.name,
        operation: val ? 'open' : 'close',
      }
      this.skillConfig[item.id] = data
    },
    handleScroll() {
      for (let i = 0; i < Object.keys(this.skillTypeList).length; i++) {
        const key = Object.keys(this.skillTypeList)[
          Object.keys(this.skillTypeList).length - 1 - i
        ]
        const ContainerId = document.querySelector('#skill_type_' + key)
        if (!ContainerId) return
        if (
          this.$refs['skillListContainer'].scrollTop >
          ContainerId.offsetTop - 140 - 1
        ) {
          this.skillTypeKey = key
          return
        }
      }
      this.skillTypeKey = ''
    },
  },
  filters: {
    cardLabelFilter(val) {
      switch (val) {
        case 1:
          return '媒资类'
        case 2:
          return '播报类'
        default:
          return ''
      }
    },
  },
}
</script>

<style lang="scss" scoped>
@import './style.scss';
.empty-skill-tips-new {
  position: absolute;
  top: 49%;
  right: -140px;
}

.tab-container {
  display: flex;
  position: relative;
  &::before {
    position: absolute;
    content: ' ';
    width: 100%;
    height: 1px;
    background: #e7e9ed;
    bottom: 0;
  }
}

.label-wrap {
  // clear: both;

  p {
    padding: 0 20px;
    height: 36px;
    line-height: 36px;
    // text-align: center;
    // color: #1784e9;
    // background-color: rgba(227, 240, 252, 1);
    // margin: 0 auto;
    border-radius: 2px;
    margin-bottom: 0;
    // top: 50%;
  }
}
.flex-center {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  padding-bottom: 15px;
}

.new-skill-wrapper {
  width: 100%;
  display: grid;
  grid-template-columns: repeat(3, 1fr); /* 将容器分为3列，每列宽度平均 */
  gap: 18px; /* 设置格子之间的间距 */
}
.content-wrap {
  display: flex;
}
</style>
