<template>
  <div class="main-content">
    <MyHeader> </MyHeader>
    <section class="main-content-banner">
      <div class="banner-text">
        <h2>智慧地铁解决方案</h2>

        <p class="banner-text-content">
          针对地铁场景打造软硬件一体化解决方案。
          让地铁设备能听会说，提升乘客购票效率及咨询体验！
        </p>

        <div class="hor-btn">
          <el-button
            class="banner-text-button"
            @click="toConsole"
            type="primary"
            round
            >合作咨询</el-button
          >
        </div>
      </div>
    </section>

    <section class="section-nav">
      <ul class="nav-ul" id="nav-ul">
        <li
          v-for="item in nav_list"
          :key="item.id"
          @click="gotoSelection(item.id)"
        >
          {{ item.name }}
        </li>
      </ul>

      <i
        v-if="nav_list.length > 4"
        :class="[
          'nav-btn',
          nav_flag ? 'el-icon-caret-bottom' : 'el-icon-caret-top',
        ]"
        id="nav-btn"
        @click="handleNavExpand"
      ></i>
    </section>

    <section class="section section1">
      <h2>应用场景</h2>

      <div class="section-item">
        <ul>
          <li v-for="(item, index) in app_scenario" :key="index" class="app">
            <img :src="item.src" :alt="item.alt" />
            <p class="app-text">{{ item.alt }}</p>
          </li>
        </ul>
      </div>
    </section>

    <section class="section section2">
      <h2>方案介绍</h2>
      <div class="dialog">
        <div class="user-icon-div">
          <img
            class="dialog-icon"
            src="../../../../../assets/images/solution/subway/user-icon.png"
          />
        </div>
        <div class="message message-user">“ 我要去上海火车站”</div>

        <div class="user-icon-div user-icon-div-second">
          <img
            class="dialog-icon"
            src="../../../../../assets/images/solution/subway/user-icon.png"
          />
        </div>
        <div class="message message-user message-user-second">好的</div>

        <div class="robot-icon-div">
          <img
            class="dialog-icon dialog-icon-robot"
            src="../../../../../assets/images/solution/subway/robot-icon.png"
          />
        </div>
        <div class="message message-robot">“ 好的，请选择购票张数”</div>

        <div class="robot-icon-div robot-icon-div-second">
          <img
            class="dialog-icon dialog-icon-robot-second"
            src="../../../../../assets/images/solution/subway/robot-icon.png"
          />
        </div>
        <div class="message message-robot message-robot-second">
          “ 好的，请选择支付方式”
        </div>
      </div>

      <div class="dialog-info">
        <div class="title">语音购票</div>
        <div class="sub-title">
          多种语音购票方式，任意说：站点购票、票价购票
        </div>
      </div>

      <el-carousel
        indicator-position="outside"
        height="500px"
        class="my-carousel"
      >
        <el-carousel-item class="my-carousel-item">
          <img
            src="../../../../../assets/images/solution/subway/stations.png"
            alt=""
          />
          <p>“ 1号线最早的1班车是几点？”</p>
          <div class="dialog-info">
            <div class="title">语音查询</div>
            <div class="sub-title">站内、站外信息，轻松问</div>
          </div>
        </el-carousel-item>

        <el-carousel-item class="my-carousel-item">
          <img
            src="../../../../../assets/images/solution/subway/lines.png"
            alt=""
          />
          <p>“ 我要去动物园，从几号口出”</p>
          <div class="dialog-info">
            <div class="title">语音查询</div>
            <div class="sub-title">站内、站外信息，轻松问</div>
          </div>
        </el-carousel-item>
      </el-carousel>

      <!-- <div class="dialog-info">
        <div class="title">语音查询</div>
        <div class="sub-title">站内、站外信息，轻松问</div>
      </div> -->
    </section>

    <section class="section section3">
      <h2>方案优势</h2>

      <ul>
        <li v-for="item in pics" :key="item.index">
          <h3>{{ item.title }}</h3>
          <p>{{ item.sub_title }}</p>

          <img :src="item.src" alt="" v-if="item.visible" />

          <i
            class="el-icon-arrow-up my-up-btn"
            v-if="item.visible"
            @click="item.visible = !item.visible"
          ></i>
          <i
            class="el-icon-arrow-down my-down-btn"
            v-else
            @click="item.visible = !item.visible"
          ></i>
        </li>
      </ul>
    </section>

    <section class="section section4">
      <h2>交付流程</h2>

      <ul>
        <li>
          <p class="title">01 方案集成</p>
          <p class="text">专为地铁设计， 支持多种组合方案， 实现快速集成。</p>
        </li>

        <li>
          <p class="title">02 项目验证</p>
          <p class="text">
            项目验证 提供公有云服务快速验证， 语音交互设计指导， 全流程技术支持
          </p>
        </li>

        <li>
          <p class="title">03 项目上线</p>
          <p class="text">提供私有化部署， 满足地铁内网使用语音交互</p>
        </li>
      </ul>
    </section>

    <section class="section section-cooperation">
      <div class="text">
        为地铁行业打造最流畅的语音交互体验 提交信息， 我们会尽快联系你
      </div>
      <div class="cooperation-btn" @click="toConsole">合作咨询</div>
    </section>

    <section class="section section-footer">
      <!-- <aiuiMobileFooter> </aiuiMobileFooter> -->
      <div class="footer-title">
        联系我们
        <span class="arrow" @click="clickFooterArrow">▼</span>
      </div>
      <div class="content">
        <p>联系电话：19955108393</p>
        <p>开发者交流群：617903641</p>
        <p>商务技术支持：<EMAIL></p>
      </div>

      <div class="footer-title">
        友情链接
        <span class="arrow" @click="clickFooterArrow">▼</span>
      </div>
      <div class="content">
        <p>科大讯飞</p>
        <p>讯飞开放平台</p>
      </div>

      <div class="corporation-info">
        ©科大讯飞股份有限公司 皖ICP备05001217号
      </div>
    </section>
  </div>
</template>

<script>
import MyHeader from '@P/aiui/solution-aiui/mobile-header.vue'
export default {
  name: 'AiuiWebIndex',

  data() {
    return {
      nav_flag: true,

      expandFlag: true,
      nav_list: [
        { name: '应用场景', id: 1 },
        { name: '方案介绍', id: 2 },
        { name: '方案优势', id: 3 },
        { name: '交付流程', id: 4 },
      ],

      app_scenario: [
        {
          alt: '地铁购票机',
          src: require('../../../../../assets/images/solution/subway/img_subway_ticket_machine.png'),
        },
        {
          alt: '自助查询机',
          src: require('../../../../../assets/images/solution/subway/img_self_service_machine.png'),
        },
      ],

      pics: [
        {
          title: '智能感知- 主动提供服务',
          sub_title:
            '走到设备正前方，主动语音交互；<br>离开设备，自动终止服务。',
          src: require('../../../../../assets/images/solution/subway/ai-perception.png'),
          visible: true,
        },
        {
          title: '定向收音- 有效抑制周围噪声',
          sub_title:
            '基于业界先进的定向收音技术，实现屏幕正前方<br>定向收音，屏蔽两侧噪声。',
          src: require('../../../../../assets/images/solution/subway/ai-inhibit-noise.png'),
          visible: false,
        },
        {
          title: '精准录音- 准确判断录音时刻',
          sub_title:
            '乘客开口录音，闭口停止录音。<br>精准去除周边噪声干扰，提升语音识别准确率。 ',
          src: require('../../../../../assets/images/solution/subway/ai-accurate-record.png'),
          visible: false,
        },
        {
          title: '虚拟形象-提升服务体验',
          sub_title: '从人机对话转变为人“人”对话，让服务更有温度。',
          src: require('../../../../../assets/images/solution/subway/ai-virtual-image.png'),
          visible: false,
        },
      ],
    }
  },

  components: {
    MyHeader,
  },

  mounted() {},

  methods: {
    gotoSelection(id) {
      const section = document.getElementsByClassName(`section` + id)[0]
      if (section) {
        section.scrollIntoView({ behavior: 'smooth' })
      }
    },

    toDoc(link) {
      window.open(link)
    },

    handleNavExpand() {
      const nav_ul = document.getElementById('nav-ul')
      const navIcon = document.getElementById('nav-btn')

      navIcon.addEventListener('click', () => {
        if (this.nav_flag) {
          nav_ul.classList.add('expanded')
        } else {
          nav_ul.classList.remove('expanded')
        }
        this.nav_flag = !this.nav_flag
      })
    },
    clickFooterArrow() {
      const titles = document.querySelectorAll('.footer-title')
      titles.forEach((title) => {
        title.addEventListener('click', () => {
          const content = title.nextElementSibling
          const arrow = title.querySelector('.arrow')

          if (content.style.display === 'none' || !content.style.display) {
            content.style.display = 'block'
            arrow.classList.add('up')
          } else {
            content.style.display = 'none'
            arrow.classList.remove('up')
          }
        })
      })
    },

    toConsole() {
      const search = window.location.search
      if (search) {
        window.open(`/solution/apply/7${search}`)
      } else {
        window.open(`/solution/apply/7`)
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.main-content {
  background-color: #f6f7f9;
  width: 750px;
  overflow: hidden;
  &-banner {
    background: url(~@A/images/solution/smart-hardware/mobile/banner2.jpg)
      center no-repeat;
    background-size: cover;
    height: 375px;
    overflow: hidden;
    width: 100%;
  }
  .banner-text {
    margin-top: 64px;
    padding-left: 44px;
    h2 {
      font-size: 48px;
      font-weight: 600;
      color: #000000;
      line-height: 68px;
    }
    p {
      width: 648px;
      font-size: 24px;
      font-weight: 300;
      color: #000000;
      line-height: 40px;
      text-align: left;
    }
    .hor-btn {
      display: flex;
      flex-direction: row;
      justify-content: flex-start;
      margin-top: 40px;
      div:nth-child(2) {
        margin-left: 30px;
      }
    }
  }

  .section {
    h2 {
      font-size: 48px;
      font-weight: 600;
      color: #000000;
      line-height: 68px;
      text-align: center;
      margin: 0 auto;
      margin-top: 50px;
      margin-bottom: 30px;
    }
    p,
    ul {
      margin-bottom: 0;
    }
    max-width: 750px;
    overflow: hidden;
    margin: 0 auto;
    .section-title {
      text-align: center;
      font-size: 36px;
      font-weight: 500;
      color: #444444;
      line-height: 54px;
      .arrow {
        width: 26px;
        height: 24px;
        display: inline-block;
        background-repeat: no-repeat;
        background-size: contain;
      }
      .arrow-left {
        background-position: left;
        background-image: url(~@A/images/solution/smart-hardware/arrow-left.png);
      }
      .arrow-right {
        background-position: right;
        background-image: url(~@A/images/solution/smart-hardware/arrow-right.png);
      }
    }
    .section-tabs {
      margin-top: 60px;
    }
  }

  .section-nav {
    background-color: #ffffff;
    position: relative;
    width: 100%;
    border: 1px solid #ddd;
    padding: 0 30px;
    .nav-ul {
      display: flex;
      justify-content: space-evenly;
      flex-wrap: wrap;
      overflow: hidden; /* 默认隐藏超出部分 */
      max-height: 68px; /* 折叠状态时，只显示一行 */
      transition: max-height 0.3s ease; /* 动画过渡效果 */
    }
    .nav-ul.expanded {
      max-height: 200px; /* 展开状态：允许显示多行 */
    }
    .nav-ul li {
      flex: 0 0 auto;
      margin-right: 30px;
      white-space: nowrap;
      cursor: pointer;
      font-size: 24px;
      font-weight: 400;
      text-align: center;
      line-height: 68px;
      color: #7a7a7a;
    }
    .nav-btn {
      position: absolute;
      height: 68px;
      width: 60px;
      right: 0px;
      top: 60%;
      transform: translateY(-50%);
      font-size: 48px;
      // background-color: red;
    }
  }

  .section1 {
    padding: 0 40px;
    .section-item {
      margin-top: 50px !important;

      > ul {
        display: flex;
        justify-content: center;
        gap: 50px;

        li {
          width: 192px;
          height: 240px;
          position: relative;
          margin-bottom: 60px;

          img {
            width: 100%;
            height: 100%;
            position: absolute;
            right: 0;
            top: 0;
          }

          img:nth-child(3) {
            position: absolute;
            top: 20px;
            right: 30px;
          }

          p {
            height: 38px;
            width: 100%;
            text-align: center;
            margin: 0 auto;
            font-size: 20px;
            line-height: 38px;
            position: absolute;
            left: 50%;
            bottom: -70px;
            transform: translate(-50%, 0%);
            margin-bottom: 20px;
          }
        }
      }
    }
  }

  .section2 {
    padding: 0 29px;
    width: 100%;

    .dialog {
      padding-top: 20px;
      margin: 0 auto;
      height: 450px;
      width: 100% !important;
      background: #e7edf2;
      border-radius: 10px;
      position: relative;

      .dialog-icon {
        height: auto;
        width: 7.5% !important;

        &-robot {
          position: relative;
          right: 5%;
          float: right;

          &-second {
            position: absolute;
            float: right;
            right: 5%;
          }
        }
      }

      .user-icon-div {
        left: 20px;
        position: relative;
        &-second {
          top: 45%;
          position: absolute;
          width: 100% !important;
        }
      }
      .robot-icon-div {
        position: relative;
        top: 8%;

        &-second {
          position: relative;
          top: 52% !important;
        }
      }
    }

    .message,
    .message {
      float: left;
      /* margin: 10px; */
      left: 90px;
      background-color: #1784e9;
      border-bottom-color: #1784e9;
      color: #fff;
      font-size: 12px;
      line-height: 18px;
      padding: 5px 12px 5px 12px;
      -webkit-box-sizing: border-box;
      box-sizing: border-box;
      border-radius: 6px;
      position: relative;
      word-break: break-all;

      &-user {
        left: 18%;
        &-second {
          position: absolute;
          top: 54%;
          left: 18%;
        }
      }

      &-robot {
        background-color: white;
        color: #000000;
        float: right;
        position: absolute;
        right: 18% !important;
        left: revert;
        top: 32%;
        width: fit-content;
        &-second {
          top: 73%;
        }
      }
    }

    .message-robot::after {
      content: '';
      position: absolute;
      top: 0;
      right: -20px;
      width: 20px;
      height: 20px;
      border-width: 0 0 10px 20px;
      border-style: solid;
      border-bottom-color: rgb(255, 255, 255);
      /*自动继承父元素的border-bottom-color*/
      border-left-color: transparent;
      border-radius: 0 0 60px 0;
    }

    /** 通过对小正方形旋转45度解决 **/
    .message-user::before {
      content: '';
      position: absolute;
      top: 0;
      left: -20px;
      width: 20px;
      height: 20px;
      border-width: 0 0 10px 0;
      border-style: solid;
      border-bottom-color: inherit;
      border-left-color: transparent;
      border-radius: 0 0 0 60px;
    }

    .dialog-info {
      padding: 20px 30px;
      height: 180px;
      background: linear-gradient(180deg, #ffffff, #ffffff);
      box-shadow: 0px -8px 10px 0px rgba(173, 173, 173, 0.11);
      border-radius: 10px;
      .title {
        font-size: 32px;
        font-weight: 500;
        text-align: left;
        color: #000000;
        margin-bottom: 20px;
        line-height: 42px;
      }
      .sub-title {
        font-size: 24px;
        font-weight: 400;
        text-align: left;
        color: #666666;
        line-height: 38px;
      }
    }

    .my-carousel {
      margin-top: 30px;
      &-item {
        img {
          width: 100%;
          height: 450px;
          margin-bottom: 20px;
        }
        p {
          font-size: 20px;
          text-align: center;
          margin-bottom: 10px;
        }
      }
    }
  }

  .section3 {
    padding: 0 29px;
    ul {
      li {
        min-height: 133px;
        padding: 20px;
        padding-bottom: 30px;
        background: linear-gradient(180deg, #ffffff, #ffffff);
        border-radius: 30px;
        box-shadow: 0px -8px 10px 0px rgba(173, 173, 173, 0.11);
        margin-bottom: 20px;
        position: relative;
        h3 {
          font-size: 32px;
          height: 42px;
          color: #000000;
          line-height: 42px;
          font-weight: 500;
          text-align: left;
          margin-bottom: 15px;
        }
        p {
          font-size: 24px;
          font-weight: 400;
          text-align: left;
          color: #666666;
          line-height: 38px;
        }
        img {
          width: 650px;
          height: 470px;
        }
        video {
          margin-top: 20px;
          width: 100%;
        }

        .my-up-btn {
          font-size: 32px;
          position: absolute;
          bottom: 5px;
          left: 50%;
          transform: translateX(-50%);
        }
        .my-down-btn {
          font-size: 32px;
          position: absolute;
          right: 20px;
          top: 66px;
          transform: translateY(-50%);
        }
      }
    }
  }
  .section4 {
    padding: 0 29px;
    ul {
      width: 100%;
      li {
        width: 696px;
        height: 138px;
        background: linear-gradient(180deg, #ffffff, #ffffff);
        border-radius: 30px;
        box-shadow: 0px -8px 10px 0px rgba(173, 173, 173, 0.11);
        margin-bottom: 20px;
        padding: 20px 15px;
        .title {
          font-size: 32px;
          font-family: PingFang SC, PingFang SC-Medium;
          font-weight: 500;
          text-align: left;
          color: #000000;
          line-height: 42px;
        }
        .text {
          font-size: 26px;
          font-family: PingFang SC, PingFang SC-Regular;
          font-weight: 400;
          text-align: left;
          color: #999999;
          line-height: 38px;
        }
      }
    }
  }
  .section-cooperation {
    width: 100%;
    height: 243px;
    .text {
      font-size: 24px;
      text-align: center;
      color: #999999;
      margin-top: 20px;
      max-width: 70%;
      margin: 0 auto;
      margin-top: 20px;
    }
    .cooperation-btn {
      width: 222px;
      height: 81px;
      background: linear-gradient(90deg, #26bcf6, #1b7bf7 98%);
      border-radius: 52px;
      text-align: center;
      margin: 0 auto;
      font-weight: 600;
      line-height: 81px;
      font-size: 32px;
      color: #f0f0f0;
      margin-top: 30px;
    }
  }

  .section-footer {
    width: 750px;
    background-color: #061930;
    padding: 30px 20px;
    .footer-title {
      color: #c1c1c1;
      display: flex;
      justify-content: space-between;
      align-items: center;
      cursor: pointer;
      font-size: 28px;
      font-weight: bold;
      margin-bottom: 20px;
    }
    .content {
      margin-top: 10px;
      display: none; /* 默认隐藏内容 */
      font-size: 24px;
      color: #7b7e93;
    }

    .content p {
      margin: 5px 0;
      font-size: 24px;
      line-height: 49px;
    }

    .arrow {
      font-size: 26px;
      transition: transform 0.5s ease;
    }

    .arrow.up {
      transform: rotate(180deg);
    }

    .corporation-info {
      margin-top: 30px;
      color: #7b7e93;
      font-size: 24px;
      text-align: left;
      line-height: 33px;
    }
  }
}
</style>
