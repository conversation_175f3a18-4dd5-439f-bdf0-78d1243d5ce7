<template>
  <div class="point-card">
    <div class="card-index">{{ index + 1 }}</div>
    <div class="card-content" :title="card.content">{{ card.content }}</div>
    <template v-if="knowledgeType === 'rag_v2'"
      ><ul>
        <li>来源文档：{{ card.docName }}</li>
        <li class="card-content-paragraph" :title="card.detail">
          段落位置：{{ card.detail }}
        </li>
        <li>相关性得分：{{ card.score }}</li>
      </ul>
      <a
        v-if="item.docId"
        target="_blank"
        :href="`/studio/ragqa/pointDetail/${repoId}/${chunkId}?type=edit&source=chat`"
        >编辑知识点</a
      ></template
    >
    <template v-else>
      <ul>
        <li>
          来源文档：<a :href="card.docId" target="_blank">{{ card.docId }}</a>
        </li>
      </ul>
    </template>
  </div>
</template>
<script>
export default {
  props: {
    item: Object,
    show: false,
    index: Number,
  },
  created() {
    // docName detail从后端取
    this.card = { ...this.item, docName: '', detail: '' }
    if (
      this.item.repoId !== 'agg_knowledge' &&
      this.item.repoId !== 'open_knowledge'
    ) {
      this.getDocInfo()
    }
  },
  data() {
    return {
      card: {},
      docId: '',
      repoId: '',
      chunkId: '',
    }
  },
  watch: {
    show(val) {
      if (val) {
        if (
          this.item.repoId !== 'agg_knowledge' &&
          this.item.repoId !== 'open_knowledge'
        ) {
          this.getDocInfo()
        }
      }
    },
  },
  computed: {
    knowledgeType() {
      if (
        this.item.repoId === 'agg_knowledge' ||
        this.item.repoId === 'open_knowledge'
      ) {
        return this.item.repoId
      } else {
        return 'rag_v2'
      }
    },
  },
  methods: {
    getDocInfo() {
      let that = this
      if (!this.item.docId) {
        return
      }
      this.$utils.httpGet(
        this.$config.api.AIUI_KNOWLEDGE_REPO_DOC_INFO,
        {
          ragDocId: this.item.docId,
        },
        {
          success: (res) => {
            if (res.flag) {
              console.log('AIUI_KNOWLEDGE_REPO_DOC_INFO', res)
              that.repoId = res.data.repoId
              that.card.docName = res.data.docName
              // that.chunkId = this.item.chunkId
              // that.docId = res.data.id
              that.getChunkSearch(res.data.id, this.item.chunkId)
            }
          },
          error: (err) => {},
        }
      )
    },

    getChunkSearch(docId, chunkId) {
      let that = this
      this.$utils.httpGet(
        this.$config.api.AIUI_KNOWLEDGE_REPO_CHUNK_SEARCH,
        {
          docId,
          chunkId,
        },
        {
          success: (res) => {
            if (res.flag) {
              console.log('AIUI_KNOWLEDGE_REPO_CHUNK_SEARCH', res)
              if (res.data?.chunkList?.length > 0) {
                that.chunkId = res.data?.chunkList[0].id
                that.card.detail = res.data?.chunkList[0].title
              }
            }
          },
          error: (err) => {},
        }
      )
    },
  },
}
</script>
<style lang="scss" scoped>
.point-card {
  width: 100%;
  padding: 17px 18px 20px;
  background: #f7f8fa;
  border-radius: 4px;
  margin: 10px 0;
  .card-index {
    width: 45px;
    height: 24px;
    margin-bottom: 8px;
    text-align: center;
    line-height: 24px;
    background: #fff;
    border-radius: 2px;
    -webkit-box-shadow: 0px 2px 10px 0px rgba(102, 102, 102, 0.1);
    box-shadow: 0 2px 10px #6666661a;
  }
  .card-content {
    font-size: 14px;
    font-weight: 500;
    word-break: break-all;
    color: #262626;
    line-height: 22px;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 4;
    -webkit-box-orient: vertical;
    margin-bottom: 11px;
  }
  .card-content-paragraph {
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 4;
    -webkit-box-orient: vertical;
  }
  > ul {
    margin-bottom: 7px;
    > li {
      font-size: 14px;
      font-weight: 400;
      color: #878787;
      line-height: 20px;
      margin: 5px 0;
      word-break: break-all;
    }
  }
}
</style>
