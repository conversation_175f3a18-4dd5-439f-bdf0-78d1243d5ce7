<template>
  <el-form ref="form" :model="form" label-width="90px" :inline="true">
    <el-form-item label="检索方法:">
      <el-select
        :value="form.channel"
        placeholder="请选择检索方式"
        @change="onSelectChange"
        size="small"
      >
        <el-option label="单向量召回" :value="1"></el-option>
        <el-option label="多路召回" :value="2"></el-option>
      </el-select>
    </el-form-item>

    <el-form-item label="相关性阈值:">
      <el-input-number
        size="small"
        :value="form.threshold"
        :min="0"
        :max="1"
        :precision="2"
        :step="0.01"
        :step-strictly="true"
        @change="onInputChange"
      ></el-input-number>
    </el-form-item>
  </el-form>
</template>
<script>
export default {
  props: {
    form: {
      type: Object,
      default() {
        return {
          channel: 2,
          threshold: 0.1,
        }
      },
    },
  },
  data() {
    return {}
  },
  methods: {
    onInputChange(val) {
      console.log('onInputChange', val)
      this.$emit('change', 'threshold', val)
    },
    onSelectChange(val) {
      this.$emit('change', 'channel', val)
    },
  },
}
</script>
<style lang="scss" scoped>
.el-form {
  padding-top: 10px;
  margin-left: -15px;
  .el-form-item {
    margin-bottom: 0px;
  }
}
</style>
