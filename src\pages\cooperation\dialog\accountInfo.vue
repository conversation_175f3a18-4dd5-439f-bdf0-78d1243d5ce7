<template>
  <el-dialog
    class="account-dialog"
    title="设置"
    :visible.sync="dialog.show"
    width="720px"
  >
    <el-scrollbar wrapStyle="max-height:490px;" :noresize="false" tag="section">
      <os-collapse :default="true" title="基本信息" style="position: relative">
        <P class="account">
          <span class="title">账号</span
          ><span class="content ellipsis" :title="account.account || '-'">{{
            account.account || '-'
          }}</span>
          <i
            class="ic-r-copy"
            title="复制"
            @click="$utils.copyClipboard(account.account)"
          ></i
        ></P>
        <P class="account">
          <span class="title">初始密码</span
          ><span class="content ellipsis" :title="account || '-'">{{
            initialPwd || '-'
          }}</span></P
        >
        <label style="margin-right: 47px; color: #8c8c8c; font-weight: 600"
          >备注</label
        >
        <el-input
          placeholder="输入备注名，便于识别"
          size="medium"
          style="display: inline-block; width: 576px"
          v-model="remark"
          :disabled="saving"
        ></el-input>
      </os-collapse>
      <os-divider style="margin: 28px 0" />
      <p class="section-title">设置权限</p>
      <el-radio-group
        class="process-methods-wrap"
        size="small"
        v-model="tabelType"
        :disabled="saving"
      >
        <el-radio-button label="skill">技能</el-radio-button>
        <el-radio-button label="app">应用</el-radio-button>
      </el-radio-group>
      <p class="divide-dashed"></p>
      <template v-if="dialog.show">
        <skill-table
          v-show="tabelType === 'skill'"
          ref="skillTable"
          :account="account"
          :saving="saving"
        ></skill-table>
        <app-table
          v-show="tabelType === 'app'"
          ref="appTable"
          :account="account"
          :saving="saving"
        ></app-table>
      </template>
    </el-scrollbar>
    <span slot="footer" class="dialog-footer">
      <el-button
        class="dialog-btn"
        type="primary"
        style="min-width: 104px"
        :loading="saving"
        @click="save"
        >{{ saving ? '保存中...' : '保存' }}</el-button
      >
    </span>
  </el-dialog>
</template>

<script>
import SkillTable from './skillTable'
import AppTable from './appTable'
export default {
  props: {
    account: {
      type: Object,
      default: {},
    },
    dialog: {
      type: Object,
      default: {},
    },
  },
  data() {
    return {
      initialPwd: '123456',
      remark: '',
      saving: false,
      tabelType: 'skill',
    }
  },
  watch: {
    'dialog.show': function (val, oldVal) {
      let self = this
      if (val) {
        this.remark = this.account.remark
        this.tabelType = 'skill'
      }
    },
  },
  methods: {
    saveRemark() {
      let self = this
      if (self.account.remark == self.remark) return Promise.resolve('noChange')
      let data = {
        subUid: self.account.subUid,
        remark: self.remark,
      }
      return new Promise((resolve, reject) => {
        self.$utils.httpPost(this.$config.api.COOP_EDIT_ACCOUNTS, data, {
          success: (res) => {
            self.account.remark = self.remark
            resolve('saveRemark success')
          },
          error: (err) => {
            reject('saveRemark failed')
          },
        })
      })
    },
    save() {
      let self = this
      if (self.saving) return
      self.saving = true
      Promise.all([
        self.saveRemark(),
        self.$refs.skillTable.saveSKill(),
        self.$refs.appTable.saveApp(),
      ])
        .then((res) => {
          self.dialog.show = false
          self.saving = false
          if (res[0] == 'noChange' && res[1] == 'noChange') return
          self.$emit('getAccounts')
          self.$message.success('保存成功')
        })
        .catch((err) => {
          self.saving = false
          if (err == 'saveRemark failed') {
            self.$message.error('备注保存失败了，请重新保存')
          } else if (err == 'saveSKill') {
            self.$message.error('技能权限设置保存失败了，请重新保存')
          } else if (err == 'saveApp') {
            self.$message.error('应用权限设置保存失败了，请重新保存')
          } else {
            self.$message.error(err || '保存失败了')
          }
        })
    },
  },
  components: {
    SkillTable,
    AppTable,
  },
}
</script>

<style lang="scss" scoped>
.account {
  display: inline-block;
  margin: 24px 0;
  width: 304px;
  &:first-child {
    margin-right: 42px;
  }
  .title {
    display: inline-block;
    width: 80px;
    text-align: left;
    color: $grey5;
    font-weight: 600;
  }
  .content {
    display: inline-block;
    vertical-align: bottom;
    max-width: 200px;
    width: auto;
  }
}
.ic-r-copy {
  vertical-align: baseline;
  margin-left: 4px;
  color: $primary;
  cursor: pointer;
}
.section-title {
  margin: 28px 0 24px;
  font-size: 16px;
  font-weight: 600;
  color: $semi-black;
}

:deep(.el-radio-group) {
  width: 260px;
}
.divide-dashed {
  border-bottom: 1px dashed rgb(228, 231, 237);
  margin-bottom: 20px;
  padding-bottom: 20px;
}
</style>
<style lang="scss">
.account-dialog {
  .os-collapse-title {
    font-size: 16px;
  }
  .el-dialog__body {
    padding: 16px 0 0;
  }
  .el-scrollbar {
    padding: 0 32px;
  }
}
</style>
