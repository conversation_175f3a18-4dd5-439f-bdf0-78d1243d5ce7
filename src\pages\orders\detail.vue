<template>
  <div class="os-page os-scroll" v-if="details" v-loading="loading">
    <div class="os-content order-detail-page">
      <p class="mgt32 mgb16" style="color: #595959">
        <router-link :to="{ name: 'orders' }">我的订单</router-link>
        / <span>订单详情</span>
      </p>
      <div class="page-titl-wrap">
        <p class="fl" style="font-size: 30px">订单详情</p>
        <div class="fr">
          <template v-if="details.status == 1">
            <el-button size="small" @click="beforeCancelOrder"
              >取消订单</el-button
            >
            <el-button class="mgl16" type="primary" size="small" @click="toPay"
              >立即付款</el-button
            >
          </template>
          <template v-if="cancelledStatuss.includes(details.status)">
            <el-button class="mgl16" size="small" @click="beforeDelOrder"
              >删除订单</el-button
            >
          </template>
        </div>
      </div>
      <el-steps class="steps-wrap" :active="active">
        <el-step title="确认订单" icon="icon-steps"></el-step>
        <el-step
          :title="paidStatus.includes(details.status) ? '已付款' : '待付款'"
          icon="icon-steps"
        ></el-step>
        <template v-if="processStep3">
          <el-step :title="processStep3" icon="icon-steps"></el-step>
          <el-step :title="processStep4" icon="icon-steps"></el-step>
        </template>
        <template v-else>
          <el-step :title="processStep4" icon="icon-steps"></el-step>
        </template>
      </el-steps>
      <div class="order-item">
        <p class="order-item-title">订单号：{{ tradeNo }}</p>
        <div class="goods-info-wrap">
          <img class="goods-img" :src="details.goodsImage" />
          <p class="goods-name" :title="details.goodsName">
            {{ details.goodsName }}
          </p>
          <p class="goods-price">￥{{ details.price }} x {{ details.count }}</p>
        </div>
        <p>
          <span class="specific-item-type">商品名称</span
          ><span
            class="specific-item-info txt-ellipsis-nowrap"
            :title="details.goodsName"
            >{{ details.goodsName }}</span
          >
          <template v-if="details.orderFrom == 'XFYUN'">
            <span class="specific-item-type">应用名称</span
            ><span class="specific-item-info">{{ details.appName }}</span>
          </template>
        </p>
        <p>
          <span class="specific-item-type">单价</span
          ><span class="specific-item-info">{{ details.price + ' 元' }}</span>
          <span class="specific-item-type">下单时间</span
          ><span class="specific-item-info">{{
            details.orderTime | date
          }}</span>
        </p>
        <p>
          <span class="specific-item-type">数量</span
          ><span class="specific-item-info">{{ details.count }}</span>
          <span class="specific-item-type">付款时间</span
          ><span class="specific-item-info">{{ details.payTime | date }}</span>
        </p>
        <p>
          <span class="specific-item-type">实付金额</span
          ><span class="specific-item-info">{{
            details.totalFee + ' 元'
          }}</span>
          <span class="specific-item-type">订单状态</span
          ><span class="specific-item-info">{{
            details.status | orderStatusList
          }}</span>
        </p>
        <p>
          <span class="specific-item-type">备注</span
          ><span class="goods-remarks">{{ details.remarks || '-' }}</span>
        </p>
      </div>
      <template v-if="details.shippingAddress">
        <os-divider />
        <div class="order-item">
          <p class="order-item-title">收货信息</p>
          <p>
            <span class="specific-item-type">姓名</span
            >{{ details.shippingAddress.userName || '-' }}
          </p>
          <p>
            <span class="specific-item-type">联系电话</span
            >{{ details.shippingAddress.phone || '-' }}
          </p>
          <p>
            <span class="specific-item-type">收货地址</span
            ><span class="address">{{
              details.shippingAddress.address || '-'
            }}</span>
          </p>
        </div>
      </template>
      <os-divider />
      <div class="order-item" v-if="details.invoiceInfo">
        <p class="order-item-title">发票信息</p>
        <template
          v-if="details.invoiceInfo.type && details.invoiceInfo.type != '0'"
        >
          <p>
            <span class="specific-item-type">发票类型</span
            >{{ details.invoiceInfo.taxType | taxType }}
          </p>
          <p>
            <span class="specific-item-type">用户类型</span
            >{{ details.invoiceInfo.type == 1 ? '个人' : '企业' }}
          </p>
          <p v-if="details.invoiceInfo.company">
            <span class="specific-item-type">公司名称</span
            >{{ details.invoiceInfo.company }}
          </p>
          <p v-if="details.invoiceInfo.taxpayerId">
            <span class="specific-item-type">税号</span
            >{{ details.invoiceInfo.taxpayerId }}
          </p>
        </template>
        <span v-else>不需要发票</span>
      </div>
      <template
        v-if="
          details.logisticInfo &&
          !hideExpressInfo.includes(details.status) &&
          !isXfyunSevicePackage
        "
      >
        <os-divider />
        <div class="order-item">
          <p class="order-item-title">物流信息</p>
          <p v-if="delivered.includes(details.status)">
            <span class="specific-item-type">已发货</span
            ><span class="mgr24"
              >物流公司：{{ details.logisticInfo.expressCompany || '-' }}</span
            >
            <span>运单号：{{ details.logisticInfo.expressNo || '-' }}</span>
          </p>
          <p v-else>
            <span class="specific-item-type">待发货</span>暂无物流信息
          </p>
        </div>
      </template>
      <div class="total-price-wrap" v-if="details.totalFee">
        <span class="specific-item-type">应付金额：</span>
        <span class="total-price"
          >{{ $utils.moneyformat(details.totalFee) }} 元</span
        >
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'order-detail',
  data() {
    return {
      loading: false,
      details: {},
      paidStatus: [2, 5, 6, 7, 8, 9, 10, 11],
      cancelledStatuss: [0, 10],
      delivered: [5, 6, 7, 9, 10, 11], //已发货
      hideExpressInfo: [0, 1, 4],
    }
  },
  computed: {
    tradeNo() {
      return sessionStorage.getItem('tradeNo')
    },
    processStep3() {
      let str,
        step = this.details.status
      if (
        this.details.orderFrom == 'XFYUN' &&
        !sessionStorage.getItem('gooodsId')
      ) {
        //开放平台服务包类，付款后即表示交易已成功
        this.details.status =
          this.details.status === 2 ? 6 : this.details.status
        return (str = '')
      }
      if (step == 0) {
        str = ''
      }
      if ([1, 2, 3, 4, 8].includes(step)) {
        str = '待发货'
      }
      if ([5, 6, 9, 11].includes(step)) {
        str = '已发货'
      }
      if (step == 7) {
        str = '退款中'
      }
      if (step == 10) {
        str = '已退款'
      }
      return str
    },
    processStep4() {
      let tmp = '',
        step = this.details.status
      if ([7, 10].includes(step)) {
        tmp = '已关闭'
      } else if (step === 0) {
        tmp = '订单已取消'
      } else {
        tmp = '交易完成'
      }
      return tmp
    },
    active() {
      let tmp,
        step = this.details.status
      if ([1, 4].includes(step)) {
        tmp = 1
      }
      if ([2, 3, 7, 8, 9].includes(step)) {
        tmp = 2
      }
      if ([0, 5, 6, 10, 11].includes(step)) {
        tmp = this.processStep3 ? 4 : 3
      }
      return tmp
    },
    isXfyunSevicePackage() {
      if (
        this.details.orderFrom == 'XFYUN' &&
        !sessionStorage.getItem('gooodsId')
      )
        return true
      return false
    },
  },
  created() {
    this.getOrderDetail()
  },
  methods: {
    getOrderDetail() {
      this.loading = true
      let self = this
      if (!self.tradeNo) {
        self.$router.replace({ name: 'orders' })
        return
      }
      let data = {
        tradeNo: self.tradeNo,
      }
      this.$utils.httpGet(self.$config.api.ORDER_DETAIL, data, {
        success: (res) => {
          if (res.data) {
            self.loading = false
            self.details = res.data
            if (
              self.details.invoiceInfo &&
              self.details.invoiceInfo.type == '0' &&
              self.isXfyunSevicePackage
            ) {
              delete self.details.shippingAddress
            }
          }
        },
        error: () => {
          self.loading = false
        },
      })
    },
    toPay() {
      let url
      if (this.details.orderFrom == 'AIFUWUS') {
        url = `${this.$config.aifuwus}user/payway?orderNumber=${this.tradeNo}&goodsId=${this.details.goodsId}`
      } else {
        url = `${this.$config.xfyunConsole}sale/payway?tradeNo=${this.tradeNo}`
      }
      window.open(url, '_blank')
    },
    beforeDelOrder(tradeNo) {
      let api = this.$config.api.ORDER_DELETE,
        desc = '删除',
        subDesc = '订单删除后不可恢复，请谨慎操作。'
      this.cancelOrDeleteOrderPopover(api, tradeNo, desc, subDesc)
    },
    beforeCancelOrder(tradeNo) {
      let api = this.$config.api.ORDER_CANCEL,
        desc = '取消',
        subDesc = '取消后需重新下单'
      this.cancelOrDeleteOrderPopover(api, tradeNo, desc, subDesc)
    },
    cancelOrDeleteOrderPopover(api, tradeNo, desc, subDesc) {
      let self = this
      this.$confirm(subDesc, `确定${desc}订单吗？`, {
        confirmButtonText: `${desc == '删除' ? '删除' : '取消订单'}`,
        cancelButtonText: `${desc == '删除' ? '取消' : '关闭'}`,
        confirmButtonClass: 'el-button--danger',
        type: 'warning',
        showClose: false,
      })
        .then(() => {
          self.cancelOrDeleteHandle(api, tradeNo, desc)
        })
        .catch(() => {})
    },
    cancelOrDeleteHandle(api, tradeNo, desc) {
      let self = this
      this.$utils.httpGet(
        api,
        { tradeNo: self.tradeNo },
        {
          success: (res) => {
            this.$message({
              message: res.desc || `${desc}成功`,
              type: 'success',
            })
            if (desc == '删除') {
              self.$router.replace({ path: '/orders' })
            } else {
              self.getOrderDetail()
            }
          },
          error: (err) => {
            this.$message({
              message: (err && err.desc) || `${desc}失败`,
              type: 'error',
            })
          },
        }
      )
    },
  },
}
</script>
<style lang="scss" scoped>
.order-detail-page {
  margin: 0 auto 100px;
  width: 1000px;
}
.page-titl-wrap {
  overflow: hidden;
}
.order-item {
  padding: 45px 0 40px;
  &-title {
    margin-bottom: 21px;
    font-size: 20px;
    line-height: 26px;
  }
}
.steps-wrap {
  margin-top: 40px;
  padding: 48px 60px;
  border-radius: 12px;
  border: 1px solid rgba(226, 231, 235, 1);
}

.goods-info-wrap {
  display: flex;
  margin-bottom: 24px;
  height: 78px;
  line-height: 78px;
  border-top: 1px solid $grey2;
  border-bottom: 1px solid $grey2;
  font-size: 16px;
}
.goods-img {
  margin-top: 9px;
  max-width: 98px;
  max-height: 65px;
}
.goods-name {
  margin: 0 46px 0 64px;
  min-width: 200px;
  font-weight: 600;
}
.specific-item-type {
  display: inline-block;
  margin-bottom: 16px;
  width: 80px;
  text-align: left;
  color: $grey5;
}
.specific-item-info {
  display: inline-block;
  vertical-align: middle;
  margin-right: 80px;
  width: 180px;
}
.goods-remarks,
.address {
  display: inline-block;
  vertical-align: top;
  width: calc(100% - 80px);
  line-height: 1.5;
}
.total-price-wrap {
  padding-top: 32px;
  border-top: 1px solid $grey2;
  text-align: right;
}
.total-price {
  font-size: 24px;
  color: $primary;
}
:deep(.el-step__title.is-finish) {
  color: $semi-black;
}
:deep(.el-step__title.is-process) {
  color: $primary;
}
</style>
<style lang="scss">
.icon-steps {
  width: 8px;
  height: 8px;
  background: #0085f1;
  border-radius: 50%;
}
.is-finish {
  .el-step__line {
    background: #0085f1;
  }
}
</style>
