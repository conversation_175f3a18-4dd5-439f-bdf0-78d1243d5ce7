<template>
  <div style="margin-left: 10px">
    <div class="listen-opt" @click.stop="togglePlay">
      <i class="icon-listen icon-pause" v-if="isPlaying"></i>
      <i class="icon-listen icon-play" v-else></i>
    </div>
    <audio
      ref="audioPlayer"
      :src="currentSrc"
      style="visibility: hidden"
      @ended="onPlayEnded"
    ></audio>
  </div>
</template>
<script>
export default {
  data() {
    return {
      isPlaying: false,
      currentSrc: 'https://aiui-file.cn-bj.ufileos.com/audio/hts.wav',
    }
  },
  methods: {
    togglePlay() {
      if (this.isPlaying) {
        // 正在播放
        this.$refs.audioPlayer && this.$refs.audioPlayer.pause()
        this.isPlaying = false
      } else {
        // 关闭其他所有可能在播放的项目
        this.$refs.audioPlayer && this.$refs.audioPlayer.pause()

        this.isPlaying = true
        this.$nextTick(() => {
          this.$refs.audioPlayer && this.$refs.audioPlayer.play()
        })
      }
    },
    onPlayEnded() {
      this.isPlaying = false
    },
  },
}
</script>
<style scoped lang="scss">
.listen-opt {
  display: flex;
  align-items: center;
  i {
    margin-right: 6px;
    width: 30px;
    height: 30px;
    display: inline-block;
    cursor: pointer;
  }
  //   span {
  //     font-size: 12px;
  //     color: #8e90a5;
  //   }
  .icon-play {
    background: url(~@A/images/model-exeperience/<EMAIL>)
      center/contain no-repeat;
  }
  .icon-pause {
    background: url(~@A/images/model-exeperience/<EMAIL>)
      center/contain no-repeat;
  }
}
</style>
