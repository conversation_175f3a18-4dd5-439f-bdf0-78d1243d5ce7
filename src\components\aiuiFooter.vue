<template>
  <div class="pc-footer">
    <div class="pc-footer-auth">
      <div v-for="(item, index) in list" :key="index" class="auth-box">
        <img
          v-lazy="require(`@A/images/home/<USER>/${item.icon}.png`)"
          alt=""
        />
        <span class="title">{{ item.title }}</span>
      </div>
    </div>
    <div class="pc-footer-container">
      <div class="pc-footer-container-logo">
        <img alt="AIUI" v-lazy="require('@A/images/home/<USER>/logo.png')" />
        <div class="desc">大模型语音交互开箱即用</div>
      </div>
      <div class="pc-footer-container-row">
        <div class="pc-footer-container-col">
          <div class="title">友情链接</div>
          <ul class="pc-footer-container-text">
            <li class="pc-footer-container-btn">
              <a href="http://www.iflytek.com" target="_blank">科大讯飞</a>
            </li>
            <li class="pc-footer-container-btn">
              <a href="https://www.xfyun.cn/" target="_blank">讯飞开放平台</a>
            </li>
            <!-- <li class="pc-footer-container-btn">
              <a href="http://www.voiceads.cn/" target="_blank">广告平台</a>
            </li>
            <li class="pc-footer-container-btn">
              <a href="http://www.toycloud.com/" target="_blank">讯飞淘云</a>
            </li> -->
          </ul>
        </div>
        <div class="pc-footer-container-col">
          <div class="title">帮助</div>
          <ul class="pc-footer-container-text">
            <li class="pc-footer-container-btn">
              <a :href="NewGuideUrl" target="_blank">新手指南</a>
            </li>
            <li class="pc-footer-container-btn">
              <a :href="WhitePaperUrl" target="_blank">产品白皮书</a>
            </li>
            <!-- <li class="pc-footer-container-btn">
              <a href="http://bbs.xfyun.cn/forum.php?mod=forumdisplay&fid=80" target="_blank">论坛</a>
            </li> -->
          </ul>
        </div>
        <div class="pc-footer-container-col">
          <div class="title">用户中心</div>
          <ul class="pc-footer-container-text">
            <!-- <li class="pc-footer-container-btn">
              <a href="/app/add" target="_blank">添加新应用</a>
            </li> -->
            <li class="pc-footer-container-btn">
              <a href="/app" target="_blank">应用管理</a>
            </li>
            <li class="pc-footer-container-btn">
              <a @click="toUserInfo">个人资料</a>
            </li>
          </ul>
        </div>
        <div class="pc-footer-container-col" style="width: 262px">
          <div class="title">联系我们</div>
          <ul class="pc-footer-container-text">
            <li>开发者交流群：<br />575396706、617903641</li>
            <!-- <li>
              工单系统:
              <a style="margin-left: 8px" href="/ask/submit" target="_blank">点击提交工单</a>
            </li>
            <li>
              <a href="/ask" target="_blank">查看我的提问</a>
            </li> -->
            <li>商务技术支持：<EMAIL></li>
          </ul>
        </div>
      </div>
    </div>
    <div class="pc-footer-bottom">
      <div class="pc-footer-bottom-copyright">
        © 科大讯飞股份有限公司 皖ICP备05001217号
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      NewGuideUrl: `${this.$config.docs}doc-2/`,
      WhitePaperUrl: `${this.$config.docs}doc-1/`,
      list: [
        {
          title: '大客户1V1专享服务',
          icon: 'icon1',
        },
        {
          title: '国家信息安全等级保护三级',
          icon: 'icon2',
        },
        {
          title: '可靠的SLA保障承诺',
          icon: 'icon3',
        },
        {
          title: '资深工程师专业支持',
          icon: 'icon4',
        },
      ],
    }
  },
  methods: {
    toUserInfo() {
      window.open(`/user/info`)
    },
  },
}
</script>

<style lang="scss" scoped>
.pc-footer {
  width: 100%;
  height: 440px;
  background: #f4f7ff;

  &-auth {
    width: 1200px;
    height: 145px;
    margin: 0 auto;
    border-bottom: 1px solid #bfc7d6;
    display: flex;
    align-items: center;
    justify-content: space-around;
    background-color: #f4f7ff;

    .auth-box {
      flex: 1;
      display: flex;
      align-items: center;

      img {
        width: 36px;
      }

      span {
        margin-left: 16px;
        font-size: 16px;
        font-family: PingFangSC, PingFangSC-Regular;
        font-weight: 400;
        text-align: justify;
        color: #000000;

        &:hover {
          color: $primary;
        }
      }
    }
  }

  &-container {
    width: 100%;
    max-width: 1200px;
    height: 184px;
    margin: auto;
    margin-top: 54px;
    display: flex;

    &-logo {
      display: block;

      img {
        width: 170px;
        height: 31px;
        vertical-align: middle;
      }

      .desc {
        margin-top: 16px;
        font-size: 14px;
        font-family: PingFangSC, PingFangSC-Regular;
        color: #8e90a5;
        line-height: 20px;
        text-align: center;
      }
    }

    &-row {
      overflow: hidden;
      position: relative;
      box-sizing: border-box;
      margin-left: 163px;
    }

    &-col {
      width: 200px;
      height: 240px;
      float: left;
      box-sizing: border-box;

      .title {
        font-size: 18px;
        font-family: PingFangSC, PingFangSC-Medium;
        font-weight: 500;
        text-align: justify;
        color: #000000;
        margin-bottom: 24px;
      }
    }

    &-text {
      & li {
        font-size: 14px;
        font-family: PingFangSC, PingFangSC-Regular;
        color: #8e90a5;
        margin-bottom: 12px;

        a {
          color: #8e90a5;
        }
      }
    }

    &-btn {
      cursor: pointer;

      a {
        color: #999;
      }
    }
  }

  &-bottom {
    display: flex;
    justify-content: space-between;
    width: 100%;
    height: 57px;
    line-height: 57px;
    background: #031a37;

    &-copyright {
      width: 1200px;
      font-size: 14px;
      font-family: PingFangSC, PingFangSC-Regular;
      font-weight: 400;
      text-align: justify;
      color: #8e90a5;
      margin: 0 auto;
    }
  }

  .icon-image {
    display: inline-block;
    background: url(~@A/images/aiui/main-page/icon-image.png) no-repeat;
  }

  .icon-order {
    height: 14px;
    width: 12px;
    background-position: 0 0;
  }

  .icon-qq {
    height: 15px;
    width: 14px;
    background-position: -12px 0;
  }

  .icon-ask {
    height: 15px;
    width: 15px;
    background-position: -26px 0;
  }
}

@media screen and (max-width: 719px) {
  .pc-footer {
    display: none;
  }
}
</style>
