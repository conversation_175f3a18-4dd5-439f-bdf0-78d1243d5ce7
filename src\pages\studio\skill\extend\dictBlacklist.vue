<template>
  <div class="extend-entity-delete-dict">
    <div class="mgb24" style="font-size: 0">
      <el-button
        class="mgr16"
        icon="ic-r-plus"
        type="primary"
        size="small"
        :disabled="total >= entityLimitCount || !subAccountEditable"
        @click="addRow"
        >黑名单词条</el-button
      >
      <el-dropdown
        trigger="click"
        @command="handleCommand"
        placement="bottom-start"
      >
        <el-button size="small" :disabled="!subAccountEditable">
          批量操作
          <i class="ic-r-triangle-down el-icon--right" />
        </el-button>
        <el-dropdown-menu style="width: 120px" slot="dropdown">
          <el-dropdown-item>
            <div @click="dialog.show = true">批量覆盖</div>
          </el-dropdown-item>
          <el-dropdown-item v-if="total >= entityLimitCount" style="padding: 0">
            <div class="import-disabled">批量追加</div>
          </el-dropdown-item>
          <el-dropdown-item v-else>
            <upload
              :dictId="dictId"
              :options="addOnly"
              :limitCount="limitCount"
              :subAccount="subAccount"
              @setLoad="setLoad"
              @setErrInfo="setErrInfo"
              @getEntryList="getDictList(1)"
            ></upload>
          </el-dropdown-item>
          <el-dropdown-item command="export">
            导出实体
            <!-- <i class="ic-r-angle-r el-dropdown-sub-icon"></i>
            <ul class="el-dropdown-sub">
              <li @click="exportExcel">EXCEL 文件</li>
              <li @click="exportTxt">TXT 文件</li>
            </ul> -->
          </el-dropdown-item>
          <el-dropdown-item command="download"
            >下载模版
            <!-- <i class="ic-r-angle-r el-dropdown-sub-icon"></i>
            <ul class="el-dropdown-sub">
              <li @click="downloadExcel">EXCEL 文件</li>
              <li @click="downloadTxt">TXT 文件</li>
            </ul> -->
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <div class="fr" @keyup.enter="getDictList(1)">
        <el-input
          class="search-area"
          placeholder="搜索词条"
          size="medium"
          v-model="searchVal"
        >
          <i
            slot="suffix"
            class="el-input__icon el-icon-search search-area-btn"
            @click="getDictList(1)"
          />
        </el-input>
      </div>
    </div>
    <div v-loading="loading" style="min-height: 200px">
      <template v-if="list && list.length">
        <div class="dict-list-wrap">
          <el-input
            :ref="'entityValueInput'"
            class="dict-input"
            placeholder="输入词条，回车添加"
            v-for="(dict, index) in list"
            :key="index"
            v-model="dict.value"
            :title="dict.value"
            :disabled="!subAccountEditable"
            @keyup.enter.native="editEntryBlur"
            @blur="addOrEditEntry(dict, index)"
          >
            <i
              slot="suffix"
              v-if="subAccountEditable"
              class="ic-r-delete"
              @click="delEntry(dict, index)"
            />
          </el-input>
        </div>
        <el-pagination
          v-if="total > size"
          class="pagination-wrap"
          layout="prev, pager, next"
          :page-size="size"
          :total="total"
          @current-change="getDictList"
        >
        </el-pagination>
      </template>
      <p class="no-data-tip" v-else>暂无数据</p>
    </div>
    <cover
      :dialog="dialog"
      :dictId="dictId"
      :limitCount="limitCount"
      :subAccount="subAccount"
      @setLoad="setLoad"
      @setErrInfo="setErrInfo"
      @getDictList="getDictList(1)"
    >
    </cover>
    <!-- 批量操作错误提示 -->
    <el-dialog title="错误提示" :visible.sync="showErrDialog" width="50%">
      <div style="margin-bottom: 20px">
        <p
          style="line-height: 22px"
          v-for="(text, index) in errList"
          :key="index"
        >
          {{ text }}
        </p>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="showErrDialog = false"
          >确 定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { mapGetters } from 'vuex'
import Upload from './uploadExtendEntity'
import Cover from './dialog/uploadCoverDialog'

export default {
  name: 'extend-entity-dict-blacklist',
  data() {
    return {
      searchVal: '',
      loading: false,
      page: 1,
      size: 40,
      list: [],
      total: 0,
      dictName: '',
      editEntryEnterBlur: false,
      addOnly: {
        type: 2,
        text: '批量追加',
      },
      dialog: {
        show: false,
      },
      showErrDialog: false,
      errList: [],
    }
  },
  computed: {
    skillId() {
      return this.$route.params.skillId
    },
    dictId() {
      return this.$route.params.delDictId || ''
    },
    ...mapGetters({
      limitCount: 'aiuiApp/limitCount',
      subAccountSkillAuths: 'studioSkill/subAccountSkillAuths',
      subAccount: 'user/subAccount',
    }),
    isCharNoLimit() {
      return this.limitCount['char_no_limit_language'] > 0 // 0：限制；>0不限制
    },
    entityLimitCount() {
      return this.limitCount['entity_entry_count'] || '20000'
    },
    subAccountEditable() {
      return this.subAccountSkillAuths[this.skillId] == 2 ? false : true
    },
  },
  created() {
    if (this.dictId) {
      this.getDictList()
    }
  },
  methods: {
    setLoad(val) {
      this.loading = val
    },
    setErrInfo(data, type) {
      this.errList = JSON.parse(data)
      this.showErrDialog = type
    },
    getDictList(page) {
      let self = this
      this.loading = true
      this.$utils.httpGet(
        this.$config.api.STUDIO_EXTEND_ENTITY_SEARCH,
        {
          dictId: self.dictId,
          pageIndex: page || self.page,
          pageSize: self.size,
          search: self.searchVal,
        },
        {
          success: (res) => {
            self.loading = false
            self.dictName = res.data.dict.name
            self.list = res.data.dictDataList
            self.total = res.data.size
          },
          error: (err) => {
            self.loading = false
          },
        }
      )
    },
    handleCommand(command) {
      let self = this
      switch (command) {
        case 'export':
          self.exportExcel()
          break
        case 'download':
          self.downloadExcel()
          break
        default:
          break
      }
    },
    exportExcel() {
      this.$utils.postopen(this.$config.api.STUDIO_EXTEND_ENTITY_EXPORT_EXCEL, {
        name: this.dictName,
        dictId: this.dictId,
      })
    },
    downloadExcel() {
      window.open(
        'https://aiui-file.cn-bj.ufileos.com/DemoEntity.xlsx',
        '_self'
      )
    },
    addRow() {
      let self = this
      let entry = this.list[0] || {}
      if (entry.id || this.list.length <= 0) {
        this.list.unshift({})
        if (this.total % this.size === 0) {
          this.size += 1
        } else {
          this.size = 40
        }
        this.total += 1
      }
      this.editEntryEnterBlur = false
      this.$nextTick(function () {
        self.$refs['entityValueInput'] &&
          self.$refs['entityValueInput'][0].focus()
      })
    },
    addOrEditEntry(data, index) {
      let self = this
      let reg =
        /^[\u4e00-\u9faf\uff00-\uffef\u30a0-\u30ff\u3000-\u303f\u3040-\u309F\u0400-\u052f\u00a0-\u00ff\u0100-\u017f\u0180-\u024f\u4e00-\u9fffa-zA-Z0-9\(\)\+\.\*`%'_ -]+$/
      let tmp = {
        skillId: self.skillId,
        extendDictId: self.dictId,
        value: data.value,
      }
      if (data.value) {
        if (data.value.length > 128) {
          return self.$message.warning('词条名不能超过128个字符')
        }
        if (!this.isCharNoLimit) {
          if (!reg.test(data.value)) {
            return self.$message.warning(
              "词条名仅支持中英文/数字/空格和._-'%`()*+"
            )
          }
        }
      } else {
        return
      }
      if (data.id) {
        if (!data.value) {
          return this.$message.warning('词条不能为空')
        }
        if (!self.list[0] && self.list[0].id) {
          index -= 1
        }
        tmp.id = data.id
      }
      self.$utils.httpPost(this.$config.api.STUDIO_EXTEND_ENTITY_ADDDICT, tmp, {
        success: (res) => {
          if (!data.id) {
            let entry = self.list[0]
            entry.id = res.data.id
            if (self.editEntryEnterBlur) {
              self.addRow()
            }
          }
        },
        error: (err) => {},
      })
    },
    editEntryBlur(event) {
      event.target.blur()
      this.editEntryEnterBlur = true
    },
    delEntry(data, index) {
      let self = this
      if (!data.id) {
        self.list.splice(index, 1)
        self.total -= 1
        return
      }
      self.loading = true
      self.$utils.httpPost(
        self.$config.api.STUDIO_EXTEND_ENTITY_DEL,
        {
          dictId: self.dictId,
          id: data.id,
          skillId: self.skillId,
        },
        {
          success: (res) => {
            self.loading = false
            self.$message.success('删除成功')
            if (self.list.length === 1 && self.page > 1) {
              self.page -= 1
            }
            self.getDictList()
          },
          error: (err) => {
            this.loading = false
          },
        }
      )
    },
  },
  components: {
    Upload,
    Cover,
  },
}
</script>
<style lang="scss" scoped>
.dict-list-wrap {
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;
}
.dict-input {
  width: 25%;
  height: 40px;
}
.ic-r-delete {
  font-size: 20px;
  cursor: pointer;
}
.pagination-wrap {
  margin: 24px auto 50px;
  text-align: center;
}

.no-data-tip {
  margin: 50px auto;
  text-align: center;
}
</style>
<style lang="scss">
.extend-entity-delete-dict {
  .dict-input {
    .el-input__suffix {
      top: 10px;
      visibility: hidden;
    }
    &:hover {
      .el-input__suffix {
        visibility: visible;
      }
    }
    .el-input__inner {
      height: 40px;
      line-height: 40px;
      text-overflow: ellipsis;
      border-radius: 0;
      border-right: none;
      border-bottom: none;
      border-color: $grey3;
    }
    &:nth-child(4n),
    &:last-child {
      .el-input__inner {
        border-right: 1px solid $grey3;
      }
    }
    &:nth-last-child(4),
    &:nth-last-child(3),
    &:nth-last-child(2),
    &:last-child {
      .el-input__inner {
        border-bottom: 1px solid $grey3;
      }
    }
    &:last-child {
      width: calc(25% + 1px);
    }
    &:nth-child(4n) {
      width: calc(25%);
    }
  }
}
</style>
