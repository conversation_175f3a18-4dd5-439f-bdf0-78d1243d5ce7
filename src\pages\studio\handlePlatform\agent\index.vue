<template>
  <div class="os-scroll">
    <div class="content">
      <!-- <div class="agent_banner"></div> -->
      <div class="title">智能体</div>

      <div class="create_search_wrap">
        <div
          class="role-header-item"
          :class="category === 1 ? 'role-header-item-active' : ''"
          @click="changeCategory(1)"
        >
          新智能体
        </div>
        <div
          class="role-header-item"
          :class="category === 2 ? 'role-header-item-active' : ''"
          @click="changeCategory(2)"
        >
          旧智能体
        </div>
        <div class="line"></div>

        <el-input
          class="search-area"
          placeholder="搜索智能体"
          size="medium"
          v-model="searchVal"
          clearable
          @clear="handleSearch"
          @keyup.enter.native="handleSearch"
          :maxlength="250"
        >
          <i
            slot="suffix"
            class="el-input__icon el-icon-search search-area-btn"
            @click="handleSearch"
          />
        </el-input>

        <el-button
          icon="ic-r-plus"
          type="primary"
          size="medium"
          @click="openAgent('create')"
          style="margin-left: auto"
          >&nbsp;创建智能体</el-button
        >
      </div>
      <el-scrollbar v-loading="tableData.loading" v-if="category === 1">
        <div class="card_list" v-if="tableData.list.length > 0">
          <AgentCard
            v-for="item in tableData.list"
            :key="item.pluginId"
            :item="item"
            @click="handleAgentItem"
            @dropdown="handleDropdownItem"
          />
        </div>
        <EmptyTip v-else />
      </el-scrollbar>

      <div class="pagination_wrap" v-if="category === 1">
        <el-pagination
          v-if="tableData.list.length > 0"
          ref="pagination"
          :current-page="tableData.page"
          :page-size="tableData.size"
          :total="tableData.total"
          :layout="pageLayout"
          @current-change="pageChange"
          class="txt-al-c"
        ></el-pagination>
      </div>

      <AgentLegacy ref="AgentLegacy" v-if="category === 2"></AgentLegacy>
    </div>
    <CreateAgent
      ref="CreateAgent"
      @refresh="refresh"
      @handleAgentItem="handleAgentItem"
    >
    </CreateAgent>

    <CreateAgentLegacy ref="CreateAgentLegacy" @refresh="refreshLegacy">
    </CreateAgentLegacy>

    <CopyAgentDialog ref="CopyAgentDialog" @confirm="doCopy" />
  </div>
</template>

<script>
import HandlePlatformTop from '../top.vue'
import CreateAgent from './createAgent.vue'
import CreateAgentLegacy from '../agent_legacy/createAgent.vue' //  这个是适配旧的智能体 创建的弹窗
import AgentCard from '@/components/agentCard.vue'
import CopyAgentDialog from '@/components/copyAgentDialog.vue'
import EmptyTip from '@/pages/studio/role/empty.vue'
import AgentLegacy from '../agent_legacy/index.vue'

export default {
  name: 'AiuiWebIndex',

  components: {
    HandlePlatformTop,
    CreateAgent,
    AgentCard,
    CopyAgentDialog,
    EmptyTip,
    AgentLegacy,
    CreateAgentLegacy,
  },
  data() {
    return {
      category: 1, //   1 新智能体   2  旧智能体
      searchVal: '',
      tableData: {
        loading: false,
        total: 0,
        page: 1,
        size: 12,
        handles: ['edit', 'del'],
        handleColumnText: '操作',
        list: [],
      },
    }
  },

  created() {
    this.getAgent(1)
  },

  mounted() {},
  computed: {
    pageLayout() {
      return this.tableData.total > 10
        ? 'prev, pager, next, jumper, total'
        : 'prev, pager, next'
    },
    // 根据templateTag内容返回对应的样式
    getTagStyle() {
      return (tag) => {
        if (tag?.includes('查询')) {
          return {
            backgroundColor: '#e5e9ff',
            color: '#544aff',
          }
        } else if (tag?.includes('预定')) {
          return {
            backgroundColor: '#fcf7e4',
            color: '#D1931A',
          }
        }
        return {}
      }
    },
  },
  methods: {
    changeCategory(type) {
      if (this.category === type) return
      this.category = type
      if (type === 1) {
        this.getAgent(1)
      } else {
      }
    },
    pageChange(e) {
      this.tableData.page = e
      this.getAgent()
    },
    handleSearch() {
      if (this.category === 1) {
        this.getAgent(1)
      } else {
        this.$refs.AgentLegacy.getAgent(1, this.searchVal)
      }
    },

    getAgent(page, callback) {
      this.tableData.loading = true
      const data = {
        pageIndex: page || this.tableData.page,
        pageSize: this.tableData.size,
        searchKey: this.searchVal,
      }
      this.$utils.httpPost(
        this.$config.api.AGENT_TABLE_LIST,
        JSON.stringify(data),
        {
          config: {
            headers: {
              'Content-Type': 'application/json;charset=UTF-8',
            },
          },
          success: (res) => {
            if (res.code == 0) {
              this.tableData.list = res.data.data
              this.tableData.total = res.data.totalSize
              this.tableData.page = res.data.pageIndex
              this.tableData.size = res.data.pageSize
              this.tableData.loading = false
              if (typeof callback === 'function') {
                callback()
              }
            }
          },
          error: (err) => {
            this.tableData.loading = false
            this.$message.error(err?.desc)
          },
        }
      )
    },

    refresh(callback) {
      this.getAgent(1, () => {
        // 如果有回调函数，则在获取数据后调用回调函数并传递列表数据
        if (typeof callback === 'function') {
          callback(this.tableData.list)
        }
      })
    },
    refreshLegacy() {
      this.$refs.AgentLegacy.getAgent()
      this.category = 2
    },

    openAgent() {
      if (this.category === 1) {
        this.$refs.CreateAgent.show()
      } else {
        this.$refs.CreateAgentLegacy.show()
      }
    },

    handleDropdownItem(data) {
      switch (data.command) {
        case 'edit':
          this.toEdit(data.item)
          break
        case 'copy':
          this.copy(data.item)
          break
        case 'del':
          this.toDel(data.item)
          break
      }
    },
    toEdit(item) {
      this.$refs.CreateAgent.show(item)
    },
    handleAgentItem(item) {
      console.log(item)
      localStorage.setItem('agentItemData', JSON.stringify(item))
      this.$router.push({
        name: 'studio-handle-platform-agent-detail',
        params: {
          agentId: item.pluginId,
          agentType: item.pluginType,
          boardId: item.boardId,
        },
      })
    },

    copy(item) {
      if (item.pluginName.length < 29) {
        // 名称长度小于29，直接复制
        this.doCopy({
          pluginId: item.pluginId,
        })
      } else {
        // 名称长度大于等于29，打开弹窗
        this.$refs.CopyAgentDialog.show(item)
      }
    },

    doCopy(params) {
      this.tableData.loading = true
      this.$utils.httpPost(
        this.$config.api.AGENT_COPY,
        JSON.stringify(params),
        {
          config: {
            headers: {
              'Content-Type': 'application/json;charset=UTF-8',
            },
          },
          success: (res) => {
            this.$message.success('复制成功')
            this.getAgent()
          },
          error: (err) => {
            this.tableData.loading = false
            this.$message.error(err.desc)
          },
        }
      )
    },

    toDel(item) {
      let self = this
      this.$confirm(
        '智能体删除后不可恢复，请谨慎操作。',
        `确定删除该智能体吗?`,
        {
          confirmButtonText: '确定删除',
          cancelButtonText: '取消',
          confirmButtonClass: 'el-button--danger',
          type: 'warning',
          showClose: false,
        }
      )
        .then(() => {
          self.delAgent(item)
        })
        .catch(() => {})
    },
    delAgent(item) {
      const params = {
        pluginId: item.pluginId,
      }
      this.tableData.loading = true
      this.$utils.httpPost(
        this.$config.api.AGENT_DELETE,
        JSON.stringify(params),
        {
          config: {
            headers: {
              'Content-Type': 'application/json;charset=UTF-8',
            },
          },
          success: (res) => {
            this.$message.success('删除成功')
            if (this.tableData.list.length === 1 && this.tableData.page > 1) {
              this.tableData.page -= 1
            }
            this.getAgent()
          },
          error: (err) => {
            this.tableData.loading = false
            this.$message.error(err.desc)
          },
        }
      )
    },
  },
}
</script>

<style lang="scss">
.card_tooltip {
  width: 300px;
  line-height: 1.5;
}
</style>
<style lang="scss" scoped>
.search-area {
  width: 234px;
}
.content {
  width: 100%;
  height: 100%;
  padding: 24px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  .agent_banner {
    margin: 0 auto;
    aspect-ratio: 4.84;
    width: 100%;
    border-radius: 7px;
    background: url('@/assets/images/AgentBanner.png') center no-repeat;
    background-size: cover;
  }
  .title {
    margin-right: auto;
    color: #000000;
    font-size: 20px;
    font-family: PingFang SC, PingFang SC-500;
    font-weight: 600;
  }
  .create_search_wrap {
    display: flex;
    align-items: center;
    // justify-content: space-between;
    // flex-direction: row-reverse;
    margin: 20px 0;
    .role-header-item {
      background: #ececf1;
      border-radius: 10px;
      padding: 7px 14px;
      color: #3f3f44;
      margin-right: 18px;
      cursor: pointer;
      font-weight: 400;
      font-family: PingFang SC;
      &-active {
        background: #e5f5ff;
        color: #009bff;
        font-weight: 500;
      }
    }

    .line {
      width: 1px;
      background: #dadada;
      height: 32px;
      margin-right: 26px;
    }
  }
  .el-scrollbar {
    flex: 1;

    :deep(.is-horizontal) {
      display: none;
    }
  }
  .card_list {
    display: grid;
    grid-gap: 15px;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  }
  .pagination_wrap {
    height: 58px;
    margin-top: 24px;
  }
}
</style>
