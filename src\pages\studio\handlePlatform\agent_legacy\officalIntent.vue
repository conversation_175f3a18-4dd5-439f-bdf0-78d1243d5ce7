<template>
  <os-page :options="pageOptions" @returnCb="goback" class="agent-os-page">
    <div class="mgb24" @keyup.enter="searchIntention">
      <el-input
        class="search-area"
        placeholder="搜索意图"
        v-model="intentionSearchName"
      >
        <i
          slot="suffix"
          class="el-input__icon el-icon-search search-area-btn"
          @click="searchIntention"
        />
      </el-input>
    </div>

    <os-table
      class="intentions-table"
      :tableData="tableData"
      style="margin-bottom: 56px"
    >
      <el-table-column type="index" width="50">
        <template slot-scope="scope">
          {{ (tableData.page - 1) * tableData.size + scope.$index + 1 }}
        </template>
      </el-table-column>

      <el-table-column prop="intentName" label="意图名称" min-width="100px">
        <template slot-scope="scope">
          <div class="intent-zhname ib" @click="toEdit(scope.row)">
            {{ scope.row.intentName }}
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="intentNameEn" label="英文标识" min-width="100px">
        <template slot-scope="scope">
          <div class="intent-zhname ib" @click="toEdit(scope.row)">
            {{ scope.row.intentNameEn }}
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="intentDesc" label="意图概述" min-width="300px">
        <template slot-scope="scope">
          <div
            class="intent-name"
            @click="toEdit(scope.row)"
            :title="scope.row.descrition"
          >
            {{ scope.row.intentDesc }}
          </div>
        </template>
      </el-table-column>

      <el-table-column label="引用" prop="quote">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.quote"
            @change="(val) => changeSwitch(scope.row, val)"
          ></el-switch>
        </template>
      </el-table-column>
    </os-table>
  </os-page>
</template>

<script>
export default {
  name: 'IflyAIuiWebReferOfficialListIntent',

  data() {
    return {
      pageOptions: {
        title: '引用官方意图',
        returnBtn: true,
        loading: false,
      },
      intentionSearchName: null,
      tableData: {
        loading: false,
        total: 0,
        page: 1,
        size: 10,
        list: [],
      },
    }
  },

  mounted() {
    this.searchIntention()
  },

  methods: {
    searchIntention() {
      const params = {
        pageIndex: this.tableData.page,
        pageSize: this.tableData.size,
        intentName: this.intentionSearchName,
        agentId: this.$route.params.agentId,
      }
      this.$utils.httpPost(
        this.$config.api.AGENT_OFFFICAL_INTENT_LIST_OLD,
        JSON.stringify(params),
        {
          config: {
            headers: {
              'Content-Type': 'application/json;charset=UTF-8',
            },
          },
          success: (res) => {
            if (res.code == '0') {
              this.tableData.list = res.data.data
            }
          },
          error: (err) => {
            this.$message.error(err.desc)
          },
        }
      )
    },
    changeSwitch(data, flag) {
      const params = {
        agentId: this.$route.params.agentId,
        intentId: data.intentId,
        quoteFlag: flag,
      }
      this.$utils.httpPost(
        this.$config.api.AGENT_OFFICAL_INTENT_QUOTE_OLD,
        JSON.stringify(params),
        {
          config: {
            headers: {
              'Content-Type': 'application/json;charset=UTF-8',
            },
          },
          success: (res) => {
            if (res.code == '0') {
              this.$message.success('操作成功')
              this.searchIntention()
            }
          },
          error: (err) => {
            this.$message.error(err.desc)
          },
        }
      )
    },
    toEdit(data) {},
    goback() {
      this.$router.go(-1)
    },
  },
}
</script>

<style lang="scss">
.el-switch.cell-handle-hovershow {
  display: inline-flex;
}
.agent-os-page {
  //   .os-scroll {
  //     display: none;
  //   }
  .search-area {
    margin-top: 10px;
  }
}
</style>
