<template>
  <el-dialog title="版本管理" :visible.sync="dialog.show" width="760px"
    ><div
      v-loading="rollbackLoading"
      element-loading-text="版本回滚中，请稍候"
      class="version-wrap"
    >
      <os-page-label label="线上版本" class="mgb12"></os-page-label>
      <os-table class="mgb40" :tableData="online">
        <el-table-column prop="number" label="版本" width="96">
        </el-table-column>
        <el-table-column prop="date" label="发布时间" width="176">
          <template slot-scope="scope">
            {{ scope.row.createTime | date }}
          </template>
        </el-table-column>
        <el-table-column label="更新描述">
          <template slot-scope="scope">
            <el-popover
              class="update-log"
              trigger="hover"
              placement="bottom-start"
              :content="scope.row.updateLog"
              v-if="scope.row.updateLog"
            >
              <div slot="reference">
                {{ scope.row.updateLog }}
              </div>
            </el-popover>
            <span v-else>-</span>
          </template>
        </el-table-column>
      </os-table>

      <os-page-label label="历史版本" class="mgb12"></os-page-label>
      <os-table :tableData="history" class="mgb56">
        <el-table-column prop="number" label="版本" width="96">
        </el-table-column>
        <el-table-column prop="date" label="发布时间" width="176">
          <template slot-scope="scope">
            {{ scope.row.createTime | date }}
          </template>
        </el-table-column>
        <el-table-column label="更新描述">
          <template slot-scope="scope">
            <el-popover
              class="update-log"
              trigger="hover"
              placement="bottom-start"
              :content="scope.row.updateLog"
              v-if="scope.row.updateLog"
            >
              <div slot="reference">
                {{ scope.row.updateLog }}
              </div>
            </el-popover>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="96">
          <template slot-scope="scope">
            <a
              v-if="scope.row.isRollback === 0 && subAccountEditable"
              @click="rollbackCheck(scope.row)"
              >回滚</a
            >
            <el-popover
              trigger="hover"
              placement="bottom-start"
              content="不可回滚至该版本"
              v-else
            >
              <div class="rollback-default" slot="reference">回滚</div>
            </el-popover>
          </template>
        </el-table-column>
      </os-table>
      <confirm-rollback-dialog
        :dialog="confirmRollbackDialog"
        :sceneCompare="sceneCompare"
        :appVersion="appVersion"
        :appId="appId"
        @getVersion="getVersion"
      /></div
  ></el-dialog>
</template>
<script>
import ConfirmRollbackDialog from './confirmRollback'

export default {
  name: 'app-publish',
  props: {
    subAccountEditable: Boolean,
    dialog: {
      type: Object,
      default: () => {
        return {
          show: false,
        }
      },
    },
  },
  data() {
    return {
      pageOptions: {
        title: '版本管理',
        loading: false,
        returnBtn: false,
      },
      online: {
        total: 0,
        list: [],
      },
      history: {
        total: 0,
        page: 1,
        size: 5,
        list: [],
      },
      confirmRollbackDialog: {
        show: false,
      },
      appVersionId: {},
      sceneCompare: {},
      appVersion: {},
      rollbackLoading: false,
    }
  },
  computed: {
    appId() {
      return this.$route.params.appId
    },
  },
  watch: {
    'dialog.show'(val) {
      if (val) {
        this.getVersion()
      }
    },
  },
  // created() {
  //   this.getVersion()
  // },
  methods: {
    getVersion() {
      this.$utils.httpGet(
        this.$config.api.AIUI_APP_VERSION_LIST,
        {
          appid: this.appId,
        },
        {
          success: (res) => {
            this.online.list.splice(0)
            this.history.list.splice(0)
            if (res.data && !Object.keys(res.data).length) {
              return
            }
            this.online.list.push(...res.data.online)
            this.history.list.push(...res.data.history)
          },
        }
      )
    },
    rollbackCheck(appVersion) {
      // this.appVersionId = appVersion.appVersionId
      // this.$utils.httpGet(this.$config.api.AIUI_APP_VERSION_ROLLBACK_CHECK, {
      //   appid: this.appId,
      //   appVersionId: this.appVersionId
      // }, {
      //   success: res => {
      //     this.openConfirmRollback(res.data, appVersion)
      //   }
      // })
      this.$confirm(
        '<p style="color: #FF5A5A;">应用回滚后可能会对您产品的线上效果产生影响。确认要回滚吗？</p>',
        '提示',
        {
          confirmButtonText: '确定回滚',
          cancelButtonText: '取消',
          type: 'warning',
          dangerouslyUseHTMLString: true,
        }
      )
        .then(() => {
          this.rollbackLoading = true
          this.$utils.httpGet(
            this.$config.api.AIUI_APP_VERSION_ROLLBACK,
            {
              appid: this.appId,
              appVersionId: appVersion.appVersionId,
            },
            {
              success: (res) => {
                this.rollbackLoading = false
                this.$message_pro_success(
                  '回滚成功',
                  '请在设备正式环境中验证回滚是否生效。'
                )
                this.getVersion()
              },
              error: (err) => {},
            }
          )
        })
        .catch(() => {})
    },
    openConfirmRollback(sceneCompare, appVersion) {
      this.confirmRollbackDialog.show = true
      this.sceneCompare = sceneCompare
      this.appVersion = appVersion
    },
  },
  components: {
    ConfirmRollbackDialog,
  },
}
</script>

<style lang="scss" scoped>
.version-wrap {
  padding-bottom: 15px;
}
.rollback-default {
  color: $grey4;
}
</style>
