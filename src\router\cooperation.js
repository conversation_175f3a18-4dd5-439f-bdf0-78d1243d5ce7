import DefaultLayout from '@L/defaultLayout'
import aiuiDefaultLayout from '@L/aiuiDefaultLayout'

export default [
  {
    path: "/cooperation",
    component: DefaultLayout,
    children: [
      {
        path: '/',
        name: 'cooperation', // 控制台
        redirect: { name: 'authorities-manage', params: { nav: 'authorities'} }
      },
      {
        path: 'authorities',
        name: 'authorities-manage', // 协同操作-子帐号管理
        component: () => import(/* webpackChunkName: "cooperation-authorities" */ '@P/cooperation/authorities')
      },
      {
        path: 'logs',
        name: 'operate-logs', // 协同操作-操作日志
        component: () => import(/* webpackChunkName: "cooperation-logs" */ '@P/cooperation/logs')
      }
    ]
  }, 
  {
    path: "/sub/user-info",
    component: aiuiDefaultLayout,
    children: [
      {
        path: '/',
        name: 'sub-user-info', // 控制台
        component: () => import(/* webpackChunkName: "studio-official-auxiliaries" */ '@P/cooperation/subUserInfo')
      }
    ]
  },
  {
    path: "/sub/cooperation",
    component: aiuiDefaultLayout,
    children: [
      {
        path: 'logs',
        name: 'sub-operate-logs', // 协同操作-操作日志
        component: () => import(/* webpackChunkName: "cooperation-logs" */ '@P/cooperation/logs')
      }
    ]
  }, 
]