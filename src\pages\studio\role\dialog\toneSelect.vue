<template>
  <el-dialog :visible.sync="dialog.show" width="840px" destroy-on-close>
    <div slot="title">
      <p class="el-dialog__title">选择音色</p>
      <span style="font-size: 13px" v-show="!fromApp"
        >角色的默认音色。注意需在<a @click="jumpToApp">应用配置</a
        >中打开“角色发音人”</span
      >
    </div>
    <div class="tone-content">
      <el-input
        class="search-area"
        placeholder="输入名称检索"
        size="small"
        v-model="filterValue.search"
      >
        <i
          slot="suffix"
          class="el-input__icon el-icon-search search-area-btn"
          @click="getTtsList()"
        />
      </el-input>
      <p>
        <label class="label">声音来源</label>
        <span
          v-for="(item, index) in sourceList"
          :key="item.value"
          class="tag-item"
          :class="{ 'tag-item-active': filterValue.source === item.value }"
          @click="() => (filterValue.source = item.value)"
        >
          {{ item.label }}
        </span>
      </p>
      <p v-show="filterValue.source === 'official'">
        <label class="label">年龄段</label>
        <span
          v-for="(item, index) in ageList"
          :key="item.value"
          class="tag-item"
          :class="{ 'tag-item-active': filterValue.age === item.value }"
          @click="() => (filterValue.age = item.value)"
        >
          {{ item.label }}
        </span>
      </p>
      <p v-show="filterValue.source === 'official'">
        <label class="label">类型</label>
        <span
          v-for="(item, index) in typeList"
          :key="item.value"
          class="tag-item"
          :class="{ 'tag-item-active': filterValue.type === item.value }"
          @click="() => (filterValue.type = item.value)"
        >
          {{ item.label }}
        </span>
      </p>
      <div class="tone-list" v-if="informants.length > 0">
        <tone-card
          v-for="(item, index) in informants"
          :key="item.vcn"
          :cardData="item"
          :roleTtsData="currenTtsData"
          :fromApp="fromApp"
          @playVoice="toggleAudio(item)"
          @selectRole="selectRole"
          @getList="getTtsList"
          @refresh="refresh"
        ></tone-card>
      </div>
      <div v-else>
        <empty :emptyText="`没有符合条件的音色`" />
      </div>
      <audio ref="audio"></audio>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="dialog.show = false"> 取消 </el-button>
      <el-button class="dialog-btn" type="primary" @click="save">
        确定
      </el-button>
    </span>
  </el-dialog>
</template>
<script>
import toneCard from '../toneCard.vue'
import Empty from '../empty.vue'

export default {
  name: 'tone-dialog',
  props: {
    dialog: {
      type: Object,
      default: () => ({
        show: false,
      }),
    },
    roleTtsData: {
      type: Object,
      default: () => ({}),
    },
    fromApp: {
      type: Boolean,
      default: false,
    },
    currentScene: {
      type: Object,
      default: () => ({}),
    },
  },
  components: {
    toneCard,
    Empty,
  },
  data() {
    return {
      sourceList: [
        { label: '官方音色', value: 'official' },
        { label: '声音复刻', value: 'custom' },
      ],
      ageList: [
        { label: '全部', value: '' },
        { label: '成人女', value: '成年女声' },
        { label: '成人男', value: '成年男声' },
        { label: '儿童女', value: '女童声' },
        { label: '儿童男', value: '男童声' },
      ],
      typeList: [
        { label: '全部', value: '' },
        { label: '超拟人', value: '3' },
        { label: '极速超拟人', value: '4' },
        { label: '普通', value: '2' },
      ],
      filterValue: {
        source: '', //official官方 custom复刻
        age: '',
        type: '',
        search: '',
      },
      informants: [],
      currenTtsData: {},
      audio: null,
    }
  },
  methods: {
    getTtsList() {
      const setInformants = (list) => {
        this.informants = list || []
        this.$nextTick(() => {
          const el = document.querySelector('.card-active')
          if (el) {
            el.scrollIntoView({ behavior: 'auto', block: 'center' })
          }
        })
      }

      if (this.filterValue.source === 'official') {
        if (this.fromApp) {
          //应用侧获取发音人列表
          let data = {
            age: this.filterValue.age,
            ttsType: this.filterValue.type,
            search: this.filterValue.search,
            appid: this.$route.params.appId,
          }
          if (this.currentScene && this.currentScene.sos) {
            data['sceneType'] = 'sparkos'
          }
          this.$utils.httpGet(this.$config.api.AIUI_BOT_ROLE_TTS_LIST, data, {
            success: (res) => {
              // this.informants = res.data.informants || []
              setInformants(res.data.informants)
            },
            error: (err) => {},
          })
        } else {
          this.$utils.httpGet(
            this.$config.api.AIUI_ROLE_TTS_LIST,
            {
              age: this.filterValue.age,
              ttsType: this.filterValue.type,
              search: this.filterValue.search,
            },
            {
              success: (res) => {
                // this.informants = res.data.informants || []
                setInformants(res.data.informants)
              },
              error: (err) => {},
            }
          )
        }
      } else if (this.filterValue.source === 'custom') {
        this.$utils.httpGet(
          this.$config.api.AIUI_ROLE_VOICE_CLONE_LIST,
          {
            search: this.filterValue.search,
          },
          {
            success: (res) => {
              // this.informants = res.data.voiceClones || []
              setInformants(res.data.voiceClones)
            },
            error: (err) => {},
          }
        )
      }
    },

    toggleAudio(row) {
      if (row.isPlay) {
        this.$set(row, 'isPlay', false)
        this.$refs.audio.pause()
      } else {
        this.informants.forEach((item) => {
          this.$set(item, 'isPlay', false)
        })
        this.$refs.audio.pause()
        this.$refs.audio.src = row.audio
        this.$refs.audio.play()
        this.$set(row, 'isPlay', true)
        this.$refs.audio.onended = () => {
          this.$set(row, 'isPlay', false)
        }
      }
    },
    selectRole(cardData) {
      if (this.fromApp) {
        if (!(cardData.auth || cardData.resId)) {
          return this.$message.warning('当前发音人未授权，请联系商务或************************获取授权')
        }
      }
      this.currenTtsData = cardData
    },
    save() {
      if (!this.currenTtsData.vcn && !this.currenTtsData.id) {
        this.$message.warning('请选择音色')
      }
      if (
        this.currenTtsData.vcn != this.roleTtsData.vcn ||
        this.currenTtsData.id != this.roleTtsData.id
      ) {
        this.$emit('change', this.currenTtsData)
      }
      this.dialog.show = false
    },
    jumpToApp() {
      window.open('/app')
    },
    refresh() {
      this.$emit('refreshTts')
    },
  },
  mounted() {},
  watch: {
    filterValue: {
      handler(newValue, oldValue) {
        this.getTtsList()
      },
      deep: true,
    },
    'dialog.show': function (val, oldVal) {
      if (val) {
        this.currenTtsData = this.roleTtsData
        if (this.roleTtsData.vcn === 'x5_clone') {
          this.filterValue.source = 'custom'
        } else {
          this.filterValue.source = 'official'
        }
      } else {
        this.filterValue = {
          source: '', //official官方 custom复刻
          age: '',
          type: '',
          search: '',
        }
      }
    },
    roleTtsData: {
      handler(newValue, oldValue) {
        this.currenTtsData = newValue
      },
    },
  },
}
</script>
<style lang="scss" scoped>
.tone-content {
  padding-bottom: 24px;
  p {
    margin-bottom: 0;
  }
  .label {
    padding: 2px 8px;
    color: #009bff;
    background: #f2f8ff;
    border: 1px solid #009bff;
    border-radius: 6px;
    display: inline-block;
    margin: 10px 0;
    margin-right: 8px;
    font-size: 12px;
  }
  .tag-item {
    display: inline-block;
    cursor: pointer;
    padding: 6px 8px;
    margin-right: 8px;
    font-size: 12px;
  }
  .tag-item-active {
    background: #e1f3ff;
    border-radius: 4px;
  }
  .search-area {
    width: 260px;
    margin-bottom: 12px;
  }
  .tone-list {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
    max-height: 400px;
    overflow: auto;
    margin-top: 8px;
  }
}
</style>
