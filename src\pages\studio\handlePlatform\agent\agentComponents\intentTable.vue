<template>
  <os-table :tableData="intentTableData" style="margin-top: 20px">
    <el-table-column type="expand">
      <template slot-scope="scopes">
        <!-- {{ scopes.row.intentName }} -->
        <el-card style="width: 100%">
          <div class="sub-title">关键信息</div>

          <el-table :data="scopes.row.keyInfoList">
            <el-table-column
              label="查询的关键信息"
              prop="query"
            ></el-table-column>
            <el-table-column label="关键信息描述" prop="desc"></el-table-column>

            <el-table-column label="是否必须" prop="required">
              <template slot-scope="scopes">
                <el-checkbox v-model="scopes.row.required"></el-checkbox>
              </template>
            </el-table-column>

            <el-table-column label="操作" width="300px">
              <template slot-scope="scopes">
                <el-button
                  type="text"
                  icon="el-icon-edit"
                  @click="editKeyInfo(scopes.row)"
                ></el-button>
                <el-button
                  type="text"
                  icon="el-icon-delete"
                  @click="deleteKeyInfo(scopes.row)"
                ></el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-button class="dashed-button" icon="el-icon-plus" @click="addInfo"
            >添加关键信息</el-button
          >

          <div class="sub-title">查询接口</div>
          <el-form :model="scopes.row.form" label-width="200px">
            <el-form-item label="工具路径：">
              <el-input></el-input>
            </el-form-item>

            <el-form-item label="授权方式：">
              <el-radio-group v-model="scopes.row.form.authType">
                <el-radio :label="-1" border
                  >无需授权，无需权限即可使用API</el-radio
                >
                <el-radio :label="2" border
                  >需要在请求头 (header) 或查询参数 (query)
                  中携带授权信息</el-radio
                >
              </el-radio-group>
            </el-form-item>

            <el-form-item label="位置：" v-if="scopes.row.form.authType === 2">
              <el-select
                v-model="scopes.row.form.location"
                style="width: 500px"
              >
                <el-option value="header" label="Header"></el-option>
                <el-option value="query" label="Query"></el-option>
              </el-select>
            </el-form-item>

            <el-form-item
              label="Parameter name："
              v-if="scopes.row.form.authType === 2"
            >
              <el-input
                v-model="scopes.row.form.parameterName"
                placeholder="密钥的参数，您需要传递Service Token的参数名"
              ></el-input>
            </el-form-item>

            <el-form-item
              label="Service token / APl key："
              v-if="scopes.row.form.authType === 2"
            >
              <el-input
                v-model="scopes.row.form.serviceToken"
                placeholder="密钥的参数，代表您的身份或给定的服务权限"
              ></el-input>
            </el-form-item>

            <el-form-item label="请求方法：">
              <el-select v-model="scopes.row.form.method">
                <el-option label="get" value="get"></el-option>
                <el-option label="post" value="post"></el-option>
                <el-option label="put" value="put"></el-option>
                <el-option label="delete" value="delete"></el-option>
                <el-option label="patch" value="patch"></el-option>
                <!-- <el-option
                  v-for="item in methodOptions"
                  :key="item.index"
                  :value="item.value"
                  :label="item.label"
                ></el-option> -->
              </el-select>
            </el-form-item>
          </el-form>

          <div style="color: black">配置输入参数</div>
        </el-card>
      </template>
    </el-table-column>

    <el-table-column
      label="意图名称"
      prop="intentName"
      width="150px"
    ></el-table-column>

    <el-table-column
      label="英文标识"
      prop="intentNameEn"
      width="150px"
    ></el-table-column>

    <el-table-column
      label="描述"
      prop="intentDesc"
      min-width="260px"
    ></el-table-column>

    <el-table-column
      label="包含实例说法"
      prop="count"
      width="150px"
    ></el-table-column>

    <el-table-column label="操作"></el-table-column>
  </os-table>
</template>

<script>
export default {
  name: 'AgentDetail1',
  data() {
    return {
      methodOptions: [
        { value: 'get', label: 'get' },
        { value: 'post', label: 'post' },
        { value: 'put', label: 'put' },
        { value: 'delete', label: 'delete' },
        { value: 'patch', label: 'patch' },
      ],
    }
  },
  props: {
    intentTableData: {
      require: true,
      type: Object,
      default: {
        loading: false,
        total: 0,
        page: 1,
        size: 10,
        handles: ['edit', 'del'],
        handleColumnTest: '操作',
        list: [],
      },
    },
  },
  mounted() {},
  methods: {
    editKeyInfo(data) {},
    deleteKeyInfo(data) {},
    addInfo() {},
  },
}
</script>

<style lang="scss">
.sub-title {
  padding-left: 10px;
  border-left: 2px solid #0000ff;
  height: 22px;
  margin: 10px 0px;
}
.dashed-button {
  width: 100%;
  border: 1px dashed #d7d7d7 !important; /* 蓝色虚线边框 */
  color: #409eff !important; /* 蓝色文字 */
  background-color: transparent !important; /* 透明背景 */
}
</style>
