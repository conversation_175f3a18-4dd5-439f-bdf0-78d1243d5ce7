<template>
  <div class="empty">
    <div>
      <img
        class="empty-img"
        :src="require(`@A/images/studio-handle-platform/empty.png`)"
      />
      <div class="empty-text">
        {{ emptyText }}
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'empty-tip-api',
  props: {
    emptyText: {
      type: String,
      default: '暂无数据',
    },
  },
  data() {
    return {}
  },
  methods: {},
}
</script>
<style lang="scss" scoped>
.empty {
  width: 100%;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  .empty-img {
    width: 130px;
    height: 120px;
    margin-bottom: 20px;
  }
  .empty-text {
    font-size: 14px;
    font-family: PingFang SC, PingFang SC-400;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.3);
    line-height: 20px;
  }
}
</style>
