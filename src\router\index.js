import Vue from 'vue'
import VueRouter from 'vue-router'
import StudioRouter from './studioRouter.js'
import AiuiRouter from './aiuiRouter.js'
import CooperationRouter from './cooperation.js'
import SubAccountRouter from './subAccountRouter.js'
import orderRouter from './orders.js'
import SubAiuiRouter from './subAiuiRouter.js'
import User from './user.js'
import AiCalender from './aiCalender'
import aiuiHome from '@L/aiuiHome'

import Store from '../store'
import { utils } from '@U'
import api from '@U/api.js'
import { Env } from '../config'

// const originalPush = VueRouter.prototype.push
// VueRouter.prototype.push = function push(location) {
//   return originalPush.call(this, location).catch((err) => err)
// }

Vue.use(VueRouter)

const DevRouter = [
  // AIUI 首页
  {
    path: '/',
    component: aiuiHome,
    children: [
      {
        path: '/',
        name: 'portal', // AIUI 首页
        meta: {
          title: 'AIUI开放平台',
          metaInfo: {
            title: 'AIUI开放平台_以讯飞星火大模型为底座的人机交互开发平台',
            keywords:
              'AIUI,讯飞语音交互,讯飞语义理解,AIUI人机交互,全链路语音交互,全链路人机交互,讯飞人机交互,讯飞自然语言理解,全双工交互,语音唤醒,语音助手开发,语音交互接入,接入语音交互,语音交互控制,语音交互设计,讯飞人工智能,讯飞AIUI开放平台,讯飞云平台,讯飞星火大模型,星火交互大模型,多模态唤醒,虚拟人互动,数字人互动,多语种识别,超拟人合成,声音复刻,硬件模组,讯飞星火,人机交互,智能硬件,消费电子语音交互,消费电子人机交互,手机语音交互,电视语音交互,机器人语音交互,地铁火车轨道交通语音交互,PC语音助手,语音软硬件,语音交互控制',
            description:
              'AIUI是以讯飞星火大模型为核心的人机交互开发平台，具备多模态唤醒、虚拟人驱动、多语种识别、超拟人合成、声音复刻等特性。广泛应用于手机、电视、机器人扫读笔、语音购票等智能硬件设备上。提供SDK、Websocket、硬件模组等，接入集成简单，开箱即用。',
          },
        },
        component: () =>
          import(/* webpackChunkName: "indexAIUI" */ '@P/aiui/index-aiui'),
      },
    ],
  },
  {
    path: '/studio/login',
    name: 'login',
    component: () => import(/* webpackChunkName: "login" */ '@P/dev/login'),
  },
  {
    path: '/ubot/login',
    name: 'ubotLogin',
    component: () => import(/* webpackChunkName: "login" */ '@P/dev/ubotLogin'),
  },
  {
    path: '/sub/login',
    name: 'sub-login',
    component: () => import(/* webpackChunkName: "login" */ '@P/dev/login'),
  },
]

const router = new VueRouter({
  mode: 'history',
  routes: [
    ...StudioRouter,
    ...AiuiRouter,
    ...DevRouter,
    ...CooperationRouter,
    ...SubAccountRouter,
    ...orderRouter,
    ...SubAiuiRouter,
    ...User,
    ...AiCalender,
  ],
})

router.onError((error) => {
  const pattern = /Loading chunk (\d)+ failed/g
  const isChunkLoadFailed = error.message.match(pattern)
  const targetPath = router.history.pending.fullPath
  console.log('router error', error)
  console.log('target path', targetPath)
  if (isChunkLoadFailed) {
    // router.replace(targetPath);
    window.location.href = targetPath
  }
})

router.beforeEach((to, from, next) => {
  let hasLogin = null,
    userInoFn = 'user/setUserInfo',
    //loginPath = `${process.wwwHost}/user/login?pageFrom=${location.href}`,
    loginPath = null,
    reg = /((aiui\.)|(studio\.))((iflyos|xfyun))\./
  if (
    (to.name && to.name.indexOf('sub-') >= 0) ||
    (to.path && to.path.indexOf('sub/') !== -1)
  ) {
    Store.dispatch('user/setAccountType', 'sub')
    userInoFn = 'user/setSubAccountInfo'
    hasLogin =
      Store.state.user.subAccountInfo &&
      Store.state.user.subAccountInfo.token === utils.getCookie('subSessionId')
    loginPath = `${location.origin}/sub/login?pageFrom=${location.href}`
  } else {
    Store.dispatch('user/setAccountType', 'main')
    userInoFn = 'user/setUserInfo'
    hasLogin =
      Store.state.user.userInfo &&
      Store.state.user.userInfo.token === utils.getCookie('ssoSessionId')
    loginPath = `${location.origin}/user/login?pageFrom=${location.href}`
  }
  if (to.meta.metaInfo) {
    Store.commit('metaModule/CHANGE_META_INFO', to.meta.metaInfo)
  }
  if (to.matched.length === 0) {
    next('/') // 判断此跳转路由的来源路由是否存在，存在的情况跳转到来源路由，否则跳转到首页面
  } else {
    next()
  }
})

export default router
