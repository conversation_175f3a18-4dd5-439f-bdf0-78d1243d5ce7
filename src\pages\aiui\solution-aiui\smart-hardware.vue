<template>
  <pc v-if="userAgent === 'pc'"></pc>
  <mobile v-else-if="userAgent === 'mobile'"></mobile>
  <span v-else>加载中...</span>
</template>

<script>
import pc from "./smart-hardware/pc.vue";
import mobile from "./smart-hardware/mobile-pages/index.vue";
import utils from "@A/lib/utils.js";

export default {
  name: "App",
  data() {
    return {
      userAgent: ""
    };
  },
  created() {
    // 获取是否是h5
    const isMobile = utils.isMobile();
    if (isMobile) {
      this.userAgent = "mobile";
    } else {
      this.userAgent = "pc";
    }
  },
  components: { pc, mobile }
};
</script>

<style lang="scss"></style>
