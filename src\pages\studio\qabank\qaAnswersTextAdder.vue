<template>
  <div class="container">
    <div class="confirm-adder" v-show="data.length < max">
      <!-- <span class="ib serial-number" style="visibility: hidden">{{
        data.length + 1
      }}</span> -->

      <inteligient-rich-input
        v-model="textObj"
        :showAdd="false"
        :showSwitch="false"
        @onAdd="onAdd"
        :placeholder="placeholder"
        ref="intelInput"
        key="intentionConfirm"
        :hasSlot="false"
        :disabled="disabled"
        :multipleMark="false"
      >
      </inteligient-rich-input>
    </div>
    <ul v-if="data && data.length > 0" class="confirm-list">
      <li v-for="(item, index) in pageData" :key="item.answer">
        <div class="content-container">
          <span class="ib serial-number">
            {{ (current - 1) * pageSize + index + 1 }}</span
          >
          <inteligient-rich-input
            :placeholder="editPlaceholder"
            :value="{
              text: item.answer,
              labels: item.labels,
              changed: item.changed,
            }"
            :showAdd="false"
            :showSwitch="false"
            :disabled="disabled"
            :edit="true"
            :editIndex="index"
            :hasSlot="false"
            :multipleMark="false"
            :renderMark="avatar"
            @onEdit="onEdit"
            @change="onChange(index)"
            :saveOnBlur="true"
          >
          </inteligient-rich-input>
        </div>
        <div class="style-container" v-if="qaType === 'sentence'">
          <el-select
            class="qabank-page-theme-content-emotion"
            size="small"
            v-model="item.emotion"
            placeholder="请选择"
            @change="(val) => onEmotionChange(val, index)"
          >
            <el-option
              v-for="it in emojiType"
              :key="it.key"
              :label="it.value"
              :value="it.key"
            >
            </el-option>
          </el-select>
          <el-select
            v-if="showStyle"
            class="qabank-page-theme-content-emotion"
            style="
              width: 160px;
              margin: 0 auto;
              justify-content: center;
              text-align: center !important;
            "
            size="small"
            v-model="item.styles"
            multiple
            @change="onStyleChange(item, index)"
            placeholder="请选择"
          >
            <el-checkbox v-model="item.checked" @change="selectAll(item)"
              >全选</el-checkbox
            >
            <el-option
              v-for="it in styleType"
              :key="it.key"
              :label="it.value"
              :value="it.key"
            >
            </el-option>
          </el-select>
        </div>
        <div class="icon-wrap">
          <!-- <i class="ai-extend-icon "></i> -->
          <i class="delete el-icon-delete" @click="onDelClick(item, index)"></i>
        </div>
      </li>
    </ul>
    <div class="pagination" v-if="data.length > pageSize">
      <el-pagination
        small
        layout="prev, pager, next"
        :total="data.length"
        :page-size="pageSize"
        @current-change="currentChange"
        :current-page="current"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script>
import InteligientRichInput from '../skill/referSlots/inteligientRichInput.vue'
import dicts from '@M/dicts'

export default {
  components: { InteligientRichInput },
  name: 'welcome-words-text-adder',
  props: {
    data: Array,
    disabled: Boolean,
    slotNames: Array,
    reg: {
      default: '',
    },
    warning: {
      type: String,
      default: '输入有误',
    },
    editPlaceholder: String,
    placeholder: String,
    max: {
      type: Number,
      default: 5,
    },
    qaType: {
      type: String,
      default: 'sentence', // 语句问答
    },
    showStyle: {
      type: Boolean,
      default: false,
    },
    avatar: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      textObj: { text: '', emotion: 'default', labels: [] },
      emojiType: dicts.getDictArr('answerEmotion'),
      styleType: dicts.getDictArr('answerStyle'),

      pageSize: 10,
      pageData: [],
      current: 1,
    }
  },
  created() {
    this.getPageData(1)
  },
  watch: {
    avatar(val) {
      console.log('avatar changed in qaAnswerTextAdder', val)
    },
    data(val, oldVal) {
      if (val && oldVal) {
        // 新增场景
        if (val.length > oldVal.length) {
          this.getPageData(1)
        } else if (val.length < oldVal.length) {
          // 删除了项目，判断现在总共有几页
          let totalPage = Math.ceil(parseFloat(val.length / this.pageSize))
          this.getPageData(Math.min(totalPage, this.current))
        } else {
        }
      }
    },
  },
  methods: {
    selectAll(item) {
      item.styles = []
      item.style = 0
      if (item.checked) {
        for (let i = 0; i < this.styleType.length; i++) {
          item.style += this.styleType[i].key
          item.styles.push(this.styleType[i].key)
        }
      } else {
        item.styles = []
      }
      console.log('selectAll', item)
    },
    onStyleChange(item, index) {
      item.style = 0
      for (let i = 0; i < item.styles.length; i++) {
        item.style += item.styles[i]
      }
      if (item.styles.length === this.styleType.length || item.style === 7) {
        item.checked = true
      } else {
        item.checked = false
      }

      let newData = this.data.map((it, i) => {
        if (i === index) {
          return { ...item }
        } else {
          return it
        }
      })
      console.log('new Data', newData)
      this.$emit('dataChange', newData)
    },
    onEmotionChange(val, index) {
      let newData = this.data.map((it, i) => {
        if (i === index) {
          return { ...it, emotion: val }
        } else {
          return it
        }
      })
      console.log('new Data', newData)
      this.$emit('dataChange', newData)
    },
    onDelClick(text, index) {
      let totalIndex = index + (this.current - 1) * this.pageSize
      this.$emit('del', text, totalIndex)
    },

    onAdd(textObj) {
      const val = textObj.text.trim()
      if (!val) {
        return this.$message.warning('输入不能为空')
      }
      // if (val.replace(/[^\{\}]/g, '').replace(/(\{\})/g, '')) {
      //   return this.$message.warning('花括号必须成对出现')
      // }
      const idx = this.data.findIndex((item) => item.answer === val)
      if (idx > -1) {
        return this.$message.warning('不能重复添加')
      }
      if (this.reg && !this.reg.test(val)) {
        return this.$message.warning(this.warning)
      }
      if (/<(i|b|h|a|d|\/)[^>]*>/gi.test(val)) {
        return this.$message.warning('不能包含HTML元素标签')
      }
      // if (/(<([^>]+)>)/i.test(val)) {
      //   return this.$message.warning('不能包含HTML元素标签')
      // }

      // if (!this.checkIntentionSlots(textObj.text)) {
      //   return
      // }
      this.$emit('add', {
        answer: textObj.text,
        emotion: 'default',
        labels: textObj.labels,
      })
      // this.text = ''
      this.$refs.intelInput.clearHTML()
      this.textObj = { text: '', labels: [] }
    },
    onEdit(textObj, index) {
      let totalIndex = index + (this.current - 1) * this.pageSize
      const val = textObj.text.trim()
      if (!val) {
        return this.$message.warning('输入不能为空')
      }
      // if (val.replace(/[^\{\}]/g, '').replace(/(\{\})/g, '')) {
      //   return this.$message.warning('花括号必须成对出现')
      // }

      if (this.reg && !this.reg.test(val)) {
        return this.$message.warning(this.warning)
      }

      // if (/(<([^>]+)>)/i.test(val)) {
      //   return this.$message.warning('不能包含HTML元素标签')
      // }
      if (/<(i|b|h|a|d|\/)[^>]*>/gi.test(val)) {
        return this.$message.warning('不能包含HTML元素标签')
      }
      this.$emit(
        'edit',
        { answer: textObj.text, labels: textObj.labels },
        totalIndex
      )
    },

    onChange(index) {
      console.log('receive onChange', index)
      if (index !== -1) {
        this.$emit('change', index)
      }
    },

    getPageData(page) {
      this.pageData = this.data.slice(
        (page - 1) * this.pageSize,
        (page - 1) * this.pageSize + this.pageSize
      )
      this.current = page || 1
      console.log('this.pageData', this.pageData)
    },
    currentChange(val) {
      console.log('currentChange', val)
      this.getPageData(val)
    },
  },
}
</script>

<style lang="scss" scoped>
.container {
  background: #fff;
  width: 100%;
  height: 100%;
  // border: 1px solid #d5d8de;
  border-radius: 4px;
  padding-bottom: 8px;
  // padding: 0 16px;
  .confirm-adder {
    display: flex;
    align-items: center;
    padding: 12px 20px 0 20px;
    margin-bottom: 10px;
    .input-container {
      width: 100%;
      border: 1px solid #e7e9ed !important;
      border-radius: 4px;
      padding-left: 14px;
    }
  }
  .confirm-list {
    padding: 0 8px;
    > li {
      display: flex;
      align-items: center;
      position: relative;
      padding-right: 20px;
      border-radius: 4px;
      &:hover {
        background: #f3f8ff;
        :deep(.el-input__inner) {
          background: #f3f8ff;
        }
        .delete {
          display: block;
        }
      }
      .icon-wrap {
        position: absolute;
        right: 10px;
        display: flex;
        align-items: center;
      }
      .delete {
        color: #bfbfbf;
        cursor: pointer;
        display: none;
      }
      .ai-extend-icon {
        width: 24px;
        height: 16px;
        background: url(~@A/images/aiui/<EMAIL>) center/contain
          no-repeat;
        margin-right: 5px;
      }
    }
    li + li {
      // margin-top: 18px;
    }
    margin-bottom: 0;
  }
  .number-label {
    margin-right: 20px;
    color: #b8babf;
  }

  .style-container {
    min-width: 60px;
    margin-left: 5px;
  }
  .content-container {
    max-width: calc(100% - 80px);
    display: flex;
    align-items: flex-start;
    flex: 1;
    .serial-number {
      margin-top: 8px;
    }
  }

  :deep(.rich-content-container) {
    .rich-content {
      padding-left: 0;
      line-height: 34px;
      &:focus {
        border: none;
        border-radius: 3px;
      }
    }
  }
}

.serial-number {
  width: 26px;
  min-width: 26px;
  height: 17px;
  background: #ffffff;
  border-radius: 2px;
  box-shadow: 0px 2px 10px 0px rgba(154, 164, 179, 0.27);

  font-size: 12px;
  color: #b1b1b1;
  line-height: 17px;
  text-align: center;
  margin: 0 16px 0 12px;
}
</style>
<style lang="scss" scoped>
.container {
  :deep(.el-input__inner) {
    border: none;
  }
  :deep(.rich-content) {
    line-height: 36px;
    padding: 0 5px;
  }
}
.pagination {
  text-align: right;
  padding: 10px;
}
</style>
