<template>
  <div>
    <div v-for="(item, index) in formData" :key="item.id" class="form-row">
      <!-- 折叠按钮 -->
      <div class="col col-1">
        <el-button
          v-if="item.type === 'object' || item.type === 'array'"
          type="text"
          size="mini"
          :icon="item.collapsed ? 'el-icon-arrow-right' : 'el-icon-arrow-down'"
          @click="toggleCollapse(index)"
          :style="{ marginLeft: `${nestingLevel * 12}px` }"
        ></el-button>
      </div>

      <!-- 参数名称 -->
      <div class="col col-7" v-if="area !== 'output'">
        <el-form-item
          :prop="`${parentProp}.${index}.name`"
          :rules="nameRules(item)"
          :style="{ paddingLeft: `${nestingLevel * 12}px` }"
        >
          <el-input
            v-model="item.name"
            placeholder="参数名称"
            :disabled="item.fatherType === 'array'"
            @change="checkArray(item)"
          ></el-input>
        </el-form-item>
      </div>
      <div class="col col-3" v-else>
        <el-form-item
          :prop="`${parentProp}.${index}.name`"
          :rules="nameRules(item)"
          :style="{ paddingLeft: `${nestingLevel * 12}px` }"
        >
          <el-input
            v-model="item.name"
            placeholder="参数名称"
            :disabled="item.fatherType === 'array'"
            @change="checkArray(item)"
          ></el-input>
        </el-form-item>
      </div>

      <!-- 参数描述 -->
      <div class="col col-8" v-if="area !== 'output'">
        <el-form-item
          :prop="`${parentProp}.${index}.description`"
          :rules="[
            { required: true, message: '请输入参数描述', trigger: 'blur' },
          ]"
        >
          <el-input
            v-model="item.description"
            placeholder="参数描述"
            @change="checkArray(item)"
          ></el-input>
        </el-form-item>
      </div>
      <div class="col col-4" v-else>
        <el-form-item
          :prop="`${parentProp}.${index}.description`"
          :rules="[
            { required: true, message: '请输入参数描述', trigger: 'blur' },
          ]"
        >
          <el-input
            v-model="item.description"
            placeholder="参数描述"
            @change="checkArray(item)"
          ></el-input>
        </el-form-item>
      </div>

      <!-- 参数类型 -->
      <div class="col col-4">
        <el-form-item
          :prop="`${parentProp}.${index}.type`"
          :rules="[
            { required: true, message: '请选择参数类型', trigger: 'change' },
          ]"
        >
          <el-select
            v-model="item.type"
            placeholder="选择参数类型"
            @change="handleTypeChange(item, $event)"
          >
            <el-option label="string" value="string"></el-option>
            <el-option label="number" value="number"></el-option>
            <el-option label="integer" value="integer"></el-option>
            <el-option label="boolean" value="boolean"></el-option>
            <el-option
              v-if="item.fatherType !== 'array'"
              label="array"
              value="array"
            ></el-option>
            <el-option label="object" value="object"></el-option>
          </el-select>
        </el-form-item>
      </div>

      <!-- 输入参数特有字段 -->
      <template v-if="area !== 'output'">
        <div class="col col-3" v-if="nestingLevel === 0">
          <el-form-item
            :prop="`${parentProp}.${index}.location`"
            :rules="[
              { required: true, message: '请选择传入方法', trigger: 'change' },
            ]"
          >
            <el-select v-model="item.location" placeholder="选择传入方法">
              <el-option label="query" value="query"></el-option>
              <el-option label="body" value="body"></el-option>
              <el-option label="path" value="path"></el-option>
              <el-option label="header" value="header"></el-option>
            </el-select>
          </el-form-item>
        </div>

        <div class="col col-3" v-if="item.fatherType !== 'array'">
          <el-form-item :prop="`${parentProp}.${index}.required`">
            <el-checkbox
              v-model="item.required"
              @change="checkArray(item)"
            ></el-checkbox>
          </el-form-item>
        </div>

        <div class="col col-3">
          <template v-if="item.type !== 'object' && item.type !== 'array'">
            <el-form-item
              :prop="`${parentProp}.${index}.defaultValue`"
              :rules="[
                {
                  required: item.required,
                  message: '请输入默认值',
                  trigger: 'blur',
                },
              ]"
              :style="{ visibility: item.arraySon ? 'hidden' : 'visible' }"
            >
              <el-input
                v-model="item.defaultValue"
                placeholder="默认值"
              ></el-input>
            </el-form-item>
          </template>
          <template v-else-if="item.type === 'array' && !item.arraySon">
            <el-form-item :prop="`${parentProp}.${index}.defaultValue`">
              <el-button type="dashed" @click="handleArrayEdit(item)"
                >编辑数组参数</el-button
              >
            </el-form-item>
          </template>
        </div>
      </template>

      <!-- 开关 -->
      <div class="col col-2">
        <el-form-item :prop="`${parentProp}.${index}.open`">
          <el-switch v-model="item.open"></el-switch>
        </el-form-item>
      </div>

      <!-- 操作按钮 -->
      <div class="col col-2">
        <el-form-item>
          <div>
            <i
              v-if="item.type === 'object'"
              class="el-icon-circle-plus-outline"
              style="cursor: pointer; margin-right: 8px"
              @click="addChild(item, 'object')"
            ></i>
            <i
              v-if="item.fatherType !== 'array'"
              class="el-icon-remove-outline"
              style="cursor: pointer"
              @click="removeItem(index)"
            ></i>
          </div>
        </el-form-item>
      </div>

      <!-- 嵌套子项 -->
      <div
        v-if="
          (item.type === 'object' || item.type === 'array') &&
          item.children &&
          item.children.length > 0 &&
          !item.collapsed
        "
        class="nested-children"
      >
        <nested-form-row
          :form-data="item.children"
          :parent-prop="`${parentProp}.${index}.children`"
          :nesting-level="nestingLevel + 1"
          :area="area"
          @update="handleChildUpdate(index, $event)"
        ></nested-form-row>
      </div>
    </div>

    <!-- 添加按钮 -->
    <el-button
      type="dashed"
      icon="el-icon-plus"
      @click="addRootItem"
      style="width: 100%; margin-top: 10px"
    >
      添加
    </el-button>
  </div>
</template>

<script>
import { v4 as uuidv4 } from 'uuid'

export default {
  name: 'NestedFormRow',
  props: {
    formData: {
      type: Array,
      required: true,
    },
    parentProp: {
      type: String,
      default: 'formData',
    },
    nestingLevel: {
      type: Number,
      default: 0,
    },
    area: {
      type: String,
      default: 'input',
    },
  },
  methods: {
    addRootItem() {
      const newItem = {
        id: uuidv4(),
        type: 'string',
        required: true,
        open: true,
        children: [],
      }

      if (this.area !== 'output') {
        newItem.location = 'query'
      }

      this.formData.push(newItem)
      this.$emit('update', this.formData)
    },
    addChild(parentItem, type) {
      if (!parentItem.children) {
        this.$set(parentItem, 'children', [])
      }

      let newChild = {}
      if (type === 'object') {
        newChild = {
          id: uuidv4(),
          type: 'string',
          required: true,
          open: true,
          fatherType: 'object',
          arraySon: parentItem.arraySon,
          collapsed: false,
        }

        if (this.area !== 'output') {
          newChild.location = 'query'
        }
      } else if (type === 'array') {
        newChild = {
          id: uuidv4(),
          name: '[Array Item]',
          fatherType: 'array',
          arraySon: true,
          type: 'string',
          required: false,
          open: true,
          collapsed: false,
        }

        if (this.area !== 'output') {
          newChild.location = 'query'
        }
      }

      parentItem.children.push(newChild)
      this.$emit('update', this.formData)
    },
    removeItem(index) {
      this.formData.splice(index, 1)
      this.$emit('update', this.formData)
    },
    toggleCollapse(index) {
      this.$set(
        this.formData[index],
        'collapsed',
        !this.formData[index].collapsed
      )
      this.$emit('update', this.formData)
    },
    handleTypeChange(item, newType) {
      if (item.children) {
        item.children = []
      }
      this.checkArray(item)

      if (newType === 'object' || newType === 'array') {
        this.addChild(item, newType)
      }

      this.$emit('update', this.formData)
    },
    checkArray(item) {
      if (item.arraySon) {
        const topArrayPath = this.findTopArrayPath(item)
        if (topArrayPath) {
          const parentValue = this.getValueByPath(topArrayPath)
          if (
            parentValue &&
            parentValue.children &&
            parentValue.children.length > 0
          ) {
            const childItem = this.clearDefault(
              JSON.parse(JSON.stringify(parentValue.children[0]))
            )
            this.setValueByPath([...topArrayPath, 'children'], [childItem])
          }
        }
      }
    },
    findTopArrayPath(item) {
      // 实现查找顶层数组路径的逻辑
    },
    clearDefault(obj) {
      obj.defaultValue = ''
      if (obj.children && obj.children.length > 0) {
        for (let child of obj.children) {
          this.clearDefault(child)
        }
      }
      return obj
    },
    nameRules(item) {
      return [
        { required: true, message: '请输入参数名称', trigger: 'blur' },
        { max: 20, message: '只能输入20个以内的字符', trigger: 'blur' },
        {
          validator: (rule, value, callback) => {
            if (!value) {
              callback()
              return
            }

            // 检查同层级名称是否重复
            const duplicates = this.formData.filter(
              (field, i) =>
                i !== this.formData.indexOf(item) && field.name === value
            )

            if (duplicates.length > 0 && item.fatherType !== 'array') {
              callback(new Error('同层级参数名称不能重复'))
            } else if (item.fatherType === 'array') {
              callback()
            } else if (/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(value)) {
              callback()
            } else {
              callback(
                new Error('只能包含字母、数字或下划线，并且以字母或下划线开头')
              )
            }
          },
          trigger: 'blur',
        },
      ]
    },
    handleChildUpdate(index, children) {
      this.$set(this.formData[index], 'children', children)
      this.$emit('update', this.formData)
    },
    handleArrayEdit(item) {
      this.$emit('edit-array', item, this.parentProp)
    },
  },
}
</script>

<style scoped>
.form-row {
  display: flex;
  margin-bottom: 16px;
  align-items: flex-start;
}

.nested-children {
  width: 100%;
  padding-left: 30px;
  margin-top: 10px;
  border-left: 1px dashed #dcdfe6;
}

.el-form-item {
  margin-bottom: 0;
}
</style>
