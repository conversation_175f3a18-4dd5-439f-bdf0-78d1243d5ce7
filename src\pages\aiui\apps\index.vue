<template>
  <div class="os-scroll">
    <div class="top-wrapper">应用</div>
    <div class="app-list-wrap">
      <div class="app-list__bottom-content">
        <template v-if="!noApp">
          <div class="top-opr-container">
            <div>
              <!-- <router-link
                v-if="createAuth"
                :to="{ name: subAccount ? 'sub-apps-add' : 'apps-add' }"
              >
                <el-button class="create-app-btn" type="primary">
                  <span
                    class="AIUI-myapp-iconfont ai-myapp-add create-app-btn-icon"
                  ></span>
                  创建应用
                </el-button>
              </router-link> -->
              <el-button
                class="create-app-btn"
                type="primary"
                size="medium"
                v-if="createAuth"
                @click="createApp"
              >
                <i class="el-icon-plus" style="margin-right: 5px"></i>创建应用
              </el-button>
              <a
                style="margin-left: 20px"
                href="/access"
                target="_blank"
                v-if="!$route.path.match('sub/apps')"
                >AIUI服务政策</a
              >
            </div>
            <el-input
              class="search-input"
              size="medium"
              v-model="search"
              placeholder="请输入应用名或APPID搜索"
              @keyup.native.enter="getSearchResult"
              style="width: 400px"
            >
              <i
                slot="prefix"
                class="el-input__icon el-icon-search search-area-btn"
                @click="getSearchResult"
              />
            </el-input>
          </div>
          <os-table
            class="app-list-table gutter-table-style transparent-bgc"
            :border="false"
            :tableData="tableData"
            :height="'calc(100vh - 212px)'"
            @change="getAppList"
            @edit="toEdit"
            @del="toDeleteApp"
            @row-click="toEdit"
          >
            <el-table-column prop="appName" label="应用名称">
              <template slot-scope="scope">
                <os-skill-simple-item
                  class="cp table-ceil-app-name"
                  :url="scope.row.url"
                  :name="scope.row.appName || '-'"
                  :index="scope.$index"
                  @click.native="toEdit(scope.row)"
                />
              </template>
            </el-table-column>
            <el-table-column prop="appid" label="APPID" width="150">
            </el-table-column>
            <el-table-column prop="appTypeName" label="分类" width="200">
              <template slot-scope="scope">
                <span
                  v-if="scope.row.appType && scope.row.appType != 'undefined'"
                  >{{ scope.row.appTypeName }}</span
                >
                <span v-else>-</span>
              </template>
            </el-table-column>
            <!-- <el-table-column prop="platform" label="应用平台" width="170">
            <template slot-scope="scope">
              <span v-if="scope.row.platform == 'all'">-</span>
              <span v-else-if="platform[scope.row.platform] == 'aiui'"
                >评估板</span
              >
              <span
                v-else-if="platform[scope.row.platform] == 'morfei'"
                title="讯飞魔飞智能麦克风"
                >讯飞魔飞智能麦克风</span
              >
              <span v-else
                >{{ platform[scope.row.platform]
                }}{{
                  platform[scope.row.platform] === 'WeChat'
                    ? '（微信公众号）'
                    : ''
                }}</span
              >
            </template>
          </el-table-column> -->
            <el-table-column prop="createTime" label="创建时间" width="150">
              <template slot-scope="scope">
                {{ scope.row.createTime | date('yyyy-MM-dd') }}
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="150">
              <template slot-scope="scope">
                <span
                  class="mgr8"
                  v-if="
                    scope.row.platform == 10 ||
                    scope.row.platform == 11 ||
                    scope.row.platform == 13
                  "
                  >-</span
                >
                <div v-else class="status-wrap">
                  <div
                    class="ib app-status"
                    :class="'app-status-' + scope.row.status"
                    v-if="scope.row.status != 5"
                  />
                  <span
                    class="mgr8"
                    :class="'app-status-text-' + scope.row.status"
                    >{{ scope.row.status | appStatus }}</span
                  >
                  <router-link
                    v-if="scope.row.status === 3"
                    :to="{
                      name: 'app-audit',
                      params: { appId: scope.row.appid },
                    }"
                    >详情</router-link
                  >
                </div>
              </template>
            </el-table-column>
          </os-table>
        </template>
        <div class="create-guide" v-else>
          <div class="icon"></div>
          <p class="title">
            你还没有创建任何应用，
            <a v-if="createAuth" @click="createApp"> 点击创建 </a>
          </p>
          <p class="desc">
            AIUI应用针对硬件产品提供云端配置、测试开发、上线。全链路配置包括语音识别、语义理解、后处理、语音合成等。
          </p>
          <a :href="`${$config.docs}doc-4/`" target="_blank">了解更多</a>
        </div>
      </div>
    </div>
    <add-dialog :dialog="dialog" @refresh="getSearchResult" />
  </div>
</template>

<script>
import dicts from '@M/dicts'
import { mapGetters } from 'vuex'
import AddDialog from './add.vue'

export default {
  name: 'apps',
  data() {
    return {
      search: '',
      tableData: {
        loading: true,
        total: 0,
        page: 1,
        size: 10,
        handles: ['edit', 'del'],
        handleColumnText: '操作',
        list: [],
      },
      platform: dicts.aiuiAppPlatform,
      noApp: false,
      createAuth: false,
      dialog: {
        show: false,
      },
    }
  },
  computed: {
    ...mapGetters({
      subAccountInfo: 'user/subAccountInfo',
      subAccount: 'user/subAccount',
    }),
  },
  created() {
    this.getAppList()
    this.getMyAuthority()
    this.$store.dispatch('aiuiApp/setAppInfo', {})
    if (this.subAccount) {
      this.init()
    }
  },
  methods: {
    getAppList(page) {
      this.tableData.loading = true
      this.tableData.page = page
      let data = {
        pageSize: this.tableData.size,
        pageIndex: this.tableData.page,
        search: this.search.trim(),
      }
      let self = this
      self.$utils.httpGet(self.$config.api.AIUI_APP_LIST, data, {
        success: (res) => {
          self.tableData.loading = false
          self.tableData.list.splice(0)
          self.tableData.total = 0
          if (
            (!res.data && !self.search) ||
            (res.data && !(res.data instanceof Object))
          ) {
            self.noApp = true
          } else {
            self.noApp = false
            if (!res.data) return
            self.tableData.list = res.data.result ? res.data.result : []
            self.tableData.total = res.data.count ? res.data.count : 0
            self.storeAppList(self.tableData.list)
          }
        },
        error: (err) => {
          self.tableData.loading = false
        },
      })
    },
    getSearchResult() {
      this.getAppList(1)
    },
    toEdit(data) {
      if (this.subAccount) {
        this.$router.push({ name: 'sub-app', params: { appId: data.appid } })
      } else {
        this.$router.push({ name: 'app', params: { appId: data.appid } })
      }
      // if (data.platform == 'all') {
      //   this.$router.push({ name: 'app-info', params: { appId: data.appid } })
      //   return
      // }
    },
    toDeleteApp(data) {
      this.$confirm_pro_warning(
        '删除应用程序后，该应用程序将无法使用AIUI服务，且无法恢复。你确认要删除该应用吗？',
        '提示',
        {
          confirmButtonText: '删除',
          cancelButtonText: '取消',
          type: 'warning',
          dangerouslyUseHTMLString: true,
        }
      )
        .then(() => {
          this.$utils.httpPost(
            this.$config.api.AIUI_APP_DELETE,
            { appid: data.appid },
            {
              success: (res) => {
                this.getAppList()
              },
              error: (err) => {},
            }
          )
        })
        .catch(() => {})
    },
    storeAppList(list = []) {
      if (!list.length) return
      let prefix = this.subAccount ? 'sub_app_' : 'app_'
      list.forEach((item) => {
        if (!localStorage.hasOwnProperty(`${prefix}${item.appid}`)) {
          localStorage.setItem(`${prefix}${item.appid}`, 1)
        }
      })
    },
    //协同操作
    getMyAuthority() {
      if (!this.$route.path.match('sub/apps')) {
        this.createAuth = true
        return
      }
      let self = this
      this.$utils.httpGet(
        this.$config.api.SUB_USER_SKILL_AUTH,
        {},
        {
          success: (res) => {
            self.createAuth = res.data.createapp
          },
          error: (err) => {},
        }
      )
    },
    init() {
      // 去除子账号地址栏中的sub_account_id/subSessionId
      window.history.replaceState(
        {},
        '',
        SSO.utils.setQueryString({
          sub_account_id: '',
          subSessionId: '',
        })
      )
    },
    createApp() {
      // this.$router.push({name: this.subAccount ? 'sub-apps-add' : 'apps-add'})
      this.dialog.show = true
    },
  },
  components: {
    AddDialog,
  },
}
</script>

<style lang="scss" scoped>
.os-scroll {
  padding: 0 29px 0 31px;
}
.top-wrapper {
  font-size: 20px;
  font-weight: 600;
  line-height: 74px;
  height: 74px;
  color: #000000;
}

// .app-list-wrap {
//   min-height: calc(100% - 74px);
// }
.app-list__bottom-content {
  position: relative;
  margin: auto;
  min-height: 400px;
  border-radius: 20px;
}
.top-opr-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-direction: row-reverse;
  margin-bottom: 20px;
  .search-input {
    position: relative;
    width: 280px;
  }
  .create-app-btn {
    &-icon {
      font-size: 18px;
      margin-right: 6px;
    }
  }
}
.app-status {
  width: 8px;
  height: 8px;
  border: 2px solid;
  border-radius: 8px;
  margin-right: 6px;
  &-0 {
    border-color: $grey5;
  }
  &-1 {
    border-color: $primary;
  }
  &-2 {
    border-color: $success;
  }
  &-3 {
    border-color: $dangerous;
  }
  &-4 {
    border-color: $grey5;
  }
}
.app-status-text {
  &-0 {
    color: $grey5;
  }
  &-1 {
    color: $primary;
  }
  &-2 {
    color: $success;
  }
  &-3 {
    color: $dangerous;
  }
  &-4 {
    color: $grey5;
  }
}
//新手引导
.create-guide {
  margin-top: 120px;
  text-align: center;
  font-size: 16px;
  color: $grey5;
}
.icon {
  margin: 160px auto 24px;
  width: 120px;
  height: 120px;
  background: url(../../../assets/images/app/create-app.png) center no-repeat;
  background-size: 100%;
}
.title {
  font-size: 16px;
  font-weight: 600;
  a {
    font-weight: 600;
  }
}
.desc {
  margin: 24px auto;
  width: 480px;
  font-size: 14px;
  line-height: 22px;
}

.status-wrap {
  display: flex;
  align-items: center;
}
</style>
<style lang="scss">
// @import '@A/scss/gutterTable.scss';
.app-list__bottom-content {
  // .el-pagination {
  //   margin-top: 24px;
  //   text-align: center;
  // }
  .el-table td {
    padding: 0;
  }
  .el-table tr {
    cursor: pointer;
  }
}
.app-list-table {
  p {
    margin-bottom: 0;
  }
  .os-skill-simple-item {
    // width: 195px;
    max-width: 100%;
    height: 100%;
  }
  .el-table--enable-row-hover {
    .el-table__body {
      tr {
        &:hover {
          // .os-skill-simple-item {
          //   &-name {
          //     color: #1f90fe;
          //   }
          // }
        }
      }
    }
  }
}
</style>
