<template>
  <el-dialog title="审核上线" :visible.sync="dialog.show" width="760px">
    <div class="app-audit-page" v-loading="loading">
      <el-alert v-if="isCertificated === false" class="el-alert" type="warning">
        <p slot="title">
          {{ subAccount ? '主账号' : '' }}账户尚未进行实名认证。<a
            v-if="!subAccount"
            class="auth-link"
            :href="`${$config.xfyunConsole}user/realnameauth`"
            target="_blank"
            >立即实名认证</a
          >
        </p>
      </el-alert>

      <el-alert v-if="form.checkStatus === 1" class="el-alert" type="info">
        你已提交上线申请，审核工作会在两个工作日内完成。
      </el-alert>

      <el-alert v-if="form.checkStatus === 2" class="el-alert" type="success">
        你的应用已于
        {{ form.checkTime | date('yyyy-MM-dd hh:mm:ss') }} 审核通过。
      </el-alert>

      <el-alert v-if="form.checkStatus === 3" class="el-alert" type="error">
        应用由于 {{ form.checkOpinion }} 审核不通过
        <a
          class="auth-link"
          href="javascript:void(0);"
          @click="reAudit"
          target="_blank"
          >重新审核</a
        >
      </el-alert>

      <p class="apply-tips" v-if="form.checkStatus === 2">
        应用已经审核通过，如免费日调用量仍不满足业务需要，请<a
          href="https://console.xfyun.cn/workorder/commit"
          target="_blank"
          >联系商务</a
        >为您处理。
      </p>
      <p class="apply-tips" v-else>
        应用审核通过后可发布至线上环境，这样您可以在测试环境体验技能编辑和应用配置的最终效果，不影响线上的用户体验
      </p>

      <el-form
        v-if="
          isCertificated === true &&
          (form.checkStatus === 0 || form.checkStatus === 4)
        "
        :model="form"
        :rules="rules"
        :disabled="!subAccountEditable"
        ref="form"
        label-width="120px"
        label-position="left"
        class=""
      >
        <el-form-item label="产品形态" required>
          <el-radio-group
            class="app-type"
            v-model="form.appType"
            @change="appTypeChange"
          >
            <el-radio :label="1">软件</el-radio>
            <el-radio :label="2">硬件</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="产品名称" prop="name">
          <el-input
            v-model.trim="form.name"
            placeholder="应用的正式名称，长度为1-30个字符"
            size="small"
          ></el-input>
        </el-form-item>
        <el-form-item label="产品介绍" prop="introduction">
          <el-input
            v-model="form.introduction"
            type="textarea"
            placeholder="请描述应用使用场景、面向人群、主要功能、语音语义使用情况等信息"
            :autosize="{ minRows: 3, maxRows: 8 }"
          ></el-input>
        </el-form-item>
        <el-form-item label="产品截图/视频" prop="appVideo">
          <el-upload
            class="audit-app-upload"
            ref="appVideoUpload"
            :action="`${this.$config.server}/aiui/web/app/auditing/upload?appid=${appId}&type=1`"
            :file-list="appVideoList"
            :on-change="handleAppVideoChange"
            :before-upload="beforeUpload"
            :on-remove="handleAppVideoRemove"
            :on-success="handleAppVideoUrl"
          >
            <el-button size="small" :plain="true">上传</el-button>
            <div slot="tip" class="el-upload__tip">
              200M以内的zip包，文件过大时请在备注中附加网盘下载链接及提取码
            </div>
          </el-upload>
          <el-input v-show="false" v-model="form.appDocument"></el-input>
        </el-form-item>
        <div v-if="form.appType === 1" key="testAddress">
          <el-form-item label="测试地址" prop="testAddress">
            <el-input
              v-model="form.testAddress"
              placeholder="请输入应用安装包下载地址或网站测试地址"
              size="small"
            ></el-input>
          </el-form-item>
        </div>
        <div v-if="form.appType === 2" key="hardwareInfo">
          <el-form-item label="硬件信息" prop="hardwareInfo">
            <el-input
              v-model="form.hardwareInfo"
              type="textarea"
              placeholder="请描述产品硬件配置，如使用了麦克风阵列，请说明麦克风阵列型号"
              :autosize="{ minRows: 3, maxRows: 8 }"
            ></el-input>
          </el-form-item>
        </div>
        <el-form-item label="备注" prop="appNote">
          <el-input
            v-model="form.appNote"
            type="textarea"
            placeholder="可填写为审核人员准备的测试账号等信息"
            :autosize="{ minRows: 3, maxRows: 8 }"
          ></el-input>
        </el-form-item>
        <el-form-item label="姓名" prop="linkman">
          <el-input
            v-model.trim="form.linkman"
            placeholder="联系人姓名"
            size="small"
          ></el-input>
        </el-form-item>
        <el-form-item label="手机" prop="telephone">
          <!-- <el-input
            v-model.trim="form.telephone"
            placeholder="多个手机号使用英文逗号隔开"
            size="small"
          ></el-input> -->
          <el-tag
            class="keyword"
            :key="index"
            v-for="(keyword, index) in telephoneArr"
            closable
            :disable-transitions="false"
            @close="handleTelephoneClose(index)"
            >{{ keyword }}</el-tag
          >
          <el-input
            size="medium"
            class="new-keyword-input"
            v-if="telephoneAddVisible"
            v-model.trim="newTelephone"
            ref="saveTagInput"
            placeholder="回车添加"
            @blur="handleNewTelephoneConfirm"
            @keyup.enter.native="handleNewTelephoneConfirm"
          ></el-input>
          <div
            v-if="!telephoneAddVisible && telephoneArr.length <= 4"
            class="button-new-keyword ib"
            @click="showNewTelephoneConfirm"
          >
            +
          </div>
          <div class="mobile-tips">
            手机号用于人工审核及授权不足时短信提醒使用。
          </div>
        </el-form-item>
      </el-form>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button
        type="primary"
        @click="submitForm('form')"
        v-if="form.checkStatus === 0"
        >提交申请</el-button
      >
      <el-button type="primary" @click="dialog.show = false" v-else
        >确定</el-button
      >
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: 'app-audit',
  props: {
    subAccount: Boolean,
    subAccountEditable: Boolean,
    dialog: {
      type: Object,
      default: () => {
        return {
          show: false,
        }
      },
    },
  },
  data() {
    return {
      pageOptions: {
        title: '审核上线',
        loading: false,
        returnBtn: false,
      },
      telephoneArr: [], // 手机号数组
      telephoneAddVisible: false, // 手机号添加是否展示加号
      newTelephone: '',
      isCertificated: null, //是否实名认证
      form: {
        appType: 1,
        name: '',
        introduction: '',
        linkman: '',
        telephone: '',
        appVideo: '', // 产品截图&视频
        testAddress: '', // 测试地址
        appPackage: '', // 应用安装包
        hardwareInfo: '', // 硬件信息
        appNote: '',
        checkTime: '',
        checkStatus: '', // 审核状态，0 为未提交审核，1 审核中，2 通过，3 不通过
        checkOpinion: '', // 审核失败原因
      },
      rules: {
        name: [
          {
            required: true,
            message: '请填写产品名称',
            trigger: ['change', 'blur'],
          },
          { max: 30, message: '产品名称长度为1-30个字符' },
          {
            pattern: /^[a-zA-Z0-9.\-_\u4e00-\u9fa5]+$/,
            message: '产品名称仅支持汉字/字母/数字/下划线',
            trigger: ['change', 'blur'],
          },
        ],
        introduction: [
          {
            required: true,
            message: '请填写产品介绍',
            trigger: ['change', 'blur'],
          },
          { max: 1000, message: '产品介绍不能超过1000字符' },
        ],
        linkman: [
          {
            required: true,
            message: '请填写联系人姓名',
            trigger: ['change', 'blur'],
          },
        ],
        telephone: [
          {
            required: true,
            message: '请填写手机号',
            trigger: ['change', 'blur'],
          },
        ],
        hardwareInfo: [
          {
            required: true,
            message: '请填写产品硬件信息',
            trigger: ['change', 'blur'],
          },
          { max: 1000, message: '硬件信息不能超过1000字符' },
        ],
        appNote: [{ max: 1000, message: '备注不能超过1000字符' }],
      },
      appVideoList: [],
      apkFileList: [],

      loading: false,
    }
  },
  methods: {
    // 手机号删除
    handleTelephoneClose(index) {
      this.telephoneArr.splice(index, 1)
      this.form.telephone = this.telephoneArr.join(',')
    },
    // 手机号添加完成
    handleNewTelephoneConfirm() {
      if (!this.newTelephone) {
        this.telephoneAddVisible = false
        this.newTelephone = ''
        return
      }
      const regExp = new RegExp('^1[3456789]\\d{9}$')
      if (!regExp.test(this.newTelephone)) {
        this.$message.warning('请输入正确格式的手机号')
        return
      }
      if (this.telephoneArr.indexOf(this.newTelephone) !== -1) {
        this.$message.warning('手机号不能重复添加')
        return
      }
      this.telephoneArr.push(this.newTelephone)
      this.form.telephone = this.telephoneArr.join(',')
      this.telephoneAddVisible = false
      this.newTelephone = ''
      console.log(this.form.telephone)
    },
    // 手机号添加开始
    showNewTelephoneConfirm() {
      this.telephoneAddVisible = true
      this.$nextTick((_) => {
        this.$refs.saveTagInput.$refs.input.focus()
      })
    },
    getCertificated() {
      let self = this
      this.$utils.httpGet(
        this.$config.api.USER_CERTIFICATED,
        {},
        {
          success: (res) => {
            if (res.flag) {
              this.isCertificated = res.data.isCertificated
            } else {
              self.$message.error(res.desc)
            }
          },
        }
      )
    },
    /**
     * 获取审核状态
     * 1. 返回值data 为空或undefined，未提交过审核
     * 2. data.checkStatus 为1 审核中，2 通过，3 不通过
     */
    getAuditInfo() {
      let self = this
      this.loading = true
      this.$utils.httpGet(
        this.$config.api.AIUI_APP_AUDIT_GET,
        {
          appid: this.appId,
        },
        {
          success: (res) => {
            if (res.flag) {
              this.loading = false
              if (res.data) {
                self.form = res.data
                if (self.form.telephone) {
                  self.telephoneArr = self.form.telephone.split(',')
                } else {
                  self.telephoneArr = []
                }
              } else {
                self.form.checkStatus = 0
              }
            }
          },
        }
      )
    },
    appTypeChange(value) {
      this.appVideoList = []
      this.apkFileList = []
      this.$refs.form.resetFields()
    },
    handleAppVideoChange(file, fileList) {
      const isZIP = file.type === 'application/zip'
      const zipEXT = /.zip$/.test(file.name)
      const isLt200M = file.size / 1024 / 1024 < 200
      if (!(isZIP || zipEXT)) {
        this.$message({
          message: '上传文件仅支持zip格式',
          type: 'warning',
        })
      }
      if (!isLt200M) {
        this.$message({
          message: '上传文件不能超过200M',
          type: 'warning',
        })
      }
      if ((isZIP || zipEXT) && isLt200M) {
        this.appVideoList = fileList.slice(-1)
      }
    },
    handleApkChange(file, fileList) {
      const isZIP = file.type === 'application/zip'
      const zipEXT = /.zip$/.test(file.name)
      const isLt200M = file.size / 1024 / 1024 < 200
      if (!(isZIP || zipEXT)) {
        this.$message({
          message: '上传文件仅支持zip格式',
          type: 'warning',
        })
      }
      if (!isLt200M) {
        this.$message({
          message: '上传文件不能超过200M',
          type: 'warning',
        })
      }
      if ((isZIP || zipEXT) && isLt200M) {
        this.apkFileList = fileList.slice(-1)
      }
    },
    // 判断上传文件类型和大小
    beforeUpload(file) {
      const isZIP = file.type === 'application/zip'
      const zipEXT = /.zip$/.test(file.name)
      const isLt200M = file.size / 1024 / 1024 < 200
      if (!(isZIP || zipEXT)) {
        this.$message({
          message: '上传文件仅支持zip格式',
          type: 'warning',
        })
      }
      if (!isLt200M) {
        if (!isLt200M) {
          this.$message({
            message: '上传文件不能超过200M',
            type: 'warning',
          })
        }
      }
      return (isZIP || zipEXT) && isLt200M
    },
    handleAppVideoRemove(file) {
      if (file) {
        this.form.appVideo = ''
        this.$refs.form.validateField('appVideo')
      }
    },
    handleAppVideoUrl(data) {
      if (data.flag) {
        this.form.appVideo = data.data
        this.$refs.form.validateField('appVideo')
      } else {
        this.$message.error(data.desc)
      }
    },
    handleAPKRemove(file) {
      if (file) {
        this.form.appPackage = ''
        this.$refs.form.validateField('appPackage')
      }
    },
    handleAPKUrl(data) {
      if (data.flag) {
        this.form.appPackage = data.data
        this.$refs.form.validateField('appPackage')
      } else {
        this.$message.warning(data.desc)
      }
    },
    submitForm(formName) {
      let self = this
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.$utils.httpPost(
            this.$config.api.AIUI_APP_AUDIT_SAVE,
            {
              appid: self.appId,
              appType: self.form.appType,
              name: self.form.name,
              introduction: self.form.introduction,
              linkman: self.form.linkman,
              telephone: self.form.telephone,
              appVideo: self.form.appVideo,
              testAddress: self.form.testAddress,
              hardwareInfo: self.form.hardwareInfo,
              appNote: self.form.appNote,
            },
            {
              success: (res) => {
                if (res.flag) {
                  self.form.checkStatus = 1
                  self.$message.success('提交成功')
                  self.resetSubAccountStorage()
                } else {
                  self.$message.error(res.desc)
                }
              },
            }
          )
        } else {
          // 手机号问题兜底
          if (!this.form.telephone && this.telephoneArr.length > 0) {
            this.form.telephone = this.telephoneArr.join(',')
          }
          return false
        }
      })
    },
    reAudit() {
      this.form.checkStatus = 0
    },
    resetSubAccountStorage() {
      if (!this.subAccountEditable) return
      let prefix = 'sub_app_'
      let item = prefix + this.appId
      localStorage.setItem(item, 3) //子账号提交审核
    },
  },
  computed: {
    appId() {
      return this.$route.params.appId
    },
  },
  watch: {
    'dialog.show'(val) {
      if (val) {
        this.getCertificated()
        this.getAuditInfo()
      }
    },
  },
  // created() {
  //   this.getCertificated()
  //   this.getAuditInfo()
  // },
}
</script>

<style lang="scss" scoped>
.app-type {
  padding-top: 15px;
}
.auth-link {
  color: $warning;
  text-decoration: underline;
}
.apply-tips {
  margin-top: 10px;
  color: $grey6;
  span {
    color: $primary;
  }
}
.mobile-tips {
  color: #ff9b00;
  font-size: 12px;
  height: 22px;
  line-height: 24px;
}
.keyword {
  margin: 0 8px 8px 0;
}
.new-keyword-input {
  width: 200px;
}
.button-new-keyword {
  margin-top: 3px;
  width: 36px;
  height: 36px;
  font-size: 24px;
  line-height: 30px;
  cursor: pointer;
  color: $primary;
  text-align: center;
  vertical-align: top;
  border: 1px solid $grey3;
  border-radius: 2px;
}

.app-audit-page {
  padding-bottom: 15px;
}
</style>
<style>
.audit-app-upload .el-upload {
  text-align: left;
}
</style>
