<template>
  <el-dialog
    :title="`创建${nameList[dialog.type]}`"
    :visible.sync="dialog.show"
    width="480px"
  >
    <el-form :model="form" :rules="rules" ref="qaForm" label-position="top">
      <div v-if="dialog.type != 2">
        <!-- <el-form-item label="问答类型" style="margin-bottom: 0" prop="type">
          <el-radio-group v-model="type" @change="clearValidateForm">
            <el-radio-button :label="0">语句问答</el-radio-button>
            <el-radio-button :label="3">关键词问答</el-radio-button>
            <el-radio-button :label="5">文档问答</el-radio-button>
          </el-radio-group>
        </el-form-item> -->
        <p class="qa-tip" :class="{ 'qa-tip-hidden': type != 0 }">
          整句话模糊匹配来回复用户
        </p>
        <p class="qa-tip" :class="{ 'qa-tip-hidden': type != 3 }">
          匹配命中的关键字回复用户
        </p>
        <p class="qa-tip" :class="{ 'qa-tip-hidden': type != 5 }">
          大模型知识库，可根据指定文档内容自由交互
        </p>
      </div>
      <el-form-item :label="`${nameList[dialog.type]}名称`" prop="name">
        <el-input
          v-model.trim="form.name"
          :placeholder="`支持中文/英文/数字/下划线格式，不超过32个字符`"
          ref="nameInput"
          @keyup.enter.native="save"
        />
        <input type="text" style="display: none" />
      </el-form-item>
      <el-form-item label="语种类型" v-if="type == 0 && hasLanguageAuth">
        <el-cascader
          v-model="languageAccent"
          :options="options"
          :props="{ expandTrigger: 'hover' }"
          @change="handleChange"
        ></el-cascader>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button
        class="dialog-btn"
        type="primary"
        style="min-width: 104px"
        @click="save"
        :loading="saving"
      >
        {{ saving ? '创建中...' : '创建' }}
      </el-button>
    </span>
  </el-dialog>
</template>

<script>
import dicts from '@M/dicts'
import IconAdd from '@A/images/plugin/add.png'
import { mapGetters } from 'vuex'
export default {
  props: {
    dialog: {
      type: Object,
      default: {},
    },
  },
  data() {
    return {
      IconAdd,
      nameList: {
        0: '语句问答库',
        3: '关键词问答库',
      },
      requiredTip: '名称不能为空',
      saving: false,
      form: {
        name: '',
        type: 0,
        repoId: '',
      },
      type: 0,
      rules: {
        name: [
          this.$rules.required('名称不能为空', 'none'),
          this.$rules.lengthLimit(1, 32, '名称长度不能超过32个字符'),
          this.$rules.baseRegLimit(),
        ],
        repoId: [
          { required: true, message: '请选择知识库类型', trigger: 'change' },
        ],
        addName: [
          {
            required: true,
            message: '请输入新增知识库名称',
            trigger: 'blur',
          },
          {
            max: 40,
            message: '内容长度不超过40个字符',
            trigger: 'change',
          },
          {
            validator: (rule, value, callback) => {
              const regExp = /^[\u4e00-\u9fa5a-zA-Z]+$/ // 正则表达式匹配中英文
              if (!regExp.test(value)) {
                callback(new Error('知识库名称只能包含中英文'))
              } else {
                callback()
              }
            },
            trigger: 'change',
          },
        ],
      },
      languageAccent: [],
      options: [],
    }
  },
  watch: {
    'dialog.show': function (val, oldVal) {
      console.log('-----------------dialog.show-------------------', val)
      let self = this
      // self.type = self.dialog.type

      if (val) {
        ;(this.form = {
          name: '',
        }),
          this.$refs.qaForm && this.$refs.qaForm.resetFields()
        self.hasLanguageAuth && self.getLanguageAccent()
        this.$nextTick(function () {
          console.log('dialog.show---', self.dialog.type)
          self.type = self.dialog.type
          self.$refs.nameInput && self.$refs.nameInput.focus()
        })
      } else {
      }
    },
    hasLanguageAuth(val) {
      if (val && this.dialog.show) {
        this.getLanguageAccent()
      }
    },
  },
  created() {
    this.form.type = 0
    if (this.dialog.type) {
      this.type = this.dialog.type
    }
  },
  mounted() {},
  methods: {
    getLanguageAccent() {
      this.$utils.httpGet(
        this.$config.api.STUDIO_QA_LANGUAGE_OPT,
        {},
        {
          success: (res) => {
            this.getOptionByData(res.data.list)
          },
          error: (err) => {},
        }
      )
    },

    getOptionByData(origin = []) {
      // 将中文拿出来放在前面, 只拿一个就OK
      let index = origin.findIndex((item) => item.language === 'zh_cn')
      let unshiftItem
      if (index !== -1) {
        unshiftItem = origin[index]
        origin.splice(index, 1)
      }
      if (unshiftItem) {
        origin.unshift(unshiftItem)
      }

      let newOption = []
      origin.forEach((item) => {
        const filterList = origin.filter((it) => item.language === it.language)
        const len = filterList.length
        if (len === 1) {
          newOption.push({
            value: `${item.language},${item.accent}`,
            label: item.languageName,
            // children: [{ value: item.accent, label: item.accentName }],
          })
        } else if (len > 1) {
          // 先看数组中有没有同名language，没有才添加
          const index = newOption.findIndex((it) => it.value === item.language)
          if (index === -1) {
            newOption.push({
              value: item.language,
              label: item.languageName,
              children: filterList.map((im) => {
                return {
                  value: im.accent,
                  label: im.accentName,
                }
              }),
            })
          }
        }
      })
      this.options = newOption
      if (newOption.length > 0) {
        let option = newOption[0]
        if (option.children && option.children.length > 0) {
          this.languageAccent = [option.value, option.children[0].value]
        } else {
          this.languageAccent = [option.value]
        }
      }
    },

    clearValidateForm(e) {
      this.form.type = this.type
      this.$refs.qaForm && this.$refs.qaForm.clearValidate()
    },
    save() {
      let self = this
      if (this.saving) {
        return
      }
      this.$refs.qaForm.validate((valid) => {
        if (valid) {
          this.saving = true
          console.log(this.form)
          let data = {
            name: this.form.name,
            type: this.type,
          }
          if (self.type == 0 && self.hasLanguageAuth) {
            let language, accent
            if (self.languageAccent.length > 1) {
              language = self.languageAccent[0]
              accent = self.languageAccent[1]
            } else if (self.languageAccent.length === 1) {
              language = self.languageAccent[0].split(',')[0]
              accent = self.languageAccent[0].split(',')[1]
            }
            data = {
              ...data,
              language,
              accent,
            }
          }
          let api = this.$config.api.STUDIO_QA_CREATE_EDIT
          this.$utils.httpPost(api, data, {
            success: (res) => {
              this.saving = false
              self.$message.success('创建成功')
              if (self.type == 0) {
                self.$router.push({
                  name: 'qaBank',
                  params: { qaId: res.data.repositoryId },
                })
              } else if (self.type == 3) {
                self.$router.push({
                  name: 'keyQABank',
                  params: {
                    repoId: res.data.repositoryId,
                    qaId: res.data.qaId,
                  },
                })
              } else if (self.type == 5) {
                self.$router.push({
                  name: 'qaBank-knowledge',
                  params: {
                    repoId: res.data.repositoryId,
                    name: this.form.name,
                  },
                })
              } else {
                self.$router.push({
                  name: 'qa-relations',
                  params: { repoId: res.data.repositoryId },
                })
              }
            },
            error: (err) => {
              this.saving = false
              console.log('page=>>')
              console.log(err)
            },
          })
        }
      })
    },
    handleChange(value) {
      console.log('handleChange', this.languageAccent)
    },
  },
  computed: {
    ...mapGetters({
      limitCount: 'aiuiApp/limitCount',
    }),
    hasLanguageAuth() {
      return (
        typeof this.limitCount.qa_language !== 'undefined' &&
        Number(this.limitCount.qa_language) > 0
      )
    },
  },
  components: {},
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="scss" scoped>
.qa-tip {
  // margin-top: 20px;
  margin-bottom: 20px;
  color: $warning;

  &-hidden {
    visibility: hidden;
    display: none;
  }
}

.kl-add {
  width: 24px;
  height: 24px;
  margin-left: 12px;
  cursor: pointer;
}
.base {
  :deep(.el-form-item__content) {
    display: flex;
    align-items: center;
  }
}
</style>
