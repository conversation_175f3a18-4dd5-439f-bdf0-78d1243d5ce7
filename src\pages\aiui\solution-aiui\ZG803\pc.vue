<template>
  <div class="main-content">
    <section class="main-content-banner">
      <div class="banner-text">
        <h2
          v-html="'ZG803 AIUI离线语音识别方案'"
          style="line-height: 60px"
        ></h2>
        <p class="pc-show banner-text-content">
          集成多语种离线语音算法的高性能，低成本，高集成度的智能语音识别方案，<br />适用于国内外小家电场景。
        </p>
        <div class="hor-btn">
          <div class="banner-text-button" @click="toConsole">合作咨询</div>
        </div>
      </div>
    </section>
    <section class="section section-2">
      <div class="section-title">应用场景</div>
      <div class="section-item">
        <ul>
          <li v-for="(item, index) in app_scenario" :key="index" class="app">
            <img :src="item.src" :alt="item.alt" />
            <p class="app-text">{{ item.alt }}</p>
          </li>
        </ul>
      </div>
    </section>
    <section class="section section-3">
      <div class="section-title">产品功能</div>
      <!-- <div class="section-item">
        <ul>
          <li v-for="(item, index) in use_list" :key="index">
            <dt>
              <img :src="item.src">
              <div>{{ item.name }}</div>
            </dt>
            <div class="line"></div>
            <dd>
              <div class="content" v-for="(it, i) in item.content" :key="i">
                <span class="round"></span>
                <div>{{ it }}</div>
              </div>
            </dd>
          </li>
        </ul>
      </div> -->
    </section>
    <section class="section section-4">
      <div class="section-title">产品视频</div>
      <div class="section-item">
        <div>
          <ul class="left">
            <li
              v-for="(item, index) in video_list"
              :key="index"
              @click="onVideoClick(index)"
              :class="{ 'video-item': active == 1 }"
            >
              <img :src="item.src" />
              <div class="line" v-show="item.name.length > 0"></div>
              <span>{{ item.name }}</span>
            </li>
          </ul>
          <div class="right">
            <video
              :src="videoSrc"
              :width="760"
              controls
              autoplay
              v-if="isPlaying"
            ></video>
            <div
              v-else
              class="video-cover"
              :style="{ backgroundImage: 'url(' + videoCover + ')' }"
              @click="isPlaying = true"
            >
              <div class="play-icon"></div>
            </div>
          </div>
        </div>
      </div>
    </section>
    <section class="section section-5">
      <div class="section-title">使用说明</div>
      <div class="section-item">
        <div class="content">
          <div class="title">免唤醒交互</div>
          <div class="main">
            本地离线交互方式支持免唤醒交互，即在设备未唤醒情况，直接说出离线识别命令词就可以与模组进行交互。
          </div>
          <img
            :src="
              require('../../../../assets/images/solution/soft-hardware/803/sm1.png')
            "
          />
        </div>
        <div class="content">
          <div class="title">唤醒词交互</div>
          <div class="main">
            支持唤醒词+离线命令词的方式交互，即先说主唤醒词将设备唤醒，然后可以进行语音交互（默认唤醒词小飞小飞）。
          </div>
          <img
            :src="
              require('../../../../assets/images/solution/soft-hardware/803/sm2.png')
            "
          />
        </div>
      </div>
    </section>
    <section class="section section-7">
      <div class="section-title">硬件参数</div>
      <div class="section-item">
        <ul>
          <li v-for="(item, index) in hard_list" :key="index" class="item">
            <div class="left">
              <img :src="item.src" />
            </div>
            <div class="right">
              <div class="title">{{ item.name }}</div>
              <span>{{ item.content }}</span>
            </div>
          </li>
        </ul>
      </div>
    </section>
    <section class="section section-8">
      <div class="section-title">产品清单</div>
      <div class="section-item">
        <div v-for="item in product_list" :key="item.index" class="item">
          <div class="title">{{ item.title }}</div>
          <p v-html="item.sub_title"></p>
        </div>
      </div>
    </section>
    <section class="section section-9">
      <div class="section-title">开发材料</div>
      <div class="section-item">
        <div class="item">
          <a
            v-for="item in develop_doc"
            :key="item.index"
            @click="toDoc(item.link)"
            style="color: #666666"
            >{{ item.name }}</a
          >
        </div>
      </div>
    </section>
    <!-- <div class="corp">
      <div class="corp-section-wrap">
        <div class="corp-section-title">
          <p class="corp-section-title-contact">立即联系您的专属顾问</p>
          <p class="corp-section-desc2">免费咨询专属顾问 为您量身定制产品推荐方案</p>
        </div>

        <div class="corp-section-item" style="padding-top: 39px; text-align: left">
          <div class="corp-section-button" @click="toConsole">合作咨询</div>
        </div>
      </div>
    </div> -->
  </div>
</template>

<script>
import corp from '@P/aiui/solution-aiui/components/corp.vue'
import VideoPlayer from '../../../../components/videoPlayer'
import UseVideo from '../../../../assets/images/solution/soft-hardware/803/use.mp4'
import PublicizeVideo from '../../../../assets/images/solution/soft-hardware/803/publicize.mp4'

export default {
  layout: 'aiuiHome',
  data() {
    return {
      showPlayer: false,
      app_scenario: [
        {
          alt: '台灯',
          src: require('../../../../assets/images/solution/soft-hardware/803/td.png'),
        },
        {
          alt: '床头灯',
          src: require('../../../../assets/images/solution/soft-hardware/803/ctd.png'),
        },
        {
          alt: '小夜灯',
          src: require('../../../../assets/images/solution/soft-hardware/803/xyd.png'),
        },
        {
          alt: '镜前灯',
          src: require('../../../../assets/images/solution/soft-hardware/803/jqd.png'),
        },
        {
          alt: '挂脖风扇',
          src: require('../../../../assets/images/solution/soft-hardware/803/gbfs.png'),
        },
        {
          alt: '常温壶',
          src: require('../../../../assets/images/solution/soft-hardware/803/cwh.png'),
        },
        {
          alt: '按摩椅',
          src: require('../../../../assets/images/solution/soft-hardware/803/amy.png'),
        },
        {
          alt: '儿童玩具',
          src: require('../../../../assets/images/solution/soft-hardware/803/etwj.png'),
        },
      ],
      use_list: [
        {
          name: '多种发音人',
          content: ['支持标准男声、女声等不同音色'],
          src: require('../../../../assets/images/solution/soft-hardware/803/dzfyr.png'),
        },
        {
          name: '语音唤醒',
          content: ['95%以上唤醒率', '支持自定义唤醒词定制'],
          src: require('../../../../assets/images/solution/soft-hardware/803/yyhx.png'),
        },
        {
          name: '离线识别',
          content: [
            '支持80条本地指令离线识别',
            '支持自定义离线命令词',
            '支持英、韩、日语、越南、印尼、 西班牙等多语种识别',
          ],
          src: require('../../../../assets/images/solution/soft-hardware/803/lxsb.png'),
        },
        {
          name: '客制化',
          content: ['可提供唤醒词定制工具，快速便捷 实现定制'],
          src: require('../../../../assets/images/solution/soft-hardware/803/kzh.png'),
        },
        {
          name: '串口通信',
          content: ['支持串口下发指令'],
          src: require('../../../../assets/images/solution/soft-hardware/803/cktx.png'),
        },
      ],
      video_list: [
        {
          name: '产品宣传视频',
          src: require('../../../../assets/images/solution/soft-hardware/803/sp3.png'),
          videoCover: require('../../../../assets/images/solution/soft-hardware/803/1.png'),
        },
        {
          name: '产品使用视频',
          src: require('../../../../assets/images/solution/soft-hardware/803/sp4.png'),
          videoCover: require('../../../../assets/images/solution/soft-hardware/803/2.png'),
        },
      ],
      video_old_list: [
        {
          name: '',
          src: require('../../../../assets/images/solution/soft-hardware/803/sp1.png'),
        },
        {
          name: '',
          src: require('../../../../assets/images/solution/soft-hardware/803/sp2.png'),
        },
        {
          name: '',
          src: require('../../../../assets/images/solution/soft-hardware/803/sp3.png'),
        },
        {
          name: '',
          src: require('../../../../assets/images/solution/soft-hardware/803/sp4.png'),
        },
      ],
      product_list: [
        {
          title: '硬件',
          sub_title:
            ' • 整机开发套件<span style="display:inline-block;padding: 0 10px"></span> • USB连接线',
        },
        {
          title: '软件',
          sub_title:
            ' • 灯具离线唤醒资源<span style="display:inline-block;padding: 0 10px"></span> • 自定义唤醒词工具',
        },
      ],
      hard_list: [
        {
          name: 'CPU',
          src: require('../../../../assets/images/solution/soft-hardware/803/cpu.png'),
          content: '32-bit DSP 主频 160MHz',
        },
        {
          name: 'SDRAM',
          src: require('../../../../assets/images/solution/soft-hardware/803/sdram.png'),
          content: '128KB',
        },
        {
          name: 'Flash',
          src: require('../../../../assets/images/solution/soft-hardware/803/flash.png'),
          content: '512KB',
        },
      ],
      develop_doc: [
        {
          name: ' • 《ZG803AIUI离线语音方案产品白皮书.pdf》',
          link: 'https://aiui-doc.xf-yun.com/project-1/doc-172/',
        },
        // {
        //   name: ' • 《ZG803 AIUI离线语音识别开发套件串口协议.pdf》',
        //   link: 'https://aiui-doc.xf-yun.com/project-1/doc-172/',
        // },
      ],
      active: 0,
      videoSrc: PublicizeVideo,
      videoCover: require('../../../../assets/images/solution/soft-hardware/803/1.png'),
      isPlaying: false,
    }
  },
  methods: {
    toConsole() {
      // 判断是否有百度SEM营销参数，有的话，提交表单页需要带上
      const search = window.location.search
      if (search) {
        window.open(`/solution/apply/22${search}`)
      } else {
        window.open('/solution/apply/22')
      }
    },
    toBuy() {
      window.open('https://www.aifuwus.com/onstage/cmddetail?product_type=4436')
    },
    toDoc(link) {
      window.open(link)
    },
    onVideoClick(index) {
      if (index !== this.active) {
        this.isPlaying = false
      }
      this.active = index
      this.videoCover = this.video_list[index].videoCover

      if (index == 0) {
        this.video_list[0].src = this.video_old_list[2].src
        this.video_list[1].src = this.video_old_list[3].src
        this.videoSrc = PublicizeVideo
      } else if (index == 1) {
        this.video_list[0].src = this.video_old_list[0].src
        this.video_list[1].src = this.video_old_list[1].src
        this.videoSrc = UseVideo
      }
    },
  },
  components: {
    corp,
    VideoPlayer,
  },
}
</script>

<style lang="scss" scoped>
@import '../../../../assets/scss/screen-and-lamp.scss';

@media screen and (min-width: 751px) {
  .main-content {
    &-banner {
      background: url(~@A/images/solution/soft-hardware/803/banner_bg.png)
        center no-repeat;
      background-size: cover;
      height: 500px;
      overflow: hidden;
      width: 100%;

      .banner-text {
        max-width: 1200px;
        color: #000;
        height: 100%;
        margin: auto;

        .hor-btn {
          display: flex;
          flex-direction: row;
          justify-content: flex-start;

          div:nth-child(2) {
            margin-left: 30px;
          }
        }

        &-button {
          font-size: 16px;
          text-align: center;
          font-weight: 400;
          width: 180px;
          height: 50px;
          line-height: 50px;
          border-radius: 4px;
          color: #fff;
          cursor: pointer;
          transition: 0.6s;
          background: $primary;
          border: none;
        }

        h2 {
          color: #000;
          padding-top: 148px;
          margin-bottom: 25px;
          font-size: 44px;
          font-weight: 600;
          line-height: 44px;
          font-family: PingFang SC, PingFang SC-Semibold;
        }

        p {
          font-size: 18px;
          margin-bottom: 25px;
        }

        .banner-text-content {
          width: 570px;
          font-size: 16px;
          font-weight: 400;
          color: #181818;
          line-height: 30px;
          font-family: PingFang SC, PingFang SC-Regular;
        }
      }
    }
  }

  .section-title {
    font-size: 34px;
    font-weight: bold;
    color: #333;
    margin-top: 0 !important;
    margin-bottom: 30px !important;
  }

  .section-sub-title {
    font-size: 16px;
    font-weight: 400;
    text-align: center;
    color: #666666;
    margin-bottom: 60px !important;
  }

  .app-text {
    color: #666;
  }

  .corp {
    padding-top: 76px;
    height: 320px;
    background: url(~@A/images/solution/acoustics/corp.png) center/cover
      no-repeat;

    .corp-section-wrap {
      width: 1200px;
      margin: 0 auto;
    }

    .corp-section-title-contact {
      font-size: 36px !important;
      font-weight: 400;
      color: #000000 !important;
      text-align: left !important;
      margin-top: 0 !important;
    }

    .corp-section-desc2 {
      text-align: left;
      margin-top: 20px !important;
      font-size: 18px;
      line-height: 30px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #262626 !important;
      display: block;
    }

    .corp-section-button {
      font-size: 16px;
      text-align: center;
      line-height: 50px;
      border: none;
      color: #fff;
      cursor: pointer;
      width: 180px;
      height: 50px;
      background: $primary;
      border-radius: 4px;
    }
  }

  .section-2 {
    .section-item {
      margin-top: 50px !important;

      > ul {
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;

        li {
          width: 25% !important;
          padding: 10px;
          position: relative;

          img {
            width: 100%;
          }

          p {
            position: absolute;
            left: 60px;
            top: 50%;
            transform: translate(0, -50%);
            margin-top: 0 !important;
          }
        }
      }
    }
  }

  .section-3 {
    height: 640px;
    width: 100%;
    background: url('../../../../assets/images/solution/soft-hardware/803/function.png')
      center no-repeat !important;
    background-size: cover !important;
    position: relative;

    .section-title {
      position: absolute;
      left: 50%;
      top: 50px;
      transform: translate(-50%, 0);
    }
  }

  .section-4 {
    background: rgb(35, 76, 210) !important;
    height: 650px !important;
    width: 100%;

    .section-title {
      margin-bottom: 0 !important;
      color: #fff;
    }

    .section-item {
      margin: 0 auto !important;
      padding: 0 !important;
      background: transparent !important;
      height: 550px;
      max-width: 1200px !important;
      display: block !important;

      & > div {
        display: flex;
        margin-top: 50px;
      }

      .left {
        display: flex;
        flex-wrap: wrap;
        position: relative;

        li {
          position: relative;
          width: 355px !important;
          height: 207px;
          margin-bottom: 16px;
          cursor: pointer;

          img {
            width: 355px !important;
            height: auto !important;
          }

          .line {
            width: 21px;
            height: 4px;
            background: #ffffff;
            position: absolute;
            left: 50px;
            top: 40px;
            font-size: 16px;
            font-weight: 600;
          }

          span {
            position: absolute;
            left: 50px;
            top: 60px;
            font-size: 18px;
            font-family: PingFang SC, PingFang SC-Semibold;
            font-weight: 600;
            color: #ffffff;
            line-height: 30px;
          }

          &:nth-child(1) {
            img {
              width: 381px !important;
            }

            span {
              color: #fff;
            }
          }

          &:nth-child(2) {
            .line {
              background: #36d1fe;
            }

            span {
              color: #000;
            }
          }
        }

        .video-item {
          &:nth-child(2) {
            img {
              width: 381px !important;
            }

            .line {
              background: #ffffff;
            }

            span {
              color: #fff;
            }
          }

          &:nth-child(1) {
            .line {
              background: #36d1fe;
            }

            span {
              color: #000;
            }

            img {
              width: 355px !important;
            }
          }
        }
      }

      .right {
        height: 432px;
        border: 2px solid;
        border-image: linear-gradient(to right, #b2cbf6, #4db9f8) 4;
      }
    }
  }

  .section-5 {
    width: 100%;
    height: 500px;
    max-width: 100% !important;
    position: relative;

    .section-item {
      display: flex;
      max-width: 1200px;
      margin: 0 auto;

      .content {
        width: 590px;
        height: 301px;
        background: #ffffff;
        border: 1px solid #f1f1f1;
        box-shadow: -4px 0px 14px 4px rgba(188, 198, 216, 0.3);
        // flex: 1;
        padding: 48px 36px;
        position: relative;

        &:nth-child(2) {
          margin-left: 20px;
        }

        .title {
          font-size: 18px;
          font-family: PingFang SC, PingFang SC-Semibold;
          font-weight: 600;
          color: #262626;
          line-height: 30px;
        }

        .main {
          width: 256px;
          font-size: 14px;
          margin-top: 24px;
          font-family: PingFang SC, PingFang SC-Regular;
          text-align: justifyLeft;
          color: #666666;
          line-height: 42px;
        }

        img {
          position: absolute;
          right: 0;
          bottom: 0;
        }
      }
    }
  }

  .section-7 {
    background-color: #fff !important;
    width: 100%;
    max-width: 100% !important;
    position: relative;

    .section-title {
      margin-bottom: 40px !important;
    }

    .section-item {
      margin-top: 50px;
      height: 115px;
      width: 100%;
      max-width: 1200px;
      margin: 0 auto;

      ul {
        width: 100%;
        display: flex;
      }

      .item {
        flex: 1;
        display: flex;
        align-items: center;
        // justify-content: center;

        .left {
          width: 73px;
        }

        .right {
          margin-left: 16px;

          .title {
            font-size: 18px;
            font-family: PingFang SC, PingFang SC-Semibold;
            font-weight: 600;
            text-align: left;
            color: #262626;
            line-height: 30px;
          }

          .content {
            font-size: 14px;
            font-family: PingFang SC, PingFang SC-Regular;
            font-weight: 400;
            text-align: justifyLeft;
            color: #666666;
            line-height: 42px;
          }
        }
      }
    }
  }

  .section-8 {
    .section-item {
      display: flex;
      flex-direction: row;
      justify-content: space-around;
      align-items: center;

      .item {
        text-align: center;
        height: 151px;
        width: 590px;
        background: url('../../../../assets/images/solution/soft-hardware/803/qd1.png')
          center no-repeat !important;
        background-size: contain !important;

        &:nth-child(2) {
          background: url('../../../../assets/images/solution/soft-hardware/803/qd2.png')
            center no-repeat !important;
          background-size: contain !important;
        }

        .title {
          font-size: 18px;
          font-family: PingFang SC, PingFang SC-Semibold;
          font-weight: 600;
          text-align: left;
          color: #262626;
          line-height: 30px;
          text-align: center;
          margin-top: 24px;
        }

        p {
          font-size: 14px;
          font-family: PingFang SC, PingFang SC-Regular;
          font-weight: 400;
          text-align: center;
          color: #666666;
          line-height: 42px;
          margin-top: 34px;
        }
      }
    }
  }

  .section-9 {
    width: 100%;
    max-width: 100% !important;
    position: relative;

    .section-item {
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      background: url('../../../../assets/images/solution/soft-hardware/803/kfcl.png')
        center no-repeat !important;
      background-size: contain !important;
      height: 224px;

      .item {
        padding-top: 50px;
        // display: grid;
        grid-template-columns: repeat(2, 1fr);
        grid-gap: 10px;
        text-align: center;
      }
    }
  }
}

@media screen and (max-width: 750px) {
  .main-content {
    &-banner {
      background: url('../../../../assets/images/solution/soft-hardware/3328/banner_bg.png')
        center no-repeat;
      background-size: cover;
    }
  }
}

.contact-wrap {
  // padding-top: 100px;
  height: 400px;
  text-align: center;

  .title {
    margin-bottom: 16px;
    font-size: 34px;
    color: #333;
    font-weight: bold;
  }

  .desc {
    font-size: 16px;
    color: #666;
    margin-bottom: 50px;
  }

  .apply-btn {
    margin: 60px auto 0;
    background: #1784e9;

    &:hover {
      color: #fff;
    }
  }
}

.video-cover {
  width: 760px;
  // height: 100%;
  height: 450px;
  background-size: cover;
  background-repeat: no-repeat;
  position: relative;
  cursor: pointer;
}

.play-icon {
  position: absolute;
  width: 100px;
  height: 100px;
  z-index: 1;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: url(~@A/images/solution/multimodal-interaction/<EMAIL>)
    center/contain no-repeat;
}
</style>
