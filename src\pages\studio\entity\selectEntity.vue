<template>
<div class="select-entity-popover">
  <div class="skill-select-popover__head">
    <div class="skill-select-popover-search">
      <el-input
        class="search-area"
        placeholder="搜索实体"
        v-model="searchName">
      </el-input>
    </div>
  </div>
  <div class="skill-select-popover__body">
    <div class="skill-select-popover-list" v-loadmore="scrollLoad" v-loading="loading">
      <div class="skill-select-popover-item"
        v-for="(item, key) in skillData.list"
        @click="selectItem(item)">
        <span :title="item.value">{{item.value}}</span>
      </div>
      <div class="el-table__empty-block" v-if="!skillData.list.length">
        <span class="el-table__empty-text">暂无数据</span>
      </div>
    </div>
    <os-divider/>
    <div class="skill-select-popover-add">
      <i class="ic-r-plus" />
      <a @click="addSkill">创建实体</a>
    </div>
  </div>
</div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  props: {
    subAccount: Boolean
  },
  data () {
    return {
      searchName: '',
      loading: false, //搜索时的loading
      skillData: {
        loading: true,
        loadend: false,
        total: 0,
        page: 1,
        size: 20,
        list: []
      },
      debounced: null
    }
  },
  computed: {
  },
  watch: {
    'searchName': function (val, oldVal) {
      this.debounced()
    }
  },
  mounted() {
    this.initData()
    this.setDebounce()
  },
  beforeDestroy(){
    this.debounced = null
  },
  methods: {
    setDebounce(){
      this.debounced = this.$utils.debounce(() => { this.initData(1) }, 500, true)
    },
    initData (page) {
      let self = this
      this.skillData.loading = true
      this.loading = true
      this.$utils.httpGet(this.$config.api.STUDIO_ENTITY_LIST, {
        pageIndex: page || this.skillData.page,
        pageSize: this.skillData.size,
        search: this.searchName
      }, {
        success: (res) => {
          if (res.data.pageIndex === 1) {
            self.skillData.list = res.data.results
          } else {
            self.skillData.list = self.skillData.list.concat(res.data.results)
          }
          self.skillData.total = res.data.count
          self.skillData.page = res.data.pageIndex
          self.skillData.size = res.data.pageSize
          self.skillData.loading = false
          self.loading = false
          self.skillData.loadend = res.data.pageSize * res.data.pageIndex >= res.data.count
        },
        error: (err) => {
          self.loading = false
        }
      })
    },
    scrollLoad () {
      if (this.skillData.loading || this.skillData.loadend) {
        return
      }
      this.initData(this.skillData.page + 1)
    },
    selectItem (item) {
      let routeData
      if(this.subAccount) {
        routeData = this.$router.resolve({ name: 'sub-entity', params: {entityId: item.id}})
      } else {
        routeData = this.$router.resolve({ name: 'entity', params: {entityId: item.id}})
      }
      location.href = routeData.href
    },
    addSkill () {
      let routeData
      if(this.subAccount) {
        routeData = this.$router.resolve({name: 'sub-studio-handle-platform-entities'})
      } else {
        routeData = this.$router.resolve({name: 'studio-handle-platform-entities'})
      }
      localStorage.setItem('pageHandle', 'create')
      window.open(routeData.href, '_blank')
    }
  },
  components: {

  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="scss">

.select-entity-popover {
  position: absolute;
  top: 0;
  left: 0;
  background: #fff;
  width: 264px;
  border-radius: 8px;
  border: 1px solid #ebeef5;
}

.skill-select-popover__head {
  display: flex;
  align-items: center;
}
.skill-select-popover__body {
  // margin-top: 10px;
}
.skill-select-popover-search {
  width: 100%;
  border-bottom: 1px solid $grey2;
}
.skill-select-popover-search input {
  border: 0;
  border-radius: 8px;
}
.skill-select-popover-list {
  width: 100%;
  height: 236px;
  overflow-y: scroll;
}
.skill-select-popover-item {
  height: 36px;
  line-height: 36px;
  padding: 0 16px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: pointer;
  &:hover {
    background: $primary-light-12;
  }
}
.skill-select-popover-add {
  height: 48px;
  line-height: 48px;
  padding: 0 16px;
  display: flex;
  i {
    color: $grey4;
  }
  a {
    font-weight: 600;
    padding-left: 6px;
  }
}
</style>
