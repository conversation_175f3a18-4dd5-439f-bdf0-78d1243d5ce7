<template>
  <div class="container">
    <div class="header">
      <div style="display: flex; align-items: center">
        <back-icon @click="toBack" style="margin-right: 10px"></back-icon>
        <h2>信源编辑</h2>
        <el-select
          v-model="sourceId"
          placeholder="信源名称"
          @change="changeSource"
        >
          <el-option
            v-for="item in sourceSelectList"
            :key="item.id"
            :value="item.sourceId"
            :label="item.sourceName"
          ></el-option>
        </el-select>
      </div>

      <el-button type="primary" @click="sourcePublish">发布</el-button>
    </div>

    <div class="content">
      <!-- <div class="sub-title">信源鉴权配置</div> -->
      <div class="sub-title">API接口配置</div>
      <div class="api-operate">
        <el-input
          v-model="searchVal"
          style="width: 300px"
          @keydown.enter.native="getAllApiList"
          class="search-area"
          placeholder="搜索API"
          size="medium"
          clearable
        >
          <i
            slot="suffix"
            class="el-input__icon el-icon-search search-area-btn"
            @click="getAllApiList()"
          />
        </el-input>

        <el-button @click="doDefaultAuth" style="margin-left: auto"
          >默认鉴权</el-button
        >
        <el-button @click="addAPi" type="primary">新增API</el-button>
      </div>

      <el-scrollbar v-loading="loading">
        <div class="api_list_wrapper" v-if="apiCardList.length > 0">
          <ApiCard
            v-for="item in apiCardList"
            :key="item.id"
            :ApiInfo="item"
            :defaultAuthConfig="defaultAuthConfig"
            @refresh="refresh"
            @doNoIdDel="doNoIdDel"
          ></ApiCard>
        </div>

        <Empty v-else style="height: calc(100% - 90px)" />
      </el-scrollbar>

      <el-pagination
        v-if="apiCardList.length > 0"
        ref="pagination"
        :current-page="pageIndex"
        :page-size="pageSize"
        :total="total"
        :layout="pageLayout"
        @current-change="pageChange"
        class="txt-al-c"
      ></el-pagination>
    </div>

    <auth-info-modal ref="AuthInfoModal" @refresh="refresh" />
  </div>
</template>

<script>
import AuthInfoModal from './components/authInfoModal.vue'
import FormRow from './components/formRow.vue'
import ArrayModal from './components/arrayModal.vue'
import DebugWrapper from './components/debugWrapper.vue'
import DebugModal from './components/debugModal.vue'
import ApiCard from './components/cardList.vue'
import Empty from './components/empty.vue'
export default {
  name: 'studio-platForm-source-detail',
  created() {
    this.sourceId = this.$route.params.sourceId
    this.getAllSource()
    this.getAllApiList()
    this.getSourceDetail()
  },
  components: {
    FormRow,
    ArrayModal,
    DebugWrapper,
    DebugModal,
    ApiCard,
    AuthInfoModal,
    Empty,
  },

  data() {
    return {
      sourceId: null,
      sourceSelectList: [],

      toolName: null,

      loading: false,
      pageIndex: 1,
      pageSize: 5,
      total: 0,
      searchVal: null,

      apiCardList: [],

      defaultAuthConfig: {},
    }
  },
  computed: {
    pageLayout() {
      return this.total > 5
        ? 'prev, pager, next, jumper, total'
        : 'prev, pager, next'
    },
  },
  methods: {
    toBack() {
      this.$router.push({
        name: 'studio-source',
      })
    },

    doNoIdDel() {
      this.apiCardList.shift()
      this.refresh()
    },
    refresh() {
      this.getSourceDetail()
      this.getAllApiList()
    },

    getAllApiList() {
      const params = {
        pageSize: this.pageSize,
        pageIndex: this.pageIndex,
        toolName: this.searchVal,
        sourceId: this.$route.params.sourceId,
      }
      this.loading = true
      this.$utils.httpPost(
        `/aiui-agent/openPlatform/source/apiPage`,
        JSON.stringify(params),
        {
          config: {
            headers: {
              'Content-Type': 'application/json;charset=UTF-8',
            },
          },
          success: (res) => {
            if (res.code === '0') {
              this.loading = false
              this.apiCardList = res.data.records
              this.total = res.data.total
            }
          },
          error: (err) => {
            this.loading = false
          },
        }
      )
    },

    getAllSource() {
      const params = {
        pageSize: 9999,
        pageIndex: this.pageIndex,
        sourceName: '',
      }
      this.$utils.httpPost(
        '/aiui-agent/openPlatform/source/page',
        JSON.stringify(params),
        {
          config: {
            headers: {
              'Content-Type': 'application/json;charset=UTF-8',
            },
          },
          success: (res) => {
            if (res.code === '0') {
              this.sourceSelectList = res.data.records.map((item) => {
                return {
                  sourceId: item.sourceId,
                  sourceName: item.sourceName,
                }
              })
            }
          },
          error: (err) => {},
        }
      )
    },

    pageChange(e) {
      this.pageIndex = e
      this.getAllApiList()
    },

    sourcePublish() {
      let sourceId = this.$route.params.sourceId
      this.$utils.httpPost(
        `/aiui-agent/openPlatform/source/apiPublish?sourceId=${sourceId}`,
        {},
        {
          config: {
            headers: {
              'Content-Type': 'application/json;charset=UTF-8',
            },
          },
          success: (res) => {
            if (res.code === '0') {
              this.$message.success('发布成功')
              this.getAllApiList()
            }
          },
          error: (err) => {
            this.$message.error(err.desc)
          },
        }
      )
    },

    getSourceDetail() {
      const params = {
        pageSize: 1,
        pageIndex: 1,
        sourceId: this.$route.params.sourceId,
      }
      this.$utils.httpPost(
        '/aiui-agent/openPlatform/source/page',
        JSON.stringify(params),
        {
          config: {
            headers: {
              'Content-Type': 'application/json;charset=UTF-8',
            },
          },
          success: (res) => {
            if (res.code === '0') {
              let authInfo = res.data?.records[0].authInfo
              this.defaultAuthConfig = authInfo ? JSON.parse(authInfo) : {}
            }
          },
          error: (err) => {},
        }
      )
    },

    changeSource(val) {
      this.$router.push({
        name: 'studio-source-detail',
        params: {
          sourceId: val,
        },
      })
      this.getAllApiList()
    },
    addAPi() {
      const ApiItem = {
        toolName: '',
        endPoint: null,
        method: 'post',
        authType: -1,
        authInfo: '',
        webSchema: JSON.stringify({
          toolRequestInput: [],
          toolRequestOutput: [],
        }),
      }

      this.apiCardList.unshift(ApiItem)
    },
    doDefaultAuth() {
      this.$refs.AuthInfoModal.show(1, null, this.defaultAuthConfig)
    },
  },
}
</script>

<style lang="scss" scoped>
.container {
  padding-top: 0px;
  background-color: $white-grey;
  height: 100%;
  // overflow-y: auto;
  .header {
    background-color: #fff;
    border: 1px solid #e1e1e1;
    border-top: 0px;
    border-left: 0px;
    display: flex;
    justify-content: space-between;
    height: 63px;
    align-items: center;
    padding: 0px 24px;
    i {
      font-size: 24px;
      margin-right: 15px;
      cursor: pointer;
    }
    h2 {
      margin-right: 20px;
    }
  }

  .content {
    padding: 20px;
    // height: calc(100% - 70px);
    background-color: $secondary-bgc;
    .sub-title {
      font-size: 18px;
      font-weight: 700;
      margin-bottom: 10px;
    }
    .api-operate {
      display: flex;
      align-content: center;
      justify-content: space-between;
      margin-bottom: 20px;
    }
    .form-item {
      width: 540px;
      border: 1px solid #d7d7d7;
      border-radius: 6px;
    }
    :deep(.el-form-item__label) {
      color: #555454 !important;
    }
    .serviceTokenLabel {
      :deep(.el-form-item__label) {
        width: 150px; /* 固定宽度 */
        display: -webkit-box;
        -webkit-line-clamp: 2; /* 限制为1行 */
        -webkit-box-orient: vertical;
        overflow: hidden;
        line-height: 1.5;
      }
    }
  }

  .el-scrollbar {
    height: calc(100vh - 266px);
    .api_list_wrapper {
      display: flex;
      flex-direction: column;
      gap: 20px;
    }
  }

  :deep(.el-scrollbar__view) {
    height: 100%;
  }
  .el-pagination {
    margin-top: 24px;
  }
}
</style>
