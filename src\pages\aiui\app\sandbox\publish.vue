<template>
  <el-dialog title="发布" :visible.sync="dialog.show" width="760px">
    <div class="app-audit-page">
      <p class="apply-tips">
        应用配置和引用技能版本的修改只在测试环境中生效，发布后才会对在生产环境中生效。<br />为了避免对用户造成困扰，在发布前，请保证你已经对应用进行了完善的测试。
      </p>
      <el-form
        :model="form"
        :rules="rules"
        :disabled="!subAccountEditable"
        ref="form"
        label-width="120px"
        label-position="left"
        class="mgt32"
      >
        <!-- rtos、评估板、魔飞应用 不展示 -->
        <template
          v-if="platformNum && platformNumList.indexOf(platformNum) == -1"
        >
          <os-page-label label="基本信息" class="mgb24"></os-page-label>
          <el-form-item label="产品形态" required>
            <el-radio-group
              class="app-type"
              v-model="form.appType"
              @change="appTypeChange"
            >
              <el-radio :label="1">软件</el-radio>
              <el-radio :label="2">硬件</el-radio>
            </el-radio-group>
          </el-form-item>
          <!-- <el-form-item
            label="是否有屏"
            prop="screen"
            v-if="form.appType == '2'"
            required
          >
            <radio-tab
              v-model="form.screen"
              :data="[
                { label: '有屏', value: '1' },
                { label: '无屏', value: '0' },
              ]"
            ></radio-tab>
          </el-form-item> -->
          <el-form-item label="产品名称" prop="name" ref="name">
            <el-input
              v-model.trim="form.name"
              placeholder="应用的正式名称，长度为1-30个字符"
              size="small"
            ></el-input>
          </el-form-item>
          <el-form-item label="产品介绍" prop="introduction" ref="introduction">
            <el-input
              v-model="form.introduction"
              type="textarea"
              placeholder="请描述应用使用场景、面向人群、主要功能、语音语义使用情况等信息"
              :autosize="{ minRows: 3, maxRows: 8 }"
            ></el-input>
          </el-form-item>
          <el-form-item label="产品截图/视频" prop="appVideo">
            <el-upload
              class="audit-app-upload"
              ref="appVideoUpload"
              :action="`${this.$config.server}/aiui/web/app/auditing/upload?appid=${appId}&type=1`"
              :file-list="appVideoList"
              :on-change="handleAppVideoChange"
              :before-upload="beforeUpload"
              :on-remove="handleAppVideoRemove"
              :on-success="handleAppVideoUrl"
            >
              <el-button size="small" :plain="true">上传</el-button>
              <div slot="tip" class="el-upload__tip">
                200M以内的zip包，文件过大时请在备注中附加网盘下载链接及提取码
              </div>
            </el-upload>
            <el-input v-show="false" v-model="form.appDocument"></el-input>
          </el-form-item>
          <div v-if="form.appType === 2" key="hardwareInfo">
            <el-form-item
              label="硬件信息"
              prop="hardwareInfo"
              ref="hardwareInfo"
            >
              <el-input
                v-model="form.hardwareInfo"
                type="textarea"
                placeholder="请描述产品硬件配置，如使用了麦克风阵列，请说明麦克风阵列型号"
                :autosize="{ minRows: 3, maxRows: 8 }"
              ></el-input>
            </el-form-item>
          </div>
          <el-form-item label="备注" prop="appNote">
            <el-input
              v-model="form.appNote"
              type="textarea"
              placeholder="可填写为审核人员准备的测试账号等信息"
              :autosize="{ minRows: 3, maxRows: 8 }"
            ></el-input>
          </el-form-item>
          <el-form-item label="姓名" prop="linkman" ref="linkman">
            <el-input
              v-model.trim="form.linkman"
              placeholder="联系人姓名"
              size="small"
            ></el-input>
          </el-form-item>
          <el-form-item label="手机" prop="telephone" ref="telephone">
            <!-- <el-input
              v-model.trim="form.telephone"
              placeholder="多个手机号使用英文逗号隔开"
              size="small"
            ></el-input> -->
            <el-tag
              class="keyword"
              :key="index"
              v-for="(keyword, index) in telephoneArr"
              closable
              :disable-transitions="false"
              @close="handleTelephoneClose(index)"
              >{{ keyword }}</el-tag
            >
            <el-input
              size="medium"
              class="new-keyword-input"
              v-if="telephoneAddVisible"
              v-model.trim="newTelephone"
              ref="saveTagInput"
              placeholder="回车添加"
              @blur="handleNewTelephoneConfirm"
              @keyup.enter.native="handleNewTelephoneConfirm"
            ></el-input>
            <div
              v-if="!telephoneAddVisible && telephoneArr.length <= 4"
              class="button-new-keyword ib"
              @click="showNewTelephoneConfirm"
            >
              +
            </div>
            <div class="mobile-tips">
              手机号用于人工审核及授权不足时短信提醒使用。
            </div>
          </el-form-item>
          <os-page-label
            label="版本更新信息"
            class="mgt56 mgb24"
          ></os-page-label>
        </template>
        <el-form-item
          label="版本号"
          class="is-required"
          prop="versionArr"
          ref="versionArr"
        >
          <el-input
            class="release-form-version-input"
            v-model.number="form.versionArr[0]"
            type="number"
            size="small"
          ></el-input>
          <span>.</span>
          <el-input
            class="release-form-version-input"
            v-model.number="form.versionArr[1]"
            type="number"
            size="small"
          ></el-input>
          <span>.</span>
          <el-input
            class="release-form-version-input mgr16"
            v-model.number="form.versionArr[2]"
            type="number"
            size="small"
          ></el-input>
          <span v-if="form.number && form.number != '0.0.0'"
            >线上版本{{ form.number }}</span
          >
          <a v-if="!neverPublished" @click="versionDiff.show = true"
            >查看版本对比</a
          >
        </el-form-item>
        <el-form-item class="mgb48" label="更新说明" prop="updateLog">
          <el-input
            v-model="form.updateLog"
            type="textarea"
            placeholder="简要说明本次的更新点，例如修改了情景模式main的识别热词。"
            :autosize="{ minRows: 3, maxRows: 8 }"
          ></el-input>
        </el-form-item>
        <el-form-item class="" label="情景模式" prop="scenes">
          <p style="color: #8c8c8c">选择你本次更新的情景模式</p>
          <div class="table-wrapper">
            <div class="checkbox-wrapper">
              <el-checkbox
                v-model="isCheckedAll"
                :indeterminate="isIndeterminate"
                @change="onCheckedAllChange"
              ></el-checkbox>
            </div>

            <os-table
              class="mgb24 table-container"
              :tableData="tableData"
              @change="getScene"
            >
              <el-table-column width="50">
                <template slot-scope="scope">
                  <el-checkbox
                    v-model="scope.row.checked"
                    @change="
                      (val) => onItemCheckedChange(val, scope.row.sceneName)
                    "
                  ></el-checkbox>
                </template>
              </el-table-column>
              <el-table-column
                prop="sceneName"
                label="名称"
                width="130"
              ></el-table-column>
              <el-table-column label="说明" width="130">
                <template slot-scope="scope">
                  {{
                    scope.row.publish
                      ? ''
                      : form.number
                      ? `${form.number}未更新`
                      : '未更新'
                  }}
                </template>
              </el-table-column>
            </os-table>
          </div>
        </el-form-item>
      </el-form>
      <version-diff
        :appName="appName"
        :number="form.number"
        :versionDiff="versionDiff"
      ></version-diff>
    </div>
    <span slot="footer" class="dialog-footer" v-if="subAccountPublishable">
      <el-button
        type="primary"
        @click="submitForm('form')"
        :loading="pageOptions.loading"
        >发布到线上</el-button
      >
    </span>
  </el-dialog>
</template>

<script>
import VersionDiff from './versionDiff'
import { mapGetters } from 'vuex'
export default {
  name: 'app-audit',
  props: {
    subAccountEditable: Boolean,
    subAccount: Boolean,
    dialog: {
      type: Object,
      default: () => {
        return {
          show: false,
        }
      },
    },
  },
  data() {
    return {
      pageOptions: {
        title: '更新发布',
        loading: false,
        returnBtn: false,
      },
      isCertificated: null, //是否实名认证
      telephoneArr: [], // 手机号数组
      telephoneAddVisible: false, // 手机号添加是否展示加号
      newTelephone: '',
      form: {
        appType: 1,
        name: '',
        introduction: '',
        linkman: '',
        telephone: '',
        appVideo: '', // 产品截图&视频
        testAddress: '', // 测试地址
        appPackage: '', // 应用安装包
        hardwareInfo: '', // 硬件信息
        appNote: '',
        number: '',
        versionArr: [],
        screen: '1',
      },
      rules: {
        name: [
          {
            required: true,
            message: '请填写产品名称',
            trigger: ['change', 'blur'],
          },
          { max: 30, message: '产品名称长度为1-30个字符' },
          {
            pattern: /^[a-zA-Z0-9.\-_\u4e00-\u9fa5]+$/,
            message: '产品名称仅支持汉字/字母/数字/下划线',
            trigger: ['change', 'blur'],
          },
        ],
        introduction: [
          {
            required: true,
            message: '请填写产品介绍',
            trigger: ['change', 'blur'],
          },
          { max: 1000, message: '产品介绍不能超过1000字符' },
        ],
        linkman: [
          {
            required: true,
            message: '请填写联系人姓名',
            trigger: ['change', 'blur'],
          },
        ],
        telephone: [
          {
            required: true,
            message: '请填写手机号',
            trigger: ['blur'],
          },
        ],
        hardwareInfo: [
          {
            required: true,
            message: '请填写产品硬件信息',
            trigger: ['change', 'blur'],
          },
          { max: 1000, message: '硬件信息不能超过1000字符' },
        ],
        appNote: [{ max: 1000, message: '备注不能超过1000字符' }],
        versionArr: [{ validator: this.checkVersion, trigger: ['blur'] }],
        // scenes: [{ required: true }],
      },
      appVideoList: [],
      apkFileList: [],
      versionDiff: {
        show: false,
      },
      neverPublished: false,
      platformNumList: ['10', '11', '13'],
      tableData: {
        total: 0,
        page: 1,
        size: 5,
        list: [],
      },
      isCheckedAll: true,
      isIndeterminate: false,
    }
  },
  computed: {
    appId() {
      return this.$route.params.appId
    },
    appName() {
      return this.$store.state.aiuiApp.app.appName || ''
    },
    changed() {
      return this.form
    },
    platformNum() {
      return this.$store.state.aiuiApp.app.platformNum || ''
    },
    ...mapGetters({
      subAccountAppAuths: 'aiuiApp/subAccountAppAuths',
    }),
    subAccountPublishable() {
      if (!this.subAccount) {
        return true
      }
      return this.subAccountAppAuths[this.appId] == 4 ? true : false
    },
  },
  watch: {
    '$store.state.aiuiApp.app.check': function (val) {
      if (!val) {
        let routeData = this.$router.resolve({
          name: 'app-audit',
          params: { appId: this.appId },
        })
        location.href = routeData.href
      }
    },
  },
  watch: {
    'dialog.show'(val) {
      if (val) {
        this.getVesion()
        this.getScenes()
      }
    },
  },
  // created() {
  //   this.getVesion()
  //   this.getScenes()
  // },
  methods: {
    checkVersion(rule, value, callback) {
      let self = this
      let first = parseInt(value[0]),
        second = parseInt(value[1]),
        third = parseInt(value[2])
      if (
        !Number.isInteger(first) ||
        !Number.isInteger(second) ||
        !Number.isInteger(third)
      ) {
        callback(new Error('版本号不能为空，且必须为数字值'))
      }
      if (!self.form.number) {
        callback()
        return
      }
      if (
        value.join('.') == self.form.number ||
        first < self.form.number.split('.')[0]
      ) {
        callback(new Error('当前版本号需大于最近发布的版本号'))
      }
      if (first == self.form.number.split('.')[0]) {
        if (second < self.form.number.split('.')[1]) {
          callback(new Error('当前版本号需大于最近发布的版本号'))
        }
        if (
          second == self.form.number.split('.')[1] &&
          third <= self.form.number.split('.')[2] - 1
        ) {
          callback(new Error('当前版本号需大于最近发布的版本号'))
        }
      }
      callback()
    },
    getVesion() {
      let self = this
      // self.pageOptions.loading = true
      this.$utils.httpGet(
        this.$config.api.AIUI_APP_VERSION,
        {
          appid: this.appId,
        },
        {
          success: (res) => {
            if (res.flag) {
              res.data
                ? (self.form = {
                    ...res.data,
                    updateLog: '',
                    appType: res.data.appType ? res.data.appType : 1,
                  })
                : ''
              if (self.form.telephone) {
                self.telephoneArr = self.form.telephone.split(',')
              } else {
                self.telephoneArr = []
              }
              if (self.form.number) {
                // self.form.versionArr = self.form.number.split('.')
                self.$set(self.form, 'versionArr', self.form.number.split('.'))
                self.form.versionArr[self.form.versionArr.length - 1] =
                  parseInt(
                    self.form.versionArr[self.form.versionArr.length - 1]
                  ) + 1
                self.neverPublished = false
              } else {
                self.form.versionArr = [0, 0, 1]
                self.neverPublished = true
              }
              // self.pageOptions.loading = false
            }
            self.$refs.form && self.$refs.form.resetFields()
          },
          error: (err) => {
            // self.pageOptions.loading = false
          },
        }
      )
    },
    appTypeChange(value) {
      this.appVideoList = []
      this.apkFileList = []
    },
    handleAppVideoChange(file, fileList) {
      const isZIP = file.type === 'application/zip'
      const zipEXT = /.zip$/.test(file.name)
      const isLt200M = file.size / 1024 / 1024 < 200
      if (!(isZIP || zipEXT)) {
        this.$message({
          message: '上传文件仅支持zip格式',
          type: 'warning',
        })
      }
      if (!isLt200M) {
        this.$message({
          message: '上传文件不能超过200M',
          type: 'warning',
        })
      }
      if ((isZIP || zipEXT) && isLt200M) {
        this.appVideoList = fileList.slice(-1)
      }
    },
    handleApkChange(file, fileList) {
      const isZIP = file.type === 'application/zip'
      const zipEXT = /.zip$/.test(file.name)
      const isLt200M = file.size / 1024 / 1024 < 200
      if (!(isZIP || zipEXT)) {
        this.$message({
          message: '上传文件仅支持zip格式',
          type: 'warning',
        })
      }
      if (!isLt200M) {
        this.$message({
          message: '上传文件不能超过200M',
          type: 'warning',
        })
      }
      if ((isZIP || zipEXT) && isLt200M) {
        this.apkFileList = fileList.slice(-1)
      }
    },
    // 判断上传文件类型和大小
    beforeUpload(file) {
      const isZIP = file.type === 'application/zip'
      const zipEXT = /.zip$/.test(file.name)
      const isLt200M = file.size / 1024 / 1024 < 200
      if (!(isZIP || zipEXT)) {
        this.$message({
          message: '上传文件仅支持zip格式',
          type: 'warning',
        })
      }
      if (!isLt200M) {
        if (!isLt200M) {
          this.$message({
            message: '上传文件不能超过200M',
            type: 'warning',
          })
        }
      }
      return (isZIP || zipEXT) && isLt200M
    },
    handleAppVideoRemove(file) {
      if (file) {
        this.form.appVideo = ''
        this.$refs.form.validateField('appVideo')
      }
    },
    handleAppVideoUrl(data) {
      if (data.flag) {
        this.form.appVideo = data.data
        this.$refs.form.validateField('appVideo')
      } else {
        this.$message.error(data.desc)
      }
    },
    handleAPKRemove(file) {
      if (file) {
        this.form.appPackage = ''
        this.$refs.form.validateField('appPackage')
      }
    },
    handleAPKUrl(data) {
      if (data.flag) {
        this.form.appPackage = data.data
        this.$refs.form.validateField('appPackage')
      } else {
        this.$message.warning(data.desc)
      }
    },
    scrollToView(_this, object) {
      for (let i in object) {
        let dom = _this.$refs[i]
        if (Object.prototype.toString.call(dom) !== '[object Object]') {
          dom = dom[0]
        }
        dom.$el.scrollIntoView({
          block: 'center',
          behavior: 'smooth',
        })
        break
      }
    },
    submitForm(formName) {
      let self = this

      const scenes = this.appScenes
        .filter((item) => item.checked)
        .map((item) => item.sceneName)

      if (scenes.length <= 0) {
        return this.$message.warning('请至少选择一个情景模式')
      }
      self.pageOptions.loading = true
      let data = {
        appid: self.appId,
        number: self.form.versionArr.join('.'),
        updateLog: self.form.updateLog || '',
        scenes: JSON.stringify(scenes),
      }
      if (
        self.platformNum &&
        self.platformNumList.indexOf(self.platformNum) == -1
      ) {
        data.appType = self.form.appType
        data.name = self.form.name
        data.introduction = self.form.introduction
        data.linkman = self.form.linkman
        data.telephone = self.form.telephone
        data.appVideo = self.form.appVideo
        data.testAddress = self.form.testAddress
        data.hardwareInfo = self.form.hardwareInfo
        data.appNote = self.form.appNote
      }
      this.$refs[formName].validate((valid, object) => {
        if (valid) {
          this.$utils.httpPost(
            this.$config.api.AIUI_APP_VERSION_PUBLISH,
            data,
            {
              success: (res) => {
                if (res.flag) {
                  self.pageOptions.loading = false
                  this.$message_pro_success(
                    '提交成功',
                    '你现在可以在设备上的正式环境体验了！'
                  )
                  // self.getScenes()
                  // self.getVesion()
                  self.dialog.show = false
                } else {
                  self.$message.error(res.desc)
                }
              },
              error: (err) => {
                self.pageOptions.loading = false
              },
            }
          )
        } else {
          console.log(data)
          self.scrollToView(self, object)
          self.pageOptions.loading = false
          // 手机号问题兜底
          if (!this.form.telephone && this.telephoneArr.length > 0) {
            this.form.telephone = this.telephoneArr.join(',')
          }
          return false
        }
      })
    },

    // 手机号删除
    handleTelephoneClose(index) {
      this.telephoneArr.splice(index, 1)
      this.form.telephone = this.telephoneArr.join(',')
    },
    // 手机号添加完成
    handleNewTelephoneConfirm() {
      if (!this.newTelephone) {
        this.telephoneAddVisible = false
        this.newTelephone = ''
        return
      }
      const regExp = new RegExp('^1[3456789]\\d{9}$')
      if (!regExp.test(this.newTelephone)) {
        this.$message.warning('请输入正确格式的手机号')
        return
      }
      if (this.telephoneArr.indexOf(this.newTelephone) !== -1) {
        this.$message.warning('手机号不能重复添加')
        return
      }
      this.telephoneArr.push(this.newTelephone)
      this.form.telephone = this.telephoneArr.join(',')
      this.telephoneAddVisible = false
      this.newTelephone = ''
      console.log(this.form.telephone)
    },
    // 手机号添加开始
    showNewTelephoneConfirm() {
      this.telephoneAddVisible = true
      this.$nextTick((_) => {
        this.$refs.saveTagInput.$refs.input.focus()
      })
    },

    getScenes() {
      let self = this
      self.tableData.loading = true
      this.$utils.httpGet(
        this.$config.api.AIUI_APP_VERSION_SCENE,
        {
          appid: this.appId,
        },
        {
          success: (res) => {
            self.tableData.loading = false
            self.appScenes = (res.data.scenes || []).map((item) => ({
              ...item,
              checked: true,
            }))
            self.tableData.total = (res.data.scenes || []).length
            self.tableData.list = []
            this.getScene(1)
          },
          error: (err) => {
            self.tableData.loading = false
          },
        }
      )
    },

    getScene(page) {
      let self = this
      this.tableData.page = page || 1
      self.tableData.loading = true
      let pageIndex = page || self.tableData.page
      let from = (pageIndex - 1) * this.tableData.size
      let to = from + this.tableData.size
      self.tableData.list = self.appScenes.slice(from, to)
      self.tableData.loading = false
    },

    onCheckedAllChange(val) {
      this.isIndeterminate = false
      if (val) {
        this.tableData.list = this.tableData.list.map((item) => ({
          ...item,
          checked: true,
        }))
        this.appScenes = this.appScenes.map((item) => ({
          ...item,
          checked: true,
        }))
      } else {
        this.tableData.list = this.tableData.list.map((item) => ({
          ...item,
          checked: false,
        }))
        this.appScenes = this.appScenes.map((item) => ({
          ...item,
          checked: false,
        }))
      }
    },

    onItemCheckedChange(val, sceneName) {
      this.appScenes = this.appScenes.map((item) => {
        if (item.sceneName === sceneName) {
          return {
            ...item,
            checked: val,
          }
        } else {
          return item
        }
      })
      const allCheck = this.appScenes.every((item) => item.checked)
      const allNoCheck = this.appScenes.every((item) => !item.checked)
      if (allCheck) {
        this.isIndeterminate = false
        this.isCheckedAll = true
      }
      if (allNoCheck) {
        this.isIndeterminate = false
        this.isCheckedAll = false
      }
      if (!allCheck && !allNoCheck) {
        this.isIndeterminate = true
      }
    },
  },
  components: {
    VersionDiff,
  },
}
</script>

<style lang="scss" scoped>
.app-type {
  padding-top: 15px;
}
.auth-link {
  color: $warning;
  text-decoration: underline;
}
.apply-tips {
  color: $grey6;
}
.release-form-version-input {
  width: 80px;
}
.mobile-tips {
  color: #ff9b00;
  font-size: 12px;
  height: 22px;
  line-height: 24px;
}
.keyword {
  margin: 0 8px 8px 0;
}
.new-keyword-input {
  width: 200px;
}
.button-new-keyword {
  margin-top: 3px;
  width: 36px;
  height: 36px;
  font-size: 24px;
  line-height: 30px;
  cursor: pointer;
  color: $primary;
  text-align: center;
  vertical-align: top;
  border: 1px solid $grey3;
  border-radius: 2px;
}

.table-wrapper {
  position: relative;
  line-height: 20px;
}
.checkbox-wrapper {
  position: absolute;
  z-index: 1;
  top: 6px;
  left: 11px;
}
.app-audit-page {
  padding-bottom: 15px;
}
</style>
<style>
.audit-app-upload .el-upload {
  text-align: left;
}
</style>
