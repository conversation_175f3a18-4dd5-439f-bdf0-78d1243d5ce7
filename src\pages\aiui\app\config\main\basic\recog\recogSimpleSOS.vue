<template>
  <div>
    <el-form
      ref="form"
      class="recognition-form"
      :model="form"
      label-width="90px"
      label-position="left"
      :inline="true"
      :disabled="
        !subAccountEditable ||
        (currentScene &&
          currentScene.chainId === 'sos_app' &&
          currentScene.point === '1,13')
      "
    >
      <el-form-item
        class="form-item"
        style="margin-right: 10px"
        label="识别引擎："
      >
        <el-select
          class="config-select"
          v-model="form.audioConf"
          @change="changeOption"
          style="width: 195px"
        >
          <el-option
            v-for="(item, index) in audioConfList"
            :key="index"
            :label="item.value"
            :value="item.key"
            :disabled="!item.select"
          >
            <div class="audioConf-option-item">
              <span class="audioConf-option-item-left">
                {{ item.value }}
              </span>
              <span
                class="audioConf-option-item-email"
                v-if="!item.select"
                style=""
              >
                联系商务************************
                <span
                  class="cp"
                  @click="$utils.copyClipboard('<EMAIL>')"
                  >（复制）</span
                >
                授权
              </span>
              <span
                class="audioConf-option-item-endtime"
                v-else-if="item.endTime"
              >
                授权截止
                {{ (item.endTime * 1000) | date('yyyy-MM-dd') }}
              </span>
            </div>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item class="form-item" v-if="form.audioConf == 'sos-tv,zh-cn,0'">
        <el-select
          class="config-select"
          v-model="form.accent"
          placeholder="请选择方言"
          @change="changeAccentOption"
          style="width: 195px"
        >
          <el-option
            v-for="(item, index) in audioLangConfList"
            :key="index"
            :label="item.value"
            :value="item.accent"
            :disabled="!item.select"
          >
            <div class="audioConf-option-item">
              <span class="audioConf-option-item-left">
                {{ item.value }}
              </span>
              <span class="audioConf-option-item-endtime" v-if="item.endTime">
                授权截止
                {{ (item.endTime * 1000) | date('yyyy-MM-dd') }}
              </span>
            </div>
          </el-option>
        </el-select>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import { mapGetters } from 'vuex'

export default {
  data() {
    return {
      form: {
        language: 'zh-cn', // 语言
        accent: 'mandarin', // 方言
        domain: 'sos-tv', // 领域
        isFar: '0', // 距离
        ptt: '0',
        nunum: '0',
        dwa: '0',
        isHot: '0',
        audioConf: '', // 集合配置
      },
      audioLangConfList: [],
      audioConfList: [],
      accentAndDomain: {},
      getDataEnd: 0, // 组件有两个获取数据请求，用于记录请求返回状态。两个都结束再渲染数据
    }
  },
  created() {
    if (this.currentScene && this.currentScene.sceneBoxId) {
      this.getAccentAndDomain() // 获取配置列表
      this.getAacConf() // 获取当前配置值
      this.getAsrLanguageEntRelation()
    }
  },
  computed: {
    ...mapGetters({
      currentScene: 'aiuiApp/currentScene',
      appInfo: 'aiuiApp/app',
      subAccountEditable: 'aiuiApp/subAccountEditable',
    }),
    appId() {
      return this.$route.params.appId
    },
  },
  watch: {
    getDataEnd() {
      if (this.getDataEnd === 2) {
        this.renderSelectList()
      }
    },
    currentScene(scene) {
      if (scene && scene.sceneBoxId) {
        this.getAccentAndDomain() // 获取配置列表
        this.getAacConf() // 获取当前配置值
        this.getAsrLanguageEntRelation()
      }
    },
  },
  methods: {
    /**
     * 修改下拉选项时遍历参数的关系，遍历其他三个下拉框，如果符合条件只有1个可选值的话需要置为默认值不可修改。
     */
    changeOption(val) {
      const valArr = val.split(',')

      this.form.language = valArr[1]
      this.form.isFar = valArr[2]
      this.form.accent = val == 'sos-tv,zh-cn,0' ? this.form.accent : valArr[3]
      this.form.domain =
        val == 'sos-tv,zh-cn,0'
          ? this.audioLangConfList.find(
              (item) => item.accent === this.form.accent
            ).domain
          : valArr[0]

      this.saveAac()
    },
    changeAccentOption(val) {
      this.form.domain = this.audioLangConfList.find(
        (item) => item.accent === val
      ).domain

      this.saveAac()
    },

    /**
     * 渲染方言、领域、距离的数据
     */
    renderSelectList() {
      // if (!this.accentAndDomain.accent) return
      this.audioConfList = [
        {
          domain: 'sos-tv',
          isFar: '0',
          key: 'sos-tv,zh-cn,0',
          language: 'zh-cn',
          select: true,
          value: '通用-中文-近场',
        },
        ...(this.accentAndDomain.relations || []).map((item) => {
          if (item.value === '电视-中文-远场-普通话') {
            return {
              ...item,
              value: '通用-中文-远场-普通话(定制)',
            }
          } else {
            return item
          }
        }),
      ]
      this.audioLangConfList = this.accentAndDomain.smsRelations
    },

    getAccentAndDomain() {
      let self = this
      const url = this.$config.api.AIUI_BOT_IAT_ACCENTANDDOMAIN
      this.$utils.httpGet(
        url,
        {
          appid: this.appId,
          sceneId: this.currentScene.sceneBoxId,
        },
        {
          success: (res) => {
            if (res.flag) {
              self.accentAndDomain = res.data
              self.getDataEnd = self.getDataEnd + 1
            }
          },
          error: (err) => {
            console.log('page=>>')
            console.log(err)
          },
        }
      )
    },
    getAacConf() {
      let self = this
      this.$utils.httpGet(
        this.$config.api.AIUI_BOT_IAT_GETCONFIG,
        {
          botId: this.currentScene.botBoxId,
        },
        {
          success: (res) => {
            if (res.flag) {
              if (!res.data) {
                self.getDataEnd = self.getDataEnd + 1
                return
              }
              let formInitData = res.data
              if (!formInitData.config) {
                formInitData.config = {}
              }
              // 组合字段拼接
              if (
                res.data.language == 'zh-cn' &&
                (res.data.domain == 'sos-tv' || res.data.domain == 'tv') &&
                res.data.isFar == '0'
              ) {
                formInitData.audioConf = 'sos-tv,zh-cn,0'
              } else {
                formInitData.audioConf = [
                  res.data.domain,
                  res.data.language,
                  res.data.isFar,
                  res.data.accent,
                ].join()
              }
              self.form = formInitData
              if (Object.keys(self.form.config).length) {
                let temp1 =
                  (self.form.config.accent &&
                    JSON.parse(self.form.config.accent)) ||
                  {}
                let temp2 =
                  (self.form.config.domain &&
                    JSON.parse(self.form.config.domain)) ||
                  {}
                self.accentConfigKeys = Object.keys(temp1)
                self.domainConfigKeys = Object.keys(temp2)
              }
              self.getDataEnd = self.getDataEnd + 1
              if (
                res.data.config &&
                res.data.config.hasOwnProperty('translateScene')
              ) {
                self.$emit('setTranslateScene', res.data.config.translateScene)
              }
            } else {
              this.$message.error(res.desc)
            }
          },
          error: (err) => {
            console.log('page=>>')
            console.log(err)
          },
        }
      )
    },
    getAsrLanguageEntRelation() {
      let self = this
      this.$utils.httpGet(
        this.$config.api.AIUI_DIST_ASR_LANGUAGE_ENT_RELATION,
        {
          appid: this.appId,
          sceneId: this.currentScene.sceneBoxId,
        },
        {
          success: (res) => {
            self.asrRelation = res.data
          },
          error: (err) => {
            console.log('page=>>')
            console.log(err)
          },
        }
      )
    },

    saveAac() {
      let data = {
        botId: this.currentScene.botBoxId,
        platform: this.appInfo.platform,
        language: this.form.language,
        isFar: this.form.isFar,
        accent: this.form.accent,
        domain: this.form.domain,
        ptt: this.form.ptt || '0',
        nunum: this.form.nunum,
        dwa: this.form.dwa,
      }
      let self = this
      this.$utils.httpPost(
        this.$config.api.AIUI_BOT_IAT_SAVECONFIG,
        JSON.stringify(data),
        {
          config: {
            headers: {
              'Content-Type': 'application/json;charset=UTF-8',
            },
          },
          success: (res) => {
            if (res.flag) {
            } else {
            }
          },
          error: (err) => {},
        }
      )
    },
  },
}
</script>
<style lang="scss" scoped>
@import '../common.scss';
</style>
