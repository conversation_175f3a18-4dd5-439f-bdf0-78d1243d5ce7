<template>
  <div class="form-row-container">
    <el-row :gutter="16" class="form-row" style="margin-bottom: 20px">
      <el-col :span="area === 'toolRequestInput' ? 4 : 8">
        <el-form-item
          :prop="fieldProp('name')"
          :rules="nameRules"
          :style="{ paddingLeft: `${nestingLevel * 12}px` }"
          label-width="0px"
        >
          <el-input
            v-model="localData.name"
            placeholder="参数名称"
            :disabled="fatherType === 'array'"
            @input="handleChange"
          />
        </el-form-item>
      </el-col>

      <el-col :span="area === 'toolRequestInput' ? 4 : 8">
        <el-form-item
          :prop="fieldProp('description')"
          :rules="[
            { required: true, message: '请输入参数描述', trigger: 'blur' },
          ]"
          label-width="0px"
        >
          <el-input
            v-model="localData.description"
            placeholder="参数描述"
            @input="handleChange"
          />
        </el-form-item>
      </el-col>

      <el-col :span="area === 'toolRequestInput' ? 3 : 4">
        <el-form-item
          :prop="fieldProp('type')"
          :rules="[
            { required: true, message: '请选择参数类型', trigger: 'change' },
          ]"
          label-width="0px"
        >
          <el-select
            v-model="localData.type"
            placeholder="参数类型"
            @change="handleTypeChange"
          >
            <el-option label="string" value="string" />
            <el-option label="number" value="number" />
            <el-option label="integer" value="integer" />
            <el-option label="boolean" value="boolean" />
            <el-option
              v-if="fatherType !== 'array'"
              label="array"
              value="array"
            />
            <el-option label="object" value="object" />
          </el-select>
        </el-form-item>
      </el-col>

      <div v-show="area === 'toolRequestInput'">
        <el-col :span="3" class="fixed-col">
          <el-form-item
            v-show="
              nestingLevel === 0 &&
              fatherType !== 'object' &&
              !localData.arraySon
            "
            :prop="fieldProp('location')"
            label-width="0px"
            :rules="locationRules"
          >
            <el-select
              v-model="localData.location"
              placeholder="传入方法"
              @change="handleChange"
            >
              <el-option label="query" value="query" />
              <el-option label="body" value="body" />
              <el-option label="path" value="path" />
              <el-option label="header" value="header" />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="3" class="fixed-col">
          <el-form-item
            v-show="fatherType !== 'array'"
            :prop="fieldProp('required')"
            label-width="0px"
          >
            <el-checkbox v-model="localData.required" @change="handleChange" />
          </el-form-item>
        </el-col>

        <el-col :span="3" class="fixed-col">
          <template>
            <el-form-item
              v-if="localData.type !== 'object' && localData.type !== 'array'"
              :prop="fieldProp('defaultValue')"
              :rules="defaultValueRules"
              :style="{
                visibility: localData.arraySon ? 'hidden' : 'visible',
              }"
              label-width="0px"
            >
              <el-input
                v-model="localData.defaultValue"
                placeholder="默认值"
                @input="handleChange"
              />
            </el-form-item>
          </template>

          <template>
            <el-form-item
              v-if="localData.type === 'array' && !localData.arraySon"
              :prop="fieldProp('defaultValue')"
              label-width="0px"
            >
              <el-button
                @click="handleArrayEdit"
                style="width: 100px; padding: left 0px"
              >
                编辑数组参数
              </el-button>
            </el-form-item>
          </template>
        </el-col>
      </div>

      <el-col :span="2">
        <el-form-item :prop="fieldProp('open')" label-width="0px">
          <el-switch v-model="localData.open" @change="handleChange" />
        </el-form-item>
      </el-col>

      <el-col :span="2" class="fixed-col">
        <el-form-item label-width="0px">
          <div class="form-actions">
            <i
              v-show="localData.type === 'object'"
              class="el-icon-circle-plus"
              @click="handleAddChild('object')"
            />
            <i
              v-show="fatherType !== 'array'"
              class="el-icon-remove"
              @click="handleRemove"
            />
          </div>
        </el-form-item>
      </el-col>
    </el-row>

    <template v-if="hasChildren && !collapsed">
      <el-form-item :prop="fieldProp('children')" label-width="0px">
        <div v-for="(child, index) in localData.children" :key="child.id">
          <template v-if="!(localData.type === 'array' && index !== 0)">
            <form-row
              :ref="`child-${index}`"
              :form-data="child"
              :path="[...path, 'children', index]"
              :nesting-level="nestingLevel + 1"
              :area="area"
              :father-type="localData.type"
              @change="handleChildChange(index, $event)"
              @remove-child="handleRemoveChild(index)"
            />
          </template>
        </div>
      </el-form-item>
    </template>
  </div>
</template>

<script>
import { v4 as uuidv4 } from 'uuid'
export default {
  name: 'FormRow',
  props: {
    formData: {
      type: Object,
      required: true,
    },
    path: {
      type: Array,
      default: () => [],
    },
    nestingLevel: {
      type: Number,
      default: 0,
    },
    area: {
      type: String,
      required: true,
      default: 'toolRequestInput',
    },
    fatherType: {
      type: String,
      default: '',
    },
  },
  data() {
    const validateName = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入参数名称'))
        return
      }

      if (value.length > 20) {
        callback(new Error('只能输入20个以内的字符'))
        return
      }

      if (this.fatherType === 'array') {
        callback()
        return
      }

      if (!/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(value)) {
        callback(new Error('包含字母、数字或下划线，并且以字母或下划线开头'))
        return
      }

      callback()
    }

    return {
      collapsed: false,
      localData: JSON.parse(JSON.stringify(this.formData)),
      nameRules: [
        {
          required: true,
          validator: validateName,
          trigger: ['blur', 'change'],
        },
      ],
      defaultValueRules: [
        {
          required: this.formData.required,
          message: '请输入默认值',
          trigger: 'blur',
        },
      ],
    }
  },
  computed: {
    isCollapsible() {
      return this.localData.type === 'object' || this.localData.type === 'array'
    },
    hasChildren() {
      return (
        (this.localData.type === 'object' || this.localData.type === 'array') &&
        this.localData.children &&
        this.localData.children.length > 0
      )
    },

    locationRules() {
      // 如果是嵌套子项或者是数组子项，不需要校验location
      if (
        this.nestingLevel > 0 ||
        this.fatherType === 'array' ||
        this.localData.arraySon
      ) {
        return []
      }

      return [
        {
          required: true,
          message: '请选择传入方法',
          trigger: 'change',
        },
      ]
    },
  },
  watch: {
    formData: {
      deep: true,
      handler(newVal) {
        this.localData = JSON.parse(JSON.stringify(newVal))
      },
    },
  },
  methods: {
    fieldProp(fieldName) {
      return this.path.join('.') + '.' + fieldName
    },

    handleChange() {
      this.$emit('change', this.localData)
    },

    handleTypeChange(val) {
      this.localData.children = undefined
      if (val === 'object') {
        // 对象类型初始化带空children数组
        this.localData.children = []
        this.handleAddChild('object')
      } else if (val === 'array') {
        // 数组类型初始化不带children
        this.handleAddChild('array')
      }

      this.handleChange()
    },

    handleAddChild(type) {
      const baseFields = {
        id: uuidv4(),
        open: true,
        startDisabled: false,
        nameErrMsg: '', // 确保每个对象都有
        descriptionErrMsg: '', // 确保每个对象都有
      }

      if (type === 'object') {
        const childType = this.localData.children?.[0]?.type || 'string'

        const newChild = {
          ...baseFields,
          name: '',
          description: '',
          type: childType,
          required: true,
          fatherType: 'object',
          arraySon: false,
        }

        // 只有子项是object类型时才初始化children
        if (childType === 'object') {
          newChild.children = []
        }

        if (!this.localData.children) {
          this.localData.children = []
        }
        this.localData.children.push(newChild)
      } else if (type === 'array') {
        // 数组子项永远不带children
        const newChild = {
          ...baseFields,
          name: '[Array Item]',
          type: 'string',
          required: false,
          fatherType: 'array',
          arraySon: true,
        }
        if (!this.localData.children) {
          this.localData.children = []
        }
        this.localData.children.push(newChild)
      }

      this.handleChange()
    },

    handleRemove() {
      this.$emit('remove-child', this.path)
    },

    handleRemoveChild(index) {
      this.localData.children.splice(index, 1)
      this.handleChange()
    },

    handleChildChange(index, newChildData) {
      this.$set(this.localData.children, index, newChildData)
      this.handleChange()
    },

    checkArray() {
      if (this.localData.arraySon) {
        // 实现清空父节点children数据的逻辑
      }
    },

    handleArrayEdit() {
      if (!this.localData.children || this.localData.children.length === 0) {
        // 初始化数组子项
        this.localData.children = [
          {
            id: uuidv4(),
            name: '[Array Item]',
            type: 'string',
            defaultValue: '',
            fatherType: 'array',
            arraySon: true,
            nameErrMsg: '', // 保持一致
            descriptionErrMsg: '', // 保持一致
          },
        ]
      }
      this.$emit('edit-array', {
        path: this.path,
        data: this.localData,
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.form-row-container {
  .form-row {
    .el-col .el-form-item {
      margin-bottom: 0px;
    }
  }
}
.fixed-col {
  min-height: 40px; // 保持高度一致
  position: relative; // 为绝对定位占位元素做准备
}
</style>
