class TypeWriter {
  constructor(onConsume, onFinish) {
    this.queue = []
    this.consuming = false
    this.finished = false
    this.timer = null
    this.onConsume = onConsume
    this.onFinish = onFinish
  }

  // 输出速度动态控制
  dynamicSpeed() {
    // const speed = 2000 / this.queue.length
    // return speed > 200 ? 200 : speed
    return 30
  }

  // 添加字符串到队列
  add(data) {
    if (!data || !data.text) return
    const messages = data.text.split('').map((txt) => {
      return {
        ...data,
        text: txt,
      }
    })
    this.queue.push(...messages)
  }

  // 消费
  consume() {
    if (this.queue.length > 0) {
      const msg = this.queue.shift()
      msg && this.onConsume(msg)
    } else if (this.finished) {
      this.done()
    }
  }

  // 消费下一个
  next() {
    this.consume()
    // 根据队列中字符的数量来设置消耗每一帧的速度，用定时器消耗
    this.timer = setTimeout(() => {
      // this.consume()
      if (this.consuming) {
        this.next()
      }
    }, this.dynamicSpeed())
  }

  // 开始消费队列
  start() {
    if (!this.consuming) {
      this.finished = false
      this.consuming = true
      this.next()
    }
  }

  // 结束消费队列
  done() {
    this.consuming = false
    clearTimeout(this.timer)
    // 把queue中剩下的字符一次性消费
    let mergedMessages = this._mergeMessages(this.queue, 'logId')
    mergedMessages.forEach((msg) => {
      this.onConsume(msg)
    })
    this.queue = []
    this.onFinish()
  }

  end() {
    this.finished = true
  }

  _mergeMessages(messages, id) {
    const messageMap = new Map()

    messages.forEach((message) => {
      if (messageMap.has(message[id])) {
        messageMap.get(message[id]).text += message.text
      } else {
        messageMap.set(message[id], { ...message })
      }
    })

    return Array.from(messageMap.values())
  }
}

export default TypeWriter
//   // Example usage:
//   const typewriter = new TypeWriter((str) => {
//     console.log(str); // 每次消费一个字符时执行的回调
//   });

//   typewriter.add("Hello, World!");
//   typewriter.start();
