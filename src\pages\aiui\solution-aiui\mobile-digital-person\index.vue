<template>
  <div class="main_content">
    <section class="main_content_banner">
      <video
        class="banner_video"
        autoplay
        muted
        loop
        playsinline
        preload="auto"
      >
        <source
          src="@/assets/images/solution/mobile-digital-person/video-banner.mp4"
          type="video/mp4"
        />
        <!-- 如果浏览器不支持视频，显示原来的背景图 -->
      </video>
      <div class="banner_text_wrapper">
        <div class="banner_text">
          <div class="banner_text_title gradient_font">
            移动数字人<br />智能导览交互新体验
          </div>
          <div class="info">深度赋能智能接待、动线指引、场景化导览</div>
        </div>
        <div class="banner_text_button_wrap">
          <div class="banner_text_button" @click="toConsole">立即体验</div>
        </div>
      </div>
    </section>

    <div class="section section1">
      <div class="section_title">应用场景</div>
      <div class="section_content">
        <div
          class="content_item"
          v-for="(item, index) in applicationScenarios"
          :key="index"
        >
          <div class="item_img">
            <img :src="item.imgSrc" :alt="item.title" />
          </div>
          <div class="item_title">{{ item.title }}</div>
        </div>
      </div>
    </div>

    <div class="physical_picture_wrap">
      <div class="physical_picture_title">实物图片</div>
    </div>

    <div class="scheme_introduction_wrap">
      <h2>方案介绍</h2>
      <div class="scheme_introduction_main">
        <div class="scheme_introduction_item" style="margin-bottom: 100px">
          <div class="introduction_left">
            <div class="introduction_left_title">
              <img
                style="width: 73px; height: 73px"
                src="@/assets/images/solution/mobile-digital-person/scheme-introduction-icon01.png"
                alt=""
              />
              <span style="padding-top: 7px" class="gradient_font"
                >自主移动导览</span
              >
            </div>
            <div class="introduction_left_desc">
              搭载双向激光雷达和RGB-D相机<br />自由部署站点、规划路径，实现地图快速构建<br />
              自主移动、站点播报、自动避障、自行充电
            </div>
          </div>
          <img
            src="@/assets/images/solution/mobile-digital-person/scheme-introduction-img01.png"
            style="width: 676px; height: 333px"
          />
        </div>

        <div
          class="scheme_introduction_item"
          style="flex-direction: row-reverse"
        >
          <div class="introduction_left">
            <div class="introduction_left_title">
              <img
                style="width: 38px; height: 36px"
                src="@/assets/images/solution/mobile-digital-person/scheme-introduction-icon02.png"
                alt=""
              />
              <span style="padding-top: 4px" class="gradient_font"
                >数字人智能交互</span
              >
            </div>
            <div class="introduction_left_desc" style="padding-left: 10px">
              降本增效，全天候服务<br />多模感知，多维表达<br />专属形象定制，专业知识库定制<br />基于星火大模型打造智能交互
            </div>
          </div>
          <img
            src="@/assets/images/solution/mobile-digital-person/scheme-introduction-img02.png"
            style="width: 669px; height: 333px"
          />
        </div>
      </div>
    </div>

    <!-- 方案优势 -->
    <div class="scheme_advantage_wrap">
      <h2>方案优势</h2>
      <div class="scheme_advantage_main">
        <div
          class="item_wrapper"
          style="
            padding: 69px 0;
            background: linear-gradient(90deg, #ffffff 0%, #e8f2ff 100%);
          "
        >
          <div class="scheme_advantage_item" style="max-width: 1000px">
            <div class="advantage_left">
              <div class="advantage_left_title">
                <span>360°声源定位</span>
              </div>
              <div class="advantage_left_desc">
                环形麦克风+窄波束算法，精准识别声源方向<br />结合移动底座，精准转向交互人<br />无需等待设备停靠，随心打断，交互更自由
              </div>
            </div>
            <img
              src="@/assets/images/solution/mobile-digital-person/scheme-advantage-img01.png"
              style="width: 524px; height: 263px"
            />
          </div>
        </div>

        <div
          class="item_wrapper"
          style="
            padding: 68px 0;
            background: linear-gradient(-90deg, #ffffff 0%, #e9f3ff 100%);
          "
        >
          <div
            class="scheme_advantage_item"
            style="flex-direction: row-reverse; max-width: 1050px"
          >
            <div class="advantage_left">
              <div class="advantage_left_title">
                <span>多模态降噪技术</span>
              </div>
              <div class="advantage_left_desc">
                支持人脸检测，唇形识别<br />精准过滤环境噪音和无关人声干扰<br /><span
                  style="margin-left: -10px"
                  >“智慧耳朵”，专注于对话用户</span
                >
              </div>
            </div>
            <img
              src="@/assets/images/solution/mobile-digital-person/scheme-advantage-img02.png"
              style="width: 524px; height: 263px"
            />
          </div>
        </div>

        <div
          class="item_wrapper"
          style="
            padding: 59px 0;
            background: linear-gradient(90deg, #ffffff 0%, #e9f3ff 100%);
          "
        >
          <div class="scheme_advantage_item" style="max-width: 1000px">
            <div class="advantage_left" style="padding-top: 75px">
              <div class="advantage_left_title">
                <span>OLED透明显示</span>
              </div>
              <div class="advantage_left_desc">
                55寸OLED透明屏<br />更加沉浸的导览体验
              </div>
            </div>
            <img
              src="@/assets/images/solution/mobile-digital-person/scheme-advantage-img03.png"
              style="width: 524px; height: 263px"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 硬件接口展示 -->
    <div class="hardware_parameters_wrap">
      <div class="hardware_parameters_title">
        <h2>硬件参数</h2>
        <div>移动数字人规格参数</div>
      </div>
      <div class="parameters_list">
        <div
          class="parameters_item"
          v-for="(item, index) in hardwareSpecs"
          :key="index"
        >
          <div class="icon_wrap">
            <img
              :src="item.imgSrc"
              :style="{ width: item.imgWidth, height: item.imgHeight }"
              alt=""
            />
          </div>
          <div class="desc_wrap">
            <div
              v-for="(line, i) in item.desc.split('\n')"
              :key="i"
              class="desc_line"
            >
              {{ line }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 合作咨询 -->
    <div class="cooperation_consulting_wrapper">
      <div class="banner_text">
        <h2 class="gradient_font">合作咨询</h2>
        <p class="banner_text_content">提交信息 我们会尽快与您联系</p>
        <div class="banner_text_button" @click="toConsole">申请合作</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AiuiWebChildToys',

  data() {
    return {
      applicationScenarios: [
        {
          title: '商场',
          imgSrc: require('@/assets/images/solution/mobile-digital-person/application-scenarios-01.png'),
        },
        {
          title: '展厅',
          imgSrc: require('@/assets/images/solution/mobile-digital-person/application-scenarios-02.png'),
        },
        {
          title: '博物馆',
          imgSrc: require('@/assets/images/solution/mobile-digital-person/application-scenarios-03.png'),
        },
        {
          title: '火车站',
          imgSrc: require('@/assets/images/solution/mobile-digital-person/application-scenarios-04.png'),
        },
        {
          title: '政务大厅',
          imgSrc: require('@/assets/images/solution/mobile-digital-person/application-scenarios-05.png'),
        },
      ],
      microphoneTypes: [
        {
          title: '线性2麦',
          imgSrc: require('@/assets/images/solution/offline-interaction/microphone-type1.png'),
          descriptions: ['专为近场交互优化', '适用于桌面级设备'],
        },
        {
          title: '线性4麦',
          imgSrc: require('@/assets/images/solution/offline-interaction/microphone-type2.png'),
          descriptions: ['前向增强拾音', '适配高度>150cm设备'],
        },
        {
          title: '环形6麦',
          imgSrc: require('@/assets/images/solution/offline-interaction/microphone-type3.png'),
          descriptions: ['360°全向收音', '适配高度<150cm设备'],
        },
      ],
      hardwareSpecs: [
        {
          desc: '显示尺寸\n55寸',
          imgSrc: require('@/assets/images/solution/mobile-digital-person/hardware-parameters-icon01.png'),
          imgWidth: '107px',
          imgHeight: '107px',
        },
        {
          desc: '显示分辨率\n1920*1080',
          imgSrc: require('@/assets/images/solution/mobile-digital-person/hardware-parameters-icon02.png'),
          imgWidth: '107px',
          imgHeight: '107px',
        },
        {
          desc: '显示亮度\n200-600cd/m',
          imgSrc: require('@/assets/images/solution/mobile-digital-person/hardware-parameters-icon03.png'),
          imgWidth: '91px',
          imgHeight: '90px',
        },
        {
          desc: '对比度\n150000:1',
          imgSrc: require('@/assets/images/solution/mobile-digital-person/hardware-parameters-icon04.png'),
          imgWidth: '97px',
          imgHeight: '97px',
        },
        {
          desc: '外形尺寸\n1755*800*559(mm)',
          imgSrc: require('@/assets/images/solution/mobile-digital-person/hardware-parameters-icon05.png'),
          imgWidth: '104px',
          imgHeight: '104px',
        },
        {
          desc: '待机时长\n10H',
          imgSrc: require('@/assets/images/solution/mobile-digital-person/hardware-parameters-icon06.png'),
          imgWidth: '91px',
          imgHeight: '91px',
        },
        {
          desc: '整机功耗\n<200W',
          imgSrc: require('@/assets/images/solution/mobile-digital-person/hardware-parameters-icon07.png'),
          imgWidth: '118px',
          imgHeight: '118px',
        },
        {
          desc: '工作温度\n0℃~40℃',
          imgSrc: require('@/assets/images/solution/mobile-digital-person/hardware-parameters-icon08.png'),
          imgWidth: '92px',
          imgHeight: '91px',
        },
      ],
    }
  },

  mounted() {},

  methods: {
    toConsole() {
      // 判断是否有百度SEM营销参数，有的话，提交表单页需要带上
      const search = window.location.search
      if (search) {
        window.open(`/solution/apply/45${search}`)
      } else {
        window.open('/solution/apply/45')
      }
    },

    toBuy() {
      window.open('https://www.aifuwus.com/onstage/cmddetail?id=4434')
    },
  },
}
</script>

<style lang="scss" scoped>
.main_content {
  background-color: #ffff;
  .gradient_font {
    background: -webkit-linear-gradient(0deg, #045dea, #04c0fa 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-weight: 600;
  }
  .banner_text_button {
    font-size: 16px;
    text-align: center;
    font-weight: 400;
    width: 133px;
    height: 44px;
    line-height: 44px;
    background: linear-gradient(90deg, #2670f0, #38b9f8 100%);
    border-radius: 7px;
    color: #fff;
    cursor: pointer;
  }
  .main_content_banner {
    position: relative;
    height: 504px;
    overflow: hidden;
    width: 100%;
    /* 保留原背景图作为视频加载失败时的备用方案 */
    background: url(~@A/images/solution/mobile-digital-person/banner.jpg) center
      no-repeat;
    background-size: cover;
  }

  .banner_video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    z-index: 1;
  }
  .banner_text_button_wrap {
    display: flex;
    gap: 20px;
  }
  .banner_text_wrapper {
    position: relative;
    max-width: 1200px;
    color: #fff;
    height: 100%;
    margin: auto;
    z-index: 2;

    .banner_text {
      width: 500px;

      margin: 110px 0 38px 0;
      .banner_text_title {
        font-size: 42px;
        letter-spacing: 1px;
      }
      .info {
        font-size: 20.3px;
        color: #000000;
        margin-top: 15px;
      }
    }

    h2 {
      color: #181818;
      padding-top: 100px;
      margin-bottom: 29px;
      font-size: 48px;
      font-weight: 600;
      font-family: PingFang SC, PingFang SC-Semibold;
      line-height: 48px;
    }
    p {
      font-size: 18px;
      margin-bottom: 74px;
    }
  }

  .section {
    padding: 60px 0 90px;
    text-align: center;
    background-color: #f8f8f8;
    .section_title {
      margin-bottom: 40px;
      font-size: 34px;
      color: #000000;
    }
    .section_content {
      display: flex;
      justify-content: center;
      gap: 20px;
      flex-wrap: wrap;
    }
    .content_item {
      background-color: #fff;
      padding-bottom: 25px;
      border-radius: 12px;
      .item_img {
        img {
          width: 223px;
          height: 300px;
          object-fit: cover;
          display: block;
        }
      }

      .item_title {
        padding-left: 20px;
        font-size: 18px;
        text-align: left;
        padding-top: 15px;
      }
    }
  }

  .physical_picture_wrap {
    height: 772px;
    background: url(~@A/images/solution/mobile-digital-person/physical-picture.png)
      center no-repeat;
    background-size: cover;
    .physical_picture_title {
      padding: 100px 0 44px;
      font-size: 40px;
      text-align: CENTER;
      color: #000000;
    }
  }

  .scheme_introduction_wrap {
    background: url(~@A/images/solution/mobile-digital-person/scheme-introduction-bg.jpg)
      center no-repeat;
    background-size: cover;
    padding-bottom: 150px;
    h2 {
      padding: 100px 0 50px;
      font-size: 40px;
      text-align: CENTER;
      color: #000000;
    }
    .scheme_introduction_main {
      max-width: 1200px;
      margin: auto;
      .scheme_introduction_item {
        display: flex;
        justify-content: space-between;

        .introduction_left {
          padding-top: 30px;
          .introduction_left_title {
            display: flex;
            align-items: center;
            gap: 14px;
            margin-bottom: 15px;
            span {
              font-size: 30px;
              letter-spacing: 2px;
            }
          }
          .introduction_left_desc {
            font-size: 18px;
            color: #999999;
            line-height: 45px;
            letter-spacing: 1px;
          }
        }
      }
    }
  }

  // 方案优势
  .scheme_advantage_wrap {
    h2 {
      padding-bottom: 50px;
      font-size: 40px;
      text-align: CENTER;
      color: #000000;
    }
    .scheme_advantage_main {
      .item_wrapper {
        .scheme_advantage_item {
          margin: auto;
          display: flex;
          justify-content: space-between;

          .advantage_left {
            padding-top: 30px;
            .advantage_left_title {
              display: flex;
              align-items: center;
              gap: 14px;
              margin-bottom: 26px;
              span {
                font-size: 30px;
                letter-spacing: 2px;
              }
            }
            .advantage_left_desc {
              font-size: 18px;
              color: #999999;
              line-height: 45px;
              letter-spacing: 1px;
            }
          }
        }
      }
    }
  }

  .hardware_parameters_wrap {
    max-width: 1400px;
    margin: auto;
    .hardware_parameters_title {
      padding: 87px 0 50px 0;
      text-align: CENTER;

      h2 {
        font-size: 40px;
        color: #000000;
      }
      div {
        font-size: 20px;
        color: #656464;
        letter-spacing: 2px;
        margin-top: 10px;
      }
    }
    .parameters_list {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      grid-template-rows: repeat(2, 1fr);
      gap: 40px 0;

      justify-items: center;
      align-items: center;
    }
    .parameters_item {
      display: flex;
      flex-direction: column;
      align-items: center;
      min-width: 180px;
      min-height: 180px;
      .icon_wrap {
        margin-bottom: 25px;
        width: 118px;
        height: 118px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .desc_wrap {
        text-align: center;
        .desc_line {
          font-size: 20px;
          color: #747474;
          &:first-child {
            margin-bottom: 5px;
          }
        }
      }
    }
  }

  .cooperation_consulting_wrapper {
    background: url(~@A/images/solution/mobile-digital-person/collaboration-consultation.jpg)
      center no-repeat;
    background-size: cover;
    height: 359px;
    overflow: hidden;
    width: 100%;
    margin-top: 100px;
    .banner_text {
      max-width: 1200px;
      height: 100%;
      margin: auto;
      text-align: center;
      padding-top: 100px;
      &_button {
        margin: auto;
      }
      h2 {
        color: #181818;
        font-size: 36px;
        letter-spacing: 3px;
      }
      p {
        font-size: 18px;
        margin: 10px 0 22px 0;
        letter-spacing: 1px;
      }

      .banner_text_content {
        font-size: 16px;
        font-family: SourceHanSansSC-Regular, SourceHanSansSC;
        font-weight: 400;
        color: #444444;
        line-height: 30px;
      }
    }
  }
}
</style>
