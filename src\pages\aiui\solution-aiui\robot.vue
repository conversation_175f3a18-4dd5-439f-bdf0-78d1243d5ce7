<template>
  <div class="solution-robot main-content">
    <section class="main-content-banner">
      <div class="banner-text">
        <h2>服务机器人解决方案</h2>
        <p class="banner-text-content">
          围绕公共场所机器人服务场景，提供&nbsp;<span style="font-weight: bold"
            >语音交互、导航定位、整机交互</span
          ><br />
          整体解决方案，助力机器人厂商快速完成核心能力集成及产品交付。
        </p>
        <div class="banner-text-button" @click="toConsole">合作咨询</div>
      </div>
    </section>
    <div class="section section-what">
      <div class="video-section">
        <div
          :class="['vdo-area', 'bg-new', 'vdo-area-active']"
          @click="playVideo('https://aiui-res.cn-bj.ufileos.com/market.mp4')"
        >
          <div class="vdo-btn">
            <i class="fas fa-play"></i>
          </div>
        </div>
      </div>
      <div class="decoration decoration1"></div>
      <div class="decoration decoration2"></div>
    </div>
    <section class="section section-1" style="padding: 100px 0">
      <div class="section-title">
        <i class="arrow arrow-left"></i
        ><span class="section-title-bold">应用场景</span
        ><i class="arrow arrow-right"></i>
      </div>

      <ul class="product-list">
        <li
          v-for="(item, index) in productList"
          :key="index"
          :class="item.klass"
        >
          <div class="desc-wrap">
            <h2>{{ item.name }}</h2>
            <ul class="icons-wrap">
              <li v-for="(it, i) in item.scenes" :key="i">
                <div class="cell-title">{{ it.name }}</div>
                <div :class="it.icon"></div>
              </li>

              <li>。。。</li>
            </ul>
          </div>
        </li>
      </ul>
    </section>

    <div class="section center-section odds-section" style="padding-top: 100px">
      <div class="section-title" style="margin-bottom: 70px">
        <i class="arrow arrow-left"></i
        ><span class="section-title-bold">方案介绍</span
        ><i class="arrow arrow-right"></i>
      </div>
      <div class="section-content">
        <div class="character-wrap">
          <div class="character-item unset-height">
            <p class="item-title">USB声卡，快速完成录音、回采</p>
            <p class="item-desc">
              外部音频采集<br />内部喇叭信号回采<br />兼容线性4、6麦及环形6麦阵列<br />USB口输出数字音频
            </p>
          </div>
          <div class="character-item bg1"></div>
        </div>

        <div class="character-wrap">
          <div class="character-item bg2"></div>
          <div
            class="character-item unset-height"
            style="width: 510px; margin-left: 190px; padding-top: 120px"
          >
            <p class="item-title">本地多能力组合，SDK一体化交付</p>
            <p class="item-desc">
              多模态能力融合<br />全链路离线语音交互<br />定制降噪模型
            </p>
          </div>
        </div>

        <div class="character-wrap">
          <div class="character-item" style="width: 550px; margin-right: 60px">
            <p class="item-title">AIUI全链路语音服务，覆盖多样场景</p>
            <p class="item-desc">
              200+官方技能<br />
              10+行业语义<br />
              15个海外多语种能力
            </p>
          </div>
          <div class="character-item bg3"></div>
        </div>

        <div
          class="character-wrap"
          style="background: #f4f7f9; padding-top: 80px"
        >
          <div class="character-item bg4"></div>
          <div
            class="character-item"
            style="margin-left: 180px; padding-top: 180px"
          >
            <p class="item-title">导航定位</p>
            <p class="item-desc">
              提供移动机器人导航定位核心算法<br />实现路径规划、自主回充等核心功能
            </p>
          </div>
        </div>
        <div
          class="character-wrap character-robot-bg"
          style="padding-top: 90px"
        >
          <p class="robot-title">面向多业务场景，提供整机交付解决方案</p>
          <div
            class="character-robot"
            style="padding-top: 40px; padding-bottom: 80px"
          >
            <div class="robot"></div>
            <div class="arrows">
              <div class="arrow"></div>
              <div class="arrow"></div>
              <div class="arrow"></div>
            </div>
            <div class="sols">
              <div class="sols-content">
                <div class="sol-cell">
                  <span>售前咨询</span>
                  <ul>
                    <li>
                      <div class="sol-icon sol-icon-1"></div>
                      <p>解决方案</p>
                    </li>
                    <li>
                      <div class="sol-icon sol-icon-2"></div>
                      <p>产品手册</p>
                    </li>
                    <li>
                      <div class="sol-icon sol-icon-3"></div>
                      <p>服务案例</p>
                    </li>
                  </ul>
                </div>
                <div class="sol-cell">
                  <span>整机交付</span>
                  <ul>
                    <li>
                      <div class="sol-icon sol-icon-4"></div>
                      <p>产品选型</p>
                    </li>
                    <li>
                      <div class="sol-icon sol-icon-5"></div>
                      <p>机器配置</p>
                    </li>
                    <li>
                      <div class="sol-icon sol-icon-6"></div>
                      <p>产品调试</p>
                    </li>
                  </ul>
                </div>
                <div class="sol-cell">
                  <span>售后服务</span>
                  <ul>
                    <li>
                      <div class="sol-icon sol-icon-7"></div>
                      <p>实施调试</p>
                    </li>
                    <li>
                      <div class="sol-icon sol-icon-8"></div>
                      <p>产品培训</p>
                    </li>
                    <li>
                      <div class="sol-icon sol-icon-9"></div>
                      <p>售后咨询</p>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="section section-advantage">
      <div class="section-title">
        <i class="arrow arrow-left"></i
        ><span class="section-title-bold">方案优势</span
        ><i class="arrow arrow-right"></i>
      </div>
      <div class="section-desc" style="text-align: center; margin: 80px 0">
        多技术融合，助力机器人高噪场景交互更简单
      </div>
      <div class="section-content section-content-1">
        <div class="section-robot-bg"></div>
        <div class="section-robot-info">
          <div class="unit">
            <p>超指向波束</p>
            <ul>
              <li>
                <div class="icon-teck icon-1"></div>
                <p>45°定向拾音</p>
              </li>
              <li>
                <div class="icon-teck icon-2"></div>
                <p style="transform: translateX(-38px)">两侧噪声抑制比>25dB</p>
              </li>
            </ul>
          </div>
          <div class="unit">
            <p>多模态检测</p>
            <ul>
              <li>
                <div class="icon-teck icon-3"></div>
                <p>支持唇形检测</p>
              </li>
              <li>
                <div class="icon-teck icon-4"></div>
                <p>支持唇语识别</p>
              </li>
            </ul>
          </div>
        </div>
      </div>
      <div class="section-desc" style="text-align: center; margin-top: 57px">
        创新技术，打造自然交互体验
      </div>
      <div
        class="section-content section-content-2"
        style="padding-bottom: 129px"
      >
        <ul>
          <li>
            <div class="icon-adv icon-adv1"></div>
            <p>主动交互</p>
            <p>主动迎宾，始终保持正面交互</p>
          </li>
          <li>
            <div class="icon-adv icon-adv2"></div>
            <p>免唤醒交互</p>
            <p>无需唤醒词，直接对话</p>
          </li>
          <li>
            <div class="icon-adv icon-adv3"></div>
            <p>离线自由说</p>
            <p>无网络交互，支持多语种</p>
          </li>
          <li>
            <div class="icon-adv icon-adv4"></div>
            <p>方言免切换</p>
            <p>支持中粤、中川免切换交互</p>
          </li>
        </ul>
      </div>
    </div>
    <div class="section center-section section-corp">
      <div class="section-title">
        <i class="arrow arrow-left"></i
        ><span class="section-title-bold">合作案例</span
        ><i class="arrow arrow-right"></i>
      </div>
      <div class="section-desc desc-corp" style="text-align: center">
        平台赋能国内超过90%的服务机器人产品
      </div>
      <div class="section-logo-wrap">
        <div class="section-logo"></div>
      </div>
    </div>
    <corp @jump="toConsole">
      <template> 提交信息，我们会尽快与您联系</template>
    </corp>
  </div>
</template>
<script>
import productImg1 from '../../../assets/images/solution/robot/products/1.jpg'
import productImg2 from '../../../assets/images/solution/robot/products/2.jpg'
import productImg3 from '../../../assets/images/solution/robot/products/3.jpg'
import productImg4 from '../../../assets/images/solution/robot/products/4.jpg'
import productImg5 from '../../../assets/images/solution/robot/products/5.jpg'
import productImg6 from '../../../assets/images/solution/robot/products/6.jpg'
import productImg7 from '../../../assets/images/solution/robot/products/7.jpg'
import productImg8 from '../../../assets/images/solution/robot/products/8.jpg'
import corp from '@P/aiui/solution-aiui/components/corp.vue'

import videoPlayer from '@C/videoPlayer/index'

export default {
  layout: 'aiuiHome',
  data() {
    return {
      oneStep: 0,
      oneRowWidth: 0,
      one: {
        first: 0,
        second: 0,
      },
      interval: null,

      productList: [
        {
          name: '迎宾机器人',
          desc: '',
          klass: 'img_domestic_cellphone',
          scenes: [
            { name: '商超', icon: 'icon_business_super' },
            { name: '景区', icon: 'icon_scenic_area' },
            { name: '政企', icon: 'icon_enterprise' },
          ],
        },
        {
          name: '配送机器人',
          desc: '',
          klass: 'img_smartwatch',
          scenes: [
            { name: '餐饮', icon: 'icon_catering' },
            { name: '酒店', icon: 'icon_hotel' },
            { name: '物流', icon: 'icon_logistics' },
          ],
        },
        {
          name: '特种机器人',
          desc: '',
          klass: 'img_instrument_smar_bracelet',
          scenes: [
            { name: '测温', icon: 'icon_thermometry' },
            { name: '巡检', icon: 'icon_inspection' },
            { name: '安防', icon: 'icon_security' },
          ],
        },
        {
          name: '清洁机器人',
          desc: '',
          klass: 'img_sports_camera',
          scenes: [
            { name: '写字楼', icon: 'icon_office_building' },
            { name: '医院', icon: 'icon_hospital' },
            { name: '商城', icon: 'icon_store' },
          ],
        },
      ],
    }
  },
  computed: {
    osPartner() {
      let maps = []
      let colum = [
        '0',
        '-240px',
        '-460px',
        '-690px',
        '665px',
        '888px',
        '440px',
        '205px',
      ]
      let columM = [
        '-6px',
        '-100px',
        '-188px',
        '-280px',
        '-368px',
        '-459px',
        '-547px',
        '-640px',
      ]
      let productsName = [
        '华隆医疗养护机器人',
        '向上儿童机器人',
        '艾娃导诊导医机器人',
        '勇艺达服务机器人',
        '艾利斯政务机器人',
        '艾米客服机器人',
        '小雪人',
        '小卒智能安防机器人',
      ]
      let productsUse = [
        '家用',
        '家用',
        '商用',
        '商用',
        '商用',
        '商用',
        '商用',
        '商用',
      ]
      let productsImgSrc = [
        productImg1,
        productImg2,
        productImg3,
        productImg4,
        productImg5,
        productImg6,
        productImg7,
        productImg8,
      ]
      let start = 690
      let startM = 1000
      for (let i = 0; i < 8; i++) {
        maps.push({
          name: productsName[i],
          use: productsUse[i],
          mapM: `${columM[i]} -${startM}px`,
          imgSrc: productsImgSrc[i],
        })
      }
      return maps
    },
    carouselStyle() {
      return {
        one: {
          first: {
            left: `${this.one.first}px`,
          },
          second: {
            left: `${this.one.second}px`,
          },
        },
      }
    },
  },
  mounted() {
    let self = this
    window.onresize = () => {
      return (() => {
        window.screenWidth = document.body.clientWidth
        self.screenWidth = window.screenWidth
      })()
    }
    this.oneRowWidth =
      (this.$refs.firstRow && this.$refs.firstRow.clientWidth) || 0
    this.beginInterval()
    this.scrollToPoints()
  },
  methods: {
    beginInterval() {
      let self = this
      this.interval = setInterval(function () {
        self.one.first = self.oneStep
        self.one.second = self.oneStep + self.oneRowWidth
        self.oneStep -= 1
        if (self.oneStep + self.oneRowWidth <= 0) {
          self.oneStep = 0
        }
      }, 20)
    },
    scrollToPoints() {
      let anchorName = localStorage.getItem('developKitsAnchor')
      const anchor = document.querySelector(anchorName)
      anchor && anchor.scrollIntoView()
    },
    toConsole() {
      // 判断是否有百度SEM营销参数，有的话，提交表单页需要带上
      const search = window.location.search
      if (search) {
        window.open(`/solution/apply/2${search}`)
      } else {
        window.open('/solution/apply/2')
      }
    },

    getWindowHeight() {
      return 'innerHeight' in window
        ? window.innerHeight
        : document.documentElement.offsetHeight
    },

    playVideo(url) {
      let height = Math.min(this.getWindowHeight() * 0.9, 562)
      let width = parseInt((1920 * height) / 1080)
      videoPlayer({
        width,
        height,
        videoSrc: url,
        videoStyle: {
          width: `${width}px`,
          height: `${height}px`,
          'box-sizing': 'border-box',
          'margin-left': `-${width * 0.5}px`,
          'margin-top': `-${height * 0.5}px`,
        },
      })
    },
  },
  components: { corp },
}
</script>
<style>
.home-main {
  overflow-x: hidden !important;
}
</style>
<style lang="scss" scoped>
/****************new start */
p {
  margin-bottom: 0;
}
.video-section {
  position: relative;
  text-align: center;

  width: 1100px;
  height: 412px;
  margin: 20px auto 0;
  background: url(~@A/images/aiui/main-page/img_video_bg.png) center/100%
    no-repeat;

  .vdo-area {
    // width: 454px;
    // height: 194px;
    background: #fff;
    cursor: pointer;
    position: absolute;
    z-index: 1;
    left: 78px;
    top: 50%;
    transform: translateY(-50%);
    transition: all 0.5s ease-out;
    transition-property: width, height, left, top, transform;
    border-radius: 16px;
  }

  .vdo-btn {
    opacity: 0.8;
    display: none;
  }
  .vdo-area-active {
    border: 2px solid #1f90fe;
    border-radius: 24px;
    box-shadow: #1f90fe 0 0 30px;
    width: 840px;
    height: 358px;
    transition: all 0.5s ease-in;
    transition-property: width, height, left, top, transform;
    background: #fff;
    z-index: 2;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    position: relative;
    .vdo-btn {
      position: absolute;
      z-index: 3;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      display: block;
      width: 80px;
      height: 80px;
      line-height: 80px;
      background: url(~@A/images/aiui/main-page/icon_play.png) center/100%
        no-repeat;
      border-radius: 50%;
      color: #fff;
      font-size: 30px;
      text-shadow: 2px 2px rgba(0, 0, 0, 20%);
      animation: 2s pulse linear infinite;
      z-index: 2;
      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(-45deg, #4486ff, #4c42c5);
        border-radius: 50%;
        opacity: 0;
        transition: 0.4s;
        z-index: -1;
      }
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border-radius: 50%;
        animation: 2s pulse 1s linear infinite;
      }
    }
  }

  .bg-new {
    background: url(~@A/images/solution/robot/new/img_viideo_bg.png) center/101%
      no-repeat;
  }
}
.section-what {
  // padding: 95px 0 100px 0 !important;
  position: relative;
  height: 658px;
  display: flex;
  align-items: center;
}
.decoration {
  position: absolute;
  z-index: 10;
}
.decoration1 {
  background: url(~@A/images/solution/robot/new/bg_element01.png) center/100%
    no-repeat;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 138px;
  height: 538px;
}

.decoration2 {
  background: url(~@A/images/solution/robot/new/bg_element02.png) center/100%
    no-repeat;
  top: 0;
  right: 0;
  width: 451px;
  height: 171px;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(68, 134, 255, 0.5);
  }

  50% {
    box-shadow: 0 0 0 10px rgba(68, 134, 255, 0.5);
  }

  100% {
    box-shadow: 0 0 0 15px transparent;
  }
}
.section-1 {
  background: #f4f7f9;
}
.product-list {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 100px auto 0;

  li {
    position: relative;
    text-align: center;
    width: 288px;
    height: 411px;

    .desc-wrap {
      padding-top: 39px;
    }
    .icons-wrap {
      display: flex;
      flex-wrap: wrap;
      margin-top: 104px;
      padding-left: 39px;
      li {
        width: 100px;
        height: 100px;
        background: #f6f6f6;
        opacity: 0.95;
        border-radius: 10px;

        > div:last-child {
          margin: 0 auto;
        }
        &:nth-child(2),
        &:nth-child(4) {
          margin-left: 10px;
        }
        &:nth-child(3),
        &:nth-child(4) {
          margin-top: 10px;
        }
        &:nth-child(1),
        &:nth-child(2),
        &:nth-child(3) {
          padding-top: 20px;
          .cell-title {
            font-size: 16px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            color: #666666;
            line-height: 16px;
          }
          .icon_business_super {
            margin-top: 18px;
            width: 35px;
            height: 36px;
            background: url(~@A/images/solution/robot/new/icon_business_super.png)
              center/100% no-repeat;
          }
          .icon_scenic_area {
            margin-top: 22px;
            width: 38px;
            height: 32px;
            background: url(~@A/images/solution/robot/new/icon_scenic_area.png)
              center/100% no-repeat;
          }
          .icon_enterprise {
            margin-top: 22px;
            width: 41px;
            height: 32px;
            background: url(~@A/images/solution/robot/new/icon_enterprise.png)
              center/100% no-repeat;
          }

          .icon_catering {
            margin-top: 22px;
            width: 40px;
            height: 32px;
            background: url(~@A/images/solution/robot/new/icon_catering.png)
              center/100% no-repeat;
          }
          .icon_hotel {
            margin-top: 22px;
            width: 39px;
            height: 32px;
            background: url(~@A/images/solution/robot/new/icon_hotel.png)
              center/100% no-repeat;
          }
          .icon_logistics {
            margin-top: 22px;
            width: 38px;
            height: 32px;
            background: url(~@A/images/solution/robot/new/icon_logistics.png)
              center/100% no-repeat;
          }

          .icon_thermometry {
            margin-top: 18px;
            width: 31px;
            height: 36px;
            background: url(~@A/images/solution/robot/new/icon_thermometry.png)
              center/100% no-repeat;
          }
          .icon_inspection {
            margin-top: 18px;
            width: 32px;
            height: 36px;
            background: url(~@A/images/solution/robot/new/icon_inspection.png)
              center/100% no-repeat;
          }
          .icon_security {
            margin-top: 18px;
            width: 38px;
            height: 35px;
            background: url(~@A/images/solution/robot/new/icon_security.png)
              center/100% no-repeat;
          }

          .icon_office_building {
            margin-top: 18px;
            width: 44px;
            height: 35px;
            background: url(~@A/images/solution/robot/new/icon_office_building.png)
              center/100% no-repeat;
          }
          .icon_hospital {
            margin-top: 20px;
            width: 40px;
            height: 34px;
            background: url(~@A/images/solution/robot/new/icon_hospital.png)
              center/100% no-repeat;
          }
          .icon_store {
            margin-top: 18px;
            width: 40px;
            height: 34px;
            background: url(~@A/images/solution/robot/new/icon_store.png)
              center/100% no-repeat;
          }
        }
        &:nth-child(4) {
          font-size: 16px;
          font-family: Microsoft YaHei;
          font-weight: 400;
          color: #666666;
          line-height: 100px;
          text-align: center;
        }
      }
    }
    .overlay {
      display: none;
      width: 100%;
      height: 100%;
      // background: rgba(0, 0, 0, 0.3);
      background-image: linear-gradient(
        0deg,
        rgb(0, 54, 255) 0%,
        rgb(39, 12, 73) 100%
      );
      opacity: 0.502;
      position: absolute;
      top: 0;
      left: 0;
      z-index: 1;
    }

    h1 {
      display: none;
      text-align: left;
      max-width: 25px;
      font-size: 24px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #ffffff;
      line-height: 30px;
      margin: 0 auto;
      position: relative;
      top: 50%;
      transform: translateY(-50%);
    }
    h2 {
      // display: none;
      text-align: center;
      // padding-top: 178px;
      // padding-left: 35px;
      font-size: 21px;
      // font-weight: bold;
      color: #ffffff;
      line-height: 21px;
    }
    p {
      // display: none;
      margin-top: 32px;
      width: 232px;
      font-size: 16px;
      font-weight: 400;
      color: #ffffff;
      line-height: 32px;
      padding-left: 35px;
      text-align: left;
    }

    &.img_domestic_cellphone {
      background: url(~@A/images/solution/robot/new/img_welcome_robot_bg.png)
        center/100% no-repeat;
    }
    &.img_smartwatch {
      background: url(~@A/images/solution/robot/new/img_delivery_robot_bg.png)
        center/100% no-repeat;
    }
    &.img_instrument_smar_bracelet {
      background: url(~@A/images/solution/robot/new/img_specialized_robot_bg.png)
        center/100% no-repeat;
    }
    &.img_sports_camera {
      background: url(~@A/images/solution/robot/new/img_cleaning_robot_bg.png)
        center/100% no-repeat;
    }
  }

  > li + li {
    margin-left: 16px;
  }
}

.section-advantage {
  background: #f4f7f9;
  .section-desc {
    font-size: 34px;
    font-family: Microsoft YaHei;
    font-weight: 400;
    color: #666666;
    line-height: 30px;
  }
}

/****************new end */
.main-content {
  &-banner {
    //  background: url('../../../assets/images/solution/robot/<EMAIL>')
    // center no-repeat;
    background: url(~@A/images/solution/robot/img_robot_banner_bg.png) center
      no-repeat;
    background-size: cover;
    height: 500px;
    overflow: hidden;
    width: 100%;
    .banner-text {
      max-width: 1200px;
      color: #fff;
      height: 100%;
      margin: auto;
      &-button {
        font-size: 16px;
        text-align: center;
        font-weight: 400;
        width: 140px;
        height: 40px;
        line-height: 40px;
        border: 1px solid #fff;
        border-radius: 40px;
        color: #fff;
        cursor: pointer;
        transition: 0.6s;
        // &:hover {
        //   color: #002985;
        //   background: #fff;
        //   transition: 0.3s;
        // }
      }
      h2 {
        color: #fff;
        padding-top: 148px;
        margin-bottom: 29px;
        font-size: 48px;
        font-weight: 500;
        line-height: 48px;
      }
      p {
        font-size: 18px;
        margin-bottom: 74px;
      }

      .banner-text-content {
        width: 570px;
        font-size: 16px;
        font-family: SourceHanSansSC-Regular, SourceHanSansSC;
        font-weight: 400;
        color: rgba(255, 255, 255, 0.86);
        line-height: 30px;
      }
    }
  }
}
.banner-wrap {
  width: 100%;
  min-width: 1000px;
  padding-top: 60px;
  background: linear-gradient(
    315deg,
    rgba(52, 54, 85, 1) 0%,
    rgba(30, 29, 57, 1) 100%
  );
  background-size: cover;
}
.banner {
  margin: 0 auto;
  padding-top: 112px;
  height: 500px;
  width: 1252px;
  position: relative;
  background: url('../../../assets/images/solution/robot/<EMAIL>')
    center no-repeat;
  background-size: 712px 428px;
  background-position-x: right;
  background-position-y: bottom;
  color: #fff;
}
.banner-title {
  font-size: 48px;
  line-height: 62px;
}
.banner-desc {
  margin: 8px 0 64px;
  width: 528px;
  font-size: 20px;
  letter-spacing: 1px;
  line-height: 32px;
}
.indevice-btn {
  display: block;
  margin-bottom: 100px;
  font-size: 16px;
  text-align: center;
  font-weight: 500;
  width: 200px;
  height: 52px;
  line-height: 52px;
  letter-spacing: 0.5px;
  border: 1px solid #fff;
  border-radius: 2px;
  color: #fff;
  cursor: pointer;
  transition: 0.6s;
  &:hover {
    color: #002985;
    background: #fff;
  }
}
.section {
  // padding: 50px 0 120px;
}
.center-section {
  margin: 0 auto;
  width: 1200px;
}
.section-title {
  text-align: center;
  font-size: 34px;
  font-family: SourceHanSansSC-Medium, SourceHanSansSC;
  font-weight: bold;
  color: #333;
  line-height: 34px;
  position: relative;
  width: 200px;
  margin: 0 auto;

  .arrow {
    width: 160px;
    height: 8px;
    display: inline-block;
    background-repeat: no-repeat;
    background-size: contain;
    position: absolute;
    z-index: 1;
  }
  .arrow-left {
    background-position: left;
    // background-image: url(~@A/images/solution/smart-hardware/arrow-left.png);
    background-image: url(~@A/images/solution/multimodality/img_title_01.png);
    top: 50%;
    left: -160px;
  }
  .arrow-right {
    background-position: right;
    // background-image: url(~@A/images/solution/smart-hardware/arrow-right.png);
    background-image: url(~@A/images/solution/multimodality/img_title_02.png);
    top: 50%;
    right: -160px;
  }
}

.section-title-bold {
  font-size: 34px;
  font-family: Microsoft YaHei;
  font-weight: bold;
  color: #333333;
}
.section-content {
  margin: 0 auto;
  // width: 1200px;
  .character-wrap {
    // width: 1200px;
    text-align: center;
  }
  .character-robot-bg {
    background: url(~@A/images/solution/robot/new/img_bg_machine_delivery.png)
      center/cover no-repeat;
  }
  .character-robot {
    display: flex;
    align-items: center;
    justify-content: center;

    .robot {
      width: 271px;
      height: 470px;
      background: url('../../../assets/images/solution/robot/new/img_robot.png')
        center/100% no-repeat;
    }
    .arrows {
      margin: 0 75px 0 60px;
      .arrow {
        width: 59px;
        height: 61px;
        background: url('../../../assets/images/solution/robot/new/img_arrow.png')
          center/100% no-repeat;
      }
      .arrow + .arrow {
        margin-top: 125px;
      }
    }
    .sols {
      .sols-content {
        background: rgba(221, 236, 249, 0.3);
        margin-top: 50px;
        width: 610px;
        height: 470px;
        // background: #f5f9fd;
        border-radius: 10px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        .sol-cell + .sol-cell {
          margin-top: 60px;
        }
      }
      .sol-cell {
        display: flex;
        align-items: center;
        span {
          font-size: 18px;
          font-family: Microsoft YaHei;
          font-weight: bold;
          color: #333333;
          line-height: 30px;
          margin-right: 69px;
        }
        ul {
          display: flex;
          li {
            p {
              font-size: 16px;
              font-family: Microsoft YaHei;
              font-weight: 400;
              color: #666666;
              line-height: 30px;
            }
          }
          li + li {
            margin-left: 66px;
          }
        }
      }
      .sol-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
      }
      .sol-icon-1 {
        background: url(~@A/images/solution/robot/new/icon_solution.png)
          center/100% no-repeat;
      }

      .sol-icon-2 {
        background: url(~@A/images/solution/robot/new/icon_productmanual.png)
          center/100% no-repeat;
      }
      .sol-icon-3 {
        background: url(~@A/images/solution/robot/new/icon_service_case.png)
          center/100% no-repeat;
      }
      .sol-icon-4 {
        background: url(~@A/images/solution/robot/new/icon_product_selection.png)
          center/100% no-repeat;
      }
      .sol-icon-5 {
        background: url(~@A/images/solution/robot/new/icon_machine_configuration.png)
          center/100% no-repeat;
      }
      .sol-icon-6 {
        background: url(~@A/images/solution/robot/new/icon_Product_debugging.png)
          center/100% no-repeat;
      }
      .sol-icon-7 {
        background: url(~@A/images/solution/robot/new/icon_implementation.png)
          center/100% no-repeat;
      }
      .sol-icon-8 {
        background: url(~@A/images/solution/robot/new/icon_product_training.png)
          center/100% no-repeat;
      }
      .sol-icon-9 {
        background: url(~@A/images/solution/robot/new/icon_afterconsulting.png)
          center/100% no-repeat;
      }
    }
  }
  .robot-title {
    font-size: 34px;
    font-family: Microsoft YaHei;
    font-weight: 400;
    color: #666666;
    line-height: 30px;
    text-align: right;
    max-width: 1080px;
    margin: 0 auto;
  }
}

.section-content-1 {
  padding-bottom: 40px;
  display: flex;
  justify-content: center;
  background: url(~@A/images/solution/robot/new/img_plan_advantage_bg.png)
    center/cover no-repeat;
  .section-robot-bg {
    width: 614px;
    height: 518px;
    background: url(~@A/images/solution/robot/new/img_robot02.png) center/100%
      no-repeat;
    margin-right: 105px;
  }
  .section-robot-info {
    width: 480px;
    height: 517px;
    border: 1px dashed rgba(31, 144, 254, 0.2);
    border-radius: 10px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: #eff5f9;
    .unit {
      > p {
        font-size: 18px;
        font-family: Microsoft YaHei;
        font-weight: bold;
        color: #333333;
        line-height: 30px;
        margin-bottom: 50px;
        text-align: center;
      }
      ul {
        display: flex;
        align-items: center;
        justify-content: center;
        li {
          width: 78px;
          .icon-teck {
            width: 78px;
            height: 78px;
            border-radius: 100%;
          }
          p {
            white-space: nowrap;
            margin-top: 14px;
            color: #666;
            font-size: 16px;
            transform: translateX(-8px);
          }
          .icon-1 {
            background: url(~@A/images/solution/robot/new/icon_pickup.png)
              center/100% no-repeat;
          }
          .icon-2 {
            background: url(~@A/images/solution/robot/new/icon_rejection_ratio.png)
              center/100% no-repeat;
          }
          .icon-3 {
            background: url(~@A/images/solution/robot/new/icon_lip_detection.png)
              center/100% no-repeat;
          }
          .icon-4 {
            background: url(~@A/images/solution/robot/new/icon_lip_recognition.png)
              center/100% no-repeat;
          }
        }
        li + li {
          margin-left: 144px;
        }
      }
    }
    .unit + .unit {
      margin-top: 60px;
    }
  }
}

.section-content-2 {
  ul {
    display: flex;
    justify-content: center;
    margin: 80px auto 0;

    li {
      .icon-adv {
        width: 140px;
        height: 140px;
        border-radius: 100%;
        margin: 0 auto;
      }
      .icon-adv1 {
        background: url(~@A/images/solution/robot/new/icon_active_interaction.png)
          center/100% no-repeat;
      }
      .icon-adv2 {
        background: url(~@A/images/solution/robot/new/icon_free_wake.png)
          center/100% no-repeat;
      }
      .icon-adv3 {
        background: url(~@A/images/solution/robot/new/icon_offline.png)
          center/100% no-repeat;
      }
      .icon-adv4 {
        background: url(~@A/images/solution/robot/new/icon_dialect.png)
          center/100% no-repeat;
      }
      p {
        text-align: center;
        white-space: nowrap;
      }
      p:first-of-type {
        font-size: 18px;
        font-family: Microsoft YaHei;
        font-weight: bold;
        color: #333333;
        margin-top: 58px;
      }
      p:last-of-type {
        font-size: 16px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #999999;
        margin-top: 22px;
      }
    }

    li + li {
      margin-left: 170px;
    }
  }
}

.section-corp {
  margin-top: 110px;
  .desc-corp {
    font-size: 16px;
    font-family: Microsoft YaHei;
    font-weight: 400;
    color: #999999;
    margin-top: 39px;
  }
  .section-logo-wrap {
    width: 1200px;
    height: 150px;
    background: #ffffff;
    border: 2px solid #eff1f1;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 79px;
  }
  .section-logo {
    width: 1067px;
    height: 57px;
    background: url(~@A/images/solution/robot/new/img_logo.png) center/100%
      no-repeat;
  }
}

.section-advantage {
  margin: 0 auto;
  padding-top: 99px;
}

//应用场景
.section-item {
  display: inline-block;
  vertical-align: bottom;
  width: 588px;
  height: 480px;
  text-align: center;
  background: #ccc;
  &:nth-child(2) {
    margin-right: 24px;
  }
}
.hover-animation {
  position: relative;
  overflow: hidden;
  color: #fff;
  .item-title {
    padding: 190px 0 16px;
    transition: ease-out 0.4s;
    font-size: 40px;
    font-weight: 500;
    line-height: 52px;
  }
  .item-desc {
    margin-bottom: 190px;
    font-size: 20px;
    line-height: 32px;
    transition: ease-out 0.4s;
    color: rgba(255, 255, 255, 0.7);
  }
  .item-detail {
    padding: 0 24px;
    font-size: 20px;
    display: flex;
    justify-content: space-between;
    .item-detail-child {
      width: 169px;
      height: 144px;
      color: $text-black;
      background: rgba(255, 255, 255, 1);
      box-shadow: 0px 12px 36px 0px rgba(0, 0, 0, 0.05);
      border-radius: 12px;
      border: 1px solid rgba(242, 245, 247, 1);
      p {
        padding: 24px 24px 14px;
      }
    }

    .detail-child-bg {
      margin-left: 90px;
      width: 64px;
      height: 64px;
      background: url('../../../assets/images/solution/robot/robot-icons.svg');
      background-position-x: 0px;
      background-position-y: 0px;
    }
    .svg2 {
      background-position-x: -64px;
    }
    .svg3 {
      background-position-x: -128px;
    }
    .svg4 {
      background-position-x: -192px;
    }
    .svg5 {
      background-position-x: 128px;
    }
    .svg6 {
      background-position-x: 64px;
    }
  }
  .opacity-gradient {
    position: absolute;
    top: 0;
    height: 256px;
    width: 100%;
    opacity: 0;
    transition: ease-out 0.4s;
    background: linear-gradient(
      180deg,
      rgba(38, 38, 38, 0.3) 0%,
      rgba(38, 38, 38, 0) 100%
    );
  }
  &:hover {
    .item-title {
      padding: 78px 0 16px;
    }
    .item-desc {
      margin-bottom: 78px;
    }
    .opacity-gradient {
      opacity: 1;
    }
  }
}
.business-robot {
  background: url('../../../assets/images/solution/robot/robot-sc-business.jpg')
    center no-repeat;
  background-size: cover;
}
.home-robot {
  background: url('../../../assets/images/solution/robot/robot-sc-home.jpg')
    center no-repeat;
  background-size: cover;
}

//方案优势-start
.odds-section {
  width: unset;
  min-width: 1200px;
  // background-color: $grey1;
}
.character-item {
  display: inline-block;
  width: 540px;
  vertical-align: top;
  margin-bottom: 60px;
  padding-top: 88px;
  height: 280px;
  text-align: left;
  .item-title {
    margin-bottom: 16px;
    font-size: 34px;
    line-height: 40px;
    color: #666;
    margin-bottom: 30px;
  }
  .item-desc {
    font-size: 16px;
    font-family: Microsoft YaHei;
    font-weight: 400;
    color: #999999;
    line-height: 30px;
  }
  // &:nth-child(odd) {
  //   margin-right: 60px;
  // }
  &:nth-child(7),
  &:nth-child(8) {
    margin-bottom: 0;
  }
}
.bg1 {
  width: 660px;
  height: 344px;
  background: url('../../../assets/images/solution/robot/new/img_usb_sound_card.png');
  background-size: 100%;
  background-repeat: no-repeat;
}
.bg2 {
  width: 500px;
  height: 409px;
  background: url('../../../assets/images/solution/robot/new/img_local_multicapability.png');
  background-size: 100%;
  background-repeat: no-repeat;
}
.bg3 {
  width: 579px;
  height: 340px;
  background: url('../../../assets/images/solution/robot/new/img_aiui_whole_link.png');
  background-size: 100%;
  background-repeat: no-repeat;
}
.bg4 {
  width: 519px;
  height: 513px;
  background: url('../../../assets/images/solution/robot/new/img_navigational_positioning.png');
  background-size: contain;
  background-repeat: no-repeat;
}

//硬件接入方式
.hardware-section {
  // background: $text-black;
  background: url(~@A/images/solution/robot/img_hardware_access_bg.png)
    center/cover no-repeat;
  width: unset;
  min-width: 1200px;
  text-align: center;
  color: #fff;
  .left-item {
    background: url('../../../assets/images/solution/robot/robot-hardware1.jpg');
    background-size: cover;
  }
  .right-item {
    background: url('../../../assets/images/solution/robot/robot-hardware2.jpg');
    background-size: cover;
  }
}
.hardware-title {
  margin: 48px 0 16px;
  font-size: 30px;
  font-weight: 500;
  line-height: 40px;
}
.hardware-desc {
  font-size: 16px;
  line-height: 24px;
}

.only-display-p {
  display: inline-block;
}
.only-display-m {
  display: none;
}

//产品案例
.product-carousel-box {
  height: 234px;
}
.product-carousel-row {
  position: absolute;
  white-space: nowrap;
  height: 230px;
  display: inline-block;
}
.partner {
  margin: 0 25px;
  width: 200px;
  height: 230px;
  position: relative;
  display: inline-block;
  &-img {
    width: 160px;
    height: 160px;
    margin: 0 auto 12px;
    background-size: contain;
  }
}
.products-wrap {
  padding: 100px 0;
  text-align: center;
  .title {
    margin-bottom: 52px;
    font-size: 34px;
    font-weight: bold;
  }
  .product-item-wrap-m {
    display: none;
  }
  .product-item {
    margin-left: 120px;
    display: inline-block;
    overflow: hidden;
    width: 200px;
    &:first-child {
      margin-left: 0;
    }
  }
  .product-name {
    margin-bottom: 2px;
    line-height: 26px;
    font-size: 20px;
  }
  .product-use {
    color: $text-mid-grey;
    &.business {
      color: #8abaf5;
    }
    &.home {
      color: #8adac8;
    }
  }
}

//合作咨询
.contact-wrap {
  padding-bottom: 120px;
  text-align: center;
  .section-title {
    margin-bottom: 10px;
  }
  .desc {
    font-size: 16px;
    color: rgba(140, 140, 140, 1);
    line-height: 24px;
    display: inline-block;
    text-align: left;
    margin: 0 auto;
  }
  .apply-btn {
    margin: 32px auto 0;
    background: #1784e9;
    &:hover {
      color: #fff;
    }
  }
}

.not-m-320w {
  display: inline;
}
.m-320w {
  display: none;
}
@media screen and (max-width: 719px) {
  .banner-wrap {
    min-width: unset;
    text-align: center;
    background: linear-gradient(
      315deg,
      rgba(52, 54, 85, 1) 0%,
      rgba(30, 29, 57, 1) 100%
    );
    .banner {
      padding-top: 0;
      width: 100%;
      height: 460px;
      background: url('../../../assets/images/solution/robot/<EMAIL>')
        center no-repeat;
      background-size: contain;
      background-position-y: bottom;
    }
    .banner-title {
      padding: 40px 48px 8px;
      font-size: 30px;
      line-height: 40px;
    }
    .banner-desc {
      margin: 0 0 32px;
      padding: 0 24px;
      width: unset;
      font-size: 14px;
      letter-spacing: normal;
      line-height: 22px;
    }
    .indevice-btn {
      display: inline-block;
    }
  }
  .indevice-btn {
    width: 160px;
  }
  .section {
    padding: 56px 0 64px;
  }
  .section-title {
    margin-bottom: 32px;
    line-height: 32px;
    font-size: 24px;
  }
  .center-section {
    padding: 56px 24px 64px;
    width: 100%;
  }
  .section-item {
    width: 100%;
  }

  //应用场景
  .hover-animation {
    margin-bottom: 24px;
    .item-title {
      padding: 40px 0 12px;
      font-size: 20px;
      font-weight: 500;
      line-height: 26px;
    }
    .item-desc {
      margin-bottom: 36px;
      padding: 0 24px;
      font-size: 14px;
      line-height: 22px;
    }
    .item-detail {
      flex-wrap: wrap;
      .item-detail-child {
        position: relative;
        margin-bottom: 16px;
        height: 144px;
        width: calc(50% - 5px);
        p {
          font-size: 16px;
          font-weight: 600;
          line-height: 22px;
          text-align: left;
        }
      }
      .detail-child-bg {
        position: absolute;
        right: 16px;
        bottom: 16px;
      }
    }
    .opacity-gradient {
      height: 136px;
    }
  }

  .section-content {
    width: 100%;
  }

  //方案优势-start
  .odds-section {
    min-width: unset;
  }
  .character-item {
    margin-bottom: 0;
    padding-top: 0;
    width: 100%;
    height: 162px;
    .item-title {
      margin-bottom: 12px;
      font-size: 20px;
      font-weight: 500;
      line-height: 26px;
    }
    .item-desc {
      font-size: 14px;
      line-height: 22px;
    }
    &:nth-child(odd) {
      margin-right: 60px;
    }
  }

  .bg1,
  .bg2,
  .bg3 {
    margin: 12px 0 32px !important;
  }
  .bg4 {
    margin-top: 12px;
  }
  .unset-height {
    height: unset;
  }

  //硬件接入方式
  .hardware-section {
    min-width: unset;
    .section-item {
      padding: 0 24px;
      height: 320px;
    }
    .left-item {
      margin-bottom: 32px;
      background: url('../../../assets/images/solution/robot/robot-hardware1-m.jpg')
        center no-repeat;
      background-size: cover;
    }
    .right-item {
      background: url('../../../assets/images/solution/robot/robot-hardware2-m.jpg')
        center no-repeat;
      background-size: cover;
    }
  }
  .hardware-title {
    margin: 40px 0 12px;
    font-size: 20px;
    line-height: 26px;
  }
  .hardware-desc {
    font-size: 14px;
    line-height: 22px;
  }
  .only-display-m {
    display: block;
  }
  .only-display-p {
    display: none;
  }

  //产品案例
  .products-wrap {
    padding: 56px 0;
    .title {
      margin-bottom: 32px;
      font-size: 24px;
    }
    .product-carousel-box {
      display: none;
    }
    .product-item-wrap-m {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      padding: 0 24px;
      width: 100%;
    }
    .product-item {
      margin-left: 0;
      margin-bottom: 24px;
      width: 152px;
    }
    .product-img {
      height: 120px;
      width: 120px;
      margin: 0 auto 12px;
      background-size: contain;
    }
    .product-name {
      font-size: 16px;
      line-height: 22px;
    }
  }

  //合作咨询
  .contact-wrap {
    padding-top: 0;
    padding-bottom: 64px;
    .title {
      margin-bottom: 12px;
      font-size: 24px;
    }
    .desc {
      line-height: 26px;
      font-size: 16px;
    }
  }
}

@media screen and (max-width: 330px) {
  .products-wrap {
    .product-item-wrap-m {
      justify-content: center;
    }
  }
  .not-m-320w {
    display: none;
  }
  .m-320w {
    display: inline;
  }
}
</style>
