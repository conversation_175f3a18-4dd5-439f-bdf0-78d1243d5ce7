<template>
  <div
    v-if="!hideAiuiMenu"
    class="menu_wrapper"
    :style="{
      transition: 'width 0.2s ease',
    }"
    :class="collapse ? 'menu_collapse' : 'menu_expand'"
  >
    <div class="logo_wrap">
      <div
        :class="`${collapse ? 'logo_icon' : 'logo_all'}`"
        @click="$router.push('/')"
      ></div>
    </div>
    <el-scrollbar>
      <el-menu
        :default-active="activeMenu"
        class="el-menu-vertical-demo"
        :collapse="collapse"
        :router="true"
        :default-openeds="['1']"
        @open="handleOpen"
        @close="handleClose"
      >
        <el-menu-item :index="subAccountInfo ? '/sub/apps' : '/app'">
          <svg-icon
            iconClass="my-application"
            class="menu_icon"
            :customStyle="{ width: '18px', height: '19px' }"
          />
          <span slot="title">我的应用</span>
        </el-menu-item>

        <!-- 展开状态下的子菜单 -->
        <el-submenu index="1" v-if="!collapse">
          <template slot="title">
            <svg-icon
              iconClass="custom-tools"
              class="menu_icon"
              :customStyle="{ width: '18px', height: '19px' }"
            />
            <span>自定义工具</span>
          </template>
          <el-menu-item-group>
            <el-menu-item index="/studio/qaBank" v-if="!subAccountInfo">
              <span slot="title">问答库</span>
            </el-menu-item>
            <el-menu-item index="/studio/agent" v-if="!subAccountInfo">
              <span slot="title">智能体</span>
            </el-menu-item>
            <el-menu-item index="/studio/source" v-if="!subAccountInfo">
              <span slot="title">信源</span>
            </el-menu-item>
            <el-menu-item
              :index="subAccountInfo ? '/sub/skills' : '/studio/skill'"
            >
              <span slot="title">技能</span>
            </el-menu-item>
            <el-menu-item index="/studio/role" v-if="!subAccountInfo">
              <span slot="title">角色</span>
            </el-menu-item>
          </el-menu-item-group>
        </el-submenu>

        <!-- 收缩状态下的子菜单 -->
        <template v-else>
          <!-- 问答库 -->
          <el-menu-item
            index="/studio/qaBank"
            v-if="!subAccountInfo"
            class="submenu-item-collapsed"
          >
            <svg-icon
              iconClass="qa-library"
              class="menu_icon"
              :customStyle="{ width: '16px', height: '19px' }"
            />
            <span slot="title">问答库</span>
          </el-menu-item>

          <!-- 智能体 -->
          <el-menu-item
            index="/studio/agent"
            v-if="!subAccountInfo"
            class="submenu-item-collapsed"
          >
            <svg-icon
              iconClass="intelligent-agent"
              class="menu_icon"
              :customStyle="{ width: '18px', height: '17px' }"
            />
            <span slot="title">智能体</span>
          </el-menu-item>

          <!-- 信源 -->
          <el-menu-item
            index="/studio/source"
            v-if="!subAccountInfo"
            class="submenu-item-collapsed"
          >
            <svg-icon
              iconClass="information-source"
              class="menu_icon"
              :customStyle="{ width: '20px', height: '19px' }"
            />
            <span slot="title">信源</span>
          </el-menu-item>

          <!-- 技能 -->
          <el-menu-item
            :index="subAccountInfo ? '/sub/skills' : '/studio/skill'"
            class="submenu-item-collapsed"
          >
            <svg-icon
              iconClass="skill"
              class="menu_icon"
              :customStyle="{ width: '18px', height: '15px' }"
            />
            <span slot="title">技能</span>
          </el-menu-item>

          <!-- 角色 -->
          <el-menu-item
            index="/studio/role"
            v-if="!subAccountInfo"
            class="submenu-item-collapsed"
          >
            <svg-icon
              iconClass="role"
              class="menu_icon"
              :customStyle="{ width: '20px', height: '19px' }"
            />
            <span slot="title">角色</span>
          </el-menu-item>
        </template>

        <!-- <el-menu-item index="/agent-square">
          <svg-icon
            iconClass="intelligent-agent"
            class="menu_icon"
            :customStyle="{ width: '20px', height: '17px' }"
          />
          <span slot="title">智能体广场</span>
        </el-menu-item> -->
        <el-menu-item index="/store/all" v-if="!subAccountInfo">
          <svg-icon
            iconClass="skills-store"
            class="menu_icon"
            :customStyle="{ width: '21px', height: '19px' }"
          />
          <span slot="title">技能商店</span>
        </el-menu-item>
      </el-menu>
    </el-scrollbar>

    <div>
      <ul class="bottom_userinfo">
        <el-tooltip
          :disabled="collapse ? false : true"
          content="文档中心"
          placement="right"
        >
          <li @click="openDocumentCenter">
            <svg-icon
              iconClass="document-center"
              class="menu_icon"
              :customStyle="{ width: '15px', height: '17px' }"
            />

            <el-link
              :style="{
                maxWidth: collapse ? '0px' : '100px',
                cursor: 'pointer',
              }"
              class="userinfo_text"
              :underline="false"
              >文档中心</el-link
            >
          </li>
        </el-tooltip>
        <el-tooltip
          class="item"
          effect="dark"
          content="<EMAIL>"
          placement="right"
        >
          <li @click="contactUs">
            <svg-icon
              iconClass="email"
              class="menu_icon"
              :customStyle="{ width: '18px', height: '19px' }"
            />

            <el-link
              :style="{
                maxWidth: collapse ? '0px' : '100px',
                cursor: 'pointer',
              }"
              class="userinfo_text"
            >
              联系我们
            </el-link>
          </li>
        </el-tooltip>
        <!-- <li @click="contactUs">
          <svg-icon
            iconClass="email"
            class="menu_icon"
            :customStyle="{ width: '17px', height: '17px' }"
          />

          <el-link
            :style="{
              maxWidth: collapse ? '0px' : '100px',
              cursor: 'pointer',
            }"
            class="userinfo_text"
          >
            联系我们
          </el-link>
        </li> -->
        <el-tooltip
          class="item"
          effect="dark"
          :content="
            subAccountInfo
              ? subAccountInfo.login
              : userInfo.mobile || encryptedmailbox(userInfo.email)
          "
          placement="right"
        >
          <li>
            <svg-icon
              iconClass="user"
              class="menu_icon"
              :customStyle="{ width: '16px', height: '18px' }"
            />
            <span
              class="userinfo_text"
              :title="subAccountInfo.login"
              v-if="subAccountInfo"
              :style="{ maxWidth: collapse ? '0px' : '100px' }"
              ><span>{{ subAccountInfo.login }}</span></span
            >
            <span
              class="userinfo_text"
              :title="userInfo.mobile || encryptedmailbox(userInfo.email)"
              v-else
              :style="{ maxWidth: collapse ? '0px' : '100px' }"
              >{{ userInfo.mobile || encryptedmailbox(userInfo.email) }}</span
            >
          </li>
        </el-tooltip>
      </ul>
      <div class="collapse_wrap">
        <div v-show="!showToggleCollapse" @click="toggleCollapse">
          <svg-icon
            iconClass="collapse-icon"
            class="collapse_icon"
            :class="{ 'rotate-180': !collapse }"
            :customStyle="{ width: '24px', height: '24px' }"
          />
        </div>
      </div>
    </div>
    <first-enter-dialog :dialog="firstDialog" />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import svgIcon from '@C/svgIcon.vue'
import { encryptedmailbox } from '@U/utils.js'
import api from '@U/api.js'
import FirstEnterDialog from './firstEnterDialog.vue'

export default {
  components: { svgIcon, FirstEnterDialog },
  data() {
    return {
      nav: '',

      firstDialog: {
        show: false,
      },
    }
  },
  created() {
    this.getUserInfo()
    if (localStorage.getItem('hasShowSOSUpdate') !== '1') {
      this.firstDialog.show = true
      localStorage.setItem('hasShowSOSUpdate', '1')
    }
    // 页面加载时检查是否需要收缩菜单
    this.checkMenuCollapse()
  },
  computed: {
    ...mapGetters({
      userInfo: 'user/userInfo',
      subAccountInfo: 'user/subAccountInfo',
      collapse: 'aiuiStore/collapse',
      hideAiuiMenu: 'aiuiStore/hideAiuiMenu',
    }),
    showToggleCollapse() {
      const routeName = this.$route.name
      return (
        (routeName && routeName.startsWith('app') && routeName !== 'apps') ||
        routeName === 'studio-handle-platform-agent-detail' ||
        routeName === 'studio-handle-platform-intent-detail' ||
        routeName === 'studio-handle-platform-agent-detail-config' ||
        (routeName && routeName.startsWith('qaBank')) ||
        (routeName && routeName.startsWith('keyQABank')) ||
        (routeName && routeName.startsWith('keyqa')) ||
        (routeName && routeName.startsWith('agent-')) ||
        routeName === 'studio-source-detail' ||
        (routeName && routeName.startsWith('skill-')) ||
        (routeName && routeName.startsWith('sub-skill-')) ||
        routeName === 'role' ||
        routeName === 'entity' ||
        routeName === 'auxiliary' ||
        routeName === 'sub-entity'
      )
    },
    activeMenu() {
      const { path } = this.$route
      if (path.startsWith('/studio/agent') || path.startsWith('/agent/legacy'))
        return '/studio/agent'
      if (
        path.startsWith('/studio/role') ||
        path.startsWith('/studio/character')
      )
        return '/studio/role'
      if (path.startsWith('/studio/source')) return '/studio/source'
      if (path.startsWith('/app')) return '/app'
      if (path.startsWith('/studio/skill')) return '/studio/skill'
      if (path.startsWith('/studio/qaBank')) return '/studio/qaBank'
      return path
    },
  },
  watch: {
    showToggleCollapse(newVal) {
      // 当收缩按钮被隐藏时（newVal为true），自动收缩菜单栏
      if (newVal) {
        this.$store.dispatch('aiuiStore/setCollapse', true)
      }
    },
  },
  methods: {
    encryptedmailbox,

    getUserInfo() {
      let userInoFn = 'user/setUserInfo',
        loginPath = null
      if (
        (this.$route.name && this.$route.name.indexOf('sub-') !== -1) ||
        (this.$route.path && this.$route.path.indexOf('sub/') !== -1)
      ) {
        this.$store.dispatch('user/setAccountType', 'sub')
        userInoFn = 'user/setSubAccountInfo'
        loginPath = `${location.origin}/sub/login?pageFrom=${location.href}`
      } else {
        this.$store.dispatch('user/setAccountType', 'main')
        userInoFn = 'user/setUserInfo'
        loginPath = `${location.origin}/user/login?pageFrom=${location.href}`
      }
      api
        .userInfo()
        .then((res) => {
          this.headLoading = false
          if (res.status === 200 && res.data.flag) {
            this.$store.dispatch(userInoFn, res.data.data)
            !this.subAccount &&
              this.userInfo &&
              this.$store.dispatch('user/setMessageUnReadCount')
            this.userInfo && this.$store.dispatch('aiuiApp/setLimitCount')
            this.subAccountInfo && this.$store.dispatch('aiuiApp/setLimitCount')
            this.subAccountInfo &&
              this.$store.dispatch('aiuiApp/setSubAccountAppAuths')
            this.subAccountInfo &&
              this.$store.dispatch('aiuiApp/setSubAccountMainAccountInfo')
          } else {
          }
        })
        .catch(() => {})
    },

    onMenuSelect(index, indexPath) {
      this.nav = index
    },
    toggleCollapse() {
      this.$store.dispatch('aiuiStore/setCollapse', !this.collapse)
    },
    handleOpen(key, keyPath) {
      console.log(key, keyPath)
    },
    handleClose(key, keyPath) {
      console.log(key, keyPath)
    },
    openDocumentCenter() {
      window.open(`${this.$config.docs}doc-1/`, '_blank')
    },
    contactUs() {
      window.location.href = 'mailto:<EMAIL>'
    },
    checkMenuCollapse() {
      // 页面加载时检查是否需要收缩菜单
      if (this.showToggleCollapse) {
        this.$store.dispatch('aiuiStore/setCollapse', true)
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.handle-platform-title {
  margin-bottom: 8px;
  cursor: pointer;
  font-weight: bold;
  text-align: left;
  padding-left: 48px;
}

.menu_wrapper {
  .logo_wrap {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 63px;
    border-right: 1px solid $grey007;
    div {
      cursor: pointer;
    }

    .logo_all {
      width: 145px;
      height: 35px;
      background: url(../assets/svgs/logo-all.png) center no-repeat;
      background-size: 100% 100%;
    }
    .logo_icon {
      width: 53px;
      height: 21px;
      background: url(../assets/svgs/logo.png) center no-repeat;
      background-size: 100% 100%;
    }
  }
  .el-scrollbar {
    height: calc(100vh - 274.5px);
    padding: 13px 13px 0 12px;
    border-right: 1px solid $grey007;
    :deep(.is-horizontal) {
      display: none;
    }
  }
  .horizontal-collapse-transition {
    transition: 0.2s width ease-in-out, 0.2s padding-left ease-in-out,
      0.2s padding-right ease-in-out;
  }
  :deep(.el-menu) {
    .menu_icon {
      margin-right: 5px;
      vertical-align: middle;
      color: #757a8c; // 图标默认颜色（灰色 非选中状态）
    }

    .el-submenu__icon-arrow {
      top: 58%;
      right: 35px;
    }
    > .el-menu-item {
      height: 36px;
      line-height: 36px;
      color: #3f4453;
      border-radius: 8px;
      margin-bottom: 7px;
      padding: 0 11px !important;
      &:last-child {
        margin-bottom: 0;
      }
    }
    > .el-submenu {
      margin-bottom: 7px;
      .el-submenu__title {
        border-radius: 8px;
        height: 36px;
        line-height: 36px;
        color: #3f4453;
        padding: 0 11px !important;
        &:hover {
          background-color: #f2f4fa;
        }
      }
      ul {
        overflow: hidden;
        .el-menu-item {
          height: 36px;
          line-height: 36px;
          color: rgba(0, 0, 0, 0.6);
          font-size: 14px;
          padding: 0 40px !important;
          border-radius: 8px;
        }
      }
    }
    .el-menu-item {
      .el-tooltip {
        padding: 0 11px !important;
      }
      &.is-active {
        color: $primary !important;
        background-color: #f2f4fa;
        .menu_icon {
          color: $primary !important;
        }
      }
      &:hover {
        background-color: #f2f4fa;
      }
    }
  }
  .collapse_wrap {
    padding-left: 20px;
    border-right: 1px solid $grey007;
    height: 63px;
    > div {
      cursor: pointer;
      display: inline-block;

      line-height: 70px;
    }
    .collapse_icon {
      &.rotate-180 {
        transform: rotate(180deg);
      }
    }
  }
  .bottom_userinfo {
    padding: 0 13px 23px 12px;
    border-bottom: 1px solid $grey007;
    border-right: 1px solid $grey007;
    margin-bottom: 0;
    li {
      height: 36px;
      line-height: 36px;
      margin-bottom: 8px;
      padding-left: 11px;
      cursor: pointer;

      &:last-child {
        margin-bottom: 0;
      }
      .userinfo_text {
        display: inline-block;
        white-space: nowrap;
        overflow: hidden;
        vertical-align: middle;
        transition: max-width 0.2s ease;
        max-width: 100px;
        opacity: 1;
        color: #38393a;
        text-overflow: ellipsis;
      }
      .menu_icon {
        margin-right: 5px;
        vertical-align: middle;
      }
      &:hover {
        background-color: #f2f4fa;
        border-radius: 8px;
      }
    }
  }
  // 展开时
  &.menu_expand {
    width: 217px;
    overflow: hidden;
  }
  // 收缩时
  &.menu_collapse {
    overflow: hidden;
    .collapse_wrap {
      border-top: 1px solid transparent;
      border-right: 1px solid $grey007;
    }
    width: 65px;
    :deep(.el-menu) {
      width: 100%;
    }
  }
}
</style>
