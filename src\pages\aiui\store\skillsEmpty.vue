<template>
  <div class="empty-skill-tip">
    <p>没有相应的技能</p>
    <p>
      这里的技能还是不能满足您的需求？您还可以
      <a @click="jump">自定义您的技能</a>
    </p>
  </div>
</template>
<script>
export default {
  methods: {
    jump() {
      let routeData = this.$router.resolve({
        path: '/studio/skill',
      })
      window.open(routeData.href, '_blank')
    },
  },
}
</script>
<style lang="scss" scoped>
@import '@A/scss/skills.scss';
</style>
