.control-btns {
  display: flex;
  align-items: center;
  height: 40px;
  background: #fff;
  padding: 0 8px;
  border-radius: 8px;
  box-shadow: 0px 2px 8px 0px rgba(24, 78, 155, 0.15);
  :deep(.el-checkbox + .el-checkbox) {
    margin-left: 15px;
  }
  :deep(.el-checkbox__label) {
    padding-left: 4px;
  }
  :deep(.el-radio + .el-radio) {
    margin-left: 15px;
  }
}
.control-btns-left {
  left: 0px;
}
.control-btns-right {
  right: 0;
  overflow: auto;
  scrollbar-width: none;
  &::-webkit-scrollbar {
    display: none;
  }
  :deep(.el-radio-group) {
    width: 430px;
    max-width: 430px;

    white-space: nowrap;
  }
  :deep(.el-checkbox-group) {
    width: 430px;
    max-width: 430px;

    white-space: nowrap;
  }
}

.type-wrapper {
  display: flex;
  align-items: center;
  font-size: 14px;
  li {
    cursor: pointer;
    height: 24px;
    padding: 2px 30px;

    border: 1px solid #cfdbff;
    border-radius: 14px;
    &:hover {
      color: $primary;
      border: 1px solid $primary;
    }
  }
  li + li {
    margin-left: 8px;
  }
}

.ability-wrapper {
  overflow: auto;
  max-height: 90%;
  li + li {
    margin-top: 15px;
  }
}

.ability-type-wrapper {
  // &:first-of-type {
  //   margin: 15px 0 20px 0;
  // }
  // &:not(:first-of-type) {
  //   margin: 30px 0 20px 0;
  // }
  font-weight: 600;
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
}

.abilities {
  position: absolute;
  z-index: 10;
  left: 0;
  top: -242px;
  width: 984px;
  top: -5px;
  transform: translateY(-100%);
  height: 50vh;
  min-height: 238px;
  padding: 16px;
  background: linear-gradient(0deg, #ffffff 88%, #dfeaff);
  border: 1px solid #ffffff;
  border-radius: 8px;
  box-shadow: 0px 4px 12px 0px rgba(46, 75, 147, 0.16);

  .close-btn {
    position: absolute;
    z-index: 11;
    top: 2px;
    right: 12px;
    font-size: 28px;
    color: #999;
    cursor: pointer;
  }

  .ability-tip {
    font-size: 14px;
    line-height: 20px;
    display: flex;
    align-items: center;
    padding-bottom: 5px;
    .ability-tip__left {
      margin-right: 10px;
      color: #8e90a5;
      .ability-number {
        color: #ec2929;
      }
    }
    .ability-tip__right {
      display: flex;
      align-items: center;
      > li {
        height: 24px;
        line-height: 24px;
        text-align: center;
        background: #e6edff;
        border-radius: 14px;
        color: #454973;
        padding: 0px 12px;
        font-size: 14px;
      }
      li + li {
        margin-left: 4px;
      }
    }
  }

  .ability-container {
    display: grid;
    grid-template-columns: repeat(4, 1fr); /* 将容器分为四列，每列宽度平均 */
    gap: 8px 16px; /* 设置格子之间的间距为20px */

    .ability-unit {
      border-radius: 8px;
      height: 64px;
      position: relative;
      background: #e9ecf3;
      padding-left: 8px;
    }

    .ability-top {
      display: flex;
      align-items: center;
      height: 100%;
    }

    .ability-right {
      font-size: 14px;
      color: #333;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      max-width: 150px;
      margin-left: 10px;
    }

    .switch-wrpper {
      position: absolute;
      z-index: 1;
      top: 10px;
      right: 10px;
      // :deep(.el-switch__core) {
      //   background: #B6C4DC;
      // }
      :deep(.el-switch .el-switch__core:after) {
        width: 16px;
        height: 16px;
        top: 1px;
        // left: 1px;
      }
      :deep(.el-switch.is-checked .el-switch__core::after) {
        background-color: #fff;
        border-color: #fff;
        // left: unset;
        // right: 1px;
      }
      :deep(.el-switch .el-switch__core) {
        height: 18px;
        background-color: #b6c4dc;
      }
      :deep(.el-switch.is-checked .el-switch__core) {
        background-color: $primary;
      }
    }

    .ability-name {
      height: 20px;
      font-size: 14px;
      font-weight: 500;
      color: #454973;
      line-height: 20px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      max-width: 110px;
    }

    .ability-desc {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      // max-width: 200px;
      margin-top: 4px;

      font-size: 14px;
      font-weight: 400;
      color: #8e90a5;
      line-height: 20px;
    }

    .ability-icon {
      img {
        width: 48px;
        height: 48px;
        border-radius: 6px;
      }
    }
    .ability-icontext {
      text-align: center;
      font-size: 24px;
      width: 48px;
      height: 48px;
      line-height: 48px;
      background: linear-gradient(0deg, #ffffff, #e9faff);
      border-radius: 6px;
      color: #abd5ff;
      font-weight: 500;
    }
  }
}

.selector-title-wrap {
  display: flex;
  align-items: center;
  .selector-icon {
    width: 24px;
    height: 24px;
    display: inline-block;
    margin-right: 4px;
    &.skill-icon {
      background: url(~@A/images/model-exeperience/v2/<EMAIL>)
        center/contain no-repeat;
    }
    &.advance-icon {
      background: url(~@A/images/model-exeperience/v2/<EMAIL>)
        center/contain no-repeat;
    }
  }
  > span {
    font-size: 14px;
    font-weight: 500;
    text-align: justify;
    color: #454973;
    line-height: 20px;
  }
  .selector-divider {
    width: 1px;
    height: 24px;
    background: #bfc7d6;
    margin: 0 10px;
  }
}

@media screen and (max-width: 1441px) {
  .type-wrapper {
    li {
      padding: 2px 20px;
    }
    li + li {
      margin-left: 6px;
    }
  }
}
