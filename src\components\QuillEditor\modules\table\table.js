let BlockEmbed = Quill.import('blots/embed')

class TableThumbnailBlot extends BlockEmbed {
  static create(value) {
    let node = super.create()
    node.setAttribute('type', 'table-thumbnail')
    node.setAttribute('class', 'table-thumbnail')
    // node.setAttribute('src', tableImage)
    node.setAttribute('content', value.content)
    return node
  }

  static value(node) {
    return {
      content: node.getAttribute('content'),
      //   type: node.getAttribute('type'),
    }
  }
}
TableThumbnailBlot.blotName = 'table'
TableThumbnailBlot.tagName = 'img'
export default TableThumbnailBlot
