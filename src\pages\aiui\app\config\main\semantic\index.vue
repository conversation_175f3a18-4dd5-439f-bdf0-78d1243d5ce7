<template>
  <card>
    <template #title> 语义模型配置 </template>
    <div>
      <p class="item-title" style="margin-top: 0">添加工具</p>
      <repo />
      <agent />
      <skill />
      <!-- <p class="item-title">提示词设置</p>
      <prompt /> -->
      <p class="item-title">搜索设置</p>
      <search />
    </div>
  </card>
</template>
<script>
import card from '../components/card'

import repo from './repo'
import agent from './agent'
import skill from './skill'

import prompt from './prompt'
import search from './search'

export default {
  data() {
    return {}
  },
  components: {
    card,
    repo,
    agent,
    skill,
    prompt,
    search,
  },
}
</script>
<style lang="scss" scoped>
@import '../common.scss';
</style>
