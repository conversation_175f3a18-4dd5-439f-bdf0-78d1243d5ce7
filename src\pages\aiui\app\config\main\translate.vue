<template>
  <div class="content-container">
    <div>
      <translation></translation>
    </div>
    <div class="gutter">
      <synthesis></synthesis>
    </div>
  </div>
</template>
<script>
import { mapGetters } from 'vuex'

import synthesis from './synthesis'
import translation from './translation'

export default {
  data() {
    return {}
  },
  computed: {
    ...mapGetters({
      currentScene: 'aiuiApp/currentScene',
      app: 'aiuiApp/app',
      subAccountEditable: 'aiuiApp/subAccountEditable',
    }),
    appId() {
      return this.$route.params.appId
    },
  },
  components: {
    synthesis,
    translation,
  },
}
</script>
<style lang="scss" scoped>
@import './common.scss';
.gutter {
  margin-top: 10px;
}
.content-container {
  padding: 14px 16px;
  max-height: calc(100vh - 128px);
  overflow: auto;
  background: $secondary-bgc;
}
</style>
