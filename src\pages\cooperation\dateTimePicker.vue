<template>
  <el-date-picker
    class="date-picker-wrap"
    v-model="value"
    :clearable="false"
    style="width:204px"
    size="small"
    type="datetime"
    prefix-icon="el-icon-date"
    placeholder="选择日期时间">
  </el-date-picker>
</template>

<script>
  export default {
    data() {
      return {
        value: ''
      }
    },
    watch: {
      value(val) {
        let tmp = this.$utils.dateFormat(val, 'yyyy-MM-dd hh:mm:ss')
        this.$emit('setTime', tmp)
      }
    },
    methods: {
      clearTime() {
        this.value = ''
      }
    }
  }
</script>

<style lang="scss" scoped>
</style>
<style lang="scss">
  .date-picker-wrap {
    padding: 0;
    .el-input__inner {
      padding-left: 15px;
      height: 36px;
      line-height: 36px;
    }
    .el-icon-date {
      position: absolute;
      right: -188px;
    }
  }
</style>