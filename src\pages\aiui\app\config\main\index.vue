<template>
  <div>
    <appHeader />
    <mainSOS v-if="currentScene && currentScene.sos === true" />

    <!-- 老应用区分不是翻译 -->
    <mainV v-if="showNormal" />

    <!-- 语音翻译场景 -->
    <translate v-if="showTranslate" />
  </div>
</template>

<script>
import appHeader from './header'

import { mapGetters } from 'vuex'
import mainV from './main'
import mainSOS from './mainSOS'
import translate from './translate'

import RECOGNITION_LLM_SEMANTIC_State from '@U/AIUIState/RECOGNITION_LLM_SEMANTIC_State'
import RECOGNITION_LLM_SEMANTIC_SYNTHESIS_State from '@U/AIUIState/RECOGNITION_LLM_SEMANTIC_SYNTHESIS_State'

import RECOGNITION_State from '@U/AIUIState/RECOGNITION_State'
import RECOGNITION_SEMANTIC_State from '@U/AIUIState/RECOGNITION_SEMANTIC_State'
import RECOGNITION_SEMANTIC_SYNTHESIS_State from '@U/AIUIState/RECOGNITION_SEMANTIC_SYNTHESIS_State'

import RECOGNITION_POSTPROCESS_State from '@U/AIUIState/RECOGNITION_POSTPROCESS_State'
import RECOGNITION_SEMANTIC_POSTPROCESS_State from '@U/AIUIState/RECOGNITION_SEMANTIC_POSTPROCESS_State'
import RECOGNITION_SYNTHESIS_State from '@U/AIUIState/RECOGNITION_SYNTHESIS_State'

import RECOGNITION_TRANSLATE_SYNTHESIS_State from '@U/AIUIState/RECOGNITION_TRANSLATE_SYNTHESIS_State'
import RECOGNITION_TRANSLATE_State from '@U/AIUIState/RECOGNITION_TRANSLATE_State'

export default {
  data() {
    return {}
  },
  computed: {
    ...mapGetters({
      currentScene: 'aiuiApp/currentScene',
      sceneRoleId: 'aiuiApp/sceneRoleId',
      context: 'aiuiApp/context',
      subAccountEditable: 'aiuiApp/subAccountEditable',
    }),
    appId() {
      return this.$route.params.appId
    },

    showNormal() {
      if (this.currentScene && this.currentScene.point && this.context) {
        const point = this.currentScene.point
        const context = this.context
        if (
          // context.isCurrentState( RECOGNITION_LLM_SEMANTIC_State) ||
          // context.isCurrentState( RECOGNITION_LLM_SEMANTIC_SYNTHESIS_State) ||
          // context.isCurrentState( RECOGNITION_State) ||
          // context.isCurrentState( RECOGNITION_SEMANTIC_State) ||
          // context.isCurrentState( RECOGNITION_SEMANTIC_SYNTHESIS_State) ||
          // context.isCurrentState( RECOGNITION_SEMANTIC_POSTPROCESS_State) ||
          // context.isCurrentState( RECOGNITION_POSTPROCESS_State) ||
          // context.isCurrentState( RECOGNITION_SYNTHESIS_State)
          this.currentScene.sos !== true &&
          !context.isCurrentState(RECOGNITION_TRANSLATE_State) &&
          !context.isCurrentState(RECOGNITION_TRANSLATE_SYNTHESIS_State)
        ) {
          return true
        }
      }
      return false
    },
    showTranslate() {
      if (this.currentScene && this.currentScene.point && this.context) {
        const point = this.currentScene.point
        const context = this.context

        if (
          (this.currentScene.sos !== true &&
            context.isCurrentState(RECOGNITION_TRANSLATE_State)) ||
          context.isCurrentState(RECOGNITION_TRANSLATE_SYNTHESIS_State)
        ) {
          return true
        }
      }
      return false
    },
  },
  watch: {
    currentScene: {
      handler(val) {
        console.log('currentScene changed', val)
      },
      immediate: true,
    },
  },
  beforeDestroy() {
    this.$store.dispatch('aiuiApp/setCurrentScene', {})
    this.$store.dispatch('aiuiApp/setSceneRole', '')
  },
  components: {
    appHeader,
    mainV,
    mainSOS,
    translate,
  },
}
</script>
<style lang="scss" scoped></style>
