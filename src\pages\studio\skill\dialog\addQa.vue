<template>
  <el-dialog title="添加追问话术" :visible.sync="dialog.show" width="568px">
    <!-- <os-text-adder
      class="mgb24"
      :data="questionSentence"
      :disabled="!subAccountEditable"
      @add="add"
      @del="del"
      :reg="textReg"
      warning="仅支持汉字/字母/数字/空格/°，且每条不超过50字"
      :readonly="true"
      :max="5"
      placeholder="最多添加5条，每条不超过50字，回车新增"
    />
    <span slot="footer" class="dialog-footer">
    </span> -->
    <label-selector
      @select="onLabelSelect"
      title="添加追问话术"
    ></label-selector>

    <qa-text-adder
      :data="questionSentence || []"
      :disabled="!subAccountEditable"
      @add="add"
      @del="del"
      @edit="edit"
      @change="onInputChange"
      :reg="textReg"
      warning="仅支持汉字/字母/数字/空格/°，且每条不超过50字"
      :max="5"
      editPlaceholder="每条不超过50字，回车或者点击框外空白处保存"
      placeholder="最多添加5条，每条不超过50字，回车新增"
      ref="qaRef"
    >
    </qa-text-adder>
    <span slot="footer" class="dialog-footer"> </span>
  </el-dialog>
</template>

<script>
import qaTextAdder from './qaTextAdder.vue'
import labelSelector from '../labelSelector.vue'
export default {
  components: { qaTextAdder, labelSelector },
  props: {
    dialog: {
      type: Object,
      default: {},
    },
    subAccountEditable: Boolean,
  },
  data() {
    return {
      saving: false,
      textReg: /^[\u4e00-\u9fffa-zA-Z0-9° ]{1,50}$/,
      questionSentence: [],
    }
  },
  computed: {
    intentId() {
      return this.$store.state.studioSkill.intention.id
    },
  },
  watch: {
    'dialog.show': function (val, oldVal) {
      if (val) {
        this.questionSentence = (this.dialog.data.questionSentence || []).map(
          (item) => {
            return {
              ...item,
              changed: false,
            }
          }
        )
      } else {
        this.$nextTick(() => {
          setTimeout(() => {
            this.questionSentence = []
            this.$refs.qaRef.resetInitialStatus()
          }, 200)
        })
      }
    },
  },
  mounted() {},
  methods: {
    onInputChange(index) {
      this.questionSentence = (this.questionSentence || []).map((item, i) => {
        if (index === i) {
          return {
            ...item,
            changed: true,
          }
        } else {
          return {
            ...item,
          }
        }
      })
    },
    onLabelSelect(label) {
      this.$refs.qaRef.$refs.intelInput.insertLabel(label)
    },
    save() {
      let self = this
    },
    add(text) {
      if (this.saving) {
        return
      }
      let questionSentence = JSON.parse(JSON.stringify(this.questionSentence))
      questionSentence[questionSentence.length] = text
      this.save(questionSentence, 'add')
    },
    del(text) {
      if (this.saving) {
        return
      }
      this.saving = true
      let questionSentence = Array.prototype.filter.call(
        this.questionSentence,
        function (item, index) {
          return item != text
        }
      )
      this.save(questionSentence, 'sub')
    },
    edit(text, index) {
      const otherItems = (this.questionSentence || []).filter(
        (_, idx) => idx !== index
      )
      const find = otherItems.findIndex((itm) => itm.answer === text.answer)
      if (find !== -1) {
        return this.$message.error('不得与其他条目重复')
      }
      let questionSentence = (this.questionSentence || []).map((item, i) => {
        if (index === i) {
          return {
            ...item,
            ...text,
          }
        } else {
          return { ...item }
        }
      })
      this.save(questionSentence, 'edit', index)
    },
    save(list, mode, index) {
      let self = this
      this.$utils.httpPost(
        this.$config.api.STUDIO_SLOT_SAVE_QUESSENTENCE,
        {
          intentId: this.intentId,
          slotId: this.dialog.data.id,
          questionSentence: JSON.stringify(
            list.map((item) => {
              return {
                answer: item.answer,
                labels: (item.labels || []).map(({ picture, ...rest }) => {
                  return { ...rest }
                }),
              }
            })
          ),
        },
        {
          success: (res) => {
            self.saving = false
            let textInfo = mode === 'sub' ? '删除成功' : '保存成功'
            self.$message.success(textInfo)
            if (mode === 'add') {
              self.$refs.qaRef.resetInitialStatus()
            }
            if (mode === 'edit') {
              self.questionSentence = (list || []).map((it, i) => {
                if (index === i) {
                  return {
                    ...it,
                    changed: false,
                  }
                } else {
                  return { ...it }
                }
              })
            } else {
              self.questionSentence = list || []
            }

            self.$emit('change')
          },
          error: (err) => {
            self.saving = false
            self.$message.error('请求失败，请稍后重试')
          },
        }
      )
    },
  },
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="scss" scoped>
.dialog-official-intents {
  margin-bottom: 15px;
}
.dialog-official-intent-selectnum {
  color: $grey4;
  margin-right: 24px;
}

.container {
  width: 100%;
  border: 1px solid #d5d8de;
  padding: 0 16px;
  .confirm-adder {
    display: flex;
    align-items: center;
  }
  .confirm-list {
    padding-top: 15px;
    > li {
      display: flex;
      align-items: center;
      position: relative;
      padding-right: 20px;
      &:hover {
        .delete {
          display: block;
        }
      }
      .delete {
        position: absolute;
        right: 0;
        color: #b8babf;
        cursor: pointer;
        display: none;
        color: #1784e9;
        // &:hover {
        //   color: #1784e9;
        // }
      }
    }
    li + li {
      margin-top: 10px;
    }
    margin-bottom: 0;
  }
  .number-label {
    margin-right: 20px;
    color: #b8babf;
  }
}
</style>
