<template>
  <el-dialog
    title="创建技能"
    :visible.sync="dialog.show"
    :close-on-click-modal="false"
    width="480px"
  >
    <el-form
      class="create-skill-form"
      :model="form"
      :rules="rules"
      ref="skillForm"
      label-position="top"
    >
      <!--<el-alert v-if="form.servicePlat == 'AIUI'"
        title="AIUI应用使用的自定义技能，你可以在应用信息中配置。"
        type="info">
      </el-alert>-->
      <!-- 1、待确定技能分类使用的不同类型的值; 2、技能标识不用填？3、普通用户只能看到aiui技能? -->
      <el-form-item label="创建方式" prop="type" class="intent-type-wrap">
        <el-button
          :type="form.type == '私有技能' ? 'primary' : ''"
          plain
          @click="changeSkillType('私有技能')"
          >私有技能</el-button
        >
        <el-button
          :type="form.type == '开放技能' ? 'primary' : ''"
          plain
          @click="changeSkillType('开放技能')"
          >开放技能</el-button
        >
        <p v-if="form.type == '私有技能'" class="skill-introduction">
          私有技能仅自己账户下的应用可以使用
        </p>
        <p v-if="form.type == '开放技能'" class="skill-introduction">
          需要明确入口词才能进入的技能；发布后将出现在技能商店供消费者使用
        </p>
      </el-form-item>
      <el-form-item label="技能名称" prop="zhName" style="margin-top: 24px">
        <el-input
          v-model.trim="form.zhName"
          ref="zhNameInput"
          placeholder="支持中英文/数字/小数点/短横线/下划线，不超过32个字符"
        />
        <input type="text" style="display: none" />
      </el-form-item>

      <template v-if="form.type == '私有技能'">
        <el-form-item class="skill-name-input" label="技能标识" prop="name">
          <el-input
            ref="nameInput"
            v-model.trim="form.name"
            placeholder="支持英文/数字/下划线格式，不超过15个字符"
          ></el-input>
        </el-form-item>
        <!-- <el-form-item v-if="qcAuth" class="qc" label="QC技能" prop="qc">
          <el-checkbox v-model="form.qc"
            >创建为QC技能（用来控制用户问询使用哪个技能来回答）</el-checkbox
          >
        </el-form-item> -->
        <el-form-item
          v-if="isCreateMultiLanguage"
          class="qc"
          label="维语技能"
          prop="language"
        >
          <el-checkbox v-model="form.language">创建为维语技能</el-checkbox>
        </el-form-item>
      </template>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button
        class="dialog-btn"
        type="primary"
        style="min-width: 104px"
        @click="save"
        :loading="saving"
      >
        {{ saving ? '创建中...' : '创建' }}
      </el-button>
    </span>
  </el-dialog>
</template>

<script>
import dicts from '@M/dicts'
import { mapGetters } from 'vuex'
export default {
  props: {
    dialog: {
      type: Object,
      default: {},
    },
  },
  data() {
    return {
      saving: false,
      form: {
        type: '开放技能',
        // secondType: '1',
        // servicePlat: 'AIUI',
        name: '',
        zhName: '',
        qc: '',
        language: '',
        // appPackage: '',
      },
      rules: {
        name: this.$rules.skillName(),
        zhName: this.$rules.skillZhName(),
        // appPackage: [
        //   this.$rules.required('关联包名不能为空'),
        //   this.$rules.onlyNumAndEnglishChar(),
        // ],
      },
    }
  },
  computed: {
    // privateSkill() {
    //   return this.form.type === '私有技能'
    // },
    ...mapGetters({
      limitCount: 'aiuiApp/limitCount',
      subAccount: 'user/subAccount',
      qcAuth: 'aiuiApp/qcAuth',
    }),
    isCreateMultiLanguage() {
      return this.limitCount['create_uyghur'] > 0 // 0：不能；>0： 能 创建维语技能
    },
  },
  watch: {
    'dialog.show': function (val, oldVal) {
      let self = this
      if (val) {
        this.form = {
          type: '私有技能',
          // secondType: '1',
          // servicePlat: 'AIUI',
          name: '',
          zhName: '',
          qc: '',
          // appPackage: '',
        }
        this.$refs.skillForm && this.$refs.skillForm.resetFields()
        this.$nextTick(function () {
          self.$refs.zhNameInput && self.$refs.zhNameInput.focus()
        })
        this.$store.dispatch('aiuiApp/setQcAuth')
      } else {
      }
    },
    // 'form.type': function (val) {
    //   if (val == '开放技能') {
    //     this.form.servicePlat = 'iFLYOS'
    //   }
    // },
  },
  methods: {
    clearValidateForm() {
      this.$refs.skillForm && this.$refs.skillForm.clearValidate()
    },
    inputEnter(type) {
      // if (!type) {
      //   // AIUI 技能，仅能标识 input enter
      //   this.save()
      // }
      // if (type == 'iFLYOS' && this.form.servicePlat == 'iFLYOS') {
      //   this.save()
      // } else {
      //   this.$refs.nameInput && this.$refs.nameInput.focus()
      // }
    },

    save() {
      let self = this
      if (this.saving) {
        return
      }
      this.$refs.skillForm.validate((valid) => {
        if (valid) {
          this.saving = true
          let data = {}

          let api =
            this.form.type === '私有技能'
              ? this.$config.api.STUDIO_ADD_EDIT_PRIVATESKILL
              : this.$config.api.STUDIO_ADD_EDIT_OPENSKILL

          if (this.form.type === '私有技能') {
            data = {
              zhname: this.form.zhName,
              name: this.form.name,
              type: this.form.qc ? 8 : 2,
            }
            if (this.form.language) {
              data = {
                ...data,
                language: 'zh_cn',
                accent: 'uyghur',
              }
            }
            // data.secondType = parseInt(this.form.secondType)
            data.secondType = 1
          } else {
            data = {
              zhName: this.form.zhName,
            }
          }

          this.$utils.httpPost(api, data, {
            success: (res) => {
              self.$message.success('创建成功')
              setTimeout(function () {
                self.subAccount
                  ? self.$router.push({
                      name: 'sub-skill-intentions',
                      params: { skillId: res.data.id || res.data.skillId },
                    })
                  : self.$router.push({
                      name: 'skill-intentions',
                      params: { skillId: res.data.id || res.data.skillId },
                    })
              }, 1500)
            },
            error: (err) => {
              this.saving = false
              console.log('page=>>')
              console.log(err)
            },
          })
        }
      })
    },
    changeSkillType(val) {
      this.clearValidateForm()
      this.form.type = val
    },
  },
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="scss" scoped>
.skill-privacy {
  display: inline-block;
  vertical-align: bottom;
  width: 110px;
  height: 24px;
  line-height: 24px;
  text-align: center;
  color: $success;
  border-radius: 12px;
  background-color: $success-light-12;
}
</style>

<style lang="scss">
.skill-name-input {
  .el-form-item__content {
    display: flex;
  }
  &-namespace {
    width: 140px;
    input {
      border-right: 0;
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
    }
  }
}
.create-skill-form {
  .el-radio__input.is-checked + .el-radio__label {
    color: $semi-black;
  }
  .el-radio-button__inner {
    background-color: transparent;
  }
  .el-alert__content {
    padding: 0;
    padding-right: 46px;
  }
  .el-alert--info {
    color: $grey5;
    border-color: $grey1;
  }
  .el-alert {
    margin-top: 8px;
    background-color: $grey1;
  }
  .el-alert__closebtn {
    color: $grey4;
  }
  .qc {
    .el-form-item__content {
      line-height: 22px;
    }
  }
}

.intent-type-wrap .el-form-item__content {
  font-size: 0;
  .el-button {
    margin-left: 0;
    padding: 10px 16px;
    min-width: unset;
    width: 208px;
    background: $white;
    border-color: $grey3;
    border-right: none;
    border-radius: 0;
    &:first-child {
      border-radius: 2px 0 0 2px;
    }
    &:nth-child(2) {
      border-right: 1px solid $grey3;
      border-radius: 0 2px 2px 0;
      &:hover {
        border-right: 1px solid $primary;
      }
    }
  }
  .el-button:hover,
  .el-button:focus,
  .el-button--primary {
    border-color: $primary;
    background-color: $primary-light-12;
  }
  .el-button:hover + .el-button {
    border-left-color: $primary;
  }
  .el-button:focus + .el-button,
  .el-button--primary + .el-button {
    border-left-color: transparent;
  }
  .el-button--primary:hover,
  .el-button--primary:focus {
    box-shadow: unset;
  }
  .el-button--primary {
    color: $primary;
    border: 1px solid $primary !important;
  }
}
.skill-introduction {
  font-size: 12px;
  color: #ffa400;
  margin-bottom: 0;
  line-height: 14px;
}
</style>
