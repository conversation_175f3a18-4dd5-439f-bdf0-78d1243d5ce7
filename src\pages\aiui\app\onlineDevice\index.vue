<template>
  <div class="main-content-container main_content_container">
    <div class="online-device">
      <div class="online_device_header">
        <div class="online_device_header_left">
          <span class="text">剩余设备：</span>
          <span class="left-number" v-if="surplusUser === '不限制'">
            不限
          </span>
          <span class="left-number" v-else>{{ surplusUser }}</span>
          <el-tooltip
            class="item"
            effect="dark"
            v-if="surplusUser"
            placement="bottom"
          >
            <div slot="content">
              装机量授权请邮件联系商务<br />
              <EMAIL><br />
              <span
                class="cp"
                @click="$utils.copyClipboard('<EMAIL>')"
                >复制邮箱</span
              >
            </div>
            <el-button type="text" size="small">提升额度</el-button>
          </el-tooltip>
        </div>
        <div class="online_device_header_right">
          <span>开启设备SN白名单</span>
          <el-tooltip
            content="面向AIUI交互授权类型，开启后仅支持已上传sn号的设备交互"
            placement="bottom"
            style="margin-left: 5px"
          >
            <i class="el-icon-question" />
          </el-tooltip>
          <el-switch
            v-model="isSN"
            @change="onSnChange"
            inactive-text=""
            active-text=""
            style="margin: 0 12px"
          >
          </el-switch>

          <el-button plain @click="downloadTemplate" style="margin: 0 12px"
            >下载模板</el-button
          >
          <el-upload
            :action="`${this.$config.server}/aiui/web/app/sn/import?appid=${this.$route.params.appId}`"
            accept=".csv, .xlsx, .xls, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel, text/csv"
            :before-upload="beforeUpload"
            :on-success="uploaded"
            :on-error="handleUploadError"
            :show-file-list="false"
            ><el-button type="primary" size="small"
              >导入设备白名单</el-button
            ></el-upload
          >
        </div>
      </div>

      <div class="online_device_tabs_wrapper">
        <el-tabs
          type="border-card"
          v-model="activeName"
          @tab-click="handleClick"
        >
          <el-tab-pane label="设备明细" name="first">
            <device-detail ref="deviceDetail"></device-detail>
          </el-tab-pane>
          <el-tab-pane name="second">
            <div slot="label" class="tip-info">
              鉴权失败设备
              <el-tooltip
                content="导入失败或设备上报SN不在白名单内鉴权失败的设备SN"
                placement="bottom"
              >
                <i class="el-icon-question" />
              </el-tooltip>
              <span class="fail-number">({{ failCount }})</span>
            </div>

            <device-fail-detail
              ref="deviceFailDetail"
              @deleted="onDeleted"
              @imported="onImported"
            ></device-fail-detail>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>

<script>
import deviceDetail from './deviceDetail.vue'
import deviceFailDetail from './deviceFailDetail.vue'
/**
 * 激活设备明细
 */
export default {
  name: 'online-device',
  components: { deviceDetail, deviceFailDetail },
  data() {
    return {
      pageOptions: {
        title: '激活设备明细',
        loading: false,
        returnBtn: false,
      },
      isSN: true,
      activeName: 'first',
      surplusUser: null,
      failCount: 0,
    }
  },
  watch: {},
  created() {
    this.getSwitchStatus()
    this.getUserDataCount()
    this.getFailCount()
  },
  methods: {
    handleClick(tab, event) {
      console.log(tab, event)
    },

    onSnChange(val) {
      console.log('onSnChange', val)
      let data = { appid: this.$route.params.appId, flag: val }
      this.$utils.httpPost(this.$config.api.APP_SN_SWITCH, data, {
        success: (res) => {
          if (res.flag) {
          }
        },
        error: (err) => {},
      })
    },
    uploaded(data) {
      console.log('-----uploaded-------', data)
      let desc = data.desc
      try {
        let descArr = JSON.parse(desc)
        if (descArr instanceof Array) {
          desc = descArr.join('，')
        }
      } catch (e) {}
      if (data.code == '0') {
        this.$message.success(desc)
        this.$refs['deviceDetail'] &&
          this.$refs['deviceDetail'].getSearchResult()
        this.$refs['deviceFailDetail'] &&
          this.$refs['deviceFailDetail'].getSearchResult()
        this.getUserDataCount()
        this.getFailCount()
      } else {
        this.$message.error(desc)
      }
    },
    beforeUpload(file) {
      let reg = /\.xls(x)?$/i
      let type = reg.test(file.name)
      let unExceed = file.size < 1024 * 1024 * 10
      if (!type) {
        this.$message.error('仅支持xls或xlsx文件')
      }
      if (!unExceed) {
        this.$message.error(`文件不能超过10M`)
      }
      // this.$emit('setLoad', type && unExceed)
      return type && unExceed
    },
    handleUploadError(val) {
      // this.$emit('setLoad', false)
      console.log('-----------------error---------', val)
    },
    getSwitchStatus() {
      this.$utils.httpGet(
        this.$config.api.APP_SN_SWITCH_GET,
        { appid: this.$route.params.appId },
        {
          success: (res) => {
            if (res.flag) {
              console.log('-------res.data.flag----------', res.data.flag)
              this.isSN = res.data.flag
            }
          },
          error: (err) => {},
        }
      )
    },
    getUserDataCount() {
      this.$utils.httpGet(
        this.$config.api.AIUI_STATISTIC_USER_COUNT,
        {
          appid: this.$route.params.appId,
        },
        {
          success: (res) => {
            this.surplusUser = res.data.surplusUser
          },
          error: (err) => {},
        }
      )
    },
    downloadTemplate() {
      window.open(
        'https://aiui-file.cn-bj.ufileos.com/sn_template.xlsx',
        '_blank'
      )
    },
    getFailCount() {
      this.$utils.httpGet(
        this.$config.api.APP_SN_GETFAIL_COUNT,
        { appid: this.$route.params.appId },
        {
          success: (res) => {
            if (res.flag) {
              console.log('-------res.data.flag----------', res.data.flag)
              this.failCount = res.data.count
            }
          },
          error: (err) => {},
        }
      )
    },

    onDeleted() {
      this.getFailCount()
    },
    onImported() {
      this.getFailCount()
      this.getUserDataCount()
    },
  },
}
</script>

<style scoped lang="scss">
@import '../config/main/common.scss';

.main_content_container {
  background-color: $secondary-bgc;
  height: calc(100vh - 128px);
}
.online_device_header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #fff;
  border-radius: 12px;
  padding: 30px;
  .online_device_header_left {
    display: flex;
    align-items: center;
    .text {
      color: #8d8d99;
    }
  }
  .online_device_header_right {
    display: flex;
    align-items: center;
  }
}

.online_device_tabs_wrapper {
  margin-top: 20px;
  border-radius: 12px;
  overflow: hidden;
  :deep(.el-tabs) {
    margin-left: 0px;
    .el-tabs__nav-wrap {
      margin-bottom: -1.5px;
    }
  }
  .el-tabs--border-card {
    border: none;
    box-shadow: none;
  }
}

.online-device-header {
  display: flex;
  margin: 20px 0;
  .online-device-header-item + .online-device-header-item {
    margin-left: 15px;
  }
  :deep(.el-date-editor .el-range-separator) {
    padding: 0 !important;
  }
}

.left-number {
  font-size: 34px;
  font-weight: 700;
  color: #222222;
  transform: translateY(-5px);
}

.tip-info {
  .fail-number {
    font-size: 16px;
    font-weight: 400;
    color: #ff5a5a;
  }
}

.tabs-container {
  background: #fff;
  top: -1px;
  z-index: 1;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  :deep(.el-tabs__header) {
    margin: 0;
  }
  :deep(.el-tabs__item) {
    padding: 0 10px;
    height: 40px;
    line-height: 40px;
  }
  :deep(.el-tabs__nav-wrap::after) {
    display: none;
  }
}
</style>
