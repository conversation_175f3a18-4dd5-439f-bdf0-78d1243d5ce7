<template>
  <os-page :options="pageOptions">
    <studio-agent-header-right slot="btn" />
    <div class="flex-btn">
      <div class="corpus-text"></div>
      <el-input
        class="search-area"
        placeholder="搜索语料"
        v-model="serachCorpus"
        style="width: 300px"
        @keyup.enter="getIndentList(1)"
      >
        <i
          slot="suffix"
          class="el-input__icon el-icon-search search-area-btn"
          @click="getCorpusList(1)"
        />
      </el-input>
    </div>

    <!-- <div class="mgb24" @keyup.enter="addCorpus">
      <el-input class="search-area" placeholder="回车添加用户的常用表达，例如：明天合肥天气怎么样" v-model="myCorpus">
        <i slot="suffix" class="el-input__icon el-icon-search search-area-btn" @click="addCorpus"></i>
      </el-input>
    </div> -->

    <div class="mgb24" @keyup.enter="addCorpus">
      <el-input
        class="corpus-add-area"
        ref="addUtter"
        placeholder="回车添加用户的常用表达，例如：明天合肥天气怎么样"
        v-model="corpus"
      >
        <i slot="prefix" class="el-input__icon ic-r-plus"></i>

        <el-tooltip content="语料泛化" placement="top" slot="suffix">
          <svg-icon
            iconClass="agent"
            className="pointer"
            @click.native="expandCorpus"
          />
        </el-tooltip>

        <el-button
          slot="suffix"
          type="text"
          class="corpus-add-area-addbtn"
          size="small"
          @click="addCorpus"
        >
          添加
        </el-button>
      </el-input>
    </div>
    <os-table
      :tableData="tableData"
      @del="del"
      :border="true"
      :show-header="false"
      @change="getCorpusList"
    >
      <el-table-column prop="corpus"> </el-table-column>
    </os-table>

    <CorpusDialog ref="CorpusDialog" @refresh="refresh" />
  </os-page>
</template>

<script>
import CorpusDialog from './corpusDialog.vue'
import StudioAgentHeaderRight from '@C/studioAgentHeaderRight.vue'
export default {
  name: 'IflyAIuiWebCorpus',

  components: {
    CorpusDialog,
    StudioAgentHeaderRight,
  },

  data() {
    return {
      pageOptions: {
        title: '语料',
        loading: false,
      },
      tableData: {
        loading: true,
        total: 0,
        page: 1,
        size: 10,
        handles: ['del'],
        // handleColumnText: '操作',
        list: [],
      },

      serachCorpus: null,
      corpus: '',
    }
  },

  mounted() {
    this.getCorpusList(1)
  },

  methods: {
    getCorpusList(page) {
      const params = {
        corpus: this.serachCorpus,
        intentId: this.$route.params.intentId,
        pageIndex: page || this.tableData.page,
        pageSize: this.tableData.size,
      }
      this.$utils.httpPost(
        this.$config.api.AGENT_CORPUS_LIST,
        JSON.stringify(params),
        {
          config: {
            headers: {
              'Content-Type': 'application/json;charset=UTF-8',
            },
          },
          success: (res) => {
            if (res.code == '0') {
              this.tableData.list = res.data.data
              this.tableData.total = res.data.totalSize
              this.tableData.page = res.data.pageIndex
              this.tableData.size = res.data.pageSize
              this.tableData.loading = false
            }
          },
          error: (err) => {
            // this.$message.error(err.desc)
          },
        }
      )
    },
    refresh() {
      this.getCorpusList()
    },
    expandCorpus() {
      this.$refs.CorpusDialog.show(this.$route.params.intentId)
    },
    addCorpus() {
      const params = {
        intentId: this.$route.params.intentId,
        corpus: this.corpus,
      }
      this.$utils.httpPost(
        this.$config.api.AGENT_CORPUS_ADD,
        JSON.stringify(params),
        {
          config: {
            headers: {
              'Content-Type': 'application/json;charset=UTF-8',
            },
          },
          success: (res) => {
            if (res.code == '0') {
              this.$message.success('添加成功')
              this.getCorpusList()
              this.corpus = ''
            }
          },
          error: (err) => {
            this.$message.error(err.desc)
          },
        }
      )
    },
    del(data) {
      const params = {
        intentId: this.$route.params.intentId,
        corpusId: data.id,
      }
      this.$utils.httpPost(
        this.$config.api.AGENT_CORPUS_DELETE,
        JSON.stringify(params),
        {
          config: {
            headers: {
              'Content-Type': 'application/json;charset=UTF-8',
            },
          },
          success: (res) => {
            if (res.code == '0') {
              this.$message.success('操作成功')
              this.getCorpusList()
            }
          },
          error: (err) => {
            this.$message.error(err.desc)
          },
        }
      )
    },
  },
}
</script>

<style lang="scss" scoped>
.flex-btn {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;
  margin-bottom: 10px;
}
.corpus-text {
  // border-left: 3px solid blue;
  padding-left: 10px;
}

.corpus-add-area,
.corpus-collapse-input {
  input {
    border: 1px solid $grey2;
    padding: 0 52px !important;
  }
  &-addbtn {
    min-width: 28px;
    padding: 0 16px;
    line-height: 44px;
  }
}
.has-gutter {
  display: none;
}
</style>
