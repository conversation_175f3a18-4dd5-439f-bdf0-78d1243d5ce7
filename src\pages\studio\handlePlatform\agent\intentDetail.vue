<template>
  <os-page class="intent-page" :options="pageOptions" @returnCb="pageReturn">
    <div slot="headLeft">
      <span
        class="intention-title"
        :title="originIntentInfo.intentName || '-'"
        >{{ originIntentInfo.intentName || '-' }}</span
      >
    </div>

    <studio-agent-header-right slot="btn" v-if="official !== 1" />

    <div class="intention-page">
      <os-collapse
        :default="false"
        size="large"
        title="意图信息"
        style="position: relative"
      >
        <template>
          <div
            v-if="!edit && official == 0"
            class="ib intention-edit-btn"
            @click.prevent.stop="toEdit"
          >
            <i class="ic-r-edit" />
            <span>编辑</span>
          </div>
        </template>

        <el-form
          class="intent-info-form"
          :model="intentForm"
          :rules="rules"
          ref="intentForm"
          label-width="100px"
          label-position="left"
        >
          <el-form-item prop="intentName" class="inline-el-form-item">
            <label slot="label">
              <span class="intention-form-label">意图名称</span>
            </label>
            <div class="intent-zh-name" v-if="!edit" :title="intentForm.zhName">
              {{ originIntentInfo.intentName }}
            </div>
            <el-input
              v-else
              v-model="intentForm.intentName"
              :title="originIntentInfo.intentName"
            />
          </el-form-item>

          <el-form-item prop="intentNameEn" class="inline-el-form-item">
            <label slot="label">
              <span class="intention-form-label">英文标识</span>
            </label>
            <div
              class="intent-name"
              v-if="!edit"
              :title="intentForm.intentNameEn"
            >
              {{ originIntentInfo.intentNameEn }}
            </div>
            <el-input
              v-else
              v-model="intentForm.intentNameEn"
              :title="originIntentInfo.intentNameEn"
            />
          </el-form-item>

          <el-form-item prop="intentDesc" class="inline-el-form-item">
            <label slot="label">
              <span class="intention-form-label">描述</span>
            </label>

            <el-tooltip
              effect="dark"
              :content="originIntentInfo.intentDesc"
              placement="bottom"
              v-if="!edit"
              :disabled="!isOverflow"
            >
              <div
                class="intent-name"
                ref="intentNameRef"
                @mouseenter="checkOverflow"
              >
                {{ originIntentInfo.intentDesc }}
              </div>
            </el-tooltip>

            <!-- <div
              class="intent-name"
              v-if="!edit"
              :title="intentForm.intentDesc"
            >
              {{ originIntentInfo.intentDesc }}
            </div> -->
            <el-input
              v-else
              v-model="intentForm.intentDesc"
              :title="originIntentInfo.intentDesc"
            />
          </el-form-item>

          <el-form-item v-if="edit" style="margin: 30px 0px">
            <el-button
              type="primary"
              size="small"
              style="min-width: 80px"
              @click="editSubmit('intentForm')"
              :loading="editSaveLoading"
            >
              {{ editSaveLoading ? '保存中...' : '保存' }}
            </el-button>
            <el-button
              size="small"
              style="min-width: 80px"
              v-on:click="
                edit = !edit
                $refs.intentForm.resetFields()
              "
            >
              取消
            </el-button>
          </el-form-item>
        </el-form>
      </os-collapse>

      <os-divider class="mgt28" />

      <os-collapse
        :default="true"
        size="large"
        title="语料"
        style="position: relative"
      >
        <div class="flex-btn">
          <div class="corpus-text"></div>
          <el-input
            class="search-area"
            placeholder="搜索语料"
            v-model="serachCorpus"
            style="width: 300px"
            @keyup.enter.native="getCorpusList(1)"
          >
            <i
              slot="suffix"
              class="el-input__icon el-icon-search search-area-btn"
              @click="getCorpusList(1)"
            />
          </el-input>
        </div>

        <div class="mgb12" @keyup.enter="addCorpus">
          <el-input
            class="corpus-add-area"
            ref="addUtter"
            placeholder="回车添加用户的常用表达，例如：明天合肥天气怎么样"
            v-model="corpus"
            :disabled="official == 1"
          >
            <i slot="prefix" class="el-input__icon ic-r-plus"></i>

            <el-tooltip
              v-if="official == 0"
              content="AI扩写"
              placement="top"
              slot="suffix"
            >
              <svg-icon
                iconClass="magic"
                className="pointer"
                @click.native="expandCorpus"
                style="color: '#009bff'; cursor: pointer"
              />
            </el-tooltip>

            <el-button
              slot="suffix"
              type="text"
              class="corpus-add-area-addbtn"
              size="small"
              @click="addCorpus"
              :disabled="official == 1"
            >
              添加
            </el-button>
          </el-input>
        </div>

        <os-table
          :tableData="tableData"
          @del="del"
          :border="true"
          :show-header="false"
          @change="getCorpusList"
        >
          <el-table-column prop="corpus"> </el-table-column>
        </os-table>
      </os-collapse>
    </div>

    <CorpusDialog ref="CorpusDialog" @refresh="refresh" />
  </os-page>
</template>

<script>
import CorpusDialog from './corpusDialog.vue'
import StudioAgentHeaderRight from '@C/studioAgentHeaderRight.vue'
export default {
  name: 'AiuiWebIntentDetail',

  components: {
    CorpusDialog,
    StudioAgentHeaderRight,
  },

  data() {
    return {
      isOverflow: false,
      official: 0,
      intentVersion: '',
      pageOptions: {
        loading: false,
        returnBtn: true,
        showHead: true,
      },

      originIntentInfo: {
        intentName: null,
        intentNameEn: null,
        intentDesc: null,
      },
      intentForm: {
        intentName: null,
        intentNameEn: null,
        intentDesc: null,
      },
      edit: false,
      editSaveLoading: false,

      tableData: {
        loading: true,
        total: 0,
        page: 1,
        size: 10,
        handles: ['del'],
        // handleColumnText: '操作',
        list: [],
      },
      serachCorpus: null,
      corpus: '',
      rules: {
        intentName: [
          { required: true, message: '请填写意图名称', trigger: 'blur' },
          {
            pattern: /^[\u4e00-\u9fa5a-zA-Z0-9._-]{0,32}$/,
            message:
              '输入内容只能包含中文、英文字母、数字、小数点、短横线和下划线，且长度不超过32个字符',
            trigger: 'blur',
          },
        ],
        intentNameEn: [
          { required: true, message: '请填写意图标识', trigger: 'blur' },
          {
            pattern: /^[a-zA-Z0-9._-]{0,32}$/,
            message:
              '输入内容只能包含英文字母、数字、小数点、短横线和下划线,且长度不超过32个字符',
            trigger: 'blur',
          },
        ],
        intentDesc: [
          { required: true, message: '请填写描述', trigger: 'blur' },
          { max: 250, message: '描述不能超过250个字符', trigger: 'blur' },
        ],
      },
    }
  },
  created() {
    this.official = this.$route.params.official
    this.intentVersion = this.$route.params.intentVersion
    if (this.official == 1) {
      this.tableData.handles = []
    }
  },
  mounted() {
    this.getIntentDetail()
    this.getCorpusList(1)
  },

  methods: {
    checkOverflow() {
      this.$nextTick(() => {
        const el = this.$refs.intentNameRef
        if (el) {
          this.isOverflow = el.scrollWidth > el.clientWidth
        }
      })
    },

    // 通过id获取意图信息
    getIntentDetail() {
      const params =
        this.official == 1
          ? {
              intentId: this.$route.params.intentId,
              intentVersion: this.intentVersion,
            }
          : { intentId: this.$route.params.intentId }

      const API =
        this.official == 1
          ? this.$config.api.AGENT_INTENT_DETAIL_OFFICIAL
          : this.$config.api.AGENT_INTENT_DETAIL

      this.$utils.httpPost(API, JSON.stringify(params), {
        config: {
          headers: {
            'Content-Type': 'application/json;charset=UTF-8',
          },
        },
        success: (res) => {
          this.originIntentInfo = res.data
        },
        error: (err) => {},
      })
    },
    refresh() {
      this.getCorpusList()
    },

    addCorpus() {
      if (!this.corpus.trim()) {
        this.$message.warning('输入内容不能为空')
        return
      }
      const params = {
        intentId: this.$route.params.intentId,
        corpusList: [this.corpus],
      }
      this.$utils.httpPost(
        this.$config.api.AGENT_CORPUS_ADD,
        JSON.stringify(params),
        {
          config: {
            headers: {
              'Content-Type': 'application/json;charset=UTF-8',
            },
          },
          success: (res) => {
            if (res.code == '0') {
              this.$message.success('添加成功')
              this.getCorpusList()
              this.corpus = ''
            }
          },
          error: (err) => {
            this.$message.error(err.desc)
          },
        }
      )
    },

    getCorpusList(page) {
      let params = {
        corpus: this.serachCorpus,
        intentId: this.$route.params.intentId,
        pageIndex: page || this.tableData.page,
        pageSize: this.tableData.size,
      }
      if (this.official == 1) {
        params = {
          ...params,
          intentVersion: this.intentVersion,
        }
      }

      const API =
        this.official == 1
          ? this.$config.api.AGENT_CORPUS_LIST_OFFICIAL
          : this.$config.api.AGENT_CORPUS_LIST

      this.$utils.httpPost(API, JSON.stringify(params), {
        config: {
          headers: {
            'Content-Type': 'application/json;charset=UTF-8',
          },
        },
        success: (res) => {
          if (res.code == '0') {
            this.tableData.list = res.data.data
            this.tableData.total = res.data.totalSize
            this.tableData.page = res.data.pageIndex
            this.tableData.size = res.data.pageSize
            this.tableData.loading = false
          }
        },
        error: (err) => {
          this.tableData.loading = false
          // this.$message.error(err.desc)
        },
      })
    },

    del(data) {
      const params = {
        intentId: this.$route.params.intentId,
        corpusId: data.id,
      }
      this.$utils.httpPost(
        this.$config.api.AGENT_CORPUS_DELETE,
        JSON.stringify(params),
        {
          config: {
            headers: {
              'Content-Type': 'application/json;charset=UTF-8',
            },
          },
          success: (res) => {
            if (res.code == '0') {
              this.$message.success('操作成功')
              this.getCorpusList()
            }
          },
          error: (err) => {
            this.$message.error(err.desc)
          },
        }
      )
    },

    expandCorpus() {
      this.$refs.CorpusDialog.show(this.$route.params.intentId)
    },

    editSubmit(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          const params = {
            intentName: this.intentForm.intentName,
            intentNameEn: this.intentForm.intentNameEn,
            intentDesc: this.intentForm.intentDesc,
            intentId: this.$route.params.intentId, //   编辑意图 传intentId
          }
          this.$utils.httpPost(
            this.$config.api.AGENT_INTENT_EDIT,
            JSON.stringify(params),
            {
              config: {
                headers: {
                  'Content-Type': 'application/json;charset=UTF-8',
                },
              },
              success: (res) => {
                if (res.code == '0') {
                  this.$message.success('编辑成功')
                  this.editSaveLoading = false
                  this.edit = false
                  this.getIntentDetail()
                }
              },
              error: (err) => {
                this.editSaveLoading = false
                this.$message.error(err.desc)
              },
            }
          )
        }
      })
    },

    pageReturn() {
      // this.$router.push({ name: 'agent-intent' })
      this.$router.go(-1)
    },

    toEdit() {
      this.edit = true
      this.$refs.intentForm.resetFields()
      this.intentForm = {
        intentName: this.originIntentInfo.intentName,
        intentNameEn: this.originIntentInfo.intentNameEn,
        intentDesc: this.originIntentInfo.intentDesc,
      }
    },
  },
}
</script>

<style lang="scss">
.intent-page {
  .os-collapse-title {
    margin: 28px 0;
    .ic-r-angle-d {
      color: $grey5;
    }
  }
  .ic-r-edit {
    color: $primary;
  }
}

.intent-info-form {
  margin-left: 10px;
  .el-form-item__content {
    height: 44px;
  }
}
.intention-edit-btn {
  position: absolute;
  top: 6px;
  left: 105px;
  margin-left: 16px;
  color: $primary;
  font-size: 14px;
  cursor: pointer;
  i {
    margin-right: 0;
  }
}
.intention-head-hovershow {
  display: inline-flex;
}
.intention-title {
  display: inline-block;
  vertical-align: bottom;
  max-width: 180px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.intention-form-label {
  font-weight: 600;
}
.utterance-edit-tip {
  margin-bottom: 16px;
  padding: 9px 16px;
  height: 40px;
  background: rgba(242, 245, 247, 1);
  border-radius: 2px;
}
.utterance-add-area,
.utterance-collapse-input {
  input {
    border: 1px solid $grey2;
    padding: 0 52px !important;
  }
  &-addbtn {
    min-width: 28px;
    padding: 0 16px;
    line-height: 44px;
  }
}

.utterance-collapse-input input {
  border: 0;
}

.utterance-collapse-item {
  border: 1px solid $grey2;
  border-bottom: 0;
  transition: all 0.2s;
  &-show {
    border-bottom: 2px solid $primary;
  }
}

.utterance-collapse-item-title {
  position: relative;
  &-del {
    position: absolute;
    right: 16px;
    top: 0;
    bottom: 0;
    margin: auto;
    line-height: 44px;
    cursor: pointer;
    display: none;
  }
  &:hover {
    .utterance-collapse-item-title-del {
      display: block;
    }
  }
}

.utterance-collapse-item:last-child {
  border-bottom: 1px solid $grey2;
}

.utterance-collapse-item-content {
  max-height: 0;
  overflow: hidden;
  // transition: max-height 0.2s ease-in;
  &-show {
    transition: max-height 0.2s ease-in;
    max-height: 900px;
    overflow: auto;
  }
}

.slot-table {
  .el-table__body tr:hover > td {
    background-color: #fff;
  }
  &-slotname input {
    border: 0 !important;
    padding: 0;
  }
  td {
    padding: 0;
    height: 56px;
    line-height: 56px;
    transition: none !important;
  }
}
.intention-page {
  .el-icon-question {
    color: $grey3;
  }
}
.inline-el-form-item {
  display: inline-block;
  width: 45%;
  &:first-child {
    margin-right: 55px;
  }
  margin-bottom: 35px;
}
.intent-zh-name,
.intent-name {
  display: inline-block;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.flex-btn {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;
  margin-bottom: 10px;
}
.corpus-text {
  // border-left: 3px solid blue;
  padding-left: 10px;
}

.corpus-add-area,
.corpus-collapse-input {
  input {
    border: 1px solid $grey2;
    padding: 0 52px !important;
  }
  &-addbtn {
    min-width: 28px;
    padding: 0 16px;
    line-height: 44px;
  }
}
.has-gutter {
  display: none;
}
</style>
