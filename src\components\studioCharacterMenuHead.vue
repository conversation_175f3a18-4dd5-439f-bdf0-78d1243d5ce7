<template>
  <div class="studio-skill-menu-head">
    <div class="studio-skill-menu-head-return" @click="returnCb">
      <svg-icon
        iconClass="secondary-return"
        :customStyle="{
          width: '13px',
          height: '9px',
          marginRight: '5px',
          transform: 'translateY(1px)',
        }"
      />
      <span>返回列表</span>
    </div>
    <!-- <div
      class="studio-skill-menu-head-skill-name"
      @click="openSelectSkill($event)"
    >
      <span :title="character.name || '-'">{{ character.name || '-' }}</span>
    </div> -->
    <el-popover placement="bottom" width="264" trigger="click">
      <select-character />
      <div slot="reference" class="studio-skill-menu-head-skill-name">
        <span :title="character.name || '-'">{{ character.name || '-' }}</span>
      </div>
    </el-popover>
    <!-- <select-skill-popover :subAccount="subAccount" /> -->
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
// import SelectSkillPopover from '@P/studio/skill/dialog/selectSkill.vue'
import SelectCharacter from '@P/studio/character/selectCharacter.vue'

export default {
  name: 'studioCharacterMenuHead',
  data() {
    return {}
  },
  computed: {
    ...mapGetters({
      character: 'studioCharacter/character',
      subAccount: 'user/subAccount',
    }),
  },
  created() {},

  methods: {
    returnCb() {
      this.$router.push({ name: 'studio-handle-platform-characters' })
    },
    openSelectSkill(event) {
      this.$store.dispatch('studioSkill/setSkillPopover', {
        show: true,
        rect: event.target.getBoundingClientRect(),
      })
    },
  },
  components: {
    // SelectSkillPopover,
    SelectCharacter,
  },
}
</script>

<style lang="scss">
.studio-skill-menu-head {
  padding: 16px 22px 0;
  height: 96px;
  &-return {
    display: inline-flex;
    cursor: pointer;
    margin-bottom: 10px;
    font-size: 14px;
    color: $grey4;
    align-items: center;
    i {
      color: $grey4;
    }
  }
  &-skill-name {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    cursor: pointer;
    span {
      font-size: 24px;
      // font-weight: 600;
    }
  }
}
</style>
