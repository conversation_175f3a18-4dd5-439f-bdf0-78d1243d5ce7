<template>
  <div>
    <div class="chain-wrapper">
      <div class="chain-list swiper" id="ability-swiper">
        <div class="swiper-wrapper">
          <div
            :class="{
              'chain-unit ability-unit': true,
              'swiper-slide': true,
              active: currentAbility.abilityId === item.abilityId,
            }"
            v-for="item in abilities"
            :key="item.abilityName"
            @click="setActive(item)"
          >
            <div class="chain-title">{{ item.abilityName }}</div>
            <div class="chain-desc" :title="item.desc" v-if="item.desc">
              {{ item.desc }}
            </div>
            <!-- <div
              class="chain-check"
              v-if="currentAbility.abilityId === item.abilityId"
            ></div>
            <div class="chain-line" :style="{ background: item.bg }"></div> -->
            <div
              class="chain-icon2"
              :style="{
                backgroundImage: 'url(' + item.icon + ')',
              }"
            ></div>
          </div>
        </div>
      </div>

      <div
        class="indicator indicator-pre"
        v-if="currentPage > 1"
        @click="prePage"
      >
        <i class="el-icon-arrow-left"></i>
      </div>
      <div
        class="indicator indicator-next"
        v-if="currentPage < pageNums"
        @click="nextPage"
      >
        <i class="el-icon-arrow-right"></i>
      </div>
    </div>
    <!-- <ul class="pagination">
      <li
        v-for="(item, index) in pageNums"
        :key="index"
        :class="{ active: index + 1 === currentPage }"
        @click="setCurrentPage(index)"
      ></li>
    </ul> -->
    <div class="chain-divider">
      <div class="chain-divider-line"></div>
      <span class="chain-divider-text">原子能力选择</span>
    </div>
  </div>
</template>
<script>
import Swiper from 'swiper'
// import 'swiper/css'
// import 'swiper/swiper-bundle.css'

const PAGESIZE = 4

export default {
  data() {
    return {
      abilities: [],
      currentPage: 1,
      swiper: null,
    }
  },
  computed: {
    pageNums() {
      return Math.ceil(this.abilities.length / 4)
    },
  },
  props: {
    currentAbility: Object,
  },
  created() {
    this.getAbilities()
  },

  methods: {
    setCurrentPage(index) {
      this.currentPage = index + 1
      this.swiper && this.swiper.slideTo(index * PAGESIZE)
    },
    nextPage() {
      if (this.currentPage < this.pageNums) {
        this.currentPage++
        this.swiper && this.swiper.slideNext()
      }
    },
    prePage() {
      if (this.currentPage > 1) {
        this.currentPage--
        this.swiper && this.swiper.slidePrev()
      }
    },
    getAbilities() {
      const bgs = [
        'linear-gradient(180deg, #35aeff, #99e4ff)',
        'linear-gradient(180deg,#566ffa, #91b0ff)',
        'linear-gradient(180deg,#ff8850, #ffcb9d)',
        'linear-gradient(180deg,#35aeff, #99e4ff)',
      ]

      this.$utils.httpGet(
        this.$config.api.RESOURCE_AIHUB_GETABILITYINFO,
        { pageIndex: 1, pageSize: 1000, platform: 2 },
        {
          success: (res) => {
            this.abilities = (res.data.list || []).map((item, index) => {
              return {
                ...item,
                // bg: bgs[index % 4],
              }
            })
            if (this.abilities.length > 0) {
              this.$emit('selected', this.abilities[0])
            }

            this.$nextTick(() => {
              this.swiper = new Swiper('#ability-swiper', {
                slidesPerView: PAGESIZE,
                spaceBetween: 10,
                slidesPerGroup: PAGESIZE,
              })
            })
          },
          error: (err) => {
            console.log('page=>>')
            console.log(err)
          },
        }
      )
    },
    setActive(item) {
      this.$emit('selected', item)
    },
  },
  watch: {},
}
</script>
<style lang="scss" scoped>
@import './common.scss';
</style>
