<template>
  <div class="debug_wrapper" :class="{ debug_wrapper_open: rightDebugOpen }">
    <div
      class="collapse_wrap"
      v-show="!rightDebugOpen"
      @click="$emit('update:rightDebugOpen', !rightDebugOpen)"
    >
      <i class="el-icon-d-arrow-left" />
      <p>调试</p>
    </div>

    <div class="expand_wrap" v-show="rightDebugOpen">
      <div class="open_top">
        <div
          class="open_top_left"
          @click="$emit('update:rightDebugOpen', !rightDebugOpen)"
        >
          <i class="el-icon-d-arrow-right" /> 调试
        </div>
        <el-button size="mini">保存并继续</el-button>
      </div>
      <div class="open_mid">
        <div class="open_mid_title">
          <div>参数配置</div>
          <el-button type="primary" size="mini">调试</el-button>
        </div>

        <el-table :data="tableData" style="width: 100%">
          <el-table-column label="参数名称" width="100">
            <template slot-scope="scope">
              <el-input
                size="small"
                v-model="scope.row.name"
                placeholder="请输入"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column label="参数类型" width="100">
            <template slot-scope="scope">
              <el-input
                size="small"
                v-model="scope.row.type"
                placeholder="请输入"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column label="是否必填" width="80">
            <template slot-scope="scope">
              {{ scope.row.isRequired == 1 ? '是' : '否' }}
            </template>
          </el-table-column>
          <el-table-column label="参数值">
            <template slot-scope="scope">
              <el-input
                size="small"
                v-model="scope.row.value"
                placeholder="请输入"
              ></el-input>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="open_bottom">
        <div class="open_bottom_title">调试结果</div>
        <el-input
          type="textarea"
          :rows="6"
          placeholder="返回报错/不合符条件"
          v-model="textarea"
        >
        </el-input>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DebugWrapper',

  props: {
    rightDebugOpen: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      textarea: '',
      tableData: [
        {
          name: 'test',
          type: 'string',
          isRequired: 1,
          value: 'Test',
        },
        {
          name: 'testyy',
          type: 'string',
          isRequired: 0,
          value: 'XiaoMing',
        },
        {
          name: 'testyy',
          type: 'string',
          isRequired: 0,
          value: 'XiaoMing',
        },
      ],
    }
  },
}
</script>

<style lang="scss" scoped>
.debug_wrapper {
  height: 100%;
  transition: 0.3s;
  width: 64px;
  height: 100%;
  border-left: 1px solid#EFF1F1;
  box-shadow: -12px 0 24px -12px rgba(0, 0, 0, 0.1);
  padding: 24px;
  background-color: #fff;
  &.debug_wrapper_open {
    width: 460px;
    transition: 0.3s;
    overflow-y: auto;
  }

  .collapse_wrap {
    color: $primary;
    font-size: 16px;
    cursor: pointer;
    p {
      font-weight: 600;
    }
  }

  .expand_wrap {
    font-size: 16px;
    .open_top {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 40px;
      .open_top_left {
        display: flex;
        align-items: center;
        cursor: pointer;

        i {
          margin-right: 3px;
        }
      }
    }
    .open_mid {
      .open_mid_title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 15px;
      }
      :deep(.el-table__body-wrapper) {
        overflow-x: hidden;
      }
    }
    .open_bottom {
      margin-top: 30px;
      .open_bottom_title {
        margin-bottom: 15px;
      }
    }
  }
}
</style>
