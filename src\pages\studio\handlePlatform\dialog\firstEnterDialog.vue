<template>
  <el-dialog
    :visible.sync="dialog.show"
    width="600px"
    center
    :show-close="false"
  >
    <span slot="title" style="font-weight: bold; font-size: 18px"
      >🎉&nbsp;文档问答库升级公告&nbsp;🎉</span
    >
    <span style="line-height: 22px">
      <p>亲爱的开发者，</p>
      <p style="text-indent: 27px">
        文档问答服务升级啦！“文档问答”正式更名为“大模型文档问答”。升级版的新特性有：
      </p>
      <p style="text-indent: unset">
        （1）全面升级RAG引擎能力，提升文档问答底座能力。
      </p>
      <p>
        （2）打磨特定场景的文档解析能力，推出说明书、Q-A对和长文本文档的知识构建策略。
      </p>
      <p>
        （3）快速干预调优功能，实现可控的大模型知识回复。通过分段配置与知识点编辑功能，调控文档解析结果。配置检索策略与关联问，能快速干预知识点召回结果，调优问答效果。
      </p>
      <p style="text-indent: 27px">
        温馨提示，旧版问答库的数据已全部迁移到新版本中。如果您之前添加过关联问，请重新指定关联问与知识点的对应关系。更好的大模型文档问答效果由您与我们共创！如有使用问题，请联系商务或************************。
      </p>
      <p style="text-align: right">2024年9月20日</p>
    </span>

    <span slot="footer" class="dialog-footer">
      <el-button @click="dialog.show = false" type="default">关闭</el-button>
    </span>
  </el-dialog>
</template>
<script>
export default {
  props: {
    dialog: Object,
  },
  data() {
    return {}
  },
}
</script>
<style lang="scss" scoped></style>
