<template>
  <el-dialog
    class="dg-body"
    :title="this.title[dialog.name] ? this.title[dialog.name] : '提示'"
    :visible.sync="dialog.show"
  >
    <div>
      {{ this.content[dialog.name] }}
    </div>
    <div class="dg-btn">
      <el-button
        class="template-btn"
        size="small"
        type="primary"
        style="min-width: 80px"
        @click="btnClick"
      >
        {{
          this.btnTitle[dialog.name] ? this.btnTitle[dialog.name] : '我知道了'
        }}
      </el-button>
      <el-button
        class="template-btn"
        :class="{ 'template-btn-visibly': this.showCancel }"
        size="small"
        style="min-width: 80px"
        @click="cancelClick"
      >
        {{ '取消' }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'resourceDialog',
  props: {
    dialog: {
      type: Object,
      default: {},
    },
  },
  data() {
    return {
      showCancel: false,
      title: {
        apply: '申请开通',
      },
      content: {
        apply:
          '默认支持3个词定制，如需要更多请联系你的商务处理，\n' +
          '\n' +
          '或者发邮件至 <EMAIL> 申请开通。',
        templateCover:
          '该资源将覆盖更新，你可以先导出已有资源备份。\n' +
          '\n' +
          '你确定要覆盖吗？',
        clearAwaken:
          '清空后不可恢复，建议你清空前导出备份。\n' +
          '\n' +
          '你确认要清空所有的资源信息吗？',
      },
      btnTitle: {
        templateCover: '确定覆盖',
        clearAwaken: '清空所有',
      },
    }
  },
  watch: {
    'dialog.show': function () {
      this.showCancel = !this.btnTitle[this.dialog.name]
    },
  },
  methods: {
    /*convertBtnTitle (name) {
            switch (name) {
                case 'templateCover' :
                  this.btnTitle = '确定覆盖'
                    break
                default :
                  this.btnTitle = '我知道了'
                    return
            }
          },*/
    btnClick() {
      this.dialog.show = false
      switch (this.dialog.name) {
        case 'templateCover':
          this.$emit('copyTemplate2Awaken', this.dialog.obj)
          break
        case 'clearAwaken':
          this.$emit('clearAwaken')
          break
      }
    },
    cancelClick() {
      this.dialog.show = false
    },
  },
}
</script>

<style scoped lang="scss">
:deep(.el-dialog) {
  margin-top: 15vh !important;
  width: 39% !important;
  height: auto;
}
.dg-btn {
  width: 100%;
  margin: 0 auto;
  margin-top: 5%;
  padding-bottom: 5%;
  display: flex;
  .template-btn {
    margin: 0 auto;
    &-visibly {
      display: none;
    }
    &:last-of-type {
      margin-left: -16%;
    }
  }
}
</style>
