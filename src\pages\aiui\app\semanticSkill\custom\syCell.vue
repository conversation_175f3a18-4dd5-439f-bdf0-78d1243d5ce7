<template>
  <div>
    <div
      :class="['skill', { 'skill-active': item.used }]"
      @click="tojump(item)"
    >
      <div
        v-if="item.type == 2"
        target="_blank"
        :class="[
          'content-wrap',
          {
            'cursor-default':
              !subAccountEditable ||
              (subAccount && !subAccountSkillAuths.hasOwnProperty(item.id)) ||
              subAccountSkillAuths[item.id] == 2,
          },
        ]"
      >
        <div
          v-if="item.outNumber && item.outNumber !== item.newestNumber"
          class="update-state-label"
          style="background: #ff5a5a"
        >
          可更新
        </div>
        <i class="skill-icon">{{ item.zhName && item.zhName.substr(0, 1) }}</i>
        <div class="skill-info">
          <p class="skill-title ib" :title="item.zhName">
            {{ item.zhName }}
          </p>

          <!-- 标题下按钮 -->
          <div class="title-btm-btn-group">
            <p class="ability-tag">{{ item | skillType }}</p>
            <p
              v-if="item.outNumber"
              :title="item.outNumber"
              style="margin-left: 8px; padding-top: 3px"
            >
              {{ item.outNumber }}
            </p>
          </div>
        </div>
      </div>
      <!-- type 为3，定制的技能 -->
      <div
        v-else
        target="_blank"
        :class="[
          'content-wrap',
          {
            'cursor-default':
              !subAccountEditable ||
              (subAccount && !subAccountSkillAuths.hasOwnProperty(item.id)) ||
              subAccountSkillAuths[item.id] == 2,
          },
        ]"
      >
        <div
          v-if="item.outNumber && item.outNumber !== item.newestNumber"
          class="update-state-label"
          style="background: #ff5a5a"
        >
          可更新
        </div>
        <i class="skill-icon">{{ item.zhName && item.zhName.substr(0, 1) }}</i>
        <div class="skill-info">
          <p class="skill-title ib" :title="item.zhName">
            {{ item.zhName }}
          </p>

          <!-- 标题下按钮 -->
          <div class="title-btm-btn-group">
            <p class="ability-tag">{{ item | skillType }}</p>
            <!-- <span v-if="item.sourceCount && item.sourceCount > 0">|</span> -->
            <a
              class="skill-source ib"
              :class="{ 'skill-disabled': !item.used }"
              v-if="item.sourceCount && item.sourceCount > 0"
              @click.stop.prevent="showSource(item)"
              style="margin-left: 8px; padding-top: 3px"
            >
              {{ item.sourceCount }}个信源
            </a>
            <p
              v-else-if="item.outNumber"
              style="margin-left: 8px; padding-top: 3px"
            >
              {{ item.outNumber }}
            </p>
          </div>
        </div>
      </div>

      <div @click.stop class="switch-wrap">
        <el-switch
          :disabled="!subAccountEditable"
          size="small"
          v-model="item.used"
          @change="(val) => onSwitchChange(val, item)"
        >
        </el-switch>
      </div>
      <!-- 下面的label area -->
      <div class="label-wrap" v-show="subAccountEditable && item.used">
        <p class="skill-desc"></p>
        <i
          @click.prevent.stop="showThresholdInfo(item)"
          class="skill-config-new AIUI-myapp-iconfont ai-myapp-setting2"
          v-show="subAccountEditable && item.used"
        ></i>
      </div>
    </div>
    <source-info
      :sourceVisible="sourceVisible"
      :sourceConfig="sourceConfig"
      :skillItem="skillItem"
      :currentScene="currentScene"
      :appId="appId"
      @sourceVisibleChange="toggleVisible"
      :isExtendSkill="isExtendSkill"
      @change="$emit('change')"
    ></source-info>

    <threshold-info
      :thresholdVisible="thresholdVisible"
      :isRepository="false"
      :configSkillItem="configSkillItem"
      :appId="appId"
      :sourceConfig="sourceConfig"
      :currentScene="currentScene"
      :skillThresholdConfig="skillThresholdConfig"
      :qaThresholdConfig="qaThresholdConfig"
      :skillConfig="skillConfig"
      :globalThresholdChange="globalThresholdChange"
      @thresholdVisibleChange="onThresholdVisibleChange"
      @change="$emit('change')"
    ></threshold-info>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import sourceInfo from '../sourceInfo.vue'
import thresholdInfo from '../thresholdInfo.vue'
export default {
  components: { sourceInfo, thresholdInfo },
  data() {
    return {
      sourceVisible: false,
      skillItem: {},
      isExtendSkill: true,
      thresholdVisible: false,
      configSkillItem: {},
    }
  },
  props: {
    item: Object,
    skillConfig: Object,
    sourceConfig: Object,
    currentScene: Object,
    appId: String,
    skillThresholdConfig: Object,
    qaThresholdConfig: Object,
    skillConfig: Object,
    globalThresholdChange: Boolean,
  },
  filters: {
    skillType(item) {
      let skillTypeMap = {
        2: '私有技能',
        9: '开放技能',
        5: '开放技能',
        3: '定制技能',
        8: 'QC技能',
      }
      return skillTypeMap[item.type]
    },
  },
  computed: {
    ...mapGetters({
      rightTestOpen: 'studioSkill/rightTestOpen',
      subAccount: 'user/subAccount',
      subAccountAppAuths: 'aiuiApp/subAccountAppAuths',
      subAccountSkillAuths: 'aiuiApp/subAccountSkillAuths',
    }),
    subAccountEditable() {
      return this.subAccountAppAuths[this.$route.params.appId] == 2
        ? false
        : true
    },
  },
  methods: {
    onThresholdVisibleChange(val) {
      this.thresholdVisible = val
    },
    showThresholdInfo(item) {
      this.configSkillItem = item
      this.thresholdVisible = true
    },
    toggleVisible(val) {
      this.sourceVisible = val
    },
    showSource(item) {
      if (!item.used) return
      this.sourceVisible = true
      this.skillItem = item
    },
    onSwitchChange(val, item) {
      // console.log('onSwitchChange', val, item)
      this.$emit('change')
      let data
      let operation = val ? 'open' : 'close'
      data = {
        id: item.id,
        name: item.name,
        operation,
      }
      if (this.skillConfig && this.skillConfig[item.id]) {
        this.skillConfig[item.id].id = data.id
        this.skillConfig[item.id].name = data.name
        this.skillConfig[item.id].operation = data.operation
      } else {
        this.skillConfig[item.id] = data
      }
    },
    tojump(item) {
      if (item.type == 2) {
        this.toSkill(item.id, '')
      } else {
        this.toSkill(item.id, 'extend-')
      }
    },
    toSkill(id, urlParams) {
      let self = this
      if (
        !self.subAccountEditable ||
        (self.subAccount && !self.subAccountSkillAuths.hasOwnProperty(id)) ||
        self.subAccountSkillAuths[id] == 2
      )
        return

      let paramObj = {}
      if (this.subAccount) {
        if (urlParams == '') {
          paramObj = {
            name: 'sub-skill-intentions',
            params: { skillId: id },
          }
        } else {
          paramObj = {
            name: 'extend-sub-skill-intentions',
            params: { skillId: id },
          }
        }
      } else {
        if (urlParams == '') {
          paramObj = {
            name: 'skill-intentions',
            params: { skillId: id },
          }
        } else {
          paramObj = {
            name: 'extend-skill-intentions',
            params: { skillId: id },
          }
        }
      }
      const routeData = this.$router.resolve(paramObj)

      window.open(routeData.href, '_blank')
    },
  },
}
</script>
<style lang="scss" scoped>
@import '../style.scss';
// .skill {
//   height: 117px;
// }

.content-wrap {
  display: flex;
  align-items: center;
  // height: 70px;
}
.skill-update {
  margin-top: 5px;
  margin-bottom: 5px;
  vertical-align: middle;
  font-size: 12px;
  color: #1784e9;
}
</style>
