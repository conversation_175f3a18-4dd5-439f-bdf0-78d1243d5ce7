<template>
  <div style="margin-top: 16px">
    <el-card
      class="box-card"
      shadow="never"
      :body-style="{ padding: '10 20', background: '#F9F9F9' }"
    >
      <div slot="header" class="clearfix">
        <span class="header-title">问答</span>
        <div style="float: right">
          <a style="" @click="add"
            ><i class="el-icon-circle-plus-outline"></i>&nbsp;添加</a
          >
          <!-- <a @click="setting"><i class="el-icon-setting"></i>&nbsp;设置</a> -->
        </div>
      </div>
      <div class="text">
        <div class="description" v-if="ragDataUsed.length > 0">
          已配置
          <span v-if="ragDataUsed.length > 20">
            <span v-for="(v, i) in Array(20)" :key="i" class="skill-cell">
              {{ ragDataUsed[i].name }}<span v-if="i < 20 - 1">、</span>
            </span>
            等
            {{ ragDataUsed.length }}
            个问答
          </span>
          <span v-else>
            <span v-for="(v, i) in ragDataUsed" :key="i" class="skill-cell">
              {{ v.name }}<span v-if="i < ragDataUsed.length - 1">、</span>
            </span>
          </span>
          共<span style="">{{ ragDataUsed.length }}</span
          >个问答
        </div>
        <div class="description" v-else>暂未配置问答</div>
        <!-- <a class="opt-link" @click="dialogVisible = true">三方问答设置 ></a> -->
      </div>
    </el-card>

    <repoSettingDialog
      :dialog="dialogRepoSet"
      :appId="appId"
      :currentScene="currentScene"
      @saveSuccess="onSearchConfigSaveSuccess"
    ></repoSettingDialog>
    <repoAddDialog
      :dialog="dialogRepoAdd"
      :appId="appId"
      :currentScene="currentScene"
      @saveSuccess="onRepoAddSaveSuccess"
    ></repoAddDialog>
    <!-- 三方问答库配置弹窗 -->
    <thirdPartyQAConfig
      v-if="dialogVisible"
      :appId="appId"
      :currentScene="currentScene"
      :visible.sync="dialogVisible"
      @confirm="dialogVisible = false"
      @saveSuccess="onRepoAddSaveSuccess"
    />
  </div>
</template>
<script>
import { mapGetters } from 'vuex'
import repoSettingDialog from './Dialog/repoSettingDialogSOS/index.vue'
import repoAddDialog from './Dialog/repoAddDialogSOS/index.vue'
import thirdPartyQAConfig from './Dialog/thirdParty-qa-config/index.vue'
export default {
  data() {
    return {
      dialogVisible: false,
      ragDataUsed: [],
      dialogRepoAdd: {
        show: false,
      },
      dialogRepoSet: {
        show: false,
      },
    }
  },
  computed: {
    ...mapGetters({
      currentScene: 'aiuiApp/currentScene',
      appInfo: 'aiuiApp/app',
    }),
    appId() {
      return this.$route.params.appId
    },
  },
  created() {
    if (this.currentScene && this.currentScene.sceneBoxId) {
      this.getAppRagConfig() // 获取配置列表
    }
  },
  watch: {
    currentScene(scene) {
      if (scene && scene.sceneBoxId) {
        this.getAppRagConfig() // 获取配置列表
      }
    },
  },

  methods: {
    // 获取已配置的文档问答信息
    getAppRagConfig() {
      let that = this
      this.loading = false
      this.$utils.httpGet(
        this.$config.api.AIUI_BOT_CONFIG_GET_BOTRAGREPOS,
        {
          botId: this.currentScene.botBoxId,
          pageIndex: 1,
          pageSize: 1000,
        },
        {
          success: (res) => {
            that.ragDataUsed = (res.data.repos || []).filter(
              (item) => item.selected
            )
          },
          error: (res) => {},
        }
      )
    },

    setting() {
      this.dialogRepoSet.show = true
    },
    add() {
      // this.dialogRepoAdd.show = true
      this.dialogVisible = true
    },
    onSearchConfigSaveSuccess() {},
    onRepoAddSaveSuccess() {
      this.getAppRagConfig()
    },
  },
  components: { repoSettingDialog, repoAddDialog, thirdPartyQAConfig },
}
</script>
<style lang="scss" scoped>
@import '../card.scss';
</style>
