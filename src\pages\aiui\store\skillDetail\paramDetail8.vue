<template>
  <div class="store-skill-params container">
    <!-- <p class="store-skill-big-label">技能参数</p> -->
    <div class="store-skill-content">
      <p class="store-skill-label mgb16">技能意图</p>
      <el-table
        class="store-skill-table-default mgb24 table-custom"
        :data="intents"
        style="width: 100%"
      >
        <el-table-column prop="key" label="意图名" width="180">
        </el-table-column>
        <el-table-column prop="value" label="说明"> </el-table-column>
      </el-table>
      <p class="store-skill-label mgb16">技能结果</p>
      <el-table
        class="store-skill-table"
        :span-method="semanticSpanMethod2"
        :data="semantic"
        style="width: 100%"
      >
        <el-table-column width="60">
          <template slot-scope="scope">
            <span class="text-bold">语义</span>
          </template>
        </el-table-column>
        <el-table-column prop="key" label="字段名" width="180">
        </el-table-column>
        <el-table-column label="字段类型" width="260">
          <template slot-scope="scope"> String </template>
        </el-table-column>
        <el-table-column prop="value" label="说明" show-overflow-tooltip>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    skill: Object,
    intents: Array,
    semantic: Array,
  },
  data() {
    return {}
  },
  methods: {
    semanticSpanMethod2({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        if (rowIndex % this.semantic.length === 0) {
          return {
            rowspan: this.semantic.length,
            colspan: 1,
          }
        } else {
          return {
            rowspan: 0,
            colspan: 0,
          }
        }
      }
    },
    semanticSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        if (rowIndex % this.skill.protocols.semantic.length === 0) {
          return {
            rowspan: this.skill.protocols.semantic.length,
            colspan: 1,
          }
        } else {
          return {
            rowspan: 0,
            colspan: 0,
          }
        }
      }
    },
    answerSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        if (rowIndex % this.skill.protocols.answer.length === 0) {
          return {
            rowspan: this.skill.protocols.answer.length,
            colspan: 1,
          }
        } else {
          return {
            rowspan: 0,
            colspan: 0,
          }
        }
      }
    },
  },
}
</script>
<style lang="scss" scoped>
.container {
  padding-top: 30px;
}
.table-custom {
  :deep(thead > tr > th) {
    .cell {
      padding-left: 70px;
    }
  }

  :deep(.el-table__body tr > td) {
    .cell {
      padding-left: 70px;
    }
  }
}
// 技能商店适配不同屏幕
@media screen and (max-width: 1601px) {
  .container {
    padding-top: 20px;
  }
}
</style>
