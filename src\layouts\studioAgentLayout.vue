<template>
  <div class="app-container">
    <div class="os-container" style="flex-direction: column">
      <!-- <aiui-header></aiui-header> -->
      <div class="os-container">
        <div class="os-aside">
          <!-- <aiuiMenu></aiuiMenu> -->
          <studio-agent-menu-head />
          <div class="os-scroll os_scroll" style="height: calc(100% - 63px)">
            <div>
              <div
                class="studio-skill-menu-head-skill-name"
                @click.stop="openSelectAgent($event)"
              >
                <span :title="agentName || '-'">{{ agentName || '-' }}</span>
              </div>
              <SelectAgentPopover ref="SelectAgentPopover"></SelectAgentPopover>
            </div>
            <el-menu class="os-menu" :default-openeds="openeds">
              <template v-for="menu in menus">
                <el-submenu
                  class="agent-menu"
                  v-if="menu.sub"
                  :key="menu.key"
                  :index="menu.key"
                >
                  <template slot="title">
                    <!-- <i :class="menu.icon"></i> -->
                    <span>{{ menu.value }}</span>
                  </template>
                  <el-menu-item-group>
                    <template v-for="submenu in menu.sub">
                      <el-menu-item
                        v-if="!submenu.disabled"
                        :key="submenu.key"
                        :index="submenu.key"
                        :class="[
                          'agent-sub-menu',
                          { 'os-menu-active': routeName === submenu.key },
                        ]"
                        @click="selectMenu(submenu)"
                      >
                        <template
                          v-if="
                            submenu.key !== 'agent-intentions' &&
                            submenu.key !== 'sub-agent-intentions'
                          "
                        >
                          {{ submenu.value }}
                        </template>
                        <template v-else>
                          {{ submenu.value }}
                          <div
                            class="os-menu-add agent-layout"
                            @click="addIntent"
                          >
                            <i class="ic-r-plus" />
                            创建
                          </div>
                        </template>
                      </el-menu-item>
                    </template>
                  </el-menu-item-group>
                </el-submenu>

                <el-menu-item
                  v-else
                  :key="menu.index"
                  :index="menu.key"
                  :class="{ 'os-menu-active': routeName === menu.key }"
                  @click="selectMenu(menu)"
                >
                  <!-- <i :class="menu.icon"></i> -->
                  <span slot="title">{{ menu.value }}</span>
                </el-menu-item>
              </template>
            </el-menu>
            <!-- <a
              class="studio-skill-layout-to-docs"
              :href="DocsInUrl"
              target="_blank"
              >文档中心<i class="ic-r-link skill-left-bar"></i
            ></a> -->
          </div>
        </div>

        <div class="os-main">
          <template>
            <router-view></router-view>
          </template>
        </div>

        <!-- <div
        class="os-side-right"
        :class="{ 'os-side-right-open': rightTestOpen }"
      >
        <right-test-close
          v-if="!rightTestOpen"
          :rightTestOpen="rightTestOpen"
        ></right-test-close>
        <skill-debug v-show="rightTestOpen" debugType="skill" />
        <feedBackHover :rightTestOpen="rightTestOpen" />
      </div> -->
        <!-- 子账号登录后首次进入提示 -->
        <cooperate-warn-dialog
          :dialog="cooperateDialog"
          type="firstEnterSkill"
        ></cooperate-warn-dialog>
      </div>
      <p
        class="publish-result-for-test"
        id="publish-result-for-test"
        style="display: none"
      ></p>
    </div>
  </div>
</template>

<script>
import Vue from 'vue'
import SSO from 'sso/sso.js'
import dicts from '@M/dicts'
import { mapGetters } from 'vuex'
import Axios from 'axios'
import RightTestClose from '@C/rightTestClose'
import feedBackHover from '../components/feedBackHover'
import CooperateWarnDialog from '@C/cooperatWarnDialog'
import AiuiHeader from '../components/aiuiHeader'
import OsHeader from '../components/header'
import aiuiMenu from '../components/aiuiMenu'
import SelectAgentPopover from '@P/studio/handlePlatform/agent/selectAgent.vue'

export default {
  data() {
    return {
      agentName: '',
      menus: [
        {
          key: 'agent-info-legacy',
          value: '基本信息',
          icon: 'ic-mn-basic-info',
          index: 0,
          show: true,
        },

        {
          key: 'agent-model-legacy',
          value: '交互模型',
          icon: 'ic-mn-interact',
          index: 1,
          show: true,
          sub: [
            {
              key: 'agent-intent-legacy',
              value: '意图',
              icon: 'ic-mn-interact',
              index: '1-1',
              show: true,
            },
            {
              key: 'agent-used-legacy',
              value: '引用意图',
              icon: 'ic-mn-launch',
              index: '1-2',
              show: true,
            },
          ],
        },
      ],

      openeds: ['agent-model-legacy'],
      DocsInUrl: `${this.$config.docs}doc-44/`,
      cooperateDialog: {
        show: false,
      },
    }
  },

  watch: {
    $route: function (to, from) {
      let self = this
      self.routeName = to.name
    },
  },
  computed: {},
  created() {
    let self = this
    if (self.$router.match(location.pathname)) {
      self.routeName = self.$router.match(location.pathname).name
    }
    console.log(this.$router.match(location.pathname).name)
  },
  mounted() {
    this.getAgentDetail()
  },
  methods: {
    openSelectAgent(event) {
      // this.$store.dispatch('studioSkill/setSkillPopover', {
      //   show: true,
      //   rect: event.target.getBoundingClientRect(),
      // })
      event.preventDefault()
      event.stopPropagation()
      this.$refs.SelectAgentPopover.show(event.target.getBoundingClientRect())
    },
    init() {},

    addIntent() {},

    selectMenu(menu) {
      let routeData = {}
      routeData.name = menu.key
      if (menu.params) {
        routeData.params = menu.params
      }
      this.$router.push(routeData)
    },

    getAgentDetail() {
      const agentId = this.$route.params.agentId
      this.$utils.httpPost(
        this.$config.api.AGENT_DETAIL_OLD,
        JSON.stringify({ agentId: agentId }),
        {
          config: {
            headers: {
              'Content-Type': 'application/json;charset=UTF-8',
            },
          },
          success: (res) => {
            console.log(res, '详情的res')
            // if (res?.data?.agentType === 3) {
            //   this.menus[2].show = false
            // }
            this.agentName = res.data.agentName
          },
          error: (err) => {},
        }
      )
    },
  },
  components: {
    OsHeader,
    AiuiHeader,
    RightTestClose,
    CooperateWarnDialog,
    feedBackHover,
    aiuiMenu,
    SelectAgentPopover,
  },
}
</script>

<style lang="scss" scoped>
.os-aside {
  :deep(.el-menu) {
    background-color: #fff;
  }
}
</style>
<style lang="scss">
.os-menu-add.agent-layout {
  position: absolute;
  right: 24px;
  top: 0;
  width: 50px;
  font-size: 14px !important;
  display: flex;
  color: $primary;
  i {
    font-size: 14px !important;
    color: $primary !important;
    flex: 1;
    margin: 0 !important;
  }
}
.agent-menu {
  .el-submenu__title {
    padding-left: 30px !important;
  }
  .agent-sub-menu.el-menu-item {
    padding-left: 78px !important;
    font-size: 14px;
  }
}

.studio-skill-layout-to-docs {
  display: block;
  margin: 24px;
  padding-top: 32px;
  border-top: 1px solid $grey2;
}
.ic-r-link.skill-left-bar {
  padding-left: 4px;
  vertical-align: -1px;
}
</style>
