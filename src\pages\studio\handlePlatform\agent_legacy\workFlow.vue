<template>
  <div class="os-scroll">
    <div class="mgb24" style="font-size: 0">
      <el-button
        icon="ic-r-plus"
        type="primary"
        size="small"
        @click="createWorkFlow"
        :loading="createLoading"
      >
        创建工作流
      </el-button>
    </div>
    <div class="mgb24" @keyup.enter="getWorkFlowList(1)">
      <el-input
        class="search-area"
        placeholder="工作流"
        v-model="workFLowSearchName"
      >
        <i
          slot="suffix"
          class="el-input__icon el-icon-search search-area-btn"
          @click="getWorkFlowList(1)"
        />
      </el-input>
    </div>

    <os-table
      :tableData="tableData"
      style="margin-bottom: 56px"
      @del="del"
      @edit="edit"
      @change="getWorkFlowList"
    >
      <el-table-column label="工作流名称" prop="workflowName" min-width="200px">
      </el-table-column>
      <el-table-column label="工作流ID" prop="workflowId" min-width="220px">
      </el-table-column>
      <el-table-column
        label="工作流描述"
        prop="workflowDesc"
        min-width="300px"
      ></el-table-column>

      <el-table-column label="引用" prop="quote" width="100px">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.quote"
            @change="(val) => changeSwitch(scope.row, val)"
          ></el-switch>
        </template>
      </el-table-column>
    </os-table>
  </div>
</template>

<script>
export default {
  name: 'IflyAIuiWebWorkFlow',

  data() {
    return {
      pageOptions: {
        title: '后处理',
        loading: false,
      },

      tableData: {
        loading: true,
        total: 0,
        page: 1,
        size: 10,
        handles: ['edit', 'del'],
        switchOptions: {
          column: 'type',
          active: 2,
          inactive: 3,
          activeText: '入口',
          inactiveText: '对话',
        },
        handleColumnText: '操作',
        list: [],
      },
      workFLowSearchName: null,

      createLoading: false,
    }
  },

  created() {
    this.getWorkFlowList(1)
  },

  methods: {
    getWorkFlowList(page) {
      const params = {
        agentId: this.$route.params.agentId,
        workflowName: this.workFLowSearchName,
        pageIndex: page || this.tableData.page,
        pageSize: this.tableData.size,
      }
      this.$utils.httpPost(
        this.$config.api.AGENT_WORK_FLOW_LIST_OLD,
        JSON.stringify(params),
        {
          config: {
            headers: {
              'Content-Type': 'application/json;charset=UTF-8',
            },
          },
          success: (res) => {
            if (res.code == '0') {
              this.tableData.loading = false
              this.tableData.list = res.data.data
              this.tableData.page = res.data.pageIndex
              this.tableData.size = res.data.pageSize
              this.tableData.total = res.data.totalSize
              this.tableData.loading = false
            }
          },
          error: (err) => {
            this.$message.error(err.desc)
            this.tableData.loading = false
          },
        }
      )
    },

    del(data) {
      this.searchQuote(data.workflowId)
    },
    edit(data) {
      // window.open(`/agent-builder/work_flow/${data.workflowId}/arrange`, '_blank')
      window.open(
        `/agent-builder/work_flow/${data.workflowId}/arrange`,
        '_blank'
      )
    },

    searchQuote(workflowId) {
      const params = {
        workflowId: workflowId,
      }
      this.$utils.httpPost(
        this.$config.api.AGENT_WORK_FLOW_QUOTE_ALL_SEARCH_OLD,
        JSON.stringify(params),
        {
          config: {
            headers: {
              'Content-Type': 'application/json;charset=UTF-8',
            },
          },
          success: (res) => {
            if (res.code == '0') {
              console.log(res, '删除前查询的res')
              let quoteInfo = ''
              if (res.data.length > 0) {
                let myInfo = res.data.map((item) => item.agentName).join('，')
                quoteInfo = `该工作流已被 ${res.data.length}个智能体(${myInfo})引用，删除后引用将失效`
                this.doDelete(quoteInfo, workflowId)
              } else {
                this.doDelete(quoteInfo, workflowId)
              }
            }
          },
          error: (err) => {
            this.$message.error(err.desc)
          },
        }
      )
    },

    doDelete(quoteInfo, workflowId) {
      this.$confirm(quoteInfo, `确定删除该工作流吗?`, {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        confirmButtonClass: 'el-button--danger',
        type: 'warning',
        showClose: false,
      })
        .then(() => {
          const params = {
            workflowId: workflowId,
          }
          this.$utils.httpPost(
            this.$config.api.AGENT_WORK_FLOW_REMOVE_OLD,
            JSON.stringify(params),
            {
              config: {
                headers: {
                  'Content-Type': 'application/json;charset=UTF-8',
                },
              },
              success: (res) => {
                if (res.code == '0') {
                  this.$message.success('操作成功')
                  this.getWorkFlowList(1)
                }
              },
              error: (err) => {
                this.$message.error(err.desc)
              },
            }
          )
        })
        .catch(() => {})
    },

    changeSwitch(data, flag) {
      const params = {
        agentId: this.$route.params.agentId,
        workflowId: data.workflowId,
        quoteFlag: flag,
      }
      this.$utils.httpPost(
        this.$config.api.AGENT_WORK_FLOW_QUOTE_OLD,
        JSON.stringify(params),
        {
          config: {
            headers: {
              'Content-Type': 'application/json;charset=UTF-8',
            },
          },
          success: (res) => {
            if (res.code == '0') {
              this.$message.success('操作成功')
              this.getWorkFlowList()
            } else {
              this.getWorkFlowList()
            }
          },
          error: (err) => {
            this.$message.error(err.desc)
            this.getWorkFlowList()
          },
        }
      )
    },

    createWorkFlow() {
      const params = {
        agentId: this.$route.params.agentId,
      }
      this.createLoading = true

      this.$utils.httpPost(
        this.$config.api.AGENT_WORK_FLOW_CREATE_OLD,
        JSON.stringify(params),
        {
          config: {
            headers: {
              'Content-Type': 'application/json;charset=UTF-8',
            },
          },
          success: (res) => {
            if (res.code == '0') {
              this.createLoading = false
              const workflowId = res.data.workflowId
              window.open(
                `/agent-builder/work_flow/${workflowId}/arrange`,
                '_blank'
              )
            }
          },
          error: (err) => {
            this.$message.error(err.desc)
            this.createLoading = false
          },
        }
      )
    },
  },
}
</script>

<style lang="scss" scoped></style>
