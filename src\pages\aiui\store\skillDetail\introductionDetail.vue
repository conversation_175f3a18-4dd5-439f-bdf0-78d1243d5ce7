<template>
  <div class="store-skill-currency container">
    <!-- <p class="store-skill-big-label">通用</p> -->
    <div class="store-skill-content">
      <template v-if="!skill.dialect">
        <p class="store-skill-label">技能简介</p>
        <p class="store-skill-desc" v-html="skill.detail.description"></p>
      </template>

      <p
        class="store-skill-label"
        v-if="
          (skill.examples &&
            skill.examples.singles &&
            skill.examples.singles.length > 0) ||
          (skill.examples &&
            skill.examples.manys &&
            skill.examples.manys.length > 0) ||
          (skill.phrases && skill.phrases.many && skill.phrases.many.length > 0)
        "
      >
        说法示例
      </p>
      <div>
        <div
          class="store-skill-phrases"
          v-if="
            skill.examples &&
            skill.examples.singles &&
            skill.examples.singles.length > 0
          "
        >
          <div class="store-skill-phrases-title">
            <!-- <img :src="require('@A/svg/store/single-phrases.svg')" /><span
              >单轮</span
            > -->
            <i class="iconfont icon-danlunduihua" /><span>单轮</span
            ><span><i class="circle-two"></i><i class="circle-two"></i></span>
          </div>
          <!-- <div class="store-skill-phrases-single">
            <div
              v-for="(single, index) in skill.phrases.single"
              class="store-skill-phrases-user"
              :key="index"
            >
              {{ single }}
            </div>
          </div> -->
          <el-table
            class="store-skill-table-default mgb24"
            :data="skill.examples.singles"
            style="width: 100%"
          >
            <el-table-column prop="description" label="意图描述">
            </el-table-column>
            <el-table-column prop="question" label="说法示例">
            </el-table-column>
          </el-table>
        </div>
        <div
          v-if="
            (skill.phrases &&
              skill.phrases.many &&
              skill.phrases.many.length > 0) ||
            (skill.examples &&
              skill.examples.manys &&
              skill.examples.manys.length > 0)
          "
          class="store-skill-phrases"
        >
          <div class="store-skill-phrases-title">
            <!-- <img :src="require('@A/svg/store/many-phrases.svg')" /><span
              >多轮</span
            > -->
            <i class="iconfont icon-duolunduihua" /><span>多轮</span
            ><span><i class="circle-two"></i><i class="circle-two"></i></span>
          </div>
          <el-table
            class="store-skill-table-default mgb24"
            :data="skill.examples.manys"
            style="width: 100%"
          >
            <el-table-column prop="description" label="意图描述">
            </el-table-column>
            <el-table-column prop="question" label="说法示例">
            </el-table-column>
          </el-table>
          <div
            class="store-skill-phrases-many"
            v-if="
              skill.phrases &&
              skill.phrases.many &&
              skill.phrases.many.length > 0
            "
          >
            <template v-for="(many, index) in skill.phrases.many">
              <div class="store-skill-phrases-user ib">
                <i class="iconfont icon-yuyin"></i>
                <span class="text-content">{{
                  many['问'] || many['question']
                }}</span>
              </div>
              <div style="overflow: auto">
                <div class="store-skill-phrases-skill">
                  <span class="text-content-a">{{
                    many['答'] || many['answer']
                  }}</span>
                  <i class="iconfont icon-yuyin revert"></i>
                </div>
              </div>
            </template>
          </div>
        </div>
      </div>
    </div>
    <div class="info">
      <span>友情提醒:</span
      ><span>{{
        (skill.detail.provider || '科大讯飞').includes('科大讯飞')
          ? '技能内容由开放平台负责'
          : '第三方技能由第三方开发提供，技能内容由第三方负责'
      }}</span>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    skill: Object,
    type: Number | String,
  },
}
</script>
<style lang="scss" scoped>
p {
  margin-bottom: 0;
}
.container {
  padding-top: 30px;
}
.info {
  font-size: 16px;
  margin-top: 20px;
  span:first-child {
    color: $grey002;
  }
  span:last-child {
    color: $grey003;
    margin-left: 10px;
  }
}
.icon-yuyin {
  font-size: 24px;
  color: $primary;
  &.revert {
    color: #fff;
    transform: rotate(180deg);
    display: inline-block;
  }
}

.text-content {
  vertical-align: text-bottom;
  margin-left: 8px;
  font-size: 20px;
}
.text-content-a {
  vertical-align: text-bottom;
  margin-right: 8px;
  font-size: 20px;
}

.icon-duolunduihua,
.icon-danlunduihua {
  font-size: 20px;
  color: $grey003;
}

.circle-two {
  display: inline-block;
  width: 10px;
  height: 10px;
  border-radius: 100%;
  border: 1px solid $grey003;
}
.circle-two + .circle-two {
  margin-left: 3px;
}

// 技能商店适配不同屏幕
@media screen and (max-width: 1601px) {
  .container {
    padding-top: 20px;
  }
  .info {
    font-size: 12px;
  }

  .circle-two {
    width: 6px;
    height: 6px;
  }
  .circle-two + .circle-two {
    margin-left: 2px;
  }
  .icon-yuyin {
    font-size: 20px;
  }
  .text-content {
    font-size: 14px;
  }
  .text-content-a {
    font-size: 14px;
  }
}
</style>
