<template>
  <div class="main-content">
    <section class="main-content-banner">
      <div class="banner-text">
        <h2>智能硬件解决方案</h2>
        <p class="banner-text-content">
          提供纯软接入，协助适配算法，有效提升智能硬件语音交互体验。<br />
          提供AIUI麦克风阵列、声卡、降噪板、语音交互评估版和多模态<br />
          交互盒子等开发套件，让硬件接入更简单。
        </p>
        <div class="banner-text-button" @click="toConsole">合作咨询</div>
      </div>
    </section>
    <section class="section section-1">
      <div class="section-title">
        <span>AI赋能</span>
      </div>

      <div class="section-tabs">
        <!-- <el-tabs v-model="activeName" @tab-click="handleClick">
          <el-tab-pane label="耳聪" name="0"> </el-tab-pane>
          <el-tab-pane label="目明" name="1"> </el-tab-pane>
          <el-tab-pane label="善言" name="2"> </el-tab-pane>
          <el-tab-pane label="博学" name="3"> </el-tab-pane>
          <el-tab-pane label="多才" name="4"> </el-tab-pane>
        </el-tabs> -->
        <ul>
          <li :class="{ active: activeName == '0' }" @click="toggleActive('0')">
            耳聪
          </li>
          <li :class="{ active: activeName == '1' }" @click="toggleActive('1')">
            目明
          </li>
          <li :class="{ active: activeName == '2' }" @click="toggleActive('2')">
            善言
          </li>
          <li :class="{ active: activeName == '3' }" @click="toggleActive('3')">
            博学
          </li>
          <li :class="{ active: activeName == '4' }" @click="toggleActive('4')">
            多才
          </li>
        </ul>
      </div>
      <div class="section-swiper" v-swiper:swiper="swiperOption">
        <div class="swiper-wrapper">
          <div class="swiper-slide" key="1">
            <cells :cells="rcCells" label="rc" />
          </div>
          <div class="swiper-slide" key="2">
            <cells :cells="mmCells" label="mm" />
          </div>
          <div class="swiper-slide" key="3">
            <cells :cells="syCells" label="sy" />
          </div>
          <div class="swiper-slide" key="4">
            <div class="content-container">
              <p class="content-sub-title">
                1500万首曲库，10余个视频品牌，有声内容1200万+小时，100余家内容提供方
              </p>
              <ul class="li-wrap">
                <li class="">
                  <i class="content-icon icon-xmly"></i>
                  <p>喜马拉雅听说</p>
                </li>
                <li class="">
                  <i class="content-icon icon-kwyy"></i>
                  <p>酷我音乐</p>
                </li>
                <li class="">
                  <i class="content-icon icon-xlxw"></i>
                  <p>新浪新闻</p>
                </li>
                <li class="">
                  <i class="content-icon icon-aqy"></i>
                  <p>爱奇艺</p>
                </li>
                <li class="">
                  <i class="content-icon icon-bilibili"></i>
                  <p>哔哩哔哩</p>
                </li>
              </ul>
            </div>
          </div>
          <div class="swiper-slide" key="5">
            <div class="content-container">
              <p class="content-sub-title">
                语音技能涵盖生活、娱乐、游戏、办公、搜索导航、
                AIOT控制，一键配置，让你的产品生而智能
              </p>
              <ul class="li-wrap">
                <li class="">
                  <i class="content-icon icon-clock"></i>
                  <p>闹钟</p>
                </li>
                <li class="">
                  <i class="content-icon icon-dictionary"></i>
                  <p>成语词典</p>
                </li>
                <li class="">
                  <i class="content-icon icon-qa"></i>
                  <p>百科问答</p>
                </li>
                <li class="">
                  <i class="content-icon icon-poetry"></i>
                  <p>古诗词</p>
                </li>
                <li class="">
                  <i class="content-icon icon-chat"></i>
                  <p>闲聊</p>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </section>
    <section class="section section-2">
      <div class="section-title">
        <span>解决方案</span>
      </div>
      <ul class="solutions">
        <li class="solution-child">
          <i></i>
          <p>儿童教育产品解决方案</p>
          <p>
            利用讯飞的语音技术、手指检测、绘本检测引擎、口语评测等多项AI能力，让机器人快速实现语音交互及教学能力
          </p>
        </li>
        <li class="solution-switch">
          <i></i>
          <p>穿戴式设备解决方案</p>
          <p>
            提供系统级的语音助手，支持免唤醒交互，让产品轻松实现语音打电话，语音导航，语音查天气等多场景语音交互
          </p>
        </li>
        <li>
          <i></i>
          <p>服务机器人解决方案</p>
          <p>
            为机器人厂家提供从前端拾音、语音交互，图像识别全链路、全场景软硬件一体化AI能力，基于高效降噪算法及多模态技术，让机器人在公共场所下轻松完成人机交互
          </p>
        </li>
        <li>
          <i></i>
          <p>健身按摩设备解决方案</p>
          <p>
            提供离线命令词识别服务，支持200条语法，零流量实时响应，满足不同终端快速稳定的本地化语音服务
          </p>
        </li>
      </ul>
    </section>
    <section class="section section-3">
      <div class="section-title">
        <span>方案优势</span>
      </div>
      <ul class="advantage">
        <li>
          <div class="advantage-image"></div>
          <div class="advantage-text">
            <p>免唤醒交互</p>
            <p>无需说出唤醒词，直接交互打造自然流畅的语音交互体验</p>
          </div>
        </li>
        <li>
          <div class="advantage-text">
            <p>多模态融合</p>
            <p>
              支持图像、手势、声纹等多模态技术与语音的融合，满足不同终端、不同场景应用需求
            </p>
          </div>
          <div class="advantage-image"></div>
        </li>
        <li>
          <div class="advantage-image"></div>
          <div class="advantage-text">
            <p>多种接入方式</p>
            <p>
              提供Android、iOS、Linux、WebAPI、软硬件一体化产品等接入方式，助力开发者以最小的成本快速接入AI能力
            </p>
          </div>
        </li>
        <li>
          <div class="advantage-text">
            <p>一对一指导</p>
            <p>
              针对智能硬件前端声学设计、语音交互设计、系统能力集成、技能开发等提供专业的技术支持，一对一贴身指导
            </p>
          </div>
          <div class="advantage-image"></div>
        </li>
      </ul>
    </section>
    <corp @jump="toConsole">
      <template> 提交信息，我们会尽快与您联系</template>
    </corp>
    <!-- <section class="section section-4">
      <div class="section-title">
        <p>合作咨询</p>
        <p>提交信息，我们会尽快与您联系</p>
      </div>
      <div class="section-item">
        <aiui-button hasTop @click.native="toConsole">申请合作</aiui-button>
      </div>
    </section> -->
  </div>
</template>

<script>
import Cells from './cells.vue'
import '../../../../../static/vue-awesome-swiper'
import corp from '@P/aiui/solution-aiui/components/corp.vue'

// import { Swiper, SwiperSlide, directive } from 'vue-awesome-swiper'

export default {
  name: 'smart-hardware',
  data() {
    return {
      activeName: '0',
      rcCells: [
        {
          title: '免唤醒',
          introduction: '提供低功耗唤醒，多<br/>模态唤醒及免唤醒交互',
        },
        {
          title: '远距离拾音',
          introduction: '支持1-3-5米远距离拾音',
        },
        {
          title: '定向拾音',
          introduction: '支持全向或指定方向拾音',
        },
        {
          title: '声纹识别',
          introduction: '支持成人、儿<br/>童、老人音质辨别',
        },
        {
          title: '高效降噪',
          introduction: '提供多领域、<br/>多场景降噪引擎',
        },
      ],
      mmCells: [
        {
          title: '人脸识别',
          introduction: '提供人脸检测、活体<br/>检测、人脸跟踪等能力',
        },
        {
          title: '手势识别',
          introduction: '支持20+常用动<br/>态手势动作识别',
        },
        {
          title: '唇形识别',
          introduction: '支持唇形检测、唇形识别',
        },
        {
          title: 'OCR',
          introduction: '用与各种场景图像文<br/>字识别，支持多个语种',
        },
        {
          title: '手指检测',
          introduction: '检测出手指在图<br/>像上的位置坐标',
        },
      ],
      syCells: [
        {
          title: '多发音人',
          introduction: '100+发音人，男女<br/>老少，风格随心选',
        },
        {
          title: '方言语种',
          introduction: '支持19个语种，11种方<br/>言，中英混合自然合成',
        },
        {
          title: '动态调参',
          introduction: '随心调节语调/语速/音量<br/>等参数，满足复杂场景需求',
        },
        {
          title: '定制音库',
          introduction:
            '赋予产品声音形象，定制发音<br/>人，为产品量身打造专属音库',
        },
        {
          title: '个性化变声',
          introduction: '将源发音人的声音转<br/>换为目标发音人音色',
        },
      ],
      swiperOption: {
        loop: true,
        autoplay: {
          delay: 4000,
          // disableOnInteraction: false,
        },
      },
    }
  },
  methods: {
    toConsole() {
      // 判断是否有百度SEM营销参数，有的话，提交表单页需要带上
      const search = window.location.search
      if (search) {
        window.open(`/solution/apply/8${search}`)
      } else {
        window.open(`/solution/apply/8`)
      }
    },
    // handleClick() {
    //   this.swiper.slideToLoop(Number(this.activeName))
    // },
    toggleActive(val) {
      this.swiper.slideToLoop(Number(val))
    },
  },
  mounted() {
    this.swiper.on('slideChange', () => {
      this.activeName = this.swiper.realIndex + ''
    })
  },
  components: { Cells, corp },
}
</script>

<style lang="scss" scoped>
.main-content {
  &-banner {
    background: url(~@A/images/solution/smart-hardware/img_intelligent_hardware_bg_banner.png)
      center no-repeat;
    background-size: cover;
    height: 500px;
    overflow: hidden;
    width: 100%;
    .banner-text {
      max-width: 1200px;
      color: #fff;
      height: 100%;
      margin: auto;
      &-button {
        font-size: 16px;
        text-align: center;
        font-weight: 400;
        width: 140px;
        height: 40px;
        line-height: 40px;
        border: 1px solid #fff;
        border-radius: 40px;
        color: #fff;
        cursor: pointer;
        transition: 0.6s;
      }
      h2 {
        color: #fff;
        padding-top: 148px;
        margin-bottom: 29px;
        font-size: 48px;
        font-weight: 500;
        line-height: 48px;
      }
      p {
        font-size: 18px;
        margin-bottom: 74px;
      }

      .banner-text-content {
        width: 570px;
        font-size: 16px;
        font-family: SourceHanSansSC-Regular, SourceHanSansSC;
        font-weight: 400;
        color: rgba(255, 255, 255, 0.86);
        line-height: 30px;
      }
    }
  }

  .section {
    p,
    ul {
      margin-bottom: 0;
    }
    max-width: 1200px;
    overflow: hidden;
    margin: 0 auto;
    .section-title {
      text-align: center;
      font-size: 35px;
      font-weight: 500;
      color: #444444;
      line-height: 53px;
      > span {
        font-size: 34px;
        font-weight: bold;
        color: #333;
      }
      .arrow {
        width: 25px;
        height: 23px;
        display: inline-block;
        background-repeat: no-repeat;
        background-size: contain;
      }
      .arrow-left {
        background-position: left;
        background-image: url(~@A/images/solution/smart-hardware/arrow-left.png);
      }
      .arrow-right {
        background-position: right;
        background-image: url(~@A/images/solution/smart-hardware/arrow-right.png);
      }
    }

    .section-tabs {
      margin-top: 50px;
      ul {
        display: flex;
        justify-content: center;
      }
      li {
        font-size: 20px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #666;
        line-height: 30px;
        position: relative;
        cursor: pointer;
        &:hover {
          color: #1f90fe;
        }
        &.active {
          color: #1f90fe;
          &::before {
            position: absolute;
            content: ' ';
            z-index: 1;
            width: 100px;
            height: 4px;
            background: #1f90fe;
            border-radius: 2px;
            bottom: -32px;
            left: -30px;
          }
          &::after {
            position: absolute;
            content: ' ';
            display: inline-block;
            width: 0;
            height: 0;
            bottom: -90px;
            left: 50%;
            transform: translateX(-50%);
            border: 46px solid;
            border-color: transparent transparent #f4f7f9;
          }
        }
      }
      li + li {
        margin-left: 215px;
      }
    }
  }
  .section-1 {
    padding-top: 50px;
    max-width: 2560px;
  }
  .section-2 {
    max-width: 1300px;
    margin-top: 50px;
    .solutions {
      display: flex;
      justify-content: center;
      flex-wrap: wrap;
      margin-top: 52px;
      li {
        width: 616px;
        height: 325px;
        text-align: center;
        background-size: contain;

        &:hover {
          p:nth-of-type(1) {
            color: #fff;
          }
          p:nth-of-type(2) {
            visibility: visible;
          }
          i {
            margin-top: 57px;
            transition: all 1s ease;
          }
        }

        i {
          // width: 51px;
          // height: 52px;
          display: inline-block;
          background-size: contain;
          margin-top: 108px;
          transition: all 1s ease;
        }
        p:nth-of-type(1) {
          font-size: 24px;
          font-weight: 400;
          color: #fff;
          line-height: 33px;
          margin-top: 29px;
        }
        p:nth-of-type(2) {
          padding: 0 70px;
          font-size: 16px;
          font-weight: 400;
          color: #ffffff;
          line-height: 26px;
          margin-top: 25px;
          visibility: hidden;
        }
        &:first-child {
          background: url(~@A/images/solution/smart-hardware/solution-tips/img_children_education.png)
            center no-repeat;
          i {
            width: 66px;
            height: 55px;
            background: url(~@A/images/solution/smart-hardware/solution-tips/child_2.png)
              center no-repeat;
          }
          &:hover {
            background: url(~@A/images/solution/smart-hardware/solution-tips/child.png)
              center no-repeat;
            // i {
            //   background: url(~@A/images/solution/smart-hardware/solution-tips/child_2.png)
            //     center no-repeat;
            // }
          }
        }
        &:nth-child(2) {
          background: url(~@A/images/solution/smart-hardware/solution-tips/img_wearable_device.png)
            center no-repeat;
          i {
            width: 45px;
            height: 65px;
            background: url(~@A/images/solution/smart-hardware/solution-tips/switch_2.png)
              center no-repeat;
          }
          &:hover {
            background: url(~@A/images/solution/smart-hardware/solution-tips/switch.png)
              center no-repeat;
            // i {
            //   background: url(~@A/images/solution/smart-hardware/solution-tips/switch_2.png)
            //     center no-repeat;
            // }
          }
        }
        &:nth-child(3) {
          background: url(~@A/images/solution/smart-hardware/solution-tips/img_service_robot.png)
            center no-repeat;
          i {
            width: 65px;
            height: 60px;
            background: url(~@A/images/solution/smart-hardware/solution-tips/robot_2.png)
              center no-repeat;
          }
          &:hover {
            background: url(~@A/images/solution/smart-hardware/solution-tips/robot.png)
              center no-repeat;
            // i {
            //   background: url(~@A/images/solution/smart-hardware/solution-tips/robot_2.png)
            //     center no-repeat;
            // }
          }
        }
        &:nth-child(4) {
          background: url(~@A/images/solution/smart-hardware/solution-tips/img_fitness_massage.png)
            center no-repeat;
          i {
            width: 68px;
            height: 55px;
            background: url(~@A/images/solution/smart-hardware/solution-tips/rest_2.png)
              center no-repeat;
          }
          &:hover {
            background: url(~@A/images/solution/smart-hardware/solution-tips/rest.png)
              center no-repeat;
            // i {
            //   background: url(~@A/images/solution/smart-hardware/solution-tips/rest_2.png)
            //     center no-repeat;
            // }
          }
        }
      }
    }
  }

  .section-3 {
    margin-top: 50px;
    .advantage {
      margin-top: 82px;

      li {
        display: flex;
        justify-content: center;
      }
      li:nth-child(1) {
        .advantage-image {
          width: 303px;
          height: 284px;
          margin-right: 158px;
          background: url(~@A/images/solution/smart-hardware/adv_1.png) center
            no-repeat;
        }
        .advantage-text {
          padding-top: 103px;
        }
      }
      li:nth-child(2) {
        margin-top: 65px;
        .advantage-image {
          width: 303px;
          height: 321px;
          margin-left: 170px;
          background: url(~@A/images/solution/smart-hardware/adv_2.png);
        }
        .advantage-text {
          padding-top: 136px;
          width: 428px;
        }
      }
      li:nth-child(3) {
        margin-top: 79px;

        .advantage-image {
          width: 303px;
          height: 321px;
          margin-right: 158px;
          background: url(~@A/images/solution/smart-hardware/adv_3.png) center
            no-repeat;
        }
        .advantage-text {
          padding-top: 103px;
        }
      }
      li:nth-child(4) {
        margin-top: 33px;
        .advantage-image {
          width: 303px;
          height: 321px;
          margin-left: 174px;
          background: url(~@A/images/solution/smart-hardware/adv_4.png) center
            no-repeat;
        }
        .advantage-text {
          padding-top: 136px;
        }
      }
    }
    .advantage-text {
      width: 428px;
      p:first-child {
        font-size: 34px;
        font-weight: 500;
        color: #666;
        line-height: 42px;
      }
      p:last-child {
        font-size: 16px;
        font-weight: 400;
        color: #999999;
        line-height: 28px;
        margin-top: 18px;
      }
    }
    .advantage-image {
    }
  }

  .section-4 {
    margin-top: 109px;
    padding-bottom: 129px;
    text-align: center;
    p {
      margin-bottom: 0;
    }
    .section-title {
      p:first-child {
        font-size: 34px;
        font-weight: bold;
        color: #333;
        line-height: 40px;
      }
      p:last-child {
        font-size: 16px;
        font-weight: 400;
        color: #666;
        line-height: 22px;
        margin-top: 43px;
      }
    }
    .section-item {
      margin-top: 49px;
      .section-button {
        color: #fff;
        background: #1784e9;
        width: 195px;
        height: 50px;
        line-height: 50px;
        text-align: center;
        margin: 0 auto;
        cursor: pointer;
      }
    }
  }

  .content-container {
    padding-top: 61px;
    padding-bottom: 75px;
    // background: rgba(171, 212, 255, 0.08);
    background: #f4f7f9;
    .content-sub-title {
      text-align: center;
      font-size: 16px;
      font-weight: 400;
      color: #999;
      line-height: 22px;
      margin-bottom: 72px;
    }
    .content-icon {
      display: inline-block;
      width: 69px;
      height: 69px;
      background-repeat: no-repeat;
      background-size: contain;
    }
    .icon-xmly {
      background-image: url(~@A/images/solution/assistant/logos/s_1.png);
    }

    .icon-kwyy {
      background-image: url(~@A/images/solution/assistant/logos/s_2.png);
    }

    .icon-xlxw {
      background-image: url(~@A/images/solution/assistant/logos/s_3.png);
    }

    .icon-aqy {
      background-image: url(~@A/images/solution/assistant/logos/s_4.png);
    }

    .icon-bilibili {
      background-image: url(~@A/images/solution/assistant/logos/s_5.png);
    }

    .icon-clock {
      background-image: url(~@A/images/solution/assistant/logos/t_1.png);
    }

    .icon-dictionary {
      background-image: url(~@A/images/solution/assistant/logos/t_2.png);
    }

    .icon-qa {
      background-image: url(~@A/images/solution/assistant/logos/t_3.png);
    }

    .icon-poetry {
      background-image: url(~@A/images/solution/assistant/logos/t_4.png);
    }

    .icon-chat {
      background-image: url(~@A/images/solution/assistant/logos/t_5.png);
    }

    .li-wrap {
      display: flex;
      justify-content: space-between;
      margin-bottom: 0;
      width: 1120px;
      margin: 0 auto;
      li {
        text-align: center;
        width: 132px;
        p {
          margin-top: 27px;
          font-size: 18px;
          font-weight: bold;
          color: #333;
          line-height: 30px;
          white-space: nowrap;
        }
      }
      li + li {
        // margin-left: 120px;
      }
    }
  }
  .section-swiper {
    background: #f4f7f9;
    margin-top: 70px;
  }
}
</style>
<style lang="scss">
.main-content {
  .section {
    .el-tabs__nav-scroll {
      margin: 0 auto;
    }

    .el-tabs__nav-wrap::after {
      display: none;
    }

    .el-tabs__item {
      padding: 0 102px;
      font-size: 20px;
      font-weight: 400;
      color: #666;
      &:hover {
        color: $primary;
      }
      &.is-active {
        color: $primary;
      }
    }
    .el-tabs__header {
      padding: 0 62px 0 96px;
      max-width: 1200px;
      margin: 0 auto;
    }
    .el-tabs__content {
      // margin-top: 46px;
      // padding-top:61px;
    }
  }
}
</style>
