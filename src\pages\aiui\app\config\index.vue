<template>
  <aiui-page :options="pageOptions">
    <div class="app-config-page">
      <!--如果point 包含2 是语义，包含4 是翻译-->

      <template v-if="showNormalConfig">
        <div class="tab-container">
          <el-tabs v-model="pointType" @tab-click="onPointTypeChange">
            <el-tab-pane label="语音识别" name="1" key="1"></el-tab-pane>
            <el-tab-pane label="结构化语义" name="2" key="2"></el-tab-pane>
            <el-tab-pane label="星火大模型" name="13" key="13"></el-tab-pane>
            <el-tab-pane label="语音合成" name="8" key="8"></el-tab-pane>
            <el-tab-pane
              label="后处理"
              name="3"
              key="3"
              v-if="
                !(
                  currentScene &&
                  currentScene.chainId === 'sos_app' &&
                  currentScene.point === '1,13'
                )
              "
            ></el-tab-pane>
          </el-tabs>
        </div>
        <recognition
          ref="recognition"
          :contentReviewConfig="contentReviewConfig"
          :appId="appId"
          :appInfo="app"
          :saving="saving"
          :limitCount="limitCount"
          :currentScene="currentScene"
          :subAccount="subAccount"
          :subAccountEditable="subAccountEditable"
          :sensitiveWordTypeAll="sensitiveWordTypeAll"
          @updateSensitiveWordTypeAll="updateSensitiveWordTypeAll"
          @changeSaving="changeSaving"
          @changeSensitiveWord="setChangeFlagSensitive"
          @change="setChangeFlag(1)"
          @onCompostionResult="onCompostionResult"
          @saveSuccess="saveSuccess(1)"
          @saveFail="saveFail"
          @setTranslateScene="setTranslateScene"
        ></recognition>
        <semantic
          ref="semantic"
          :contentReviewConfig="contentReviewConfig"
          :appId="appId"
          :appInfo="app"
          :saving="saving"
          :status="showControl[2] || showControl[13]"
          :limitCount="limitCount"
          :currentScene="currentScene"
          :rightTestOpen="rightTestOpen"
          :subAccountEditable="subAccountEditable"
          :sensitiveWordTypeAll="sensitiveWordTypeAll"
          :modelType="modelStatus"
          @updateSensitiveWordTypeAll="updateSensitiveWordTypeAll"
          @changeSaving="changeSaving"
          @changeList="changeList"
          @changeSensitiveWord="setChangeFlagSensitive"
          @change="setChangeFlag(2)"
          @setQcChange="setQcChanged"
          @statusChange="statusChange($event, 2)"
          @modelStatusChange="(val, init) => modelStatusChange(val, init)"
          @saveSuccess="saveSuccess(2)"
          @saveFail="saveFail"
          @setOpenQAInfo="setOpenQAInfo"
          @saveSuccessMessage="saveSuccessMessage"
        >
          <backstop
            v-if="!showControl[13]"
            :saving="saving"
            :status="showControl['backstop']"
            :semanticStatus="showControl[2]"
            :currentScene="currentScene"
            :openQAInfo="openQAInfo"
            :subAccountEditable="subAccountEditable"
            @change="setChangeFlag('backstop')"
            @statusChange="statusChange($event, 'backstop')"
            @saveSuccess="saveSuccess('backstop')"
            @saveFail="saveFail"
            @updateSemanticConfig="updateSemanticConfig"
          ></backstop>
        </semantic>

        <spark
          ref="spark"
          :modelType="modelStatus"
          :appId="appId"
          :currentScene="currentScene"
          :subAccountEditable="subAccountEditable"
          :saving="saving"
          :itemChangeList="itemChangeList"
          @sparkChange="onSparkChange"
          @modelStatusChange="(val, init) => modelStatusChange(val, init)"
        >
        </spark>
        <synthesisSOS
          v-if="currentScene && currentScene.chainId === 'sos_app'"
          ref="synthesis"
          :appId="appId"
          :appInfo="app"
          :saving="saving"
          :status="true"
          :currentScene="currentScene"
          :subAccountEditable="subAccountEditable"
        >
        </synthesisSOS>
        <synthesis
          v-else
          ref="synthesis"
          :appId="appId"
          :appInfo="app"
          :saving="saving"
          :status="showControl[8] || showControl[14]"
          :currentScene="currentScene"
          :subAccountEditable="subAccountEditable"
          @change="setChangeFlag(8)"
          @vcnchange="onVcnChange"
          @speedchange="onSpeedChange"
          @volumechange="onVolumeChange"
          @statusChange="statusChange($event, 8)"
          @saveSuccess="saveSuccess(8)"
          @saveFail="saveFail"
        ></synthesis>

        <div ref="postProcessDiv" id="postProcessDivId">
          <post-process
            v-if="!showControl[13]"
            ref="postProcess"
            :appId="appId"
            :appInfo="app"
            :saving="saving"
            :status="showControl[3]"
            :currentScene="currentScene"
            :subAccountEditable="subAccountEditable"
            @change="
              () => {
                setChangeFlag(3)
                hasPostProcessError = false
              }
            "
            @statusChange="statusChange($event, 3)"
            @saveSuccess="saveSuccess(3)"
            @saveFail="saveFail"
            @validSuccess="saving = true"
            @validFail="validatePostProcessFail"
            @clearError="hasPostProcessError = false"
          ></post-process>

          <!-- 大模型后处理 -->
          <llm-post-process
            v-if="
              showControl[13] &&
              !(
                currentScene &&
                currentScene.chainId === 'sos_app' &&
                currentScene.point === '1,13'
              )
            "
            ref="llmPostProcess"
            :appId="appId"
            :appInfo="app"
            :saving="saving"
            :currentScene="currentScene"
            :subAccountEditable="subAccountEditable"
            @change="setLLMPostProcessChange"
            @saveSuccess="setLLMPostProcessSuccess"
            @validFail="validateLLMPostProcessFail"
            @clearError="hasLLMPostProcessError = false"
          ></llm-post-process>
        </div>
        <div
          :style="{
            width: '100%',
            height: advancedPlaceholderHeight,
          }"
        ></div>

        <!-- <virtual-human
          v-if="app && app.avatar && app.apiSecret"
          ref="virtualRef"
          :saving="saving"
          :status="showControl[10]"
          :semanticStatus="showControl[2]"
          :synthesisStatus="showControl[8]"
          :vcn="vcn"
          :volume="volume"
          :speed="speed"
          :currentScene="currentScene"
          :subAccountEditable="subAccountEditable"
          @change="setChangeFlag(10)"
          @statusChange="statusChange($event, 10)"
          @saveSuccess="saveSuccess(10)"
          @saveFail="saveFail"
          @support="toggleSupport"
          @vcnmatch="toggleVcnmatch"
        >
        </virtual-human> -->
      </template>
      <template
        v-if="
          currentScene.point && currentScene.point.split(',').indexOf('4') >= 0
        "
      >
        <translate
          ref="translate"
          :contentReviewConfig="contentReviewConfig"
          :appId="appId"
          :appInfo="app"
          :saving="saving"
          :currentScene="currentScene"
          :subAccountEditable="subAccountEditable"
          :limitCount="limitCount"
          :sensitiveWordTypeAll="sensitiveWordTypeAll"
          :canCreateTranslateScene="canCreateTranslateScene"
          @updateSensitiveWordTypeAll="updateSensitiveWordTypeAll"
          @changeSensitiveWord="setChangeFlagSensitive"
          @changeSaving="changeSaving"
          @change="setChangeFlag(4)"
          @saveSuccess="saveSuccess(4)"
          @setTranslateScene="setTranslateScene"
        >
        </translate>
        <synthesis
          :appId="appId"
          :appInfo="app"
          :saving="saving"
          :status="showControl[8]"
          :currentScene="currentScene"
          :subAccountEditable="subAccountEditable"
          @change="setChangeFlag(8)"
          @statusChange="statusChange($event, 8)"
          @saveSuccess="saveSuccess(8)"
          @saveFail="saveFail"
        >
        </synthesis>
      </template>
      <div
        v-if="!(currentScene && currentScene.point)"
        v-loading="!(currentScene && currentScene.point)"
        style="height: calc(100vh - 60px)"
      ></div>
    </div>
    <message-box :dialog="msgDialog" :subAccount="subAccount"></message-box>
    <sub-account-save
      :dialog="subAccountSaveDialog"
      @saveSubAccountEdit="saveChange"
    ></sub-account-save>
    <FristSettingDialog
      :visible="fristSettingVisible"
      @toSaveAiPower="toSaveAiPower"
    />
    <publish-dialog
      :dialog="publishDialog"
      :subAccountEditable="subAccountEditable"
      :subAccount="subAccount"
    />
    <version-dialog
      :dialog="versionDialog"
      :subAccountEditable="subAccountEditable"
    />
    <audit-dialog
      :dialog="auditDialog"
      :subAccountEditable="subAccountEditable"
      :subAccount="subAccount"
    />
  </aiui-page>
</template>

<script>
import { mapGetters } from 'vuex'
import scene from './scene'
import sceneImport from './sceneImport'
import recognition from './recognition.vue'
import semantic from './semantic.vue'
import spark from './spark.vue'
import virtualHuman from './virtualHuman.vue'
import backstop from './backstop.vue'
import postProcess from './postProcess.vue'
import llmPostProcess from './llmPostProcess.vue'
import synthesis from './synthesis.vue'
import synthesisSOS from './synthesisSOS.vue'
import translate from './translate.vue'
import MessageBox from './messageDialog'
import SubAccountSave from '../dialog/subAccountSave'
import FristSettingDialog from '../dialog/fristSettingDialog.vue'
import PublishDialog from '../sandbox/publish.vue'
import VersionDialog from '../sandbox/version.vue'
import AuditDialog from '../audit.vue'

export default {
  name: 'app-config',
  components: {
    scene,
    sceneImport,
    recognition,
    semantic,
    spark,
    postProcess,
    llmPostProcess,
    synthesis,
    synthesisSOS,
    backstop,
    virtualHuman,
    translate,
    MessageBox,
    SubAccountSave,
    FristSettingDialog,
    PublishDialog,
    VersionDialog,
    AuditDialog,
  },
  props: {
    contentReviewConfig: {
      type: Object,
      default: function () {
        return {}
      },
      deep: true,
    },
    subAccount: Boolean,
    rightTestOpen: Boolean,
    subAccountEditable: Boolean,
  },
  data() {
    return {
      // 当前tab聚焦的point
      pointType: '1',
      fristSettingVisible: false, // 首次进入配置页面弹窗
      pageOptions: {
        title: '',
        showHead: true,
        loading: false,
        returnBtn: false,
      },
      sceneList: [],
      // currentScene: {
      //   sceneName: this.$route.query.sceneName || 'main',
      // },
      showControl: {},
      change: false,
      saving: false,
      count: 0,
      sensitiveWordTypeAll: [],
      itemChangeList: {}, //0: 开关配置、1: 识别、 2：语义、3：后处理、4：翻译、8：合成, 9: 识别敏感词, 10: 语义敏感词, 11: 翻译合成前敏感词识别
      openQAInfo: {},
      msgDialog: {
        show: false,
        isCertificated: false,
      },
      canCreateTranslateScene: false,
      qcChanged: false,
      subAccountSaveDialog: {
        show: false,
      },
      // 四元组组合是否正确
      hasError: false,

      // 是否是虚拟人暂时支持的类型
      supportVirtualHuman: true,

      // 虚拟人与合成发音人是否匹配
      anchorVcnMatch: true,

      // 合成的vcn（发音人）
      vcn: '',
      // 合成的speed（语速）
      speed: 50,
      // 合成的volume (音量)
      volume: 50,

      // 存放当前modelStatus
      modelStatus:
        this.currentScene &&
        this.currentScene.point &&
        this.currentScene.point.split(',').indexOf('13') >= 0
          ? '13'
          : '2',

      // 大模型后处理是否修改
      llmPostProcessChange: false,
      hasLLMPostProcessError: false,

      hasPostProcessError: false,

      publishDialog: {
        show: false,
      },
      versionDialog: {
        show: false,
      },
      auditDialog: {
        show: false,
      },

      advancedPlaceholderHeight: 0,

      sparkIsChange: false,
      sparkChangeParam: {},
    }
  },
  methods: {
    handleObserve() {
      let that = this
      // 获取要监听的DOM元素
      const targetElement = document.getElementById('postProcessDivId')

      // 创建 MutationObserver 实例
      const observer = new MutationObserver((mutations) => {
        console.log('监听到的mutation', mutations)
        mutations.forEach((mutation) => {
          // 当元素样式发生变化时
          adjustElementHeight()
        })
      })

      // 配置 MutationObserver 监听属性变化
      const config = {
        childList: true, // 子节点的变动（新增、删除或者更改）
        attributes: true, // 属性的变动
        attributeFilter: ['style'],
        characterData: true, // 节点内容或节点文本的变动
        subtree: true, // 是否将观察器应用于该节点的所有后代节点
      }

      // 开始监听
      observer.observe(targetElement, config)

      // 函数用于确保元素高度至少为一屏的高度
      function adjustElementHeight() {
        const screenHeight = window.innerHeight
        let elementHeight = targetElement.getBoundingClientRect().height
        elementHeight = Math.max(100, elementHeight)
        if (elementHeight <= screenHeight) {
          console.log(
            'elementHeight <= screenHeight',
            elementHeight,
            screenHeight
          )
          that.advancedPlaceholderHeight = `${Math.max(
            screenHeight - elementHeight - 300,
            0
          )}px`
        } else {
          console.log(
            'elementHeight> screenHeight',
            elementHeight,
            screenHeight
          )
          that.advancedPlaceholderHeight = 0
        }
      }
      adjustElementHeight()
    },
    getAppSceneList() {
      let self = this
      this.$utils.httpGet(
        this.$config.api.AIUI_SCENE_LIST,
        {
          appid: this.appId,
        },
        {
          success: (res) => {
            if (res.flag) {
              self.sceneList = res.data
              // 获取当前场景
              let sceneName = self.$route.query.sceneName || 'main'
              res.data.forEach((item) => {
                if (item.sceneName === sceneName) {
                  // self.currentScene = item
                  self.initParams(item)
                  this.$store.dispatch('aiuiApp/setCurrentScene', item)
                }
              })
            } else {
              this.$router.push({ name: 'apps' })
            }
          },
          error: (err) => {
            console.log('page=>>')
            console.log(err)
          },
        }
      )
    },
    initParams(scene) {
      if (!scene.point) return
      this.change = false
      this.saving = false
      if (scene.point.includes('13')) {
        this.modelStatus = '13'
      } else {
        this.modelStatus = '2'
      }
      let point = scene.point.split(',')
      this.showControl = {}
      this.itemChangeList = {}

      point.forEach((item) => {
        this.showControl[item] = true
        this.itemChangeList[item] = false
      })
    },
    setChangeFlagSensitiveAll(keys, flag) {
      for (let i = 0; i < keys.length; i++) {
        this.itemChangeList[keys[i]] = flag
      }
      this.change = flag
      if (!flag) {
        this.count++
      }
    },
    changeList(key, flag) {
      this.itemChangeList[key] = flag
    },
    setChangeFlagSensitive(key, flag) {
      this.itemChangeList[key] = flag
      this.change = flag
      if (!flag) {
        this.count++
      }
    },
    onCompostionResult(val) {
      this.hasError = val
    },
    setChangeFlag(key) {
      this.itemChangeList[key] = true
      this.change = true
    },
    /**
     * 单组件保存成功回调
     * 把change 标志位置为未保存，触发一次count 变化，用于检测整体是否保存结束。
     */
    saveSuccess(key) {
      this.itemChangeList[key] = false
      this.qcChanged = false
      this.count++
    },
    changeSaving(flag) {
      //this.saving = flag
      this.saveFail()
    },
    saveFail() {
      this.saving = false
    },
    sceneChange(scene) {
      // this.currentScene = scene
      this.initParams(scene)
      this.$store.dispatch('aiuiApp/setCurrentScene', scene)
    },
    statusChange(value, key) {
      let obj = {}
      if (key == 2) {
        // 关闭了语义理解的开关，2，13,均要关闭
        if (!value) {
          obj[2] = false
          obj[13] = false
          this.modelStatus = '2'
        } else {
          if (this.modelStatus == 2) {
            obj[2] = true
            obj[13] = false
          } else {
            obj[2] = false
            obj[13] = true
          }
        }
      } else if (key == 8) {
        // 合成
        if (!value) {
          // 14为大模型合成
          obj[8] = false
          obj[14] = false
        } else {
          obj[8] = true
        }
      } else {
        obj[key] = value
      }

      this.showControl = Object.assign({}, this.showControl, obj)
      console.log(
        '---------------print this.showControl--------',
        this.showControl,
        JSON.stringify(this.showControl)
      )
      if (key == '2') {
        this.showControl['backstop'] = false
        this.showControl['10'] = false
      }
      if (key == '8') {
        // 语音合成关闭时，虚拟人应该也联动关闭
        this.showControl['10'] = false
      }

      if (key !== 'backstop') {
        this.itemChangeList[0] = true
        this.change = true
      }
    },
    modelStatusChange(key, init) {
      // 处理模型变化，2为普通语义，13为大模型
      console.log('modelStatusChange', key)
      this.modelStatus = key
      if (key == '2') {
        this.$set(this.showControl, '2', true)
        this.$set(this.showControl, '13', undefined)
        this.llmPostProcessChange = false
        if (!init) {
          this.$set(this.showControl, 'backstop', false)
        }
      }
      if (key == '13') {
        this.$set(this.showControl, '13', true)
        this.$set(this.showControl, '2', undefined)
        this.$set(this.showControl, '3', undefined)
        this.itemChangeList[3] = false
      }

      if (!init) {
        this.itemChangeList[0] = true
        this.change = true
      }
    },
    saveAaScene() {
      // 2 为通用语义模型，13 为星火认知交互大模型, 两者互斥
      let self = this
      let point = ['1']
      if (self.showControl[2]) {
        point.push('2')
      }
      if (self.showControl[13]) {
        const index = point.findIndex((im) => im === '2')
        if (index !== -1) {
          point.splice(index, 1)
        }
        point.push('13')
      }

      // AIUI交互大模型开关开时，其他清除
      if (self.showControl[3] && !self.showControl[13]) {
        point.push('3')
      }
      if (self.showControl[4]) {
        point.push('4')
      }
      if (self.showControl[8] || self.showControl[14]) {
        if (self.showControl[2]) {
          point.push('8')
        }
        if (self.showControl[13]) {
          point.push('14')
        }
      }

      if (!self.showControl[2] && !self.showControl[13]) {
        // 两种语义理解模型均关闭时，语音合成有设置的话应该为8
        if (self.showControl[8] || self.showControl[14]) {
          point.push('8')
        }
      }

      if (self.showControl[10] && !self.showControl[13]) {
        point.push('10')
      }

      let data = {
        appid: self.appId,
        point: point.join(','),
        sceneId: self.currentScene.sceneBoxId,
        sceneName: self.currentScene.sceneBoxName,
      }

      // 判断 保存 交互大模型 时，还须调用另外接口
      const pointFlg = point.join(',')
      let allPromises = [this.saveNormalAaScene(data)]
      if (pointFlg.includes('13')) {
        // 表示切换成AIUI交互大模型
        allPromises = [this.saveNormalAaScene(data), this.savecmbv45AaScene()]
      }

      Promise.all(allPromises)
        .then((res) => {
          self.updateSceneListPoint(point.join(','))
          self.saveSuccess(0)
        })
        .catch((e) => {
          if (e) {
            this.$message.error(res.desc)
          }
        })
    },

    saveNormalAaScene(data) {
      let self = this
      return new Promise((resolve, reject) => {
        this.$utils.httpPost(this.$config.api.AIUI_SCENE_SAVEAASCENE, data, {
          success: (res) => {
            if (res.flag) {
              // self.updateSceneListPoint(point.join(','))
              // self.saveSuccess(0)
              resolve()
            } else {
              // this.$message.error(res.desc)
              // self.saveFail()
              reject(res.desc)
            }
          },
          error: (err) => {
            // self.saveFail()
            // console.log('page=>>')
            // console.log(err)
            reject()
          },
        })
      })
    },

    savecmbv45AaScene() {
      let that = this
      let param = {
        appid: this.appId,
        chainId: this.currentScene.chainId || 'cbm_v45',
        sceneName: this.currentScene.sceneBoxName,
      }
      if (this.sparkIsChange) {
        // param = {
        //   ...param,
        //   distToken: this.sparkChangeParam.distToken || 0,
        //   ttsCnr: JSON.stringify({
        //     enable: this.sparkChangeParam.enable,
        //     colloguialDegree: this.sparkChangeParam.colloguialDegree,
        //   }),
        // }
        if (this.sparkChangeParam.distribute) {
          param.distribute = this.sparkChangeParam.distribute
        }
        if (this.sparkChangeParam.searchType) {
          param.searchType = this.sparkChangeParam.searchType
        }
      }

      return new Promise((resolve, reject) => {
        this.$utils.httpPost(
          this.$config.api.AIUI_APP_PLUGINSTUDIO_SAVECONFIG,
          param,
          {
            success: (res) => {
              that.sparkIsChange = false
              that.sparkChangeParam = {}
              resolve()
            },
          }
        )
      })
    },
    updateSceneListPoint(point) {
      this.sceneList.forEach((item) => {
        if (item.sceneName === (this.$route.query.sceneName || 'main')) {
          item.point = point
        }
      })
    },
    beforeSaveChange() {
      if (this.subAccount) {
        this.subAccountSaveDialog.show = true
      } else {
        this.saveChange()
      }
    },
    saveChange() {
      // 大模型后处理是否有错，有错滚动到指定位置
      if (this.hasLLMPostProcessError || this.hasPostProcessError) {
        this.scrollPostProcess()
      } else {
        // 如果后处有修改，先检测后处理是否合法
        if (this.itemChangeList[3] && this.showControl[3]) {
          this.$refs.postProcess.validForm()
        } else if (
          this.itemChangeList[2] &&
          this.showControl[2] &&
          this.qcChanged
        ) {
          this.$refs.semantic.qcConfigValid()
        } else if (
          (this.itemChangeList[9] ||
            this.itemChangeList[10] ||
            this.itemChangeList[11]) &&
          this.change &&
          this.sensitiveWordTypeAll.length > 0
        ) {
          this.updateSensitiveWordType()
        } /* else if (this.itemChangeList[10] && this.change) {
          this.$refs.semantic.emitUpdateSensitiveWord()
        }*/ else {
          this.saving = true
        }
      }
    },
    updateSensitiveWordTypeAll(sensitiveWordTypeAll) {
      this.sensitiveWordTypeAll = sensitiveWordTypeAll
    },
    // 更新敏感词类型
    updateSensitiveWordType() {
      let thiz = this
      console.log(this.sensitiveWordTypeAll)
      thiz.changeSaving(true)
      let url =
        this.$config.api.SENSITIVE_TYPE_AND_CONFIG_UPDATE +
        '?appid=' +
        thiz.appId +
        '&sceneId=' +
        thiz.currentScene.sceneBoxId
      //+ '&type=' + (thiz.type === 0 ? 1 : 2)
      this.$utils.httpPost(url, JSON.stringify(thiz.sensitiveWordTypeAll), {
        config: {
          headers: {
            'Content-Type': 'application/json;charset=UTF-8',
          },
        },
        noErrorMessage: true,
        success: (res) => {
          if (res.flag) {
            thiz.sensitiveWordTypeAll = []
            thiz.changeSaving(true)
            let keys = []
            if (this.itemChangeList[9] && this.change) {
              keys.push(9)
              thiz.$refs.recognition.getSensitiveWordType()
            }
            if (this.itemChangeList[10] && this.change) {
              keys.push(10)
              thiz.$refs.semantic.getSensitiveWordType()
            }
            if (this.itemChangeList[11] && this.change) {
              keys.push(11)
              thiz.$refs.translate.getSensitiveWordType()
            }
            thiz.setChangeFlagSensitiveAll(keys, false)
          } else {
            thiz.changeSaving(false)
          }
        },
        error: (err) => {
          thiz.changeSaving(false)
          if (err) thiz.$message.error(err.desc)
        },
      })
    },
    setOpenQAInfo(val) {
      this.openQAInfo = val
    },
    updateSemanticConfig(val) {
      if (val) {
        this.$refs.semantic.updateSemanticConfig()
      }
    },
    setTranslateScene(val) {
      this.canCreateTranslateScene = val == 1 ? true : false
    },
    setQcChanged() {
      this.qcChanged = true
      this.setChangeFlag(2)
    },

    setLLMPostProcessChange() {
      this.change = true
      this.llmPostProcessChange = true
      this.hasLLMPostProcessError = false
    },
    setLLMPostProcessSuccess() {
      this.change = false
      this.llmPostProcessChange = false
      this.count++
    },

    validateLLMPostProcessFail() {
      // this.change = false
      this.hasLLMPostProcessError = true
    },

    validatePostProcessFail() {
      this.hasPostProcessError = true
      this.scrollPostProcess()
    },

    scrollPostProcess() {
      let scrollDom = document.getElementById('scrollDom')
      if (this.$refs['postProcessDiv']) {
        scrollDom.scrollTo({
          top: this.$refs['postProcessDiv'].offsetTop - 189,
          behavior: 'smooth',
        })
      }
    },

    // 是否支持本地虚拟人配置
    toggleSupport(val) {
      this.supportVirtualHuman = val
    },
    // 选择的合成发音人与虚拟人是否匹配
    toggleVcnmatch(val) {
      this.anchorVcnMatch = val
    },

    onVcnChange(val) {
      // this.$refs.virtualRef.validateValid()
      console.log('onVcnChange', val)
      this.vcn = val
    },
    onSpeedChange(val) {
      this.speed = val
    },
    onVolumeChange(val) {
      this.volume = val
    },
    toSaveAiPower(semanticsUnderstanding, speechSynthesis) {
      this.statusChange(semanticsUnderstanding, 2)
      this.statusChange(speechSynthesis, 8)
      this.fristSettingVisible = false
      this.saveChange()
    },
    saveSuccessMessage() {
      // this.$message_pro_success(
      //   '保存成功',
      //   '当前保存仅在测试环境生效，应用更新发布后正式环境才能生效配置。'
      // )
      const str = `当前页面配置修改仅在测试环境${
        this.$route.query.sceneName || 'main'
      }_box生效，生产环境${
        this.$route.query.sceneName || 'main'
      }尚未生效，请“更新发布”至生产环境${
        this.$route.query.sceneName || 'main'
      }体验。`
      this.$message_warning_success(str)
    },

    onPointTypeChange() {
      let scrollDom = document.getElementById('scrollDom')
      if (this.pointType === '1') {
        scrollDom.scrollTo({
          top: 0,
          behavior: 'smooth',
        })
      } else if (this.pointType === '2') {
        console.log(`this.$refs['semantic'].offsetTop`, this.$refs['semantic'])
        scrollDom.scrollTo({
          top: this.$refs['semantic'].$el.offsetTop - 189,
          behavior: 'smooth',
        })
      } else if (this.pointType === '13') {
        console.log(`this.$refs['spark'].offsetTop`, this.$refs['spark'])
        scrollDom.scrollTo({
          top: this.$refs['spark'].$el.offsetTop - 189,
          behavior: 'smooth',
        })
      } else if (this.pointType === '8') {
        scrollDom.scrollTo({
          top: this.$refs['synthesis'].$el.offsetTop - 189,
          behavior: 'smooth',
        })
      } else if (this.pointType === '3') {
        scrollDom.scrollTo({
          top: this.$refs['postProcessDiv'].offsetTop - 189,
          behavior: 'smooth',
        })
      }
    },

    handleScroll() {
      let scrollDom = document.getElementById('scrollDom')

      if (
        this.$refs['postProcessDiv'] &&
        scrollDom.scrollTop > this.$refs['postProcessDiv'].offsetTop - 189 - 1
      ) {
        this.pointType = '3'
        return
      }
      if (
        this.$refs['synthesis'] &&
        scrollDom.scrollTop > this.$refs['synthesis'].$el.offsetTop - 189 - 1
      ) {
        this.pointType = '8'
        return
      }

      if (
        this.$refs['spark'] &&
        scrollDom.scrollTop > this.$refs['spark'].$el.offsetTop - 189 - 1
      ) {
        this.pointType = '13'
        return
      }
      if (
        this.$refs['semantic'] &&
        scrollDom.scrollTop > this.$refs['semantic'].$el.offsetTop - 189 - 1
      ) {
        this.pointType = '2'
        return
      }
      this.pointType = '1'
    },

    onSparkChange(property, val) {
      this.change = true
      this.sparkIsChange = true
      if (property) {
        this.sparkChangeParam[property] = val
      }
    },
  },
  computed: {
    ...mapGetters({
      currentScene: 'aiuiApp/currentScene',
      app: 'aiuiApp/app',
      limitCount: 'aiuiApp/limitCount',
    }),
    appId() {
      return this.$route.params.appId
    },
    showNormalConfig() {
      if (
        this.currentScene.point &&
        this.currentScene.point.split(',').indexOf('1') >= 0 &&
        this.currentScene.point.split(',').indexOf('4') < 0
      ) {
        return true
      } else {
        return false
      }
    },
  },
  watch: {
    count() {
      /**
       * 各组件 change 标志位均为false，则保存结束
       */
      let flag = false
      for (let i in this.itemChangeList) {
        if (this.itemChangeList[i]) {
          flag = true
          break
        }
      }
      if (this.llmPostProcessChange) {
        flag = true
      }
      this.change = flag
      this.saving = flag
      if (!this.saving) {
        this.saveSuccessMessage()
      }
    },
    saving() {
      if (this.saving && this.itemChangeList[0]) {
        this.saveAaScene()
      }
      if (this.saving) {
        this.pageOptions.loading = true
      } else {
        this.pageOptions.loading = false
      }
    },
    change() {
      this.$store.dispatch('aiuiApp/setConfigChange', this.change)
    },
    app(val) {
      let prefix = this.subAccount ? 'sub_app_' : 'app_'
      let item = prefix + val.appid
      this.msgDialog.isCertificated = val.check
      if (
        val &&
        val.check &&
        this.subAccount &&
        localStorage.getItem(item) == 3
      ) {
        this.msgDialog.show = true
        localStorage.setItem(item, 2)
      } else if ((val && val.check) || this.subAccount) {
        if (localStorage.getItem(item) == 1) {
          this.msgDialog.show = true
          localStorage.setItem(item, 2)
        }
      } else {
        this.msgDialog.show = false
      }
    },
    showNormalConfig(val) {
      if (val) {
        this.$nextTick(() => {
          this.handleObserve()
        })
      }
    },
  },
  created() {
    // 首次弹窗
    this.fristSettingVisible = this.$route.params.isFrist
    this.getAppSceneList()
  },
  mounted() {
    this.$nextTick(function () {
      window.onbeforeunload = () => {
        if (this.change) return '放弃当前未保存内容而关闭页面？'
      }
    })
    const dom = document.getElementById('scrollDom')
    if (dom) {
      dom.addEventListener('scroll', this.handleScroll)
    }
  },
  destroyed() {
    const dom = document.getElementById('scrollDom')
    if (dom) {
      dom.removeEventListener('scroll', this.handleScroll)
    }

    // 离开此页面时，置空当前情景模式的设置, 防止进入列表页 换了appid后，currentScene错误的问题
    this.$store.dispatch('aiuiApp/setCurrentScene', {})
  },
  beforeRouteLeave: function (to, from, next) {
    if (this.change) {
      this.$confirm('放弃当前未保存内容而关闭页面？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          window.onbeforeunload = null
          next()
        })
        .catch(() => {
          next(false)
        })
    } else {
      window.onbeforeunload = null
      next()
    }
  },
}
</script>

<style lang="scss" scoped>
.app-config-page {
  padding-bottom: 40px;
}
.sandbox-tip {
  margin-bottom: 24px;
  padding: 11px 17px;
  color: $warning;
  border-radius: 4px;
  border: 1px solid $warning;
}

.item-title {
  position: relative;
  font-size: 16px;
  font-weight: 500;
  padding-left: 10px;
  margin-top: 24px;
  margin-bottom: 8px;
  &:before {
    width: 2px;
    height: 16px;
    background-color: $primary;
    content: ' ';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    margin: auto;
  }
}

.tab-container {
  position: sticky;
  top: 0px;
  z-index: 100;
  background: #fff;
  height: 60px;
  border-bottom: 1px solid #e7e9ed;
  display: flex;
  align-items: center;
  justify-content: space-between;
  :deep(.el-tabs__header) {
    margin: 0;
  }
  :deep(.el-tabs__item) {
    padding: 0 40px;
    font-size: 14px;
    height: 60px;
    line-height: 60px;
  }
  :deep(.el-tabs__nav-wrap::after) {
    display: none;
  }
}
</style>
