<template>
  <div class="app-container">
    <aiui-menu />
    <div class="os-container" style="flex-direction: column">
      <!-- <aiui-header></aiui-header> -->
      <div class="os-container">
        <div class="os-aside">
          <studio-skill-menu-head />
          <div class="os-scroll os_scroll" style="height: calc(100% - 63px)">
            <div>
              <div
                class="studio-skill-menu-head-skill-name"
                @click.stop="openSelectSkill($event)"
              >
                <span :title="skill.zhName || '-'">{{
                  skill.zhName || '-'
                }}</span>
              </div>
              <select-skill-popover :subAccount="subAccount" />
            </div>

            <el-menu class="os-menu" :default-openeds="openeds">
              <template v-for="menu in menus">
                <el-submenu
                  class="skill-menu"
                  v-if="menu.sub"
                  :key="menu.key"
                  :index="menu.key"
                >
                  <template slot="title">
                    <!-- <i :class="menu.icon"></i> -->
                    <!-- <svg-icon
                      :iconClass="menu.icon"
                      class="menu_icon"
                      :customStyle="{ width: menu.width, height: menu.height }"
                    /> -->
                    <span>{{ menu.value }}</span>
                  </template>
                  <el-menu-item-group>
                    <template v-for="submenu in menu.sub">
                      <el-menu-item
                        v-if="!submenu.disabled"
                        :key="submenu.key"
                        :index="submenu.key"
                        :class="[
                          'skill-sub-menu',
                          { 'os-menu-active': routeName === submenu.key },
                        ]"
                        @click="selectMenu(submenu)"
                      >
                        <template
                          v-if="
                            submenu.key !== 'skill-intentions' &&
                            submenu.key !== 'sub-skill-intentions'
                          "
                        >
                          {{ submenu.value }}
                        </template>
                        <template v-else>
                          {{ submenu.value }}
                          <div
                            class="os-menu-add skill-layout"
                            @click="addIntent"
                            v-if="subAccountEditable"
                          >
                            <i class="ic-r-plus" />
                            创建
                          </div>
                        </template>
                      </el-menu-item>
                    </template>
                  </el-menu-item-group>
                </el-submenu>
                <el-menu-item
                  v-else
                  :key="menu.index"
                  :index="menu.key"
                  :class="{ 'os-menu-active': routeName === menu.key }"
                  @click="selectMenu(menu)"
                >
                  <!-- <i :class="menu.icon"></i> -->
                  <!-- <svg-icon
                    :iconClass="menu.icon"
                    class="menu_icon"
                    :customStyle="{ width: menu.width, height: menu.height }"
                  /> -->
                  <span slot="title">{{ menu.value }}</span>
                </el-menu-item>
              </template>
            </el-menu>
            <!-- <a
              class="studio-skill-layout-to-docs"
              :href="DocsInUrl"
              target="_blank"
              >文档中心<i class="ic-r-link skill-left-bar"></i
            ></a> -->
          </div>
        </div>
        <div class="os-main">
          <template v-if="skill.id">
            <router-view
              :subAccount="subAccount"
              :subAccountEditable="subAccountEditable"
              :limitCount="limitCount"
            ></router-view>
          </template>
        </div>
        <div
          class="os-side-right"
          :class="{ 'os-side-right-open': rightTestOpen }"
        >
          <right-test-close
            v-if="!rightTestOpen"
            :rightTestOpen="rightTestOpen"
          ></right-test-close>
          <skill-debug v-show="rightTestOpen" debugType="skill" />
          <feedBackHover :rightTestOpen="rightTestOpen" />
        </div>
        <!-- 子账号登录后首次进入提示 -->
        <cooperate-warn-dialog
          :dialog="cooperateDialog"
          type="firstEnterSkill"
        ></cooperate-warn-dialog>
      </div>
      <p
        class="publish-result-for-test"
        id="publish-result-for-test"
        style="display: none"
      >
        {{ publishResultForTest }}
      </p>
    </div>
  </div>
</template>

<script>
import Vue from 'vue'
import SSO from 'sso/sso.js'
import dicts from '@M/dicts'
import { mapGetters } from 'vuex'
import Axios from 'axios'
import RightTestClose from '@C/rightTestClose'
import feedBackHover from '../components/feedBackHover'
import CooperateWarnDialog from '@C/cooperatWarnDialog'
import AiuiHeader from '../components/aiuiHeader'
import OsHeader from '../components/header'
import aiuiMenu from '../components/aiuiMenu'
import SelectSkillPopover from '@P/studio/skill/dialog/selectSkill.vue'

export default {
  data() {
    return {
      menus: [
        {
          key: 'skill-info',
          value: '基本信息',
          icon: 'basic-information',
          index: 0,
          width: '18px',
          height: '18px',
        },
        {
          key: 'skill-model',
          value: '交互模型',
          icon: 'interaction-model',
          index: 1,
          width: '16px',
          height: '16px',
          sub: [
            {
              key: 'skill-intentions',
              value: '意图',
              index: '1-1',
              disabled: false,
            },
            {
              key: 'skill-modifiers',
              value: '自定义修饰语',
              index: '1-2',
              disabled: true,
            },
            {
              key: 'skill-entities',
              value: '引用的实体',
              index: '1-3',
              disabled: false,
            },
            {
              key: 'skill-auxiliaries',
              value: '引用的辅助词',
              index: '1-4',
              disabled: false,
            },
            // {
            //   key: 'skill-refered-intentions',
            //   value: '引用的意图',
            //   index: '1-5',
            //   disabled: false,
            // },
          ],
        },
        {
          key: 'skill-post-process',
          value: '技能后处理',
          icon: 'skills-post-processing',
          index: 2,
          width: '17px',
          height: '17px',
        },
        {
          key: 'skill-publish',
          value: '发布',
          icon: 'release',
          index: 3,
          width: '17px',
          height: '17px',
        },
        // { key: 'skill-optimize', value: '推荐优化', icon: 'ic-mn-optimize', index: 4},
        {
          key: 'skill-version',
          value: '版本管理',
          icon: 'version-management',
          index: 5,
          width: '18px',
          height: '16px',
        },
      ],
      openeds: ['skill-model'],
      DocsInUrl: `${this.$config.docs}doc-44/`,
      cooperateDialog: {
        show: false,
      },
    }
  },
  computed: {
    ...mapGetters({
      skill: 'studioSkill/skill',
      subAccountSkillAuths: 'studioSkill/subAccountSkillAuths',
      subAccount: 'user/subAccount',
      rightTestOpen: 'studioSkill/rightTestOpen',
      limitCount: 'aiuiApp/limitCount',
      businessId: 'studioSkill/id',

      // for test 巡检
      publishResultForTest: 'studioSkill/publishResultForTest',
      // for test 巡检
    }),
    subAccountEditable() {
      // 权限： 2=查看，3=编辑， 4=发布
      return this.subAccountSkillAuths[this.skill.id] == 2 ? false : true
    },
  },
  created() {
    let self = this
    if (self.$router.match(location.pathname)) {
      self.routeName = self.$router.match(location.pathname).name
    }
    this.$store.dispatch('studioSkill/setSkill', this.$route.params.skillId)
    this.$store.dispatch('studioSkill/initHasSkillQuote', 0)
    this.$store.dispatch('studioSkill/initHasSkillQuote', 1)
    this.$store.dispatch('studioSkill/setRightTestOpen', false)
    this.$store.dispatch('aiuiApp/setLimitCount')
    this.subAccountInit()
    if (this.skill && this.skill.type == 8) {
      this.menus[0].menus.splice(2, 1)
    }
    self.init()
  },
  mounted() {
    let self = this
    if (window.userDetailInfo && window.userDetailInfo.subAccount) {
      window.logout = function () {
        let data = {
          subSessionId: self.$utils.getCookie('subSessionId'),
          sub_account_id: self.$utils.getCookie('sub_account_id'),
        }
        self.$utils.httpPost(self.$config.api.COOP_SUB_ACCOUNT_LOGOUT, data, {
          success: (res) => {
            if (res.flag) {
              self.$message.success('退出成功')
              self.$router.push({ name: 'sub-login' })
              localStorage.removeItem('firstEnterSkill', '1')
              localStorage.removeItem('firstEnterEntity', '1')
              localStorage.removeItem('firstEnterAuxiliary', '1')
            }
          },
          error: (err) => {},
        })
      }
    } else {
      window.logout = function () {
        SSO.logout(function () {
          self.$utils.clearCookie()
          self.$store.dispatch('user/setUserInfo', null)
          self.$message.success('已退出登录')
          setTimeout(function () {
            window.location.href = '/index-studio'
          }, 1000)
        })
      }
    }

    // 调用修饰语的接口，如果没有自定义修饰语，则不显示该菜单（对新用户隐藏）
    this.handleModifiersMenu()
  },
  watch: {
    $route: function (to, from) {
      let self = this
      self.routeName = to.name
    },
    limitCount: function (val) {
      window.limitCount = val
      if (window.header && !window.header.limitCount) {
        window.header.limitCount = val
      }
    },
    'skill.type': function (val) {
      let sub = this.menus[1].sub
      if (val == 3 && this.menus[0].key.indexOf('extend-') == -1) {
        this.menus[0].key = 'extend-' + this.menus[0].key
        sub[0].key = 'extend-' + sub[0].key
        sub[2].key = 'extend-' + sub[2].key
      }
      if (val == 8) {
        // qc 技能时，去除技能后处理
        let tmp = this.menus.filter((item) => item.value != '技能后处理')
        this.menus = tmp
      }
      // 非定制技能需要加上 引用的意图 菜单
      if (val && val != 3) {
        // 增加之前要判断之前是否已有菜单，有的话不用再新增
        const index = this.menus[1].sub.findIndex(
          (item) => item.value === '引用的意图'
        )
        if (index === -1) {
          this.menus[1].sub.push({
            key: this.subAccount
              ? 'sub-skill-refered-intentions'
              : 'skill-refered-intentions',
            value: '引用的意图',
            index: '1-5',
            disabled: false,
          })
        }
      }
      // TODO: 确认啥时展示 引用的标签 是否与上面 引用的意图菜单一直
      // 非定制技能需要加上 引用的意图 菜单
      // if (val && val != 3) {
      //   // 增加之前要判断之前是否已有菜单，有的话不用再新增
      //   const index = this.menus[1].sub.findIndex(
      //     (item) => item.value === '引用的交互标签'
      //   )
      //   if (index === -1) {
      //     this.menus[1].sub.push({
      //       key: this.subAccount
      //         ? 'sub-skill-refered-labels'
      //         : 'skill-refered-labels',
      //       value: '引用的交互标签',
      //       index: '1-6',
      //       disabled: false,
      //     })
      //   }
      // }
    },
    '$store.state.studioSkill.hasSkillEntity': function (val) {
      this.menus[1].sub[2].disabled = val ? false : true
    },
    '$store.state.studioSkill.hasSkillAuxiliary': function (val) {
      this.menus[1].sub[3].disabled = val ? false : true
    },
  },
  methods: {
    init() {
      let self = this
      let hasSkillEntity = self.$store.state.studioSkill.hasSkillEntity
      let hasSkillAuxiliary = self.$store.state.studioSkill.hasSkillAuxiliary
      self.menus[1].sub[2].disabled = hasSkillEntity ? false : true
      self.menus[1].sub[3].disabled = hasSkillAuxiliary ? false : true
    },
    openSelectSkill(event) {
      this.$store.dispatch('studioSkill/setSkillPopover', {
        show: true,
        rect: event.target.getBoundingClientRect(),
      })
    },
    addIntent() {
      this.$store.dispatch('studioSkill/openCreateIntentDialog')
    },
    selectMenu(menu) {
      let routeData = {}
      routeData.name = menu.key
      if (menu.params) {
        routeData.params = menu.params
      }
      this.$router.push(routeData)
    },

    //协同操作--start
    subAccountInit() {
      if (!this.subAccount) return
      if (localStorage.getItem('firstEnterSkill')) {
        this.cooperateDialog.show = true
      }
      this.accoutType()
      this.subAccountSKilInit()
    },
    accoutType() {
      this.menus = [
        {
          key: 'sub-skill-info',
          value: '基本信息',
          icon: 'ic-mn-basic-info',
          index: 0,
        },
        {
          key: 'skill-model',
          value: '交互模型',
          icon: 'ic-mn-interact',
          index: 1,
          sub: [
            {
              key: 'sub-skill-intentions',
              value: '意图',
              index: '1-1',
              disabled: false,
            },
            {
              key: 'sub-skill-modifiers',
              value: '自定义修饰语',
              index: '1-2',
              disabled: false,
            },
            {
              key: 'sub-skill-entities',
              value: '引用的实体',
              index: '1-3',
              disabled: false,
            },
            {
              key: 'sub-skill-auxiliaries',
              value: '引用的辅助词',
              index: '1-4',
              disabled: false,
            },
          ],
        },
        {
          key: 'sub-skill-post-process',
          value: '技能后处理',
          icon: 'ic-mn-code',
          index: 2,
        },
        {
          key: 'sub-skill-publish',
          value: '发布',
          icon: 'ic-mn-launch',
          index: 3,
        },
        // { key: 'sub-skill-optimize', value: '推荐优化', icon: 'ic-mn-optimize', index: 4}, // 2020-01-08 推荐优化暂时下线
        {
          key: 'sub-skill-version',
          value: '版本管理',
          icon: 'ic-mn-version',
          index: 5,
        },
      ]
    },
    subAccountSKilInit() {
      this.$store.dispatch('studioSkill/setSubAccountSkillAuths')
    },
    //协同操作--end

    handleModifiersMenu() {
      let self = this
      let data = {
        businessId: this.businessId,
        pageIndex: 1,
        pageSize: 10,
      }
      self.$utils.httpGet(self.$config.api.STUDIO_MODIFIER_GET_LIST, data, {
        success: (res) => {
          if (res.data.count > 0) {
            this.menus[1].sub[1].disabled = false
          } else {
            this.menus[1].sub[1].disabled = true
          }
        },
        error: (err) => {},
      })
    },
  },
  components: {
    OsHeader,
    AiuiHeader,
    RightTestClose,
    CooperateWarnDialog,
    feedBackHover,
    aiuiMenu,
    SelectSkillPopover,
  },
}
</script>

<style lang="scss">
.os-menu-add.skill-layout {
  position: absolute;
  right: 24px;
  top: 0;
  width: 50px;
  font-size: 14px !important;
  display: flex;
  color: $primary;
  i {
    font-size: 14px !important;
    color: $primary !important;
    flex: 1;
    margin: 0 !important;
  }
}
.skill-menu {
  .el-submenu__title {
    padding-left: 30px !important;
  }
  .skill-sub-menu.el-menu-item {
    padding-left: 78px !important;
    font-size: 14px;
  }
}

.studio-skill-layout-to-docs {
  display: block;
  margin: 24px;
  padding-top: 32px;
  border-top: 1px solid $grey2;
}
.ic-r-link.skill-left-bar {
  padding-left: 4px;
  vertical-align: -1px;
}
</style>
