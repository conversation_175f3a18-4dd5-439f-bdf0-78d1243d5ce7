<template>
  <div class="player-wrap">
    <span class="type-tip">影视泛搜索结果：</span>
    <ul class="type-wrapper">
      <li
        @click.stop="showJsonDetail(item)"
        v-for="item in resources"
        :key="item.name"
        class="active"
      >
        {{ item.name }}
      </li>
    </ul>
    <el-dialog
      class="debug-json-dialog"
      title="JSON"
      :visible.sync="showJson"
      width="50%"
      :append-to-body="true"
    >
      <div class="request-json">
        <template>
          <i class="ic-r-copy" title="复制代码" @click="copyJson(resJson)"></i>
          <json-view class="json-wrap" :data="resJson"></json-view>
        </template>
      </div>
      <div class="dialog-bottom"></div>
    </el-dialog>
  </div>
</template>
<script>
export default {
  props: {
    resources: Array,
  },
  data() {
    return {
      resJson: {},
      showJson: false,
    }
  },
  methods: {
    showJsonDetail(item) {
      this.resJson = JSON.parse(item.json || '{}')
      this.showJson = true
    },
  },
}
</script>
<style lang="scss" scoped>
.player-wrap {
  display: flex;
  align-items: center;
}
.type-tip {
  font-size: 12px;
  color: #7b839c;
  white-space: nowrap;
}
.type-wrapper {
  display: flex;
  // flex-wrap: wrap;
  margin-bottom: -10px;
  margin-right: -10px;
  font-size: 14px;
  // margin-top: 10px;
  li {
    cursor: pointer;
    height: 24px;
    line-height: 24px;
    padding: 0px 10px;
    background: linear-gradient(90deg, #e6eeff, #f9faff);
    border: 1px solid #b6c9ff;
    border-radius: 16px;
    margin-bottom: 10px;
    margin-right: 6px;
    white-space: nowrap;
    &:hover,
    &.active {
      color: $primary;
    }
  }
  // li + li {
  //   margin-left: 8px;
  // }
}

.json-wrap {
  min-height: 250px;
  max-height: 500px;
  padding: 25px;
  border-radius: 12px;
  margin-bottom: 20px;
  // border: 1px solid $grey4;
  overflow-x: hidden;
  overflow-y: auto;
  word-break: break-word;
  word-wrap: break-word;
  background-color: $grey1-30 !important;
}

.dialog-bottom {
  height: 20px;
}

.ic-r-copy {
  position: absolute;
  right: 24px;
  top: 24px;
  z-index: 8;
  font-size: 16px;
  color: $grey4;
  cursor: pointer;

  &::after {
    content: '';
    display: inline-block;
    position: absolute;
    right: -8px;
    top: -8px;
    z-index: -5;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: $white;
    box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.1);
  }
}
</style>
