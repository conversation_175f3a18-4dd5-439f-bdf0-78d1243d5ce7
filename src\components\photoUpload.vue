<template>
  <el-upload
    :class="options.className+'-uploader'"
    :action="options.action+id"
    :show-file-list="false"
    :on-success="handleAvatarSuccess"
    :on-error="handleAvatarError"
    :data="options.data"
    :with-credentials="true"
    :before-upload="beforeAvatarUpload">
    <img v-if="imageUrl" :src="imageUrl" :class="options.className">
    <i v-else class="el-icon-plus" :class="options.className+'-uploader-icon'"></i>
  </el-upload>
</template>

<script>
import Axios from 'axios'
import crypto from 'crypto'

export default {
  name: 'OsPhotoUpload',
  props: {
    options: {
      type: Object,
      default: {}
    },
    id: {
      default:''
    },
    imageUrl: {
      default: ''
    }
  },
  data () {
    return {
      action: ''
    }
  },
  computed: {

  },
  mounted() {

  },
  methods: {
    handleAvatarSuccess(res, file) {
      this.$emit('uploadSuccess', res)
    },
    handleAvatarError(err, file, fileList) {
      this.$message.error('上传次数超出每日上限')
    },
    beforeAvatarUpload(file) {

      const sizeLt = file.size < (this.options.sizeLimit || 1024*1024*2);
       // / 1024 / 1024 < 2;
      if (!sizeLt) {
        this.$message.error(this.options.sizeLimitTips || '请上传不超过2MB的图片');
      }
      const isImage = file.type === 'image/jpeg' || file.type === 'image/png';
      if (!isImage) {
        this.$message.error('请上传JPG或PNG格式的图片');
      }

      return sizeLt && isImage;
    },
    photoCompress(file, w, objDiv) {
      let self = this
      const ready = new FileReader();
      /* 开始读取指定的Blob对象或File对象中的内容. 当读取操作完成时,readyState属性的值会成为
      DONE,如果设置了onloadend事件处理程序,则调用之.同时,result属性中将包含一个data: URL格式
      的字符串以表示所读取文件的内容. */
      ready.readAsDataURL(file);
      ready.onload = function () {
        const re = this.result;
        self.canvasDataURL(re, w, objDiv);
      };
    },
    canvasDataURL(path, obj, callback) {
      const img = new Image();
      img.src = path;
      img.onload = function () {
        const that = this;
        // 默认按比例压缩
        let w = that.width;
        let h = that.height;
        const scale = w / h;
        w = obj.width || w;
        h = obj.height || (w / scale);
        let quality = 0.7; // 默认图片质量为0.7
        // 生成canvas
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        // 创建属性节点
        const anw = document.createAttribute('width');
        anw.nodeValue = w;
        const anh = document.createAttribute('height');
        anh.nodeValue = h;
        canvas.setAttributeNode(anw);
        canvas.setAttributeNode(anh);
        ctx.drawImage(that, 0, 0, w, h);
        // 图像质量
        if (obj.quality && obj.quality <= 1 && obj.quality > 0) {
          ({ quality } = obj);
        }
        // quality值越小，所绘制出的图像越模糊
        const base64 = canvas.toDataURL('image/png', quality);
        // 回调函数返回base64的值
        callback(base64);
      };
    },
    convertBase64UrlToBlob(base64Data) {
      var byteString;
      if (base64Data.split(',')[0].indexOf('base64') >= 0) {
        byteString = atob(base64Data.split(',')[1]);
      } else {
        byteString = unescape(base64Data.split(',')[1]);
      }
      var mimeString = base64Data.split(',')[0].split(':')[1].split(';')[0];
      var ia = new Uint8Array(byteString.length);
      for (var i = 0; i < byteString.length; i++) {
        ia[i] = byteString.charCodeAt(i);
      }
      return new Blob([ia], {type:mimeString});
    },
    upload(param) {
      let self = this
      let md5 = crypto.createHash('md5')
      let md5string = param.file.name + param.file.lastModified + param.file.size
      let tail = ''
      let fileType = param.file.type

      md5.update(md5string)
      if(fileType === 'image/jpeg') {
        tail = '.jpg'
      } else if(fileType === 'image/png') {
        tail = '.png'
      }
      // ? 这里是 thumb/'... ？？？
      let fileName = `${param.file.name.split('.')[0]}.${md5.digest('hex')}${tail}`
      console.log(fileName);
      console.log(param.file);
      console.log(this.options.action);
      console.log(this.options.defaultData);
      console.log(this.options.defaultKey);

    },
    uploadProgress(res, key) {

    },
    uploadFail(res, key) {

    }
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="scss" scoped>

</style>
