<template>
  <div
    class="section-what wow fadeInUp"
    style="position: relative"
    v-lazy:background="require(`@A/images/home/<USER>/banner.png`)"
  >
    <div class="section-wrap">
      <div class="section-title">
        <span class="title">什么是AIUI</span
        ><span class="desc">以大模型为核心的人机交互平台</span>
      </div>
      <div class="section-data">
        <div class="video-section">
          <div
            :class="[
              'vdo-area',
              item.bg_klass,
              { 'vdo-area-active': videoActive === index },
              { 'vdo-area-old': videoOld === index },
              {
                'vdo-area-default': videoOld !== index && videoActive !== index,
              },
            ]"
            v-lazy:background-image="
              require(`@A/images/home/<USER>/${item.bg_image}.png`)
            "
            :key="index"
            v-for="(item, index) in videos"
            @click="onVideoClick(index, 'video')"
          >
            <div
              class="vdo-btn"
              v-lazy:background-image="require('@A/images/home/<USER>/play.png')"
            >
              <i class="fas fa-play"></i>
            </div>
          </div>

          <div
            class="vdo-area vdo-area-active"
            style="position: absolute"
            v-if="isPlaying"
          >
            <video
              :src="videoSrc"
              autoplay
              preload
              controls
              style="width: 100%; height: 100%"
            ></video>
          </div>
        </div>

        <div class="info-section">
          <div
            class="info-section-box"
            v-for="(item, index) in info"
            :key="index"
            @click="onVideoClick(index, 'info')"
          >
            <div :class="['title', { 'title-active': videoActive === index }]">
              <img
                v-lazy="require(`@A/images/home/<USER>/${item.icon}.png`)"
                alt=""
              />
              {{ item.title }}
            </div>
            <div class="details" v-if="videoActive === index">
              <div v-for="(it, i) in item.details" :key="i">{{ it }}</div>
              <img
                class="img"
                v-lazy="require(`@A/images/home/<USER>/${item.img}.png`)"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import videoPlayer from '@C/videoPlayer/index'
function getWindowHeight() {
  return 'innerHeight' in window
    ? window.innerHeight
    : document.documentElement.offsetHeight
}
export default {
  name: 'section-what',
  data() {
    return {
      videoActive: 0,
      videoOld: 2,
      videos: [
        {
          url: 'https://aiui-res.cn-bj.ufileos.com/AIUIVideo.mp4',
          bg_klass: 'bg-old',
          bg_image: 'card1',
        },
        {
          url: 'https://aiui-file.xfyun.cn/vedio/homeVedio.mp4',
          bg_klass: 'bg-new',
          bg_image: 'card2',
        },
        {
          url: 'https://aiui-res.cn-bj.ufileos.com/video/video2.mp4',
          bg_klass: 'bg-new',
          bg_image: 'card3',
        },
        {
          url: 'https://aiui-res.cn-bj.ufileos.com/video/video3.mp4',
          bg_klass: 'bg-new1',
          bg_image: 'card4',
        },
      ],
      videoSrc: '',
      isPlaying: false,
      info: [
        {
          title: 'AIUI开放平台介绍',
          icon: 'icon1',
          img: 'details1',
          details: [
            '讯飞星火大模型重塑语音交互链路',
            '支持业务自由定制',
            '硬件模组快速接入',
          ],
        },
        {
          title: '大模型极速超拟人交互',
          icon: 'icon2',
          img: 'details2',
          details: [
            '秒级响应，超低延时',
            '全双工无惧干扰与打断',
            '自定义角色共情聊天',
            '超拟人发音，支持声音复刻',
          ],
        },
        {
          title: '多模态交互模组',
          icon: 'icon3',
          img: 'details3',
          details: [
            '实现公共高噪场景下精准的语音识别',
            '支持驱动本地虚拟人',
            '一对一技术支持',
          ],
        },
        {
          title: '语音交互模组',
          icon: 'icon4',
          img: 'details4',
          details: ['快速验证项目可行性', '一对一技术支持'],
        },
      ],
      infoActive: 0,
    }
  },
  methods: {
    onVideoClick(index, source = 'video') {
      if (source === 'info') {
        // 右侧卡片点击，直接激活
        this.videoActive = index
        this.infoActive = index
        this.isPlaying = false
        this.toggleVideoActive(index)
        return
      }
      // 判断点击的是 default 还是 old 区块
      const isDefault = this.videoOld !== index && this.videoActive !== index
      const isOld = this.videoOld === index
      if (isDefault) {
        // 向下切换
        this.videoActive = (this.videoActive + 1) % this.info.length
        this.infoActive = this.videoActive
        this.isPlaying = false
        this.toggleVideoActive(this.videoActive)
      } else if (isOld) {
        // 向上切换
        this.videoActive =
          (this.videoActive - 1 + this.info.length) % this.info.length
        this.infoActive = this.videoActive
        this.isPlaying = false
        this.toggleVideoActive(this.videoActive)
      } else {
        // 仍然保留原有点击当前激活区块时的播放逻辑
        if (index === this.videoActive) {
          this.videoSrc = this.videos[index].url
          this.isPlaying = true
        } else {
          this.isPlaying = false
          this.toggleVideoActive(index)
        }
      }
    },
    playVideo(index) {
      let height = Math.min(getWindowHeight() * 0.9, 562)
      let width = parseInt((1920 * height) / 1080)
      videoPlayer({
        width,
        height,
        videoSrc: this.videos[index].url,
        videoStyle: {
          width: `${width}px`,
          height: `${height}px`,
          'box-sizing': 'border-box',
          'margin-left': `-${width * 0.5}px`,
          'margin-top': `-${height * 0.5}px`,
        },
      })
    },
    toggleVideoActive(val) {
      this.videoActive = val
      if (val == 0) {
        this.videoOld = 2
      } else if (val == 1) {
        this.videoOld = 0
      } else {
        this.videoOld = 1
      }
    },

    onInfoClick(val) {
      this.infoActive = val
    },
  },
}
</script>
<style lang="scss" scoped>
@import './common.scss';

.section-what {
  width: auto;
  height: 680px;
  margin-top: 1px;
  background: #fff center/100% 100% no-repeat;

  .section-wrap {
    width: 1200px;
    margin: 0 auto;
    padding: 64px 0 74px 0;

    .section-title {
      display: flex;
      align-items: center;

      .title {
        font-size: 30px;
        color: #000000;
        font-weight: 500;
        font-family: PingFangSC, PingFangSC-Medium;
      }

      .desc {
        font-size: 16px;
        font-family: PingFangSC, PingFangSC-Regular;
        font-weight: 400;
        margin-left: 24px;
        color: #676a99;
      }
    }

    .section-data {
      margin-top: 74px;
      margin-left: 32px;
      display: flex;
    }
  }
}

.video-section {
  position: relative;
  text-align: center;
  width: 692px;
  height: 389px;
  background-position: center;
  background-size: 100%;
  background-repeat: no-repeat;

  .vdo-area {
    width: 644px;
    height: 341px;
    background: #fff;
    cursor: pointer;
    position: absolute;
    z-index: 2;
    left: 24px;
    top: 72px;
    transition: all 0.5s ease-out;
    transition-property: width, height, left, top, transform;
  }

  .vdo-btn {
    opacity: 0.8;
    display: none;
  }

  .vdo-area-active {
    width: 692px;
    height: 389px;
    transition: all 0.5s ease-in;
    transition-property: width, height, left, top, transform;
    background: #fff;
    z-index: 3;
    left: 0;
    top: 0;
    position: relative;

    .vdo-btn {
      position: absolute;
      z-index: 3;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      display: block;
      width: 100px;
      height: 100px;
      line-height: 100px;
      background-position: center;
      background-size: 100%;
      background-repeat: no-repeat;
      background: center/100% no-repeat;
      border-radius: 50%;
      color: #fff;
      font-size: 30px;
      z-index: 2;

      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(-45deg, #4486ff, #4c42c5);
        border-radius: 50%;
        opacity: 0;
        transition: 0.4s;
        z-index: -1;
      }

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border-radius: 50%;
      }
    }
  }

  .vdo-area-default {
    background-image: url('../../../assets/images/home/<USER>/old1.png') !important;
  }

  .vdo-area-old {
    width: 596px;
    height: 293px;
    left: 48px;
    top: 144px;
    z-index: 1;
    background-image: url('../../../assets/images/home/<USER>/old2.png') !important;
  }

  .bg-old {
    background-position: center;
    background-size: 100%;
    background-repeat: no-repeat;
  }

  .bg-new {
    background-position: center;
    background-size: 100%;
    background-repeat: no-repeat;
  }

  .bg-new1 {
    background-position: center;
    background-size: 100%;
    background-repeat: no-repeat;
  }
}

.info-section {
  width: 400px;
  // height: 434px;
  margin-left: 44px;
  box-shadow: 0px 6px 24px 0px rgba(62, 117, 251, 0.2);

  .info-section-box {
    transition: 0.5s cubic-bezier(0.05, 0.61, 0.41, 0.95);

    .title {
      width: 100%;
      // height: 72px;
      background: linear-gradient(180deg, #d5edff, #effeff);
      border: 1px solid #ffffff;
      box-shadow: 0px 4px 12px 0px rgba(62, 117, 251, 0.2);
      padding: 8px 27px;
      font-size: 18px;
      font-family: PingFangSC, PingFangSC-Medium;
      font-weight: 500;
      display: flex;
      align-items: center;
      cursor: pointer;
      &:hover {
        color: $primary;
      }

      img {
        width: 42px;
        height: 42px;
        margin-right: 24px;
      }
    }

    .title-active {
      background: linear-gradient(270deg, #235ae8, #00d7ff 89%, #09ffd5);
      border: 1px solid #ffffff;
      box-shadow: 0px 4px 12px 0px rgba(62, 117, 251, 0.2);
      color: #ffffff !important;
    }

    .details {
      width: 100%;
      height: 200px;
      background: #ffffff;
      box-shadow: 0px 6px 24px 0px rgba(62, 117, 251, 0.2);
      padding: 20px 32px;
      position: relative;
      animation: fadeInBottom 0.5s ease 0.1s both;

      .img {
        width: 147px;
        height: 140px;
        position: absolute;
        right: 0;
        bottom: 0;
      }

      div {
        margin-bottom: 24px;
        font-size: 16px;
        font-family: PingFangSC, PingFangSC-Regular;
        font-weight: 400;
        text-align: justify;
        color: #262b4f;
        line-height: 22px;

        &::before {
          display: inline-block;
          content: '';
          width: 10px;
          height: 10px;
          background: $primary;
          border-radius: 50%;
          margin-right: 8px;
        }
      }
    }
  }
}

// .fade-enter-active {
//   transition: all .5s ease;
// }

// .fade-leave-active {
//   transition: all .5s cubic-bezier(1.0, 0.5, 0.8, 1.0);
// }

.fade-enter,
.fade-leave-active {
  transform: translateX(10px);
  opacity: 0;
}
</style>
