<template>
  <div>
    <p class="mgb24 section-sub-title">创建技能
      <el-checkbox style="margin-left: 25px;" v-model="createSkill"
        :disabled="saving">创建新技能权限</el-checkbox>
      <el-input size="medium" style="margin-left: 114px; width: 330px;"
        placeholder="根据技能名称/英文标识搜索" v-model="searchVal"  @keyup.enter.native="searchSkill">
        <i slot="suffix" class="el-input__icon el-icon-search search-area-btn"
          @click="searchSkill" />
      </el-input> 
    </p>
    <p class="section-sub-title">已有技能</p>
    <div class="btn-wrap">
        <el-select v-model="flag" class="mgr8"
          placeholder="请选择" size="medium" style="width: 112px;"
          :disabled="saving">
          <el-option
            v-for="item in filterTypes"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
        <el-button size="medium"
          @click="batchOptionType = 2" :disabled="saving">设为查看</el-button>
        <el-button size="medium"
          @click="batchOptionType = 3" :disabled="saving">设为编辑</el-button>
        <el-button size="medium"
          @click="batchOptionType = 4" :disabled="saving">设为发布</el-button>
        <el-button size="medium"
          :disabled="flag === 2 || saving"
          @click="batchOptionType = 0">关闭权限</el-button>
      </div>
      <os-table
        :tableData="tableData"
        size="small"
        style="min-height: 200px;"
        @change="changePages">
        <el-table-column
          width="114" label="权限开关">
          <template slot-scope="scope">
            <el-switch v-model="scope.row.checked" @change="setSelection(scope.row)"></el-switch>
          </template>
        </el-table-column>
        <el-table-column
          width="240"
          label="技能">
          <template slot-scope="scope">
            <div class="text-blod" :title="scope.row.zhName || '-'">{{scope.row.zhName }}</div>
          </template>
        </el-table-column>
        <el-table-column
          width="300"
          label="权限">
          <template slot-scope="scope">
            <el-radio-group v-model="scope.row.subResourceId">
              <el-radio :label="2" :disabled="!scope.row.checked">查看</el-radio>
              <el-radio :label="3" :disabled="!scope.row.checked">编辑</el-radio>
              <el-radio :label="4" :disabled="!scope.row.checked">发布</el-radio>
            </el-radio-group>
          </template>
        </el-table-column>
      </os-table>
  </div>
</template>

<script>
  export default {
    name: 'skill-table',
    props: {
      account: {
        type: Object,
        default: {}
      },
      saving: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        flag: 0,
        filterTypes: [
          {
            value: 0,
            label: '全部'
          },{
            value: 1,
            label: '有权限'
          },{
            value: 2,
            label: '无权限'
          }
        ],
        batchOptionType: null,
        searchVal: '',
        createSkill: false,
        initCreateSkill: false,
        tableData: {
          loading: true,
          total: 0,
          page: 1,
          size: 10,
          list: []
        },
        skillIdsToSave: [],
        skillsToSave: [],
        pagesList: {}, //每页的数据
        initSkillIds: [], //每页有权限的skillIds，用于计算delete的技能
        initAuthSkills: [] //每页有权限的skill，用于判断数据有没有改动
      }
    },
    watch: {
      'account.subUid': function(val) {
        this.getAuthority(1)
      },
      flag(val) {
        console.log(this.flag)
        this.init()
        this.getAuthority(1)
      },
      batchOptionType(val, oldVal) {
        let self = this
        if(val !== oldVal) {
          let keys = Object.keys(self.pagesList)
          keys.forEach(key => {
            self.pagesList[key].forEach( item => {
              self.$set(item, 'subResourceId', val)
              item.checked = val > 0 ? true : false
              self.$forceUpdate()
            })
          })
        }
      },
      saving(val) {
        this.tableData.loading = val
      }
    },
    created() {
      this.flag = 0
      this.init()
      this.getAuthority(1)
    },
    methods: {
      init(){
        this.batchOptionType = null
        this.tableData.total = 0
        this.tableData.list = []
        this.pagesList = {}
        this.initSkillIds = []
        this.initAuthSkills = []
        this.skillIdsToSave = []
        this.skillsToSave = []
      },
      changePages(page) {
        let self = this
        if(self.pagesList.hasOwnProperty(page)){
          self.tableData.list = self.pagesList[page]
        } else {
          self.getAuthority(page)
        }
      },
      searchSkill () {
        this.getAuthority(1)
      },
      getAuthority(page){
        let self = this
        let data = {
          subUid: self.account.subUid,
          pageIndex: page || self.tableData.page,
          pageSize: self.tableData.size,
          search: self.searchVal,
          flag: self.flag,
          ability: 1
        }
        if (self.searchVal != '') {
          this.pagesList = {}
          this.initSkillIds = []
          this.initAuthSkills = []
        }
        self.tableData.loading = true
        this.$utils.httpGet(this.$config.api.COOP_GET_AUTHORITY, data, {
          success: (res) => {
            self.tableData.loading = false
            self.createSkill = res.data.create
            self.initCreateSkill = res.data.create
            self.tableData.total = res.data.edit.count
            self.tableData.page = res.data.edit.pageIndex
            self.tableData.size = res.data.edit.pageSize
            if(res.data && res.data.edit) {
              if(!res.data.edit.count) res.data.edit.count = 0
              if(!res.data.edit.skills) res.data.edit.skills = []
              let tmp = []
              res.data.edit.skills.forEach(item => {
                //已开权限的标志：有subResourceId属性
                if(!self.batchOptionType && self.batchOptionType !== 0 ) {
                  if(item.hasOwnProperty('subResourceId') && item.subResourceId) {
                    item.checked = true
                    self.initSkillIds.push(item.id)
                    tmp.push({
                      id: item.id,
                      subResourceId: item.subResourceId
                    })
                  } else {
                    if(self.flag === 2) {
                      self.initSkillIds.push(item.id)
                    }
                    item.checked = false
                  }
                } else {
                  item.subResourceId = self.batchOptionType
                  item.checked = self.batchOptionType ? true : false
                }
              })
              self.pagesList[page] = res.data.edit.skills
              self.tableData.list = res.data.edit.skills
              self.initAuthSkills.push(...JSON.parse(JSON.stringify(tmp)))
            }
          },
          error: (err) => {
            self.tableData.loading = false
          }
        })
      },
      beforeSaveSkills(){
        let self = this
        let keys = Object.keys(self.pagesList)
        keys.forEach(key => {
          self.pagesList[key].forEach( item => {
            if(item.checked && item.subResourceId){
              self.skillsToSave.push({
                id: item.id,
                subResourceId: item.subResourceId
              })
              self.skillIdsToSave.push(item.id)
            }
          })
        })
      },
      async saveSKill(){
        let self = this
        if(self.batchOptionType >= 0) {
          await self.saveBatchOption()
        }
        self.beforeSaveSkills()
        if(JSON.stringify(self.skillsToSave) === JSON.stringify(self.initAuthSkills) && self.initCreateSkill === self.createSkill){
          return Promise.resolve('noChange')
        }
        let tmp = {
          create: this.createSkill,
          edit: self.skillsToSave,
          'delete': this.$utils.arraysDiff(this.initSkillIds, this.skillIdsToSave)
        }
        let data = {
          subUid: self.account.subUid,
          authority: JSON.stringify(tmp)
        }
        return new Promise((resolve, reject) => {
          self.$utils.httpPost(this.$config.api.COOP_SAVE_AUTHORITY, data, {
            success: (res) => {
              resolve('saveSKill success')
            },
            error: (err) => {
              reject('saveSKill')
            }
          })
        })
      },
      setSelection(val) {
        let self = this
        if(!val.hasOwnProperty('oldSubResourceId')) {
          self.$set(val, 'oldSubResourceId', '')
        }
        if(val.checked) {
          if(!val.hasOwnProperty('subResourceId') || val.subResourceId === 0) {
            self.$set(val, 'subResourceId', 2)
            val.oldSubResourceId = ''
          } else {
            val.subResourceId = val.oldSubResourceId
            val.oldSubResourceId = ''
          }
        } else {
          val.oldSubResourceId = val.subResourceId
          val.subResourceId = ''
        }
      },
      async saveBatchOption() {
        let self = this
        if(typeof self.batchOptionType !== 'number') {
          return Promise.resolve('noChange')
        }
        let data = {
          subUid: self.account.subUid,
          flag: self.flag,
          type: self.batchOptionType,
          ability: 1,
          search: self.searchVal
        }
        return new Promise((resolve, reject) => {
          self.$utils.httpPost(this.$config.api.COOP_BATCH_SAVE_AUTHORITY, data, {
            success: (res) => {
              resolve('saveSKill success')
            },
            error: (err) => {
              reject('saveSKill')
            }
          })
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
@import "./authTable.scss";
</style>