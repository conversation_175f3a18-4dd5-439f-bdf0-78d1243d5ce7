<template>
  <div>
    <el-table border :data="sectionData" class="localism-buy-table">
      <el-table-column
        align="center"
        label="方言"
        prop="description"
      ></el-table-column>
      <el-table-column align="center" label="价格">
        <template slot-scope="scope">2万元/年</template>
      </el-table-column>
      <el-table-column label="购买" align="center">
        <template slot-scope="scope">
          <el-button
            type="text"
            @click="buyControl(scope.row, wareData['方言'])"
            >立即购买</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <add-to-app :dialog="add2AppDialog"></add-to-app>
  </div>
</template>

<script>
import AddToApp from '../pages/aiui/buy-service/addToApp.vue'

export default {
  name: 'localismBuy',
  components: {
    AddToApp,
  },
  data() {
    return {
      add2AppDialog: {
        show: false,
      },
      wareData: {},
      sectionData: [],
    }
  },
  created() {
    this.getWare()
    // 获取方言列表
    this.$utils.httpGet(
      this.$config.api.GET_ACCENT,
      {},
      {
        success: (res) => {
          this.sectionData = res.data.accents
        },
        error: (err) => {},
      }
    )
  },
  methods: {
    getWare() {
      let thiz = this
      this.$utils.httpGet(
        this.$config.api.GET_APP_WARE,
        {},
        {
          success: (res) => {
            if (res.flag) {
              thiz.wareData = res.data
            }
            /*self.apps = Array.prototype.map.call(res.data, function (item, index) {
              item.selected = item.isUsed
              return item
            })*/
          },
          error: (err) => {},
        }
      )
    },
    buyControl(row, type) {
      if (type == 1701) {
        this.add2AppDialog = {
          show: true,
          wareId: type,
          packageId: type + row.id,
        }
      } else if (type == 7002 || type == 1202) {
        this.add2AppDialog = {
          show: true,
          wareId: type,
          packageId: row.packageId,
        }
      }
    },
  },
}
</script>

<style lang="scss">
.localism-buy-table {
  th {
    .cell {
      font-weight: bold;
      color: #677183;
    }
  }
}
</style>
