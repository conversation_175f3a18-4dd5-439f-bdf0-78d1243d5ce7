<template>
  <div class="main-content">
    <MyHeader> </MyHeader>

    <section class="main-content-banner">
      <div class="banner-text">
        <h2>免唤醒语音交互解决方案</h2>

        <p class="banner-text-content">
          无需唤醒设备即可语音点播内容、控制设备交互，
          为产品打造自然流畅的语音交互体验。
        </p>

        <div class="hor-btn">
          <el-button
            class="banner-text-button"
            @click="toConsole"
            type="primary"
            round
            >合作咨询</el-button
          >
        </div>
      </div>
    </section>

    <section class="section-nav">
      <ul class="nav-ul" id="nav-ul">
        <li
          v-for="item in nav_list"
          :key="item.id"
          @click="gotoSelection(item.id)"
        >
          {{ item.name }}
        </li>
      </ul>

      <i
        v-if="nav_list.length > 4"
        :class="[
          'nav-btn',
          nav_flag ? 'el-icon-caret-bottom' : 'el-icon-caret-top',
        ]"
        id="nav-btn"
        @click="handleNavExpand"
      ></i>
    </section>

    <section class="section section1">
      <h2>适用产品</h2>

      <div class="section-item">
        <ul>
          <li v-for="(item, index) in app_scenario" :key="index" class="app">
            <img :src="item.src" :alt="item.alt" />
            <p class="app-text">{{ item.alt }}</p>
          </li>
        </ul>
      </div>
    </section>

    <section class="section section2">
      <h2>方案亮点</h2>

      <ul>
        <li v-for="item in pics" :key="item.index">
          <h3>{{ item.title }}</h3>
          <p>{{ item.sub_title }}</p>

          <img :src="item.src" alt="" v-if="item.visible" />

          <i
            class="el-icon-arrow-up my-up-btn"
            v-if="item.visible"
            @click="item.visible = !item.visible"
          ></i>
          <i
            class="el-icon-arrow-down my-down-btn"
            v-else
            @click="item.visible = !item.visible"
          ></i>
        </li>
      </ul>
    </section>

    <section class="section section3">
      <h2>接入方式</h2>

      <ul class="join-way">
        <li v-for="(item, index) in access" :key="index">
          <p class="title">{{ item.title }}</p>
          <p class="sub-title">{{ item.title2 }}</p>

          <img class="section-4-bg" :src="item.img" :alt="item.title" />
        </li>
      </ul>
    </section>

    <section class="section section4">
      <h2>合作咨询</h2>

      <h3>免唤醒语音交互方案 帮你打造自然的语音交互产品</h3>

      <div class="cooperation-btn" @click="toConsole">申请合作</div>
    </section>

    <section class="section section-footer">
      <div class="footer-title">
        联系我们
        <span class="arrow" @click="clickFooterArrow">▼</span>
      </div>
      <div class="content">
        <p>联系电话：19955108393</p>
        <p>开发者交流群：617903641</p>
        <p>商务技术支持：<EMAIL></p>
      </div>

      <div class="footer-title">
        友情链接
        <span class="arrow" @click="clickFooterArrow">▼</span>
      </div>
      <div class="content">
        <p>科大讯飞</p>
        <p>讯飞开放平台</p>
      </div>

      <div class="corporation-info">
        ©科大讯飞股份有限公司 皖ICP备05001217号
      </div>
    </section>
  </div>
</template>

<script>
import MyHeader from '@P/aiui/solution-aiui/mobile-header.vue'
export default {
  name: 'AiuiWebIndex',

  components: {
    MyHeader,
  },

  data() {
    return {
      nav_flag: true,

      expandFlag: true,
      nav_list: [
        { name: '适用产品', id: 1 },
        { name: '方案亮点', id: 2 },
        { name: '接入方式', id: 3 },
      ],

      app_scenario: [
        {
          alt: '家用机器人',
          src: require('../../../../../assets/images/solution/wakeup/img_domestic_robot.png'),
        },
        {
          alt: '投影仪',
          src: require('../../../../../assets/images/solution/wakeup/img_projector.png'),
        },
        {
          alt: '护眼仪',
          src: require('../../../../../assets/images/solution/wakeup/img_instrument_shield_eye.png'),
        },
        {
          alt: '按摩椅',
          src: require('../../../../../assets/images/solution/wakeup/img_massage_armchair.png'),
        },
      ],

      pics: [
        {
          title: '离线+离在线结合',
          sub_title:
            '可以纯离线，没有网络也可以使用；300ms响应，打造产品操控体验',
          visible: true,
          src: require('../../../../../assets/images/solution/wakeup/img_offline_combination.png'),
        },
        {
          title: '说法支持自由定制',
          sub_title: '根据产品需要自定义说法；满足产品在不同情境下的个性化需求',
          visible: false,
          src: require('../../../../../assets/images/solution/wakeup/img_free_custom.png'),
        },
        {
          title: '设备控制直接说',
          sub_title:
            '开机关机、屏幕亮度、声音大小；无需唤醒设备，直接语音交互；支持无网使用',
          visible: false,
          src: require('../../../../../assets/images/solution/wakeup/img_equipment_control.png'),
        },
        {
          title: '播放控制直接说',
          sub_title:
            '可以直接语音控制播放器，支持无网使用；如：快进快退、跳过片头、换一首、上一首、下一首',
          visible: false,
          src: require('../../../../../assets/images/solution/wakeup/img_play_control.png'),
        },

        {
          title: '界面交互直接说',
          sub_title: '页面导航所见即可直接说；语音模拟点击；跳转至任意层级页面',
          visible: false,
          src: require('../../../../../assets/images/solution/wakeup/img_interface_interaction.png'),
        },

        {
          title: '内容点播直接说',
          sub_title: '电影动画、音乐儿歌、戏曲小说；无需唤醒，可以直接语音点播',
          visible: false,
          src: require('../../../../../assets/images/solution/wakeup/img_content_demand.png'),
        },
      ],

      access: [
        {
          title: '纯软接入、无需改造硬件',
          title2: '提供SDK,可以直接端集成；说法量身定制，端上适配即可使用',
          img: require('@A/images/solution/wakeup/img_pure_soft.png'),
        },
        {
          title: '麦克风阵列+声学算法',
          title2:
            '针对没有录音装置的设备；提供2/4/6麦USB语音模组和CAE算法；帮你解决硬件收音问题',
          img: require('@A/images/solution/wakeup/img_microphone_array.png'),
        },
      ],
    }
  },

  mounted() {},

  methods: {
    gotoSelection(id) {
      const section = document.getElementsByClassName(`section` + id)[0]
      if (section) {
        section.scrollIntoView({ behavior: 'smooth' })
      }
    },

    handleNavExpand() {
      const nav_ul = document.getElementById('nav-ul')
      const navIcon = document.getElementById('nav-btn')

      navIcon.addEventListener('click', () => {
        if (this.nav_flag) {
          nav_ul.classList.add('expanded')
        } else {
          nav_ul.classList.remove('expanded')
        }
        this.nav_flag = !this.nav_flag
      })
    },

    clickFooterArrow() {
      const titles = document.querySelectorAll('.footer-title')
      titles.forEach((title) => {
        title.addEventListener('click', () => {
          const content = title.nextElementSibling
          const arrow = title.querySelector('.arrow')

          if (content.style.display === 'none' || !content.style.display) {
            content.style.display = 'block'
            arrow.classList.add('up')
          } else {
            content.style.display = 'none'
            arrow.classList.remove('up')
          }
        })
      })
    },

    toConsole() {
      const search = window.location.search
      if (search) {
        window.open(`/solution/apply/6${search}`)
      } else {
        window.open('/solution/apply/6')
      }
    },

    gotoSDK() {
      window.open('https://aiui-doc.xf-yun.com/project-1/doc-418/')
    },

    gotoClick() {
      window.open('https://www.aifuwus.com/onstage/cmddetail?id=4434')
    },
  },
}
</script>

<style lang="scss" scoped>
.main-content {
  background-color: #f6f7f9;
  width: 750px;
  overflow: hidden;
  &-banner {
    background: url(~@A/images/solution/smart-hardware/mobile/banner2.jpg)
      center no-repeat;
    background-size: cover;
    height: 375px;
    overflow: hidden;
    width: 100%;
  }

  .banner-text {
    margin-top: 64px;
    padding-left: 44px;
    h2 {
      font-size: 48px;
      font-weight: 600;
      color: #000000;
      line-height: 68px;
    }
    p {
      width: 648px;
      font-size: 24px;
      font-weight: 300;
      color: #000000;
      line-height: 40px;
      text-align: left;
    }
    .hor-btn {
      display: flex;
      flex-direction: row;
      justify-content: flex-start;
      margin-top: 40px;
      div:nth-child(2) {
        margin-left: 30px;
      }
    }
  }

  .section {
    h2 {
      font-size: 48px;
      font-weight: 600;
      color: #000000;
      line-height: 68px;
      text-align: center;
      margin: 0 auto;
      margin-top: 50px;
      margin-bottom: 30px;
    }
    p,
    ul {
      margin-bottom: 0;
    }
    max-width: 750px;
    overflow: hidden;
    margin: 0 auto;
    .section-title {
      text-align: center;
      font-size: 36px;
      font-weight: 500;
      color: #444444;
      line-height: 54px;
      .arrow {
        width: 26px;
        height: 24px;
        display: inline-block;
        background-repeat: no-repeat;
        background-size: contain;
      }
      .arrow-left {
        background-position: left;
        background-image: url(~@A/images/solution/smart-hardware/arrow-left.png);
      }
      .arrow-right {
        background-position: right;
        background-image: url(~@A/images/solution/smart-hardware/arrow-right.png);
      }
    }
    .section-tabs {
      margin-top: 60px;
    }
  }

  .section-nav {
    background-color: #ffffff;
    position: relative;
    width: 100%;
    border: 1px solid #ddd;
    padding: 0 30px;
    .nav-ul {
      display: flex;
      justify-content: space-evenly;
      flex-wrap: wrap;
      overflow: hidden; /* 默认隐藏超出部分 */
      max-height: 68px; /* 折叠状态时，只显示一行 */
      transition: max-height 0.3s ease; /* 动画过渡效果 */
    }
    .nav-ul.expanded {
      max-height: 200px; /* 展开状态：允许显示多行 */
    }
    .nav-ul li {
      flex: 0 0 auto;
      margin-right: 30px;
      white-space: nowrap;
      cursor: pointer;
      font-size: 24px;
      font-weight: 400;
      text-align: center;
      line-height: 68px;
      color: #7a7a7a;
    }
    .nav-btn {
      position: absolute;
      height: 68px;
      width: 60px;
      right: 0px;
      top: 60%;
      transform: translateY(-50%);
      font-size: 48px;
      // background-color: red;
    }
  }

  .section1 {
    padding: 0 40px;
    .section-item {
      margin-top: 50px !important;

      > ul {
        display: flex;
        justify-content: flex-start;
        flex-wrap: wrap;
        gap: 30px;

        li {
          // flex: 0 0 calc(33.33%);
          width: 192px;
          height: 240px;
          position: relative;
          background-size: cover;

          border-radius: 16px;
          margin-bottom: 60px;

          img {
            width: 100%;
            height: 100%;
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
          }

          img:nth-child(3) {
            position: absolute;
            top: 20px;
            right: 30px;
          }

          p {
            height: 38px;
            width: 100%;
            text-align: center;
            margin: 0 auto;
            font-size: 20px;
            line-height: 38px;
            position: absolute;
            left: 50%;
            bottom: -70px;
            transform: translate(-50%, 0%);
            margin-bottom: 20px;
          }
        }
      }
    }
  }

  .section2 {
    padding: 0 29px;
    ul {
      li {
        min-height: 133px;
        padding: 20px;
        padding-bottom: 30px;
        background: linear-gradient(180deg, #ffffff, #ffffff);
        border-radius: 30px;
        box-shadow: 0px -8px 10px 0px rgba(173, 173, 173, 0.11);
        margin-bottom: 20px;
        position: relative;
        h3 {
          font-size: 32px;
          height: 42px;
          color: #000000;
          line-height: 42px;
          font-weight: 500;
          text-align: left;
          margin-bottom: 15px;
        }
        p {
          font-size: 24px;
          font-weight: 400;
          text-align: left;
          color: #666666;
          line-height: 38px;
        }
        img {
          width: 650px;
          height: 470px;
        }
        video {
          margin-top: 20px;
          width: 100%;
        }

        .my-up-btn {
          font-size: 32px;
          position: absolute;
          bottom: 5px;
          left: 50%;
          transform: translateX(-50%);
        }
        .my-down-btn {
          font-size: 32px;
          position: absolute;
          right: 20px;
          top: 66px;
          transform: translateY(-50%);
        }
      }
    }
  }

  .section3 {
    width: 100%;
    padding: 0 29px;
    ul {
      li {
        margin-bottom: 50px;
        p {
          font-size: 30px;
          text-align: center;
        }
        .sub-title {
          font-size: 24px;
          color: #7a7a7a;
          margin: 10px auto;
        }
        img {
          margin-top: 20px;
          width: 100%;
          // height: 360px;
        }
      }
    }
  }

  .section4 {
    padding: 0 29px;
    text-align: center;
    margin-bottom: 20px;
    h3 {
      font-size: 24px;
      color: #7a7a7a;
      margin: 30px auto;
      margin-bottom: 20px;
    }
    .cooperation-btn {
      width: 222px;
      height: 81px;
      background: linear-gradient(90deg, #26bcf6, #1b7bf7 98%);
      border-radius: 52px;
      text-align: center;
      margin: 0 auto;
      font-weight: 600;
      line-height: 81px;
      font-size: 32px;
      color: #f0f0f0;
      margin-top: 60px;
    }
  }

  .section-footer {
    width: 750px;
    background-color: #061930;
    padding: 30px 20px;
    .footer-title {
      color: #c1c1c1;
      display: flex;
      justify-content: space-between;
      align-items: center;
      cursor: pointer;
      font-size: 28px;
      font-weight: bold;
      margin-bottom: 20px;
    }
    .content {
      margin-top: 10px;
      display: none; /* 默认隐藏内容 */
      font-size: 24px;
      color: #7b7e93;
    }

    .content p {
      margin: 5px 0;
      font-size: 24px;
      line-height: 49px;
    }

    .arrow {
      font-size: 26px;
      transition: transform 0.5s ease;
    }

    .arrow.up {
      transform: rotate(180deg);
    }

    .corporation-info {
      margin-top: 30px;
      color: #7b7e93;
      font-size: 24px;
      text-align: left;
      line-height: 33px;
    }
  }
}
</style>
