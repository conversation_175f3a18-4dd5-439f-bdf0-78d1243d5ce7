<template>
  <div>
    <div
      class="qa-template"
      v-if="showValue"
      contenteditable="true"
      :ref="'qaTemplate' + data.id "
      :id="'qaTemplate' + data.id"
      :title="data.template"
      :placeholder="placeholder"
      @blur="handleBlur"
      @keydown="handleKeyDown"
      @keyup.16.219="checkShowVarable($event)"
      @keyup="setRealTimeAddInput"
      @mouseup="handleMouseUp"
      @paste="handlePaste"
    >
      <template v-for="(value, index) in values">
        <span
          v-if="value.type != 'text'"
          :class="{
            'qa-template-variable': value.type === 'variable'
          }"
          contenteditable="false"
          :data-index="index"
          :data-type="value.type"
          :key="index"
          >{{ value.text }}</span
        ><template v-else>{{ value.text }}</template>
      </template>
    </div>
    <qa-popover :listType="listType" :variablePopover="variablePopover"
      @selectItem="setVariable"></qa-popover>
  </div>
</template>
<script>
import QaPopover from './dailog/qaPopover'
export default {
  props: {
    data: {
      type: Object,
      default: () => {
        return {}
      }
    },
    selected: {},
    selectedIndex: Number,
    listType: String,
    valMaxLength: Number,
    placeholder: String,
    inputFun: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      showValue: true,
      values: [],
      range: null,
      cursort: -1,
      variablePopover: {
        show: false,
        rect: null
      }
    }
  },
  computed: {
    templateArr() {
      if(this.listType == 'answer') {
        return ['问法关键字', '关系关键字', '回复语']
      } else {
        return ['问法关键字', '关系关键字']
      }
    }
  },
  watch: {
    showValue: function(val) {
      let self = this
      if (val) {
        this.$nextTick(function() {
          let value = self.getNodesValue()
          if (value && self.cursort >= 0) {
            let editableValue = self.formatValues(
              value.slice(0, self.cursort)
            )
            if (editableValue.length && editableValue[editableValue.length - 1].text === '{') {
              self.setCursortPosition(editableValue.length)
            } else {
              self.setCursortPosition(editableValue.length + 1)
            }
            self.cursort = -1
          }
        })
      }
    },
    'data.id': function(val) {
      this.setValues(this.data.template)
    }
  },
  created(){
    this.setValues(this.data.template)
  },
  methods: {
    setValues(data) {
      let self = this
      this.showValue = false
      this.values = this.formatValues(data)
      this.$nextTick(function() {
        self.showValue = true
      })
    },
    formatValues(data){
      let self = this
      if(!data) return
      let tmp = data.split(/[{}]/) || []
      let regexValues = data.match(/{.*?}/g)
      let newVal = []
      tmp.forEach((item, index) => {
        if(self.$utils.inArray(regexValues || [], `{${item}}`)) {
          if(self.$utils.inArray(this.templateArr || [], item)) {
            newVal.push({
              type: 'variable',
              text: `{${item}}`
            })
          }else {
           item && newVal.push({
              type: 'text',
              text: `{${item}}`
            })
          }
          
        } else {
          item && newVal.push({
            type: 'text',
            text: item
          })
        }
      })
      return newVal
    },
    handleMouseUp(e) {
      e.stopPropagation()
      if (e.target.dataset.type === 'variable' ) {
        let nodes = this.$refs[`qaTemplate${this.data.id}`].childNodes
        for (let i = 0; i <= nodes.length - 1; i++) {
          if (nodes[i] == e.target) {
            this.setCursortPosition(i + 1)
          }
        }
      }
    },
    // 获取光标位置
    getCursortPosition() {
      var caretOffset = 0
      var element = this.$refs[`qaTemplate${this.data.id}`]
      var doc = element.ownerDocument || element.document
      var win = doc.defaultView || doc.parentWindow
      var sel
      if (typeof win.getSelection != 'undefined') {
        //谷歌、火狐
        sel = win.getSelection()
        if (sel.rangeCount > 0) {
          //选中的区域
          var range = win.getSelection().getRangeAt(0)
          var preCaretRange = range.cloneRange() //克隆一个选中区域
          preCaretRange.selectNodeContents(element) //设置选中区域的节点内容为当前节点
          preCaretRange.setEnd(range.endContainer, range.endOffset) //重置选中区域的结束位置
          caretOffset = preCaretRange.toString().length
        }
      } else if ((sel = doc.selection) && sel.type != 'Control') {
        //IE
        var textRange = sel.createRange()
        var preCaretTextRange = doc.body.createTextRange()
        preCaretTextRange.moveToElementText(element)
        preCaretTextRange.setEndPoint('EndToEnd', textRange)
        caretOffset = preCaretTextRange.text.length
      }
      return caretOffset
    },
    // 设置光标位置
    setCursortPosition(pos) {
      var range, selection
      var element = this.$refs[`qaTemplate${this.data.id}`]
      if (document.createRange) {
        //Firefox, Chrome, Opera, Safari, IE 9+
        range = document.createRange() //创建一个选中区域
        range.selectNodeContents(element) //选中节点的内容
        var text = ''
        if (!element.childNodes[pos]) {
          text = document.createTextNode('')
          element.appendChild(text)
          if (element.childNodes.length === pos) {
            pos -= 1
          }
        } else if (element.childNodes[pos].nodeName === 'SPAN') {
          text = document.createTextNode('')
          element.insertBefore(text, element.childNodes[pos])
        }
        range.setStart(element.childNodes[pos], 0) //设置光标起始为指定位置
        range.collapse(true) //设置选中区域为一个点
        selection = window.getSelection() //获取当前选中区域
        selection.removeAllRanges() //移出所有的选中范围
        selection.addRange(range) //添加新建的范围
      } else if (document.selection) {
        //IE 8 and lower
        range = document.body.createTextRange() //Create a range (a range is a like the selection but invisible)
        range.moveToElementText(element) //Select the entire contents of the element with the range
        range.collapse(false) //collapse the range to the end point. false means collapse to end rather than the start
        range.select() //Select the range (make it the visible selection
      }
    },
    // 获取valueNodes html value值
    getNodesValue() {
      let nodes = this.$refs[`qaTemplate${this.data.id}`].childNodes
      let value = ''
      for (let i = 0; i <= nodes.length - 1; i++) {
        value +=
          nodes[i].innerText || nodes[i].nodeValue || nodes[i].textContent
      }
      return value
    },
    setRealTimeAddInput(e) {
      let value = this.getNodesValue()
      let reg1 = /{问法关键字}/g
      let reg2 = /{关系关键字}/g
      let reg3 = /{回复语}/g
      if(reg1.exec(value) !== null || reg2.exec(value) !== null || reg3.exec(value) !== null) {
        this.inputFun == 'addInput' ? this.$emit('setRealTimeAddInput', value) : ''
      }
    },
    checkShowVarable(e) {
      let self = this
      const cursort = this.getCursortPosition()
      const value = this.getNodesValue()
      const rect = window
        .getSelection()
        .getRangeAt(0)
        .getBoundingClientRect()
      setTimeout(function() {
        if (value[cursort - 1] === '{') {
          self.cursort = cursort
          self.variablePopover= {
            show: true,
            rect: rect
          }
          e.target.blur()
        }
      }, 4)
    },
    setVariable(val) {
      let self = this
      if(!val) return
      if (this.data.id === this.selected || !this.data.id) {
        let value = this.getNodesValue()
        value = `${value.slice(0, this.cursort)}${
          val
        }}${value.slice(self.cursort)}`
        this.setValues(value)
        this.inputFun == 'addInput' ? this.$emit('setRealTimeAddInput', value) : '' 
      }     
    },
    handleKeyDown (event) {
      let keycode = window.event ? event.keyCode : event.which;
      let evt = event || window.event;
      if (keycode == 13 && !(evt.ctrlKey)) {
        // 发送消息的代码
        event.target.blur()
        event.preventDefault();
        return false;
      }
    },
    handleBlur(){
      let self = this
      let value = this.getNodesValue()
      if( value !== self.data.template && !self.variablePopover.show) {
        let reg = /\{问法关键字\}/
        if(self.listType == 'question' && !reg.test(value)) {
          return this.$message.warning('问法模板必须要包含"{问法关键字}"')
        }
        this.data.template = value
        this.$emit('setRealTimeAddInput', '')
        this.$emit('addOrEdit', this.data, this.selectedIndex)
        if(this.data && !this.data.id) {
          this.data.template = ''
          this.setValues(this.data.template)
        }
      }
    },
    handlePaste (e) {
      e.preventDefault();
      var text;
      var clp = (e.originalEvent || e).clipboardData;
      if (clp === undefined || clp === null) {
          text = window.clipboardData.getData("text") || "";
          if (text !== "") {
              if (window.getSelection) {
                  var newNode = document.createElement("span");
                  newNode.innerHTML = text;
                  window.getSelection().getRangeAt(0).insertNode(newNode);
              } else {
                  document.selection.createRange().pasteHTML(text);
              }
          }
      } else {
          text = clp.getData('text/plain') || "";
          if (text !== "") {
            document.execCommand('insertText', false, text);
          }
      }
    }
  },
  components: { QaPopover }
}
</script>
<style lang="scss">
  .qa-template {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    line-height: 34px;
    font-size: 14px;
  }
  .qa-template:empty:before {
      content: attr(placeholder);
      color: #B8BABF;
    }
  .qa-template-variable {
    color: $primary;
  }
</style>
