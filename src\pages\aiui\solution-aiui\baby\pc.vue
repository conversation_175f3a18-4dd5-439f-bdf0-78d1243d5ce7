<template>
  <div class="main-content">
    <banner :src="'solution/baby/img_baby_crying_banner.png'" @jump="toConsole">
      <template v-slot:title> 婴儿啼哭检测解决方案 </template
      ><template>
        基于音频属性检测技术，实时监测婴儿啼哭，协助听障人士或外出父<br />
        母第一时间感知宝宝需求。
      </template></banner
    >
    <section class="section section-1">
      <div class="section-title">
        <i class="arrow arrow-left"></i
        ><span class="section-title-bold">应用场景</span
        ><i class="arrow arrow-right"></i>
      </div>
      <div class="section-desc" style="text-align: center">
        基于婴儿啼哭检测能力，配合周边设备震动，闪烁，短信等，第一时间提醒父母
      </div>
      <ul class="product-list">
        <li
          v-for="(item, index) in productList"
          :key="index"
          :class="item.klass"
        >
          <!-- <h1>{{ item.name }}</h1> -->
          <div class="desc-wrap">
            <h2>{{ item.name }}</h2>
            <p>
              {{ item.desc }}
            </p>
          </div>
          <!-- <div class="overlay"></div> -->
        </li>
      </ul>
    </section>
    <section class="section section-7">
      <div class="section-title">
        <i class="arrow arrow-left"></i
        ><span class="section-title-bold">方案介绍</span
        ><i class="arrow arrow-right"></i>
      </div>
      <div class="section-desc" style="text-align: center">
        持续侦测婴儿啼哭，通过婴儿哭声特征提取及专属定制模型，能够及时准确输出识别结果
      </div>
      <div class="section-item">
        <ul>
          <li class="image-1"></li>
          <li class="image-arrow"></li>
          <li class="container">
            <div class="box1">
              <p>预处理</p>
            </div>
            <div class="image-arrow"></div>
            <div class="box1">
              <p>特征处理</p>
            </div>
            <div class="image-arrow"></div>
            <div class="box2">
              <p>
                婴儿啼哭<br />
                检测模型
              </p>
            </div>
          </li>
          <li class="image-arrow-long"></li>
          <li class="image-2"></li>
        </ul>
      </div>
    </section>
    <section class="section section-3">
      <div>
        <div class="section-title">
          <i class="arrow arrow-left"></i
          ><span class="section-title-bold">方案特点</span
          ><i class="arrow arrow-right"></i>
        </div>
        <ul class="advantage">
          <li>
            <p>>95%</p>
            <p>识别准确率</p>
          </li>
          <li>
            <p>3次/48h</p>
            <p>误唤醒率</p>
          </li>
          <li>
            <p>0-3岁</p>
            <p>适用年龄段</p>
          </li>
        </ul>
      </div>
    </section>
    <corp @jump="toConsole">
      <template> 提交信息，我们会尽快与您联系</template>
    </corp>
  </div>
</template>

<script>
import banner from '@P/aiui/solution-aiui/components/banner.vue'
import corp from '@P/aiui/solution-aiui/components/corp.vue'

export default {
  layout: 'aiuiHome',
  data() {
    return {
      productList: [
        {
          name: '手机',
          desc: '',
          klass: 'img_domestic_cellphone',
        },
        {
          name: '摄像头',
          desc: '',
          klass: 'img_smartwatch',
        },
        {
          name: '陪伴机器人',
          desc: '',
          klass: 'img_instrument_smar_bracelet',
        },
        {
          name: '儿童玩具',
          desc: '',
          klass: 'img_sports_camera',
        },
        {
          name: '母婴用品',
          desc: '',
          klass: 'img_smart_helmet',
        },
      ],
    }
  },
  methods: {
    toConsole() {
      // 判断是否有百度SEM营销参数，有的话，提交表单页需要带上
      const search = window.location.search
      if (search) {
        window.open(`/solution/apply/19${search}`)
      } else {
        window.open('/solution/apply/19')
      }
    },
  },
  components: {
    banner,
    corp,
  },
}
</script>

<style lang="scss" scoped>
.main-content {
  .section {
    p,
    ul {
      margin-bottom: 0;
    }
    max-width: 1200px;
    overflow: hidden;
    margin: 0 auto;
    .section-title {
      text-align: center;
      font-size: 34px;
      font-family: SourceHanSansSC-Medium, SourceHanSansSC;
      font-weight: bold;
      color: #333;
      line-height: 34px;
      position: relative;
      width: 200px;
      margin: 0 auto;
      .arrow {
        width: 160px;
        height: 8px;
        display: inline-block;
        background-repeat: no-repeat;
        background-size: contain;
        position: absolute;
        z-index: 1;
      }
      .arrow-left {
        background-position: left;
        background-image: url(~@A/images/solution/offline/img_title_01.png);
        top: 50%;
        left: -160px;
      }
      .arrow-right {
        background-position: right;
        background-image: url(~@A/images/solution/offline/img_title_02.png);
        top: 50%;
        right: -160px;
      }
      .arrow-left1 {
        background-position: left;
        background-image: url(~@A/images/solution/wakeup/img_access_title_01.png);
        top: 50%;
        left: -160px;
      }
      .arrow-right1 {
        background-position: right;
        background-image: url(~@A/images/solution/wakeup/img_access_title_02.png);
        top: 50%;
        right: -160px;
      }
    }
    .section-desc {
      text-align: left;
      margin-top: 40px;
      font-size: 16px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #999999;
    }
    .section-title-bold {
      font-size: 34px;
      font-family: Microsoft YaHei;
      font-weight: bold;
      color: #333333;
    }
    .section-title-spec {
      font-size: 30px;
      font-weight: 500;
      color: #656565;
      line-height: 42px;
    }
    .section-tabs {
      margin-top: 70px;
      ul {
        display: flex;
        justify-content: center;
      }
      li {
        font-size: 20px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #666;
        line-height: 30px;
        position: relative;
        cursor: pointer;
        &:hover {
          color: #1f90fe;
        }
        &.active {
          color: #1f90fe;
          &::before {
            position: absolute;
            content: ' ';
            z-index: 1;
            width: 180px;
            height: 4px;
            background: #1f90fe;
            border-radius: 2px;
            bottom: -32px;
            left: -48px;
          }
          &::after {
            position: absolute;
            content: ' ';
            display: inline-block;
            width: 0;
            height: 0;
            bottom: -100px;
            left: 50%;
            transform: translateX(-50%);
            border: 46px solid;
            border-color: transparent transparent #f4f7f9;
          }
        }
      }
      li + li {
        margin-left: 165px;
      }
    }
    .section-sub-title {
      text-align: center;
      font-size: 18px;
      font-weight: 400;
      color: #777777;
      line-height: 25px;
      margin-top: 42px;
    }
    .section-sub-title-spec {
      font-size: 17px;
      font-weight: 400;
      color: #999999;
      line-height: 28px;
      margin-top: 18px;
    }

    .product-list {
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 50px auto 0;

      li {
        position: relative;
        text-align: center;
        width: 230px;
        height: 328px;

        .desc-wrap {
          padding-top: 39px;
        }
        .overlay {
          display: none;
          width: 100%;
          height: 100%;
          // background: rgba(0, 0, 0, 0.3);
          background-image: linear-gradient(
            0deg,
            rgb(0, 54, 255) 0%,
            rgb(39, 12, 73) 100%
          );
          opacity: 0.502;
          position: absolute;
          top: 0;
          left: 0;
          z-index: 1;
        }

        h1 {
          display: none;
          text-align: left;
          max-width: 25px;
          font-size: 24px;
          font-family: Microsoft YaHei;
          font-weight: 400;
          color: #ffffff;
          line-height: 30px;
          margin: 0 auto;
          position: relative;
          top: 50%;
          transform: translateY(-50%);
        }
        h2 {
          // display: none;
          text-align: center;
          // padding-top: 178px;
          // padding-left: 35px;
          font-size: 21px;
          // font-weight: bold;
          color: #ffffff;
          line-height: 21px;
        }
        p {
          // display: none;
          margin-top: 32px;
          width: 232px;
          font-size: 16px;
          font-weight: 400;
          color: #ffffff;
          line-height: 32px;
          padding-left: 35px;
          text-align: left;
        }

        &.img_domestic_cellphone {
          background: url(~@A/images/solution/baby/img_mobile_phone.png)
            center/100% no-repeat;
        }
        &.img_smartwatch {
          background: url(~@A/images/solution/baby/img_camera.png) center/100%
            no-repeat;
        }
        &.img_instrument_smar_bracelet {
          background: url(~@A/images/solution/baby/img_companion_robot.png)
            center/100% no-repeat;
        }
        &.img_sports_camera {
          background: url(~@A/images/solution/baby/img_childrens_toys.png)
            center/100% no-repeat;
        }
        &.img_smart_helmet {
          background: url(~@A/images/solution/baby/img_infant_mom.png)
            center/100% no-repeat;
        }
      }

      li + li {
        margin-left: 16px;
      }
    }
  }

  .section-1 {
    margin-top: 110px;
  }

  .section-3 {
    max-width: unset;
    padding: 110px 0 5px 0;
    > div {
      margin: 0 auto;
    }

    p {
      margin-bottom: 0;
    }
    .advantage {
      margin-top: 84px;
      display: flex;
      justify-content: center;

      > li {
        width: 318px;
        height: 177px;
        border: 2px solid #eff1f1;
        &:hover {
          border: 1px solid $primary;
          box-shadow: 0px 0px 5px 0px rgba(45, 153, 255, 0.8);
        }

        border-radius: 15px;
        text-align: center;
        padding-top: 42px;

        p {
          margin-bottom: 0;
          &:first-child {
            font-size: 36px;
            font-family: Microsoft YaHei;
            font-weight: bold;
            color: $primary;
            line-height: 36px;
          }
          &:last-child {
            font-size: 18px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            color: #666;
            margin-top: 32px;
          }
        }
      }

      li + li {
        margin-left: 23px;
      }
    }
  }

  .section-7 {
    width: 100%;
    max-width: 100%;
    margin-top: 100px;
    background: #f4f7f9;

    padding: 110px 0 109px 0;
    .section-title {
      color: #fff;
    }
    .section-item {
      margin-top: 70px;

      > ul {
        display: flex;
        align-items: center;
        justify-content: center;
        li {
          position: relative;
        }
        li + li {
          margin-left: 1px;
        }
        .container {
          padding: 0 24px;
          background: #e1f0ff;
          border-radius: 20px;
          height: 180px;
          display: flex;
          align-items: center;
        }
        .image-1 {
          width: 154px;
          height: 119px;
          background: url(~@A/images/solution/baby/icon_baby_crying.png);
        }
        .image-2 {
          width: 137px;
          height: 120px;
          background: url(~@A/images/solution/baby/icon_parents.png);
        }
        .image-arrow {
          width: 61px;
          height: 18px;
          background: url(~@A/images/solution/baby/icon_shape.png);
        }
        .image-arrow-long {
          width: 131px;
          height: 18px;
          background: url(~@A/images/solution/baby/icon_shape_long.png);
        }
        .box1 {
          width: 159px;
          height: 65px;
          background: url(~@A/images/solution/baby/icon_box_blue.png);
          p {
            font-size: 20px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            color: $primary;
            line-height: 65px;
            text-align: center;
          }
        }
        .box2 {
          width: 159px;
          height: 65px;
          background: url(~@A/images/solution/baby/icon_box_red.png);
          p {
            font-size: 18px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            color: #f77e7e;
            line-height: 24px;
            text-align: center;
            padding-top: 5px;
          }
        }
      }
    }
  }
}
</style>
