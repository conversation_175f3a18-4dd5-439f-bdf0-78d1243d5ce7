<template>
  <os-page class="skill-version-page" :options="pageOptions">
    <studio-skill-header-right slot="btn" />
    <div class="version-page">
      <!-- 审核中 start -->
      <template v-if="!privateSkill">
        <os-collapse :default="true" size="large" title="审核中">
          <os-table
            class="gutter-table-style secondary-table"
            :tableData="check"
          >
            <el-table-column prop="number" label="版本" width="156">
            </el-table-column>
            <el-table-column
              prop="date"
              label="发布时间"
              width="200"
              show-overflow-tooltip
            >
            </el-table-column>
            <el-table-column label="更新描述" show-overflow-tooltip>
              <template slot-scope="scope">
                {{ scope.row.updateLog || '-' }}
              </template>
            </el-table-column>
            <el-table-column label="状态" width="200">
              <template slot-scope="scope">
                <div
                  class="check-status"
                  :class="'check-status-' + scope.row.checkStatus"
                ></div>
                {{ checkStatus[scope.row.checkStatus] }}
                <span
                  class="explain"
                  :class="{ 'not-allowed': !subAccountEditable }"
                  @click="cancelPublish(scope.row.mallId)"
                  >取消</span
                >
              </template>
            </el-table-column>
          </os-table>
        </os-collapse>
        <os-divider />
      </template>
      <!-- 线上版本 start -->
      <os-collapse :default="true" size="large" title="线上版本">
        <os-table class="gutter-table-style secondary-table" :tableData="mall">
          <el-table-column prop="number" label="版本" width="156">
          </el-table-column>
          <el-table-column
            prop="date"
            label="发布时间"
            width="200"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column label="更新描述" show-overflow-tooltip>
            <template slot-scope="scope">
              {{ scope.row.updateLog || '-' }}
            </template>
          </el-table-column>
          <el-table-column label="状态" width="200" v-if="!privateSkill">
            <template slot-scope="scope">
              <div
                class="skill-status"
                :class="'skill-status-' + scope.row.status"
              ></div>
              {{ status[scope.row.status] }}
              <div
                class="explain"
                :class="[
                  { 'not-allowd': !subAccountEditable },
                  'skill-status-' + scope.row.status,
                ]"
                @click="unshelve"
              >
                下线
              </div>
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            width="200"
            v-if="privateSkill && (skillType == '2' || skillType == '3')"
          >
            <template slot-scope="scope">
              <a
                :class="{ 'not-allowed': !subAccountEditable }"
                @click="showUseInApp(scope.row.number, scope.row.mallId, true)"
                >在应用中生效</a
              >
            </template>
          </el-table-column>
        </os-table>
      </os-collapse>
      <os-divider />
      <!-- 历史版本 start -->
      <os-collapse :default="true" size="large" title="历史版本">
        <os-table
          class="gutter-table-style secondary-table"
          :tableData="mallHistory"
          style="margin-bottom: 56px"
          @change="changePage"
        >
          <el-table-column prop="number" label="版本" width="156">
          </el-table-column>
          <el-table-column
            prop="date"
            label="发布时间"
            width="200"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column label="更新描述" show-overflow-tooltip>
            <template slot-scope="scope">
              {{ scope.row.updateLog || '-' }}
            </template>
          </el-table-column>
          <el-table-column label="状态" width="200" v-if="!privateSkill">
            <template slot-scope="scope">
              <template v-if="scope.row.checkStatus !== 2">
                <!-- 取消审核或审核未通过的 -->
                <div
                  class="check-status"
                  :class="'check-status-' + scope.row.checkStatus"
                ></div>
                <span
                  >{{ checkStatus[scope.row.checkStatus] }}
                  <el-popover
                    trigger="click"
                    placement="bottom-start"
                    :content="scope.row.opinion"
                  >
                    <span class="explain" slot="reference"> 详情 </span>
                  </el-popover>
                </span>
              </template>
              <template v-else>
                <div
                  class="skill-status"
                  :class="'skill-status-' + scope.row.status"
                ></div>
                <span>{{ status[scope.row.status] }}</span>
              </template>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200">
            <template
              slot-scope="scope"
              v-if="
                privateSkill &&
                (skillType == '2' || skillType == '3') &&
                scope.row.canUse
              "
            >
              <a
                :class="{ 'not-allowed': !subAccountEditable }"
                @click="showUseInApp(scope.row.number, scope.row.mallId, false)"
                >在应用中生效</a
              >
            </template>
          </el-table-column>
        </os-table>
      </os-collapse>
    </div>
    <use-in-app
      :dialog="dialog"
      :businessId="businessId"
      :selectNumber="selectNumber"
      :selectMallId="selectMallId"
      :onlineFlag="onlineFlag"
    ></use-in-app>
  </os-page>
</template>
<script>
import UseInApp from './dialog/useInApp'

export default {
  name: 'skill-version',
  data() {
    return {
      pageOptions: {
        title: '版本管理',
        loading: false,
      },
      check: {
        total: 0,
        list: [],
      },
      mall: {
        total: 0,
        list: [],
      },
      mallHistory: {
        total: 0,
        page: 1,
        size: 5,
        list: [],
      },
      //测试数据
      allMallHistory: [],
      checkStatus: {
        1: '审核中',
        2: '审核通过',
        3: '审核未通过',
        4: '取消审核',
      },
      status: {
        0: '已下线',
        1: '已发布',
      },
      dialog: {
        show: false,
      },
      selectNumber: '',
      selectMallId: '',
      onlineFlag: false,
    }
  },
  computed: {
    businessId() {
      return this.$store.state.studioSkill.id
    },
    privateSkill() {
      return this.$store.state.studioSkill.skill.privateSkill || ''
    },
    skillType() {
      return this.$store.state.studioSkill.skill.type || ''
    },
    subAccountEditable() {
      let auths = this.$store.state.studioSkill.subAccountSkillAuths
      return auths[this.businessId] == 2 ? false : true
    },
  },
  created() {
    this.getVersion()
  },
  methods: {
    getVersion() {
      this.pageOptions.loading = true
      this.$utils.httpGet(
        this.$config.api.STUDIO_SKILL_VERSION,
        {
          businessId: this.businessId,
        },
        {
          success: (res) => {
            this.check.list.splice(0)
            this.mall.list.splice(0)
            this.mallHistory.list.splice(0)
            this.allMallHistory.splice(0)
            this.pageOptions.loading = false
            if (res.data && !Object.keys(res.data).length) {
              return
            }
            if (this.privateSkill) {
              if (res.data.private && res.data.private.length) {
                this.mall.list.push(res.data.private[0])
                this.allMallHistory.push(
                  ...res.data.private.slice(1, res.data.private.length)
                )
                this.mallHistory.total = res.data.private.length - 1
                this.mallHistory.list.push(
                  ...res.data.private.slice(1, this.mallHistory.size + 1)
                )
              }
              return
            } else {
              res.data.hasOwnProperty('check') &&
                this.check.list.push(...res.data.check)
              res.data.hasOwnProperty('mall') &&
                this.mall.list.push(...res.data.mall)
              this.allMallHistory =
                res.data.hasOwnProperty('mallHistory') && res.data.mallHistory
              this.mallHistory.total = Math.floor(
                this.allMallHistory.length / this.mallHistory.size
              )
              this.mallHistory.list.push(
                ...this.allMallHistory.slice(0, this.mallHistory.size)
              )
            }
          },
          error: (err) => {
            this.pageOptions.loading = false
          },
        }
      )
    },
    changePage(val) {
      let size = this.mallHistory.size
      this.mallHistory.list = this.allMallHistory.slice(
        (val - 1) * size,
        val * size
      )
    },
    unshelve() {
      if (!this.subAccountEditable) return
      this.$utils.httpGet(
        this.$config.api.STUDIO_SKILL_UNSHELVE,
        {
          businessId: this.businessId,
        },
        {
          success: (res) => {
            this.getVersion()
            this.$message.success('下线成功')
            this.$store.dispatch('studioSkill/setSkill', this.businessId)
          },
        }
      )
    },
    cancelPublish(mallId) {
      if (!this.subAccountEditable) return
      this.$utils.httpGet(
        this.$config.api.STUDIO_SKILL_CANCELPUBLISH,
        {
          businessId: this.businessId,
          mallId: mallId,
        },
        {
          success: (res) => {
            this.getVersion()
            this.$message.success('取消成功')
            this.$store.dispatch('studioSkill/setSkill', this.businessId)
          },
        }
      )
    },
    showUseInApp(num, mallId, online) {
      if (!this.subAccountEditable) return
      this.dialog.show = true
      this.selectNumber = num
      this.selectMallId = mallId
      this.onlineFlag = online
    },
  },
  components: {
    UseInApp,
  },
}
</script>
<style lang="scss" scoped>
.explain {
  color: $primary;
  cursor: pointer;
}
.skill-status,
.check-status {
  display: inline-block;
  width: 8px;
  height: 8px;
  border: 2px solid;
  border-radius: 8px;
  margin-right: 6px;
}
.skill-status {
  &-0 {
    border-color: $grey5;
  }
  &-1 {
    border-color: $success;
  }
}
.check-status {
  &-1 {
    border-color: $primary;
  }
  &-2 {
    border-color: $success;
  }
  &-3 {
    border-color: $dangerous;
  }
  &-4 {
    border-color: $warning;
  }
}
</style>
<style lang="scss">
.skill-version-page {
  .os-collapse-title {
    margin: 28px 0;
    .ic-r-angle-d {
      color: $grey5;
    }
  }
}
</style>
