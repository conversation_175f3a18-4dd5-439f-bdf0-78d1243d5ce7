<template>
  <ul class="function-container">
    <li v-for="(item, index) in cells" :key="index">
      <div
        class="icon-tip"
        :style="{
          backgroundImage:
            'url(' +
            require(`../../../../assets/images/solution/smart-hardware/icon-tips/${label}_${
              index + 1
            }.png`) +
            ')',
        }"
      ></div>
      <p>{{ item.title }}</p>
      <div v-html="item.introduction"></div>
    </li>
  </ul>
</template>
<script>
export default {
  props: {
    cells: {
      required: true,
      type: Array,
      default: [],
    },
    label: {
      required: true,
      type: String,
      default: '',
    },
  },
}
</script>
<style lang="scss" scoped>
p {
  margin-bottom: 0;
}
.function-container {
  display: flex;
  justify-content: center;
  // align-items: center;
  // background: rgba(171, 212, 255, 0.08);
  padding-top: 61px;
  padding-bottom: 69px;
  li {
    width: 180px;
    text-align: center;
    .icon-tip {
      width: 98px;
      height: 98px;
      background-repeat: no-repeat;
      background-size: contain;
      margin: 0 auto;
    }
    p {
      font-size: 18px;
      font-weight: bold;
      color: #333;
      line-height: 30px;
      margin-top: 16px;
    }
    div:last-of-type {
      font-size: 15px;
      font-weight: 400;
      color: #999999;
      line-height: 23px;
      text-align: center;
      margin-top: 22px;
      white-space: nowrap;
    }
  }
  li + li {
    margin-left: 76px;
  }
}
</style>
