<template>
  <div class="os-scroll">
    <handle-platform-top v-if="subAccount"></handle-platform-top>
    <div
      class="handle-platform-content"
      :style="{ padding: subAccount ? '10px' : 0 }"
    >
      <div
        style="
          font-size: 0;
          display: flex;
          justify-content: space-between;
          flex-direction: row-reverse;
        "
      >
        <div>
          <el-button
            v-if="
              (limitCount && limitCount['skill_extend_count'] > 0) ||
              (createAuth && subAccountHasExtendSkillAuth)
            "
            type="text"
            class="btn-create-extend-skill"
            :disabled="!createAuth"
            @click="openCreateExtendSkill"
            >定制官方技能</el-button
          >
          <el-button
            class="mgb24"
            icon="ic-r-plus"
            type="primary"
            :disabled="!createAuth"
            size="medium"
            @click="openCreateSkill"
          >
            创建技能
          </el-button>
          <!-- <a
          class="btn-create-extend-skill"
          v-if="
            (limitCount && limitCount['skill_extend_count'] > 0) ||
            (createAuth && subAccountHasExtendSkillAuth)
          "
          @click="openCreateExtendSkill"
          >定制官方技能</a
        > -->
        </div>
        <el-form
          inline
          label-width="70px"
          class="search-form"
          :model="searchFilter"
          size="medium"
          ref="searchForm"
        >
          <!-- <el-form-item label="技能类型" prop="type" style="margin-right: 0">
            <el-cascader
              class="skill-cascader"
              ref="cascader"
              v-model="searchFilter.type"
              :options="options"
              @change="handleChange"
            ></el-cascader>
          </el-form-item> -->
          <el-form-item
            label="关键字"
            prop="searchVal"
            style="margin-right: 22px"
          >
            <el-input
              placeholder="输入技能中、英文名称搜索"
              v-model="searchFilter.searchVal"
              style="width: 240px"
              @keydown.native.enter.prevent="getSkills(1)"
            />
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              size="medium"
              style="min-width: 80px"
              @click="getSkills(1)"
              >搜索</el-button
            >
            <el-button
              size="medium"
              style="min-width: 80px; margin-left: 4px"
              @click="resetSearch"
              >重置</el-button
            >
          </el-form-item>
        </el-form>
      </div>
      <os-table
        :tableData="tableData"
        :height="'calc(100vh - 231.5px)'"
        style="margin-bottom: 15px"
        class="skills-table gutter-table-style transparent-bgc"
        v-if="hasSkill"
        @change="getSkills"
        @edit="toEdit"
        @del="toDel"
        @row-click="toEdit"
      >
        <el-table-column prop="name" width="300" label="技能">
          <template slot-scope="scope">
            <os-skill-simple-item
              class="cp skills-page-skill-zh-name"
              :url="scope.row.url"
              :name="scope.row.zhName || '-'"
            />
          </template>
        </el-table-column>
        <el-table-column prop="type" width="140" label="技能类型">
          <template slot-scope="scope">
            <template v-if="scope.row.type === '5'">
              <span class="vt-middle">开放技能</span>
              <div class="intent-tag ib">开</div>
            </template>
            <template v-else-if="scope.row.type === '3'">
              <span class="vt-middle">定制技能</span>
              <div class="intent-tag ib extend-icon-tag">定</div>
            </template>
            <template v-else-if="scope.row.type === '8'">
              <span class="vt-middle">QC技能</span>
              <div class="intent-tag ib qc-icon-tag">Q</div>
            </template>
            <template v-else-if="scope.row.type === '9'">
              <span class="vt-middle">开放技能</span>
              <div class="intent-tag ib">开</div>
            </template>
            <template v-else>
              <span class="vt-middle">私有技能</span>
              <div class="intent-tag ib private">私</div>
            </template>
          </template>
        </el-table-column>
        <!-- <el-table-column prop="type" width="140" label="服务平台">
          <template slot-scope="scope">
            <span v-if="scope.row.type === '2' || scope.row.type === '3'"
              >AIUI</span
            >
            <span v-else>iFLYOS</span>
          </template>
        </el-table-column> -->
        <!-- <el-table-column
          prop="releaseTime"
          width="200"
          label="发布时间">
          <template slot-scope="scope">
            <div>{{scope.row.releaseTime | date('yyyy-MM-dd')}}</div>
          </template>
        </el-table-column> -->
        <el-table-column prop="updateTime" width="230" label="更新时间">
          <template slot-scope="scope">
            <div>{{ scope.row.updateTime | date('yyyy-MM-dd hh:mm:ss') }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="status" width="190" label="状态">
          <template slot-scope="scope">
            <div
              class="ib skill-status"
              :class="'skill-status-' + scope.row.status"
            />
            <span class="mgr8">{{ scope.row.status | skillStatus }}</span>
            <el-popover
              trigger="click"
              placement="bottom-start"
              v-if="scope.row.status === 4"
              :content="scope.row.checkOpinion"
            >
              <a class="explain" slot="reference"> 详情 </a>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column width="100" label="操作">
          <template slot-scope="scope">
            <el-tooltip
              class="item"
              effect="dark"
              content="编辑"
              placement="top"
            >
              <i
                class="sub-account cell-handle-ic ic-r-edit"
                @click.stop.prevent="toEdit(scope.row)"
              />
            </el-tooltip>

            <i
              v-if="skillAuths && skillAuths[scope.row.id] != '2'"
              class="sub-account cell-handle-hovershow cell-handle-ic ic-r-delete"
              @click.prevent.stop="toDel(scope.row)"
            />
          </template>
        </el-table-column>
      </os-table>
      <div class="create-guide" v-else>
        <div class="icon"></div>
        <p class="title">
          你还没有创建任何技能，
          <a @click="openCreateSkill" v-if="createAuth"> 点击创建 </a>
        </p>
        <p class="desc">
          技能，类似于手机 APP
          的概念，一个语音技能用于解决一类用户需求，不同于手机 App
          的地方在于，语音技能使用语音作为交互的入口。
        </p>
        <a :href="`${$config.docs}doc-44/`" target="_blank">了解更多</a>
      </div>
    </div>
    <create-skill-dialog :dialog="dialog" @change="getSkills" />
    <create-extend-skill-dialog
      :dialog="extendSkillDialog"
    ></create-extend-skill-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import HandlePlatformTop from './top.vue'
import CreateSkillDialog from './dialog/createSkill.vue'
import CreateExtendSkillDialog from './dialog/createExtendSkill.vue'
const intiOptions = [
  {
    value: '',
    label: '全部',
  },
  {
    value: '1',
    label: '开放技能',
    children: [
      {
        value: '10',
        label: '全部',
      },
      {
        value: '11',
        label: '自定义语音技能-iFLYOS',
      },
      {
        value: '12',
        label: 'APP控制技能-iFLYOS',
      },
    ],
  },
  {
    value: '2',
    label: '私有技能',
    children: [
      {
        value: '20',
        label: '全部',
      },
      {
        value: '21',
        label: '自定义语音技能-iFLYOS',
      },
      {
        value: '22',
        label: '自定义语音技能-AIUI',
      },
    ],
  },
]
export default {
  name: 'studio-handle-platform-skills',
  data() {
    return {
      nav: 'skills',
      tableData: {
        loading: true,
        total: 0,
        page: 1,
        size: 10,
        // handles: ['edit', 'del'],
        // handles:[],
        handleColumnText: '操作',
        list: [],
      },
      dialog: {
        show: false,
      },
      hasSkill: true,
      extendSkillDialog: {
        show: false,
      },
      createAuth: true,
      skillAuths: {},
      subAccountHasExtendSkillAuth: false,
      searchFilter: {
        type: [''],
        searchVal: '',
      },
      first: '',
      second: '',
      platform: '2',
    }
  },
  computed: {
    ...mapGetters({
      limitCount: 'aiuiApp/limitCount',
      subAccountInfo: 'user/subAccountInfo',
      subAccount: 'user/subAccount',
    }),
    options() {
      if (
        (this.limitCount && this.limitCount['skill_extend_count'] > 0) ||
        (this.createAuth && this.subAccountHasExtendSkillAuth)
      ) {
        return intiOptions.concat({
          value: '3',
          label: '业务定制',
        })
      } else {
        return intiOptions
      }
    },
  },
  created() {
    this.getSkills(1)
    this.getMyAuthority()
  },
  watch: {
    subAccountInfo(val) {
      if (val && this.limitCount) {
        this.getSubExtendCount()
      }
    },
    limitCount(val) {
      if (val && this.subAccountInfo) {
        this.getSubExtendCount()
      }
    },
  },
  mounted() {
    if (localStorage.getItem('addSkill') === 'true') {
      this.openCreateSkill()
      localStorage.setItem('addSkill', false)
    }
  },
  methods: {
    handleChange(val) {
      if (val && val.length <= 1) {
        this.first = val[0] == 3 ? 3 : ''
        this.second = ''
        this.platform = ''
      } else if (val[0] && val[0] == 1) {
        this.first = 1
        this.second = val[1].substr(1)
        this.platform = 1
      } else {
        this.first = 2
        this.second = 1
        this.platform = val[1].substr(1)
      }
    },
    resetSearch() {
      this.$refs.searchForm.resetFields()
      this.searchFilter.type = ['']
      this.$forceUpdate()
      this.first = ''
      this.second = ''
      this.platform = '2'
      this.getSkills(1)
    },
    getSkills(page) {
      let self = this
      this.tableData.loading = true
      this.$utils.httpGet(
        this.$config.api.STUDIO_USER_SKILLS,
        {
          pageIndex: page || this.tableData.page,
          pageSize: this.tableData.size,
          search: this.searchFilter.searchVal,
          first: this.first,
          second: this.second,
          // platform: this.platform,
          platform: '2',
        },
        {
          success: (res) => {
            if (
              res.data.skills &&
              !res.data.skills.length &&
              !self.searchFilter.searchVal
            ) {
              self.hasSkill = false
            } else {
              self.hasSkill = true
            }
            self.tableData.list = res.data.skills
            self.tableData.total = res.data.count
            self.tableData.page = res.data.pageIndex
            self.tableData.size = res.data.pageSize
            self.tableData.loading = false
          },
          error: (err) => {},
        }
      )
    },
    openCreateSkill() {
      this.dialog.show = true
    },
    toEdit(data) {
      console.log(JSON.stringify(data))
      let isSubAccount = this.subAccountInfo
      let routeData
      if (data.type && data.type == '3') {
        if (isSubAccount) {
          // this.$router.push({
          //   name: 'extend-sub-skill-intentions',
          //   params: { skillId: data.id },
          // })
          routeData = this.$router.resolve({
            name: 'extend-sub-skill-intentions',
            params: { skillId: data.id },
          })
        } else {
          // this.$router.push({
          //   name: 'extend-skill-intentions',
          //   params: { skillId: data.id },
          // })
          routeData = this.$router.resolve({
            name: 'extend-skill-intentions',
            params: { skillId: data.id },
          })
        }
        window.open(routeData.href, '_blank')
        return
      }
      if (isSubAccount) {
        // this.$router.push({ name: 'sub-skill', params: { skillId: data.id } })
        routeData = this.$router.resolve({
          name: 'sub-skill',
          params: { skillId: data.id },
        })
      } else {
        routeData = this.$router.resolve({
          name: 'skill',
          params: { skillId: data.id },
        })
      }
      window.open(routeData.href, '_blank')
    },
    toDel(data) {
      let self = this
      this.$confirm(
        '删除技能将删除该技能的所有数据，删除后不可恢复，请谨慎操作。',
        '要删除技能吗？',
        {
          confirmButtonText: '删除技能',
          cancelButtonText: '取消',
          confirmButtonClass: 'el-button--danger',
          type: 'warning',
          showClose: false,
        }
      )
        .then(() => {
          self.delSkill(data)
        })
        .catch(() => {})
    },
    delSkill(data) {
      console.log(data)
      let self = this
      this.$utils.httpPost(
        this.$config.api.STUDIO_SKILL_DELETE,
        {
          businessId: data.id,
        },
        {
          success: (res) => {
            self.$message.success('技能删除成功')
            self.getSkills(1)
          },
          error: (err) => {},
        }
      )
    },
    openCreateExtendSkill() {
      if (!this.createAuth && !this.subAccountHasExtendSkillAuth) return
      this.extendSkillDialog.show = true
    },
    //协同操作
    getMyAuthority() {
      if (!this.$route.path.match('sub/skills')) return
      let self = this
      this.$utils.httpGet(
        this.$config.api.SUB_USER_SKILL_AUTH,
        {},
        {
          success: (res) => {
            self.createAuth = res.data.create
            self.skillAuths = res.data.edit
          },
          error: (err) => {},
        }
      )
    },
    getSubExtendCount() {
      if (
        this.limitCount &&
        this.limitCount.hasOwnProperty('skill_extend_count') &&
        this.limitCount['skill_extend_count'] > 0
      ) {
        return
      }

      if (!this.subAccountInfo) return
      this.$utils.httpGet(
        this.$config.api.SUB_USER_EXTEND_COUNT,
        {
          subUid: this.subAccountInfo.subUid,
        },
        {
          success: (res) => {
            if (res.data.hasOwnProperty('sub_extend_count')) {
              this.subAccountHasExtendSkillAuth =
                res.data.sub_extend_count > 0 ? true : false
            }
          },
          error: (err) => {},
        }
      )
    },
  },
  components: {
    HandlePlatformTop,
    CreateSkillDialog,
    CreateExtendSkillDialog,
  },
}
</script>

<style lang="scss" scoped>
.handle-platform-content {
  // max-width: 1200px;
  width: 100%;
  margin: auto;
}
.search-area {
  width: 480px;
}
.skill-status {
  width: 8px;
  height: 8px;
  border: 2px solid;
  border-radius: 8px;
  margin-right: 6px;
  &-1 {
    border-color: $warning;
  }
  &-2 {
    border-color: $primary;
  }
  &-3 {
    border-color: $success;
  }
  &-4 {
    border-color: $dangerous;
  }
  &-5 {
    border-color: $grey5;
  }
}

.skills-table-name {
  cursor: pointer;
}
.os-skill-simple-item {
  width: 300px;
}

.btn-create-extend-skill {
  font-size: 14px;
  margin-left: 15px;
}

.intent-tag.private {
  color: $semi-black;
  background-color: $grey1;
}
.intent-tag.qc-icon-tag {
  color: $dangerous;
  background-color: $dangerous-light-12;
}

.create-guide {
  margin: 76px 0;
  text-align: center;
  font-size: 16px;
  color: $grey5;
  .icon {
    margin: 0 auto 24px;
    width: 120px;
    height: 120px;
    background: url(../../../assets/images/app/create-app.png) center no-repeat;
    background-size: 100%;
  }
  .title {
    font-size: 16px;
    font-weight: 600;
    a {
      font-weight: 600;
    }
  }
  .desc {
    margin: 24px auto;
    width: 480px;
    font-size: 14px;
    line-height: 22px;
  }
}

.search-form {
  float: right;
}
.el-cascader__dropdown {
  .el-cascader-menu {
    min-width: 160px;
  }
  .el-cascader-menu__wrap {
    height: auto;
  }
  .el-cascader-node {
    padding: 0 30px 0 6px;
  }
  .el-cascader-node__prefix {
    display: none;
  }
}
</style>
<style lang="scss">
.skills-table {
  .el-table td {
    padding: 0;
  }
  .el-table tr {
    cursor: pointer;
  }
}
tr:hover > td {
  .skills-page-skill-zh-name > p {
    color: $primary;
  }
}
</style>
