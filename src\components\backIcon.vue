<template>
  <div
    class="back_icon"
    @click="handleClick"
    @mouseenter="isHover = true"
    @mouseleave="isHover = false"
  >
    <img v-if="!isHover" src="../assets/images/back-arrow.png" alt="返回" />
    <img v-else src="../assets/images/back-arrow-active.png" alt="返回" />
  </div>
</template>

<script>
export default {
  name: 'back_icon',
  data() {
    return {
      isHover: false,
    }
  },
  methods: {
    handleClick() {
      this.$emit('click')
    },
  },
}
</script>

<style scoped>
.back_icon {
  display: inline-block;
  cursor: pointer;
  width: 27px;
  height: 27px;
}

.back_icon img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}
</style>
