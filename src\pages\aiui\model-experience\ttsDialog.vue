<template>
  <div>
    <el-dialog
      title="流式合成"
      :visible.sync="dialog.show"
      width="700px"
      :append-to-body="true"
      custom-class="new-style-dialog"
    >
      <div class="tts-container">
        <div class="tip" v-if="current === 'product'">中文</div>
        <div class="tip" v-else>{{ currentChain | translate }}</div>
        <ul class="speaker">
          <li
            v-for="(item, index) in mySpeakers"
            :key="item.vcn"
            :class="{ active: item.vcn === currentVcn }"
            @click="selectVcn(item)"
          >
            <div
              class="avatar"
              :style="{ backgroundImage: 'url(' + item.url + ')' }"
            ></div>
            <div class="name" :title="item.name">{{ item.name }}</div>
            <div
              class="listen-opt"
              v-if="item.isPlaying"
              @click.stop="togglePlay(index)"
            >
              <i class="icon-listen icon-pause"></i><span>试听</span>
            </div>
            <div class="listen-opt" v-else @click.stop="togglePlay(index)">
              <i class="icon-listen icon-play"></i><span>试听</span>
            </div>
          </li>
        </ul>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="save">保存</el-button>
      </span>
    </el-dialog>
    <audio
      ref="audioPlayer"
      :src="currentSrc"
      style="visibility: hidden"
    ></audio>
  </div>
</template>
<script>
export default {
  props: {
    dialog: {
      type: Object,
      default: () => {
        return {
          show: false,
        }
      },
    },
    vcnConfig: Object,
    speakers: Array,
    currentChain: String,
    current: String,
  },
  data() {
    return {
      form: {
        name: '',
        desc: '',
      },
      mySpeakers: [],
      // 正在进行试听播放音频的发音人
      currentIndex: -1,

      // 选中的当前发音人
      currentVcn: '',
      // currentTtsType: '',
    }
  },
  watch: {
    'dialog.show'(val) {
      if (val) {
        this.currentVcn = (this.vcnConfig && this.vcnConfig.vcn) || ''
        this.getInfosTTSExperience()
      }
    },
  },
  mounted() {
    this.initAudio()
  },
  computed: {
    currentSrc() {
      return (
        (this.mySpeakers[this.currentIndex] &&
          this.mySpeakers[this.currentIndex].audio) ||
        ''
      )
    },
  },
  methods: {
    initAudio() {
      this.$refs.audioPlayer.onended = () => {
        this.mySpeakers[this.currentIndex].isPlaying = false
        this.currentIndex = -1
      }
      this.$refs.audioPlayer.onpause = () => {
        this.mySpeakers[this.currentIndex].isPlaying = false
        this.currentIndex = -1
      }
      this.$refs.audioPlayer.oncanplay = () => {}
    },
    getInfosTTSExperience() {
      this.mySpeakers = this.speakers
        .filter((item) =>
          this.current === 'product'
            ? item.lang === 'chinese'
            : item.lang === this.currentChain
        )
        .map((item) => {
          return {
            ...item,
            isPlaying: false,
          }
        })
    },
    togglePlay(index) {
      if (this.mySpeakers[index].isPlaying) {
        // 正在播放
        this.$refs.audioPlayer && this.$refs.audioPlayer.pause()
        this.mySpeakers[index].isPlaying = false
      } else {
        // 关闭其他所有可能在播放的项目
        this.$refs.audioPlayer && this.$refs.audioPlayer.pause()

        this.mySpeakers = this.mySpeakers.map((item) => {
          return {
            ...item,
            isPlaying: false,
          }
        })
        this.currentIndex = index
        this.mySpeakers[index].isPlaying = true
        this.$nextTick(() => {
          this.$refs.audioPlayer && this.$refs.audioPlayer.play()
        })
      }
    },
    selectVcn(item) {
      this.currentVcn = item.vcn
      // this.currentTtsType = item.ttsType
    },
    save() {
      if (!this.currentVcn) {
        return this.$message.warning('请选择发音人')
      } else {
        this.$emit('setVcn', {
          vcn: this.currentVcn,
          // ttsType: this.currentTtsType,
        })
        this.dialog.show = false
      }
    },
  },
  filters: {
    translate(val) {
      const langMap = { chinese: '中文', cantonese: '粤语', english: '英文' }
      return langMap[val] || ''
    },
  },
}
</script>
<style lang="scss" scoped>
.tts-container {
  background: #e9f1ff;
  border: 1px solid #ffffff;
  border-radius: 8px;
  padding: 12px;
  .tip {
    font-size: 14px;
    font-weight: 400;
    color: #8e90a5;
    line-height: 20px;
    margin-bottom: 8px;
  }
  .speaker {
    display: grid;
    grid-template-columns: repeat(3, 1fr); /* 将容器分为四列，每列宽度平均 */
    gap: 12px;
    > li {
      cursor: pointer;
      height: 48px;
      background: #ffffff;
      border-radius: 8px;
      display: flex;
      align-items: center;
      padding-left: 12px;
      &.active {
        border: 1px solid $primary;
      }
      .avatar {
        width: 32px;
        height: 32px;
        background-repeat: no-repeat;
        background-size: contain;
        background-position: center;
        border-radius: 100%;
      }
      .name {
        font-size: 14px;
        font-weight: 500;
        color: #454973;
        line-height: 20px;
        margin: 0 14px 0 12px;
        max-width: 68px;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
      }
      .listen-opt {
        display: flex;
        align-items: center;
        i {
          margin-right: 6px;
          width: 16px;
          height: 16px;
          display: inline-block;
        }
        span {
          font-size: 12px;
          color: #8e90a5;
        }
        .icon-play {
          background: url(~@A/images/model-exeperience/v2/<EMAIL>)
            center/contain no-repeat;
        }
        .icon-pause {
          background: url(~@A/images/model-exeperience/v2/<EMAIL>)
            center/contain no-repeat;
        }
      }
    }
  }
}
</style>
