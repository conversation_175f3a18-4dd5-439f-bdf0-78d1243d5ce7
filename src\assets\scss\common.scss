/**
 * commonn [公用样式文件]
 */

.fl {
  float: left;
}

.fr {
  float: right;
}

.flex {
  display: flex;
}

.block {
  display: block;
}

.ib {
  display: inline-block;
}

.if-alc {
  display: inline-flex;
  align-items: center;
}

.vt-middle {
  vertical-align: middle;
}

.txt-al-c {
  text-align: right;
}

.cp {
  cursor: pointer;
}

.fx1 {
  flex: 1;
}

.txt-al-c {
  text-align: right;
}

.txt-al-l {
  text-align: left;
}

.txt-al-r {
  text-align: right;
}

.txt-ellipsis-nowrap {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.app-container {
  display: flex;
  height: 100%;
}

.os-container {
  display: flex;
  flex: 1;
  box-sizing: border-box;
  min-width: 0;
  height: 100%;
  overflow: hidden;
}
.os-aside {
  width: 214px;
  border-right: 1px solid $grey007;
  // background: #f2f3f5;
  .os_scroll {
    background: #fff !important;
    .os-menu {
      padding: 15px 4px 0 4px;
      .menu_icon {
        margin-right: 5px;
        vertical-align: middle;
        color: #757a8c; // 图标默认颜色（灰色 非选中状态）
      }
      > .el-menu-item {
        height: 36px;
        line-height: 36px;
        border-radius: 8px;
        margin-bottom: 10px;
        padding-left: 20px !important;
        color: #3f4453;
      }
      > .el-submenu {
        margin-bottom: 10px;
        .el-submenu__title {
          height: 36px;
          line-height: 36px;
          border-radius: 8px;
          padding-left: 20px !important;
          color: #3f4453;
          &:hover {
            background-color: #f2f4fa;
          }
        }
        ul {
          overflow: hidden;
          .el-menu-item {
            height: 36px;
            line-height: 36px;
            color: #757a8c;
            padding-left: 30px !important;
            border-radius: 8px;
          }
        }
      }

      .el-menu-item {
        &.os-menu-active {
          // color: $primary !important;
          background-color: #f2f4fa;
          .menu_icon {
            color: $primary !important;
          }
          &::before {
            display: none;
          }
        }
        &:hover {
          background-color: #f2f4fa;
        }
      }
    }
  }
}
.os-main {
  display: block;
  flex: 1;
  overflow: hidden;
  height: 100%;
  min-width: 400px;
}
.os-side-right {
  transition: 0.3s;
  width: 64px;
  height: 100%;
  // box-shadow: 0 0 24px 0 rgba(0, 0, 0, 0.1);
  border-left: 1px solid$grey007;
}
.os-side-right-open {
  width: 550px;
  transition: 0.3s;
}
// 技能商店适配不同屏幕
@media screen and (max-width: 1601px) {
  .os-side-right-open {
    width: 367px;
  }
}

.os-scroll {
  background-color: $white-grey !important;
  overflow-y: auto !important;
  height: 100%;
  @include scroll();
}

.mgt12 {
  margin-top: 12px;
}

.mgt24 {
  margin-top: 24px;
}

.mgt28 {
  margin-top: 28px;
}

.mgt32 {
  margin-top: 32px;
}

.mgt48 {
  margin-top: 48px;
}
.mgt40 {
  margin-top: 40px;
}
.mgt56 {
  margin-top: 56px;
}

.mgb8 {
  margin-bottom: 8px;
}

.mgb12 {
  margin-bottom: 12px;
}

.mgb16 {
  margin-bottom: 16px;
}

.mgb24 {
  margin-bottom: 24px;
}

.mgb32 {
  margin-bottom: 32px;
}

.mgb36 {
  margin-bottom: 36px;
}

.mgb40 {
  margin-bottom: 40px;
}

.mgb45 {
  margin-bottom: 45px;
}
.mgb48 {
  margin-bottom: 48px;
}

.mgb56 {
  margin-bottom: 56px;
}

.mgr8 {
  margin-right: 8px;
}

.mgr16 {
  margin-right: 16px;
}

.mgr24 {
  margin-right: 24px;
}

.mglr2 {
  margin: 0 2px;
}
.mgl3mgr1 {
  margin: 0 1px 0 3px;
}
.mgl1mgr3 {
  margin: 0 3px 0 1px;
}

.text-bold {
  font-weight: 600;
}

.intent-tag,
.skill-tag {
  color: $primary;
  width: 20px;
  height: 20px;
  border-radius: 20px;
  text-align: center;
  line-height: 20px;
  font-size: 12px;
  font-weight: 600;
  background-color: $primary-light-12;
  vertical-align: middle;
  &-entity-3 {
    background-color: $warning-light-12;
    color: $warning;
  }
  &-entity-5 {
    background-color: $success-light-12;
    color: $success;
  }
  img {
    width: 20px;
    height: 20px;
  }
}
.extend-icon-tag {
  color: $purple;
  background: $purple-light-12;
}

.page-form-btns {
  padding-left: 120px;
}

.text-primary {
  color: $primary;
}

.text-red {
  color: $dangerous;
}

.text-grey {
  color: $grey4;
}
.text-grey6 {
  color: $grey6;
}

.text-blod {
  font-weight: 600;
}

.text-blue {
  color: $primary;
}

.text-black {
  color: $semi-black;
}
.fs25 {
  font-size: 25px;
}
.fs18 {
  font-size: 18px;
}

// 搜索框的button
.search-area-btn {
  font-size: 16px;
  cursor: pointer;
}

// 解决搜索框文本遮挡问题
.search-area {
  // 输入框容器
  .el-input__inner {
    padding-right: 55px !important; // 为搜索图标和可能的清除按钮预留空间
  }
}

// .utterance-color {
//   background-color: #fff;
//   &-1 {
//     background-color: rgba(255, 213, 0, 0.3);
//   }
//   &-2 {
//     background-color: rgba(35, 217, 176, 0.3);
//   }
//   &-3 {
//     background-color: rgba(82, 128, 255, 0.3);
//   }
//   &-4 {
//     background-color: rgba(255, 77, 77, 0.3);
//   }
//   &-5 {
//     background-color: rgba(23, 132, 233, 0.3);
//   }
//   &-6 {
//     background-color: rgba(24, 218, 0, 0.3);
//   }
//   &-7 {
//     background-color: rgba(157, 218, 0, 0.3);
//   }
//   &-8 {
//     background-color: rgba(255, 164, 0, 0.3);
//   }
//   &-9 {
//     background-color: rgba(170, 150, 200, 0.3);
//   }
//   &-10 {
//     background-color: rgba(150, 188, 200, 0.3);
//   }
//   &-11 {
//     background-color: rgba(169, 200, 150, 0.3);
//   }
//   &-12 {
//     background-color: rgba(200, 150, 150, 0.3);
//   }
// }

.handle-platform {
  &-top {
    padding-top: 42px;
    margin-bottom: 0px;
    width: 100%;
    background-color: $grey1;
    &-search {
      width: 480px;
      margin: auto;
      padding-bottom: 48px;
    }
  }
  &-navs {
    // width: 700px;
    // width: 400px;
    // margin: auto;
    display: flex;
    justify-content: center;
    margin: 0 auto;
  }
  &-nav {
    width: 144px;
    height: 48px;
    line-height: 48px;
    display: inline-block;
    text-align: center;
    font-weight: 600;
    border-radius: 6px;
    border-bottom-right-radius: 0px;
    border-bottom-left-radius: 0px;
    cursor: pointer;
    color: $semi-black;
    font-size: 16px;
    &-active {
      background-color: #fff;
      color: $primary;
    }
  }
}

//实体相关：批量追加 disabled
.import-disabled {
  padding: 0 20px;
  color: #c0c4cc;
  cursor: not-allowed;
  background-image: none;
  background-color: #fff;
  border-color: #ebeef5;
}

.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
//不可点击
.not-allowed {
  cursor: not-allowed !important;
}

//测试体验框
.right-test-collapse {
  padding: 12px 24px;
  width: 64px;
  color: $primary;
  font-weight: 600;
  cursor: pointer;
  p {
    width: 16px;
    font-size: 16px;
  }
}

// 重写的 toast message
.toast-pro-base {
  padding: 30px;
  width: 550px;
  opacity: 0.9;
  border-radius: 10px;
  border: none;
  .el-message__icon {
    padding-bottom: 8px;
    &::before {
      font-family: 'AIUI-myapp-iconfont' !important;
      font-style: normal;
      content: '\e621';
      font-size: 30px;
    }
  }
  .el-message__content {
    .toast-container {
      color: #ffffff;
      .title {
        margin-bottom: 10px;
        font-size: 16px;
      }
    }
  }
}
.toast-pro-success {
  background-color: #00ca43;
  .el-message__icon {
    &::before {
      color: #ffffff;
    }
  }
  .el-message__content {
    .toast-container {
      color: #ffffff;
    }
  }
}

.toast-warning-base {
  padding: 10px 14px;
  width: 660px;
  // opacity: 0.9;
  border-radius: 10px;
  border: none;
  .el-message__icon {
    // padding-bottom: 8px;
    width: 40px;
    height: 40px;
    background: url(~@A/images/aiui5/warning2.png) center/100% no-repeat;
    &::before {
      content: ' ';
    }
  }
  .el-message__content {
    .toast-container {
      color: #ffffff;
      line-height: 20px;
      .title {
        margin-bottom: 10px;
        font-size: 14px;
      }
    }
  }
}
.toast-warning-success {
  background-color: #fbe6c5;
  .el-message__icon {
    &::before {
      color: #ffffff;
    }
  }
  .el-message__content {
    .toast-container {
      color: #262626;
    }
  }
}

// tooltip 气泡框样式
$el-tooltip__popper_bg: #e4e7ed;
$el-tooltip__popper_color: #666666;
.el-tooltip__popper.is-dark {
  background-color: $el-tooltip__popper_bg;
  color: $el-tooltip__popper_color;
}
.el-tooltip__popper[x-placement^='top'] .popper__arrow {
  border-top-color: $el-tooltip__popper_bg !important;
  &::after {
    border-top-color: $el-tooltip__popper_bg !important;
  }
}
.el-tooltip__popper[x-placement^='left'] .popper__arrow {
  border-left-color: $el-tooltip__popper_bg !important;
  &::after {
    border-left-color: $el-tooltip__popper_bg !important;
  }
}
.el-tooltip__popper[x-placement^='right'] .popper__arrow {
  border-right-color: $el-tooltip__popper_bg !important;
  &::after {
    border-right-color: $el-tooltip__popper_bg !important;
  }
}
.el-tooltip__popper[x-placement^='bottom'] .popper__arrow {
  border-bottom-color: $el-tooltip__popper_bg !important;
  &::after {
    border-bottom-color: $el-tooltip__popper_bg !important;
  }
}

.new-style-dialog {
  background: linear-gradient(0deg, #ffffff 80%, #dfeaff);
  .el-dialog__header {
    position: relative;
  }
  .el-dialog__title {
    color: #333333;
    font-size: 16px;
    font-weight: 600;
    &::before {
      position: absolute;
      z-index: 1;
      content: ' ';
      width: 6px;
      height: 18px;
      background: $primary;
      border-radius: 3px;
      top: 50%;
      transform: translateY(-50%);
      left: 16px;
    }
  }

  .el-dialog__body {
    .el-form-item__label {
      color: #262b4f;
      font-size: 14px;
      font-weight: 400;
    }
  }
  .el-form-item.is-required:not(.is-no-asterisk) > .el-form-item__label:before {
    /*隐藏✳*/
    content: unset;
  }
}

// confirmProWarning 样式
.confirm-pro-warning {
  overflow: visible;
  &.el-message-box {
    padding-bottom: 14px;
  }
  .el-message-box__headerbtn {
    top: 11px;
    right: 11px;
  }
  .el-message-box__content {
    padding: 42px 0 58px;
  }
  .el-message-box__title {
    visibility: hidden;
  }
  .el-message-box__btns {
    text-align: center;
    border-top: 1px solid #e1e5ed;
    padding-top: 16px;
    button {
      font-size: 14px;
    }
  }
  .el-message-box__headerbtn .el-message-box__close {
    font-size: 22px;
  }
}
.confirm-pro-warning-icon {
  width: 113px;
  height: 111px;
  background: url(~@A/images/aiui5/warning.png) center/100% no-repeat;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  top: -90px;
}
