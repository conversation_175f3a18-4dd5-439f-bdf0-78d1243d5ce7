<template>
  <!-- <div class="time-line">
    <div class="time-line-unit" v-for="(item, index) in updates" :key="index">
      <p class="timestamp">{{ item.time }}</p>
      <div class="card">
        <p class="title" :title="item.title">{{ item.title }}</p>
        <p class="description" :title="item.description">
          {{ item.description }}
        </p>
      </div>
    </div>
  </div> -->
  <div class="updates-container">
    <div class="updates-banner">
      <h2>产品动态</h2>
      <p>为您提供平台产品与业务变更的最新动态</p>
    </div>
    <div class="updates-content">
      <ul class="updates-list">
        <li v-for="(item, index) in updates" :key="index">
          <div class="timer">
            <div class="date">{{ item.time.split('-')[2] }}</div>
            <span>/</span>
            <div class="month-year">
              <p>{{ item.time.split('-')[1] }}月</p>
              <p>{{ item.time.split('-')[0] }}</p>
            </div>
          </div>
          <div class="divider"></div>
          <div class="card">
            <p class="title" :title="item.titleAsTitle" v-html="item.title"></p>
            <p
              class="description"
              :title="item.descriptionAsTitle"
              v-html="item.description"
            ></p>
          </div>
        </li>
      </ul>
      <div class="pagination" v-show="false">
        <el-pagination
          layout="prev, pager, next"
          :total="allUpdates.length"
          :page-size="pageSize"
          @current-change="currentChange"
        >
        </el-pagination>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      updates: [],
      allUpdates: [],
      pageSize: 20,
    }
  },
  created() {
    this.getAllUpdates()
  },
  methods: {
    getAllUpdates() {
      let that = this
      this.$utils.httpGet(
        this.$config.api.RESOURCE_DYNAMICS_LIST,
        {
          // pageIndex: page || this.tableData.page,
          // pageSize: this.tableData.size,
        },
        {
          success: (res) => {
            // let dynamics = [
            //   {
            //     content:
            //       '1上线酷狗音乐，音乐技能升级1上线酷狗音乐，音乐技能升级1上线酷狗音乐，音乐技能升级1上线酷狗音乐，音乐技能升级1上线酷狗音乐，音乐技能升级1',
            //     createTime: 1676476800000,
            //     title: '上线酷狗音乐，音乐技能升级1',
            //     top: 1,
            //   },
            //   {
            //     content:
            //       '2上线酷狗音乐，音乐技能升级1上线酷狗音乐，音乐技能升级1上线酷狗音乐，音乐技能升级1上线酷狗音乐，音乐技能升级1上线酷狗音乐，音乐技能升级1',
            //     createTime: 1686844800000,
            //     title: '上线酷狗音乐，音乐技能升级1',
            //     top: 0,
            //   },
            //   {
            //     content:
            //       '3上线酷狗音乐，音乐技能升级1上线酷狗音乐，音乐技能升级1上线酷狗音乐，音乐技能升级1上线酷狗音乐，音乐技能升级1上线酷狗音乐，音乐技能升级1',
            //     createTime: 1684166400000,
            //     title: '上线酷狗音乐，音乐技能升级1',
            //     top: 0,
            //   },
            //   {
            //     content:
            //       '4上线酷狗音乐，音乐技能升级1上线酷狗音乐，音乐技能升级1上线酷狗音乐，音乐技能升级1上线酷狗音乐，音乐技能升级1上线酷狗音乐，音乐技能升级1',
            //     createTime: 1681574400000,
            //     title: '上线酷狗音乐，音乐技能升级1',
            //     top: 0,
            //   },
            //   {
            //     content:
            //       '5上线酷狗音乐，音乐技能升级1上线酷狗音乐，音乐技能升级1上线酷狗音乐，音乐技能升级1上线酷狗音乐，音乐技能升级1上线酷狗音乐，音乐技能升级1',
            //     createTime: 1676476800000,
            //     title: '上线酷狗音乐，音乐技能升级1',
            //     top: 0,
            //   },
            //   {
            //     content:
            //       '6上线酷狗音乐，音乐技能升级1上线酷狗音乐，音乐技能升级1上线酷狗音乐，音乐技能升级1上线酷狗音乐，音乐技能升级1上线酷狗音乐，音乐技能升级1',
            //     createTime: 1676476800000,
            //     title: '上线酷狗音乐，音乐技能升级1',
            //     top: 0,
            //   },
            //   {
            //     content:
            //       '7上线酷狗音乐，音乐技能升级1上线酷狗音乐，音乐技能升级1上线酷狗音乐，音乐技能升级1上线酷狗音乐，音乐技能升级1上线酷狗音乐，音乐技能升级1',
            //     createTime: 1676476800000,
            //     title: '上线酷狗音乐，音乐技能升级1',
            //     top: 0,
            //   },
            //   {
            //     content:
            //       '8上线酷狗音乐，音乐技能升级1上线酷狗音乐，音乐技能升级1上线酷狗音乐，音乐技能升级1上线酷狗音乐，音乐技能升级1上线酷狗音乐，音乐技能升级1',
            //     createTime: 1676476800000,
            //     title: '上线酷狗音乐，音乐技能升级1',
            //     top: 0,
            //   },
            //   {
            //     content:
            //       '9上线酷狗音乐，音乐技能升级1上线酷狗音乐，音乐技能升级1上线酷狗音乐，音乐技能升级1上线酷狗音乐，音乐技能升级1上线酷狗音乐，音乐技能升级1',
            //     createTime: 1676476800000,
            //     title: '上线酷狗音乐，音乐技能升级1',
            //     top: 0,
            //   },
            //   {
            //     content:
            //       '10上线酷狗音乐，音乐技能升级1上线酷狗音乐，音乐技能升级1上线酷狗音乐，音乐技能升级1上线酷狗音乐，音乐技能升级1上线酷狗音乐，音乐技能升级1',
            //     createTime: 1676476800000,
            //     title: '上线酷狗音乐，音乐技能升级1',
            //     top: 0,
            //   },
            //   {
            //     content:
            //       '11上线酷狗音乐，音乐技能升级1上线酷狗音乐，音乐技能升级1上线酷狗音乐，音乐技能升级1上线酷狗音乐，音乐技能升级1上线酷狗音乐，音乐技能升级1',
            //     createTime: 1676476800000,
            //     title: '上线酷狗音乐，音乐技能升级1',
            //     top: 0,
            //   },
            //   {
            //     content:
            //       '12上线酷狗音乐，音乐技能升级1上线酷狗音乐，音乐技能升级1上线酷狗音乐，音乐技能升级1上线酷狗音乐，音乐技能升级1上线酷狗音乐，音乐技能升级1',
            //     createTime: 1676476800000,
            //     title: '上线酷狗音乐，音乐技能升级1',
            //     top: 0,
            //   },
            //   {
            //     content:
            //       '13上线酷狗音乐，音乐技能升级1上线酷狗音乐，音乐技能升级1上线酷狗音乐，音乐技能升级1上线酷狗音乐，音乐技能升级1上线酷狗音乐，音乐技能升级1',
            //     createTime: 1676476800000,
            //     title: '上线酷狗音乐，音乐技能升级1',
            //     top: 0,
            //   },
            //   {
            //     content:
            //       '14上线酷狗音乐，音乐技能升级1上线酷狗音乐，音乐技能升级1上线酷狗音乐，音乐技能升级1上线酷狗音乐，音乐技能升级1上线酷狗音乐，音乐技能升级1',
            //     createTime: 1676476800000,
            //     title: '上线酷狗音乐，音乐技能升级1',
            //     top: 0,
            //   },
            //   {
            //     content:
            //       '15上线酷狗音乐，音乐技能升级1上线酷狗音乐，音乐技能升级1上线酷狗音乐，音乐技能升级1上线酷狗音乐，音乐技能升级1上线酷狗音乐，音乐技能升级1',
            //     createTime: 1676476800000,
            //     title: '上线酷狗音乐，音乐技能升级1',
            //     top: 0,
            //   },
            //   {
            //     content:
            //       '16上线酷狗音乐，音乐技能升级1上线酷狗音乐，音乐技能升级1上线酷狗音乐，音乐技能升级1上线酷狗音乐，音乐技能升级1上线酷狗音乐，音乐技能升级1',
            //     createTime: 1676476800000,
            //     title: '上线酷狗音乐，音乐技能升级1',
            //     top: 0,
            //   },
            //   {
            //     content:
            //       '17上线酷狗音乐，音乐技能升级1上线酷狗音乐，音乐技能升级1上线酷狗音乐，音乐技能升级1上线酷狗音乐，音乐技能升级1上线酷狗音乐，音乐技能升级1',
            //     createTime: 1676476800000,
            //     title: '上线酷狗音乐，音乐技能升级1',
            //     top: 0,
            //   },
            //   {
            //     content:
            //       '18上线酷狗音乐，音乐技能升级1上线酷狗音乐，音乐技能升级1上线酷狗音乐，音乐技能升级1上线酷狗音乐，音乐技能升级1上线酷狗音乐，音乐技能升级1',
            //     createTime: 1676476800000,
            //     title: '上线酷狗音乐，音乐技能升级1',
            //     top: 0,
            //   },
            //   {
            //     content:
            //       '19上线酷狗音乐，音乐技能升级1上线酷狗音乐，音乐技能升级1上线酷狗音乐，音乐技能升级1上线酷狗音乐，音乐技能升级1上线酷狗音乐，音乐技能升级1',
            //     createTime: 1676476800000,
            //     title: '上线酷狗音乐，音乐技能升级1',
            //     top: 0,
            //   },
            //   {
            //     content:
            //       '20上线酷狗音乐，音乐技能升级1上线酷狗音乐，音乐技能升级1上线酷狗音乐，音乐技能升级1上线酷狗音乐，音乐技能升级1上线酷狗音乐，音乐技能升级1',
            //     createTime: 1676476800000,
            //     title: '上线酷狗音乐，音乐技能升级1',
            //     top: 0,
            //   },
            // ]
            // this.allUpdates = (dynamics || []).map((item) => {
            //   return {
            //     title: item.title,
            //     description: item.content,
            //     time: that.$utils.dateFormat(item.createTime, 'yyyy-MM-dd'),
            //   }
            // })
            this.allUpdates = (res.data.dynamics || [])
              .sort((obj1, obj2) => {
                if (obj1.top < obj2.top) {
                  return 1
                } else if (obj1.top > obj2.top) {
                  return -1
                } else {
                  return 0
                }
              })
              .map((item) => {
                return {
                  title: item.title,
                  titleAsTitle: this.getAsTitle(item.title),
                  description: item.content,
                  descriptionAsTitle: this.getAsTitle(item.content),
                  time: that.$utils.dateFormat(item.createTime, 'yyyy-MM-dd'),
                }
              })
            this.getUpdates(1)
          },
          error: (err) => {},
        }
      )
    },

    getAsTitle(content) {
      let div = document.createElement('div')
      div.innerHTML = content
      return div.innerText || ''
    },

    getUpdates(page) {
      this.updates = this.allUpdates.slice(
        (page - 1) * this.pageSize,
        (page - 1) * this.pageSize + this.pageSize
      )
    },
    currentChange(val) {
      console.log('currentChange', val)
      this.getUpdates(val)
    },
  },
}
</script>
<style lang="scss" scoped>
.updates-container {
  .updates-banner {
    height: 260px;
    background: url(~@A/images/aiui/updates/updates-bg.png) center/cover
      no-repeat;
    padding-top: 98px;
    text-align: center;
    h2 {
      font-size: 36px;
      line-height: 36px;
      font-weight: 600;
      color: #000000;
    }
    p {
      font-size: 18px;
      line-height: 18px;
      font-weight: 400;
      color: #000000;
      margin-top: 19px;
    }
  }
  .updates-content {
    padding-bottom: 40px;

    .updates-list {
      > li {
        width: 1200px;
        height: 124px;
        border-bottom: 1px solid #e8e9ed;
        display: flex;
        align-items: center;
        margin: 0 auto;
        .timer {
          width: 183px;
          padding-left: 49px;
          display: flex;
          align-items: center;
          .date {
            font-size: 40px;
            font-weight: 600;
            color: #252b3a;
          }
          > span {
            font-size: 23px;
            font-weight: 400;
            color: #252b3a;
            opacity: 0.2;
            margin: 0 2px;
          }
          .month-year {
            font-size: 16px;
            font-weight: 400;
            text-align: left;
            color: #252b3a;
            line-height: 19px;
          }
        }
        .divider {
          width: 1px;
          height: 68px;
          background: #e8e9ed;
        }
        .card {
          padding-left: 34px;
          padding-right: 58px;
          flex: 1;
          background: #ffffff;

          .title {
            font-size: 18px;
            font-weight: 600;
            color: #252b3a;
            line-height: 19px;
            margin-bottom: 17px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          .description {
            font-size: 14px;
            font-weight: 400;
            color: #252b3a;
            line-height: 26px;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            overflow: hidden;
          }
        }
      }
    }
  }

  .pagination {
    text-align: right;
    width: 1200px;
    margin: 50px auto 0;
  }
}
</style>
