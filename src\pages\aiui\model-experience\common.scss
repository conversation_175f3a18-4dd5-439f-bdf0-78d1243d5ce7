.chain-wrapper {
  position: relative;
  .indicator {
    position: absolute;
    z-index: 1;
    top: 10px;
    width: 24px;
    height: 82px;
    background: #ffffff;
    border-radius: 4px;
    color: #5f6286;

    font-size: 12px;
    text-align: center;
    line-height: 82px;
    cursor: pointer;
    i {
      font-weight: bold !important;
    }
  }
  .indicator-pre {
    left: -32px;
    box-shadow: 4px 0px 8px 0px rgba(139, 165, 222, 0.5);
  }
  .indicator-next {
    right: -32px;
    box-shadow: -4px 0px 8px 0px rgba(139, 165, 222, 0.5);
  }
}
.chain-list {
  // overflow: hidden;
  overflow: visible;
  display: flex;
  align-items: center;
  //   justify-content: space-between;
  margin-top: 2px;
  padding: 10px 0;
  // max-width: 1088px;
  // width: 1088px;
  .chain-unit {
    cursor: pointer;
    position: relative;
    height: 82px;
    padding: 12px 0 0 16px;
    box-sizing: border-box;
    background: linear-gradient(0deg, #dfeaff, #ffffff);
    border: 1px solid #ffffff;
    border-radius: 8px;
    box-shadow: 0px 2px 8px 0px rgba(124, 159, 245, 0.5);

    &:hover:not(.disabled),
    &.active {
      border: 1px solid $primary;
      background: #fff;
      .chain-title {
        color: $primary;
      }
    }
    &.disabled {
      cursor: not-allowed !important;
      .chain-title,
      .chain-desc {
        color: #c0c4cc !important;
      }
    }
    &.small {
      height: 52px;
      padding: 16px 0 0 16px;
    }
    .chain-title {
      font-size: 14px;
      font-weight: 500;
      color: #05133b;
      line-height: 20px;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }
    .chain-desc {
      margin-top: 4px;
      font-size: 12px;
      font-weight: 400;
      color: #7b839c;
      line-height: 17px;
      display: -webkit-box;
      text-overflow: ellipsis;
      overflow: hidden;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }

    .chain-check {
      position: absolute;
      z-index: 1;
      width: 15px;
      height: 15px;
      // background: url(~@A/images/sparkcons/<EMAIL>) center/contain
      //   no-repeat;
      top: 11px;
      right: 11px;
    }
    .chain-line {
      position: absolute;
      z-index: 1;
      left: 0;
      top: 17px;
      width: 4px;
      height: 13px;
      // background: linear-gradient(180deg, #35aeff, #99e4ff);
      border-radius: 0px 2px 2px 0px;
    }
    .chain-icon {
      position: absolute;
      z-index: 1;
      width: 50px;
      height: 50px;
      right: 0;
      top: 0;
      background-position: center;
      background-repeat: no-repeat;
      background-size: contain;
    }
    .chain-icon2 {
      position: absolute;
      z-index: 1;
      width: 30px;
      height: 30px;
      right: 8px;
      top: 4px;
      background-position: center;
      background-repeat: no-repeat;
      background-size: contain;
    }
  }
}
.pagination {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 10px;
  li + li {
    margin-left: 4px;
  }
  li {
    width: 7px;
    height: 4px;
    background: #d0dbed;
    border-radius: 2px;
    cursor: pointer;
    &.active {
      width: 16px;
      background: #b9c5d8;
    }
  }
}
.chain-divider {
  position: relative;
  margin-top: 14px;
  .chain-divider-line {
    width: 100%;
    height: 1px;
    background: #c9cfdd;
    transform: scaleY(0.5);
  }
  span {
    font-size: 12px;
    font-weight: 400;
    color: #a2a8b7;
    line-height: 17px;
    display: inline-block;
    position: absolute;
    z-index: 1;
    left: 50%;
    top: -8px;
    transform: translateX(-50%);
    background: #f1f5fc;
    padding: 0 34px;
  }
}
