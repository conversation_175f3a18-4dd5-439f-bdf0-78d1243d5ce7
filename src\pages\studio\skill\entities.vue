<template>
  <os-page :options="pageOptions">
    <studio-skill-header-right slot="btn" />
    <div class="os-scroll">
      <div class="mgt32 mgb24" @keyup.enter="searchEntities">
        <el-input
          class="search-area"
          placeholder="搜索引用的实体"
          v-model="entitySearchName"
        >
          <i
            slot="suffix"
            class="el-input__icon el-icon-search search-area-btn"
            @click="searchEntities"
          />
        </el-input>
      </div>

      <os-table
        class="skill-entities-table gutter-table-style secondary-table"
        :tableData="tableData"
        :height="'calc(100vh - 203px)'"
        @change="getEntities"
        @edit="toEdit"
      >
        <el-table-column type="index" width="50">
          <template slot-scope="scope">
            {{ (tableData.page - 1) * tableData.size + scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column width="174" prop="zhName" label="实体名称">
          <template slot-scope="scope">
            <div class="intent-zhname ib" @click="toEdit(scope.row)">
              {{ scope.row.zhName }}
            </div>
            <div class="intent-tag ib" v-if="scope.row.type === 1">官</div>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="实体标识"> </el-table-column>
        <el-table-column prop="description" label="描述"> </el-table-column>
        <el-table-column prop="example" label="示例"> </el-table-column>
        <el-table-column prop="count" label="词条数"> </el-table-column>
      </os-table>
    </div>
  </os-page>
</template>

<script>
export default {
  name: 'skill-entities',
  data() {
    return {
      pageOptions: {
        title: '引用的实体',
        loading: false,
      },
      entitySearchName: '',
      tableData: {
        loading: true,
        total: 0,
        page: 1,
        size: 10,
        handles: ['edit'],
        handleColumnText: '操作',
        list: [],
      },
    }
  },
  computed: {
    businessId() {
      return this.$store.state.studioSkill.id
    },
  },
  created() {
    this.getEntities()
  },
  methods: {
    getEntities(page) {
      this.tableData.loading = true
      this.$utils.httpGet(
        this.$config.api.STUDIO_SKILL_ENTITYS,
        {
          businessId: this.businessId,
          pageIndex: page || this.tableData.page,
          pageSize: this.tableData.size,
          search: this.entitySearchName,
          type: 0,
        },
        {
          success: (res) => {
            this.tableData.list = Array.prototype.map.call(
              res.data.entities,
              function (item, index) {
                if (item.type === 1) {
                  item.noEdit = true
                }
                return item
              }
            )
            this.tableData.total = res.data.count
            this.tableData.page = res.data.pageIndex
            this.tableData.size = res.data.pageSize
            this.tableData.loading = false
          },
          error: (err) => {
            console.log('page=>>')
            console.log(err)
          },
        }
      )
    },
    searchEntities() {
      this.getEntities(1)
    },
    toEdit(entity) {
      if (entity.type === 1) {
        return this.$message.warning('该实体为官方实体，不可编辑')
      }
      let routeData = this.$router.resolve({
        name: 'entity',
        params: { entityId: entity.id },
      })
      window.open(routeData.href, '_blank')
    },
  },
  components: {},
}
</script>

<style lang="scss" scoped>
.search-area-btn {
  cursor: pointer;
}
.intent-handle-group {
  position: relative;
  margin-right: -3px;
  &::after {
    position: absolute;
    content: ' ';
    width: 1px;
    height: 100%;
    top: 0;
    right: -1px;
    background-color: $grey3;
  }
}
.intent-zhname {
  margin-right: 7px;
  cursor: pointer;
  font-weight: 600;
}
</style>
<style lang="scss">
.skill-entities-table {
  tr:hover {
    .intent-zhname {
      color: $primary;
    }
  }
  .el-table .ic-r-edit {
    color: $primary;
  }
}
</style>
