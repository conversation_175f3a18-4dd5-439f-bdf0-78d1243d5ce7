<template>
  <ul class="function-container">
    <li v-for="(item, index) in cells" :key="index">
      <div
        class="icon-tip"
        :style="{
          backgroundImage:
            'url(' +
            require(`../../../../../assets/images/solution/smart-hardware/icon-tips/${label}_${
              index + 1
            }.png`) +
            ')',
        }"
      ></div>
      <p>{{ item.title }}</p>
      <div>{{ item.introduction }}</div>
    </li>
  </ul>
</template>
<script>
export default {
  props: {
    cells: {
      required: true,
      type: Array,
      default: [],
    },
    label: {
      required: true,
      type: String,
      default: "",
    },
  },
};
</script>
<style lang="scss" scoped>
.function-container {
  display: flex;
  flex-wrap: wrap;
  background: rgba(171, 212, 255, 0.08);
  padding-top: 61px;
  padding-bottom:69px;
  padding-left:64px;
  max-width: 750px;
  margin:0 auto;
  >li {
    width: 284px;
    text-align: center;
    .icon-tip {
      width: 80px;
      height: 80px;
      background-repeat: no-repeat;
      background-size: contain;
      margin: 0 auto;
    }
    p {
      font-size: 36px;
      font-weight: 400;
      color: #444444;
      line-height: 50px;
      margin-top: 26px;
    }
    div:last-of-type {
      font-size: 24px;
      font-weight: 400;
      color: #999999;
      line-height: 37px;
    }
  }
  >li:nth-child(even){
    margin-left: 54px;
  }
  >li:not(:last-child){
    margin-bottom: 90px;
  }
}
</style>
