const rules = {
  required: (msg, trigger = 'blur') => {
    return {
      required: true,
      message: msg,
      trigger: trigger,
    }
  },
  lengthLimit: (min = 0, max = 10, msg, trigger = 'blur') => {
    return {
      min: min,
      max: max,
      message: msg,
      trigger: trigger,
    }
  },
  baseRegLimit: (msg = '只支持中文/英文/数字/下划线格式', trigger = 'blur') => {
    return {
      pattern: /^[\u4e00-\u9fffa-zA-Z0-9_]{0,}$/,
      message: msg,
      trigger: trigger,
    }
  },
  limitForSkillIntent: (
    msg = '只支持中文/英文/数字/小数点/下划线格式',
    trigger = 'blur'
  ) => {
    return {
      pattern: /^[\u4e00-\u9fffa-zA-Z0-9_.]{0,}$/,
      message: msg,
      trigger: trigger,
    }
  },
  baseRegLimitForSkillZhName: (
    msg = '只支持中文/英文/数字/小数点/短横线/下划线格式',
    trigger = 'blur'
  ) => {
    return {
      pattern: /^[\u4e00-\u9fffa-zA-Z0-9_.-]{0,}$/,
      message: msg,
      trigger: trigger,
    }
  },
  chineseRegLimit: (trigger = 'blur') => {
    return {
      pattern: /^[\u4e00-\u9fff]{0,}$/,
      message: '只支持汉字格式',
      trigger: 'blur',
    }
  },
  englishRegLimit: (msg = '只支持英文/数字/下划线格式', trigger = 'blur') => {
    return {
      pattern: /^[a-zA-Z0-9_]{0,}$/,
      message: '只支持英文/数字/下划线格式',
      trigger: 'blur',
    }
  },
  englishReglimitForSkillIntent: (
    msg = '只支持英文/数字/小数点/下划线格式',
    trigger = 'blur'
  ) => {
    return {
      pattern: /^[a-zA-Z0-9_.]{0,}$/,
      message: '只支持英文/数字/小数点/下划线格式',
      trigger: 'blur',
    }
  },
  englishRegLimitV2: (msg = '只支持英文/数字/小数点格式', trigger = 'blur') => {
    return {
      pattern: /^[a-zA-Z0-9.]{0,}$/,
      message: '只支持英文/数字/小数点格式',
      trigger: 'blur',
    }
  },
  skillName: () => {
    return [
      rules.required('技能标识不能为空'),
      rules.lengthLimit(1, 15, '技能标识长度不能超过15个字符'),
      rules.englishRegLimit(),
    ]
  },
  skillZhName: () => {
    return [
      rules.required('名称不能为空'),
      rules.lengthLimit(1, 32, '技能中文名长度不能超过32个字符'),
      rules.baseRegLimitForSkillZhName(),
    ]
  },
  skillCallName: () => {
    return [
      rules.required('调用名称不能为空'),
      rules.lengthLimit(3, 8, '调用名称为3-8个汉字'),
      rules.chineseRegLimit(),
    ]
  },
  entityName: () => {
    return [
      rules.required('实体英文标识不能为空'),
      rules.lengthLimit(1, 15, '实体英文标识长度不能超过15个字符'),
      rules.englishRegLimit(),
    ]
  },
  entityValue: () => {
    return [
      rules.required('实体中文名称不能为空'),
      rules.lengthLimit(1, 32, '实体中文名称长度不能超过32个字符'),
      rules.baseRegLimit(),
    ]
  },
  judgeUtteranceParams: (
    value,
    length = 200,
    judgeObj = '语料',
    sumOfMiddleBracketsAndSmallBrackets = 15,
    nesting = false
  ) => {
    if (value.length > length) {
      return {
        valid: false,
        data: { message: `${judgeObj}长度不能大于${length}个字符` },
      }
    }

    let result = value.replace(/[^\[\]]/g, '').replace(/(\[\])/g, '')
    if (!nesting && result) {
      return {
        valid: false,
        data: { message: '中括号必须成对出现' },
      }
    }

    result = value.replace(/[^\(\)]/g, '').replace(/(\(\))/g, '')
    if (!nesting && result) {
      return {
        valid: false,
        data: { message: '小括号必须成对出现' },
      }
    }

    result = value.replace(/[^\{\}]/g, '').replace(/(\{\})/g, '')
    if (result) {
      return {
        valid: false,
        data: { message: '花括号必须成对出现' },
      }
    }

    let flowerBrackets = value.match(/\{(.*?)\}/g) || []
    let middleBrackets = value.match(/\[(.*?)\]/g) || []
    let smallBrackets = value.match(/\((.*?)\)/g) || []

    if (
      !nesting &&
      middleBrackets.length + smallBrackets.length >
        sumOfMiddleBracketsAndSmallBrackets
    ) {
      return {
        valid: false,
        data: {
          message: `中括号和小括号个数之和最多支持${sumOfMiddleBracketsAndSmallBrackets}个`,
        },
      }
    }
    for (let i = 0; i < flowerBrackets.length; i++) {
      let item = flowerBrackets[i].substring(1, flowerBrackets[i].length - 1)
      if (item == '') {
        return {
          valid: false,
          data: { message: '花括号内容不能为空' },
        }
      } else if (item.length > 32) {
        return {
          valid: false,
          data: { message: '花括号内容长度不能超过32个字符' },
        }
      } else if (!/^[a-zA-Z0-9#._]{0,}$/.test(item)) {
        return {
          valid: false,
          data: { message: '花括号内容仅支持字母/数字/下划线/小数点/井号' },
        }
      }
    }

    if (!nesting) {
      for (let i = 0; i < middleBrackets.length; i++) {
        let item = middleBrackets[i].substring(1, middleBrackets[i].length - 1)
        if (item == '') {
          return {
            valid: false,
            data: { message: '中括号内容不能为空' },
          }
        } else {
          let flag = /\(|\)/.test(item)
          if (flag) {
            return {
              valid: false,
              data: { message: '中括号内容不能包含小括号' },
            }
          }
        }

        // 中括号拆分
        let splitWord = item.split('|')

        for (let i = 0; i < splitWord.length; i++) {
          if (splitWord[i] === '') {
            return {
              valid: false,
              data: { message: ' | 符号两边内容不能为空' },
            }
          }
        }
      }

      for (let i = 0; i < smallBrackets.length; i++) {
        let item = smallBrackets[i].substring(1, smallBrackets[i].length - 1)
        if (item === '') {
          return {
            valid: false,
            data: { message: '小括号内容不能为空' },
          }
        } else {
          let flag = /[\[\]]/.test(item)
          if (flag) {
            return {
              valid: false,
              data: { message: '小括号内容不能包含中括号' },
            }
          }
        }

        // 小括号拆分
        let splitWord = item.split('|')

        for (let i = 0; i < splitWord.length; i++) {
          if (splitWord[i] === '') {
            return {
              valid: false,
              data: { message: ' | 符号两边内容不能为空' },
            }
          }
        }
      }

      // 获取没有可选/必选符的语句，检测是否包含或 符号
      let utterance = value.replace(/\[(.*?)\]/g, '').replace(/\((.*?)\)/g, '')
      if (/\|/.test(utterance)) {
        return {
          valid: false,
          data: { message: ' | 符号只能出现在中括号或小括号中' },
        }
      }
    }

    if (judgeObj === '内容') {
      let splitWordCount = value.split('|')
      if (splitWordCount.length > 6) {
        return {
          valid: false,
          data: { message: ' | 符号不得超过5' },
        }
      }
    }
    // 语料不能全部为可选符
    result = value.replace(/\[(.*?)\]/g, '')
    if (result === '') {
      return {
        valid: false,
        data: { message: '不能全部为可选符' },
      }
    }

    return {
      valid: true,
      data: { message: '' },
    }
  },
  repositoryCommonReg: (
    msg = '只支持中文/英文/数字/空格/英文单引号/英文句号',
    trigger = ['blur', 'change']
  ) => {
    return {
      pattern: /^[\u4e00-\u9fffa-zA-Z0-9 \.']{0,}$/,
      message: msg,
      trigger: trigger,
    }
  },
  onlyNumAndEnglishChar: (
    msg = '只支持英文/数字和英文符号',
    trigger = ['blur', 'change']
  ) => {
    return {
      pattern: /^[\w\d;'":\\,\/\|\.#@!\$%\^&\*]{0,}$/,
      message: msg,
      trigger: trigger,
    }
  },
}

export default {
  install(Vue) {
    Vue.prototype.$rules = rules
  },
}
