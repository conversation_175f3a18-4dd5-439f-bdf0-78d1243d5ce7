<template>
  <!-- 查看知识库溯源引用 -->
  <el-dialog
    title="溯源引用"
    :visible.sync="dialog.show"
    width="50%"
    custom-class="linkDialog"
  >
    <div style="padding-bottom: 20px">
      <!-- <div class="point-card" v-for="(item, index) in resLinkJson" :key="index">
        <div class="card-index">{{ index + 1 }}</div>
        <div class="card-content">{{ item.content }}</div>
        <ul>
          <li>来源文档：{{ item.docName }}</li>
          <li>相关段落：{{ item.detail }}</li>
          <li>相关性得分：{{ item.score }}</li>
        </ul>
      </div> -->
      <point-card
        v-for="(item, index) in resLinkJson"
        :key="index"
        :index="index"
        :item="item"
        :show="dialog.show"
      ></point-card>
    </div>
  </el-dialog>
</template>
<script>
import PointCard from './pointCard.vue'
export default {
  props: {
    dialog: Object,
    json: Array,
  },
  data() {
    return {
      resLinkJson: [],
    }
  },
  watch: {
    'dialog.show'(val) {
      if (val) {
        this.resLinkJson = this.json
      } else {
        this.$nextTick(() => {
          this.resLinkJson = []
        })
      }
    },
  },
  components: { PointCard },
}
</script>
<style lang="scss">
.linkDialog {
  .el-dialog__body {
    padding: 0 32px 0 32px;
  }
}
</style>
<style lang="scss" scoped></style>
