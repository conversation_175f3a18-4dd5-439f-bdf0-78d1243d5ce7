<template>
  <div class="main-content">
    <section class="main-content-banner">
      <div class="banner-text">
        <h2>RK3588 AIUI多模态开发套件</h2>
        <p class="pc-show banner-text-content">
          设备算力高，内部集成多模态交互引擎及AIUI全链路交互能力，适用于公共场景下<br />的复杂高噪人机交互。
        </p>
        <div class="hor-btn">
          <div class="banner-text-button" @click="toConsole">合作咨询</div>
          <div class="banner-text-buy" @click="toBuy">立即购买</div>
        </div>
      </div>
    </section>
    <section class="section section-2">
      <div class="section-title">应用场景</div>
      <div class="section-item">
        <ul>
          <li v-for="(item, index) in app_scenario" :key="index" class="app">
            <img :src="item.src" :alt="item.alt" />
            <p class="app-text">{{ item.alt }}</p>
          </li>
        </ul>
      </div>
    </section>
    <section class="section section-3">
      <div class="section-title">产品功能</div>
      <div class="section-item"></div>
    </section>

    <section class="section section-5">
      <div class="section-title" style="margin-bottom: 50px !important">
        实物图片
      </div>
      <div class="section-item">
        <div class="section-item-title title-1">3588主机盒子</div>
        <div class="section-item-title title-2">
          双目八麦麦克风阵列结构件（根据不同项目提供不同规格）
        </div>
      </div>
    </section>
    <section class="section section-6">
      <div class="section-title">主板接口说明</div>
      <div class="section-item"></div>
      <div class="section-item-details"></div>
    </section>
    <section class="section section-7">
      <div class="section-title">接线示意图</div>
      <div class="section-item"></div>
    </section>
    <section class="section section-8">
      <div class="section-title">硬件参数</div>
      <div class="section-item">
        <ul>
          <li v-for="(item, index) in hard_list" :key="index" class="item">
            <div class="left">
              <img :src="item.src" />
            </div>
            <div class="right">
              <div class="title">{{ item.name }}</div>
              <span>{{ item.content }}</span>
            </div>
          </li>
        </ul>
      </div>
    </section>
    <!-- <section class="section section-4">
      <div class="section-title">产品清单</div>
      <div class="section-item">
        <div class="left">
          <div class="top">
            <div><span></span>主机盒子</div>
            <div><span></span>麦克风板</div>
            <div><span></span>摄像头</div>
          </div>
          <div class="bottom">
            <div><span></span>麦克风排线（带屏蔽）</div>
            <div><span></span>电源适配器</div>
          </div>
        </div>
        <div class="right">
          <div class="top">
            <div><span></span>唤醒SDK，集成前端声学算法 AIUI</div>
          </div>
          <div class="bottom">
            <div><span></span>SDK，集成云端交互能力</div>
          </div>
        </div>
      </div>
    </section> -->
    <section class="section section-81">
      <div class="section-title">产品清单</div>
      <div class="section-item">
        <div v-for="item in product_list" class="item" :key="item.index">
          <div class="title">{{ item.title }}</div>
          <!-- <p v-html="item.sub_title"></p> -->
          <ul class="sub_item">
            <li v-for="it in item.sub_title" :key="it">{{ it }}</li>
          </ul>
        </div>
      </div>
    </section>
    <section class="section section-9">
      <div class="section-title">开发材料</div>
      <div class="section-item">
        <div class="item">
          <a
            v-for="item in develop_doc"
            :key="item.index"
            @click="toDoc(item.link)"
            style="color: #666666"
            >{{ item.name }}</a
          >
        </div>
      </div>
    </section>
    <div class="corp">
      <div class="section-wrap">
        <div class="section-title2">
          <p class="section-title-contact">立即联系您的专属顾问</p>
          <p class="section-desc2">免费咨询专属顾问 为您量身定制产品推荐方案</p>
        </div>

        <div class="section-item" style="padding-top: 39px; text-align: left">
          <div class="section-button" @click="toConsole">合作咨询</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import corp from '@P/aiui/solution-aiui/components/corp.vue'

export default {
  layout: 'aiuiHome',
  data() {
    return {
      app_scenario: [
        {
          alt: '大屏一体机',
          src: require('../../../../assets/images/solution/soft-hardware/3588/section-2-1.png'),
        },
        {
          alt: '自助服务终端',
          src: require('../../../../assets/images/solution/soft-hardware/3588/section-2-2.png'),
        },
        {
          alt: '公交站台大屏',
          src: require('../../../../assets/images/solution/soft-hardware/3588/section-2-3.png'),
        },
        {
          alt: '大屏调度',
          src: require('../../../../assets/images/solution/soft-hardware/3588/section-2-4.png'),
        },
      ],
      hard_list: [
        {
          name: 'CPU',
          src: require('../../../../assets/images/solution/soft-hardware/3588/section-8-1.png'),
          content: '四核Cortex-A76+四核Cortex-A55',
        },
        {
          name: '主频',
          src: require('../../../../assets/images/solution/soft-hardware/3588/section-8-2.png'),
          content: '2.4GHz + 1.8GHz',
        },
        {
          name: '操作系统',
          src: require('../../../../assets/images/solution/soft-hardware/3588/section-8-3.png'),
          content: 'Andriod',
        },
        {
          name: '电压',
          src: require('../../../../assets/images/solution/soft-hardware/3588/section-8-4.png'),
          content: 'DC12V',
        },
        {
          name: '电流',
          src: require('../../../../assets/images/solution/soft-hardware/3588/section-8-5.png'),
          content: '典型值0.13A,max0.24A',
        },
        {
          name: '扬声器功率',
          src: require('../../../../assets/images/solution/soft-hardware/3588/section-8-6.png'),
          content: '双5W 4Ω',
        },
        {
          name: 'WIFI',
          src: require('../../../../assets/images/solution/soft-hardware/3588/section-8-8.png'),
          content: 'IEEE 802.11a/b/g/n/ac MIMO',
        },
        {
          name: '麦克风',
          src: require('../../../../assets/images/solution/soft-hardware/3588/section-8-9.png'),
          content: '默认模拟硅麦',
        },
        {
          name: '灵敏度',
          src: require('../../../../assets/images/solution/soft-hardware/3588/section-8-10.png'),
          content: '-38dB',
        },
        {
          name: '信噪比',
          src: require('../../../../assets/images/solution/soft-hardware/3588/section-8-11.png'),
          content: '66dB',
        },
        {
          name: '主板尺寸',
          src: require('../../../../assets/images/solution/soft-hardware/3588/section-8-12.png'),
          content: '250*170*52mm',
        },
        {
          name: '工作环境',
          src: require('../../../../assets/images/solution/soft-hardware/3588/section-8-7.png'),
          content: '-10~75°，相对湿度≤80%',
        },
      ],
      develop_doc: [
        {
          name: ' • 《RK3588 AIUI多模态开发套件产品规格书》',
          link: 'https://aiui-doc.xf-yun.com/project-1/doc-361/',
        },
        {
          name: ' • 《RK3588多模态套件使用手册》',
          link: 'https://aiui-doc.xf-yun.com/project-1/doc-362/',
        },
        {
          name: ' • 《多模态设备视频传输协议》',
          link: 'https://aiui-doc.xf-yun.com/project-1/doc-360/',
        },
        {
          name: ' • 《3588串口通信协议手册》',
          link: 'https://aiui-doc.xf-yun.com/project-1/doc-367/',
        },
      ],
      product_list: [
        {
          title: '硬件',
          sub_title: [
            '评估板套件',
            '麦克风板',
            '麦克风',
            '排线回采线',
            'USB线',
            '电源适配器',
          ],
        },
        {
          title: '软件',
          sub_title: [
            '唤醒SDK，集成前端声学算法',
            'AIUI SDK，集成云端交互能力',
          ],
        },
      ],
    }
  },
  methods: {
    toConsole() {
      // 判断是否有百度SEM营销参数，有的话，提交表单页需要带上
      const search = window.location.search
      if (search) {
        window.open(`/solution/apply/29${search}`)
      } else {
        window.open('/solution/apply/29')
      }
    },
    toBuy() {
      window.open('https://www.aifuwus.com/onstage/cmddetail?id=4866')
    },
    toDoc(link) {
      window.open(link)
    },
  },
  components: { corp },
}
</script>

<style lang="scss" scoped>
@import '../../../../assets/scss/screen-and-lamp.scss';
@media screen and (min-width: 751px) {
  .main-content {
    &-banner {
      background: url(~@A/images/solution/soft-hardware/3588/banner_bg.png)
        center no-repeat;
      background-size: cover;
      height: 500px;
      overflow: hidden;
      width: 100%;

      .banner-text {
        max-width: 1200px;
        color: #fff;
        height: 100%;
        margin: auto;

        .hor-btn {
          display: flex;
          flex-direction: row;
          justify-content: flex-start;
          margin-top: 25px;
          div:nth-child(2) {
            margin-left: 30px;
          }
        }

        &-button {
          font-size: 16px;
          text-align: center;
          font-weight: 400;

          border: none;
          color: #fff;
          cursor: pointer;
          transition: 0.6s;

          width: 180px;
          height: 50px;
          background: $primary;
          border-radius: 4px;
          line-height: 50px;
        }

        &-buy {
          font-size: 16px;
          text-align: center;
          font-weight: 400;
          width: 180px;
          height: 50px;
          line-height: 50px;
          color: $primary;
          cursor: pointer;
          transition: 0.6s;
          background: #fff;
          border-radius: 4px;
          box-shadow: 1px 1px 2.91px 0px #2173ff;
        }

        h2 {
          color: #181818;
          font-size: 44px;
          font-weight: 600;
          line-height: 96px;
          padding-top: 122px;
          margin-bottom: 0px;
        }
        p {
          color: #444444;
          font-size: 16px;
          font-weight: 400;
          line-height: 24px;
          // margin-top: 25px;
        }

        // .banner-text-content {
        //   width: 570px;
        //   font-size: 16px;
        //   font-weight: 400;
        //   color: rgba(255, 255, 255, 0.86);
        //   line-height: 30px;
        // }
      }
    }
  }

  .section-title {
    font-size: 34px;
    font-weight: bold;
    color: #333;
    margin-top: 0 !important;
    margin-bottom: 30px !important;
  }

  .section-sub-title {
    font-size: 16px;
    font-weight: 400;
    text-align: center;
    color: #666666;
    margin-bottom: 60px !important;
  }

  .app-text {
    color: #666;
  }

  .section-2 {
    .section-item {
      margin-top: 50px !important;
      margin-bottom: 50px;

      > ul {
        display: flex;
        justify-content: space-between !important;

        li {
          width: 23% !important;
          position: relative;

          img {
            width: 100%;
          }

          p {
            position: absolute;
            left: 50%;
            top: 0;
            transform: translate(-50%, 0);
            color: #212122;
          }
        }
      }
    }
  }

  .section-3 {
    background: url('../../../../assets/images/solution/soft-hardware/3588/banner2.png')
      center no-repeat !important;
    background-size: cover !important;
    // height: 786px;
    width: 100%;

    .section-item {
      background: url('../../../../assets/images/solution/soft-hardware/3588/section-3.png')
        center no-repeat !important;
      background-size: contain !important;
      height: 475px;
      margin-top: 45px !important;
    }

    .section-title {
      margin-bottom: 0 !important;
    }
  }

  .section-4 {
    background: white !important;
    height: 328px !important;
    width: 100%;

    .section-title {
      margin-bottom: 0 !important;
    }

    .section-item {
      margin-top: 0 !important;
      padding: 0 !important;
      background: #fff !important;
      height: 100px;
      margin-top: 64px !important;

      .left {
        width: 418px;
        height: 100px;
        background: url('../../../../assets/images/solution/soft-hardware/3588/section-4-1.png')
          center no-repeat !important;
        background-size: cover !important;
        margin-right: 33px;
      }

      .right {
        width: 418px;
        height: 100px;
        background: url('../../../../assets/images/solution/soft-hardware/3588/section-4-2.png')
          center no-repeat !important;
        background-size: cover !important;
        margin-left: 33px;
      }

      .left,
      .right {
        padding-left: 160px;
        padding-right: 54px;
        padding-top: 30px;

        .top,
        .bottom {
          color: #666666;
          font-size: 12px;
          display: flex;
          align-items: center;
          justify-content: space-between;

          & > div {
            display: flex;
            align-items: center;
          }

          span {
            display: inline-block;
            width: 5px;
            height: 5px;
            background: #d3d7de;
            border-radius: 50%;
            margin-right: 6px;
          }
        }

        .bottom {
          margin-top: 4px;
        }
      }
    }
  }

  .section-5 {
    background: url('../../../../assets/images/solution/soft-hardware/3588/banner3.png')
      center no-repeat !important;
    background-size: cover !important;
    width: 100%;
    height: 640px;
    max-width: 100% !important;
    position: relative;

    .section-title {
      color: #fff;
    }

    .section-item {
      background: url('../../../../assets/images/solution/soft-hardware/3588/section-5.png')
        center/contain no-repeat !important;
      // background-size: cover !important;
      width: 1200px;
      height: 377px;
      margin: 0 auto;
      position: relative;
      .section-item-title {
        font-size: 18px;
        font-weight: 400;
        text-align: center;
        color: #ffffff;
        line-height: 60px;
        position: absolute;
        z-index: 1;
        top: -6px;
        &.title-1 {
          left: 246px;
        }
        &.title-2 {
          left: 673px;
        }
      }
    }
  }

  .section-6 {
    .section-item {
      margin-top: 50px;
      background: url('../../../../assets/images/solution/soft-hardware/3588/section-6-1.png')
        center no-repeat !important;
      background-size: contain !important;
      height: 242px;
      width: 100%;
      max-width: 1200px;
      position: relative;
    }

    .section-item-details {
      background: url('../../../../assets/images/solution/soft-hardware/3588/section-6-2.png')
        center no-repeat !important;
      background-size: contain !important;
      height: 203px;
      margin-top: 60px;
      margin-bottom: 70px;
    }
  }

  .section-7 {
    background: url('../../../../assets/images/solution/soft-hardware/3588/banner4.png')
      center no-repeat !important;
    background-size: cover !important;
    width: 100%;
    max-width: 100% !important;
    position: relative;

    .section-title {
      margin-bottom: 40px !important;
    }

    .section-item {
      margin-top: 50px;
      background: url('../../../../assets/images/solution/soft-hardware/3588/section-7.png')
        center no-repeat !important;
      background-size: contain !important;
      height: 294px;
      // width: 1200px;
      margin: 0 auto;
    }
  }

  .section-8 {
    background-color: #fff !important;
    width: 100%;
    max-width: 100% !important;
    height: 480px;
    position: relative;

    .section-title {
      margin-bottom: 40px !important;
    }

    .section-item {
      margin-top: 50px;
      height: 115px;
      width: 100%;
      max-width: 1200px;
      margin: 0 auto;

      ul {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
      }

      .item {
        width: 25%;
        display: flex;
        align-items: center;
        margin-top: 24px;

        .left {
          width: 73px;
        }

        .right {
          margin-left: 16px;

          .title {
            font-size: 18px;
            font-family: PingFang SC, PingFang SC-Semibold;
            font-weight: 600;
            text-align: left;
            color: #262626;
            line-height: 30px;
          }

          .content {
            font-size: 14px;
            font-family: PingFang SC, PingFang SC-Regular;
            font-weight: 400;
            text-align: justifyLeft;
            color: #666666;
            line-height: 42px;
          }

          .sub-content {
            display: inline-block;
            font-size: 14px;
            font-family: PingFang SC, PingFang SC-Regular;
            font-weight: 400;
            text-align: justifyLeft;
            color: rgb(167, 167, 167);
          }
        }
      }
    }
  }

  .section-81 {
    .section-item {
      display: flex;
      flex-direction: row;
      justify-content: space-around;
      align-items: center;

      .item {
        text-align: center;
        height: 151px;
        width: 590px;
        background: url('../../../../assets/images/solution/soft-hardware/803/qd1.png')
          center no-repeat !important;
        background-size: contain !important;

        &:nth-child(2) {
          background: url('../../../../assets/images/solution/soft-hardware/803/qd2.png')
            center no-repeat !important;
          background-size: contain !important;
        }

        .title {
          font-size: 18px;
          font-family: PingFang SC, PingFang SC-Semibold;
          font-weight: 600;
          text-align: left;
          color: #262626;
          line-height: 30px;
          text-align: center;
          margin-top: 24px;
        }

        // p {
        //   font-size: 14px;
        //   font-family: PingFang SC, PingFang SC-Regular;
        //   font-weight: 400;
        //   text-align: center;
        //   color: #666666;
        //   line-height: 42px;
        //   margin-top: 34px;
        // }
        ul {
          display: flex;
          justify-content: center;
          padding-top: 40px;
          li {
            color: #666666;
            font-size: 14px;
            position: relative;
            &::before {
              content: ' ';
              position: absolute;
              z-index: 1;
              top: 8px;
              left: -14px;
              width: 5px;
              height: 5px;
              border-radius: 100%;
              background: #d3d7de;
            }
          }
          li + li {
            margin-left: 32px;
          }
        }
      }
    }
  }

  .section-9 {
    background: #fff !important;
    width: 100%;
    max-width: 100% !important;
    // margin-top: 50px !important;
    position: relative;

    .section-item {
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      background: url('../../../../assets/images/solution/soft-hardware/3328/section-9.png')
        center no-repeat !important;
      background-size: contain !important;
      // width: 1200px;
      height: 267px;

      .item {
        padding-top: 50px;
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        grid-gap: 10px;
      }
    }
  }
}

@media screen and (max-width: 750px) {
  .main-content {
    &-banner {
      background: url('../../../../assets/images/solution/soft-hardware/3588/banner_bg.png')
        center no-repeat;
      background-size: cover;
    }
  }
}

.contact-wrap {
  // padding-top: 100px;
  height: 400px;
  text-align: center;

  .title {
    margin-bottom: 16px;
    font-size: 34px;
    color: #333;
    font-weight: bold;
  }

  .desc {
    font-size: 16px;
    color: #666;
    margin-bottom: 50px;
  }

  .apply-btn {
    margin: 60px auto 0;
    background: #1784e9;

    &:hover {
      color: #fff;
    }
  }
}

.corp {
  // padding: 80px 0 100px 0;
  padding-top: 76px;
  height: 320px;
  background: url(~@A/images/solution/acoustics/corp.png) center/cover no-repeat;
  .section-wrap {
    width: 1200px;
    margin: 0 auto;
    .section-title-bold {
      text-align: left;
    }
  }

  .section-desc2 {
    text-align: left;
    margin-top: 20px;
    font-size: 18px;
    line-height: 30px;
    font-family: Microsoft YaHei;
    font-weight: 400;
    color: #262626;
    display: block;
  }
  .section-title-bold {
    font-size: 34px;
    font-family: Microsoft YaHei;
    font-weight: bold;
    color: #333333;
    text-align: center;
  }

  .section-button {
    font-size: 16px;
    text-align: center;
    line-height: 50px;
    border: none;
    color: #fff;
    cursor: pointer;

    width: 180px;
    height: 50px;
    background: $primary;
    border-radius: 4px;
  }

  .section-title-contact {
    font-size: 36px;
    font-weight: 400;
    color: #000000;
  }
}
</style>
