<template>
  <div>
    <os-page-label label="编辑资源" class="mgb24">
    </os-page-label>
    <os-table
      :border="true"
      class="see-say-entity__personal-table mgb24"
      :tableData="tableData"
    >
      <el-table-column type="expand">
        <template slot-scope="props">
          <el-form label-position="left" label-width="200px" class="personal-table-expand">
            <el-form-item label="预处理 (非必填)">
              <el-tooltip :content="tips.preprocess" style="position: absolute; left: -98px; top: 15px;" placement="right">
                <i class="el-icon-question"></i>
              </el-tooltip>
              <el-checkbox v-model="props.row.preprocess[0]" true-label="1" false-label="0">统一转小写</el-checkbox>
              <el-checkbox v-model="props.row.preprocess[1]" true-label="1" false-label="0">统一转半角</el-checkbox>
              <el-checkbox v-model="props.row.preprocess[2]" true-label="1" false-label="0">统一转中文简体</el-checkbox>
            </el-form-item>
          </el-form>
        </template>
      </el-table-column>
      <el-table-column
        width="200">
        <template slot="header" slot-scope="scope">
          <os-table-qahead label="资源名" :tip="tips.resname" />
        </template>
        <template slot-scope="scope">
          <span v-if="scope.row.personalId > 0">{{scope.row.name}}</span>
          <!-- <el-form v-else :model="scope.row" ref="nameForm" label-width="0px" class="one-input-form">
            <el-form-item prop="name"
              :rules="[
                $rules.required('资源名不能为空'),
                $rules.lengthLimit(1, 20, '资源名长度不能超过20个字符'),
                {pattern: /^[a-zA-Z0-9_/]{0,}$/, message: '仅支持字母/数字/下划线', trigger: 'blur' },
                {validator: checkName, trigger: ['blur', 'change']}]">
              <el-input
                class="personal-value"
                size="small"
                placeholder="输入资源名"
                :title="scope.row.name"
                v-model="scope.row.name"
              />
              <input type="text" style="display: none;" />
            </el-form-item>
          </el-form> -->
          <el-input
            v-else
            class="personal-value"
            size="small"
            placeholder="输入资源名"
            :title="scope.row.name"
            v-model="scope.row.name"
            @blur="checkName(scope.row.name)"
          />
        </template>
      </el-table-column>
      <el-table-column
        width="200">
        <template slot="header" slot-scope="scope">
          <os-table-qahead label="数据所在主字段" :tip="tips.fileds" />
        </template>
        <template slot-scope="scope">
          <el-form :model="scope.row" ref="mainFieldForm" label-width="0px" class="one-input-form">
            <el-form-item prop="mainField"
              :rules="[
                $rules.required('主字段不能为空'),
                $rules.lengthLimit(1, 20, '主字段长度不能超过20个字符'),
                {validator: checkMainField, trigger: 'blur'},
                {validator: checkJson, trigger: 'blur'}]">
              <el-input
                class="personal-value"
                size="small"
                placeholder="仅支持一个主字段"
                :title="scope.row.mainField"
                v-model="scope.row.mainField"
              />
              <input type="text" style="display: none;" />
            </el-form-item>
          </el-form>
        </template>
      </el-table-column>
      <el-table-column
        label="数据所在从字段 (非必填)">
        <template slot-scope="scope">
          <el-form :model="scope.row" ref="slaveFieldForm" label-width="0px" class="one-input-form">
            <el-form-item
              prop="slaveField"
              :rules="[
                {validator: checkSlaveField, trigger: 'blur'},
                {validator: checkJson, trigger: 'blur'}]">
              <el-input
                class="personal-value"
                size="small"
                placeholder="最多支持10个从字段，英文逗号隔开"
                :title="scope.row.slaveField"
                v-model="scope.row.slaveField"
              />
              <input type="text" style="display: none;" />
            </el-form-item>
          </el-form>
        </template>
      </el-table-column>
    </os-table>
    <el-button class="mgb56" type="primary" size="small" @click="onSubmit" :loading="saving">
      {{saving ? '保存中...' : '保存'}}
    </el-button>
  </div>
</template>

<script>
import dicts from "@M/dicts"
export default {
  props: ['entityId'],
  data () {
    return {
      saving: false,
      tableData: {
        loading: true,
        total: 0,
        page: 1,
        size: 10,
        list: []
      },
      oldList: [],
      tips: {},
      nameRepeated: false,
      fieldValidate: true,
      resourceNameError: false
    }
  },
  created () {
    this.resourceNameError = false
    this.getData()
  },
  methods: {
    getData () {
      let self = this
      this.tableData.loading = true
      this.$utils.httpGet(this.$config.api.STUDIO_ENTITY_PERSONAL_DETAIL, {
        entityId: this.entityId
      }, {
        success: (res) => {
          let list = []
          if(res.data.personals && res.data.personals.length) {
            list = Array.prototype.map.call(res.data.personals, (item, index) => {
              item.fields = item.fields ? JSON.parse(item.fields) : {
                'col_name:s': [],
                'col_name:m': ''
              }
              let newItem = {
                personalId: item.pId ||  -1,
                name: item.pName || '',
                type: 4,
                title: item.title || 'none',
                mainField: item.fields['col_name:m'],
                slaveField: item.fields['col_name:s'].join(','),
                preprocess: item.preprocess ? item.preprocess.split(',') : ['0', '0', '0'],
                extend: item.extend ? item.extend : 'none',
                disabled: true
              }
              return newItem
            })
          } else {
            list.push({
              personalId: -1,
              name: '',
              type: 4,
              title: 'none',
              mainField: '',
              slaveField: '',
              preprocess: ['0', '0', '0'],
              extend: 'none',
              disabled: true
            })
          }
          this.tips = res.data.tips
          this.oldList = JSON.parse(JSON.stringify(list))
          this.tableData.list = list
          // this.tableData.total = res.data.count
          // this.tableData.page = res.data.pageIndex
          // this.tableData.size = res.data.pageSize
          this.tableData.loading = false
        },
        error: (err) => {

        }
      })
    },
    checkJson(rule, val, callback) {
      if(/\/$/.test(val)) {
        return callback(new Error('Json Pointer格式不正确'))
      }
      let keyword = 'none'
      if(val.toLowerCase() == keyword) {
        return callback(new Error('不能有none字段'))
      }
      let arr = val.split('/')
      if( arr && arr.length > 4) {
        return callback(new Error('最多支持4层'))
      }
      for(let i = 0; i<arr.length; i++) {
        if(arr[i].toLowerCase() == keyword) {
          return callback(new Error('不能有none字段'))
        } 
      }
      callback()
    },
    checkMainField (rule, val, callback) {
      if(!val) {
        callback()
        return
      }
      let reg = /^[a-zA-Z0-9_/]{0,}$/
      if(!reg.test(val)){
        return callback(new Error('仅支持字母/数字/下划线'))
      }
      callback()
    },
    checkSlaveField (rule, val, callback) {
      if(!val) {
        callback()
        return
      }
      if (val.split(',').length > 10) {
        callback(new Error('最多支持10个从字段'))
        return
      }
      let reg = /^[a-zA-Z0-9_,/]{0,}$/
      if(!reg.test(val)){
        return callback(new Error('仅支持字母/数字/下划线/逗号'))
      }
      callback()
    },
    checkForm(){
      // this.$refs['nameForm'].validate((valid) => {
      //   if(!valid){
      //     this.fieldValidate = false
      //     return
      //   }
      // })
      this.$refs['mainFieldForm'].validate((valid) => {
        if(!valid){
          this.fieldValidate = false
          return
        }
      })
      this.$refs['slaveFieldForm'].validate((valid) => {
        if(!valid){
          this.fieldValidate = false
          return
        }
      })
    },
    onSubmit () {
      let self = this
      if(self.resourceNameError) {
        return self.$message.error('资源名不合法')
      }
      let data = Array.prototype.map.call(this.tableData.list, function (item, index) {
        let newItem = self.$deepClone(item)
        newItem.preprocess = newItem.preprocess.join(',')
        return newItem
      })
      this.fieldValidate = true
      this.checkForm()
      if(!this.fieldValidate) {
        return
      }
      this.$utils.httpPost(this.$config.api.STUDIO_ENTITY_PERSONAL_SAVE, {
        entityId: this.entityId,
        dynamicJson: JSON.stringify(data)
      }, {
        success: (res) => {
          self.$message.success('保存成功')
          self.getData()
          self.$emit('change')
        },
        error: (err) => {

        }
      })
    },
    checkName (name) {
      let self = this
      if (!name) {
        self.resourceNameError = true
        return self.$message.error('资源名不能为空')
      }
      if(name.length > 20) {
        self.resourceNameError = true
        return self.$message.error('资源名长度不能超过20个字符')
      }
      if(!/^[a-zA-Z0-9_]{0,}$/.test(name)) {
        self.resourceNameError = true
        return self.$message.error('资源名仅支持字母/数字/下划线')
      }
      this.$utils.httpPost(this.$config.api.STUDIO_ENTITY_PERSONAL_CHECKNAME, {
        entityId: this.entityId,
        name: name || ''
      }, {
        success: (res) => {
          self.resourceNameError = false
        },
        error: (err) => {
          self.resourceNameError = true
        }
      })
    }
  }

}
</script>

<style lang="scss">
.see-say-entity__personal-table {
  .el-table__row td {
    padding: 0;
    height: 56px;
  }
  .el-table__body tr:hover > td {
    background-color: #fff;
  }
  &-expand {
    padding: 12px 10px;
    .el-form-item__label, .el-form-item__content {
      line-height: 36px;
    }
    .el-form-item:last-child {
      margin-bottom: 0;
    }
    &-radio {
      height: 36px;
      display: flex;
      align-items: center;
    }
  }
  .el-icon-question {
    color: $grey3;
  }
}
</style>
