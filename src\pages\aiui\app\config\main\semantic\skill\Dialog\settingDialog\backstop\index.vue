<template>
  <div>
    <div>
      <p class="item-title" style="margin-top: 40px">
        兜底设置
        <el-switch
          :value="status"
          :disabled="!subAccountEditable"
          class="mgr16"
          @change="topSwitch"
        ></el-switch>
      </p>
      <p class="config-desc">
        当所有语义技能均无法响应用户表述时，以下兜底将会按照你设置的优先级进行响应。
      </p>
      <!-- <p class="config-desc config-desc-bt-clo" style="color: #ff9b00">
        使用兜底设置前，请先开通语义理解。
      </p> -->
    </div>
    <div v-loading="loading">
      <draggable
        class="list-wrap"
        v-model="dataList"
        :disabled="!subAccountEditable"
        @end="dragFinished"
      >
        <transition-group type="transition">
          <div
            class="item"
            v-for="(item, index) in dataList"
            :key="item.service"
            v-if="item.service !== 'LastGuard'"
          >
            <i class="ic-r-menu"></i>
            <span class="item-name"
              >{{ item.serviceName }}（ {{ item.service }} ）</span
            >
            <span
              class="setting-icon"
              v-if="
                item.level != 0 &&
                item.serviceLabel &&
                item.serviceLabel.length > 1
              "
              @click="openQAConfig(item, index)"
            >
              <i class="ic-r-setting"></i>
              去设置
            </span>
            <el-switch
              class="btn-switch"
              v-model="switchList[index]"
              :disabled="!subAccountEditable"
              @change="off(item, index)"
            ></el-switch>
          </div>
        </transition-group>
      </draggable>
      <div class="item item__last-guard" v-if="dataList && dataList.length">
        <span class="item-name">无回复兜底（ LastGuard ）</span>
        <span
          class="setting-icon"
          v-if="switchList[switchList.length - 1]"
          @click="lastGuardDialog.show = true"
        >
          <i class="ic-r-setting"></i>
          去设置
        </span>
        <el-switch
          class="btn-switch"
          v-if="lastGuardSwitchShow"
          v-model="switchList[switchList.length - 1]"
          :disabled="!subAccountEditable"
          @change="off(dataList[dataList.length - 1], switchList.length - 1)"
        ></el-switch>
      </div>
      <!-- 问答库配置弹窗 -->
      <el-dialog
        class="config-list-dialog"
        :visible.sync="configDialogShow"
        width="568px"
        append-to-body
      >
        <template slot="title">
          {{ currentItem.serviceName }}问答库配置
        </template>
        <div
          class="config-list-wrap"
          v-if="currentItem && currentItem.service === 'Turing'"
        >
          <!-- radio：图灵通用版/儿童版 start -->
          <div class="radio-wrap">
            <el-radio class="radio" v-model="radio" label="turing"
              >通用版</el-radio
            >
            <el-radio v-model="radio" label="turingchild">儿童版</el-radio>
            <p class="radio-tip">
              图灵儿童版的闲聊风格会以和小朋友对话的口吻进行，建议儿童类产品使用，一般类产品使用通用版。
            </p>
          </div>
          <p class="top-tip">勾选需要使用的问答库：</p>
          <el-checkbox-group v-model="checkedList" style="margin-bottom: 32px">
            <el-checkbox
              v-for="(item, index) in currentItem.serviceLabel"
              v-show="
                item.label != 'TuringTongueTws' &&
                item.label != 'TuringDoggerel'
              "
              :key="index"
              :label="item.label"
              @change="qAConfig(item, index)"
            >
              {{ item.labelName }}（{{ item.label }}）</el-checkbox
            >
          </el-checkbox-group>
          <div class="buy-wrap">
            <p>
              每个 APPID 可以免费调用 500
              次/天，你可以根据使用情况购买图灵科技流量包。
            </p>
            <el-button size="mini" type="primary" @click="buyTuring"
              >购买</el-button
            >
          </div>
        </div>
        <el-tabs
          v-model="activeName"
          type="card"
          @tab-click="handleClick"
          v-if="currentItem && currentItem.service === 'iFlytekQA'"
        >
          <el-tab-pane label="通用版" name="general">
            <div class="config-list-wrap">
              <!-- radio：讯飞闲聊 多风格 start -->
              <div
                class="radio-wrap"
                v-if="currentItem && currentItem.service === 'iFlytekQA'"
                @change="doChange"
              >
                <el-radio class="radio" v-model="radioIFlytekQA" label="0"
                  >默认</el-radio
                >
                <el-radio class="radio" v-model="radioIFlytekQA" label="1"
                  >正式</el-radio
                >
                <el-radio v-model="radioIFlytekQA" label="2">可爱</el-radio>
                <p class="radio-tip p-margin-unset" v-html="iflyTips"></p>
              </div>
              <!-- radio：讯飞闲聊 多风格 end -->
              <p class="top-tip">勾选多项系统会检索多个问答库给最优的答复：</p>
              <el-checkbox-group
                v-model="checkedList"
                style="margin-bottom: 32px"
              >
                <el-checkbox
                  v-for="(item, index) in currentItem.serviceLabel"
                  v-show="
                    item.label != 'TuringTongueTws' &&
                    item.label != 'TuringDoggerel'
                  "
                  :key="index"
                  :label="item.label"
                  @change="qAConfig(item, index)"
                >
                  {{ item.labelName }}（{{ item.label }}）
                  <template v-if="item.label === 'iFlytekChat'">
                    <el-tooltip
                      content="与用户闲扯，无论用户说什么，都能给出回复。"
                      placement="right"
                    >
                      <i style="color: #b8babf" class="el-icon-question"></i>
                    </el-tooltip>
                  </template>
                </el-checkbox>
              </el-checkbox-group>
            </div>
          </el-tab-pane>
          <el-tab-pane label="儿童版" name="child">
            <div class="config-list-wrap">
              <!-- radio：讯飞闲聊 多风格 start -->
              <div class="radio-wrap" @change="doChange">
                <p class="radio-tip p-margin-unset" v-html="iflyTips"></p>
              </div>
              <!-- radio：讯飞闲聊 多风格 end -->
              <p class="top-tip">勾选多项系统会检索多个问答库给最优的答复：</p>
              <el-checkbox-group
                v-model="checkedList"
                style="margin-bottom: 32px"
              >
                <el-checkbox
                  v-for="(item, index) in currentItem.serviceLabel"
                  v-show="
                    item.label != 'TuringTongueTws' &&
                    item.label != 'TuringDoggerel'
                  "
                  :key="index"
                  :label="item.label"
                  @change="qAConfig(item, index)"
                  >{{ item.labelName }}（{{ item.label }}）</el-checkbox
                >
              </el-checkbox-group>
            </div>
          </el-tab-pane>
        </el-tabs>
        <span slot="footer" class="dialog-footer">
          <el-button @click="configDialogShow = false">取消</el-button>
          <el-button type="primary" @click="saveQAConfig">确定</el-button>
        </span>
      </el-dialog>

      <!-- 无回复兜底弹窗 -->
      <last-guard
        :dialog="lastGuardDialog"
        :configList="lastGuardList"
        :saving="saving"
        :currentScene="currentScene"
        @change="emitChange"
        @saveSuccess="$emit('saveSuccess')"
        @saveFail="$emit('saveFail')"
      ></last-guard>
    </div>
  </div>
</template>

<script>
import lastGuard from './lastGuard'
import draggable from 'vuedraggable'

export default {
  name: 'backstop',
  props: {
    saving: Boolean,
    semanticStatus: Boolean,
    currentScene: Object,
    openQAInfo: Object,
    subAccountEditable: Boolean,

    show: Boolean,
  },

  data() {
    return {
      activeName: 'general',
      iflyTips:
        '选择不同版，可以实现以不同的风格效果来回复用户。<br/>儿童版的闲聊会以和小朋友对话的口吻进行，建议儿童类产品使用，一般类产品使用通用版。',
      contentShow: true,
      change: false,
      loading: false,
      dataList: [],
      noResdata: {}, //无回复兜底数据
      switchList: [],
      initSwitchList: [],
      currentItem: {
        serviceLabel: [],
      },
      currentItemIndex: {},
      configDialogShow: false,
      initcheckList: [],
      checkedList: [],
      assistConfig: [],
      radio: '',
      radioIFlytekQA: '', // 讯飞闲聊 多风格 默认--0, 正式--1, 可爱--2
      radioIFlytekQAChild: '', // 讯飞闲聊 儿童版--4
      lastGuardDialog: {
        show: false,
      },
      lastGuardList: [],
      lastGuardSwitchShow: false,
      shouldUpdateSemanticConfig: false,

      status: false,
    }
  },
  computed: {
    appId() {
      return this.$store.state.aiuiApp.id
    },
    appName() {
      return this.$store.state.aiuiApp.app.appName
    },
  },

  // created() {
  //   this.init()
  //   this.contentShow = this.status
  // },
  watch: {
    radioIFlytekQA: function () {
      if (!!this.radioIFlytekQA) {
        this.radioIFlytekQAChild = ''
      }
    },
    radioIFlytekQAChild: function () {
      if (!!this.radioIFlytekQAChild) {
        this.radioIFlytekQA = ''
      }
    },
    /*'activeName': function () {
        if (this.activeName === 'general') {
          this.radioIFlytekQA = '0' // 经和芸芸沟通，切换回通用版后，默认选中 默认风格
        } else if (this.activeName === 'child') {
          this.radioIFlytekQAChild = '4'
        }
      },*/
    status() {
      this.contentShow = this.status
    },
    saving() {
      if (!this.saving) {
        return
      }
      if (!this.semanticStatus && this.switchList.indexOf(true) !== -1) {
        this.emitChange()
        this.assistConfig = []
        this.save()
        return
      }
      if (this.status && this.change) {
        this.formatSaveData()
        this.save()
        return
      }
      if (!this.status && this.change) {
        this.assistConfig = []
        this.save()
      }
    },

    show(val) {
      if (val) {
        this.init()
      }
    },
  },
  methods: {
    handleClick(tab, event) {
      if (tab.name === 'general') {
        this.radioIFlytekQA = '0' // 经和芸芸沟通，切换回通用版后，默认选中 默认风格
      } else if (tab.name === 'child') {
        this.radioIFlytekQAChild = '4'
      }
    },
    doChange(e) {
      this.shouldUpdateSemanticConfig = true
    },
    emitChange() {
      // this.change = true
      // this.$emit('change')
      this.formatSaveData()
      this.save()
    },
    topSwitch(value) {
      // this.$emit('statusChange', value)
      // if (!value) {
      //   this.emitChange()
      // }
      //  if (this.status && this.change) {
      //   this.formatSaveData()
      //   this.save()
      //   return
      // }
      // if (!this.status && this.change) {
      //   this.assistConfig = []
      //   this.save()
      // }

      if (value) {
        // this.formatSaveData()
        // this.save({ isOpen: 'on' })
        this.status = true
      } else {
        this.assistConfig = []
        this.save({ isOpen: 'off' })
      }
    },
    init() {
      this.getData()
      this.getLastGuard()
    },
    getData() {
      let self = this
      this.loading = true
      this.$utils.httpGet(
        this.$config.api.AIUI_BACKSTOP_LIST,
        {
          appid: this.appId,
          sceneId: this.currentScene.sceneBoxId,
        },
        {
          success: (res) => {
            this.loading = false
            if (res.data.switch == 'on') {
              // this.$emit('statusChange', true)
              self.status = true
            } else if (res.data.switch == 'off') {
              self.status = false
            }
            this.dataList.splice(0)
            this.switchList.splice(0)
            this.lastGuardSwitchShow = false
            let tmp = []
            tmp.push(...this.$utils.orderBy(res.data.assistConfig, ['level'])) //升序排列
            let lastGuard = 0,
              zeroList = [], //level = 0的数据
              plusList = [] //level > 0
            for (let i = 0; i < tmp.length; i++) {
              if (!tmp[i].isAvailable) {
                continue
              }
              //拦截，踢出tmp中的无回复兜底数据
              if (tmp[i].service == 'LastGuard') {
                lastGuard = tmp[i]
                continue
              }
              if (tmp[i].level == '0') {
                zeroList.push(tmp[i])
              } else {
                plusList.push(tmp[i])
                this.switchList.push(true)
              }
            }
            this.dataList.push(...plusList, ...zeroList, lastGuard)
            zeroList.forEach((item) => {
              if (item.level) {
                this.switchList.push(true)
              } else {
                this.switchList.push(false)
              }
            })
            this.switchList.push(lastGuard.level ? true : false)
            this.lastGuardSwitchShow = true
          },
          error: (err) => {
            this.loading = false
          },
        }
      )
    },
    getLastGuard() {
      this.loading = true
      this.$utils.httpGet(
        this.$config.api.AIUI_LAST_GUARD_LIST,
        {
          appid: this.appId,
          sceneId: this.currentScene.sceneBoxId,
        },
        {
          success: (res) => {
            this.lastGuardList.splice(0)
            if (res.data && res.data.length) {
              res.data.forEach((item) => {
                this.lastGuardList.push(item.config)
              })
            }
          },
          error: (err) => {},
        }
      )
    },
    off(item, index) {
      // 兜底项 switch

      if (this.switchList[index]) {
        this.dataList[index].level = '1'
        this.status = true
      } else {
        this.dataList[index].level = '0'
        // return
      }
      for (let i = 0; i < item.serviceLabel.length; i++) {
        if (
          item.service == 'iFlytekQA' &&
          item.serviceLabel[i].label == 'iFlytekGenericQA' &&
          this.openQAInfo?.num
        ) {
          //精品问答默认不勾选
          item.serviceLabel[i].isUsed = '0'
          continue
        }
        item.serviceLabel[i].isUsed = '1'
      }

      this.emitChange()
    },
    openQAConfig(item, index) {
      this.configDialogShow = true
      this.currentItem = item
      this.currentItemIndex = index
      if (item.service == 'Turing') {
        this.radio = item.source
      }
      if (item.service === 'iFlytekQA') {
        if (item.source === '4') {
          this.radioIFlytekQAChild = item.source
          this.activeName = 'child'
        } else {
          this.radioIFlytekQA = item.source
          this.activeName = 'general'
        }
      }
      this.initcheckList.splice(0)
      this.checkedList.splice(0)
      if (item.serviceLabel && item.serviceLabel.length <= 1) {
        return
      }
      for (let i = 0; i < item.serviceLabel.length; i++) {
        if (item.serviceLabel[i].isUsed !== '0') {
          this.initcheckList.push(item.serviceLabel[i].label)
          this.checkedList.push(item.serviceLabel[i].label)
        }
      }
    },
    qAConfig(item, index) {
      if (this.checkedList.indexOf('iFlytekGenericQA') === -1) {
        this.shouldUpdateSemanticConfig = false
        return
      }
      let self = this
      if (this.openQAInfo?.num && item && item.label == 'iFlytekGenericQA') {
        this.$confirm(
          '「开放问答」现在已经迁移至「兜底-讯飞闲聊-精选问答」，开启「精选问答」后将为您自动移除所有的「开放问答」，「开放问答」将无法再次添加。',
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }
        )
          .then(() => {
            self.shouldUpdateSemanticConfig = true
          })
          .catch(() => {
            self.checkedList.splice(index, 1)
            self.shouldUpdateSemanticConfig = false
          })
      }
    },
    saveQAConfig() {
      this.configDialogShow = false
      let tmp = this.currentItem.serviceLabel
      if (tmp && tmp.length <= 1) {
        this.emitChange()
        return
      }
      for (let i = 0; i < tmp.length; i++) {
        if (this.checkedList.indexOf(tmp[i].label) !== -1) {
          tmp[i].isUsed = '1'
        } else {
          tmp[i].isUsed = '0'
        }
      }
      if (this.currentItem.service == 'iFlytekQA' && !this.checkedList.length) {
        this.switchList[this.currentItemIndex] = false
        this.currentItem.level = 0
      }
      if (this.currentItem.service == 'iFlytekQA') {
        if (!!this.radioIFlytekQA) {
          this.currentItem.source = this.radioIFlytekQA
        } else if (!!this.radioIFlytekQAChild) {
          this.currentItem.source = this.radioIFlytekQAChild
        }
      }
      if (this.currentItem.service == 'Turing') {
        this.currentItem.source = this.radio
      }
      this.emitChange()
    },
    buyTuring() {
      window.open(
        this.$config.xfyunConsole +
          'sale/buy?wareId=1007&appId=' +
          this.appId +
          '&appName=' +
          this.appName +
          '&serviceName=图灵科技信源'
      )
    },
    dragFinished() {
      this.switchList.splice(0)
      for (let i = 0; i < this.dataList.length; i++) {
        if (this.dataList[i].level == '0') {
          this.switchList.push(false)
        } else {
          this.switchList.push(true)
        }
      }
      this.emitChange()
    },
    save(param) {
      this.$utils.httpPost(
        this.$config.api.AIUI_SAVE_BACKSTOP,
        {
          appid: this.appId,
          sceneId: this.currentScene.sceneBoxId,
          isOpen: param ? param.isOpen : this.status ? 'on' : 'off',
          assistConfig: JSON.stringify(this.assistConfig),
        },
        {
          success: (res) => {
            this.change = false
            this.$emit('saveSuccess')
            this.init()
            if (this.shouldUpdateSemanticConfig) {
              this.$emit('updateSemanticConfig', true)
            }
          },
          error: (err) => {
            this.change = true
            this.$emit('saveFail')
          },
        }
      )
    },
    formatSaveData() {
      let tmp = [],
        nonzero = 1
      this.assistConfig.splice(0)
      for (let i = 0; i < this.dataList.length; i++) {
        let tmp2 = []
        if (this.dataList[i].level == '0') {
          continue
        }
        for (let j = 0; j < this.dataList[i].serviceLabel.length; j++) {
          if (this.dataList[i].serviceLabel[j].isUsed !== '0') {
            tmp2.push(this.dataList[i].serviceLabel[j].label)
          }
        }
        tmp = {
          service: this.dataList[i].service,
          level: nonzero,
          serviceConfig: tmp2,
        }
        if (this.dataList[i].service == 'Turing') {
          tmp.source = this.radio
          tmp2.indexOf('TuringTongueTws') == -1
            ? tmp2.push('TuringTongueTws')
            : ''
          tmp2.indexOf('TuringDoggerel') == -1
            ? tmp2.push('TuringDoggerel')
            : ''
        }

        // 为讯飞闲聊 添加多风格 单选值
        if (this.dataList[i].service == 'iFlytekQA') {
          if (this.activeName === 'general') {
            tmp.source = this.radioIFlytekQA
          } else {
            tmp.source = this.radioIFlytekQAChild
          }
          console.log(JSON.stringify(this.dataList[i]))
          this.dataList[i].source = tmp.source
        }

        this.assistConfig.push(tmp)
        nonzero += 1
      }
    },
  },
  components: {
    lastGuard,
    draggable,
  },
}
</script>

<style lang="scss" scoped>
@import '../../common.scss';

.p-margin-unset {
  margin-bottom: unset !important;
}
.item {
  position: relative;
  padding-left: 12px;
  height: 56px;
  line-height: 56px;
  font-size: 0;
  border-bottom: 1px solid $grey3;
  cursor: move;
}
.item__last-guard {
  padding-left: 40px;
  cursor: default;
}
.item-name {
  font-size: 14px;
  font-weight: 500;
}
.btn-switch {
  vertical-align: -2px;
}
.ic-r-menu {
  vertical-align: -1px;
  margin-right: 14px;
  font-size: 14px;
  color: $grey4;
  cursor: move;
}
.setting-icon {
  position: absolute;
  top: -1px;
  right: 90px;
  display: flex;
  align-items: center;
  font-size: 16px;
  color: #1f90fe;
  cursor: pointer;
  i {
    margin-right: 5px;
    font-size: 18px;
  }
}

.top-tip {
  font-weight: 500;
  color: $grey6;
}

//问答库配置
.radio-tip {
  padding-top: 10px;
}
.checkbox-group-wrap,
.radio-wrap {
  margin-bottom: 32px;
  padding-bottom: 32px;
  border-bottom: 1px solid $grey2;
}
.buy-wrap {
  margin-bottom: 32px;
  padding-top: 32px;
  border-top: 1px solid $grey2;
  p {
    padding-bottom: 10px;
  }
}

.btn-buy.el-button-mini {
  min-width: 72px !important;
}
</style>
<style lang="scss">
.config-desc-bt-clo {
  bottom: 10%;
  position: relative;
}
.list-wrap,
.item__last-guard {
  .el-switch {
    position: absolute;
    top: 20px;
    right: 0;
  }
}
.config-list-dialog {
  //问答库配置
  .el-dialog__header {
    padding: 24px 32px 16px;
    font-size: 24px;
    color: $semi-black;
  }
  .dialog-footer .el-button {
    width: 120px;
  }
  .el-dialog__footer {
    padding-bottom: 32px;
  }
}

.config-list-wrap {
  .el-checkbox {
    display: block;
    padding-top: 16px;
  }
  .el-checkbox__label {
    // font-size: 16px;
    // font-weight: 500;
    // color: $semi-black;
  }
  .el-checkbox + .el-checkbox {
    margin-left: 0;
  }
  .el-checkbox__input.is-checked + .el-checkbox__label,
  .el-radio__input.is-checked + .el-radio__label {
    color: $semi-black;
  }
}
.buy-wrap {
  .el-button {
    min-width: 72px;
  }
}
</style>
