import Axios from 'axios'
import bus from './bus'

class Utils {

  constructor() {
    this.Connect = Axios.create({
			baseURL: ''
		})
  }

  isMobile() {
		var sUserAgent = navigator.userAgent.toLowerCase();
		var bIsIpad = sUserAgent.match(/ipad/i) == "ipad";
		var bIsIphoneOs = sUserAgent.match(/iphone os/i) == "iphone os";
		var bIsMidp = sUserAgent.match(/midp/i) == "midp";
		var bIsUc7 = sUserAgent.match(/rv:*******/i) == "rv:*******";
		var bIsUc = sUserAgent.match(/ucweb/i) == "ucweb";
		var bIsAndroid = sUserAgent.match(/android/i) == "android";
		var bIsCE = sUserAgent.match(/windows ce/i) == "windows ce";
		var bIsWM = sUserAgent.match(/windows mobile/i) == "windows mobile";
		if (bIsIpad || bIsIphoneOs || bIsMidp || bIsUc7 || bIsUc || bIsAndroid || bIsCE || bIsWM) {
			//跳转移动端页面
			return true
		} else {
			//跳转pc端页面
			return false
		}
	}

  isIphone () {
    var sUserAgent = navigator.userAgent.toLowerCase();
		var bIsIpad = sUserAgent.match(/ipad/i) == "ipad";
		var bIsIphoneOs = sUserAgent.match(/iphone os/i) == "iphone os";
		var bIsMidp = sUserAgent.match(/midp/i) == "midp";
		var bIsUc7 = sUserAgent.match(/rv:*******/i) == "rv:*******";
		var bIsUc = sUserAgent.match(/ucweb/i) == "ucweb";
		var bIsAndroid = sUserAgent.match(/android/i) == "android";
		var bIsCE = sUserAgent.match(/windows ce/i) == "windows ce";
		var bIsWM = sUserAgent.match(/windows mobile/i) == "windows mobile";
		if (bIsIpad || bIsIphoneOs) {
			//ios
			return true
		} else {
			return false
		}
  }

  agentFrom () {
    if (!!navigator.userAgent.match(/AppleWebKit.*Mobile.*/)) {//判断是否是移动设备打开。
      var ua = navigator.userAgent.toLowerCase();//获取判断用的对象
      if (ua.match(/MicroMessenger/i) == "micromessenger") {
        // 在微信中打开
        return 'wx'
      } else if (ua.match(/WeiBo/i) == "weibo") {
        // 在新浪微博客户端打开
        return 'sina'
      } else if (ua.match(/QQ/i) == "qq") {
        // 在QQ打开
        return 'qq'
      } else {
        // 未知默认为浏览器
        return 'webview'
      }
    } else {
      //否则就是PC浏览器打开
      return 'pc'
    }
  }

  httpRequest(url, method, options) {
    let _this = this
    this.Connect({
      url: url,
      method: method,
      headers: {
        'Authorization': `Bearer ${_this.getCookie('ssoSessionId')}`
      },
      data: options.data || {}
    }).then((response)=> {
      options.success(response.data)
		}, (err)=> {
      options.error(err)
		})
	}

  onEvent (element, type, callback) {
    if(element.addEventListener){
			element.addEventListener(type,callback,false);
		} else if(element.attachEvent){
			element.attachEvent('on'+type,callback);
		} else {
			element['on' + type] = callback;
		}
  }

  getCookie (name) {
    var arr, reg = new RegExp("(^| )" + name + "=([^;]*)(;|$)")
    if (arr = document.cookie.match(reg)) {
			return unescape(arr[2])
		} else {
			return null
		}
  }


}

export default new Utils();
