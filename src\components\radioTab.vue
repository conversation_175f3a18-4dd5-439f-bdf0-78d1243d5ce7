<template>
  <ul class="radio-tab-wrap">
    <li
      :key="index"
      v-for="(item, index) in data"
      :class="item.value == value ? 'active' : ''"
      @click="onClick(item)"
    >
      {{ item.label }}
    </li>
  </ul>
</template>

<script>
export default {
  name: 'radio-tab',
  props: {
    value: String,
    data: [],
  },
  methods: {
    onClick(item) {
      this.$emit('input', item.value)
    },
  },
}
</script>

<style lang="scss">
.radio-tab-wrap {
  display: flex;
  li {
    // padding: 10px 16px;
    text-align: center;
    min-width: unset;
    width: 208px;
    background: #fff;
    border: 1px solid $grey3;
    cursor: pointer;
    line-height: 44px;
    font-size: 14px;
    &.active,
    &:hover {
      border: 1px solid $primary;
      color: $primary;
      background: $primary-light-12;
    }
  }
}
</style>
