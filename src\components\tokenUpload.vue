<template>
  <upload
    ref="upload"
    :headers="{ 'X-Csrf-Token': token }"
    v-bind="$attrs"
    v-on="$listeners"
  >
    <slot></slot>
    <template v-for="(index, name) in $slots" v-slot:[name]>
      <slot :name="name"></slot>
    </template>
  </upload>
</template>
<script>
import { Upload } from 'element-ui'

export default {
  data() {
    return {
      token: localStorage.getItem('AIUI_GLOBAL_VARIABLE'),
    }
  },
  methods: {
    submit() {
      this.$refs.upload.submit() // 透传 submit() 方法
    },
    clearFiles() {
      this.$refs.upload.clearFiles() // 透传 clearFiles() 方法
    },
  },
  components: {
    Upload,
  },
}
</script>
