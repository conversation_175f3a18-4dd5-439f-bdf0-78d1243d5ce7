<template>
  <div
    :class="['skill', { 'skill-active': item.used }]"
    @click="toDocCard(item)"
  >
    <div :class="['content-wrap', { 'cursor-default': subAccount }]">
      <i class="skill-icon" :style="{ backgroundColor: item.color }">{{
        item.name && item.name.substr(0, 1)
      }}</i>
      <div class="skill-info">
        <p class="skill-title" :title="item.name">
          {{ item.name }}
        </p>

        <!-- 标题下按钮 -->
        <div class="title-btm-btn-group">
          <p class="ability-tag">文档问答</p>
        </div>
      </div>
    </div>

    <div @click.stop class="switch-wrap">
      <el-switch
        :disabled="!subAccountEditable"
        size="small"
        v-model="item.used"
        @change="(val) => onSwitchChange(val, item)"
      >
      </el-switch>
    </div>
    <!-- 下面的label area -->
    <!-- <div v-if="subAccountEditable && item.used" class="label-wrap">
      <p class="skill-desc" :title="item.name"></p>
      <i
        @click.prevent.stop="showThresholdInfo(item)"
        class="skill-config-new AIUI-myapp-iconfont ai-myapp-setting2"
        v-if="subAccountEditable && item.used"
      ></i>
    </div> -->
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  components: {},
  data() {
    return {
      configSkillItem: {},
    }
  },
  props: {
    item: Object,
    docConfig: Object,

    currentScene: Object,
    appId: String,
    skillConfig: Object,
  },
  computed: {
    ...mapGetters({
      subAccount: 'user/subAccount',
      subAccountAppAuths: 'aiuiApp/subAccountAppAuths',
      subAccountSkillAuths: 'aiuiApp/subAccountSkillAuths',
    }),
    subAccountEditable() {
      return this.subAccountAppAuths[this.$route.params.appId] == 2
        ? false
        : true
    },
  },
  methods: {
    toDocCard(item) {
      if (this.subAccount) return
      // window.open(`/studio/character/${id}`, '_blank')
      window.open(`/studio/knowledge/${item.repoId}/info`, '_blank')
    },
    doRealChange(val, item) {
      console.log('onSwitchChange', val, item)
      this.$emit('change')
      let data
      let operation = val ? 'open' : 'close'
      data = {
        repoId: item.repoId,
        operation,
      }
      this.docConfig[item.repoId] = data
    },

    onSwitchChange(val, item) {
      let that = this
      that.doRealChange(val, item)
    },
  },
}
</script>
<style lang="scss" scoped>
@import '../style.scss';

.content-wrap {
  display: flex;
  // height: 70px;
}
</style>
