<template>
  <os-page class="extend-entity-wrap" :options="pageOptions" @returnCb="pageReturn">
    <div slot="headLeft">
      <span class="extend-entity-title" :title="zhName || '-'">{{zhName || '-'}}</span>
    </div>
    <el-collapse class="mgt32 mgb24 extend-entitie-collapse">
      <el-collapse-item>
        <template slot="title">
          <span class="tip-title">定制实体和<a @click.prevent.stop="toEntity">静态实体引用</a>的区别</span>
        </template>
        <div style="margin-bottom: 16px;">2019年3月，AIUI 业务定制进行了升级，推出了静态实体引用，静态实体引用的功能比定制实体更加完善，优先推荐使用静态实体引用。定制实体的存在是为了兼容以前2019年3月以前的旧版业务定制。其中定制实体和静态实体引用的区别如下：</div>
        <p>1. 静态实体引用属于账户，可以引用多个实体生成新实体，并在此基础上进行增删，影响范围是使用该实体的语料。</p>
        <p>2. 定制实体属于技能，在单技能下进行定制，不影响其他技能，影响范围是技能中原有的语料。</p>
        <p>3. 定制实体的实体名不会改变，为固定值。</p>
      </el-collapse-item>
    </el-collapse>
    <studio-skill-header-right slot="btn" />
    <el-tabs v-model="activeName" type="card">
      <el-tab-pane label="新增词条" name="first">
        <add-dict></add-dict>
      </el-tab-pane>
      <el-tab-pane label="词条黑名单" name="second">
        <dict-blacklist></dict-blacklist>
      </el-tab-pane>
    </el-tabs>
  </os-page>
</template>
<script>
import AddDict from './addDict'
import DictBlacklist from './dictBlacklist'

export default {
  name: 'extend-skill-entity',
  data() {
    return {
      pageOptions: {
        loading: false,
        returnBtn: true,
        showHead: true
      },
      activeName: 'first',
    }
  },
  computed: {
    zhName() {
      return this.$route.query.name || '-'
    }
  },
  methods: {
    pageReturn () {
      this.$router.push({ name: 'extend-skill-entities' })
    },
    toEntity(){
      let routeData = this.$router.resolve({name: 'studio-handle-platform-entities', query: {option: 'createEntity'}})
      localStorage.setItem('pageHandle', 'create')
      window.open(routeData.href, '_blank')
    }
  },
  components: {
    AddDict,
    DictBlacklist
  }
}
</script>
<style lang="scss" scoped>
  .tip-title {
    font-size: 16px;
    color: $semi-black;
    font-weight: 600;
  }
  p {
    padding-left: 10px;
  }
</style>
<style lang="scss">
  .extend-entity-wrap {
    .el-tabs--card > .el-tabs__header .el-tabs__nav {
      height: 40px;
      border: none;
    }
    .el-tabs--card > .el-tabs__header .el-tabs__item {
      padding: 0 15px;
      border-left: none;
      border-bottom: 1px solid #e4e7ed;
    }
    .el-tabs__item.is-top.is-active {
      border-top: 1px solid #e4e7ed;
      border-right: 1px solid #e4e7ed;
      border-left: 1px solid #e4e7ed;
      border-bottom-color: $white; 
      border-radius: 6px 6px 0 0;
    }
  }
  .extend-entitie-collapse {
    border: none;
    .el-collapse-item {
      background: $grey1;
      border-radius: 12px;
      padding: 0 24px;
    }
    .el-collapse-item__arrow {
      margin-right: 0;
      color: $grey4;
    }
    .el-collapse-item__header {
      padding: 16px 0;
      height: unset;
      line-height: unset;
    }
    .el-collapse-item__header, .el-collapse-item__wrap {
      border: none;
      background-color: $grey1;
    }
    
  }
</style>


