<template>
  <div class="make-resource-page">
    <div
      class="make-item"
      v-loading="makeLoading || uploadLoading"
      :element-loading-text="
        makeLoading ? '资源制作中，请稍候' : '上传中，请稍候'
      "
    >
      <div class="make-list">
        <div class="opt-wrap">
          <el-dropdown
            trigger="click"
            @command="handleCommand"
            placement="bottom-start"
          >
            <el-button size="small">
              批量操作
              <i class="ic-r-triangle-down el-icon--right" />
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="cover">
                <upload
                  :appId="appId"
                  :options="options"
                  :limitCount="limitCount"
                  @setLoad="setLoad"
                  @getAwakenList="getAwakenList(1)"
                ></upload>
              </el-dropdown-item>
              <el-dropdown-item command="export">导出所有</el-dropdown-item>
              <el-dropdown-item command="down">模版下载</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>

          <el-dropdown
            slot="btn"
            @command="handleMakeFileCommand"
            style="margin-left: 10px"
          >
            <el-button type="primary">
              开始制作并下载<i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item v-if="ktvHasAuth" command="7.0"
                >KTV版本</el-dropdown-item
              >
              <el-dropdown-item command="7.1">3.8引擎版本</el-dropdown-item>
              <el-dropdown-item command="3.17.7"
                >3.17.7引擎版本</el-dropdown-item
              >
            </el-dropdown-menu>
          </el-dropdown>
        </div>

        <div class="template-ul">
          <el-button
            v-for="(item, index) in templates"
            v-bind:key="index"
            class="template-btn"
            @click="showDialogBefore('templateCover', item)"
          >
            {{ item.name }}
          </el-button>
        </div>
        <div>
          <p>你可以使用下面AIUI预制的场景资源模板快速制作。</p>
        </div>
      </div>
      <div class="buy-section buy-section-3">
        <ul class="buy-section-3-header">
          <li style="min-width: 24% !important">
            词名称
            <el-tooltip
              class="item"
              effect="dark"
              content=""
              placement="bottom"
            >
              <div slot="content">
                词名称默认支持3个词定制；<br />
                最高支持300个资源词定制（<a
                  @click="showDialog('apply')"
                  target="_self"
                  >申请开通</a
                >）。
              </div>
              <i class="el-icon-question" />
            </el-tooltip>
          </li>
          <li class="word-type">
            词类型
            <el-tooltip
              class="item"
              effect="dark"
              content=""
              placement="bottom"
            >
              <div slot="content">
                词类型分为：<br />
                1、唤醒词：唤醒后可进行人机对话，如“小飞小飞”；<br />
                2、离线命令词：用于设备离线交互，如控制设备声音大小的词，如“声音大一点”；<br />
                3、云端命令词：可以把影响交互体验的命令词，做成云端命令词，如切歌、静音，利用云端语义来防止误触发；<br />
                4、免唤醒入口词：如你把“我想看”设置为入口词，用户无需唤醒设备，可以直接说“我想看XXX”就可以点播电影、动画。
              </div>
              <i class="el-icon-question" />
            </el-tooltip>
          </li>
          <li>
            敏感度
            <el-tooltip
              class="item"
              effect="dark"
              content=""
              placement="bottom"
            >
              <div slot="content">
                阈值越高越不敏感，越需要发音清晰，不容易误触发；<br />
                阈值越低越敏感，越容易说，也容易误触发，<br />
                敏感度默认阈值是900，你可以根据设备真实效果自行调整。
              </div>
              <i class="el-icon-question" />
            </el-tooltip>
          </li>
          <li>
            操作
            <a style="margin-left: 5%" @click="showDialog('clearAwaken')"
              >清空所有</a
            >
          </li>
        </ul>
        <el-tabs
          tab-position="left"
          v-model="list"
          @tab-click="clickTab"
          :class="search.people == 1 ? 'people' : ''"
        >
          <div class="tab-content" v-if="list.list.length" v-loading="loading">
            <div class="tab-content-list">
              <div class="add-input">
                <el-input
                  v-model="awaken.name"
                  @input="addChange($event)"
                  icon="ic-r-plus"
                  placeholder="添加资源词，回车新增"
                  @keydown.enter.native="onChangeSelf(awaken)"
                >
                  <i slot="prefix" class="el-input__icon ic-r-plus"></i>
                </el-input>
              </div>
              <div class="word-type word-type-select-div">
                <el-select
                  class="word-type-select"
                  v-model="awaken.type"
                  size="medium"
                  @change="onChangeSelf(awaken)"
                >
                  <el-option
                    v-for="(item, index) in awakenType"
                    :key="index"
                    :value="item.value"
                    :label="item.label"
                  ></el-option>
                  <div slot="suffix">
                    <i class="AIUI-iconfont ai-mn-xiala"></i>
                  </div>
                </el-select>
              </div>
              <div>
                <a-input-number
                  v-model="awaken.threshold"
                  :min="0"
                  :step="5"
                  :max="5000"
                  :default-value="900"
                  @keydown.enter.native="onChangeSelf(awaken)"
                  @blur="onChangeSelf(awaken)"
                />
                <!--<div slot="suffix" style="cursor: pointer;">
                    <span v-html="awaken.threshold.replace(new RegExp(' '.trim(), 'im'),'<span class=\'qabank-page-hight-light\'>' + ''.trim() + '</span>')"></span>
                    &lt;!&ndash;{{awaken.threshold}}
                    <i class="AIUI-iconfont ai-mn-xiala"></i>
                    <i class="AIUI-iconfont ai-mn-xiala"></i>
                    <i class="icon-sort"></i>&ndash;&gt;
                  </div>-->
              </div>
              <div>
                <i
                  v-if="awaken.id"
                  class="tab-content-list-del ic-r-delete"
                  @click="delAwaken(awaken)"
                />
              </div>
            </div>
            <div
              v-for="(awaken, index) in list.list"
              :key="index"
              class="tab-content-list"
            >
              <div @click="ontoggle(awaken)" ref="edit">
                <template v-if="awaken.edit">
                  <div>
                    <el-input
                      type="text"
                      autocomplete="true"
                      ref="input"
                      v-focus
                      v-model="awaken.name"
                      @keydown.enter.native="onChangeSelf(awaken)"
                    ></el-input>
                  </div>
                </template>
                <template v-else>
                  {{ awaken.name }}
                </template>
              </div>
              <div class="word-type word-type-select-div">
                <el-select
                  class="word-type-select"
                  v-model="awaken.type"
                  size="medium"
                  @change="onChangeSelf(awaken)"
                >
                  <el-option
                    v-for="(item, index) in awakenType"
                    :key="index"
                    :value="item.value"
                    :label="item.label"
                  ></el-option>
                  <div slot="suffix">
                    <i class="AIUI-iconfont ai-mn-xiala"></i>
                  </div>
                </el-select>
              </div>
              <div>
                <a-input-number
                  v-model="awaken.threshold"
                  :min="0"
                  :step="5"
                  :max="5000"
                  :default-value="900"
                  @keydown.enter.native="onChangeSelf(awaken, $event)"
                  @blur="onChangeSelf(awaken)"
                />
              </div>
              <div>
                <i
                  v-if="awaken.id"
                  class="tab-content-list-del ic-r-delete"
                  @click="delAwaken(awaken)"
                />
              </div>
            </div>
          </div>
          <div v-else class="tab-content">
            <div class="tab-content-list">
              <div class="add-input">
                <el-input
                  v-model="awaken.name"
                  icon="ic-r-plus"
                  placeholder="添加资源词，回车新增"
                  @keydown.enter.native="onChangeSelf(awaken)"
                >
                  <i slot="prefix" class="el-input__icon ic-r-plus"></i>
                </el-input>
              </div>
              <div class="word-type word-type-select-div">
                <el-select
                  class="word-type-select"
                  v-model="awaken.type"
                  size="medium"
                  @change="onChangeSelf(awaken)"
                >
                  <el-option
                    v-for="(item, index) in awakenType"
                    :key="index"
                    :value="item.value"
                    :label="item.label"
                  ></el-option>
                  <div slot="suffix">
                    <i class="AIUI-iconfont ai-mn-xiala"></i>
                  </div>
                </el-select>
              </div>
              <div>
                <a-input-number
                  v-model="awaken.threshold"
                  :min="0"
                  :step="5"
                  :max="5000"
                  :default-value="900"
                  @keydown.enter.native="onChangeSelf(awaken)"
                  @blur="onChangeSelf(awaken)"
                />
                <!--<div slot="suffix" style="cursor: pointer;">
                    <span v-html="awaken.threshold.replace(new RegExp(' '.trim(), 'im'),'<span class=\'qabank-page-hight-light\'>' + ''.trim() + '</span>')"></span>
                    &lt;!&ndash;{{awaken.threshold}}
                    <i class="AIUI-iconfont ai-mn-xiala"></i>
                    <i class="AIUI-iconfont ai-mn-xiala"></i>
                    <i class="icon-sort"></i>&ndash;&gt;
                  </div>-->
              </div>
              <div>
                <i
                  v-if="awaken.id"
                  class="tab-content-list-del ic-r-delete"
                  @click="delAwaken(awaken)"
                />
              </div>
            </div>
            <div style="text-align: center; padding: 25px 0; color: #959595">
              暂无数据
            </div>
          </div>
        </el-tabs>
        <div class="mgt24" v-if="list.total > 10">
          <el-pagination
            ref="pagination"
            v-if="list.list.length"
            class="txt-al-c"
            @current-change="getQaPair"
            :current-page="list.pageIndex"
            :page-size="list.pageSize"
            :total="list.total"
            :layout="pageLayout"
          >
          </el-pagination>
        </div>
      </div>
    </div>
    <ResourceDialog
      :dialog="dialog"
      @copyTemplate2Awaken="copyTemplate2Awaken"
      @clearAwaken="clearAwaken"
    ></ResourceDialog>
  </div>
</template>

<script>
import Upload from './uploadResource'
import ResourceDialog from './dialog/resourceDialog'
import { mapGetters } from 'vuex'
import InputNumber from 'ant-design-vue/lib/input-number'
import 'ant-design-vue/lib/input-number/style/css'

/**
 * 离在线资源制作页
 */
export default {
  name: 'make-resource',
  data() {
    return {
      awaken: {
        name: null,
        type: 0,
        threshold: 900,
      },
      dialog: {
        name: '',
        show: false,
        obj: Object,
      },
      loading: false,
      awakenType: [
        {
          label: '唤醒词(唤醒后交互)',
          value: 0,
        },
        {
          label: '离线命令词(无网络使用)',
          value: 2,
        },
        {
          label: '云端命令词(使用AIUI下发该命令)',
          value: 3,
        },
        {
          label: '免唤醒入口词(如：“导航”去科大讯飞)',
          value: 4,
        },
      ],
      templates: [],
      search: {
        people: '2',
        voice: '0',
        tab: '1',
      },
      pageOptions: {
        title: '资源制作',
        loading: false,
        returnBtn: false,
      },
      options: {
        text: '覆盖更新',
      },
      beforeFile: {},
      makeLoading: false,
      uploadLoading: false,
      list: {
        total: 1,
        pageIndex: 1,
        pageSize: 10,
        list: [],
      },
      ktvHasAuth: false,
    }
  },
  watch: {
    search: {
      handler(val) {
        this.getInformants()
      },
      deep: true,
    },
    awaken: {
      handler(newVal, oldVal) {
        console.log(newVal)
        console.log(oldVal)
      },
      deep: true,
    },
  },
  created() {
    this.getAllAwakenTemplates()
    this.getResList()
    this.getKtvHasAuth()
  },
  mounted() {
    this.$nextTick(() => {})
  },
  computed: {
    appId() {
      return this.$route.params.appId
    },
    pageLayout() {
      if (this.list.total / this.list.list > 10) {
        return 'prev, pager, next, jumper, total'
      }
      return 'prev, pager, next, jumper, total'
    },
    ...mapGetters({
      limitCount: 'aiuiApp/limitCount',
      topicNumber: 'commonStore/topicNumber',
    }),
    // ktvHasAuth() {
    //   return false
    // },
  },
  methods: {
    ontoggle(awaken) {
      for (let i = 0; i < this.list.list.length; i++) {
        if (awaken.id != this.list.list[i].id) {
          if (this.list.list[i].edit) {
            this.list.list[i].edit = !this.list.list[i].edit
          }
        } else {
          if (!awaken.edit) {
            awaken.edit = !awaken.edit
          }
        }
      }
      this.$forceUpdate()
    },
    addChange(e) {
      this.$forceUpdate()
    },
    onChangeSelectSelf(awaken) {
      console.log(awaken)
    },
    inputChange(e) {
      const o = e.target
      o.value = o.value.replace(/[^\u4E00-\u9FA5]/g, '') // 清除除了中文以外的输入的字符
    },
    onChangeSelf(awaken, thiz) {
      if (!awaken.name) {
        this.$message.error('资源词名称不能为空')
        return
      }
      if (
        !(2 <= awaken.name.length && awaken.name.length <= 10) ||
        /[^\u4E00-\u9FA5]/g.test(awaken.name)
      ) {
        this.$message.error('资源词名称长度为2~10个中文')
        return
      }
      if (
        awaken.threshold == null ||
        awaken.threshold == undefined ||
        awaken.threshold == '' ||
        !/^(0|[1-9]{1}|[1-4][0-9]{3}|[1-9][0-9]{1,2}|5000)$/.test(
          awaken.threshold
        )
      ) {
        //this.$message.error("敏感度不能为空")
        awaken.threshold = 900
        //return
      }
      this.loading = true
      let data
      if (awaken.id) {
        data = {
          id: awaken.id,
          appid: this.appId,
          name: awaken.name,
          type: awaken.type,
          threshold: awaken.threshold,
        }
      } else {
        data = {
          appid: this.appId,
          name: awaken.name,
          type: awaken.type,
          threshold: awaken.threshold,
        }
      }
      this.$utils.httpPost(this.$config.api.AWAKEN_ADD_EDIT, data, {
        success: (res) => {
          this.loading = false
          if (res.flag) {
            //this.$message.success(res.desc)
            this.getAwakenList()
            this.awaken = {
              name: null,
              type: 0,
              threshold: 900,
            }
          }
        },
        error: (err) => {
          this.loading = false
        },
      })
    },
    showDialogBefore(name, obj) {
      if (this.ktvHasAuth) {
        this.showDialog(name, obj)
      } else {
        this.$message.error(
          '你尚未开通行业唤醒词权限，如需开通权限，请联系商务************************。'
        )
      }
    },
    showDialog(name, obj) {
      this.dialog.show = true
      this.dialog.name = name
      this.dialog.obj = obj
    },
    delAwaken(awaken) {
      this.loading = true
      this.$utils.httpGet(
        this.$config.api.AWAKEN_DELETE,
        {
          appid: this.appId,
          id: awaken.id,
        },
        {
          success: (res) => {
            this.loading = false
            if (res.flag) {
              this.$message.success(res.desc)
              this.list = {
                total: 1,
                pageIndex: 1,
                pageSize: 10,
                list: [],
              }
              this.getAwakenList()
            }
          },
          error: (err) => {
            this.loading = false
          },
        }
      )
    },
    // 批量操作
    handleCommand(command) {
      let self = this
      switch (command) {
        case 'cover':
          break
        case 'questioning':
          break
        case 'export':
          this.$utils.postopen(this.$config.api.AWAKEN_EXPORT, {
            appid: this.appId,
          })
          break
        case 'down':
          window.open(
            'https://aiui-file.cn-bj.ufileos.com/resourcewords.xlsx',
            '_self'
          )
          break
        default:
          g
          break
      }
    },
    setLoad(val) {
      this.uploadLoading = val
    },
    getQaPair(page) {
      this.list.pageIndex = page
      this.getAwakenList(page)
    },
    getAwakenList(page) {
      let self = this
      this.loading = true
      this.$utils.httpGet(
        this.$config.api.AWAKEN_GET_LIST,
        {
          appid: this.appId,
          pageIndex: page || this.list.pageIndex,
          pageSize: this.list.pageSize,
        },
        {
          success: (res) => {
            self.list.list = res.data.list
            //self.oldList = JSON.parse(JSON.stringify(res.data.topics))
            //self.tableData.list = res.data.topics
            self.list.total = res.data.count
            self.list.page = res.data.pageIndex
            self.list.size = res.data.pageSize
            self.loading = false
          },
          error: (err) => {
            self.loading = false
          },
        }
      )
    },
    clickTab(tab) {
      // this.getInformants(tab)
    },
    downloadTemplate() {
      window.open(
        `${this.$config.server}/aiui/web/download?url=http://aiui-file.cn-bj.ufileos.com/res.txt&fileName=res.txt&code=utf-8`,
        '_self'
      )
    },
    downloadBeforeFile() {
      this.$utils.httpGet(
        this.$config.api.AWAKEN_DOWNLOAD,
        {
          appid: this.appId,
        },
        {
          success: (res) => {
            let a = document.createElement('a')
            a.href = URL.createObjectURL(new Blob([res.data]))
            a.download = this.beforeFile.fileName
            document.body.appendChild(a)
            a.click()
            setTimeout(() => {
              document.body.removeChild(a)
            }, 500)
          },
          error: (err) => {},
        }
      )
    },
    downloadZip(url) {
      window.open(url, '_self')
    },
    makeFile(version) {
      this.makeLoading = true
      if (!this.list.list.length > 0) {
        this.$message.error('你还没有添加资源')
        this.makeLoading = false
        return
      }
      this.$utils.httpPost(
        this.$config.api.AWAKEN_MAKEANDDOWNLOAD,
        {
          appid: this.appId,
          version,
        },
        {
          config: {
            headers: {},
            responseType: 'blob',
          },
          success: (res) => {
            this.makeLoading = false
            this.$message_pro_success(
              '制作成功',
              '已为你下载，请按照配置说明进行更新你的资源。'
            )
            let urlObject = window.URL || window.webkitURL || window
            let contentDisposition = res.headers['content-disposition']
            let fileName
            if (contentDisposition) {
              fileName = window.decodeURI(
                res.headers['content-disposition'].split('=')[1],
                'UTF-8'
              )
            }
            let url = urlObject.createObjectURL(
              new Blob([res.data], { type: 'application/zip' })
            )
            let a = document.createElement('a')
            a.style.display = 'none'
            a.href = url
            //a.setAttribute('download', fileName);
            a.download = fileName
            document.body.appendChild(a)
            a.click()
            setTimeout(
              () => {
                window.URL.revokeObjectURL(url)
                document.body.removeChild(a)
              },
              500,
              () => {
                this.getAwakenList()
              }
            )

            this.$alert(
              '引擎版本互不兼容。请使用VTN库的CAEGetVersion方法确认唤醒资源版本和VTN库的版本是否一致。',
              '温馨提示',
              {
                type: 'info',
                confirmButtonText: '知道了',
                callback: (action) => {},
              }
            )
          },
          error: (err) => {
            this.makeLoading = false
            this.$message.error('制作失败，请稍后再试')
          },
        }
      )
    },
    changePage(page) {
      this.list.pageIndex = page
      this.getResList()
    },
    getResList() {
      this.$utils.httpGet(
        this.$config.api.AWAKEN_GET_LIST,
        {
          appid: this.appId,
          pageIndex: this.list.pageIndex,
          pageSize: this.list.pageSize,
        },
        {
          success: (res) => {
            this.list.total = res.data.count
            this.list.list = res.data.list
          },
          error: (err) => {},
        }
      )
    },
    handlerBeforeUpload(file) {
      if (file.type !== 'text/plain') {
        this.$confirm('资源模板需要是 .txt 文件', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          showCancelButton: false,
          type: 'warning',
        })
          .then(() => {})
          .catch(() => {
            return 0
          })
        return false
      }
      this.uploadLoading = true
      return true
    },
    handlerFileSuccess(res, file) {
      this.uploadLoading = false
      if (file.status === 'success') {
        if (res.flag) {
          this.$message.success(res.desc)
          this.getBeforeFile()
        } else {
          this.$message.warning({ message: res.desc, duration: 5000 })
        }
      }
    },
    getAllAwakenTemplates() {
      this.$utils.httpGet(
        this.$config.api.AWAKEN_TEMPLATES,
        {},
        {
          success: (res) => {
            if (res.data) {
              this.templates = res.data
            }
          },
          error: (err) => {},
        }
      )
    },
    copyTemplate2Awaken(template) {
      this.uploadLoading = true
      this.$utils.httpGet(
        this.$config.api.AWAKEN_CPY,
        {
          appid: this.appId,
          template: template.id,
        },
        {
          success: (res) => {
            this.uploadLoading = false
            if (res.flag) {
              this.$message.success(res.desc)
              this.getAwakenList(1)
            }
          },
          error: (err) => {
            this.uploadLoading = false
          },
        }
      )
    },
    clearAwaken() {
      this.uploadLoading = true
      this.$utils.httpGet(
        this.$config.api.AWAKEN_DELETE_ALL,
        {
          appid: this.appId,
        },
        {
          success: (res) => {
            this.uploadLoading = false
            if (res.flag) {
              this.$message.success(res.desc)
              this.list = {
                total: 1,
                pageIndex: 1,
                pageSize: 10,
                list: [],
              }
              this.getAwakenList(1)
            }
          },
          error: (err) => {
            this.uploadLoading = false
          },
        }
      )
    },

    handleMakeFileCommand(command) {
      // 7.0版本，ktv版本；7.1 版本，通用版本
      if (command === '7.0') {
        // ktv版本，需判断开关是否开启，
        if (this.ktvHasAuth) {
          this.makeFile(command)
        } else {
          this.$message.error(
            '你尚未开通行业唤醒词权限，如需开通权限，请联系商务************************。'
          )
        }
      } else if (command === '7.1') {
        this.makeFile(command)
      } else if (command === '3.17.7') {
        this.makeFile(command)
      }
    },

    handleMake71File() {
      this.makeFile(7.1)
    },

    getKtvHasAuth() {
      this.$utils.httpGet(
        this.$config.api.AWAKEN_CONFIG,
        {
          appid: this.$route.params.appId,
        },
        {
          success: (res) => {
            if (res.data || res.data.awakeConfig) {
              let config = JSON.parse(res.data.awakeConfig || '{}')
              if (String(config.switch) === '1') {
                this.ktvHasAuth = true
              }
            }
          },
          error: (err) => {},
        }
      )
    },
  },
  components: {
    Upload,
    ResourceDialog,
    [InputNumber.name]: InputNumber,
  },
}
</script>

<style scoped lang="scss">
.opt-wrap {
  display: flex;
  align-items: center;
  padding-bottom: 16px;
  border-bottom: 1px solid $grey007;
}
.template-btn {
  color: #555454;
  border: 1px solid #d5d8de;
  border-radius: 5px;
  background-color: transparent;
  &:hover {
    border: 1px solid $primary;
    color: $primary;
  }
}
.word-type {
  min-width: 33% !important;
  text-align: center;
  align-items: center;
  margin: 0 auto;
  justify-content: center;
  align-content: center;
  display: flex;
  &-select-div {
    margin-left: 2%;
  }
}
.word-type-select {
  min-width: 32% !important;
  width: 100% !important;
  flex: 1;
  margin: 0 auto;
  position: relative;
}
.el-pagination__total {
  position: relative;
  left: 5%;
}
.template-ul {
  padding-top: 16px;
}
.buy-section-3-header {
  display: flex;
  justify-content: space-around;
  align-items: center;
  height: 56px;
  background: #f2f5f7;
  border: 1px solid #e4e7ed;
  border-bottom: 0;
  border-right: 0;
  li {
    flex: 1;
    text-align: center;
    border-right: 1px solid #e4e7ed;
    height: 56px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  :deep(.el-input__inner) {
    border: 0;
    width: 120px;
    padding-right: 30px;
    background: #f2f5f7;
  }
}

.tab-content {
  border: 1px solid #e4e7ed !important;
  border-left: 0;
  height: auto !important;
  overflow-y: auto;
  &-list {
    border-bottom: 1px solid #e4e7ed !important;
  }
  &-list {
    display: flex;
    height: 56px;
    justify-content: space-around;
    align-items: center;
    .add-input {
      left: 1%;
      position: relative;
      .el-input {
        position: relative;
        .ic-r-plus {
          position: relative;
          /*line-height: 36px;*/
          display: inline-block;
          vertical-align: top;
          /*width: 50px;*/
          text-align: center;
          color: #d5d8de;
        }
      }
    }
    &:hover &-del {
      display: block;
    }
    &-del {
      width: 20%;
      position: relative;
      color: $grey4;
      font-size: 20px;
      cursor: pointer;
      display: none;
      margin: 0 auto;
      text-align: center;
      align-items: center;
      justify-content: center;
      flex: 1;
    }
    > div {
      flex: 1;
      text-align: center;
      .san-circle {
        width: 30px;
        height: 30px;
        margin: 0 auto;
        border-radius: 50%;
        background: #1784e9;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        .san.play {
          display: none;
        }
        .span.pause {
          display: inline-block;
        }
        .er.play {
          display: inline-block;
        }
        .er.pause {
          display: none;
        }
        .san {
          width: 0;
          height: 0;
          border-left: 10px solid transparent;
          border-right: 10px solid transparent;
          border-top: 10px solid transparent;
          border-bottom: 10px solid #fff;
          border-radius: 3px;
          display: inline-block;
          transform: rotate(90deg);
          position: relative;
          left: 7px;
        }
        .er {
          span {
            display: inline-block;
            width: 4px;
            height: 15px;
            background: #fff;
            margin-top: 5px;
          }
        }
      }
    }
  }
}

.make-list {
  &.make-list-border {
    border-top: 1px solid #e4e7ed;
    border-bottom: 1px solid #e4e7ed;
    .make-title {
      margin-top: 20px;
    }
  }
  .make-title {
    font-size: 20px;
    color: #262626;
    margin: 40px 0 12px;
  }
  > div {
    p {
      line-height: 28px;
      font-size: 14px;
      color: #8c8c8c;
      padding: 10px 0 5px 0;
      margin: inherit;
    }
  }
  .upload-btn {
    display: flex;
    margin: 25px 0 25px;
    cursor: pointer;
    &-span {
      color: #1784e9;
      display: inline-block;
      margin-right: 30px;
    }
  }
  .download-detail {
    color: #e96b6b;
    margin-bottom: 20px;
    span + span {
      display: inline-block;
      margin-left: 30px;
      line-height: 1;
      cursor: pointer;
    }
  }

  .resource-list {
    > span {
      display: inline-block;
      line-height: 25px;
    }
    .resource-time {
      color: #8c8c8c;
      width: 150px;
    }
    .resource-name {
      color: #262626;
    }
    .resource-btn {
      color: #1784e9;
      margin-left: 20px;
      cursor: pointer;
    }
  }

  .resource-list + .resource-list {
    margin-top: 14px;
  }
}
</style>
