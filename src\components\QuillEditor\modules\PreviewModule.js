// import Preview from './Preview'
import QuillEditor from '../QuillEditor.vue'
import Vue from 'vue'

class PreviewModule {
  constructor(quill, options) {
    this.quill = quill
    this.options = options
    this.quill.root.addEventListener('click', this.handleClick, false)
  }

  handleClick = (evt) => {
    if (
      evt.target &&
      evt.target.tagName &&
      evt.target.tagName.toUpperCase() === 'IMG'
    ) {
      // 点击了IMG元素，可能为图片，音频，视频，实现预览

      const type = evt.target.getAttribute('type')
      const content = evt.target.getAttribute('content')

      if (type === 'table-thumbnail' && content) {
        this.options &&
          this.options.handlers &&
          this.options.handlers.detail &&
          this.options.handlers.detail(content)
      }
    }
  }
}
export default PreviewModule
