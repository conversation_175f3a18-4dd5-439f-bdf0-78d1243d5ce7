<template>
  <div class="third_party_qa_content">
    <el-button plain size="small" class="debug_btn" @click="handleTest"
      >调试</el-button
    >
    <div class="switch_tip">
      <div class="switch">
        <span style="font-weight: 600; color: #262626">三方问答库开关：</span>
        <el-switch v-model="value1"> </el-switch>
      </div>
      <div class="tip">
        配置你的三方知识库的接口信息，在语音交互中直接调用，三方知识库的优先级高于文档问答，低于语句问答及关键词问答
      </div>
    </div>

    <el-form
      ref="thirdPartyForm"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      label-position="right"
      class="third_party_form"
    >
      <el-form-item label="接口地址：" prop="apiUrl">
        <el-input
          v-model="formData.apiUrl"
          placeholder="请输入文字"
          size="small"
        ></el-input>
      </el-form-item>

      <el-form-item label="鉴权信息：" prop="authInfo">
        <el-input
          v-model="formData.authInfo"
          placeholder="请输入文字"
          size="small"
        ></el-input>
      </el-form-item>

      <el-form-item label="知识库标识：" prop="modelId">
        <el-input
          v-model="formData.modelId"
          placeholder="请输入文字"
          size="small"
        ></el-input>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'DocQaPane',
  props: {
    modelInfo: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      value1: true,
      formData: {
        apiUrl: '',
        authInfo: '',
        modelId: '',
      },
      formRules: {
        apiUrl: [
          { required: true, message: '请输入接口地址', trigger: 'blur' },
        ],
        authInfo: [
          { required: true, message: '请输入鉴权信息', trigger: 'blur' },
        ],
        modelId: [
          { required: true, message: '请输入知识库标识', trigger: 'blur' },
        ],
      },
    }
  },
  created() {
    this.initFormData()
  },
  methods: {
    initFormData() {
      // 如果modelInfo中有配置信息，则填充表单
      if (this.modelInfo && this.modelInfo.config) {
        const { apiUrl, authInfo, modelId } = this.modelInfo.config
        this.formData = {
          apiUrl: apiUrl || '',
          authInfo: authInfo || '',
          modelId: modelId || '',
        }
      } else {
        // 重置表单
        this.formData = {
          apiUrl: '',
          authInfo: '',
          modelId: '',
        }
      }
      // 重置表单验证
      this.$nextTick(() => {
        if (this.$refs.thirdPartyForm) {
          this.$refs.thirdPartyForm.clearValidate()
        }
      })
    },
    handleTest() {
      this.$refs.thirdPartyForm.validate((valid) => {
        if (valid) {
          this.$message.success('开始测试连接...')
          setTimeout(() => {
            this.$message.success('连接测试成功')
          }, 1000)
        }
      })
    },
    validate() {
      return new Promise((resolve) => {
        this.$refs.thirdPartyForm.validate((valid) => {
          resolve(valid ? this.formData : false)
        })
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.third_party_qa_content {
  .switch_tip {
    margin: 20px 0;

    .tip {
      color: #b8bcbd;
      margin-top: 5px;
    }
  }

  :deep(.el-form) {
    .el-form-item {
      .el-form-item__label {
        padding: 0;
        &::before {
          display: none;
        }
      }
    }
  }
}
</style>
