<template>
  <os-page :options="pageOptions" v-loading="loading">
    <studio-skill-header-right slot="btn" />
    <div class="os-scroll">
      <div class="mgt32 mgb24" style="font-size: 0">
        <el-dropdown
          trigger="click"
          @command="handleCommand"
          placement="bottom-start"
        >
          <el-button size="small">
            批量操作
            <i class="ic-r-triangle-down el-icon--right" />
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="cover">
              <upload
                :qaId="businessId"
                :options="cover"
                :limitCount="limitCount"
                @setLoad="setLoad"
                @uploadSuccess="uploadSuccess"
              ></upload>
            </el-dropdown-item>
            <!-- <el-dropdown-item command="questioning">
              <upload
                :qaId="qaId"
                :options="addOnly"
                :limitCount="limitCount"
                @setLoad="setLoad"
                @getQaPair="getQaPair(1)"
              ></upload>
            </el-dropdown-item> -->
            <!-- <el-dropdown-item command="export">导出问答</el-dropdown-item> -->
            <el-dropdown-item command="down">下载模版</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
      <ul class="reply-container">
        <li>
          <div class="info-brief">
            <div class="slots">dsdfsfsdfs胜多负少的</div>
            <div class="replies">
              <inteligient-rich-input
                key="111"
                placeholder=""
                :value="{
                  text: 'sdfdsfds测试',
                  labels: [],
                  changed: false,
                }"
                :showAdd="false"
                :showSwitch="false"
                :disabled="true"
                :edit="true"
                :editIndex="0"
                :hasSlot="false"
              >
              </inteligient-rich-input>
            </div>
          </div>
          <div class="info-detail">
            <skill-reply
              :intentionObj="intention"
              :subAccountEditable="subAccountEditable"
            ></skill-reply>
            <div>
              <el-button type="primary" size="small" @click="onOk(index)"
                >保存</el-button
              >
            </div>
          </div>
        </li>
        <li v-for="(item, index) in replies" :key="index">
          <div class="info-brief">
            <div class="slots">{{ item.str1 }}</div>
            <div class="replies" v-if="item.collapse">
              <inteligient-rich-input
                :key="index"
                placeholder=""
                :value="{
                  text: item.str2,
                  labels: [],
                  changed: false,
                }"
                :showAdd="false"
                :showSwitch="false"
                :disabled="true"
                :edit="true"
                :editIndex="0"
                :hasSlot="false"
              >
              </inteligient-rich-input>
            </div>
            <div class="operation">
              <i
                class="ic-r-edit"
                @click="onEdit(index)"
                :style="{ visibility: item.collapse ? 'visible' : 'hidden' }"
              ></i
              >&nbsp;
              <i class="ic-r-delete"></i>
            </div>
          </div>
          <div class="info-detail" v-if="!item.collapse">
            <skill-reply
              :intentionObj="intention"
              :subAccountEditable="subAccountEditable"
            ></skill-reply>
            <div>
              <el-button type="primary" size="small" @click="onOk(index)"
                >保存</el-button
              >
              <el-button type="default" size="small" @click="onCancel(index)"
                >取消</el-button
              >
            </div>
          </div>
        </li>
      </ul>
    </div>
  </os-page>
</template>

<script>
import { mapGetters } from 'vuex'
import Upload from './uploadReply'
import skillReply from './skillReply.vue'
import InteligientRichInput from '../skill/referSlots/inteligientRichInput.vue'

export default {
  name: 'skill-replies',
  data() {
    return {
      pageOptions: {
        title: '回复语管理',
        loading: false,
      },
      loading: false,
      cover: {
        isCover: true,
        text: '批量覆盖',
      },
      intention: [],
      replies: [
        { str1: 'test1111', str2: 'test2222', collapse: true },
        { str1: 'test222', str2: 'test3333', collapse: true },
      ],
    }
  },
  computed: {
    ...mapGetters({
      businessId: 'studioSkill/id',
      skill: 'studioSkill/skill',
      limitCount: 'aiuiApp/limitCount',
      subAccount: 'user/subAccount',
      subAccountSkillAuths: 'studioSkill/subAccountSkillAuths',
    }),
    subAccountEditable() {
      let auth = this.subAccountSkillAuths[this.businessId]
      if (auth == 2) {
        return false
      } else {
        return true
      }
    },
  },

  created() {},
  methods: {
    setLoad() {},
    uploadSuccess() {},
    handleCommand() {},
    onSave(index) {
      // 保存调用
      console.log('**********' + index)
      this.replies[index].collapse = true
    },
    onCancel(index) {
      console.log('22222' + index)
      this.replies[index].collapse = true
    },
    onEdit(index) {
      this.replies[index].collapse = false
    },
  },
  components: {
    Upload,
    skillReply,
    InteligientRichInput,
  },
}
</script>

<style lang="scss" scoped>
.reply-container {
  > li {
    border: 1px solid #e4e6ea;
    border-radius: 2px;
    padding: 17px 23px;
    &:hover {
      .ic-r-delete {
        visibility: visible;
      }
    }
  }
  li + li {
    margin-top: 18px;
  }

  .info-brief {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .slots {
      width: 30%;
      font-size: 14px;
      font-weight: 400px;
      color: #262626;
    }
    .replies {
      width: 50%;
      font-size: 14px;
      font-weight: 400px;
      color: #262626;
    }
    .operation {
      width: 20%;
      min-width: 100px;
      color: $primary;
      text-align: right;
    }
  }
  .info-detail {
    margin-top: 18px;
  }
}
.ic-r-edit,
.ic-r-delete {
  font-size: 20px;
  cursor: pointer;
}
.ic-r-delete {
  visibility: hidden;
}
</style>
