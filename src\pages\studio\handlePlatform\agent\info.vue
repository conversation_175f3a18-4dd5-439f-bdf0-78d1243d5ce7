<template>
  <os-page :options="pageOptions">
    <studio-agent-header-right slot="btn" />

    <el-form
      :model="form"
      ref="form"
      label-position="left"
      label-width="100px"
      :rules="rules"
      class="agent-form"
    >
      <el-form-item label="智能体id" prop="agentId">
        <!-- <el-input v-model="form.agentId" disabled></el-input> -->
        <span>{{ form.agentId }}</span>
      </el-form-item>

      <el-form-item label="智能体名称" prop="agentName">
        <el-input
          placeholder="支持中英文/数字/小数点/短横线/下划线,不超过32个字符"
          v-model="form.agentName"
        ></el-input>
      </el-form-item>

      <el-form-item label="描述" prop="agentDesc">
        <el-input
          type="textarea"
          :rows="3"
          placeholder="请输入智能体描述,不超过250个字符"
          v-model="form.agentDesc"
        ></el-input>
      </el-form-item>

      <el-form-item label="智能体类型" prop="agentType">
        <el-select v-model="form.agentType">
          <el-option
            v-for="item in agentTypeList"
            :key="item.index"
            :label="item.name"
            :value="item.code"
            :disabled="item.code == 1 || item.code == 3"
          ></el-option>
        </el-select>
      </el-form-item>

      <!-- <el-form-item label="落域" prop="classifyType">
        <el-select v-model="form.classifyType" disabled>
          <el-option
            v-for="item in classify_type_list"
            :key="item.code"
            :label="item.name"
            :value="item.code"
          ></el-option>
        </el-select>
      </el-form-item> -->

      <el-form-item>
        <el-button type="primary" @click="saveAgent">保存</el-button>

        <el-popover
          width="240"
          placement="bottom-start"
          trigger="click"
          v-model="visible"
        >
          <div class="give-up-save-title">
            <i class="ic-r-exclamation" />
            <span>确定放弃修改吗？</span>
          </div>
          <p class="give-up-save-content">所修改的内容将会丢失。</p>
          <div style="text-align: right; margin: 0">
            <el-button
              size="mini"
              style="min-width: 64px"
              @click="visible = false"
              >取消</el-button
            >
            <el-button
              type="danger"
              size="mini"
              style="min-width: 84px"
              @click="noSave"
              >放弃修改</el-button
            >
          </div>

          <el-button slot="reference" class="btn-give-up" type="text"
            >放弃修改</el-button
          >
        </el-popover>
      </el-form-item>
    </el-form>
  </os-page>
</template>

<script>
export default {
  name: 'AiuiWebInfo',

  data() {
    return {
      pageOptions: {
        title: '基本信息',
        loading: true,
      },

      visible: false,
      agentTypeList: [],
      classify_type_list: [],
      form: {
        agentId: null,
        agentName: null,
        agentDesc: null,
        agentType: null,
        classifyType: 0,
      },

      rules: {
        agentName: [
          {
            required: true,
            message: '请输入智能体名称',
            max: 32,
            trigger: 'blur',
          },
        ],
        agentDesc: [
          { required: true, message: '请输入智能体描述', trigger: 'blur' },
          { max: 250, message: '描述不能超过250个字符', trigger: 'blur' },
        ],
        agentType: [{ required: true, message: '请选择类型', trigger: 'blur' }],
        // classifyType: [{ required: true, message: '请选择落阈类型', trigger: 'blur' }],
        agentId: [{ required: false, trigger: 'blur' }],
      },
    }
  },

  created() {
    this.getAgentInfo()
    this.getAgentTypeList()
    // this.getClassifyType()
  },

  methods: {
    saveAgent() {
      const params = {
        agentId: this.form.agentId,
        agentName: this.form.agentName,
        agentDesc: this.form.agentDesc,
        agentType: this.form.agentType,
        classifyType: this.form.classifyType,
      }
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.$utils.httpPost(
            this.$config.api.AGENT_EDIT,
            JSON.stringify(params),
            {
              config: {
                headers: {
                  'Content-Type': 'application/json;charset=UTF-8',
                },
              },
              success: (res) => {
                if (res.code == 0) {
                  this.$message.success(res.desc)
                  this.getAgentInfo()
                  this.$refs.form.clearValidate()
                  // location.reload()
                }
              },
              error: (err) => {
                this.$message.error(err.desc)
              },
            }
          )
        }
      })
    },
    noSave() {
      this.getAgentInfo()
      this.visible = false
    },

    giveEdit() {
      this.visible = true
    },
    getAgentInfo() {
      console.log('info里面的get')
      const params = {
        agentId: this.$route.params.agentId,
      }
      this.$utils.httpPost(
        this.$config.api.AGENT_DETAIL,
        JSON.stringify(params),
        {
          config: {
            headers: {
              'Content-Type': 'application/json;charset=UTF-8',
            },
          },
          success: (res) => {
            if (res.code == '0') {
              this.form = {
                agentId: res.data.agentId,
                agentName: res.data.agentName,
                agentDesc: res.data.agentDesc,
                agentType: res.data.agentType,
                classifyType: res.data.classifyType,
              }
              this.visible = false
              this.pageOptions.loading = false
            }
          },
          error: (err) => {
            this.$message.error(err.desc)
            this.pageOptions.loading = false
          },
        }
      )
    },

    getAgentTypeList() {
      this.$utils.httpGet(
        this.$config.api.AGENT_TYPE_LIST,
        {},
        {
          success: (res) => {
            if (res.code == 0) {
              this.agentTypeList = res.data
            }
          },
          error: (err) => {},
        }
      )
    },

    getClassifyType() {
      this.$utils.httpGet(
        this.$config.api.AGENT_CLASSIFY_TYPE,
        {},
        {
          success: (res) => {
            if (res.code == 0) {
              this.classify_type_list = res.data
            }
          },
          error: (err) => {},
        }
      )
    },
  },
}
</script>

<style lang="scss" scoped>
.agent-form {
  margin-top: 10px;
}

.give-up-save-title {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  i {
    font-size: 16px;
    color: $warning;
  }
  span {
    margin-left: 8px;
    font-size: 16px;
    font-weight: 500;
  }
}
.give-up-save-content {
  padding-left: 24px;
  margin-bottom: 16px;
}
.btn-give-up {
  color: $primary;
  &:hover {
    color: $primary;
  }
}
</style>
