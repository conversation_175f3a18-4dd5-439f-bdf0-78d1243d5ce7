<template>
  <el-dialog
    title="设备人设"
    :visible.sync="dialog.show"
    width="616px"
    :append-to-body="true"
    custom-class="new-style-dialog"
  >
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      label-width="80px"
      :hide-required-asterisk="false"
    >
      <el-form-item label="姓名" prop="personaName">
        <el-input
          v-model="form.personaName"
          placeholder="请填写设备人设姓名，例如：蓝小飞"
          :maxlength="100"
        ></el-input>
      </el-form-item>

      <el-form-item label="开发者" prop="personaFather">
        <el-input
          v-model="form.personaFather"
          placeholder="请填写设备人设开发者，例如：科大讯飞"
          :maxlength="100"
        ></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="save">保存</el-button>
    </span>
  </el-dialog>
</template>
<script>
export default {
  props: {
    dialog: {
      type: Object,
      default: () => {
        return {
          show: false,
        }
      },
    },
    personaConfig: Object,
  },
  data() {
    return {
      form: {
        personaName: '',
        personaFather: '',
      },
      rules: {
        personaName: [
          { required: true, message: '请填写设备人设姓名', trigger: 'change' },
          // { min: 3, max: 5, message: '长度在 3 到 5 个字符', trigger: 'blur' },
        ],
        personaFather: [
          {
            required: true,
            message: '请填写设备人设开发者',
            trigger: 'change',
          },
        ],
      },
    }
  },
  watch: {
    'dialog.show'(val) {
      if (val) {
        if (this.personaConfig) {
          this.form.personaName = this.personaConfig.personaName
          this.form.personaFather = this.personaConfig.personaFather
        }
      } else {
        this.$refs.form.resetFields()
        this.current = ''
      }
    },
  },
  methods: {
    save() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          let cfg = {
            personaName: this.form.personaName,
            personaFather: this.form.personaFather,
          }
          this.$emit('setPersonaConfig', cfg)
          this.dialog.show = false
        } else {
          return false
        }
      })
    },
  },
}
</script>
<style lang="scss" scoped></style>
