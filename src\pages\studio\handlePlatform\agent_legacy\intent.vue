<template>
  <os-page :options="pageOptions">
    <studio-agent-header-right-legacy slot="btn" />

    <div class="os-scroll">
      <div class="mgt32 mgb24" style="font-size: 0">
        <el-button
          icon="ic-r-plus"
          type="primary"
          size="small"
          @click="openCreateIntent"
        >
          创建意图
        </el-button>
        <el-button
          class="mgr16"
          type="primary"
          size="small"
          @click="referOfficialIntentions"
        >
          引用官方意图
        </el-button>
      </div>
      <div class="mgb24" @keyup.enter="getIndentList(1)">
        <el-input
          class="search-area"
          placeholder="搜索意图"
          v-model="intentionSearchName"
        >
          <i
            slot="suffix"
            class="el-input__icon el-icon-search search-area-btn"
            @click="getIndentList(1)"
          />
        </el-input>
      </div>

      <os-table
        class="intentions-table gutter-table-style secondary-table"
        :tableData="tableData"
        :height="'calc(100vh - 264px)'"
        @del="del"
        @edit="edit"
        @change="getIndentList"
        @row-click="toCorpus"
      >
        <el-table-column prop="intentName" label="意图名称" min-width="150px">
          <template slot-scope="scope">
            <div class="intent-zhname ib">
              {{ scope.row.intentName }}
            </div>
            <el-tooltip
              v-if="scope.row.quote"
              effect="dark"
              content="官方意图"
              placement="top"
            >
              <div class="intent-tag ib">官</div>
            </el-tooltip>
          </template>
        </el-table-column>

        <el-table-column prop="intentNameEn" label="英文标识" min-width="150px">
          <template slot-scope="scope">
            <div class="intent-name">
              {{ scope.row.intentNameEn }}
            </div>
          </template>
        </el-table-column>

        <el-table-column
          label="意图描述"
          prop="intentDesc"
          min-width="260px"
        ></el-table-column>

        <el-table-column label="操作">
          <template slot-scope="scope">
            <i
              class="ic-r-edit cell-handle-ic"
              @click.prevent.stop="edit(scope.row)"
              v-if="!scope.row.quote"
            ></i>

            <i
              class="cell-handle-hovershow ic-r-delete cell-handle-ic"
              @click.prevent.stop="del(scope.row)"
            ></i>
          </template>
        </el-table-column>
      </os-table>
    </div>
    <IntentDialog ref="IndentDialog" @refresh="refresh" />
  </os-page>
</template>

<script>
import StudioAgentHeaderRightLegacy from '../../../../components/studioAgentHeaderRightLegacy.vue'
import IntentDialog from './intentDialog.vue'
export default {
  name: 'AiuiWebIndent',

  data() {
    return {
      pageOptions: {
        title: '意图',
        loading: false,
      },

      tableData: {
        loading: true,
        total: 0,
        page: 1,
        size: 10,
        // handles: ['del', 'edit'],
        switchOptions: {
          column: 'type',
          active: 2,
          inactive: 3,
          activeText: '入口',
          inactiveText: '对话',
        },
        // handleColumnText: '操作',
        list: [],
      },

      intentionSearchName: null,
    }
  },

  components: {
    IntentDialog,
    StudioAgentHeaderRightLegacy,
  },

  mounted() {
    this.getIndentList(1)
  },

  methods: {
    getIndentList(page) {
      const params = {
        agentId: this.$route.params.agentId,
        intentName: this.intentionSearchName,
        pageIndex: page || this.tableData.page,
        pageSize: this.tableData.size,
      }
      this.$utils.httpPost(
        this.$config.api.AGENT_INTENT_TABLE_LIST_OLD,
        JSON.stringify(params),
        {
          config: {
            headers: {
              'Content-Type': 'application/json;charset=UTF-8',
            },
          },
          success: (res) => {
            if (res.code == '0') {
              this.tableData.list = res.data.data
              this.tableData.page = res.data.pageIndex
              this.tableData.size = res.data.pageSize
              this.tableData.total = res.data.totalSize
              this.tableData.loading = false
            }
          },
          error: (err) => {
            this.$message.error(err.desc)
            this.tableData.loading = false
          },
        }
      )
    },

    openCreateIntent() {
      this.$refs.IndentDialog.show(this.$route.params.agentId)
    },

    referOfficialIntentions() {
      this.$router.push({
        name: 'agent-intent-offical-legacy',
      })
    },

    toCorpus(data) {
      console.log(data, 'row-click的data')
      if (!data.quote) {
        this.$router.push({
          name: 'agent-intent-detail-legacy',
          params: {
            intentId: data.intentId,
          },
        })
      }
    },

    edit(data) {
      // this.$refs.IndentDialog.show(this.$route.params.agentId, data)
      console.log(data, 'edit的data')
      if (!data.quote) {
        this.$router.push({
          name: 'agent-intent-detail-legacy',
          params: {
            intentId: data.intentId,
          },
        })
      }
    },

    del(data) {
      let self = this
      this.$confirm('意图删除后不可恢复，请谨慎操作。', `确定删除该意图吗?`, {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        confirmButtonClass: 'el-button--danger',
        type: 'warning',
        showClose: false,
      })
        .then(() => {
          if (data.quote) {
            const params = {
              agentId: this.$route.params.agentId,
              intentId: data.intentId,
            }
            self.delOfficalIntent(params)
          } else {
            const params = {
              agentId: data.agentId,
              intentId: data.intentId,
            }
            self.delIndent(params)
          }
        })
        .catch(() => {})
    },
    delIndent(params) {
      this.$utils.httpPost(
        this.$config.api.AGENT_INDENT_DELETE_OLD,
        JSON.stringify(params),
        {
          config: {
            headers: {
              'Content-Type': 'application/json;charset=UTF-8',
            },
          },
          success: (res) => {
            if (res.code == '0') {
              this.$message.success('操作成功')
              this.getIndentList(1)
            }
          },
          error: (err) => {
            this.$message.error(err.desc)
          },
        }
      )
    },

    delOfficalIntent(params) {
      // const params = {
      //   agentId: this.$route.params.agentId,
      //   intentId: data.intentId,
      //   quoteFlag: false,
      // }
      this.$utils.httpPost(
        this.$config.api.AGENT_OFFICAL_INTENT_QUOTE_OLD,
        JSON.stringify({ ...params, quoteFlag: false }),
        {
          config: {
            headers: {
              'Content-Type': 'application/json;charset=UTF-8',
            },
          },
          success: (res) => {
            if (res.code == '0') {
              this.$message.success('操作成功')
              this.getIndentList(1)
            }
          },
          error: (err) => {
            this.$message.error(err.desc)
          },
        }
      )
    },

    refresh() {
      this.getIndentList(1)
    },
  },
}
</script>

<style lang="scss" scoped>
.intentions-table {
  :deep(.el-table__body-wrapper) {
    border-bottom: 1px solid #d8e0ed !important;
  }
}
</style>
