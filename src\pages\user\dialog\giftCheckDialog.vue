<template>
  <el-dialog
    :visible.sync="dialog.show"
    width="30.83%"
    border-radius="20px"
    :before-close="handleClose"
    class="gc-d-dialog"
  >
    <div class="gc-d-main">
      <div>
        <h3 class="second-tite">{{content[dialog.type]}}</h3>
      </div>
      <div class="div-btn">
        <el-button
          type="primary"
          class="btn-check"
          @click="closeDialog"
        >
          确定
        </el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script>
    export default {
        name: "giftCheckDialog",
      props: {
        dialog: {
          type: Object,
          default: {}
        }
      },
      data () {
        return {
          content: {
            1: "当前账号没有实名认证，请进行实名认证。",
            2: "你不满足新用户免费套餐领取条件。"
          },
        }
      },
      methods: {
        closeDialog () {
          this.dialog.show = false
        },
        handleClose(done) {
          done();
        },
      }
    }
</script>

<style>
  .el-dialog__body {
    padding-top: 0 !important;
  }
</style>
<style scoped lang="scss">
.gc-d-dialog {
  .first-title {
    text-align: center;
    &-inner {
      border-bottom: 1px solid #e4e7ed;
      width: 100%;
      margin: 0 auto;
    }
  }
}
.gc-d-main {
  color: #333333;
  margin-bottom: 3.13% !important;
  display: block;
  .div-btn {
    text-align: center;
    .btn-check {
      box-shadow: 0px 2px 4px 0px rgba(23, 132, 233, 0.2);
      border-radius: 4px;
      margin-top: 15.24%;
      margin-bottom: 12%;
      position: relative;
      padding: 4%;
      max-width: 20.47% !important;
      min-width: 40.47% !important;
    }
  }
  .second-tite {
    margin: 0 auto;
    margin-top: 6%;
    text-align: center;
    display: flex;
    justify-content: center;
    width: 100%;
    align-content: center;
    align-items: center;
  }
  .third-title {
    line-height: 18px;
    margin: 0 auto;
    margin-top: 1%;
  }

}

</style>
