<template>
  <div class="ota-container">
    <!-- <div slot="btn" class="header-app-id">APPID: {{ appId }}</div> -->
    <div class="option-header">
      <el-button type="primary" size="small" @click="handleAddOTAItem"
        >新增固件</el-button
      >
    </div>
    <os-table
      :tableData="tableData"
      class="ota-list"
      :height="'calc(100vh - 190px)'"
      style="margin-bottom: 56px"
      empty-text="当前未上传固件，请新增固件"
    >
      <el-table-column prop="name" label="固件名称" width="150">
      </el-table-column>
      <el-table-column prop="version" label="固件版本" width="100">
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" width="180">
      </el-table-column>
      <el-table-column prop="description" label="版本描述">
        <template slot-scope="scope">
          <el-tooltip effect="light" placement="top-start">
            <div slot="content" style="max-width: 400px">
              {{ scope.row.description }}
            </div>
            <div class="description-container">
              {{ scope.row.description }}
            </div>
          </el-tooltip>
        </template> </el-table-column
      ><el-table-column prop="status" label="验证状态" width="150">
        <template slot-scope="scope">
          <el-tag type="info" v-if="scope.row.status === 1">未验证</el-tag>
          <el-tag type="danger" v-else-if="scope.row.status === 2"
            >验证未发布</el-tag
          >
          <el-tag type="success" v-else>已发布</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="280">
        <template slot-scope="scope">
          <el-button
            size="small"
            type="text"
            :disabled="scope.row.status === 3 ? 'disabled' : false"
            @click="openVerifyOTAModal(scope.row)"
            >验证固件</el-button
          >
          <el-button
            size="small"
            type="text"
            :disabled="scope.row.status !== 2 ? 'disabled' : false"
            @click="openPublishOTAModal(scope.row)"
            >发布</el-button
          >
          <el-button
            size="small"
            type="text"
            :disabled="scope.row.status !== 1 ? 'disabled' : false"
            @click="openEditOTAModal(scope.row)"
            >编辑</el-button
          >
          <el-button
            size="small"
            type="text"
            :disabled="scope.row.status === 3 ? 'disabled' : false"
            @click="openDeleteOTAModal(scope.row)"
            >删除</el-button
          >
          <el-button
            size="small"
            type="text"
            @click="openDetailOTAModal(scope.row)"
            >详情</el-button
          >
          <!-- <el-button type="text" :disabled="scope.row.type === 2"
            >文字按钮</el-button
          > -->
        </template>
      </el-table-column>
    </os-table>

    <VerifyOTAModal
      @getList="getList"
      :modalParam="verifyModalParam"
    ></VerifyOTAModal>
    <DeleteOTAModal
      @getList="getList"
      :modalParam="deleteModalParam"
    ></DeleteOTAModal>
    <DetailOTAModal :modalParam="detailModalParam"></DetailOTAModal>
    <PublishOTAModal
      @getList="getList"
      :modalParam="publishModalParam"
    ></PublishOTAModal>

    <!-- <AddOTAModal @getList="getList" :modalParam="addModalParam"></AddOTAModal> -->
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import VerifyOTAModal from './dialog/otaUpdate/verifyOTAModal'
import DeleteOTAModal from './dialog/otaUpdate/deleteOTAModal'
import DetailOTAModal from './dialog/otaUpdate/detailOTAModal'
import PublishOTAModal from './dialog/otaUpdate/publishOTAModal'
// import AddOTAModal from './dialog/otaUpdate/otaUpdateFormModal'

export default {
  name: 'app-ota-update',
  components: {
    VerifyOTAModal,
    DeleteOTAModal,
    DetailOTAModal,
    PublishOTAModal,
    // AddOTAModal,
  },
  data() {
    return {
      pageOptions: {
        title: '固件升级管理',
        loading: false,
        returnBtn: false,
      },
      tableData: {
        loading: false,
        // total: 0,
        // page: 1,
        // size: 10,
        list: [],
      },
      verifyModalParam: {
        show: false,
        item: {},
      },
      deleteModalParam: {
        show: false,
        item: {},
      },
      detailModalParam: {
        show: false,
        item: {},
      },
      publishModalParam: {
        show: false,
        item: {},
      },
      addModalParam: {
        show: false,
        item: {},
      },
    }
  },
  methods: {
    // 添加固件
    handleAddOTAItem() {
      this.$router.push({
        name: `${this.subAccount ? 'sub-' : ''}app-ota-update-form`,
      })
    },
    // 验证固件
    openVerifyOTAModal(item) {
      this.verifyModalParam = {
        show: true,
        item,
      }
    },
    // 发布固件
    openPublishOTAModal(item) {
      this.publishModalParam = {
        show: true,
        item,
      }
    },
    // 删除固件
    openDeleteOTAModal(item) {
      this.deleteModalParam = {
        show: true,
        item,
      }
    },
    // 详情固件
    openDetailOTAModal(item) {
      this.detailModalParam = {
        show: true,
        item,
      }
    },
    // 编辑固件
    openEditOTAModal(item) {
      this.$router.push({
        name: `${this.subAccount ? 'sub-' : ''}app-ota-update-form`,
        params: { ...item },
      })
    },

    // 刷新列表
    getList() {
      this.tableData.loading = true
      this.$utils.httpGet(
        this.$config.api.OTA_LIST,
        {
          appid: this.appId,
        },
        {
          success: (res) => {
            this.tableData.loading = false
            if (res.flag) {
              this.tableData.list = res.data
            } else {
              this.$message.error(res.desc)
            }
           
          },
          error: () => {
            this.tableData.loading = false
          },
        }
      )
    },
  },
  mounted() {
    this.getList()
  },
  computed: {
    ...mapGetters({
      subAccount: 'user/subAccount',
    }),
    appId() {
      return this.$route.params.appId
    },
  },
}
</script>

<style scoped lang="scss">
.ota-container {
  padding: 0 20px;
}
.header-app-id {
  font-size: 18px;
}
.option-header {
  padding: 15px 0;
  display: flex;
  flex-direction: row-reverse;
}
.ota-list button {
  min-width: 0;
  &[disabled='disabled'] {
    color: #c0c4cc;
    &:hover {
      color: #c0c4cc;
    }
  }
}
.description-container {
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
