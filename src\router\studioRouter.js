import DefaultLayout from '@L/defaultLayout'
import StudioSkillLayout from '@L/studioSkillLayout'
import StudioAgentLayout from '@L/studioAgentLayout'
import StudioCharacterLayout from '@L/studioCharacterLayout'
import StudioQaBankLayout from '@L/studioQaBankLayout'
import StudioKnowledgeLayout from '@L/studioKnowledgeLayout'
import StudioDocQaLayout from '@L/studioDocQaLayout'

import StudioQaKeyLayout from '@L/studioQaKeyLayout'

import aiuiHome from '@L/aiuiHome'
import aiuiDefaultLayout from '@L/aiuiDefaultLayout'

import AiuiMenu from '@C/aiuiMenu'

export default [
  {
    path: '/studio',
    component: aiuiDefaultLayout,
    meta: {
      metaInfo: {
        title: '自定义业务控制台-AIUI开放平台',
        description:
          '自定义业务,快速接入业务知识,让语音问答灵活可控。AIUI是以讯飞星火大模型为核心的人机交互开发平台，具备多模态唤醒、虚拟人驱动、多语种识别、超拟人合成、声音复刻等特性。广泛应用于手机、电视、机器人扫读笔、语音购票等智能硬件设备上。提供SDK、Websocket、硬件模组等，接入集成简单，开箱即用。',
        keywords:
          '知识问答,大模型知识库,自定义技能,AIUI,讯飞语音交互,讯飞语义理解,AIUI人机交互,全链路语音交互,全路人机交互,讯飞人机交互,讯飞自然语言理解,全双工交互,语音唤醒,语音助手开发,语音交互接入,接入语音交互,语音交互控制,语音交互设计,讯飞人工智能,讯飞AIUI开放平台,讯飞云平台,讯飞星火大模型,星火交互大模型,多模态唤醒,虚拟人互动,数字人互动,多语种识别,超拟人合成,声音复刻,硬件模组,讯飞星火,人机交互,智能硬件,消费电子语音交互,消费电子人机交互,手机语音交互,电视语音交互,机器人语音交互,地铁火车轨道交通语音交互,PC语音助手,语音软硬件,语音交互控制',
      },
    },
    children: [
      {
        path: '/',
        name: 'studio-skill', // 技能工作室
        redirect: {
          name: 'studio-handle-platform-skills',
          params: { nav: '/studio/skill' },
        },
      },
      {
        path: 'skill',
        name: 'studio-handle-platform-skills', // 控制台-我的技能
        meta: {
          metaInfo: {
            title: '我的技能-AIUI开放平台',
          },
        },
        component: () => import('@P/studio/handlePlatform/skill/index'),
      },
      {
        path: 'agent',
        name: 'studio-handle-platform-agent',
        meta: {
          metaInfo: {
            title: '我的智能体-AIUI开放平台',
          },
        },
        component: () => import('@P/studio/handlePlatform/agent/index'),
        // children: [
        //   {
        //     path: ':agentId',
        //     name: 'studio-handle-platform-agent-detail',
        //     component: () =>
        //       import('@P/studio/handlePlatform/agent/agentDetail.vue'),
        //   },
        // ],
      },
      {
        path: 'agent/:agentId/:agentType/:boardId?',
        name: 'studio-handle-platform-agent-detail',
        component: () =>
          import('@P/studio/handlePlatform/agent/agentDetail.vue'),
      },
      {
        path: 'agent/intentDetail/:intentId/:agentId/:intentVersion/:official', // 三方智能体的意图详情页
        name: 'studio-handle-platform-intent-detail',
        component: () =>
          import('@P/studio/handlePlatform/agent/intentDetail.vue'),
      },
      {
        path: 'agent/:agentId/:agentType/:intentId', // 模板智能体的意图里面的配置  嵌套表单那一块
        name: 'studio-handle-platform-agent-detail-config',
        component: () =>
          import('@P/studio/handlePlatform/agent/advancedConfig.vue'),
      },
      {
        path: 'source',
        name: 'studio-source',
        meta: {
          metaInfo: {
            title: '信源库',
          },
        },
        component: () => import('@P/studio/handlePlatform/source/index'),
      },
      {
        path: 'source/:sourceId',
        name: 'studio-source-detail',
        meta: {
          metaInfo: {
            title: '信源库',
          },
        },
        component: () => import('@P/studio/handlePlatform/source/detail'),
      },
      {
        path: 'role',
        name: 'studio-role',
        meta: {
          metaInfo: {
            title: '我的角色-AIUI开放平台',
          },
        },
        component: () => import('@P/studio/role/index'),
      },
      {
        path: 'role/:roleId',
        name: 'role', // 角色详情页
        meta: {
          metaInfo: {
            title: '我的角色-AIUI开放平台',
          },
        },
        component: () =>
          import(/* webpackChunkName: "role" */ '@P/studio/role/roleDetail'),
      },

      {
        path: 'entity',
        name: 'studio-handle-platform-entities', // 控制台-我的实体
        component: () => import('@P/studio/handlePlatform/entities'),
      },
      {
        path: 'auxiliary',
        name: 'studio-handle-platform-auxiliaries', // 控制台-我的辅助词
        component: () => import('@P/studio/handlePlatform/auxiliaries'),
      },
      {
        path: 'label',
        name: 'studio-handle-platform-labels', // 控制台-我的交互标签
        component: () => import('@P/studio/handlePlatform/labels'),
      },
      {
        path: 'character',
        name: 'studio-handle-platform-characters', // 控制台-设备人设
        meta: {
          metaInfo: {
            title: '我的设备人设-AIUI开放平台',
          },
        },
        component: () => import('@P/studio/handlePlatform/characters'),
      },
      {
        path: 'qaBank',
        name: 'studio-handle-platform-qabanks', // 控制台-我的问答库
        meta: {
          metaInfo: {
            title: '我的问答库-AIUI开放平台',
          },
        },
        component: () => import('@P/studio/handlePlatform/qabank/index'),
      },
      {
        path: 'qaDoc',
        name: 'studio-handle-platform-qadocs', // 控制台-文档问答
        component: () => import('@P/studio/handlePlatform/qadoc'),
      },

      // {
      //   path: 'qaBank/:qaId',
      //   name: 'qaBank', // 问答详情页
      //   component: () =>
      //     import(/* webpackChunkName: "qabank" */ '@P/studio/qabank'),
      // },
      // {
      //   path: 'keyQABank/:repoId/:qaId',
      //   name: 'keyQABank', // 问答详情页
      //   component: () => import('@P/studio/qabank/indexKeyQA'),
      // },
      {
        path: 'official-entities',
        name: 'studio-official-entities', // 官方实体
        component: () => import('@P/studio/handlePlatform/official-entities'),
      },
      {
        path: 'official-auxiliaries',
        name: 'studio-official-auxiliaries', // 官方辅助词
        component: () =>
          import(
            /* webpackChunkName: "studio-official-auxiliaries" */ '@P/studio/handlePlatform/official-auxiliaries'
          ),
      },
      {
        path: 'official-labels',
        name: 'studio-official-labels', // 官方交互标签
        component: () =>
          import(
            /* webpackChunkName: "studio-official-labels" */ '@P/studio/handlePlatform/official-labels'
          ),
      },
      {
        path: 'entity/:entityId',
        name: 'entity', // 实体详情页
        meta: {
          metaInfo: {
            title: '我的实体-AIUI开放平台',
          },
        },
        component: () =>
          import(/* webpackChunkName: "entity" */ '@P/studio/entity'),
      },
      {
        path: 'auxiliarie/:entityId',
        name: 'auxiliary', // 副助词详情页
        component: () =>
          import(/* webpackChunkName: "auxiliary" */ '@P/studio/auxiliary'),
      },
      {
        path: 'character/:characterId',
        name: 'character', // 人设详情页
        meta: {
          metaInfo: {
            title: '我的设备人设-AIUI开放平台',
          },
        },
        component: () =>
          import(/* webpackChunkName: "character" */ '@P/studio/character'),
      },
      {
        path: 'label/:labelId',
        name: 'label', // 标签详情页
        component: () =>
          import(/* webpackChunkName: "label" */ '@P/studio/label'),
      },

      {
        path: '/agent/legacy/:agentId',
        component: StudioAgentLayout,
        meta: {
          metaInfo: {
            title: '我的智能体-AIUI开放平台',
          },
        },

        children: [
          {
            path: '/',
            name: 'agent-nav', // 技能页
            redirect: { name: 'agent-info-legacy' },
          },
          {
            path: 'info',
            name: 'agent-info-legacy', // 基本信息页
            component: () =>
              import('@P/studio/handlePlatform/agent_legacy/info.vue'),
          },
          {
            path: 'intent',
            name: 'agent-intent-legacy',
            component: () =>
              import('@P/studio/handlePlatform/agent_legacy/intent.vue'),
          },
          {
            path: 'intent/:intentId',
            name: 'agent-intent-detail-legacy',
            component: () =>
              import('@P/studio/handlePlatform/agent_legacy/intentDetail.vue'),
          },

          {
            path: 'used',
            name: 'agent-used-legacy',
            component: () =>
              import('@P/studio/handlePlatform/agent_legacy/used.vue'),
          },
          {
            path: 'process',
            name: 'agent-process-legacy',
            component: () =>
              import('@P/studio/handlePlatform/agent_legacy/process.vue'),
          },

          {
            path: 'intent/offical', // 官方意图
            name: 'agent-intent-offical-legacy',
            component: () =>
              import('@P/studio/handlePlatform/agent_legacy/officalIntent.vue'),
          },
          {
            path: 'intent/corpus',
            name: 'agent-intent-corpus-legacy',
            component: () =>
              import('@P/studio/handlePlatform/agent_legacy/corpus.vue'),
          },
        ],
      },
    ],
  },
  {
    path: '/studio/skill/:skillId',
    component: StudioSkillLayout,
    meta: {
      metaInfo: {
        title: '我的技能-AIUI开放平台',
      },
    },
    children: [
      {
        path: '/',
        name: 'skill', // 技能页
        redirect: { name: 'skill-intentions' },
      },
      {
        path: 'info',
        name: 'skill-info', // 基本信息页
        component: () =>
          import(/* webpackChunkName: "skill-info" */ '@P/studio/skill/info'),
      },
      {
        path: 'intent',
        name: 'skill-intentions', // 意图页
        component: () =>
          import(
            /* webpackChunkName: "skill-intentions" */ '@P/studio/skill/intentions'
          ),
      },
      {
        path: 'intent/:intentId/replies',
        name: 'skill-replies', // 回复语管理页
        component: () =>
          import(
            /* webpackChunkName: "skill-replies" */ '@P/studio/skill/replies'
          ),
      },
      {
        path: 'referOfficialIntentions',
        name: 'refer-official-intentions', // 引用官方意图页
        component: () =>
          import(
            /* webpackChunkName: "refer-official-intentions" */ '@P/studio/skill/referOfficialIntentions'
          ),
      },
      {
        path: 'referedIntentions',
        name: 'skill-refered-intentions', // 引用的意图
        component: () =>
          import(
            /* webpackChunkName: "refered-intentions" */ '@P/studio/skill/referedIntentions'
          ),
      },
      {
        path: 'referedLabels',
        name: 'skill-refered-labels', // 引用的交互标签
        component: () =>
          import(
            /* webpackChunkName: "refered-labels" */ '@P/studio/skill/referedLabels'
          ),
      },
      {
        path: 'modify',
        name: 'skill-modifiers', // 自定义修饰语
        component: () =>
          import(
            /* webpackChunkName: "studio-modifiers" */ '@P/studio/skill/modifiers'
          ),
      },
      {
        path: 'entity',
        name: 'skill-entities', // 引用的实体页
        component: () =>
          import(
            /* webpackChunkName: "skill-entities" */ '@P/studio/skill/entities'
          ),
      },
      {
        path: 'auxiliary',
        name: 'skill-auxiliaries', // 引用的辅助词页
        component: () =>
          import(
            /* webpackChunkName: "skill-auxiliaries" */ '@P/studio/skill/auxiliaries'
          ),
      },
      {
        path: 'intent/:intentId',
        name: 'skill-intention', // 意图详情页
        component: () =>
          import(
            /* webpackChunkName: "skill-intention" */ '@P/studio/skill/intention'
          ),
      },

      {
        path: 'referedIntentions/:intentId/:quoteId',
        name: 'skill-intention-referenced', // 意图详情页
        component: () =>
          import(
            /* webpackChunkName: "skill-intention-referenced" */ '@P/studio/skill/intentionReferenced/index.vue'
          ),
      },
      {
        path: 'postProcess',
        name: 'skill-post-process', // 后处理
        component: () =>
          import(
            /* webpackChunkName: "skill-postprocess" */ '@P/studio/skill/postProcess'
          ),
      },
      {
        path: 'publish',
        name: 'skill-publish', // 发布页
        component: () =>
          import(
            /* webpackChunkName: "skill-publish" */ '@P/studio/skill/publish'
          ),
      },
      // 2020-01-08 推荐优化暂时下线
      {
        path: 'optimize',
        redirect: { name: 'skill-intentions' }, //下次上线时，需去除此行
        // name: 'skill-optimize', // 推荐优化页
        // component:() => import(/* webpackChunkName: "skill-optimize" */ '@P/studio/skill/optimize')
      },
      {
        path: 'version',
        name: 'skill-version', // 版本管理
        component: () =>
          import(
            /* webpackChunkName: "skill-version" */ '@P/studio/skill/version'
          ),
      },
      //业务定制
      {
        path: 'extend-info',
        name: 'extend-skill-info', // 基本信息页
        component: () =>
          import(
            /* webpackChunkName: "extend-skill-info" */ '@P/studio/skill/extend/info'
          ),
      },
      {
        path: 'extend-intentions',
        name: 'extend-skill-intentions', // 意图页
        component: () =>
          import(
            /* webpackChunkName: "extend-skill-intentions" */ '@P/studio/skill/extend/intentions'
          ),
      },
      {
        path: 'extend-intention/:intentId',
        name: 'extend-skill-intention', // 意图详情页
        component: () =>
          import(
            /* webpackChunkName: "extend-skill-intention" */ '@P/studio/skill/extend/intention'
          ),
      },
      {
        path: 'extend-entities',
        name: 'extend-skill-entities', // 引用的实体页
        component: () =>
          import(
            /* webpackChunkName: "extend-skill-entities" */ '@P/studio/skill/extend/entities'
          ),
      },
      {
        path: 'extend-entity/:addDictId/:delDictId',
        name: 'extend-skill-entity', // 定制的实体详情页
        component: () =>
          import(
            /* webpackChunkName: "extend-skill-entity" */ '@P/studio/skill/extend/extendEntitie'
          ),
      },
    ],
  },

  // {
  //   path: '/studio/agent/:agentId',
  //   // name: 'studio-handle-platform-agent-detail',
  //   component: AiuiMenu,
  //   // component: () => import('@P/studio/handlePlatform/agent/agentDetail.vue'),
  //   children: [
  //     {
  //       path: '/',
  //       name: 'studio-handle-platform-agent-detail',
  //       component: () =>
  //         import('@P/studio/handlePlatform/agent/agentDetail.vue'),
  //     },
  //   ],
  // },

  // {
  //   path: '/studio/character/:characterId',
  //   component: StudioCharacterLayout,
  //   children: [
  //     {
  //       path: '/',
  //       name: 'character', // 设备人设编辑页
  //       // redirect: { name: 'skill-intentions' },
  //       component: () =>
  //         import(/* webpackChunkName: "character" */ '@P/studio/character'),
  //     },
  //     {
  //       path: 'info',
  //       name: 'character-info', // 基本信息页
  //       component: () =>
  //         import(
  //           /* webpackChunkName: "character-info" */ '@P/studio/character/info'
  //         ),
  //     },

  //     {
  //       path: 'publish',
  //       name: 'character-publish', // 发布页
  //       component: () =>
  //         import(
  //           /* webpackChunkName: "character-publish" */ '@P/studio/character/publish'
  //         ),
  //     },

  //     {
  //       path: 'version',
  //       name: 'character-version', // 版本管理
  //       component: () =>
  //         import(
  //           /* webpackChunkName: "character-version" */ '@P/studio/character/version'
  //         ),
  //     },
  //   ],
  // },
  // 语句问答
  {
    path: '/studio/qaBank/:qaId',
    component: StudioQaBankLayout,
    meta: {
      metaInfo: {
        title: '语句问答-AIUI开放平台',
      },
    },
    children: [
      {
        path: '/',
        name: 'qaBank', // 语句问答编辑页
        component: () =>
          import(/* webpackChunkName: "qaBank" */ '@P/studio/qabank'),
      },
      {
        path: 'info',
        name: 'qaBank-info', // 基本信息页
        component: () =>
          import(/* webpackChunkName: "qaBank-info" */ '@P/studio/qabank/info'),
      },
      {
        path: 'publish',
        name: 'qaBank-publish', // 发布页
        component: () =>
          import(
            /* webpackChunkName: "qaBank-publish" */ '@P/studio/qabank/publish'
          ),
      },

      {
        path: 'version',
        name: 'qaBank-version', // 版本管理
        component: () =>
          import(
            /* webpackChunkName: "qaBank-version" */ '@P/studio/qabank/version'
          ),
      },
      // {
      //   path: 'version',
      //   name: 'qaBank-version', // 版本管理
      //   component: () =>
      //     import(
      //       /* webpackChunkName: "qaBank-version" */ '@P/studio/qabank/version'
      //     ),
      // },
    ],
  },
  {
    path: '/studio/knowledge/:repoId',
    name: 'knowledge', // 问答详情页
    component: StudioKnowledgeLayout,
    children: [
      {
        path: 'info',
        name: 'qaBank-knowledge', // 知识
        component: () =>
          import(
            /* webpackChunkName: "qaBank-knowledge-info" */ '@P/studio/qabank/knowledge'
          ),
      },
      {
        path: 'manage',
        name: 'qaBank-knowledge-manage',
        component: () =>
          import(
            /* webpackChunkName: "qaBank-knowledge-manage" */ '@P/studio/qabank/knowledge-manage'
          ),
      },
    ],
  },
  {
    path: '/studio/docqa/:repoId',
    name: 'docqa', // 问答详情页
    component: StudioDocQaLayout,
    children: [
      {
        path: 'info',
        name: 'qaBank-docqa', // 知识
        component: () =>
          import(
            /* webpackChunkName: "qaBank-docqa-info" */ '@P/studio/qabank/docqa'
          ),
      },
      {
        path: 'manage',
        name: 'qaBank-docqa-manage',
        component: () =>
          import(
            /* webpackChunkName: "qaBank-docqa-manage" */ '@P/studio/qabank/docqa-manage'
          ),
      },
      {
        path: 'document-manage',
        name: 'qaBank-document-manage',
        component: () =>
          import(
            /* webpackChunkName: "qaBank-document-manage" */ '@P/studio/qabank/documentManage'
          ),
      },
    ],
  },

  // 关键词问答
  {
    path: '/studio/keyQABank/:repoId/:qaId',
    component: StudioQaKeyLayout,
    meta: {
      metaInfo: {
        title: '关键词问答-AIUI开放平台',
      },
    },
    children: [
      {
        path: '/',
        name: 'keyQABank', // 语句问答编辑页
        component: () =>
          import(
            /* webpackChunkName: "keyQABank" */ '@P/studio/qabank/indexKeyQA'
          ),
      },
      {
        path: 'info',
        name: 'keyqa-info', // 基本信息页
        component: () =>
          import(/* webpackChunkName: "keyqa-info" */ '@P/studio/qabank/info'),
      },

      {
        path: 'publish',
        name: 'keyqa-publish', // 发布页
        component: () =>
          import(
            /* webpackChunkName: "keyqa-publish" */ '@P/studio/qabank/publish'
          ),
      },

      {
        path: 'version',
        name: 'keyqa-version', // 版本管理
        component: () =>
          import(
            /* webpackChunkName: "keyqa-version" */ '@P/studio/qabank/keyQaVersion'
          ),
      },
    ],
  },
  {
    path: '/studio/repo/:repoId',
    component: DefaultLayout,
    children: [
      {
        path: '/',
        name: 'qa-relations', // 知识库问答关系列表页
        component: () =>
          import(
            /* webpackChunkName: "qaRelationList" */ '@P/studio/repository'
          ),
      },
      {
        path: 'keywords',
        name: 'question-keywords', // 问法关键词
        component: () =>
          import(
            /* webpackChunkName: "questionKeywords" */ '@P/studio/repository/questionKeywords'
          ),
      },
      {
        path: ':themeId',
        name: 'qa-relation-detail', // 问答关系详情页
        component: () =>
          import(
            /* webpackChunkName: "qaRelationDetail" */ '@P/studio/repository/qaRelationDetail'
          ),
      },
      {
        path: 'create',
        name: 'create-qa-relation-page', // 问答关系详情页
        component: () =>
          import(
            /* webpackChunkName: "qaRelationDetail" */ '@P/studio/repository/qaRelationDetail'
          ),
      },
    ],
  },
]
