<template>
  <div id="video-layer" class="video-layer" v-if="show">
    <div class="layer"></div>

    <div class="dialog" :style="videoStyle">
      <div
        v-video-player:myVideoPlayer="playerOptions"
        class="video-player-box"
        :playsinline="true"
      ></div>
      <i class="ic-r-cross-thin" @click="close"></i>
    </div>
  </div>
</template>

<script>
import '../../../static/vue-video-player'
export default {
  // props: {
  //   width: '',
  //   height: '',
  //   videoStyle: {},
  //   videoSrc: '',
  // },
  data() {
    return {
      playerOptions: {
        autoplay: true,
        preload: 'auto',
        language: 'zh-CN',
        fluid: false, // 当true时，它将按比例缩放以适应其容器。
        sources: [
          {
            type: 'video/mp4',
            src: '',
          },
        ],
        poster: '', //你的封面地址
        controlBar: {
          playToggle: true, //左下角播放按钮显示与否
          progressControl: true, //控制进度条显示与否
          timeDivider: true,
          currentTimeDisplay: true,
          durationDisplay: true,
          remainingTimeDisplay: true, //剩余时间显示与否
          fullscreenToggle: true, //全屏按钮显示与否
          volumePanel: { inline: false }, //音量控制条竖着显示
        },
      },

      width: '',
      height: '',
      videoStyle: {},
      videoSrc: '',
      show: true,
    }
  },
  methods: {
    init() {
      this.playerOptions.width = this.width
      this.playerOptions.height = this.height
      this.playerOptions.sources[0].src = this.videoSrc
    },
    close() {
      // this.$emit('close')
      this.show = false
    },
  },
  created() {
    this.init()
    console.log('created')
  },
}
</script>

<style lang="scss" scoped>
.video-layer {
  position: absolute;
  top: 0px;
  left: 0px;
  height: 100%;
  width: 100%;
  min-width: 1200px;
  z-index: 1000;
}
.layer {
  position: fixed;
  width: 100%;
  min-width: 1200px;
  height: 100%;
  opacity: 0.5;
  background-color: #000;
}
.dialog {
  width: 500px;
  text-align: center;
  position: fixed;
  top: 50%;
  left: 50%;
  margin-top: -215px;
  margin-left: -250px;
}
.ic-r-cross-thin {
  position: absolute;
  right: 10px;
  top: 10px;
  font-size: 25px;
  cursor: pointer;
  color: #b8babf;
  z-index: 999;
}
@media screen and (max-width: 719px) {
  .dialog {
    margin-left: 0 !important;
    left: 0;
  }
}
</style>
<style lang="scss">
.video-player-box .vjs-control-bar * {
  color: #fff;
  outline: none;
}
.video-js .vjs-big-play-button {
  top: 50%;
  left: 50%;
  margin-top: -0.75em;
  margin-left: -1.5em;
  .vjs-icon-placeholder {
    color: #fff;
  }
}
@media screen and (max-width: 719px) {
  .vjs_video_3-dimensions {
    width: auto !important;
  }
}
</style>
