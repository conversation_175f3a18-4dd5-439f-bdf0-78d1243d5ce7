export default function dragElement(elmnt) {
  var pos1 = 0,
    pos2 = 0,
    pos3 = 0,
    pos4 = 0
  if (document.getElementById(elmnt.id + 'header')) {
    /* 如果存在，header 就是你移动 DIV 的地方：*/
    document.getElementById(elmnt.id + 'header').onmousedown = dragMouseDown
  } else {
    /* 否则，将 DIV 从 DIV 内的任何位置移动：*/
    elmnt.onmousedown = dragMouseDown
  }

  function dragMouseDown(e) {
    e = e || window.event
    e.preventDefault()
    // 在启动时获取鼠标光标位置：
    pos3 = e.clientX
    pos4 = e.clientY
    document.onmouseup = closeDragElement
    // 当光标移动时调用一个函数：
    document.onmousemove = elementDrag
  }

  function elementDrag(e) {
    e = e || window.event
    e.preventDefault()
    // 计算新的光标位置：
    pos1 = pos3 - e.clientX
    pos2 = pos4 - e.clientY
    pos3 = e.clientX
    pos4 = e.clientY
    // 设置元素的新位置：
    // console.log('设置元素的新位置：', elmnt.getBoundingClientRect().top - pos2, elmnt.getBoundingClientRect().left - pos1)
    elmnt.style.top = elmnt.getBoundingClientRect().top - pos2 + 'px'
    elmnt.style.left = elmnt.getBoundingClientRect().left - pos1 + 'px'
  }

  function closeDragElement() {
    /* 释放鼠标按钮时停止移动：*/
    document.onmouseup = null
    document.onmousemove = null
  }
}
