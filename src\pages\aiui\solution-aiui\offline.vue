<template>
  <pc v-if="userAgent === 'pc'"></pc>
  <pc v-else-if="userAgent === 'mobile'"></pc>
  <span v-else>加载中...</span>
</template>

<script>
import pc from './offline/pc.vue'
import mobile from './offline/mobile-pages/index.vue'
import utils from '@A/lib/utils.js'

export default {
  name: 'App',
  data() {
    return {
      userAgent: '',
    }
  },
  created() {
    // 获取是否是h5
    const isMobile = utils.isMobile()
    if (isMobile) {
      this.userAgent = 'mobile'
    } else {
      this.userAgent = 'pc'
    }
  },
  components: { pc, mobile },
}
</script>

<style lang="scss"></style>
