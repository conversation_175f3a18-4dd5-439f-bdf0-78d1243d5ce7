<template>
  <el-scrollbar>
    <div class="list_wrapper">
      <div
        v-for="(item, index) in data"
        :class="['doc_card', { doc_active: item.selected }]"
        @click="toDocCard(item)"
        :key="item.id"
      >
        <div :class="['content_wrap']">
          <i class="skill-icon" :style="{ backgroundColor: item.color }">
            {{ item.name && item.name.substr(0, 1) }}
          </i>
          <div class="skill-info">
            <p class="skill-title" :title="item.name">
              {{ item.name }}
            </p>

            <!-- 标题下按钮 -->
            <div class="title_btm_btn_group">
              <p class="ability-tag">文档问答</p>
            </div>
          </div>
        </div>

        <div @click.stop class="switch_wrap">
          <el-switch
            size="small"
            :value="item.selected"
            @change="(val) => onSwitchChange(val, item)"
          >
          </el-switch>
        </div>
      </div>
    </div>
  </el-scrollbar>
</template>
<script>
export default {
  props: {
    data: {
      type: Array,
      default() {
        return []
      },
    },
    dataCopy: {
      type: Array,
      default() {
        return []
      },
    },
  },
  data() {
    return {}
  },
  methods: {
    toDocCard(item) {
      console.log('toDocCard', item)
      window.open(`/studio/ragqa/${item.id}/localDoc`, '_blank')
    },
    onSwitchChange(val, item) {
      console.log('onSwitchChange', val, item)
      this.$emit('selectchange', item.id, val)
    },
  },
}
</script>

<style lang="scss" scoped>
@import '../../common.scss';

.el-scrollbar {
  height: 100%;
  .list_wrapper {
    display: grid;
    grid-template-columns: repeat(3, 1fr); /* 将容器分为3列，每列宽度平均 */
    gap: 16px;
    .add_item {
      padding-top: 35px;
      text-align: center;
      position: relative;
      background: #fff;
      border: 1px solid #e7e9ed;
      border-radius: 4px;
    }
    .doc_card {
      cursor: pointer;
      padding: 18.5px 15px;
      background-color: #fff;
      border-radius: 12px;
      display: flex;
      justify-content: space-between;
      // background-image: url(~@A/images/aiui/skill-bg.png);
      // background-size: 100% 100%; /* 强制拉伸填满 */
      // background-repeat: no-repeat;
      .content_wrap {
        display: flex;
      }
      .switch_wrap {
        // margin-left: 10px;
        transform: translateY(-3px);
      }
      // &.doc_active {
      //   background-image: url(~@A/images/aiui/skill-bg-active.png);
      //   background-size: 100% 100%; /* 强制拉伸填满 */
      //   background-repeat: no-repeat;
      // }
    }
  }
}
</style>
