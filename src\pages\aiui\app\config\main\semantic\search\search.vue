<template>
  <div class="container">
    <div>
      <div>
        <span class="title">搜索引擎</span>
        <el-switch
          v-model="searchType"
          :active-value="'SearchAggContent'"
          :inactive-value="'Close'"
          @change="onSearchChange"
          :disabled="!subAccountEditable"
        ></el-switch>
      </div>
      <p class="desc">开启后召回实时信息及知识内容回复更准确</p>
    </div>
    <div style="margin-top: 23px">
      <div>
        <span class="title">知识溯源</span>
        <el-switch
          v-model="cbm_knowledge"
          @change="onChange"
          :active-value="true"
          :inactive-value="false"
          :disabled="!subAccountEditable"
        ></el-switch>
      </div>
      <p class="desc">开启后下发本次回复参考了哪些信息</p>
    </div>
  </div>
</template>
<script>
import { mapGetters } from 'vuex'

export default {
  data() {
    return {
      cbm_knowledge: false,
      searchType: 'Close',
      distributes: [],
    }
  },
  created() {
    if (this.currentScene && this.currentScene.sceneBoxId) {
      this.getChainInfo() // 获取配置列表
    }
  },
  computed: {
    ...mapGetters({
      currentScene: 'aiuiApp/currentScene',
      appInfo: 'aiuiApp/app',
      subAccountEditable: 'aiuiApp/subAccountEditable',
    }),
    appId() {
      return this.$route.params.appId
    },
  },
  watch: {
    currentScene(scene) {
      if (scene && scene.sceneBoxId) {
        this.getChainInfo() // 获取配置列表
      }
    },
  },
  methods: {
    onSearchChange(val) {
      this.saveConfig('searchType', { searchType: val })
    },
    onChange(val) {
      this.saveConfig('cbm_knowledge', { cbm_knowledge: val })
    },
    getChainInfo() {
      let that = this
      this.$utils.httpGet(
        this.$config.api.AIUI_APP_CHAIN_INFO,
        {
          appid: this.appId || this.$route.params.appId,
          sceneName: this.currentScene.sceneBoxName,
          chainId: this.currentScene.chainId,
        },
        {
          success: (res) => {
            that.distributes = (res.data.ability || [])
              .filter((item) => !item.distributeOfficialSelected)
              .filter((item) => item.distributeSelected)
              .filter((item) => item.abilityId !== 'cbm_knowledge')
              .map((item) => item.abilityId)

            const cbm_knowledge_ability =
              (res.data.ability || []).find(
                (a) => a.abilityId === 'cbm_knowledge'
              ) || {}
            that.cbm_knowledge = !!cbm_knowledge_ability.distributeSelected

            // 搜索相关配置
            // if(res.data.searchType !== 'Close') {
            //   that.searchType = 'SearchAggContent'
            // } else {
            //   that.searchType = res.data.searchType
            // }
            if (res.data.searchType) {
              if (res.data.searchType !== 'Close') {
                that.searchType = 'SearchAggContent'
              } else {
                that.searchType = 'Close'
              }
            } else {
              that.searchType = 'Close'
            }
          },
        }
      )
    },

    saveConfig(type, obj = {}) {
      let that = this
      let param = {
        appid: this.appId,
        chainId: this.currentScene.chainId || 'cbm_v45',
        sceneName: this.currentScene.sceneBoxName,
      }
      if (type === 'cbm_knowledge') {
        if (obj.cbm_knowledge) {
          param.distribute = JSON.stringify([
            ...this.distributes,
            'cbm_knowledge',
          ])
        } else {
          param.distribute = JSON.stringify([...this.distributes])
        }
      }

      if (type === 'searchType') {
        param.searchType = obj.searchType
      }

      // else {
      //   param.distribute = JSON.stringify([...this.distributes])
      // }

      this.$utils.httpPost(
        this.$config.api.AIUI_APP_PLUGINSTUDIO_SAVECONFIG,
        param,
        {
          success: (res) => {
            // that.getChainInfo()
          },
        }
      )
    },
  },
}
</script>
<style lang="scss" scoped>
.container {
  padding: 30px 0 0 16px;
}

.title {
  font-size: 14px;
  font-weight: 400;
  color: #17171e;
  line-height: 20px;
  margin-right: 20px;
}
.desc {
  font-size: 14px;
  font-weight: 400;
  color: #b8bcbd;
  line-height: 20px;
  margin-top: 10px;
}
</style>
