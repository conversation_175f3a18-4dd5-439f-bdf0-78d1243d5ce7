'use strict'
// Template version: 1.3.1
// see http://vuejs-templates.github.io/webpack for documentation.

const path = require('path')
module.exports = {
  dev: {
    // Paths
    assetsSubDirectory: 'static',
    assetsPublicPath: '/',
    proxyTable: {
      '/aiui/web': {
        target: 'http://teststudio.iflyos.cn/',
        changeOrigin: true
      },
      '/aiui/subweb': {
        target: 'http://teststudio.iflyos.cn',
        changeOrigin: true
      },
      '/SSOService': {
        target: 'https://ssodev.xfyun.cn',
        changeOrigin: true
      }
    },

    // Various Dev Server settings
    host: '0.0.0.0', // can be overwritten by process.env.HOST
    port: 8080, // can be overwritten by process.env.PORT, if port is in use, a free one will be determined
    autoOpenBrowser: false,
    errorOverlay: true,
    notifyOnErrors: true,
    poll: false, // https://webpack.js.org/configuration/dev-server/#devserver-watchoptions-

    /**
     * Source Maps
     */

    // https://webpack.js.org/configuration/devtool/#development
    devtool: 'cheap-module-eval-source-map',

    // If you have problems debugging vue-files in devtools,
    // set this to false - it *may* help
    // https://vue-loader.vuejs.org/en/options.html#cachebusting
    cacheBusting: true,

    cssSourceMap: true,

    env: 'development',
    docHost: 'https://staging-doc.iflyos.cn',
    cdnHost: 'https://cdn.iflyos.cn/',
    wwwHost: 'https://www.iflyos.cn',
    aiuiHost: 'https://staging-aiui.xfyun.cn',
    studioHost: 'https://studio.iflyos.cn',
    deviceHost: 'https://device.iflyos.cn',
    serviceHost: 'https://service.iflyos.cn',
    ssoHost: 'https://sso.xfyun.cn'
  },

  build: {
    // Template for index.html
    index: path.resolve(__dirname, '../dist/index.html'),
    indexTemplate: path.resolve(__dirname, '../build/index.html'),
    // Paths
    assetsRoot: path.resolve(__dirname, '../dist'),
    assetsSubDirectory: 'os-platform',
    assetsPublicPath: '/',

    /**
     * Source Maps
     */

    productionSourceMap: false,
    // https://webpack.js.org/configuration/devtool/#production
    devtool: '#source-map',

    // Gzip off by default as many popular static hosts such as
    // Surge or Netlify already gzip all static assets for you.
    // Before setting to `true`, make sure to:
    // npm install --save-dev compression-webpack-plugin
    productionGzip: true,
    productionGzipExtensions: ['js', 'css'],

    // Run the build command with an extra argument to
    // View the bundle analyzer report after build finishes:
    // `npm run build --report`
    // Set to `true` or `false` to always turn it on or off
    bundleAnalyzerReport: process.env.npm_config_report,

    env: '{{ NODE_ENV }}',
    docHost: 'https://doc.iflyos.cn',
    cdnHost: 'https://cdn.iflyos.cn/',
    wwwHost: 'https://www.iflyos.cn',
    aiuiHost: '{{ AIUI_URL }}',
    studioHost: '{{ AIUI_URL }}',
    deviceHost: 'https://device.iflyos.cn',
    serviceHost: 'https://service.iflyos.cn',
    ssoHost: 'https://{{ SSO_PLATFORM_HOST }}'
  },
  stagingHF: {
    env: 'stagingHF',
    docHost: 'https://staging-doc.iflyos.cn/',
    studioHost: 'https://staging-studio.iflyos.cn/'
  },
  production: {
    // 合肥
    env: 'production',
    docHost: 'https://doc.iflyos.cn',
    cdnHost: 'https://cdn.iflyos.cn/',
    wwwHost: 'https://www.iflyos.cn',
    aiuiHost: 'https://aiui.xfyun.cn',
    studioHost: 'https://studio.iflyos.cn',
    deviceHost: 'https://device.iflyos.cn',
    serviceHost: 'https://service.iflyos.cn',
    ssoHost: 'https://sso.xfyun.cn'
  }
}