<template>
  <el-dialog
    :visible.sync="dialog.show"
    title="免费领取礼包"
    width="425px"
    border-radius="20px"
    :before-close="handleClose"
    class="gc-d-dialog"
  >
    <div class="gc-d-main">
      <ul>
        <li>
          <div class="gc-app-title">
            <span>应用</span>
          </div>
          <el-select
            class="gc-sel"
            :class="{ 'gc-sel-alert': check }"
            v-model="appid"
            size="medium"
            @change="onChangeSelf(appid)"
          >
            <el-option
              v-for="(item, index) in giftApps"
              :key="index"
              :value="item.appid"
              :label="item.appName"
            ></el-option>
            <div slot="suffix">
              <i class="AIUI-iconfont ai-mn-xiala"></i>
            </div>
          </el-select>
        </li>
        <li>
          <span v-if="giftApps.length == 0" style="color: #ffa400"
            >您没有可用应用，<a
              href="/app/add"
              style="cursor: pointer"
              target="_blank"
              >去创建</a
            ></span
          >
        </li>
        <li>
          <el-button
            class="btn-confirm-collect"
            type="primary"
            @click="giftCollect"
            >确定领取
          </el-button>
          <el-button
            class="btn-confirm-collect"
            type="default"
            @click="doCancel"
            >取消
          </el-button>
        </li>
      </ul>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'giftCollectDialog',
  props: {
    dialog: {
      type: Object,
      default: {},
    },
  },
  data() {
    return {
      check: false,
      appid: null,
      giftApps: [],
    }
  },
  watch: {
    'dialog.show': function (val, oldVal) {
      if (val) {
        this.getGiftApps()
      }
    },
  },
  created() {
    this.getGiftApps()
  },
  methods: {
    doCancel() {
      this.dialog.show = false
    },
    handleClose(done) {
      done()
    },
    onChangeSelf(appid) {
      if (this.appid == null) {
        this.check = true
      } else {
        this.check = false
      }
    },
    getGiftApps() {
      this.$utils.httpGet(
        this.$config.api.GET_GIFT_APPS,
        {},
        {
          success: (res) => {
            if (res.flag) {
              this.giftApps = res.data.apps
            }
          },
          error: (err) => {
            this.$message.error(err)
          },
        }
      )
    },
    giftCollect() {
      if (this.appid == null) {
        this.check = true
        return
      } else {
        this.check = false
      }
      this.$utils.httpGet(
        this.$config.api.GIFT_COLLECT,
        {
          appid: this.appid,
        },
        {
          success: (res) => {
            if (res.flag) {
              this.$message.success('领取成功')
              this.dialog.show = false
            } else {
              this.$message.error(res.desc)
            }
          },
          error: (err) => {
            this.$message.error(err)
          },
        }
      )
    },
  },
}
</script>

<style>
.el-dialog__body {
  padding-top: 0 !important;
}
</style>
<style scoped lang="scss">
.gc-d-dialog {
  .first-title {
    text-align: center;
    &-inner {
      border-bottom: 1px solid #e4e7ed;
      width: 100%;
      margin: 0 auto;
    }
  }
}
.gc-d-main {
  color: #333333;
  margin-bottom: 3.13% !important;
  display: flex;
  justify-content: center;
  > ul > li {
    .gc-app-title {
      float: left;
      text-align: center;
      align-content: center;
      justify-content: center;
      position: absolute;
      left: 8%;
      > span {
        vertical-align: middle;
        width: 31px;
        height: 17px;
        font-size: 16px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #333333;
        line-height: 21px;
      }
    }
    :deep(.gc-sel) {
      width: 120%;
      height: 19.9%;
      position: relative;
      display: inline-block;
      -webkit-box-pack: center;
      -ms-flex-pack: center;
      justify-content: center;
      -ms-flex-line-pack: center;
      align-content: center;
      right: 10%;
      &-alert {
        :deep(input) {
          // 列表为空时显示红框
          border: 1px red solid;
        }
      }
    }
  }

  .btn-confirm-collect {
    box-shadow: 0px 2px 4px 0px rgba(23, 132, 233, 0.2);
    border-radius: 4px;
    margin-top: 15.24%;
    position: relative;
    padding: 4%;
    max-width: 20.47% !important;
    min-width: 40.47% !important;
    right: -50%;
  }
}
</style>
