<template>
  <div class="os-container" style="flex-direction: column">
    <aiui-header />
    <div class="scroll-wrap" @scroll="onScroll" ref="scroll">
      <banner />
      <notice-tip />
      <div class="os-container" style="height: auto">
        <div
          class="os-aside new-aside"
          :style="{ visibility: displayFixed ? 'hidden' : 'visible' }"
        >
          <store-menu :menus="menus" />
        </div>
        <div v-if="displayFixed" class="os-aside new-aside menu-fixed">
          <store-menu :menus="menus" />
        </div>
        <div class="os-main">
          <router-view
            :search="searchVal"
            ref="son"
            :fixed="displayFixed"
            :scrollTop="scrollTop"
            :scrollTo="scrollTo"
          ></router-view>
        </div>
      </div>
    </div>

    <feedBackHover />
  </div>
</template>

<script>
import feedBackHover from '../components/feedBackHover'
import banner from '@C/skillBanner'
import noticeTip from '@C/skillNoticeTip'
import storeMenu from './components/menu.vue'

export default {
  data() {
    return {
      menuIndex: 8,

      menus: [
        {
          name: '',
          menus: [
            {
              path: '/store/all',
              value: '全部技能',
              icon: 'icon-quanbufenlei',
              index: 1,
            },
            {
              path: '/store/domain',
              value: '领域技能包',
              icon: 'icon-quanbubeifen2',
              index: 5,
            },
            {
              path: '/store/new',
              value: '最新上架',
              icon: 'icon-time',
              index: 2,
            },
          ],
        },
      ],
      icon: {
        10100: 'ai-store-efficient',
        10200: 'ai-store-life',
        10300: 'ai-store-media',
        10400: 'ai-store-edu',
        10500: 'ai-store-health',
        10600: 'ai-store-game',
        10700: 'ai-store-baby',
        10800: 'ai-store-finance',
        10900: 'ai-store-iot',
        20100: 'ai-store-qa',
      },
      iconInverted: {
        10100: 'efficiencyTool',
        10200: 'lifeService',
        10300: 'vedioRead',
        10400: 'education',
        10500: 'healthy',
        10600: 'entertainment',
        10700: 'childrenEducation',
        10800: 'finance',
        10900: 'smartHome',
        20100: 'qa',
      },
      searchVal: '',
      titles: {
        all: '全部技能',
        new: '最新上架',
        hot: '热门排行',
        vehicle: '车载专区',
        official: '官方技能',
        other: '第三方技能',
        20100: '开放问答',
        dialect: '方言技能',
        open: '开放技能',
      },
      scrollTop: 0,
    }
  },
  watch: {
    $route: function (val) {
      if (val.params.skillType !== 'all') {
        this.searchVal = ''
      }
    },
  },
  computed: {
    pageTitle() {
      return this.titles[this.$route.params.skillType]
    },
    key() {
      return this.$route.fullPath
    },
    displayFixed() {
      let distance = 240 + 36
      if (window.screen.availHeight < 801) {
        distance = 136 + 32
      }
      return this.scrollTop > distance
    },
  },
  created() {
    this.searchVal = this.$store.state.aiuiStore.searchVal
    // this.getSkillType()
  },
  beforeRouteLeave(to, from, next) {
    if (/\/skill\//.test(to.path) && this.searchVal) {
      this.$store.dispatch('aiuiStore/setSearchVal', this.searchVal)
    } else {
      this.$store.dispatch('aiuiStore/setSearchVal', '')
    }
    next()
  },
  methods: {
    getSkillType() {
      let self = this
      this.$utils.httpGet(
        this.$config.api.AIUI_STORE_SKILL_TYPES,
        {},
        {
          success: (res) => {
            res.data[20100] = '开放问答'
            let menus = Array.prototype.map.call(
              Object.keys(res.data),
              (item, index) => {
                let menu = {
                  path: `/store/${this.iconInverted[item]}`,
                  value: res.data[item],
                  icon: self.icon[item],
                  index: self.menuIndex + index,
                }
                return menu
              }
            )
            console.log(JSON.stringify(menus))
            self.menus[2].menus = menus
            Object.assign(self.titles, res.data)
          },
          error: (err) => {},
        }
      )
    },
    searchSkill() {
      if (this.$route.params.skillType === 'all') {
        this.$refs.son.handleSkillType('all')
      } else {
        this.$router.push({
          name: 'store-skills',
          params: { skillType: 'all' },
        })
      }
    },

    onScroll() {
      this.scrollTop = this.$refs.scroll.scrollTop
    },
    scrollTo(val) {
      this.$refs.scroll.scrollTop = val
    },
  },
  components: {
    feedBackHover,
    banner,
    noticeTip,
    storeMenu,
  },
}
</script>

<style lang="scss" scoped>
.store-skills-title {
  font-size: 24px;
  color: $semi-black;
  padding: 24px 0 23px 48px;
}
.store-skill-search {
  padding: 22px 12px;
}

.menu-fixed {
  position: fixed;
  left: 0;
  z-index: 10;
  top: 60px;
  bottom: 0;
}
@media screen and (max-width: 1601px) {
  .menu-fixed {
    top: 40px;
  }
}

.new-aside {
  width: 234px;
  :deep(.el-menu-item) {
    height: 80px;
    line-height: 80px;
  }
}

@media screen and (max-height: 801px) {
  .new-aside {
    width: 160px;
    :deep(.el-menu-item) {
      height: 54px;
      line-height: 54px;
    }
  }
}

.scroll-wrap {
  overflow: auto;
}
</style>
