<template>
  <div class="os-scroll">
    <div class="handle-platform-content">
      <div class="mgb24 header_wrapper">
        <!-- <el-button
          v-if="hasAimindAuth"
          type="text"
          size="medium"
          @click="createKnowledgeMap"
        >
          去创建知识图谱
        </el-button> -->

        <div @keyup.enter="getQabankList(1)">
          <el-input
            class="search-area"
            :placeholder="`输入${repoTypeName}问答库名称`"
            size="medium"
            v-model="searchVal"
          >
            <i
              slot="suffix"
              class="el-input__icon el-icon-search search-area-btn"
              @click="getQabankList(1)"
            />
          </el-input>
        </div>

        <el-button
          icon="ic-r-plus"
          type="primary"
          size="medium"
          @click="openCreateQabank('qa', type)"
        >
          &nbsp;创建{{ repoTypeName }}问答库
        </el-button>
      </div>
      <os-table
        class="qabanks-table gutter-table-style transparent-bgc"
        :height="'calc(100vh - 230px)'"
        :tableData="tableData"
        style="margin-bottom: 15px"
        @change="getQabankList"
        @row-click="toEdit"
        v-if="hasItem"
      >
        <el-table-column width="320" :label="`${repoTypeName}问答库名称`">
          <template slot-scope="scope">
            <div
              class="text-blod cp qabank-page-qa-zh-name ellipsis"
              :title="scope.row.name"
              @click.stop.prevent="toEdit(scope.row)"
            >
              {{ scope.row.name || '-' }}
              <!-- <i class="ic-qa-repository repo" v-if="scope.row.type == 2"
                >知识库</i
              >
              <i class="ic-qa-repository key-qa" v-else-if="scope.row.type == 3"
                >关键词问答</i
              >
              <i class="ic-qa-repository repo" v-else-if="scope.row.type == 5"
                >文档问答</i
              >
              <i class="ic-qa-repository" v-else>语句问答</i> -->
            </div>
          </template>
        </el-table-column>
        <el-table-column width="120" label="主题数量">
          <template slot-scope="scope">
            <div>{{ scope.row.topicNumber }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="updateTime" width="200" label="更新时间">
          <template slot-scope="scope">
            <div>{{ scope.row.updateTime | date('yyyy-MM-dd') }}</div>
          </template>
        </el-table-column>
        <el-table-column width="120" label="被引用数">
          <template slot-scope="scope">
            <div
              v-if="scope.row.appNumber"
              class="text-primary"
              style="cursor: pointer; height: 40px; line-height: 40px"
              @click.prevent.stop="openCountDialog(scope.row)"
            >
              {{ scope.row.appNumber }}
            </div>
            <span v-else>{{ scope.row.appNumber }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120">
          <template slot-scope="scope">
            <el-tooltip
              class="item"
              effect="dark"
              content="编辑"
              placement="top"
            >
              <i class="ic-r-edit cell-handle-ic"></i>
            </el-tooltip>

            <i
              class="cell-handle-hovershow ic-r-delete cell-handle-ic"
              @click.prevent.stop="del(scope.row)"
            ></i>
          </template>
        </el-table-column>
      </os-table>
      <div class="create-guide" v-else>
        <div class="icon"></div>
        <p class="title">
          你还没有创建任何{{ repoTypeName }}问答库，
          <a @click="openCreateQabank('qa', type)"> 点击创建 </a>
        </p>
      </div>
    </div>
    <create-qa-dialog :dialog="dialog" @change="getQabankList" />
    <skill-quote-dialog :dialog="countDialog" type="qabank" />
  </div>
</template>

<script>
import CreateQaDialog from './dialog/createQa.vue'
import SkillQuoteDialog from './dialog/skillQuotecharater.vue'
// import SkillQuoteDialog from './dialog/skillQuote.vue'
import { mapGetters } from 'vuex'

export default {
  name: 'studio-handle-platform-qabanks',
  props: {
    type: Number,
  },
  data() {
    return {
      nav: 'qabank',
      searchVal: '',
      tableData: {
        loading: true,
        total: 0,
        page: 1,
        size: 10,
        list: [],
      },
      dialog: {
        show: false,
        name: '',
        type: 0,
      },
      countDialog: {
        show: false,
        repoId: '',
      },
      hasItem: true,
    }
  },
  created() {
    this.getQabankList(1)
    console.log(
      `----localStorage.getItem('pageHandle')----,`,
      localStorage.getItem('pageHandle')
    )
    if (localStorage.getItem('pageHandle') === 'createRepo') {
      this.dialog.show = true
      this.dialog.name = 'repo'
      this.dialog.type = 2
      localStorage.setItem('pageHandle', null)
    }
    if (localStorage.getItem('pageHandle') === 'createQa') {
      this.dialog.show = true
      this.dialog.name = 'qa'
      this.dialog.type = 0
      localStorage.setItem('pageHandle', null)
    }
    if (localStorage.getItem('pageHandle') === 'createKeyQa') {
      this.dialog.show = true
      this.dialog.name = 'qa'
      this.dialog.type = 3
      localStorage.setItem('pageHandle', null)
    }
    // if (localStorage.getItem('pageHandle') === 'createDocQa') {

    //   this.dialog.show = true
    //   this.dialog.name = 'qa'
    //   this.dialog.type = 5
    //   localStorage.setItem('pageHandle', null)
    // }
  },
  computed: {
    ...mapGetters({
      userInfo: 'user/userInfo',
      subAccountInfo: 'user/subAccountInfo',
      limitCount: 'aiuiApp/limitCount',
    }),
    hasAimindAuth() {
      return (
        this.limitCount &&
        this.limitCount.aimind_menu &&
        Number(this.limitCount.aimind_menu) > 0
      )
    },
    repoTypeName() {
      if (this.type === 0) {
        return '语句'
      } else if (this.type === 3) {
        return '关键词'
      } else {
        return ''
      }
    },
  },
  methods: {
    getQabankList(page) {
      let self = this
      this.tableData.loading = true
      this.$utils.httpGet(
        this.$config.api.STUDIO_QA_LIST,
        {
          pageIndex: page || this.tableData.page,
          pageSize: this.tableData.size,
          search: this.searchVal,
          all: this.type === 0 ? 'sentenceQA' : 'keywordQA',
        },
        {
          success: (res) => {
            if (res.data.count <= 0 && !self.searchVal) {
              self.hasItem = false
            } else {
              self.hasItem = true
            }
            self.tableData.list = res.data.repositoryList
            self.tableData.total = res.data.count
            self.tableData.page = res.data.pageIndex
            self.tableData.size = res.data.pageSize
            self.tableData.loading = false
          },
          error: (err) => {},
        }
      )
    },
    openCreateQabank(name, type) {
      this.dialog.show = true
      this.dialog.name = name
      this.dialog.type = type
    },
    // 打开引用技能的dialog
    openCountDialog(item) {
      this.countDialog.show = true
      this.countDialog.repoId = item.repositoryId
    },
    toEdit(data) {
      if (!data) return
      let routeData
      if (data.type == 2) {
        routeData = this.$router.resolve({
          name: 'qa-relations',
          params: { repoId: data.repositoryId },
        })
        window.open(routeData.href, '_blank')
        // this.$router.push({
        //   name: 'qa-relations',
        //   params: { repoId: data.repositoryId },
        // })
        return
      }
      if (data.type == 3) {
        // this.$router.push({
        //   name: 'keyQABank',
        //   params: { repoId: data.repositoryId, qaId: data.qaId },
        // })
        routeData = this.$router.resolve({
          name: 'keyQABank',
          params: { repoId: data.repositoryId, qaId: data.qaId },
        })
        window.open(routeData.href, '_blank')
        return
      }

      if (data.type == 5) {
        // this.$router.push({
        //   name: 'qaBank-knowledge',
        //   params: { repoId: data.repositoryId, name: data.name },
        // })
        routeData = this.$router.resolve({
          name: 'qaBank-knowledge',
          params: { repoId: data.repositoryId, name: data.name },
        })
        window.open(routeData.href, '_blank')
        return
      }

      // this.$router.push({ name: 'qaBank', params: { qaId: data.repositoryId } })
      routeData = this.$router.resolve({
        name: 'qaBank',
        params: { qaId: data.repositoryId },
      })
      window.open(routeData.href, '_blank')
    },
    del(data) {
      let self = this
      let itemType = data.type == 2 ? '知识' : '问答'
      if (data.appNumber) {
        this.$message.warning(
          `该${itemType}库已被设备/应用引用，请先取消引用后再删除`
        )
        return
      }
      this.$confirm(
        `${itemType}库删除后不可恢复，请谨慎操作。`,
        `确定删除${itemType}库 - ${data.name}`,
        {
          confirmButtonText: '确定删除',
          cancelButtonText: '取消',
          confirmButtonClass: 'el-button--danger',
          type: 'warning',
          showClose: false,
        }
      )
        .then(() => {
          self.delEntity(data)
        })
        .catch(() => {})
    },
    delEntity(data) {
      let self = this
      this.tableData.loading = true
      this.$utils.httpPost(
        this.$config.api.STUDIO_QA_DEL,
        {
          repoId: data.repositoryId,
        },
        {
          success: (res) => {
            self.$message.success('删除成功')
            if (self.tableData.list.length === 1 && self.tableData.page > 1) {
              self.tableData.page -= 1
            }
            self.getQabankList()
          },
          error: (err) => {
            this.tableData.loading = false
          },
        }
      )
    },

    getKnowledgeMapUrl() {
      let host = window.location.host
      let url
      if (
        host === 'teststudio.iflyos.cn' ||
        host === 'integration-aiui.xfyun.cn'
      ) {
        url = 'http://*************:18800/'
      } else {
        // 认为是正式环境
        url = 'https://aimind.xfyun.cn/'
      }
      return url
    },

    // https://fxm-graph-aiui.iflyresearch.com/#/home/<USER>/list&token=xxx&userInfo=xxx;(在传参之前，需要先将userInfo通过encodeURIComponent(JSON.stringify(userInfo))进行加密，然后拼接到url进行传参）

    createKnowledgeMap() {
      // 调用接口拿到aimind的token，携带token跳转链接
      let self = this
      this.$utils.httpGet(
        this.$config.api.AIMIND_GET_TOKEN,
        {
          // uid: userInfo.uid,
        },
        {
          success: (res) => {
            const token = res.data.token
            let userInfo = res.data.user || {}
            const encodeUsrInfo = encodeURIComponent(JSON.stringify(userInfo))
            let aimindDomain = self.getKnowledgeMapUrl()
            const url = `${aimindDomain}#/home/<USER>/list?token=${token}&userInfo=${encodeUsrInfo}`
            window.open(url, '_blank')
          },
          error: (err) => {},
        }
      )
    },
  },
  components: {
    CreateQaDialog,
    SkillQuoteDialog,
  },
}
</script>

<style lang="scss" scoped>
.handle-platform-content {
  // max-width: 1200px;
  width: 100%;
  margin: auto;
  .header_wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    // .search-area{
    //   width: 480px;
    // }
  }
}

.search-area {
  width: 480px;
}

.entity-status {
  width: 8px;
  height: 8px;
  border: 2px solid;
  border-radius: 8px;
  margin-right: 6px;

  &-0 {
    border-color: $grey5;
  }

  &-1 {
    border-color: $success;
  }

  &-2 {
    border-color: $grey4;
  }

  &-txt {
    color: $grey5;
  }

  &-count {
    color: $primary;
  }
}

.ic-qa-repository {
  margin-right: 4px;
  // width: 36px;
  width: auto;
  padding: 0 8px;
  height: 20px;
  line-height: 1.5;
  font-size: 12px;
  font-weight: 500;
  color: #0381ec;
  border-radius: 10px;
  text-align: center;
  background-color: $primary-light-12;
}

.repo {
  color: $dangerous;
  background: $dangerous-light-12;
}

.key-qa {
  color: $success;
  background: $success-light-12;
}

.create-guide {
  margin: 76px 0;
  text-align: center;
  font-size: 16px;
  color: $grey5;

  .icon {
    margin: 0 auto 24px;
    width: 120px;
    height: 120px;
    background: url(~@A/images/app/create-app.png) center no-repeat;
    background-size: 100%;
  }

  .title {
    font-size: 16px;
    font-weight: 600;

    a {
      font-weight: 600;
    }
  }

  .desc {
    margin: 24px auto;
    width: 480px;
    font-size: 14px;
    line-height: 22px;
  }
}
</style>
<style lang="scss">
.el-table--enable-row-hover .el-table__body tr:hover > td {
  .qabank-page-qa-zh-name {
    color: $primary;
  }
}

.el-table .ic-r-edit {
  color: $primary;
}

.qabanks-table {
  .el-table .cell {
    cursor: pointer;
  }
}
</style>
