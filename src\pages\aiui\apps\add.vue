<template>
  <el-dialog title="创建应用" :visible.sync="dialog.show" width="760px">
    <el-form
      class="create-form"
      :label-position="labelPosition"
      :model="appForm"
      :rules="rules"
      ref="appForm"
    >
      <el-form-item label="应用名称" prop="appName" style="width: 640px">
        <el-input
          v-model.trim="appForm.appName"
          placeholder="请输入应用的名称，支持汉字/字母/数字/下划线，30个字符以内"
        ></el-input>
      </el-form-item>

      <el-form-item
        label="应用分类"
        :class="{ 'no-error': typeListOpen }"
        :show-message="!typeListOpen"
        prop="appType"
      >
        <type-list
          @setType="setType"
          @setTypeListStatu="setTypeListStatu"
        ></type-list>
      </el-form-item>
      <el-form-item label="设备信息" prop="deviceType">
        <el-checkbox-group class="app-type" v-model="appForm.deviceType">
          <el-checkbox :label="1">
            有屏幕
            <el-tooltip
              content=" 设备上有屏幕，在屏幕展示AIUI下发的内容"
              placement="bottom-start"
            >
              <i style="color: #b8babf" class="el-icon-question"></i>
            </el-tooltip>
          </el-checkbox>
          <el-checkbox :label="2">
            有蓝牙
            <el-tooltip
              content="设备上有蓝牙模块，可用于配网或者连接其他设备"
              placement="bottom-start"
            >
              <i style="color: #b8babf" class="el-icon-question"></i>
            </el-tooltip>
          </el-checkbox>
          <el-checkbox :label="4">
            有WIFI
            <el-tooltip
              content=" 设备上有WiFi模块，可以连接网络"
              placement="bottom-start"
            >
              <i style="color: #b8babf" class="el-icon-question"></i>
            </el-tooltip>
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="应用描述" prop="appDescription">
        <el-input
          type="textarea"
          v-model="appForm.appDescription"
          :autosize="{ minRows: 3, maxRows: 6 }"
          placeholder="请简要描述应用，最多1000字"
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-checkbox-group v-model="appForm.checkList">
          <el-checkbox label="1" disabled
            >大模型语音交互每日免费500次</el-checkbox
          >
          <el-checkbox label="2">赠送通用星火大模型token 100w</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="dialog.show = false">取 消</el-button>
      <el-button type="primary" @click="submit('appForm')">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import TypeList from './appTypeList'
import { mapGetters } from 'vuex'

export default {
  name: 'apps-add',
  props: {
    dialog: {
      type: Object,
      default: {},
    },
  },
  data() {
    return {
      labelPosition: 'top',
      appForm: {
        appName: '',
        appType: '',
        appTypeName: '',
        deviceType: [],
        appDescription: '',
        checkList: ['2', '1'],
      },
      appTypeList: [],

      morfeiCore: {
        icon: 'ic-sys-mf',
        name: 'MorfeiCore',
      },
      iflyosEntryShow: false,
      rules: {
        appName: [
          {
            required: true,
            message: '应用名称不能为空',
            trigger: ['change', 'blur'],
          },
          { validator: this.checkName, trigger: ['change', 'blur'] },
          { validator: this.repeatName, trigger: ['blur'] },
        ],

        appType: [
          {
            required: true,
            message: '应用类型不能为空',
            trigger: ['change', 'blur'],
          },
        ],
        appDescription: [
          this.$rules.lengthLimit(1, 1000, '应用描述的长度不能超过1000个字符'),
        ],
      },
      typeListOpen: false,
    }
  },
  watch: {
    'dialog.show'(val) {
      if (!val) {
        this.$refs.appForm.clearValidate()
      }
    },
  },
  computed: {
    ...mapGetters({
      limitCount: 'aiuiApp/limitCount',
    }),

    toAppsName() {
      return this.$route.path.includes('/sub/apps') ? 'sub-apps' : 'apps'
    },
    toAppConfigName() {
      return this.$route.path.includes('/sub/app')
        ? 'sub-app-config'
        : 'app-config'
    },
  },

  methods: {
    //表单校验规则
    checkName(rule, val, callback) {
      if (val && val.length > 30) {
        callback(new Error('不能超过30个字符'))
        return
      }
      let reg = /^[a-zA-Z0-9.\-_\u4e00-\u9fa5]+$/
      if (!reg.test(val)) {
        callback(new Error('仅支持汉字/字母/数字/下划线'))
        return
      }
      callback()
    },
    repeatName(rule, val, callback) {
      this.$utils.httpPost(
        this.$config.api.AIUI_APP_CHECKNAME,
        { appName: val },
        {
          success: (res) => {
            callback(new Error('应用名称不能重复'))
            return
          },
          error: (err) => {},
        }
      )
      callback()
    },
    setType(type, typeName) {
      this.appForm.appType = type
      this.appForm.appTypeName = typeName
      let typeList = ['0302', '0303', '0306', '0307', '0309', '0310']
      if (
        (type && typeList.indexOf(type.substring(0, 4)) != -1) ||
        type == '030101' ||
        type == '030199'
      ) {
        this.iflyosEntryShow = true
      } else {
        this.iflyosEntryShow = false
      }
    },
    setTypeListStatu(val) {
      this.typeListOpen = val
    },
    submit(formName) {
      const deviceType =
        this.appForm.deviceType.length == 0
          ? 0
          : this.appForm.deviceType.reduce((prev, current, index, arr) => {
              return prev + current
            })
      let data = {
        appName: this.appForm.appName,
        appType: this.appForm.appType,
        appTypeName: this.appForm.appTypeName,
        appDescription: this.appForm.appDescription,
        deviceType,
      }
      // 勾选了 “赠送通用星火大模型token 100w”
      if (this.appForm.checkList.includes('2')) {
        data.spark = '1'
      }
      if (this.$route.query.source && this.$route.query.source === 'aibot') {
        data.source = 'aibot'
      }
      let self = this
      self.$refs[formName].validate((valid) => {
        if (valid) {
          self.$utils.httpPost(self.$config.api.AIUI_APP_CREATE, data, {
            success: (res) => {
              if (!res.flag) {
                self.$message.error('创建失败，请重试')
                return
              }
              self.$message.success('创建成功')
              self.dialog.show = false
              self.$emit('refresh')
              self.$router.push({
                name: self.toAppConfigName,
                params: {
                  appId: res.data,
                  isFrist: true,
                },
              })
            },
            error: (err) => {},
          })
        }
      })
    },
  },
  components: {
    TypeList,
  },
}
</script>

<style lang="scss" scoped>
.os-scroll {
  padding-top: 30px;
}
.create-form {
  padding-left: 12px;
  :deep(.el-checkbox__input.is-disabled + span.el-checkbox__label) {
    color: #595959 !important;
    cursor: not-allowed;
  }
}
.app-sys-tips {
  color: $warning;
}

.breadcrumb-wrap {
  padding: 20px 0 12px;
  color: #b8babf;
  font-size: 14px;
  i {
    margin-right: 3px;
    color: #b8babf;
  }
}
.create-app-wrap {
  margin: 0 auto;
  max-width: 1200px;
  overflow: hidden;
  height: 100%;
}
//iflyos 快速接入
.iflyos-entry-content {
  position: relative;
  width: 640px;
  height: 136px;
  text-align: center;
  border: 1px solid $grey3;
  background: url(../../../assets/svg/apps/bg-ad-iflyos.svg);
  p {
    margin: 32px 0 16px;
    text-align: center;
    font-size: 18px;
    color: $semi-black;
  }
}
.btn-iflyos-entry {
  display: inline-block;
  width: 160px;
  height: 36px;
  line-height: 36px;
  color: $semi-black;
  border-radius: 2px;
  background: $grey4-15;
}
</style>
<style lang="scss">
.create-form {
  .el-form-item__label {
    line-height: normal;
    padding-bottom: 8px;
  }
  .el-form-item {
    // margin-bottom: 40px;
  }
  .type-input {
    width: 640px;
  }
  .no-error .el-input__inner {
    border: 1px solid #d5d8de;
  }
  .os-icon-radio-active {
    width: 136px;
    height: 136px;
  }
  .os-icon-radio {
    margin-bottom: 5px;
  }
}
</style>
