<template>
  <el-dialog
    title="更换头像"
    :visible.sync="dialogVisible"
    width="600px"
    @close="handleClose"
  >
    <div class="icon_container">
      <div
        v-for="(item, index) in iconList"
        :key="index"
        class="icon_item"
        :class="{
          icon_item_selected: selectedIcon === item,
        }"
        @click="selectIcon(item)"
      >
        <el-image :src="item" fit="cover" class="icon_image"> </el-image>
        <div
          v-if="selectedIcon === item"
          class="icon_check"
        >
          <i class="el-icon-check"></i>
        </div>
      </div>
      <div v-if="loading" class="loading_container">
        <i class="el-icon-loading"></i>
      </div>
      <div
        v-if="iconList.length === 0 && !loading"
        class="empty_container"
      >
        <span>暂无可用头像</span>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleConfirm">确认</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: 'SelectIconDialog',
  data() {
    return {
      pluginIconKey: null,
      dialogVisible: false,
      iconList: [], // 存储所有图标的扁平数
      loading: false,
      selectedIcon: null,
    }
  },
  props:{
    roleInfo: {
      type: Object,
      default: () => ({}),
    },
  },
  created() {
    this.getIconList()
  },
  methods: {
    show() {
      this.dialogVisible = true
      this.selectedIcon = this.roleInfo.image
      this.$nextTick(() => {
        const iconElements = document.querySelectorAll('.icon_item_selected')
        console.log('iconElements', iconElements)
        if (iconElements && iconElements.length > 0) {
          iconElements[0].scrollIntoView({
            behavior: 'smooth',
            block: 'center',
          })
        }
      })
    },
    initDialogData() {
      this.dialogVisible = false
      this.selectedIcon = null
    },
    handleClose() {
      this.initDialogData()
    },
    selectIcon(item) {
      this.selectedIcon = item
    },
    handleConfirm() {
      if (this.selectedIcon != this.roleInfo.image) {
        this.$emit('change', this.selectedIcon)
      }
      this.initDialogData()
    },
    getIconList() {
      this.loading = true
      this.iconList = []
      this.iconData = {}

      this.$utils.httpGet(
        this.$config.api.AIUI_ROLE_ICON_LIST,
        {},
        {
          success: (res) => {
            if (res.code === 0 || res.code === '0') {
              this.iconList = res.data.list
            }
            this.loading = false
          },
          error: (err) => {
            this.$message.error(err?.desc || '获取头像列表失败')
            this.loading = false
          },
        }
      )
    },
  },
}
</script>

<style lang="scss" scoped>
:deep(.el-dialog__body) {
  padding-bottom: 16px;
}
.tabs_container {
  :deep(.el-tabs__active-bar) {
    display: none;
  }
  :deep(.el-tabs__nav-wrap::after) {
    display: none;
  }
  :deep(.el-tabs) {
    .el-tabs__item {
      transition: all 0.3s;
      border-radius: 4px;
      padding: 0 16px;
      height: 32px;
      line-height: 32px;

      &.is-active {
        background-color: #e1f3ff;
        color: inherit; /* 恢复默认字体颜色 */
      }

      &:hover {
        color: inherit; /* hover时保持字体颜色不变 */
      }

      &:first-child {
        padding-left: 16px; /* 确保第一个tab有左边距 */
      }
    }
  }
}

.icon_container {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 16px;
  // padding-bottom: 16px;
  max-height: 300px;
  position: relative;
  overflow-y: auto;
}

.icon_item {
  position: relative;
  width: 100%;
  aspect-ratio: 1/1;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  border: 1.5px solid transparent;
  transition: all 0.3s;

  &:hover {
    border-color: $primary;
  }
}

.icon_item_selected {
  border-color: $primary;
}

:deep(.icon_image) {
  width: 100%;
  height: 100%;
  img {
    width: 100%;
    height: 100%;
  }
}

.icon_check {
  position: absolute;
  top: 4px;
  right: 4px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: $primary;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 12px;
}

.loading_container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 32px;
  color: $primary;
}

.empty_container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #909399;
  font-size: 14px;
}
</style>
