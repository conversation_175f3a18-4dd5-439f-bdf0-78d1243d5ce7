<template>
  <div class="app-container">
    <aiui-menu />
    <div class="os-container" style="flex-direction: column">
      <!-- <aiui-header /> -->
      <div class="os-container">
        <div class="os-aside">
          <div class="aiui-app-menu-head">
            <div class="aiui-app-menu-head-return" @click="returnCb">
              <svg-icon
                iconClass="secondary-return"
                :customStyle="{
                  width: '13px',
                  height: '9px',
                  marginRight: '5px',
                  transform: 'translateY(1px)',
                }"
              />
              <span>返回列表</span>
            </div>
            <div class="aiui-app-menu-head-skill-name">
              <span :title="appName || '-'">{{ appName || '-' }}</span>
            </div>
          </div>
          <os-menu style="padding-bottom: 100px" :menus="menus" />
        </div>
        <div class="os-main">
          <router-view
            :contentReviewConfig="contentReviewConfig"
            :subAccount="subAccount"
            :rightTestOpen="rightTestOpen"
            :subAccountEditable="subAccountEditable"
            :subAccountHasCreateAuth="subAccountHasCreateAuth"
          ></router-view>
        </div>
        <div
          class="os-side-right"
          :class="{ 'os-side-right-open': rightTestOpen }"
        >
          <right-test-close
            v-if="!rightTestOpen"
            :rightTestOpen="rightTestOpen"
            debugType="app"
          ></right-test-close>
          <skill-debug
            v-show="rightTestOpen && modelType === '2'"
            debugType="app"
          />
          <app-debug45
            v-show="rightTestOpen && modelType === '13'"
          ></app-debug45>
          <feedBackHover :rightTestOpen="rightTestOpen" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import RightTestClose from '@C/rightTestClose'
import feedBackHover from '../components/feedBackHover'
import { mapGetters } from 'vuex'
import { bus } from '@U/bus'
import appIcon from '@A/images/aiui5/app/<EMAIL>'
import appActiveIcon from '@A/images/aiui5/app/<EMAIL>'

import devIcon from '@A/images/aiui5/app/<EMAIL>'
import devActiveIcon from '@A/images/aiui5/app/<EMAIL>'

import statisticsIcon from '@A/images/aiui5/app/<EMAIL>'
import statisticsActiveIcon from '@A/images/aiui5/app/<EMAIL>'
import aiuiMenu from '../components/aiuiMenu'

export default {
  data() {
    return {
      paidSources: [],
      contentReviewConfig: {},
      menus: [
        {
          name: '应用',
          icon: appIcon,
          activeIcon: appActiveIcon,
          menus: [
            {
              key: 'app-info',
              value: '应用信息',
              icon: 'ic-mn-basic-info',
              index: 0,
            },
            {
              key: 'app-config',
              value: '应用配置',
              icon: 'ic-mn-device-skill',
              index: 1,
            },
          ],
        },
        // {
        //   name: '开发',
        //   menus: [
        //     {
        //       // key: `${self.subAccount ? 'sub-app-tool' : 'app-tool'}`,
        //       key: 'app-tool',
        //       value: '接入配置',
        //       icon: 'ai-mn-code',
        //       index: 2,
        //     },
        //     // {
        //     //   key: 'app-whitelist',
        //     //   value: 'IP白名单',
        //     //   icon: 'ai-mn-ip-list',
        //     //   index: 3,
        //     // },
        //   ],
        // },
      ],
      publishInfo: {
        name: '上线',
        menus: [
          {
            key: 'app-publish',
            value: '更新发布',
            icon: 'ic-mn-launch',
            index: 5,
          },
          {
            key: 'app-version',
            value: '版本管理',
            icon: 'ai-mn-ip-list',
            index: 6,
          },
        ],
      },
      statistic: {
        name: '应用数据统计',
        icon: statisticsIcon,
        activeIcon: statisticsActiveIcon,
        menus: [
          {
            key: 'app-statistic-service-index',
            value: '服务统计',
            icon: 'ai-mn-data',
            index: '7',
          },
        ],
      },
      appMenus: [
        {
          key: 'app-users',
          value: '设备统计',
          icon: 'ai-mn-data-user',
          index: '8',
        },
      ],
      sourceMenu: [
        {
          key: 'app-sources',
          value: '信源授权统计',
          icon: 'ai-mn-dict',
          index: '9',
        },
      ],
      updateMenu: {
        name: '升级',
        menus: [
          {
            key: 'app-ota-update',
            value: '固件升级',
            icon: 'ic-mn-update',
            index: '10',
          },
        ],
      },
      originModelType: '',
    }
  },
  computed: {
    ...mapGetters({
      rightTestOpen: 'studioSkill/rightTestOpen',
      subAccount: 'user/subAccount',
      subAccountAppAuths: 'aiuiApp/subAccountAppAuths',
      currentScene: 'aiuiApp/currentScene',
    }),
    appName() {
      return this.$store.state.aiuiApp.app.appName
    },
    appPlatformNum() {
      return this.$store.state.aiuiApp.app.platformNum
    },
    subAccountEditable() {
      return this.subAccountAppAuths[this.$route.params.appId] == 2
        ? false
        : true
    },
    subAccountHasCreateAuth() {
      return this.$store.state.aiuiApp.subAccountHasCreateAuth
    },
    modelType() {
      // 根据切换时的模型选择
      if (this.originModelType) {
        return this.originModelType
      } else {
        if (
          this.currentScene &&
          this.currentScene.point &&
          this.currentScene.point.split(',').indexOf('13') !== -1
        ) {
          return '13'
        } else {
          return '2'
        }
      }
    },
  },

  created() {
    this.$store.dispatch('aiuiApp/setLimitCount')
    this.$store.dispatch('aiuiApp/setId', this.$route.params.appId)
    this.getApp(() => {
      this.getWakeupAuth() /*, this.appendRecommend()*/, this.getPaidSource()
    })
    this.$store.dispatch('aiuiApp/setQcAuth')
    this.subAccountSKilInit()
    //this.appendItem2Static()
    bus.$on('MODELCHANGE', (val) => {
      this.originModelType = val
    })
  },

  methods: {
    returnCb() {
      let name = this.subAccount ? 'sub-apps' : 'apps'
      this.$router.push({ name: name })
    },
    appendRecommend() {
      let find = this.menus.find((item) => item.name === '开发')
      if (find) {
        find.menus.push({
          key: 'app-content-recommend',
          value: '内容推荐',
          icon: 'ai-mn-code',
          index: 20,
        })
      } else {
        this.menus.push({
          name: '开发',
          menus: [
            {
              key: 'app-content-recommend',
              value: '内容推荐',
              icon: 'ai-mn-code',
              index: 20,
            },
          ],
        })
      }
    },

    getApp(callback) {
      let self = this
      this.$utils.httpGet(
        this.$config.api.AIUI_APPINFO,
        {
          appid: this.$route.params.appId,
        },
        {
          success: (res) => {
            if (res.flag) {
              /**
               * 应用侧边栏
               * RTOS 应用不显示开发工具（ 13: '讯飞RTOS硬件模组'）
               *
               * WebAPI 应用显示 IP白名单（5: 'WebAPI',）
               *
               * 评估板、魔飞、RTOS 应用不显示审核上线（10: 'aiui', 11: 'morfei', 13: '讯飞RTOS硬件模组'）
               *
               * platform == 'all'时，除应用信息栏可点击外，其他导航栏均不可点击
               */
              let find = this.menus.find((item) => item.name === '开发')
              if (find) {
                find.menus.push({
                  key: `${self.subAccount ? 'sub-app-tool' : 'app-tool'}`,
                  value: '接入配置',
                  icon: 'ic-mn-debug',
                  index: 2,
                })
              } else {
                self.menus.push({
                  name: '开发',
                  icon: devIcon,
                  activeIcon: devActiveIcon,
                  menus: [
                    {
                      key: `${self.subAccount ? 'sub-app-tool' : 'app-tool'}`,
                      value: '接入配置',
                      icon: 'ic-mn-debug',
                      index: 2,
                    },
                  ],
                })
              }
              if (
                // aiui mofei rtos
                res.data.platform != 10 &&
                res.data.platform != 11 &&
                res.data.platform != 13
              ) {
                if (!res.data.check) {
                  // self.menus.push({
                  //   name: '上线',
                  //   menus: [
                  //     {
                  //       key: `${
                  //         self.subAccount ? 'sub-app-audit' : 'app-audit'
                  //       }`,
                  //       value: '审核上线',
                  //       icon: 'ic-mn-launch',
                  //       index: 4,
                  //     },
                  //   ],
                  // })
                } else {
                  // self.menus.push(this.publishInfo)
                }
              } else {
                // 评估板、魔飞、RTOS 直接显示审核上线、版本管理
                // self.menus.push(this.publishInfo)
              }

              //数据统计- 12, 13 分别是微信和 RTOS，这两者均无数据统计模块
              // if (![12, 13, '12', '13'].includes(res.data.platform)) {
              //   // 微信与rtos以外的平台

              //   // 5,14即 webapi和MorfeiCore 没有 用户统计
              //   if (res.data.platform != 5 && res.data.platform != 14) {
              //     self.statistic.menus = self.statistic.menus.concat(
              //       ...self.appMenus
              //     )
              //   }
              //   self.menus.push(self.statistic)
              // }

              self.statistic.menus = self.statistic.menus.concat(
                ...self.appMenus
              )
              self.menus.push(self.statistic)

              self.$store.dispatch('aiuiApp/setAppInfo', res.data)
            } else {
              self.$router.push({ name: self.subAccount ? 'sub-apps' : 'apps' })
            }
            if (!!callback) {
              callback()
            }
            // this.appendItem2Static()
          },
          error: (err) => {
            self.$router.push({ name: self.subAccount ? 'sub-apps' : 'apps' })
          },
        }
      )
    },
    appendItem2Static() {
      let find = this.menus.find((item) => item.name === '应用数据统计')
      if (find) {
        find.menus.push({
          key: this.subAccount ? 'sub-interact-record' : 'interact-record',
          value: '交互记录',
          icon: 'ai-mn-jiaoyijilu',
          index: 20,
        })
      } else {
        this.menus.push({
          name: '应用数据统计',
          icon: statisticsIcon,
          activeIcon: statisticsActiveIcon,
          menus: [
            {
              key: this.subAccount ? 'sub-interact-record' : 'interact-record',
              value: '交互记录',
              icon: 'ai-mn-jiaoyijilu',
              index: 20,
            },
          ],
        })
      }
    },
    getPaidSource() {
      let self = this
      this.$utils.httpGet(
        this.$config.api.SOURCE_STATISTIC_PAIDSOURCE,
        {
          appid: this.$route.params.appId,
        },
        {
          success: (res) => {
            if (res.flag) {
              if (!!res.data && !!res.data.length) {
                self.paidSources = res.data
                let find = self.menus.find(
                  (item) => item.name === '应用数据统计'
                )
                let sourceMenu = self.sourceMenu
                if (this.subAccount) {
                  sourceMenu.key = 'sub-app-sources'
                } else {
                  sourceMenu.key = 'app-sources'
                }
                if (!!find) {
                  find.menus = find.menus.concat(...sourceMenu)
                } else {
                  self.menus.push({
                    name: '应用数据统计',
                    icon: statisticsIcon,
                    activeIcon: statisticsActiveIcon,
                    menus: [...sourceMenu],
                  })
                }
              }
            } else {
              this.$message.error(res.desc)
            }
          },
        }
      )
    },
    // getWakeupAuth() {
    //   let self = this
    //   this.$utils.httpGet(
    //     this.$config.api.AWAKEN_CONFIG,
    //     {
    //       appid: this.$route.params.appId,
    //     },
    //     {
    //       success: (res) => {
    //         if (!res.data || !res.data.awakeConfig) return
    //         self.contentReviewConfig =
    //           (res.data.contentReviewConfig &&
    //             JSON.parse(res.data.contentReviewConfig)) ||
    //           {}
    //         let config =
    //           res.data.awakeConfig && JSON.parse(res.data.awakeConfig)
    //         let find = this.menus.find((item) => item.name === '应用数据统计')
    //         if (config.switch == '1') {
    //           if (find) {
    //             // 激活设备明细，子账号
    //             find.menus.push({
    //               key: this.subAccount
    //                 ? 'sub-app-online-device'
    //                 : 'app-online-device',
    //               value: '激活设备明细',
    //               icon: 'ai-mn-zaixianshebeimingxi',
    //               index: 19,
    //             })
    //           } else {
    //             self.menus.push({
    //               name: '应用数据统计',
    //               menus: [
    //                 {
    //                   key: this.subAccount
    //                     ? 'sub-app-online-device'
    //                     : 'app-online-device',
    //                   value: '激活设备明细',
    //                   icon: 'ai-mn-zaixianshebeimingxi',
    //                   index: 19,
    //                 },
    //               ],
    //             })
    //           }
    //         }

    //         let findDevelop = this.menus.find((item) => item.name === '开发')
    //         if (config.switch == '1') {
    //           if (findDevelop) {
    //             findDevelop.menus.push({
    //               key: this.subAccount ? 'sub-make-resource' : 'make-resource',
    //               value: '资源制作',
    //               icon: 'ai-mn-huanxing1',
    //               index: 18,
    //             })
    //           } else {
    //             this.menus.splice(1, 0, {
    //               name: '开发',
    //               menus: [
    //                 {
    //                   key: this.subAccount
    //                     ? 'sub-make-resource'
    //                     : 'make-resource',
    //                   value: '资源制作',
    //                   icon: 'ai-mn-huanxing1',
    //                   index: 18,
    //                 },
    //               ],
    //             })
    //           }
    //         }
    //       },
    //       error: (err) => {},
    //     }
    //   )
    // },

    getWakeupAuth() {
      let self = this
      this.handleResouceMakeMenu()
      this.$utils.httpGet(
        this.$config.api.AWAKEN_CONFIG,
        {
          appid: this.$route.params.appId,
        },
        {
          success: (res) => {
            if (!res.data || !res.data.awakeConfig) return
            self.contentReviewConfig =
              (res.data.contentReviewConfig &&
                JSON.parse(res.data.contentReviewConfig)) ||
              {}
            let config =
              res.data.awakeConfig && JSON.parse(res.data.awakeConfig)
            if (res.data.otaConfig) {
              this.menus.push(self.updateMenu)
            }
          },
          error: (err) => {},
        }
      )
    },

    handleResouceMakeMenu() {
      let self = this
      let find = this.menus.find((item) => item.name === '应用数据统计')
      if (find) {
        // 激活设备明细，子账号
        find.menus.push({
          key: this.subAccount ? 'sub-app-online-device' : 'app-online-device',
          value: '激活设备明细',
          icon: 'ai-mn-zaixianshebeimingxi',
          index: 19,
        })
      } else {
        self.menus.push({
          name: '应用数据统计',
          icon: statisticsIcon,
          activeIcon: statisticsActiveIcon,
          menus: [
            {
              key: this.subAccount
                ? 'sub-app-online-device'
                : 'app-online-device',
              value: '激活设备明细',
              icon: 'ai-mn-zaixianshebeimingxi',
              index: 19,
            },
          ],
        })
      }

      // let findDevelop = this.menus.find((item) => item.name === '开发')
      // if (findDevelop) {
      //   findDevelop.menus.push({
      //     key: this.subAccount ? 'sub-make-resource' : 'make-resource',
      //     value: '资源制作',
      //     icon: 'ai-mn-huanxing1',
      //     index: 18,
      //   })
      // } else {
      //   this.menus.splice(1, 0, {
      //     name: '开发',
      //     icon: devIcon,
      //     activeIcon: devActiveIcon,
      //     menus: [
      //       {
      //         key: this.subAccount ? 'sub-make-resource' : 'make-resource',
      //         value: '资源制作',
      //         icon: 'ai-mn-huanxing1',
      //         index: 18,
      //       },
      //     ],
      //   })
      // }
    },

    subAccountSKilInit() {
      if (!this.subAccount) return
      this.accoutType()
      this.$store.dispatch('aiuiApp/setSubAccountAppAuths')
    },
    accoutType() {
      this.menus[0].menus = [
        {
          key: 'sub-app-info',
          value: '应用信息',
          icon: 'ic-mn-basic-info',
          index: 0,
        },
        {
          key: 'sub-app-config',
          value: '应用配置',
          icon: 'ic-mn-device-skill',
          index: 1,
        },
      ]
      // this.menus[1].menus = [
      //   {
      //     name: '开发',
      //     menus: [
      //       {
      //         key: 'sub-app-tool',
      //         value: '接入配置',
      //         icon: 'ai-mn-code',
      //         index: 2,
      //       },

      //     ],
      //   },
      // ]
      this.publishInfo.menus = [
        {
          key: 'sub-app-publish',
          value: '更新发布',
          icon: 'ic-mn-launch',
          index: 5,
        },
        {
          key: 'sub-app-version',
          value: '版本管理',
          icon: 'ic-mn-launch',
          index: 6,
        },
      ]
      this.statistic.menus = [
        {
          key: 'sub-app-statistic-service-index',
          value: '服务统计',
          icon: 'ai-mn-data',
          index: '7',
        },
      ]
      this.appMenus = [
        {
          key: 'sub-app-users',
          value: '用户统计',
          icon: 'ai-mn-data-user',
          index: '9',
        },
      ]
      this.updateMenu.menus = [
        {
          key: 'sub-app-ota-update',
          value: '固件升级',
          icon: 'ic-mn-update',
          index: '10',
        },
      ]
    },
  },
  components: {
    RightTestClose,
    feedBackHover,
    aiuiMenu,
  },
}
</script>

<style lang="scss" scoped>
.aiui-app-menu-head {
  padding: 16px 22px 0;
  height: 96px;
  &-return {
    display: inline-flex;
    align-items: center;
    cursor: pointer;
    margin-bottom: 10px;
    font-size: 14px;
    color: $grey4;
  }
  &-skill-name {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    span {
      font-size: 20px;
    }
  }
}

@media screen and (max-width: 1601px) {
  .os-aside {
    width: 180px;
  }
}
</style>
