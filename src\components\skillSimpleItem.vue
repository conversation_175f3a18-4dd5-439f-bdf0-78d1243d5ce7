<template>
  <div class="os-skill-simple-item">
    <div
      :class="[
        'os-skill-simple-item-thumb',
        { noborder: noborder },
        { circle: !showName },
      ]"
      v-if="!sceneName"
      :style="thumbStyle"
    >
      <img v-if="url" :src="url" />
      <span v-else>{{ name.slice(0, 1) }}</span>
    </div>
    <p v-if="showName" class="os-skill-simple-item-name" :title="name">
      {{ name }}
    </p>
    <p v-if="showName && sceneName" slot="charater" class="sceneName">
      {{ sceneName }}
    </p>
  </div>
</template>

<script>
export default {
  name: 'OsSkillSimpleItem',
  props: {
    url: {
      type: String,
      default: '',
    },
    name: {
      type: String,
      default: '',
    },
    sceneName: {
      type: String,
      default: '',
    },
    noborder: {
      type: Boolean,
      default: false,
    },
    showName: {
      type: Boolean,
      default: true,
    },
    index: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      colors: ['#7B67FB', '#009BFF', '#F18A56', '#32C9FB', '#3257FB'],
    }
  },
  computed: {
    thumbStyle() {
      // 如果没有url，则使用颜色数组中的颜色作为背景色
      if (!this.url) {
        // 根据index选择颜色，确保不超出数组范围
        const colorIndex = this.index % this.colors.length
        return {
          backgroundColor: this.colors[colorIndex],
          color: '#fff', // 使用白色文字以便于在彩色背景上显示
        }
      }
      return {}
    },
  },
  mounted() {},
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="scss" scoped>
p {
  margin-bottom: 0;
}
.os-skill-simple-item {
  display: inline-flex;
  align-items: center;

  &-thumb {
    border: 1px solid $grey2;
    box-shadow: 0 6px 12px 0 rgba(0, 0, 0, 0.05);
    border-radius: 6px;
    margin: 10px 0;
    width: 28px;
    min-width: 28px;
    height: 28px;
    font-size: 14px;
    text-align: center;
    line-height: 28px;
    color: $semi-black;
    background: $white;
    &.circle {
      position: relative;
      width: 66px;
      min-width: 66px;
      height: 66px;
      border-radius: 50%;
      text-align: center;
      vertical-align: middle;
      align-items: center;
      display: flex;
      justify-content: center;
      align-self: center;
      align-content: center;
    }
    &.noborder {
      border: none;
      border-radius: none;
      box-shadow: none;
    }
    img {
      width: 100%;
      height: 100%;
    }
  }
  &-name {
    padding: 0 16px;
    color: $semi-black;
    font-size: 14px;
    font-weight: 600;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    margin-bottom: 0;
    /*padding-top: 13px;*/
    // transform: translateY(30%);
  }
  .sceneName {
    padding: 20px;
  }
}
</style>
