<template>
  <el-dialog
    title="修改密码"
    :visible.sync="dialog.show"
    width="520px"
    :before-close="handleClose"
  >
    <p class="text-grey mgb32">登录邮箱: <span class="mgl8">{{userInfo && userInfo.email}}</span></p>

    <el-form :model="resetForm" label-position="top" :rules="rules" ref="resetForm" label-width="100px" class="login-reg-form mgb32">
      <el-form-item label="旧密码" prop="oldPsw">
        <el-input type="password" v-model="resetForm.oldPsw" placeholder="请输入旧密码" auto-complete="off"></el-input>
      </el-form-item>
      <el-form-item label="新密码" prop="newPsw">
        <el-input type="password" v-model="resetForm.newPsw" placeholder="请输入新密码" auto-complete="off"></el-input>
      </el-form-item>
      <el-form-item label="确认密码" prop="confirmPsw">
        <el-input type="password" v-model="resetForm.confirmPsw" placeholder="请输入确认密码" auto-complete="off"></el-input>
      </el-form-item>
    </el-form>

    <span slot="footer" class="dialog-footer">
      <el-button
        class="reset-btn fs16"
        type="primary"
        @click="submit"
        :class="{'reset-btn-letter': !submitLoading}"
        :loading="submitLoading">{{ submitLoading ? '修改中...' : '确定' }}
      </el-button>
    </span>

  </el-dialog>
</template>

<script>
import { mapGetters } from 'vuex'
import md5 from 'md5-js'

export default {
  props: {
    dialog: {
      type: Object,
      default: {}
    }
  },
  data () {
    var oldPass = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入密码'));
      } else {
        if (value.length < 6) {
          callback(new Error('请至少输入6个字符'));
        }
        if (this.resetForm.newPsw !== '') {
          this.$refs.resetForm.validateField('newPsw');
        }
        callback();
      }
    };
    var validatePass = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入密码'));
      } else {
        if (value.length < 6) {
          callback(new Error('请至少输入6个字符'));
        }
        if (value === this.resetForm.oldPsw) {
          callback(new Error('新密码跟旧密码相同，请重新输入'));
        }
        if (this.resetForm.confirmPsw !== '') {
          this.$refs.resetForm.validateField('confirmPsw');
        }
        callback();
      }
    };
    var validatePass2 = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请再次输入密码'));
      } else if (value !== this.resetForm.newPsw) {
        callback(new Error('两次输入密码不一致!'));
      } else {
        callback();
      }
    };
    return {
      resetForm: {
        oldPsw: '',
        newPsw: '',
        confirmPsw: ''
      },
      submitLoading: false,
      rules: {
        oldPsw: [{ validator: oldPass, trigger: 'blur' }],
        newPsw: [{ validator: validatePass, trigger: 'blur' }],
        confirmPsw: [{ validator: validatePass2, trigger: 'blur' }],
      }
    }
  },
  computed: {
    ...mapGetters({
      userInfo: 'user/userInfo'
    })
  },
  watch: {
    'dialog.show': function(val, oldVal) {
      let self = this
      if (val) {
        this.resetForm = {
          oldPsw: '',
          newPsw: '',
          confirmPsw: ''
        }
      } else {
      }
    }
  },
  mounted() {
  },
  methods: {

    handleClose(done) {
      done();
    },
    close() {
      let self = this
      self.dialog.show = false
    },

    submit() {
      let self = this
      this.$refs.resetForm.validate((valid) => {
        if (valid) {
          self.submitLoading = true
          let data = {
            oldPassword: md5(self.resetForm.oldPsw),
            newPassword: md5(self.resetForm.newPsw)
          }
          this.$utils.httpPost(this.$config.api.USER_AUTH_UPDATEPASS, data, {
            success: (res) => {
              self.$message.success('修改密码成功')
              self.close()
              self.$utils.toPage('/user/logout?pageFrom=login')
            },
            error: (err) => {
              self.submitLoading = false
            }
          })

          /*self.$utils.mutate(self.$apollo, self.$graphQl.changePassword, {
            variables:{
              oldPassword: self.resetForm.oldPsw,
              newPassword: self.resetForm.newPsw
            },
            success: (result) => {
              self.$message.success('修改密码成功')
              self.close()
              self.$utils.toPage('/user/logout?pageFrom=login')
            },
            complete: () => {
              self.submitLoading = false
            }
          })*/
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    }

  },
  components: {
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="scss" scoped>
.reset-btn {
  width: 100%;
  // height: 48px;
  &-letter {
    letter-spacing: 8px;
  }
}
</style>
