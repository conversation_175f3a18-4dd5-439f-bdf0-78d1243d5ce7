.section-sub-title {
  font-weight:600;
  color: $grey5;
}
.btn-wrap {
  margin: 6px 0 8px;
  font-size: 0;
  .el-button {
    margin-left: 0;
    padding: 10px 16px;
    min-width: unset;
    border-right: none;
    border-radius: 0;
    border-right: 1px solid $grey3;
    &:first-child {
      border-radius: 3px 0px 0px 3px;
    }
    &:last-child {
      border-right: none;
      border-radius: 0px 3px 3px 0px;
      &:hover, &:focus {
        border-right: 1px solid $primary;
      }
    }
  }
  .el-button:hover, .el-button:focus, .el-button--primary  {
    border-color: $primary;
    background-color: $primary-light-12;
  }
  .el-button:focus  + .el-button, .el-button--primary + .el-button {
    border-left-color: transparent;
  }
  .el-button--primary:hover, .el-button--primary:focus {
    box-shadow: unset;
  }
  .el-button--primary {
    color: $primary;
    border: 1px solid $primary !important;
  }
}