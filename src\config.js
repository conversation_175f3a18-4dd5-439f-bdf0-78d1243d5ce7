const XFYUN_CONSOLE = {
  development: 'http://test.console.xfyun.cn/',
  integration: 'http://test.console.xfyun.cn/',
  staging: 'https://console.xfyun.cn/',
  stagingHF: 'http://test.console.xfyun.cn/',
  production: 'https://console.xfyun.cn/',
  devops: 'https://console.xfyun.cn/',
}
const AIFUWUS = {
  development: 'http://test.aifuwus.com/',
  stagingHF: 'http://test.aifuwus.com/',
  staging: 'http://www.aifuwus.com/',
  production: 'http://www.aifuwus.com/',
  devops: 'http://www.aifuwus.com/',
}

const AIMIND = {
  development: 'http://172.16.154.32:18800/',
  stagingHF: 'http://172.16.154.32:18800/',
  staging: 'http://172.16.154.32:18800/',
  production: 'http://www.aifuwus.com/',
  devops: 'http://www.aifuwus.com/',
}

export const api = {
  // @账号
  USER_AUTH_USERINFO: `/user/auth/userInformation`,

  // 申请反馈
  SOLUTION_APPLY_LIST: `/resource/solution/list`,
  SOLUTION_APPLY: `/resource/solution/apply`,

  // @技能studio
  STUDIO_USER_SKILLS: `/skill/getUserSkillList`,
  STUDIO_ADD_EDIT_PRIVATESKILL: `/skill/addOrEditPrivateSkill`,
  STUDIO_ADD_EDIT_OPENSKILL: `/skill/addOrEditOpenSkill`,
  STUDIO_ADD_EDIT_SKILL: `/skill/addOrEditMallSkill`,
  STUDIO_SKILL_UP_FUZZY: `/skill/upIsFuzzy`,
  STUDIO_SKILL_DETAIL: `/skill/getSkillDetail`,
  STUDIO_SKILL_DELETE: `/skill/deleteBusiness`,
  STUDIO_SKILL_SKILL_TYPES: `/resource/skill/getSkillType`,
  STUDIO_INTENTS: `/intent/getIntents`,
  STUDIO_ADD_EDIT_INTENT: `/intent/addOrEdit`,
  STUDIO_DEL_INTENT: `/intent/deleteIntent`,
  STUDIO_INTENT_DETAIL: `/intent/getIntentDetail`,
  STUDIO_BULITIN_INTENTS: `/intent/getBulitinIntents`,
  STUDIO_SWITCH_BULITIN_INTENTS: `/intent/switchBulitinIntent`,
  STUDIO_INTENT_SAVE_CONFIRM: `/intent/saveConfirm`,
  STUDIO_INTENT_SAVE_CONFIRM_SENTENCE: `/intent/saveConfirmSentence`,
  STUDIO_INTENT_SAVE_REPLY: `/intent/saveReply`,
  STUDIO_EXPORT_INTENT: `/intent/export`,
  STUDIO_NAMESPACE: `/skill/getNamespace`,
  STUDIO_INTENT_ALL_SLOTS: `/intent/getAllSlots`,

  // 技能优化
  STUDIO_SYSTEM_INTENTS: `/intent/getSystemIntents`,

  // 意图引用优化
  STUDIO_INTENT_CLASSIFIERS: '/intent/getIntentClassifies',
  STUDIO_INTENT_SYSTEM_UTTERANCES: '/utterance/getSystemUtterances',
  STUDIO_SYSTEM_INTENTS_DETAIL: '/intent/getSystemIntentDetail',
  STUDIO_SYSTEM_INTENT_SLOTS: '/intent/getSystemSlots',

  // @技能语料
  STUDIO_INTENT_UTTERANCES: `/utterance/getUtterances`,
  STUDIO_INTENT_ADD_EDIT_UTTERANCE: `/utterance/addOrEdit`,
  STUDIO_INTENT_DEL_UTTERANCE: `/utterance/deleteUtterance`,
  STUDIO_INTENT_UTTERANCE_MARKS_UPDATE: `/utterance/updateMark`,
  STUDIO_INTENT_UTTERANCE_MARK_UPDATE: `/utterance/updateUtteranceMark`,

  STUDIO_INTENT_SLOTS: `/intent/getSlots`,
  STUDIO_SLOT_NECESSARY: `/slot/dialog/saveNecessary`,
  STUDIO_SLOT_UPDATE: `/slot/updateUtteranceSlot`,
  STUDIO_SLOT_SAVE_QUESSENTENCE: `/slot/dialog/saveQuestionSentence`,
  STUDIO_SLOT_DEPLOY_ENTITY: `/slot/deployEntity`,
  STUDIO_SLOT_GET_WILDCARD: `/slot/wildcard/getSlotWildcard`,
  STUDIO_SLOT_ADD_EDIT_WILDCARD: `/slot/wildcard/addOrEidtSlotWildcard`,
  STUDIO_PRIVATE_SKILL_PUBLISH_INIT: `/skill/private/publishInit`,
  STUDIO_SKILL_ENTITYS: `/skill/getEntityList`,
  STUDIO_SKILL_ALL_ENTITYS: `/entity/getEntityAllList`,
  STUDIO_SKILL_EXPERIENCE: `/skill/experience`,
  STUDIO_SKILL_STRUCTURE: `/skill/structure`,
  STUDIO_SKILL_PUBLISH_CHECK: `/skill/publish/checkStatus`,
  STUDIO_SKILL_EXPERIENCE_CLEAN: `/skill/clean-history`,
  //推荐优化
  STUDIO_OPTIMIZE_GET_DATA: `/skill/optimize/get`,
  STUDIO_OPTIMIZE_UPDATE: `/skill/optimize/update`,
  STUDIO_OPTIMIZE_UPDATE_ALL: `/skill/optimize/updateAll`,

  // 业务定制
  STUDIO_EXTEND_SKILL: `/skill/extend/getMakeBusinessList`,
  STUDIO_EXTEND_SKILL_CREATE: `/skill/extend/createExtendSkill`,
  STUDIO_EXTEND_SKILL_EDIT: `/skill/extend/editExtendSkill`,
  STUDIO_EXTEND_ENTITY_SEARCH: `/entity/dict/getExtendDataList`,
  STUDIO_EXTEND_ENTITY_EXPORT_EXCEL: `/entity/dict/doExcelExport`,
  STUDIO_EXTEND_ENTITY_EXPORT_TXT: `/entity/dict/export`,
  STUDIO_EXTEND_ENTITY_ADDDICT: `/entity/dict/addExtendData`,
  STUDIO_EXTEND_ENTITY_EDITDICT: `/entity/dict/addExtendData`,
  STUDIO_EXTEND_ENTITY_DEL: `/entity/dict/deleteExtendData`,
  STUDIO_EXTEND_ENTITY_CREATE: `/entity/dict/createExtendDictByEntityId`,
  STUDIO_EXTEND_GET_CUSTOMIZABLE_SKILL: `/skill/extend/getCustomizableSkills`,
  STUDIO_EXTEND_DEPLOY_SOURCE_ENTITY: `/slot/deploySourceEntity`,
  STUDIO_EXTEND_REVERT_TO_SOURCE_PROCESS: `/cloudFunction/restore`,

  //修饰语
  STUDIO_MODIFIER_GET_LIST: `/modifier/getListByPage`,
  STUDIO_MODIFIER_DEL: `/modifier/delete`,
  STUDIO_MODIFIER_ADD_OR_EDIT: `/modifier/addOrEdit`,
  STUDIO_MODIFIER_DETAIL: `/modifier/detail`,
  STUDIO_MODIFIER_FRAGMENT: `/modifier/fragment/list`,
  STUDIO_MODIFIER_FRAGMENT_ADD_OR_EDIT: `/modifier/fragment/addOrEdit`,
  STUDIO_MODIFIER_FRAGMENT_DEL: `/modifier/fragment/delete`,
  STUDIO_MODIFIER_SLOTS: `/modifier/slotEntity/list`,
  STUDIO_MODIFIER_SLOT_BINDS: `/modifier/slotEntity/bind`,
  STUDIO_MODIFIER_IN_INTENTION: `/modifier/getListByIntent`,
  STUDIO_MODIFIER_OF_SKILL: `/modifier/getListByBusiness`,
  STUDIO_MODIFIER_USED_BY_MODIFIER: `/modifier/getListByModifier`, //当前修饰语引用的修饰语
  STUDIO_MODIFIER_GET_MODIFIERS: `/modifier/getListForTip`, //所有可以被引用的修饰语

  // 实体
  STUDIO_ENTITY_CREATE: `/entity/create`,
  STUDIO_ENTITY_LIST: `/entity/getEntityList`,
  STUDIO_BASE_ENTITY_LIST: `/entity/getBaseEntityList`,
  STUDIO_ENTITY: `/entity/getEntity`,
  STUDIO_ENTITY_EXPORT_EXCEL: `/entity/doExcelExport`,
  STUDIO_ENTITY_EXPORT_TXT: `/entity/dict/exportByDictId`,
  STUDIO_ENTITY_EDIT: `/entity/edit`,
  STUDIO_ENTITY_DEL: `/entity/delete`,
  STUDIO_ENTITY_ENTRY_LIST: `/entity/getEntityData`,
  STUDIO_ENTITY_ENTRY_ADD: `/entity/addData`,
  STUDIO_ENTITY_ENTRY_EDIT: `/entity/editEntityData`,
  STUDIO_ENTITY_ENTRY_DEL: `/entity/deleteData`,
  STUDIO_ENTITY_COMPILE: `/entity/compile`,
  STUDIO_ENTITY_CHECKSKILL: `/entity/checkPersonalSkill`,
  STUDIO_ENTITY_CHECK_COMPILE_STATUS: `entity/checkCompileStatus`,
  STUDIO_ENTITYS_EXCEL: `/entity/doExcelZipExport`,

  // 组合实体
  STUDIO_ENTITY_DETAIL: `/entity/getEntityDetail`,
  STUDIO_ENTITY_COMPILE_BASE_ADD: `/entity/addBaseEntity`,
  STUDIO_ENTITY_COMPILE_BASE_DEL: `/entity/delBaseEntity`,
  STUDIO_ENTITY_DICT_DATA: `/entity/dict/getData`,
  STUDIO_ENTITY_DICT_DEL: `/entity/dict/deleteData`,
  STUDIO_ENTITY_DICT_ADD: `/entity/dict/addData`,
  STUDIO_ENTITY_DICT_EDIT: `/entity/dict/editData`,

  // 动态实体
  STUDIO_ENTITY_PERSONAL_DETAIL: `entity/personal/getPersonalDetail`,
  STUDIO_ENTITY_PERSONAL_CHECKNAME: `/entity/personal/checkRepetName`,
  STUDIO_ENTITY_PERSONAL_SAVE: `/entity/personal/savePersonal`,
  STUDIO_ENTITY_ACCOUNT_KEY: `/user/getUserData`,
  STUDIO_ENTITY_RESET_ACCOUNT_KEY: `/user/resetAccountKey`,
  STUDIO_ENTITY_WHITE_IPS: `/user/setWhiteIps`,
  STUDIO_ENTITY_SWITCH_IP: `/user/switchWhiteIP`,

  // 官方实体
  STUDIO_OFFICIAL_ENTITY_LIST: `/resource/entity/getList`,

  // 辅助词
  STUDIO_AUXILIARY_LIST: `/entity/getAuxiliaryList`,

  // 交互标签
  STUDIO_LABEL_LIST: `/skill/avatar/getMyLabels`,
  STUDIO_LABEL_DEL: `/skill/avatar/deleteActionLabel`,
  STUDIO_LABEL_CREATE: `/skill/avatar/addActionLabel`,
  STUDIO_OFFICIAL_LABEL: `/skill/avatar/getSystemLabels`,
  STUDIO_CANUSE_LABELS: `/skill/avatar/getCanUseLabels`,
  STUDIO_QUOTE_LABELS: `/skill/getQuoteLabels`,
  STUDIO_LABEL_EDIT: `/skill/avatar/editActionLabel`,
  STUDIO_LABEL_DETAIL: `/skill/avatar/getActionLabel`,
  // 问答库
  STUDIO_QA_CHECK_IS_NATIVE_ACCOUNT: `/qa/checkIsNativeAccount`,
  STUDIO_QA_CREATE_EDIT: `/qa/createOrEdit`,
  STUDIO_QA_DEL: `/qa/deleteRepo`,
  STUDIO_QA_LIST: `/qa/getRepositoryList`,
  STUDIO_QA_INFO: `/qa/getRepositoryByRepoId`,
  STUDIO_QA_VERSIONS: `/qa/getVersions`,
  STUDIO_KEY_QA_PUBLISH: `/qa/keyword/publish`,
  STUDIO_KEY_QA_CHECK: `/qa/keyword/publish/check`,
  STUDIO_KEY_QA_STRUCT: `/qa/keyword/struct`,
  STUDIO_KEY_QA_VERSIONS: `/qa/keyword/getQaVersions`,
  STUDIO_KEY_QA_LIMIT: `/qa/keyword/getUserLimit`,
  STUDIO_QA_PAIR_GET: `/qa/queryQaPair`,
  STUDIO_QA_PAIR_ADD_UPDATE: `/qa/addOrUpdateQaPair`,
  STUDIO_KEY_QA_PAIR_ADD_UPDATE: `/qa/keyword/addOrEditQaPair`,
  STUDIO_KEY_QA_PAIR_QUERY: `/qa/keyword/queryQaPair`,
  STUDIO_QA_PAIR_DEL: `/qa/deleteQaPair`,
  STUDIO_KEY_QA_PAIR_DEL: `/qa/keyword/deleteQaPair`,
  STUDIO_QA_EXCEL_EXPORT: `/qa/doExcelExport`,
  STUDIO_KEY_QA_EXCEL_EXPORT: `/qa/keyword/export`,
  STUDIO_QA_SCENES_LIST: `/qa/getScenesByRepoId`,
  STUDIO_QA_STRUCT_OR_PUBLISH: `/qa/structOrPublish`,
  STUDIO_QA_STRUCT_OR_PUBLISH_STATUS: `/qa/structOrPublishStatus`,
  STUDIO_QA_LANGUAGE_OPT: `/qa/getLanguageOpt`,
  STUDIO_QA_TYPE: `/qa/getQaType`,
  STUDIO_QA_USED_LIST: `/app/qa/getUsedAppList`,
  STUDIO_KEY_QA_USED_LIST: `/app/keywordqa/getUsedAppList`,
  STUDIO_QA_USE_IN_APP: `/app/effectQa`,
  STUDIO_KEY_QA_USE_IN_APP: `/app/effectKeywordqa`,
  STUDIO_QA_EXPERIENCE: `/qa/experience`,
  STUDIO_KEY_QA_EXPERIENCE: `/qa/keyword/experience`,

  // 语句问答库AI扩写
  STUDIO_QA_AI_REWRITE: `/qa/switchRewrite`,
  STUDIO_QA_AI_REWRITE_QA_PAIR: `/qa/rewriteQaPair`,
  STUDIO_QA_AI_QUERYREWRITEQ: `/qa/queryRewriteQ`,
  STUDIO_QA_AI_UPDATEREWRITEQ: `/qa/updateRewriteQ`,
  STUDIO_QA_AI_DELETEREWRITEQ: `/qa/deleteRewriteQ`,

  STUDIO_QA_REWRITE: `/qa/rewrite`,

  // 关键词问答AI扩写
  STUDIO_QA_KEYWORD_AI_REWRITE: `/qa/keyword/switchRewrite`,
  STUDIO_QA_KEYWORD_AI_REWRITE_QA_PAIR: `/qa/keyword/rewriteQaPair`,
  STUDIO_QA_KEYWORD_AI_QUERYREWRITEQ: `/qa/keyword/queryRewriteQ`,
  STUDIO_QA_KEYWORD_AI_UPDATEREWRITEQ: `/qa/keyword/updateRewriteQ`,
  STUDIO_QA_KEYWORD_AI_DELETEREWRITEQ: `/qa/keyword/deleteRewriteQ`,

  STUDIO_QA_KEYWORD_REWRITE: `/qa/keyword/rewrite`,
  // 设备人设
  STUDIO_CHARACTER_LIST: `/device/property/repository/getList`,
  STUDIO_CHARACTER_ADD: `/device/property/repository/create`,
  STUDIO_CHARACTER_DEL: `/device/property/repository/delete`,
  STUDIO_CHARACTER_COPY: `/device/property/repository/getList`,
  STUDIO_CHARACTER_ATTRIBUTES: `/device/property/getList`,
  STUDIO_CHARACTER_CATEGORY: `/device/property/getCategoryList`,
  STUDIO_CHARACTER_ATTRIBUTE_EDIT: `/device/property/edit`,
  STUDIO_CHARACTER_QUOTE: `/device/property/repository/getUsed`,
  STUDIO_CHARACTER_IMPORT: `/device/property/excel/import`,
  STUDIO_CHARACTER_EXPORT: `/device/property/excel/export`,
  // STUDIO_CHARACTER_IMPORT: `/device/property/excel/import`,
  STUDIO_CHARACTER_STRUCT: `/device/property/struct`,
  STUDIO_CHARACTER_STATUS: `/device/property/struct/status`,

  // 知识库
  STUDIO_REPO_REL_LIST: `/kbqa/theme/getList`, // 问答关系列表
  STUDIO_REPO_REL_LIST_DEL: '/kbqa/theme/delete',
  STUDIO_REPO_REL_CREATE_EDIT: `/kbqa/theme/addOrEdit`,
  STUDIO_REPO_REL_DETAIL: `/kbqa/template/themeDetail`,
  STUDIO_REPO_QUESTION_ADD_EDIT: `/kbqa/template/question/addOrEdit`, // 编辑or添加问题模板
  STUDIO_REPO_QUESTION_DEL: `/kbqa/template/question/delete`,
  STUDIO_REPO_ANSWER_ADD_EDIT: `/kbqa/template/answer/addOrEdit`,
  STUDIO_REPO_ANSWER_DEL: `/kbqa/template/answer/delete`,
  STUDIO_REPO_KEYWORD_ADD_EDIT: `/kbqa/keyword/addOrEdit`,
  STUDIO_REPO_KEYWORD_DEL: `/kbqa/keyword/delete`,
  STUDIO_REPO_KEYWORD_LIST: `/kbqa/keyword/getList`,
  STUDIO_REPO_REPLY_ADD_EDIT: `/kbqa/reply/addOrEdit`,
  STUDIO_REPO_REPLY_LIST: `/kbqa/reply/getList`,
  STUDIO_REPO_EXCEL_EXPORT: `/kbqa/excel/export`,

  STUDIO_DOCQA_QUOTE: `/app/pluginStudio/getRagV2Config`,

  // 版本管理
  STUDIO_SKILL_VERSION: `/skill/getVersions`,
  STUDIO_SKILL_UNSHELVE: `/skill/unshelve`,
  STUDIO_SKILL_CANCELPUBLISH: `/skill/cancelAuditSkill`,
  STUDIO_SKILL_APP_USED_LIST: `/app/getUsedAppList`,
  STUDIO_SKILL_USE_IN_APP: `/app/effectSkill`,
  // 后处理
  STUDIO_PROCESS_INFO: `/cloudFunction/info`,
  STUDIO_PROCESS_UPLOAD: `/cloudFunction/uploadJS`,
  STUDIO_PROCESS_DOWNLOAD_FILE: `/cloudFunction/downloadFile`,
  STUDIO_PROCESS_SAVE_SCRIPT: `/cloudFunction/saveSingleJS`,
  STUDIO_PROCESS_DELETE_FILE: `/cloudFunction/deleteFile`,
  STUDIO_PROCESS_WEBHOOK_INFO: `/skill/webhookInit`,
  STUDIO_PROCESS_WEBHOOK_SAVE: `/skill/webhookSave`,
  STUDIO_PROCESS_WEBHOOK_GENERATE_KEY: `/skill/generateRsaKey`,

  // @aiui
  AIUI_APP_INTER_LIST: `/app/interaction/list`,
  AIUI_APP_EXPERIENCE: `/app/taste/getAnswer`,
  AIUI_APP_EXPERIENCE_CLEAN: `/app/taste/clean-history`,
  AIUI_APP_LIST: `/app/getAppList`,
  AIUI_APP_DELETE: `/app/delete`,
  AIUI_APP_TYPE_List: `/app/getAppTypeList`,
  AIUI_APP_CHECKNAME: `app/checkName`,
  AIUI_APP_CREATE: `app/create`,
  AIUI_APPINFO: `/app/getApp`,
  AIUI_EDIT_INFO: `/app/edit`,
  AIUI_RESETAPIKEY: `/app/webapi/resetApiKey`,
  AIUI_STATISTIC_USER: `/app/getUserData`,
  AIUI_STATISTIC_USER_COUNT: `/app/getUserDataCount`,
  AIUI_STATISTIC_SERVICE_DATA: `/app/getServiceData`, // 非webApi应用的交互次数、响应时间
  AIUI_STATISTIC_SERVICE: `/app/getServiceList`, // 非webApi应用的调用次数
  AIUI_STATISTIC_WEBAPI_TABEL: `/app/webapi/getWebAPIMeterCount`,
  AIUI_STATISTIC_WEBAPI_CHART: `/app/webapi/getWebAPIMeterCountList`,
  AIUI_STATISTIC_CHART: '/app/getMeterCountList',
  AIUI_STATISTIC_SOURCE_TABEL: `/app/financial/getSource`,
  AIUI_STATISTIC_SOURCE_CHART: `/app/financial/getSourceCount`,
  AIUI_STATISTIC_RCLOG: `/app/getRclog`,
  AIUI_STATISTIC_ALL_USED: `/app/financial/getAllUsed`,
  AIUI_EXPORT_RCLOG: `/app/exportRclog`,
  AIUI_SCENE_LIST: `/app/scene/list`,
  AIUI_SCENE_CREATE: `/app/scene/create`,
  AIUI_SCENE_DELETE: `/app/scene/delete`,
  AIUI_SCENE_SAVEAASCENE: `/app/scene/saveAaScene`,
  AIUI_APP_SDKVERSION: `/app/sdkversion`,
  AIUI_APP_DOWNLOADSDK: `/app/downloadSDK`,
  AIUI_BACKSTOP_LIST: `/app/assist/getAssistByScene`,
  AIUI_SAVE_BACKSTOP: `/app/assist/appAssistConfig`,
  AIUI_LAST_GUARD_LIST: `/app/assist/getAppEnsureConfig`,
  AIUI_SAVE_LAST_GUARD: `/app/assist/editAppEnsureConfig`,
  AIUI_SKILL_VERSION: `/skill/getSkillVersions`,
  AIUI_QA_VERSION: `/qa/getQaVersions`,
  AIUI_MUST_ANSWER: `/app/mustanswer/get`,
  AIUI_SAVE_MUST_ANSWER: `/app/mustanswer/save`,
  AIUI_COPY_SCENE_LIST: `/app/scene/getCopyScenes`,
  AIUI_COPY_SCENE: `/app/scene/copy`,

  // 大模型相关
  AIUI_APP_SERVICE_PACKAGE: `/app/statistics/model/getServicePackage`,

  // @ip白名单
  AIUI_APP_GET_WHITE_IPS: `/app/webapi/getWebAPIWhiteIPs`,
  AIUI_APP_SWITCH_WHITE_IPS: `/app/webapi/switchWebAPIWhiteIP`,
  AIUI_APP_SET_WHITE_IPS: `/app/webapi/setWebAPIWhiteIps`,

  // @审核上线 & 发布 & 版本管理
  AIUI_APP_AUDIT_GET: `/app/auditing/get`,
  AIUI_APP_AUDIT_SAVE: `/app/auditing/save`,
  AIUI_APP_VERSION_DIFF: `/app/version/compare`,
  AIUI_APP_VERSION: `/app/version/get`,
  AIUI_APP_VERSION_PUBLISH: `/app/version/publish`,
  AIUI_APP_VERSION_LIST: `/app/version/list`,
  AIUI_APP_VERSION_ROLLBACK_CHECK: `/app/version/rollback/check`,
  AIUI_APP_VERSION_ROLLBACK: `/app/version/rollback`,
  AIUI_APP_VERSION_SCENE: `/app/version/scene/list`,

  //QC
  AIUI_APP_QC_AUTH: `/app/qc/getQcAuth`,
  AIUI_APP_QC_CONFIG: `/app/qc/getConfig`,
  AIUI_APP_QC_SKILLS: `/app/qc/getSkills`,
  AIUI_APP_QC_JS_LIST: `/app/qc/getJsList`,
  AIUI_APP_QC_JS_VERSIONS: `/app/qc/getJsVersions`,
  AIUI_APP_QC_SAVE_CONFIG: `/app/qc/saveConfig`,

  // @技能商店
  AIUI_STORE_SKILLS: `/resource/skill/getList`,
  AIUI_STORE_QAS: `/resource/qa/getList`,
  AIUI_STORE_SKILL_TYPES: `/resource/skill/getSkillType`,
  AIUI_STORE_SKILL_DETAIL: `/resource/skill/getDetail`,
  AIUI_STORE_QA_DETAIL: `resource/qa/getDetail`,
  AIUI_STORE_DIALECT_SKILL_DETAIL: `/resource/skill/getDialectDetail`,
  AIUI_STORE_OTHER_SKILL_DETAIL: `/resource/skill/getSkillMallDetail`,
  AIUI_STORE_SKILL_PROVIDERS: `/resource/skill/getProviders`,
  AIUI_STORE_SKILL_CONTENTDATA: `/resource/skill/getContentData`,
  AIUI_STORE_TASTE: `/resource/taste/getAnswer`,
  AIUI_STORE_SKILL_APP_AUTH_LIST: `/app/getAppAuthList`,
  AIUI_STORE_APP_SKILL_CONFIG: `/app/storeAppConfig`,
  AIUI_STORE_EXPERIENCE_CLEAN: `/resource/taste/clean-history`,
  AIUI_STORE_SKILL_NOTICES: `/resource/skill/store/info`,

  FILE_DOWNLOAD: `/download`,

  // @AIUI app 语音识别
  AIUI_DIST_ACCENTANDDOMAIN: `/app/dist/getAccentAndDomain`,
  AIUI_DIST_AACCONF: `/app/dist/getAacConf`,
  AIUI_DIST_SAVEAACCONF: `/app/dist/saveAacConf`,
  AIUI_DIST_SIMPLEPROTOCOL: `/app/simpleprotocol/get`,
  AIUI_DIST_SAVE_SIMPLEPROTOCOL: `/app/simpleprotocol/save`,
  AIUI_DIST_HOTWORD: `/app/dist/getHotWord`,
  AIUI_DIST_DOWN_HOT_WORD: `/app/dist/downHotWordFDFS`,
  AIUI_DIST_DELETE_HOT_WORD: `/app/dist/deleteHotWord`,
  AIUI_DIST_ASR_LANGUAGE_ENT_RELATION: `/app/dist/getAsrLanguageEntRelation`,
  AIUI_DIST_DHW: `/app/dist/getDhwConfig`,
  AIUI_DIST_SAVE_DHW: `/app/dist/saveDhwConfig`,

  // sos应用
  // 极速链路获取语音识别引擎列表
  AIUI_BOT_IAT_ACCENTANDDOMAIN: `/app/dist/sos/getAccentAndDomain`,
  // 保存语音识别引擎参数
  AIUI_BOT_IAT_SAVECONFIG: `/bot/iat/saveConfig`,
  // 获取语音识别引擎参数
  AIUI_BOT_IAT_GETCONFIG: `/bot/iat/getConfig`,
  // 获取语义模型列表
  AIUI_BOT_MODEL_LIST: `/bot/model/list`,
  //
  AIUI_BOT_MODEL_SAVE: `/bot/model/save`,

  AIUI_BOT_CONFIG_GET_PROMPT: `/bot/config/getPrompt`,
  AIUI_BOT_CONFIG_SAVE_PROMPT: `/bot/config/savePrompt`,

  // 应用角色配置
  AIUI_BOT_ROLE_LIST: 'bot/role/publish/list',
  AIUI_BOT_ROLE_SAVE: 'bot/role/saveConfig',
  AIUI_BOT_ROLE_DETAIL: 'bot/role/publish/get',
  AIUI_BOT_ROLE_TTS_LIST: 'app/tts/informants',
  AIUI_BOT_ROLE_TTS_SAVE: 'app/tts/saveSosTtsConfig',
  AIUI_BOT_ROLE_TTS_GET: 'app/tts/getSosTtsConfig',
  AIUI_BOT_ROLE_TTS_VCN: 'app/tts/getSosVcn',
  AIUI_BOT_ROLE_TTS_TEXT: 'bot/role/tts/voiceClone/text',

  AIUI_BOT_CONFIG_GET_BOTRAGREPOS: `/bot/config/getBotRagRepos`,
  AIUI_BOT_CONFIG_SAVE_RAGREOPCONFIG: '/bot/config/saveRagRepoConfig',

  // 智能体（插件）
  AIUI_BOT_CONFIG_GET_BotAgentPlugins: `/bot/config/getBotAgentPlugins`,
  AIUI_BOT_CONFIG_SAVE_AgentPluginConfig: '/bot/config/saveAgentPluginConfig',

  AIUI_BOT_CONFIG_GET_AbleAndSearchConfig: `/bot/config/getAbleAndSearchConfig`,
  AIUI_BOT_CONFIG_SAVE_AbleAndSearchConfig: `/bot/config/saveAbleAndSearchConfig`,

  AIUI_BOT_CONFIG_GET_MODELS: `/bot/model/list`,
  AIUI_BOT_CONFIG_SAVE_MODEL: `/bot/model/save`,
  AIUI_BOT_CONFIG_GET_MODEL_INFO: `/bot/model/get`,

  AIUI_BOT_CONFIG_HANDLE_GET: `/bot/config/handle/get`,
  AIUI_BOT_CONFIG_HANDLE_SAVE: `/bot/config/handle/save`,

  // app 微信
  AIUI_APP_WX_LIST: `/app/wx/getWXList`,
  AIUI_APP_WX_CONFIG: `/app/wx/getWXConfig`,
  AIUI_APP_WX_LOCATION: `/app/wx/getWXLocation`,
  AIUI_APP_WX_SAVE_CONFIG: `/app/wx/saveWXConfig`,
  AIUI_APP_WX_SAVE_LOCATION: `/app/wx/saveWXLocation`,

  // app语义配置
  AIUI_APP_KEYWORD_FILTER: `/app/keywordFilter`,
  AIUI_APP_SKILL_CONFIG: `/app/getSkillConfigs`,
  AIUI_APP_CONFIG: `/app/getAppConfig`,
  AIUI_APP_BUSINESS_CAN_USE: `/app/businessCanUse`,
  AIUI_APP_OTHER_CONFIG: `/app/getAppOtherConfig`,
  AIUI_APP_THRESHOLD: `/app/threshold/getThreshold`,
  AIUI_APP_BUSINESS_SOURCE_CONFIG: `/app/getBusinessSourceConfig`,
  AIUI_APP_SAVE_SEMANTIC: `/app/saveSemantic`,
  AIUI_APP_TRANSLATION_SOURCE_TARGET: `/app/translation/getSourceTarget`,
  AIUI_APP_TRANSLATION_TASTE: `/app/translation/taste`,
  AIUI_APP_TRANSLATION_TRANSCONF: `/app/translation/getAiuiItransConf`,
  AIUI_APP_TRANSLATION_SAVE_CONF: `/app/translation/saveAiuiItransConf`,
  AIUI_APP_CALLBACK_AAHCONF: `/app/callback/getAahConf`,
  AIUI_APP_CALLBACK_CHECKURL: `/app/callback/checkUrl`,
  // 大模型后处理
  AIUI_APP_HANDLE_GET: `/app/handle/get`,
  AIUI_APP_HANDLE_SAVE: `/app/handle/save`,

  AIUI_APP_CALLBACK_SAVEAAHCONF: `/app/callback/saveAahConf`,
  AIUI_APP_AWAKEN_INFO: `/app/morfeiAndAiui/awakenInfo`,
  AIUI_APP_AWAKEN_SAVE: `app/morfeiAndAiui/awaken60Save`,
  AIUI_APP_AWAKEN_PACK: `/app/morfeiAndAiui/awaken60Pack`,

  AIUI_APP_SCENE_CHECKAUTH: `/app/scene/checkAuth`,
  AIUI_APP_STATISTICS_ALL_COUNT: `/app/statistics/model/getAllCount`,

  // 4.5链路
  AIUI_APP_PLUGINSTUDIO_USABLELIST: `/app/pluginStudio/getUsableList`,
  AIUI_APP_PLUGINSTUDIO_SAVECONFIG: `/app/pluginStudio/saveConfig`,
  AIUI_APP_PLUGINSTUDIO_GETCONFIG: `/app/pluginStudio/getConfig`,

  // 虚拟人配置
  AIUI_VIRTUAL_CONFIG: `/app/avatar/getConfig`,
  AIUI_VIRTUAL_SAVE_CONFIG: `/app/avatar/saveConfig`,
  AIUI_VIRTURAL_LIST: `/app/avatar/getAnchors`,
  AIUI_VIRTUAL_SDK: `/app/avatar/downloadSDK`,
  AIUI_VIRTUAL_VCN_RELATION: `/app/avatar/getAvatarVcnRelation`,
  // app语音合成
  AIUI_TTS_INFORMANTLIST: `/app/tts/informantList`,
  AIUI_TTS_TTSCONFIG: `/app/tts/getTtsConfig`,
  AIUI_TTS_SAVETTSCONFIG: `/app/tts/saveTtsConfig`,
  AIUI_TTS_AUDITIONSYNTHESIS: `/app/tts/auditionSynthesis`,
  AIUI_TTS_INFORMANTS: `/app/tts/getInformants`,

  // webapi调试
  AIUI_WEBAPI_DEBUG: `/app/webapi/debug/webaiui`,

  // @用户 userinfo
  USER_LIMIT_COUNT: `/user/getLimitCount`,
  USER_CERTIFICATED: `/user/isCertificated`,
  USER_MESSAGE_UNREAD_COUNT: `/user/message/unReadCount`,
  USER_MESSAGE_LIST: `/user/message/list`,
  USER_MESSAGE_DETAIL: `/user/message/detail`,
  USER_MESSAGE_DELETE: `/user/message/delete`,
  USER_MESSAGE_READ: `/user/message/read`,
  USER_MESSAGE_READ_ALL: `/user/message/readAll`,
  USER_AUTH_UPDATE: `/user/auth/update`,
  USER_AUTH_UPDATEPASS: `/user/auth/updatePass`,
  USER_ASSISTANT_QUERY: `user/assistant/query`,
  USER_ASSISTANT_QUESTIONS: `/user/assistant/questions`,

  // 协同操作
  COOP_ACCOUNTS: `user/sub/getAccounts`,
  COOP_ADD_ACCOUNTS: `user/sub/add-account`,
  COOP_EDIT_ACCOUNTS: `user/sub/edit-account`,
  COOP_DEL_ACCOUNTS: `user/sub/delete-account`,
  COOP_GET_AUTHORITY: `user/sub/getAuthority`,
  COOP_SAVE_AUTHORITY: `user/sub/saveAuthority`,
  COOP_BATCH_SAVE_AUTHORITY: `user/sub/batchSaveAuthority `,
  COOP_LOGS: `user/sub/getLogs`,
  COOP_LOGS_SEARCH: `user/sub/getLogSearch`,
  COOP_UPDATE_PWD: `user/sub/password/update`,
  COOP_SUB_ACCOUNT_LOGIN: `/user/sub/login/check-account`,
  COOP_SUB_ACCOUNT_LOGOUT: `/user/sub/login/logout `,
  COOP_SUB_ACCOUNT_SET_COOKIES: `user/sub/login/setcookies`,
  COOP_SUB_ACCOUNT_GET_COOKIES: `user/sub/login/getcookies`,
  SUB_USER_ACCOUNT_INFO: `/user/sub/accountInfo`,
  SUB_USER_SKILL_AUTH: `/user/sub/getMyAuthority`,
  SUB_USER_EXTEND_COUNT: `/user/sub/getSubExtendCount`,

  // AIUI 应用子账号
  AIUI_APP_SUB_ACCOUNT_LOG_SAVE: `/app/log/save`,

  // 我的订单
  ORDER_LIST: `/order/listByUid`,
  ORDER_CANCEL: `/order/cancel`,
  ORDER_DELETE: `/order/delete`,
  ORDER_DETAIL: `/order/detail`,

  // partner
  AIUI_PARTNER: `/app/scheme/apply`,

  // 70唤醒词
  AWAKEN_TEMPLATES: '/app/awaken/templates',
  AWAKEN_CPY: '/app/awaken/copy',
  AWAKEN_IMPORT: '/app/awaken/import',
  AWAKEN_EXPORT: '/app/awaken/export',
  AWAKEN_MAKEANDDOWNLOAD: '/app/awaken/makeAndDownload',
  AWAKEN_DELETE_ALL: '/app/awaken/deleteAll',
  AWAKEN_DELETE: '/app/awaken/delete',
  AWAKEN_ADD_EDIT: '/app/awaken/addOrEdit',
  AWAKEN_CONFIG: '/app/awaken/getConfig',
  AWAKEN_UPLOAD: '/app/awaken/uploadRes',
  AWAKEN_GET_NAME: '/app/awaken/getRes',
  AWAKEN_DOWNLOAD: '/app/awaken/downRes',
  AWAKEN_MAKE: '/app/awaken/makeRes',
  AWAKEN_GET_LIST: '/app/awaken/getList',

  // SN信息展示
  APP_SN_LIST: '/app/sn/getList',
  APP_SN_EXPORT: '/app/sn/doExport',
  APP_SN_SWITCH: '/app/sn/switch',
  APP_SN_SEARCH_LIST: '/app/sn/searchList',
  APP_SN_SEARCH_FAIL_LIST: '/app/sn/searchFailList',
  APP_SN_EXPORT_DETAIL: '/app/sn/exportDetail',
  APP_SN_ABILITY_EXPORT_DETAIL: `/app/sn/exportAbilityList`,
  APP_SN_EXPORT_FAIL_DETAIL: '/app/sn/exportFailDetail',
  APP_SN_IMPORT: '/app/sn/import',
  APP_SN_DELETE_FAIL: '/app/sn/deleteFail',
  APP_SN_SWITCH_GET: '/app/sn/switch/get',
  APP_SN_IMPORT_FAIL: '/app/sn/importFail',
  APP_SN_GETFAIL_COUNT: '/app/sn/getFailCount',

  APP_SN_GET_ABILITY_LIST: '/app/sn/getAbilityLicc',
  APP_SN_SEARCH_ABILITY_LIST: '/app/sn/searchAbilityList',
  // 购买价格页
  GET_ACCENT: '/app/dist/getAccents',
  GET_INFORMANTS: '/app/tts/informants',
  GET_APP_WARE: '/app/ware/get', // 获取aiui所有购买包
  GET_APP: '/app/ware/getApps', // 点击立即购买弹出的appid列表

  //新手礼包
  GET_GIFT_APPS: '/app/getGiftApps',
  GIFT_COLLECT: '/user/gift/get',
  GIFT_COLLECT_CONDITION: '/user/gift/check',
  GIFT_SHOW: '/user/gift/show',
  GIFT_POPUP: '/resource/gift/popup',

  // AIUI工单系统
  WORKER_ORDER_ALL_GET: '/user/workorder/getAllWorkOrder',
  WORKER_ORDER_TYPES_GET: '/user/workorder/getWorkOrderTypes',
  WORKER_ORDER_UPLOAD_POST: '/user/workorder/upload',
  WORKER_ORDER_SUBMIT_POST: '/user/workorder/submitWorkOrder',
  WORKER_ORDER_DETAIL_GET: '/user/workorder/getWorkOrderDetail',
  WORKER_ORDER_REPLY_POST: '/user/workorder/submitContent',
  // 内容推荐
  RECOMMEND_CONTENT_LIST: '/app/content/getContentTypes',
  RECOMMEND_CONTENT_GET_ALBUM: '/app/content/getContentTypesChild',
  RECOMMEND_CONTENT_GET_SONGS: '/app/content/getAlbumDetails',
  RECOMMEND_CONTENT_ADDCONTENTTYPES: '/app/content/addContentTypes',
  RECOMMEND_CONTENT_UPDATECONTENTTYPES: '/app/content/updateContentTypes',
  RECOMMEND_CONTENT_DELETESENCETYPES: '/app/content/deleteContentTypes',
  RECOMMEND_CONTENT_ADDALBUM: '/app/content/addAlbums',
  RECOMMEND_CONTENT_GETSCENCE: '/app/content/getBusinessFunctions',
  RECOMMEND_CONTENT_ALBUMUPLOAD: '/app/content/upload',
  RECOMMEND_CONTENT_DELETE_ALBUM: '/app/content/deleteAlbums',
  RECOMMEND_CONTENT_UPDATE_ALBUM: '/app/content/updateAlbums',
  RECOMMEND_CONTENT_BUSINESSFUNC: '/app/content/getBusinessFunctions',
  RECOMMEND_CONTENT_BUSINESSSEARCH: '/app/content/getBusinessSearch',
  RECOMMEND_CONTENT_ADD_DETAILS: '/app/content/addDetails',
  RECOMMEND_CONTENT_BUSINESS_RELEASE: '/app/content/businessRelease',
  RECOMMEND_CONTENT_BUSINESS_DETAIL: '/app/content/getBusinessAlbumDetail',
  RECOMMEND_CONTENT_DEFAULT_IMPORT: '/app/content/defaultImport',

  // 敏感词
  SENSITIVE_TYPE_AND_CONFIG: '/app/sensitive/word/type',
  SENSITIVE_TYPE_AND_CONFIG_UPDATE: '/app/sensitive/word/type/level',
  SENSITIVE_TYPE_DOWNLOAD: '/app/sensitive/word/downloadSensitiveWord',
  SENSITIVE_TYPE_TRUNCATE: '/app/sensitive/word/truncateSensitiveWordExcel',
  SENSITIVE_TYPE_GET_UPLOADED: '/app/sensitive/word/getSensitiveWord',

  // 信源统计
  SOURCE_STATISTIC_PAIDSOURCE: '/app/source/paidSources',
  SOURCE_STATISTIC_DEVICEREPORT: '/app/source/devicesActivedReport',
  SOURCE_STATISTIC_ACTIVEDETAIL: '/app/source/devicesActivedDetail',
  SOURCE_STATISTIC_ACTIVEEXPORT: '/app/source/devicesActivedExport',

  // 服务统计
  SERVICE_STATISTICS_GETAPPBUSINESS: '/app/financial/getAppBusiness', // 查询应用商务类型, 显示按台授权不限或按实际授权限制

  // AIMIND对接
  AIMIND_GET_TOKEN: 'qamind/km/getToken',

  // OTA固件升级
  OTA_LIST: '/app/firmware/version/list',
  // 新增固件/修改固件/发布/删除/验证/是否强制更新
  OTA_FORM_SUBMIT: '/app/firmware/version/submit',
  // 验证固件：查询设备id列表
  OTA_VERIFY_LIST: '/app/firmware/version/device/list',
  // 验证固件：批量添加设备id
  OTA_VERIFY_ADD: '/app/firmware/version/device/batchUpdate',
  // 固件下载
  OTA_DOWNLOAD: '/app/firmware/version/download',

  // chat 大模型体验
  CHAT_FILE_UPLOAD: '/user/chat/file/upload',
  CHAT_FILE_DELETE: '/user/chat/file/delete',
  CHAT_FILE_GET: '/user/chat/file/get',
  CHAT_FILE_DOWNLOAD: '/user/chat/file/download',
  CHAT_SIGN: '/user/chat/sign',
  CHAT_MUSIC: '/user/chat/music',

  RESOURCE_AIHUB_GETSKILLSLIST: `/resource/skill/getList`,
  RESOURCE_AIHUB_GETSELECTED_SKILLSLIST: `/resource/aihub/getSelectedSkills`,
  RESOURCE_AIHUB_GETPLUGINLIST: `/resource/aihub/getPluginList`,
  RESOURCE_AIHUB_GETKNOWLEDGELIST: `/resource/knowledge/official`,
  RESOURCE_AIHUB_GETABILITYINFO: `/resource/aihub/getAbilityInfo`,
  RESOURCE_AIHUB_GETAllCHAININFO: `/resource/aihub/getAllChainInfo`,
  RESOURCE_AIHUB_GETREPLYSTYLES: `/resource/getReplyStyles`,

  APP_TTS_GET_INFOS_FOR_EXPERIENCE: '/app/tts/getInfosForExperience',

  AIUI_APP_CHAIN_INFO: '/app/getChainInfo',
  AIUI_APP_GET_COLLOQUIAL_DEGREE: `/app/pluginStudio/getColloquialDegree`,

  // 产品动态
  RESOURCE_DYNAMICS_LIST: '/resource/dynamics/list',

  //知识管理
  KNOWLEDGE_TYPE_CREATE: '/knowledge/repo/create', //知识库类型创建
  KNOWLEDGE_TYPE_LIST: '/knowledge/repo/list', //知识库类型获取
  KNOWLEDGE_TABLE_LIST: '/knowledge/doc/list', //知识库列表
  KNOWLEDGE_TABLE_STATUS_LIST: '/knowledge/doc/status/list', //知识库状态列表
  KNOWLEDGE_STORE_DELETE: '/knowledge/doc/delete', //知识库删除
  KNOWLEDGE_STORE_CREATE: '/knowledge/doc/create', //知识库创建
  KNOWLEDGE_STORE_MODEL: '/knowledge/model', //知识库模板获取
  KNOWLEDGE_STORE_FILE_LIST: '/knowledge/doc/files', //知识库文档选项获取
  KNOWLEDGE_STORE_FILE_DELETE: '/knowledge/doc/file/delete', //知识库文件删除
  KNOWLEDGE_STORE_FILE_EDIT: '/knowledge/doc/edit', //知识库编辑
  KNOWLEDGE_STORE_POINT_LIST: '/knowledge/knowledge/list', //知识点列表获取
  KNOWLEDGE_STORE_POINT_EDIT: '/knowledge/knowledge/edit', //知识点编辑
  KNOWLEDGE_STORE_POINT_DELETE: '/knowledge/knowledge/delete', //知识点删除
  KNOWLEDGE_STORE_POINT_CREATE: '/knowledge/knowledge/create', //知识点新增
  KNOWLEDGE_STORE_QS_ADD: '/knowledge/question/create', //知识点问题新增
  KNOWLEDGE_STORE_QS_DEL: '/knowledge/question/delete', //知识点问题删除
  KNOWLEDGE_STORE_BUILD: '/knowledge/build', //知识构建
  KNOWLEDGE_EXPERIENCE: '/knowledge/search', //知识体验
  QA_GETREPODOCINFO: '/qa/getRepoDocInfo',
  KNOWLEDGE_DOC_INFO: '/knowledge/doc/info',

  // v2 版文档问答库
  AIUI_KNOWLEDGE_GET_REPO_LIST: `/aiuiKnowledge/knowledge/repo/aiui/getList`,
  AIUI_KNOWLEDGE_GET_MODELS: `/aiuiKnowledge/knowledge/model/getModels`,
  AIUI_KNOWLEDGE_REPO_CREATE: `/aiuiKnowledge/knowledge/repo/create`,
  AIUI_KNOWLEDGE_REPO_DELETE: `/aiuiKnowledge/knowledge/repo/delete`,

  // rag知识库
  AIUI_KNOWLEDGE_REPO_DOC_INFO: '/aiuiKnowledge/knowledge/doc/docInfo',
  AIUI_KNOWLEDGE_REPO_CHUNK_SEARCH: `/aiuiKnowledge/knowledge/chunk/search`,

  // role角色
  AIUI_ROLE_LIST: '/bot/role/list',
  AIUI_ROLE_TEMPLATE_LIST: '/bot/role/template/list',
  AIUI_ROLE_CREATE: '/bot/role/create',
  AIUI_ROLE_DELETE: '/bot/role/delete',
  AIUI_ROLE_DETAIL: '/bot/role/get',
  AIUI_ROLE_EDIT: '/bot/role/edit',
  AIUI_ROLE_ICON_LIST: '/bot/role/list/image',
  AIUI_ROLE_PUBLISH: '/bot/role/publish',
  AIUI_ROLE_PROPERTY_DETAIL: '/bot/role/property/get',
  AIUI_ROLE_PROPERTY_EDIT: '/bot/role/property/edit',
  AIUI_ROLE_CATEGORY_LIST: '/bot/role/category/list',
  AIUI_ROLE_TTS: '/bot/role/tts/get',
  AIUI_ROLE_TTS_SAVE: '/bot/role/tts/save',
  AIUI_ROLE_TTS_LIST: '/bot/role/tts/informants',
  AIUI_ROLE_VOICE_CLONE: '/bot/role/tts/voiceClone/upload',
  AIUI_ROLE_VOICE_CLONE_LIST: '/bot/role/tts/voiceClone/list',
  AIUI_ROLE_VOICE_CLONE_DELETE: '/bot/role/tts/voiceClone/delete',
  AIUI_ROLE_VOICE_CLONE_CHECK: '/bot/role/tts/voiceClone/delete/check',
  AIUI_ROLE_USED_APP_LIST: '/bot/role/getUsedAppList',

  // agent 智能体
  AGENT_TABLE_LIST: '/aiui-agent/plugin/list',
  AGENT_CREATE: '/aiui-agent/plugin/create',
  AGENT_TYPE_LIST: '/aiui-agent/openPlatform/workflowTypeList',
  AGENT_COPY: '/aiui-agent/plugin/copy',
  AGENT_DELETE: '/aiui-agent/plugin/delete',
  AGENT_EDIT: '/aiui-agent/plugin/edit',
  AGENT_DETAIL: '/aiui-agent/plugin/detail',
  AGENT_BUILD: 'aiui-agent/plugin/build',
  AGENT_ALL_LIST: '/aiui-agent/agent/user_custom/all',
  AGENT_CLASSIFY_TYPE: '/aiui-agent/agent/classify_type/all',
  AGENT_THIRD_INFO_SAVE: '/aiui-agent/plugin/third/save',
  AGENT_THIRD_PARTY: '/aiui-agent/third-party/type/all',
  AGENT_ICON_LIST: '/aiui-agent/plugin/icon/candidates',
  AGENT_DIALOG_CHECK: '/aiui-agent/plugin/dialog/parameter/check',

  // agent下的意图
  AGENT_INTENT_CREATE: '/aiui-agent/plugin/intent/add',
  AGENT_INTENT_TABLE_LIST: '/aiui-agent/plugin/intent/page',
  AGENT_INTENT_EDIT: '/aiui-agent/intent/edit',
  AGENT_INTENT_DETAIL: '/aiui-agent/intent/detail',
  AGENT_INTENT_DETAIL_OFFICIAL: '/aiui-agent/intent/detail/version',

  //官方意图
  AGENT_OFFFICAL_INTENT_LIST: '/aiui-agent/intent/official/list',
  AGENT_OFFICAL_INTENT_QUOTE: '/aiui-agent/plugin/intent/quote', // 引用  官方意图
  AGENT_INDENT_DELETE: '/aiui-agent/plugin/intent/delete', // 删除意图

  // 引用的意图
  AGENT_USED_QUOTE_LIST: '/aiui-agent/agent/intent/quote/list',

  // 语料
  AGENT_CORPUS_LIST: '/aiui-agent/intent/corpus/list',
  AGENT_CORPUS_ADD: '/aiui-agent/intent/corpus/add',
  AGENT_CORPUS_DELETE: '/aiui-agent/intent/corpus/delete',
  AGENT_CORPUS_EXPAND: '/aiui-agent/tools/corpus/generalization',
  AGENT_CORPUS_LIST_OFFICIAL: '/aiui-agent/intent/version/corpus/list',

  // 后处理
  AGENT_POSTHANDLE_EDIT: '/aiui-agent/agent/postHandle/edit',

  // 工作流
  AGENT_WORK_FLOW_LIST: '/aiui-agent/workflow/list',
  AGENT_WORK_FLOW_SYNC: '/aiui-agent/workflow/sync',
  AGENT_WORK_FLOW_QUOTE: '/aiui-agent/agent/workflow/quote',
  AGENT_WORK_FLOW_REMOVE: '/aiui-agent/workflow/remove',
  AGENT_WORK_FLOW_CREATE: '/aiui-agent/workflow/create',
  AGENT_WORK_FLOW_PUBLISH: '/workflow/publish',
  AGENT_WORK_FLOW_INTENT_BUILD: '/aiui-agent/plugin/pluginIntentBuild',

  //  查询引用工作流的全量智能体列表
  AGENT_WORK_FLOW_QUOTE_ALL_SEARCH: '/aiui-agent/workflow/quote_agent/all',

  // --------------------------------------------------------------------------------------------------

  //   这个是所有的老的智能体的接口  加上了OLD  后缀
  // agent 智能体
  AGENT_TABLE_LIST_OLD: '/aiui-agent/agent/list',
  AGENT_CREATE_OLD: '/aiui-agent/agent/create',
  AGENT_TYPE_LIST_OLD: '/aiui-agent/agent/type/all',
  AGENT_DELETE_OLD: '/aiui-agent/agent/delete',
  AGENT_EDIT_OLD: '/aiui-agent/agent/edit',
  AGENT_DETAIL_OLD: '/aiui-agent/agent/detail',
  AGENT_BUILD_OLD: '/aiui-agent/agent/build',
  AGENT_ALL_LIST_OLD: '/aiui-agent/agent/user_custom/all',
  AGENT_CLASSIFY_TYPE_OLD: '/aiui-agent/agent/classify_type/all',

  // agent下的意图
  AGENT_INTENT_CREATE_OLD: '/aiui-agent/agent/intent/create',
  AGENT_INTENT_TABLE_LIST_OLD: '/aiui-agent/agent/intent/list2',
  AGENT_INTENT_EDIT_OLD: '/aiui-agent/agent/intent/edit',
  AGENT_INTENT_DETAIL_OLD: '/aiui-agent/agent/intent/detail',

  //官方意图
  AGENT_OFFFICAL_INTENT_LIST_OLD: '/aiui-agent/agent/intent/official/list',
  AGENT_OFFICAL_INTENT_QUOTE_OLD: '/aiui-agent/agent/intent/quote', // 引用  官方意图
  AGENT_INDENT_DELETE_OLD: '/aiui-agent/agent/intent/delete', // 删除意图

  // 引用的意图
  AGENT_USED_QUOTE_LIST_OLD: '/aiui-agent/agent/intent/quote/list',

  // 语料
  AGENT_CORPUS_LIST_OLD: '/aiui-agent/agent/intent/corpus/list',
  AGENT_CORPUS_ADD_OLD: '/aiui-agent/agent/intent/corpus/add',
  AGENT_CORPUS_DELETE_OLD: '/aiui-agent/agent/intent/corpus/delete',
  AGENT_CORPUS_EXPAND_OLD: '/aiui-agent/tools/corpus/expand',

  // 后处理
  AGENT_POSTHANDLE_EDIT_OLD: '/aiui-agent/agent/postHandle/edit',

  // 工作流
  AGENT_WORK_FLOW_LIST_OLD: '/aiui-agent/workflow/list',
  AGENT_WORK_FLOW_SYNC_OLD: '/aiui-agent/workflow/sync',
  AGENT_WORK_FLOW_QUOTE_OLD: '/aiui-agent/agent/workflow/quote',
  AGENT_WORK_FLOW_REMOVE_OLD: '/aiui-agent/workflow/remove',
  AGENT_WORK_FLOW_CREATE_OLD: '/aiui-agent/workflow/create',

  //  查询引用工作流的全量智能体列表
  AGENT_WORK_FLOW_QUOTE_ALL_SEARCH_OLD: '/aiui-agent/workflow/quote_agent/all',

  USER_LIMIT_COUNT_OLD: `/user/getLimitCount`,
}

export const Env = process.env

export default {
  install(Vue) {
    Vue.prototype.$config = {
      server: '',
      api: api,
      env: process.env,
      xfyunConsole: XFYUN_CONSOLE[process.env.NODE_ENV],
      aifuwus: AIFUWUS[process.env.NODE_ENV],
      aimind: AIMIND[process.env.NODE_ENV],
      //docs: process.docHost + '/',
      docs: 'https://aiui-doc.xf-yun.com/project-1/',
      // studio: process.studioHost + '/',
      // aiui: process.aiuiHost + '/',
    }
    // console.log(
    //   '***************xfyunConsole,aifuwus',
    //   XFYUN_CONSOLE[process.env.NODE_ENV],
    //   AIFUWUS[process.env.NODE_ENV]
    // )
  },
  env: process.env.NODE_ENV,
}
