<template>
  <!-- 1:开放技能 2:发布至商店技能 3:方言技能 7:兜底技能 9:封闭技能-->
  <div class="os-scroll">
    <official-skill v-if="type == 1 || type == 7"></official-skill>
    <dialect-skill v-else-if="type == 3"></dialect-skill>
    <other-skill v-else-if="type == 2 || type == 9"></other-skill>
  </div>
</template>

<script>
import OfficialSkill from './officialSkill'
import DialectSkill from './dialectSkill'
import OtherSkill from './otherSkill'

export default {
  name: 'store-skill',
  data() {
    return {
      id: '',
      type: '',
    }
  },
  watch: {},
  computed: {
    skillParamsShow() {
      return this.skill.protocols.answer || this.skill.protocols.semantic
    },
  },
  created() {
    this.id = this.$route.params.skillId
    this.type = this.$route.query.type
    // type 1: 开放技能 2：商店发布技能 7：兜底技能 3: 方言技能 9: 后来新增的封闭技能
    if (
      this.type != 1 &&
      this.type != 2 &&
      this.type != 7 &&
      this.type != 3 &&
      this.type != 9
    ) {
      this.$router.push({ path: '/store/skills' })
    }
  },
  methods: {},
  components: {
    OfficialSkill,
    DialectSkill,
    OtherSkill,
  },
}
</script>

<style lang="scss">
.aiui-store-skills {
  padding: 40px 37px 50px;
  .store-skill-big-label {
    font-size: 20px;
    margin-bottom: 24px;
  }
  .store-skill-label {
    font-size: 18px;
    position: relative;
    font-weight: 500;
    padding-left: 10px;
    color: $grey001;
    margin-bottom: 20px;
    &:before {
      width: 4px;
      height: 20px;
      background-color: $primary;
      content: ' ';
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      margin: auto;
    }
  }
  .store-skill-desc {
    color: #000;
    font-size: 16px;
    line-height: 30px;
    margin-bottom: 20px;
  }
}
.store-skill-detail {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
  &-thumb {
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 10px;
    width: 120px;
    height: 120px;
    text-align: center;
    font-size: 30px;
    overflow: hidden;
    border: 1px solid $grey2;
    img {
      width: 100%;
      height: 100%;
    }
  }
  &-content {
    margin-left: 20px;
  }
  &-name {
    font-size: 20px;
    margin-bottom: 13px;
    span:nth-child(1) {
      color: $grey001;
      margin-right: 16px;
    }
    span:nth-child(2) {
      color: $grey002;
    }
  }
  &-tags {
    margin-bottom: 10px;
    display: flex;
    align-items: center;
  }
  &-tag {
    display: inline-flex;
    align-items: center;
    margin-right: 20px;
    span {
      padding-left: 8px;
      color: $grey003;
    }
    i {
      color: $warning;
    }
  }
  &-tag:nth-child(2) i {
    color: $success;
  }
  &-tag:nth-child(3) i {
    color: $dangerous;
  }
}

.store-skill-phrases {
  &-title {
    display: flex;
    align-items: center;
    color: $grey002;
    margin-bottom: 20px;
    span {
      padding-left: 4px;
      font-size: 16px;
    }
  }
  &-single,
  &-many {
    border: 1px solid $grey2;
  }
  &-single {
    .store-skill-phrases-user {
      display: inline-block;
      margin: 15px 0 15px 60px;
      word-break: break-all;
    }
  }
  &-many {
    padding: 30px 15% 30px 0px;
    .store-skill-phrases-user,
    .store-skill-phrases-skill {
      width: auto;
      // min-width: 228px;
      max-width: calc(100% - 120px);
      margin: 10px 18px;
      word-break: break-all;
    }
  }
  &-user {
    width: 228px;
    // background-color: $grey1;
    background-color: #e3f0fc;
    color: $primary;
    padding: 10px 16px;
    border-radius: 6px;
    position: relative;
    margin-left: 4px;
    &:before {
      position: absolute;
      content: ' ';
      top: 50%;
      left: -11px;
      width: 0;
      height: 0;
      border-width: 9px 7px 0;
      border-style: solid;
      border-color: #f2f5f7 transparent transparent;
      transform: translateY(-50%) rotate(90deg);
      border-color: #e3f0fc transparent transparent;
    }
  }
  &-skill {
    width: 228px;
    // background-color: $primary-light-12;
    background-color: $primary;
    color: #fff;
    padding: 10px 16px;
    border-radius: 6px;
    position: relative;
    // margin-left: calc(50% + 15px) !important;
    margin-left: 4px;
    float: right;
    text-align: right;
    display: inline-block;
    &:before {
      position: absolute;
      content: ' ';
      top: 50%;
      right: -11px;
      width: 0;
      height: 0;
      border-width: 9px 7px 0;
      border-style: solid;
      border-color: $primary transparent transparent;
      transform: translateY(-50%) rotate(-90deg);
    }
  }
}

.store-skill-phrases + .store-skill-phrases {
  margin-top: 30px;
}

.store-skill-source {
  margin-bottom: 24px;
  &-info {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    span {
      padding-left: 20px;
      color: $grey002;
      font-size: 16px;
      font-weight: 400;
    }
  }
  &-desc {
    color: $grey003;
  }
}
.store-skill-content {
  // padding-left: 17px;
}

.store-skill-table,
.store-skill-table-default {
  thead {
    tr {
      th {
        background: #f7f8fa;
      }
    }
  }
}
.store-skill-table,
.store-skill-table-default {
  border-top: 0;
  .el-table__empty-block {
    display: none;
  }
  thead {
    color: $grey001;
  }
  thead > tr > th {
    // border-right: 1px solid $grey2;
    padding: 21px 0;
    // border-top: 1px solid $grey2;
    .cell {
      line-height: 20px;
      font-size: 16px;
    }
    &:first-child {
      // padding-left: 10px;
      border-right: 0;
    }
  }
  .el-table__body tr:first-child > td {
    padding-top: 22px;
  }
  .el-table__body tr:last-child > td {
    padding-bottom: 22px;
  }
  .el-table__body tr > td {
    // border-bottom: 0;
    // border-right: 1px solid $grey2;
    padding: 21px 0;
    .cell {
      line-height: 20px;
      font-size: 16px;
      color: $grey002;
    }
  }
  .el-table__body tr:hover > td {
    background-color: #fff;
  }
}
.store-skill-table-default {
  thead > tr > th:first-child {
    // border-right: 1px solid $grey2;
  }
}

// 技能商店适配不同屏幕
@media screen and (max-width: 1601px) {
  .aiui-store-skills {
    padding: 26px 25px 50px;
    .store-skill-big-label {
      font-size: 14px;
      margin-bottom: 14px;
    }
    .store-skill-label {
      font-size: 14px;
      position: relative;
      font-weight: 400;
      padding-left: 10px;
      margin-bottom: 10px;
      &:before {
        width: 3px;
        height: 13px;
      }
    }
    .store-skill-desc {
      font-size: 12px;
      line-height: 20px;
      margin-bottom: 13px;
    }
  }
  .store-skill-detail {
    margin-bottom: 16px;
    &-thumb {
      border-radius: 10px;
      width: 80px;
      height: 80px;
      font-size: 30px;
    }
    &-content {
      margin-left: 20px;
    }
    &-name {
      font-size: 14px;
      margin-bottom: 0px;
      span:nth-child(1) {
        color: $grey001;
        margin-right: 13px;
      }
      span:nth-child(2) {
        color: $grey002;
      }
    }
    .el-button {
      padding: 7px 13px;
      font-size: 12px;
    }
    &-tags {
      margin-bottom: 0px;
      display: flex;
      align-items: center;
    }
    &-tag {
      display: inline-flex;
      align-items: center;
      margin-right: 20px;
      span {
        padding-left: 8px;
        font-size: 12px;
        color: $grey003;
      }
      i {
        color: $warning;
      }
    }
  }

  .store-skill-table,
  .store-skill-table-default {
    thead > tr > th {
      padding: 16px 0;
      .cell {
        line-height: 18px;
        font-size: 12px;
      }
    }
    .el-table__body tr:first-child > td {
      padding-top: 16px;
    }
    .el-table__body tr:last-child > td {
      padding-bottom: 16px;
    }
    .el-table__body tr > td {
      padding: 16px 0;
      .cell {
        line-height: 18px;
        font-size: 12px;
      }
    }
  }

  .store-skill-phrases {
    &-title {
      color: $grey002;
      margin-bottom: 14px;
      span {
        padding-left: 4px;
        font-size: 12px;
      }
    }
    &-many {
      padding: 30px 15% 30px 0px;
    }
  }
  .store-skill-phrases + .store-skill-phrases {
    margin-top: 15px;
  }

  .store-skill-source {
    margin-bottom: 12px;
    &-info {
      margin-bottom: 8px;
      span {
        font-size: 14px;
      }
    }
    &-desc {
      color: #000;
      font-size: 12px;
    }
  }
}
</style>
