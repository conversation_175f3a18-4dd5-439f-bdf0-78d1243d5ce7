<template>
  <os-page :options="pageOptions">
    <studio-qaBank-header-right slot="btn" />
    <div class="info-page">
      <el-form
        class="mgt48 mgb56"
        ref="skillForm"
        :rules="rules"
        :model="qa"
        label-width="118px"
        label-position="left"
      >
        <el-form-item label="问答类型">
          {{ qa.type == 0 ? '语句问答' : qa.type == 3 ? '关键词问答' : '' }}
        </el-form-item>
        <el-form-item label="问答名称" prop="name" :placeholder="'问答名称'">
          <el-input v-model="qa.name"></el-input>
        </el-form-item>
        <el-form-item label="问答id"> {{ qa.repositoryId }}</el-form-item>
      </el-form>

      <div style="margin-top: 40px; text-align: right">
        <os-give-up-save :edited="changed" @noSave="noSave" />
        <el-button
          type="primary"
          @click="onSubmit(2)"
          :loading="saving"
          :disabled="!changed"
        >
          {{ saving ? '保存中...' : '保存' }}
        </el-button>
      </div>
    </div>
    <page-leave-tips
      :dialog="leaveDialog"
      @save="onSubmit"
      @noSave="noSave"
      @noJump="noJump"
    />
  </os-page>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'qa-info',
  data() {
    return {
      pageOptions: {
        title: '基本信息',
        loading: false,
      },
      rules: {
        name: [
          {
            required: true,
            message: '问答库名称不能为空',
            trigger: 'blur',
          },
          {
            min: 0,
            max: 32,
            message: '问答名称长度不能超过32个字符',
            trigger: 'blur',
          },
          {
            pattern: /^[a-zA-Z0-9_\u4e00-\u9fff]+$/,

            message: '问答名称仅支持汉字、字母、数字、下划线',
            trigger: 'blur',
          },
        ],
      },
      qa: {},

      saving: false,
      leaveDialog: {
        show: false,
      },
      routeTo: {},

      initDataChanged: false, //记录computed检测不到的数据是否有改变(包括：别名、示例说法、iconUrl)
    }
  },
  beforeRouteLeave: function (to, from, next) {
    if (this.changed) {
      this.leaveDialog.show = true
      this.routeTo = to
      next(false)
    } else {
      next()
    }
  },
  computed: {
    ...mapGetters({
      originalQa: 'studioQa/qa',
    }),
    edited() {
      let self = this
      return self.qa.name !== self.originalQa.name
    },
    changed() {
      return this.edited || this.initDataChanged
    },
  },
  watch: {
    originalQa: function (val, oldVal) {
      this.qa = this.$deepClone(val)

      if (this.routeTo.name) {
        this.$router.push({
          name: this.routeTo.name,
          params: this.routeTo.params,
        })
      }
    },
  },
  created() {
    if (this.$store.state.studioQa.id) {
      this.qa = this.$deepClone(this.$store.state.studioQa.qa)
    }
  },
  methods: {
    // onZhNameBlur(e) {
    //   this.$refs.skillForm.validateField('name')
    // },
    checkInitDataStatus() {
      let self = this
      if (self.qa.name != self.originalQa.name) {
        self.initDataChanged = true
      } else {
        self.initDataChanged = false
      }
    },

    onSubmit() {
      let self = this
      if (this.saving) {
        return
      }

      this.$refs.skillForm.validate((valid) => {
        if (valid) {
          console.log('this.qa', this.qa.repositoryId)
          this.saving = true
          let data = {
            repoId: this.qa.repositoryId,
            name: this.qa.name,
            // avatar: this.qa.avatar,
            type: this.qa.type,
          }
          if (this.qa.avatar) {
            data.avatar = this.qa.avatar
          }

          //    {
          //   repoId: this.qaInfo.repositoryId,
          //   name: this.qaInfo.name,
          //   avatar: val ? 1 : 0,
          //   type: 0, //type: 自定义问答 0, 开放问答 1, 知识库 2
          // },
          const api = this.$config.api.STUDIO_QA_CREATE_EDIT
          this.$utils.httpPost(api, data, {
            success: (res) => {
              self.saving = false
              self.$message.success('保存成功')
              self.$refs.skillForm && self.$refs.skillForm.clearValidate()
              self.$store.dispatch('studioQa/setQa', this.qa.repositoryId)
              self.initDataChanged = false
            },
            error: (err) => {
              this.saving = false
              console.log('page=>>')
              console.log(err)
            },
          })
        }
      })
    },
    noSave() {
      this.$refs.skillForm.clearValidate()
      this.initDataChanged = false
      this.qa = this.$deepClone(this.originalQa)

      if (this.routeTo.name) {
        this.$router.push({
          name: this.routeTo.name,
          params: this.routeTo.params,
        })
      }
    },
    noJump() {
      this.routeTo = {}
    },
  },
  components: {},
}
</script>

<style lang="scss" scoped></style>
