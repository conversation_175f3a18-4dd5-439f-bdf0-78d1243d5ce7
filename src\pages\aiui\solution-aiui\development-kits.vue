<template>
  <div class="ktv-solution">
    <div class="banner-wrap">
      <div class="banner">
        <p class="banner-title">软硬件一体化产品</p>
        <p class="banner-desc">iFLYOS 与业内知名厂商合作，共同打造软硬件一体化产品方案，多种平台选择，专业技术支持服务，更快落地您的智能语音设备。</p>
      </div>
    </div>

    <div class="character-section">
      <p class="title anchor1">成品音箱，助力开发者快速打造自己的产品</p>
      <div class="character-item character1"></div>
      <div class="character-item character2">
        <p class="item-title">蓝小飞智能音箱</p>
        <p class="item-desc">环形 6MIC 阵列结构、设备级二次开发、持续交互模式、IP 设备自由定制的成品音箱方案，搭载 iFLYOS 生态，200+ 技能、100+ 内容合作方、 20+ 主流智能家居品牌、云端消息推送、应用级定制开发等，可高度定制您的智能音箱。</p>
        <a class="btn-to-learn-more anchor2" :href="href1" target="_blank">了解更多</a>
      </div>
      <div class="divide"></div>
      <div class="character-item character3">
        <p class="item-title">蓝小飞智屏</p>
          <p class="item-desc">线性 4MIC 阵列结构、7 寸高清大屏、设备级二次开发、持续交互模式的有屏音箱方案，搭载 iFLYOS 生态，海量音视频资源、丰富的儿童内容、粤语及中英文混合交互，高度开放，可根据自身需求进行二次开发与定制。</p>
        <a class="btn-to-learn-more" :href="href2" target="_blank">了解更多</a>
      </div>
      <div class="character-item character4"></div>
    </div>
    <div class="kits-section">
      <p class="title anchor3">开发套件，快速评估验证，灵活定制生产</p>
      <div class="kits-section_content">
        <div class="kit-item">
          <p class="kit-name">iFLYOS XR872 开发套件</p>
          <div class="kit1"></div>
          <p class="kit-desc">深度融合了讯飞声学前端单麦唤醒和iFLYOS丰富的在线语音技能，并支持离线语音控制。低成本低功耗，高集成度，适用于儿童玩具、智能台灯、智能闹钟、插座等使用场景。</p>
          <p class="kit-params">
            芯片：XR872<br />
            系统：FreeRtos <br />
            麦克风：1MIC
          </p>
          <a class="btn-to-learn-more" :href="XR872Href" target="_blank">了解更多</a>
        </div>
        <div class="kit-item">
          <p class="kit-name">iFLYOS MT8516 开发套件</p>
          <div class="kit-img kit2"></div>
          <p class="kit-desc">环形 6MIC 阵列结构与 MT8516 开发板高度集成的软硬件一体化开发方案，红外、串口、ZigBee 等多接口开放，小巧易扩展，搭载 iFLYOS 生态，满足多种远、近场语音交互场景。</p>
          <p class="kit-params">
            芯片：MT8516<br />
            系统：Linux<br />
            麦克风：环形 6MIC
          </p>
          <a class="btn-to-learn-more" :href="href3" target="_blank">了解更多</a>
        </div>
        <div class="kit-item">
          <p class="kit-name">iFLYOS R328 开发套件</p>
          <div class="kit-img kit3"></div>
          <p class="kit-desc">内置iFLYOS丰富的内容资源，支持远场拾音，兼顾性能和成本，适用于低成本音箱等设备。</p>
          <p class="kit-params">
            芯片：R328<br />
            系统：Linux<br />
            麦克风：线性 2MIC
          </p>
          <a class="btn-to-learn-more sell-soon">即将开放</a>
        </div>
      </div>
    </div>
    <div class="products-wrap">
      <div class="title">合作案例</div>
      <div class="product-carousel-box">
        <div class="product-carousel-row" ref="firstRow" :style="carouselStyle.one.first">
          <div
            class="product-carousel-row-partner partner"
            v-for="(partner, index) in osPartner" :key="index"
          >
            <div class="partner-img" :style="{'background-position': partner.map}"></div>
            <p class="product-name">{{partner.name}}</p>
          </div>
        </div>
        <div class="product-carousel-row" :style="carouselStyle.one.second">
          <div
            class="product-carousel-row-partner partner"
            v-for="(partner, index) in osPartner" :key="index"
          >
            <div class="partner-img" :style="{'background-position': partner.map}"></div>
            <p class="product-name">{{partner.name}}</p>
          </div>
        </div>
      </div>
      <div class="product-item-wrap-m">
        <div class="product-item" v-for="(partner, index) in osPartner" :key="index">
          <div class="product-img" :style="{'background-position': partner.mapM}"></div>
          <p class="product-name">{{partner.name}}</p>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  layout: 'home',
  data() {
    return {
      href1: 'http://www.aifuwus.com/onstage/cmddetail?product_type=1352',
      href2: 'http://www.aifuwus.com/onstage/cmddetail?product_type=1362',
      href3: 'http://www.aifuwus.com/onstage/cmddetail?product_type=1364',
      XR872Href: 'http://www.aifuwus.com/onstage/cmddetail?product_type=1580',
      screenWidth: 0,
      oneStep: 0,
      oneRowWidth: 0,
      one: {
        first: 0,
        second: 0
      },
      interval: null
    }
  },
  computed: {
    osPartner() {
      let maps = []
      let colum = ['0', '-240px', '-460px', '-690px', '665px', '888px', '440px', '205px']
      let columM = ['-4px', '-100px', '-188px', '-280px', '-368px', '-459px', '-547px', '-640px']
      let productsName = ['小先智能音箱', '中移晓言音箱', '乐森星际特工T9-x', '小飞智驾语音支架', '迪沃智能AI手表', '晨芯视频早教机', '金源德智能儿童闹钟', '智能背景音乐主机']
      let start = 690
      let startM = 2985
      for (let i=0; i<8; i++) {
        maps.push({
          map: `${colum[i]} -${start}px`,
          name: productsName[i],
          mapM: `${columM[i]} -${startM}px`,
        })
      }

      maps.push({
        map: `-8px -910px`,
        name: '小黄蜂智能音箱',
        mapM: `-5px -3973px`,
      })
      return maps
    },
    carouselStyle() {
      return {
        one: {
          first: {
            left: `${this.one.first}px`
          },
          second: {
            left: `${this.one.second}px`
          }
        }
      }
    }
  },
  mounted(){
    let self = this
    window.onresize = () => {
      return (() => {
        window.screenWidth = document.body.clientWidth
        self.screenWidth = window.screenWidth
      })()
    }
    this.oneRowWidth = this.$refs.firstRow && this.$refs.firstRow.clientWidth || 0
    this.beginInterval()
    this.scrollToPoints()
  },
  methods: {
    beginInterval () {
      let self = this
      this.interval = setInterval(function(){
        self.one.first = self.oneStep
        self.one.second = self.oneStep + self.oneRowWidth
        self.oneStep -= 1
        if (self.oneStep + self.oneRowWidth <= 0) {
          self.oneStep = 0
        }
      }, 20)
    },
    scrollToPoints(){
      let anchorName = localStorage.getItem('developKitsAnchor')
      const anchor = document.querySelector(anchorName);
      anchor && anchor.scrollIntoView()
    }
  }
}
</script>
<style lang="scss" scoped>
.banner-wrap {
  width: 100%;
  min-width: 1000px;
  padding-top: 60px;
  background: url('../../../assets/images/solutions/development-kits/banner.png') center no-repeat #010251;
  background-size: 1900px;
  background-position-y: -63px;
}
.banner {
  margin: 0 auto;
  padding-top: 112px;
  height: 500px;
  width: 1200px;
  position: relative;
  background-size: 100%;
  background-position-x: 390px;
  color: #fff;
}
.banner-title{
  font-size: 46px;
  line-height: 60px;
  letter-spacing: 2px;
}
.banner-desc {
  margin: 16px 0 64px;
  max-width: 580px;
  font-size: 20px;
  line-height: 32px;
}

.title {
  margin-bottom: 80px;
  font-size: 36px;
  text-align: center;
}
.character-section {
  margin: 0 auto;
  padding: 100px 0 67px;
  width: 1200px;
  font-size: 0;
  .title {
    margin-bottom: 12px;
  }
}
.character-item {
  display: inline-block;
  width: 460px;
  vertical-align: top;
  min-height: 413px;
  .item-title {
    margin-top: 120px;
    margin-bottom: 12px;
    font-size: 30px;
    line-height: 40px;
    font-size: 500;
  }
  .item-desc {
    font-size: 16px;
    line-height: 24px;
    color:$grey5;
  }
}
.divide {
  width: 1200px;
  height: 0px;
  border-top: 1px solid $grey2;
}
.character1 {
  margin-right: 80px;
  margin-left: 110px;
  height: 440px;
  background: url('../../../assets/images/solutions/development-kits/development-kit.png');
  background-size: 1600px;
  background-position-y: -4px;
  background-position-x: 24px;
}
.character3 {
  margin-right: 80px;
  margin-left: 110px;
}
.character4 {
  background: url('../../../assets/images/solutions/development-kits/development-kit.png');
  background-size: 913px;
  background-position-y: -9px;
  background-position-x: -436px;
  background-size: 1605px;
  background-position-y: -5px;
}

.btn-to-learn-more {
  display: inline-block;
  margin: 32px auto 28px;
  width: 120px;
  height: 44px;
  text-align: center;
  line-height: 44px;
  font-size: 16px;
  font-weight: 500;
  color: #fff;
  border-radius: 1px;
  background: $primary;
}
.sell-soon {
  opacity: 0.65;
  cursor: default;
}

.kits-section {
  padding: 100px 0;
  border-top: 1px solid $grey2;
  border-bottom: 1px solid $grey2;
  .kits-section_content {
    margin: 0 auto;
    width: 1202px;
    font-size: 0;
  }
  .kit-item {
    display: inline-block;
    vertical-align:top;
    padding: 0 48px;
    width:400px;
    height:626px;
    text-align: center;
    border: 1px solid $grey2;
    border-left: none;
    &:first-child {
      border-left: 1px solid $grey2;
    }
  }
  .kit-name {
    margin: 48px 0 32px;
    font-size: 24px;
    font-weight:500;
    line-height: 26px;
  }
  .kit-img {
    margin: 0 auto 24px;
    width:160px;
    height:160px;
    background: url('../../../assets/images/solutions/development-kits/development-kit.png');
    background-size: 1540px;
    background-position-y: 540px;
  }
  .kit-desc {
    margin-bottom: 12px;
    height: 120px;
    font-size: 16px;
    line-height: 24px;
    text-align: left;
    color: $grey6;
  }
  .kit-params {
    text-align: left;
    line-height: 22px;
    font-size: 14px;
    color: $grey5;
  }
  .btn-to-learn-more {
    margin-top: 40px;
  }
  .kit1 {
    margin: 0 auto 24px;
    width:160px;
    height:160px;
    background: url('../../../assets/images/solutions/development-kits/XR872.png');
    background-size: contain;
    background-position-y: -5px;
    background-position-x: 1px;
  }
  .kit2 {
    background-position-x: 0px;
  }
  .kit3 {
    background-position-x: -306px;
  }
}
.product-carousel-box {
  height: 234px;
}
.product-carousel-row {
  position: absolute;
  white-space: nowrap;
  height: 230px;
  display: inline-block;
}
@mixin partners () {
  background: url('../../../assets/images/solutions/development-kits/development-kit.png');
  background-size: auto;
}
.partner {
  margin: 0 25px;
  width: 200px;
  height: 230px;
  position: relative;
  display: inline-block;
  &-img {
    width: 200px;
    height: 200px;
    margin: auto;
    @include partners();
    background-size: 1800px;
  }
}

.products-wrap {
  padding: 100px 0;
  text-align: center;
  .title {
    margin-bottom: 52px;
    font-size: 36px;
  }
  .product-item-wrap-m {
    display: none;
  }
  .product-item {
    margin-left: 120px;
    display: inline-block;
    overflow: hidden;
    width: 200px;
    &:first-child {
      margin-left: 0;
    }
  }
  .product-img {
    height: 200px;
    margin-bottom:4px;
    background: url('../../../assets/images/solutions/development-kits/development-kit.png');
    background-size: 870px;
    background-position-y: 200px;
  }
  .product-name {
    line-height:26px;
    font-size: 20px;
  }
}
@media screen and (max-width:1400px) {
  .banner-wrap {
    background-position-x: 50%;
  }
}
@media screen and (max-width:719px) {
  .banner-wrap {
    min-width: unset;
    height: 452px;
    text-align: center;
    background: transparent;
    .banner {
      width: 100%;
      padding-top: 56px;
      height: 392px;
      background: url('../../../assets/images/solutions/development-kits/banner-m.png') center no-repeat #010251;
      background-size: cover;
    }
    .banner-title{
      font-size: 30px;
      line-height: 40px;
      letter-spacing: 0.5px;
    }
    .banner-desc {
      margin: 8px 0 32px;
      padding: 0 31px;
      font-size: 14px;
      letter-spacing: normal;
      line-height: 22px;
    }
  }
  .title {
    padding: 0 24px;
    font-size: 24px;
  }
  .character-section {
    position: relative;
    padding: 56px 0;
    width: 100%;
    min-width: unset;
    height: 1150px;
    .divide {
      border-top: none;
    }
    .character-item {
      display: block;
      margin: 0 auto;
      padding: 0 23px;
      min-width: unset;
      min-height: unset;
      width: 100%;
      text-align: center;
      .item-title {
        margin-top: 0;
        margin-bottom: 8px;
        font-size: 18px;
        line-height: 24px;
      }
      .item-desc {
        text-align: left;
        font-size: 16px;
        line-height:24px;
      }
    }
    .character1 {
      margin-top: 16px;
      width: 240px;
      height: 240px;
      background-position: 0;
      background-size: 880px;
      background-position-y: 0;
    }
    .character3 {
      position: absolute;
      bottom: 56px;
    }
    .character4 {
      margin: 63px auto 15px;
      width: 210px;
      height: 210px;
      background-position: 630px -18px;
      background-size: 887px;
    }
  }
  .btn-to-learn-more {
    margin: 20px auto 0;
    width: 104px;
  }
  .kits-section {
    padding: 56px 0;
    .kits-section_content {
      width: 100%;
    }
    .title {
      margin-bottom: 0;
    }
    .kit-item {
      display: block;
      margin: 0 auto;
      padding: 0;
      width: calc( 100% - 48px );
      height: auto;
      border: none;
      &:first-child {
        border-left: none;
      }
    }
    .kit-name {
      margin: 56px 0 24px;
      font-size: 20px;
      font-weight:500;
      line-height: 26px;
    }
    .kit-desc {
      margin-bottom: 24px;
      height: auto;
    }
    .btn-to-learn-more {
      margin-top: 24px;
    }
  }
  .products-wrap {
    padding: 56px 0;
    .title {
      margin-bottom: 32px;
      font-size: 24px;
    }
    .product-carousel-box {
      display: none;
    }
    .product-item-wrap-m {
      display: flex;
      flex-wrap: wrap;
      width: 100%;
    }
    .product-item {
      margin-left: 0;
      flex: 1 1 auto;
    }
    .product-img {
      height: 72px;
      width: 72px;
      margin: 0 auto;
      background-size: 720px;
      background-position-y: -640px;
      background-position-x: -14px;
    }
    .product-name {
      font-size: 16px;
      margin-bottom: 32px;
    }
  }
  .contact-wrap {
    padding: 56px 20px;
    height: 274px;
    .title {
      margin-bottom: 12px;
      font-size: 24px;
    }
    .desc {
      line-height: 26px;
      font-size: 16px
    }
  }
}
@media screen and (min-width: 330px) and (max-width: 374px){
  .character-section {
    height: 1190px;
  }
}
@media screen and (min-width: 375px) and (max-width: 390px){
  .character-section {
    height: 1170px;
  }
}
@media screen and (max-width: 330px){
  .character-section {
    height: 1238px;
  }
  .kit-name {
    padding: 0 10px;
  }
}
</style>

