<template>
  <el-dialog
    title="使用到应用中"
    :visible.sync="dialog.show"
    width="640px"
  >
    <el-table
      ref="table"
      class="mgb40"
      :data="apps"
      height="300"
      style="width: 100%"
      v-loading="loading"
      @row-click="handleSelect">
      <el-table-column
        prop="appName"
        label="应用名称"
        width="130">
      </el-table-column>
      <el-table-column
        prop="appid"
        label="APPID"
        width="160">
      </el-table-column>
      <el-table-column
        prop="sceneName"
        label="情景模式"
        width="130">
      </el-table-column>
      <el-table-column width="100">
        <template slot-scope="scope">
          <el-checkbox class="no_radio_label" v-model="scope.row.selected" :disabled="scope.row.isUsed"></el-checkbox>
        </template>
      </el-table-column>
    </el-table>
    <!-- <el-checkbox v-model="checked" class="mgb16"><span>我已阅读并同意<a>《科大讯飞用户协议》</a></span></el-checkbox> -->
    <span slot="footer" class="dialog-footer">
      <el-button class="dialog-btn"
        type="primary"
        style="min-width: 100%;"
        @click="save"
        :disabled="btnDisabled"
      >
        完成
      </el-button>
    </span>
  </el-dialog>
</template>

<script>

export default {
  props: {
    dialog: {
      type: Object,
      default: {}
    }
  },
  data () {
    return {
      loading: true,
      apps: [],
      selectedApp: {}
    }
  },
  computed: {
    btnDisabled () {
      return !this.selectedApp.appid
    }
  },
  watch: {
    'dialog.show': function(val, oldVal) {
      if (val) {
        this.apps = []
        this.selectedApp = {}
        this.getApps()
      } else {

      }
    }
  },
  mounted() {

  },
  methods: {
    getApps () {
      let self = this
      this.loading = true
      this.$utils.httpGet(this.$config.api.AIUI_STORE_SKILL_APP_AUTH_LIST, {
        businessId: this.dialog.data.skillId
      }, {
        success: (res) => {
          self.apps = Array.prototype.map.call(res.data, function (item, index) {
            item.selected = item.isUsed
            return item
          })
          self.loading = false
        },
        error: (err) => {

        }
      })
    },
    handleSelect (row, event, column) {
      let self = this
      // let apps = Array.prototype.map.call(this.apps, function (item, index) {
      //   if (item.id === row.id && item.sceneId === row.sceneId) {
      //     item.selected = true
      //     self.selectedApp = item
      //   } else {
      //     item.selected = false
      //   }
      //   return item
      // })
      // this.apps = apps
      if(!row.isUsed) {
        row.selected = false
        self.selectedApp = row
      }
    },
    save () {
      let self = this
      let configs = [this.dialog.data]
      let sceneList = []
      self.apps.forEach( item => {
        if(item.selected && !item.isUsed) {
          sceneList.push({
            appid: item.appid,
            sceneId: item.sceneId
          })
        }
      })

      this.$utils.httpPost(this.$config.api.AIUI_STORE_APP_SKILL_CONFIG, {
        configs: JSON.stringify(configs),
        sceneList: JSON.stringify(sceneList),
        filter: 'appid'
      }, {
        success: (res) => {
          self.$message.success('保存成功')
          self.dialog.show = false
        },
        error: (err) => {

        }
      })
    }
  },
  components: {

  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="scss" scoped>

</style>
