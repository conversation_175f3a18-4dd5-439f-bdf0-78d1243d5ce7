<template>
  <div 
    class="qa-popover el-popover el-popper el-popover--plain"
    v-if="variablePopover.show"
    v-clickoutside="closePopover"
    :style="popperStyle"
    x-placement="bottom">
    <div class="">
      <div class="popover-item" v-for="item in list" :key="item"
        @click="selectItem(item)">
        {{item}}
      </div>
    </div>
    <div x-arrow="" class="popper__arrow" style="left: 13.5px;"></div>
  </div>
</template>
<script>
export default {
  name: 'qa-popover',
  props: {
    listType: String,
    variablePopover: {
      type: Object,
      default: () => ({ 
        show: false,
        listType: ''
      })
    }
  },
  data() {
    return {
      rect: {
        top: 0,
        left: 0,
        width: 0
      }
    }
  },
  computed: {
    popperStyle() {
      if (this.rect) {
        let bottom = this.docHeight - this.rect.top
        if (this.rect.bottom + 285 > this.docHeight) {
          return {
            bottom: `${bottom + 10}px`,
            left: `${this.rect.left - 20}px`
          }
        } else {
          return {
            top: `${this.rect.top + 20}px`,
            left: `${this.rect.left - 20}px`
          }
        }
      } else {
        return {
          display: `none`
        }
      }
    },
    list(){
      if(this.listType == 'answer') {
        return ['问法关键字', '关系关键字', '回复语']
      } else {
        return ['问法关键字', '关系关键字']
      }
    }
  },
  watch: {
    'variablePopover.rect': function() {
      this.rect = JSON.parse(JSON.stringify(this.variablePopover.rect))
    }
  },
  methods: {
    closePopover(){
      this.variablePopover.show = false
    },
    selectItem(val) {
      this.$emit('selectItem', val)
      this.closePopover()
    }
  }
}
</script>
<style lang="scss" scoped>
.qa-popover {
  position: fixed;
  transform-origin: center top 0px;
  z-index: 2000;
  padding: 8px 0;
}

.popover-item {
  height: 30px;
  line-height: 30px;
  padding: 0 16px;
  cursor: pointer;
  &:hover, &:first-child{
    background: #e3f0fc;
  }
}

</style>


