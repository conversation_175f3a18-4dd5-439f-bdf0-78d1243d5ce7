<template>
  <div class="main-content">
    <img
      class="bg-img bg-img-left"
      src="../../../assets/images/app/myapp-left-img.png"
      alt=""
    />
    <img
      class="bg-img bg-img-right"
      src="../../../assets/images/app/myapp-right-img.png"
      alt=""
    />
    <section class="main-content-banner">
      <div class="banner-text">
        <h2>AIUI 应用接入</h2>
        <p>开放识别、语义、合成、翻译全链路语音能力</p>
        <a class="banner-text-button" @click="goApp">进入应用</a>
        <!-- <a class="banner-text-button" href="/buy">服务量购买</a> -->
      </div>
    </section>
    <section class="section section-5">
      <div class="section-title">AIUI 服务政策</div>
      <div class="section-item">
        <el-tabs value="serviceSummarize" class="top-tabs">
          <el-tab-pane label="服务概述" name="serviceSummarize">
            <div class="subhead-title">AIUI 可授权的能力</div>
            <ul class="summarize-power-list">
              <li v-for="(item, index) in serviceSummarizeData" :key="index">
                <i :class="`AIUI-myapp-iconfont ${item.icon}`"></i>
                <span>{{ item.title }}</span>
              </li>
            </ul>
            <div class="business-tips">
              <p>AIUI提供按台授权、按交互量授权两种不同的服务方式</p>
              <p>
                应用创建后，默认有<b>20台</b>测试设备，赠<b>10000次</b>测试交互
              </p>
              <p>更多授权及私有化定制需线下联系商务洽谈</p>
            </div>
            <div class="business-mail">
              如需联系商务，请邮件
              <span
                class="AIUI-myapp-iconfont ai-myapp-email icon-email"
              ></span>
              <a href="mailto:<EMAIL>"
                ><EMAIL></a
              >
            </div>
          </el-tab-pane>

          <el-tab-pane label="装机量" name="installNum">
            <div class="subhead-title">AIUI 按装机量授权</div>
            <div class="business-tips">
              <p>所有智能硬件设备，AIUI均采用按装机量授权合作</p>
              <p>
                装机量授权根据授权能力（如降噪唤醒、离线在线交互等）、产品形态和采购数量评估单台价格
              </p>
              <p>授权后产品全生命周期里不限交互次数</p>
            </div>
            <div class="business-mail">
              如需联系商务，请邮件
              <span
                class="AIUI-myapp-iconfont ai-myapp-email icon-email"
              ></span>
              <a href="mailto:<EMAIL>"
                ><EMAIL></a
              >
            </div>
          </el-tab-pane>

          <el-tab-pane label="交互量" name="interaction">
            <div class="subhead-title">AIUI 交互授权</div>
            <div class="business-tips">
              <p>按交互授权适用于电话外呼、客服等服务-服务的接入的方式</p>
              <p>AIUI交互包含"识别-语义-合成"全链路能力，也支持单能力调用</p>
              <p>每个新应用均有<b>10000次</b>的交互量可测试试用</p>
            </div>
            <div class="business-mail">
              如需联系商务，请邮件
              <span
                class="AIUI-myapp-iconfont ai-myapp-email icon-email"
              ></span>
              <a href="mailto:<EMAIL>"
                ><EMAIL></a
              >
            </div>
          </el-tab-pane>

          <el-tab-pane label="信源服务" name="information">
            <div class="subhead-title">AIUI 信源授权</div>
            <ul class="summarize-power-list">
              <li v-for="(item, index) in informationData" :key="index">
                <i :class="`AIUI-myapp-iconfont ${item.icon}`"> </i>
                <span>{{ item.title }}</span>
              </li>
            </ul>
            <div class="business-tips">
              <p>提供正版TME音乐授权，授权后产品全生命周期一直可用。</p>
              <p>音乐信源按台付费授权，其余信源免费提供。</p>
            </div>
            <div class="business-mail">
              如需联系商务，请邮件
              <span
                class="AIUI-myapp-iconfont ai-myapp-email icon-email"
              ></span>
              <a href="mailto:<EMAIL>"
                ><EMAIL></a
              >
            </div>
          </el-tab-pane>

          <el-tab-pane label="方言识别" name="localism">
            <div class="subhead-title">AIUI 方言授权</div>
            <p class="top-tips">四川话与粤语识别免费提供</p>
            <p class="top-tips" style="margin-bottom: 50px">
              其他方言包年或按台授权，授权后可在我的应用-应用配置页面配置使用
            </p>
            <LocalismBuy />
          </el-tab-pane>

          <el-tab-pane label="发音人" name="anchor">
            <div class="subhead-title">AIUI 发音人</div>
            <p class="top-tips">AIUI提供了3位免费的发音人</p>
            <p class="top-tips" style="margin-bottom: 50px">
              其他发音人购买后可以在我的应用内配置使用
            </p>
            <AnchorBuy />
          </el-tab-pane>
        </el-tabs>
      </div>
    </section>
    <!-- <section class="section section-2">
      <div class="section-title">AIUI 适用说明</div>
      <div class="section-item">
        <ul>
          <li
            v-for="(item, index) in explain"
            :key="index"
            class="explain"
            :style="{ background: `url(${item.url})` }"
          >
            <p class="explain-title">{{ item.title }}</p>
            <p class="explain-text">{{ item.text }}</p>
          </li>
        </ul>
      </div>
    </section>
    <section class="section section-3">
      <div class="section-title">多种接入方式</div>
      <div class="section-item">
        <ul>
          <li v-for="(item, index) in mode" :key="index" class="mode">
            <img :src="item.url" />
            <p class="mode-title">{{ item.title }}</p>
            <p class="mode-text">{{ item.text }}</p>
          </li>
        </ul>
      </div>
    </section>
    <section class="section section-4">
      <a class="banner-text-button btn-apps-add" href="/app/add"
        >创建我的应用</a
      >
    </section> -->
  </div>
</template>

<script>
import aiuiHeader from '../../../components/aiuiHeader.vue'
import LocalismBuy from '../../../components/localismBuy.vue'
import AnchorBuy from '../../../components/anchorBuy.vue'

export default {
  layout: 'aiuiHome',
  components: {
    LocalismBuy,
    AnchorBuy,
  },
  data() {
    return {
      explain: [
        {
          url: require('../../../assets/images/aiui/app-and-partner/app-section-1.jpg'),
          title: '中小应用',
          text: '自定义技能 + 免费开放200+官方技能',
        },
        {
          url: require('../../../assets/images/aiui/app-and-partner/app-section-2.jpg'),
          title: '企业级客户',
          text: '开放语义、支持云对云接入',
        },
        {
          url: require('../../../assets/images/aiui/app-and-partner/app-section-3.jpg'),
          title: '方案商',
          text: '支持复杂业务自由定制',
        },
      ],
      mode: [
        {
          url: require('../../../assets/images/aiui/app-and-partner/app-section-4.jpg'),
          title: '智能硬件',
          text: '提供基于Android、Linux系统的智能硬件解决方案，可应用于机器人、智能家居、智能音箱、车载、电视手持设备等多种领域，具备远场降噪、全双工交互、声源定位、上下文对话能力。',
        },
        {
          url: require('../../../assets/images/aiui/app-and-partner/app-section-5.jpg'),
          title: 'SDK',
          text: '提供基于Android、iOS、Windows、Linux平台的移动端解决方案，可应用于语音助手、智能客服、地图导航，信息查询等领域。具备语音唤醒、语音识别、语音合成、语义理解等多种能力。',
        },
        {
          url: require('../../../assets/images/aiui/app-and-partner/app-section-6.jpg'),
          title: 'WebSocket API',
          text: '提供基于WebSocket协议接入方案，可运用于多种操作系统，提供完备的智能语音、语义理解、闲聊问答等解决方案。',
        },
        // {
        //   url: require('../../../assets/images/aiui/app-and-partner/app-section-7.jpg'),
        //   title: '微信',
        //   text: '提供基于微信公众号的解决方案，可应用于微信语音、文本、客服、翻译、聊天助手、服务导航、智能问答等应用场景。'
        // }
      ],
      // 服务概述 可授权能力列表
      serviceSummarizeData: [
        {
          title: '硬件模组',
          icon: 'ai-myapp-yingjianmozu',
        },
        {
          title: '降噪唤醒算法',
          icon: 'ai-myapp-jiangzaohuanxing',
        },
        {
          title: '离在线语音交互',
          icon: 'ai-myapp-lizaixianyuyin',
        },
        {
          title: '内容信源',
          icon: 'ai-myapp-neirongxinyuan',
        },
        {
          title: '方言及精品发音人',
          icon: 'ai-myapp-fangyanfaying',
        },
        {
          title: '私有化部署',
          icon: 'ai-myapp-siyuobushu',
        },
      ],

      // 信源授权 列表
      informationData: [
        {
          title: '音乐/儿歌',
          icon: 'ai-myapp-yinyueerge',
        },
        {
          title: '影视/电视节目',
          icon: 'ai-myapp-yinshidianshi',
        },
        {
          title: '有声书',
          icon: 'ai-myapp-youshengshu',
        },
        {
          title: '广播电台',
          icon: 'ai-myapp-guangbodiantai',
        },
        {
          title: '天气',
          icon: 'ai-myapp-tianqi',
        },
        {
          title: '百科知识',
          icon: 'ai-myapp-kaikezhishi',
        },
      ],
    }
  },
  methods: {
    goApp() {
      this.$router.push('/app')
    },
  },
}
</script>

<style lang="scss" scoped>
.main-content {
  min-width: 1200px;
  overflow: auto;
  .bg-img {
    position: absolute;
    width: 171px;
    height: 451px;
  }
  .bg-img-left {
    left: 0;
    top: 480px;
  }
  .bg-img-right {
    top: 880px;
    right: 0;
    width: 138px;
    height: 528px;
  }
  &-banner {
    height: 460px;
    overflow: hidden;
    width: 100%;
    background: url('../../../assets/images/aiui/app-and-partner/app-banner.jpg')
      center center no-repeat;
    // background: url('../../assets/images/aiui/app-and-partner/app-banner.jpg') center center no-repeat;
    background-size: cover;
    .banner-text {
      display: flex;
      flex-direction: column;
      justify-content: center;
      padding-top: 55px;
      max-width: 1200px;
      color: #fff;
      height: 100%;
      margin: auto;
      h2 {
        color: #fff;
        margin-bottom: 12px;
        font-size: 46px;
        font-weight: 500;
      }
      p {
        font-size: 20px;
        margin-bottom: 50px;
      }
    }
  }
  .section {
    width: 1200px;
    overflow: hidden;
    margin: 0 auto;
    padding: 50px 0 90px;
    &-title {
      font-size: 36px;
      margin: 0 0 35px;
      text-align: center;
    }
  }

  .btn-buy-service {
    display: inline-block;
  }
  .banner-text-button {
    display: inline-block;
    font-size: 18px;
    text-align: center;
    font-weight: 500;
    width: 240px;
    height: 52px;
    line-height: 52px;
    letter-spacing: 1px;
    border: 1px solid #fff;
    border-radius: 1px;
    color: #fff;
    cursor: pointer;
    transition: 0.6s;
    &:hover {
      color: #002985;
      background: #fff;
      transition: 0.3s;
    }
  }
  .section-2 {
    padding-bottom: 130px;
    ul {
      display: flex;
      justify-content: space-around;
      .explain {
        width: 378px;
        height: 170px;
        background-size: cover;
        background-repeat: no-repeat;
        padding-top: 50px;
        p {
          text-align: center;
          color: #fff;
          font-size: 18px;
        }
        .explain-title {
          font-size: 24px;
          margin-bottom: 8px;
        }
      }
    }
  }
  .section-3 {
    padding-bottom: 110px;
    min-width: 100%;
    background: #f2f5f7;
    ul {
      width: 1200px;
      margin: 0 auto;
      display: flex;
      justify-content: space-around;
      li {
        width: 280px;
        height: 340px;
        text-align: center;
        border-radius: 10px;
        box-shadow: 0 0 8px #e5e5e5;
        padding: 20px 18px;
        background: #fff;
        .mode-text {
          text-align: justify;
          color: #a0a0a0;
        }
        .mode-title {
          font-size: 28px;
          margin: 15px 0 20px;
        }
      }
    }
  }
  .section-4 {
    padding: 100px 0;
  }

  .section-5 {
    position: relative;
    .section-title {
      margin-bottom: 60px;
      font-weight: bold;
    }
    .subhead-title {
      margin: 55px 0 50px;
      color: #666666;
      font-size: 30px;
      text-align: center;
    }
    .summarize-power-list {
      display: flex;
      justify-content: center;
      li {
        display: flex;
        flex-direction: column;
        align-items: center;
        & + li {
          margin-left: 90px;
        }
        i {
          display: flex;
          justify-content: center;
          align-items: center;
          margin-bottom: 35px;
          width: 100px;
          height: 100px;
          background: #ffffff;
          box-shadow: 0px 5px 20px 0px rgba(165, 165, 165, 0.2);
          border-radius: 50%;
          font-size: 40px;
        }
        span {
          font-size: 18px;
          font-weight: bold;
          color: #333333;
        }
      }
    }

    .top-tips {
      text-align: center;
      font-size: 18px;
      color: #999999;
    }

    .business-tips {
      margin: 70px 0 60px;
      padding: 40px 0;
      width: 1200px;
      font-size: 18px;
      background-color: #fcfcfc;
      border: 1px dashed #cbd5dc;
      border-radius: 5px;
      p {
        margin: 0;
        text-align: center;
        & + p {
          margin-top: 13px;
        }
        b {
          font-weight: normal;
          color: #1f90fe;
        }
      }
    }

    .business-mail {
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 24px;
      font-weight: bold;
      color: #333333;
      a {
        font-weight: bold;
        color: #1f90fe;
      }
    }
  }
  .btn-apps-add {
    display: block;
    margin: auto;
    width: 195px;
    color: #fff;
    background: #1784e9;
    &:hover {
      color: #fff;
      background: #1784e9;
    }
  }
}
.icon-email {
  margin: 1px 3px 0;
  font-size: 32px;
  color: #1f90fe;
}
@media screen and (max-width: 1601px) {
  .main-content {
    &-banner {
      .banner-text {
        padding-top: 35px;
      }
    }
  }
}
@media screen and (max-width: 1440px) {
  .bg-img {
    display: none;
  }
}
</style>
<style lang="scss">
.main-content {
  .section-5 {
    .section-item {
      display: flex;
      justify-content: center;
      .top-tabs {
        & > .el-tabs__header {
          .el-tabs__item {
            margin-bottom: 30px;
            width: 200px;
            text-align: center;
            font-size: 20px;
          }
          .el-tab-pane {
            padding-top: 50px;
          }
        }
        .el-tabs__active-bar {
          height: 4px;
          background: #1f90fe;
          border-radius: 2px;
        }
        .el-tabs__nav-wrap {
          &::after {
            height: 1.5px;
          }
        }
      }
    }
  }
}
</style>
