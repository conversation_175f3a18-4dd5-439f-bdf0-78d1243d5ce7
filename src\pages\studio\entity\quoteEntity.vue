<template>
<div class="select-entity-popover">
  <div class="skill-select-popover__head">
    <div class="skill-select-popover-search">
      <el-input
        class="search-area"
        placeholder="搜索实体"
        v-model="searchName"
        autofocus
        @keyup.enter.native="selectItem()"
        @keyup.up.native="preItem"
        @keyup.down.native="nextItem">
      </el-input>
    </div>
  </div>
  <div class="skill-select-popover__body">
    <el-radio-group class="quote-entity-type" v-model="entityType" @change="initData">
      <el-radio :label="0">全部</el-radio>
      <el-radio :label="1">官方</el-radio>
      <el-radio :label="2">自定义</el-radio>
    </el-radio-group>
    <os-divider/>
    <div class="skill-select-popover-list">
      <div
        :class="['skill-select-popover-item', {'disabled': item.isUsed}, {'hover': selectIndex === index}]"
        v-for="(item, index) in skillData.list"
        @click="selectItem(item)">
        <span :title="item.zhName">{{item.zhName}}</span>
        <span class="fr" :title="item.name">{{item.name}}</span>
      </div>
      <div class="el-table__empty-block" v-if="!skillData.list.length">
        <span class="el-table__empty-text">暂无数据</span>
      </div>
    </div>
  </div>
</div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  props: ['count', 'entityId'],
  data () {
    return {
      searchName: '',
      entityType: 0,
      skillData: {
        loading: true,
        list: []
      },
      selectIndex: -1
    }
  },
  computed: {
  },
  watch: {
    'searchName': function (val, oldVal) {
      this.initData()
    },
    'count': function (val, oldVal) {
      this.searchName = ''
      this.initData()
    }
  },
  mounted() {
    this.initData()
  },
  methods: {
    initData () {
      let self = this
      this.skillData.loading = true
      this.$utils.httpGet(this.$config.api.STUDIO_BASE_ENTITY_LIST, {
        entityId: this.entityId,
        search: this.searchName,
        type: this.entityType
      }, {
        success: (res) => {
          self.skillData.list = res.data
          self.skillData.loading = false
          self.selectIndex = -1
        },
        error: (err) => {

        }
      })
    },
    preItem() {
      for(let i = this.selectIndex; ;) {
        i--
        if(i < 0) {
          i = this.skillData.list.length - 1
        }
        if(this.skillData.list[i].isUsed === 0) {
          this.selectIndex = i
          break
        }
      }
    },
    nextItem() {
      for(let i = this.selectIndex; ;) {
        i++
        if(i >= this.skillData.list.length) {
          i = 0
        }
        if(this.skillData.list[i].isUsed === 0) {
          this.selectIndex = i
          break
        }
      }
    },
    selectItem(item) {
      // input 回车事件会触发select。这里加个判断
      if(!item) {
        // 回车事件
        if(this.selectIndex !== -1) {
          this.$emit('select', this.skillData.list[this.selectIndex])
        }
      } else {
        // 点击事件
        if(!item.isUsed) {
          this.$emit('select', item)
        }
      }
    },
  },
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="scss" scoped>

.select-entity-popover {
  position: absolute;
  top: 0;
  left: 0;
  background: #fff;
  width: 360px;
  border-radius: 8px;
  border: 1px solid #ebeef5;
}

.skill-select-popover__head {
  display: flex;
  align-items: center;
}
.skill-select-popover__body {
  // margin-top: 10px;
}
.skill-select-popover-search {
  width: 100%;
  border-bottom: 1px solid $grey2;
}
.skill-select-popover-search input {
  border: 0;
  border-radius: 8px;
}
.skill-select-popover-list {
  width: 100%;
  height: 236px;
  overflow-y: scroll;
}
.skill-select-popover-item {
  height: 36px;
  line-height: 36px;
  padding: 0 16px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: pointer;
  &.disabled {
    color: $grey4;
    cursor: not-allowed;
    &:hover {
     background: #fff;
    }
   }
  span {
    display: inline-block;
    max-width: 150px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  span:last-child {
    color: $grey4;
  }
  &:hover, &.hover {
    background: $primary-light-12;
  }
}
.skill-select-popover-add {
  height: 48px;
  line-height: 48px;
  padding: 0 16px;
  display: flex;
  i {
    color: $grey4;
  }
  a {
    font-weight: 600;
    padding-left: 6px;
  }
}
.quote-entity-type {
  padding: 9px 16px;
}
</style>
