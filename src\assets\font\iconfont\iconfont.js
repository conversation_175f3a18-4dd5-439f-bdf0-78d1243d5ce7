;(window._iconfont_svg_string_4947598 =
  '<svg><symbol id="icon-notice" viewBox="0 0 1024 1024"><path d="M972.8 85.333333c12.8-2.133333 25.6 0 36.266667 8.533334 8.533333 8.533333 14.933333 21.333333 14.933333 34.133333v682.666667c0 12.8-6.4 25.6-14.933333 34.133333-8.533333 4.266667-17.066667 8.533333-27.733334 8.533333h-8.533333l-288-64-25.6 117.333334c-4.266667 19.2-21.333333 34.133333-42.666667 34.133333h-8.533333L341.333333 881.066667c-10.666667-2.133333-21.333333-8.533333-27.733333-19.2-6.4-8.533333-8.533333-21.333333-6.4-32l25.6-117.333334L209.066667 682.666667H42.666667c-23.466667 0-42.666667-19.2-42.666667-42.666667V298.666667c0-23.466667 19.2-42.666667 42.666667-42.666667h166.4l763.733333-170.666667zM938.666667 757.333333v-576l-682.666667 151.466667v273.066667l136.533333 29.866666L661.333333 695.466667l277.333334 61.866666z m-356.266667 87.466667l17.066667-74.666667-183.466667-40.533333-17.066667 74.666667 183.466667 40.533333zM85.333333 597.333333h85.333334V341.333333H85.333333v256z" fill="#009bff" ></path></symbol></svg>'),
  ((n) => {
    var t = (e = (e = document.getElementsByTagName('script'))[
        e.length - 1
      ]).getAttribute('data-injectcss'),
      e = e.getAttribute('data-disable-injectsvg')
    if (!e) {
      var o,
        i,
        c,
        d,
        l,
        s = function (t, e) {
          e.parentNode.insertBefore(t, e)
        }
      if (t && !n.__iconfont__svg__cssinject__) {
        n.__iconfont__svg__cssinject__ = !0
        try {
          document.write(
            '<style>.svgfont {display: inline-block;width: 1em;height: 1em;fill: currentColor;vertical-align: -0.1em;font-size:16px;}</style>'
          )
        } catch (t) {
          console && console.log(t)
        }
      }
      ;(o = function () {
        var t,
          e = document.createElement('div')
        ;(e.innerHTML = n._iconfont_svg_string_4947598),
          (e = e.getElementsByTagName('svg')[0]) &&
            (e.setAttribute('aria-hidden', 'true'),
            (e.style.position = 'absolute'),
            (e.style.width = 0),
            (e.style.height = 0),
            (e.style.overflow = 'hidden'),
            (e = e),
            (t = document.body).firstChild
              ? s(e, t.firstChild)
              : t.appendChild(e))
      }),
        document.addEventListener
          ? ~['complete', 'loaded', 'interactive'].indexOf(document.readyState)
            ? setTimeout(o, 0)
            : ((i = function () {
                document.removeEventListener('DOMContentLoaded', i, !1), o()
              }),
              document.addEventListener('DOMContentLoaded', i, !1))
          : document.attachEvent &&
            ((c = o),
            (d = n.document),
            (l = !1),
            r(),
            (d.onreadystatechange = function () {
              'complete' == d.readyState && ((d.onreadystatechange = null), a())
            }))
    }
    function a() {
      l || ((l = !0), c())
    }
    function r() {
      try {
        d.documentElement.doScroll('left')
      } catch (t) {
        return void setTimeout(r, 50)
      }
      a()
    }
  })(window)
