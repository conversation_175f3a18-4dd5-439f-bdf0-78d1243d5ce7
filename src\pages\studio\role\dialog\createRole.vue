<template>
  <el-dialog :title="`创建角色${dialog.type==='copy'?'副本':''}`" :visible.sync="dialog.show" width="480px">
    <p class="note-msg" v-if="dialog.type==='copy'">
      副本的名称超过字数限制，请重新命名。
    </p>
    <el-form :model="form" :rules="rules" ref="qaForm" label-position="top">
      <el-form-item :label="`角色名称`" prop="name">
        <el-input
          v-model.trim="form.name"
          :placeholder="`支持中文/英文/数字/下划线格式，不超过32个字符`"
          ref="nameInput"
          @keyup.enter.native="save"
        />
      </el-form-item>
      <el-form-item :label="`角色模版`" prop="fromRoleId" v-if="dialog.type!='copy'">
        <el-select
          v-model="form.fromRoleId"
          placeholder="请选择导入角色模版"
          style="width: 100%"
          clearable
        >
          <el-option
            v-for="item in roleTmpList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button
        class="dialog-btn"
        type="primary"
        style="min-width: 104px"
        @click="save"
        :loading="saving"
      >
        {{ saving ? '创建中...' : '创建' }}
      </el-button>
    </span>
  </el-dialog>
</template>

<script>
import IconAdd from '@A/images/plugin/add.png'
import { mapGetters } from 'vuex'
export default {
  props: {
    dialog: {
      type: Object,
      default: {},
    },
  },
  data() {
    return {
      IconAdd,
      saving: false,
      form: {
        name: '',
        fromRoleId: '',
      },
      roleTmpList: [], // 角色模版列表
      rules: {
        name: [
          this.$rules.required('名称不能为空', 'none'),
          this.$rules.lengthLimit(1, 32, '名称长度不能超过32个字符'),
          this.$rules.baseRegLimit(),
        ],
      },
    }
  },
  watch: {
    'dialog.show': function (val, oldVal) {
      let self = this
      if (val) {
        // 模态框弹出
        this.getRolesAll()
        this.form = {
          name: '',
          fromRoleId: '',
        }
        this.$nextTick(function () {
          self.$refs.nameInput && self.$refs.nameInput.focus()
        })
      } else {
        this.$refs.qaForm && this.$refs.qaForm.resetFields()
      }
    },
  },
  methods: {
    getRolesAll() {
      this.$utils.httpGet(
        this.$config.api.AIUI_ROLE_TEMPLATE_LIST,
        {},
        {
          success: (res) => {
            if (res.code == 0) {
              this.roleTmpList = res.data.list || []
            }
          },
          error: (err) => {},
        }
      )
    },
    clearValidateForm(e) {
      this.$refs.qaForm && this.$refs.qaForm.clearValidate()
    },
    save() {
      let self = this
      if (this.saving) {
        return
      }
      this.$refs.qaForm.validate((valid) => {
        if (valid) {
          this.saving = true
          let data = {
            name: this.form.name,
            fromRoleId: this.form.fromRoleId,
          }
          if(this.dialog.type==='copy'){
            data['fromRoleId'] = this.dialog.fromId
          }
          let api = this.$config.api.AIUI_ROLE_CREATE
          this.$utils.httpPost(api, JSON.stringify(data), {
            config: {
              headers: {
                'Content-Type': 'application/json;charset=UTF-8',
              },
            },
            success: (res) => {
              self.saving = false
              if (res.code == 0) {
                self.$message.success('创建成功')
                this.$router.push({
                  name: 'role',
                  params: {
                    roleId: res.data.id,
                  },
                })
              }
            },
            error: (err) => {
              self.saving = false
              console.log(err)
            },
          })
        }
      })
    },
  },
  computed: {},
  components: {},
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="scss" scoped>
.note-msg {
  width: 416px;
  height: 32px;
  padding-left: 20px;
  margin-bottom: 20px;
  background: #fff8ed;
  border-radius: 2px;
  font-size: 14px;
  font-weight: 400;
  color: rgba(38, 38, 38, 1);
  line-height: 32px;
}
</style>
