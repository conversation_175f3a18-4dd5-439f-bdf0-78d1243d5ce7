import Vue from 'vue'
import feedBackHover from './feedBackHover'

import Config from '../config'
import Utils from '../utils'
Vue.use(Config)
Vue.use(Utils)

const vm = new Vue({
  beforeCreate() {
    this.$utils.init(this)
  },
  render(h) {
    return h(feedBackHover, {
      props: {
        rightTestOpen: false, // 可以控制feedBackHover是否显示
      },
    })
  },
}).$mount()

const show = () => {
  document.body.appendChild(vm.$el)
}

const hide = () => {
  if (vm.$el && vm.$el.parentNode) {
    vm.$el.parentNode.removeChild(vm.$el)
  }
}

export { show, hide }
