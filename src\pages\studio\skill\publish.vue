<template>
  <os-page :options="pageOptions">
    <studio-skill-header-right slot="btn" />
    <div class="skill-publish-page">
      <!-- 原开放技能&未认证 -->
      <div
        class="mgt48 mgb56 certificate-wrap"
        v-if="!skill.privateSkill && !isCertificated"
      >
        <p class="title">
          <i class="ic-r-exclamation"></i
          >{{ !subAccount ? '你尚未实名认证！' : '主帐号未实名认证！' }}
        </p>
        <p style="margin-bottom: 4px; padding-left: 36px">
          申请开放技能发布前，需要完成实名认证。
        </p>
        <a
          v-if="!subAccount"
          style="padding-left: 36px"
          :href="`${$config.xfyunConsole}user/realnameauth`"
          target="_blank"
          >立即实名认证</a
        >
      </div>
      <template v-else>
        <os-collapse :default="true" size="large" title="基本信息">
          <el-form
            ref="skillForm"
            :rules="rules"
            :model="skill"
            label-width="118px"
            label-position="left"
            :disabled="skill.isCheck || !subAccountEditable"
          >
            <el-form-item label="技能类型">
              <span v-if="skill.privateSkill">私有技能</span>
              <span v-else>开放技能</span>
            </el-form-item>
            <!-- <el-form-item label="二级分类">
              <span v-if="skill.secondType === 2">APP控制技能</span>
              <span v-else>自定义语音技能</span>
            </el-form-item> -->
            <!-- <el-form-item label="服务平台">
              <span v-if="skill.type === '2'">AIUI</span>
              <span v-else>iFLYOS</span>
            </el-form-item> -->
            <el-form-item label="技能协议" class="item-skill-privacy">
              <span class="skill-privacy">v{{ skill.protocolVersion }}</span>
              <a style="font-size: 14px" :href="DocsInUrl" target="_blank"
                >查看开发文档</a
              >
            </el-form-item>
            <!-- 3 定制技能 -->
            <el-form-item v-if="skill.type == '3'" label="技能名称">
              <span>{{ skill.zhName }}</span>
            </el-form-item>
            <el-form-item
              v-else
              :label="theme + '名称'"
              prop="zhName"
              :placeholder="'请输入' + theme + '名称'"
            >
              <el-input v-model="skill.zhName"></el-input>
            </el-form-item>
            <el-form-item v-if="!skill.privateSkill">
              <span style="font-weight: 600" slot="label"
                >{{ theme + '别名' }}
                <el-tooltip
                  effect="dark"
                  :content="
                    '用户可通过' +
                    theme +
                    '名称和' +
                    theme +
                    '别名来访问你的技能'
                  "
                >
                  <i class="el-icon-question" />
                </el-tooltip>
              </span>
              <skill-alias
                :zhName="skill.zhName"
                :initAlias="initAlias"
                :onCheck="skill.isCheck"
                :subAccountEditable="subAccountEditable"
                @setAlias="setAlias"
              ></skill-alias>
            </el-form-item>
            <el-form-item label="英文标识">
              <span>{{ skill.identify }}</span>
            </el-form-item>
            <!--<el-form-item v-if="!skill.privateSkill"
              label="调用名称" prop="callName" placeholder="请输入调用名称">
              <el-input v-model="skill.callName"></el-input>
              <span>{{skill.callName}}</span>
            </el-form-item>-->
            <template v-if="skill.privateSkill">
              <el-form-item v-if="skill.type !== '3'" label="技能分类">
                <el-select v-model="skill.businesstype" placeholder="请选择">
                  <el-option
                    v-for="(val, key, index) in skillTypeList"
                    :key="index"
                    :label="val"
                    :value="key"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </template>
            <el-form-item
              class="is-required"
              v-else
              prop="businesstype"
              label="技能分类"
            >
              <el-select
                v-model="skill.businesstype"
                placeholder="请选择"
                @change="$refs.skillForm.validate()"
              >
                <el-option
                  v-for="(val, key, index) in skillTypeList"
                  :key="index"
                  :label="val"
                  :value="key"
                >
                </el-option>
              </el-select>
            </el-form-item>

            <!-- 1226-关联包名 本次更新 -->
            <template v-if="skill.secondType == 2">
              <el-form-item
                class="skill-name-input"
                v-if="!skill.privateSkill"
                prop="appPackage"
              >
                <span style="font-weight: 600" slot="label"
                  >关联包名
                  <el-tooltip
                    effect="dark"
                    content="技能和APP一对一关联。请确保你开发技能的行为不会对APP产生侵权。"
                  >
                    <i class="el-icon-question" />
                  </el-tooltip>
                </span>
                <el-input
                  ref="nameInput"
                  v-model.trim="skill.appPackage"
                  placeholder="请输入技能关联应用的完整package_name"
                ></el-input>
              </el-form-item>
              <el-form-item
                class="skill-name-input"
                v-if="!skill.privateSkill"
                prop="appDownloadUrl"
              >
                <span style="font-weight: 600" slot="label"
                  >APP下载地址
                  <el-tooltip
                    effect="dark"
                    content="请确保该下载地址可以一直下载到最新的APP。"
                  >
                    <i class="el-icon-question" />
                  </el-tooltip>
                </span>
                <el-input
                  ref="nameInput"
                  v-model.trim="skill.appDownloadUrl"
                  placeholder="请输入APP的下载地址"
                ></el-input>
              </el-form-item>
            </template>

            <el-form-item
              class="is-required"
              v-if="!skill.privateSkill"
              :label="skill.secondType === 2 ? 'APP图标' : '技能图标'"
            >
              <upload-icon
                :skillId="skill.id"
                :disabled="!subAccountEditable"
                :image="image"
                @setIconInfo="setIconInfo"
              ></upload-icon>
            </el-form-item>
            <el-form-item class="is-required" v-if="!skill.privateSkill">
              <span style="font-weight: 600" slot="label"
                >示例说法
                <el-tooltip
                  effect="dark"
                  v-if="theme === 'APP'"
                  content="请告诉用户可以通过语言怎样和你的技能交互"
                >
                  <i class="el-icon-question" />
                </el-tooltip>
              </span>
              <span>回车添加示例说法：</span>
              <example-utterance
                key="exampleUtterance"
                ref="egUtterance"
                :list="initEgUtterances"
                :subAccountEditable="subAccountEditable"
                @setExampleUtterance="setExampleUtterance"
              ></example-utterance>
            </el-form-item>
            <el-form-item
              class="is-required"
              v-if="!skill.privateSkill"
              label="欢迎语示例说法"
            >
              <span>回车添加示例说法：</span>
              <!-- <example-utterance
                key="welcomeUtterance"
                style="margin-bottom: 8px"
                ref="egUtterance"
                :list="initWelcomePhrase"
                :subAccountEditable="subAccountEditable"
                @setWelcomePhrase="setWelcomePhrase"
              ></example-utterance> -->
              <welcome-words
                :list="initWelcomePhrase.data"
                :subAccountEditable="subAccountEditable && !skill.isCheck"
                @setWelcomePhrase="setWelcomePhrase"
              ></welcome-words>
              <p class="welcom-words-tip">
                温馨提示：该欢迎语与AIUI技能自定义回复语一起使用，使用技能后处理写技能回复，则在技能后处理里添加欢迎语。
              </p>
            </el-form-item>
            <el-form-item
              class="is-required"
              v-if="!skill.privateSkill"
              prop="briefIntroduction"
              label="技能简介"
            >
              <el-input
                type="textarea"
                resize="none"
                style="font-family: Arial"
                :rows="3"
                placeholder="请输入技能简介，最多1000个字符"
                v-model="skill.briefIntroduction"
              >
              </el-input>
            </el-form-item>
            <!-- 协议简介 -->
            <el-form-item label="协议简介" v-if="!skill.privateSkill">
              <ul class="param-container">
                <li>
                  <div>类型</div>
                  <div>字段</div>
                  <div>字段类型</div>
                  <div>字段说明</div>
                  <div>操作</div>
                </li>
                <li>
                  <el-form
                    ref="protocolForm"
                    :model="protocolToBeAdded"
                    label-width="118px"
                    label-position="left"
                    class="protocol-container"
                    :disabled="skill.isCheck || !subAccountEditable"
                  >
                    <div>
                      <el-form-item
                        prop="type"
                        :rules="[
                          {
                            required: true,
                            trigger: 'change',
                            message: '类型必选',
                          },
                        ]"
                      >
                        <el-select
                          placeholder="请选择类型"
                          v-model="protocolToBeAdded.type"
                          size="small"
                        >
                          <el-option
                            v-for="it in protocolOptions"
                            :key="it.value"
                            :label="it.label"
                            :value="it.value"
                          >
                          </el-option>
                        </el-select>
                      </el-form-item>
                    </div>

                    <div>
                      <el-form-item
                        prop="field"
                        :rules="[
                          {
                            required: true,
                            trigger: 'blur',
                            message: '字段必填',
                          },
                          { max: 64, message: '字段不超过64字符' },
                        ]"
                      >
                        <el-input
                          size="small"
                          placeholder="回车，最多64字符"
                          @keyup.enter.native.prevent.stop="
                            handleAddProtocol(protocolToBeAdded)
                          "
                          v-model="protocolToBeAdded.field"
                        ></el-input>
                      </el-form-item>
                    </div>
                    <div>
                      <el-form-item
                        prop="fieldType"
                        :rules="[
                          {
                            required: true,
                            trigger: 'blur',
                            message: '字段类型必填',
                          },
                          { max: 24, message: '字段类型不超过24字符' },
                        ]"
                      >
                        <el-input
                          size="small"
                          placeholder="回车，最多24字符"
                          v-model="protocolToBeAdded.fieldType"
                          @keyup.enter.native.prevent.stop="
                            handleAddProtocol(protocolToBeAdded)
                          "
                        ></el-input>
                      </el-form-item>
                    </div>
                    <div>
                      <el-form-item
                        prop="fieldName"
                        :rules="[
                          {
                            required: true,
                            trigger: 'blur',
                            message: '字段说明必填',
                          },
                          { max: 500, message: '字段说明不超过500字符' },
                        ]"
                      >
                        <el-input
                          size="small"
                          placeholder="回车，最多500字符"
                          v-model="protocolToBeAdded.fieldName"
                          @keyup.enter.native.prevent.stop="
                            handleAddProtocol(protocolToBeAdded)
                          "
                        ></el-input>
                      </el-form-item>
                    </div>
                    <div>
                      <el-button
                        size="small"
                        type="text"
                        @click="handleAddProtocol(protocolToBeAdded)"
                        >新增</el-button
                      >
                    </div>
                  </el-form>
                </li>
                <li
                  v-for="(item, index) in skill.protocols"
                  :key="index"
                  class="param-sub param-added"
                >
                  <div>
                    <el-form-item
                      :prop="'protocols.' + index + '.type'"
                      :rules="[
                        {
                          required: true,
                          trigger: 'change',
                          message: '类型必选',
                        },
                      ]"
                    >
                      <el-select
                        placeholder="请选择类型"
                        v-model="item.type"
                        size="small"
                        disabled
                      >
                        <el-option
                          v-for="it in protocolOptions"
                          :key="it.value"
                          :label="it.label"
                          :value="it.value"
                        >
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </div>

                  <div>
                    <el-form-item
                      :prop="'protocols.' + index + '.field'"
                      :rules="[
                        {
                          required: true,
                          trigger: 'blur',
                          message: '字段必填',
                        },
                        { max: 64, message: '字段不超过64字符' },
                      ]"
                    >
                      <el-input
                        size="small"
                        placeholder="不超过64字符"
                        v-model="item.field"
                      ></el-input>
                    </el-form-item>
                  </div>
                  <div>
                    <el-form-item
                      :prop="'protocols.' + index + '.fieldType'"
                      :rules="[
                        {
                          required: true,
                          trigger: 'blur',
                          message: '字段类型必填',
                        },
                        { max: 24, message: '字段类型不超过24字符' },
                      ]"
                    >
                      <el-input
                        size="small"
                        placeholder="不超过24字符"
                        v-model="item.fieldType"
                      ></el-input>
                    </el-form-item>
                  </div>
                  <div>
                    <el-form-item
                      :prop="'protocols.' + index + '.fieldName'"
                      :rules="[
                        {
                          required: true,
                          trigger: 'blur',
                          message: '字段说明必填',
                        },
                        { max: 500, message: '字段说明不超过500字符' },
                      ]"
                    >
                      <el-input
                        size="small"
                        placeholder="不超过500字符"
                        v-model="item.fieldName"
                      ></el-input>
                    </el-form-item>
                  </div>
                  <div>
                    <!-- <el-button size="small" @click="handleDelProtocol(index)"
                      >删除</el-button
                    > -->
                    <i
                      v-if="!skill.isCheck && subAccountEditable"
                      class="delete ic-r-delete"
                      @click="handleDelProtocol(index)"
                    ></i>
                  </div>
                </li>
              </ul>
            </el-form-item>

            <el-form-item
              v-if="!skill.privateSkill"
              prop="provider"
              label="开发者"
            >
              <span>{{ skill.provider || '-' }}</span>
            </el-form-item>
            <!-- 其他人定制开发 -->
            <!-- <el-form-item v-if="skill.type == '9'" prop="extendable" label="">
              <el-checkbox v-model="skill.extendable"
                >允许其他开发者定制开发</el-checkbox
              >
            </el-form-item> -->
          </el-form>
        </os-collapse>
        <os-divider />
        <os-collapse
          :default="true"
          size="large"
          title="联系人信息"
          v-if="!skill.privateSkill"
        >
          <el-form
            class="contact-form"
            ref="contactForm"
            label-position="left"
            label-width="98px"
            :rules="rules"
            :model="skill"
            :disabled="skill.isCheck || !subAccountEditable"
          >
            <el-form-item
              label="姓名"
              class="is-required"
              prop="linkman"
              placeholder="联系人姓名"
            >
              <el-input v-model="skill.linkman"></el-input>
            </el-form-item>
            <el-form-item
              label="手机"
              class="is-required"
              prop="telephone"
              placeholder="联系人手机号"
            >
              <el-input v-model="skill.telephone"></el-input>
            </el-form-item>
            <el-form-item
              label="职位"
              class="is-required"
              prop="position"
              placeholder="联系人职位"
            >
              <el-input v-model="skill.position"></el-input>
            </el-form-item>
            <el-form-item
              label="邮箱"
              class="is-required"
              prop="email"
              placeholder="联系人邮箱"
            >
              <el-input v-model="skill.email"></el-input>
            </el-form-item>
          </el-form>
        </os-collapse>
        <os-divider />
        <!-- 联系人信息-end -->
        <os-collapse :default="skill.privateSkill ? true : false" size="large">
          <template slot="title">
            <template v-if="!skill.privateSkill">
              审核信息<a
                style="margin-left: 8px"
                :href="`${this.$config.docs}doc-54/`"
                target="_blank"
                >《开发者技能审核规范》</a
              >
            </template>
            <template v-else>发布信息</template>
          </template>
          <el-form
            ref="releaseForm"
            :model="skill"
            label-width="98px"
            :rules="rules"
            label-position="left"
            :disabled="skill.isCheck || !subAccountEditable"
          >
            <el-form-item
              label="版本号"
              class="is-required"
              prop="latestVersionArr"
              v-if="skill.latestVersionArr"
            >
              <el-input
                class="release-form-version-input"
                v-model.number="skill.latestVersionArr[0]"
                type="number"
              ></el-input>
              <span>.</span>
              <el-input
                class="release-form-version-input"
                v-model.number="skill.latestVersionArr[1]"
                type="number"
              ></el-input>
              <span>.</span>
              <el-input
                class="release-form-version-input mgr16"
                v-model.number="skill.latestVersionArr[2]"
                type="number"
              ></el-input>
              <span v-if="skill.onlineVersion && skill.onlineVersion != '0.0.0'"
                >最近发布的版本{{ skill.onlineVersion }}</span
              >
            </el-form-item>
            <el-form-item label="更新说明">
              <el-input
                type="textarea"
                resize="none"
                style="font-family: Arial"
                :autosize="{ minRows: 4, maxRows: 4 }"
                placeholder="请简要描述你的技能功能，若为版本更新请说明更新点。
  如果审核时需要注意什么，也请告诉我们。"
                v-model="skill.updateLog"
              >
              </el-input>
            </el-form-item>
          </el-form>
        </os-collapse>
        <div class="mgb56" style="margin-top: 40px" v-if="subAccountEditable">
          <el-button
            type="primary"
            @click="onSubmit(1)"
            :loading="publishing"
            :disabled="!canPulish"
          >
            {{ publishing ? '发布中...' : '发布上线' }}
          </el-button>

          <el-button
            @click="onSubmit(2)"
            :loading="saving"
            :disabled="!changed"
          >
            {{ saving ? '保存中...' : '仅保存' }}
          </el-button>
          <os-give-up-save :edited="changed" @noSave="noSave" />
        </div>
      </template>
    </div>
    <page-leave-tips
      :dialog="leaveDialog"
      @save="onSubmit(2)"
      @noSave="noSave"
      @noJump="noJump"
    />
  </os-page>
</template>

<script>
import { mapGetters } from 'vuex'
import SkillAlias from './storeSkill/skillAlias'
import UploadIcon from './storeSkill/uploadIcon'
import ExampleUtterance from './storeSkill/exampleUtterance'
import welcomeWords from './welcomeWords.vue'

export default {
  name: 'skill-release',
  data() {
    return {
      pageOptions: {
        title: '发布',
        loading: false,
      },
      rules: {
        zhName: this.$rules.skillZhName(),
        name: this.$rules.skillZhName(),
        businesstype: this.$rules.required('技能类型不能为空'),
        briefIntroduction: [
          this.$rules.required('技能简介不能为空'),
          this.$rules.lengthLimit(1, 1000, '技能简介长度不能超过1000个字符'),
        ],
        // 动态表格

        linkman: this.$rules.required('姓名不能为空'),
        telephone: [
          this.$rules.required('手机不能为空'),
          {
            pattern:
              /^((13[0-9])|(14[5,7,9])|(15[^4])|(17[0,1,3,5-8])|(18[0-9])|166|198|199|(147))\d{8}$/,
            message: '手机格式不正确',
            trigger: ['blur'],
          },
        ],
        position: this.$rules.required('职位不能为空'),
        email: [
          this.$rules.required('邮箱不能为空'),
          { type: 'email', message: '请输入正确的邮箱地址', trigger: ['blur'] },
        ],
        latestVersionArr: [{ validator: this.checkVersion, trigger: ['blur'] }],
        appPackage: [
          this.$rules.required('关联包名不能为空'),
          this.$rules.onlyNumAndEnglishChar(),
        ],
        appDownloadUrl: [
          this.$rules.required('APP下载地址不能为空'),
          {
            pattern:
              /^([hH][tT]{2}[pP]:\/\/|[hH][tT]{2}[pP][sS]:\/\/)(([A-Za-z0-9-~]+).)+([A-Za-z0-9-~\/])+$/,
            message: 'APP下载地址格式不正确',
            trigger: 'blur',
          },
        ],
      },
      skill: {},
      skillTypeList: [],
      publishing: false,
      saving: false,
      leaveDialog: {
        show: false,
      },
      routeTo: {},
      versionUnValid: false,
      isAliasValid: null,
      initAlias: {
        data: [],
      },
      initEgUtterances: {
        data: [],
        type: 'setExampleUtterance',
        tip: '例如：打开小飞天文百科,回车新增',
      },
      initWelcomePhrase: {
        data: [],
        type: 'setWelcomePhrase',
        tip: '例如：欢迎来到小飞天文百科,回车新增',
      },
      image: {
        url: '',
      },
      initDataChanged: false, //记录computed检测不到的数据是否有改变(包括：别名、示例说法、iconUrl)
      protocolOptions: [
        { value: 1, label: '意图' },
        { value: 2, label: '语义' },
        { value: 3, label: '数据字段' },
        { value: 4, label: '答案' },
      ],
      protocolToBeAdded: {
        fieldName: '', //字段说明
        field: '', //字段
        type: '', // 1:意图，2：语义，3：数据字段，4：答案
        fieldType: '', //字段类型
      },
    }
  },
  beforeRouteLeave: function (to, from, next) {
    if (this.changed) {
      this.leaveDialog.show = true
      this.routeTo = to
      next(false)
    } else {
      next()
    }
  },
  computed: {
    ...mapGetters({
      originalSkill: 'studioSkill/skill',
      userInfo: 'user/userInfo',
      subAccountSkillAuths: 'studioSkill/subAccountSkillAuths',
      subAccount: 'user/subAccount',
      subAccountMainAccountInfo: 'aiuiApp/subAccountMainAccountInfo',
    }),
    edited() {
      let self = this
      console.log('this.skill.protocols', this.skill.protocols)
      console.log('self.originalSkill.protocols', self.originalSkill.protocols)
      return (
        self.skill.zhName !== self.originalSkill.zhName ||
        self.skill.name !== self.originalSkill.name ||
        self.skill.businesstype !== self.originalSkill.businesstype ||
        self.skill.briefIntroduction !== self.originalSkill.briefIntroduction ||
        self.skill.linkman !== self.originalSkill.linkman ||
        self.skill.telephone !== self.originalSkill.telephone ||
        self.skill.position !== self.originalSkill.position ||
        self.skill.email !== self.originalSkill.email ||
        self.skill.updateLog !== self.originalSkill.updateLog ||
        self.skill.appPackage !== self.originalSkill.appPackage ||
        self.skill.appDownloadUrl != self.originalSkill.appDownloadUrl ||
        JSON.stringify(this.skill.protocols || []) !==
          JSON.stringify(self.originalSkill.protocols || [])
      )
    },
    changed() {
      return this.edited || this.initDataChanged
    },
    isCertificated() {
      if (this.subAccount) {
        return this.subAccountMainAccountInfo.isCertificated
      }
      return (this.userInfo && this.userInfo.isCertificated) || false
    },
    DocsInUrl() {
      // 新文档下线，暂时不动
      if (this.skill.protocolVersion && this.skill.protocolVersion == '2.0') {
        return `https://aiui.xfyun.cn/doc/aiui/4_skill_develop/8_agreement/protocal/request.html`
      }
      return `${this.$config.docs}doc-73/`
    },
    subAccountEditable() {
      return this.subAccountSkillAuths[this.skill.id] == 2 ? false : true
    },
    canPulish() {
      if (this.subAccount) {
        if (this.skill.privateSkill) {
          return this.subAccountSkillAuths[this.skill.id] == 4
        } else {
          return !this.skill.isCheck
        }
      } else {
        return !this.skill.isCheck
      }
    },
    theme() {
      return this.skill.secondType === 2 ? 'APP' : '技能'
    },
  },
  watch: {
    originalSkill: function (val, oldVal) {
      this.skill = this.$deepClone(val)
      this.returnInitData()
      if (this.routeTo.name) {
        this.$router.push({
          name: this.routeTo.name,
          params: this.routeTo.params,
        })
      }
    },
  },
  created() {
    if (this.$store.state.studioSkill.skill.id) {
      let skill = this.$deepClone(this.$store.state.studioSkill.skill)
      this.skill = {
        ...skill,
        protocols: skill.protocols || [],
      }

      this.returnInitData()
    }
    this.getSkillType()
  },

  methods: {
    checkVersion(rule, value, callback) {
      let self = this
      let first = parseInt(value[0]),
        second = parseInt(value[1]),
        third = parseInt(value[2])
      if (
        !Number.isInteger(first) ||
        !Number.isInteger(second) ||
        !Number.isInteger(third)
      ) {
        callback(new Error('版本号必须为数字值'))
      }
      if (
        value.join('.') == self.skill.onlineVersion ||
        first < self.skill.onlineVersion.split('.')[0]
      ) {
        callback(new Error('当前版本号需大于最近发布的版本号'))
      }
      if (first == self.skill.onlineVersion.split('.')[0]) {
        if (second < self.skill.onlineVersion.split('.')[1]) {
          callback(new Error('当前版本号需大于最近发布的版本号'))
        }
        if (
          second == self.skill.onlineVersion.split('.')[1] &&
          third <= self.skill.onlineVersion.split('.')[2] - 1
        ) {
          callback(new Error('当前版本号需大于最近发布的版本号'))
        }
      }
      callback()
    },
    returnInitData() {
      if (this.skill.privateSkill) return
      this.initEgUtterances.isCheck = this.skill.isCheck
      this.initWelcomePhrase.isCheck = this.skill.isCheck
      this.initAlias.data = this.skill.aliasName
        ? JSON.parse(this.skill.aliasName)
        : []
      this.initEgUtterances.data = this.skill.examplePhrase
        ? JSON.parse(this.skill.examplePhrase)
        : []
      // this.initWelcomePhrase.data = this.skill.welcomePhrase
      //   ? JSON.parse(this.skill.welcomePhrase)
      //   : []
      this.initWelcomePhrase.data = this.skill.welcomePhrase
        ? this.skill.welcomePhrase
        : []
      this.image.url = this.skill.iconUrl
    },
    checkInitDataStatus() {
      let self = this
      if (
        self.skill.aliasName != self.originalSkill.aliasName ||
        self.skill.examplePhrase !== self.originalSkill.examplePhrase ||
        self.skill.welcomePhrase !== self.originalSkill.welcomePhrase ||
        self.skill.iconUrl !== self.originalSkill.iconUrl
      ) {
        self.initDataChanged = true
      } else {
        self.initDataChanged = false
      }
    },
    getSkillType() {
      this.$utils.httpGet(
        this.$config.api.STUDIO_SKILL_SKILL_TYPES,
        {},
        {
          success: (res) => {
            this.skillTypeList = res.data
          },
          error: (err) => {
            console.log('page=>>')
            console.log(err)
          },
        }
      )
    },
    setAlias(val, valid) {
      this.skill.aliasName = val
      this.isAliasValid = valid
      this.checkInitDataStatus()
    },
    setExampleUtterance(val) {
      this.skill.examplePhrase = val
      this.checkInitDataStatus()
    },
    setWelcomePhrase(val) {
      this.skill.welcomePhrase = val
      this.checkInitDataStatus()
    },
    setIconInfo(val) {
      this.skill.iconUrl = val
      this.checkInitDataStatus()
    },
    checkStoreSkill() {
      if (!this.isAliasValid && this.isAliasValid != null) {
        this.$message.warning('技能别名不合法')
        return false
      }
      if (this.skill.examplePhrase && this.skill.examplePhrase.length < 3) {
        this.$message.warning('示例说法不能为空')
        return false
      }
      if (this.skill.welcomePhrase && this.skill.welcomePhrase.length < 1) {
        this.$message.warning('欢迎语示例说法不能为空')
        return false
      }
      if (!this.skill.iconUrl) {
        this.$message.warning('技能图标不能为空')
        return false
      }
      return true
    },
    onSubmit(isPublish) {
      let self = this
      if (this.saving || this.publishing) {
        return
      }
      if (!this.skill.privateSkill && !this.checkStoreSkill()) {
        return
      }
      let canSubmit = true
      this.$refs.releaseForm.validate((valid) => {
        if (!valid) {
          canSubmit = false
        }
      })
      this.$refs.contactForm &&
        this.$refs.contactForm.validate((valid) => {
          if (!valid) {
            canSubmit = false
          }
        })
      this.$refs.skillForm.validate((valid) => {
        if (!valid) {
          canSubmit = false
        }
      })
      if (!canSubmit) return
      this.$refs.egUtterance && this.$refs.egUtterance.delText()
      let data = {
        isPublish: isPublish,
        updateLog: this.skill.updateLog || '',
        secondType: this.skill.secondType,
      }
      data.latestVersion = this.skill.latestVersionArr.join('.')
      if (data.latestVersion === '..') {
        data.latestVersion = ''
      }
      self.pageOptions.loading = true
      let api = ''
      if (this.skill.privateSkill) {
        // 私有技能创建
        api = this.$config.api.STUDIO_ADD_EDIT_PRIVATESKILL
        ;(data.id = this.skill.id),
          (data.zhname = this.skill.zhName),
          (data.name = this.skill.name),
          (data.businesstype = this.skill.businesstype || '')
        data.type = this.skill.type
      } else {
        if (!this.checkStoreSkill) {
          return
        }
        // 区分原有os开放技能与AIUI新建的开放技能
        if (this.skill.privateSkill) {
          api = this.$config.api.STUDIO_ADD_EDIT_SKILL
        } else {
          // 这里skill type 为9
          api = this.$config.api.STUDIO_ADD_EDIT_OPENSKILL
          // AIUI新建的开放技能不需要二级分类
          delete data.secondType
        }
        data.skillId = this.skill.id
        ;(data.zhName = this.skill.zhName),
          (data.businesstype = this.skill.businesstype)
        data.aliasName = this.skill.aliasName || ''
        data.examplePhrase = this.skill.examplePhrase || ''
        // data.welcomePhrase = this.skill.welcomePhrase || ''
        data.welcomePhrase = this.skill.welcomePhrase
          ? JSON.stringify(this.skill.welcomePhrase)
          : ''
        data.iconUrl = this.skill.iconUrl || ''
        data.briefIntroduction = this.skill.briefIntroduction || ''
        if (this.skill.protocols.length > 0) {
          data.protocols = JSON.stringify(this.skill.protocols || [])
        }

        data.provider = this.skill.provider || ''
        data.linkman = this.skill.linkman
        data.telephone = this.skill.telephone
        data.position = this.skill.position
        data.email = this.skill.email
        if (this.skill.secondType == 2) {
          data.appPackage = this.skill.appPackage || ''
          data.appDownloadUrl = this.skill.appDownloadUrl || ''
        }
      }
      if (isPublish === 1) {
        this.publishing = true
      } else {
        this.saving = true
      }
      this.$utils.httpPost(api, data, {
        success: (res) => {
          self.pageOptions.loading = false
          if (isPublish === 1) {
            this.publishing = false
            self.$message.success('发布成功')
            self.$store.dispatch(
              'studioSkill/setPublishResultForTest',
              'publish_success'
            )
          } else {
            this.saving = false
            self.$message.success('保存成功')
          }
          self.$store.dispatch('studioSkill/setSkill', this.skill.id)
          self.initDataChanged = false
        },
        error: (err) => {
          self.pageOptions.loading = false
          if (isPublish === 1) {
            this.publishing = false
            err &&
              err.desc &&
              self.$store.dispatch(
                'studioSkill/setPublishResultForTest',
                err.desc
              )
          } else {
            this.saving = false
          }
          console.log('page=>>')
          console.log(err)
        },
      })
    },
    uploadSuccess(res) {
      let self = this
      this.$utils.httpPost(
        this.$config.api.STUDIO_ADD_EDIT_SKILL,
        {
          skillId: this.skill.id,
          callName: this.skill.callName,
          zhName: this.skill.zhName,
          iconUrl: res.data,
        },
        {
          success: (res) => {
            self.$message.success('保存成功')
            self.$store.dispatch('studioSkill/setSkill', this.skill.id)
          },
          error: (err) => {
            console.log('page=>>')
            console.log(err)
          },
        }
      )
    },
    noSave() {
      this.$refs.skillForm.clearValidate()
      this.$refs.contactForm && this.$refs.contactForm.clearValidate()
      this.initDataChanged = false
      this.skill = this.$deepClone(this.originalSkill)
      this.returnInitData()
      if (this.routeTo.name) {
        this.$router.push({
          name: this.routeTo.name,
          params: this.routeTo.params,
        })
      }
    },
    noJump() {
      this.routeTo = {}
    },
    handleAddProtocol(protocol) {
      this.$refs.protocolForm.validate((valid) => {
        if (valid) {
          let pro = {
            type: protocol.type,
            field: protocol.field,
            fieldType: protocol.fieldType,
            fieldName: protocol.fieldName,
          }
          this.skill.protocols.push(pro)
          this.protocolToBeAdded = {
            type: '',
            field: '',
            fieldType: '',
            fieldName: '',
          }
          this.$nextTick(() => {
            this.$refs.protocolForm.clearValidate()
          })
        }
      })
    },
    handleDelProtocol(index) {
      this.skill.protocols.splice(index, 1)
    },
  },
  components: {
    SkillAlias,
    UploadIcon,
    ExampleUtterance,
    welcomeWords,
  },
}
</script>

<style lang="scss" scoped>
.ic-r-delete {
  font-size: 20px;
  cursor: pointer;
  color: $primary;
  display: none;
}

.param-sub {
  :deep(.el-form-item.is-success .el-input__inner) {
    border-color: transparent;
  }
  :deep(.el-form-item .el-input__inner) {
    border-color: transparent;
  }
  :deep(.el-form-item.is-success .el-input__inner:focus) {
    border-color: transparent;
  }
  :deep(.el-form-item.is-error .el-input__inner) {
    border-color: transparent;
  }
  :deep(.el-form-item.is-error .el-input__inner:focus) {
    border-color: transparent;
  }
}
.param-added {
  :deep(
      .el-form-item.is-success .el-input__inner .el-form-item .el-input__inner,
      .el-form-item .el-textarea__inner
    ) {
    border-color: transparent;
  }
  :deep(.el-form-item .el-input__inner:focus) {
    border-color: #8bc1f4;
  }
  :deep(.el-form-item.is-success .el-input__inner:focus) {
    border-color: #3fbf70;
  }
  :deep(.el-form-item.is-error .el-input__inner) {
    border-color: transparent;
  }
  :deep(.el-form-item.is-error .el-input__inner:focus) {
    border-color: #ff3838;
  }

  &:hover {
    .ic-r-delete {
      display: inline-block;
    }
  }
}
.skill-publish-page {
  padding-bottom: 70px;
}
.release-form-price-select {
  width: 92px;
}
.release-form-price-input {
  .el-input__inner {
    width: 160px;
  }
}
.release-form-version-input {
  width: 80px;
}
.release-form-phrase-label {
  color: $grey5;
  padding-right: 24px;
}
.item-skill-privacy {
  font-size: 0;
}
.skill-privacy {
  display: inline-block;
  margin-right: 16px;
  width: 52px;
  height: 24px;
  line-height: 24px;
  text-align: center;
  color: $success;
  border-radius: 12px;
  background-color: $success-light-12;
}
.contact-form {
  .el-form-item {
    width: 43%;
    display: inline-block;
  }
  .el-form-item:nth-child(2n + 1) {
    margin-right: 13%;
  }
}
//实名认证
.certificate-wrap {
  padding: 40px 48px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 1);
  border: 1px solid rgba(242, 245, 247, 1);
  box-shadow: 0px 6px 18px 0px rgba(0, 0, 0, 0.05);
  font-size: 16px;
  .title {
    margin-bottom: 12px;
    line-height: 1.23;
    font-size: 20px;
    font-weight: 500;
  }
  .ic-r-exclamation {
    margin-right: 16px;
    vertical-align: -1px;
    color: $warning;
  }
}
.welcom-words-tip {
  padding-left: 16px;
  height: 40px;
  color: $grey5;
  background: $grey4-15;
  border-radius: 2px;
}

.param-container {
  margin: 0 auto;
  // width: 80%;

  li,
  .protocol-container {
    display: flex;
    //  justify-content: space-between;
    list-style-type: none;
    > div {
      border-left: 1px solid #eee;
      border-top: 1px solid #eee;
      border-bottom: 1px solid #eee;
      text-align: center;
      padding: 10px;
    }
    > div:last-child {
      border-right: 1px solid #eee;
    }
    > div:nth-child(1) {
      width: 20%;
    }
    > div:nth-child(2) {
      width: 20%;
    }
    > div:nth-child(3) {
      width: 20%;
    }
    > div:nth-child(4) {
      width: 20%;
    }
    > div:nth-child(5) {
      width: 20%;
    }
    :deep(.el-form-item__content) {
      margin-left: 0 !important;
    }
  }

  .protocol-container {
    width: 100%;
  }
}
</style>
<style lang="scss">
.skill-publish-page {
  .os-collapse-title {
    margin: 28px 0;
    .ic-r-angle-d {
      color: $grey5;
    }
    a {
      font-size: 14px;
    }
  }
}
</style>
