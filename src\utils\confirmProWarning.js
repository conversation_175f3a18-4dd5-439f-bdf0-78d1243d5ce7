import { MessageBox } from 'element-ui'

const confirmProWarning = (message, title, options = {}) => {
  const customOption = {
    title,
    iconClass: 'confirm-pro-warning-icon',
    customClass: 'confirm-pro-warning',
    showCancelButton: true,
  }
  const paramOption = Object.assign({}, customOption, {
    message,
    title: '提示',
    ...options,
  })
  console.log('paramOption', paramOption)
  return MessageBox(paramOption)
}

export default {
  install(Vue) {
    Vue.prototype.$confirm_pro_warning = confirmProWarning
  },
}
