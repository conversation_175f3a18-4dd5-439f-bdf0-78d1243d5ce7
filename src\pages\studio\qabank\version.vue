<template>
  <os-page class="skill-version-page" :options="pageOptions">
    <studio-qaBank-header-right slot="btn" />
    <div class="version-page">
      <!-- 线上版本 start -->
      <os-collapse :default="true" size="large" title="线上版本">
        <os-table class="gutter-table-style secondary-table" :tableData="mall">
          <el-table-column prop="outNumber" label="版本" width="156">
          </el-table-column>
          <el-table-column label="发布时间" width="200">
            <template slot-scope="scope">
              {{ scope.row.createTime | date }}
            </template>
          </el-table-column>
          <el-table-column label="更新描述">
            <template slot-scope="scope">
              <el-popover
                class="update-log"
                trigger="hover"
                placement="bottom-start"
                :content="scope.row.updateLog"
                v-if="scope.row.updateLog"
              >
                <div slot="reference">
                  {{ scope.row.updateLog }}
                </div>
              </el-popover>
              <span v-else>-</span>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="200">
            <template slot-scope="scope">
              <a @click="showUseInApp(scope.row.outNumber, scope.row.id, false)"
                >在应用中生效</a
              >
            </template>
          </el-table-column>
        </os-table>
      </os-collapse>
      <os-divider />
      <!-- 历史版本 start -->
      <os-collapse :default="true" size="large" title="历史版本">
        <os-table
          class="gutter-table-style secondary-table"
          :tableData="mallHistory"
          style="margin-bottom: 56px"
          @change="changePage"
        >
          <el-table-column prop="outNumber" label="版本" width="156">
          </el-table-column>
          <el-table-column label="发布时间" width="200">
            <template slot-scope="scope">
              {{ scope.row.createTime | date }}
            </template>
          </el-table-column>
          <el-table-column label="更新描述">
            <template slot-scope="scope">
              <el-popover
                class="update-log"
                trigger="hover"
                placement="bottom-start"
                :content="scope.row.updateLog"
                v-if="scope.row.updateLog"
              >
                <div slot="reference">
                  {{ scope.row.updateLog }}
                </div>
              </el-popover>
              <span v-else>-</span>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="200">
            <template slot-scope="scope">
              <a @click="showUseInApp(scope.row.outNumber, scope.row.id, false)"
                >在应用中生效</a
              >
            </template>
          </el-table-column>
        </os-table>
      </os-collapse>
    </div>
    <use-in-app
      :dialog="dialog"
      :businessId="businessId"
      :selectNumber="selectNumber"
      :selectMallId="selectMallId"
      :onlineFlag="onlineFlag"
    ></use-in-app>
  </os-page>
</template>
<script>
import UseInApp from './dialog/useInApp'

export default {
  name: 'skill-version',
  data() {
    return {
      pageOptions: {
        title: '版本管理',
        loading: false,
      },

      mall: {
        total: 0,
        list: [],
      },
      mallHistory: {
        total: 0,
        page: 1,
        size: 5,
        list: [],
      },
      //测试数据
      allMallHistory: [],

      status: {
        0: '已下线',
        1: '已发布',
      },
      dialog: {
        show: false,
      },
      selectNumber: '',
      selectMallId: '',
      onlineFlag: false,
    }
  },
  computed: {
    businessId() {
      return this.$store.state.studioQa.id
    },

    skillType() {
      return this.$store.state.studioSkill.skill.type || ''
    },
    // subAccountEditable() {
    //   let auths = this.$store.state.studioSkill.subAccountSkillAuths
    //   return auths[this.businessId] == 2 ? false : true
    // },
  },
  created() {
    this.getVersion()
  },
  methods: {
    getVersion() {
      this.pageOptions.loading = true
      this.$utils.httpGet(
        this.$config.api.STUDIO_QA_VERSIONS,
        {
          repoId: this.businessId,
        },
        {
          success: (res) => {
            this.mall.list.splice(0)
            this.mallHistory.list.splice(0)
            this.allMallHistory.splice(0)
            this.pageOptions.loading = false

            if (res.data && !Object.keys(res.data).length) {
              return
            }

            if ((res.data.private || []).length <= 0) {
              return
            }
            this.mall.list.push(res.data.private[0])
            this.allMallHistory.push(
              ...res.data.private.slice(1, res.data.private.length)
            )
            this.mallHistory.total = res.data.private.length - 1
            this.mallHistory.list.push(
              ...res.data.private.slice(1, this.mallHistory.size + 1)
            )
          },
          error: (err) => {
            this.pageOptions.loading = false
          },
        }
      )
    },
    changePage(val) {
      let size = this.mallHistory.size
      this.mallHistory.list = this.allMallHistory.slice(
        (val - 1) * size,
        val * size
      )
    },
    unshelve() {
      if (!this.subAccountEditable) return
      this.$utils.httpGet(
        this.$config.api.STUDIO_SKILL_UNSHELVE,
        {
          businessId: this.businessId,
        },
        {
          success: (res) => {
            this.getVersion()
            this.$message.success('下线成功')
            this.$store.dispatch('studioSkill/setSkill', this.businessId)
          },
        }
      )
    },
    cancelPublish(mallId) {
      if (!this.subAccountEditable) return
      this.$utils.httpGet(
        this.$config.api.STUDIO_SKILL_CANCELPUBLISH,
        {
          businessId: this.businessId,
          mallId: mallId,
        },
        {
          success: (res) => {
            this.getVersion()
            this.$message.success('取消成功')
            this.$store.dispatch('studioSkill/setSkill', this.businessId)
          },
        }
      )
    },
    showUseInApp(num, id, online) {
      this.dialog.show = true
      this.selectNumber = num
      this.selectMallId = id
      this.onlineFlag = online
    },
  },
  components: {
    UseInApp,
  },
}
</script>
<style lang="scss" scoped>
.explain {
  color: $primary;
  cursor: pointer;
}
.update-log > div {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.skill-status,
.check-status {
  display: inline-block;
  width: 8px;
  height: 8px;
  border: 2px solid;
  border-radius: 8px;
  margin-right: 6px;
}
.skill-status {
  &-0 {
    border-color: $grey5;
  }
  &-1 {
    border-color: $success;
  }
}
.check-status {
  &-1 {
    border-color: $primary;
  }
  &-2 {
    border-color: $success;
  }
  &-3 {
    border-color: $dangerous;
  }
  &-4 {
    border-color: $warning;
  }
}
</style>
<style lang="scss">
.skill-version-page {
  .os-collapse-title {
    margin: 28px 0;
    .ic-r-angle-d {
      color: $grey5;
    }
  }
}
</style>
