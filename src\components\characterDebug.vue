<template>
  <div class="debug-wrap">
    <div class="debug-header">
      <!-- <div class="notice-title" >
        <i class="circle"></i>
        <span class="title">设备人设体验<span class="plus">+</span></span>
      </div> -->
      <div class="collapse-btn" @click="closeRightTest">
        <i class="iconfont icon-zhankai"></i>
        <span>{{ '设备人设体验' }}</span>
      </div>
      <div class="send-wrap">
        <el-input
          class="debug-input"
          :maxlength="120"
          v-model="question"
          size="medium"
          @keyup.enter.native="experience"
          @keyup.up.native="preQuestion"
          @keyup.down.native="nextQuestion"
          placeholder="输入语料，回车体验"
        ></el-input>
        <span
          :class="['debug-send', { 'debug-send-active': question }]"
          @click="experience"
          >发送</span
        >
      </div>
    </div>
    <div class="clear" @click="cleanHistory">
      <i class="iconfont icon-shanchu"></i>
    </div>

    <div class="debug-dialog" ref="list">
      <div class="robot">
        <i class="iconfont icon-jiqiren"></i>
      </div>
      <div
        :class="['dialog dialog-' + item.type]"
        v-for="(item, index) in dialogList"
        :key="index"
      >
        <template v-if="item.type === 'answer'">
          <!-- <i class="ib icon-debug-answer"></i> -->
          <div class="ib message">
            <!-- 非iflyos技能 -->
            <template
              v-if="
                item.data.res &&
                item.data.res.semantic &&
                Array.isArray(item.data.res.semantic)
              "
            >
              <p class="msg-item">{{ item.data[item.type] }}</p>
              <div class="intent-info-wrap">
                <div
                  v-if="
                    item.data.res.semantic[0] &&
                    item.data.res.semantic[0].intent
                  "
                >
                  <span class="intent-info-title">意图</span>
                  <span>{{ item.data.res.semantic[0].intent }}</span>
                </div>
                <template
                  v-if="
                    item.data.res.semantic[0] &&
                    item.data.res.semantic[0].slots &&
                    item.data.res.semantic[0].slots.length
                  "
                >
                  <span class="intent-info-title">实体槽位</span>

                  <span class="tag-wrap">
                    <el-popover
                      placement="bottom-start"
                      trigger="click"
                      v-for="(tag, index) in item.data.res.semantic[0].slots"
                      :key="index"
                    >
                      <p class="entity-item">
                        <span class="tag">槽位标识</span>{{ tag.name }}
                      </p>
                      <p
                        v-if="
                          entityInIntent &&
                          Object.keys(entityInIntent).length &&
                          entityInIntent[tag.name]
                        "
                        class="entity-item"
                      >
                        <span class="tag">对应实体</span>@{{
                          entityInIntent[tag.name]
                        }}
                      </p>
                      <p class="entity-item">
                        <span class="tag">对应短语</span>{{ tag.value }}
                      </p>
                      <el-tag slot="reference" :disable-transitions="false">
                        <span class="slot-tag" :title="tag.name">{{
                          tag.name
                        }}</span>
                      </el-tag>
                    </el-popover></span
                  >
                </template>
              </div>
              <div class="view-button-group">
                <a
                  class="msg-item view-button"
                  v-if="!!item.data.res || !!item.data.req"
                  @click="openJsonDialog(item.data)"
                  ><i class="iconfont icon-chakan"></i
                  ><span>&nbsp;查看JSON</span></a
                >
                <a
                  class="msg-item"
                  v-if="
                    item.data[item.type] &&
                    item.data[item.type].indexOf('error:') !== -1
                  "
                  :href="`${$config.docs}aiui/4_skill_develop/4_private_skill/test.html`"
                  target="_blank"
                  >查看文档</a
                >
              </div>
            </template>
            <!-- iflyos技能 -->
            <template
              v-else-if="
                item.data.res &&
                item.data.res.response &&
                item.data.res.response.directives &&
                item.data.res.response.directives[0] &&
                item.data.res.response.directives[0].updatedIntent
              "
            >
              <p class="msg-item">{{ item.data[item.type] }}</p>
              <div class="intent-info-wrap">
                <div
                  v-if="item.data.res.response.directives[0].updatedIntent.name"
                >
                  <span class="intent-info-title">意图</span>
                  <span>{{
                    item.data.res.response.directives[0].updatedIntent.name
                  }}</span>
                </div>
                <template
                  v-if="
                    item.data.res.response.directives[0].updatedIntent.slots &&
                    Object.keys(
                      item.data.res.response.directives[0].updatedIntent.slots
                    ).length
                  "
                >
                  <span class="intent-info-title">槽位标识</span>

                  <span class="tag-wrap"
                    ><el-popover
                      placement="bottom-start"
                      trigger="click"
                      v-for="(tag, index) in item.data.res.response
                        .directives[0].updatedIntent.slots"
                      :key="index"
                    >
                      <p class="entity-item">
                        <span class="tag">槽位标识</span>{{ tag.name }}
                      </p>
                      <p
                        v-if="
                          entityInIntent &&
                          Object.keys(entityInIntent).length &&
                          entityInIntent[tag.name]
                        "
                        class="entity-item"
                      >
                        <span class="tag">对应实体</span>@{{
                          entityInIntent[tag.name]
                        }}
                      </p>
                      <p class="entity-item">
                        <span class="tag">对应短语</span>{{ tag.value }}
                      </p>
                      <el-tag slot="reference" :disable-transitions="false">
                        <span class="slot-tag" :title="tag.name">{{
                          tag.name
                        }}</span>
                      </el-tag>
                    </el-popover></span
                  >
                </template>
              </div>
              <div class="view-button-group">
                <a
                  class="msg-item view-button"
                  v-if="!!item.data.res || !!item.data.req"
                  @click="openJsonDialog(item.data)"
                  ><i class="iconfont icon-chakan"></i
                  ><span>&nbsp;查看JSON</span></a
                >
                <a
                  class="msg-item"
                  v-if="
                    item.data[item.type] &&
                    item.data[item.type].indexOf('error:') !== -1
                  "
                  :href="`${$config.docs}aiui/4_skill_develop/4_private_skill/test.html`"
                  target="_blank"
                  >查看文档</a
                >
              </div>
            </template>
            <div v-else style="padding: 0 16px">
              <span>{{ item.data[item.type] }}</span>
              <template
                v-if="
                  !item.data.hasOwnProperty('hasscript') ||
                  (item.data.hasOwnProperty('hasscript') && item.data.hasscript)
                "
              >
                <div class="view-button-group">
                  <a
                    class="view-button"
                    v-if="item.data.httpCode == 204"
                    @click="openJsonDialog(item.data, index)"
                    ><i class="iconfont icon-chakan"></i
                    ><span>&nbsp;查看JSON</span></a
                  >
                  <a
                    v-else-if="
                      item.data.httpCode == 206 || item.data.httpCode == 400
                    "
                    :href="`${$config.docs}aiui/4_skill_develop/4_private_skill/test.html`"
                    target="_blank"
                    >查看文档</a
                  >
                  <a
                    class="view-button"
                    v-else-if="!!item.data.res || !!item.data.req"
                    @click="openJsonDialog(item.data)"
                    ><i class="iconfont icon-chakan"></i
                    ><span>&nbsp;查看JSON</span></a
                  >
                </div>
              </template>
              <template
                v-if="
                  item.data.hasOwnProperty('hasscript') && !item.data.hasscript
                "
              >
                <span v-if="$route.path.match(/postprocess/g)"
                  >请打开技能后处理后再测试！</span
                >
                <span v-else
                  >请打开<a @click="toPostprocess">技能后处理</a
                  >后再测试！</span
                >
              </template>
            </div>
          </div>
        </template>
        <div class="ib message" v-else>
          {{ item.data[item.type] }}
        </div>
      </div>
    </div>

    <el-dialog
      class="debug-json-dialog"
      title="JSON"
      :visible.sync="showJson"
      width="50%"
    >
      <div class="request-json">
        <template
          v-if="
            skill.protocolVersion == '2.1' &&
            skill.type !== '2' &&
            skill.type != '9'
          "
        >
          <template v-if="resJson.httpCode && resJson.httpCode == '204'">
            <i
              class="ic-r-copy"
              title="复制代码"
              @click="copyJson(resJson)"
            ></i>
            <json-view class="json-wrap" :data="resJson"></json-view>
          </template>
          <el-tabs v-else v-model="activeType" type="card">
            <el-tab-pane label="Request" name="Request">
              <i
                class="ic-r-copy"
                title="复制代码"
                @click="copyJson(reqJson)"
              ></i>
              <json-view class="json-wrap" :data="reqJson"></json-view>
            </el-tab-pane>
            <el-tab-pane label="Response" name="Response">
              <i
                class="ic-r-copy"
                title="复制代码"
                @click="copyJson(resJson)"
              ></i>
              <json-view class="json-wrap" :data="resJson"></json-view>
            </el-tab-pane>
          </el-tabs>
        </template>

        <template v-else>
          <i class="ic-r-copy" title="复制代码" @click="copyJson(resJson)"></i>
          <json-view class="json-wrap" :data="resJson"></json-view>
        </template>
      </div>
      <div class="dialog-bottom"></div>
    </el-dialog>
    <entity-popover
      :slotInfo="slotInfo"
      :variablePopover="variablePopover"
    ></entity-popover>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import EntityPopover from './skillDebugEntityPopover'

export default {
  name: 'character-debug',
  props: ['debugType'],
  data() {
    return {
      submitting: false,
      rc4Answer: [
        '啊哦~~这个问题太难了，换个问题吧！',
        '不好意思，我好像没听懂。。。',
      ],
      rc4Index: 0,
      question: '',
      questionList: [],
      questionIndex: 0, //记录按上下键问题开始位置
      uid: this.$utils.experienceUid(),
      dialogList: [
        {
          type: 'answer',
          data: {
            answer: '你好，我是智能的小飞~',
          },
        },
      ],
      thresholdEdit: false,
      threshold: 82,
      showJson: false,
      resJson: {},
      reqJson: {},
      hasShowEntityTip: false, // 部分商店技能体验需动态实体提示
      activeType: 'Request',
      slotInfo: {},
      variablePopover: {
        show: false,
        rect: null,
      },
    }
  },
  computed: {
    ...mapGetters({
      skill: 'studioSkill/skill',
      app: 'aiuiApp/app',
      currentScene: 'aiuiApp/currentScene',
      appConfigChange: 'aiuiApp/configChange',
      translateConfig: 'aiuiApp/translateConfig',
      skillDetail: 'aiuiStore/skillDetail',
      entityInIntent: 'studioSkill/entityInIntent',
    }),
  },
  created() {},
  updated() {
    if (this.$refs.list) {
      this.$refs.list.scrollTop = 100000
    }
  },
  methods: {
    preQuestion() {
      if (this.questionIndex > 0) {
        this.questionIndex--
        this.question = this.questionList[this.questionIndex]
      } else {
        this.question = ''
        this.questionIndex = -1
      }
    },
    nextQuestion() {
      if (this.questionIndex < this.questionList.length) {
        this.questionIndex++
        this.question = this.questionList[this.questionIndex]
      } else {
        this.question = ''
        this.questionIndex = this.questionList.length
      }
    },
    addDialog(type, data) {
      this.dialogList.push({
        type: type,
        data: data,
        httpCode: null,
      })
    },

    experience() {
      if (this.submitting) return
      if (!this.question) return

      let self = this
      let data = {
        businessId: this.skill.id,
        text: this.question.trim(),
        name: this.skill.name,
        tyuid: this.uid,
      }
      if (this.skill.isFuzzy) data.threshold = this.threshold / 100

      this.submitting = true
      this.$utils.httpPost(this.$config.api.STUDIO_SKILL_EXPERIENCE, data, {
        success: (res) => {
          this.submitting = false
          if (res.flag) {
            self.addDialog('question', {
              question: self.question.trim(),
            })
            self.questionList.push(self.question)
            self.questionIndex = self.questionList.length
            self.question = ''

            // 规避res.data.res 为空。2.1协议的 res.data 在httpCode != 200 时， res.data.res 为空
            res.data.res = res.data.res
              ? JSON.parse(res.data.res)
              : res.data.res

            // 2.0协议技能相关
            if (res.data.res.hasOwnProperty('rc') && res.data.res.rc == 4) {
              res.data.answer = self.rc4Answer[self.rc4Index]
              self.rc4Index = 1
            }
            if (res.data.res.hasOwnProperty('console')) {
              self.$store.dispatch(
                'studioSkill/setDebugConsole',
                res.data.res.console
              )
              delete res.data.res.console
            }

            // 2.1协议技能相关
            if (res.data.hasOwnProperty('httpCode')) {
              if (res.data.hasOwnProperty('hasscript') && !res.data.hasscript) {
                // res.data.answer = '请打开技能后处理的云函数后再测试！'
                // 跳转到技能后处理页
              } else if (res.data.httpCode !== 200) {
                if (res.data.httpCode == 204) {
                  res.data.answer = '不好意思，我好像没听懂...'
                  res.data.httpCode = '204'
                  res.data.res = {
                    httpCode: res.data.httpCode,
                    userId: self.uid,
                    requestId: res.data.requestId,
                    text: res.data.text,
                  }
                }
                if (res.data.httpCode == 206) {
                  // res.data.answer = `服务超时: ${res.data.httpCode}`
                  res.data.answer = '服务超时'
                  res.data.httpCode = '206'
                }
                if (res.data.httpCode == 400) {
                  res.data.answer = '无效输入'
                  res.data.httpCode = '400'
                }
              } else {
                if (res.data && !res.data.hasOwnProperty('answer')) {
                  res.data.answer = '请点击json按钮，查看语义结果！'
                }
              }
            } else {
              if (res.data.res.rc == 4) {
                res.data.answer = self.rc4Answer[self.rc4Index]
                self.rc4Index = 1
              }
            }

            // 处理云函数日志
            if (
              res.data.hasOwnProperty('scriptResponse') &&
              res.data.scriptResponse
            ) {
              let scriptResponse = JSON.parse(res.data.scriptResponse)
              if (scriptResponse) {
                let { response = {} } = scriptResponse
                if (
                  response &&
                  response.hasOwnProperty('console') &&
                  response.console
                ) {
                  self.$store.dispatch(
                    'studioSkill/setDebugConsole',
                    scriptResponse.response.console
                  )
                }
              }
              if (
                res.data.res.hasOwnProperty('response') &&
                res.data.res.response
              ) {
                delete res.data.res.response.console
              }
            }

            // 判断是否构建
            if (!res.data.isStructure) {
              res.data.answer += '(您的技能尚未构建)'
            }
            self.addDialog('answer', res.data)
          }
        },
        error: (err) => {
          this.submitting = false
        },
      })
    },
    cleanHistory() {
      let self = this
      let data = {
        businessId: this.skill.id,
        tyuid: this.uid,
        protocolVersion: this.skill.protocolVersion,
      }

      this.$utils.httpGet(
        this.$config.api.STUDIO_SKILL_EXPERIENCE_CLEAN,
        data,
        {
          success: (res) => {
            self.dialogList.splice(1)
            self.$store.dispatch('studioSkill/setDebugConsole', [])
          },
          error: (err) => {
            console.log('page=>>')
            console.log(err)
          },
        }
      )
    },

    copyJson(data) {
      this.$utils.copyClipboard(JSON.stringify(data, null, '    '))
    },
    openJsonDialog(datas) {
      this.showJson = !this.showJson
      if (datas.hasOwnProperty('res') && datas.res) {
        this.resJson = datas.res
      }
      if (datas.hasOwnProperty('req')) {
        this.reqJson = datas.req ? JSON.parse(datas.req) : {}
      }
    },
    closeRightTest() {
      this.$store.dispatch('studioSkill/setRightTestOpen', false)
    },

    toPostprocess() {
      let self = this
      let name = this.$route.path.match(/\/sub/g)
        ? 'sub-skill-post-process'
        : 'skill-post-process'
      let routeData = this.$router.resolve({
        name: name,
        params: { skillId: self.skill.id },
      })
      window.open(routeData.href, '_blank')
    },
  },
  components: { EntityPopover },
}
</script>

<style lang="scss" scoped>
.notice-title {
  position: relative;
  .circle {
    width: 8px;
    height: 8px;
    border: 2px solid #1ed3ff;
    border-radius: 50%;
    display: inline-block;
  }
  .title {
    font-size: 14px;
    font-family: Microsoft YaHei;
    font-weight: bold;
    color: $primary;
    position: relative;
  }
  .plus {
    position: absolute;
    z-index: 1;
    top: 0px;
    right: -6px;
    color: #1ed3ff;
    font-size: 12px;
    display: inline-block;
    line-height: 0;
  }
}
.debug-input {
  width: 100%;
}
.view-button-group {
  position: absolute;
  bottom: -44px;
  left: 0px;
}
.view-button {
  display: flex;
  align-items: center;
  padding: 1px 15px !important;
  background: $primary;
  color: #fff;
  border-radius: 4px;
  &:hover {
    background: $hover;
  }
}
.icon-chakan {
  font-size: 20px;
  color: #fff;
}
.debug-wrap {
  position: relative;
  height: 100%;
  color: $semi-black;
  background: #f7f8fa;
}
.debug-header {
  // width: 280px;
  height: 64px;
  padding: 1px 16px;
  line-height: 44px;
}
.send-wrap {
  display: flex;
  align-items: center;
  :deep(.el-input__inner) {
    border: none;
    box-shadow: 0px 5px 20px 0px rgba(165, 165, 165, 0.3);
  }
}
.debug-send {
  opacity: 1;
  color: #fff;
  background-color: $primary;
  height: 36px;
  display: inline-block;
  line-height: 36px;
  width: 80px;
  text-align: center;
  cursor: pointer;
  &:hover {
    background-color: $hover;
  }
}
.debug-send-active {
  opacity: 1;
}
.robot {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: $primary;
  text-align: center;
  line-height: 36px;
  margin: 4px 0 10px 0;
  i {
    color: $white;
    font-size: 20px;
  }
}
.clear {
  position: absolute;
  top: 104px;
  right: 8px;
  width: 32px;
  height: 32px;
  line-height: 24px;
  color: $grey4;
  cursor: pointer;
  font-size: 18px;
  padding: 4px;
  z-index: 2;
  border-radius: 50%;
  background: $white;
  box-shadow: 0px 5px 20px 0px rgba(165, 165, 165, 0.3);
  text-align: center;
  .ic-r-delete {
    color: $grey002;
  }
  .icon-shanchu {
    color: $grey002;
  }
}
.ic-ed-delete {
  vertical-align: top;
}
.debug-dialog {
  overflow: auto;
  height: calc(100% - 100px);
  padding: 48px 16px 45px;
  width: 100%;
  margin-top: 32px;
  .dialog {
    // overflow: hidden;
    margin-bottom: 24px;
  }
  .dialog-answer:nth-of-type(2) {
    margin-bottom: 24px;
  }
  .dialog-answer:not(:nth-of-type(2)) {
    margin-bottom: 56px;
  }
}
.icon-debug-answer {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  vertical-align: top;
  margin-right: 8px;
  background: url('../assets/svg/debug/icon-debug-answer.svg') no-repeat 5px 5px
    $primary-light-12;
}

.dialog-answer {
  .message {
    transition: 0.3s;
    position: relative;
    max-width: 410px;
    color: $grey002;
    // background: $grey1;
    padding: 9px 0;
    border-radius: 10px;
    background: rgba(255, 255, 255, 1);
    // border: 1px solid rgba(228, 231, 237, 1);
    // box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.05);
  }
  .message::before {
    position: absolute;
    content: ' ';
    top: 14px;
    left: -11px;
    width: 0;
    height: 0;
    border-width: 9px 7px 0;
    border-style: solid;
    transform: rotate(90deg);
    border-color: #fff transparent transparent;
  }
  .message::after {
    content: ' ';
    position: absolute;
    top: 0;
    left: -20px;
    width: 20px;
    height: 20px;
    border-radius: 8px;
    background: $white;
    z-index: -1;
  }
}

.dialog-question {
  text-align: right;
  .message {
    position: relative;
    // float: right;
    color: $white;
    max-width: 225px;
    word-break: break-word;
    word-wrap: break-word;
    background: $primary;
    padding: 9px 16px;
    margin-right: 10px;
    border-radius: 10px;
  }
  .message::before {
    position: absolute;
    content: ' ';
    top: 14px;
    right: -11px;
    width: 0;
    height: 0;
    border-width: 9px 7px 0;
    border-style: solid;
    transform: rotate(-90deg);
    border-color: $primary transparent transparent;
  }
  .message::after {
    content: ' ';
    position: absolute;
    top: 0;
    right: -20px;
    width: 20px;
    height: 20px;
    border-radius: 8px;
    background: $white;
    z-index: -1;
  }
}

.debug-threshold {
  position: fixed;
  bottom: 0;
  width: 550px;
  height: 48px;
  line-height: 28px;
  padding: 10px 24px;
  border-top: 1px solid $grey2;
  span {
    cursor: pointer;
  }
}
@media screen and (max-width: 1601px) {
  .debug-threshold {
    width: 367px;
  }
}
.threshold-input {
  width: 50px;
}

.json-wrap {
  min-height: 250px;
  max-height: 500px;
  padding: 25px;
  border-radius: 12px;
  margin-bottom: 20px;
  // border: 1px solid $grey4;
  overflow-x: hidden;
  overflow-y: auto;
  word-break: break-word;
  word-wrap: break-word;
  background-color: $grey1-30 !important;
}
.dialog-bottom {
  height: 20px;
}
.ic-r-copy {
  position: absolute;
  right: 24px;
  top: 24px;
  z-index: 8;
  font-size: 16px;
  color: $grey4;
  cursor: pointer;
  &::after {
    content: '';
    display: inline-block;
    position: absolute;
    right: -8px;
    top: -8px;
    z-index: -5;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: $white;
    box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.1);
  }
}
.intent-info-wrap {
  margin-top: 10px;
  padding: 2px 16px 8px;
  border-top: 1px solid rgba(228, 231, 237, 1);
  // .el-tag {
  //   &:nth-of-type(3n-1) {
  //     background: #d3f2fe;
  //     color: #2aafe3;
  //   }
  //   &:nth-of-type(3n) {
  //     background: #dcf3eb;
  //     color: #54b39e;
  //   }
  //   &:nth-of-type(3n + 1) {
  //     background: #e7e8ff;
  //     color: #797dd5;
  //   }
  // }
  .tag-wrap {
    > span {
      margin-right: 2px;
      &:nth-child(3n-1) {
        .el-tag {
          background: #d3f2fe;
          color: #2aafe3;
        }
      }
      &:nth-child(3n) {
        .el-tag {
          background: #dcf3eb;
          color: #54b39e;
        }
      }
      &:nth-child(3n + 1) {
        .el-tag {
          background: #e7e8ff;
          color: #797dd5;
        }
      }
    }
  }
}
.el-tag {
  height: 24px;
  line-height: 24px;
  padding: 0 12px;
  margin: 12px 0 8px;
  border-radius: 12px;
  border: none;
}
// .el-tag + .el-tag {
//   margin-left: 2px;
// }
.msg-item {
  padding: 0 16px;
}
.intent-info-title {
  display: inline-block;
  margin-top: 9px;
  margin-right: 12px;
  width: 56px;
  font-weight: 600;
  color: $grey001;
}
.collapse-btn {
  margin-top: 12px;
  height: 22px;
  font-weight: 600;
  color: rgba(38, 38, 38, 1);
  line-height: 22px;
  cursor: pointer;
  display: flex;
  align-items: center;
  i {
    margin-right: 2px;
  }
  span {
    font-size: 14px;
    color: $grey001;
  }
}
.slot-tag {
  cursor: pointer;
}
.el-icon-d-arrow-right {
  font-size: 24px;
  color: $grey001;
  font-weight: 500;
}
.icon-zhankai {
  font-size: 18px;
  color: $grey001;
  font-weight: 500;
  transform: rotate(180deg);
  margin-right: 6px;
}

// 技能商店适配不同屏幕
@media screen and (max-width: 1601px) {
  .dialog-answer {
    font-size: 12px;
    .message {
      max-width: 270px;
    }
  }
  .dialog-question {
    font-size: 12px;
  }
  .view-button {
    font-size: 12px;
  }
  .el-tag {
    padding: 0 7px;
  }
  .send-wrap {
    font-size: 12px;

    :deep(input::-webkit-input-placeholder) {
      font-size: 12px;
    }
    :deep(input::-moz-placeholder) {
      /* Mozilla Firefox 19+ */
      font-size: 12px;
    }

    :deep(input:-ms-input-placeholder) {
      /* Internet Explorer 10-11 */
      font-size: 12px;
    }
  }
}
</style>
<style lang="scss">
.request-json {
  position: relative;
  .el-tabs--card > .el-tabs__header .el-tabs__nav {
    height: 40px;
    border: none;
  }
  .el-tabs--card > .el-tabs__header .el-tabs__item {
    padding: 0 15px;
    border-left: none;
    border-bottom: 1px solid #e4e7ed;
  }
  .el-tabs__item.is-top.is-active {
    border-top: 1px solid #e4e7ed;
    border-right: 1px solid #e4e7ed;
    border-left: 1px solid #e4e7ed;
    border-bottom-color: $white;
    border-radius: 6px 6px 0 0;
  }
}

.entity-item {
  margin-bottom: 8px;
}
.tag {
  display: inline-block;
  margin-right: 12px;
  width: 56px;
  font-weight: 600;
  color: $grey5;
}
</style>
