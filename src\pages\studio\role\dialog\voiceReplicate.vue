<template>
  <el-dialog :visible.sync="dialog.show" width="600px" title="声音复刻">
    <el-form
      :model="form"
      :rules="rules"
      ref="qaForm"
      label-position="left"
      label-width="100px"
      @submit.native.prevent
    >
      <el-form-item label="声音名称：" prop="name">
        <el-input v-model.trim="form.name" :disabled="saving" />
      </el-form-item>
    </el-form>
    <p class="voice-tip">
      朗读以下文本，录制音频并上传。支持pcm和wav格式文件，录音时长小于30s。如有IP声音复刻需求，请联系
      <span style="color: #009bff"><EMAIL></span>。
    </p>
    <el-upload
      class="voice-uplopad"
      drag
      name="audio"
      ref="upload"
      action=""
      accept=".pcm,.wav"
      :multiple="false"
      :limit="1"
      :file-list="fileList"
      :auto-upload="false"
      :http-request="uploadAudio"
      :on-change="handleChange"
      :disabled="saving"
    >
      <div class="voice-text" @click.stop>
        {{ voiceText }}
      </div>
      <p class="record-tip" v-if="status === 'ing'">
        录音中（{{ seconds }}秒）
      </p>
      <div class="voice-btn">
        <el-button
          size="small"
          type="primary"
          slot="trigger"
          v-show="status === 'end'"
          :disabled="saving"
          >上传声音</el-button
        >
        <el-button
          size="small"
          v-show="status === 'end'"
          @click.stop.prevent="start"
          :disabled="saving"
          >开始录音</el-button
        >
        <el-button
          size="small"
          type="primary"
          v-show="status === 'ing'"
          @click.stop.prevent="end"
          >结束录音</el-button
        >
      </div>
    </el-upload>

    <span slot="footer" class="dialog-footer">
      <el-button @click="dialog.show = false"> 取消 </el-button>
      <el-button
        class="dialog-btn"
        type="primary"
        @click="save"
        :loading="saving"
      >
        确定
      </el-button>
    </span>
  </el-dialog>
</template>
<script>
import Worker from '../tts/transcode.worker.js'
const transWorker = new Worker()
let timer
export default {
  name: 'voiceReplicate',
  props: {
    dialog: {
      type: Object,
      default: {},
    },
  },
  created() {
    let self = this
    transWorker.onmessage = function (event) {
      self.audioData = self.appendBuffer(self.audioData, event.data)
    }
  },
  data() {
    return {
      form: {
        name: '',
      },
      rules: {
        name: [this.$rules.required('名称不能为空', 'none')],
      },
      saving: false,
      voiceText:
        '贝加尔湖是世界上最古老、最深的淡水湖泊，位于俄罗斯西伯利亚地区， 湖水极其清澈透明，是世界上最纯净的湖泊之一。',
      fileList: [],
      status: 'end', //end结束状态 ing正在录音
      seconds: 0, //录音计时
      timer: null,
      audioData: new ArrayBuffer(),
    }
  },
  methods: {
    getTtsText() {
      this.$utils.httpGet(
        this.$config.api.AIUI_BOT_ROLE_TTS_TEXT,
        {},
        {
          success: (res) => {
            if (res.data) {
              this.voiceText = res.data.text
            }
          },
          error: (err) => {},
        }
      )
    },
    save() {
      this.$refs.qaForm.validate((valid) => {
        if (valid) {
          if (this.fileList.length === 0) {
            this.$message.error('请上传声音文件或录制一段音频～')
            return
          }
          if (this.fileList[0]['type'] === 'record') {
            // 录音的文件
            const formData = new FormData()
            formData.append('name', this.form.name)
            formData.append(
              'audio',
              this.fileList[0]['audioBlobData'],
              this.fileList[0].name
            )
            this.handleUpload(formData)
          } else {
            // 本地音频文件
            this.$refs.upload.submit()
          }
        }
      })
    },
    uploadAudio(params) {
      console.log('上传参数', params)
      const file = params.file
      const reader = new FileReader()
      let self = this
      reader.onload = () => {
        const blob = new Blob([reader.result], { type: 'audio/pcm' })
        const formData = new FormData()
        formData.append('name', this.form.name)
        formData.append('audio', blob, file.name)
        self.handleUpload(formData)
      }
      reader.onerror = (err) => {
        console.error('读取文件错误', err)
        self.$message.error('读取音频文件失败')
      }
      reader.readAsArrayBuffer(file) // 读取为 ArrayBuffer
    },
    handleChange(file, list) {
      let fileType = file.name.split('.').pop().toLowerCase()
      if (fileType != 'pcm' && fileType != 'wav') {
        this.$message.error('当前仅支持上传pcm及wav格式文件')
        this.fileList = []
        return
      }
      this.fileList = list
    },
    handleUpload(formData) {
      this.saving = true
      this.$utils.httpPost(this.$config.api.AIUI_ROLE_VOICE_CLONE, formData, {
        config: {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        },
        success: (res) => {
          this.saving = false
          this.$message.success('音频复刻成功')
          this.dialog.show = false
        },
        error: (err) => {
          this.saving = false
          this.$message.error(err.data)
          console.error('上传错误', err)
        },
      })
    },
    start() {
      this.seconds = 0
      this.status = 'ing'
      this.timer = setInterval(() => {
        this.seconds += 1
      }, 1000)
      this.recorderStart()
      this.countDown(30, this.stop)
    },
    end() {
      if (this.seconds < 10) {
        this.$message.warning('录音时间需大于10s')
        return
      }
      clearInterval(this.timer)
      this.timer = null
      this.recorderStop()
    },
    recorderStart() {
      if (!this.audioContext) {
        this.recorderInit()
      } else {
        this.audioContext.resume()
      }
    },
    recorderStop() {
      // safari下suspend后再次resume录音内容将是空白，设置safari下不做suspend
      if (
        !(
          /Safari/.test(navigator.userAgent) &&
          !/Chrome/.test(navigator.userAgent)
        )
      ) {
        console.log('------recorderStop---------')
        this.audioContext && this.audioContext.suspend()
      }
      this.status = 'end'
      // this.downloadPCM(this.audioData)
      // console.log('获取的录音数据', this.audioData, this.$refs.upload.handleStart);
      const pcmBlob = new Blob([this.audioData], { type: 'audio/pcm' })
      // const pcmFile = new File([pcmBlob], `录音数据_${this.$utils.dateFormat(new Date())}.pcm`, {
      //   type: 'audio/pcm',
      // });
      this.fileList = [
        {
          name: `复刻录音数据_${this.$utils.dateFormat(new Date())}.pcm`,
          url: '',
          audioBlobData: pcmBlob,
          type: 'record',
        },
      ]
      this.audioData = new ArrayBuffer()
    },
    // 初始化浏览器录音
    recorderInit() {
      navigator.getUserMedia =
        navigator.getUserMedia ||
        navigator.webkitGetUserMedia ||
        navigator.mozGetUserMedia ||
        navigator.msGetUserMedia

      // 创建音频环境
      try {
        this.audioContext = new (window.AudioContext ||
          window.webkitAudioContext)()

        this.audioContext.resume()
        if (!this.audioContext) {
          alert('浏览器不支持webAudioApi相关接口')
          return
        }
      } catch (e) {
        if (!this.audioContext) {
          alert('浏览器不支持webAudioApi相关接口')
          return
        }
      }

      // 获取浏览器录音权限
      if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
        navigator.mediaDevices
          .getUserMedia({
            audio: true,
            video: false,
          })
          .then((stream) => {
            getMediaSuccess(stream)
          })
          .catch((e) => {
            getMediaFail(e)
          })
      } else if (navigator.getUserMedia) {
        navigator.getUserMedia(
          {
            audio: true,
            video: false,
          },
          (stream) => {
            getMediaSuccess(stream)
          },
          function (e) {
            getMediaFail(e)
          }
        )
      } else {
        if (
          navigator.userAgent.toLowerCase().match(/chrome/) &&
          location.origin.indexOf('https://') < 0
        ) {
          alert(
            'chrome下获取浏览器录音功能，因为安全性问题，需要在localhost或127.0.0.1或https下才能获取权限'
          )
        } else {
          alert('无法获取浏览器录音功能，请升级浏览器或使用chrome')
        }
        this.audioContext && this.audioContext.close()
        return
      }
      // 获取浏览器录音权限成功的回调
      let getMediaSuccess = (stream) => {
        console.log('getMediaSuccess', this.audioContext.sampleRate)
        // 创建一个用于通过JavaScript直接处理音频
        this.scriptProcessor = this.audioContext.createScriptProcessor(0, 1, 1)
        this.scriptProcessor.onaudioprocess = (e) => {
          // 去处理音频数据
          if (this.status === 'ing') {
            transWorker.postMessage({
              audioData: e.inputBuffer.getChannelData(0),
              sampleRate: this.audioContext.sampleRate,
            })
          }
        }
        // 创建一个新的MediaStreamAudioSourceNode 对象，使来自MediaStream的音频可以被播放和操作
        this.mediaSource = this.audioContext.createMediaStreamSource(stream)
        // 连接
        this.mediaSource.connect(this.scriptProcessor)
        this.scriptProcessor.connect(this.audioContext.destination)
      }

      let getMediaFail = (e) => {
        alert('请求麦克风失败')
        console.log(e)
        this.audioContext && this.audioContext.close()
        this.audioContext = undefined
      }
    },
    /**
     * Creates a new Uint8Array based on two different ArrayBuffers
     *
     * @private
     * @param {ArrayBuffers} buffer1 The first buffer.
     * @param {ArrayBuffers} buffer2 The second buffer.
     * @return {ArrayBuffers} The new ArrayBuffer created out of the two.
     */
    appendBuffer(buffer1, buffer2) {
      var tmp = new Uint8Array(buffer1.byteLength + buffer2.byteLength)
      tmp.set(new Uint8Array(buffer1), 0)
      tmp.set(new Uint8Array(buffer2), buffer1.byteLength)
      return tmp.buffer
    },
    countDown(time, callback) {
      if (time <= 0) {
        return
      }
      timer = setInterval(() => {
        time--
        if (time <= 0) {
          clearInterval(timer)
          callback && callback()
        } else {
        }
      }, 1000)
    },
  },
  watch: {
    'dialog.show': function (val, oldVal) {
      let self = this
      if (val) {
        this.fileList = []
        this.form.name = ''
        this.getTtsText()
      }
    },
    fileList: function (val, oldVal) {
      // console.log('fileList', val);
    },
  },
}
</script>
<style lang="scss" scoped>
.voice-tip {
  font-size: 12px;
  font-family: PingFang SC, PingFang SC-400;
  font-weight: 400;
  color: #202020;
  margin-bottom: 26px;
}
.record-tip {
  font-size: 12px;
  color: #86909c;
  margin-top: 26px;
}
.voice-uplopad {
  width: 100%;
  position: relative;
  margin-bottom: 20px;
  :deep(.el-upload-dragger) {
    width: 100%;
    height: 244px;
    background: linear-gradient(0deg, #ffffff 0%, #f3faff);
    border: 1px dashed #e0e0e0;
    border-radius: 8px;
    padding: 75px 32px 15px 32px;
  }
  .voice-text {
    font-size: 14px;
    font-family: PingFang SC, PingFang SC-500;
    font-weight: 500;
    text-align: center;
  }
  .voice-btn {
    display: flex;
    justify-content: center;
    gap: 10px;
    position: absolute;
    width: 100%;
    bottom: 12px;
    left: 0;
  }
}
</style>
