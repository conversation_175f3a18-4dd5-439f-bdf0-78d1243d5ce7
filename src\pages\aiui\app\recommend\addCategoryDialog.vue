<template>
  <el-dialog
    class="add-category-dialog"
    :title="`添加${dict[dialog.type]}类`"
    :visible.sync="dialog.show"
    :append-to-body="true"
    width="570px"
  >
    <div class="div-add-category-dialog">
      <ul>
        <li>
          <p>名称：</p>
          <el-input
            class="content-name"
            :class="{ 'content-name-tips-bd-show': contentNameShow }"
            v-model="contentName"
            :placeholder="`请输入${dict[dialog.type]}类名称，不超过20个字符`"
            @input="checkChange($event)"
            @blur="checkBlank($event)"
          ></el-input>
          <p
            class="content-name-tips"
            :class="{ 'content-name-tips-show': contentNameShow }"
          >
            {{ `请输入${dict[dialog.type]}类名称，不超过20个字符` }}
          </p>
        </li>
        <li>
          <el-button
            class="template-btn"
            size="small"
            type="primary"
            style="min-width: 80px"
            @click="btnClick"
          >
            {{ '添加' }}
          </el-button>
          <el-button
            class="template-btn"
            size="small"
            style="min-width: 80px"
            @click="cancelClick"
          >
            {{ '取消' }}
          </el-button>
        </li>
      </ul>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'addCategoryDialog',
  props: {
    dialog: {
      type: Object,
      default: {
        show: false,
      },
    },
    skillId: '',
    outNumber: '',
  },
  data() {
    return {
      contentName: null,
      contentNameShow: false,
      dict: {
        1: '一级分',
        2: '子',
      },
    }
  },
  watch: {
    'dialog.show'() {
      this.contentName = null
      this.contentNameShow = false
    },
  },
  /*computed: {
        'dialog.show': {
          get () {
            return this.dialog.show
          },
          set (val) {
            this.$emit('update:dialog.show', val)
          }
        }
      },*/
  methods: {
    checkChange(e) {
      if (e) {
        this.contentNameShow = false
      } else {
        this.contentNameShow = true
      }
    },
    checkBlank(e) {
      if (e.target.value) {
        this.contentNameShow = false
      } else {
        this.contentNameShow = true
        return
      }
      if (
        e.target.value.length >
        20 /*|| /[^\u4E00-\u9FA5]/g.test(e.target.value)*/
      ) {
        this.contentNameShow = true
        this.$message.error('长度不超过20个字符')
        return
      }
    },
    btnClick() {
      if (
        !this.contentName ||
        (this.contentName &&
          this.contentName.length >
            20 /*|| /[^\u4E00-\u9FA5]/g.test(this.contentName)*/)
      ) {
        this.contentNameShow = true
        return
      }
      this.$emit(
        'addCategory',
        this.dialog.type,
        this.dialog.scene,
        this.dialog.parentId,
        this.contentName,
        () => {
          this.dialog.show = false
          //this.$emit('getSceneList')
          //this.$emit('getContentTypesList')
        }
      )
    },
    cancelClick() {
      this.dialog.show = false
    },
  },
}
</script>

<style scoped lang="scss">
.div-add-category-dialog {
  padding-bottom: 3%;
  position: relative;
  > ul > li {
    display: flex;
    justify-content: center;
    margin-bottom: 5%;
    > p {
      width: 12%;
      line-height: 3;
      font-size: 15px;
      text-align: center;
      vertical-align: middle;
    }
    .content-name {
      width: 80%;
      &-tips {
        color: $warning;
        position: absolute;
        display: none;
        visibility: hidden;
        width: 100%;
        margin: 0 auto;
        top: 22%;
        justify-content: left;
        left: 19.5%;
        &-show {
          display: flex;
          visibility: visible;
        }
        :deep(&-bd-show > input) {
          border-color: #f5222d;
        }
      }
    }
  }
}
</style>
