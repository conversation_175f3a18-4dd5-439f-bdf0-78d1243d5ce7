<template>
  <el-dialog
    title="上传比对实体"
    :visible.sync="dialog.show"
    :close-on-click-modal="false"
    width="600px"
  >
    <el-form
      class=""
      :model="ruleForm"
      :rules="rules"
      ref="uploadAndCompareForm"
    >
      <el-form-item label="选择实体" prop="entity">
        <el-select
          v-model="ruleForm.entity"
          placeholder="请选择"
          @change="onEntityChange"
        >
          <el-option
            v-for="item in options"
            :key="item.name"
            :label="item.value"
            :value="item.name"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="选择文件" prop="file">
        <el-upload
          :multiple="false"
          ref="upload"
          accept="text/plain"
          :auto-upload="false"
          class=""
          drag
          :show-file-list="false"
          :with-credentials="true"
          :before-upload="beforeUpload"
          :on-success="onSuccess"
          :on-error="onError"
          :on-progress="onProgress"
          :on-change="onChange"
          :file-list="fileList"
          :action="`${this.$config.server}/aiui/web/entity/compare/import?entityId=${this.$route.params.entityId}&name=${this.ruleForm.entity}&id=${this.id}`"
        >
          <i class="el-icon-upload"></i>
          <div class="">点击或将文件拖拽到这里导入</div>
          <div class="upload-tip">
            单次导入文件不超过2W条，文件大小不超过5MB，单日比对不超过10次。
          </div>
        </el-upload>
        <div class="file-area">
          <div class="info-container">
            <i v-if="fileList.length > 0" class="ic-r-file"></i>
            <span v-if="fileList.length > 0">{{ fileList[0].name }}</span>
          </div>

          <div class="progress-container" v-if="percentage">
            <el-progress
              :text-inside="true"
              :stroke-width="16"
              :percentage="percentage"
            ></el-progress>
          </div>
        </div>
      </el-form-item>
    </el-form>
    <div slot="footer">
      <el-button type="primary" plain size="medium" @click="nextStep"
        >下一步</el-button
      >
      <el-button size="medium" @click="onClose">取消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { mapGetters } from 'vuex'
import InfoTip from './infoTip.vue'

export default {
  components: {
    InfoTip,
  },
  props: {
    dialog: {
      type: Object,
      default: () => {},
    },
    options: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      saving: false,
      rules: {
        // entity: [{ required: true, message: '请选择实体', trigger: 'none' }],
        entity: [{ required: true, message: '请选择实体' }],
        file: [{ required: true, message: '请选择文件', trigger: 'change' }],
      },
      ruleForm: {
        entity: '',
        file: '',
      },
      id: '',
      // fileName: '',
      percentage: 0,
      fileList: [],
    }
  },
  computed: {
    ...mapGetters({
      subAccount: 'user/subAccount',
    }),
  },
  watch: {
    'dialog.show': function (val, oldVal) {
      if (val) {
        if (this.options && this.options[0] && this.options[0].name) {
          this.ruleForm.entity = this.options[0].name
          this.id = this.options[0].id
        }
      } else {
        this.reset()
      }
    },
  },
  methods: {
    reset() {
      this.fileList = []
      this.ruleForm.file = null
      this.ruleForm.entity = ''
      this.percentage = 0
      this.$nextTick(() => {
        this.$refs.uploadAndCompareForm.clearValidate()
      })
    },

    beforeUpload(file) {
      console.log('beforeUpload', file)
    },
    onSuccess(response, file, fileList) {
      console.log('onSuccess', response, file, fileList)
      // element upload组件 再次点击上传时，只会上传status 为ready的文件
      fileList.forEach((file) => {
        file.status = 'ready'
      })
      if (response.code === '0') {
        this.loading.close()
        this.$msgbox({
          title: null,
          type: 'success',
          showClose: false,
          closeOnClickModal: false,
          message: this.$createElement(InfoTip, {
            props: {
              count: response.data.count,
              entityId: String(this.$route.params.entityId),
            },
          }),
        }).then((action) => {
          this.onClose()
        })
      } else {
        this.loading.close()
        this.$message.error(response.desc)
      }
    },
    onError(err, file, fileList) {
      console.log('onError', err, file, fileList)
    },
    onProgress(event, file, fileList) {
      console.log('onProgress', event, file, fileList)
      this.percentage = event.percent
      if (event.percent == 100) {
        // 提示用户正在分析中
        this.loading = this.$loading({
          lock: true,
          text: '文件上传成功，正在分析中...',
          spinner: 'el-icon-loading',
          background: 'hsla(0,0%,100%,.9)',
        })
      }
    },
    onChange(file, fileList) {
      console.log('onChange', file, fileList)
      // 设置上传列表，如果不设置

      if (file.status === 'ready') {
        if (file.size === 0) {
          return this.$message.error('上传文件不能为空')
        }
        if (file.size > 5 * 1024 * 1024) {
          return this.$message.error('上传文件不得大于5M')
        }
        this.fileList = [file]
        // this.fileName = file.name
        this.ruleForm.file = file
        this.$refs.uploadAndCompareForm.validate()
        this.percentage = 0
      }
    },
    nextStep() {
      this.$refs.uploadAndCompareForm.validate((valid) => {
        if (valid) {
          this.$refs.upload.submit()
        } else {
          console.log('error-------------')
          return false
        }
      })
    },
    onEntityChange(val) {
      console.log('onEntityChange', val)
      this.id = this.options.find((item) => item.name === val).id
    },
    onClose() {
      this.dialog.show = false
    },
  },
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="scss">
.el-upload-dragger {
  height: auto !important;
  width: 400px;
}
.upload-tip {
  font-size: 12px;
}
.file-area {
  display: flex;
  align-items: center;
  padding-left: 66px;
  .info-container {
    // width: 40%;
  }
  .progress-container {
    margin-left: 10px;
    width: 40%;
  }
}
</style>
