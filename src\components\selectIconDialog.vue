<template>
  <el-dialog
    title="更换头像"
    :visible.sync="dialogVisible"
    width="600px"
    @close="handleClose"
  >
    <div class="tabs_container">
      <el-tabs v-model="activeCategory">
        <el-tab-pane
          v-for="(category, i) in categories"
          :key="i"
          :label="category"
          :name="category"
        ></el-tab-pane>
      </el-tabs>
    </div>
    <div class="icon_container">
      <div
        v-for="(item, index) in filteredIconList"
        :key="index"
        class="icon_item"
        :class="{
          icon_item_selected: selectedIconIndex === getOriginalIndex(item),
        }"
        @click="selectIcon(getOriginalIndex(item))"
        @mouseover="hoverIndex = getOriginalIndex(item)"
        @mouseleave="hoverIndex = -1"
      >
        <el-image :src="item.url" fit="cover" class="icon_image"> </el-image>
        <div
          v-if="selectedIconIndex === getOriginalIndex(item)"
          class="icon_check"
        >
          <i class="el-icon-check"></i>
        </div>
      </div>
      <div v-if="loading" class="loading_container">
        <i class="el-icon-loading"></i>
      </div>
      <div
        v-if="filteredIconList.length === 0 && !loading"
        class="empty_container"
      >
        <span>暂无可用头像</span>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleConfirm">确认</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: 'SelectIconDialog',
  data() {
    return {
      pluginIconKey: null,
      dialogVisible: false,
      iconData: {}, // 存储原始接口返回的对象数据
      iconList: [], // 存储所有图标的扁平数组
      selectedIconIndex: -1,
      hoverIndex: -1,
      loading: false,
      selectedIcon: null,
      originalIcon: null,
      activeCategory: '全部',
      categories: ['全部'], // 将从接口获取的分类动态填充
    }
  },
  computed: {
    filteredIconList() {
      if (this.activeCategory === '全部') {
        return this.iconList
      } else {
        return this.iconData[this.activeCategory] || []
      }
    },
  },
  created() {
    this.getIconList()
  },
  methods: {
    show(pluginIconKey) {
      this.pluginIconKey = pluginIconKey
      this.dialogVisible = true

      const iconIndex = this.iconList.findIndex(
        (icon) => icon.code === pluginIconKey
      )
      if (iconIndex !== -1) {
        this.originalIcon = this.iconList[iconIndex]
        this.selectIcon(iconIndex)

        this.$nextTick(() => {
          const iconElements = document.querySelectorAll('.icon_item')
          if (iconElements[iconIndex]) {
            iconElements[iconIndex].scrollIntoView({
              behavior: 'smooth',
              block: 'center',
            })
          }
        })
      }
    },
    initDialogData() {
      this.dialogVisible = false
      this.selectedIconIndex = -1
      this.hoverIndex = -1
      this.selectedIcon = null
      this.originalIcon = null
      this.activeCategory = '全部'
    },
    handleClose() {
      if (this.originalIcon) {
        console.log('originalIcon', this.originalIcon)
        this.$emit('updatePluginIcon', this.originalIcon.url)
      }

      this.initDialogData()
    },
    selectIcon(index) {
      this.selectedIconIndex = index
      this.selectedIcon = this.iconList[index]
      this.$emit('updatePluginIcon', this.selectedIcon.url)
    },
    handleConfirm() {
      if (this.selectedIcon) {
        this.$emit('confirm', this.selectedIcon)
      }
      this.initDialogData()
    },
    getIconList() {
      this.loading = true
      this.iconList = []
      this.iconData = {}

      this.$utils.httpGet(
        this.$config.api.AGENT_ICON_LIST,
        {},
        {
          success: (res) => {
            if (res.code === 0 || res.code === '0') {
              // 处理新的数据格式
              if (res.data && typeof res.data === 'object') {
                this.iconData = res.data

                // 更新分类列表
                this.categories = [
                  '全部',
                  ...Object.keys(res.data).filter((key) => key !== '全部'),
                ]

                // 构建扁平的图标列表用于全局索引
                this.iconList = []
                if (res.data['全部'] && Array.isArray(res.data['全部'])) {
                  // 如果接口直接返回了"全部"分类，则直接使用
                  this.iconList = res.data['全部']
                } else {
                  // 否则从各个分类中合并构建"全部"列表
                  const allIcons = new Map()
                  Object.keys(res.data).forEach((category) => {
                    if (Array.isArray(res.data[category])) {
                      res.data[category].forEach((icon) => {
                        if (!allIcons.has(icon.code)) {
                          allIcons.set(icon.code, icon)
                        }
                      })
                    }
                  })
                  this.iconList = Array.from(allIcons.values())
                }
              }
            } else {
              this.$message.error(res.desc || '获取头像列表失败')
            }
            this.loading = false
          },
          error: (err) => {
            this.$message.error(err?.desc || '获取头像列表失败')
            this.loading = false
          },
        }
      )
    },

    getOriginalIndex(item) {
      return this.iconList.findIndex((icon) => icon.code === item.code)
    },
  },
}
</script>

<style lang="scss" scoped>
:deep(.el-dialog__body) {
  padding-bottom: 16px;
}
.tabs_container {
  :deep(.el-tabs__active-bar) {
    display: none;
  }
  :deep(.el-tabs__nav-wrap::after) {
    display: none;
  }
  :deep(.el-tabs) {
    .el-tabs__item {
      transition: all 0.3s;
      border-radius: 4px;
      padding: 0 16px;
      height: 32px;
      line-height: 32px;

      &.is-active {
        background-color: #e1f3ff;
        color: inherit; /* 恢复默认字体颜色 */
      }

      &:hover {
        color: inherit; /* hover时保持字体颜色不变 */
      }

      &:first-child {
        padding-left: 16px; /* 确保第一个tab有左边距 */
      }
    }
  }
}

.icon_container {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 16px;
  // padding-bottom: 16px;
  max-height: 300px;
  position: relative;
  overflow-y: auto;
}

.icon_item {
  position: relative;
  width: 100%;
  aspect-ratio: 1/1;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  border: 1.5px solid transparent;
  transition: all 0.3s;

  &:hover {
    border-color: $primary;
  }
}

.icon_item_selected {
  border-color: $primary;
}

:deep(.icon_image) {
  width: 100%;
  height: 100%;
  img {
    width: 100%;
    height: 100%;
  }
}

.icon_check {
  position: absolute;
  top: 4px;
  right: 4px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: $primary;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 12px;
}

.loading_container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 32px;
  color: $primary;
}

.empty_container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #909399;
  font-size: 14px;
}
</style>
