<template>
  <div class="os-scroll"> 
    <handle-platform-top></handle-platform-top>
    <div class="handle-platform-content">
      <div class="search-wrap">
        <div>
          <span class="search-item-title">操作时间</span>
          <date-time-picker style="display:inline-block;"
            ref="startDatePicker"
            @setTime="setStartTime"
            ></date-time-picker> <span>~</span>
          <date-time-picker 
            style="display:inline-block;"
            ref="endDatePicker"            
            @setTime="setEndTime"></date-time-picker>
        </div>
        <div style="font-size: 0;">
          <el-select
            class="logs-chart-type"
            style="width: 120px;"
            v-model="theme" size="medium" :title="theme">
            <el-option
              v-for="item in themes"
              :key="item"
              :label="item"
              :value="item">
            </el-option>
          </el-select>
          <el-select
            class="logs-chart-type-item"
            style="width: 165px;"
            filterable
            v-model="themeId" size="medium" placeholder="请输入关键字" :title="themeName">
            <el-option
              v-for="item in themeList"
              :key="item.categoryId"
              :label="item.zhName"
              :value="item.categoryId"
              @click.native="setThemeName(item.zhName)">
            </el-option>
          </el-select>
        </div>
        <div>
          <span class="search-item-title">操作事件</span>
          <el-select
            style="width: 120px;"
            v-model="incident" size="medium" :title="incident" placeholder="请选择">
            <el-option
              v-for="item in incidents"
              :key="item"
              :label="item"
              :value="item">
            </el-option>
          </el-select>
        </div>
        <div>
          <el-button class="dialog-btn"
            type="primary"
            size="small"
            style="min-width: 80px;"
            @click="getLogs(1)">搜索</el-button>
          <el-button class="dialog-btn"
            size="small"
            style="min-width: 80px;"
            @click="reset">重置</el-button>
        </div>
      </div>
      <os-table
        :tableData="tableData"
        style="margin-bottom: 56px;"
        class="accounts-table mgt24"
        @change="getLogs">
        <el-table-column
          prop="operatorTime"
          width="200"
          label="操作时间">
          <template slot-scope="scope">
            <div>{{scope.row.operatorTime | date}}</div>
          </template>
        </el-table-column>
        <el-table-column
          prop="operator"
          width="180"
          label="操作帐号">
        </el-table-column>
        <el-table-column
          prop="remark"
          width="180"
          label="帐号备注">
          <template slot-scope="scope">
            <span v-if="scope.row.remark">{{scope.row.remark}}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="zhName"
          width="200"
          :label="theme">
          <template slot-scope="scope">
            <div v-if="scope.row.zhName" class="text-blod" :title="scope.row.zhName">{{scope.row.zhName}}</div>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="incident"
          width="160"
          label="操作事件">
        </el-table-column>
        <el-table-column
          prop="content"
          width="280"
          label="操作内容">
          <template slot-scope="scope">
            <div class="auxiliarie-name" :title="scope.row.content">{{scope.row.content}}</div>
          </template>
        </el-table-column>
      </os-table>
    </div>
  </div>
</template>

<script>
  import HandlePlatformTop from './top.vue'
  import DateTimePicker from './dateTimePicker'
  export default {
    name: 'cooperation-logs',
    data() {
      return {
        tableData: {
          loading: true,
          total: 0,
          page: 1,
          size: 10,
          list: []
        },
        theme: '技能',
        themes: [
          '技能',
          '实体/辅助词',
          '应用'
        ],
        themeId: '',
        themeName: '',
        themeList: [],
        themeType: '',
        skills: [],
        entities: [],
        apps: [],
        incidents: [],
        initIncidents: [],
        skillIncidents: [],
        entitiyIncidents: [],
        appIncidents: [],
        incident: '全部',
        startTime: '',
        endTime: ''
      }
    },
    watch: {
      theme(val) {
        if (val == '技能') {
          this.themeList = this.skills
          this.themeId = ''
          this.themeType = 1
          this.incidents = this.skillIncidents
        } else if (val == '应用') {
          this.themeList = this.apps
          this.themeId = ''
          this.themeType = 4
          this.incidents = this.appIncidents
        } else {
          this.themeList = this.entities
          this.themeId = ''
          this.themeType = 2
          this.incidents = this.entitiyIncidents
        }
      }
    },
    created() {
      this.getLogs(1)
      this.getLogSearch()
    },
    methods: {
      getLogs(page){
        let self = this
        if(!this.checkTime()) return
        let data = {
          pageIndex: page || self.tableData.page,
          pageSize: self.tableData.size,
          incident: self.incident == '全部' ? '' : self.incident,
          type: self.themeType,
          categoryId: self.themeId,
          startTime: self.startTime,
          endTime: self.endTime
        }
        self.tableData.loading = true
        self.tableData.list = []
        this.$utils.httpPost(this.$config.api.COOP_LOGS, data, {
          success: (res) => {
            if(res.data && res.data.logs) {
              self.tableData.list = res.data.logs
            }
            self.tableData.total = res.data.count
            self.tableData.page = res.data.pageIndex
            self.tableData.size = res.data.pageSize
            self.tableData.loading = false
          },
          error: (err) => {
            self.tableData.loading = false
          }
        })
      },
      getLogSearch() {
        let self = this
        self.initIncidents = [ ]
        this.$utils.httpPost(this.$config.api.COOP_LOGS_SEARCH, '', {
          success: (res) => {
            self.skills = [{categoryId:'', zhName:'全部'}, ...res.data.skills] 
            self.entities = [{categoryId:'', zhName:'全部'}, ...res.data.entities]
            self.apps = [{categoryId:'', zhName:'全部'}, ...res.data.apps]
            self.themeList = self.skills
            if(res.data.incidents.hasOwnProperty('skill')) {
              self.skillIncidents = ['全部', ...res.data.incidents.skill.incidents]
            } else {
              self.themes = self.themes.filter(item => {
                return item !== '技能'
              })
            }
            if(res.data.incidents.hasOwnProperty('entity')) {
              self.entitiyIncidents = ['全部', ...res.data.incidents.entity.incidents]
            } else {
              self.themes = self.themes.filter(item => {
                return item !== '实体/辅助词'
              })
            }
            if (res.data.incidents.hasOwnProperty('app')) {
              self.appIncidents = ['全部', ...res.data.incidents.app.incidents]
            } else {
              self.themes = self.themes.filter(item => {
                return item != '应用'
              })
            }
            let skillIntentsTmp = self.skillIncidents.length ? self.skillIncidents.slice(1) : []
            let entityIntentsTmp = self.entitiyIncidents.length ? self.entitiyIncidents.slice(1) : []
            let appIntentsTmp = self.appIncidents.length ? self.appIncidents.slice(1) : []
            self.initIncidents = ['全部',  ...skillIntentsTmp, ...entityIntentsTmp, ...appIntentsTmp]
            self.incidents = self.initIncidents
          },
          error: (err) => {
          }
        })
      },
      setThemeName(val) {
        this.themeName = val
      },
      setStartTime(val) {
        this.startTime = val != '-' ? val : ''
        this.checkTime()
      },
      setEndTime(val) {
        this.endTime = val != '-' ? val : ''
        this.checkTime()
      },
      checkTime() {
        let endTimestamp, startTimestamp
        if(this.endTime && this.startTime) {
          endTimestamp = new Date(this.endTime).getTime()
          startTimestamp = new Date(this.startTime).getTime()
          if(endTimestamp < startTimestamp) {
            this.$message.warning('开始时间须早于结束时间')
            return false
          } else {
            return true
          }
        }
        return true
      },
      reset() {
        let self = this
        self.startTime = ''
        self.endTime = ''
        self.theme = '技能'
        self.themeId = ''
        self.themeList = self.skills
        self.incident = '全部'
        self.$refs.startDatePicker.clearTime()
        self.$refs.endDatePicker.clearTime()
      }
    },
    components: { HandlePlatformTop, DateTimePicker }
  }
</script>

<style lang="scss" scoped>
.handle-platform-content {
  width: 1200px;
  margin: auto;
}
.search-wrap {
  display: flex;
  justify-content: space-between;
  line-height: 36px;
  color:#8c8c8c;
}
.search-item-title {
  margin-right: 4px;
}
</style>
<style lang="scss">
.logs-chart-type {
  .el-input__inner {
    border-right-color: transparent;
    border-radius: 2px 0 0 2px;
  }
}
.logs-chart-type-item {
  .el-input__inner {
    border-radius: 0 2px 2px 0;
  }
}
</style>