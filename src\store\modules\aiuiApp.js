// @服务信息
import { utils } from '@U'
import { api } from '@/config'
import dicts from '@M/dicts'
import Router from '../../router'
import Context from '@U/AIUIState/Context'

export default {
  namespaced: true,
  state: {
    id: '',
    app: {},
    limitCount: {},
    currentScene: {},
    configChange: false,
    translateConfig: {},
    qcAuth: false,
    sceneRoleId: '',
    subAccountAppAuths: {},
    subAccountSkillAuths: {},
    subAccountHasCreateAuth: false,
    subAccountHasCreateSkillAuth: false,
    subAccountMainAccountInfo: {},

    // app 链路状态上下文
    context: null,

    subAccountEditable: true,

    sceneList: [],
  },

  actions: {
    setId({ state, commit, rootState }, appId) {
      commit('setId', appId)
    },
    setApp({ state, commit, rootState }, appId) {
      commit('setId', appId)
      utils.httpGet(
        api.AIUI_APPINFO,
        {
          appid: appId,
        },
        {
          success: (res) => {
            commit('setApp', res.data)
          },
          error: (err) => {
            Router.push({ name: 'apps' })
          },
        }
      )
    },
    setAppInfo({ state, commit, rootState }, app) {
      commit('setApp', app)
    },
    setLimitCount({ commit }) {
      utils.httpGet(
        api.USER_LIMIT_COUNT,
        {},
        {
          success: (res) => {
            commit('setLimitCount', res.data)
          },
          error: (err) => {
            commit('setLimitCount', {})
          },
        }
      )
    },
    setCurrentScene({ state, commit, rootState }, sceneVal = {}) {
      let scene = {
        ...sceneVal,
        point: (sceneVal.point || '')
          .split(',')
          .filter((p) => p !== '10')
          .join(','),
      }
      commit('setCurrentScene', scene)

      if (scene?.sos !== true && scene?.point) {
        const context = new Context()
        context.addSwitches(scene?.point)
        commit('setContext', context)
      } else {
        commit('setContext', null)
      }
    },

    addSwitches({ state, commit, rootState }, switches) {
      // 创建一个新的 context 对象或克隆现有的 context
      const context = state.context
        ? new Context(state.context.getState(), state.context.getSwitches())
        : new Context()
      // 在克隆的对象上操作
      context.addSwitches(switches)
      // 获取当前状态值，发接口保存
      const point = context.getStateValue()
      let data = {
        appid: state.app.appid,
        point,
        sceneId: state.currentScene.sceneBoxId,
        sceneName: state.currentScene.sceneBoxName,
      }
      utils.httpPost(api.AIUI_SCENE_SAVEAASCENE, data, {
        success: (res) => {
          if (res.flag) {
            commit('setContext', context)
            const newSceneList = state.sceneList.map((item) => {
              if (item.sceneBoxId === state.currentScene.sceneBoxId) {
                return {
                  ...item,
                  point,
                }
              } else {
                return { ...item }
              }
            })
            commit('setSceneList', newSceneList)
          } else {
          }
        },
        error: (err) => {},
      })
    },

    removeSwitch({ state, commit, rootState }, switches) {
      // 创建一个新的 context 对象或克隆现有的 context
      console.log(state.context.getState())
      console.log(state.context.getSwitches())

      const context = state.context
        ? new Context(state.context.getState(), state.context.getSwitches())
        : new Context()
      // 在克隆的对象上操作
      context.removeSwitch(switches)

      // 获取当前状态值，发接口保存
      const point = context.getStateValue()
      let data = {
        appid: state.app.appid,
        point,
        sceneId: state.currentScene.sceneBoxId,
        sceneName: state.currentScene.sceneBoxName,
      }
      utils.httpPost(api.AIUI_SCENE_SAVEAASCENE, data, {
        success: (res) => {
          if (res.flag) {
            commit('setContext', context)
            // commit('setCurrentScene', { ...state.currentScene, point })
            const newSceneList = state.sceneList.map((item) => {
              if (item.sceneBoxId === state.currentScene.sceneBoxId) {
                return {
                  ...item,
                  point,
                }
              } else {
                return { ...item }
              }
            })
            commit('setSceneList', newSceneList)
          } else {
          }
        },
        error: (err) => {},
      })
    },

    setConfigChange({ state, commit, rootState }, flag) {
      commit('setConfigChange', flag)
    },
    setTranslateConfig({ state, commit, rootState }, config) {
      commit('setTranslateConfig', config)
    },
    setQcAuth({ state, commit }) {
      utils.httpGet(
        api.AIUI_APP_QC_AUTH,
        {},
        {
          success: (res) => {
            if (res.flag && res.data) {
              commit('setQcAuth', res.data.auth)
            }
          },
          error: (err) => {},
        }
      )
    },
    setSubAccountAppAuths({ commit }) {
      utils.httpGet(
        api.SUB_USER_SKILL_AUTH,
        {},
        {
          success: (res) => {
            commit('setSubAccountAppAuths', res.data.editapp)
            commit('setSubAccountSkillAuths', res.data.edit)
            commit('setSubAccountHasCreateAuth', res.data.createapp)
            commit('setSubAccountHasCreateSkillAuth', res.data.create)
          },
          error: (err) => {},
        }
      )
    },
    setSubAccountMainAccountInfo({ commit }) {
      utils.httpGet(
        api.USER_AUTH_USERINFO,
        {},
        {
          success: (res) => {
            commit('setSubAccountMainAccountInfo', res.data)
          },
          error: (err) => {},
        }
      )
    },
    setSceneRole({ state, commit, rootState }, role) {
      commit('setSceneRole', role)
    },

    setSubAccountEditable({ commit }, editable) {
      commit('setSubAccountEditable', editable)
    },

    // sceneList操作
    setSceneList({ commit }, list) {
      commit('setSceneList', list)
    },
  },

  mutations: {
    setId(state, id) {
      state.id = id
    },
    setApp(state, app) {
      state.app = app
      state.app.platformNum = app.platform
      state.app.platform = dicts.aiuiAppPlatform[app.platform]
    },
    setLimitCount(state, data) {
      state.limitCount = data
    },
    setCurrentScene(state, scene) {
      state.currentScene = scene
      //
    },
    setConfigChange(state, flag) {
      state.configChange = flag
    },
    setTranslateConfig(state, config) {
      state.translateConfig = config
    },
    setQcAuth(state, qcAuth) {
      state.qcAuth = qcAuth
    },
    setSubAccountAppAuths(state, data) {
      state.subAccountAppAuths = data
    },
    setSubAccountSkillAuths(state, data) {
      state.subAccountSkillAuths = data
    },
    setSubAccountHasCreateAuth(state, data) {
      state.subAccountHasCreateAuth = data
    },
    setSubAccountHasCreateSkillAuth(state, data) {
      state.subAccountHasCreateSkillAuth = data
    },
    setSubAccountMainAccountInfo(state, data) {
      state.subAccountMainAccountInfo = data
    },
    setSceneRole(state, data) {
      state.sceneRoleId = data
    },

    setContext(state, context) {
      state.context = context
    },

    setSubAccountEditable(state, editable) {
      state.subAccountEditable = editable
    },

    setSceneList(state, list) {
      state.sceneList = list
    },
  },

  getters: {
    id(state, getters, rootState) {
      return state.id
    },
    app(state, getters, rootState) {
      return state.app
    },
    limitCount(state, getters, rootState) {
      return state.limitCount
    },
    currentScene(state, getters, rootState) {
      return state.currentScene
    },
    configChange(state, getters, rootState) {
      return state.configChange
    },
    translateConfig(state, getters, rootState) {
      return state.translateConfig
    },
    qcAuth(state) {
      return state.qcAuth
    },
    subAccountAppAuths(state) {
      return state.subAccountAppAuths
    },
    subAccountSkillAuths(state) {
      return state.subAccountSkillAuths
    },
    subAccountHasCreateAuth(state) {
      return state.subAccountHasCreateAuth
    },
    subAccountHasCreateSkillAuth(state) {
      return state.subAccountHasCreateSkillAuth
    },
    subAccountMainAccountInfo(state) {
      return state.subAccountMainAccountInfo
    },
    sceneRoleId(state) {
      return state.sceneRoleId
    },
    context(state) {
      return state.context
    },

    subAccountEditable(state) {
      return state.subAccountEditable
    },

    sceneList(state) {
      return state.sceneList
    },
  },
}
