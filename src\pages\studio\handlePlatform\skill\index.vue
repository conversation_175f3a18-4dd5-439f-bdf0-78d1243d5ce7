<template>
  <div class="os-scroll">
    <!-- <handle-platform-top></handle-platform-top> -->
    <div class="tab-wrap" v-if="!subAccount">
      <el-tabs
        :value="activeName"
        @input="handleInputVal"
        @tab-click="handleClick"
        style="width: 100%"
      >
        <el-tab-pane label="技能" name="skill"
          ><skills v-if="tabsObj['skill']"></skills
        ></el-tab-pane>
        <el-tab-pane label="实体" name="entity"
          ><entities v-if="tabsObj['entity']"></entities
        ></el-tab-pane>
        <el-tab-pane label="辅助词" name="auxiliary"
          ><auxiliaries v-if="tabsObj['auxiliary']"></auxiliaries
        ></el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>
<script>
import HandlePlatformTop from '../top.vue'
import skills from '../skills.vue'
import entities from '../entities.vue'
import auxiliaries from '../auxiliaries.vue'
import { mapGetters } from 'vuex'

export default {
  data() {
    return {
      activeName: 'skill',
      tabsObj: {
        skill: true,
        entity: false,
        auxiliary: false,
      },
    }
  },
  computed: {
    ...mapGetters({
      subAccount: 'user/subAccount',
    }),
  },
  created() {
    if (this.$route.query.type) {
      this.activeName = this.$route.query.type
      this.setItemTrueOthersNot(this.$route.query.type)
    }
  },
  methods: {
    handleClick(tab, event) {
      console.log(tab, event)
      this.setItemTrueOthersNot(tab.name)
    },
    handleInputVal(val) {
      this.$router.replace({
        name: 'studio-handle-platform-skills',
        query: { type: val },
      })
    },
    setItemTrueOthersNot(key) {
      Object.keys(this.tabsObj).forEach((k) => {
        if (k !== key) {
          this.$set(this.tabsObj, k, false)
        }
      })
      this.$set(this.tabsObj, [key], true)
    },
  },
  components: { HandlePlatformTop, skills, entities, auxiliaries },
}
</script>
<style lang="scss" scoped>
.tab-wrap {
  // max-width: 1200px;
  width: 100%;
  margin: auto;
  .el-tabs {
    margin-left: 0;
  }
  :deep(> .el-tabs > .el-tabs__header) {
    margin-bottom: 20px;
    // border-bottom: 1px solid #e7e7e7;
    height: 63.5px;
    background-color: $white-grey;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  :deep(> .el-tabs > .el-tabs__header .el-tabs__nav-wrap) {
    display: flex;
    justify-content: center;
    .el-tabs__nav-scroll {
      .el-tabs__nav {
        background: #eef0f1;
        padding: 0 4px;
        height: 42px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        .el-tabs__active-bar {
          display: none;
        }
        .el-tabs__item {
          height: 34px;
          line-height: 34px;
          font-size: 14px;
          text-align: center;
          border-radius: 6px;
          cursor: pointer;
          transition: all 0.2s ease;
          color: #555454;
          &.is-active {
            background: rgba(255, 255, 255, 0.8);
            color: #009bff;
            border-radius: 6px;
          }
          &:nth-child(2) {
            padding-left: 20px;
          }
          &:last-child {
            padding-right: 20px;
          }
        }
      }
    }
  }
  :deep(> .el-tabs > .el-tabs__header .el-tabs__nav-wrap::after) {
    display: none;
  }
  :deep(> .el-tabs .el-tabs__content) {
    padding: 0 35px;
  }
}
</style>
