<template>
  <el-dialog
    :title="title"
    :visible="visible"
    @close="cancel"
    :close-on-click-modal="false"
  >
    <el-form
      :model="form"
      label-position="left"
      label-width="90px"
      :rules="rules"
      ref="form"
    >
      <el-form-item label="信息名称" prop="keyDataName">
        <el-input v-model="form.keyDataName"></el-input>
      </el-form-item>

      <el-form-item label="信息描述" prop="keyDataDesc">
        <el-input v-model="form.keyDataDesc"></el-input>
      </el-form-item>

      <el-form-item label="英文标识" prop="keyDataNameEn">
        <el-input v-model="form.keyDataNameEn"></el-input>
      </el-form-item>

      <el-form-item label="是否必填" prop="required">
        <el-radio :label="0" v-model="form.required">否</el-radio>
        <el-radio :label="1" v-model="form.required">是</el-radio>
      </el-form-item>

      <el-form-item label="是否多值项" prop="multivalued">
        <el-radio :label="0" v-model="form.multivalued">否</el-radio>
        <el-radio :label="1" v-model="form.multivalued">是</el-radio>
      </el-form-item>
    </el-form>

    <span slot="footer" class="dialog-footer">
      <el-button @click="cancel">取消</el-button>
      <el-button
        class="dialog-btn"
        type="primary"
        @click="handleConfirm"
        :loading="saving"
      >
        {{ saving ? '创建中...' : '确定' }}
      </el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: 'keyInfoModal',
  data() {
    const validateName = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('请输入用户名'))
      }

      // 检查是否以字母或下划线开头
      if (!/^[a-zA-Z_]/.test(value)) {
        return callback(new Error('必须以字母或下划线开头'))
      }

      // 检查是否只包含字母、数字或下划线
      if (!/^[a-zA-Z0-9_]*$/.test(value)) {
        return callback(new Error('只能包含字母、数字或下划线'))
      }

      callback()
    }
    return {
      title: '新增关键信息',
      visible: false,
      keyDataId: false,
      form: {
        keyDataName: null,
        keyDataDesc: null,
        keyDataNameEn: null,
        required: null,
        multivalued: null,
      },
      saving: false,
      rules: {
        keyDataName: [
          { required: true, message: '请输入关键信息名称', trigger: 'blur' },
          {
            pattern: /^[\u4e00-\u9fa5a-zA-Z0-9._-]{0,32}$/,
            // validator: validateName,
            trigger: ['blur', 'change'],
            message:
              '输入内容只能包含中文、字母、数字或下划线，且长度不超过32个字符',
            trigger: 'blur',
          },
          {
            max: 32,
            message: '输入内容不能超过32个字符',
            trigger: ['blur', 'change'],
          },
        ],
        keyDataDesc: [
          { required: true, message: '请输入关键信息描述', trigger: 'blur' },
          { max: 250, message: '输入内容请在250个字符以内', trigger: 'blur' },
        ],
        keyDataNameEn: [
          { required: true, message: '请输入英文标识', trigger: 'blur' },
          // {
          //   pattern: /^[a-zA-Z0-9._-]{0,32}$/,
          //   message:
          //     '输入内容只能包含英文字母、数字、小数点、短横线和下划线,且长度不超过32个字符',
          //   trigger: 'blur',
          // },
          {
            validator: validateName,
            trigger: ['blur', 'change'],
            message:
              '输入内容只能包含字母、数字或下划线、并且以字母或下划线开头，且长度不超过32个字符',
          },
          {
            max: 32,
            message: '英文名称不能超过32个字符',
            trigger: ['blur', 'change'],
          },
        ],
        required: [
          { required: true, message: '请选择是否必填', trigger: 'change' },
        ],
        multivalued: [
          { required: true, message: '请选择多值项', trigger: 'change' },
        ],
      },
    }
  },
  methods: {
    show(data) {
      this.visible = true
      if (data && data?.keydataId) {
        this.keyDataId = data.keydataId
        this.title = '编辑关键信息'
        this.form = {
          keyDataName: data.keyDataName,
          keyDataDesc: data.keyDataDesc,
          keyDataNameEn: data.keyDataNameEn,
          required: data.required,
          multivalued: data.multivalued,
        }
      }
    },
    handleConfirm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          const params = this.keyDataId
            ? {
                intentId: this.$route.params.intentId,
                keyDataId: this.keyDataId,
                pluginId: this.$route.params.agentId,
                ...this.form,
              }
            : {
                intentId: this.$route.params.intentId,
                pluginId: this.$route.params.agentId,
                ...this.form,
              }
          this.saving = true
          if (this.keyDataId) {
            this.$utils.httpPost(
              '/aiui-agent/openPlatform/keyData/edit',
              JSON.stringify(params),
              {
                config: {
                  headers: {
                    'Content-Type': 'application/json;charset=UTF-8',
                  },
                },
                success: (res) => {
                  if (res.code === '0') {
                    this.$message.success('操作成功')
                    this.saving = false
                    this.cancel()
                    this.$emit('refresh')
                  }
                },
                error: (err) => {
                  this.saving = false
                },
              }
            )
          } else {
            this.$utils.httpPost(
              '/aiui-agent/openPlatform/keyData/add',
              JSON.stringify(params),
              {
                config: {
                  headers: {
                    'Content-Type': 'application/json;charset=UTF-8',
                  },
                },
                success: (res) => {
                  if (res.code === '0') {
                    this.$message.success('操作成功')
                    this.saving = false
                    this.cancel()
                    this.$emit('refresh')
                  }
                },
                error: (err) => {
                  this.saving = false
                },
              }
            )
          }
        }
      })
    },
    cancel() {
      this.visible = false
      this.keyDataId = null
      this.saving = false
      this.form = {
        keyDataName: null,
        keyDataDesc: null,
        keyDataNameEn: null,
        required: null,
        multivalued: null,
      }
      this.title = '新增关键信息'
      this.$refs.form.resetFields()
    },
  },
}
</script>

<style lang="scss" scoped></style>
