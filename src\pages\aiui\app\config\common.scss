.ability {
  width: 100%;
  height: 80px;
  position: relative;
}

.ability-logo {
  height: 80px;
  width: 167px;
  position: absolute;
  z-index: 1;
  left: 24px;
  top: 0;
}

.ability-content {
  display: flex;
  align-items: center;
  z-index: 1;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 200px;
  padding-right: 120px;
}
.ability-title {
  font-size: 16px;
  font-weight: 500;
  color: #262626;
  white-space: nowrap;
}
.ability-split {
  width: 1px;
  height: 21px;
  background: #a6c3e6;
  margin: 0 10px;
}
.ability-desc {
  font-size: 14px;
  color: #999;
  overflow: hidden;
  word-wrap: break-word;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}
.ability-switch {
  position: absolute;
  z-index: 1;
  right: 48px;
  top: 30px;
}
