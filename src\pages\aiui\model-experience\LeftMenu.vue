<template>
  <div class="nav-container">
    <div class="logo">
      <a href="/"><div class="aiui-logo"></div></a>
      <!-- <div class="aiui-logout" @click="logout"></div> -->
    </div>

    <div class="exp-wrap">
      <div class="exp-txt">我要体验</div>
      <ul class="exp-list">
        <li
          v-for="(item, index) in expList"
          :key="index"
          :class="{ [item.cls]: true, active: item.key === current }"
          @click="setCurrent(item)"
        >
          <div>
            <div class="title">{{ item.title }}</div>
            <div class="desc" :title="item.desc">{{ item.desc }}</div>
          </div>
        </li>
      </ul>
    </div>
    <div class="top-btns-wrap">
      <div class="top-button" @click="jumpIntro">
        <i class="icon-access"></i><span>立即接入</span>
      </div>
      <div class="top-button" @click="jumpHelp">
        <i class="icon-doc"></i><span>文档中心</span>
      </div>
    </div>
    <div class="bottom-text"></div>
  </div>
</template>
<script>
export default {
  props: {
    current: {
      type: String,
    },
  },
  data() {
    return {
      expList: [
        {
          title: '交互全链路体验',
          cls: 'exp-icon1',
          key: 'chain',
          desc: '端到端交互体验',
        },
        {
          title: '交互单能力体验',
          cls: 'exp-icon2',
          key: 'ability',
          desc: '关键能力模块体验',
        },
        {
          title: '产品品类交互体验',
          cls: 'exp-icon3',
          key: 'product',
          desc: '针对不同产品打造不同特性',
        },
      ],
    }
  },
  created() {},
  methods: {
    setCurrent(item) {
      this.$emit('setCurrent', item.key)
    },
    jumpIntro() {
      window.open('/app', '_blank')
    },
    jumpHelp() {
      window.open('https://aiui-doc.xf-yun.com/project-1/doc-182/', '_blank')
    },
    logout() {
      this.toPage('/user/logout', 'aiui')
    },
    toPage(path) {
      this.$router.push({ path })
    },
  },
}
</script>
<style scoped lang="scss">
.nav-wrap {
  position: relative;
}
.nav-container {
  width: 288px;
  min-width: 288px;
  background: linear-gradient(180deg, #f6f9ff, #dce8ff 24%, #cee0ff);
  border: 1px solid #bfc7d6;
  height: 100%;
  display: flex;
  flex-direction: column;
  left: 0;
  top: 0;
  bottom: 0;
  z-index: 10;
  padding: 15px 16px 27px 16px;
  position: relative;
}
.logo {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .aiui-logo {
    width: 142px;
    height: 34px;
    background: url(~@A/images/aiui/logo_aiui_black.png) center/contain
      no-repeat;
  }
  .aiui-logout {
    width: 24px;
    height: 24px;
    border-radius: 4px;
    background: url(~@A/images/model-exeperience/v2/<EMAIL>) center/50%
      no-repeat;
    background-color: #dde9ff;
    cursor: pointer;
  }
}

.top-btns-wrap {
  // margin-top: 30px;
  position: absolute;
  z-index: 1;
  bottom: 50px;
  left: 16px;
  right: 16px;
  .top-button {
    font-size: 14px;
    color: #fff;
    text-align: center;
    width: 100%;
    height: 40px;
    line-height: 40px;
    background: $primary;
    border: 1px solid $primary;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    > span {
      font-size: 14px;
    }
    > i {
      margin-right: 8px;
    }
  }
  .top-button + .top-button {
    margin-top: 8px;
  }
}

.exp-wrap {
  .exp-txt {
    font-size: 14px;
    font-weight: 400;
    text-align: left;
    color: #8e90a5;
    line-height: 20px;
    margin: 30px 0 16px 0;
  }
  .exp-list {
    li {
      position: relative;
      // height: 88px;
      padding-top: 34.8%;
      border-radius: 8px;
      cursor: pointer;
      box-shadow: 0px 0px 8px 0px rgba(90, 132, 217, 0.5);
      > div {
        padding: 0px 20px 0 20px;

        position: absolute;
        z-index: 1;
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;
        display: flex;
        flex-direction: column;
        justify-content: center;

        .title {
          color: #454973;
          font-size: 16px;
        }
        .desc {
          color: #7b839c;
          font-size: 12px;
          margin-top: 4px;
          display: -webkit-box;
          text-overflow: ellipsis;
          overflow: hidden;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
        }
      }

      &.exp-icon1 {
        > div {
          background: url(~@A/images/model-exeperience/v2/ab_bg1.png)
            center/contain no-repeat;
        }
      }
      &.exp-icon2 {
        > div {
          background: url(~@A/images/model-exeperience/v2/ab_bg2.png)
            center/contain no-repeat;
        }
      }
      &.exp-icon3 {
        > div {
          background: url(~@A/images/model-exeperience/v2/ab_bg3.png)
            center/contain no-repeat;
        }
      }
      &:hover,
      &.active {
        box-shadow: 0px 2px 12px 0px rgba(40, 95, 213, 0.49) inset;
        > div {
          .title {
            color: #ffffff;
          }
          .desc {
            color: #ffffff;
          }
        }

        &.exp-icon1 {
          > div {
            background: url(~@A/images/model-exeperience/v2/ab_bg1_active.png)
              center/contain no-repeat;
          }
        }
        &.exp-icon2 {
          > div {
            background: url(~@A/images/model-exeperience/v2/ab_bg2_active.png)
              center/contain no-repeat;
          }
        }
        &.exp-icon3 {
          > div {
            background: url(~@A/images/model-exeperience/v2/ab_bg3_active.png)
              center/contain no-repeat;
          }
        }
      }
    }
    li + li {
      margin-top: 16px;
    }
  }
}

.icon-access {
  display: inline-block;
  width: 20px;
  height: 20px;
  background: url(~@A/images/model-exeperience/v2/<EMAIL>) center/contain
    no-repeat;
}
.icon-doc {
  display: inline-block;
  width: 20px;
  height: 20px;
  background: url(~@A/images/model-exeperience/v2/<EMAIL>) center/contain
    no-repeat;
}

.bottom-text {
  width: 213px;
  height: 14px;
  background: url(~@A/images/model-exeperience/v2/bottom_text.png)
    center/contain no-repeat;
  position: absolute;
  bottom: 19px;
  left: 50%;
  transform: translateX(-50%);
}

@media screen and (max-width: 1440px) {
  .nav-container {
    width: 240px;
    min-width: 240px;
  }
}
</style>
