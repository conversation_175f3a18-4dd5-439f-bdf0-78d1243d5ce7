<template>
  <div class="tabs-container">
    <div
      v-for="(tab, index) in allTabs"
      :key="index"
      :class="['tab-item']"
      @click="onTabsClick(index)"
    >
      <router-link :to="{ name: tab.key }" active-class="active">{{
        tab.name
      }}</router-link>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      // tabs: ['调用量统计', '设备统计', '设备明细'],
      // routeNames: [
      //   'app-statistic-service-index',
      //   'app-users',
      //   'app-online-device',
      // ],

      hasSource: false,

     
    }
  },
  created() {
    this.getPaidSource()
  },
  computed: {
    allTabs() {
      return [
        {
          key: 'app-statistic-service-index',
          name: '调用量统计',
        },
        {
          key: 'app-users',
          name: '设备统计',
        },
        {
          key: 'app-online-device',
          name: '设备明细',
        },
        this.hasSource
          ? {
              key: 'app-sources',
              name: '信源授权统计',
            }
          : null,
      ].filter(Boolean)
    },
  },
  methods: {
    onTabsClick(index) {},
    getPaidSource() {
      let that = this
      this.$utils.httpGet(
        this.$config.api.SOURCE_STATISTIC_PAIDSOURCE,
        {
          appid: this.$route.params.appId,
        },
        {
          success: (res) => {

            if (res.flag) {
              if (!!res.data && !!res.data.length) {
                that.hasSource = true
              }
            } else {
              that.$message.error(res.desc)
            }
          },
        }
      )
    },
  },
}
</script>

<style scoped lang="scss">
.tabs-container {
  height: 64px;
  display: flex;
  align-items: center;
  padding: 4px;
  box-sizing: border-box;
}

.tab-item {
  height: 100%;
  padding: 0 15px;
  line-height: 64px;
  font-size: 14px;
  font-weight: 400;
  text-align: center;
  a {
    color: #555454;
  }
  a.active {
    display: inline-block;
    color: $primary;
    width: 80px;
    width: 100%;
    height: 100%;
    border-bottom: 1px solid $primary;
  }

  cursor: pointer;
  transition: all 0.2s ease;
}

// .tab-item.active {
//   .active {
//     width: 80px;
//     background: rgba(255, 255, 255, 0.8);
//     a {
//       color: $primary; /* 可调整为选中时的字体颜色 */
//     }
//   }
// }
</style>
