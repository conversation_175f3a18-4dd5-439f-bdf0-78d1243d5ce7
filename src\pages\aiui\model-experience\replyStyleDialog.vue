<template>
  <el-dialog
    title="回复风格"
    :visible.sync="dialog.show"
    width="616px"
    :append-to-body="true"
    custom-class="new-style-dialog"
  >
    <ul class="type-wrapper">
      <li
        @click.stop="selectReplyStyle(item)"
        v-for="item in styleTypeList"
        :key="item.name"
        :class="{ active: item.name === current }"
      >
        {{ `${item.name}风格` }}
      </li>
    </ul>
    <el-form
      ref="form"
      :model="form"
      label-width="80px"
      :rules="rules"
      :hide-required-asterisk="false"
      style="margin-top: 20px"
    >
      <el-form-item label="风格设定" prop="guideType">
        <el-input
          v-model="form.guideType"
          placeholder="儿童可爱"
          :maxlength="100"
        ></el-input>
      </el-form-item>

      <el-form-item label="风格描述" prop="guideValues">
        <el-input
          type="textarea"
          v-model="form.guideValues"
          placeholder="儿童可爱风格，习惯在交互中撒娇卖萌"
          :maxlength="1000"
          :autosize="{ minRows: 3, maxRows: 15 }"
        ></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="save">保存</el-button>
    </span>
  </el-dialog>
</template>
<script>
export default {
  props: {
    dialog: {
      type: Object,
      default: () => {
        return {
          show: false,
        }
      },
    },
    replyStyleConfig: Object,
  },
  data() {
    return {
      form: {
        guideType: '',
        guideValues: '',
      },
      rules: {
        guideType: [
          { required: true, message: '请输入风格设定', trigger: 'change' },
        ],
        guideValues: [
          { required: true, message: '请输入风格描述', trigger: 'change' },
        ],
      },
      styleTypeList: [],
      current: '',
    }
  },
  watch: {
    'dialog.show'(val) {
      if (val) {
        if (this.replyStyleConfig) {
          this.form.guideType = this.replyStyleConfig.guideType
          this.form.guideValues = this.replyStyleConfig.guideValues
          // 判断根据当前填充值是否选中某个current
        }
        // get reply style from backend
        this.getReplyStyle()
      } else {
        this.$refs.form.resetFields()
        this.current = ''
      }
    },
  },
  methods: {
    getReplyStyle() {
      this.$utils.httpGet(
        this.$config.api.RESOURCE_AIHUB_GETREPLYSTYLES,
        {},
        {
          success: (res) => {
            this.styleTypeList = (res.data && res.data.list) || []
            // 获取了接口值后和本地存储进行对比
            if (this.replyStyleConfig) {
              const name = (
                this.styleTypeList.find(
                  (item) =>
                    item.name === this.replyStyleConfig.guideType &&
                    item.desc === this.replyStyleConfig.guideValues
                ) || {}
              ).name
              if (name) {
                this.current = name
              }
            }
          },
          error: (err) => {
            console.log('page=>>')
            console.log(err)
          },
        }
      )
    },
    selectReplyStyle(item) {
      this.current = item.name
      this.form.guideType = item.name
      this.form.guideValues = item.desc
    },
    save() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          let cfg = {
            guideType: this.form.guideType,
            guideValues: this.form.guideValues,
          }
          this.$emit('setReplyStyleConfig', cfg)
          this.dialog.show = false
        } else {
          return false
        }
      })
    },
  },
}
</script>
<style lang="scss" scoped>
.type-wrapper {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: -10px;
  margin-right: -20px;
  font-size: 14px;
  padding-left: 15px;
  li {
    cursor: pointer;
    height: 30px;
    line-height: 30px;
    padding: 0px 10px;
    border: 1px solid #cfdbff;
    border-radius: 14px;
    margin-bottom: 10px;
    margin-right: 20px;
    &:hover,
    &.active {
      color: $primary;
      border: 1px solid $primary;
    }
  }
  // li + li {
  //   margin-left: 8px;
  // }
}
</style>
