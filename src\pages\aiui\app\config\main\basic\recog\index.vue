<template>
  <div>
    <recogSimpleSOS v-if="currentScene && currentScene.sos === true" />
    <recogSimple
      v-if="
        currentScene && currentScene.sceneBoxId && currentScene.sos !== true
      "
    />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import recogSimple from './recogSimple'
import recogSimpleSOS from './recogSimpleSOS'

export default {
  data() {
    return {}
  },
  computed: {
    ...mapGetters({
      currentScene: 'aiuiApp/currentScene',
      appInfo: 'aiuiApp/app',
      subAccountEditable: 'aiuiApp/subAccountEditable',
    }),
    appId() {
      return this.$route.params.appId
    },
  },
  components: {
    recogSimple,
    recogSimpleSOS,
  },
}
</script>
<style lang="scss" scoped></style>
