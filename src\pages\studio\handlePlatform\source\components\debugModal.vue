<template>
  <el-dialog
    title="调试"
    :visible.sync="visible"
    width="700px"
    close-on-click-modal
  >
    <div class="title-row">
      <span></span>
      <el-button type="primary" @click="doDebug" size="mini"
        >发送请求</el-button
      >
    </div>

    <el-form ref="form" :model="form" :rules="rules">
      <div class="debug-param-container">
        <div class="debug-param-header">
          <el-row>
            <el-col :span="8">参数名称</el-col>
            <el-col :span="6">参数类型</el-col>
            <el-col :span="4">是否必填</el-col>
            <el-col :span="6">取值</el-col>
          </el-row>
        </div>
        <el-scrollbar class="custom-scrollbar">
          <div class="debug-param-body">
            <debug-param-row
              v-for="(param, index) in form.inputParams"
              :key="param.id"
              :param="param"
              @change="handleParamChange(index, $event)"
              :field-path="`inputParams[${index}]`"
            />
          </div>
        </el-scrollbar>
      </div>
    </el-form>

    <div class="debug_result_title">调试结果</div>

    <div class="jsonShow">
      <!-- <pre>
        <code>{{ jsonDataRes }}</code>
      </pre> -->
      <json-view
        class="json-wrap"
        v-if="jsonDataRes"
        :data="jsonDataRes"
      ></json-view>
    </div>

    <span slot="footer" class="dialog-footer">
      <el-button @click="cancel" size="mini">取消</el-button>
    </span>
  </el-dialog>
</template>

<script>
import DebugParamRow from './debugParamRow.vue'
export default {
  name: 'debuggModal',
  components: {
    DebugParamRow,
  },
  props: {
    value: Boolean,
    inputParams: {
      type: Array,
      default: () => [],
    },
    openApiForm: {
      type: Object,
    },
    toolId: {
      type: String,
    },
    ApiInfo: {
      type: Object,
    },
    defaultAuthConfig: {
      type: Object,
    },
  },
  data() {
    return {
      visible: false,

      jsonDataRes: null,

      form: {
        inputParams: JSON.parse(JSON.stringify(this.inputParams || [])),
      },

      rules: {}, // 规则定义
    }
  },

  mounted() {
    this.form.inputParams = JSON.parse(JSON.stringify(this.inputParams || []))
  },
  methods: {
    show(inputParams) {
      this.visible = true
      console.log(this.ApiInfo, '在调试中的ApiInfo')
      this.form.inputParams = JSON.parse(JSON.stringify(this.inputParams || []))
    },
    cancel() {
      this.visible = false
      this.jsonDataRes = null
    },
    handleSubmit() {
      this.$emit('submit', this.form.inputParams)
      this.visible = false
    },
    handleParamChange(index, newParam) {
      this.$set(this.form.inputParams, index, newParam)

      this.$nextTick(() => {
        this.$refs.form.validateField(`inputParams.${index}.defaultValue`)
      })
    },

    doDebug() {
      let data = this.openApiForm
      let webSchema = JSON.stringify({
        toolRequestInput: this.inputParams || [],
        toolRequestOutput: data.outputForm || [],
      })

      const params = {
        endPoint: data.endPoint,
        method: data.method,
        webSchema: webSchema,
        toolId: this.toolId,
        // authType: this.ApiInfo?.authType,
        // authInfo: this.ApiInfo?.authInfo,
      }

      if (this.ApiInfo?.authType === -1) {
        console.log(this.defaultAuthConfig, '保存的默认授权defaultAuthConfigm')
        if (this.defaultAuthConfig?.location) {
          params.authType = 2
          params.authInfo = JSON.stringify(this.defaultAuthConfig)
        } else {
          params.authType = -1
          params.authInfo = ''
        }
      } else if (this.ApiInfo?.authType === 2) {
        params.authType = 2
        params.authInfo = this.ApiInfo?.authInfo
      }

      this.$refs.form.validate((valid) => {
        if (valid) {
          this.$utils.httpPost(
            `/aiui-agent/plugin/debugPluginCustom`,
            JSON.stringify(params),
            {
              config: {
                headers: {
                  'Content-Type': 'application/json;charset=UTF-8',
                },
              },
              success: (res) => {
                if (res.code === '0') {
                  this.$message.success('调试成功')
                  let dataObj = JSON.parse(res?.data)
                  // let strData = JSON.stringify(dataObj, null, 2)
                  this.jsonDataRes = dataObj
                  this.$emit('refresh')
                }
              },
              error: (err) => {
                this.$message.error(err?.desc || '调试失败')
                this.$emit('refresh')
              },
            }
          )
        }
      })
    },

    getParamRules(param) {
      const rules = []

      if (param.required && !['object', 'array'].includes(param.type)) {
        rules.push({
          required: true,
          message: `${param.name} 不能为空`,
          trigger: 'blur',
        })
      }

      if (param.type === 'number') {
        rules.push({
          type: 'number',
          message: `${param.name} 必须是数字`,
          trigger: 'blur',
          transform: (value) => Number(value),
        })
      }

      return rules
    },

    validateNestedParams(params, path = []) {
      const errors = []

      params.forEach((param, index) => {
        const currentPath = [...path, index]

        // 必填校验
        if (param.required && !param.defaultValue && param.defaultValue !== 0) {
          errors.push(`参数 ${param.name} 必须填写`)
        }

        // 类型校验（只有有值时才校验）
        if (
          param.defaultValue !== undefined &&
          param.defaultValue !== null &&
          param.defaultValue !== ''
        ) {
          // 数字类型校验
          if (param.type === 'number' && isNaN(Number(param.defaultValue))) {
            errors.push(`参数 ${param.name} 必须是数字`)
          }

          // 整数类型校验 - 修正部分
          else if (param.type === 'integer') {
            const strValue = String(param.defaultValue)

            if (!/^-?\d+$/.test(strValue)) {
              errors.push(`参数 ${param.name} 必须是整数`)
            }
          }

          // 布尔类型校验
          else if (param.type === 'boolean') {
            let strValue = String(param.defaultValue).toLowerCase()
            if (strValue !== 'true' && strValue !== 'false') {
              errors.push(`参数 ${param.name} 必须是 true 或 false`)
            }
          }
        }

        // 递归校验子项
        if (param.children) {
          errors.push(
            ...this.validateNestedParams(param.children, [
              ...currentPath,
              'children',
            ])
          )
        }
      })
      return errors
    },
  },
}
</script>

<style lang="scss" scoped>
.title-row {
  display: flex;
  justify-content: space-between;
  align-content: center;
  margin-bottom: 20px;
}
.el-form {
  border: 1px solid #e1e1e1;
  border-radius: 8px;
  overflow: hidden;
  .debug-param-container {
    width: 100%;
    .debug-param-header {
      padding: 21px 0;
      background: #f6f8fa;
      padding-left: 10px;
    }
    .custom-scrollbar {
      height: 300px;
      ::v-deep .el-scrollbar__wrap {
        overflow-x: hidden;
      }
      ::v-deep .el-scrollbar__bar.is-horizontal {
        display: none;
      }
    }
  }
}
.debug_result_title {
  margin-top: 25px;
  margin-bottom: 20px;
  font-weight: 600;
  color: #000000;
}
.jsonShow {
  width: 100%;
  height: 300px;
  overflow: auto;
  background: #f0f0f0;
  padding: 8px;
  border-radius: 6px;
  margin-bottom: 12px;
}
</style>
