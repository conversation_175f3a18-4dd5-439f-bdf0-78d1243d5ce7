@import './mixin.scss';
@import './common.scss';
@import './fonticon.scss';
@import './aiui-fonticon.scss';
@import './aiui-2-font.scss';
@import './aiui-myapp-font.scss';
@import './reset.scss';

* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  outline: none;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  font-family: PingFangSC, PingFangSC-Regular, Microsoft YaHei, Open Sans, Arial,
    Hiragino Sans GB, '\5fae\8f6f\96c5\9ed1', STHeiti, WenQuanYi Micro Hei,
    SimSun, sans-serif;
  @include scroll();
}

*,
:after,
:before {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

label {
  font-weight: normal;
}

li,
ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

a {
  text-decoration: none;
  color: $primary;
  cursor: pointer;
}

body,
html {
  height: 100%;
  font-family: 'PingFang Sc', 'Microsoft Arial', '微软等线', 'Microsoft YaHei',
    '微软雅黑', 'Helvetica Neue', Helvetica, 'Segoe UI', Arial;
  color: $semi-black;
  font-size: 14px;
}

h1 {
  font-size: 36px;
  color: $semi-black;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
}
input[type='number'] {
  -moz-appearance: textfield;
}

.icon-ico_fangyan {
  font-size: 26px !important;
  margin-right: 15px !important;
  padding-left: 3px;
}

input::-webkit-input-placeholder {
  color: $grey003;
}
input::-moz-placeholder {
  /* Mozilla Firefox 19+ */
  color: $grey003;
}
input:-moz-placeholder {
  /* Mozilla Firefox 4 to 18 */
  color: $grey003;
}
input:-ms-input-placeholder {
  /* Internet Explorer 10-11 */
  color: $grey003;
}
