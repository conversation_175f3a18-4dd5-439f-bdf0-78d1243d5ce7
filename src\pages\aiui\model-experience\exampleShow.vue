<template>
  <div class="card card-bottom">
    <div class="title">
      <span class="line"></span> <span class="exmaple-title">交互示例</span>
    </div>
    <div class="tabs">
      <ul class="tabs-list">
        <li
          v-for="item in examples"
          :key="item.key"
          @click="toggleActive(item)"
          :class="{ active: item.key == active }"
        >
          {{ item.name }}
        </li>
      </ul>
    </div>
    <div class="content">
      <div class="banner">{{ banner[active].description }}</div>
      <file-upload v-if="active === '2'" />
      <chat-show v-else :chat="selectedChatList" />
      <img :src="banner[active].bg" />
    </div>
  </div>
</template>
<script>
import FileUpload from './fileUpload.vue'
import ChatShow from './chatShow.vue'

import decoration1 from '@A/images/model-exeperience/bg01.png'
import decoration2 from '@A/images/model-exeperience/bg02.png'
import decoration3 from '@A/images/model-exeperience/bg03.png'
import decoration4 from '@A/images/model-exeperience/bg04.png'
// import decoration5 from '@A/images/model-exeperience/bg05.png'

export default {
  data() {
    return {
      examples: [
        {
          key: '0',
          name: '多轮贯穿',
          chatList: [
            { people: 'me', con: '来首刘德华的忘情水' },
            {
              people: 'ai',
              con: '即将为您播放刘德华的《忘情水》，希望这首歌能让您忘掉伤悲。',
            },

            { people: 'me', con: '这首歌有什么寓意吗' },
            {
              people: 'ai',
              con: '《忘情水》这首歌寓意着人们在爱情中被伤害后想把不开心的事情忘掉，寄托了有梦可追的美好，也道出了“一路走来不能回”的无奈。旋律优美，情真意切，堪称是一首经典之作。',
            },
            { people: 'me', con: '还有他的其他歌吗' },
            {
              people: 'ai',
              con: '当然有，这是刘德华的一首经典歌曲《男人哭吧不是罪》，希望您会喜欢。',
            },
          ],
        },
        {
          key: '1',
          name: '领域知识问答',
          chatList: [
            { people: 'me', con: '《追随》的电影导演是谁？' },
            {
              people: 'ai',
              con: '电影《追随》的导演是克里斯托弗·诺兰，这是一部1998年在英国上映的电影。',
            },
            { people: 'me', con: '这个导演还导演过什么电影吗' },
            {
              people: 'ai',
              con: '克里斯托弗·诺兰是一位多产的导演，除了《追随》，他还执导了《盗梦空间》（2010年）。诺兰的作品以独特的叙事技巧和创意著称。',
            },
            { people: 'me', con: '盗梦空间说了一个怎样的故事？' },
            {
              people: 'ai',
              con: '电影《盗梦空间》讲述了一位梦境盗贼的故事，他能够进入别人的梦境盗取重要机密。在影片中，梦境被虚拟为分层的空间，人们可以通过注射药物在不同空间中穿越。影片展现了丰富的世界和激烈的情节，同时也让观众思考真实与非真实的界限。',
            },
          ],
        },
        // { key: '2', name: '文档快速学习' },
        {
          key: '3',
          name: '内容生成',
          chatList: [
            { people: 'me', con: '帮我写一个描述冬天下雪场景的五言诗' },
            {
              people: 'ai',
              con: `好的，以下是一个描述冬天下雪场景的五言诗：<br/>白雪纷纷何所似？<br/>撒盐空中差可拟。<br/>胡为乎骑马踏雪去？<br/>君不见满陇雪花如大絮。<br/>希望这个描述能够满足您的需求！如果您还有其他问题或需要其他帮助，请随时告诉我。`,
            },
            {
              people: 'me',
              con: '你这不是五言诗呀，五言诗是每句都是五个字，请重新生成',
            },
            {
              people: 'ai',
              con: '抱歉，我理解有误。请允许我为您重新生成一首五言诗：<br/>雪落千山间，<br/>银装素裹闲。<br/>寒风凛冽至，<br/>世界似画篇。<br/>这是一首描述冬天下雪场景的五言诗。希望这次能满足您的要求！如果您还有其他问题或需要其他帮助，请随时告诉我。',
            },
          ],
        },
        {
          key: '4',
          name: '推理问答',
          chatList: [
            {
              people: 'me',
              con: '桌子上有4个苹果，小红吃了1个，小刚拿走了2个，还剩下几个苹果？',
            },
            {
              people: 'ai',
              con: '还剩下1个苹果。',
            },
            { people: 'me', con: '一公斤的棉花和一公斤的铁，哪一个更重？' },
            {
              people: 'ai',
              con: '一样重，都是一公斤。',
            },
            {
              people: 'me',
              con: '有以下三件事：1）厨房的水烧开了；2）下雨天要收衣服；3）和朋友打电话聊天。请问应该按什么顺序处理？',
            },
            {
              people: 'ai',
              con: '建议先处理“厨房的水烧开了”和“下雨天要收衣服”，因为这两个事情比较紧急，而“和朋友打电话聊天”则可以稍后再做。',
            },
          ],
        },
      ],
      banner: {
        0: {
          description: '贯穿技能、问答、闲聊的上下文多轮穿插式对话',
          bg: decoration1,
        },
        1: {
          description:
            '针对百科、景点、美食、影视等领域，可进行多样化的深入回复',
          bg: decoration2,
        },
        3: {
          description: '具备基础的内容生成能力',
          bg: decoration4,
        },
        4: { description: '具备基础的推理问答能力', bg: decoration3 },
        // {
        //   description: '支持用户在部分对话场景纠偏纠错',
        //   bg: decoration5,
        // },
      },
      active: '0',
    }
  },
  methods: {
    toggleActive(item) {
      this.active = item.key
    },
  },
  computed: {
    selectedChatList() {
      return this.examples.find((item) => item.key === this.active).chatList
    },
  },
  components: { FileUpload, ChatShow },
}
</script>
<style lang="scss" scoped>
// .card {
//   border-radius: 8px;
//   box-shadow: 0px 0px 23.75px 0px #cbd5fb;
// }

.card-bottom {
  padding: 0px 30px;
  display: flex;
  flex-direction: column;
}

.title {
  display: flex;
  align-items: center;
  margin-bottom: 14px;
  font-size: 18px;
  font-family: PingFang SC, PingFang SC-Heavy;
  font-weight: 800;
  text-align: center;
  color: #262626;

  .line {
    width: 2px;
    height: 20px;
    background: $primary;
    display: inline-block;
    margin-right: 10px;
  }
}

.icon-example {
  width: 14px;
  height: 15px;
  display: inline-block;
  // background: url(~@A/images/model-exeperience/<EMAIL>) center/contain
  //   no-repeat;
  margin-right: 2px;
}

.exmaple-title {
  font-size: 14px;
  font-weight: bold;
}

.tabs-list {
  display: flex;
  align-items: center;
  justify-content: space-between;

  li {
    width: 110px;
    height: 33px;
    line-height: 33px;
    // border: 1px solid #bacbf6;
    // border-radius: 4px;
    color: #262626;
    text-align: center;
    font-size: 14px;
    font-family: PingFang SC, PingFang SC-Medium;
    font-weight: 500;

    &:hover {
      cursor: pointer;
    }

    &.active,
    &:hover {
      background: #dae8ff;
      border-radius: 4px;
      color: $primary;
      font-weight: 700;
      text-align: center;
    }
  }
}

.content {
  // height: calc(100% - 80px);
  flex: 1;
  // background: linear-gradient(41deg, #edefff 0%, #dff4ff 100%);
  // border-radius: 4px;
  margin-top: 22px;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.8);
  border-radius: 4px;
  box-shadow: -5px 0px 14.25px 0.25px #c9dcff;
  position: relative;

  .banner {
    width: 100%;
    height: 77px;
    line-height: 57px;
    background: url(~@A/images/model-exeperience/background4.png) center/contain
      no-repeat;
    background-size: cover;
    padding: 10px;
  }

  img {
    position: absolute;
    right: 20px;
    top: 0;
    height: 77px;
  }
}

@media screen and (max-width: 1601px) {
  .tabs-list {
    li {
      width: 85px;
      height: 26px;
      line-height: 26px;
      font-size: 12px;
    }
  }

  // .card-bottom {
  //   height: calc(50% - 5px);
  //   margin-top: 10px;
  // }
}

@media screen and (max-height: 800px) {
  .content {
    margin-top: 10px;

    .banner {
      height: 60px;
      line-height: 40px;
      font-size: 13px;
    }

    img {
      height: 60px;
    }
  }

  .title {
    margin-bottom: 10px;
  }
}
</style>
