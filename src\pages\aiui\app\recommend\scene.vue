<template>
  <div>
    <span>内容推荐</span>
    <el-select
      filterable
      style="width: 240px;"
      size="medium"
      v-model="scene"
      @change="switchScene"
      placeholder="场景模式">
      <el-option
        v-for="(item, index) in sceneList"
        :key="index"
        :label="item.scene"
        :value="item.scene"
        @click.native="changeScene(item)">
        <span class="fl">{{ item.scene }}</span>
        <i
          class="ic-r-delete fr"
          v-if="item.scene !== 'main' && subAccountEditable"
          @click.stop="deleteScene(item, index)"></i>
      </el-option>
    </el-select>
    <div
      class="ib create-scene"
      @click="createSceneVisible">
      +
    </div>

    <el-dialog
      title="创建场景"
      :visible.sync="dialogVisible"
      @close="$refs['form'].resetFields()"
      width="560px">
      <el-form
        ref="form"
        :model="form"
        :rules="formRules"
        label-width="20%"
        onSubmit="return false">
        <el-form-item label="场景名称" prop="scene" class="scene-edit-wrap">
          <el-input
            type="text"
            class="wild-card-input"
            v-model="form.scene"
            auto-complete="off"
            placeholder="长度为1-16个字符，仅支持字母/数字"
            @keyup.enter.native="$event.target.blur(), doSubmit('form')"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="doSubmit('form')"
          :disabled="!canCreateTranslateScene && form.point == '1,4'">确 定</el-button>
      </span>
    </el-dialog>

  </div>
</template>

<script>
  import { mapGetters } from 'vuex'

  export default {
    name: 'scene',
    props: {
      sceneList: Array,
      currentScene: Object,
      currentSceneId: Number,
      globalChange: Boolean,
      canCreateTranslateScene: Boolean,
      subAccount: Boolean,
      subAccountEditable: Boolean
    },
    data() {
      const checkKeyWord = (rule, value, callback) => {
        let keyWordsArr = ['null', 'nil', 'true', 'false']
        for(let i = 0, len = keyWordsArr.length; i < len; i++) {
          if(keyWordsArr[i] == value.toLowerCase()) {
            callback(new Error('场景名称不能为关键字'))
          }
        }

        callback()
      }
      const repeatedSceneName = (rule, value, callback) => {
        for(let i = 0, len = this.sceneList.length; i < len; i++) {
          if(this.sceneList[i].scene.toLowerCase() === value.toLowerCase()) {
            callback(new Error('场景名称已存在'))
          }
        }
        callback()
      }

      return {
        scene: null,
        dialogVisible: false,
        form: {
          scene: '',
          point: '1'
        },
        formRules: {
          scene: [
            {required: true, message: '请输入场景名称'},
            {max: 16, message: '场景名称长度不能超过16个字符'},
            {pattern: /^[a-zA-Z0-9]+$/, message: '场景名称仅支持字母/数字', trigger: ['blur', 'change']},
            {validator: checkKeyWord, trigger: 'blur'},
            {validator: repeatedSceneName, trigger: 'blur'}
          ]
        },
        formSubmitting: false
      }
    },
    computed: {
      ...mapGetters({
        limitCount: 'aiuiApp/limitCount',
      })
    },
    watch: {
      'currentScene.scene': function () {
        //this.scene = this.currentScene.scene || (this.sceneList && this.sceneList.length > 0 ? this.sceneList[0].scene : null)
      },
      'sceneList': function () {
        //this.scene = JSON.stringify(this.currentScene) !== '{}' ? this.currentScene : this.sceneList.find(item => { return item.scene === 'Main' })
        let tmp = this.scene ? this.sceneList.find(item => { return item.scene === this.scene }) : this.sceneList.find(item => { return item.scene === 'main' })
        this.scene = tmp.scene
        this.$emit('switchSceneCategory', tmp)
      }
    },
    created (){

    },
    methods: {
      createSceneVisible() {
        this.dialogVisible = true
        /*if(this.sceneList.length < this.limitCount['app_scene_count']) {
          this.dialogVisible = true
        } else {
          this.$message.warning('场景个数已达上限')
        }*/
      },
      doSubmit(formName) {
        let self = this
        this.$refs[formName].validate(valid => {
          if (valid) {
            return this.$emit('addCategory', 0, this.form.scene, null, null, () => {
              this.scene = this.form.scene
              this.dialogVisible = false
              this.$emit('getSceneList')
              //this.$emit('getContentTypesList')
            })
          } else {
            return false
          }
        })
      },
      addScene(scene) {
        this.sceneList.push(scene)
        this.changeScene(scene)
      },
      switchScene(e) {
        this.$emit('switchSceneCategory', this.sceneList.find(scene => {return scene.scene === e}))
      },
      changeScene(scene, forceChange) {
        //this.$emit('getContentTypesList', scene.id)
      },
      deleteScene(item, index) {
        let self = this
        this.$confirm('确定删除？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          self.$utils.httpGet(this.$config.api.RECOMMEND_CONTENT_DELETESENCETYPES, {
            appid: item.appId,
            contentTypeId: item.id,
            type: 0
          }, {
            success: (res) => {
              this.scene = null
              this.$message.success('场景删除成功')
              this.$emit('getSceneList')
              //this.$emit('getContentTypesList')
            },
            error: (err) => {
              console.log('page=>>');
              console.log(err);
            }
          })
        }).catch(() => {
        })
      },
    }
  }
</script>

<style lang="scss" scoped>
  .scene-label {
    font-size: 14px;
    color: $grey6;
  }
  .ic-r-delete {
    font-size: 16px;
    color: $grey4;
  }
  .create-scene {
    width: 36px;
    height: 36px;
    line-height: 32px;
    border-radius: 2px;
    margin-left: 10px;
    font-size: 26px;
    cursor: pointer;
    color: $primary;
    border: 1px solid $grey3;
    text-align: center;
    vertical-align: middle;
  }
</style>
