@font-face {
  font-family: 'AIUI-iconfont';
  src:  url('../font/AIUI-iconfont.eot?pbea4k');
  src:  url('../font/AIUI-iconfont.eot?pbea4k#iefix') format('embedded-opentype'),
  url('../font/AIUI-iconfont.ttf?pbea4k') format('truetype'),
  url('../font/AIUI-iconfont.woff?pbea4k') format('woff'),
  url('../font/AIUI-iconfont.svg?pbea4k#AIUI-iconfont') format('svg');
  font-weight: normal;
  font-style: normal;
}

[class^="ai-"], [class*=" ai-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'AIUI-iconfont' !important;
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.ai-mn-data-user:before {
  content: "\e900";
}
.ai-mn-basic-info:before {
  content: "\e901";
}
.ai-mn-device-skill:before {
  content: "\e902";
}
.ai-mn-cli-capcity:before {
  content: "\e903";
}
.ai-mn-sys-char:before {
  content: "\e904";
}
.ai-mn-integ-test:before {
  content: "\e905";
}
.ai-mn-device-auth:before {
  content: "\e906";
}
.ai-mn-update:before {
  content: "\e907";
}
.ai-mn-custom:before {
  content: "\e908";
}
.ai-mn-version:before {
  content: "\e909";
}
.ai-mn-interact:before {
  content: "\e90a";
}
.ai-mn-skills-ex:before {
  content: "\e90b";
}
.ai-mn-debug:before {
  content: "\e90c";
}
.ai-mn-launch:before {
  content: "\e90d";
}
.ai-mn-code:before {
  content: "\e90e";
}
.ai-mn-data:before {
  content: "\e90f";
}
.ai-mn-optimize:before {
  content: "\e910";
}
.ai-mn-dict:before {
  content: "\e911";
}
.ai-mn-edit:before {
  content: "\e912";
}
.ai-mn-ip-list:before {
  content: "\e913";
}
.ai-mn-link:before {
  content: "\e914";
}
.ai-mn-download:before {
  content: "\e915";
}
.ai-mn-reg-rec:before {
  content: "\e916";
}
.ai-store-3rd-party:before {
  content: "\e917";
}
.ai-store-all:before {
  content: "\e918";
}
.ai-store-baby:before {
  content: "\e919";
}
.ai-store-car:before {
  content: "\e91a";
}
.ai-store-edu:before {
  content: "\e91b";
}
.ai-store-efficient:before {
  content: "\e91c";
}
.ai-store-finance:before {
  content: "\e91d";
}
.ai-store-game:before {
  content: "\e91e";
}
.ai-store-health:before {
  content: "\e91f";
}
.ai-store-iot:before {
  content: "\e920";
}
.ai-store-life:before {
  content: "\e921";
}
.ai-store-media:before {
  content: "\e922";
}
.ai-store-new:before {
  content: "\e923";
}
.ai-store-os:before {
  content: "\e924";
}
.ai-store-qa:before {
  content: "\e925";
}
.ai-store-rank:before {
  content: "\e926";
}
.ai-sys-android:before {
  content: "\e927";
}
.ai-sys-linux:before {
  content: "\e928";
}
.ai-sys-windows:before {
  content: "\e929";
}
.ai-sys-ios:before {
  content: "\e92a";
}
.ai-sys-webapi:before {
  content: "\e92b";
}
.ai-sys-wechat:before {
  content: "\e92c";
}
.ai-sys-rtos:before {
  content: "\e92d";
}
.ai-sys-morfei:before {
  content: "\e92e";
}
.ai-sc-soundbox:before {
  content: "\e92f";
}
.ai-sc-refri:before {
  content: "\e930";
}
.ai-sc-tv:before {
  content: "\e931";
}
.ai-sc-screen:before {
  content: "\e932";
}
.ai-sc-robot:before {
  content: "\e933";
}
.ai-sc-usual:before {
  content: "\e934";
}
.ai-sc-inte-appli:before {
  content: "\e935";
}
.ai-sc-vehicle:before {
  content: "\e936";
}
.ai-sys-other:before {
  content: "\e937";
}
.ai-sc-toy:before {
  content: "\e938";
}
.ai-sc-other:before {
  content: "\e939";
}
.ai-big-success:before {
  content: "\e93a";
}
.ai-big-failed:before {
  content: "\e93b";
}
.ai-r-plus:before {
  content: "\e93c";
}
.ai-r-plus-thin:before {
  content: "\e93d";
}
.ai-r-tick:before {
  content: "\e93e";
}
.ai-r-tick-thin:before {
  content: "\e93f";
}
.ai-r-cross:before {
  content: "\e940";
}
.ai-r-cross-thin:before {
  content: "\e941";
}
.ai-r-enlarge:before {
  content: "\e942";
}
.ai-r-shrink:before {
  content: "\e943";
}
.ai-r-angle-d:before {
  content: "\e944";
}
.ai-r-angle-u:before {
  content: "\e945";
}
.ai-r-angle-l:before {
  content: "\e946";
}
.ai-r-angle-r:before {
  content: "\e947";
}
.ai-r-angle-d-line:before {
  content: "\e948";
}
.ai-r-angle-u-line:before {
  content: "\e949";
}
.ai-r-angle-l-line:before {
  content: "\e94a";
}
.ai-r-angle-r-line:before {
  content: "\e94b";
}
.ai-r-angle-d-oval:before {
  content: "\e94c";
}
.ai-r-angle-u-oval:before {
  content: "\e94d";
}
.ai-r-angle-l-oval:before {
  content: "\e94e";
}
.ai-r-angle-r-oval:before {
  content: "\e94f";
}
.ai-r-triangle-down:before {
  content: "\e950";
}
.ai-r-triangle-up:before {
  content: "\e951";
}
.ai-r-num-decrease:before {
  content: "\e952";
}
.ai-r-num-increase:before {
  content: "\e953";
}
.ai-r-tick-oval:before {
  content: "\e954";
}
.ai-r-cross-oval:before {
  content: "\e955";
}
.ai-r-exclamation:before {
  content: "\e956";
}
.ai-r-tip:before {
  content: "\e957";
}
.ai-r-play:before {
  content: "\e958";
}
.ai-r-pause:before {
  content: "\e959";
}
.ai-r-upload:before {
  content: "\e95a";
}
.ai-r-download:before {
  content: "\e95b";
}
.ai-r-edit:before {
  content: "\e95c";
}
.ai-r-delete:before {
  content: "\e95d";
}
.ai-r-data:before {
  content: "\e95e";
}
.ai-r-setting:before {
  content: "\e95f";
}
.ai-r-search:before {
  content: "\e960";
}
.ai-r-sound:before {
  content: "\e961";
}
.ai-r-see:before {
  content: "\e962";
}
.ai-r-calendar:before {
  content: "\e963";
}
.ai-r-copy:before {
  content: "\e964";
}
.ai-r-move:before {
  content: "\e965";
}
.ai-r-email:before {
  content: "\e966";
}
.ai-r-phone:before {
  content: "\e967";
}
.ai-r-select:before {
  content: "\e968";
}
.ai-r-menu:before {
  content: "\e969";
}
.ai-r-more:before {
  content: "\e96a";
}
.ai-r-equal:before {
  content: "\e96b";
}
.ai-r-exchange:before {
  content: "\e96c";
}
.ai-r-filter:before {
  content: "\e96d";
}
.ai-r-loading:before {
  content: "\e96e";
}
.ai-r-minus:before {
  content: "\e96f";
}
.ai-r-list:before {
  content: "\e970";
}
.ai-r-extend:before {
  content: "\e971";
}
.ai-r-node:before {
  content: "\e972";
}
.ai-r-subflow:before {
  content: "\e973";
}
.ai-r-link:before {
  content: "\e974";
}
.ai-r-file:before {
  content: "\e975";
}
.ai-r-info:before {
  content: "\e976";
}
.ai-r-seq-handle:before {
  content: "\e977";
}
.ai-r-wave:before {
  content: "\e978";
}
