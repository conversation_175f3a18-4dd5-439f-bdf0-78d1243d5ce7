<template>
  <div class="studio-skill-menu-head">
    <div class="studio-skill-menu-head-return" @click="returnCb">
      <svg-icon
        iconClass="secondary-return"
        :customStyle="{
          width: '13px',
          height: '9px',
          marginRight: '5px',
          transform: 'translateY(1px)',
        }"
      />
      <span>返回列表</span>
    </div>
    <!-- <div
      class="studio-skill-menu-head-skill-name"
      @click.stop="openSelectSkill($event)"
    >
      <span :title="skill.zhName || '-'">{{ skill.zhName || '-' }}</span>
    </div>
    <select-skill-popover :subAccount="subAccount" /> -->
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import SelectSkillPopover from '@P/studio/skill/dialog/selectSkill.vue'

export default {
  name: 'studioSkillMenuHead',
  data() {
    return {}
  },
  computed: {
    ...mapGetters({
      skill: 'studioSkill/skill',
      subAccount: 'user/subAccount',
    }),
  },
  created() {},

  methods: {
    returnCb() {
      if (this.subAccount) {
        this.$router.push('/sub/skills')
      } else {
        this.$router.push({ name: 'studio-skill' })
      }
    },
    openSelectSkill(event) {
      this.$store.dispatch('studioSkill/setSkillPopover', {
        show: true,
        rect: event.target.getBoundingClientRect(),
      })
    },
  },
  components: {
    SelectSkillPopover,
  },
}
</script>

<style lang="scss">
.studio-skill-menu-head {
  padding: 16px 22px 0;
  height: 96px;
  &-return {
    display: inline-flex;
    cursor: pointer;
    margin-bottom: 10px;
    font-size: 14px;
    color: $grey4;
    align-items: center;
    i {
      color: $grey4;
    }
  }
  &-skill-name {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    cursor: pointer;
    span {
      font-size: 24px;
      // font-weight: 600;
    }
  }
}
</style>
