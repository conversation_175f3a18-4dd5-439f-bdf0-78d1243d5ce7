<template>
  <os-page :options="pageOptions">
    <studio-skill-header-right slot="btn" />
    <div class="info-page">
      <el-form class="mgt48 mgb56" ref="skillForm" :rules="rules" :model="skillForm" label-width="98px" label-position="left" :disabled="!subAccountEditable"
        @submit.native.prevent>
        <el-form-item label="技能类型">
          <span>业务定制私有技能</span>
        </el-form-item>
        <el-form-item label="服务平台">
          <span>AIUI</span>
        </el-form-item>
        <el-form-item label="技能名称">
          <span>{{skill.zhName}}</span>
        </el-form-item>
        <el-form-item label="定制名称" prop="alias" placeholder="请输入定制名称">
          <el-input v-model="skillForm.alias"></el-input>
        </el-form-item>
        <el-form-item label="英文标识">
          <span v-if="skill.name">{{ skill.identify}}</span>
        </el-form-item>
        <el-form-item v-if="skill.category" label="技能分类">
          <span>{{skillTypeList[skill.category]}}</span>
        </el-form-item>
      </el-form>
      <div class="page-form-btns" v-if="subAccountEditable">
        <el-button type="primary"
          @click="onSubmit"
          :loading="saving"
          :disabled="!edited">
          {{saving ? '保存中...' : '保存'}}
        </el-button>
        <os-give-up-save :edited="edited" @noSave="noSave" />
      </div>
    </div>
    <page-leave-tips :dialog="leaveDialog" @save="onSubmit" @noSave="noSave" @noJump="noJump" />
  </os-page>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'extend-skill-info',
  data () {
    return {
      pageOptions: {
        title: '基本信息',
        loading: false
      },
      skillForm: {
        alias: ''
      },
      oldAlias: '',
      rules: {
        'alias': [
          this.$rules.required('定制名称不能为空'),
          this.$rules.baseRegLimit(),
          this.$rules.lengthLimit(1, 32, '定制名称长度不能超过32个字符')
        ]
      },
      skill: {},
      skillTypeList: [],
      saving: false,
      leaveDialog: {
        show: false
      },
      routeTo: {},
      parenthesesReg: /\((.*?)\)/g
    }
  },
  beforeRouteLeave: function (to, from, next) {
    if (this.edited) {
      this.leaveDialog.show = true
      this.routeTo = to
      next(false)
    } else {
      next()
    }
  },
  computed: {
    ...mapGetters({
      originalSkill: 'studioSkill/skill',
      subAccountSkillAuths: 'studioSkill/subAccountSkillAuths'
    }),
    edited () {
      return this.skillForm.alias !== this.oldAlias
    },
    subAccountEditable(){
      return this.subAccountSkillAuths[this.skill.id] == 2 ? false : true
    }
  },
  watch: {
    'originalSkill': function (val, oldVal) {
      this.skill = this.$deepClone(val)
      if (this.routeTo.name) {
        this.$router.push({name: this.routeTo.name, params: this.routeTo.params})
      }
    },
    'skill.zhName': function(val) {
      if(!val) return
      let regResArr = val.match(this.parenthesesReg) || null
      if(!regResArr) {
        this.skillForm.alias = ''
        this.oldAlias = ''
        return
      }
      let temp = regResArr[regResArr.length - 1]
      this.skillForm.alias = temp.substring(1, temp.length-1)
      this.oldAlias = temp.substring(1, temp.length-1)
    }
  },
  created () {
    if (this.$store.state.studioSkill.skill.id) {
      this.skill = this.$deepClone(this.$store.state.studioSkill.skill)
    }
    this.getSkillType()
  },
  methods: {
    getSkillType () {
      this.$utils.httpGet(this.$config.api.STUDIO_SKILL_SKILL_TYPES, {}, {
        success: (res) => {
          this.skillTypeList = res.data
        },
        error: (err) => {
        }
      })
    },
    mergeSkillZhName(){
      let first = this.skill.zhName.split('(')[0]
      let regRes = this.skill.zhName.match(this.parenthesesReg) || null
      if(!regRes) {
        return this.skill.zhName
      }
      regRes[regRes.length - 1] = `(${this.skillForm.alias})`
      let end = regRes.join('')
      return `${first}${end}`
    },
    onSubmit () {
      let self = this
      if (this.saving) {
        return
      }
      this.$refs.skillForm.validate((valid) => {
        if (valid) {
          this.saving = true
          let temp = this.mergeSkillZhName()
          let data = {
            businessId: this.skill.id,
            businessName: temp
          }
          let api = this.$config.api.STUDIO_EXTEND_SKILL_EDIT
          this.$utils.httpPost(api, data, {
            success: (res) => {
              self.saving = false
              self.$message.success('保存成功')
              self.$refs.skillForm && self.$refs.skillForm.clearValidate()
              self.$store.dispatch('studioSkill/setSkill', this.skill.id)
            },
            error: (err) => {
            }
          })
        }
      })
    },
    noSave () {
      this.$refs.skillForm.clearValidate()
      this.skill = this.$deepClone(this.originalSkill)
      this.skillForm.alias = this.oldAlias
      if (this.routeTo.name) {
        this.$router.push({name: this.routeTo.name, params: this.routeTo.params})
      }
    },
    noJump () {
      this.routeTo = {}
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
