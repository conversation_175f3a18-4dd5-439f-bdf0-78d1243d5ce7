<template>
  <el-table
    class="small-table"
    :data="tableData.list"
    v-loading="tableData.loading"
    :border="false"
    row-key="id"
    empty-text="暂无参数"
    :show-header="false"
    align="left"
  >
     <el-table-column label="属性" width="100" prop="zhName"></el-table-column>
    <el-table-column label="属性值" width="100" prop="value"></el-table-column>
  </el-table>
</template>
<script>
export default {
  props: {
    activeName: {
      value: String
    },
    tab: {
      value: Object
    },
    characterId:{
        value: String
    }
  },
  data() {
    return {
      tableData: {
        loading: false,
        total: 0,
        page: 1,
        size: 999,
        list: [
        ]
      }
    };
  },

  created() {
   this.getData();
  },
  methods: {
    getData() {

      if( !this.characterId){
        return
      }
      let self = this;
      this.tableData.loading = true;
      this.$utils.httpPost(
        this.$config.api.STUDIO_CHARACTER_ATTRIBUTES,
        {
          repositoryId: this.characterId,
          category: parseInt(this.activeName),
          pageIndex: this.tableData.page,
          pageSize: 999
        },
        {
          success: res => {
            self.tableData.list = (res.data.list);
            self.tableData.total = res.data.count;
            self.tableData.page = res.data.pageIndex;
            self.tableData.size = res.data.pageSize;
            self.tableData.loading = false;
            // self.characterName = res.data.context.name;
            // self.characterId = res.data.context.id;
            this.$emit("getTableData", res.data);
            this.originTableData = this.$deepClone(self.tableData);
            // this.tabData["tab" + this.activeName] = this.$deepClone(
            //   self.tableData
            // );
          },
          error: err => {
            console.error(err);
          }
        }
      );
    }
  }
};
</script>
<style lang="scss" scoped>

</style>