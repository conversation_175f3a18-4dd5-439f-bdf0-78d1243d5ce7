<template>
  <div style="padding: 40px;">
    <p style="margin-bottom: 20px;">ubot 测试环境登陆页</p>
    <el-form :model="loginForm" label-position="top" ref="loginForm" label-width="100px" class="login-reg-form mgb48">
      <el-form-item label="账号" prop="username">
        <el-input type="input" v-model="loginForm.username" placeholder="请输入手机号/邮箱/用户名" auto-complete="off"></el-input>
      </el-form-item>
      <el-form-item label="密码" prop="password">
        <el-input type="password" v-model="loginForm.password" placeholder="请输入密码" auto-complete="off"></el-input>
      </el-form-item>
    </el-form>
    <div class="mgb24">
      <el-button
        class="login-btn"
        type="primary"
        @click="submit"
      >登录</el-button>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import SSO from 'sso/sso.js'
import { Base64 } from 'js-base64'
import Axios from 'axios'
import md5 from 'md5-js'

export default {
  name: 'login',
  data () {
    return {
      loginForm: {
        username: '',
        password: ''
      }
    };
  },
  computed: {
    ...mapGetters({
      subAccount: 'user/subAccount'
    })
  },
  methods: {
    submit() {
      let self = this
      let data = {
        accountName: this.loginForm.username,
        accountPwd: self.loginForm.password
      }
      if(this.subAccount) {
        data.accountPwd = md5(self.loginForm.password)
        data.isMd5 = true
        return self.subAccountLogin(data)
      }
      data.jump = this.$route.query.pageFrom || window.location.origin + '/robots/my'
      data.isAct = true
      SSO.login(data, (err) => {
        if (err.desc) {
          self.$message.error(err.desc)
        }
      }, (res, cb) => {
        self.$message.success('登录成功，正在跳转')
        cb && cb()
      })
    },
    subAccountLogin(data) {
      let self = this
      let jump = this.$route.query.pageFrom || window.location.origin + '/sub/skills'
      this.$utils.httpPost(self.$config.api.COOP_SUB_ACCOUNT_LOGIN, data, {
        success: (res) => {
          self.setcookies(res.data.subSessionId, res.data.sub_account_id)
          const {subSessionId = null, sub_account_id = null} = res.data
          subSessionId && self.$utils.setCookie('subSessionId', subSessionId)
          sub_account_id && self.$utils.setCookie('sub_account_id', sub_account_id)
          localStorage.setItem("firstEnterSkill", "1");
          localStorage.setItem("firstEnterEntity", "1");
          localStorage.setItem("firstEnterAuxiliary", "1");
          self.$message.success('登录成功，正在跳转')
        },
        error: err => {
          console.log(err)
        }
      })
    },
    setcookies(subSessionId, sub_account_id) {
      let self = this
      let jump = this.$route.query.pageFrom || window.location.origin + '/sub/skills'
      window.location.href =
        `${window.location.origin}/aiui/subweb/user/sub/login/setcookies?`
        + `action=add`
        + `&url=${Base64.encode(jump)}`
        + `&subSessionId=${subSessionId}`
        + `&sub_account_id=${sub_account_id}`
    }
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="scss" scoped>

</style>
