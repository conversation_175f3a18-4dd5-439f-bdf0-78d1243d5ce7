<template>
  <os-page :options="pageOptions">
    <studio-skill-header-right slot="btn" />
    <div class="os-scroll">
      <div class="mgt32 mgb24" @keyup.enter="searchEntities">
        <div class="btn-wrap">
          <el-button
            size="medium"
            :type="activeType == 'all' ? 'primary' : ''"
            plain
            @click="activeType = 'all'"
            >全部</el-button
          >
          <el-tooltip effect="dark" content="源技能的实体" placement="top">
            <el-button
              size="medium"
              :type="activeType == 'sourceSkill' ? 'primary' : ''"
              plain
              @click="activeType = 'sourceSkill'"
              >源技能的</el-button
            >
          </el-tooltip>
          <el-tooltip
            effect="dark"
            content="所有新增的非源技能的实体"
            placement="top"
          >
            <el-button
              size="medium"
              :type="activeType == 'notSourceSkill' ? 'primary' : ''"
              plain
              @click="activeType = 'notSourceSkill'"
              >新增</el-button
            >
          </el-tooltip>
          <el-tooltip effect="dark" content="所有官方实体" placement="top">
            <el-button
              size="medium"
              :type="activeType == 'iflyos' ? 'primary' : ''"
              plain
              @click="activeType = 'iflyos'"
              >官方</el-button
            >
          </el-tooltip>
          <el-tooltip effect="dark" content="所有自定义实体" placement="top">
            <el-button
              size="medium"
              :type="activeType == 'self' ? 'primary' : ''"
              plain
              @click="activeType = 'self'"
              >自定义</el-button
            >
          </el-tooltip>
        </div>
        <el-input
          class="search-area"
          size="medium"
          placeholder="搜索引用的实体"
          v-model="entitySearchName"
        >
          <i
            slot="suffix"
            class="el-input__icon el-icon-search search-area-btn"
            @click="searchEntities"
          />
        </el-input>
      </div>
      <os-table
        class="extend-skill-entities gutter-table-style secondary-table"
        :tableData="tableData"
        @change="changePage"
        @row-click="toEdit"
      >
        <el-table-column type="index" width="50" label="#">
          <template slot-scope="scope">
            {{ (tableData.page - 1) * tableData.size + scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column min-width="40%" prop="zhName" label="实体名称">
          <div class="zhName-wrap" slot-scope="scope">
            <div class="entity-zhname ib" :title="scope.row.zhName">
              {{ scope.row.zhName }}
            </div>
            <el-tooltip
              v-if="scope.row.source == 1"
              effect="dark"
              content="源技能的实体"
              placement="top"
            >
              <div class="intent-tag ib source-skill-icon">源</div>
            </el-tooltip>
            <template v-if="scope.row.type === 1">
              <div class="intent-tag ib">官</div>
            </template>
            <i class="extend-icon" v-if="scope.row.isExtend"></i>
          </div>
        </el-table-column>
        <el-table-column label="实体标识" min-width="40%">
          <template slot-scope="scope">
            <span :title="scope.row.name || '-'">{{
              scope.row.name || '-'
            }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="描述"> </el-table-column>
        <el-table-column prop="example" label="示例"> </el-table-column>
        <el-table-column prop="count" label="词条数"> </el-table-column>
        <el-table-column label="操作" width="120">
          <template slot-scope="scope">
            <span
              style="color: #1784e9; cursor: pointer"
              v-if="
                scope.row.isExtend || scope.row.hasOwnProperty('dictDetail')
              "
              >扩展</span
            >
            <!--官方实体和源技能实体不能有 编辑icon-->
            <!-- 2022.9.2 修改： type 为3，且other字段有值为true，表示是引用的别人的，不显示编辑按钮 -->
            <i
              v-if="
                ([1, 2, 3].indexOf(scope.row.type) == -1 &&
                  !scope.row.source) ||
                (scope.row.type == 3 && !scope.row.other)
              "
              class="ic-r-edit cell-handle-ic"
            ></i>
          </template>
        </el-table-column>
      </os-table>
    </div>
  </os-page>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'extend-skill-entities',
  data() {
    return {
      pageOptions: {
        title: '引用的实体',
        loading: false,
      },
      activeType: 'all',
      entitySearchName: '',
      tableData: {
        loading: true,
        total: 0,
        page: 1,
        size: 10,
        // handles: ['edit'],
        handleColumnText: '操作',
        list: [],
      },
      originEntityList: [],
    }
  },
  computed: {
    ...mapGetters({
      subAccount: 'user/subAccount',
    }),
    businessId() {
      return this.$store.state.studioSkill.id
    },
  },
  watch: {
    activeType(val) {
      this.formate(val, 1)
    },
  },
  created() {
    this.getEntities()
  },
  methods: {
    changePage(val) {
      this.tableData.page = val
      this.formate(this.activeType)
    },
    formate(type, initPage) {
      let tmp
      let list = []
      this.tableData.page = initPage || this.tableData.page
      let page = this.tableData.page
      let size = this.tableData.size
      for (let i = 0; i < this.originEntityList.length; i++) {
        let item = this.originEntityList[i]
        if (type == 'all') {
          if (
            (item.type === 1 &&
              !item.hasOwnProperty('dictDetail') &&
              !item.hasOwnProperty('noEdit')) ||
            item.type == 3 ||
            item.type == 5
          ) {
            item.noEdit = true
          }
          list.push(item)
        }
        if (
          type == 'iflyos' &&
          item.type === 1 &&
          !item.hasOwnProperty('dictDetail')
        ) {
          if (!item.hasOwnProperty('noEdit')) {
            item.noEdit = true
          }
          list.push(item)
        }
        if (type == 'sourceSkill' && item.source === 1) {
          if (
            !item.hasOwnProperty('noEdit') ||
            item.type == 3 ||
            item.type == 5
          ) {
            item.noEdit = true
          }
          list.push(item)
        }
        if (type == 'notSourceSkill' && item.source !== 1) {
          list.push(item)
        }
        if (type == 'self' && item.type !== 1 && item.source !== 1) {
          list.push(item)
        }
      }
      this.tableData.total = list.length
      this.tableData.list.splice(0)
      this.tableData.list = list.slice((page - 1) * size, page * size)
      this.tableData.loading = false
    },
    getEntities(page) {
      this.tableData.loading = true
      this.$utils.httpGet(
        this.$config.api.STUDIO_SKILL_ENTITYS,
        {
          businessId: this.businessId,
          pageIndex: page || this.tableData.page,
          search: this.entitySearchName,
          type: 0,
        },
        {
          success: (res) => {
            this.originEntityList = res.data.entities
            this.formate(this.activeType, 1)
          },
          error: (err) => {},
        }
      )
    },
    searchEntities() {
      this.getEntities(1)
    },
    toEdit(entity) {
      if (entity.source == 1 && [1, 2].indexOf(entity.type) == -1) {
        return this.$message.warning('只能定制源技能的静态实体和官方实体')
      }
      let routeData
      let pathName = this.subAccount
        ? 'extend-sub-skill-entity'
        : 'extend-skill-entity'
      if ([1, 2].indexOf(entity.type) != -1) {
        if (!entity.hasOwnProperty('dictDetail')) {
          this.$utils.httpGet(
            this.$config.api.STUDIO_EXTEND_ENTITY_CREATE,
            {
              entityId: entity.id,
              businessId: this.businessId,
            },
            {
              success: (res) => {
                if (res && res.data) {
                  this.$router.push({
                    name: pathName,
                    params: {
                      skillId: this.businessId,
                      addDictId: res.data.addDictId,
                      delDictId: res.data.delDictId,
                    },
                    query: { name: entity.zhName },
                  })
                }
              },
              error: (err) => {},
            }
          )
        } else {
          this.$router.push({
            name: pathName,
            params: {
              skillId: this.businessId,
              addDictId: entity.dictDetail.addDictId,
              delDictId: entity.dictDetail.delDictId,
            },
            query: { name: entity.zhName },
          })
        }
      } else {
        // 为3时要判断，other字段为true时，表示引用别人的实体，不允许跳转编辑
        if (!entity.other) {
          routeData = this.$router.resolve({
            name: 'entity',
            params: { entityId: entity.id },
          })
          window.open(routeData.href, '_blank')
        }
      }
    },
  },
  components: {},
}
</script>

<style lang="scss" scoped>
.search-area {
  float: right;
  width: 240px;
}
.search-area-btn {
  cursor: pointer;
}
.intent-handle-group {
  position: relative;
  margin-right: -3px;
  &::after {
    position: absolute;
    content: ' ';
    width: 1px;
    height: 100%;
    top: 0;
    right: -1px;
    background-color: $grey3;
  }
}
.intent-zhname {
  margin-right: 14px;
  cursor: pointer;
  font-weight: 600;
}

.btn-wrap {
  display: inline-block;
  font-size: 0;
  .el-button {
    margin-left: 0;
    padding: 10px 16px;
    min-width: unset;
    background: $white;
    border-color: $grey3;
    border-right: none;
    border-radius: 0;
    &:first-child {
      border-radius: 2px 0 0 2px;
    }
    &:last-child {
      border-right: 1px solid $grey3;
      border-radius: 0 2px 2px 0;
      &:hover {
        border-right: 1px solid $primary;
      }
    }
  }
  .el-button:hover,
  .el-button:focus,
  .el-button--primary {
    border-color: $primary;
    background-color: $primary-light-12;
  }
  .el-button:hover + .el-button {
    border-left-color: $primary;
  }
  .el-button:focus + .el-button,
  .el-button--primary + .el-button {
    border-left-color: transparent;
  }
  .el-button--primary:hover,
  .el-button--primary:focus {
    box-shadow: unset;
  }
  .el-button--primary {
    color: $primary;
    border: 1px solid $primary !important;
  }
}

.zhName-wrap {
  font-size: 0;
}
.entity-zhname {
  vertical-align: bottom;
  margin-right: 7px;
  max-width: calc(100% - 32px);
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
}
.extend-icon {
  display: inline-block;
  margin-left: 5px;
  vertical-align: middle;
  width: 12px;
  height: 12px;
  background: url('../../../../assets/svg/extendSkill/r-extend.svg') center
    no-repeat;
  background-size: 100%;
}
.cell-handle-ic {
  color: $primary;
}
.source-skill-icon {
  margin-right: 4px;
}
</style>
<style lang="scss">
.extend-skill-entities {
  .cell {
    white-space: nowrap;
  }
}
</style>
