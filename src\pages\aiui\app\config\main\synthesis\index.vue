<template>
  <card>
    <template #title>
      语音合成配置 &nbsp;
      <el-switch
        v-if="currentScene && currentScene.chainId !== 'sos_app'"
        class="mgr16"
        :disabled="!subAccountEditable"
        :value="isOn"
        @change="configSwitch"
      ></el-switch
    ></template>

    <synthesisSOSAPP
      v-if="currentScene && currentScene.chainId === 'sos_app'"
      ref="synthesis"
      :appId="appId"
      :appInfo="app"
      :saving="saving"
      :status="true"
      :currentScene="currentScene"
      :subAccountEditable="subAccountEditable"
    >
    </synthesisSOSAPP>
    <synthesis v-else ref="synthesis"></synthesis>
  </card>
</template>
<script>
import card from '../components/card'
import { mapGetters } from 'vuex'
import synthesisSOSAPP from './synthesisSOSAPP.vue'
import synthesis from './synthesis.vue'
import RECOGNITION_SEMANTIC_SYNTHESIS_State from '@U/AIUIState/RECOGNITION_SEMANTIC_SYNTHESIS_State'
import RECOGNITION_TRANSLATE_SYNTHESIS_State from '@U/AIUIState/RECOGNITION_TRANSLATE_SYNTHESIS_State'
import RECOGNITION_LLM_SEMANTIC_SYNTHESIS_State from '@U/AIUIState/RECOGNITION_LLM_SEMANTIC_SYNTHESIS_State'
import RECOGNITION_SYNTHESIS_State from '@U/AIUIState/RECOGNITION_SYNTHESIS_State'
import RECOGNITION_SEMANTIC_POSTPROCESS_SYNTHESIS_State from '@U/AIUIState/RECOGNITION_SEMANTIC_POSTPROCESS_SYNTHESIS_State'
import RECOGNITION_POSTPROCESS_SYNTHESIS_State from '@U/AIUIState/RECOGNITION_POSTPROCESS_SYNTHESIS_State'
export default {
  name: 'sys',
  components: {
    synthesis,
    synthesisSOSAPP,
    card,
  },
  computed: {
    ...mapGetters({
      currentScene: 'aiuiApp/currentScene',
      app: 'aiuiApp/app',
      limitCount: 'aiuiApp/limitCount',
      subAccountEditable: 'aiuiApp/subAccountEditable',
      context: 'aiuiApp/context',
    }),
    appId() {
      return this.$route.params.appId
    },
    isOn() {
      if (this.currentScene && this.currentScene.point && this.context) {
        const point = this.currentScene.point
        const context = this.context
        if (
          context.isCurrentState(RECOGNITION_SEMANTIC_SYNTHESIS_State) ||
          context.isCurrentState(RECOGNITION_TRANSLATE_SYNTHESIS_State) ||
          context.isCurrentState(RECOGNITION_LLM_SEMANTIC_SYNTHESIS_State) ||
          context.isCurrentState(RECOGNITION_SYNTHESIS_State) ||
          context.isCurrentState(
            RECOGNITION_SEMANTIC_POSTPROCESS_SYNTHESIS_State
          ) ||
          context.isCurrentState(RECOGNITION_POSTPROCESS_SYNTHESIS_State)
        ) {
          return true
        }
      }
      return false
    },
  },
  data() {
    return {
      showControl: {},
    }
  },
  created() {},
  methods: {
    configSwitch(val) {
      if (val) {
        this.$store.dispatch('aiuiApp/addSwitches', '8')
      } else {
        this.$store.dispatch('aiuiApp/removeSwitch', '8')
      }
    },
  },
  watch: {},
}
</script>
<style lang="scss" scoped></style>
