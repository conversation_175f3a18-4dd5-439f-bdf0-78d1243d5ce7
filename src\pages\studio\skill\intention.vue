<template>
  <os-page class="intent-page" :options="pageOptions" @returnCb="pageReturn">
    <div slot="headLeft">
      <span class="intention-title" :title="originalIntention.zhName || '-'">{{
        originalIntention.zhName || '-'
      }}</span>
    </div>
    <studio-skill-header-right slot="btn" />
    <div class="intention-page">
      <os-collapse
        :default="false"
        size="large"
        title="意图信息"
        style="position: relative"
      >
        <template v-if="subAccountEditable">
          <div
            v-if="!edit"
            class="ib intention-edit-btn"
            @click.prevent.stop="toEdit"
          >
            <i class="ic-r-edit" />
            <span>编辑</span>
          </div>
        </template>

        <el-form
          class="intent-info-form"
          :model="editIntention"
          :rules="rules"
          ref="intentForm"
          label-width="100px"
          label-position="left"
        >
          <el-form-item prop="zhName" class="inline-el-form-item">
            <label slot="label">
              <span class="intention-form-label">意图名称</span>
            </label>
            <div class="intent-zh-name" v-if="!edit" :title="intention.zhName">
              {{ intention.zhName }}
            </div>
            <el-input
              v-else
              v-model="editIntention.zhName"
              :title="intention.zhName"
            />
          </el-form-item>
          <el-form-item prop="name" class="inline-el-form-item">
            <label slot="label">
              <span class="intention-form-label">英文标识</span>
            </label>
            <div class="intent-name" v-if="!edit" :title="intention.name">
              {{ intention.name }}
            </div>
            <el-input
              v-else
              v-model="editIntention.name"
              :title="intention.name"
            />
          </el-form-item>
          <el-form-item
            v-if="(skill && skill.privateSkill) || skill.secondType == 2"
          >
            <label slot="label">
              <span class="intention-form-label">意图类型</span>
            </label>
            <span v-if="!edit">{{
              intention.type === 2 ? '入口' : '对话'
            }}</span>
            <el-switch
              class="intention-head-hovershow"
              v-else
              v-model="editIntention.type"
              :active-value="2"
              :inactive-value="3"
              :active-text="editIntention.type === 2 ? '入口' : '对话'"
            >
            </el-switch>
          </el-form-item>
          <el-form-item v-if="edit">
            <el-button
              type="primary"
              size="small"
              style="min-width: 80px"
              @click="editSubmit('intentForm')"
              :loading="editSaveLoading"
            >
              {{ editSaveLoading ? '保存中...' : '保存' }}
            </el-button>
            <el-button
              size="small"
              style="min-width: 80px"
              v-on:click="
                edit = !edit
                $refs.intentForm.resetFields()
              "
            >
              取消
            </el-button>
          </el-form-item>
        </el-form>
      </os-collapse>
      <os-divider class="mgt28" />
      <os-collapse :default="true" size="large">
        <template slot="title">
          语料
          <el-tooltip
            class="item"
            effect="dark"
            content="你可以在此添加一些用户常用的表述"
            placement="right"
          >
            <i class="el-icon-question" />
          </el-tooltip>
          <div style="flex: auto; margin-left: 20px">
            <el-checkbox
              v-model="arcSwitch"
              @change="changeArcSwitch"
              :disabled="!subAccountEditable"
              >智能贴弧(beta)</el-checkbox
            >
            <el-tooltip
              content="系统会自动识别你输入的文本中包含的槽，进行实体匹配"
              placement="right"
            >
              <i class="el-icon-question" />
            </el-tooltip>
          </div>
          <div v-on:click.stop>
            <div class="fr" @keyup.enter="searchUtterance">
              <el-input
                class="search-area"
                size="medium"
                placeholder="搜索语料"
                v-model="utteranceSearchName"
              >
                <i
                  slot="suffix"
                  class="el-input__icon el-icon-search search-area-btn"
                  @click="searchUtterance"
                />
              </el-input>
            </div>
          </div>
        </template>
        <p class="utterance-edit-tip">
          你可以通过
          <a :href="`${$config.docs}doc-47/`" target="_blank">例句语料</a>
          或
          <a :href="`${$config.docs}doc-47/`" target="_blank"> 模板语料 </a
          >两种方式新增语料
        </p>
        <div class="mgb24" @keyup.enter="addUtteranceBlur">
          <el-input
            class="utterance-add-area"
            ref="addUtter"
            placeholder="回车添加用户的常用表达，例如：明天合肥天气怎么样 / 或：{date}{city}天气怎么样"
            :disabled="!subAccountEditable"
            v-model="utteranceAddName"
            @blur="addUtterance"
            @keyup.native="angleBracketHandle"
            @keyup.native.down.stop.prevent="handleDown"
          >
            <i slot="prefix" class="el-input__icon ic-r-plus"></i>
            <el-button
              slot="suffix"
              type="text"
              class="utterance-add-area-addbtn"
              size="small"
            >
              添加
            </el-button>
          </el-input>
        </div>
        <div class="utterance-collapse" v-loading="utterancesData.loading">
          <div
            v-for="(utterance, index) in oldUtterances"
            class="utterance-collapse-item"
            :key="index"
          >
            <div
              class="utterance-collapse-item-title"
              :class="{
                'utterance-collapse-item-title-show':
                  collapseActive === utterance.id,
              }"
              @click="handleCollapse(utterance, $event)"
            >
              <os-utterance
                :defaultUtterance="utterance"
                :defaultIndex="index"
                @del="beforeDel"
                @change="refreshData"
                @editScore="editScore"
                :subAccountEditable="subAccountEditable"
              />
            </div>
            <div
              class="utterance-collapse-item-content"
              :class="{
                'utterance-collapse-item-content-show':
                  collapseActive === utterance.id,
              }"
            >
              <utterance-slot-table
                :utterance="utterance"
                :subAccountEditable="subAccountEditable"
                @change="refreshData"
              />
            </div>
          </div>
        </div>

        <div>
          <el-pagination
            ref="pagination"
            v-if="showPagination"
            class="txt-al-c"
            @current-change="getUtterances"
            :current-page="utterancesData.page"
            :page-size="utterancesData.size"
            :total="utterancesData.total"
            :layout="pageLayout"
          >
          </el-pagination>
        </div>
      </os-collapse>
      <os-divider class="mgt28" />
      <os-collapse :default="true" size="large" title="实体">
        <os-table
          class="slot-table gutter-table-style secondary-table-thead"
          :border="true"
          :tableData="slotsData"
          ref="slotTable"
        >
          <el-table-column label="槽位标识" width="120">
            <template slot-scope="scope">
              <div
                class="slot-table-slotname"
                @keyup.enter="changeSlotNameBlur"
              >
                <span v-if="!subAccountEditable">{{ scope.row.slotName }}</span>
                <el-input
                  v-else
                  class="search-area"
                  size="medium"
                  placeholder="请输入槽位标识"
                  v-model="scope.row.slotName"
                  @blur="changeSlotName(scope.row, scope.$index)"
                >
                </el-input>
              </div>
            </template>
          </el-table-column>
          <el-table-column width="200" label="对应实体">
            <!-- <template slot="header" slot-scope="scope">
              <os-table-qahead label="对应实体" tip="对应实体" />
            </template> -->
            <template slot-scope="scope">
              <div v-if="scope.row.entityName">
                <span
                  :style="utteranceColor[scope.row.slotName]"
                  @click.stop="openSelectEntity(scope.row, $event)"
                >
                  @{{ scope.row.entityName }}
                </span>
                <i
                  v-if="
                    scope.row.entityName.match('IFLYTEK.Wildcard') &&
                    subAccountEditable
                  "
                  class="ic-r-edit cp wildcard"
                  style="margin-left: 8px"
                  @click="openSetWildcardDialog(scope.row, $event)"
                />
                <i
                  v-if="scope.row.entityType !== 1"
                  class="ic-r-edit cp"
                  style="margin-left: 8px"
                  @click="toEditEntity(scope.row)"
                />
              </div>
              <a v-else @click.stop="openSelectEntity(scope.row, $event)"
                >设置对应实体</a
              >
            </template>
          </el-table-column>
          <!-- qc技能无【对话必须】和【追问话术】 -->
          <template v-if="skill && skill.type != 8">
            <el-table-column width="100">
              <template slot="header" slot-scope="scope">
                <os-table-qahead
                  label="对话必须"
                  tip="请勾选该意图下的必选槽值"
                />
              </template>
              <template slot-scope="scope">
                <el-checkbox
                  v-model="scope.row.necessary"
                  :true-label="1"
                  :false-label="0"
                  :disabled="!scope.row.entityName || !subAccountEditable"
                  :change="necessaryChange(scope.row, scope.$index)"
                />
              </template>
            </el-table-column>
            <el-table-column width="230">
              <template slot="header" slot-scope="scope">
                <os-table-qahead
                  label="追问话术"
                  tip="当槽值缺失时，系统追问用户的话术"
                />
              </template>
              <template slot-scope="scope">
                <div v-if="scope.row.necessary">
                  <a
                    class="flex"
                    v-if="
                      scope.row.questionSentence &&
                      scope.row.questionSentence.length
                    "
                    @click="openAddQaDialog(scope.row)"
                  >
                    <span class="txt-ellipsis-nowrap">{{
                      scope.row.questionSentence[0].answer
                    }}</span>
                    [{{ scope.row.questionSentence.length }}]
                  </a>
                  <a v-else @click="openAddQaDialog(scope.row)">添加</a>
                </div>
                <div v-else>
                  <span>-</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              label="回答语料"
              width="230"
              v-if="skill.protocolVersion === '2.1'"
            >
              <template slot-scope="scope">
                <a
                  class="flex"
                  v-if="scope.row.answers && scope.row.answers.length"
                  @click="openReplyDialog(scope.row)"
                >
                  <span class="txt-ellipsis-nowrap">
                    {{ scope.row.answers[0] }}
                  </span>
                  <span style="min-width: 30px"
                    >[{{ scope.row.answers.length }}]</span
                  >
                </a>
                <a v-else @click="openReplyDialog(scope.row)">添加</a>
              </template>
            </el-table-column>
          </template>
        </os-table>
      </os-collapse>
      <os-divider class="mgt28" />
      <os-collapse :default="true" size="large">
        <template slot="title">
          辅助词
          <el-tooltip
            content="辅助词指的是在用户的表述中会出现的词汇，但是这些词汇开发者并不关心其具体值，其存在的意义是保证表述完整，例如常见的辅助词有：请问、查看、如何。"
            placement="right"
          >
            <i class="el-icon-question"></i>
          </el-tooltip>
        </template>
        <os-table
          class="slot-table gutter-table-style secondary-table-thead"
          :border="true"
          :tableData="auxiliarySlotsData"
          ref="auxiliarySlotTable"
        >
          <el-table-column label="槽位标识" width="120">
            <template slot-scope="scope">
              <div
                class="slot-table-slotname"
                @keyup.enter="changeSlotNameBlur"
              >
                <span v-if="!subAccountEditable">{{ scope.row.slotName }}</span>
                <el-input
                  v-else
                  class="search-area"
                  size="medium"
                  placeholder="请输入槽位标识"
                  v-model="scope.row.slotName"
                  @blur="changeSlotName(scope.row, scope.$index)"
                >
                </el-input>
              </div>
            </template>
          </el-table-column>
          <el-table-column width="200" label="对应辅助词">
            <template slot-scope="scope">
              <span
                v-if="scope.row.entityName"
                :style="utteranceColor[scope.row.slotName]"
                @click.stop="openSelectEntity(scope.row, $event)"
              >
                #{{ scope.row.entityName }}
              </span>
              <a v-else @click.stop="openSelectEntity(scope.row, $event)"
                >设置对应辅助词</a
              >
            </template>
          </el-table-column>
        </os-table>
      </os-collapse>
      <os-divider class="mgt28" />

      <modifier
        v-if="intention.id"
        ref="modifierTable"
        :intention="intention"
        :subAccountEditable="subAccountEditable"
        @change="refreshData"
      ></modifier>

      <!-- <os-divider class="mgt28" /> -->

      <intention-reply
        @setEditSaveLoading="setEditSaveLoading"
        :intentionObj="intention"
        :intentId="intentId"
        :subAccountEditable="subAccountEditable"
        :slotNames="necessarySlotNames"
      ></intention-reply>
      <os-divider class="mgt28" />
      <intention-confirm
        @setEditSaveLoading="setEditSaveLoading"
        :intentionObj="intention"
        :intentId="intentId"
        :subAccountEditable="subAccountEditable"
        :slotNames="necessarySlotNames"
      ></intention-confirm>
    </div>
    <set-wildcard-dialog
      :variablePopover="wildCardPopover"
      @change="getSlots"
    />
    <add-qa-dialog
      :dialog="addQaDialog"
      :subAccountEditable="subAccountEditable"
      @change="getSlots"
    />
    <select-entity-popover @change="refreshData" :showType="'intention'" />
    <utterance-select-entity-popover @change="refreshData" />
    <reply-dialog
      :dialog="replyDialog"
      :slotId="currentSlotId"
      :businessId="businessId"
      :intentId="intentId"
      :subAccountEditable="subAccountEditable"
      :qaSentence="qaSentence"
      :slotNames="slotNames"
      @change="getSlots"
    ></reply-dialog>
    <os-modifier-select
      ref="modifierSelectPopover"
      :variablePopover="modifierSelectPopover"
      inAddInput="true"
      @setModifier="setModifier"
    />
  </os-page>
</template>

<script>
import { mapGetters } from 'vuex'
import SetWildcardDialog from './dialog/setWildcard.vue'
import AddQaDialog from './dialog/addQa.vue'
import SelectEntityPopover from './dialog/selectEntity.vue'
import UtteranceSlotTable from './utteranceSlotTable.vue'
import UtteranceSelectEntityPopover from './dialog/utteranceSelectEntityPopover.vue'
import ReplyDialog from './dialog/replyDialog.vue'
import Modifier from './modifierInintention'
import intentionConfirmTextAdder from './intentionConfirmTextAdder.vue'
import intentionConfirm from './intentionConfirm.vue'
import intentionReply from './intentionReply.vue'

export default {
  data() {
    return {
      pageOptions: {
        loading: false,
        returnBtn: true,
        showHead: true,
      },
      intentId: '',
      intention: {},
      // 编辑
      edit: false,
      editSaveLoading: false,
      editIntention: {
        name: '',
        zhName: '',
      },
      rules: {
        name: [
          this.$rules.required('英文标识不能为空'),
          this.$rules.lengthLimit(1, 32, '英文标识长度不能超过32个字符'),
          this.$rules.englishReglimitForSkillIntent(),
          { validator: this.checkBoundary, trigger: ['blur'] },
        ],
        zhName: [
          this.$rules.required('意图名称不能为空'),
          this.$rules.lengthLimit(1, 32, '意图名称长度不能超过32个字符'),
          this.$rules.limitForSkillIntent(),
          { validator: this.checkBoundary, trigger: ['blur'] },
        ],
      },
      textReg: /^[\u4e00-\u9fffa-zA-Z0-9 {}_?？°]{1,50}$/,
      // 语料
      utteranceSearchName: '',
      utteranceAddName: '',
      collapseActive: false,
      utterancesData: {
        loading: true,
        total: 0,
        page: 1,
        size: 10,
        list: [],
      },
      oldUtterances: [],
      // 实体
      slotsData: {
        loading: true,
        total: 0,
        page: 1,
        size: 5,
        // handles: ['del'],
        list: [],
      },
      slotNames: [],
      necessarySlotNames: [],
      // 辅助词
      auxiliarySlotsData: {
        loading: true,
        total: 0,
        page: 1,
        size: 5,
        // handles: ['del'],
        list: [],
      },
      oldAuxiliarySlotList: [],
      oldSlotList: [],
      addQaDialog: {
        show: false,
        data: [],
      },
      fzyValueList: [], //语料kv值 fzy对应的value汇总
      arcSwitch: true,
      replyDialog: {
        show: false,
      },
      currentSlotId: '',
      qaSentence: '',
      cursorPos: -1,
      modifierSelectPopover: {
        show: false,
        rect: null,
      },
      wildCardPopover: {
        show: false,
        rect: null,
      },
    }
  },
  computed: {
    ...mapGetters({
      businessId: 'studioSkill/id',
      originalIntention: 'studioSkill/intention',
      utteranceColor: 'studioSkill/utteranceColor',
      skill: 'studioSkill/skill',
      subAccountSkillAuths: 'studioSkill/subAccountSkillAuths',
    }),
    pageLayout() {
      if (this.utterancesData.total / this.utterancesData.size > 7) {
        return 'prev, pager, next, jumper'
      }
      return 'prev, pager, next'
    },
    showPagination() {
      return this.utterancesData.total > this.utterancesData.size
    },
    subAccountEditable() {
      return this.subAccountSkillAuths[this.businessId] == 2 ? false : true
    },

    limitCount() {
      return this.$store.state.aiuiApp.limitCount
    },
  },
  watch: {
    originalIntention: function (val, oldVal) {
      this.intention = this.$deepClone(val)
      if (this.intention.hasOwnProperty('arcSwitch')) {
        this.arcSwitch = this.intention.arcSwitch == 1 ? true : false
      } else {
        this.arcSwitch = true
        this.intention.arcSwitch = 1
      }
    },
    utteranceColor: function (val, oldVal) {
      this.$refs.slotTable && this.$refs.slotTable.doLayout()
      this.$refs.auxiliarySlotTable && this.$refs.auxiliarySlotTable.doLayout()
    },
  },
  created() {
    if (!this.$route.params.intentId) {
      this.$router.push({ name: 'skill-intentions' })
    } else {
      this.intentId = this.$route.params.intentId
      this.$store.dispatch(
        'studioSkill/setIntention',
        this.$route.params.intentId
      )
      this.getUtterances()
      this.getSlots()
    }
  },
  mounted() {
    document.getElementById('scrollDomDiv').style.paddingBottom = '300px'
  },
  beforeRouteLeave(to, from, next) {
    document.getElementById('scrollDomDiv').style.paddingBottom = '70px'
    next()
  },
  methods: {
    setEditSaveLoading(val) {
      this.editSaveLoading = val
    },
    checkBoundary(rule, value, callback) {
      let reg = /^iFLYTEK\./i
      if (reg.test(value)) {
        callback(new Error('不能以iFLYTEK. 开头，不分大小写'))
      }
      callback()
    },
    changeArcSwitch() {
      let self = this
      this.$utils.httpPost(
        this.$config.api.STUDIO_ADD_EDIT_INTENT,
        {
          businessId: this.businessId,
          id: this.originalIntention.id,
          type: this.intention.type,
          arcSwitch: this.arcSwitch ? 1 : 0,
        },
        {
          success: (res) => {
            self.$message.success('保存成功')
            self.$store.dispatch(
              'studioSkill/setIntention',
              self.$route.params.intentId
            )
          },
          error: (err) => {},
        }
      )
    },
    // 获取语料列表
    getUtterances(page) {
      let self = this
      this.utterancesData.loading = true
      this.$utils.httpGet(
        this.$config.api.STUDIO_INTENT_UTTERANCES,
        {
          businessId: this.businessId,
          intentId: this.intentId,
          pageIndex: page || this.utterancesData.page,
          pageSize: this.utterancesData.size,
          utterance: this.utteranceSearchName,
          type: 1, //非业务定制 传1
        },
        {
          success: (res) => {
            self.utterancesData.list = res.data.utterances
            self.oldUtterances = []
            self.utterancesData.total = res.data.count
            self.utterancesData.page = res.data.pageIndex
            self.utterancesData.size = res.data.pageSize
            self.$nextTick(function () {
              self.oldUtterances = JSON.parse(
                JSON.stringify(res.data.utterances)
              )
            })
            self.utterancesData.loading = false
            self.getCurrentUtterFzy(self.utterancesData.list)
          },
          error: (err) => {
            self.utterancesData.loading = false
          },
        }
      )
    },
    getSlots() {
      this.getAuxiliarySlots()
      this.getEntitySlots()
    },
    // 获取辅助词槽位
    getAuxiliarySlots() {
      let self = this
      this.auxiliarySlotsData.loading = true
      this.$utils.httpGet(
        this.$config.api.STUDIO_INTENT_SLOTS,
        {
          businessId: this.businessId,
          intentId: this.intentId,
          type: 1,
        },
        {
          success: (res) => {
            res.data.forEach(function (item, index) {
              if (item.slotName) {
                self.$store.dispatch(
                  'studioSkill/setUtteranceColor',
                  item.slotName
                )
              }
            })
            this.auxiliarySlotsData.list = res.data
            this.oldAuxiliarySlotList = JSON.parse(JSON.stringify(res.data))
            this.auxiliarySlotsData.loading = false
          },
          error: (err) => {},
        }
      )
    },
    // 获取实体槽位
    getEntitySlots() {
      let self = this
      this.slotsData.loading = true
      this.$utils.httpGet(
        this.$config.api.STUDIO_INTENT_SLOTS,
        {
          businessId: this.businessId,
          intentId: this.intentId,
          type: 0,
        },
        {
          success: (res) => {
            let len = res.data && res.data.length
            let tmp = {}
            for (let i = 0; i < len; i++) {
              if (res.data[i].slotName) {
                self.$store.dispatch(
                  'studioSkill/setUtteranceColor',
                  res.data[i].slotName
                )
              }
              tmp[res.data[i].slotName] = res.data[i].entityName
            }
            self.$store.commit('studioSkill/setEntityInIntent', tmp)
            this.slotsData.list = res.data
            this.slotsData.list.forEach((item) => {
              this.slotNames.push(item.slotName)
              if (item.necessary == '1') {
                this.necessarySlotNames.push(item.slotName)
              }
            })
            this.oldSlotList = JSON.parse(JSON.stringify(res.data))
            this.slotsData.loading = false
          },
          error: (err) => {},
        }
      )
    },
    pageReturn() {
      this.$router.push({ name: 'skill-intentions' })
    },
    // <--------获取数据end-------->
    toEdit() {
      this.edit = true
      this.$refs.intentForm.resetFields()
      this.editIntention = {
        name: this.intention.name,
        zhName: this.intention.zhName,
        type: this.intention.type,
      }
    },
    // 编辑保存意图信息
    editSubmit(formName) {
      let self = this
      self.$refs[formName].validate((valid) => {
        if (valid) {
          self.editSaveLoading = true
          self.$utils.httpPost(
            self.$config.api.STUDIO_ADD_EDIT_INTENT,
            {
              businessId: self.businessId,
              id: self.intentId,
              name: self.editIntention.name,
              zhName: self.editIntention.zhName,
              type: self.editIntention.type,
              arcSwitch: self.intention.arcSwitch,
            },
            {
              success: (res) => {
                self.$message.success('保存成功')
                self.intention.name = self.editIntention.name
                self.intention.zhName = self.editIntention.zhName
                self.editSaveLoading = false
                self.edit = false
                self.$store.dispatch(
                  'studioSkill/setIntention',
                  self.$route.params.intentId
                )
              },
              error: (err) => {
                self.editSaveLoading = false
              },
            }
          )
        }
      })
    },
    searchUtterance() {
      this.getUtterances(1)
    },
    addUtteranceBlur(event) {
      event.target.blur()
    },
    addUtterance() {
      let self = this
      if (this.cursorPos > -1) return
      this.utteranceAddName = this.$utils.trimSpace(this.utteranceAddName)
      this.utteranceAddName = this.$utils.filterUtterSpace(
        this.utteranceAddName
      )
      if (!this.utteranceAddName) {
        return
      }
      let nesting = this.limitCount && this.limitCount['utterance_nesting'] > 0

      let nameValid = this.$rules.judgeUtteranceParams(
        this.utteranceAddName,
        200,
        '语料',
        15,
        nesting
      )
      if (!nameValid.valid) {
        return self.$message.warning(nameValid.data.message)
      }

      this.$utils.httpPost(
        this.$config.api.STUDIO_INTENT_ADD_EDIT_UTTERANCE,
        {
          businessId: this.businessId,
          intentId: this.intentId,
          utterance: this.utteranceAddName,
          type: 1,
        },
        {
          success: (res) => {
            self.$message.success('添加成功')
            let nowTotal = this.utterancesData.total + 1
            let basePage = nowTotal % this.utterancesData.size ? 1 : 0
            self.utteranceAddName = ''
            self.refreshData(1)
            // self.$store.dispatch('studioSkill/setSkill', this.businessId)
            self.collapseActive = res.data.template === 2 ? res.data.id : ''
            self.$refs.addUtter.$refs.input.focus()
          },
          error: (err) => {},
        }
      )
    },
    editUtterance(utterance, index) {
      let self = this
      if (!utterance.utterance) {
        return
      }
      this.$utils.httpPost(
        this.$config.api.STUDIO_INTENT_ADD_EDIT_UTTERANCE,
        {
          id: utterance.id,
          businessId: this.businessId,
          intentId: this.intentId,
          utterance: utterance.utterance,
          oldUtterance: this.oldUtterances[index].utterance,
        },
        {
          success: (res) => {
            self.$message.success('修改成功')
            self.getUtterances()
            self.getSlots()
            // self.$store.dispatch('studioSkill/setSkill', this.businessId)
          },
          error: (err) => {
            self.$message.error('修改失败，请稍后重试')
          },
        }
      )
    },
    beforeDel(utterance, index) {
      let self = this
      this.$confirm(' ', '确定要删除语料吗？', {
        confirmButtonText: '删除语料',
        cancelButtonText: '取消',
        confirmButtonClass: 'el-button--danger',
        type: 'warning',
        showClose: false,
      })
        .then(() => {
          self.delUtterance(utterance, index)
        })
        .catch(() => {})
    },
    // 删除语料
    delUtterance(utterance, index) {
      let self = this

      this.$utils.httpPost(
        this.$config.api.STUDIO_INTENT_DEL_UTTERANCE,
        {
          id: utterance.id,
          businessId: this.businessId,
          intentId: this.intentId,
          utterance: this.oldUtterances[index].utterance,
        },
        {
          success: (res) => {
            self.$message.success('删除成功')
            let nowTotal = this.utterancesData.total - 1
            let basePage = nowTotal % this.utterancesData.size ? 1 : 0
            let page =
              parseInt(nowTotal / this.utterancesData.size) + basePage >
              this.utterancesData.page
                ? this.utterancesData.page
                : parseInt(nowTotal / this.utterancesData.size) + basePage
            self.refreshData(page)
            // self.$store.dispatch('studioSkill/setSkill', this.businessId)
          },
          error: (err) => {
            self.$message.error('删除失败，请稍后重试')
          },
        }
      )
    },
    // 控制语料展开
    handleCollapse(item, event) {
      if (
        (this.collapseActive === item.id &&
          event.target.className !== 'utterance-area') ||
        event.target.className === 'ic-r-delete' ||
        item.template !== 2
      ) {
        return
      }
      this.collapseActive = this.collapseActive === item.id ? '' : item.id
    },
    delSlot(data) {
      console.log(data)
    },

    // 槽位名称修改
    changeSlotNameBlur(event) {
      event.target.blur()
    },
    // 槽位名称修改
    changeSlotName(item, index) {
      let self = this
      let oldName = ''
      if (item.slotType === 0) {
        if (item.slotName === this.oldSlotList[index].slotName) {
          return
        }
        oldName = this.oldSlotList[index].slotName
      } else {
        if (item.slotName === this.oldAuxiliarySlotList[index].slotName) {
          return
        }
        oldName = this.oldAuxiliarySlotList[index].slotName
      }
      let data = {
        businessId: this.businessId,
        intentId: this.intentId,
        id: item.id,
        name: item.slotName,
        oldSlotName: oldName,
      }

      this.$utils.httpPost(this.$config.api.STUDIO_SLOT_UPDATE, data, {
        success: (res) => {
          self.$message.success('修改成功')
          self.refreshData()
        },
        error: (err) => {
          console.log('page=>>')
          console.log(err)
        },
      })
    },

    necessaryChange(row, index) {
      let self = this
      if (this.oldSlotList[index].necessary != row.necessary) {
        this.$utils.httpPost(
          this.$config.api.STUDIO_SLOT_NECESSARY,
          {
            intentId: this.intentId,
            slotId: row.id,
            necessary: row.necessary,
          },
          {
            success: (res) => {
              self.$message.success('保存成功')
              self.oldSlotList = JSON.parse(JSON.stringify(self.slotsData.list))
              // self.$store.dispatch('studioSkill/setSkill', this.businessId)
              self.getSlots()
            },
            error: (err) => {},
          }
        )
      }
    },
    // 去编辑自定义实体
    toEditEntity(row) {
      let routeName = this.$route.path.includes('/sub/')
        ? 'sub-entity'
        : 'entity'
      let routeData = this.$router.resolve({
        name: routeName,
        params: { entityId: row.entityId },
      })
      window.open(routeData.href, '_blank')
    },
    // 打开设置通配实体dialog
    openSetWildcardDialog(row, e) {
      const rect = e.target.getBoundingClientRect()
      this.wildCardPopover = {
        show: true,
        data: row,
        rect: {
          top: rect.top,
          left: rect.left,
          width: rect.width,
          y: rect.y,
        },
      }
    },
    // 打开添加追问dialog
    openAddQaDialog(row) {
      this.addQaDialog.show = true
      this.addQaDialog.data = row
    },

    checkIntentionConfirm(val) {
      let reg = /\{(.*?)\}/g
      let tmp = val.match(reg)
      if (!tmp) {
        return true
      }
      if (!this.slotNames.length) {
        this.$message.warning('当前意图无语义槽，请勿使用花括号')
        return false
      }
      for (let i = 0; i < tmp.length; i++) {
        let slot = tmp[i].substring(1, tmp[i].length - 1)
        if (this.slotNames.indexOf(slot) == -1) {
          this.$message.warning('花括号内参数名称需和当前意图下的语义槽名相同')
          return false
        }
      }
      return true
    },

    openSelectEntity(data, event) {
      let rect = event.target.getBoundingClientRect()
      this.$store.dispatch('studioSkill/setEntityPopover', {
        show: true,
        showType: 'intention',
        data: data,
        rect: {
          top: rect.top,
          left: rect.left,
          width: rect.width,
          y: rect.y,
        },
      })
    },
    // 修改或新增槽位 更新语料
    updateUtterance(newUtterance) {
      let self = this
      this.oldUtterances = Array.prototype.map.call(
        this.oldUtterances,
        function (item, index) {
          if (item.id === newUtterance.id) {
            newUtterance.mark = JSON.parse(newUtterance.mark)
            return newUtterance
          } else {
            return item
          }
        }
      )
    },
    refreshData(utterancePage) {
      this.$store.dispatch('studioSkill/initHasSkillQuote', 0)
      this.$store.dispatch('studioSkill/initHasSkillQuote', 1)
      console.log('intention refreshData ==> close')
      this.$store.dispatch('studioSkill/setUtteranceEntityPopover', {
        show: false,
      })
      this.getUtterances(utterancePage)
      this.getSlots()
      this.$refs.modifierTable && this.$refs.modifierTable.getData()
    },
    editScore(utterance) {
      this.$utils.httpPost(
        this.$config.api.STUDIO_INTENT_ADD_EDIT_UTTERANCE,
        {
          businessId: this.businessId,
          intentId: this.intentId,
          id: utterance.id,
          utterance: utterance.utterance,
          oldUtterance: utterance.utterance,
          type: 1, // qc技能传1，业务定制传2
          score: utterance.score || 0,
          slotData: utterance.slotData || [],
        },
        {
          success: (res) => {
            this.$message.success('操作成功')
            this.refreshData()
          },
          error: (err) => {},
        }
      )
    },
    // 语料kv值 fzy对应的value汇总
    getCurrentUtterFzy(utterances) {
      let self = this
      self.fzyValueList.splice(0)
      for (let utter of utterances) {
        if (!utter.hasOwnProperty('slotData')) {
          break
        }
        let kv = JSON.parse(utter.slotData)
        if (!kv.hasOwnProperty('Fzy')) {
          break
        }
        let value = kv['Fzy'][0].value
        if (self.fzyValueList.indexOf(value) === -1) {
          self.fzyValueList.push(value)
        }
      }
    },
    openReplyDialog(row) {
      this.replyDialog.show = true
      this.currentSlotId = row.id
      if (!row.necessary) {
        return (this.qaSentence = '')
      }
      this.qaSentence = row.hasOwnProperty('questionSentence')
        ? row.questionSentence
        : ''
    },

    //自定义修饰语
    angleBracketHandle(e) {
      let self = this
      let leftAngleBracket = 188,
        rightAngleBracket = 190,
        angleCode = [188, 190]
      if (e.keyCode === 38 || e.keyCode === 40) return //键盘上下方向键
      if (!angleCode.includes(e.keyCode) && self.cursorPos === -1) return
      if (e.keyCode === rightAngleBracket && self.cursorPos !== -1) {
        return (self.modifierSelectPopover.show = false)
      }
      const cursor = this.$refs.addUtter.$refs.input.selectionStart //input 里的鼠标当前位置
      const value = e.target.value
      let inputWrap = this.$refs.addUtter
      const rect = e.target.getBoundingClientRect()
      let searchVal = ''
      if (value[cursor - 1] === '<') {
        self.cursorPos = cursor
      }
      if (self.cursorPos > cursor) {
        return (self.modifierSelectPopover.show = false)
      }
      self.cursorPos !== -1 &&
        setTimeout(function () {
          self.modifierSelectPopover = {
            show: true,
            rect: rect,
            cursorPos: cursor,
            searchVal: value.substring(self.cursorPos, cursor),
          }
        }, 0)
    },
    setModifier(item) {
      let self = this
      if (!item) return (this.cursorPos = -1)
      let searchValLen = this.modifierSelectPopover.searchVal
        ? this.modifierSelectPopover.searchVal.length
        : 0
      this.utteranceAddName =
        this.utteranceAddName.substring(0, this.cursorPos) +
        item +
        '>' +
        this.utteranceAddName.substring(this.cursorPos + searchValLen)
      let currentCursorPos = this.cursorPos + item.length + 1
      let input = this.$refs.addUtter.$refs.input
      input.setSelectionRange(this.cursorPos, currentCursorPos)
      input.focus()
      this.cursorPos = -1
    },
    handleDown() {
      if (!this.modifierSelectPopover.show) return
      this.$refs.modifierSelectPopover.handleDown()
    },
  },
  components: {
    SetWildcardDialog,
    AddQaDialog,
    SelectEntityPopover,
    UtteranceSlotTable,
    UtteranceSelectEntityPopover,
    ReplyDialog,
    Modifier,
    intentionConfirmTextAdder,
    intentionConfirm,
    intentionReply,
  },
}
</script>

<style lang="scss">
.intent-page {
  .os-collapse-title {
    margin: 28px 0;
    .ic-r-angle-d {
      color: $grey5;
    }
  }
  .ic-r-edit {
    color: $primary;
  }
}

.intent-info-form {
  margin-left: 10px;
  .el-form-item__content {
    height: 44px;
  }
}
.intention-edit-btn {
  position: absolute;
  top: 6px;
  left: 105px;
  margin-left: 16px;
  color: $primary;
  font-size: 14px;
  cursor: pointer;
  i {
    margin-right: 0;
  }
}
.intention-head-hovershow {
  display: inline-flex;
}
.intention-title {
  display: inline-block;
  vertical-align: bottom;
  max-width: 180px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.intention-form-label {
  font-weight: 600;
}
.utterance-edit-tip {
  margin-bottom: 16px;
  padding: 9px 16px;
  height: 40px;
  background: rgba(242, 245, 247, 1);
  border-radius: 2px;
}
.utterance-add-area,
.utterance-collapse-input {
  input {
    border: 1px solid $grey2;
    padding: 0 52px !important;
  }
  &-addbtn {
    min-width: 28px;
    padding: 0 16px;
    line-height: 44px;
  }
}

.utterance-collapse-input input {
  border: 0;
}

.utterance-collapse-item {
  border: 1px solid $grey2;
  border-bottom: 0;
  transition: all 0.2s;
  &-show {
    border-bottom: 2px solid $primary;
  }
}

.utterance-collapse-item-title {
  position: relative;
  &-del {
    position: absolute;
    right: 16px;
    top: 0;
    bottom: 0;
    margin: auto;
    line-height: 44px;
    cursor: pointer;
    display: none;
  }
  &:hover {
    .utterance-collapse-item-title-del {
      display: block;
    }
  }
}

.utterance-collapse-item:last-child {
  border-bottom: 1px solid $grey2;
}

.utterance-collapse-item-content {
  max-height: 0;
  overflow: hidden;
  // transition: max-height 0.2s ease-in;
  &-show {
    transition: max-height 0.2s ease-in;
    max-height: 900px;
    overflow: auto;
  }
}

.slot-table {
  .el-table__body tr:hover > td {
    background-color: #fff;
  }
  &-slotname input {
    border: 0 !important;
    padding: 0;
  }
  td {
    padding: 0;
    height: 56px;
    line-height: 56px;
    transition: none !important;
  }
}
.intention-page {
  .el-icon-question {
    color: $grey3;
  }
}
.inline-el-form-item {
  display: inline-block;
  width: 45%;
  &:first-child {
    margin-right: 55px;
  }
}
.intent-zh-name,
.intent-name {
  display: inline-block;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
