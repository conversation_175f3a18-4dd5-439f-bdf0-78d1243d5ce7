<template>
  <!-- <div class="os-scroll"> -->
  <os-page :options="pageOptions">
    <div slot="btn">
      <el-button
        size="small"
        type="primary"
        class="fr btn"
        style="margin-top: 10px"
        :loading="buildLoading"
        @click="handleKnowledgeBuild"
      >
        构建发布
      </el-button>
    </div>
    <div style="padding: 20px 24px 70px 24px">
      <div class="mgb16 manage-header">
        <el-button
          type="primary"
          size="small"
          class="fl"
          @click="handleKnowledgeManage(true)"
          :disabled="rowSelected.length == 0"
        >
          知识问题管理
        </el-button>
        <el-select
          v-model="docId"
          placeholder="请选择文件"
          style="width: 200px"
        >
          <el-option
            v-for="item in fileOptions"
            :key="item.id"
            :label="item.docName"
            :value="item.id"
          >
          </el-option>
        </el-select>
        <div class="fr mgb16">
          <el-input
            placeholder="输入知识点、知识id、知识问题关键字搜索"
            size="medium"
            style="display: inline-block; width: 320px; margin: 0 10px"
            v-model="search"
            @keydown.native.enter.prevent="handleSearch"
          >
            <i
              slot="suffix"
              class="el-input__icon el-icon-search search-area-btn"
            />
          </el-input>
          <el-button
            type="primary"
            size="medium"
            style="min-width: 80px"
            @click="handleSearch"
            >搜索</el-button
          >
          <el-button
            size="medium"
            style="min-width: 80px; margin-left: 4px"
            @click="resetSearch"
            >重置</el-button
          >
        </div>
      </div>
      <div>
        <el-table
          :data="tableData.list"
          v-loading="tableData.loading"
          style="width: 100%"
        >
          <el-table-column label="选择" width="60" align="center">
            <template slot-scope="scope">
              <el-checkbox
                v-show="!scope.row.isAdd"
                v-model="scope.row.selected"
                @change="handleRowCheck($event, scope.row)"
              >
              </el-checkbox>
            </template>
          </el-table-column>
          <el-table-column prop="id" label="知识点id" width="100">
            <template slot-scope="scope">
              <span v-show="!scope.row.isAdd">{{ scope.row.id }}</span>
            </template>
          </el-table-column>
          <el-table-column label="知识点">
            <template slot-scope="scope">
              <!-- @click.native="onEditorFocus(scope.row)"
              ref="editRef" -->
              <QuillEditor
                :key="scope.row.id"
                :rowId="scope.row.id"
                :value="scope.row"
                :disabled="scope.row.editDisabled"
                :isAdd="scope.row.isAdd ? true : false"
                ref="quillRef"
                @cancel="onEditorCancel"
                @save="onEditorSave"
                @blur.native="onEditorBlur(scope.row)"
              />
            </template>
          </el-table-column>
          <el-table-column label="知识问题">
            <template slot-scope="scope">
              <!-- 40个字符限制 -->
              <QuestionList
                v-show="!scope.row.isAdd"
                :qsList="scope.row.query"
                :rowData="scope.row"
                :editDisabled="scope.row.qsDisabled"
                @qsDel="qsDel"
                @qsAdd="qsAdd"
              />
              <!-- <question-utterance
                class="qs-box"
                ref="qeUtterance"
                v-show="!scope.row.isAdd"
                :list="qsUtterance(scope.row)"
                :subAccountEditable="true"
                @qsAdd="qsAdd"
                @qsDel="qsDel"
              /> -->
            </template>
          </el-table-column>
          <el-table-column label="操作" width="80">
            <template slot-scope="scope">
              <div v-show="!scope.row.isAdd">
                <!-- <el-tooltip
                  class="item"
                  effect="dark"
                  content="新增知识点"
                  placement="top"
                >
                  <i
                    class="ic-compare ic-r-plus"
                    @click="addRow(scope.$index, scope.row)"
                  ></i>
                </el-tooltip> -->
                <el-tooltip
                  class="item"
                  effect="dark"
                  content="原始文档预览对比"
                  placement="top"
                >
                  <i
                    class="ic-compare ic-r-file"
                    @click="handleDocxTrace(true, scope.row)"
                  >
                  </i>
                </el-tooltip>
                <!-- <el-tooltip
                  class="item"
                  effect="dark"
                  content="编辑"
                  placement="top"
                >
                  <i
                    class="sub-account cell-handle-ic ic-r-edit"
                    @click="onEditorFocus(scope.row)"
                  />
                </el-tooltip> -->
                <!-- <i
                  class="
                    sub-account
                    cell-handle-hovershow cell-handle-ic
                    ic-r-delete
                  "
                  @click.prevent.stop="handleDelete(scope.row)"
                /> -->
              </div>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          v-if="tableData.total > tableData.size"
          ref="pagination"
          class="txt-al-c"
          @current-change="handleCurrentChange"
          :current-page="tableData.page"
          :page-size="tableData.size"
          :total="tableData.total"
          layout="prev, pager, next,jumper,slot,total"
        >
          <span style="min-width: 24px">,</span>
        </el-pagination>
      </div>
      <QsManageDialog
        :dialogVisible="dialogVisible"
        :rowSelected="rowSelected"
        @changeVisible="handleKnowledgeManage"
      />
      <DocxPreview
        :fileUrl="fileUrl"
        :paragraphId="paragraphId"
        :docVisible="docVisible"
        :title="fileSelectDocName"
        @changeVisible="handleDocxTrace"
      />
    </div>
    <!-- <FloatBox /> -->
  </os-page>
  <!-- </div> -->
</template>

<script>
import QsManageDialog from './manageComponents/qsManageDialog.vue'
import QuillEditor from '@C/QuillEditor/QuillEditor.vue'

import QuestionUtterance from './manageComponents/questionUtterance.vue'
import QuestionList from './manageComponents/questionList.vue'
// import FloatBox from "./FloatBox";
import DocxPreview from './manageComponents/docxPreview.vue'
import { mapGetters } from 'vuex'
import qs from 'qs'
export default {
  name: 'plugin-platform-knowledge-manage',
  components: {
    QsManageDialog,
    QuillEditor,
    QuestionUtterance,
    // FloatBox,
    QuestionList,
    DocxPreview,
  },
  data() {
    return {
      pageOptions: {
        title: '知识拆分管理',
        loading: false,
        returnBtn: false,
      },
      repoId: '',
      id: '',
      docId: '',
      fileOptions: [],
      fileSelectDocName: '',
      dialogVisible: false,
      rowSelected: [],
      search: '',
      buildLoading: false,
      tableData: {
        loading: true,
        total: 0,
        page: 1,
        size: 10,
        list: [],
      },
      docVisible: false,
      fileUrl: '',
      paragraphId: [],
    }
  },
  created() {
    // this.repoId = this.$route.params.repoId //来自问答列表中的repoId
    this.repoId = this.repoIds
    this.id = this.knowledgeId //来自问答列表中的id
    if (this.id) {
      this.initData()
    }
    // this.getBottomStutus()
  },
  mounted() {},
  computed: {
    ...mapGetters({
      knowledgeId: 'pluginKnowledge/knowledgeId',
      repoIds: 'pluginKnowledge/repoIds',
    }),
    fileType: function () {
      return this.fileUrl ? this.fileUrl.split('.').pop() : ''
    },
  },
  methods: {
    getBottomStutus() {
      let self = this
      let data = {
        pageIndex: 1,
        pageSize: 200,
      }
      let newData = qs.stringify(data, { arrayFormat: 'repeat' })
      this.$utils.httpGet(
        `${this.$config.api.KNOWLEDGE_TABLE_STATUS_LIST}?${newData}`,
        {},
        {
          success: (res) => {
            // 问答列表页的 弹窗 兼容解析中 构建中 已完成，如有解析中，构建中需要前端主动弹出弹窗
            for (let i = 0; i < res.data.records.length; i++) {
              let item = res.data.records[i]
              // 如有解析中，构建中需要前端主动弹出弹窗
              if (item.extractStatus == 2 || item.buildStatus == 2) {
                this.$store.dispatch('pluginKnowledge/setFloatOpen', 'open')
                break
              }
            }
          },
          error: (err) => {},
        }
      )
    },
    initData() {
      // this.$store.dispatch(
      //   'pluginKnowledge/setRepoIds',
      //   this.$route.params.repoId
      // )
      let data = {
        repoId: this.repoId,
        id: this.id,
      }
      let self = this
      this.$utils.httpGet(this.$config.api.KNOWLEDGE_STORE_FILE_LIST, data, {
        success: (res) => {
          self.fileOptions = res.data || []
          if ((res.data || []).length === 0) {
            this.$message.error('当前知识问答无文档，请先上传')
            setTimeout(() => {
              this.$router.push({ name: 'qaBank-knowledge' })
            }, 1000)
          }

          self.docId = (res.data || []).length > 0 ? res.data[0].id : ''
          self.toastHandle(res.data || [])
        },
        error: (err) => {},
      })
      // this.getList();
    },
    toastHandle(data) {
      if (data.length == 0) {
        return
      }
      let extracting = false
      for (let i = 0; i < data.length; i++) {
        let item = data[i]
        if (item.status == 3) {
          this.$message.error(
            '你好，本次构建存在知识提取失败的文档，' + item.docName
          )
          break
        }
        if (item.status == 2) {
          extracting = true
          break
        }
      }
      if (extracting) {
        let self = this
        // 延迟两秒
        setTimeout(function () {
          let data = {
            repoId: self.repoId,
            id: self.id,
          }
          self.$utils.httpGet(
            self.$config.api.KNOWLEDGE_STORE_FILE_LIST,
            data,
            {
              success: (res) => {
                self.toastHandle(res.data || [])
              },
              error: (err) => {},
            }
          )
        }, 2000)
      }
    },
    getList(page) {
      if (page) {
        this.tableData.page = page
      }
      let params = {
        pageIndex: page || this.tableData.page,
        pageSize: this.tableData.size,
        search: this.search,
        knowledgeId: this.id,
        docId: this.docId,
      }
      let self = this
      this.$utils.httpGet(this.$config.api.KNOWLEDGE_STORE_POINT_LIST, params, {
        success: (res) => {
          if (res.code === '0') {
            if (res.data.records.length === 0 && res.data.current > 1) {
              this.tableData.page -= 1
            } else {
              this.tableData.loading = false
              this.tableData.total = res.data.total
              this.tableData.list = this.handleOriginData(res.data.records)
            }
          }
        },
        error: (err) => {},
      })
    },
    handleSearch() {
      this.tableData.loading = true
      this.tableData.page = 1
      this.getList()
    },
    handleOriginData(array) {
      let data = this.$deepClone(array)
      data.map((item) => {
        item.editDisabled = true
        item.qsDisabled = true
        item.selected = false
        item.input = ''
      })
      return data
    },
    resetSearch() {
      // this.pageData.pageIndex = 1
      this.tableData.page = 1
      this.search = ''
      this.tableData.loading = true
      this.getList()
    },
    handleRowCheck(val, row) {
      // console.log(val, row);
      if (val === true) {
        let newArray = this.rowSelected.filter((item) => item.id !== row.id)
        newArray.push(row)
        this.rowSelected = newArray
      } else {
        let newArray = this.rowSelected.filter((item) => item.id !== row.id)
        this.rowSelected = newArray
      }
      // 去重啊
      this.rowSelected = Array.from(new Set(this.rowSelected))
      console.log('==========this.rowSelected==', this.rowSelected)
    },
    handleKnowledgeManage(value) {
      this.dialogVisible = value
      if (value === false) {
        this.tableData.loading = true
      }
      this.getList()
    },
    addRow(index, row) {
      console.log(index, row)
      this.tableData.list.splice(index, 0, {
        id: '0',
        knowledge: '',
        reference: '',
        isAdd: true,
        editDisabled: false,
        qsDisabled: false,
        refKnowledgeId: row.id,
        docId: row.docId,
        query: [],
        input: '',
      })
    },
    qsUtterance(row) {
      let res = {
        data: row.query || [],
        type: 'setExampleUtterance',
        tip: '输入问题,回车新增',
        docId: row.docId,
        pointIds: row.id,
      }
      return res || {}
    },
    onEditorFocus(row) {
      row.editDisabled = false
      row.qsDisabled = false
    },
    onEditorCancel(row) {
      console.log('cancel', row)
      if (!row.editDisabled) {
        row.editDisabled = true
        // this.getList();
      }
      this.getList()
    },
    onEditorBlur(row) {
      console.log('失焦w')
      row.editDisabled = true
      this.$refs.quillRef.saveContent()
    },
    onEditorSave(row) {
      console.log('save', row)
      if (!row.editDisabled) {
        row.editDisabled = true
        if (row.isAdd) {
          this.handlePointAdd(row)
        } else {
          this.handlePointSave(row)
        }
      }
    },
    handlePointAdd(row) {
      let data = {
        refKnowledgeId: row.refKnowledgeId,
        knowledge: row.newKnowledge,
        reference: row.newReference
          ? JSON.stringify(row.newReference)
          : res.reference,
        docId: row.docId,
      }
      this.tableData.loading = true
      let self = this
      this.$utils.httpPost(
        this.$config.api.KNOWLEDGE_STORE_POINT_CREATE,
        data,
        {
          success: (res) => {
            if (res) {
              self.getList()
            }
          },
          error: (err) => {},
        }
      )
    },
    handlePointSave(row) {
      this.tableData.loading = true
      let data = {
        id: row.id,
        knowledge: row.newKnowledge || row.knowledge,
        reference: JSON.stringify(row.newReference || row.reference),
      }
      let self = this
      this.$utils.httpPost(this.$config.api.KNOWLEDGE_STORE_POINT_EDIT, data, {
        success: (res) => {
          if (res) {
            self.getList()
          }
        },
        error: (err) => {
          self.getList()
        },
      })
    },
    handlePointEdit(row) {
      row.editDisabled = !row.editDisabled
    },
    handleDelete(row) {
      let data = {
        id: row.id,
      }
      let self = this
      this.$utils.httpGet(this.$config.api.KNOWLEDGE_STORE_POINT_DELETE, data, {
        success: (res) => {
          self.$message.success('知识点删除成功')
          self.tableData.loading = true
          self.getList()
        },
        error: (err) => {},
      })
    },
    qsAdd(val) {
      // console.log( val )
      // this.tableLoading = true;
      let queryList = [val.query]
      let data = {
        docId: val.docId,
        pointIds: val.pointIds,
        queryList: queryList,
      }
      let self = this
      this.$utils.httpPost(this.$config.api.KNOWLEDGE_STORE_QS_ADD, data, {
        success: (res) => {
          if (res) {
            self.getList()
          }
        },
        error: (err) => {},
      })
    },
    qsDel(val) {
      this.tableData.loading = true
      let data = {
        queryIds: val,
      }
      let self = this
      this.$utils.httpGet(this.$config.api.KNOWLEDGE_STORE_QS_DEL, data, {
        success: (res) => {
          self.$message.success('问题删除成功')
          self.getList()
        },
        error: (err) => {
          self.getList()
        },
      })
    },
    handleSizeChange(val) {
      // this.pageData.pageSize = val
      this.tableData.size = val
    },
    handleCurrentChange(page) {
      this.tableData.loading = true
      this.tableData.page = page
      this.getList(page)
    },
    getRepoDocInfo() {
      let params = this.$route.params
      let data = {
        repoId: params.repoId,
      }
      if (!params.repoId) {
        return
      }
      const self = this
      this.$utils.httpGet(this.$config.api.QA_GETREPODOCINFO, data, {
        success: (res) => {
          if (res.data) {
            // 发布成功状态
            if (res.data.extractStatus === 1) {
              if (res.data.buildStatus === 1) {
                this.$message.success(
                  '您的知识文档构建发布成功，请在右侧进行知识体验。'
                )
              }
            }

            if (
              // 解析中
              // 解析成功，构建中
              res.data.extractStatus === 2 ||
              (res.data.extractStatus === 1 && res.data.buildStatus === 2)
            ) {
              self.timer = setTimeout(() => {
                self.getRepoDocInfo()
              }, 2000)
            } else if (
              // 待构建状态(待发布)
              res.data.extractStatus === 1 &&
              res.data.buildStatus === 0
            ) {
              self.timer = setTimeout(() => {
                self.getRepoDocInfo()
              }, 3000)
            }
          }
        },
        error: (err) => {},
      })
    },
    handleKnowledgeBuild() {
      this.buildLoading = true
      let data = {
        id: this.id,
      }
      let self = this
      this.$utils.httpGet(this.$config.api.KNOWLEDGE_STORE_BUILD, data, {
        success: (res) => {
          if (res) {
            self.buildLoading = false
            if (res.code === '0') {
              this.$message.success('您的知识正在构建')
              this.getRepoDocInfo()
              // this.$router.push({ name: 'knowledge-list' })
            } else {
              this.$message.error(`您的知识构建失败,${res.desc}`)
            }
          }
        },
        error: (err) => {
          self.buildLoading = false
        },
      })
    },
    handleDocxTrace(val, row) {
      if (row) {
        let arr = this.fileOptions.filter((item) => item.id == this.docId)
        console.log('====arr===', arr)
        if (arr.length > 0) {
          this.fileSelectDocName = arr[0].docName
        }
        this.paragraphId = row.paragraphId
        if (['txt', 'docx'].includes(this.fileType)) {
          this.docVisible = val
        } else {
          this.$message.warning('当前文件格式暂不支持溯源功能')
        }
      } else {
        this.docVisible = val
      }
    },
  },

  watch: {
    docId: function (val, oldVal) {
      this.docId = this.$deepClone(val)
      this.tableData.loading = true
      this.fileOptions.forEach((file) => {
        if (file.id === val) {
          this.fileUrl = file.filePath
        }
      })
      console.log(this.fileUrl)
      this.tableData.page = 1
      this.getList()
    },
    // 'tableData.page': function (val, oldVal) {
    //   this.tableData.loading = true
    //   this.getList()
    // },
    'tableData.size': function (val, oldVal) {
      this.tableData.loading = true
      this.getList()
    },
    dialogVisible: {
      handler(nVal, oVal) {
        //nval改变后的新数据，oval改变前的旧数据
        if (nVal) {
          console.log('===========打开知识管理')
        }

        if (oVal) {
          console.log('===========关闭知识管理')
          this.rowSelected = []
        }
      },
      deep: true, // 深度监听
      immediate: true, //立即执行
    },
    knowledgeId(val) {
      if (val) {
        this.id = val
        this.repoId = this.repoIds
        this.initData()
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.manage-header {
  text-align: center;
  .title {
    display: inline-block;
    margin-top: 10px;
    font-size: 15px;
  }
  :deep(.el-select) {
    .el-input,
    .el-input__inner {
      border: none;
      box-shadow: none;
      font-size: 18px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
  :deep(.el-select .el-input .el-select__caret) {
    color: #000000;
    font-size: 18px;
  }
}
.ic-compare {
  font-size: 20px;
  color: $primary;
  cursor: pointer;
  margin-right: 8px;
}
.pagination-footer {
  float: right;
}
</style>
