const { merge } = require('webpack-merge')
const { readEnv, getConditionalLoader } = require('./utils')
const config = readEnv('./.env.development')
const ESLintPlugin = require('eslint-webpack-plugin')
const { DefinePlugin, ProgressPlugin } = require('webpack')
const webpackCommonConfig = require('./webpack.common.js')
const path = require('path')

//读取环境变量
module.exports = merge(webpackCommonConfig, {
  mode: 'development',
  devtool: 'eval-cheap-module-source-map',
  // devtool:false,
  module: {
    rules: [
      {
        test: /\.css$/i,
        use: ['style-loader', 'css-loader'],
      },
      {
        test: /\.s[ac]ss$/i,
        // use: ["style-loader", "css-loader", "postcss-loader", "sass-loader", getConditionalLoader()],
        use: [
          'style-loader',
          'css-loader',
          {
            loader: 'postcss-loader',
            options: {
              sourceMap: false,
            },
          },
          {
            loader: 'sass-loader',
            options: {
              sourceMap: false,
              additionalData: `@import "@/assets/scss/variable.scss";`,
            },
          },
          getConditionalLoader(),
        ],
      },
    ],
  },
  plugins: [
    new DefinePlugin({
      BASE_URL: JSON.stringify('/'),
      'process.env': config,
    }),
    // new ESLintPlugin({
    //   fix: true /* 自动帮助修复 */,
    //   extensions: ["js", "json", "vue"],
    //   files: "src",
    // }),
    new ProgressPlugin(),
  ],
  devServer: {
    port: 8080,
    hot: true,
    host: 'localhost',
    historyApiFallback: true, //history路由错误问题
    client: {
      logging: 'warn',
      overlay: {
        errors: true,
        warnings: false,
      },
    },
    headers: {
      'Access-Control-Allow-Origin': '*',
    },
    proxy: {
      '/aiui/web': {
        target: 'http://teststudio.iflyos.cn/',
        // target: 'https://aiui.xfyun.cn/',
        // target: 'http://pre-aiui.xfyun.cn/',
        changeOrigin: true,
      },
      '/aiui/subweb': {
        target: 'http://teststudio.iflyos.cn/',
        // target: 'https://aiui.xfyun.cn/',
        // target: 'http://pre-aiui.xfyun.cn/',
        changeOrigin: true,
      },
      '/SSOService': {
        // target: 'https://sso.xfyun.cn',
        target: 'https://ssodev.xfyun.cn',
        changeOrigin: true,
      },
    },
  },
  stats: 'errors-warnings',
})
