<template>
  <os-page :options="pageOptions">
    <el-button
      v-if="subAccountEditable"
      type="primary"
      @click="onSubmit('form')"
      slot="btn"
      size="small"
      :loading="saving"
      :disabled="!formChanged"
      >保存修改</el-button
    >
    <div class="app-info-page">
      <el-form
        class="mgt48 app-info-form"
        ref="form"
        :rules="rules"
        :model="form"
        label-width="120px"
        label-position="left"
        :disabled="!subAccountEditable"
      >
        <el-form-item label="应用名称" prop="appName">
          <el-input
            v-model.trim="form.appName"
            @mouseout.native="nameOrDescInputMouseout"
          ></el-input>
        </el-form-item>
        <!-- <el-form-item
          label="应用平台"
          prop="platformNum"
          v-if="platform[form.platformNum] != 'all'"
        >
          <template>
            <span v-if="platform[form.platformNum] == 'aiui'">评估板</span>
            <span
              v-else-if="platform[form.platformNum] == 'morfei'"
              title="讯飞魔飞智能麦克风"
              >讯飞魔飞智能麦克风</span
            >
            <span v-else
              >{{ platform[form.platformNum]
              }}{{
                platform[form.platformNum] === 'WeChat' ? '（微信公众号）' : ''
              }}</span
            >
          </template>
        </el-form-item> -->
        <el-form-item label="APPID">
          <span class="static-txt">{{ form.appid }}</span>
        </el-form-item>
        <el-form-item label="APPKEY" v-if="form.appkey != '-'">
          <span class="static-txt" v-if="form.appkey"
            >{{ form.appkey && form.appkey.substr(0, 12) }}***{{
              form.appkey && form.appkey.substr(-3, 3)
            }}</span
          >
          <a class="btn" @click="copyAppKey(form.appkey)">复制</a>
        </el-form-item>
        <el-form-item
          label="APISECRET"
          v-if="form.apiSecret != '-' && form.avatar"
        >
          <span class="static-txt" v-if="form.apiSecret"
            >{{ form.apiSecret && form.apiSecret.substr(0, 12) }}***{{
              form.apiSecret && form.apiSecret.substr(-3, 3)
            }}</span
          >
          <a class="btn" @click="copyAppKey(form.apiSecret)">复制</a>
        </el-form-item>

        <el-form-item
          label="应用分类"
          prop="appType"
          :class="{ 'no-error': typeListOpen || form.appType }"
          :show-message="!showMessage"
        >
          <el-input
            v-if="!subAccountEditable"
            v-model.trim="form.appTypeName"
          ></el-input>
          <type-list
            v-else
            @setType="setType"
            @setTypeListStatu="setTypeListStatu"
            :initAppTypeName="form.appTypeName"
          ></type-list>
        </el-form-item>
        <el-form-item label="设备信息" prop="deviceType">
          <el-checkbox-group class="app-type" v-model="form.deviceType">
            <el-checkbox :label="1">
              有屏幕
              <el-tooltip
                content=" 设备上有屏幕，在屏幕展示AIUI下发的内容"
                placement="bottom-start"
              >
                <i style="color: #b8babf" class="el-icon-question"></i>
              </el-tooltip>
            </el-checkbox>
            <el-checkbox :label="2">
              有蓝牙
              <el-tooltip
                content="设备上有蓝牙模块，可用于配网或者连接其他设备"
                placement="bottom-start"
              >
                <i style="color: #b8babf" class="el-icon-question"></i>
              </el-tooltip>
            </el-checkbox>
            <el-checkbox :label="4">
              有WIFI
              <el-tooltip
                content=" 设备上有WiFi模块，可以连接网络"
                placement="bottom-start"
              >
                <i style="color: #b8babf" class="el-icon-question"></i>
              </el-tooltip>
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="应用描述" prop="appDescription">
          <el-input
            type="textarea"
            v-model="form.appDescription"
            :autosize="{ minRows: 3, maxRows: 6 }"
            @mouseout.native="nameOrDescInputMouseout"
            placeholder="请简要描述应用，最多1000字"
          ></el-input>
        </el-form-item>
        <!-- <el-form-item
          label="微信公众号"
          prop="appDescription"
          v-if="subAccountEditable"
        >
          <we-chat :appId="form.appid" :appInfo="form"></we-chat>
        </el-form-item> -->
      </el-form>
    </div>
  </os-page>
</template>

<script>
import dicts from '@M/dicts'
import TypeList from './../apps/appTypeList'
import WeChat from './weChat'

export default {
  name: 'app-info',
  props: {
    subAccount: Boolean,
    subAccountEditable: Boolean,
  },
  data() {
    return {
      pageOptions: {
        title: '应用信息',
        loading: false,
        returnBtn: false,
      },
      appOldName: '',
      initForm: {},
      form: {
        appName: '',
        appid: '',
        platformNum: '',
        appType: '',
        appTypeName: '',
        appDescription: '',
        appkey: '',
        apiSecret: '',
        deviceType: [],
      },
      platform: dicts.aiuiAppPlatform,
      rules: {
        appName: [
          this.$rules.required('应用名称不能为空'),
          { validator: this.checkName, trigger: ['blur', 'change'] },
          { validator: this.repeatName, trigger: ['blur'] },
        ],
        // 'platformNum' : [this.$rules.required('应用平台不能为空')],
        appType: [this.$rules.required('应用分类不能为空')],
        appDescription: [
          this.$rules.lengthLimit(1, 1000, '应用描述的长度不能超过1000个字符'),
        ],
      },
      saving: false,
      change: false,
      typeListOpen: false,
      // xfOpenPlatformApp: false, //同步讯飞开放平台的无平台分类的应用
      // platformInVain: '',
      // platformNums:[{
      //   label: 'Windows',
      //   value: '1'
      // },{
      //   label: 'iOS',
      //   value: '2'
      // },{
      //   label: 'Linux',
      //   value: '3'
      // },{
      //   label: 'Android',
      //   value: '4'
      // },{
      //   label: 'WebAPI',
      //   value: '5'
      // },{
      //   label: 'WeChat',
      //   value: '12'
      // }],
      nameOrDescChanged: false,
    }
  },
  computed: {
    appInfo() {
      return this.$store.state.aiuiApp.app
    },
    showMessage() {
      let tmp = this.typeListOpen || this.form.appType ? true : false
      return tmp
    },
    deviceTypeChanged() {
      if (
        this.form.deviceType.length === 0 &&
        this.initForm.deviceType.length === 0
      ) {
        return false
      } else if (
        this.form.deviceType.length === 0 ||
        this.initForm.deviceType.length === 0
      ) {
        return true
      }
      return (
        this.form.deviceType.reduce((prev, current, index, arr) => {
          return prev + current
        }) !==
        this.initForm.deviceType.reduce((prev, current, index, arr) => {
          return prev + current
        })
      )
    },
    formChanged() {
      return this.change || this.nameOrDescChanged || this.deviceTypeChanged
    },
  },
  watch: {
    appInfo(val) {
      if (val) {
        this.init()
      }
    },
    saving(val) {
      if (val) {
        this.pageOptions.loading = true
      } else {
        this.pageOptions.loading = false
      }
    },
    'form.appType': function (val, oldVal) {
      //过滤 评估版 appType 为 undefined的情况
      if (val == 'undefined' || oldVal == 'undefined') {
        this.form.appType = ''
      }
    },
  },
  created() {
    if (this.$store.state.aiuiApp.id) {
      this.init()
    }
  },
  methods: {
    init() {
      const formData = this.$deepClone(this.$store.state.aiuiApp.app)
      const deviceType = []
      for (let i = 4; i >= 1; i /= 2) {
        if (formData.deviceType - i >= 0) {
          deviceType.push(i)
          formData.deviceType -= i
        }
      }
      this.form = {
        ...formData,
        deviceType,
      }
      this.initForm = {
        ...this.$deepClone(this.$store.state.aiuiApp.app),
        deviceType,
      }
      this.initAppName = this.form.appName || ''
      this.initAppDescription = this.form.initAppDescription || ''
      // this.checkAppSource()
    },
    //表单校验规则
    checkName(rule, val, callback) {
      if (val && val.length > 30) {
        callback(new Error('不能超过30个字符'))
        return
      }
      let reg = /^[a-zA-Z0-9.\-_\u4e00-\u9fa5]+$/
      if (!reg.test(val)) {
        callback(new Error('仅支持汉字/字母/数字/下划线'))
        return
      }
      callback()
    },
    repeatName(rule, val, callback) {
      if (val == this.initForm.appName) {
        callback()
        return
      }
      this.$utils.httpPost(
        this.$config.api.AIUI_APP_CHECKNAME,
        { appName: val },
        {
          success: (res) => {
            callback(new Error('应用名称不能重复'))
            return
          },
          error: (err) => {},
        }
      )
      callback()
    },
    /**
     * 检查应用平台是否为空
     */
    // checkPlatform(show){
    //   this.form.platformNum = this.platformInVain
    //   if(!show) {
    //     this.$refs.form && this.$refs.form.validateField(['platformNum'])
    //   }
    // },
    /**
     * 检查应用的创建平台，xfOpenPlatformApp为true时，表明应用来自讯飞开放平台
     */
    // checkAppSource(){
    //   if(this.form && this.form.platformNum == 'all') {
    //     this.xfOpenPlatformApp = true
    //     this.form.platformNum = ''
    //     this.$refs.form && this.$refs.form.validateField(['platformNum'])
    //     return
    //   }
    //   this.xfOpenPlatformApp = false
    // },
    setType(type, typeName) {
      this.form.appType = type
      this.form.appTypeName = typeName
      this.change = true
    },
    setTypeListStatu(val) {
      this.typeListOpen = val
    },
    changAppDescription(val) {
      if (val.trim() !== this.initForm.appDescription) {
        this.change = true
        this.form.appDescription = val.trim()
      } else {
        this.change = false
      }
    },
    onSubmit(formName) {
      const deviceType =
        this.form.deviceType.length == 0
          ? 0
          : this.form.deviceType.reduce((prev, current, index, arr) => {
              return prev + current
            })
      let data = {
        appName: this.form.appName,
        appid: this.form.appid,
        // platform: this.form.platformNum,
        appType: this.form.appType,
        appTypeName: this.form.appTypeName,
        appDescription: this.form.appDescription || '',
        deviceType,
      }
      this.saving = true
      this.$refs[formName].validate((valid, item) => {
        let unvalidKeys = Object.keys(item)
        if (
          valid ||
          (unvalidKeys.length == 1 &&
            unvalidKeys[0] == 'appType' &&
            this.form.appType)
        ) {
          this.$utils.httpPost(this.$config.api.AIUI_EDIT_INFO, data, {
            success: (res) => {
              this.saving = false
              this.change = false
              this.nameOrDescChanged = false
              this.$store.dispatch('aiuiApp/setApp', this.$route.params.appId)
              this.$message_pro_success(
                '保存成功',
                '应用分类及设备信息会影响到默认配置'
              )
              this.subAccount && this.saveSubAccountLog()
            },
            error: (err) => {
              this.saving = false
            },
          })
        } else {
          this.saving = false
          return
        }
      })
    },
    copyAppKey(value) {
      this.$utils.copyClipboard(value)
      this.$message.success('已复制到剪切板')
    },
    // resetApiKey() {
    //   let self = this
    //   this.$confirm('重置后原本的API Key将失效，确定重置吗？', '重置API Key', {
    //     confirmButtonText: '确定',
    //     cancelButtonText: '取消',
    //     type: 'warning'
    //   }).then(() => {
    //     self.$utils.httpPost(self.$config.api.AIUI_RESETAPIKEY, {
    //     appid: self.form.appid },{
    //       success: res => {
    //         self.$store.dispatch('aiuiApp/setApp', self.$route.params.appId)
    //         self.$message.success('重置成功')
    //       },
    //       error: err => {
    //         this.$message.error( err || 'API Key 重置失败')
    //       }
    //     })
    //   })
    // },
    nameOrDescInputMouseout() {
      if (
        this.initForm.appName !== this.form.appName ||
        this.initForm.appDescription !== this.form.appDescription
      ) {
        this.nameOrDescChanged = true
      } else {
        this.nameOrDescChanged = false
      }
    },
    saveSubAccountLog() {
      this.$utils.httpPost(
        this.$config.api.AIUI_APP_SUB_ACCOUNT_LOG_SAVE,
        {
          appid: this.form.appid,
        },
        {
          success: (res) => {
            // this.$message.success('子账号保存成功')
          },
          error: (err) => {
            // this.$message.error( err || '子账号保存失败')
          },
        }
      )
    },
  },
  beforeRouteLeave: function (to, from, next) {
    if (this.change || this.nameOrDescChanged) {
      this.$confirm('放弃当前未保存内容而关闭页面？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          window.onbeforeunload = null
          next()
        })
        .catch(() => {
          next(false)
        })
    } else {
      window.onbeforeunload = null
      next()
    }
  },
  components: {
    TypeList,
    WeChat,
  },
}
</script>

<style lang="scss" scoped>
.app-info-form {
  margin-bottom: 100px;
}
.api-key-wrap {
  font-size: 0;
}
.static-txt {
  font-size: 14px;
}
.btn {
  font-size: 14px;
  margin-left: 24px;
}
</style>
<style lang="scss">
.app-info-form {
  .el-form-item__label {
    position: relative;
    padding-left: 11px;
  }
  .el-form-item.is-required:not(.is-no-asterisk) > .el-form-item__label:before {
    position: absolute;
    left: 0px;
  }
  .no-error .el-input__inner {
    border: 1px solid #d5d8de;
  }
}
</style>
