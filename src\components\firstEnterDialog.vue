<template>
  <el-dialog
    :visible.sync="dialog.show"
    width="600px"
    center
    :show-close="false"
  >
    <span slot="title" style="font-weight: bold; font-size: 18px"
      >🎉&nbsp;平台升级公告&nbsp;🎉</span
    >
    <span style="line-height: 22px">
      <p>亲爱的开发者，</p>
      <p style="text-indent: 27px">
        AIUI功能升级，全链路语音交互能力灵活配置！本版本具有以下新特性：
      </p>
      <p style="text-indent: unset">
        (1)&nbsp;交互大模型发布V2版本，全面升级至极速超拟人交互体验。
      </p>
      <p>(2)&nbsp;多家大模型可选可配，预置丰富的精品人设可供使用。</p>
      <p>
        (3)&nbsp;设备人设升级为大模型角色，自定义人设Prompt，一句话复刻声音，打造个性化对话体验。
      </p>
      <p>
        (4)&nbsp;简单便捷定制交互智能体，支持模板与工作流两种开发模式，快速实现抽槽、追问与信源查询的能力开发。
      </p>
      <p>
        (5)&nbsp;更加开放！支持OpenAPI协议自由接入三方模型，适配coze与星辰Agent平台接入三方智能体。三方知识库即将上线，敬请期待。
      </p>
      <!-- <p style="text-indent: 27px">
        温馨提示，旧版问答库的数据已全部迁移到新版本中。如果您之前添加过关联问，请重新指定关联问与知识点的对应关系。更好的大模型文档问答效果由您与我们共创！如有使用问题，请联系商务或************************。
      </p> -->
      <p style="text-align: right">2025年6月12日</p>
    </span>

    <span slot="footer" class="dialog-footer">
      <el-button @click="dialog.show = false" type="default"
        >我已知晓</el-button
      >
    </span>
  </el-dialog>
</template>
<script>
export default {
  props: {
    dialog: Object,
  },
  data() {
    return {}
  },
}
</script>
<style lang="scss" scoped></style>
