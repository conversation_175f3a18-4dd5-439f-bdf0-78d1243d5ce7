<template>
  <div class="s-body">
    <div class="s-body-inner">
      <div class="form-header__title" v-if="$route.params.solutionId != 41">
        {{ title
        }}{{ this.$route.params.solutionId == 24 ? '' : '解决方案' }}咨询申请
      </div>

      <div class="form-header__title" v-if="$route.params.solutionId == 41">
        <span style="font-size: 17px; font-weight: 600">儿童守护计划</span
        ><br />
        AC7911儿童玩具开发套件申请
      </div>

      <p class="field__description" v-if="$route.params.solutionId == 24">
        信息提交后，将于五月六号讯飞大模型发布会后陆续审核开通
      </p>
      <p class="field__description" v-else-if="$route.params.solutionId == 41">
        诚邀您参与开发儿童专属交互。信息提交后，将有工作人员联系发货，为您提供免费样品与1V1技术支持。
      </p>
      <p class="field__description" v-else>
        信息提交后，我们会在1个工作日内联系您，为您{{
          this.$route.params.solutionId == 1
            ? '产品设计、生产、出货及内容定制等全链路的技术支持服务。'
            : this.$route.params.solutionId == 2
            ? '的机器人提供设计、咨询、能力内容定制等全链路的技术支持服务。'
            : '提供全链路的技术支持服务。'
        }}
      </p>

      <div class="s-content">
        <a-form
          :form="form"
          :label-col="{ span: 6 }"
          :wrapper-col="{ span: 17 }"
          @submit="handleSubmit"
        >
          <a-form-item label="公司名称">
            <a-input
              v-decorator="[
                'companyName',
                {
                  rules: [
                    {
                      required: true,
                      message: '请输入公司名称',
                    },
                  ],
                },
              ]"
            />
          </a-form-item>
          <a-form-item label="联系人">
            <a-input
              v-decorator="[
                'linkMan',
                { rules: [{ required: true, message: '请输入联系人' }] },
              ]"
            />
          </a-form-item>
          <a-form-item label="联系方式">
            <a-input
              v-decorator="[
                'tel',
                {
                  rules: [
                    { validator: checkTel },
                    { required: true, message: '请输入联系方式' },
                  ],
                },
              ]"
            />
          </a-form-item>
          <a-form-item label="联系人职位" v-if="$route.params.solutionId != 41">
            <a-input
              v-decorator="[
                'position',
                { rules: [{ required: false, message: '请输入联系人职位' }] },
              ]"
            />
          </a-form-item>
          <a-form-item label="所在城市" v-if="$route.params.solutionId != 41">
            <a-input
              v-decorator="[
                'city',
                { rules: [{ required: true, message: '请输入所在城市' }] },
              ]"
            />
          </a-form-item>
          <!-- 虚拟人形象资产库使用 -->
          <a-form-item label="需求形象" v-if="$route.params.solutionId == 37">
            <a-select
              mode="tags"
              placeholder="请选择需要的虚拟人形象"
              style="width: 100%"
              v-decorator="[
                'metaHumanData',
                { rules: [{ required: false, message: '请选择需求形象' }] },
              ]"
            >
              <a-select-option v-for="img in imageData" :key="img.anchor_id">
                {{ img.name + '_' + img.anchor_id }}
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="需求描述" v-if="$route.params.solutionId != 41">
            <!-- <p style="margin-bottom: 0.1rem; margin-left: 0rem">
              请简要描述你的需求
            </p> -->
            <a-textarea
              class="fb_content_main"
              style="width: 100%; height: 100%; color: #aaaaaa"
              placeholder="请简要描述你的需求，以便我们能够提供更好的服务。"
              :rows="4"
              :maxLength="200"
              v-decorator="[
                'description',
                { rules: [{ required: true, message: '请输入需求内容' }] },
              ]"
            />
          </a-form-item>
          <a-form-item :wrapper-col="{ span: 12, offset: 10 }">
            <a-button type="primary" html-type="submit"> 提交 </a-button>
          </a-form-item>
        </a-form>
      </div>
    </div>
  </div>
</template>

<script>
import Form from 'ant-design-vue/lib/form'
import Input from 'ant-design-vue/lib/input'
import Button from 'ant-design-vue/lib/button'
import Select from 'ant-design-vue/lib/select'
import 'ant-design-vue/lib/form/style/css'
import 'ant-design-vue/lib/input/style/css'
import 'ant-design-vue/lib/button/style/css'
import { imageData } from '../metahuman-image/image-data'

export default {
  name: 'solutionApply',
  data: function () {
    return {
      imageData,
      solutionId: this.$route.params.solutionId,
      title: '',
      formLayout: 'horizontal',
      form: this.$form.createForm(this, { name: 'coordinated' }),
    }
  },
  methods: {
    checkTel(rule, value, callbackFn) {
      if (!value) {
        callbackFn()
        return
      }
      const reg = /^[1][3,4,5,6,7,8,9][0-9]{9}$/
      if (!reg.test(value)) {
        callbackFn('请检查手机号码格式')
        return
      }
      callbackFn()
    },
    onChange(value) {
      console.log(value)
    },
    handleSubmit(e) {
      e.preventDefault()
      this.form.validateFields((err, values) => {
        if (!err) {
          console.log('Received values of form: ', values)
          let paramObj = {
            ...values,
            solutionId: this.solutionId,
          }
          if (this.$route.params.solutionId == 41) {
            paramObj.position = 'position'
            paramObj.city = 'city'
            paramObj.description = 'description'
          }
          if (this.$route.params.solutionId == 37) {
            // 需求形象信息拼接在描述中
            paramObj['description'] =
              paramObj['description'] +
              '/n/n 虚拟人需求形象：' +
              paramObj['metaHumanData']
            delete paramObj['metaHumanData']
          }
          if (window.location.search) {
            paramObj.otherParam = window.location.href
          }
          this.$utils.httpGet(this.$config.api.SOLUTION_APPLY, paramObj, {
            success: (res) => {
              if (res.code == 0 && res.flag) {
                this.$message.success('提交成功')
                this.form.resetFields()
              } else {
                this.$message.error(res.desc)
              }
            },
            error: (err) => {},
          })
        } else {
          var a = document.getElementsByClassName('s-body')
          a.length &&
            a[0].scrollIntoView({
              behavior: 'smooth',
              block: 'center',
              inline: 'center',
            })
        }
      })
    },
    handleSelectChange(value) {
      console.log(value)
      this.form.setFieldsValue({
        note: `Hi, ${value === 'male' ? 'man' : 'lady'}!`,
      })
    },
  },
  mounted() {
    const that = this
    window.onresize = () => {
      return (() => {
        window.screenWidth = document.body.clientWidth
        that.screenWidth = window.screenWidth
      })()
    }
    ;(function () {
      var currClientWidth, fontValue, originWidth
      originWidth = 750 //ui设计稿的宽度，一般750或640
      __resize()

      window.addEventListener('resize', __resize, false)

      function __resize() {
        currClientWidth = document.documentElement.clientWidth
        if (currClientWidth > 768) {
          currClientWidth = 768
        }
        if (currClientWidth < 320) {
          currClientWidth = 320
        }
        fontValue = ((625 * currClientWidth) / originWidth).toFixed(2)
        document.documentElement.style.fontSize = fontValue + '%'
      }
    })()
  },
  created() {
    let self = this
    this.$utils.httpGet(
      this.$config.api.SOLUTION_APPLY_LIST,
      {},
      {
        success: (res) => {
          if (res.data.solutions && res.data.solutions.length) {
            /*self.title = res.data.solutions.filter(solution => {
              return solution.id == self.solutionId
            })[0].name*/
            for (let i = 0; i < res.data.solutions.length; i++) {
              if (res.data.solutions[i].id == self.solutionId) {
                self.title = res.data.solutions[i].name
              }
            }

            if (
              this.$route.params.solutionId == 37 &&
              this.$route.query?.fromImage
            ) {
              // 虚拟人资产从卡片点击的授权要带入形象数据
              this.form.setFieldsValue({
                metaHumanData: [this.$route.query.fromImage],
              })
            }
          }
        },
        error: (err) => {},
      }
    )
  },
  components: {
    [Form.name]: Form,
    [Form.Item.name]: Form.Item,
    [Input.name]: Input,
    [Button.name]: Button,
    [Input.TextArea.name]: Input.TextArea,
    'a-select': Select,
    'a-select-option': Select.Option,
  },
}
</script>

<style scoped lang="scss" scoped>
.s-body {
  p {
    margin-bottom: 0;
  }
  background: white;
  background-image: url('../../../../assets/images/solutions/apply/solution_apply.jpg');
  background-size: cover;
  background-position: bottom;
  background-repeat: no-repeat;
  margin-bottom: 100px;
  width: 100%;
  height: 100%;
  margin: 0 auto;
  align-items: center;
  background-position: center center;
  background-attachment: fixed;
  background-size: cover;
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #fff;
  position: relative;
  white-space: nowrap;
  overflow-y: auto;
  overflow-x: hidden;
  background-size: 100%;
  white-space: nowrap;

  position: fixed;
  background-size: cover;

  .s-body-inner {
    background: white;
    border: 1px solid #e7e7e7;
    height: auto;
    width: 7rem;
    margin: 0.3rem;
    padding: 0.2rem;
    min-height: calc(100vh - 0.5rem);

    .field__description {
      // color: rgb(16, 16, 16);
      color: #ff8c00;
      font-size: 14px;
      font-weight: normal;
      background-color: rgb(245, 245, 245);
      padding: 8px;
      //   width: 80%;
      margin: 10px auto;
      word-wrap: break-word;
      white-space: normal;
    }
  }
}
.form-header__title {
  margin-top: 0.2rem;
  //   margin-left: 0.7rem;
  text-align: center;
  white-space: normal;
  font-family: inherit;
  font-size: 15px;
  font-weight: normal;
  color: rgb(34, 34, 34);
}
.s-body {
  :deep(.ant-form-item) {
    display: flex !important;
    align-items: center !important;
  }
}
</style>
<style scoped lang="scss"></style>
