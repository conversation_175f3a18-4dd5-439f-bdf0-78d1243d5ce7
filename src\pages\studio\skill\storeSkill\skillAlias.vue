<template>
  <div>
    <el-tag
      class="skill-alias"
      :key="index"
      v-for="(tag, index) in initAlias.data"
      :closable="closable"
      :disable-transitions="false"
      :title="tag"
      @close="handleAliasClose(index)"
    >
      {{ tag }}
    </el-tag>
    <el-button
      class="button-new-tag"
      size="small"
      v-if="!inputAliasVisible && initAlias.data && initAlias.data.length < 5"
      @click="showAliasInput"
      ><i class="ic-r-plus"></i
    ></el-button>
    <el-form
      :model="form"
      ref="form"
      :rules="rules"
      style="display: inline-block"
      v-if="inputAliasVisible && initAlias.data && initAlias.data.length < 5"
    >
      <el-form-item prop="newAlias">
        <el-input
          class="new-alias-input"
          v-model.trim="form.newAlias"
          ref="saveTagInput"
          size="medium"
          placeholder="1-10个字符，回车添加"
          @keyup.enter.native="newAliasConfirm"
          @blur="newAliasConfirm"
        >
        </el-input>
        <input type="text" style="display: none" />
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
export default {
  name: 'store-skill-alias',
  props: {
    zhName: '',
    initAlias: {
      data: [],
    },
    onCheck: Boolean,
    subAccountEditable: Boolean,
  },
  data() {
    return {
      inputAliasVisible: false,
      form: {
        newAlias: '',
      },
      rules: {
        newAlias: [
          this.$rules.lengthLimit(1, 10, '别名长度不能超过10个字符'),
          { validator: this.checkName, trigger: 'blur' },
        ],
      },
    }
  },
  computed: {
    closable() {
      return !this.onCheck && this.subAccountEditable
    },
  },
  methods: {
    checkName(rule, value, callback) {
      let reg = /^[\u4e00-\u9fffa-zA-Z0-9]{0,10}$/
      if (value && value == this.zhName) {
        callback(new Error('不能和技能名称重复'))
      }
      if (this.initAlias.data.indexOf(value) != -1) {
        callback(new Error('不能和已有别名重复'))
      }
      if (!reg.test(value)) {
        callback(new Error('别名仅支持中文、英文、数字'))
      }
      callback()
    },
    handleAliasClose(index) {
      this.initAlias.data.splice(index, 1)
      this.$emit('setAlias', JSON.stringify(this.initAlias.data), true)
    },
    showAliasInput() {
      this.inputAliasVisible = true
      this.$nextTick((_) => {
        this.$refs.saveTagInput.$refs.input.focus()
      })
    },
    newAliasConfirm() {
      if (!this.form.newAlias) {
        this.inputAliasVisible = false
        this.$refs.form.resetFields()
        this.$emit('setAlias', JSON.stringify(this.initAlias.data), true)
        return
      }
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.initAlias.data.push(this.form.newAlias)
          this.form.newAlias = ''
          this.inputAliasVisible = false
          this.$emit('setAlias', JSON.stringify(this.initAlias.data), true)
        } else {
          this.$emit('setAlias', '', false)
        }
      })
    },
  },
}
</script>
<style lang="scss" scoped>
.skill-alias {
  vertical-align: middle;
  overflow: hidden;
  margin-right: 8px;
  color: $semi-black;
  border-radius: 2px;
  border-color: transparent;
  background: $grey4-15;
}
.button-new-tag {
  min-width: unset;
  width: 36px;
  height: 36px;
  background: $grey4-15;
}
.new-alias-input {
  width: 180px;
}
</style>
