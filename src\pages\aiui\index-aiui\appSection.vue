<template>
  <div class="section-app wow fadeInUp" id="section-app-id">
    <div class="title">
      <span>应用案例</span>
      <span class="sub-title">联合合作伙伴，共同打磨领先的人机交互体验</span>
    </div>
    <ul class="is-ul_tab">
      <li
        v-for="(item, index) in list"
        :key="index"
        :class="['is-li_title', { 'is-li_title_active': activeIndex == index }]"
        :style="{
          minWidth: `calc(${100 / list.length}% - 20px)`,
        }"
        @mouseenter="handleMouseEnter(index)"
      >
        {{ item.title }}
      </li>
    </ul>
    <div
      v-for="(ct, ctIndex) in list"
      :key="ctIndex"
      :class="['is-content', { 'is-content__active': activeIndex == ctIndex }]"
    >
      <div class="is-content__left">
        <img
          class="bg"
          v-lazy="require(`@A/images/home/<USER>/${ct.bg}.png`)"
          alt=""
        />
      </div>
      <div class="is-content__right"></div>
      <div class="is-panel">
        <div class="is-panel-content">
          <div class="is-panel-box">
            <div class="is-panel-title">{{ ct.title }}</div>
            <div class="is-panel-desc">{{ ct.desc }}</div>
            <div class="is-panel-btn" v-if="!!ct.solutionName">
              <a
                :href="`${transJumpPageUrl(ct.solutionLink, {
                  chan: 'AIUI',
                  way: 'card',
                })}`"
                target="_blank"
              >
                {{ ct.title }}解决方案&nbsp;>>&nbsp;
              </a>
            </div>
            <div class="is-panel-btn-disabled" v-else></div>
            <div class="is-panel-h">合作案例</div>
            <div
              class="is-panel-imgs"
              v-if="activeIndex == ctIndex && ct.cases.length > 5"
              v-swiper="customerSwiperOption"
            >
              <ul class="case-list swiper-wrapper">
                <li
                  v-for="(it, idx) in ct.cases"
                  :key="idx"
                  class="swiper-slide customing"
                >
                  <div
                    class="case-block"
                    :style="{
                      cursor: it.isVideo ? 'pointer' : 'default',
                      backgroundImage:
                        'url(' +
                        require(`@A/images/aiui/main-page/${it.coverImage}.png`) +
                        ')',
                      backgroundSize: it.backgroundSize
                        ? it.backgroundSize
                        : '100%',
                    }"
                    :src="it.videoSrc ? it.videoSrc : ''"
                  >
                    <i
                      class="icon-video-play icon-video-play2"
                      v-if="it.isVideo"
                      :style="{
                        pointerEvents: 'none',
                        backgroundImage:
                          'url(' +
                          require(`@A/images/aiui/main-page/btn_video_playback.png`) +
                          ')',
                      }"
                    ></i>
                  </div>

                  <p :title="it.desc">{{ it.desc }}</p>
                </li>
              </ul>
            </div>
            <div
              class="is-panel-imgs"
              v-else-if="activeIndex == ctIndex && ct.cases.length <= 5"
            >
              <ul class="case-list">
                <li v-for="(it, idx) in ct.cases" :key="idx">
                  <div
                    class="case-block"
                    :style="{
                      cursor: it.isVideo ? 'pointer' : 'default',
                      backgroundImage:
                        'url(' +
                        require(`@A/images/aiui/main-page/${it.coverImage}.png`) +
                        ')',
                      backgroundSize: it.backgroundSize
                        ? it.backgroundSize
                        : '100%',
                    }"
                    @click="playCaseVideo(it)"
                  >
                    <i
                      class="icon-video-play"
                      v-if="it.isVideo"
                      :style="{
                        backgroundImage:
                          'url(' +
                          require(`@A/images/aiui/main-page/btn_video_playback.png`) +
                          ')',
                      }"
                    ></i>
                  </div>

                  <p :title="it.desc">{{ it.desc }}</p>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
function getWindowHeight() {
  return 'innerHeight' in window
    ? window.innerHeight
    : document.documentElement.offsetHeight
}
import { transJumpPageUrl } from '@U/transJumpPageUrl.js'
import videoPlayer from '@C/videoPlayer/index'

export default {
  name: 'section-app',
  data() {
    return {
      customerSwiperOption: {
        loop: true,
        autoplay: {
          delay: 0,
          stopOnLastSlide: false,
          disableOnInteraction: false,
        },
        speed: 5000,
        freeMode: false,
        slidesPerView: 'auto',
        loopedSlides: 15,
        watchSlidesProgress: true,
        watchSlidesVisibility: true,
        lazy: {
          loadPrevNext: true,
          loadPrevNextAmount: 15,
          loadOnTransitionStart: true,
        },
      },
      activeIndex: 0,
      list: [
        {
          title: '机器人',
          desc: 'AIUI 为消费领域的家用机器人和B端的服务型机器人提供麦克风阵列、前端声学技术和语音交互服务。通过语音操控，可以控制机器人前进、后退等机械性动作，同时通过定制化开发，可以让机器人实现医院导诊、商场导购、餐厅点餐、展厅和营业厅的咨询回复等简单机械的重复性工作，降低企业人工成本。',
          targetUrl: '',
          bg: 'card1',
          solutionLink: `/solution/robot`,
          solutionName: '机器人解决方案',
          cases: [
            {
              isVideo: false,
              coverImage: 'robot/pudu',
              desc: '普渡餐饮机器人',
              backgroundSize: '101%',
            },
            {
              isVideo: false,
              coverImage: 'robot/yongyida',
              desc: '勇艺达服务机器人',
              backgroundSize: '101%',
            },
            {
              isVideo: false,
              coverImage: 'robot/aiwa',
              desc: '艾娃导诊导医机器人',
              backgroundSize: '101%',
            },

            {
              isVideo: false,
              coverImage: 'robot/穿山甲机器人',
              desc: '穿山甲机器人',
              backgroundSize: 'contain',
            },
            {
              isVideo: false,
              coverImage: 'robot/进化者机器人',
              desc: '进化者机器人',
              backgroundSize: 'contain',
            },
            {
              isVideo: false,
              coverImage: 'robot/擎朗智能机器人',
              desc: '擎朗智能机器人',
              backgroundSize: 'contain',
            },
            {
              isVideo: false,
              coverImage: 'robot/云迹机器人',
              desc: '云迹机器人',
              backgroundSize: 'contain',
            },
            {
              isVideo: false,
              coverImage: 'robot/中智卫安机器人',
              desc: '中智卫安机器人',
              backgroundSize: 'contain',
            },
          ],
          case: [
            'card2_1',
            'card2_2',
            'card2_3',
            'card2_4',
            'card2_5',
            'card2_6',
          ],
        },
        {
          title: '电视/投影仪',
          desc: 'AIUI电视，投影仪等带屏设备，利用语音遥控器或麦克风阵列技术，结合AIUI免唤醒语音交互技术，实现内容搜索点播、设备控制、播放控制的语音操控。让文字输入、内容搜索、设备操控变得更加简单。',
          targetUrl: '',
          bg: 'card2',
          case: ['card1_1', 'card1_2', 'card1_3'],
          solutionName: '',
          solutionLink: '',
          cases: [
            {
              isVideo: false,
              coverImage: 'tv/TCL',
              desc: 'TCL',
              backgroundSize: '101%',
            },
            {
              isVideo: false,
              coverImage: 'tv/海尔电视',
              desc: '海尔电视',
              backgroundSize: 'contain',
            },
            {
              isVideo: false,
              coverImage: 'tv/海信电视',
              desc: '海信电视',
              backgroundSize: 'contain',
            },
            {
              isVideo: false,
              coverImage: 'tv/极米',
              desc: '极米',
              backgroundSize: 'contain',
            },
            {
              isVideo: false,
              coverImage: 'tv/坚果投影仪',
              desc: '坚果投影仪',
              backgroundSize: 'contain',
            },
            {
              isVideo: false,
              coverImage: 'tv/三星电视',
              desc: '三星电视',
              backgroundSize: 'contain',
            },

            {
              isVideo: false,
              coverImage: 'tv/创维电视',
              desc: '创维电视',
              backgroundSize: 'contain',
            },
            {
              isVideo: false,
              coverImage: 'tv/康佳电视',
              desc: '康佳电视',
              backgroundSize: 'contain',
            },
            {
              isVideo: false,
              coverImage: 'tv/夏普电视',
              desc: '夏普电视',
              backgroundSize: 'contain',
            },
            {
              isVideo: false,
              coverImage: 'tv/长虹电视',
              desc: '长虹电视',
              backgroundSize: 'contain',
            },
          ],
        },
        {
          title: '儿童教育',
          desc: 'AIUI为儿童玩具和教育类产品提供语音交互，让儿童轻松获得口语评测、百科知识、儿童闲聊和丰富的儿童内容资源，更有留声技术可以实现让设备使用爸爸妈妈的声音和儿童进行互动。让产品有关爱、交互更有趣。',
          targetUrl: '',
          bg: 'card3',
          case: ['card3_1', 'card3_2', 'card3_3', 'card3_4'],
          solutionName: '',
          solutionLink: '',
          cases: [
            {
              isVideo: false,
              coverImage: 'kid/jiuxue',
              desc: '九学王教育平板',
              backgroundSize: '101%',
            },
            {
              isVideo: false,
              coverImage: 'kid/benniu',
              desc: '牛听听儿童绘本机器人',
              backgroundSize: '101%',
            },
            {
              isVideo: false,
              coverImage: 'kid/xiaozhuangyuan',
              desc: '小状元扫读笔',
              backgroundSize: '101%',
            },
            {
              isVideo: false,
              coverImage: 'kid/diwo',
              desc: '迪沃儿童手表',
              backgroundSize: '101%',
            },
            {
              isVideo: false,
              coverImage: 'kid/格灵教育平板',
              desc: '格灵教育平板',
              backgroundSize: '101%',
            },
            {
              isVideo: false,
              coverImage: 'kid/三基同创智能手表',
              desc: '三基同创智能手表',
              backgroundSize: 'contain',
            },
          ],
        },
        {
          title: '语音点歌',
          desc: 'AIUI 利用讯飞AIUI语音技术，让KTV、家庭点歌机、户外拉杆箱等场景下的歌曲点播、播放控制、设备控制等高频词汇,做到无需唤醒直接语音交互，提升音乐点播和播放控制的交互体验。',
          targetUrl: '',
          bg: 'card4',
          case: [
            'card2_1',
            'card2_2',
            'card2_3',
            'card2_4',
            'card2_5',
            'card2_6',
          ],
          solutionLink: `/solution/ktv`,
          solutionName: '语音点歌解决方案',
          cases: [
            {
              isVideo: true,
              coverImage: 'ktv/juhi',
              desc: '巨嗨语音点歌',
              backgroundSize: '105% 100%',
              videoSrc:
                'https://gh-test1.oss-cn-beijing.aliyuncs.com/temp/%E5%B7%A8%E5%97%A8%E8%AF%AD%E9%9F%B3%E6%95%99%E7%A8%8B.mp4',
            },
            {
              isVideo: false,
              coverImage: 'ktv/yimeilai',
              desc: '懿美莱便携式拉杆音箱',
              backgroundSize: '101%',
            },
            {
              isVideo: false,
              coverImage: 'ktv/jiashilian',
              desc: '佳视联',
              backgroundSize: '101%',
            },
            {
              isVideo: false,
              coverImage: 'ktv/深圳视触',
              desc: '深圳视触',
              backgroundSize: 'contain',
            },
            {
              isVideo: false,
              coverImage: 'ktv/星网视易',
              desc: '星网视易',
              backgroundSize: 'contain',
            },
            {
              isVideo: false,
              coverImage: 'ktv/音创',
              desc: '音创',
              backgroundSize: 'contain',
            },
          ],
        },
        {
          title: '大屏调度',
          desc: '让传统的商超、政务、银行、医疗、物流、办公等领域的大屏，可以语音互动,一句话完成深层操作，让传统大屏拥有“嘴巴”和“耳朵”，能听或说，更懂用户。',
          targetUrl: '',
          bg: 'card5',
          case: [
            'card2_1',
            'card2_2',
            'card2_3',
            'card2_4',
            'card2_5',
            'card2_6',
          ],
          solutionLink: `/solution/screen`,
          solutionName: '智慧大屏解决方案',
          cases: [
            {
              isVideo: false,
              coverImage: 'screen/xinhuashe',
              desc: '新华社采编大屏系统',
              backgroundSize: '101%',
            },
            {
              isVideo: false,
              coverImage: 'screen/北京名洋',
              desc: '北京名洋',
              backgroundSize: 'contain',
            },
            {
              isVideo: false,
              coverImage: 'screen/河南省大数据局',
              desc: '河南省大数据局',
              backgroundSize: 'contain',
            },
            {
              isVideo: false,
              coverImage: 'screen/河南省投',
              desc: '河南省投',
              backgroundSize: 'contain',
            },
            {
              isVideo: false,
              coverImage: 'screen/宁夏电力',
              desc: '宁夏电力',
              backgroundSize: 'contain',
            },
          ],
        },
        {
          title: '智能家居',
          desc: 'AIUI可以搭载干家居中控面板、智能空调、冰箱、风扇、灯泡等智能家居设备,实现语音打开设备、温度调节、菜谱査询,灯光模式切换等常用功能。实现真正的智慧家庭，万物联网。',
          targetUrl: '',
          bg: 'card7',
          case: ['card2_1', 'card2_2', 'card2_3'],
          solutionName: '',
          solutionLink: '',
          cases: [
            {
              isVideo: false,
              coverImage: 'smarthome/lilin',
              desc: '立林室内智能终端',
              backgroundSize: '101%',
            },
            {
              isVideo: false,
              coverImage: 'smarthome/aite',
              desc: '艾特语音面板',
              backgroundSize: '101%',
            },
            {
              isVideo: false,
              coverImage: 'smarthome/mogen',
              desc: '摩根智能闹钟',
              backgroundSize: '101%',
            },

            {
              isVideo: false,
              coverImage: 'smarthome/tp-link面板',
              desc: 'tp-link面板',
              backgroundSize: '101%',
            },
            {
              isVideo: false,
              coverImage: 'smarthome/欧普照明家居面板',
              desc: '欧普照明家居面板',
              backgroundSize: 'contain',
            },
            {
              isVideo: false,
              coverImage: 'smarthome/探乐派闺蜜机',
              desc: '探乐派闺蜜机',
              backgroundSize: 'contain',
            },
            {
              isVideo: false,
              coverImage: 'smarthome/中科世为面板',
              desc: '中科世为面板',
              backgroundSize: '101%',
            },
          ],
        },
        {
          title: '智能办公',
          desc: 'AIUI可以应用于键盘、鼠标、升降桌等办公场景，进一步帮助用户提升生产效率。',
          targetUrl: '',
          bg: 'card6',
          case: ['card2_1', 'card2_2'],
          solutionLink: '',
          solutionName: '',
          cases: [
            {
              isVideo: false,
              coverImage: 'office/乐歌升降桌',
              desc: '乐歌升降桌',
              backgroundSize: 'contain',
            },
            {
              isVideo: false,
              coverImage: 'office/马道键鼠',
              desc: '马道键鼠',
              backgroundSize: 'cover',
            },
          ],
        },
        {
          title: '智能穿戴',
          desc: 'AIUI可以应用于智能眼镜、智能头盔、智能手表等穿戴场景，方便用户在不方便触屏操作的情况便捷的使用语音控制设备',
          targetUrl: '',
          bg: 'card8',
          case: ['card2_1', 'card2_2', 'card2_3'],
          solutionName: '',
          solutionLink: '',
          cases: [
            {
              isVideo: false,
              coverImage: 'dress/雷鸟眼镜',
              desc: '雷鸟眼镜',
              backgroundSize: 'contain',
            },
            {
              isVideo: false,
              coverImage: 'dress/李未可眼镜',
              desc: '李未可眼镜',
              backgroundSize: 'contain',
            },
            {
              isVideo: false,
              coverImage: 'dress/影目眼镜',
              desc: '影目眼镜',
              backgroundSize: 'cover',
            },
          ],
        },
      ],
    }
  },
  mounted() {
    // this.appSwiper.on('slideChange', () => {
    //   this.$nextTick(() => {
    //     this.activeIndex = this.appSwiper.realIndex
    //   })
    // })

    document.getElementById('section-app-id').addEventListener('click', (e) => {
      console.log('-----------------', e.target)
      if (e.target && e.target.classList.contains('case-block')) {
        let src = e.target.getAttribute('src')
        if (src) {
          this.playCaseVideo({
            isVideo: true,
            videoSrc: src,
          })
        }
      }
    })
  },
  methods: {
    transJumpPageUrl,
    handleMouseEnter(val) {
      this.activeIndex = val
      // this.$nextTick(() => {
      //   this.appSwiper.slideToLoop(val)
      // })
    },

    playCaseVideo(it) {
      if (it.isVideo) {
        let height = Math.min(getWindowHeight() * 0.9, 562)
        let width = parseInt((1920 * height) / 1080)
        videoPlayer({
          width,
          height,
          videoSrc: it.videoSrc,
          videoStyle: {
            width: `${width}px`,
            height: `${height}px`,
            'box-sizing': 'border-box',
            'margin-left': `-${width * 0.5}px`,
            'margin-top': `-${height * 0.5}px`,
          },
        })
      }
    },
  },
}
</script>
<style lang="scss" scoped>
@import './common.scss';

.section-app {
  width: auto;
  height: 772px;
  background: #f4f7fe;
  overflow: hidden;

  .title {
    width: 1200px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    padding: 64px 0 48px;

    span {
      font-size: 30px;
      font-family: PingFangSC, PingFangSC-Medium;
      font-weight: 500;
      text-align: justify;
      color: #000000;
    }

    .sub-title {
      font-size: 16px;
      font-family: PingFangSC, PingFangSC-Regular;
      font-weight: 400;
      color: #676a99;
      margin-left: 24px;
    }
  }

  .is-ul_tab {
    width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;

    .is-li_title {
      font-size: 16px;
      color: #032340;
      cursor: pointer;
      font-family: PingFangSC, PingFangSC-Regular;
      transition: all 0.2s;
      height: 30px;
      line-height: 30px;
      position: relative;
      text-align: center;
      padding-bottom: 20px;

      &:after {
        content: '';
        width: 0%;
        height: 0;
        position: absolute;
        left: 50%;
        bottom: -2px;
        border-bottom: 3px solid $primary;
        transform: translateX(-50%);
        transition: width 0.2s;
      }

      &_active {
        font-size: 18px;
        color: $primary;
        font-family: PingFangSC, PingFangSC-Medium;

        &:after {
          width: 70%;
        }
      }
    }
  }

  .is-content {
    width: 100%;
    height: 564px;
    display: none;
    margin-top: 24px;
    position: relative;

    &__active {
      display: flex;
    }

    &__left {
      width: 61%;
      height: 100%;
      position: relative;

      .bg {
        position: relative;
        height: 564px;
        width: 100%;
        object-fit: cover;
        animation: bcu1Wkwl 0.5s ease;
      }
    }

    &__right {
      width: 39%;
      height: 100%;
      box-sizing: border-box;
    }
  }

  .is-panel {
    position: absolute;
    top: 98px;
    left: 41%;
    height: 387px;
    background: linear-gradient(270deg, #ffffff, rgba(255, 255, 255, 0.9));
    border: 1px solid #ffffff;
    box-shadow: -12px 4px 24px 0px rgba(0, 0, 0, 0.2);
    box-sizing: border-box;
    z-index: 10;

    &-content {
      width: 1504px;
      box-sizing: border-box;
      padding: 40px 64px;

      .is-panel-box {
        width: 680px;
      }

      .is-panel-title {
        font-size: 24px;
        font-family: PingFangSC, PingFangSC-Medium;
        font-weight: 500;
        text-align: left;
        color: #000000;
        animation: fadeInBottom 0.5s ease 0.1s both;
      }

      .is-panel-desc {
        width: 680px;
        font-size: 14px;
        font-family: PingFangSC, PingFangSC-Regular;
        font-weight: 400;
        text-align: justify;
        color: #032340;
        margin-top: 12px;
        animation: fadeInBottom 0.5s ease 0.1s both;
      }

      .is-panel-btn {
        margin-top: 16px;
        width: 165px;
        height: 36px;
        line-height: 36px;
        text-align: center;
        background: $primary;
        animation: fadeInBottom 0.5s ease 0.1s both;
        cursor: pointer;

        a {
          color: #fff;
        }

        &:hover {
          background: linear-gradient(270deg, #00d7ff, #235ae8);
        }
      }

      .is-panel-btn-disabled {
        margin-top: 16px;
        width: 165px;
        height: 36px;
      }

      .is-panel-h {
        font-size: 14px;
        font-family: PingFangSC, PingFangSC-Regular;
        font-weight: 400;
        text-align: justify;
        color: #5b5c65;
        margin-top: 32px;
        animation: fadeInBottom 0.5s ease 0.1s both;
      }

      .is-panel-imgs {
        margin-top: 10px;
        animation: fadeInBottom 0.5s ease 0.1s both;

        .case-list {
          display: flex;
          margin: 0;
          transition-timing-function: linear !important;

          li {
            position: relative;
            width: unset;

            .case-block {
              width: 114px;
              height: 78px;
              position: relative;
              background-repeat: no-repeat;
              background-position: center;
              background-color: #fff;
              border-radius: 5px;
              border: 1px solid #e0e5f2;
              .icon-video-play {
                display: inline-block;
                width: 40px;
                height: 40px;
                position: absolute;
                z-index: 10;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background-size: contain;
                // background: url(~@A/images/aiui/main-page/btn_video_playback.png);
              }
            }

            p {
              text-align: center;
              font-size: 12px;
              font-family: PingFangSC, PingFangSC-Regular;
              color: #5b5c65;
              margin: 12px auto 0;
              white-space: nowrap;
              width: auto;
            }
          }

          li + li {
            margin-left: 12px;
          }
        }
      }
    }
  }
}

.swiper-container-fade .swiper-slide-active .swiper-slide {
  pointer-events: auto !important;
}
</style>
