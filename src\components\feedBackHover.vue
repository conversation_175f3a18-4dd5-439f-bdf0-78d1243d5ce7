<template>
  <div class="feedback-container">
    <div
      class="feed-back"
      ref="feedBackRef"
      v-if="!rightTestOpen"
      @click="toggleAssistant"
      @mouseover="onMouseOver"
      @mouseout="onMouseOut"
    >
      <ul>
        <li class="kf">
          <i class="icon-kefu"></i>
        </li>
        <li :class="['arrow']" @click="backTop" v-if="showBackTop && canShow">
          <div :class="['arrow-inner']">
            <i class="icon-arrow"></i>
          </div>
        </li>
      </ul>
    </div>
    <service-experience :show="showAssistant"></service-experience>
  </div>
</template>

<script>
import serviceExperience from './serviceExperience/index.vue'
import dragElement from '@U/dragElement.js'

let timer = null

export default {
  name: 'feedBackHover',
  props: {
    rightTestOpen: {
      type: Boolean,
      default: false,
    },
    showBackTop: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      showAssistant: false,
    }
  },
  mounted() {
    document.addEventListener('click', this.popHandler)
    dragElement(this.$refs['feedBackRef'])
  },
  destroyed() {
    document.removeEventListener('click', this.popHandler)
  },
  methods: {
    toggleAssistant() {
      this.showAssistant = !this.showAssistant
    },
    closeAssistant() {
      if (this.showAssistant) {
        this.showAssistant = false
      }
    },
    popHandler(e) {
      try {
        if (
          !document
            .getElementsByClassName('assistant-container')[0]
            .contains(e.target) &&
          !document.getElementsByClassName('feed-back')[0].contains(e.target)
        ) {
          this.showAssistant = false
        } else {
        }
      } catch (e) {}
    },

    onMouseOver() {
      this.countDown(4, () => {
        if (!this.showAssistant) {
          this.showAssistant = true
        }
      })
    },
    onMouseOut() {
      clearInterval(timer)
    },
    countDown(time, callback) {
      if (time <= 0) {
        return
      }
      timer = setInterval(() => {
        time--
        if (time <= 0) {
          clearInterval(timer)
          callback && callback()
        } else {
        }
      }, 1000)
    },
  },
  components: {
    serviceExperience,
  },
}
</script>

<style scoped lang="scss">
ul {
  margin-bottom: 0;
}

.el-icon {
  color: #1890ff;
  font-size: 16px;
  margin-right: 5px;
}

.feed-back {
  position: fixed;
  right: 30px;
  top: 60%;
  z-index: 1000;

  > ul {
    display: flex;
    flex-direction: column;

    > li {
      cursor: pointer;
      display: inline-block;
      width: 70px;
      height: 70px;
      // box-shadow: 0px 5px 20px 0px rgba(31, 144, 254, 0.3);

      box-shadow: 0px 5px 20px 0px rgba(165, 165, 165, 0.3);
      border-radius: 10px;

      &.kf {
        background: $primary;
        border-radius: 1px;
        box-shadow: 0px 4px 16px 0px rgba(0, 0, 0, 0.2);
        position: relative;

        .icon-kefu {
          width: 32px;
          height: 32px;
          display: inline-block;
          position: absolute;
          z-index: 20;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          background: url(~@A/images/aiui/main-page/icon-image.png) no-repeat;
          background-repeat: no-repeat;
          background-position: -137px 0;
        }

        > div {
          display: none;
        }

        &:hover {
          color: $primary;

          > div {
            display: block;
            position: absolute;
            display: block;
            right: 70px;
            padding-right: 10px;
          }

          > div > div {
            position: relative;
            width: 200px;
            background-color: #fff;
            box-shadow: 0 0 10px #ccc;
            color: #656565;
            text-align: center;
            font-size: 10px;
            cursor: default;
            border-radius: 2%;

            > ul {
              display: inline-flex;
              width: 100% !important;
              height: 100%;
              margin: 0 auto;
              padding: 0 10px;
              position: relative;

              > li {
                align-self: center;
                text-align: center;
                justify-content: center;
              }

              > li:nth-child(1) {
                font-size: 300%;
                margin-right: 2%;
              }

              > li:nth-child(2) > p {
                margin-bottom: unset !important;
              }
            }
          }
        }
      }

      &.arrow {
        background: #fff;
        position: relative;

        .arrow-inner {
          position: relative;
          width: 100%;
          height: 100%;

          &.down {
            transform: rotate(180deg);
            transform-origin: center;
          }
        }

        .icon-arrow {
          width: 25px;
          height: 18px;
          display: inline-block;
          position: absolute;
          z-index: 20;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          background: url(~@A/images/aiui/main-page/icon-image.png) no-repeat;
          background-position: -81px 0;
        }
      }
    }

    li + li {
      margin-top: 10px;
    }
  }
}

@media screen and (max-width: 1601px) {
  .feed-back {
    right: 20px;

    > ul {
      > li {
        width: 60px;
        height: 60px;

        &.kf {
          &:hover {
            > div {
              right: 60px;
            }
          }
        }
      }
    }
  }
}

@media screen and (max-width: 1000px) {
  .feedback-container {
    display: none;
  }
}
</style>
