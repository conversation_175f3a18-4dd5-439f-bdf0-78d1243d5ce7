<template>
  <el-dialog
    title="AI扩写"
    :close-on-click-modal="false"
    :visible="isShow"
    @close="cancel"
    width="500"
  >
    <div class="desc-title">
      调用AI帮你扩写说法，泛化语料。全部满意，点击
      <el-button type="text" :disabled="tableData.loading" @click="addAll"
        >一键添加</el-button
      >
    </div>
    <os-table :tableData="tableData">
      <el-table-column
        prop="number"
        width="100px"
        label="序号"
      ></el-table-column>
      <el-table-column prop="text" label="语料"></el-table-column>
      <el-table-column label="操作" width="100px" align="center">
        <template slot-scope="scope">
          <el-button
            type="text"
            @click="addCropus(scope.row)"
            :disabled="scope.row.added"
            class="btn"
          >
            {{ scope.row.added ? '已添加' : '添加' }}</el-button
          >
        </template>
      </el-table-column>
    </os-table>
  </el-dialog>
</template>

<script>
export default {
  name: 'IflyAIuiWebCorpusDialog',

  data() {
    return {
      isShow: false,
      tableList: [],
      intentId: null,

      tableData: {
        loading: true,
        total: 0,
        page: 1,
        size: 10,
        // handles: ['add'],
        switchOptions: {
          column: 'type',
          active: 2,
          inactive: 3,
          activeText: '入口',
          inactiveText: '对话',
        },
        handleColumnText: '操作',
        list: [],
      },

      copyList: [],
    }
  },

  mounted() {},

  methods: {
    show(intentId) {
      this.isShow = true
      if (intentId) {
        this.intentId = intentId
        const params = {
          intentId: intentId,
        }
        this.tableData.loading = true
        this.$utils.httpPost(
          this.$config.api.AGENT_CORPUS_EXPAND_OLD,
          JSON.stringify(params),
          {
            config: {
              headers: {
                'Content-Type': 'application/json;charset=UTF-8',
              },
            },
            success: (res) => {
              if (res.code == '0') {
                this.tableData.list = res.data.corpusList.map((item, index) => {
                  return {
                    number: index + 1,
                    text: item,
                    added: false,
                  }
                })

                this.tableData.loading = false
              } else {
                this.tableData.loading = false
              }
            },
            error: (err) => {
              this.$message.error(err.desc)
              this.tableData.loading = false
            },
          }
        )
      } else {
        this.tableData.loading = false
      }
    },

    addAll() {
      const params = {
        intentId: this.intentId,
        corpusList: this.tableData.list.map((item) => item.text),
      }

      this.$utils.httpPost(
        this.$config.api.AGENT_CORPUS_ADD_OLD,
        JSON.stringify(params),
        {
          config: {
            headers: {
              'Content-Type': 'application/json;charset=UTF-8',
            },
          },
          success: (res) => {
            if (res.code == '0') {
              this.saving = false
              this.$message.success('添加成功')
              this.tableData.list.forEach((item) => {
                item.added = true
              })
              this.$emit('refresh')
            }
          },
          error: (err) => {
            this.saving = false
            this.$message.error(err.desc)
          },
        }
      )
    },

    addCropus(data) {
      const params = {
        intentId: this.$route.params.intentId,
        corpus: data.text,
      }
      this.$utils.httpPost(
        this.$config.api.AGENT_CORPUS_ADD_OLD,
        JSON.stringify(params),
        {
          config: {
            headers: {
              'Content-Type': 'application/json;charset=UTF-8',
            },
          },
          success: (res) => {
            if (res.code == '0') {
              this.$message.success('添加成功')
              this.$set(data, 'added', true)
              this.$emit('refresh')
            }
          },
          error: (err) => {
            this.$message.error(err.desc)
          },
        }
      )
    },
    cancel() {
      this.isShow = false
      this.intentId = null
      this.tableData.list = []
    },
  },
}
</script>

<style lang="scss" scoped>
.desc-title {
  color: #bdbdbd;
  margin-bottom: 15px;
}
:deep(.el-dialog__body) {
  padding-top: 0px;
}
:deep(.el-dialog__header) {
  padding-bottom: 10px;
}
.btn {
  text-align: left;
}
</style>
