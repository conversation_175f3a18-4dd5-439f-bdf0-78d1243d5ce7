<template>
  <el-dialog title="定制官方技能" :visible.sync="dialog.show" width="524px">
    <el-form ref="form" :model="form" :rules="rules" label-position="top">
      <el-form-item label="源技能" prop="name">
        <el-cascader
          ref="sourceSkill"
          class="create-extend-skill"
          style="width: 460px"
          v-model="sourceSkillArr"
          placeholder="搜索、选择源技能"
          :options="options"
          filterable
          @change="handleChange"
        ></el-cascader>
      </el-form-item>
      <el-form-item label="定制名称" prop="alias">
        <el-input
          v-model.trim="form.alias"
          placeholder="支持中文、英文、数字、下划线格式，不超过32个字符"
          @keyup.enter.native="submitForm"
        />
      </el-form-item>
      <el-form-item label="技能名称">
        <span>{{ form.zhName || '*' }}(</span
        ><span>{{ form.alias || '定制名称' }})</span>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button
        class="dialog-btn"
        type="primary"
        style="min-width: 104px"
        :loading="saving"
        @click="submitForm"
      >
        {{ saving ? '创建中...' : '创建' }}
      </el-button>
    </span>
  </el-dialog>
</template>

<script>
import { mapGetters } from 'vuex'
export default {
  props: {
    dialog: {
      type: Object,
      default: {},
    },
  },
  data() {
    return {
      loading: false,
      form: {
        name: '', //英文
        zhName: '',
        alias: '',
      },
      rules: {
        name: [this.$rules.required('源技能不能为空')],
        alias: [
          this.$rules.required('定制名称不能为空'),
          this.$rules.baseRegLimit(),
          this.$rules.lengthLimit(1, 32, '定制名称长度不能超过32个字符'),
        ],
      },
      saving: false,
      sourceSkillArr: '',
      options: [],
      parentId: '',
      filterableSuggestions: [],
    }
  },
  computed: {
    ...mapGetters({
      subAccount: 'user/subAccount',
    }),
  },
  watch: {
    'dialog.show': function (val, oldVal) {
      if (val) {
        this.form = {
          name: '',
          zhName: '',
          alias: '',
        }
        this.sourceSkillArr = ''
        this.filterableSuggestions = []
        this.$refs.form && this.$refs.form.resetFields()
        this.getSkills()
      }
    },
  },
  methods: {
    getSkills() {
      let self = this
      this.loading = true
      this.$utils.httpGet(
        this.$config.api.STUDIO_EXTEND_GET_CUSTOMIZABLE_SKILL,
        {},
        {
          success: (res) => {
            self.loading = false
            self.options = self.dataFormat(res.data)
          },
          error: (err) => {
            self.loading = false
          },
        }
      )
    },
    dataFormat(data) {
      let obj = data
      let temp = []
      let index = 0
      for (let i in obj) {
        temp.push({
          value: obj[i][0].name,
          label: i, //只有一层的，展示中文名,对应的字段是key
          name: obj[i][0].name,
          zhName: i,
          id: obj[i][0].id,
        })
        if (obj[i].length > 1) {
          let subdata = []
          obj[i].forEach((item) => {
            subdata.push({
              value: item.name,
              label: item.name, //2层的，展示英文名，对应的字段是name
              name: item.name,
              zhName: item.zhName,
              id: item.id,
            })
          })
          temp[index]['children'] = subdata
        }
        index++
      }
      return temp
    },
    handleChange(val) {
      let self = this
      let dataObj
      let checkedNodes = this.$refs['sourceSkill'].getCheckedNodes() || []
      let selectedName = val.length == 1 ? val[0] : val[1]
      if (checkedNodes.length && checkedNodes[0].data.name == selectedName) {
        this.filterableSuggestions = []
        dataObj = checkedNodes[0].data
      } else {
        this.filterableSuggestions = this.$refs['sourceSkill'].suggestions
        dataObj = this.filterableRes(this.filterableSuggestions, selectedName)
      }
      this.parentId = dataObj.id
      this.form.name = dataObj.name
      this.form.zhName = dataObj.zhName
    },
    filterableRes(arr, value) {
      for (let i = 0; i < arr.length; i++) {
        if (arr[i].data.value == value) {
          return arr[i].data
        }
      }
    },
    checkFormName(val) {
      if (!val) {
        this.$refs['form'].validateField('name')
      }
    },
    submitForm() {
      let self = this
      if (self.saving) {
        return
      }
      self.$refs['form'].validate((valid) => {
        if (valid) {
          self.saving = true
          self.$utils.httpPost(
            self.$config.api.STUDIO_EXTEND_SKILL_CREATE,
            {
              parentBusinessName: self.form.name,
              parentId: self.parentId,
              zhName: self.form.zhName + '(' + self.form.alias + ')',
            },
            {
              success: (res) => {
                self.$message.success('创建成功')
                self.subAccount
                  ? self.$router.push({
                      name: 'extend-sub-skill-intentions',
                      params: { skillId: res.data.id },
                    })
                  : self.$router.push({
                      name: 'extend-skill-intentions',
                      params: { skillId: res.data.id },
                    })
              },
              error: (err) => {
                self.saving = false
              },
            }
          )
        } else {
          return false
        }
      })
    },
  },
}
</script>
<style>
.el-cascader__dropdown .el-cascader-menu__wrap {
  max-height: 255px;
}
</style>
