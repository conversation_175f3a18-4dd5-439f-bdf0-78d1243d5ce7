<template>
  <div class="corp">
    <div class="section-title">
      <p class="section-title-bold">合作咨询</p>
      <p class="section-desc">
        <slot></slot>
      </p>
    </div>

    <div class="section-item" style="padding-top: 56px; text-align: center">
<!--      <aiui-button hasTop @click.native="toConsole">申请合作</aiui-button>-->
      <div class="section-button" @click="toConsole">申请合作</div>
    </div>
  </div>
</template>
<script>
export default {
  methods: {
    toConsole() {
      this.$emit('jump')
    },
  },
}
</script>
<style lang="scss" scoped>
p {
  margin-bottom: 0;
}
.corp {
  padding: 80px 0 100px 0;
}
.section-title {
  text-align: center;
}
.section-desc {
  text-align: left;
  margin-top: 20px;
  font-size: 16px;
  line-height: 30px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #999999;
  display: inline-block;
}
.section-title-bold {
  font-size: 34px;
  font-family: Microsoft YaHei;
  font-weight: bold;
  color: #333333;
  text-align: center;
}

.section-button {
  font-size: 16px;
  text-align: center;
  font-weight: 400;
  width: 140px;
  margin: 0 auto;
  height: 40px;
  line-height: 40px;
  border: none;
  color: #fff;
  cursor: pointer;
  transition: 0.6s;
  background: linear-gradient(0deg, #00b4ff 0%, #2371ff 100%);
  border-radius: 20px;
}
</style>
