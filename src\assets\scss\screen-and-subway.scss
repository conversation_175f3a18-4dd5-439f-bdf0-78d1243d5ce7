@media screen and (min-width: 751px) {
  .main-content {
    .m-show {
      display: none;
    }
    &-banner {
      height: 500px;
      overflow: hidden;
      width: 100%;
      .banner-text {
        max-width: 1200px;
        color: #fff;
        height: 100%;
        margin: auto;
        &-button {
          font-size: 18px;
          text-align: center;
          font-weight: 500;
          width: 240px;
          height: 52px;
          line-height: 52px;
          letter-spacing: 1px;
          border: 1px solid #fff;
          border-radius: 1px;
          color: #fff;
          cursor: pointer;
          transition: 0.6s;
          &:hover {
            color: #002985;
            background: #fff;
            transition: 0.3s;
          }
        }
        h2 {
          color: #fff;
          padding-top: 160px;
          margin-bottom: 12px;
          font-size: 46px;
          font-weight: 500;
        }
        p {
          font-size: 20px;
        }
        p:nth-of-type(3) {
          margin-bottom: 50px;
        }
      }
    }

    .section {
      max-width: 1200px;
      // overflow: hidden;
      margin: 0 auto;
      padding: 50px 0;
      &-purchase {
        font-size: 30px;
        text-align: center;
        font-family: Source Han Sans CN;
        font-weight: bold;
        color: #333333;
      }
      &-title {
        font-size: 34px;
        margin: 15px 0 35px;
        text-align: center;
        font-weight: bold;
        p {
          font-size: 16px;
          font-family: Microsoft YaHei;
          font-weight: 400;
          color: #666666;
          line-height: 30px;
          text-align: left;
          display: block;
          margin: 0 auto;
          display: inline-block;
        }
      }
    }
    .section:last-of-type {
      margin-bottom: 50px;
    }

    .section-2 {
      .section-item {
        > ul {
          display: flex;
          // align-content: center;
          // align-items: center;
          width: 100%;
          margin: 0 auto;
          align-items: center;
          justify-content: space-between;
          display: flex;
          li {
            // width: 29.22%;
            // margin: 1.41%;
            img {
              width: 100%;
            }
            // &:hover .app-text {
            //      display: flex;
            //    }
          }
          li + li {
            margin-left: 25px;
          }
        }
      }
    }
    .section-3 {
      background: #f2f5f7;
      max-width: 100%;
      width: 100%;
      .section-item {
        max-width: 1200px;
        margin: 0 auto;
        > ul {
          li {
            display: flex;
            justify-content: space-between;
            align-items: center;
            .section-item-text {
              text-align: center;
              color: #7d7d7d;
              &-title {
                font-size: 30px;
                color: #1b1b1b;
                padding: 10px 0;
              }
              p {
                line-height: 22px;
              }
            }
            .section-item-bg {
              padding-top: 10px;
              img {
                width: 100%;
                vertical-align: middle;
              }
            }
          }
        }
      }
    }

    .section-1 {
      background: white;
      max-width: 100%;
      width: 77%;
      .section-item {
        max-width: 1200px;
        margin: 0 auto;
        > ul {
          li {
            display: flex;
            justify-content: space-between;
            align-items: center;
            .section-item-text {
              // width: 45%;
              text-align: left;
              color: #7d7d7d;
              // left: 12%;
              position: relative;
              &-title {
                font-size: 34px;
                font-family: Microsoft YaHei;
                font-weight: 400;
                color: #666666;
                line-height: 30px;
              }
              p {
                font-size: 16px;
                font-family: Microsoft YaHei;
                font-weight: 400;
                color: #999999;
                line-height: 30px;
                margin-top: 45px;
              }
            }
            .section-item-bg {
              width: 45%;
              padding-top: 10px;
              img {
                width: 60%;
                vertical-align: middle;
                left: 20% !important;
                position: relative;
              }
            }
          }
          li + li {
            margin-top: 56px;
          }
        }
      }
    }
    .section-4 {
      background: #f2f5f7;
      padding-bottom: 170px;
      max-width: 100%;
      border-bottom: 1px solid #e4e7ed;
      .section-item {
        max-width: 1200px;
        width: 60%;
        margin: 0 auto;
        > ul {
          display: flex;
          width: 100%;
          padding: 0 50px;
          align-items: center;

          display: flex;
          justify-content: center;

          li {
            /*width: 4%;*/
            text-align: center;
            position: relative;
            > span {
              display: inline-block;
              width: 20px;
              height: 20px;
              border-radius: 50%;
              color: #fff;
              background: #0162fe;
              text-align: center;
              line-height: 20px;
            }
            p {
              position: absolute;
              text-align: center;
              font-size: 18px;
              width: 200px;
              left: 50%;
              transform: translateX(-50%);
              font-size: 18px;
              font-family: Microsoft YaHei;
              font-weight: 400;
              color: #333333;
              margin-top: 20px;
              span {
                color: #999;
                font-size: 16px;
                text-align: left;
                display: inline-block;
                line-height: 30px;
                margin-top: 20px;
              }
            }
            img {
              width: 80%;
              margin: 0 auto;
            }
          }
          .rightArrow {
            text-align: center;
            margin: 0 auto;
          }
        }
      }
    }
  }
}

.section-2 {
  .app {
    position: relative;
    .app-text {
      color: #fff;
      position: absolute;
      width: 100%;
      height: 36px;
      margin: 0 auto;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      z-index: 3;
      top: 50px;
      text-align: center;
      font-size: 26px;
      &-voice {
        position: absolute;
        display: inline-block;
        bottom: -24%;
        font-size: 16px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #333333;
      }
    }
  }
}
.banner-text-button {
  font-size: 18px;
  text-align: center;
  font-weight: 500;
  width: 240px;
  height: 52px;
  line-height: 52px;
  letter-spacing: 1px;
  border: 1px solid #fff;
  border-radius: 1px;
  color: #fff;
  cursor: pointer;
  transition: 0.6s;
  position: relative;
  top: 80%;
  left: 7%;
  &:hover {
    color: #002985;
    background: #fff;
    transition: 0.3s;
  }
}
.section-5 {
  .section-item {
    .section5-button {
      color: #fff;
      background: #1784e9;
      width: 195px;
      height: 50px;
      line-height: 50px;
      text-align: center;
      margin: 0 auto;
      cursor: pointer;
      &:hover {
        color: #1784e9;
        border: 1px solid #1784e9;
        background: #fff;
        transition: 0.3s;
      }
    }
  }
}

@media screen and (max-width: 750px) {
  .main-content {
    width: 100%;
    margin-top: 60px;
    .pc-show {
      display: none;
    }
    &-banner {
      height: 460px;
      overflow: hidden;
      width: 100%;
      background-size: cover;
      .banner-text {
        max-width: 1200px;
        color: #fff;
        height: 100%;
        margin: auto;
        padding-left: 10px;
        text-align: center;
        h2 {
          padding-top: 40px;
          font-size: 28px;
          font-weight: 500;
          margin-bottom: 10px;
        }
        p {
          font-size: 14px;
          padding: 0 15px;
        }
        p:nth-of-type(4) {
          margin-bottom: 30px;
        }
        .banner-text-button {
          font-size: 16px;
          width: 160px;
          left: revert;
          margin: 0 auto;
        }
      }
    }

    .section {
      padding: 20px 10px;
      &-purchase {
        font-size: 30px;
        text-align: center;
        font-family: Source Han Sans CN;
        font-weight: bold;
        color: #333333;
      }

      .pc-show {
        padding-top: 0.9%;
        font-size: 20px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #333333;
        text-align: center;
        margin-bottom: 4.25%;

        &-ai {
          font-size: 14px;
          color: #666666;
          text-align: left;
        }
      }
      &-title {
        font-size: 26px;
        margin: 0 0 35px;
        text-align: center;
        p {
          font-size: 16px;
          font-family: Microsoft YaHei;
          font-weight: 400;
          color: #666666;
          line-height: 30px;
        }
      }
    }
    .section:last-of-type {
      margin-bottom: 50px;
    }

    .section-2 {
      .section-item {
        > ul {
          padding: 0 10px;
          li {
            width: 100%;
            margin-bottom: 20px;
            .app-text {
              display: flex;
            }
            .app-text-voice {
              display: none;
            }
            img {
              width: 100%;
            }
          }
        }
      }
    }

    .section-3 {
      .section-item {
        .section-item-bg.dialog {
          width: 100% !important;
          margin: 0 auto;
          position: relative;
          max-width: 420px;
          right: inherit;
        }
        .dialog-purcgase {
          width: 50% !important;
          margin: 0 auto;
        }
        .user-manipulate {
          width: 100%;
          top: 30px;
          right: revert !important;
        }
      }
    }
    .section-1 {
      background: #f2f5f7;
      width: 100%;
      .section-item {
        width: 100%;
        > ul {
          li {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 10px 0;
            .section-item-text {
              text-align: left;
              color: #7d7d7d;
              padding: 0 10px;
              width: 50%;
              &-title {
                font-size: 18px;
                color: #1b1b1b;
                padding: 10px 0;
              }
              p {
                line-height: 22px;
                font-size: 14px;
              }
            }
            .section-item-bg {
              width: 50%;
              display: flex;
              align-items: center;
              img {
                width: 100%;
              }
            }
          }
        }
      }
    }
    .section-4 {
      background: #f2f5f7;
      padding-bottom: 170px;
      max-width: 100%;
      border-bottom: 1px solid #e4e7ed;
      .section-item {
        max-width: 1200px;
        width: 100%;
        margin: 0 auto;
        > ul {
          display: flex;
          width: 100%;
          padding: 0 50px;
          align-items: center;

          display: flex;
          justify-content: center;

          li {
            text-align: center;
            position: relative;
            display: block;
            > span {
              display: inline-block;
              width: 20px;
              height: 20px;
              border-radius: 50%;
              color: #fff;
              background: #0162fe;
              text-align: center;
              line-height: 20px;
            }
            p {
              position: absolute;
              text-align: center;
              font-size: 18px;
              span {
                color: #8c8c8c;
                font-size: 14px;
              }
            }
            img {
              width: 40%;
              margin: 0 auto;
            }
          }
          .rightArrow {
            text-align: center;
            margin: 0 auto;
          }
        }
      }
    }
  }
}
