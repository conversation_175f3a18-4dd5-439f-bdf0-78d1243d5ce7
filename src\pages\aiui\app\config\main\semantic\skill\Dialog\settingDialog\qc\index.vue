<template>
  <div>
    <p class="item-title">语义分发管理</p>

    <el-form
      :model="form"
      ref="form"
      :rules="rules"
      :disabled="!subAccountEditable"
      label-position="left"
      class="qc-conifg-form mgt24"
      :inline="true"
    >
      <el-form-item
        class="form-item-in-line1"
        label="后处理"
        prop="jsName"
        style="margin-bottom: 10px"
      >
        <el-select v-model="form.jsName" @change="changeJs">
          <el-option
            v-for="(item, index) in jsList"
            :key="index"
            :label="item.zhName"
            :value="item.jsName"
            @click.native="changeJsItem(item)"
          >
            <span
              class="txt-ellipsis-nowrap zh-name-text"
              :title="item.zhName"
              >{{ item.zhName }}</span
            >
            <span class="txt-ellipsis-nowrap name-text" :title="item.jsName">{{
              item.jsName
            }}</span>
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item
        class="form-item-version form-item-in-line1"
        prop="verNo"
        style="margin-bottom: 10px; margin-left: 6px !important"
      >
        <el-select v-model="form.verNo" @change="changeJsVersion">
          <el-option
            v-for="(item, index) in jsVersions"
            :key="index"
            :label="item.verNo"
            :value="item.verNo"
          >
            <span style="float: left">{{ item.verNo }}</span>
            <span class="js-version-test btn-tip" v-if="item.isTest == 1"
              >测试</span
            >
            <span
              class="js-version-time"
              style="float: right; color: #b8babf"
              v-if="item.operateTime"
              >{{ item.operateTime | date }}</span
            >
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <a v-if="subAccountEditable" @click="toClearForm">重置</a>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'qc-conifg',
  props: {
    appId: '',
    currentScene: Object,
    subAccountEditable: Boolean,
    show: Boolean,
  },
  data() {
    return {
      form: {
        skillId: '',
        sysNumber: '',
        outNumber: '',
        canUpdate: false,
        verNo: '',
        jsName: '',
      },
      rules: {
        jsName: [{ validator: this.checkValidate, trigger: ['blur'] }],
      },
      initForm: {},
      qcSkills: [],
      initQcJsName: '',
      jsList: [],
      currentJsId: '',
      jsVersions: [],
      change: false,
      qcUpdated: false,
      dialog: {
        show: false,
      },
      clearForm: false,
    }
  },
  computed: {},
  watch: {
    // saving() {
    //   if (this.saving && this.change) {
    //     this.beforeSave()
    //   }
    // },
    show(val) {
      if (val) {
        this.getQcConfig() // 获取配置列表
      }
    },
  },

  methods: {
    emitChange() {
      // this.change = true
      // this.$emit('change')
      this.beforeSave()
    },
    checkValidate(rule, value, callback) {
      // if (!value && this.form.skillId ) {
      //   callback(new Error('配置语义分发管理时，后处理不能为空'))
      // }
      callback()
    },
    getQcConfig() {
      let self = this
      this.$utils.httpGet(
        this.$config.api.AIUI_APP_QC_CONFIG,
        {
          appid: this.appId,
          sceneName: this.currentScene.sceneBoxName,
        },
        {
          success: (res) => {
            if (res.flag) {
              self.getJsList()
              if (!Object.keys(res.data).length) return
              self.form = res.data
              self.initQcJsName = res.data.jsName
              self.initForm = JSON.parse(JSON.stringify(self.form))
            } else {
              self.$message.error(res.desc)
            }
          },
        }
      )
    },
    getSkills() {
      let self = this
      this.$utils.httpGet(
        this.$config.api.AIUI_APP_QC_SKILLS,
        {},
        {
          success: (res) => {
            if (res.flag) {
              if (!Object.keys(res.data).length) return
              self.qcSkills = res.data.skills
            } else {
              self.$message.error(res.desc)
            }
          },
        }
      )
    },
    getJsList() {
      let self = this
      this.$utils.httpGet(
        this.$config.api.AIUI_APP_QC_JS_LIST,
        {},
        {
          success: (res) => {
            if (res.flag) {
              if (!Object.keys(res.data).length) return
              self.jsList = res.data.js
              self.jsList.filter((item) => {
                if (item.jsName == self.form.jsName) {
                  self.currentJsId = item.jsId
                  this.getJsVersions(self.currentJsId)
                }
              })
            } else {
              self.$message.error(res.desc)
            }
          },
        }
      )
    },
    getJsVersions(id) {
      let self = this
      this.$utils.httpGet(
        this.$config.api.AIUI_APP_QC_JS_VERSIONS,
        {
          jsId: id,
          appid: self.appId,
        },
        {
          success: (res) => {
            if (res.flag) {
              if (!Object.keys(res.data).length) return
              self.jsVersions = res.data.jsversions
              if (!self.form.verNo) {
                self.form.verNo = self.jsVersions[0].verNo
                self.emitChange()
              }
            } else {
              self.$message.error(res.desc)
            }
          },
        }
      )
    },

    changeJs() {
      this.$refs.form.validateField('jsName')
      // this.emitChange()
    },
    changeJsVersion() {
      this.emitChange()
    },
    toClearForm() {
      let self = this
      if (!self.initQcJsName) {
        return self.resetForm()
      }
      let desc = '重置后现有配置将被置空，不再使用该能力。'
      this.$confirm(desc, '确定要重置吗？', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        confirmButtonClass: 'el-button--danger',
        type: 'warning',
        showClose: false,
      })
        .then(() => {
          self.resetForm()
          self.clearForm = true
          self.initQcJsName = ''
          self.emitChange()
        })
        .catch(() => {})
    },
    resetForm() {
      this.$refs.form && this.$refs.form.resetFields()
      this.form = {}
      this.initForm = {}
      this.jsVersions = []
      this.currentJsId = null
    },
    changeJsItem(item) {
      let self = this
      if (item.jsId !== self.currentJsId) {
        self.currentJsId = item.jsId
        self.form.verNo = ''
        this.getJsVersions(self.currentJsId)
        // this.emitChange()
      }
    },
    beforeSave() {
      let self = this
      self.$refs.form.validate((valid) => {
        if (valid) {
          this.$emit('validSuccess')
          self.save()
        } else {
          this.$message.error('语义分发管理参数配置有误')
          this.$emit('saveFail')
        }
      })
    },
    save() {
      let self = this
      if (!self.form.jsName && !self.clearForm) {
        self.$refs.form.clearValidate()
        self.$emit('saveSuccess')
        return
      }
      let data = {
        jsName: self.form.jsName || '',
        verNo: self.form.verNo || '',
        // skillId: self.form.skillId || '',
        appid: self.appId,
        sceneName: self.currentScene.sceneBoxName,
      }
      if (self.qcUpdated) {
        data.flag = true
        data.sysNumber = self.form.sysNumber
        data.outNumber = self.form.outNumber
      }
      if (self.clearForm) {
        data.action = 'delete'
      }
      this.$utils.httpPost(this.$config.api.AIUI_APP_QC_SAVE_CONFIG, data, {
        success: (res) => {
          if (res.flag) {
            self.change = false
            self.qcUpdated = false
            self.clearForm = false
            self.$refs.form.clearValidate()
            self.$emit('saveSuccess', 2)
            self.getQcConfig()
          } else {
            self.$message.error(res.desc)
            self.$emit('saveFail')
          }
        },
        error: () => {
          this.$emit('saveFail')
        },
      })
    },
  },
}
</script>

<style lang="scss" scoped>
@import '../../common.scss';

.btn-tip {
  display: inline-block;
  margin-left: 6px;
  padding: 0 12px;
  height: 24px;
  cursor: pointer;
  line-height: 24px;
  text-align: center;
  border-radius: 12px;
}
.update-btn {
  color: #1784e9;
  background-color: rgba(23, 132, 233, 0.12);
}
.js-version-test {
  margin-left: 12px;
  color: $warning;
  background-color: $warning-light-12;
}
.zh-name-text {
  float: left;
  width: 44%;
}
.name-text {
  float: right;
  width: 54%;
  text-align: right;
  color: #b8babf;
}
</style>
<style lang="scss" scoped>
.qc-conifg-form {
  // .el-form-item {
  //   width: calc(50% - 10px);
  // }
  // :deep(.form-item-in-line) {
  //   display: inline-block;
  // }
  // .el-select {
  //   width: 100%;
  // }
  // .form-item-version {
  //   .el-form-item__content {
  //     margin-left: 0 !important;
  //   }
  // }
}
</style>
