<template>
  <el-upload
    :class="['upload-zip-wrap', { uploaded: JSType == 'zip' }]"
    drag
    :action="`${baseUrl}/aiui/${
      subAccount ? 'sub' : ''
    }web/cloudFunction/uploadJS?skillId=${businessId}`"
    :file-list="fileList"
    :limit="1"
    :on-exceed="exceed"
    :before-upload="beforeUpload"
    :on-success="success"
    :before-remove="beforeRemove"
    :on-remove="remove"
    :disabled="!subAccountEditable"
  >
    <template v-if="fileList && !fileList.length">
      <i class="ic-r-upload" style="margin: 43px 0 12px; font-size: 64px"></i>
      <div class="el-upload__text">拖拽 .zip 文件到此处上传</div>
      <el-popover
        v-if="JSType == 'js'"
        placement="bottom-start"
        width="360"
        trigger="hover"
        content="您已上传了单文件。选择多文件上传后，单文件将会被覆盖！"
      >
        <el-button slot="reference" type="primary" size="small"
          >上传文件</el-button
        >
      </el-popover>
      <el-button
        v-else
        type="primary"
        size="small"
        :disabled="!subAccountEditable"
        >上传文件</el-button
      >
    </template>
    <div class="upload-time" v-if="fileList && fileList.length">
      {{ fileList[0].uploadTime | date('yyyy-MM-dd hh:mm') }}
    </div>
    <i
      v-if="JSType == 'zip'"
      class="ic-r-download"
      @click.stop.prevent="download"
    ></i>
    <i
      v-if="JSType == 'zip' && subAccountEditable"
      class="ic-r-delete"
      @click.stop.prevent="beforeDelete"
    ></i>
    <div class="mask" v-if="hideTip"></div>
  </el-upload>
</template>
<script>
export default {
  props: {
    businessId: '',
    fileList: {
      type: Array,
    },
    JSType: '',
    subAccount: Boolean,
    subAccountEditable: Boolean,
    limitSize: {
      type: Number,
      default: 100,
    },
  },
  data() {
    return {
      baseUrl: this.$config.server,
      // limitSize: 100,
      hideTip: false,
      visible: false,
    }
  },
  watch: {
    fileList(val) {
      if (val && val.length) {
        this.hideTip = true
      }
    },
  },
  methods: {
    beforeUpload(file) {
      this.hideTip = true
      let reg = /\.zip$/i
      let isZIP = reg.test(file.name)
      let isExceed = file.size / 1024 > this.limitSize
      if (!isZIP) {
        this.$message.error('上传文件仅支持zip文件')
      }
      if (isExceed) {
        this.$message.error(`上传文件不能超过${this.limitSize}KB`)
      }
      return isZIP && !isExceed
    },
    success(data) {
      if (data.flag) {
        this.$emit('setShowLog', true)
        this.$emit('getInfo')
        this.$message.success('上传成功')
        this.hideTip = false
      } else {
        this.$message.warning(data.desc || '上传失败')
        this.hideTip = false
        this.$emit('getInfo')
      }
    },
    beforeRemove(file) {
      let reg = /\.zip$/i
      let isExceed = file.size / 1024 > this.limitSize
      if ((file && !reg.test(file.name)) || isExceed) {
        this.fileList.splice(0)
        this.hideTip = false
        return false
      } else {
        return true
      }
    },
    beforeDelete() {
      if (this.disabled) {
        return
      }
      this.$confirm('删除后不可恢复', '确定删除吗？', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          this.remove()
        })
        .catch(() => {})
    },
    remove() {
      let self = this
      self.$utils.httpPost(
        self.$config.api.STUDIO_PROCESS_DELETE_FILE,
        {
          skillId: self.businessId,
        },
        {
          success: (res) => {
            self.hideTip = false
            self.$emit('setShowLog', false)
            self.$emit('getInfo')
            self.$message.success('删除成功')
            self.visible = false
          },
          error: (err) => {
            self.visible = false
          },
        }
      )
    },
    exceed(file, fileList) {
      if (file && fileList && fileList.length) {
        this.$message.error('最多只能上传一份文件')
      }
    },
    download() {
      window.open(
        this.baseUrl +
          `/aiui/${
            this.subAccount ? 'sub' : ''
          }web/cloudFunction/downloadFile?skillId=` +
          this.businessId,
        '_self'
      )
    },
  },
}
</script>
<style lang="scss" scoped>
.el-upload__text {
  margin-bottom: 16px;
  color: $semi-black;
}
.mask {
  position: absolute;
  top: 0;
  width: 100%;
  height: 240px;
  background: $grey1;
}
.upload-time {
  position: absolute;
  top: 114px;
  left: 172px;
  z-index: 10;
  font-size: 12px;
  color: $grey5;
}
.ic-r-download {
  position: absolute;
  top: 101px;
  right: 172px;
  z-index: 10;
  color: $primary;
}
.ic-r-delete {
  position: absolute;
  top: 100px;
  right: 152px;
  z-index: 10;
  color: $grey4;
  display: inline-block;
}
.give-up-save-title {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  i {
    font-size: 16px;
    color: $warning;
  }
  span {
    margin-left: 8px;
    font-size: 16px;
    font-weight: 500;
  }
}
.give-up-save-content {
  padding-left: 24px;
  margin-bottom: 16px;
}
</style>
<style lang="scss">
.upload-zip-wrap {
  position: relative;
  .el-upload,
  .el-upload-dragger {
    width: 100%;
    height: 240px;
    background: $grey1;
  }
  .el-upload-list {
    position: absolute;
    top: calc(50% - 30px);
    left: 0;
    padding: 0 148px;
    width: 100%;
  }
  .el-upload-list__item {
    margin: 0;
    height: 55px;
    border-radius: 0;
    transition-duration: 0s;
  }
  .el-upload-list__item.is-success {
    border-bottom: 1px solid $grey3;
  }
  .el-icon-document {
    color: $primary;
  }
  .el-upload-list__item:hover {
    background: none;
  }
  .el-icon-close-tip {
    display: none;
  }

  // .is-success{
  //   .el-upload-list__item-status-label{
  //     display: none;
  //   }
  // }
}
.upload-zip-wrap.uploaded {
  .el-icon-upload-success,
  .el-icon-close {
    display: none;
  }
}
</style>
