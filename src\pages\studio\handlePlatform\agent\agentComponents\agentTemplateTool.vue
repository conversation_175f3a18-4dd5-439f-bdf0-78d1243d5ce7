<template>
  <div class="main_page">
    <div class="title">意图</div>
    <div class="operate-wrap">
      <el-input
        placeholder="搜索意图名称 描述"
        size="medium"
        v-model="searchVal"
        class="search-input"
        clearable
        @keyup.enter.native="searchIntent"
      >
        <i
          slot="suffix"
          class="el-input__icon el-icon-search search-area-btn"
          @click="searchIntent"
      /></el-input>
      <el-button type="primary" plain @click="addIntent" class="add_btn"
        ><i class="el-icon-plus" style="margin-right: 5px"></i
        >创建意图</el-button
      >
      <el-button type="primary" @click="quoteIntent">引用官方意图</el-button>
    </div>

    <el-scrollbar v-loading="loading">
      <div class="intent_list" v-if="intentList.length > 0">
        <IntentItem
          v-for="item in intentList"
          :key="item.intentId"
          :intent-data="item"
          @click="handleIntentItem"
          @command="handleDropdownItem"
        />
      </div>
      <Empty v-else style="height: calc(100% - 90px)" />
    </el-scrollbar>

    <el-pagination
      ref="pagination"
      :current-page="page"
      :page-size="size"
      :total="total"
      :layout="pageLayout"
      @current-change="pageChange"
      class="txt-al-c"
    ></el-pagination>
    <IntentDialog ref="IntentDialog" @refresh="refresh" />
    <IntentLibDialog ref="IntentLibDialog" @refresh="refresh" />
  </div>
</template>

<script>
import IntentDialog from '../intentDialog.vue'
import IntentItem from './intentItem.vue'
import IntentLibDialog from '../intentLibDialog.vue'
import Empty from './empty.vue'

export default {
  name: 'AgentTemplateTool',
  components: {
    IntentDialog,
    IntentItem,
    IntentLibDialog,
    Empty,
  },
  data() {
    return {
      searchVal: '',
      total: 0,
      page: 1,
      size: 10,
      intentList: [],
      loading: false,
    }
  },
  created() {
    this.searchIntent()
  },
  computed: {
    pageLayout() {
      return this.total > 10
        ? 'prev, pager, next, jumper, total'
        : 'prev, pager, next'
    },
  },
  methods: {
    searchIntent() {
      const params = {
        pluginId: this.$route.params.agentId,
        pageIndex: this.page,
        pageSize: this.size,
        searchKey: this.searchVal,
      }
      this.loading = true
      this.$utils.httpPost(
        // '/aiui-agent/intent/published/list', // this.$config.api.AGENT_INTENT_TABLE_LIST,
        this.$config.api.AGENT_INTENT_TABLE_LIST,
        JSON.stringify(params),
        {
          config: {
            headers: {
              'Content-Type': 'application/json;charset=UTF-8',
            },
          },
          success: (res) => {
            console.log(res, '模板智能体的intentList')
            this.intentList = res.data.data
            // .filter((item) => {
            //   return item.versionList.find((a) => a.quote)
            // })
            this.total = res.data.totalSize
            this.loading = false
          },
          error: (err) => {
            this.loading = false
          },
        }
      )
    },
    pageChange(e) {
      console.log('pageChange=>', e)
      this.page = e
      this.searchIntent()
    },

    refresh() {
      this.searchIntent()
    },
    getInfo() {},
    addIntent() {
      this.$refs.IntentDialog.show()
    },
    quoteIntent() {
      this.$refs.IntentLibDialog.show()
    },
    handleIntentItem(item) {
      console.log('点击某一项意图=>', item)
      // 这个是模板的智能体 进入到 详情的第二级页面
      localStorage.setItem('intentName', item.intentName)
      this.$router.push({
        name: 'studio-handle-platform-agent-detail-config',
        params: {
          agentId: this.$route.params.agentId,
          agentType: 21,
          intentId: item.intentId,
        },
      })
    },

    gotoCorpus(item) {
      console.log(item, 'item=====>')
      this.$router.push({
        name: 'studio-handle-platform-intent-detail',
        params: {
          intentId: item.intentId,
          agentId: this.$route.params.agentId,
          intentVersion: item.version,
          official: item.official,
        },
      })
    },
    handleDropdownItem(command, item) {
      switch (command) {
        case 'edit':
          this.toEditIntent(item)
          break
        case 'del':
          if (item.official === 1) {
            this.toDereference(item)
          } else {
            this.toDelIntent(item)
          }

          break
        case 'detailConfig':
          this.handleIntentItem(item)
          break

        case 'detailCorpus':
          this.gotoCorpus(item)
          break
      }
    },
    toDelIntent(item) {
      console.log('点击删除 item=>', item)

      let self = this
      this.$confirm('意图删除后不可恢复，请谨慎操作。', `确定删除该意图?`, {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        confirmButtonClass: 'el-button--danger',
        type: 'warning',
        showClose: false,
      })
        .then(() => {
          self.delAgent(item)
        })
        .catch(() => {})
    },

    toDereference(item) {
      const params = {
        pluginId: this.$route.params.agentId,
        intentId: item.intentId,
        intentVersion: item.version,
        quoteFlag: false,
      }
      this.$utils.httpPost(
        this.$config.api.AGENT_OFFICAL_INTENT_QUOTE,
        JSON.stringify(params),
        {
          config: {
            headers: {
              'Content-Type': 'application/json;charset=UTF-8',
            },
          },
          success: (res) => {
            if (res.code == '0') {
              this.$message.success('操作成功')
              this.searchIntent()
              this.$refs.IntentLibDialog.getOfficialIntentList()
            }
          },
          error: (err) => {
            this.$message.error(err?.desc)
          },
        }
      )
    },
    delAgent(data) {
      const params = {
        intentId: data.intentId,
        pluginId: this.$route.params.agentId,
      }
      this.$utils.httpPost(
        this.$config.api.AGENT_INDENT_DELETE,
        JSON.stringify(params),
        {
          config: {
            headers: {
              'Content-Type': 'application/json;charset=UTF-8',
            },
          },
          success: (res) => {
            this.$message.success('删除成功')
            this.searchIntent()
          },
          error: (err) => {
            this.$message.error(err.desc)
          },
        }
      )
    },

    toEditIntent(item) {
      this.$refs.IntentDialog.show(this.$route.params.agentId, item)
    },

    filterIntentData(item) {
      console.log(item, 'filterIntentData函数的')
      const list = item.versionList.filter((i) => i.quote)
      let obj = list?.length > 0 ? list[0] : {}
      return { ...obj }
    },
  },
}
</script>

<style lang="scss" scoped>
.main_page {
  flex: 1;
  .title {
    font-weight: 600;
    font-size: 24px;
    color: #000000;
  }
  .operate-wrap {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    .add_btn {
      margin-left: auto;
    }
    .search-input {
      width: 230px;
      margin-right: 20px;
    }
  }
  .el-scrollbar {
    height: calc(100vh - 283px);
    .intent_list {
      display: grid;
      grid-template-columns: repeat(1, 1fr);
      gap: 10px;
    }
  }

  :deep(.el-scrollbar__view) {
    height: 100%;
  }
  .el-pagination {
    margin-top: 24px;
  }
}
</style>
