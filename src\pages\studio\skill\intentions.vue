<template>
  <os-page :options="pageOptions" v-loading="tableData.loading">
    <studio-skill-header-right slot="btn" />
    <div class="os-scroll os_scroll">
      <div class="mgt32 mgb24" style="font-size: 0">
        <el-button
          icon="ic-r-plus"
          type="primary"
          size="small"
          :disabled="!subAccountEditable"
          @click="openCreateIntent"
        >
          创建意图
        </el-button>
        <el-button
          class="mgr16"
          type="primary"
          size="small"
          :disabled="!subAccountEditable"
          @click="referOfficialIntentions"
        >
          引用官方意图
        </el-button>
        <upload
          v-if="skill && skill.type != 8"
          :businessName="skill.zhName"
          :businessId="businessId"
          :limitCount="limitCount"
          :subAccountEditable="subAccountEditable"
          :skillType="skill.type"
          :subAccount="subAccount"
          @setLoad="setLoad"
          @reloadAfterUpload="reloadAfterUpload"
        ></upload>
        <el-popover
          style="margin-left: 10px"
          v-if="skill && skill.type != 8"
          placement="right"
          width="136"
          :disabled="!subAccountEditable"
          trigger="click"
        >
          <el-switch
            v-model="isFuzzy"
            :active-value="true"
            :inactive-value="false"
            active-text="模糊匹配"
            @change="save"
          />
          <i
            slot="reference"
            class="ic-r-more btn-fuzzy"
            :class="{ 'not-allowed': !subAccountEditable }"
          />
        </el-popover>

        <!-- <div class="ib">
          <el-button size="small" @click="openAddOfficialIntent">
            引用官方意图
          </el-button>
        </div> -->
      </div>
      <div class="mgb24" @keyup.enter="searchIntention">
        <el-input
          class="search-area"
          placeholder="搜索意图"
          v-model="intentionSearchName"
        >
          <i
            slot="suffix"
            class="el-input__icon el-icon-search search-area-btn"
            @click="searchIntention"
          />
        </el-input>
      </div>

      <os-table
        class="intentions-table gutter-table-style secondary-table"
        :tableData="tableData"
        height="calc(100vh - 264px)"
        @change="getIntentions"
        @edit="toEdit"
        @del="toDel"
        @switch="changeSwitch"
      >
        <el-table-column type="index" width="50">
          <template slot-scope="scope">
            {{ (tableData.page - 1) * tableData.size + scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="zhName" label="意图名称" min-width="200px">
          <template slot-scope="scope">
            <div
              class="intent-zhname ib"
              :class="{ 'no-pointer': scope.row.type && scope.row.type == 6 }"
              @click="toEdit(scope.row)"
            >
              {{ scope.row.zhName }}
            </div>
            <div
              class="intent-tag ib"
              v-if="scope.row.official || scope.row.type == 6"
            >
              官
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="英文标识" min-width="200px">
          <template slot-scope="scope">
            <div
              class="intent-name"
              :class="{ 'no-pointer': scope.row.type && scope.row.type == 6 }"
              @click="toEdit(scope.row)"
            >
              {{ scope.row.name }}
            </div>
          </template>
        </el-table-column>
        <!-- <template v-if="!subAccountEditable"> -->
        <el-table-column
          v-if="skill.privateSkill || skill.secondType == 2"
          prop="type"
          label="意图类型"
          width="140px"
        >
          <template slot-scope="scope">
            <span v-if="scope.row.type !== 6">{{
              scope.row.type == 2 ? '入口' : '对话'
            }}</span>
          </template>
        </el-table-column>

        <!-- <el-table-column
            label="操作">
            <template slot-scope="scope" v-if="scope.row.type !== 6">
              <i class="sub-account ic-r-edit" @click="toEdit(scope.row)"/>
              <i class="sub-account ic-r-delete" @click="toDel(scope.row)"/>
            </template>
          </el-table-column> -->
      </os-table>
    </div>
    <create-intent-dialog
      :dialog="createIntentDialog"
      @change="getIntentions"
    />
    <add-official-intent-dialog
      :dialog="addOfficialIntentDialog"
      @change="getIntentions"
    />
  </os-page>
</template>

<script>
import { mapGetters } from 'vuex'
import CreateIntentDialog from './dialog/createIntent'
import AddOfficialIntentDialog from './dialog/addOfficialIntent'
import Upload from './uploadIntent'

export default {
  name: 'skill-intentions',
  data() {
    return {
      pageOptions: {
        title: '意图',
        loading: false,
      },
      intentionSearchName: '',
      tableData: {
        loading: true,
        total: 0,
        page: 1,
        size: 10,
        handles: ['edit', 'del'],
        switchOptions: {
          column: 'type',
          active: 2,
          inactive: 3,
          activeText: '入口',
          inactiveText: '对话',
        },
        tips: {
          content:
            '用户可以通过命中【入口意图】来进入技能，只有在用户处于技能中时，才可能命中【对话意图】',
          icon: 'el-icon-question tooltip-icon',
        },
        handleColumnText: '操作',
        list: [],
      },
      createIntentDialog: {
        show: false,
      },
      addOfficialIntentDialog: {
        show: false,
      },
      selectedIntention: {},
      isFuzzy: true,
    }
  },
  computed: {
    ...mapGetters({
      businessId: 'studioSkill/id',
      skill: 'studioSkill/skill',
      limitCount: 'aiuiApp/limitCount',
      subAccount: 'user/subAccount',
      subAccountSkillAuths: 'studioSkill/subAccountSkillAuths',
    }),
    subAccountEditable() {
      let auth = this.subAccountSkillAuths[this.businessId]
      if (auth == 2) {
        this.tableData.handles = []
        return false
      } else {
        return true
      }
    },
  },
  watch: {
    'skill.isFuzzy': function (val) {
      this.isFuzzy = val ? true : false
    },
  },
  created() {
    this.getIntentions()
    this.$store.dispatch('aiuiApp/setLimitCount')
    this.isFuzzy = this.skill.isFuzzy ? true : false
  },
  methods: {
    save() {
      let self = this
      this.$utils.httpPost(
        this.$config.api.STUDIO_SKILL_UP_FUZZY,
        {
          skillId: this.businessId,
          isFuzzy: this.isFuzzy ? 1 : 0,
        },
        {
          success: (res) => {
            self.$message.success('保存成功')
            self.$store.dispatch('studioSkill/setSkill', this.businessId)
          },
          error: (err) => {
            console.log(err)
          },
        }
      )
    },
    getIntentions(page) {
      let self = this
      this.tableData.loading = true
      this.$utils.httpGet(
        this.$config.api.STUDIO_INTENTS,
        {
          businessId: this.businessId,
          pageIndex: page || this.tableData.page,
          pageSize: this.tableData.size,
          search: this.intentionSearchName,
        },
        {
          success: (res) => {
            this.tableData.total = res.data.count
            this.tableData.page = res.data.pageIndex
            this.tableData.size = res.data.pageSize
            this.tableData.loading = false
            let fallbackIndex,
              cancelIndex,
              fallbackIntent,
              cancelIntent,
              tmpIntents = res.data.intents || []
            tmpIntents.forEach((item, index) => {
              if (/YesIntent|NoIntent/.test(item.name)) {
                item.noDel = true
              }
              if (item.type !== 6) return
              item.noEdit = true
              if (item.name == 'iFLYTEK.fallback_intent') {
                fallbackIndex = index
                fallbackIntent = item
                item.tip =
                  '当所有意图均无法响应用户表述时，兜底回复将会随机返回给用户'
              }
              if (item.name == 'iFLYTEK.cancel_intent') {
                item.noTips = true
                cancelIndex = index
                cancelIntent = item
              }
              if (
                !this.skill.privateSkill &&
                /fallback|cancel/.test(item.name)
              ) {
                item.noDel = true
              }
            })

            this.tableData.list = tmpIntents

            // if (fallbackIndex === 0 && cancelIndex == 1) return
            // if (fallbackIndex >= 0 && cancelIndex >= 0) {
            //   tmpIntents.splice(0, 0, fallbackIntent)
            //   tmpIntents.splice(1, 0, cancelIntent)
            //   if (fallbackIndex < cancelIndex) {
            //     tmpIntents.splice(fallbackIndex + 2, 1)
            //     tmpIntents.splice(cancelIndex + 1, 1)
            //   } else {
            //     tmpIntents.splice(cancelIndex + 2, 1)
            //     tmpIntents.splice(fallbackIndex + 1, 1)
            //   }
            // } else if (fallbackIndex >= 0 && !(cancelIndex >= 0)) {
            //   tmpIntents.splice(0, 0, fallbackIntent)
            //   tmpIntents.splice(fallbackIndex + 1, 1)
            // } else if (cancelIndex >= 0 && !(fallbackIndex >= 0)) {
            //   tmpIntents.splice(0, 0, cancelIntent)
            //   tmpIntents.splice(cancelIndex + 1, 1)
            // }
          },
          error: (err) => {
            this.tableData.loading = false
            console.log('page=>>')
            console.log(err)
          },
        }
      )
    },
    searchIntention() {
      this.getIntentions(1)
    },
    toDel(row) {
      let self = this
      let confirmTitle =
        row.zhName && row.zhName.length > 9
          ? `${row.zhName.substring(0, 9)}...`
          : row.zhName
      let desc
      if (this.tableData.list.length === 1) {
        desc = '至少保留一个意图，否则系统将无法进入您的技能。'
      } else {
        desc = '意图删除后不可恢复，请谨慎操作。'
      }

      this.$confirm(desc, `确定删除意图 - ${confirmTitle}`, {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        confirmButtonClass: 'el-button--danger',
        type: 'warning',
        showClose: false,
      })
        .then(() => {
          self.delIntent(row)
        })
        .catch(() => {})
    },
    delIntent(row) {
      let self = this
      this.$utils.httpPost(
        this.$config.api.STUDIO_DEL_INTENT,
        {
          businessId: this.businessId,
          intentId: row.id,
        },
        {
          success: (res) => {
            self.$message.success('删除成功')
            if (self.tableData.list.length === 1 && self.tableData.page > 1) {
              self.tableData.page -= 1
            }
            self.getIntentions()
            self.$store.dispatch('studioSkill/initHasSkillQuote', 0)
            self.$store.dispatch('studioSkill/initHasSkillQuote', 1)
          },
          error: (err) => {
            console.log('page=>>')
            console.log(err)
          },
        }
      )
    },
    openCreateIntent() {
      // this.createIntentDialog.show = true
      this.$store.dispatch('studioSkill/openCreateIntentDialog')
    },
    openAddOfficialIntent() {
      this.addOfficialIntentDialog.show = true
    },
    toEdit(intention) {
      if (intention.type == 6) return
      if (this.subAccount) {
        this.$router.push({
          name: 'sub-skill-intention',
          params: { intentId: intention.id },
        })
      } else {
        this.$router.push({
          name: 'skill-intention',
          params: { intentId: intention.id },
        })
      }
    },
    changeSwitch(data) {
      let self = this
      this.$utils.httpPost(
        this.$config.api.STUDIO_ADD_EDIT_INTENT,
        {
          businessId: this.businessId,
          id: data.id,
          name: data.name,
          zhName: data.zhName,
          type: data.type,
        },
        {
          success: (res) => {
            self.$message.success('保存成功')
            self.$store.dispatch('studioSkill/setSkill', this.businessId)
          },
          error: (err) => {
            self.getIntentions()
          },
        }
      )
    },
    pageReturn() {
      this.selectedIntention = {}
    },
    setLoad(val) {
      this.tableData.loading = val
    },
    reloadAfterUpload() {
      this.getIntentions()
      this.$store.dispatch('studioSkill/setSkill', this.businessId)
      this.$store.dispatch('studioSkill/initHasSkillQuote', 0)
      this.$store.dispatch('studioSkill/initHasSkillQuote', 1)
    },

    referOfficialIntentions() {
      if (!this.subAccount) {
        this.$router.push({
          name: 'refer-official-intentions',
        })
      } else {
        this.$router.push({
          name: 'sub-refer-official-intentions',
        })
      }
    },
  },
  components: {
    CreateIntentDialog,
    AddOfficialIntentDialog,
    Upload,
  },
}
</script>

<style lang="scss" scoped>
.os_scroll {
  overflow-y: hidden !important;
}
.intent-handle-group {
  position: relative;
  margin-right: -3px;
  &::after {
    position: absolute;
    content: ' ';
    width: 1px;
    height: 100%;
    top: 0;
    right: -1px;
    background-color: $grey3;
  }
}
.intent-zhname {
  margin-right: 7px;
  cursor: pointer;
  font-weight: 600;
}
.intent-tag {
  vertical-align: baseline;
}
.intent-name {
  cursor: pointer;
}
.btn-fuzzy {
  vertical-align: bottom;
  width: 36px;
  height: 37px;
  border-radius: 2px;
  background-color: $grey4-15;
  text-align: center;
  line-height: 37px;
  font-size: 16px;
  cursor: pointer;
  border-left: 1px solid $grey2;
}
.no-pointer {
  cursor: default;
}
.sub-account.ic-r-edit {
  font-size: 20px;
  cursor: pointer;
}
</style>
<style lang="scss">
.intentions-table {
  .tooltip-icon {
    vertical-align: -1px;
    margin-left: 2px;
    font-size: 16px;
    color: $grey4;
    cursor: pointer;
  }
  tr:hover {
    .intent-zhname,
    .intent-name {
      color: $primary;
    }
  }
  .el-table .ic-r-edit {
    color: $primary;
  }
}
</style>
