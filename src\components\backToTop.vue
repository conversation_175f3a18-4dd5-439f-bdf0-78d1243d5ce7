<template>
  <div class="feed-back">
    <ul>
      <li :class="['arrow']" @click="backTop" v-if="showBackTop">
        <div :class="['arrow-inner']">
          <!-- <i class="icon-arrow"></i> -->
          <img v-lazy="require('../assets/images/aiui/backup.png')" alt="" />
        </div>
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  name: 'backTop',
  props: {
    showBackTop: {
      type: Boolean,
      default: false,
    },
  },
  methods: {
    backTop() {
      this.$emit('backtop')
    },
  },
}
</script>

<style scoped lang="scss">
ul {
  margin-bottom: 0;
}

.feed-back {
  position: fixed;
  right: 30px;
  top: calc(60% + 80px);
  z-index: 1000;

  > ul {
    display: flex;
    flex-direction: column;

    > li {
      cursor: pointer;
      display: inline-block;
      width: 70px;
      height: 70px;
      // box-shadow: 0px 5px 20px 0px rgba(31, 144, 254, 0.3);
      box-shadow: 0px 5px 20px 0px rgba(165, 165, 165, 0.3);
      border-radius: 10px;

      &.arrow {
        background: #ffffff;
        border-radius: 1px;
        box-shadow: 0px 4px 16px 0px rgba(0, 0, 0, 0.2);
        position: relative;

        .arrow-inner {
          position: relative;
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;

          img {
            width: 26px;
          }

          &.down {
            transform: rotate(180deg);
            transform-origin: center;
          }
        }

        .icon-arrow {
          width: 25px;
          height: 18px;
          display: inline-block;
          position: absolute;
          z-index: 20;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          background: url(~@A/images/aiui/main-page/icon-image.png) no-repeat;
          background-position: -81px 0;
        }
      }
    }

    li + li {
      margin-top: 10px;
    }
  }
}

@media screen and (max-width: 1601px) {
  .feed-back {
    top: calc(60% + 70px);
    right: 20px;

    > ul {
      > li {
        width: 60px;
        height: 60px;
      }
    }
  }
}
</style>
