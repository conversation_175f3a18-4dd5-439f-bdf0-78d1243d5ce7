<template>
  <el-dialog
    :visible.sync="isShow"
    :title="title"
    :close-on-click-modal="false"
    :modal-append-to-body="false"
    class="intent_lib_dialog"
    width="1000px"
    @close="cancel"
  >
    <el-input
      placeholder="搜索意图"
      size="medium"
      v-model="searchVal"
      @keyup.enter.native="getOfficialIntentList(1)"
      :maxlength="250"
    >
      <i
        slot="prefix"
        class="el-input__icon el-icon-search search-area-btn"
        @click="getOfficialIntentList(1)"
      />
    </el-input>
    <el-table
      v-loading="tableData.loading"
      ref="singleTable"
      :data="tableData.list"
      style="width: 100%"
      max-height="400"
    >
      <el-table-column type="index" width="50">
        <template slot-scope="scope">
          {{ (tableData.page - 1) * tableData.size + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column
        show-overflow-tooltip
        property="intentName"
        label="意图名称"
        width="120"
      >
      </el-table-column>
      <el-table-column
        show-overflow-tooltip
        property="intentNameEn"
        label="英文标识"
        width="120"
      >
      </el-table-column>
      <el-table-column
        property="intentDesc"
        show-overflow-tooltip
        label="意图概述"
      >
      </el-table-column>
      <el-table-column label="引用" width="100">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.quote"
            @change="(val) => changeSwitch(scope.row, val)"
          >
          </el-switch>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      :current-page="tableData.page"
      :page-size="tableData.size"
      :total="tableData.total"
      :layout="pageLayout"
      @current-change="pageChange"
      class="txt-al-c"
    ></el-pagination>
    <span slot="footer" class="dialog-footer">
      <!-- <el-button @click="cancel">取消</el-button>
      <el-button
        class="dialog-btn"
        type="primary"
        style="min-width: 104px"
        @click="handleConfirm"
      >
        确认
      </el-button> -->
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: 'IflyAIuiWebIndentLibDialog',

  data() {
    return {
      isShow: false,
      title: '引用官方意图库',
      searchVal: '',
      tableData: {
        loading: true,
        total: 12,
        page: 1,
        size: 10,
        list: [
          {
            indentName: '查询相关信息查询相关信息查询相关信息',
            indentSign: 'ask',
            indentDesc:
              '查询相关信息的意图用于用户查询内容资源（音乐/视频）等场景的相关信息。',
            isQuote: true,
          },
          {
            indentName: '查询相关信息',
            indentSign: 'ask',
            indentDesc:
              '查询相关信息的意图用于用户查询内容资源（音乐/视频）等场景的相关信息。已经为你整理好了用户表达确认的多样化表达，可直接饮用询问相关的信息意图',
            isQuote: true,
          },
        ],
      },
    }
  },

  computed: {
    pageLayout() {
      return this.tableData.total > 10
        ? 'prev, pager, next, jumper, total'
        : 'prev, pager, next'
    },
  },
  methods: {
    show() {
      this.tableData.page = 1
      this.getOfficialIntentList()
      this.isShow = true
    },
    handleConfirm() {
      this.isShow = false
      this.searchVal = ''
    },
    pageChange(e) {
      this.tableData.page = e
      this.getOfficialIntentList()
    },

    getOfficialIntentList(page) {
      this.tableData.loading = true
      const params = {
        pageIndex: page || this.tableData.page,
        pageSize: this.tableData.size,
        searchKey: this.searchVal,
        pluginId: this.$route.params.agentId,
      }
      this.$utils.httpPost(
        this.$config.api.AGENT_OFFFICAL_INTENT_LIST,
        JSON.stringify(params),
        {
          config: {
            headers: {
              'Content-Type': 'application/json;charset=UTF-8',
            },
          },
          success: (res) => {
            if (res.code == '0') {
              this.tableData.list = res.data.data
              this.tableData.total = res.data.totalSize
              this.tableData.page = res.data.pageIndex
              this.tableData.size = res.data.pageSize
              this.tableData.loading = false
            }
          },
          error: (err) => {
            this.tableData.loading = false
            this.$message.error(err.desc)
          },
        }
      )
    },
    changeSwitch(data, flag) {
      this.tableData.loading = true
      const params = {
        pluginId: this.$route.params.agentId,
        intentId: data.intentId,
        intentVersion: data.version,
        quoteFlag: flag,
      }
      this.$utils.httpPost(
        this.$config.api.AGENT_OFFICAL_INTENT_QUOTE,
        JSON.stringify(params),
        {
          config: {
            headers: {
              'Content-Type': 'application/json;charset=UTF-8',
            },
          },
          success: (res) => {
            if (res.code == '0') {
              this.$message.success('操作成功')
              this.$emit('refresh')
              this.getOfficialIntentList()
            }
          },
          error: (err) => {
            this.tableData.loading = false
            this.$message.error(err.desc)
          },
        }
      )
    },
    cancel() {
      this.isShow = false
      this.searchVal = ''
    },
  },
}
</script>

<style lang="scss">
.intent_lib_dialog {
  .el-dialog__body {
    .el-input {
      margin-bottom: 18px;
    }
    .el-switch.is-checked .el-switch__core {
      background-color: $primary;
    }
    .el-switch__core {
      height: 20px;
      &::after {
        width: 15px;
        height: 15px;
        top: 2.5px;
        background-color: #fff;
        border-color: #fff;
      }
    }

    .el-table {
      // 默认所有单元格字体大小
      td {
        .cell {
          font-size: 13px;
        }
      }

      // 意图名称列
      .el-table__row td:nth-child(2) {
        .cell {
          color: $primary;
          cursor: pointer;
          &:hover {
            color: $hover;
          }
        }
      }

      // 英文标识列
      .el-table__row td:nth-child(3) {
        .cell {
          color: #42464e;
        }
      }

      // 意图概述列
      .el-table__row td:nth-child(4) {
        .cell {
          font-size: 12px;
          color: #696871;
          line-height: 21px;
        }
      }
    }
    .el-pagination {
      margin: 20px 0 10px 0;
    }
  }
}
</style>
