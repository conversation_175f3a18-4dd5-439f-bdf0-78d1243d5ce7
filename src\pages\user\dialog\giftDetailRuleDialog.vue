<template>
  <el-dialog
    :visible.sync="dialog.show"
    width="52.08%"
    border-radius="20px"
    :before-close="handleClose"
    class="gc-d-dialog"
  >
    <template slot="title">
      <div class="first-title">
        <div class="first-title-inner"><h2>详细规则</h2></div>
      </div>
    </template>
    <div class="gc-d-main">
      <h3 class="second-tite">参与条件</h3>
      <p class="third-title">
        （1）2020年12月9日起新注册的用户，且注册3个月内；
      </p>
      <p class="third-title">（2）通过AIUI开放平台企业或个人实名认证；</p>
      <h3 class="second-tite">开通免费产品说明</h3>
      <p class="third-title">
        （1）符合参与条件的用户，在领取免费交互量礼包后不允许更改，领取成功后剩余体验量相应增加；
      </p>
      <p class="third-title">
        （2）每个认证账户只允许领取一次，以用户当前认证身份判定，请在领取前进行认证身份升级；
      </p>
      <h3 class="second-tite">参与用户说明</h3>
      <p class="third-title">
        （1）符合参与条件的同一用户仅能开通一次免费产品。同一用户举例：同一手机号、同一设备等；
      </p>
      <p class="third-title">
        （2）同一用户，不同应用下，仅能开通一次免费产品，请仔细选择想要免费试用产品的应用；
      </p>
      <h3 class="second-tite">其他说明</h3>
      <p class="third-title">
        （1）免费活动期间，用户获取免费试用的规则、可能随AIUI开放平台的业务需求而随时调整，不同时期的用户可能会得到不同的
        试用量，根据领取时平台展示为准；
      </p>
      <p class="third-title">
        （2）为保证活动的公平公正，AIUI开放平台有权对恶意刷抢（如创建不同账号，在一个应用中使用该服务）活动资源，利用资源
        从事违法违规行为的用户收回免费使用资格；
      </p>
      <p class="third-title">
        （3）AIUI开放平台有权根据自身运营安排，自主决定和调整本活动的具体规则，具体活动规则以活动页公布规则为准。相关规则
        一经公布即产生效力；
      </p>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'giftDetailRuleDialog',
  props: {
    dialog: {
      type: Object,
      default: {},
    },
  },
  data() {
    return {}
  },
  methods: {
    handleClose(done) {
      done()
    },
  },
}
</script>

<style>
.el-dialog__body {
  padding-top: 0 !important;
}
</style>
<style scoped lang="scss">
.gc-d-dialog {
  .first-title {
    text-align: center;
    &-inner {
      border-bottom: 1px solid #e4e7ed;
      width: 100%;
      margin: 0 auto;
    }
  }
}
.gc-d-main {
  color: #333333;
  margin-bottom: 3.13% !important;
  display: inline-block;
  .second-tite {
    margin: 0 auto;
    margin-top: 1%;
  }
  .third-title {
    line-height: 18px;
    margin: 0 auto;
    margin-top: 1%;
  }
}
</style>
