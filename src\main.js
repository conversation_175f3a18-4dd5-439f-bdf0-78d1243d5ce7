import Vue from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'
import VueMeta from 'vue-meta'

import { osELement } from 'os-element/lib/index.com.min'
import 'os-element/theme/index.css'
import '@A/scss/style_overwrite.scss'
import 'wow.js/css/libs/animate.css'
import './style/common.scss'
import 'slick-carousel/slick/slick.css'
import '@/assets/font/iconfont/iconfont.css'
import '@A/scss/gutterTable.scss'

Vue.use(VueMeta)
Vue.use(osELement)

// 引入
import OsTable from '@C/osTable'
Vue.component(OsTable.name, OsTable)

import BackIcon from '@C/backIcon'
Vue.component('back-icon', BackIcon)

import Config from './config'
import Utils from './utils'
import Rules from '@M/rules'
Vue.use(Config)
Vue.use(Utils)
Vue.use(Rules)

import OsComponents from '@C'
Vue.use(OsComponents)
import '@A/scss/basic.scss'

import Filter from '@M/filter.js'
Filter.init(Vue)

import mobileAdapt from './mobileAdapt'
mobileAdapt()

import '@M/directives.js'

import DeepClone from './utils/deepClone.js'
Vue.prototype.$deepClone = DeepClone

Vue.config.productionTip = false

import 'swiper/swiper-bundle.css'

import PortalVue from 'portal-vue'
Vue.use(PortalVue)

import VueLazyload from 'vue-lazyload'
Vue.use(VueLazyload, {
  listenEvents: ['scroll', 'wheel', 'mousewheel', 'resize'],
})

import AiuiButton from '@C/aiuiButton'
Vue.component(AiuiButton.name, AiuiButton)

import AiuiTextAdder from '@C/aiuiTextAdder'
Vue.component(AiuiTextAdder.name, AiuiTextAdder)

import { InfiniteScroll } from 'element-ui'
Vue.use(InfiniteScroll)

import ElementUI, { Message, Upload } from 'element-ui'
import TokenUpload from '@C/tokenUpload'
import SvgIcon from '@C/svgIcon.vue'

/**
 *  重写ElementUI的Message
 *  single默认值true，因为项目需求，默认只弹出一个，可以根据实际需要设置
 */

let messageInstance = null
function DonMessage(options) {
  return Message.call(this, options)
}
DonMessage.prototype = Object.create(Message.prototype)
DonMessage.prototype.constructor = DonMessage
;['success', 'warning', 'info', 'error'].forEach((type) => {
  DonMessage[type] = (options, single = true) => {
    if (!options) {
      return
    }
    if (typeof options === 'string') {
      options = {
        message: options,
      }
    }
    options.type = type
    if (messageInstance && single) {
      messageInstance.close() //先把原来的关闭
    }
    messageInstance = DonMessage(options)
    return messageInstance
  }
})
DonMessage.close = () => {
  if (messageInstance) {
    messageInstance.close()
  }
}

Vue.use(ElementUI)

Vue.component(Upload.name, TokenUpload)

// 重新el-upload ,使之底层统一处理headers csrf token

Vue.prototype.$message = DonMessage

// 重写toast success
Vue.prototype.$message_pro_success = (title, desc) => {
  DonMessage({
    customClass: 'toast-pro-base toast-pro-success',
    dangerouslyUseHTMLString: true,
    message: `
      <div class="toast-container">
        <div class="title">${title}</div>
        <div class="desc">${desc}</div>
      </div>
      `,
  })
}

// 使用于应用中的保存修改
Vue.prototype.$message_warning_success = (desc) => {
  DonMessage({
    customClass: 'toast-warning-base toast-warning-success',
    dangerouslyUseHTMLString: true,
    message: `
      <div class="toast-container">
        <div class="desc">${desc}</div>
      </div>
      `,
  })
}

Vue.directive('focus', {
  inserted(el, binding, vnode) {
    // 聚焦元素
    el.focus()
  },
})

Vue.directive('focus', {
  inserted(el, binding, vnode) {
    // 聚焦元素
    el.querySelector('input').focus()
  },
})

Vue.component(SvgIcon.name, SvgIcon)
const req = require.context('./assets/svgs', false, /\.svg$/)
const requireAll = (requireContext) => requireContext.keys().map(requireContext)
requireAll(req)

import ConfirmProWarning from '@/utils/confirmProWarning'
Vue.use(ConfirmProWarning)

new Vue({
  router,
  store,
  metaInfo() {
    return {
      title: this.$store.state.metaModule.metaInfo.title,
      meta: [
        {
          name: 'keywords',
          vmid: 'keywords',
          content: this.$store.state.metaModule.metaInfo.keywords,
        },
        {
          name: 'description',
          vmid: 'description',
          content: this.$store.state.metaModule.metaInfo.description,
        },
      ],
    }
  },

  mounted() {
    this.$utils.init(this)
  },
  render: (h) => h(App),
}).$mount('#app')
