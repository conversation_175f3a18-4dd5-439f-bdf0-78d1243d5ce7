<template>
  <el-dialog
    class="add-category-dialog"
    title="编辑专辑内容"
    :visible.sync="dialog.show"
    :close-on-click-modal="false"
    width="870px"
  >
    <div class="div-album-content">
      <ul>
        <li class="div-album-content-self">
          <p>专辑名称:</p>
        </li>
        <li class="div-album-content-self-2"><p>{{`${dialog.album ? dialog.album.albumName : null}`}}</p></li>
      </ul>
      <ul>
        <li>
          <el-select placeholder="展示内容名称" v-model="condition.selectedFirst"  @change="doChangeSelectFirst">
            <el-option
              v-for="(item, index) in businessData"
              :key="index"
              :value="item.enName"
              :label="item.zhName"
            />
          </el-select>
        </li>
        <li>
          <el-select placeholder="按内容分类添加" v-model="condition.selectedSecond"  >
            <el-option
              v-for="(item, index) in secondData"
              :key="index"
              :value="item.idStr"
              :label="item.zhName"
              @click.native="doClickSelectSecond(item)"
            />

          </el-select>
        </li>
        <li>
          <el-input placeholder="输入关键字搜索" v-model="keyWord" @input="doInput"/>
        </li>
        <li>
          <el-button size="medium" type="primary" @click="doSearchReset" :loading="searchLoading">
            搜 索
          </el-button>
        </li>
      </ul>
      <ul>
        <li class="tips-left-album"><p>专辑只可以选择一个，专辑添加后内容支持拖动箭头排序</p></li>
        <li><p>已绑定的内容</p></li>
      </ul>
      <ul>
        <li class="os-scroll" :class="{'li-scroll-disabled': busy}">
          <div class="origin origin-left scrollLeft li-scroll" :class="{'li-scroll-disabled': busy}" ref="scrollLeft" @scroll="bindScroll">
            <ul v-if="condition.enName === 'query' && searchRestultData !== undefined && searchRestultData.length > 0" v-for="(item, index) in searchRestultData" :key="index" @click="doBind(item)">
              <li @click.stop>
                <el-checkbox title="选择该曲目" v-model="item.selected" @change="checkedChange($event,item)"></el-checkbox>
              </li>
              <li :title="item.songname">
                <p>{{item.songname}}</p>
              </li>
            </ul>
            <ul v-if="condition.enName === 'album' && searchRestultData !== undefined && searchRestultData.length > 0" v-for="(item, index) in searchRestultData" :key="index" @click="doBind(item)">
                <li @click.stop>
                  <el-radio class="radio-album" title="选择该专辑" v-model="radioSelected" :label="item._id" @change="checkedChangeRadio($event, item)">
                    <ul :title="item.album" class="radio-album-ul">
                      <li>
                        <img v-if="item.img" :src="item.img" style="margin-bottom: .5em; max-width: 80%"/>
                        <p>{{item.album}}</p>
                      </li>
                    </ul>
                  </el-radio>
                </li>
              </ul>
            <ul v-if="searchRestultData !== undefined && searchRestultData.length === 0">
              <li style="margin: 0 auto;">
                <span>暂无内容</span>
              </li>
            </ul>
          </div>
        </li>
        <li>
          <div class="origin origin-right li-scroll" ref="scrollRight" @scroll="getBusinessDetailScroll">
            <draggable class="list-wrap" v-model="selectedSearchRestultData" filter=".forbid" animation="300" :move="onMove">
              <!--<transition-group type="transition">-->
                <ul class="ul-origin-right-album" :class="condition.enName === 'album' && index_i === 0 ? 'forbid':'item'" v-for="(item, index_i) in selectedSearchRestultData" :key="index_i">
                  <div v-if="condition.enName === 'album' && JSON.stringify(radioSelected) !== '{}' && index_i === 0">
                    <li>
                    </li>
                    <li :title="item.album">
                      <img v-if="item.img" :src="item.img" style="margin-bottom: .5em; max-width: 80%"/>
                      <p>{{item.album}}</p>
                    </li>
                    <li title="删除">
                      <i class="entity-page-form-cancel el-icon-close" data-action="del" @click.stop="removeBind($event, item, index_i)"></i>
                    </li>
                  </div>
                  <div v-else-if="condition.enName === 'album' && JSON.stringify(radioSelected) !== '{}' && index_i !== 0">
                    <li title="上下拖动排序">
                      <i class="ic-r-select"></i>
                    </li>
                    <li :title="item.songname">
                      <p>{{item.songname}}</p>
                    </li>
                    <li title="删除">
                      <i class="entity-page-form-cancel el-icon-close" data-action="del" @mousedown.left="removeBind($event, item, index_i)"></i>
                    </li>
                  </div>
                  <div v-else>
                    <li>
                      <i class="ic-r-select"></i>
                    </li>
                    <li :title="item.songname">
                      <p>{{item.songname}}</p>
                    </li>
                    <li title="删除">
                      <i class="entity-page-form-cancel el-icon-close" data-action="del" @mousedown.left="removeBind($event, item, index_i)"></i>
                    </li>
                  </div>
                </ul>
              <!--</transition-group>-->
            </draggable>
          </div>
        </li>
      </ul>
      <ul>
        <li>
          <el-button @click="doCancel" size="medium">取 消</el-button>
        </li>
        <li>
          <el-button type="primary" size="medium" @click="finish" :loading="loading">确认</el-button>
        </li>
      </ul>
    </div>
  </el-dialog>
</template>

<script>
  // v-infinite-scroll="loadMore" infinite-scroll-disabled="busy" infinite-scroll-sdistance="5" immediate-check="false" infinite-scroll-immediate-check="false"
  import draggable from 'vuedraggable'
  export default {
        name: "albumContent",
      props: {
        dialog: {
          type: Object,
          default: {
            show: false
          }
        }
      },
    components: {
      draggable
    },
      data () {
        return {
          searchLoading: false,
          businessData: [],
          secondData: [],
          condition: {
            selectedFirst: null,
            selectedSecond: null,
            enName: null,
            enName_: null
          },
          keyWord: null,
          searchRestultData: [],
          selectedSearchRestultData: [],
          songData: [],
          pageIndex: 1,
          pageIndexRight: 1,
          pageIndexSong: 1,
          pageSize: 10,
          busy: false,
          busyRight: false,
          busySong: false,
          loading: false,
          radioSelected: {}
        }
      },
      watch: {
        'dialog.show': function () {
          if (this.dialog.show === true) {
            this.getBusinessFunc()
            this.getSongById()
          }
          this.keyWord = null
          this.condition = {
                              selectedFirst: null,
                              selectedSecond: null,
                              enName: null
                          }
          this.pageIndex = 1,
          this.pageIndexRight = 1,
          this.pageIndexSong = 1,
          this.radioSelected = {}
          this.songData = []
          this.secondData = []
          this.searchRestultData = []
          this.selectedSearchRestultData = []
          this.searchLoading = false
          this.loading = false
         }
      },
      computed: {
        appId() {
          return this.$route.params.appId
        }
      },
      updated() {
        //console.log(JSON.stringify(this.selectedSearchRestultData))
      },
      created() {
      },
      mounted () {
        //window.addEventListener('scroll', this.bindScroll);
      },
    methods: {
      doInput (e) {
        this.$forceUpdate()
      },
      onMove (e) {
        if(this.condition.enName === 'album' && e.relatedContext.index === 0) return false;
        return true;
      },
      getBusinessDetailScroll () {
        if (!this.dialog.show) return
        if(this.$refs['scrollRight'].offsetHeight + this.$refs['scrollRight'].scrollTop >= this.$refs['scrollRight'].scrollHeight - 3){
          if (this.condition.enName === 'album' && JSON.stringify(this.radioSelected) !== '{}') {
            setTimeout(
              () => {
                this.pageIndexRight ++
                if (!this.busyRight) {
                  this.getBusinessDetail()
                }
              },500);
          } else {
            setTimeout(
              () => {
                this.pageIndexSong ++
                if (!this.busySong) {
                  this.getSongById()
                }
              },500);
          }
        }
      },
      getBusinessDetail () {
        let thiz = this
        this.$utils.httpGet(this.$config.api.RECOMMEND_CONTENT_BUSINESS_DETAIL, {
          category: this.condition.selectedFirst,
          album: this.selectedSearchRestultData[0].album,
          author: this.selectedSearchRestultData[0].author,
          type: this.selectedSearchRestultData[0].type, // -1专辑，-2单曲 1一级分类，2二级分类 参考2.4
          page: this.pageIndexRight,
          size: this.pageSize
        }, {
          success: (res) => {
            if (res.flag) {
              thiz.selectedSearchRestultData = thiz.selectedSearchRestultData.concat(res.data.list)
              let listTotal = res.data.total
              if (listTotal > 0 && thiz.selectedSearchRestultData.length -- < listTotal) {
                thiz.busyRight = false
              } else {
                thiz.busyRight = true
              }
            } else {
              this.$message.error(res.desc)
              thiz.busyRight = false
              thiz.pageIndexRight --
            }
          },
          error: (err) => {
          }
        })
      },
        removeBind (e, item, index) {
          if (this.condition.enName === 'album' && index !== undefined && index === 0) {
            this.selectedSearchRestultData = []
            this.radioSelected = {}
          } else {
            //this.selectedSearchRestultData.splice(this.selectedSearchRestultData.findIndex(data => data._id === item._id), 1)
            this.selectedSearchRestultData.splice(index, 1)
            if (this.condition.enName === 'album' && this.selectedSearchRestultData.length === 1) this.selectedSearchRestultData = []
            this.searchRestultData.forEach((data, index) => {
              if (data._id === item._id) {
                data.selected = false
              }
            })
          }
        },
        doCancel () {
          this.dialog.show = false
        },
         finish () {
            if (this.selectedSearchRestultData.length == 0) {
              this.$message.error("请选择添加内容")
              return
            }
            this.loading = true
           let thiz = this

           new Promise((resolve, reject) => {
              thiz.getSongById(-1, resolve, reject)
            }).then((result) => {
              if (!result) return
             let tmpData = thiz.condition.enName === 'album' ? this.selectedSearchRestultData.filter((item,index) => {return index !== 0}) : this.selectedSearchRestultData
             let detailBody = Array.prototype.map.call(tmpData.concat(result.slice(thiz.songData.length)), function (item, index) {
               let tmp = {
                 resourceId: item.resourceId,
                 resourceType: item.type,
                 detailName: item.songname,
                 cover: item.cover,
                 author: item.author,
                 url: item.url,
                 source: item.source,
                 orderNum: index
               }
               return tmp;
             });
             this.$utils.httpPost(this.$config.api.RECOMMEND_CONTENT_ADD_DETAILS,JSON.stringify({
               type: this.dialog.type === -1 ? 1 : 2, // 绑定类型，1专辑，2非专辑（即分类直接挂单曲）
               albumId: this.dialog.album.id,
               detailBody: detailBody
             }), {
               config: {
                 headers: {
                   "Content-Type": "application/json;charset=UTF-8",
                 },
               },
               success: (res) => {
                 if(res.flag) {
                   this.$message.success(res.desc)
                   this.dialog.show = false
                 } else {
                   this.$message.error(res.desc)
                 }
                 this.loading = false
               },
               error: (err) => {
               }
             })
           })
        },
        checkedChangeRight (e, item) {
          item.selected = false
          if (!item.selected) {
            this.selectedSearchRestultData.splice(this.selectedSearchRestultData.findIndex(data => data._id === item._id), 1)
            this.searchRestultData.forEach((data, index) => {
              if (data._id === item._id) {
                data.selected = false
              }
            })
          }
        },
        releaseBind (item) {
          if (item.selected) {
            this.selectedSearchRestultData.splice(this.selectedSearchRestultData.findIndex(data => data._id === item._id), 1)
            this.searchRestultData.forEach((data, index) => {
              if (data._id === item._id) {
                data.selected = false
              }
            })
          }
        },
        checkedChangeRadio (e, item) {
          this.selectedSearchRestultData = []
          this.selectedSearchRestultData.push(item)
          let [ ...tmp ] = this.songData //深拷贝新数组
          this.selectedSearchRestultData = this.selectedSearchRestultData.concat(tmp.splice(0, 100))
          this.pageIndexRight = 1
          this.getBusinessDetail()
        },
        checkedChange (e, item) {
          item.selected = e
          if (e) {
            this.selectedSearchRestultData.push(item)
          } else {
            this.selectedSearchRestultData.splice(this.selectedSearchRestultData.findIndex(data => data._id === item._id), 1)
          }

        },
        doBind (item) {
          if (item.selected) {
            item.selected = !item.selected
          } else {
            item.selected = true
          }
          if (item.selected) {
            this.selectedSearchRestultData.push(item)
          } else {
            this.selectedSearchRestultData.splice(this.selectedSearchRestultData.findIndex(data => data._id === item._id), 1)
          }
        },
        bindScroll () {
          if (!this.dialog.show) return
          if(this.$refs['scrollLeft'].offsetHeight + this.$refs['scrollLeft'].scrollTop >= this.$refs['scrollLeft'].scrollHeight - 3){
            setTimeout(
              () => {
                this.pageIndex ++
                if (!this.busy) {
                  this.doSearch()
                }
              },500);
          }
        },
        getSongById (pageSize, resolve, reject) {
        let thiz = this
          this.$utils.httpGet(this.$config.api.RECOMMEND_CONTENT_GET_SONGS, {
            appid: this.appId,
            albumId: this.dialog.album.id,
            //type: type, // -1专辑，-2单曲 1一级分类，2二级分类 参考2.4
            page: this.pageIndexSong,
            size: pageSize || this.pageSize // 查询所有已绑定曲目
          }, {
            success: (res) => {
              if (res.flag) {
                if (resolve) {
                  resolve(res.data.list)
                  return
                }
                thiz.songData = thiz.songData.concat(res.data.list)
                thiz.selectedSearchRestultData = thiz.selectedSearchRestultData.concat(res.data.list)
                let listTotal = res.data.total
                if (listTotal > 0 && thiz.selectedSearchRestultData.length < listTotal) {
                  thiz.busySong = false
                } else {
                  thiz.busySong = true
                }
              } else {
                this.$message.error(res.desc)
                thiz.busySong = false
                thiz.pageIndexSong --
              }
            },
            error: (err) => {
            }
          })
        },
        loadMore(){
          setTimeout(() => {  //发送请求有时间间隔第一个滚动时间结束后才发送第二个请求
            // this.busy = true
            this.pageIndex ++
            this.doSearch();
          }, 1000);
        },
        doSearchReset(e) {
          this.searchLoading = false
          this.radioSelected = {}
          this.searchRestultData = []
          this.condition.enName = this.condition.enName_
          this.pageIndex = 1
          this.doSearch()
        },
        doSearch() {
          let thiz = this
          if (!this.condition.selectedSecond) {
            this.$message.error("请添加查询条件")
            return
          }
          this.$utils.httpGet(this.$config.api.RECOMMEND_CONTENT_BUSINESSSEARCH, {
            id: thiz.condition.selectedSecond,
            searchKey: thiz.keyWord,
            page: thiz.pageIndex,
            size: thiz.pageSize
          },{
            success: res => {
              this.searchLoading = false
              if (res.flag) {
                if (res.data.list) {
                  thiz.searchRestultData = thiz.searchRestultData.concat(res.data.list)
                  let listTotal = res.data.total
                  if (listTotal > 0 && thiz.searchRestultData.length < listTotal) {
                    thiz.busy = false
                  } else {
                    thiz.busy = true
                  }
                }
              } else {
                this.$message.error(res.desc)
                thiz.busy = false
                thiz.pageIndex --
              }
            },
            error: err => {
            }
          })
        },
        doClickSelectSecond(item) {
          this.condition.selectedSecond = item.idStr
          this.condition.enName_ = item.enName
        },
        doChangeSelectSecond(e) {
          this.condition.selectedSecond = e
        },
        doChangeSelectFirst (e) {
          this.condition.selectedFirst = e
          this.secondData  = this.businessData.find(item => {
            return item.enName == e
          }).children
          this.condition.selectedSecond = null
        },
          getBusinessFunc () {
            this.$utils.httpGet(this.$config.api.RECOMMEND_CONTENT_BUSINESSFUNC, {
            },{
              success: res => {
                if (res.flag) {
                  this.businessData = res.data.list
                } else {
                  this.$message.error(res.desc)
                }
              },
              error: err => {
              }
            })
          }
      }
    }
</script>

<style scoped lang="scss">
  .radio-album {
    display: inline-flex;
    width: 100%;
    height: 100%;
    position: relative;
    justify-content: center;
    align-items: center;
    align-self: center;
    &-ul {
      height: 100%;
      width: 100%;
      display: inline-flex;
      > li {
        margin: 0 auto;
        > p {
          text-align: center;
          margin-bottom: .5em;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          line-height: normal;
        }
      }
    }
  }

  .li-scroll {
    overflow-y: auto !important;
    border: 1px solid black;
    height: 3em;
    width: 10em;
    line-height: 1em;
  }

  .li-scroll::-webkit-scrollbar {
    -webkit-appearance: none;
  }

  .li-scroll::-webkit-scrollbar:vertical {
    width: 11px;
  }

  .li-scroll::-webkit-scrollbar:horizontal {
    height: 11px;
  }

  .li-scroll::-webkit-scrollbar-thumb {
    border-radius: 8px;
    border: 2px solid white; /* should match background, can't be transparent */
    background-color: rgba(0, 0, 0, .5);
  }
  .div-album-content {
    position: relative;
    padding-bottom: 2% !important;
    &-self {
      flex: unset !important;
      &-2 {
        left: 2%;
        position: relative;
      }
    }
    > ul {
      display: flex;
      > li {
        flex: 1;
        display: inline-block;
      > p {
          display: unset;
        }
      }
    }
    > ul:nth-of-type(1) > li {
      font-size: medium;
    }
    > ul:nth-of-type(2) > li {
      padding-right: 2%;
      align-self: center;
    }
    > ul:nth-of-type(5) {
      text-align: right;
      display: block;
      position: relative;
     > li {
       margin-right: 3%;
      }
    }

    .tips-left-album {
      color: $warning
    }

    .li-scroll-disabled {
      overflow: hidden !important;
    }

    .origin {
      width: 96% !important;
      max-height: 230px;
      min-height: 230px;
      /*height: 100%;*/
      border: 1px solid $grey3;
      overflow-x: hidden;
      overflow-y: auto !important;
      /*&-left {
        overflow-y: auto !important;
        height: 100%;
      }*/
      .list-wrap {
        height: 100%;
      }
      > ul, .ul-origin-right {
        position: relative;
        width: 100%;
        display: inline-flex;
        border-bottom: 1px solid $grey3;
        padding-top: 1%;
        padding-bottom: 1%;
        /*height: 15%;*/
        &:hover {
          background-color: $bg-color;
          color: $warning;
        }
        > li:nth-child(1) {
          margin-left: 5%;
        }
        > li:nth-child(2) {
          margin-left: 1%;
        }
        > li:nth-child(3) {
          position: relative;
          right: 3%;
          flex: 1;
          text-align: right;
        }
        > li {
          position: relative;
          justify-content: center;
          align-items: center;
          align-self: center;
          align-content: center;
          text-align: center;
          list-style:none;
          flex-direction:row;
          vertical-align: middle;
          overflow: hidden;
          > p {
            text-align: center;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            align-items: center;
            align-self: center;
            align-content: center;
            text-align: center;
            vertical-align: middle;
            position: relative;
            top: 12%;
            margin-bottom: auto !important;
            line-height: normal;
          }
        }
      }
      .item:hover{
        cursor: move;
      }
      .ul-origin-right-album > div {
        position: relative;
        width: 100%;
        display: inline-flex;
        border-bottom: 1px solid $grey3;
        padding-top: 1%;
        padding-bottom: 1%;
        /*height: 15%;*/
        &:hover {
          background-color: $bg-color;
          color: $warning;
        }
        > li:nth-child(1) {
          margin-left: 5%;
        }
        > li:nth-child(2) {
          margin-left: 1%;
        }
        > li:nth-child(3) {
          position: absolute;
          right: 3%;
          text-align: center;
        }
        > li {
          position: relative;
          justify-content: center;
          align-items: center;
          align-self: center;
          align-content: center;
          text-align: center;
          list-style:none;
          flex-direction:row;
          vertical-align: middle;
          overflow: hidden;
          > p {
            text-align: center;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            align-items: center;
            align-self: center;
            align-content: center;
            text-align: center;
            vertical-align: middle;
            position: relative;
            top: 12%;
            margin-bottom: auto !important;
            line-height: normal;
          }
        }
      }
    }
  }
</style>
