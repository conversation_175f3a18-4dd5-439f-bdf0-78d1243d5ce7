<template>
  <div class="corp">
    <div class="section-wrap">
      <div class="section-title">
        <p class="section-title-contact">合作咨询</p>
        <p class="section-desc2">提交信息，我们会尽快与您联系</p>
      </div>

      <div class="section-item" style="padding-top: 39px; text-align: left">
        <div class="section-button" @click="toConsole">申请合作</div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  methods: {
    toConsole() {
      this.$emit('jump')
    },
  },
}
</script>
<style lang="scss" scoped>
.corp {
  // padding: 80px 0 100px 0;
  padding-top: 76px;
  height: 320px;
  background: url(~@A/images/solution/acoustics/corp.png) center/cover no-repeat;
}
.section-wrap {
  width: 1200px;
  margin: 0 auto;
  .section-title-bold {
    text-align: left;
  }
}

.section-desc2 {
  text-align: left;
  margin-top: 20px;
  font-size: 18px;
  line-height: 30px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #262626;
  display: block;
}
.section-title-bold {
  font-size: 34px;
  font-family: Microsoft YaHei;
  font-weight: bold;
  color: #333333;
  text-align: center;
}

.section-button {
  font-size: 16px;
  text-align: center;
  line-height: 50px;
  border: none;
  color: #fff;
  cursor: pointer;

  width: 180px;
  height: 50px;
  background: $primary;
  border-radius: 4px;
}

.section-title-contact {
  font-size: 36px;
  font-weight: 400;
  color: #000000;
}
</style>
