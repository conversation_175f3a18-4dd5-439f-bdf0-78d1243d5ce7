<template>
  <div class="modifier-utter-wrap">
    <div class="mgb24" @keyup="angleBracketHandle" @keyup.enter="addUtteranceBlur">
      <el-input
        class="utterance-add-area"
        ref="addUtter"
        placeholder="回车添加修饰语内容，例如：{play}(的){type}"
        @blur="addUtterance"
        @keyup.native="angleBracketHandle"
        v-model="utteranceAddName">
        <i slot="prefix" class="el-input__icon ic-r-plus"></i>
        <el-button slot="suffix" type="text"
          class="utterance-add-area-addbtn"
          size="small">
          添加
        </el-button>
      </el-input>
    </div>
    <div v-loading="tableData.loading">
      <div v-for="(utterance, index) in oldUtterances"
        class="utterance-collapse-item"
        :key="index">
        <div class="utterance-collapse-item-title">
          <utter-editor 
            :defaultUtterance="utterance"
            :businessId="businessId"
            :modifierId="dialog.modifierId"
            :defaultIndex="index" @del="delReply" @change="refreshData"
            @reloadModifier="reloadModifier"/>
      </div>
    </div>
    </div>
    <os-pagination class="pagination" v-model="tableData.page" :total="tableData.total" :size="tableData.size" 
      @change="getUtterances"/>
    <os-modifier-select ref="modifierSelectPopover" :variablePopover="modifierSelectPopover"
      inAddInput="true"
      @setModifier="setModifier" />
  </div>
</template>

<script>
import UtterEditor from './utterEditor.vue'
  export default {
    name: 'modifier-utter',
    props: {
      businessId: String,
      dialog: {
        type: Object,
        default: () => ({ 
          show: false,
          modifierId: ''
        })
      }
    },
    data() {
      return {
        cursorPos: -1,
        utteranceAddName: '',
        oldUtterances: [],
        tableData: {
          loading: false,
          total: 0,
          page: 1,
          size: 5,
          defaultSize: 10,
          blackSize: 40, // 黑名单词条一页词条数
          handles: ['del'],
          handleColumnText: '',
          list: []
        },
        modifierSelectPopover: {
          show: false,
          rect: null,
          type: 'usedInModifier'
        }
      }
    },
    computed: {
      utteranceColor () {
        return this.$store.state.studioSkill.utteranceColor
      },
      fragmentCount(){
        return (this.$store.state.aiuiApp.limitCount && this.$store.state.aiuiApp.limitCount.skill_modifier_fragment_count) || 20
      },
      bracketsCount(){
        return (this.$store.state.aiuiApp.limitCount && this.$store.state.aiuiApp.limitCount.skill_modifier_brackets_count) || 3
      }
    },
    created(){
      this.init()
    },
    methods: {
      init(){
        this.utteranceAddName = '',
        this.getUtterances()
      },
      refreshData(page){
        this.getUtterances(page)
        this.$emit('reloadSlots')
      },
      // 获取语料列表
      getUtterances (page) {
        if(!this.dialog.modifierId) return
        let self = this
        this.tableData.loading = true
        this.$utils.httpGet(this.$config.api.STUDIO_MODIFIER_FRAGMENT, {
          businessId: this.businessId,
          modifierId: this.dialog.modifierId,
          pageIndex: page || this.tableData.page || 1,
          pageSize: this.tableData.size,
          search: '',
        }, {
          success: (res) => {
            self.tableData.list = res.data.list
            self.oldUtterances = []
            self.tableData.total = res.data.count
            self.tableData.page = res.data.pageIndex
            self.tableData.size = res.data.pageSize
            self.$nextTick(function () {
              self.oldUtterances = JSON.parse(JSON.stringify(res.data.list))
            })
            self.tableData.loading = false
          },
          error: (err) => {
            self.tableData.loading = false
          }
        })
      },
      addUtteranceBlur (event) {
        event.target.blur()
      },
      addUtterance () {
        let self = this
        if(this.cursorPos > -1) return
        this.utteranceAddName = this.$utils.trimSpace(this.utteranceAddName) 
        this.utteranceAddName = this.$utils.filterUtterSpace(this.utteranceAddName)
        if (!this.utteranceAddName) {
          return
        }
        if(self.utteranceAddName && self.tableData.total >= self.fragmentCount) {
          return self.$message.warning(`一个修饰语最多仅能创建${self.fragmentCount}条内容`)
        }
        let nameValid = this.$rules.judgeUtteranceParams(this.utteranceAddName, 100, '内容', self.bracketsCount )
        if (!nameValid.valid) {
          return self.$message.warning(nameValid.data.message)
        }
        this.$utils.httpPost(this.$config.api.STUDIO_MODIFIER_FRAGMENT_ADD_OR_EDIT, {
          businessId: this.businessId,
          modifierId: this.dialog.modifierId,
          fragment: this.utteranceAddName,
          id:''
        }, {
          success: (res) => {
            self.$message.success('添加成功')
            let nowTotal = this.tableData.total + 1
            let basePage = nowTotal % this.tableData.size ? 1 : 0
            self.utteranceAddName = ''
            self.$refs.addUtter && self.$refs.addUtter.$refs.input.focus()
            this.refreshData(1)
            self.reloadModifier()
          },
          error: (err) => {
          }
        })
      },
      angleBracketHandle(e) {
        let self = this
        /*
          * 188 和 190 分别对应 <  >
          */
          let angleCode= [188, 190],
          popoverType = ''
        if(e.keyCode === 38 || e.keyCode === 40) return //键盘上下方向键
        if(!angleCode.includes(e.keyCode) && self.cursorPos === -1) return
        if(e.keyCode === 190 && self.cursorPos !== -1 ) {
          return self.modifierSelectPopover.show = false
        }
        const cursor = this.$refs.addUtter.$refs.input.selectionStart  //input 里的鼠标当前位置
        const value = e.target.value
        let inputWrap = this.$refs.addUtter
        const rect = e.target.getBoundingClientRect()
        let searchVal = ''
        if (value[cursor - 1] === '<'){
          self.cursorPos = cursor
        }
        if(self.cursorPos > cursor) {
          return self.variablePopover.show = false
        }
        self.cursorPos !== -1 && setTimeout(function() {
          self.modifierSelectPopover= {
            show: true,
            rect: rect,
            cursorPos: cursor,
            searchVal: value.substring(self.cursorPos, cursor),
            type: 'usedInModifier',
            businessId: self.businessId,
            modifierId: self.dialog.modifierId
          }
        }, 0)
      },
      delReply (utterance, index) {
        let self = this

        this.$utils.httpPost(this.$config.api.STUDIO_MODIFIER_FRAGMENT_DEL, {
          businessId: this.businessId,
          modifierId: this.oldUtterances[index].modifierId,
          fragmentId: this.oldUtterances[index].id
        }, {
          success: (res) => {
            self.$message.success('删除成功')
            let nowTotal = this.tableData.total - 1
            let basePage = nowTotal % this.tableData.size ? 1 : 0
            let page = parseInt(nowTotal / this.tableData.size) + basePage > this.tableData.page ? this.tableData.page : parseInt(nowTotal / this.tableData.size) + basePage
            self.refreshData(page)
            self.reloadModifier()
          },
          error: (err) => {
          }
        })
      },
      // 20200315-技能再定制修改 
      //获取修饰语下所有已引用修饰语
      setModifier(item){
        let self = this
        if(!item) return this.cursorPos = -1
        let searchValLen = this.modifierSelectPopover.searchVal 
          ? this.modifierSelectPopover.searchVal.length : 0
        this.utteranceAddName = this.utteranceAddName.substring(0, this.cursorPos) + item + '>'
          + this.utteranceAddName.substring(this.cursorPos + searchValLen)
        let currentCursorPos = this.cursorPos + item.length + 1
        let input = this.$refs.addUtter.$refs.input
        input.setSelectionRange(this.cursorPos, currentCursorPos)
        input.focus()
        this.cursorPos = -1
      },
      handleDown(){
        if(!this.modifierSelectPopover.show) return
        this.$refs.modifierSelectPopover.handleDown()
      },
      reloadModifier(){
        this.$emit('reloadModifier')
      }
    },
    components: {
      UtterEditor
    }
  }
</script>

<style lang="scss">
.modifier-utter-wrap {
  .utterance-collapse-item-title {
    position: relative;
  }
  .pagination {
    margin: 16px auto 28px;
    text-align: center;
  }

  .utterance-collapse-item {
    border: 1px solid $grey2;
    border-bottom: 0;
    transition: all .2s;
  }
  .utterance-collapse-item:last-child {
    border-bottom: 1px solid $grey2;
  }
  .utterance-add-area-addbtn {
    min-width: 28px;
    padding: 0 16px;
    line-height: 44px;
  }
}

</style>