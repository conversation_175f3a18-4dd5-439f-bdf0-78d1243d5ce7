<template>
  <div
    class="os-page"
    v-loading="options.loading"
    :class="{ 'os-page-screen': options.screen }"
  >
    <div v-show="options.title || options.showHead" class="os-page-head">
      <!-- <i
        v-if="options.returnBtn"
        class="el-icon-back os-page-head-back"
        @click="back"
      /> -->
      <back-icon
        v-if="options.returnBtn"
        @click="back"
        style="margin-right: 9px"
      ></back-icon>
      <!-- <div v-if="options.title" class="os-page-head-line"></div>
      <div
        v-if="options.title"
        class="os-page-head-title"
        v-html="options.title"
      ></div> -->
      <div class="os-page-head-title">
        <slot name="headLeft"></slot>
      </div>
      <div>
        <slot name="btn"></slot>
      </div>
    </div>
    <!-- <os-divider /> -->
    <div
      id="scrollDom"
      class="os-scroll"
      :style="{
        padding: '0 30px',
        height:
          options.title || options.showHead
            ? 'calc(100% - 63px)'
            : 'calc(100% - 1px)',
      }"
    >
      <div id="scrollDomDiv">
        <slot></slot>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'OsPage',
  props: {
    options: {
      type: Object,
      default: {
        title: '',
        loading: true,
      },
    },
  },
  data() {
    return {}
  },
  mounted() {},
  methods: {
    back() {
      this.$emit('returnCb')
    },
  },
}
</script>

<style lang="scss">
.os-page {
  height: 100%;

  &-screen {
    width: 80%;
    margin: 0 auto;
  }

  &-head {
    height: 63px;
    font-size: 24px;
    padding: 0 30px;
    display: flex;
    align-items: center;
    background-color: $white-grey !important;
    &-back {
      cursor: pointer;
      // color: $grey4;
      font-size: 18px;
      margin-right: 16px;
    }

    &-line {
      width: 3px;
      height: 19px;
      background: $primary;
      margin-right: 9px;
    }

    &-title {
      flex: auto;
      width: 100px;
      font-size: 16px;
      font-family: PingFang SC, PingFang SC-Medium;
      font-weight: 500;
      text-align: left;
      color: #262626;
      line-height: 56px;
    }
  }

  .os-scroll {
    background-color: $white-grey !important;
  }
}
</style>
