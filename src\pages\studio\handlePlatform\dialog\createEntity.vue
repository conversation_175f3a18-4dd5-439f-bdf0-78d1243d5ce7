<template>
  <el-dialog title="创建实体" :visible.sync="dialog.show" width="480px">
    <el-form :model="form" :rules="rules" ref="entityForm" label-position="top">
      <el-form-item label="实体类型" class="is-required">
        <el-radio-group v-model="form.type">
          <el-radio-button label="2">静态实体</el-radio-button>
          <el-radio-button label="3">动态实体</el-radio-button>
          <el-radio-button label="5">所见即可说</el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="中文名称" prop="value">
        <el-input
          v-model.trim="form.value"
          placeholder="支持中文/英文/数字/下划线格式，不超过32个字符"
          ref="valueInput"
          @keyup.enter.native="toName"
        />
      </el-form-item>
      <el-form-item label="英文标识" prop="name">
        <el-input
          ref="nameInput"
          v-model.trim="form.name"
          placeholder="支持英文/数字/下划线格式，不超过15个字符"
          @keyup.enter.native="save"
        />
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button
        class="dialog-btn"
        type="primary"
        style="min-width: 104px"
        @click="save"
        :loading="saving"
      >
        {{ saving ? '创建中...' : '创建' }}
      </el-button>
    </span>
  </el-dialog>
</template>

<script>
import dicts from '@M/dicts'
import { mapGetters } from 'vuex'

export default {
  props: {
    dialog: {
      type: Object,
      default: {},
    },
  },
  data() {
    return {
      saving: false,
      form: {
        value: '',
        name: '',
        type: 2,
      },
      rules: {
        name: this.$rules.entityName(),
        value: this.$rules.entityValue(),
      },
    }
  },
  computed: {
    ...mapGetters({
      subAccount: 'user/subAccount',
    }),
  },
  watch: {
    'dialog.show': function (val, oldVal) {
      let self = this
      if (val) {
        ;(this.form = {
          value: '',
          name: '',
          type: 2,
        }),
          this.$refs.entityForm && this.$refs.entityForm.resetFields()
        this.$nextTick(function () {
          self.$refs.valueInput && self.$refs.valueInput.focus()
        })
      } else {
      }
    },
    'form.type': function (val, oldVal) {
      let self = this
      !self.form.value &&
        self.$refs.entityForm &&
        self.$refs.entityForm.clearValidate('value')
      !self.form.name &&
        self.$refs.entityForm &&
        self.$refs.entityForm.clearValidate('name')
    },
  },
  methods: {
    toName() {
      this.$refs.nameInput && this.$refs.nameInput.focus()
    },
    save() {
      let self = this
      if (this.saving) {
        return
      }
      this.$refs.entityForm.validate((valid) => {
        if (valid) {
          this.saving = true
          let data = {
            name: this.form.name,
            value: this.form.value,
            type: this.form.type,
          }
          let api = this.$config.api.STUDIO_ENTITY_CREATE
          this.$utils.httpPost(api, data, {
            success: (res) => {
              this.saving = false
              self.$message.success('创建成功')
              self.dialog.show = false
              self.$emit('change', 1)
              self.subAccount
                ? self.$router.push({
                    name: 'sub-entity',
                    params: { entityId: res.data.id },
                  })
                : self.$router.push({
                    name: 'entity',
                    params: { entityId: res.data.id },
                  })
            },
            error: (err) => {
              this.saving = false
              console.log('page=>>')
              console.log(err)
            },
          })
        }
      })
    },
  },
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="scss" scoped></style>
