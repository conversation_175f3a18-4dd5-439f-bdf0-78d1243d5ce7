<template>
  <div class="os-scroll">
    <!-- <handle-platform-top></handle-platform-top> -->
    <div class="tab-wrap">
      <el-tabs
        :value="activeName"
        @input="handleInputVal"
        @tab-click="handleClick"
        style="width: 100%"
      >
        <el-tab-pane label="大模型文档问答" name="document"
          ><document-qa v-if="tabsObj['document']"></document-qa
        ></el-tab-pane>
        <el-tab-pane label="语句问答" name="sentence"
          ><bank-qa :type="0" v-if="tabsObj['sentence']"></bank-qa
        ></el-tab-pane>
        <el-tab-pane label="关键词问答" name="keyword"
          ><bank-qa :type="3" v-if="tabsObj['keyword']"></bank-qa
        ></el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>
<script>
import HandlePlatformTop from '../top.vue'
import documentQa from '../qadoc.vue'
import bankQa from '../qabank.vue'

export default {
  data() {
    return {
      activeName: 'document',
      tabsObj: {
        document: true,
        sentence: false,
        keyword: false,
      },
    }
  },
  created() {
    if (this.$route.query.type) {
      this.activeName = this.$route.query.type
      this.setItemTrueOthersNot(this.$route.query.type)
    }
  },
  methods: {
    handleClick(tab, event) {
      // console.log(tab, event)
      // this.$route.query.type = this.activeName
      console.log('this.activeName', tab.name)
      this.setItemTrueOthersNot(tab.name)
    },

    setItemTrueOthersNot(key) {
      Object.keys(this.tabsObj).forEach((k) => {
        if (k !== key) {
          this.$set(this.tabsObj, k, false)
        }
      })
      this.$set(this.tabsObj, [key], true)
    },
    handleInputVal(val) {
      this.$router.replace({
        name: 'studio-handle-platform-qabanks',
        query: { type: val },
      })
    },
  },
  components: { HandlePlatformTop, documentQa, bankQa },
}
</script>
<style lang="scss" scoped>
// .os-scroll {
//   background-color: $white-grey;
// }
.tab-wrap {
  // max-width: 1200px;
  width: 100%;
  margin: auto;
  .el-tabs {
    margin-left: 0;
  }
  :deep(> .el-tabs > .el-tabs__header) {
    margin-bottom: 20px;
    // border-bottom: 1px solid #e7e7e7;
    height: 63.5px;
    background-color: $white-grey;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  :deep(> .el-tabs > .el-tabs__header .el-tabs__nav-wrap) {
    // padding-left: calc(50% - 195px);
    display: flex;
    justify-content: center;
    .el-tabs__nav-scroll {
      .el-tabs__nav {
        background: #eef0f1;
        padding: 0 4px;
        height: 42px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        .el-tabs__active-bar {
          display: none;
        }
        .el-tabs__item {
          height: 34px;
          line-height: 34px;
          font-size: 14px;
          text-align: center;
          border-radius: 6px;
          cursor: pointer;
          transition: all 0.2s ease;
          color: #555454;
          &.is-active {
            background: rgba(255, 255, 255, 0.8);
            color: #009bff;
            border-radius: 6px;
          }
          &:nth-child(2) {
            padding-left: 20px;
          }
          &:last-child {
            padding-right: 20px;
          }
        }
      }
    }
  }
  :deep(> .el-tabs > .el-tabs__header .el-tabs__nav-wrap::after) {
    display: none;
  }
  :deep(> .el-tabs .el-tabs__content) {
    padding: 0 35px;
  }
}
</style>
