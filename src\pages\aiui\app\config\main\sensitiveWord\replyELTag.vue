<template>
  <el-form class="my-form" size="mini" label-position="top" ref="formReply">
    <el-form-item>
      <div class="inline-box">
        <div
          v-for="(reply, index) in sensitiveWordTypeCopySingle.reply"
          :key="index"
        >
          <el-input
            type="textarea"
            :autosize="true"
            class="input-new-tag"
            v-if="editable[index]"
            v-model.trim="reply.label"
            :ref="'editableInput' + index"
            size="small"
            placeholder="请输入回复语，回车添加"
            @keyup.enter.native="handleEditableInputConfirm(reply, index)"
            @blur="handleEditableInputBlur(reply, index)"
            maxlength="100"
            show-word-limit
          ></el-input>
          <div
            class="div-st-reply"
            v-if="!editable[index]"
            @click="showEditTagInput(index)"
          >
            <ul>
              {{
                reply.label
              }}
            </ul>
            <ul>
              <i
                class="el-tag__close el-icon-close"
                title="删除该条回复语"
                @click.stop="handleClose(reply, index)"
              ></i>
            </ul>
          </div>
        </div>
        <div>
          <el-input
            type="textarea"
            class="input-new-tag"
            v-if="inputVisible"
            v-model="inputValue"
            ref="saveTagInput"
            size="small"
            placeholder="请输入回复语，回车添加"
            @keyup.enter.native="handleInputConfirm"
            @blur="handleInputConfirm"
            maxlength="100"
            show-word-limit
          ></el-input>
          <el-button
            v-else-if="
              !returnEditable &&
              !inputVisible &&
              (!sensitiveWordTypeCopySingle.reply ||
                (sensitiveWordTypeCopySingle.reply &&
                  sensitiveWordTypeCopySingle.reply.length < 30))
            "
            class="button-new-tag"
            size="small"
            @click="showInput"
            >+ 新增回复语</el-button
          >
        </div>
      </div>
    </el-form-item>
  </el-form>
</template>

<script>
export default {
  name: 'replyELTag',
  components: {
    // "v-chart": ECharts,
  },
  props: {
    sensitiveWordTypeCopySingle: {
      type: Object,
      default: () => {
        reply: []
      },
      deep: true,
    },

    canDoConfirm: {
      type: Object,
      default: {
        isOk: true,
      },
      deep: true,
    },
  },
  data() {
    return {
      inputVisible: false,
      editable: [],
      inputValue: '',
    }
  },
  computed: {
    returnEditable: function () {
      let flag = false
      this.editable.forEach((item, index) => {
        if (item) {
          flag = true
          return flag
        }
      })
      return flag
    },
  },
  methods: {
    //============添加回复语=====================
    //添加 回复语  input显示
    showInput() {
      this.inputVisible = true
      this.$nextTick((_) => {
        this.$refs.saveTagInput.$refs.textarea.focus()
      })
    },
    //添加 回复语input失去焦点
    handleInputConfirm() {
      let self = this
      let inputValue = this.inputValue
      if (inputValue) {
        let reg = /^[a-zA-Z0-9\u4e00-\u9fa5]+$/
        inputValue = inputValue.replace(/[\t|\n|\v|\r|\f]/g, '')
        if (!reg.test(inputValue)) {
          this.canDoConfirm.isOk = false
          this.canDoConfirm.clean = false
          this.canDoConfirm.random = Math.random()
          return
        }
        var tagInfo = {
          label: inputValue,
        }
        if (!this.sensitiveWordTypeCopySingle.reply) {
          this.sensitiveWordTypeCopySingle = {
            ...this.sensitiveWordTypeCopySingle,
            reply: [],
          }
        }
        let [reply = []] = [this.sensitiveWordTypeCopySingle.reply || []]
        reply.push(tagInfo)
        this.$emit('pushReply', reply)
      }
      this.inputVisible = false
      this.inputValue = ''
      this.canDoConfirm.isOk = true
    },
    //============编辑回复语=====================
    //编辑 回复语input显示
    showEditTagInput(index) {
      this.$set(this.editable, index, true)
      this.$nextTick((_) => {
        var editableInput = 'editableInput' + index
        this.$refs[editableInput][0].$refs.textarea.focus()
      })
    },
    //编辑 回复语 input发生改变
    handleEditableInputConfirm(item, index) {
      let reg = /^[a-zA-Z0-9\u4e00-\u9fa5]+$/
      if (!reg.test(item.label)) {
        this.canDoConfirm.isOk = false
        this.canDoConfirm.clean = false
        this.canDoConfirm.random = Math.random()
        return
      }
      this.canDoConfirm.isOk = true
      if (item.label) {
        this.$set(this.editable, index, false)
      } else {
        this.$message({ message: '请输入回复语，回车添加', type: 'info' })
      }
    },
    //编辑 回复语 input失去焦点
    handleEditableInputBlur(item, index) {
      let reg = /^[a-zA-Z0-9\u4e00-\u9fa5]+$/
      if (!reg.test(item.label)) {
        this.canDoConfirm.isOk = false
        this.canDoConfirm.clean = false
        this.canDoConfirm.random = Math.random()
        this.editable[index] = true
        return
      }
      this.canDoConfirm.isOk = true
      if (item.label) {
        this.$set(this.editable, index, false)
      } else {
        this.$message({ message: '请输入回复语，回车添加', type: 'info' })
      }
    },
    //删除回复语
    handleClose(reply, index) {
      this.editable.splice(index, 1)
      this.sensitiveWordTypeCopySingle.reply.splice(index, 1)
      this.$emit('pushReply', this.sensitiveWordTypeCopySingle.reply)
    },
  },
}
</script>

<style scoped lang="scss">
.div-st-reply {
  /*line-height: 34px;*/
  padding: 2%;
  font-size: 14px;
  color: #1784e9;
  background-color: rgba(23, 132, 233, 0.12);
  border-radius: 4px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border: 1px solid rgba(23, 132, 233, 0.5);
  margin-bottom: 1%;
  word-wrap: break-word;
  > ul:nth-child(2) {
    right: 0%;
    position: relative;
    display: -ms-inline-flexbox;
    bottom: 28px;
    margin-top: 20px;
    > i {
      right: 2%;
      bottom: -8px;
      position: relative;
      display: inline-flex;
      float: right;
      &:hover {
        cursor: pointer;
        color: $warning;
      }
    }
  }
}
.inline-box {
  display: block;
}
.inline-box > div {
  margin-right: 5px;
}
</style>
