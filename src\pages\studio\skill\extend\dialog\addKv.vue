<template>
  <el-dialog
    title="附加Key和Value"
    :visible.sync="dialog.show"
    width="568px"
    class="utterance-kv-dialog"
  >
    <os-table
      :border="true"
      :tableData="tableData"
      :header-cell-style="tableCellStyle"
      :cell-style="tableCellStyle"
      @del="delKV">
      <el-table-column
        prop="tag"
        label="Tag"
        width="90">
        <template slot-scope="scope">
          <el-select v-model="tagList[scope.$index]" placeholder="请选择"
            :disabled="!subAccountEditable" 
            @change="tagChanged(tagList[scope.$index], scope.$index)">
            <el-option
              v-for="item in tags"
              :key="item.value"
              :label="item.label"
              :value="item.value"
              :disabled="item.value == 'Fzy' && slotNames && !slotNames.length || tagList[scope.$index] !='Fzy' && item.value == 'Fzy' && fzyDisabled">
            </el-option>
          </el-select>
        </template>
      </el-table-column>
      <el-table-column
        prop="key"
        label="Key"
        width="160">
        <template slot-scope="scope">
          <el-autocomplete
            class="inline-input"
            v-if="tagList[scope.$index] == 'Sys'"
            v-model="scope.row.key"
            :fetch-suggestions="querySearch"
            :disabled="!subAccountEditable"
            placeholder="请输入"
            @blur="sysKeyBlur(scope.row, scope.$index)"
            @select="setSysKey(scope.row, scope.$index)"
          ></el-autocomplete>
          <el-input
            v-else
            ref="keyInput"
            :disabled="tagList[scope.$index] == 'Fzy' || !subAccountEditable"
            :value="scope.row.key" 
            :title="scope.row.key"
            placeholder="请输入"
            v-model="scope.row.key"
            @keyup.enter.native="addKeyEnter(scope.row, scope.$index)"
            @blur="addKey(scope.row, scope.$index)"></el-input>
        </template>
      </el-table-column>
      <el-table-column
        prop="value"
        label="value"
        width="160">
        <template slot-scope="scope">
          <template v-if="tagList[scope.$index] == 'Fzy'">
            <el-select
            placeholder="请选择"
            v-model="scope.row.value"
            :disabled="!subAccountEditable"
              @change="valueChanged(scope.row.value, scope.$index)">
              <el-option
                v-for="item in slotNames"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </template>
          <template v-else>
            <el-select
              v-if="tagList[scope.$index] === 'Sys' && scope.row.key === 'man_intv'"
              placeholder="请选择"
              v-model="scope.row.value"
              :disabled="!subAccountEditable"
              @change="valueChanged(scope.row.value, scope.$index)">
              <el-option
                v-for="item in manIntvValue"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
            <el-input
              ref="valueInput"
              v-else
              :value="scope.row.value"
              :title="scope.row.value"
              :disabled="!subAccountEditable"
              placeholder="请输入"
              v-model="scope.row.value"
              @keyup.enter.native="addValueEnter(scope.row, scope.$index)"
              @blur="addValue(scope.row, scope.$index)"></el-input>
          </template>
        </template>
      </el-table-column>
    </os-table>
    <div class="add-item" @click.prevent.stop="addRow" v-if="tableData.list && tableData.list.length < 5 && subAccountEditable">
      <i class="ic-r-plus"></i>添加
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button :disabled="!subAccountEditable" class="dialog-btn" type="primary" style="min-width: 104px;" @click="save">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>

export default {
  props: {
    dialog: {
      type: Object,
      default: {}
    },
    slotData: { 
      //kv值
      type: String,
      default: '{}'
    }, 
    markList: {
      type: Array,  //当前行语料中所有的槽名
      default: []
    },
    subAccountEditable: Boolean
  },
  data () {
    return {
      saving: false,
      tableCellStyle: {
        'padding': '0px',
        'height': '40px',
        'line-height': 'unset' //防止hover时，抖动
      },
      tableData: {
        loading: false,
        total: 0,
        page: 1,
        size: 5,
        handles: ['del'],
        list: []
      },
      tagList: [],
      initTagList: [],
      defaultTag: 'Ctx',
      tags: [{
          value: 'Ctx',
          label: 'Ctx'
        },{
          value: 'Slt',
          label: 'Slt'
        },{
          value: 'Sys',
          label: 'Sys'
        },{
          value: 'Fzy',
          label: 'Fzy'
      }],
      lastTag: '',
      keyWords: ['ctx', 'slt', 'sys', 'fzy' ],
      // kReg: /^((?![\u4e00-\u9fff]|：|:).)*$/,
      kReg: /^[a-zA-Z0-9._,#\$\^]{0,64}$/,
      vReg: /^((?!：).)*$/,
      fzyDisabled: false,
      sysValue: '',
      sysKeys: [
        { 'value': 'man_intv'},
        { 'value': 'servicepriority'}
      ],
      manIntvValue: [{
          value: 'DELALL',
          label: 'DELALL'
        },{
          value: 'DEL',
          label: 'DEL'
        },{
          value: 'TOP1',
          label: 'TOP1'
        },{
          value: 'ONLY',
          label: 'ONLY'
        },{
          value: 'BIZ_TOP1',
          label: 'BIZ_TOP1'
        }
      ],
      valueCheckList: ['DELALL', 'DEL','TOP1', 'ONLY','BIZ_TOP1']
    }
  },
  computed: {
    slotNames() {
      let tmp = [], uniqueSlotNameList = []
      if(this.markList && !this.markList.length) {
        return tmp = []
      }
      for(let i=0; i< this.markList.length; i++) {
        if(this.markList[i].hasOwnProperty('slotName')) {
          if(uniqueSlotNameList.indexOf(this.markList[i].slotName) === -1) {
            uniqueSlotNameList.push(this.markList[i].slotName)
            tmp.push({
              'value': this.markList[i].slotName,
              'label': this.markList[i].slotName
            })
          }
        }
      }
      return tmp
    }
  },
  watch: {
    'dialog.show': function(val, oldVal) {
      if(!val) return
      this.tableData.list.splice(0)
      this.tagList.splice(0)
      this.initTagList.splice(0)
      /**
       * slotData的两种格式
       * 1. 无tag的老格式 "[{"key":"dd","value":"dd"},{"key":"dd","value":"ss"}]" 
       * 2. 有tag的新格式 "{"ctx":[{"key":"dd","value":"vz"}],"stx":[{"key":"2d","value":"2v"}]}"
       */
      if(this.slotData) {
        let jsonObj = JSON.parse(this.slotData)
        if(jsonObj.length) {
          //过滤掉无tag的老数据
          return  
        }
        for(let item in jsonObj){
          if(jsonObj[item] && !jsonObj[item][0]) return
          if(jsonObj[item].length > 1) {
            let tmp = (new Array(jsonObj[item].length)).fill(item)
            this.tagList.push(...tmp)
            this.initTagList.push(...tmp)
            this.tableData.list.push(...jsonObj[item])
          } else {
            this.tagList.push(item)
            this.initTagList.push(item)
            this.tableData.list.push(jsonObj[item][0])
          }
        }
      }
      this.tableData.handles = this.subAccountEditable ? ['del'] : []
    },
    initTagList(val) {
      if(val && val.indexOf('Fzy') != -1) {
        this.fzyDisabled = true
      } else {
        this.fzyDisabled = false
      }
    }
  },
  methods: {
    querySearch (queryString, cb) {
      let sysKeys = this.sysKeys
      cb(sysKeys)
    },
    handleSelect (item) {
      console.log(item)
    },
    addRow (text) {
      let valid = true
      for(let i=0; i<this.tableData.list.length; i++) {
        if(!this.tableData.list[i].key) {
          valid = false
          this.$message.warning('Key值不能有空值')
          break
        }
        if(this.keyWords.indexOf(this.tableData.list[i].key) > -1) {
          valid = false
          this.$message.warning('Key值不能为系统保留值（Ctx、Slt、Sys、Fzy），不分大小写')
          break
        }
        if(!this.tableData.list[i].value) {
          valid = false
          this.$message.warning('Value值不能有空值')
          break
        }
      }
      if(!valid) return
      this.tagList.push(this.defaultTag)
      this.tableData.list.push({
        key: '',
        value: ''
      })
      this.$nextTick(function () {
        this.$refs.keyInput && this.$refs.keyInput.focus()
      })
    },
    jumpToValue() {
      this.$nextTick(function () {
        this.$refs.valueInput && this.$refs.valueInput.focus()
      })
    },
    tagChanged(tag, index){
      if(this.initTagList[index]) {
        this.lastTag = this.initTagList[index]
      }
      if(tag == 'Fzy') {
        this.tableData.list[index].key = 'slot'
        this.tableData.list[index].value = this.slotNames[0].value
      }
      if( tag != this.lastTag && this.lastTag == 'Fzy') {
        this.tableData.list[index].key = ''
        this.tableData.list[index].value = ''
      }
      this.lastTag = tag
      this.initTagList[index] = tag
      if(this.initTagList && this.initTagList.indexOf('Fzy') != -1) {
        this.fzyDisabled = true
      } else {
        this.fzyDisabled = false
      }
    },
    sysKeyBlur(data, index) {
      data.key = data.key.trim()
      if(data.key == 'man_intv' || data.key == 'servicepriority' ) {
        this.tableData.list[index].value = ''
      }
    },
    setSysKey(data, index) {
      this.tableData.list[index].value = ''
    },
    valueChanged(val, index) {
      this.tableData.list[index].value = val
    },
    addKey(data, index) {
      data.key = data.key.trim()
      if(!data.key) {
        return
      }
      if(!this.kReg.test(data.key)) {
        return this.$message.warning('Key 仅支持#$.,_^英文和数字，且每个不超过64字')
      }
      if(this.keyWords.indexOf(data.key.toLowerCase()) > -1) {
        return this.$message.warning('Key值不能为系统保留值（Ctx、Slt、Sys、Fzy），不分大小写')
      }
    },
    addKeyEnter(data, index){
      // this.addKey(data, index)
      this.jumpToValue()
    },
    addValue(data, index) {
      data.value = data.value.trim()
      if(!data.value) {
        return
      }
      if(data.key !== 'man_intv' && this.valueCheckList.indexOf(data.value) !== -1) {
        return this.$message.warning(`仅在 Tag 为 Sys，Key为 man_intv 时，Value 才可输入 ${data.value}`)
      }
      if(!this.vReg.test(data.value) || data.value.length > 64) {
        return this.$message.warning('Value 中不支持中文冒号，且每个不超过64字')
      }
    },
    addValueEnter(data, index){
      this.addValue(data, index)
      if( this.tableData.list.length < 5) {
        this.addRow()
      }
    },
    delKV (data, index) {
      this.tableData.list.splice(index, 1)
      this.tagList.splice(index, 1)
    },
    save () {
      let valid = false
      let list = this.tableData.list
      let data = {}
      for(let i=0; i<list.length; i++) {
        if(!list[i].key) {
          return this.$message.warning('Key值不能有空值')
        }
        if(this.keyWords.indexOf(list[i].key) > -1) {
          return this.$message.warning('Key值不能为系统保留值（Ctx、Slt、Sys、Fzy），不分大小写')
        }
        if(!this.kReg.test(list[i].key)) {
          return this.$message.warning('Key 仅支持#$.,_^英文和数字，且每个不超过64字')
        }
        if(!list[i].value) {
          return this.$message.warning('Value值不能有空值')
        }
        if(list[i].key !== 'man_intv' && this.valueCheckList.indexOf(list[i].value) !== -1) {
          return this.$message.warning(`仅在 Tag 为 Sys，Key为 man_intv 时，Value 才可输入 ${list[i].value}`)
        }
        if(!this.vReg.test(list[i].value) || list[i].value.length > 64) {
          return this.$message.warning('Value 中不支持中文冒号，且每个不超过64字')
        }
        if(data.hasOwnProperty(this.tagList[i])) {
          data[this.tagList[i]].push(list[i])
        } else {
          data[this.tagList[i]] = [list[i]]
        }
      }
      this.dialog.show = false
      this.$emit('saveKV', JSON.stringify(data))
    }
  }
}
</script>

<style lang="scss">
.utterance-kv-dialog {
  .el-dialog__body {
    margin-bottom: 23px;
    font-size: 0;
  }
  .el-table__body tr:hover > td {
    background-color: #fff;
  }
  th .cell {
    font-weight: 600;
  }
  .cell .el-input__inner {
    padding-left: 0;
    border: none;
  }
  .add-item {
    padding-left: 25px;
    height: 40px;
    line-height: 40px;
    font-size: 14px;
    font-weight: 600;
    color: $primary;
    cursor: pointer;
    border: 1px solid $grey2;
    border-top-color: transparent;

    .ic-r-plus {
      margin-right: 4px;
    }
  }
  .el-input.is-disabled .el-input__inner {
    background-color: transparent;
  }
}
</style>
