<template>
  <el-dialog
    class="character-quote-dialog"
    :title="title"
    :visible.sync="dialog.show"
    width="480px"
  >
    <div class="os-scroll skill-quote">
      <el-tabs type="border-card">
        <el-tab-pane>
          <span slot="label">设备({{ iflyos.length || 0 }})</span>
          <div class="list-content">
            <os-skill-simple-item
              :noborder="true"
              v-for="(item, index) in iflyos"
              :key="index"
              class="skill-quote-item"
              :url="item.image"
              :name="item.clientName || item.name || '-'"
            />
          </div>
        </el-tab-pane>
        <el-tab-pane>
          <span slot="label">应用({{ aiui.length || 0 }})</span>
          <div class="list-content">
            <os-skill-simple-item
              :noborder="true"
              v-for="(aiui, index) in aiui"
              :key="index"
              class="skill-quote-item"
              :name="aiui.appName || '-'"
              :sceneName="aiui.sceneName || '-'"
              slot="charater"
            />
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </el-dialog>
</template>

<script>
import dicts from '@M/dicts'

export default {
  props: {
    dialog: {
      type: Object,
      default: {},
    },
    type: String,
  },
  data() {
    return {
      title: '0个引用',
      aiui: [],
      iflyos: [],
    }
  },
  watch: {
    'dialog.show': function (val, oldVal) {
      if (val) {
        this.getSkills()
      } else {
      }
    },
  },
  mounted() {},
  methods: {
    getSkills() {
      let self = this
      this.skills = []
      let api, data
      if (this.type === 'character') {
        api = this.$config.api.STUDIO_CHARACTER_QUOTE
        data = {
          id: this.dialog.id,
        }
      } else {
        api = this.$config.api.STUDIO_QA_SCENES_LIST
        data = {
          repoId: this.dialog.repoId,
        }
      }
      if (this.dialog.id || this.dialog.repoId) {
        this.$utils.httpGet(api, data, {
          success: (res) => {
            self.aiui = res.data.aiui || []
            self.iflyos = res.data.iflyos || []

            self.title = `${parseInt(
              self.aiui.length + self.iflyos.length
            )}个引用`
          },
          error: (err) => {},
        })
      }
    },
  },
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="scss" scoped>
.skill-quote {
  max-height: 560px;
  margin: 0 -16px;
  &-item {
    width: 188px;
    margin: 0 10px 24px;
    border: 1px solid $grey2;
    box-shadow: 0 6px 12px 0 rgba(0, 0, 0, 0.05);
    border-radius: 10px;
  }
  .list-content {
    height: 285px;
    overflow-x: hidden;
    overflow-y: scroll;
  }
}
</style>
<style lang="scss" scoped>
.character-quote-dialog {
  :deep(.el-dialog) {
    padding-bottom: 30px;
  }
}
</style>
