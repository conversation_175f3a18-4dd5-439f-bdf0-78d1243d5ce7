<template>
  <el-dialog
    title="动态实体密钥"
    :visible.sync="dialog.show"
    width="568px"
    class="dialog-account-key"
  >
    <!-- <p class="top-tip">重置 AccountKey 会导致服务立即失效。仅推荐当 AccountKey 可能出现泄露时使用。</p> -->
    <el-form
      :model="form"
      ref="secretKeyForm"
      label-width="120px"
      label-position="left"
    >
      <el-form-item label="AccountKey">
        <div class="account-key-wrap">
          <span
            >{{ form.key && form.key.substr(0, 3) }}***{{
              form.key && form.key.substr(-3, 3)
            }}</span
          >
          <a @click="copyAppKey(form.key)">复制</a>
          <!-- <os-reset :tips="tips" @reset="reset"></os-reset> -->
        </div>
      </el-form-item>
      <el-form-item>
        <span style="font-weight: 600" slot="label"
          >namespace
          <el-tooltip
            effect="dark"
            content="AIUI开放平台的命名空间，代表用户唯一标识"
          >
            <i class="el-icon-question" />
          </el-tooltip>
        </span>
        <span>{{ form.namespace || '-' }}</span>
        <a v-if="form.namespace" @click="copyAppKey(form.namespace)">复制</a>
      </el-form-item>
      <el-form-item label="IP白名单">
        <el-tooltip
          content="关闭 IP 白名单时，任意终端均可访问服务器"
          placement="right"
        >
          <i class="ic-r-tip" />
        </el-tooltip>
        <el-switch v-model="flag" @change="switchWhiteIp"> </el-switch>
        <os-text-adder
          class="mgb24"
          v-if="flag"
          style="max-width: 640px; margin-top: 12px"
          :data="ips"
          :reg="textReg"
          warning="请输入正确的ip地址"
          :max="20"
          :readonly="true"
          @add="add"
          @del="del"
          placeholder="回车新增，最多20条"
        />
      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script>
import dicts from '@M/dicts'

export default {
  props: {
    dialog: {
      type: Object,
      default: {},
    },
  },
  data() {
    return {
      form: {
        key: '',
        namespace: '',
      },
      flag: false,
      ips: [],
      textReg:
        /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/,
      tips: {
        type: Object,
        title: '确定重置吗？',
        optionDesc: '重置后所有动态实体均会受到影响。',
        confirmText: '重置',
        btn: '重置',
      },
    }
  },
  watch: {
    'dialog.show': function (val, oldVal) {
      if (val) {
        this.getData()
        this.getNamespace()
      } else {
      }
    },
  },
  created() {
    this.getData()
  },
  methods: {
    // 数据初始化
    getData() {
      this.$utils.httpPost(
        this.$config.api.STUDIO_ENTITY_ACCOUNT_KEY,
        {},
        {
          success: (res) => {
            this.ips.splice(0)
            this.form.key = res.data.secretKey.accountKey
            this.flag = res.data.secretKey.flag
            if (res.data.secretKey && res.data.secretKey.ips) {
              this.ips = res.data.secretKey.ips.split(',')
            }
          },
          error: (err) => {},
        }
      )
    },
    reset() {
      this.$utils.httpPost(
        this.$config.api.STUDIO_ENTITY_RESET_ACCOUNT_KEY,
        {},
        {
          success: (res) => {
            this.$message.success('重置成功')
            this.form.key = res.data
          },
          error: (err) => {},
        }
      )
    },
    copyAppKey(value) {
      this.$utils.copyClipboard(value)
    },
    switchWhiteIp() {
      this.$utils.httpPost(
        this.$config.api.STUDIO_ENTITY_SWITCH_IP,
        {
          flag: this.flag,
        },
        {
          success: (res) => {},
          error: (err) => {},
        }
      )
    },
    add(item) {
      this.ips.push(item)
      this.saveIps()
    },
    del(item, index) {
      this.ips.splice(index, 1)
      this.saveIps()
    },
    calcIpStr() {
      let tmp = ''
      for (let i = 0, len = this.ips.length; i < len; i++) {
        tmp += this.ips[i] + ','
      }
      return tmp.substr(0, tmp.length - 1)
    },
    saveIps() {
      this.$utils.httpPost(
        this.$config.api.STUDIO_ENTITY_WHITE_IPS,
        {
          ips: this.calcIpStr(this.ips),
        },
        {
          success: (res) => {},
          error: (err) => {},
        }
      )
    },
    getNamespace() {
      this.$utils.httpPost(
        this.$config.api.STUDIO_NAMESPACE,
        {},
        {
          success: (res) => {
            this.form.namespace = res.data
          },
          error: (err) => {},
        }
      )
    },
  },
  components: {},
}
</script>

<style lang="scss" scoped>
.top-tip {
  margin-bottom: 24px;
  padding: 11px 17px;
  color: $warning;
  border-radius: 4px;
  border: 1px solid $warning;
}
.account-key-wrap {
  font-size: 0;
  & span,
  a {
    margin-right: 24px;
    font-size: 14px;
  }
}
.ic-r-tip {
  position: absolute;
  left: -55px;
  top: -1px;
  color: $grey3;
}
</style>
<style lang="scss">
.dialog-account-key {
  .el-dialog__body {
    overflow: hidden;
  }
  .el-button {
    width: auto;
    min-width: unset;
    font-size: 14px;
  }
}
</style>
