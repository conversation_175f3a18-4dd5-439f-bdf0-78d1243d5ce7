<template>
  <os-page :options="pageOptions">
    <scene
      slot="headLeft"
      :globalChange="change"
      @switchSceneCategory="switchSceneCategory"
      :sceneList="sceneList"
      :currentScene="currentScene"
      :currentSceneId="currentSceneId"
      :canCreateTranslateScene="canCreateTranslateScene"
      :subAccount="subAccount"
      :subAccountEditable="subAccountEditable"
      @addCategory="addCategory"
      @getSceneList="getSceneList"
      @getContentTypesList="getContentTypesList"
      @change="sceneChange"
    ></scene>
    <div slot="btn">
      <el-button
        type="primary"
        size="small"
        :loading="previewLoading"
        @click="doPreview"
        :disabled="currentSceneId && !needDefaultImport ? false : true"
        >预览</el-button
      >
      <el-button
        type="primary"
        size="small"
        :loading="releaseLoading"
        @click="doRelease"
        :disabled="currentSceneId && !needDefaultImport ? false : true"
        >发布</el-button
      >
    </div>
    <div class="div-add-ft">
      <a
        class="div-add-ft-a"
        v-if="currentSceneId && !needDefaultImport"
        @click="addContentTypes(1, currentSceneId)"
        >添加一级分类</a
      >
    </div>
    <div
      class="div-need-default-import"
      v-if="needDefaultImport"
      v-loading="defaultImportLoading"
    >
      <ul>
        <li>
          <p>
            如果你的带屏设备需要展示推荐语音交互支持的内容，你可以在这里设置推荐。
          </p>
        </li>
      </ul>
      <ul>
        <li>
          <p>
            AIUI内置了推荐模板， 现在去 <a @click="confirmStart">开启使用</a> 。
          </p>
        </li>
      </ul>
    </div>
    <div
      class="div-os-collapse"
      v-loading="releaseLoading"
      v-if="!needDefaultImport && list.children !== undefined"
      v-for="(item, index) in list.children"
      :key="index"
    >
      <os-collapse-recommend
        class="os-collapse-recommend-class"
        ref="categoryCo"
        :id="Math.random()"
        :key="index"
        :index_1="index"
        size="large"
        :category="item"
        @editTitle="editTitle"
        @checkTitleLengthAndType="checkTitleLengthAndType"
        @getAlbumOrSongById="getAlbumOrSongById"
        @addContentTypes="addContentTypes(2, item.id)"
        @editAlbum="editAlbum"
        @confirmDeleteCategory="confirmDeleteCategory"
      >
        <div class="os-collapse-recommend-class-div">
          <div
            class="div-os-collapse-recommend-inner"
            v-if="item.categoryData !== undefined"
            v-for="(child, index_c) in item.categoryData"
            :key="index_c"
            :class="{
              'div-os-collapse-recommend-inner-inline': child.type == -1,
            }"
          >
            <div v-if="child.type == -1" class="div-album">
              <img-hover
                class="img-hover-album"
                :imgData="{
                  album: child,
                  category: item,
                  index_1: index,
                  index_2: index_c,
                }"
                @editAlbum="editAlbum"
                @getSceneList="getSceneList"
                @updateAlbumLocal="updateAlbumLocal"
                @deleteAlbumLocal="deleteAlbumLocal"
                @getAlbumOrSongById="getAlbumOrSongById"
              />
            </div>
            <os-collapse-recommend
              v-else
              class="os-collapse-recommend-class"
              ref="categoryCo"
              size="large"
              :index_1="index"
              :index_2="index_c"
              :id="Math.random()"
              :key="index_c"
              :category="child"
              @editTitle="editTitle"
              @checkTitleLengthAndType="checkTitleLengthAndType"
              @addContentTypes="addContentTypes(2, child.id)"
              @editAlbum="editAlbum"
              @confirmDeleteCategory="confirmDeleteCategory"
              @getAlbumOrSongById="getAlbumOrSongById"
            >
              <div
                class="div-album"
                v-if="child.albumData !== undefined"
                v-for="(album, index_a) in child.albumData"
                :key="index_a"
              >
                <img-hover
                  class="img-hover-album"
                  :id="Math.random()"
                  :imgData="{
                    album: album,
                    category: child,
                    index_1: index_c,
                    index_2: index_a,
                  }"
                  @editAlbum="editAlbum"
                  @getSceneList="getSceneList"
                  @updateAlbumLocal="updateAlbumLocal"
                  @deleteAlbumLocal="deleteAlbumLocal"
                  @getAlbumOrSongById="getAlbumOrSongById"
                />
              </div>
            </os-collapse-recommend>
          </div>
        </div>
      </os-collapse-recommend>
    </div>
    <add-category-dialog
      :dialog="addCategoryDialog"
      @addCategory="addCategory"
      @getSceneList="getSceneList"
      @getContentTypesList="getContentTypesList"
    ></add-category-dialog>
    <addEditAlbumDialog
      :dialog="addEditAlbumDialog"
      @addOrUpdateAlbumLocal="addOrUpdateAlbumLocal"
      @getSceneList="getSceneList"
      @getAlbumOrSongById="getAlbumOrSongById"
      @getAlbumOrSongByIdSingle="getAlbumOrSongByIdSingle"
    ></addEditAlbumDialog>
    <previewDialog
      :dialog="previewDialog"
      @getSceneList="getSceneList"
      @getAlbumOrSongById="getAlbumOrSongById"
    ></previewDialog>
  </os-page>
</template>

<script>
import scene from './scene'
import addCategoryDialog from './addCategoryDialog'
import addEditAlbumDialog from './addEditAlbumDialog'
import previewDialog from './previewDialog'
import imgHover from './imgHover'

export default {
  name: 'contentRecommend',
  props: {
    subAccount: Boolean,
    subAccountEditable: Boolean,
  },
  data() {
    return {
      change: false,
      previewLoading: false,
      releaseLoading: false,
      defaultImportLoading: false,
      sceneList: [],
      albumList: [[]],
      currentSceneId: null,
      addCategoryDialog: {
        show: false,
      },
      scrollTestDialog: {
        show: false,
      },
      addEditAlbumDialog: {
        show: false,
        type: 0,
        contentTypeId: null,
      },
      previewDialog: {
        show: false,
        currentSceneId: null,
        list: {
          children: [],
        },
      },
      currentScene: {},
      needDefaultImport: false,
      canCreateTranslateScene: false,
      list: {
        children: [],
      },
      listOrigin: {
        children: [
          {
            categoryData: [{ albumData: {} }],
          },
        ],
      },
      categoryData: [],
      albumData: [],
      pageOptions: {
        title: '',
        showHead: true,
        loading: false,
        returnBtn: false,
      },
    }
  },
  components: {
    scene,
    addCategoryDialog,
    addEditAlbumDialog,
    previewDialog,
    imgHover,
  },
  computed: {
    appId() {
      return this.$route.params.appId
    },
  },
  created() {
    this.getSceneList()
  },
  watch: {
    list: function () {
      this.$forceUpdate()
    },
  },
  methods: {
    checkTitleLengthAndType(title) {
      if (!title || title.length > 20 /*|| /[^\u4E00-\u9FA5]/g.test(title)*/) {
        this.$message.error('长度不超过20个字符')
        return
      }
    },
    doDefaultImport() {
      this.defaultImportLoading = true
      this.$utils.httpGet(
        this.$config.api.RECOMMEND_CONTENT_DEFAULT_IMPORT,
        {
          appid: this.appId,
          sceneId: this.currentScene.id,
        },
        {
          success: (res) => {
            if (res.flag) {
              this.$message.success(res.desc)
              this.needDefaultImport = false
              this.getSceneList()
            } else {
              this.$message.error(res.desc)
            }
            this.defaultImportLoading = false
          },
          error: (err) => {},
        }
      )
    },
    doPreview() {
      if (this.currentSceneId === null) {
        this.$message.error('场景不能为空')
        return
      }
      this.previewDialog.currentSceneId = this.currentSceneId
      this.previewDialog.list = this.list
      this.previewDialog.show = true
    },
    doRelease() {
      this.releaseLoading = true
      this.$utils.httpGet(
        this.$config.api.RECOMMEND_CONTENT_BUSINESS_RELEASE,
        {
          appid: this.appId,
          scene: this.currentScene.scene,
        },
        {
          success: (res) => {
            if (res.flag) {
              this.$message.success(res.desc)
            } else {
              this.$message.error(res.desc)
            }
            this.releaseLoading = false
          },
          error: (err) => {},
        }
      )
    },
    switchSceneCategory(currentScene) {
      let thiz = this
      let expand = false
      if (
        thiz.currentSceneId !== null &&
        thiz.currentSceneId === currentScene.id
      ) {
        expand = true
      }
      thiz.currentSceneId = currentScene.id
      this.currentScene = currentScene
      if (this.currentScene.childrenNum === 0) {
        thiz.needDefaultImport = true
      } else {
        thiz.needDefaultImport = false
      }
      this.list = this.sceneList.find((scene) => {
        return scene.id === currentScene.id
      })
      /*if (expand) {
              let $ref = thiz.$refs['categoryCo'];
              $ref.forEach( (item, index) => {
                //item.edit = false
                //item.open = false
                if (item.$children) {
                  if (item.open === true) {
                    item.change(0, index)
                  }
                }
              })
            }*/

      if (!expand) {
        let $ref = thiz.$refs['categoryCo']
        if ($ref) {
          $ref.forEach((item) => {
            item.edit = false
            item.open = false
          })
        }
      }
      //this.$forceUpdate()
      //thiz.getAlbumOrSongById(category)
    },
    getSceneList(callback) {
      let self = this
      this.$utils.httpGet(
        this.$config.api.RECOMMEND_CONTENT_LIST,
        {
          appid: this.appId,
        },
        {
          success: (res) => {
            if (res.flag) {
              // 区分场景、分类
              self.sceneList = res.data.list.filter((item) => {
                return item.hasOwnProperty('scene')
              })
              if (callback) {
                callback(
                  self.sceneList.filter((scene, index) => {
                    scene.id === self.currentSceneId
                  })
                )
              }
            } else {
              this.$message.error(res.desc)
            }
          },
          error: (err) => {},
        }
      )
    },
    getContentTypesList(sceneId) {
      let thiz = this
      if (sceneId) thiz.currentSceneId = sceneId
      this.$utils.httpGet(
        this.$config.api.RECOMMEND_CONTENT_LIST,
        {
          appid: thiz.appId,
          sceneId: sceneId || thiz.currentSceneId,
        },
        {
          success: (res) => {
            if (res.flag) {
              if (res.data && res.data.list) {
                thiz.list = res.data.list
              }
            } else {
              this.$message.error(res.desc)
            }
          },
          error: (err) => {},
        }
      )
    },
    getAlbumOrSongById(category, index_1, index_2, callback) {
      let thiz = this
      thiz.listOrigin = JSON.parse(JSON.stringify(thiz.list))
      this.$utils.httpGet(
        this.$config.api.RECOMMEND_CONTENT_GET_ALBUM,
        {
          appid: thiz.appId,
          contentTypeId: category.id,
          type: category.type, // -1专辑，-2单曲 1一级分类，2二级分类 参考2.4
          page: 1,
          size: 99999,
        },
        {
          success: (res) => {
            if (res.flag) {
              if (category.type == 1) {
                if (index_1 != undefined && index_2 == undefined) {
                  if (thiz.list.children[index_1]) {
                    thiz.list.children[index_1].categoryData = res.data.list
                  }
                }
              } else {
                if (index_1 != undefined && index_2 != undefined) {
                  let parentCategory = thiz.listOrigin.children.find((item) => {
                    return item.id == category.parentId
                  })
                  thiz.getAlbumOrSongById(
                    parentCategory,
                    index_1,
                    undefined,
                    () => {
                      //thiz.list.children[index_1].categoryData[index_2].albumData = res.data.list
                      if (
                        thiz.listOrigin.children[index_1] &&
                        thiz.listOrigin.children[index_1].categoryData &&
                        thiz.listOrigin.children[index_1].categoryData[index_2]
                      ) {
                        thiz.listOrigin.children[index_1].categoryData[
                          index_2
                        ].albumData = res.data.list
                      }
                      thiz.list = thiz.listOrigin
                    }
                  )
                }
              }
              if (callback) callback()
              thiz.$forceUpdate()
            } else {
              this.$message.error(res.desc)
            }
          },
          error: (err) => {},
        }
      )
    },
    addOrUpdateAlbumLocal(type, album, category, index_1, index_2) {
      let thiz = this
      if (type === 0) {
        // 更新
        if (category.type === 1) {
          let tmp = Array.prototype.map.call(
            category.categoryData,
            function (item, index) {
              if (item.type === album.type && item.id === album.id) {
                item = album
              }
              return item
            }
          )
          category.categoryData = tmp
          thiz.list.children.forEach((item) => {
            if (item.type === category.type && item.id === category.id) {
              item = category
            }
          })
        } else if (category.type === 2) {
          let tmp = Array.prototype.map.call(
            category.albumData,
            function (item, index) {
              if (item.type === album.type && item.id === album.id) {
                item = album
              }
              return item
            }
          )
          category.albumData = tmp
          thiz.list.children.forEach((item) => {
            if (item.type === 1 && item.categoryData !== undefined) {
              item.categoryData.forEach((cat) => {
                if (
                  cat.type === category.type &&
                  cat.albumData &&
                  cat.id === category.id
                ) {
                  cat = category
                }
              })
            }
          })
        }
      } else if (type === 1) {
        // 新增
        if (category.type === 1) {
          category.categoryData.push(album)
          if (category.childrenNum) {
            category.childrenNum++
          } else {
            category.childrenNum = 1
          }
        } else if (category.type === 2) {
          if (category.albumData) {
            category.albumData.push(album)
          } else {
            category.albumData = [album]
          }
          if (category.childrenNum) {
            category.childrenNum++
          } else {
            category.childrenNum = 1
          }
        }
      }
      thiz.$forceUpdate()
    },
    deleteCategoryLocal(category, index_1, index_2) {
      let thiz = this
      if (category.type === 1) {
        thiz.list.children.splice(
          thiz.list.children.findIndex((item, index, arr) => {
            return item.type === category.type && item.id === category.id
          }),
          1
        )
        if (
          thiz.list.children === undefined ||
          (thiz.list.children !== undefined && thiz.list.children.length === 0)
        ) {
          thiz.list.childrenNum = 0
          thiz.needDefaultImport = true
        }
      } else if (category.type === 2) {
        thiz.list.children[index_1].categoryData.splice(
          thiz.list.children[index_1].categoryData.findIndex(
            (item, index, arr) => {
              return item.type === category.type && item.id === category.id
            }
          ),
          1
        )
        if (
          thiz.list.children[index_1].categoryData === undefined ||
          (thiz.list.children[index_1].categoryData !== undefined &&
            thiz.list.children[index_1].categoryData.length === 0)
        ) {
          thiz.list.children[index_1].childrenNum = 0
        }
      }
      thiz.$forceUpdate()
    },
    deleteAlbumLocal(album, category) {
      let thiz = this
      if (category.type === 1) {
        category.categoryData.splice(
          category.categoryData.findIndex((item, index, arr) => {
            return item.type === album.type && item.id === album.id
          }),
          1
        )
        if (
          category.categoryData === undefined ||
          (category.categoryData !== undefined &&
            category.categoryData.length === 0)
        ) {
          category.childrenNum = 0
        }
      } else if (category.type === 2) {
        category.albumData.splice(
          category.albumData.findIndex((item, index, arr) => {
            return item.type === album.type && item.id === album.id
          }),
          1
        )
        if (
          category.albumData === undefined ||
          (category.albumData !== undefined && category.albumData.length === 0)
        ) {
          category.childrenNum = 0
        }
      }
      thiz.$forceUpdate()
    },
    updateAlbumLocal(album, category) {
      let thiz = this
      if (category.type === 1) {
        let tmp = Array.prototype.map.call(
          category.categoryData,
          function (item, index) {
            if (item.type === album.type && item.id === album.id) {
              item = album
            }
            return item
          }
        )
        category.categoryData = tmp
        thiz.list.children.forEach((item) => {
          if (item.type === category.type && item.id === category.id) {
            item = category
          }
        })
      } else if (category.type === 2) {
        let tmp = Array.prototype.map.call(
          category.albumData,
          function (item, index) {
            if (item.type === album.type && item.id === album.id) {
              item = album
            }
            return item
          }
        )
        category.albumData = tmp
        thiz.list.children.forEach((item) => {
          if (item.type === 1 && item.categoryData !== undefined) {
            item.categoryData.forEach((cat) => {
              if (
                cat.type === category.type &&
                cat.albumData &&
                cat.id === category.id
              ) {
                cat = category
              }
            })
          }
        })
      }
      thiz.$forceUpdate()
    },
    getAlbumOrSongByIdSingle(album, category) {
      let thiz = this
      this.$utils.httpGet(
        this.$config.api.RECOMMEND_CONTENT_GET_ALBUM,
        {
          appid: thiz.appId,
          contentTypeId: album.id,
          type: album.type, // -1专辑，-2单曲 1一级分类，2二级分类 参考2.4
          page: 1,
          size: 99999,
        },
        {
          success: (res) => {
            if (res.flag) {
              let tmp = res.data.list
              if (album.type === -1) {
                for (let i = 0; i < tmp.length; i++) {
                  Array.prototype.map.call(
                    thiz.list.children,
                    function (item, index) {
                      return Array.prototype.map.call(
                        item,
                        function (item, index) {
                          if (category.type === 1) {
                            return item.categoryData.forEach((albumItem) => {
                              if (
                                albumItem.id === album.id &&
                                albumItem.type === album.type
                              ) {
                                albumItem = tmp[i]
                              }
                            })
                          }
                        }
                      )
                    }
                  )
                }
              }
              thiz.$forceUpdate()
            } else {
              this.$message.error(res.desc)
            }
          },
          error: (err) => {},
        }
      )
    },
    editTitle(e, title, category, index_1, index_2, close) {
      if (!title || title.length > 20 /*|| /[^\u4E00-\u9FA5]/g.test(title)*/) {
        this.$message.error('长度不超过20个字符')
        return
      }
      let self = this
      this.$utils.httpPost(
        this.$config.api.RECOMMEND_CONTENT_UPDATECONTENTTYPES,
        {
          appid: this.appId,
          id: category.id,
          contentName: title,
        },
        {
          success: (res) => {
            if (res.flag) {
              this.$message.success(res.desc)
              close()
              res.data = {
                ...category,
                ...res.data,
              }
              self.addOrUpdateCategoryLocal(0, res.data)
            } else {
              this.$message.error(res.desc)
            }
          },
          error: (err) => {},
        }
      )
    },
    editAlbum(type, album, category, index_1, index_2) {
      this.addEditAlbumDialog = {
        show: true,
        type: type,
        album: album,
        category: category,
        index_1: index_1,
        index_2: index_2,
      }
    },
    addContentTypes(type, parentId) {
      this.addCategoryDialog = {
        show: true,
        type: type,
        parentId: parentId,
        scene: this.currentScene.scene,
        appId: this.appId,
      }
    },
    deleteContentType(category, index_1, index_2) {
      let self = this
      this.$utils.httpPost(
        this.$config.api.RECOMMEND_CONTENT_DELETESENCETYPES,
        {
          appid: this.appId,
          contentTypeId: category.id,
          type: category.type,
        },
        {
          success: (res) => {
            if (res.flag) {
              this.$message.success(res.desc)
              self.deleteCategoryLocal(category, index_1, index_2)
            } else {
              this.$message.error(res.desc)
            }
          },
          error: (err) => {},
        }
      )
    },
    confirmStart() {
      const h = this.$createElement
      let thiz = this
      this.$confirm('提示', {
        title: '提示',
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        message: h('div', { class: 'el-div-question' }, [
          h('p', '开启后你可以继续编辑推荐内容，确认要开启吗？'),
        ]),
      }).then(() => {
        thiz.doDefaultImport()
      })
    },
    confirmDeleteCategory(contentType, index_1, index_2) {
      const h = this.$createElement
      let thiz = this
      this.$confirm('提示', {
        title: '提示',
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        message: h('div', { class: 'el-div-question' }, [
          h('p', '删除发布后该内容不再展示给用户，你确认要删除吗？'),
        ]),
      }).then(() => {
        thiz.deleteContentType(contentType, index_1, index_2)
      })
    },
    addCategory(type, scene, parentId, contentName, callback) {
      let self = this
      let data = {
        appid: this.appId,
        type: type, // 0: 场景， 1:一级分类，2:二级分类，依次类推
      }
      if (parentId) {
        data = {
          ...data,
          parentId: parentId, // 顶级分类
        }
      }
      if (contentName) {
        data = {
          ...data,
          contentName: contentName,
        }
      }
      if (scene) {
        data = {
          ...data,
          scene: scene,
        }
      }
      this.$utils.httpPost(
        this.$config.api.RECOMMEND_CONTENT_ADDCONTENTTYPES,
        data,
        {
          noErrorMessage: true,
          success: (res) => {
            if (res.flag) {
              this.$message.success(res.desc)
              callback()
              self.addOrUpdateCategoryLocal(1, res.data)
            } else {
              this.$message.error(
                res.desc === 'Duplicate name !' ? '名称不能重复' : res.desc
              )
            }
          },
          error: (err) => {
            this.$message.error(
              err.desc === 'Duplicate name !' ? '名称不能重复' : err.desc
            )
          },
        }
      )
    },
    addOrUpdateCategoryLocal(type, category) {
      let thiz = this
      if (type === 1) {
        category.childrenNum = 0
        if (category.type === 1) {
          thiz.list.children.push(category)
          if (thiz.list.childrenNum) {
            thiz.list.childrenNum++
          } else {
            thiz.list.childrenNum = 1
          }
        } else if (category.type === 2) {
          thiz.list.children.forEach((item, index) => {
            if (item.id === category.parentId) {
              if (item.categoryData) {
                item.categoryData.push(category)
              } else {
                item.categoryData = [category]
              }
              if (item.childrenNum) {
                item.childrenNum++
              } else {
                item.childrenNum = 1
              }
            }
          })
        }
      } else if (type === 0) {
        if (category.type === 1) {
          let tmp = Array.prototype.map.call(
            thiz.list.children,
            function (item, index) {
              if (item.type === category.type && item.id === category.id) {
                item = category
              }
              return item
            }
          )
          thiz.list.children = tmp
        } else if (category.type === 2) {
          let tmp = Array.prototype.map.call(
            thiz.list.children,
            function (item, index) {
              if (item.type === 1 && item.id === category.parentId) {
                item.categoryData = Array.prototype.map.call(
                  item.categoryData,
                  function (catg, index) {
                    if (
                      catg.type === category.type &&
                      catg.id === category.id
                    ) {
                      catg = category
                    }
                    return catg
                  }
                )
              }
              return item
            }
          )
          thiz.list.children = tmp
        }
      }
      thiz.$forceUpdate()
    },
    sceneChange(scene) {
      this.currentScene = scene
      //this.initParams(scene)
      //this.$store.dispatch('aiuiApp/setCurrentScene', scene)
    },
  },
}
</script>

<style>
.basic-info-edit-btn-inner {
  cursor: pointer;
  margin-left: 3%;
}

.basic-info-edit-btn-inner > span {
  font-size: 16px;
  color: #1890ff;
}

.el-div-question {
  width: 100%;
  margin: 0 auto;
}
</style>

<style scoped lang="scss">
.el-div-question {
  color: $warning;
}

.div-need-default-import > ul > li > p {
  color: $warning;
  font-size: 16px;
}
.div-need-default-import > ul:nth-of-type(2) > li > p > a {
  cursor: pointer;
}

.div-album {
  width: 15% !important;
  height: auto;
  position: relative;
  display: inline-flex;
  padding-top: 2%;
  padding-left: 2%;
  padding-bottom: 5%;
}
.div-os-collapse-recommend-inner {
  position: relative;
  width: 100% !important;
  max-width: 100%;
  min-width: 100%;
  left: 2%;
  padding-top: 1%;
  &-inline {
    width: 20% !important;
    display: inline;
    margin-bottom: 5%;
  }
}

.ib {
  flex-direction: column;
}
.basic-info-edit-btn {
  margin-left: 3%;
  :deep(> span) {
    font-size: 16px;
  }
}
.div-os-collapse {
  border-bottom: 1px solid #e4e7ed;
  margin-top: 2% !important;
  position: relative;
}

.div-add-ft {
  margin-top: 2% !important;
  position: relative;
  &-a {
    font-size: 20px;
  }
}
</style>
