<template>
  <el-dialog
    title="问答库设置"
    :visible="thresholdVisible"
    width="570px"
    @close="onClose"
    :modal="false"
    :append-to-body="true"
  >
    <div v-if="isFuzzy == 1">
      <p class="add-skill-title">阈值设置</p>
      <br />

      <p class="add-qa-threashold">
        阈值越低，用户的说法越容易匹配出结果；
        <br />阈值越高，对说法匹配的相似度要求越高，结果更准确。
        <br />
        <a :href="`${$config.docs}doc-50/`" target="_blank">查看阈值文档</a>
      </p>

      <div>
        <span class="threshold-label">阈值</span>
        <el-slider
          class="threshold-slider"
          v-model="threshold"
          :min="0.5"
          :max="0.99"
          :step="0.01"
          :show-input="true"
          @change="setThreshold"
        ></el-slider>
      </div>
      <p class="threshold-desc">
        产品中有多个技能，建议您所有技能均使用默认阈值，通过丰富语料的方式提高命中率。
      </p>
    </div>
    <p class="add-skill-title skill-version-title">问答库版本</p>
    <os-table
      class="mgb24 table-container"
      :tableData="tableData"
      @change="getVersion"
    >
      <el-table-column
        prop="outNumber"
        label="版本"
        width="130"
      ></el-table-column>
      <el-table-column label="版本说明" width="130">
        <template slot-scope="scope">
          <span v-if="!scope.row.updateLog">-</span>
          <el-popover
            v-else
            class="update-log"
            trigger="hover"
            placement="bottom-start"
            :content="scope.row.updateLog"
          >
            <div slot="reference">{{ scope.row.updateLog }}</div>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column prop="date" label="发布时间" width="130">
        <template slot-scope="scope">{{
          scope.row.createTime | date('yyyy-MM-dd')
        }}</template>
      </el-table-column>
      <el-table-column label="引用" width="55">
        <template slot-scope="scope">
          <el-radio
            v-model="radio"
            :label="scope.row.id"
            @change="setQaVersion(scope.row)"
            >&nbsp;</el-radio
          >
        </template>
      </el-table-column>
    </os-table>

    <span slot="footer" class="dialog-footer">
      <el-button @click="toggleThresholdVisible(false)">取消</el-button>
      <el-button type="primary" @click="thresholdConfirm">确定</el-button>
    </span>
  </el-dialog>
</template>
<script>
export default {
  props: {
    thresholdVisible: Boolean,
    configSkillItem: Object,
    globalThresholdChange: Boolean,
    globalThreshold: Number,
    // isRepository: Boolean,
    appId: String,
    currentScene: Object,
    qaConfig: Object,
    qaThresholdConfig: Object,
    skillThresholdConfig: Object,
  },
  data() {
    return {
      isFuzzy: 1,
      title: '技能设置',
      thresholdChange: false,
      threshold: 0.82, // 技能默认阈值 0.82, 问答默认阈值 0.81
      qaVersionChange: false,
      mallId: Number,
      outNumber: '',
      radio: '',
      tableData: {
        total: 0,
        page: 1,
        size: 3,
        list: [],
      },
    }
  },
  watch: {
    thresholdVisible(val) {
      this.$set(this.tableData, 'page', 1)
      if (val) {
        this.configThreshold(this.configSkillItem)
      }
    },
  },
  methods: {
    onClose() {
      this.toggleThresholdVisible(false)
    },
    toggleThresholdVisible(val) {
      this.$emit('thresholdVisibleChange', val)
    },
    setThreshold() {
      this.threshold = parseFloat(this.threshold.toFixed(2))
      this.thresholdChange = true
    },
    thresholdConfirm() {
      if (!this.thresholdChange && !this.qaVersionChange) {
        this.toggleThresholdVisible(false)
        return
      }
      if (this.thresholdChange) {
        let data = {
          repositoryId: this.configSkillItem.id,
          threshold: this.threshold,
        }
        this.qaThresholdConfig[this.configSkillItem.repositoryId] = data
      }
      if (this.qaVersionChange) {
        let data = {}
        if (this.qaConfig.hasOwnProperty(this.configSkillItem.id)) {
          console.log('111', this.qaConfig)
          console.log('222', this.configSkillItem)
          data = this.qaConfig[this.configSkillItem.id]
          data.repositoryId = this.configSkillItem.id
        } else {
          console.log('this.configSkillItem', this.configSkillItem)
          data = this.configSkillItem
          data.repositoryId = data.id
          data.operation = 'open'
        }
        data.verId = this.mallId

        console.log('this.qaVersionChange', data)
        // if (!this.isRepository) {
        //   data.outNumber = this.outNumber
        // }

        this.qaConfig[data.id] = data
        // this.skillList.sbusinessList.forEach((item, index) => {
        //   if (data.id == item.id) {
        //     data.outNumber = this.outNumber
        //     this.skillList.sbusinessList.splice(index, 0)
        //   }
        // })
      }
      this.$emit('change')
      // 关闭弹窗
      this.toggleThresholdVisible(false)
    },
    configThreshold(item) {
      let self = this
      this.configSkillItem = item
      this.thresholdChange = false
      this.qaVersionChange = false

      // 重置阈值默认值，技能默认阈值 0.82, 问答默认阈值 0.81
      let list = '',
        key = ''
      this.threshold = 0.81
      list = this.qaThresholdConfig
      key = item.id

      // 如果本地已修改该技能的阈值则显示修改的，否则向后台请求阈值
      if (this.globalThresholdChange) {
        self.threshold = this.globalThreshold

        // 修改了全局阈值，则判断单个阈值有没有修改，如果没有修改则return 显示全局的修改值
        if (!list.hasOwnProperty(key)) {
          return
        }
      }
      if (list.hasOwnProperty(key)) {
        self.threshold = list[key]['threshold']
        return
      }

      let data = {
        appid: this.appId,
        sceneId: this.currentScene.sceneBoxId,
        sceneName: this.currentScene.sceneBoxName,
      }
      data.id = item.id
      data.type = 'qa'
      this.isFuzzy = 1
      this.canUpdate = false
      this.getVersions(item)

      this.$utils.httpGet(this.$config.api.AIUI_APP_THRESHOLD, data, {
        success: (res) => {
          if (res.flag && res.data) {
            self.threshold = parseFloat(res.data)
          }
        },
      })
    },
    getVersions(item) {
      let self = this,
        tmp = {}
      self.tableData.loading = true
      this.$utils.httpGet(
        this.$config.api.AIUI_QA_VERSION,
        {
          repoId: item.id,
        },
        {
          success: (res) => {
            self.tableData.loading = false
            self.qaVersion = res.data.list
            self.tableData.total = res.data.count
            self.tableData.list = []
            tmp = self.qaVersion.find(function (arr, index) {
              if (self.isNumberEqual(item.outNumber, arr.outNumber)) {
                self.radio = arr.id
                let i = index / self.tableData.size
                self.currentPage6 = parseInt(i) + 1
                return arr
              }
            })
            this.getVersion(self.currentPage6)
          },
          error: (err) => {
            self.tableData.loading = false
          },
        }
      )
    },
    getVersion(page) {
      let self = this
      this.tableData.page = page || 1
      self.tableData.loading = true
      let pageIndex = page || self.tableData.page
      let from = (pageIndex - 1) * this.tableData.size
      let to = from + this.tableData.size
      self.tableData.list = self.qaVersion.slice(from, to)
      self.tableData.loading = false
    },
    isNumberEqual(num1 = '0.0.0', num2 = '0.0.0') {
      let arr1 = num1.split('.'),
        arr2 = num2.split('.')
      if (arr1[0] == arr2[0] && arr1[1] == arr2[1] && arr1[2] == arr2[2]) {
        return true
      }
    },
    setQaVersion(mall) {
      this.mallId = mall.id
      this.outNumber = mall.number
      this.qaVersionChange = true
    },
  },
}
</script>
<style lang="scss" scoped>
@import '../style.scss';
.table-container {
  :deep(.el-pagination) {
    overflow: auto;
  }
}
</style>
