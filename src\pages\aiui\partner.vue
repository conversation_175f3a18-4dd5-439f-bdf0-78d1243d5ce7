<template>
  <div class="os-scroll main-content">
    <section class="main-content-banner">
    </section>
    <section class="section section-2">
      <div class="section-title">成为合作伙伴，我们将提供</div>
      <div class="section-item">
        <ul>
          <li v-for="(item, index) in partnerData" :key="index" class="app">
            <img :src="item.icon" />
            <p class="section-2-title">{{item.title}}</p>
            <p class="section-2-text" v-html="item.text"></p>
          </li>
        </ul>
      </div>
    </section>
    <section class="section section-3" v-loading="loading">
      <span class="section-3-san"></span>
      <div class="section-title">合作申请</div>
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm" class="section-form">
        <el-form-item prop="name" label="企业名称:" class="section-form-box">
          <el-tooltip placement="top" class="section-form-tooltip">
            <div slot="content">企业申请说明：<br /><br />1. 正规的公司资质；<br />2. 二年及以上的该行业的服务经验；<br />3. 项目需符合相关法律、法规规定；
            </div>
            <span :style="span_style">?</span>
          </el-tooltip>
          <el-input v-model="ruleForm.name" placeholder="请输入你的团队或公司名称" />
        </el-form-item>
        <el-form-item label="企业网址:" prop="url">
          <el-input v-model="ruleForm.url" />
        </el-form-item>
        <el-form-item label="项目介绍:" prop="desc" class="section-form-box">
          <el-input v-model="ruleForm.desc" placeholder="请介绍你的项目和对讯飞能力的需求，以便我们熟悉需求，快速和你沟通。" type="textarea" rows="4" resize="none" />
          <span class="text-area-maxlength">160字内</span>
        </el-form-item>
        <el-form-item label="项目所在地:" prop="address">
          <el-input v-model="ruleForm.address" />
        </el-form-item>
        <el-form-item label="联系人:" prop="contact">
          <el-input v-model="ruleForm.contact" />
        </el-form-item>
        <el-form-item label="联系人职位:" prop="position">
          <el-input v-model="ruleForm.position"/>
        </el-form-item>
        <el-form-item label="手机号:" prop="phone">
          <el-input v-model="ruleForm.phone" />
        </el-form-item>
        <div class="section3-button" @click="submitForm('ruleForm')">申请合作</div>
      </el-form>
    </section>
  </div>
</template>

<script>
  export default {
    data () {
      return {
        loading: false,
        ruleForm: {
          name: '',
          url: '',
          desc: '',
          address: '',
          contact: '',
          position: '',
          phone: ''
        },
        rules: {
          name: [
            {required: true, message: '请输入企业名称', trigger: 'blur'},
            {min: 1, max: 100, message: '100个字符内', trigger: 'blur'}
          ],
          url: [
            {required: true, pattern: /^(https?:\/\/(([a-zA-Z0-9]+-?)+[a-zA-Z0-9]+\.)+[a-zA-Z]+)(\/.*)?$/, message: '请输入正确的企业网址', trigger: 'blur'},
            {min: 1, max: 100, message: '100个字符内', trigger: 'blur'}
          ],
          desc: [
            {required: true, message: '请输入项目介绍', trigger: 'blur'},
            {min: 1, max: 160, message: '160个字符内', trigger: 'blur'}
          ],
          address: [
            {required: true, message: '请输入项目所在地', trigger: 'blur'},
            {min: 1, max: 10, message: '10个字符内', trigger: 'blur'}
          ],
          contact: [
            {required: true, message: '请输入联系人', trigger: 'blur'},
            {min: 1, max: 10, message: '10个字符内', trigger: 'blur'}
          ],
          position: [
            {required: true, message: '请输入联系人职位', trigger: 'blur'},
            {min: 1, max: 10, message: '10个字符内', trigger: 'blur'}
          ],
          phone: [
            {required: true, message: '请输入手机号', trigger: 'blur'},
            {pattern: /^1[3456789]\d{9}$/,  message: '请输入正确的手机号', trigger: 'blur'}
          ],
        },
        partnerData: [
          {
            icon: require('@A/images/aiui/app-and-partner/partner-icon-1.jpg'),
            title: '领先的A.I.技术',
            text: '20年语音技术积累<br />与你共享'
          },
          {
            icon: require('@A/images/aiui/app-and-partner/partner-icon-2.jpg'),
            title: '专业的技术支持',
            text: '提供1对1的技术支持<br />专业化团队服务'
          },
          {
            icon: require('@A/images/aiui/app-and-partner/partner-icon-3.jpg'),
            title: '深度打造方案',
            text: '提供更多的行业资源<br />充分发挥A.I.优势'
          },
          {
            icon: require('@A/images/aiui/app-and-partner/partner-icon-4.jpg'),
            title: '优质客户介绍',
            text: '平台提供方案推广<br />共享优质客户'
          }
        ],
        span_style: 'display: inline-block;width: 15px;height: 15px;border-radius: 50%;background: #ccc;color: #fff;text-align: center;line-height: 15px;font-size: 12px'
      }
    },
    methods: {
      submitForm (formName) {
        let self = this
        this.$refs[formName].validate((valid) => {
          if (valid) {
            let data = {
              enterpriseName: self.ruleForm.name || '',
              enterpriseUrl: self.ruleForm.url || '',
              projectIntroduction: self.ruleForm.desc || '',
              projectLocation: self.ruleForm.address || '',
              linkMan: self.ruleForm.contact || '',
              linkPosition: self.ruleForm.position || '',
              tel: self.ruleForm.phone || '',
            }
            self.$utils.httpPost(self.$config.api.AIUI_PARTNER, data, {
              success: (res) => {
                self.$alert('我们会在1个工作日内联系您', '提交成功', {
                  confirmButtonText: '确定',
                  type: 'warning'
                }).then(() => {
                  self.loading = true
                  setTimeout(function(){
                    self.$refs[formName] && self.$refs[formName].resetFields()
                    self.loading = false
                  }, 200)
                }).catch(() => {
                  self.loading = true
                  setTimeout(function(){
                    self.$refs[formName] && self.$refs[formName].resetFields()
                    self.loading = false
                  }, 200)
                })
              },
              error: (err) => {},
            }) 
          }
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
  .main-content {
    min-width: 1200px;
    overflow: auto;
    &-banner {
      height: 400px;
      overflow: hidden;
      width: 100%;
      background: url('../../assets/images/aiui/app-and-partner/partner-banner.jpg') center center no-repeat;
      background-size: cover;
    }
    .section {
      width: 1200px;
      overflow: hidden;
      margin: 0 auto;
      padding: 50px 0;
      &-title {
        font-size: 36px;
        margin: 15px 0 35px;
        text-align: center;
      }
    }

    .section-2 {
      padding-bottom: 150px;
      ul {
        display: flex;
        justify-content: space-between;
        li {
          width: 25%;
          box-sizing: border-box;
          text-align: center;
          .section-2-title {
            font-size: 24px;
            color: #333;
            margin: 20px 0 15px;
          }
          .section-2-text {
            color: #999;
            font-size: 16px;
          }
        }
      }
    }
    .section-3 {
      background: #f0f2f4;
      width: 100%;
      position: relative;
      &-san {
        display: inline-block;
        width: 30px;
        height: 30px;
        background: #fff;
        transform: rotate(45deg) translate(50%, 0);
        position: absolute;
        top: -28px;
        left: 50%;
        margin-top: 2px;
        margin-left: -25px
      }
      .section-form {
        width: 870px;
        margin: 0 auto;
        .section-form-box {
          position: relative;
          .section-form-tooltip {
            position: absolute;
            top: 10px;
            left: 75px;
          }
          .text-area-maxlength {
            position: absolute;
            right: 5px;
            bottom: -5px;
            color: #d5d5d5;
            -moz-user-select: none;
            -webkit-user-select: none;
            -ms-user-select: none;
            -khtml-user-select: none;
            user-select: none;
          }
        }
      }
      .section3-button {
        color: #fff;
        background: #1784e9;
        width: 195px;
        height: 50px;
        line-height: 50px;
        text-align: center;
        margin: 60px auto 50px;
        cursor: pointer;
      }
    }
  }
</style>