<template>
  <div class="os-container" style="flex-direction: column">
    <div class="os-main" style="padding-top: 0px">
      <router-view></router-view>
    </div>
  </div>
</template>

<script>
import SSO from 'sso/sso.js'
import Vue from 'vue'
import { mapGetters } from 'vuex'

export default {
  components: {},
  computed: {
    ...mapGetters({
      limitCount: 'aiuiApp/limitCount',
    }),
  },
  watch: {},
  created() {},
  mounted() {
    let self = this
    if (window.userDetailInfo && window.userDetailInfo.subAccount) {
      window.logout = function () {
        let data = {
          subSessionId: self.$utils.getCookie('subSessionId'),
          sub_account_id: self.$utils.getCookie('sub_account_id'),
        }
        self.$utils.httpPost(self.$config.api.COOP_SUB_ACCOUNT_LOGOUT, data, {
          success: (res) => {
            if (res.flag) {
              self.$message.success('退出成功')
              self.$router.push({ name: 'sub-login' })
              localStorage.removeItem('firstEnterSkill', '1')
              localStorage.removeItem('firstEnterEntity', '1')
              localStorage.removeItem('firstEnterAuxiliary', '1')
            }
          },
          error: (err) => {},
        })
      }
    } else {
      window.logout = function () {
        console.log('logout')
        SSO.logout(function () {
          self.$utils.clearCookie()
          self.$store.dispatch('user/setUserInfo', null)
          self.$message.success('已退出登录')
          setTimeout(function () {
            window.location.href = location.hostname.match('studio')
              ? '/index-studio'
              : '/index-aiui'
          }, 1000)
        })
      }
    }
  },
}
</script>

<style lang="scss" scoped></style>
