<template>
  <div>
    <statisticHeader />
    <router-view></router-view>
  </div>
</template>
<script>
import { mapGetters } from 'vuex'
import statisticHeader from './header'

export default {
  data() {
    return {}
  },
  computed: {
    ...mapGetters({
      currentScene: 'aiuiApp/currentScene',
      app: 'aiuiApp/app',
      subAccountEditable: 'aiuiApp/subAccountEditable',
    }),
    appId() {
      return this.$route.params.appId
    },
  },
  components: {
    statisticHeader,
  },
}
</script>
<style lang="scss" scoped>
.content-container {
  padding: 14px 16px;
  max-height: calc(100vh - 134px);
  overflow: auto;
  background: $secondary-bgc;
}
</style>
