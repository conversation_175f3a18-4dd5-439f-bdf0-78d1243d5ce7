const { merge } = require('webpack-merge')
const { readEnv, getConditionalLoader, resolve } = require('./utils')
const config = readEnv('./.env.production')
const { CleanWebpackPlugin } = require('clean-webpack-plugin')
const MiniCssExtractPlugin = require('mini-css-extract-plugin')
const CssMinimizerPlugin = require('css-minimizer-webpack-plugin')
const WebpackBar = require('webpackbar')
const TerserPlugin = require('terser-webpack-plugin')
const CompressionPlugin = require('compression-webpack-plugin')
const { DefinePlugin } = require('webpack')
const webpackCommonConfig = require('./webpack.common.js')

//读取环境变量
module.exports = merge(webpackCommonConfig, {
  mode: 'production',
  // devtool: "cheap-module-source-map",
  devtool: false,
  output: {
    filename: 'js/[name].[fullhash:8].js',
    path: resolve('dist'),
    publicPath: '/',
  },
  module: {
    rules: [
      {
        test: /\.css$/i,
        use: [MiniCssExtractPlugin.loader, 'css-loader'],
      },
      {
        test: /\.s[ac]ss$/i,
        use: [
          MiniCssExtractPlugin.loader,
          {
            loader: 'css-loader',
            options: {
              sourceMap: false,
            },
          },
          {
            loader: 'postcss-loader',
            options: {
              sourceMap: false,
            },
          },
          {
            loader: 'sass-loader',
            options: {
              sourceMap: false,
              additionalData: `@import "@/assets/scss/variable.scss";`,
            },
          },
          getConditionalLoader(),
        ],
      },
    ],
  },
  plugins: [
    new DefinePlugin({
      BASE_URL: JSON.stringify('/'),
      'process.env': config,
    }),
    new MiniCssExtractPlugin({
      filename: 'css/[name]_[contenthash:8].css',
      // ignoreOrder: true,
    }),
    new CleanWebpackPlugin(),
    // 进度条
    new WebpackBar({
      reporters: ['fancy', 'profile'],
      profile: true,
    }),
    ...(process.env.APP_GZIP === 'ON'
      ? [
          new CompressionPlugin({
            filename: '[path][base].gz',
            threshold: 10240,
            minRatio: 0.8,
          }),
        ]
      : []),
  ],
  optimization: {
    moduleIds: 'deterministic',
    runtimeChunk: {
      name: 'manifest',
    },
    minimize: true,
    minimizer: [
      new CssMinimizerPlugin({
        minimizerOptions: {
          preset: [
            'default',
            {
              discardComments: { removeAll: true },
            },
          ],
        },
      }),
      new TerserPlugin({
        terserOptions: {
          format: {
            comments: false,
          },
        },
        extractComments: false,
      }),
    ],
    splitChunks: {
      chunks: 'async',
      minSize: 30000,
      minChunks: 1,
      maxAsyncRequests: 5,
      maxInitialRequests: 3,
      name: false,
      cacheGroups: {
        vendor: {
          name: 'vendor',
          chunks: 'initial',
          priority: -10,
          reuseExistingChunk: false,
          test: /node_modules\/(.*)\.js/,
        },
        // styles: {
        //   name: 'styles',
        //   test: /\.(scss|css)$/,
        //   chunks: 'all',
        //   minChunks: 1,
        //   reuseExistingChunk: true,
        //   enforce: true,
        // },
      },
    },
  },
})
