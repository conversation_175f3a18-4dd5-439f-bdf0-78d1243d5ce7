<template>
  <div class="container">
    <ul
      v-if="data && data.length > 0"
      class="confirm-list"
      :style="{ paddingBottom: data.length < max ? '0px' : '15px' }"
    >
      <li v-for="(item, index) in data" :key="item.answer">
        <span class="number-label">{{ index + 1 }}</span>
        <!-- <span>{{ item }}</span> -->
        <inteligient-rich-input
          :placeholder="editPlaceholder"
          :value="{
            text: item.answer,
            labels: item.labels,
            changed: item.changed,
          }"
          :showAdd="false"
          :showSwitch="false"
          :disabled="disabled"
          :edit="true"
          :editIndex="index"
          @onEdit="onEdit"
          @change="onChange(index)"
          :saveOnBlur="true"
        >
        </inteligient-rich-input>
        <i
          class="delete ic-r-delete"
          @click="onDelClick(item)"
          v-if="!disabled"
        ></i>
      </li>
    </ul>
    <div class="confirm-adder" v-show="data.length < max">
      <span class="number-label">{{ data.length + 1 }}</span>
      <!-- <el-input
        v-model="text"
        @change="onChange"
        placeholder=" 使用“{”符号引用必选槽位，最多添加5条，每条不超过50字，回车新增。例：你是不是要问{city}的天气"
      ></el-input> -->
      <!-- <inteligient-input
        v-model="text"
        @onAdd="onAdd"
        :placeholder="placeholder"
      ></inteligient-input> -->
      <inteligient-rich-input
        v-model="textObj"
        :showAdd="true"
        :showSwitch="false"
        @onAdd="onAdd"
        :placeholder="placeholder"
        ref="intelInput"
        key="intentionConfirm"
        :disabled="disabled"
      >
      </inteligient-rich-input>
    </div>
  </div>
</template>

<script>
import InteligientInput from './referSlots/inteligientInput.vue'
import InteligientRichInput from './referSlots/inteligientRichInput.vue'

export default {
  components: { InteligientInput, InteligientRichInput },
  name: 'intention-confirm-text-adder',
  props: {
    data: Array,
    disabled: Boolean,
    slotNames: Array,
    reg: {
      default: '',
    },
    warning: {
      type: String,
      default: '输入有误',
    },
    placeholder: String,
    editPlaceholder: String,
    max: {
      type: Number,
      default: 5,
    },
  },
  data() {
    return {
      // text: '',
      textObj: { text: '', labels: [] },
    }
  },
  methods: {
    checkIntentionSlots(val) {
      let reg = /\{(.*?)\}/g
      let tmp = val.match(reg)
      if (!tmp) {
        return true
      }
      if (!this.slotNames.length) {
        this.$message.warning('当前意图无语义槽，请勿使用花括号')
        return false
      }
      for (let i = 0; i < tmp.length; i++) {
        let slot = tmp[i].substring(1, tmp[i].length - 1)
        if (this.slotNames.indexOf(slot) == -1) {
          this.$message.warning(
            '花括号内参数名称需和当前意图下的必选语义槽名相同'
          )
          return false
        }
      }
      return true
    },
    onDelClick(text) {
      this.$emit('del', text)
    },
    // onChange() {
    //   this.$emit('add', this.text)
    //   this.text = ''
    // },
    onAdd(textObj) {
      const val = textObj.text.trim()
      if (!val) {
        return this.$message.warning('输入不能为空')
      }
      if (val.replace(/[^\{\}]/g, '').replace(/(\{\})/g, '')) {
        return this.$message.warning('花括号必须成对出现')
      }
      const idx = this.data.findIndex((item) => item.answer === val)
      if (idx > -1) {
        return this.$message.warning('不能重复添加')
      }
      if (this.reg && !this.reg.test(val)) {
        return this.$message.warning(this.warning)
      }
      if (!this.checkIntentionSlots(textObj.text)) {
        return
      }
      this.$emit('add', { answer: textObj.text, labels: textObj.labels })
      // this.$refs.intelInput.clearHTML()
      // this.textObj = { text: '', labels: [] }
    },

    resetInitialStatus() {
      this.$refs.intelInput.clearHTML()
      this.textObj = { text: '', labels: [] }
    },

    onEdit(textObj, index) {
      const val = textObj.text.trim()
      if (!val) {
        return this.$message.warning('输入不能为空')
      }
      if (val.replace(/[^\{\}]/g, '').replace(/(\{\})/g, '')) {
        return this.$message.warning('花括号必须成对出现')
      }
      // const idx = this.data.findIndex((item) => item.answer === val)
      // if (idx > -1) {
      //   return this.$message.warning('不能重复添加')
      // }
      if (this.reg && !this.reg.test(val)) {
        return this.$message.warning(this.warning)
      }
      if (!this.checkIntentionSlots(textObj.text)) {
        return
      }
      this.$emit(
        'edit',
        { answer: textObj.text, labels: textObj.labels },
        index
      )
    },
    onChange(index) {
      console.log('receive onChange', index)
      if (index !== -1) {
        this.$emit('change', index)
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.container {
  width: 100%;
  border: 1px solid #d5d8de;
  padding: 0 16px;
  .confirm-adder {
    display: flex;
    align-items: center;
    margin-top: 18px;
  }
  .confirm-list {
    padding-top: 15px;
    > li {
      display: flex;
      align-items: center;
      position: relative;
      padding-right: 20px;
      &:hover {
        .delete {
          display: block;
        }
      }
      .delete {
        position: absolute;
        right: 0;
        color: #b8babf;
        cursor: pointer;
        display: none;
        color: #1784e9;
        font-size: 20px;
        // &:hover {
        //   color: #1784e9;
        // }
      }
    }
    li + li {
      margin-top: 18px;
    }
    margin-bottom: 0;
  }
  .number-label {
    margin-right: 20px;
    color: #b8babf;
  }
}
</style>
<style lang="scss" scoped>
.container {
  :deep(.el-input__inner) {
    border: none;
  }
}
</style>
