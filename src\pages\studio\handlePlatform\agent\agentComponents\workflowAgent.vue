<template>
  <div class="workflow_agent_wrapper" v-loading="frameLoading">
    <!-- <el-button @click="drawerOpen = true">打开</el-button> -->
    <iframe
      :src="iframeUrl"
      ref="childFrame"
      frameborder="0"
      style="width: 100%; height: 100%; border: none"
      @load="onIframeLoad"
      @error="onIframeError"
    ></iframe>
    <div class="custom_drawer" :class="{ drawer_open: drawerOpen }">
      <div class="drawer_header">
        <span class="drawer_title">意图管理</span>

        <i class="el-icon-close drawer-close" @click="drawerOpen = false"></i>
      </div>
      <div class="drawer_content">
        <IntentManage></IntentManage>
      </div>
    </div>
  </div>
</template>

<script>
import IntentManage from './intentManage.vue'
import { bus } from '@U/bus'

const DEBUG_AGENT_FLOW = 'DEBUG_AGENT_FLOW'
const CONSTRUCT_AGENT_FLOW = 'CONSTRUCT_AGENT_FLOW'

export default {
  name: 'WorkflowAgent',
  props: {
    workflowPluginInfo: {
      type: Object,
      default: () => {},
    },
  },
  components: {
    IntentManage,
  },
  data() {
    return {
      pluginId: '',
      drawerOpen: false,
      frameLoading: true,
    }
  },
  created() {
    this.$store.dispatch('aiuiStore/setHideAiuiMenu', true)
    this.pluginId = this.$route.params.agentId
  },

  mounted() {
    // 监听来自子窗口的消息
    window.addEventListener('message', this.handleMessageFromChild)

    bus.$on(DEBUG_AGENT_FLOW, (val) => {
      this.sendToChild({ type: DEBUG_AGENT_FLOW })
    })
    bus.$on(CONSTRUCT_AGENT_FLOW, (val) => {
      this.sendToChild({ type: CONSTRUCT_AGENT_FLOW })
    })
  },

  computed: {
    iframeUrl() {
      //       return `/agent/flow/${this.$route.params.boardId}`
      return `/agent/flow/${this.$route.params.boardId}`
    },
  },
  methods: {
    onIframeLoad() {
      console.log('Child iframe loaded successfully')
      this.frameLoading = false
    },
    onIframeError() {
      console.error('Failed to load child iframe')
    },
    sendToChild(message) {
      // 确保 iframe 已加载
      const iframe = this.$refs.childFrame
      if (!iframe) return

      // 发送消息到子窗口
      iframe.contentWindow.postMessage(message, '*') // 生产环境应该指定具体的 origin 而不是 '*'
    },
    handleMessageFromChild(event) {
      // 验证消息来源（生产环境应该检查 event.origin）
      // if (event.origin !== "https://expected-origin.com") return;

      const message = event.data

      console.log('message come from child: ', message)
      if (!message) {
        return
      }
      const { type } = message
      switch (type) {
        case 'SHOULD_CONSTRUCT_AGENT_FLOW':
          this.contructAgentFlow()
          break
        case 'INTENT_CLICK':
          this.drawerOpen = true
          break
        case 'LINK_CREATE_SOURCE':
          window.open('/studio/source', '_blank')
          break
        case 'LINK_CREATE_REPO':
          window.open('/studio/qaBank', '_blank')
          break
      }
    },

    contructAgentFlow() {
      bus.$emit('AGENT_FLOW_LOADING', true)

      // 先调用构建意图的接口
      return new Promise((resolve, reject) => {
        this.$utils.httpPost(
          `${this.$config.api.AGENT_WORK_FLOW_INTENT_BUILD}?pluginId=${this.$route.params.agentId}`,
          JSON.stringify({}),
          {
            config: {
              headers: {
                'Content-Type': 'application/json;charset=UTF-8',
              },
            },
            success: (res) => {
              if (res.code === 0 || res.code === '0') {
                resolve(res.data)
              } else {
                reject(new Error(res.desc || '构建意图失败'))
              }
            },
            error: (err) => {
              reject(err)
            },
          }
        )
      })
        .then((buildIntentResult) => {
          // 构建意图成功后，再调用发布工作流的接口
          return new Promise((resolve, reject) => {
            this.$utils.httpGet(
              this.$config.api.AGENT_WORK_FLOW_PUBLISH,
              {
                id: this.$route.params.boardId,
              },
              {
                success: (res) => {
                  if (res.code === 0 || res.code === '0') {
                    resolve({
                      buildIntentResult,
                      publishResult: res.data,
                    })
                  } else {
                    reject(new Error(res.desc || '发布工作流失败'))
                  }
                },
                error: (err) => {
                  reject(err)
                },
              }
            )
          })
        })
        .then((results) => {
          this.$message.success('构建成功')
          // 发送按钮加载状态为false
          bus.$emit('AGENT_FLOW_LOADING', false)
          return results
        })
        .catch((error) => {
          this.$message.error(error?.desc || error?.message || '操作失败')
          // 发送按钮加载状态为false（即使出错也需要关闭加载状态）
          bus.$emit('AGENT_FLOW_LOADING', false)
          return Promise.reject(error)
        })
    },
    publishFlow() {},
    async publishIntent() {
      return Promise.resolve()
    },
  },
  beforeDestroy() {
    this.$store.dispatch('aiuiStore/setHideAiuiMenu', false)

    // 组件销毁前移除事件监听
    window.removeEventListener('message', this.handleMessageFromChild)
  },
}
</script>

<style scoped lang="scss">
.workflow_agent_wrapper {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.custom_drawer {
  position: fixed;
  bottom: 0;
  right: -500px;
  width: 500px;
  height: calc(100vh - 63px);
  background: transparent;
  z-index: 1;
  transition: right 0.3s ease;
  background-color: #fff;
  padding: 10px 15px 15px 15px;
  box-shadow: 0 12px 16px -4px #10182814, 0 4px 6px -2px #10182808;
  &.drawer_open {
    right: 0;
  }

  .drawer_header {
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
    .drawer_title {
      font-size: 16px;
      font-weight: 600;
      white-space: nowrap;
      max-width: 280px;
      overflow: hidden;
      -o-text-overflow: ellipsis;
      text-overflow: ellipsis;
    }
    .drawer-close {
      font-size: 18px;
      cursor: pointer;
    }
  }

  .drawer_content {
    height: calc(100% - 50px);
    overflow-y: auto;
  }
}
</style>
