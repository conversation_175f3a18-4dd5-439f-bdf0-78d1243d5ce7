<template>
  <div class="header-right">
    <div v-if="qa && qa.operator">
      <p class="header-save-time">
        最近由<span class="text-blod" style="color: #262626">{{
          qa.operator
        }}</span>
      </p>
      <p
        v-if="qa.updateTime"
        class="header-save-time"
        style="text-align: right"
      >
        保存于{{ qa.updateTime | time }}
      </p>
    </div>
    <template v-else>
      <span class="header-save-time" v-if="qa.updateTime"
        >最近保存 {{ qa.updateTime | time }}</span
      >
    </template>
    <span class="header-qa">
      <el-tooltip
        class="item"
        effect="dark"
        content="问答库修改后需要重新构建"
        placement="bottom"
      >
        <i class="el-icon-question" />
      </el-tooltip>
    </span>
    <el-button
      size="small"
      type="primary"
      @mousedown.native.prevent="beforeSturcture"
      :loading="structureLoading"
    >
      {{ structureLoading ? '构建中...' : '构建问答库' }}
    </el-button>
    <!-- <el-dialog title="构建设备人设" :visible.sync="dialogShow" width="480px">
      <el-form
        :model="form"
        ref="form"
        label-width="50px"
        class="demo-ruleForm"
        @submit.native.prevent
      >
        <el-form-item
          label="备注"
          prop="content"
          :rules="[
            { required: true, message: '备注不能为空' },
            {
              min: 1,
              max: 200,
              message: '长度在 1 到 200 个字符',
              trigger: 'blur',
            },
          ]"
        >
          <el-input
            type="textarea"
            v-model="form.content"
            autocomplete="off"
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            size="medium"
            style="margin-bottom: 32px; min-width: 104px"
            @click="submitForm"
            >提交</el-button
          >
          <el-button size="medium" style="min-width: 104px" @click="cancel"
            >取消</el-button
          >
        </el-form-item>
      </el-form>
    </el-dialog> -->
    <el-dialog title="提示" :visible.sync="structResDialogShow" width="480px">
      <p v-for="(item, index) of structRes" :key="index">{{ item }}</p>
      <span slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          style="margin-top: 8px; min-width: 104px"
          @click="structResDialogShow = false"
          >知道了</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'studioQaBankHeaderRight',
  data() {
    return {
      mohu: true,
      structureLoading: false,
      checkCount: 0,
      qa: {},
      dialogShow: false,
      form: {
        content: '',
      },
      structResDialogShow: false,
      structRes: [],
    }
  },
  computed: {
    ...mapGetters({
      originalQa: 'studioQa/qa',
      subAccount: 'user/subAccount',
    }),
  },
  watch: {
    originalQa: function (val, oldVal) {
      this.qa = this.$deepClone(val)
    },
  },
  created() {
    if (this.$store.state.studioQa.id) {
      this.qa = this.$deepClone(this.$store.state.studioQa.qa)
    }
  },
  methods: {
    beforeSturcture() {
      if (this.qa.type == 0) {
        this.structure()
      } else if (this.qa.type == 3) {
        this.structureKeyQa()
      }
    },
    // submitForm() {
    //   let self = this
    //   this.$refs.form.validate((valid) => {
    //     if (valid) {
    //       self.structure(self.form.content)
    //     }
    //   })
    // },
    structure(content) {
      let self = this
      let data = {
        repositoryId: this.$store.state.studioQa.id,
      }
      if (content) {
        data.content = content
      }
      this.structureLoading = true
      this.$utils.httpPost(this.$config.api.STUDIO_QA_STRUCT_OR_PUBLISH, data, {
        success: (res) => {
          self.$message.success('提交成功，正在构建...')
          self.checkStatus(res.data.id)
        },
        error: (err) => {
          self.structureLoading = false
        },
      })
    },

    structureKeyQa(content) {
      let self = this
      let data = {
        qaId: this.$route.params.qaId,
      }
      if (content) {
        data.content = content
      }
      this.structureLoading = true

      this.$utils.httpPost(this.$config.api.STUDIO_KEY_QA_STRUCT, data, {
        success: (res) => {
          if (res.code == 0) {
            self.$message.success('构建成功')
          } else {
            self.$message.error(res.desc)
          }
          self.structureLoading = false
        },
        error: (err) => {
          self.structureLoading = false
        },
      })
    },

    checkStatus(id) {
      let self = this
      this.checkCount += 1
      this.$utils.httpGet(
        this.$config.api.STUDIO_QA_STRUCT_OR_PUBLISH_STATUS,
        {
          id,
        },
        {
          success: (res) => {
            if (String(res.data.ok) === '1') {
              if (self.structureLoading) {
                self.$message.success('构建成功')
              }
              self.structureLoading = false
              self.checkCount = 0
            } else if (String(res.data.ok) === '-1') {
              if (self.structureLoading) {
                self.$message.error('构建失败')
              }
              self.structureLoading = false
              self.checkCount = 0
            } else {
              if (self.checkCount < 300) {
                setTimeout(function () {
                  self.checkStatus(id)
                }, 2000)
              } else {
                if (self.structureLoading) {
                  self.$message.error('构建失败')
                }
                self.structureLoading = false
                self.checkCount = 0
              }
            }
          },
          error: (err) => {
            console.log('page=>>')
            console.log(err)
          },
        }
      )
    },
    cancel() {
      this.$refs.form.resetFields()
      this.dialogShow = false
    },
    // update() {
    //   this.$utils.httpGet(
    //     this.$config.api.STUDIO_SKILL_DETAIL,
    //     {
    //       skillId: this.$route.params.skillId,
    //     },
    //     {
    //       success: (res) => {
    //         this.qa.updateTime = res.data.updateTime
    //       },
    //     }
    //   )
    // },
  },
  components: {},
}
</script>

<style lang="scss">
.header-right {
  display: flex;
  align-items: center;
}
.header-save-time {
  font-size: 12px;
  color: $grey5;
  margin-right: 24px;
}
// .header-more {
//   font-size: 16px;
//   color: $grey5;
//   margin-right: 24px;
//   cursor: pointer;
// }
.header-qa {
  font-size: 16px;
  color: $grey4;
  // margin-right: 8px;
  cursor: pointer;
}
</style>
