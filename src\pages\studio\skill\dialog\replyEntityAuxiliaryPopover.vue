<template>
  <div
    class="
      reply-entity-auxiliary-popover
      el-popover el-popper
      el-popover--plain
      editor-select-popover
    "
    v-if="variablePopover.show"
    v-clickoutside="closePopover"
    :style="popperStyle"
    visible-arrow="false"
    x-placement="bottom-start"
  >
    <div
      class="editor-select-popover__body"
      ref="replyPopover"
      @keyup.up="handleUp"
      @keyup.down="handleDown"
      @keyup.enter="handleEnter"
    >
      <input
        class="focus-input"
        ref="focusInput"
        style="height: 0; overflow: hidden"
      />
      <div class="editor-select-popover-list" ref="popper" v-loading="loading">
        <el-scrollbar
          tag="ul"
          class="el-select-dropdown__wrap"
          view-class="el-select-dropdown__list"
          ref="scrollbar"
          style="height: 210px"
          v-show="slots.length > 0 && !loading"
        >
          <li
            class="editor-select-popover-item"
            :ref="'selectItem' + index"
            :class="{ 'highlight-item': index === selectIndex }"
            v-for="(item, index) in slots"
            :key="index"
            @click="selectItem(item)"
          >
            <span class="txt-ellipsis-nowrap" :title="item.slotName">{{
              item.slotName
            }}</span>
            <span
              class="txt-ellipsis-nowrap"
              style="float: right; width: 116px"
              :title="item.entityName"
            >
              {{ item.entityName && (item.entityType === 1 ? '@' : '#')
              }}{{ item.entityName }}</span
            >
          </li>
        </el-scrollbar>
        <div class="el-table__empty-block" v-if="!slots.length">
          <span class="el-table__empty-text">暂无数据</span>
        </div>
      </div>
    </div>
    <div x-arrow="" class="popper__arrow" style="left: 13.5px"></div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
export default {
  name: 'reply-entity-auxiliary-popover',
  props: {
    variablePopover: {
      type: Object,
      default: () => ({
        show: false,
        searchVal: '',
      }),
    },
    inAddInput: false,
  },
  data() {
    return {
      loading: false,
      rect: {
        top: 0,
        left: 0,
        width: 0,
      },
      initSlots: [],
      slots: [],
      selectIndex: -1,
    }
  },
  computed: {
    popperStyle() {
      if (this.rect) {
        if (!this.inAddInput) {
          return {
            top: `${this.rect.top + 15}px`,
            left: `${this.rect.left}px`,
          }
        }
        return {
          top: `${this.rect.top + 30}px`,
          // left: `${ this.rect.left + 20 + this.variablePopover.cursorPos * 8}px`
          left: `${this.rect.left - 20}px`,
        }
      } else {
        return {
          display: `none`,
        }
      }
    },
  },
  watch: {
    'variablePopover.rect': function () {
      this.rect = JSON.parse(JSON.stringify(this.variablePopover.rect))
    },
    'variablePopover.show': function (val) {
      if (val) {
        this.getAllSlots()
        this.selectIndex = 0
      } else {
        this.$emit('setSlot')
      }
    },
    'variablePopover.searchVal': function (val, oldVal) {
      if (val !== oldVal) {
        this.filterHandle()
      }
    },
    'slots.length': function (val, oldVal) {
      if (!val && this.variablePopover.searchVal) {
        this.variablePopover.show = false
      }
    },
    selectIndex(val) {
      if (!val) return
      let num = val - 1
      this.scrollToOption(this.$refs['selectItem' + num])
    },
  },
  methods: {
    closePopover() {
      this.variablePopover.show = false
    },
    getAllSlots() {
      let self = this
      self.loading = true
      let businessId = self.$route.params.skillId
      let intentId = self.$route.params.intentId
      this.$utils.httpGet(
        self.$config.api.STUDIO_INTENT_ALL_SLOTS,
        {
          businessId: businessId,
          intentId: intentId,
        },
        {
          success: (res) => {
            self.loading = false
            self.initSlots = res.data.slots || []
            self.filterHandle()
          },
          error: (err) => {
            self.loading = false
          },
        }
      )
    },
    selectItem(item) {
      this.$emit('setSlot', item.slotName)
      this.variablePopover.show = false
    },
    filterHandle() {
      let self = this
      if (!self.variablePopover.searchVal) {
        return (self.slots = JSON.parse(JSON.stringify(self.initSlots)))
      }
      self.slots = self.initSlots.filter((item) => {
        return item.slotName.indexOf(self.variablePopover.searchVal) > -1
      })
    },
    handleDown() {
      let self = this
      window.getSelection().removeAllRanges()
      self.$refs.focusInput.focus()
      this.selectIndex++
      if (this.selectIndex === this.slots.length) {
        this.selectIndex = 0
      }
    },
    handleUp() {
      this.$refs.focusInput.focus()
      this.selectIndex--
      if (this.selectIndex < 0) {
        this.selectIndex = this.slots.length - 1
      }
    },
    inputFocus() {
      this.$refs.focusInput.focus()
    },
    handleEnter() {
      this.selectItem(this.slots[this.selectIndex])
    },
    scrollToOption(option) {
      const menu =
        this.$refs.popper &&
        this.$refs.popper.querySelector('.el-select-dropdown__wrap')
      this.$utils.scrollIntoView(menu, option[0])
      this.$refs.scrollbar && this.$refs.scrollbar.handleScroll()
    },
  },
}
</script>
<style lang="scss">
.reply-entity-auxiliary-popover {
  .highlight-item {
    background: #e3f0fc;
  }
  .focus-input {
    height: 0;
    display: inherit;
    border: none;
    width: 100%;
  }
}
</style>
