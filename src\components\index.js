/**
 * 注册全局组件
 */
import Header from './header.vue'
import aiuiHeader from './aiuiHeader.vue'
import Menu from './menu.vue'
import Page from './page.vue'
import CustomPage from './customPage.vue'
import Divider from './divider.vue'
import PageLabel from './pageLabel.vue'
import SkillDebug from './skillDebug.vue'

import CharacterDebug from './characterDebug.vue'
import QaDebug from './qaDebug.vue'
import AppDebug45 from './appDebug45/index.vue'

import JsonView from './jsonView.vue'
import StudioSkillHeaderRight from './studioSkillHeaderRight.vue'

import StudioAgentHeaderRight from './studioAgentHeaderRight.vue'
import StudioAgentHeaderRightLegacy from './studioAgentHeaderRightLegacy.vue'

import StudioCharacterHeaderRight from './studioCharacterHeaderRight.vue'
import StudioQaBankHeaderRight from './studioQaBankHeaderRight.vue'
import Utterance from './utterance.vue'
import PhotoUpload from './photoUpload.vue'
import StudioSkillMenuHead from './studioSkillMenuHead.vue'
import StudioAgentMenuHead from './studioAgentMenuHead.vue'
import StudioCharacterMenuHead from './studioCharacterMenuHead.vue'
import StudioQaBankMenuHead from './studioQaBankMenuHead.vue'
import PageLeaveTips from './pageLeaveTips.vue'
import GiveUpSave from './giveUpSave.vue'
import SkillSimpleItem from './skillSimpleItem.vue'
import Reset from './reset.vue'
import CharacterSimpleItem from './characterSimpleItem.vue'
import ChartLine from './chartLine.vue'
import chartBar from './chartBar.vue'
import ModifierSelect from './modifierSelect.vue'
import RadioTab from './radioTab.vue'

const components = [
  Header,
  aiuiHeader,
  Menu,
  Page,
  CustomPage,
  Divider,
  PageLabel,
  SkillDebug,
  CharacterDebug,
  QaDebug,
  AppDebug45,
  JsonView,
  StudioSkillHeaderRight,
  StudioAgentHeaderRight,
  StudioAgentHeaderRightLegacy,
  StudioCharacterHeaderRight,
  StudioQaBankHeaderRight,
  Utterance,
  PhotoUpload,
  StudioSkillMenuHead,
  StudioAgentMenuHead,
  StudioCharacterMenuHead,
  StudioQaBankMenuHead,
  PageLeaveTips,
  GiveUpSave,
  SkillSimpleItem,
  Reset,
  CharacterSimpleItem,
  ChartLine,
  chartBar,
  ModifierSelect,
  RadioTab,
]

export default {
  install(Vue, opts = {}) {
    components.map((component) => {
      Vue.component(component.name, component)
    })
  },
}
