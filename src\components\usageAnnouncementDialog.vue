<template>
  <el-dialog
    :visible.sync="dialog.show"
    width="600px"
    :show-close="false"
    title="使用公告"
  >
    <span style="line-height: 26px">
      <p>为了成功接入三方智能体，请注意：</p>
      <p>
        1.当前版本，不论您接入的是智能体还是工作流，平台将仅向三方平台传递用户Query（不带对话历史），并将三方智能体返回的Answer用作回复语。
      </p>

      <p>
        2.如果您需要接入星辰平台的工作流，建议您不要在星辰工作流的开始节点和结束节点之间定义消息下发节点，以避免下发结果解析错误。
      </p>

      <p style="text-align: right; margin: 20px 0">2025年6月12日</p>
    </span>

    <span slot="footer" class="dialog-footer">
      <el-button @click="dialog.show = false" type="default"
        >我已知晓</el-button
      >
    </span>
  </el-dialog>
</template>
<script>
export default {
  props: {
    dialog: Object,
  },
  data() {
    return {}
  },
}
</script>
<style lang="scss" scoped>
:deep(.el-dialog__body) {
  padding-top: 0;
}
:deep(.el-dialog__footer) {
  text-align: center;
}
</style>
