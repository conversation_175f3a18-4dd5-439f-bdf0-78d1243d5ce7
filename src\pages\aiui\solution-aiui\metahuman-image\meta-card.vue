<template>
  <div class="metahuman-card" :class="humanData.isSuper?'super-card':''">
    <div class="hover-area">
      <!-- 添加蒙版层节点 -->
      <div class="hover-mask">
        <div class="auth-btn" @click="openAuth">
          申请授权
        </div>
        <img 
          :src="require('/src/assets/images/solution/metahuman-image/look.png')" 
          class="look-detail"
          @click="lookImageDetail"
        />
      </div>
      <div class="card-field":class="humanData.isSuper?'super-field':''" >
        {{ humanData.isSuper?'超拟人': humanData.field }}
      </div>
      <div class="card-image">
        <img :src="humanData.imgUrl" :alt="humanData.name" :class="humanData.needAdjust?'human-top':''"/>
      </div>
    </div>
    <div class="card-info">
      <p style="display: flex;align-items: center;margin-bottom: 8px;">
        <span class="human-name">{{ humanData.name }}</span>
        <span class="human-id">ID：{{ humanData.anchor_id }}</span>
        <i
          class="id-copy"
          title="复制"
          @click="copyId"
        ></i>
      </p>
      <p style="display: flex;align-items: center;">
        <span
          v-for="(item,index) in humanData.labels"
          :key="index"
          class="card-label"
        >
          {{ item }}
        </span>
      </p>
    </div>
  </div>
</template>
<script>
export default {
  name:'',
  props:{
    humanData:{
      type:Object,
      default(){
        return {}
      }
    }
  },
  data() {
    return {
    }
  },
  methods:{
    copyId(){
      this.$utils.copyClipboard(this.humanData.anchor_id)
      this.$message.success('已复制到剪切板')
    },
    lookImageDetail(){
      this.$emit('showDetail',this.humanData.anchor_id)
    },
    openAuth(){
      window.open(`/solution/apply/37?fromImage=${this.humanData.anchor_id}`)
    }
  },
}
</script>
<style lang="scss" scoped>
.metahuman-card{
  width: 100%;
  height: 426px;
  background: linear-gradient(180deg,#e7f1ff, #ffffff 91%);
  border-radius: 13px;
  box-shadow: 0px 5px 19px 0px rgba(231,231,231,0.50); 
  .card-field{
    width: 85px;
    height: 32px;
    line-height: 32px;
    text-align: center;
    font-size: 15px;
    font-family: PingFang SC, PingFang SC-Medium;
    font-weight: 500;
    color: #ffffff;
    border-radius: 13px 0px 13px 0px;
    background: linear-gradient(260deg, #87bdfd, #54a5fe);   
    box-shadow: 0px 5px 19px 0px rgba(231,231,231,0.50); 
  }
  .super-field{
    background: linear-gradient(260deg, #9C8BFE, #7A9DF1); 
  }
  .card-image{
    height: 318px;
    overflow: hidden;
    img{
      width: 100%;
    }
    .human-top{
      position: relative;
      bottom: 52px;
    }
  }

  .card-info{
    height: 76px;
    background: #ffffff;
    padding-top: 8px;
    padding-left: 14px;
    .human-name{
      font-size: 19px;
      font-family: PingFang SC, PingFang SC-Semibold;
      font-weight: 600;
      padding-left: 4px;
      color: #535353;
    }
    .human-id{
      margin-left: 6px;
      font-size: 14px;
      font-family: PingFang SC, PingFang SC-Regular;
      font-weight: 400;
      color: #777676;
      // letter-spacing: 1px;
    }
    .id-copy{
      width: 12px;
      height: 12px;
      margin-left: 4px;
      cursor: pointer;
      background: url('~@A/images/solution/metahuman-image/copy.png') no-repeat;
      background-size: cover;
    }
    .card-label{
      background: #f6f6f6;
      padding: 0 6px;
      height: 21px;
      border-radius: 4px;
      line-height: 21px;
      font-size: 12px;
      color: #5e5e5e;
    }
    .card-label + .card-label{
      margin-left: 12px;
    }
  }

  .hover-area{
    position: relative;
  }
  .hover-mask {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.24); /* 黑色半透明 */
    opacity: 0; /* 默认隐藏 */
    transition: background 0.3s ease; /* 添加过渡效果 */
    border-radius: 13px 13px 0 0;
    display: flex;
    align-items: center;
    justify-content: center;
    .auth-btn{
      font-family: PingFang SC, PingFang SC-Medium;
      color: #ffffff;
      width: 76px;
      height: 32px;
      line-height: 32px;
      text-align: center;
      font-size: 18px;
      font-weight: 500;
      cursor: pointer;
      z-index: 3;
    }
    .look-detail{
      width: 30px;
      height: 19px;
      position: absolute;
      bottom: 10px;
      right: 14px;
      cursor: pointer;
      z-index: 2;
    }
  }

  /* 鼠标移入时显示蒙版 */
  .hover-area:hover .hover-mask {
    opacity: 1;
  }

}

.super-card{
  height: 288px;
  .card-image{
    height: 180px;
    text-align: center;
    img{
      height: 100%;
      width: auto;
    }
  }
  .hover-area  .look-detail{
    display: none;
  }
}
</style>
