/**
 * filter [过滤器文件 -- vue过滤器]
 */
export default {
  init(Vue) {
    var dicts = require('./dicts').default
    var dictArr = dicts.dictArr
    for (var key in dictArr) {
      if (dictArr.hasOwnProperty(key)) {
        (function (key) {
          Vue.filter(key, function (n) {
            var item = dicts.getDict(key)
            return item[n + ''] || 　'-'
          })
        }(key))
      }
    }

    Vue.filter('date', function (date, format) {
      if (!date) return '-'
      if (date == '-' || date == '--') return date
      var date = new Date(date)
      var paddNum = function (num) {
        num += "";
        return num.replace(/^(\d)$/, "0$1");
      }
      //指定格式字符
      var cfg = {
        yyyy: date.getFullYear(), //年 : 4位
        yy: date.getFullYear().toString().substring(2), //年 : 2位
        M: date.getMonth() + 1, //月 : 如果1位的时候不补0
        MM: paddNum(date.getMonth() + 1), //月 : 如果1位的时候补0
        d: date.getDate(), //日 : 如果1位的时候不补0=
        dd: paddNum(date.getDate()), //日 : 如果1位的时候补0
        hh: paddNum(date.getHours()), //时
        mm: paddNum(date.getMinutes()), //分
        ss: paddNum(date.getSeconds()) //秒
      }
      format || (format = "yyyy-MM-dd hh:mm:ss");
      return format.replace(/([a-z])(\1)*/ig, function (m) {
        return cfg[m];
      })
    })

    Vue.filter('time', function (str) {
      var now = new Date()
      var tick = new Date(str)
      if (!tick) {
        return '未知'
      }
      var offset = parseInt((now.getTime() - tick.getTime()) / 1000)
      //0dian
      now.setHours(0)
      now.setMinutes(0)
      now.setSeconds(0)
      now.setMilliseconds(0)
      var todayTick = now.getTime()
      var addZero = function (num) {
        if (num < 10) {
            return `0${num}`
        }
        return num + ''
      }
      if (tick.getTime() >= todayTick) {
        if (offset < 60) {
          return '刚刚'
        } else if (offset < 3600) {
          return `${parseInt(offset / 60)}分钟前`
        }
        return `${parseInt(offset / 3600)}小时前`
      }
      now.setDate(now.getDate() - 1)
      var yesterdayTick = now.getTime()

      if (tick.getTime() >= yesterdayTick) {
        return `昨天 ${addZero(tick.getHours())}:${addZero(tick.getMinutes())}`
      }
      now.setDate(now.getDate() - 1)
      var beforeYesterTick = now.getTime()
      if (tick.getTime() >= beforeYesterTick) {
        return `前天 ${addZero(tick.getHours())}:${addZero(tick.getMinutes())}`
      }
      return `${tick.getFullYear()}年${addZero(tick.getMonth() + 1)}月${addZero(tick.getDate())}日 ${addZero(tick.getHours())}:${addZero(tick.getMinutes())}`
    })

  }

}
