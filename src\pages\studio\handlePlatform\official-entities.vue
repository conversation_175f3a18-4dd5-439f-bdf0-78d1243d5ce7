<template>
  <div class="os-scroll os_scroll">
    <div class="handle-platform-top handle_platform_top">
      <div
        class="handle-platform-top-search"
        @keyup.enter="getOfficialEntities(1)"
      >
        <el-input
          class="search-area"
          placeholder="输入要查询的内容，如：清华大学"
          v-model="searchVal"
        >
          <i
            slot="suffix"
            class="el-input__icon el-icon-search search-area-btn"
            @click="getOfficialEntities(1)"
          />
        </el-input>
      </div>
      <h1 class="txt-al-c">官方实体</h1>
    </div>
    <div class="handle-platform-content">
      <os-table
        class="gutter-table-style secondary-table"
        :tableData="tableData"
        :height="'calc(100vh - 180px)'"
        @change="getOfficialEntities"
      >
        <el-table-column prop="value" width="160" label="实体">
          <template slot-scope="scope">
            <div class="text-blod">{{ scope.row.zhName || '-' }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="name" width="200" label="英文标识">
        </el-table-column>
        <el-table-column prop="description" width="200" label="描述">
        </el-table-column>
        <el-table-column prop="example" width="200" label="示例">
        </el-table-column>
        <el-table-column prop="count" width="120" label="词条数">
          <template slot-scope="scope">
            <div v-if="scope.row.count">{{ scope.row.count }}条</div>
            <div v-else>0条</div>
          </template>
        </el-table-column>
      </os-table>
    </div>
  </div>
</template>

<script>
export default {
  name: 'studio-official-entities',
  data() {
    return {
      nav: 'entities',
      searchVal: '',
      tableData: {
        loading: true,
        total: 0,
        page: 1,
        size: 10,
        list: [],
      },
      dialog: {
        show: false,
      },
      countDialog: {
        show: false,
        entityId: '',
      },
    }
  },
  created() {
    this.getOfficialEntities(1)
  },
  computed: {},
  methods: {
    getOfficialEntities(page) {
      let self = this
      this.tableData.loading = true
      this.$utils.httpGet(
        this.$config.api.STUDIO_OFFICIAL_ENTITY_LIST,
        {
          pageIndex: page || this.tableData.page,
          pageSize: this.tableData.size,
          search: this.searchVal,
          type: 1,
        },
        {
          success: (res) => {
            self.tableData.list = res.data.entities
            self.tableData.total = res.data.count
            self.tableData.page = res.data.pageIndex
            self.tableData.size = res.data.pageSize
            self.tableData.loading = false
          },
          error: (err) => {},
        }
      )
    },
  },
  components: {},
}
</script>

<style lang="scss" scoped>
.handle-platform-content {
  // max-width: 1200px;
  margin: auto;
}

.os_scroll {
  padding: 24px;
  background-color: $secondary-bgc !important;
  .handle_platform_top {
    background-color: $secondary-bgc !important;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding-top: 0;
    .handle-platform-top-search {
      margin: 0;
      padding-bottom: 0;
    }
    h1 {
      font-size: 20px;
      color: #000000;
      font-weight: 600;
    }
  }
}
</style>
