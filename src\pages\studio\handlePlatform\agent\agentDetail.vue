<template>
  <div class="agent_detail_content">
    <div class="agent_detail_content_left">
      <div class="agent_header">
        <div class="agent_header_left">
          <back-icon @click="goback"></back-icon>
          <span v-if="agentType === 20" class="agent_name">编辑智能体</span>
          <span v-if="agentType === 21" class="agent_name">
            {{ currentPluginInfo.pluginName }}的意图</span
          >
          <img
            :src="currentPluginInfo.pluginIconUrl"
            fit="cover"
            @click="openSelectIconDialog"
            class="agent_avatar"
          />

          <el-popover popper-class="custom_agent_popover" trigger="hover">
            <el-select
              slot="reference"
              v-model="currentPluginInfo.pluginId"
              placeholder="请选择"
              @change="selectAgent"
              size="small"
            >
              <el-option
                v-for="(item, i) in formatAgentList"
                :key="i"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>

            <AgentPopover :pluginInfo="currentPluginInfo" />
          </el-popover>

          <i class="el-icon-edit" @click="editCurrentAgent"></i>
          <div
            class="publish_status"
            :class="
              currentPluginInfo.pluginStatus == 1 || isPublished
                ? 'published'
                : ''
            "
          >
            {{
              currentPluginInfo.pluginStatus == 1 || isPublished
                ? '已发布'
                : '待发布'
            }}
          </div>
        </div>

        <div class="agent_header_right">
          <studio-agent-header-right
            @updatePublishStatus="
              (val) => {
                isPublished = val
              }
            "
          >
            <el-button
              v-if="agentType === 21"
              @click="advancedModel"
              v-loading="saveLoading"
              >高级模式</el-button
            >
            <el-button v-if="agentType === 22" @click="workflowDebug"
              >调试</el-button
            >
          </studio-agent-header-right>
        </div>
      </div>
      <div class="agent_detail_wrapper" v-if="showWrapper">
        <div
          class="agent_detail"
          :class="{ agent_detail_workflow: agentType === 22 }"
        >
          <AgentThreeForm
            ref="AgentThreeForm"
            v-if="agentType === 20"
          ></AgentThreeForm>

          <AgentTemplateTool
            ref="AgentTemplateTool"
            v-if="agentType === 21"
          ></AgentTemplateTool>

          <WorkflowAgent
            v-if="agentType === 22"
            :workflowPluginInfo="workflowPluginInfo"
          ></WorkflowAgent>
        </div>
      </div>
    </div>
    <div
      v-if="agentType !== 22"
      class="os-side-right"
      :class="{ 'os-side-right-open': rightTestOpen }"
    >
      <div
        v-if="!rightTestOpen"
        :class="{ disabled_component: agentType !== 20 }"
        @click="handleRightTestCloseClick"
      >
        <right-test-close
          :rightTestOpen="rightTestOpen"
          :firstVisit="firstVisit"
          @updateFirstVisit="
            (val) => {
              firstVisit = val
            }
          "
          debugType="plugin"
        ></right-test-close>
      </div>
      <AgentDebug v-show="rightTestOpen"></AgentDebug>
    </div>
    <CreateAgent ref="CreateAgent" @refresh="getAgentList"> </CreateAgent>
    <SelectIconDialog
      ref="SelectIconDialog"
      @confirm="handleIconConfirm"
      @updatePluginIcon="updatePluginIcon"
    >
    </SelectIconDialog>
  </div>
</template>

<script>
// import IntentTable from './agentComponents/intentTable.vue'
import { mapGetters } from 'vuex'
import AgentThreeForm from './agentComponents/agentThreeForm.vue'
import AgentTemplateTool from './agentComponents/agentTemplateTool.vue'
import CreateAgent from './createAgent.vue'
import AgentPopover from './agentComponents/agentPopover.vue'
import RightTestClose from '@C/rightTestClose'
import AgentDebug from './agentComponents/agentDebug.vue'
import SelectIconDialog from '@C/selectIconDialog.vue'
import WorkflowAgent from './agentComponents/workflowAgent.vue'
import StudioAgentHeaderRight from '@C/studioAgentHeaderRight.vue'

import { bus } from '@U/bus'

export default {
  name: 'AgentDetail1',
  data() {
    return {
      rightDebugOpen: false,
      saveLoading: false,
      agentType: 21, //  20：三方   21：AIUI智能体模板
      intentSearch: null,
      isPublished: false,
      agentList: [],
      currentPluginInfo: {},
      workflowPluginInfo: {},
      showWrapper: true,
      firstVisit: true, // 标记是否是首次访问页面
      intentTableData: {
        loading: false,
        total: 0,
        page: 1,
        size: 10,
        handles: ['edit', 'del'],
        handleColumnTest: '操作',
        list: [
          {
            intentName: 'hahahah1',
            keyInfoList: [
              {
                query: '查询的关键信息',
                desc: '查询的关键信息描述',
                required: true,
              },
            ],
            form: {},
          },
        ],
      },
    }
  },
  components: {
    // IntentTable,
    AgentThreeForm,
    AgentTemplateTool,
    CreateAgent,
    AgentPopover,
    RightTestClose,
    AgentDebug,
    SelectIconDialog,
    WorkflowAgent,
    StudioAgentHeaderRight,
  },
  created() {
    this.$store.dispatch('studioSkill/setRightTestOpen', false) // 关闭右侧对话面板
    this.agentType = this.$route.params.agentType * 1
    this.initAgentInfo()
    this.getAgentList()
  },
  beforeDestroy() {
    this.$store.dispatch('studioSkill/setRightTestOpen', false) // 关闭右侧对话面板
  },

  computed: {
    ...mapGetters({
      rightTestOpen: 'studioSkill/rightTestOpen',
    }),
    formatAgentList() {
      return this.agentList.map((item) => {
        return { value: item.pluginId, label: item.pluginName }
      })
    },
  },

  methods: {
    initAgentInfo() {
      const agentItemData = JSON.parse(
        localStorage.getItem('agentItemData') || '{}'
      )
      if (agentItemData && agentItemData.pluginId) {
        this.currentPluginInfo = JSON.parse(JSON.stringify(agentItemData))
      }
    },
    goback() {
      this.$router.push({
        name: 'studio-handle-platform-agent',
      })
    },
    buildAgent() {},
    advancedModel() {
      this.$confirm(
        `切换高级模式后，会自动切换成工作流类型(回不到模版)，是否继续？`,
        {
          confirmButtonClass: '确定切换',
          cancelButtonText: '取消',
          confirmButtonClass: 'el-button-danger',
          type: 'warning',
          showClose: false,
        }
      )
        .then(() => {
          this.saveLoading = true
          let id = this.$route.params.agentId
          this.$utils.httpPost(
            `/aiui-agent/plugin/switchWorkflow?pluginId=${id}`,
            {},
            {
              config: {
                headers: {
                  'Content-Type': 'application/json;charset=UTF-8',
                },
              },
              success: async (res) => {
                if (res.code === '0') {
                  this.$message.success('切换成功')
                  this.saveLoading = false
                  this.workflowPluginInfo = res.data
                  await this.getAgentList()
                  // 更新路由参数
                  this.$router
                    .replace({
                      name: this.$route.name,
                      params: {
                        agentId: this.currentPluginInfo.pluginId,
                        agentType: this.currentPluginInfo.pluginType,
                        boardId: this.workflowPluginInfo.boardId,
                      },
                    })
                    .catch((err) => {
                      if (err.name !== 'NavigationDuplicated') {
                        throw err
                      }
                    })
                  this.agentType = 22 // 显示工作流页面
                } else {
                  this.$message.error('切换失败')
                }
              },
              error: (err) => {
                this.saveLoading = false
                this.$message.error(err?.desc || '切换失败')
              },
            }
          )
        })
        .catch(() => {})
    },
    workflowDebug() {
      console.log('工作流调试')
      bus.$emit('DEBUG_AGENT_FLOW')
    },
    async getAgentList() {
      const data = {
        pageIndex: 1,
        pageSize: 999,
      }

      try {
        const result = await new Promise((resolve, reject) => {
          this.$utils.httpPost(
            this.$config.api.AGENT_TABLE_LIST,
            JSON.stringify(data),
            {
              config: {
                headers: {
                  'Content-Type': 'application/json;charset=UTF-8',
                },
              },
              success: (res) => {
                if (res.code == 0) {
                  resolve(res)
                } else {
                  reject(new Error(res.desc || '获取列表失败'))
                }
              },
              error: (err) => {
                reject(err)
              },
            }
          )
        })

        this.agentList = result.data.data

        // 更新当前智能体信息
        if (this.currentPluginInfo.pluginId) {
          const currentAgentInfo = this.agentList.find(
            (item) => item.pluginId === this.currentPluginInfo.pluginId
          )
          if (currentAgentInfo) {
            this.currentPluginInfo = JSON.parse(
              JSON.stringify(currentAgentInfo)
            )
          }
        }

        return result
      } catch (error) {
        this.$message.error(error?.desc || '获取列表失败')
        throw error
      }
    },
    selectAgent(val) {
      const selectedPlugin = this.agentList.find(
        (item) => item.pluginId === val
      )

      if (selectedPlugin) {
        this.currentPluginInfo = JSON.parse(JSON.stringify(selectedPlugin))
        this.agentType = this.currentPluginInfo.pluginType
        // 关闭右侧对话面板
        this.$store.dispatch('studioSkill/setRightTestOpen', false)
        // 更新路由参数
        this.$router
          .replace({
            name: this.$route.name,
            params: {
              agentId: this.currentPluginInfo.pluginId,
              agentType: this.currentPluginInfo.pluginType,
              boardId: this.currentPluginInfo.boardId,
            },
          })
          .catch((err) => {
            if (err.name !== 'NavigationDuplicated') {
              throw err
            }
          })
        // 强制更新组件
        this.showWrapper = false
        this.$nextTick(() => {
          this.showWrapper = true
        })
      }
    },
    editCurrentAgent() {
      this.$refs.CreateAgent.show(this.currentPluginInfo)
    },
    openSelectIconDialog() {
      this.$refs.SelectIconDialog.show(this.currentPluginInfo.pluginIconKey)
    },
    handleIconConfirm(icon) {
      console.log('handleIconConfirm=>', icon, this.currentPluginInfo)
      if (!icon || !this.currentPluginInfo) return

      const paramsData = {
        pluginName: this.currentPluginInfo.pluginName,
        pluginDesc: this.currentPluginInfo.pluginDesc,
        pluginId: this.currentPluginInfo.pluginId,
        pluginIconKey: icon.code,
      }

      this.$utils.httpPost(
        this.$config.api.AGENT_EDIT,
        JSON.stringify(paramsData),
        {
          config: {
            headers: {
              'Content-Type': 'application/json;charset=UTF-8',
            },
          },
          success: (res) => {
            if (res.code === 0 || res.code === '0') {
              this.$message.success('头像更新成功')
              // 更新agentList中的数据
              const index = this.agentList.findIndex(
                (item) => item.pluginId === this.currentPluginInfo.pluginId
              )
              if (index !== -1) {
                this.agentList[index].pluginIconUrl = icon.url
                // 同时更新currentPluginInfo中的pluginIconUrl和pluginIconKey
                this.currentPluginInfo.pluginIconUrl = icon.url
                this.currentPluginInfo.pluginIconKey = icon.code

                // 将更新后的数据保存到localStorage，以便重新加载时使用
                localStorage.setItem(
                  'agentItemData',
                  JSON.stringify(this.currentPluginInfo)
                )
              }
            } else {
              this.$message.error(res.desc || '头像更新失败')
            }
          },
          error: (err) => {
            this.$message.error(err?.desc || '头像更新失败')
          },
        }
      )
    },
    updatePluginIcon(imgUrl) {
      if (imgUrl) {
        // 临时更新currentPluginInfo的头像URL（用于预览）
        this.currentPluginInfo.pluginIconUrl = imgUrl
      } else {
        this.initAgentInfo()
      }
    },
    handleRightTestCloseClick(event) {
      if (this.agentType !== 20) {
        this.$message.warning('功能开发中，敬请期待')
      } else {
        // 如果是 agentType === 20，则打开右侧测试面板
        this.$store.dispatch('studioSkill/setRightTestOpen', true)
      }
    },
  },
}
</script>

<style>
.custom_agent_popover {
  max-width: 300px !important;
  padding: 16px !important;
}
</style>
<style lang="scss">
.agent_detail_content {
  padding-top: 0px;
  background-color: #f5f5f5;
  height: 100%;
  display: flex;
  .agent_detail_content_left {
    flex: 1;
  }
  // overflow-y: auto;

  .disabled_component {
    opacity: 0.5;
    cursor: not-allowed;
    position: relative;

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 1;
    }
  }

  .agent_header {
    background-color: #fff;
    border-bottom: 1px solid $grey007;
    display: flex;
    height: 63px;
    align-items: center;
    justify-content: space-between;
    padding: 0px 24px;

    .agent_header_left {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 10px;

      .agent_name,
      .el-icon-back {
        font-size: 20px;
        color: #000000;
        font-weight: 600;
      }

      .agent_name {
        max-width: 300px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .agent_avatar {
        width: 50px;
        height: 50px;
        border-radius: 5px;
        overflow: hidden;
        cursor: pointer;

        img {
          width: 100%;
          height: 100%;
        }
      }

      .el-icon-edit {
        margin: 0 10px;
        cursor: pointer;

        &:hover {
          color: #595959;
        }
      }

      .publish_status {
        font-size: 12px;
        padding: 4px;
        color: #fff;
        background-color: $primary;
        border-radius: 4px;

        &.published {
          background-color: $success;
        }
      }
    }

    .right_btns {
      margin-left: auto;
    }
  }

  .agent_detail_wrapper {
    height: calc(100vh - 63px);
    display: flex;

    .agent_detail {
      flex: 1;
      padding: 24px;
      overflow-y: auto;

      .title {
        font-size: 18px;
        color: #000000;
        margin-bottom: 20px;
      }

      &.agent_detail_workflow {
        padding: 0px;
      }
    }
  }
  .os-side-right {
    height: 100%;
    background-color: #fff;
    overflow: hidden;
  }
}
</style>
