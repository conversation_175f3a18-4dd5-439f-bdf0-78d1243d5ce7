<template>
  <div class="inputing">
    <span class="pot" :class="{ active: index === 0 }"></span>
    <span class="pot" :class="{ active: index === 1 }"></span>
    <span class="pot" :class="{ active: index === 2 }"></span>
  </div>
</template>

<script>
export default {
  data() {
    return {
      index: 0,
      timer: null,
    }
  },
  mounted() {
    this.timer = setInterval(() => {
      if (this.index > 2) {
        this.index = 0
      } else {
        this.index++
      }
    }, 200)
  },
  destroyed() {
    clearInterval(this.timer)
    this.index = 0
  },
}
</script>

<style lang="scss" scoped>
.inputing {
  display: inline-flex;
  align-items: center;
  .pot {
    // display: inline-block;
    background-clip: padding-box;
    border-top-left-radius: 999px; /* 左上角 */
    border-top-right-radius: 999px; /* 右上角 */
    border-bottom-right-radius: 999px; /* 右下角 */
    border-bottom-left-radius: 999px; /* 左下角 */
    border-radius: 999px;
    width: 4px;
    height: 4px;
    background: #bebebe;
    &.active {
      width: 6px;
      height: 6px;
    }
  }
  .pot:nth-child(2) {
    // width: 8px;
    border-radius: 50%;
    border-radius: 500px;
  }
  .pot + .pot {
    margin-left: 4px;
  }
}
</style>
