<template>
  <el-upload
    class="qabank__upload-file"
    :action="`${baseUrl}/aiui/web${this.$config.api.AWAKEN_IMPORT}?appid=${appId}`"
    :show-file-list="false"
    :before-upload="beforeUpload"
    :on-success="success"
    :on-error="uploadError"
  >
    <el-button size="small"> {{ options.text }}</el-button>
  </el-upload>
</template>
<script>
export default {
  props: {
    appId: '',
    options: {
      text: '',
    },
    limitCount: Object,
  },
  data() {
    return {
      baseUrl: this.$config.server,
    }
  },
  methods: {
    beforeUpload(file) {
      let reg = /\.xls(x)?$/i
      let type = reg.test(file.name)
      let app_awaken_file_size = this.limitCount['app_awaken_file_size'] || 1
      let unExceed = file.size < 1024 * 1024 * app_awaken_file_size
      if (!type) {
        this.$message.error('资源模板需要是.excel文件，请在批量操作下载模板。')
        return type
      }
      if (!unExceed) {
        this.$message.error(`文件不能超过${app_awaken_file_size}M`)
        return unExceed
      }
      this.$emit('setLoad', type && unExceed)
      return type && unExceed
    },
    success(data) {
      if (data.flag) {
        this.$emit('setLoad', false)
        this.$emit('getAwakenList')
        this.$message.success('上传成功')
      } else {
        this.$emit('setLoad', false)
        this.$message.error(data.desc || '上传失败')
      }
    },
    uploadError() {
      this.$emit('setLoad', false)
    },
  },
}
</script>
<style lang="scss">
.qabank__upload-file {
  .el-button {
    padding: 11px 0 !important;
    min-width: unset;
    text-align: left;
    color: $grey6;
    border: none;
    background: transparent;
    &:hover {
      background-color: #e8f3fd;
      color: #459ded;
    }
  }
}
</style>
