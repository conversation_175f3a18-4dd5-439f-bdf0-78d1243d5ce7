<template>
  <card>
    <template #title>
      星火大模型配置 &nbsp;
      <el-switch
        v-if="
          !(
            currentScene &&
            currentScene.chainId === 'sos_app' &&
            currentScene.point === '1,13'
          )
        "
        :value="isOn"
        class="mgr16"
        :disabled="!subAccountEditable"
        @change="onSwitchChange"
      ></el-switch
    ></template>
    <div>
      <p class="item-title" style="margin-top: 0">添加工具</p>
      <repo />
      <agent />
      <!-- <skill /> -->

      <p class="item-title">搜索设置</p>
      <search />
    </div>
  </card>
</template>
<script>
import card from '../components/card'

import RECOGNITION_LLM_SEMANTIC_State from '@U/AIUIState/RECOGNITION_LLM_SEMANTIC_State'
import RECOGNITION_LLM_SEMANTIC_SYNTHESIS_State from '@U/AIUIState/RECOGNITION_LLM_SEMANTIC_SYNTHESIS_State'

import { mapGetters } from 'vuex'

import repo from './repo'
import agent from './agent'
// import skill from './skill'

import prompt from './prompt'
import search from './search'

export default {
  data() {
    return {}
  },

  computed: {
    ...mapGetters({
      currentScene: 'aiuiApp/currentScene',
      app: 'aiuiApp/app',
      context: 'aiuiApp/context',
      subAccountEditable: 'aiuiApp/subAccountEditable',
    }),
    appId() {
      return this.$route.params.appId
    },
    isOn() {
      if (this.currentScene && this.currentScene.point && this.context) {
        const point = this.currentScene.point
        const context = this.context

        if (
          context.isCurrentState(RECOGNITION_LLM_SEMANTIC_State) ||
          context.isCurrentState(RECOGNITION_LLM_SEMANTIC_SYNTHESIS_State)
        ) {
          return true
        }
      }
      return false
    },
  },

  methods: {
    onSwitchChange(val) {
      let that = this
      this.$utils.httpGet(
        this.$config.api.AIUI_APP_SCENE_CHECKAUTH,
        {
          appid: this.appId,
          sceneId: this.currentScene.sceneBoxId,
          point: val ? '13' : '2',
        },
        {
          success: (res) => {
            if (res.flag) {
              if (res.data.check) {
                that.doConfirm(val)
              } else {
                that.$message.warning(res.data.desc)
              }
            }
          },
        }
      )
    },

    doConfirm(val) {
      this.$confirm(
        '通用语义模型同星火交互认知大模型的协议格式互不兼容，详情可见 <a target="_blank" href="https://aiui-doc.xf-yun.com/project-1/doc-182/">文档中心</a>，切换前请确保已经做了相应的解析适配。',
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          dangerouslyUseHTMLString: true,
        }
      )
        .then(() => {
          if (val) {
            // 处理即将打开的情况，可能的情况组合是
            this.$store.dispatch('aiuiApp/addSwitches', '13')
          } else {
            // 处理即将关闭的情况

            this.$store.dispatch('aiuiApp/removeSwitch', '13')
          }
        })
        .catch(() => {})
    },
  },

  components: {
    card,
    repo,
    agent,
    // skill,
    prompt,
    search,
  },
}
</script>
<style lang="scss" scoped>
@import '../common.scss';
</style>
