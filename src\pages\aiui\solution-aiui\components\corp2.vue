<template>
  <div class="corp">
    <div class="section-title">
      <p><span class="section-title-bold">接入虚拟人交互体验</span></p>
      <p class="section-desc">
        <slot></slot>
      </p>
    </div>

    <div class="section-item" style="text-align: center">
      <div class="section-button" @click="toConsole">合作咨询</div>
    </div>
  </div>
</template>
<script>
export default {
  methods: {
    toConsole() {
      this.$emit('jump')
    },
  },
}
</script>
<style lang="scss" scoped>
p {
  margin-bottom: 0;
}
.corp {
  padding: 50px 0;
  background: url(~@A/images/solution/meta-human/corp.png) center/cover
    no-repeat;
}
.section-title {
  text-align: center;
}
.section-desc {
  text-align: left;
  margin-top: 25px;
  font-weight: 400;
  display: inline-block;
  font-size: 16px;
  font-weight: 400;
  text-align: left;
  color: #444444;
  line-height: 25px;
}
.section-title-bold {
  font-size: 41px;
  font-weight: 600;
  line-height: 46px;
  color: #0065f9;
  text-align: center;
  -webkit-text-fill-color: transparent;
  -webkit-background-clip: text;
  background-clip: text;
  background-image: linear-gradient(
    270deg,
    #006cf2 40%,
    #00a9f9 56%,
    #00d7ff 98%
  );
}

.section-button {
  font-size: 22px;
  text-align: center;
  font-weight: 400;
  margin: 30px auto 0;

  border: none;
  color: #fff;
  cursor: pointer;

  width: 180px;
  height: 50px;
  line-height: 50px;
  background: #2274ff;

  border-radius: 4px;
  backdrop-filter: blur(7px);
}
</style>
