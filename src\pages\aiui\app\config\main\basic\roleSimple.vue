<template>
  <el-form
    ref="form"
    class="recognition-form"
    :model="form"
    label-width="90px"
    label-position="left"
    :inline="true"
    style="width: 100%"
  >
    <el-form-item class="form-item" label="回复角色：">
      <el-select
        class="config-select"
        v-model="form.role"
        @change="changeOption"
        size="small"
        style="width: 400px"
        :disabled="!subAccountEditable"
      >
        <el-option
          v-for="(item, index) in roleList"
          :key="item.id"
          :label="item.name"
          :value="item.id"
        >
          <span>{{ item.name }}</span>
          <span class="option-official" v-show="item.official">官方</span>
        </el-option>
      </el-select>
    </el-form-item>
  </el-form>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  data() {
    return {
      roleList: [],
      form: {
        role: '',
      },
    }
  },
  computed: {
    ...mapGetters({
      currentScene: 'aiuiApp/currentScene',
      appInfo: 'aiuiApp/app',
      subAccountEditable: 'aiuiApp/subAccountEditable',
    }),
    appId() {
      return this.$route.params.appId
    },
  },
  created() {
    if (this.currentScene && this.currentScene.botBoxId) {
      this.getRoleList()
    }
  },
  watch: {
    currentScene(scene) {
      if (scene && scene.botBoxId) {
        this.$store.dispatch('aiuiApp/setSceneRole', '')
        this.getRoleList()
      }
    },
  },
  methods: {
    changeOption(val) {
      let data = {
        botId: this.currentScene.botBoxId,
        roleId: val,
      }
      this.$utils.httpPost(
        this.$config.api.AIUI_BOT_ROLE_SAVE,
        JSON.stringify(data),
        {
          config: {
            headers: {
              'Content-Type': 'application/json;charset=UTF-8',
            },
          },
          success: (res) => {
            if (res.flag) {
              this.$store.dispatch('aiuiApp/setSceneRole', val)
            } else {
              this.$message.error(res.desc)
              this.getRoleList()
            }
          },
          error: (err) => {},
        }
      )
    },
    getRoleList() {
      this.$utils.httpGet(
        this.$config.api.AIUI_BOT_ROLE_LIST,
        {
          botId: this.currentScene.botBoxId,
        },
        {
          success: (res) => {
            this.roleList = res.data.list
            const selectedItem = res.data.list.find((data) => data.selected)
            if (selectedItem) {
              this.form.role = selectedItem.id
              this.$store.dispatch('aiuiApp/setSceneRole', selectedItem.id)
            } else {
              this.form.role = ''
            }
          },
          error: (err) => {},
        }
      )
    },
  },
}
</script>

<style lang="scss" scoped>
@import './common.scss';
.option-official {
  background: #e5e9ff;
  border-radius: 4px;
  padding: 2px 7px;
  font-size: 12px;
  color: #544aff;
  margin-left: 2px;
}
</style>
