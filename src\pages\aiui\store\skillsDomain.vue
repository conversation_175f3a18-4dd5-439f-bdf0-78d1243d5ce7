<template>
  <div class="content-container">
    <div class="top-area" :style="{ visibility: fixed ? 'hidden' : 'visible' }">
      <el-tabs v-model="domain" @tab-click="handleDomainClick">
        <el-tab-pane
          v-for="(item, index) in domains"
          :label="item.label"
          :name="item.value"
          :key="index"
        ></el-tab-pane>
      </el-tabs>
      <!-- <div class="store-skill-search" @keyup.enter="searchSkill">
        <el-input
          class="search-area"
          size="medium"
          placeholder="输入技能名称"
          v-model.trim="searchVal"
        >
          <i
            slot="suffix"
            class="el-input__icon el-icon-search search-area-btn"
            @click="searchSkill"
          />
        </el-input>
      </div> -->
    </div>
    <div class="top-area top-area-fixed" v-if="fixed">
      <el-tabs v-model="domain" @tab-click="handleDomainClick">
        <el-tab-pane
          v-for="(item, index) in domains"
          :label="item.label"
          :name="item.value"
          :key="index"
        ></el-tab-pane>
      </el-tabs>
    </div>
    <div class="os-scroll-bottom" v-loading.body="skillsData.loading">
      <template v-if="skillsData.total > 0">
        <p
          class="store-desc"
          :style="{ visibility: skillsData.loading ? 'hidden' : 'visible' }"
        >
          {{ description }}
        </p>
        <div class="store-skills-row">
          <div
            class="store-skills-col ib"
            v-for="(skill, index) in skillsData.list"
            :key="index"
          >
            <div class="os-store-skill" @click="toSkillDetail(skill)">
              <div class="os-store-skill-thumb">
                <img v-if="skill.url" :src="skill.url" />
                <span v-else>{{
                  skill.zhName && skill.zhName.substr(0, 1)
                }}</span>
              </div>
              <div class="os-store-skill-content">
                <p class="os-store-skill-name" :title="skill.zhName">
                  {{ skill.zhName }}
                </p>
                <p class="os-store-skill-provider">
                  {{
                    skill.provider !== 'undefined' ? skill.provider || '-' : '-'
                  }}
                </p>
                <p class="os-store-skill-desc" :title="skill.briefIntroduction">
                  {{ skill.briefIntroduction }}
                </p>
              </div>
              <div class="os-store-skill-tag-area">
                <span class="os-store-skill-tag blue">
                  {{
                    skill.provider.includes('科大讯飞')
                      ? '官方技能'
                      : '第三方技能'
                  }}</span
                >
              </div>
              <div class="os-store-skill-tag-area tag-area-bottom">
                <span v-if="skill.count" class="os-store-skill-tag">
                  {{ skill.count }}个信源
                </span>
                <!-- <span v-if="skill.dialectinfo" class="os-store-skill-dialect-tag">
                {{ skill.dialectinfo }}
              </span> -->
              </div>
            </div>
          </div>
        </div>
        <div class="store-skill-page">
          <el-pagination
            v-if="showPagination"
            ref="pagination"
            class="txt-al-c"
            @current-change="getSkills"
            :current-page="skillsData.page"
            :page-size="skillsData.size"
            :total="skillsData.total"
            :layout="pageLayout"
          >
          </el-pagination>
        </div>
      </template>

      <!-- <div
        class="empty-skill-tip"
        v-if="skillsData.total === 0 && !skillsData.loading"
      >
        <p>没有相应的技能</p>
        <p>
          这里的技能还是不能满足您的需求？您还可以
          <router-link :to="{ path: '/studio/skill' }"
            >自定义您的技能</router-link
          >
        </p>
      </div> -->
      <skills-empty v-if="skillsData.total === 0 && !skillsData.loading" />
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import skillsEmpty from './skillsEmpty.vue'

export default {
  name: 'store-skill-all',
  props: ['search', 'fixed', 'scrollTop', 'scrollTo'],
  data() {
    return {
      sortType: 0,
      businessType: '',
      domain: '1',
      icon: {
        efficiencyTool: 10100,
        lifeService: 10200,
        vedioRead: 10300,
        education: 10400,
        healthy: 10500,
        entertainment: 10600,
        childrenEducation: 10700,
        finance: 10800,
        smartHome: 10900,
        qa: 20100,
      },
      provider: '',
      skillsData: {
        loading: true,
        total: -1,
        page: 1,
        size: 6,
        list: [],
      },
      needSearch: false,
      categoryTypes: [],
      domains: [],
      showCategorySearch: true,
      searchVal: '',
      // domainDescription: {
      //   1: '通用技能包中包含了天气、音乐等常用技能，覆盖了绝大多数的场景。在应用中勾选了相关的应用类型后，AIUI会自动勾选技能包中的技能。',
      //   2: '机器人技能包中包含了机器人动作控制等机器人场景常用技能。在应用中勾选了相关的应用类型后，AIUI会自动勾选技能包中的技能。',
      //   3: '车载技能包中包含了地图导航等车载领域常用技能。在应用中勾选了相关的应用类型后，AIUI会自动勾选技能包中的技能。',
      //   4: '家庭技能包中包含了电视节目单、智能家居控制等家庭领域常用技能。在应用中勾选了相关的应用类型后，AIUI会自动勾选技能包中的技能。',
      // },
      description: '',
    }
  },
  watch: {
    $route: function (val, oldVal) {},
  },
  computed: {
    pageLayout() {
      if (this.skillsData.total / this.skillsData.size > 7) {
        return 'prev, pager, next, jumper'
      }
      return 'prev, pager, next'
    },
    showPagination() {
      return this.skillsData.total > this.skillsData.size
    },
  },
  created() {
    // 根据屏幕尺寸获取分页参数
    this.getAdaptPageSize()
    this.handleSkillType(this.$route.params.skillType)
    // 获取各种二级分类的类型
    this.getDomains()
  },
  methods: {
    getAdaptPageSize() {
      // if (window.innerHeight < 600) {
      //   this.skillsData.size = 6
      // } else {
      //   this.skillsData.size = 9
      // }
      this.skillsData.size = 18
    },
    searchSkill() {
      this.sortType = 0
      this.businessType = '0'
      this.provider = ''
      this.needSearch = true
      this.showCategorySearch = true
      this.domain = '1'
      this.getSkills(1)
    },
    handleSkillType(type) {
      console.log(type)
      this.sortType = 0
      this.businessType = '0'
      this.provider = ''
      this.needSearch = true
      this.showCategorySearch = true
      const infoObj = JSON.parse(
        sessionStorage.getItem('GLOBAL_SKILL_STORE_DOMAIN') || '{}'
      )
      if (infoObj.domain) {
        this.domain = infoObj.domain
      }
      if (infoObj.page) {
        this.skillsData.page = infoObj.page
        sessionStorage.removeItem('GLOBAL_SKILL_STORE_DOMAIN')
      }
      this.getSkills()
    },
    getSkills(page) {
      this.skillsData.loading = true
      this.$utils.httpGet(
        this.$config.api.AIUI_STORE_SKILLS,
        {
          sortType: this.sortType,
          businessType: this.businessType === '0' ? '' : this.businessType,
          domain: this.domain,
          search: this.needSearch ? this.searchVal : '',
          provider: this.provider,
          pageIndex: page || this.skillsData.page,
          pageSize: this.skillsData.size,
        },
        {
          success: (res) => {
            this.skillsData.list = res.data.skills
            this.skillsData.total = res.data.count
            this.skillsData.page = res.data.pageIndex
            // this.skillsData.size = res.data.pageSize
            this.description = res.data.desc
            this.skillsData.loading = false
            this.$nextTick(() => {
              this.restoreScrollTop()
            })
          },
          error: (err) => {
            console.log('page=>>')
            console.log(err)
          },
        }
      )
    },
    restoreScrollTop() {
      let top =
        sessionStorage.getItem('GLOBAL_SKILL_STORE_DOMAIN_SCROLLTOP') || 0
      if (top) {
        this.scrollTo(Number(top))
        sessionStorage.removeItem('GLOBAL_SKILL_STORE_DOMAIN_SCROLLTOP')
      }
    },
    toSkillDetail(skill) {
      // 保存当前页码等信息，便于返回时重新获取
      const infoObj = {
        domain: this.domain,
        page: this.skillsData.page,
      }
      sessionStorage.setItem(
        'GLOBAL_SKILL_STORE_DOMAIN',
        JSON.stringify(infoObj)
      )
      sessionStorage.setItem(
        'GLOBAL_SKILL_STORE_DOMAIN_SCROLLTOP',
        this.scrollTop
      )
      this.$router.push({
        name: 'store-skill',
        params: { skillId: skill.id },
        query: { type: skill.type },
      })
    },
    getDomains() {
      const domains = [
        { label: '通用', value: '1' },
        { label: '机器人', value: '2' },
        { label: '车载', value: '3' },
        { label: '家居', value: '4' },
      ]
      this.domains = domains
    },
    handleDomainClick() {
      this.getSkills(1)
    },
  },
  components: {
    skillsEmpty,
  },
}
</script>

<style lang="scss" scoped>
@import '@A/scss/skills.scss';
.store-desc {
  font-size: 14px;
  padding-top: 20px;
}
.store-skills-row {
  height: calc(100% - 30px);
}
</style>
