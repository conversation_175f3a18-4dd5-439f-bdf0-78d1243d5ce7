<template>
  <div class="content-container">
    <UseInfoTable @setTuringInfo="setTuringInfo"></UseInfoTable>
    <div class="chart_wrap">
      <!-- 调用次数图 start -->
      <os-page-label label="服务类型" class="mgb24 chart-option-wrap">
        <el-select
          class="chart-type-select"
          v-model="serviceType"
          size="medium"
          placeholder="请选择"
          @change="getServiceData"
        >
          <el-option
            v-for="item in serviceTypeList"
            :key="item.label"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
        <date-range @setTime="setServiceTime"></date-range>
      </os-page-label>
      <os-chart-line
        v-loading="serviceChartLoading"
        style="height: 400px"
        ref="serviceChart"
      ></os-chart-line>

      <!-- <template>
        <os-page-label label="交互次数" class="mgb24 chart-option-wrap">
          <date-range @setTime="setInteractionTime"></date-range>
        </os-page-label>
        <div style="height: 400px" v-loading="interactionNumChartLoading">
          <os-chart-bar
            v-show="!noInteractionNum"
            ref="interactionChart"
          ></os-chart-bar>
          <div v-show="noInteractionNum" class="no-data-tip">暂无数据</div>
        </div>
      </template> -->

      <!-- 信源 chart -->
      <os-page-label
        v-if="hasTuring"
        label="信源统计"
        class="mgb24 chart-option-wrap"
      >
        <a class="btn-export" @click="exportExcel" href="javascript:void(0);"
          >导出 Excel</a
        >
        <date-range @setTime="sourceSetTime"></date-range>
      </os-page-label>
      <os-chart-line v-if="hasTuring" ref="sourceChart"></os-chart-line>
    </div>
  </div>
</template>
<script>
import UseInfoTable from './statisticUseInfoTable'
import dateRange from './dateRange'
import datePicker from './datePicker'
import dicts from '@M/dicts'

let lineOption = dicts.lineOption
lineOption.color = ['#1784e9']
let barOption = dicts.barOption

export default {
  name: 'app-statistic-service-index',
  data() {
    return {
      pageOptions: {
        title: '服务统计',
        loading: false,
      },
      serviceType: 0,
      serviceTypeList: [
        {
          value: 0,
          label: '全部',
        },
        {
          value: 1,
          label: 'Windows',
        },
        {
          value: 2,
          label: 'ios',
        },
        {
          value: 3,
          label: 'Linux',
        },
        {
          value: 4,
          label: 'Android',
        },
        {
          value: 5,
          label: 'WEBAPI',
        },
      ],
      serviceStart: '',
      serviceEnd: '',
      serviceChartLoading: false,

      interactionNumStart: '',
      interactionNumEnd: '',
      interactionNumChartLoading: false,
      noInteractionNum: false,

      hasTuring: false,
      sourceStartDate: '',
      sourceEndDate: '',
      baseUrl: this.$config.server,
      exportExcelStatu: true,
      serviceTimeChanged: false,
      sourceTimeChanged: false,
      interactionTimeChanged: false,
    }
  },
  computed: {
    appId() {
      return this.$route.params.appId
    },
    platform() {
      return this.$store.state.aiuiApp.app.platform || ''
    },
  },
  watch: {
    serviceTimeChanged(val) {
      if (val && this.platform) {
        this.getServiceData()
      }
    },
    platform() {
      this.getServiceData()
    },
    sourceTimeChanged(val) {
      if (val) {
        this.getSourceService()
      }
    },
    interactionTimeChanged(val) {
      if (val) {
        this.getInteractionNum()
      }
    },
  },
  methods: {
    setServiceTime(start, end) {
      this.serviceStart = start
      this.serviceEnd = end
      this.serviceTimeChanged = true
    },
    /**
     * 调用次数图相关
     * getService() 非webapi
     * getWebApiService weapi
     */
    getServiceData() {
      // if (this.platform == 'WebAPI') {
      //   this.getWebApiService()
      // } else {
      //   this.getService()
      // }
      this.getService()
      this.serviceTimeChanged = false
    },
    getService(start, end) {
      this.serviceChartLoading = true
      this.$utils.httpGet(
        this.$config.api.AIUI_STATISTIC_CHART,
        {
          appid: this.appId,
          startDate: this.serviceStart,
          endDate: this.serviceEnd,
          type: this.serviceType,
        },
        {
          success: (res) => {
            this.serviceChartLoading = false
            let serviceOption = JSON.parse(JSON.stringify(lineOption))
            serviceOption.legend.data = ['调用次数']
            serviceOption.series[0].name = '调用次数'
            for (let i = 0; i < res.data.dailyService.xAxis.length; i++) {
              serviceOption.xAxis[0].data.push([res.data.dailyService.xAxis[i]])
              serviceOption.series[0].data.push(res.data.dailyService.yAxis[i])
            }
            this.setChart(serviceOption, 'serviceChart')
          },
          error: (err) => {
            this.serviceChartLoading = false
          },
        }
      )
    },
    getWebApiService() {
      this.serviceChartLoading = true
      this.$utils.httpGet(
        this.$config.api.AIUI_STATISTIC_WEBAPI_CHART,
        {
          appid: this.appId,
          type: this.serviceType,
          startTime: this.serviceStart,
          endTime: this.serviceEnd,
        },
        {
          success: (res) => {
            this.serviceChartLoading = false
            if (
              !res.data.hasOwnProperty('yAxis') ||
              !res.data.hasOwnProperty('xAxis')
            ) {
              return //过滤测试环境无数据的情况
            }
            let serviceOption = JSON.parse(JSON.stringify(lineOption))
            serviceOption.legend.data = ['调用次数']
            serviceOption.series[0].name = '调用次数'
            for (let i = 0; i < res.data.xAxis.length; i++) {
              serviceOption.xAxis[0].data.push([res.data.xAxis[i]])
              serviceOption.series[0].data.push(res.data.yAxis[0].data[i])
            }
            this.setChart(serviceOption, 'serviceChart')
          },
          error: (err) => {
            this.serviceChartLoading = false
          },
        }
      )
    },
    setChart(option, chartName) {
      let self = this
      self.$refs[chartName] && self.$refs[chartName].setOption(option)
    },
    //交互信息统计相关
    setInteractionTime(start, end) {
      //交互次数
      this.interactionNumStart = start
      this.interactionNumEnd = end
      this.interactionTimeChanged = true
    },
    getInteractionNum(start, end) {
      this.interactionNumChartLoading = true
      this.$utils.httpGet(
        this.$config.api.AIUI_STATISTIC_SERVICE_DATA,
        {
          appid: this.appId,
          startDate: this.interactionNumStart,
          endDate: this.interactionNumEnd,
        },
        {
          success: (res) => {
            this.interactionTimeChanged = false
            if (
              res.data.interactionNumber.xAxis &&
              !res.data.interactionNumber.xAxis.length
            ) {
              this.noInteractionNum = true
              this.interactionNumChartLoading = false
              return
            } else {
              this.noInteractionNum = false
            }
            let interacOption = JSON.parse(JSON.stringify(barOption))
            interacOption.xAxis.name = '次'
            interacOption.dataset.source.push(['次', 'name'])
            interacOption.series[0].encode.x = '次'
            interacOption.series[0].encode.y = 'name'
            for (let i = 0; i < res.data.interactionNumber.xAxis.length; i++) {
              interacOption.dataset.source.push([
                res.data.interactionNumber.yAxis[i],
                res.data.interactionNumber.xAxis[i],
              ])
            }
            this.setChart(interacOption, 'interactionChart')
            this.interactionNumChartLoading = false
          },
          error: (err) => {
            this.interactionNumChartLoading = false
            this.interactionTimeChanged = false
          },
        }
      )
    },

    //信源相关
    setTuringInfo(val) {
      this.hasTuring = val
    },
    sourceSetTime(start, end) {
      this.sourceStartDate = start
      this.sourceEndDate = end
      this.sourceTimeChanged = true
    },
    getSourceService() {
      this.$utils.httpGet(
        this.$config.api.AIUI_STATISTIC_SOURCE_CHART,
        {
          appid: this.appId,
          licName: 'turing.turing',
          startDate: this.sourceStartDate,
          endDate: this.sourceEndDate,
        },
        {
          success: (res) => {
            let sourceOption = JSON.parse(JSON.stringify(lineOption))
            for (let i = 0; i < res.data.xAxis.length; i++) {
              sourceOption.legend.data = ['图灵科技']
              sourceOption.xAxis[0].data.push([res.data.xAxis[i]])
              sourceOption.series[0].name = '图灵科技'
              sourceOption.series[0].data.push(res.data.yAxis[0].data[i])
            }
            this.setChart(sourceOption, 'sourceChart')
            this.sourceTimeChanged = false
          },
          error: (err) => {
            this.sourceTimeChanged = false
          },
        }
      )
    },
    exportExcel() {
      if (this.exportExcelStatu) {
        this.exportExcelStatu = false
        setTimeout(function () {
          this.exportExcelStatu = true
        }, 3000)
        let data = {
          appid: this.appId,
          name: '图灵科技',
          licName: 'turing.turing',
          startDate: this.sourceStartDate,
          endDate: this.sourceEndDate,
        }
        window.open(
          this.baseUrl +
            '/aiui/web/app/financial/exportSourceCount?appid=' +
            data.appid +
            '&name=' +
            data.name +
            '&licName=' +
            data.licName +
            '&startDate=' +
            data.startDate +
            '&endDate=' +
            data.endDate,
          '_self'
        )
      } else {
        this.$message({
          message: '操作过快，稍后再试！',
          type: 'warning',
        })
      }
    },
  },
  components: {
    UseInfoTable,
    dateRange,
    datePicker,
  },
}
</script>
<style lang="scss" scoped>
@import './config/main/common.scss';

.chart-option-wrap {
  position: relative;
}
.chart-type-select {
  position: absolute;
  right: 270px;
}
.date-range-wrap,
.date-picker-wrap {
  position: absolute;
  right: 0;
}
.no-data-tip {
  height: 200px;
  text-align: center;
  line-height: 200px;
  color: $grey5;
}
.el-icon-question {
  color: $grey4;
}
.btn-export {
  margin-left: 30px;
  vertical-align: bottom;
}

.content-container {
  padding: 14px;
  max-height: calc(100vh - 128px);
  overflow: auto;
  background: $secondary-bgc;
  .chart_wrap {
    margin-top: 20px;
    background: #fff;
    padding: 20px;
    border-radius: 12px;
    :deep(.os-page-label) {
      .title {
        position: relative;
        font-size: 16px;
        color: #000000;
        padding-left: 10px;
        &:before {
          width: 2px;
          height: 16px;
          background-color: $primary;
          content: ' ';
          position: absolute;
          left: 0;
          top: 0;
          bottom: 0;
          margin: auto;
        }
      }
    }
  }
}
</style>
