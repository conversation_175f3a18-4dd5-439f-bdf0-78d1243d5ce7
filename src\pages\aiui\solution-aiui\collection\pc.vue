<template>
  <div class="main-content">
    <ul>
      <li
        v-for="(item, index) in solutions"
        :key="index"
        :style="{
          'background-image':
            'url(' +
            require(`@A/images/solution/collection/img_0${index + 1}.png`) +
            ')',
        }"
      >
        <h1>{{ item.title }}</h1>
        <p>{{ item.desc }}</p>
        <a :href="item.url" target="_blank"></a>
      </li>
    </ul>
    <a href="/" target="_blank">科大讯飞AIUI开放平台 https://aiui.xfyun.cn/</a>
  </div>
</template>

<script>
export default {
  data() {
    return {
      solutions: [
        {
          title: '免唤醒语音交互解决方案',
          desc: '无需唤醒，说你所想，一语即达，为产品打造更自然的语音交互体验。',
          url: '/solution/wakeup',
        },
        {
          title: '软硬件一体化解决方案',
          desc: '基于开发套件，快速完成AI能力接入，验证你的产品方案。',
          url: '/solution/soft-hardware',
        },
        {
          title: '机器人语音交互解决方案',
          desc: '提供多种接入方式及优质的技能，助力产品智能化升级。',
          url: '/solution/robot',
        },
        {
          title: '智慧大屏解决方案',
          desc: 'USB式麦克风阵列板即插即用，快速为大屏接入语音能力。',
          url: '/solution/screen',
        },
        {
          title: '智能硬件解决方案',
          desc: '通过集成讯飞多种AI能力，智能硬件厂家可快速实现应用场景创新。',
          url: '/solution/smart-hardware',
        },
        {
          title: 'APP语音助手解决方案',
          desc: '快速集成语音语义能力，让APP能听会说。',
          url: '/solution/assistant',
        },
        {
          title: '智慧地铁解决方案',
          desc: '针对地铁购票、服务咨询等场景，提供软硬件一体化语音交互解决方案。',
          url: '/solution/subway',
        },
        {
          title: '语音点歌解决方案',
          desc: '无需手动触屏，歌名歌手语音搜索，让点唱方式更简单更有趣。',
          url: '/solution/ktv',
        },
      ],
    }
  },
  methods: {
    toConsole() {},
  },
  mounted() {
    document.getElementsByClassName('home-main')[0].style['overflow-x'] =
      'hidden'
  },
  destroyed() {
    document.getElementsByClassName('home-main')[0].style['overflow-x'] = 'auto'
  },
}
</script>

<style lang="scss" scoped>
h1,
ul,
li,
p {
  margin: 0;
  padding: 0;
}

.main-content {
  overflow-x: hidden;
  width: 100vw;
  height: 366.93vw;
  overflow-y: auto;
  background-image: url(~@A/images/solution/collection/img_bg.png);
  background-repeat: no-repeat;
  background-size: 100%;
  background-position: center;
  padding-top: 370px;
  ul {
    li {
      width: 700px;
      height: 240px;
      background-size: 100%;
      background-position: center;
      background-repeat: no-repeat;
      padding: 42px 0 0 31px;
      margin: 0 auto;
      position: relative;
      h1 {
        font-size: 32px;
        font-family: Source Han Sans CN;
        font-weight: bold;
        color: #ffffff;
        line-height: 32px;
      }
      p {
        font-size: 24px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #ffffff;
        line-height: 36px;
        margin-top: 42px;
        max-width: 407px;
      }
      a {
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
      }
    }
    li + li {
      margin-top: 45px;
    }
  }
  > a {
    display: block;
    text-align: center;
    margin: 0 auto;
    width: 700px;
    height: 60px;
    background: #3c54e2;
    opacity: 0.5;
    border-radius: 15px;
    margin-top: 31px;
    font-size: 24px;
    font-family: Source Han Sans CN;
    font-weight: 400;
    color: #d7d6d3;
    line-height: 60px;
  }
}
</style>
