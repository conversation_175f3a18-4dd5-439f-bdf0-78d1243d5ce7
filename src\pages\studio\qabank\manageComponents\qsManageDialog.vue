<template>
  <el-dialog
    title="知识问题管理"
    :visible="dialogVisible"
    @close="handleClose(false)"
    width="660px"
  >
    <div class="dialog-content">
      <div class="left">
        <h4>已选择知识点</h4>
        <el-alert
          v-if="rowSelected.length === 0"
          show-icon
          title="请先选择知识点再添加问题"
          type="warning"
          :closable="false"
        ></el-alert>
        <div class="point-box scroll-box" v-else>
          <p
            class="point-tag"
            v-for="(item, index) in rowSelected"
            :key="index"
          >
            {{ item.id }}
          </p>
        </div>
      </div>
      <div class="right">
        <h4>添加问题</h4>
        <div class="qs-box scroll-box">
          <ul style="margin-top: 10px" class="qs-list">
            <li
              v-for="(qs, index) in qsList"
              :key="index"
              :title="qs"
              class="qs-item"
            >
              <span class="qs-index">{{ index + 1 }}</span>
              <span class="qs-name ellipsis">{{ qs }}</span>
              <i class="el-icon-close qs-del" @click="qsDel(index)"></i>
            </li>
            <li class="qs-item">
              <span class="qs-index ellipsis">{{ qsList.length + 1 }}</span>
              <el-input
                v-model="qsInput"
                placeholder="输入问题，回车键添加"
                style="width: 85%"
                @input="validateInput"
                @keyup.enter.native="addQs()"
              ></el-input>
            </li>
          </ul>
        </div>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button
        type="primary"
        size="medium"
        @click="handleQsSave()"
        :disabled="saveDisabled"
        >保存</el-button
      >
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: 'knowledge-qs-dialog',
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    rowSelected: {
      type: Array,
      default: [],
    },
  },
  data() {
    return {
      qsList: [],
      qsInput: '',
      saveDisabled: false,
    }
  },
  mounted() {
    if (this.rowSelected.length === 0) {
      this.saveDisabled = true
    }
  },
  computed: {
    pointIds: function () {
      let res = []
      this.rowSelected.map((row) => {
        res.push(row.id)
      })
      return res.join(',')
    },
  },
  methods: {
    handleClose(val) {
      this.qsInput = ''
      this.qsList = []
      this.$emit('changeVisible', false)
    },
    handleQsSave() {
      if (this.qsInput.length > 0) {
        this.qsList.push(this.qsInput)
        this.qsInput = ''
      }
      let data = {
        pointIds: this.pointIds,
        queryList: this.qsList,
      }
      let self = this
      this.$utils.httpPost(this.$config.api.KNOWLEDGE_STORE_QS_ADD, data, {
        success: (res) => {
          if (res) {
            this.$message.success('问题批量添加成功')
            this.$emit('changeVisible', false)
          }
        },
        error: (err) => {},
      })
    },
    validateInput() {
      if (this.qsInput.length > 0 && this.rowSelected.length > 0) {
        if (!this.validateQs(this.qsInput)) {
          this.$message.warning(
            '仅支持汉字/字母/数字/空格/{}/_/?/°，且每条不超过60字'
          )
          this.saveDisabled = true
        } else {
          this.saveDisabled = false
        }
      }
    },
    validateQs(val) {
      const qsPattern = /^[\u4e00-\u9fffa-zA-Z0-9 {}_?？°]{1,60}$/
      return qsPattern.test(val)
    },
    addQs() {
      this.qsList.push(this.qsInput)
      this.qsInput = ''
    },
    qsDel(index) {
      this.qsList.splice(index, 1)
    },
  },
  // watch: {
  //   qsInput: function (val) {
  //     if (val.length > 40) {
  //       this.$message.error("问题不得超过40个字符");
  //       this.saveDisabled = true;
  //     } else {
  //       this.saveDisabled = false;
  //     }
  //   },
  // },
}
</script>

<style lang="scss" scoped>
.dialog-content {
  display: flex;
  justify-content: space-between;
  h4 {
    font-size: 16px;
    font-family: PingFangSC, PingFangSC-Medium;
    font-weight: 500;
  }
  .left {
    .point-box {
      width: 240px;
      height: 260px;
      border: 1px solid #dcdfe6;
      border-radius: 8px;
      padding: 12px 10px;
      overflow-y: auto;
      overflow-wrap: break-word;
      .point-tag {
        background: #f5f5fa;
        border-radius: 2px;
        padding: 1px 10px;
        margin: 2px 0;
      }
    }
  }
  .right {
    .qs-box {
      width: 323px;
      height: 260px;
      border: 1px solid #dcdfe6;
      border-radius: 8px;
      overflow-y: auto;
      .qs-list {
        .qs-item {
          background: #f5f5fa;
          border-radius: 4px;
          height: 40px;
          margin: 8px 6px;
          display: flex;
          padding: 5px;
          .qs-index {
            width: 26px;
            height: 26px;
            background: #ffffff;
            border-radius: 4px;
            display: inline-block;
            text-align: center;
            padding: 5px;
            // position: relative;
            // top: 5px;
            // left: 5px;
          }
          .qs-name {
            width: 260px;
            display: inline-block;
            height: 30px;
            line-height: 30px;
            padding-left: 12px;
          }
          .qs-del {
            font-size: 18px;
            cursor: pointer;
            position: relative;
            top: 5px;
          }
          :deep(.el-input__inner) {
            background-color: #f5f5fa;
            border: none;
            height: 30px;
          }
        }
      }
    }
  }
  .scroll-box::-webkit-scrollbar {
    width: 6px;
  }

  /* Track */
  .scroll-box::-webkit-scrollbar-track {
    background-color: #f1f1f1;
  }

  /* Handle */
  .scroll-box::-webkit-scrollbar-thumb {
    background-color: #909399;
    border-radius: 6px;
  }

  /* Handle on hover */
  .scroll-box::-webkit-scrollbar-thumb:hover {
    background-color: #555;
  }
}
</style>
