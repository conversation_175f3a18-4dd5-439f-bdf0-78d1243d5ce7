<template>
  <div class="main-content">
    <section class="main-content-banner" id="product_page0">
      <div class="banner-text">
        <h2>软硬件一体化</h2>
        <p class="banner-text-content">
          为满足客户快速集成AI能力，即插即用，<br />
          AIUI平台打造多款软硬件一体产品，满足录音、降噪、交互等多种需求
        </p>
        <!-- <div class="banner-text-button" @click="toConsole">合作咨询</div> -->
      </div>
    </section>

    <div class="section-wrap">
      <section class="section" id="product_page0">
        <div class="whole">
          <h1>ZG803 AIUI离线语音识别开发套件</h1>
          <h2>支持多语种离线控制，提供简单友好的客制化平台开发工具</h2>
          <div class="text-area">
            <p>80条以内离线控制指令</p>
            <p>
              支持中文、英语、日语、韩语、泰语、越南语、西班牙语、印尼语等多语种控制
            </p>
            <p>串口通信</p>
            <p>尺寸：17mm x 18.5mm x 2.5mm</p>
            <p>适用场景：小家电、智能穿戴等品类</p>
          </div>
          <div class="button-wrap">
            <!-- <aiui-button hasTop @click.native="toConsole('14')"
                      >申请合作</aiui-button
                    > -->
            <div @click="toConsole('22')" class="apply-button">申请合作</div>
            <div
              @click="to803"
              class="apply-button-detail"
              style="margin-left: 20px"
            >
              产品详情
            </div>
          </div>
        </div>
        <div class="solution-image-wrap" style="width: 580px">
          <div class="solution-image-7"></div>
        </div>
      </section>
    </div>
    <div class="section-wrap" style="background: #f4f7f9">
      <section class="section" id="product_page1">
        <div class="solution-image solution-image-1"></div>
        <div class="whole">
          <h1>AIUI USB 声卡开发套件</h1>
          <h2>录音回采二合一，配合麦克风阵列算法快速完成前端声学能力集成</h2>
          <div class="text-area">
            <p>外部音频采集，内部喇叭信号回采</p>
            <p>兼容线性2、4、6麦及环形6麦阵列</p>
            <p>USB口输出数字音频</p>
          </div>
          <div class="button-wrap">
            <!-- <aiui-button hasTop @click.native="toUSB">申请合作</aiui-button> -->
            <div @click="toUSB" class="apply-button">申请合作</div>
            <div
              @click="toAIUIUSB"
              class="apply-button-detail"
              style="margin-left: 20px"
            >
              产品详情
            </div>
          </div>
        </div>
      </section>
    </div>
    <div class="section-wrap">
      <section class="section" id="product_page2">
        <div class="whole">
          <h1>RK3328 AIUI降噪板开发套件</h1>
          <h2>直接输出降噪后纯净音频，支持多种麦克风阵列构型</h2>
          <div class="text-area">
            <p>内置唤醒、声纹、降噪等算法</p>
            <p>USB口/3.5mm耳机口输出降噪音频</p>
            <p>支持线性4、6麦以及环形6麦</p>
            <p>尺寸：80mm*80mm*1.6mm</p>
            <p>适用场景：自助终端机、机器人、会议大屏等品类</p>
          </div>
          <div class="button-wrap">
            <!-- <aiui-button hasTop @click.native="toConsole('14')"
                      >申请合作</aiui-button
                    > -->
            <div @click="toConsole('23')" class="apply-button">申请合作</div>
            <div
              @click="to3328"
              class="apply-button-detail"
              style="margin-left: 20px"
            >
              产品详情
            </div>
          </div>
        </div>
        <div class="solution-image-wrap">
          <div class="solution-image-9"></div>
        </div>
      </section>
    </div>
    <div class="section-wrap" style="background: #f4f7f9">
      <section class="section" id="product_page3">
        <div class="solution-image solution-image-6"></div>
        <div class="whole">
          <h1>AC7911 AIUI语音交互开发套件</h1>
          <h2>
            支持离在线语音交互及AIUI 官方技能，提供简单友好的客制化平台开发工具
          </h2>
          <div class="text-area">
            <p>200条以内离线控制指令</p>
            <p>语音+WIFI+蓝牙三合一</p>
            <p>串口通信</p>
            <p>核心模组尺寸：17mm x 18mm x 2.5mm</p>
            <p>适用场景：智能家电、智能音箱等品类</p>
          </div>
          <div class="button-wrap">
            <div @click="toConsole('21')" class="apply-button">申请合作</div>
            <div
              @click="to7911"
              class="apply-button-detail"
              style="margin-left: 20px"
            >
              产品详情
            </div>
          </div>
        </div>
      </section>
    </div>
    <div class="section-wrap">
      <section class="section" id="product_page4">
        <div class="whole">
          <h1>RK3328 AIUI评估板开发套件</h1>
          <h2>
            搭载AIUI全链路语音交互能力，方便客户快速熟悉平台，完成方案评估和验证
          </h2>
          <div class="text-area">
            <p>支持线阵4麦或者环阵6麦</p>
            <p>对外提供WIFI、HDMI、RJ45等丰富的接口</p>
            <p>提供丰富技能以及海量的内容资源</p>
            <p>尺寸：150mm x 96.2mm x 1.6mm</p>
            <p>适用场景：智能机器人、智能家居等品类</p>
          </div>
          <div class="button-wrap">
            <div @click="toConsole('12')" class="apply-button">申请合作</div>
            <div
              @click="to3328s"
              class="apply-button-detail"
              style="margin-left: 20px"
            >
              产品详情
            </div>
          </div>
        </div>
        <div class="solution-image solution-image-8"></div>
      </section>
    </div>
    <div class="section-wrap" style="background: #f4f7f9">
      <section class="section" id="product_page5">
        <div class="solution-image solution-image-4"></div>
        <div class="whole">
          <h1>RK3588 AIUI多模态交互开发套件</h1>
          <h2>内置讯飞多模态能力，适用公共场所人机交互</h2>
          <div class="text-area">
            <p>高性能配置、尺寸小巧</p>
            <p>兼容线性2、4、6、8麦及环形6麦阵列</p>
            <p>接口类型丰富</p>
            <p>内部集成前端声学算法、多模态能力、全链路语音交互</p>
          </div>
          <div class="button-wrap">
            <div @click="toConsole('29')" class="apply-button">申请合作</div>
            <div
              @click="to3588"
              class="apply-button-detail"
              style="margin-left: 20px"
            >
              产品详情
            </div>
          </div>
        </div>
      </section>
    </div>
    <!-- <div class="section-wrap">
      <section class="section" id="product_page6">
        <div class="whole">
          <h1>AIUI PAGER 智慧大屏演示器</h1>
          <h2>手持演示器，让大屏交互更自然</h2>
          <div class="text-area">
            <p>支持翻页/激光/聚光灯</p>
            <p>双麦克风搭配降噪增益算法，实时转写语音</p>
            <p>语音指令，智能操控</p>
            <p>支持语音交互定制</p>
          </div>
          <div class="button-wrap">
            
            <div @click="toConsole('14')" class="apply-button">申请合作</div>
          </div>
        </div>
        <div class="solution-image-wrap">
          <div class="solution-image-5"></div>
        </div>
      </section>
    </div> -->
    <!-- <div class="section-wrap" style="background: #f4f7f9">
      <section class="section" id="product_page7">
        <div class="solution-image solution-image-0"></div>
        <div class="whole">
          <h1>讯飞智能台历企业定制</h1>
          <h2>能听会说的桌面小帮手，外观时尚、轻巧易用</h2>
          <div class="text-area" style="line-height: 30px">
            <p class="other">
              具备蓝牙音箱、小夜灯、助眠仪、香薰机、闹钟、万年<br />
              历等功能，适用于家庭床头与办公桌面
            </p>
            <p style="margin-top: 12px" class="other">
              面向酒店、金融、保险、医疗、公益等行业的生活、服<br />
              务、礼品等场景提供定制服务
            </p>
          </div>
          <div class="button-wrap">
            <div @click="toCalendar" class="apply-button">申请合作</div>
          </div>
        </div>
      </section>
    </div> -->

    <!-- <div class="section-wrap">
      <section class="section" id="product_page2">
        <div class="whole">
          <h1>AIUI R818 麦克阵列开发套件</h1>
          <h2>内置麦克风阵列算法，直接输出降噪音频及离线命令</h2>
          <div class="text-area">
            <p>外部录音，喇叭信号回采</p>
            <p>内置语音唤醒、降噪算法及离线命令</p>
            <p>适配线性4、6麦及环形6麦阵列</p>
            <p>USB口输出降噪音频</p>
            <p>上位机串口通信</p>
          </div>
          <div class="button-wrap">
            <div @click="toR818" class="apply-button">申请合作</div>
          </div>
        </div>
        <div class="solution-image solution-image-2"></div>
      </section>
    </div> -->

    <!-- <section class="section-4">
      <div class="section-title">
        <p>合作咨询</p>
        <p>提交信息，我们会尽快与你联系</p>
      </div>
      <div class="section-item">
        <aiui-button hasTop @click.native="toConsole">申请合作</aiui-button>
      </div>
    </section> -->
  </div>
</template>

<script>
import { transJumpPageUrl } from '@U/transJumpPageUrl.js'
export default {
  name: 'smart-hardware',
  data() {
    return {}
  },
  watch: {
    $route(val) {
      this.scrollIntoHash()
    },
  },
  mounted() {
    // let self = this
    // // 设置高度
    // let height = this.getWindowHeight()
    // Array.from(document.getElementsByClassName('sectionX')).forEach((item) => {
    //   item.style.height = `${height * 0.7}px`
    // })

    // Array.from(document.getElementsByClassName('section')).forEach((item) => {
    //   item.style.height = `${height * 0.7}px`
    // })

    this.scrollIntoHash()

    window.addEventListener(
      'hashchange',
      (e) => {
        self.scrollIntoHash()
      },
      false
    )
  },
  methods: {
    toCalendar() {
      window.open(`https://www.aifuwus.com/onstage/cmddetail?product_type=3984`)
    },
    toR818() {
      window.open(`https://www.aifuwus.com/onstage/cmddetail?id=2998`)
    },
    toUSB() {
      const newUrl = transJumpPageUrl('/solution/apply/10')
      window.open(newUrl)
      // window.open(`https://www.aifuwus.com/onstage/cmddetail?id=3062`)
    },
    scrollIntoHash() {
      let hash = window.location.hash.replace(/#/, '')
      let dom
      if (hash) {
        dom = document.getElementById(window.location.hash.replace(/#/, ''))
      }
      if (dom) {
        dom.scrollIntoView()
        // window.scrollTo(this.heightToTop(dom))
        // let top = this.heightToTop(dom)
        // dom.scrollTop = top
      }
    },
    heightToTop(ele) {
      //ele为指定跳转到该位置的DOM节点
      let root = document.body
      let height = 0
      do {
        height += ele.offsetTop
        ele = ele.offsetParent
      } while (ele !== root)
      return height
    },

    toConsole(type) {
      // 判断是否有百度SEM营销参数，有的话，提交表单页需要带上
      const newUrl = transJumpPageUrl(`/solution/apply/${type}`)
      window.open(newUrl)
    },
    to7911() {
      const newUrl = transJumpPageUrl(`/solution/soft-hardware/7911`)
      window.open(newUrl)
    },
    to3328() {
      const newUrl = transJumpPageUrl(`/solution/soft-hardware/RK3328`)
      window.open(newUrl)
    },
    to3328s() {
      const newUrl = transJumpPageUrl(`/solution/soft-hardware/RK3328S`)
      window.open(newUrl)
    },
    to803() {
      const newUrl = transJumpPageUrl(`/solution/soft-hardware/ZG803`)
      window.open(newUrl)
    },
    to3588() {
      const newUrl = transJumpPageUrl(`/solution/soft-hardware/RK3588`)
      window.open(newUrl)
    },
    toAIUIUSB() {
      const newUrl = transJumpPageUrl(`/solution/soft-hardware/usb-soundcard`)
      window.open(newUrl)
    },
    getWindowHeight() {
      return 'innerHeight' in window
        ? window.innerHeight
        : document.documentElement.offsetHeight
    },
  },
}
</script>

<style lang="scss" scoped>
.apply-button {
  width: 140px;
  height: 40px;
  background: #1f90fe;
  border-radius: 20px;
  cursor: pointer;
  text-align: center;
  line-height: 40px;
  color: #fff;
  &-detail {
    width: 140px;
    height: 40px;
    background: transparent;
    border-radius: 20px;
    cursor: pointer;
    text-align: center;
    line-height: 40px;
    border: 1px solid #999;
    color: #999;
  }
  &:hover {
    background: #61b0fd;
  }
}
p {
  margin-bottom: 0;
}
.main-content {
  &-banner {
    background: url(~@A/images/solution/soft-hardware/img_soft_hardware_bg_banner.png)
      center no-repeat;
    background-size: cover;
    height: 500px;
    overflow: hidden;
    width: 100%;
    .banner-text {
      max-width: 1200px;
      color: #fff;
      height: 100%;
      margin: auto;
      &-button {
        font-size: 16px;
        text-align: center;
        font-weight: 400;
        width: 140px;
        height: 40px;
        line-height: 40px;
        border: 1px solid #fff;
        border-radius: 40px;
        color: #fff;
        cursor: pointer;
        transition: 0.6s;
        // &:hover {
        //   color: #002985;
        //   background: #fff;
        //   transition: 0.3s;
        // }
      }
      h2 {
        color: #fff;
        padding-top: 148px;
        margin-bottom: 29px;
        font-size: 48px;
        font-weight: 500;
        line-height: 48px;
      }
      p {
        font-size: 18px;
        margin-bottom: 74px;
      }

      .banner-text-content {
        width: 570px;
        font-size: 16px;
        font-family: SourceHanSansSC-Regular, SourceHanSansSC;
        font-weight: 400;
        color: rgba(255, 255, 255, 0.86);
        line-height: 30px;
      }
    }
  }
}
.banner-wrap1 {
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.main-content {
  .section {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 1160px;
    position: relative;
    // display: flex;
    // align-items: center;
    margin: 0 auto;
    padding: 117px 0 113px 0;
    .whole {
      position: relative;
      width: 580px;
      .button-wrap {
        // position: absolute;
        // bottom: -20px;
        margin-top: 20px;
        display: flex;
        flex-direction: row;
        .aiui-button {
          margin-left: 0;
          width: 160px;
          height: 40px;
          line-height: 40px;
        }
      }
      h1 {
        font-size: 28px;
        font-weight: 800;
        color: #333;
      }
      h2 {
        font-size: 16px;
        font-weight: 400;
        color: #666;
      }
      .text-area {
        margin-top: 20px;
        line-height: 48px;
        padding-left: 16px;
        p {
          color: #999;
          position: relative;
          &::before {
            position: absolute;
            content: ' ';
            width: 10px;
            height: 10px;
            border-radius: 100%;
            background: #1f90fe;
            top: 50%;
            transform: translateY(-50%);
            left: -16px;
          }
        }
        p.other {
          &::before {
            position: absolute;
            content: ' ';
            top: 10px;
            transform: translateY(0);
            left: -16px;
          }
        }
      }
    }
  }
  .section-4 {
    margin-top: 109px;
    padding-bottom: 129px;
    text-align: center;
    p {
      margin-bottom: 0;
    }
    .section-title {
      p:first-child {
        font-size: 28px;
        font-weight: 400;
        color: #444444;
        line-height: 40px;
      }
      p:last-child {
        font-size: 16px;
        font-weight: 400;
        color: #777777;
        line-height: 22px;
        margin-top: 18px;
      }
    }
    .section-item {
      margin-top: 49px;
      .section-button {
        color: #fff;
        background: #1784e9;
        width: 195px;
        height: 50px;
        line-height: 50px;
        text-align: center;
        margin: 0 auto;
        cursor: pointer;
      }
    }
  }
  .solution-image {
    width: 500px;
    height: 274px;
    // background-size: cover !important;
  }
  .solution-image-0 {
    width: 512px;
    height: 242px;
    background: url(~@A/images/solution/soft-hardware/img_taili.png) center/100%
      no-repeat;
  }
  .solution-image-1 {
    width: 513px;
    height: 220px;
    background: url(~@A/images/solution/soft-hardware/img_usb.png) center/100%
      no-repeat;
  }
  .solution-image-2 {
    width: 497px;
    height: 297px;
    background: url(~@A/images/solution/soft-hardware/img_r818.png) center/100%
      no-repeat;
  }
  .solution-image-4 {
    width: 500px;
    height: 155px;
    background: url(~@A/images/solution/soft-hardware/img_rk3399.png) center
      no-repeat;
  }

  .solution-image-5 {
    width: 65px;
    height: 273px;
    margin: 0 auto;
    background: url(~@A/images/solution/soft-hardware/img_pager.png) center
      no-repeat;
  }
  .solution-image-6 {
    width: 329px;
    height: 340px;
    // margin: 0 auto;
    margin-left: 20px;
    background: url(~@A/images/solution/soft-hardware/img_product_7911.png)
      center no-repeat;
  }
  .solution-image-7 {
    width: 540px;
    height: 245px;
    margin: 0 auto;
    background: url(~@A/images/solution/soft-hardware/img_product_zg803.png)
      center/100% no-repeat;
  }
  .solution-image-8 {
    width: 412px;
    height: 280px;
    // margin: 0 auto;
    background: url(~@A/images/solution/soft-hardware/img_product_rk23328.png)
      center no-repeat;
  }
  .solution-image-9 {
    width: 412px;
    height: 280px;
    // margin: 0 auto;
    background: url(~@A/images/solution/soft-hardware/img_product_rk23328_jzb.png)
      center no-repeat;
    background-size: cover;
  }
  .solution-image-wrap {
    width: 500px;
  }
}
</style>
