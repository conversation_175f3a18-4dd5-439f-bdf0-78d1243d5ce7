{"name": "AIUIWeb", "version": "1.0.0", "description": "AIUI FE Project", "author": "", "private": true, "scripts": {"analyzer": "webpack --config webpack/webpack.analyzer.js", "dev": "webpack serve --config webpack/webpack.dev.js", "start": "cross-env NODE_ENV=development npm run dev", "build0": "webpack --config webpack/webpack.prod.js", "build:lib": "webpack --config webpack/webpack.lib.js", "build": "cross-env NODE_ENV=production npm run build0 && npm run build:lib", "build:dev": "cross-env NODE_ENV=development node npm run build0", "build:integration": "cross-env NODE_ENV=staging npm run build0", "build:staging": "cross-env NODE_ENV=staging npm run build0", "build:stagingHF": "cross-env NODE_ENV=stagingHF npm run build0", "build:production": "cross-env NODE_ENV=production npm run build0", "build:devops": "npm run build0", "postinstall": "npx husky install"}, "dependencies": {"@codemirror/commands": "^6.8.1", "@codemirror/state": "^6.5.2", "@codemirror/view": "^6.36.8", "@microsoft/fetch-event-source": "^2.0.1", "ant-design-vue": "^1.6.5", "axios": "^0.27.2", "babel-polyfill": "^6.26.0", "babel-runtime": "^6.26.0", "blueimp-md5": "^2.10.0", "docx-preview": "^0.1.18", "echarts": "^4.2.0-rc.2", "element-ui": "^2.4.11", "fullpage.js": "^3.1.2", "jquery": "^3.7.1", "js-base64": "^2.4.9", "js-file-download": "^0.4.4", "lodash-es": "^4.17.15", "lottie-web": "^5.12.2", "md5-js": "^0.0.3", "portal-vue": "^2.1.7", "quill": "^1.3.7", "quill-image-extend-module": "^1.1.2", "swiper": "^6.1.2", "uuid": "^11.1.0", "viewerjs": "^1.11.4", "vue": "^2.6.14", "vue-awesome-swiper": "^3.1.3", "vue-cropper": "^0.5.6", "vue-happy-scroll": "^2.1.0", "vue-json-pretty": "1.4.1", "vue-lazyload": "^0.9.5", "vue-markdown": "^2.2.4", "vue-meta": "^2.4.0", "vue-monaco-editor": "0.0.19", "vue-router": "^3.5.3", "vue-slick": "^1.2.0", "vue-video-player": "^5.0.2", "vuedraggable": "^2.23.2", "vuex": "^3.6.2", "wow.js": "^1.2.2"}, "devDependencies": {"@babel/core": "^7.18.0", "@babel/preset-env": "^7.18.0", "@vue/babel-helper-vue-jsx-merge-props": "^1.4.0", "@vue/babel-preset-jsx": "^1.4.0", "autoprefixer": "^10.4.19", "babel-loader": "^8.2.5", "buffer": "^6.0.3", "clean-webpack-plugin": "^4.0.0", "compression-webpack-plugin": "^10.0.0", "copy-webpack-plugin": "^11.0.0", "core-js": "^3.22.5", "cross-env": "^5.2.0", "crypto-browserify": "^3.12.0", "css-loader": "^6.6.0", "css-minimizer-webpack-plugin": "^3.4.1", "dotenv": "^16.0.0", "eslint": "^8.2.0", "eslint-config-airbnb-base": "15.0.0", "eslint-config-prettier": "^8.5.0", "eslint-import-resolver-webpack": "^0.13.2", "eslint-plugin-import": "^2.25.2", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-vue": "^8.7.1", "eslint-webpack-plugin": "^3.1.1", "file-loader": "^6.2.0", "html-webpack-plugin": "^5.5.0", "husky": "^7.0.4", "js-conditional-compile-loader": "^1.0.15", "lint-staged": "^12.4.1", "mini-css-extract-plugin": "^2.5.3", "postcss": "^8.4.14", "postcss-loader": "^7.0.0", "postcss-preset-env": "^7.6.0", "postcss-pxtorem": "^6.1.0", "prettier": "^2.6.2", "process": "^0.11.10", "purgecss-webpack-plugin": "^4.1.3", "regenerator-runtime": "^0.13.9", "sass": "^1.52.1", "sass-loader": "^13.0.0", "stream-browserify": "^3.0.0", "style-loader": "^3.3.1", "svg-sprite-loader": "^6.0.11", "terser-webpack-plugin": "^5.3.1", "url-loader": "^4.1.1", "vm-browserify": "^1.1.2", "vue-loader": "^15.9.8", "vue-template-compiler": "^2.6.14", "webpack": "^5.72.1", "webpack-bundle-analyzer": "^4.5.0", "webpack-cli": "^4.9.2", "webpack-dev-server": "^4.9.0", "webpack-merge": "^5.8.0", "webpackbar": "^5.0.2", "worker-loader": "^3.0.1"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"**/*": "prettier --write --ignore-unknown"}}